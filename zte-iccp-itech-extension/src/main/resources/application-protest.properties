
#msb\u6CE8\u518C\u5730\u5740\uFF0C\u793A\u4F8B\u662F\u6D4B\u8BD555\u73AF\u5883\u5730\u5740
#msb\u6CE8\u518C\u5730\u5740\uFF0C\u793A\u4F8B\u662F\u6D4B\u8BD555\u73AF\u5883\u5730\u5740

artifactory.url = https://artsz.zte.com.cn/artifactory/it-release-maven/com/zte/iccp/itech/zte-iccp-itech-extension/1.0-SNAPSHOT/

plugin.public.baseEntity.enhance = true
register.address = msrtest.zte.com.cn:10081

register.node.ip = ************
register.node.port = 8888
register.version = v1
servicecenter = msb

apijson.db.version = 5.7.22
apijson.db.schema = lcap_pro_st
jdbc1.type = com.alibaba.druid.pool.DruidDataSource
jdbc1.driverClassName = com.mysql.jdbc.Driver
msa.encrypt.datakey = H9BrfkazKTddWiHGTM91rY06keKguMtnOpgh6vpYq1JqFmqB7EXlDgI7TyZ/FrfLHeQEFNdgFs2snSkyYBoY9w==
msa.rootkey.factor1 = zte-iccp-itech-demoapi

msa.ccs.encrypt.factor = ENC(BjAUxH0Icl4LmdaTOr0i6nzjaqO/oDRl+5YLg496ktHP6AcdyFu08YCzcUNMY4QE1Tqdo4id+hA=)
datasource.config.rsa.public-key = KENC(0GlwawL8rMWmspGc9YgKPYuL+C91vDAaEm/E8ibBiLmIP2E4lOmD3PhCT8FyhXOKF1HRlq9J631yFebfbV30OxEcqnnZwjXPHMwiTTDI9l/SFNqSlaHicURT0juTtSdioZydKxb2woqoY9s/gZZKw8zHk6DWWSIHmfZ9XRCdd5oNkglFLueMLKsQOnG/8V318Z9NJQmPnYghsMVuEAWwqqrho4H9mx5kT8qk3lGrGTtbsUzAy9NOV/gVaA1Ks2ZliajNbcvTmMt88lfzpRoWsrZKGZglzmf3qsLBu4fHvC8++Igze9S4ew==$$xa3qgjMlruH6h+pE)
spring.datasource.druid.stat-view-servlet.enabled = true
spring.datasource.druid.stat-view-servlet.url-pattern = /druid/*
spring.datasource.druid.stat-view-servlet.login-username = root
spring.datasource.druid.filter.stat.enabled = true
spring.datasource.druid.filter.stat.db-type = mysql
spring.datasource.druid.filter.stat.log-slow-sql = true
spring.datasource.druid.filter.stat.slow-sql-millis = 500

jdbc.maxActive = 150
jdbc.validationQuery = SELECT 1 FROM DUAL

jdbc.testOnBorrow = true
jdbc.testOnReturn = true
jdbc.minEvictableIdleTimeMillis = 600000
jdbc.druid.maxEvictableIdleTimeMillis = 3600000
jdbc.timeBetweenEvictionRunsMillis = 60000

jdbc.removeAbandoned = false

spring.flyway.enabled = false

spring.flyway.encoding = UTF-8
spring.flyway.locations = classpath:db/migration/test/V12412/update

spring.flyway.sql-migration-prefix = V

spring.flyway.sql-migration-separator = __
spring.flyway.validate-on-migrate = true

spring.flyway.baseline-on-migrate = true
spring.flyway.placeholder-replacement = false

spring.flyway.out-of-order = true

lcappro.design.auth.enabled = true
inone.url = https://icosg.test.zte.com.cn
spring.servlet.multipart.max-file-size = 200MB

spring.servlet.multipart.max-request-size = 200MB

uac.sdk.tenantId = 10001
uac.sdk.issuer = https://uactest.zte.com.cn:5555/zte-sec-uac-iportalbff/oidc/10001

uac.sdk.baseUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff

uac.sdk.appId = ************
uac.sdk.clientId = ************
approval.sdk.app.appId = ************

approval.sdk.app.appCode = ************

approval.sdk.use.signatureAlgorithm = SHA256
upp.auth.productId = 6751046

upp.auth.moduleId = ************
jdbc1.url = ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
uac.sdk.responseRule = msa

uac.url = https://uactest.zte.com.cn:5555
uac.sdk.cookie.httpOnly = false

lcap.employee.provider = uacEmployeeProvider

uac.idType = T0001

dtems.agent.applicationInsId = 6925029778749446
dtems.agent.amosBaseUrl = https://ume.uat.zte.com.cn
dtems.agent.enable = true

dtems.agent.codes.default = **********

dtems.agent.moc = cn.dt.business

alarm.code.operation = 171612669
alarm.businessId = 699328029902536704
sync.interface.suffix = /ZXLCAP/LCAPPRO300/api/v1/sync

approval.sdk.request.header.tenantId.source = true

jdbc.poolPreparedStatements = false
jdbc.druid.keepAlive = true

ucs.tenantId = 10001
msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-003

cacheCloud.server = http://mspuat.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4
msa.redis.pool.maxIdle = 300

msa.redis.pool.minIdle = 300
msa.redis.database = 2

redis.customCacheManager = true

msa.redis.serializerType = genericJackson2Json
spring.redis.sentinel.master = zxiccp-iepms300-003
spring.redis.sentinel.nodes = *************:36379,*************:36379,*************:36379
msa.redis.pool.maxActive = 300

lcap.redis.exception.inject.enabled = true

lcap.redis.exception.inject.down = false
msa.redis.driveType = jedis

cloudDiskSDK.host = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk
cloudDiskSDK.httpClientLogSwitch = true

cloudDiskSDK.xOriginServiceName = EPMS

lcap.file.previewUrl = https://idrive.test.zte.com.cn/zte-km-cloududm-docview

upp.auth.tenantId = 4304

upp.auth.appListConstraintCode = app_list
upp.auth.appListConstraintId = 2970006
upp.auth.authUrl = https://uactest.zte.com.cn/zte-sec-upp-authbff
upp.auth.manageUrl = https://uactest.zte.com.cn/zte-sec-upp-bff

message.server = http://msptest.zte.com.cn:8888/zte-itp-msp-message/v1
message.inoneAppCode = KENC(xh3gIkRhzqg37lI+I8eDRdykm49Hyr5RDH3SFL/BE6bm0UjsI2Az7JIcQ/rnM+Lu$$Rp7fODF/5ZD+VrRF)

spring.kafka.consumer.groupId = zte-iccp-itech-demoapi

spring.kafka.enabled = false
approval.kafka.topic = zte-iss-approval-nj
approval.sdk.header.xEmpNo.default = 10284287
approval.sdk.kafka.receipt.enabled = false
approval.sdk.webhook.url = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/flow/webhookProcess

lcap.flow.update.enable = true

keypath = https://msptest.zte.com.cn/zte-itp-isoa-security/securityctl/getsk

fileFormat.limit = php,exe,asp,jsp,aspx
lcap.comment.icenter.sysCode = 228229

lcap.comment.email.address = <EMAIL>

lcap.comment.serviceName = zte-paas-lacp-demoapi

query.distinct.switch = false

web.interceptor.header-verification.excludePathPatterns[0] = *.js,*.gif,*.jpg,*.png,*.css,*.ico
web.interceptor.header-verification.excludePathPatterns[1] = /swagger-*/**

web.interceptor.header-verification.excludePathPatterns[2] = /v3/api-docs

web.interceptor.header-verification.excludePathPatterns[3] = /v1/app/*/page/*/constraint/*/list

web.interceptor.header-verification.excludePathPatterns[4] = /v1/app/*/model/*/constraint/*/list
web.interceptor.header-verification.excludePathPatterns[5] = /v1/models/formdata

web.interceptor.header-verification.excludePathPatterns[6] = /v1/models/formdata/*
web.interceptor.header-verification.excludePathPatterns[7] = /v1/attachment/tempurl
web.interceptor.header-verification.excludePathPatterns[8] = /v1/attachment/batch/tempurl
web.interceptor.header-verification.excludePathPatterns[9] = /v1/attachment/temp/download/*

web.interceptor.header-verification.excludePathPatterns[10] = /v1/flow/reassignTask
web.interceptor.header-verification.excludePathPatterns[11] = /v1/flow/pageFlowInstance

web.interceptor.header-verification.excludePathPatterns[12] = /v1/flowInstance/getFlowInstanceProcess

web.interceptor.header-verification.excludePathPatterns[13] = /v1/flowInstance/getOperateRecords
web.interceptor.header-verification.excludePathPatterns[14] = /v1/flowInstance/getPageInstanceDataByFlowInstance

web.interceptor.header-verification.excludePathPatterns[15] = /v1/flow/myTaskToDo
web.interceptor.header-verification.excludePathPatterns[16] = /v1/flow/saveFlowInstanceData
web.interceptor.header-verification.excludePathPatterns[17] = /v1/flow/submitTask
web.interceptor.header-verification.excludePathPatterns[18] = /v1/attachmentFile/queryInfo

web.interceptor.header-verification.excludePathPatterns[19] = /v1/attachmentFile/download

web.interceptor.header-verification.excludePathPatterns[20] = /prometheus
web.interceptor.header-verification.excludePathPatterns[21] = /error

web.interceptor.header-verification.excludePathPatterns[22] = /v1/flow/webhookProcess
web.interceptor.header-verification.excludePathPatterns[23] = /v1/bizobject/batchquery

web.interceptor.header-verification.excludePathPatterns[24] = /v1/bizobject/queryentity

web.interceptor.header-verification.excludePathPatterns[25] = /v1/application/batchquery
web.interceptor.header-verification.excludePathPatterns[26] = /v1/app/*/document/download/*
web.interceptor.header-verification.excludePathPatterns[27] = /v1/entryentity/redirect/*
web.interceptor.header-verification.excludePathPatterns[28] = /customComponents/resources/download/**

web.interceptor.header-verification.excludePathPatterns[29] = /v1/attachment/download

web.interceptor.header-verification.excludePathPatterns[30] = /v1/attachment/uploadFile

web.interceptor.header-verification.excludePathPatterns[31] = /components/resource/download

web.interceptor.header-verification.excludePathPatterns[32] = /v1/app/*/file/uncheck/downloadImage
web.interceptor.header-verification.excludePathPatterns[33] = /v1/app/file/uncheck/download
web.interceptor.header-verification.excludePathPatterns[34] = /v1/app/*/bizObj/*/treeDataConstraint
web.interceptor.header-verification.excludePathPatterns[35] = /v1/app/*/bizObj/*/dataConstraint
web.interceptor.header-verification.excludePathPatterns[36] = /zte-iccp-itech-demoapi/customComponents/resources/download/**
web.interceptor.header-verification.excludePathPatterns[37] = /zte-iccp-itech-demoapi/v1/flow/webhookProcess

web.interceptor.header-verification.excludePathPatterns[38] = /APP0984032412495249408/batchtasks/*/4auth

web.interceptor.header-verification.excludePathPatterns[39] = /zte-iccp-itech-demoapi/idop/**
web.interceptor.header-verification.excludePathPatterns[40] = /v1/app/file/uncheck/encryptDownload
web.interceptor.header-verification.excludePathPatterns[41] = /zte-iccp-itech-demoapi/cnop/**
web.interceptor.header-verification.excludePathPatterns[42] = /zte-iccp-itech-demoapi/common/**

fastcode.iss.system.code = IEPMS

lcap.oss.strategy.nas.path = /usr/local/app/lcap_pro/plugins
lcap.oss.strategy = CloudUdm

sync.export.data.limit = 200
entryEntity.import.maxCount = 100

email.address = <EMAIL>
route.url = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/

ruleEngine.impl = inferenceRuleEngine

lcap.metadata.name = RadioField_1666706847500906498,TreeView_1666708164319424512,Button_1666708164319424513,FormContainer_1668511540921364481,FlexContainer_1668511540921364521,Page_1668511540921364522,RootHeader_1668511540921364523,RootContent_1668511540921364524,RootFooter_1668511540921364525,CheckboxField_1669157069015523330,CreatorField_1669159037637271554,ModifierField_1669160258221678594,ColumnsLayout_1669163100164628482,TextField_1669263997930668034,TextareaField_1669264775302971393,NumberField_1669264843577851906,SelectField_1669264989531242498,MultiSelectField_1669265075824852994,TimeRangeField_1669265341731143681,TimeField_1669265453039583233,CascadeDateField_1669265491434242050,DateField_1669265512862941186,AttachmentField_1669266526156455937,EmployeeField_1669267520932118529,DepartmentSelectField_1669267628729925634,Steps_1669267902223712258,Link_1669268060441247745,TablePc_1669268167463108610,FieldLayoutPanel_1669268524767477762,AdvancedContainer_1669269111378640898,TabsLayout_1669269190479020033,ServiceObject_1669269190479021128,ToolbarField_1669269324835160065,Column_1671396272621334529,Tag_1673211663472484353,BillEntity_1674597481403899905,FastCodeField_1674610451040423938,FilterControl_1675305711890087937,BillStatusField_1675305711890087938,BasicDataField_1675305721830084930,BasicDataProps_1675305770033407910,BillNumberField_1675315325835024930,EntryEntity_1677146589633851394,SubEntryEntity_1677152291462983682,MultiFastCodeField_1677590491235467265,ServiceEntity_1679010253945192449,ServiceEntryEntity_1679052327948005378,TableFieldNew_1679310696938815489,CountrySelectField_1679760740479250434,Search_1679760740479250489,TabPanel_1681843769295593473,AssociationList_1681960665247842306,GroupDataField_1682277538857496578,ParentBasicDataField_1683369541332328449,MyReviewField_1686935437505396737,ToolbarItem_1688373444119355393,Prompt_1688726315538108418,EditorField_1688872066648133634,TeamViewPageField_1689878053168603137,CommentField_1690904008878206978,TeamConfigPageField_1690971833915285505,ApproveHistoryField_1691002512976228353,FlowProcessField_1691003592871096322,ExtendEntity_1691063693937098754,ApproveCommunicationField_1691064211279331330,AdvancedContainerToolbar_1692425780766523393,BillTypeField_1693824780483575810,MultiBasicDataField_1694190376775278594,PictureField_1694623101919408129,ServiceObjectProps_1697926811336531969,BusinessObjectProps_1697926453910528002,RichText_1697446694240657410,BusinessObjectField_1697919178500169730,ConditionField_1699715635744354306,ApprovalProgress_1706205948884803585,OperationRecords_1716749328987996162,SubTableField_1716287217534500865,MyReviewTileField_1716718545015562241,FormStatusDiagram_1712444431648636930,FilterProgramme_1726514685908578306,SubButtonItem_1725407107878014978,TextField0113_1729106748838604802,AttachmentDownloader_1742397387786379266,InlinePage_1742891095489077250,FlowChart_1745733431735631873,SwitchField_1745733468167356417,TreeSelectField_1746880575654084610,QrCode_1762315391887990786,MultiBusinessObjectField_1762315391887990996,Breadcrumb_1765568886942736386,SlotComponent_1771435586834993154,Iframe_1782769796791902209,RefComplexCondition_1783127124235382785,AggregationField_1783694852430053378,Card_1792759766927376385,CardContainer_1792759926776496130,CardToolbar_1792760098340306946,MultilingualTextField_1805044544690065409,MultiServiceObject_1724134829869895678,Control_1669268214657445762,StdExtMultiSelectField_1675304721833081930,StdExtSelectField_1669263167465103610,Divider_1724032682457898940,FilterCustom_1726514685908574851,ChangeDifference_1727245121817569854,GridCol_1969269111378640584,GridLayouter_1969269111378684572,GridRow_1969269111378648957,DropdownListField_1669264989531244587,Dialog_1669268524767476832,Filter_1669268524767473747,ValidateContainer_1746880575654483612,Div_1969269111378640897,SelectNewField_1109438420029014016,RadioNewField_1666706847500906499,DepartmentField_1669267628724824854,MultiSelectNewField_1669265075824852995,CheckboxNewField_1669157069015523331

spring.mvc.throw-exception-if-no-handler-found = true
spring.resources.add-mappings = false
lcap.auth.service.register.boListUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/app/bo/pageQuery

rnUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo
plugin.public.pro.appid = ZXISS_IAPPROVAL001
plugin.public.iss.review.approve.approveTeamConfigCid = approveTeamConfigCid

plugin.public.iss.review.approve.approveResultCid = approveResultCid
plugin.public.iss.review.approve.resultYkey = pass
plugin.public.iss.review.approve.resultNkey = noPass

plugin.public.iss.review.approve.resultCkey = cpass
plugin.public.iss.review.approve.resultRejectkey = refuse

dynamic.domain.form.model.expire.time = 28800
dynamic.domain.sub.page.expire.time = 28800
dynamic.domain.operationContext.expire.time = 28800
lcap.ddm.validator.disable = true

dynamic.domain.load.status.mark.header.expire.time = 28800
dynamic.domain.load.table.field.data.expire.time = 28800

lcap.db.open.duplicate = false

lcap.context.path.download = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi
hcp.bucket.non-anonymous = lcap
lcap.oss.strategy.nas.file.max.size = 209715200
alarm.code.rule = 565279899
alarm.code.privilege = 702085763
plugin_load_type = Nas

alarm.code.pluginLoading = 1457578756
isoalog.kafka.topic = ms-zte-lcap-app
lcap.host.port = iepms.test.zte.com.cn:8443

upp.specialDataAuth.url.format = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/preview.html?lang=%s&type=page&bizObject=specialDataAuth#/app/%s/page/PAGE0917349422860693510
upp.fieldAuth.url.format = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/dataPermission.html#/upp/%s/roleAuthorization/fieldAuth
jetcache.statIntervalMinutes = 60
jetcache.areaInCacheName = false

jetcache.hiddenPackages = com.zte
jetcache.local.default.type = caffeine
jetcache.local.default.keyConvertor = fastjson2

jetcache.local.default.limit = 500

jetcache.local.default.expireAfterAccessInMillis = 0

jetcache.local.default.expireAfterWriteInMillis = 1800000

jetcache.local.otherArea.type = caffeine
jetcache.local.otherArea.keyConvertor = fastjson2
jetcache.local.otherArea.limit = 100
jetcache.local.otherArea.expireAfterAccessInMillis = 0
jetcache.local.otherArea.expireAfterWriteInMillis = 1800000
jetcache.remote.default.type = redis.zlettuce

jetcache.remote.default.keyPrefix = lcap_
jetcache.remote.default.broadcastChannel = defaultDynamicDomainService
jetcache.remote.default.keyConvertor = fastjson2
jetcache.remote.default.valueEncoder = kryo5

jetcache.remote.default.valueDecoder = kryo5

jetcache.remote.default.expireAfterWriteInMillis = 3600000

jetcache.remote.default.uri = redis-sentinel://${spring.redis.password}@${spring.redis.sentinel.nodes}?MultiModeProdConsts=10s&sentinelMasterId=${spring.redis.sentinel.master}&database=${redis.uri.database}

jetcache.remote.otherArea.type = redis.zlettuce
jetcache.remote.otherArea.keyPrefix = lcap_
jetcache.remote.otherArea.broadcastChannel = otherDynamicDomainService

jetcache.remote.otherArea.keyConvertor = fastjson2

jetcache.remote.otherArea.valueEncoder = kryo5

jetcache.remote.otherArea.valueDecoder = kryo5
jetcache.remote.otherArea.expireAfterWriteInMillis = 3600000
jetcache.remote.otherArea.uri = redis-sentinel://${spring.redis.password}@${spring.redis.sentinel.nodes}?timeout=10s&sentinelMasterId=${spring.redis.sentinel.master}&database=${redis.uri.database}

redis.uri.database = 1

jdbc.minIdle = 50
jdbc.initialSize = 50

lcap.auth.mode = lcap
lcap.host = iepms.test.zte.com.cn
lcap.approval.sdk.app.appId = KENC(GB4W1FZ3XJsoWAo65cJVD8leSj9rN7vni/g4Fu48zsNTItYY$$CC4tWTgBy4v35lmF)

lcap.approval.sdk.app.appCode = zte-pass-lcap
cos.host.https = https://apigwtest.zte.com.cn:2443


lcap.openapi.path.download = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/attachment/temp/download/
page.running.url.format = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=app#/app/%s/page/%s
entryBillEntity.import.maxCount = 50000

lcap.app.map = {APP0984032412495249408:"371663511441"}

lcappro.plugin.sysCode = pro
lcap.mc.service.name = zte-iccp-itech-promcapi


lcap.promgrapi.version = v2
lcap.mc.version.no = v2
debug.code.flag = flase
management.endpoint.health.enabled = false

notice.center.base.url = https://icenter-systest.test.zte.com.cn/zte-icenter-notice-message
lcap.save.operation = true

http.socketTimeout = 30000

basic.tree.fuzzy.query.page.size = 20


springfox.documentation.auto-startup = true

swagger.exclude.url = /v2/api-docs,/prometheus

chat.bot.config.roleCode = ai-bot-viewer

chat.bot.config.productId = 2251001
chat.bot.config.moduleId = 3420003
lcap.component.strategy = S3

plugin.public.authTask.enabled = true

plugin.public.clockInTask.enabled = true

plugin.public.approval.ccnAreaTd.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025234,6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025235
plugin.public.approval.repOfficeTd.inter.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849
plugin.public.approval.repOfficeTd.inter.prodIdPath = 6872952355824349360/
plugin.public.approval.tdNetOffice.inner.negative.prodIdPath = 6872952355824349357/
plugin.public.approval.tdNetOffice.inter.negative.prodIdPath = 6872952355824349360/
plugin.public.approval.tdNetOffice.inter.negative.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849
plugin.public.approval.remoteCenterOwner.prodIdPath = 6872952355824349360/,6872952355824349361/

plugin.public.approval.testDeptApproval.prodIdPath = 6872952355824349360/6872952355824349368/
plugin.public.grantFile.secretKey.rsaPriKey = KENC(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$$avECD6i3xuqhY0+k)
plugin.public.grantFile.secretKey.rsaPubKey = KENC(lMKvie26BkUTHPY0aT17kq++GCDMxQixpEraE4fnQGq+EmjzzfL3QMP0wIaqJo+34yi7Jp+LQ2AQDneVCnO2nBsPyumILxid+DhAHzuTtlZtLIcW7HyA9VNcxg/SKGmqgRG63utoa20PF+BDn01umhkJeQEsRcP39CdPCR1hWKWLQPVMw1AE38qo1dgECbm7CE7GpbUz655IbHs9dNTjuotK4HmWu0Tj4Pm+uRqt/1utx0sE+9M5iE0F/Ula4EE0qbfxlhRlodgTneHd1qza37iymn+wBig6khHkO/IOUySp1yl7iqrvInJUBMK3u+geDme89qeuZ2AtpfeMdOH9Jgkyiw9p9hY/9EZ5SpebbAQYmFIOunyQA85+KCwLnpEVGMYte7kIJNgIiafs2vMqz190pFhtAJ2VQFnr5YyCK/+u2LTuWkOKNC8yggEfh+bmCRKwjxBHipT1NnRHr+Wzlnxuyp/Z1qv/UL7n2ZwurEm9dfaKa/Bld8OlAixmncfbX9A21lyIbxhkBWcCoXu7lMwjXosFkijm$$8lCt8ihMjtLvQx9+)
plugin.public.grantFile.downloadApiUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/APP0984032412495249408/grantfiles?sa=%s&entityType=%s&bizId=%s
plugin.public.grantFile.downloadPageUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=zh&type=app&bizObject=null&sa=%s&entityType=%s&bizId=%s#/app/APP0984032412495249408/page/download_grant_file

plugin.public.cces.host = https://cces.zte.com.cn:9119/
plugin.public.cces.callings.url = api/aicall/callings
plugin.public.cces.callresult.url = api/aicall/callresult

plugin.public.cces.clientId = KENC(Qbb++EiQy19XGuEmIRHXvJyAdwuB8njPv2VPh/xbKaqoryHIEf8+BAnTZDBxx7FUZbh4Ag==$$ukOT9BoE7svm42OO)
plugin.public.cces.clientSecret = KENC(S+jLDN7G9lwaubobcyhALj34YUkfsTkoHKuAkBuOWWMFTBiQocj4RznkoL7lv5xRtpBw8g==$$vGRmPCSydMlF9uSL)
plugin.public.cces.white.telphone = ***********,***********,***********,***********,***********,***********,***********,***********,***********

plugin.public.crm.host = http://test.ssc.zte.com.cn:8888/

plugin.public.crm.service.name = zte-crm-account-info
plugin.public.crm.service.url.queryAccount = /selectAccountPageByKeyWords/v2
plugin.public.crm.service.url.getCustomerDetails = /noPermisonAccount
plugin.public.crm.service.url.querySimpleInfo = /api/customer/simple/info
plugin.public.crm.service.url.queryCustomerInfoList = /ZXISS_SS300/ACCOUNT_INFO/zte-crm-account-info/api/customer/batchDetail/v2
plugin.public.hr.host = https://ecsuat.zte.com.cn/
plugin.public.hr.service.name.person = zte-hrm-usercenter-pginfo

plugin.public.hr.service.name.organization = zte-hrm-usercenter-orginfo
plugin.public.hr.service.url.conditionQueryOrganizationInfo = /ihol/usercenter/orginfo/usercenter/queryOrgInfo

plugin.public.hr.service.url.queryPersonGeneralInfo = /ihol/usercenter/pginfo/usercenter/plain/getPersonGeneralInfo
plugin.public.hr.service.url.queryOrganizationInfo = /ihol/usercenter/orginfo/usercenter/getOrgInfo

plugin.public.hr.enable.service.url.queryCertificate = /enable-bff/srv/engineer/certificate/query
plugin.public.nis.host = http://************:8888

plugin.public.nis.service.name = /zte-iccp-iepms-nis
plugin.public.nis.service.url.queryProductionTree = /api/productclassifications/tree

plugin.public.sysAuth.accessKey[iNet] = KENC(qcscuryZmtP+Ds5l/wRPlNZAtruzBTYDmcJ1DadgEB5irvXQMXKZr/kmBV21iZthooXQvw==$$ANQbi8C3+C6fs1Zi)
plugin.public.nis.service.url.queryOrganizationTree = /api/organizations/tree
plugin.public.nis.service.url.queryNetworks = /api/networks/queries

plugin.public.nis.service.url.header.xItpValue.secretKey = KENC(rJpOtw4IiCIpi7XqZ+oFUdB4KUHnq4x7GIKLjY5c7D9Ftqk55WTE8vb7FAMfCXRWrmZinnsLIas8g/tqRQrYJ8onNvzbqtk36Bw0OHBzHYU=$$KnC3mihv6vBznZEv)

plugin.public.nis.service.url.queryProductModel = /api/productmodels/queries

plugin.public.nis.service.url.queryCustomerFlag = /api/customer/queryCustomerFlag

plugin.public.nis.service.url.queryProductInfo = /api/productclassifications/selectbyidpath
plugin.public.gtdCenter.service.name = /zte-iccp-iepms-gtdcenter
plugin.public.icrm.ai.host = https://boassistant.test.zte.com.cn/
plugin.public.icrm.ai.service.name = zte-doc-ai-assistant
plugin.public.icrm.ai.service.url.templateTransmission = /templateTransmission

plugin.public.iVersion.host = https://iversion.test.zte.com.cn

plugin.public.iVersion.service.name = zte-plm-iversion-api
plugin.public.iVersion.url.getVersionInfoAndCorrelationInfo = https://iversion.test.zte.com.cn/zte-plm-iversion-api/publish/versionOutgo/getReleaseVersionInfo

plugin.public.iVersion.url.getBasicAndIncrementVerByCondition = https://iversion.test.zte.com.cn/zte-plm-iversion-api/publish/getBasicAndIncrementVerByCondition
plugin.public.ucs.keyword.accessKey = KENC(aDFD1FR4A9tinfLt1cf1nlewEHN8dvHQgJF/CHaaOIsQg2Kz1Diqfb2p0wFNQ1rby25+2w==$$srwIqzGzkhrhzrCF)
plugin.public.ucs.keyword.secretKey = KENC(9ZmIAfNJpjniV/8q+dMiqPHZkHbDAPR9TaQs1ZV7yCRRPS13bZn0Swg0ikgXnLPKHa5ONw==$$npmFyacM+boVY6ar)

plugin.public.ucs.keyword.appId = 10015

plugin.public.ucs.keyword.tenantId = 10001

plugin.public.ucs.host = http://test55.ucs.zte.com.cn:8888/

plugin.public.ucs.service.name = zte-bmt-ucs-api

plugin.public.ucs.url.queryAllUserInfo = /srv/v2/account/queryall
plugin.public.ccn.prodIdPath = 6872952355824349361/

plugin.public.bn.prodIdPath = 6872952355824349358/
plugin.public.fm.prodIdPath = 6872952355824349359/
plugin.public.mmvs.prodIdPath = 6872952355824349359/6872952355824349383/

plugin.public.clouddisk.url.sendPartData = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/file/sendPartData
plugin.public.clouddisk.url.getDownloadToken = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadToken
plugin.public.clouddisk.url.downloadByToken = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/{appCode}/{token}
plugin.public.clouddisk.url.queryFileStatus = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/file/queryFileStatus
plugin.public.clouddisk.url.finishPartObject = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/finishPartObject
plugin.public.clouddisk.url.queryFileInfo = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/queryFileInfo

plugin.public.pdm.url.queryProductInfoByLevel = https://pdmweb.test.zte.com.cn/zte-plm-iproduct-category/category/v1/query
plugin.public.pdm.url.queryProductInfoByLinkage = https://pdmweb.test.zte.com.cn/zte-plm-iproduct-category/relation/v1/query
plugin.public.pdm.auth.value = KENC(GBGW26XFKbb7sE1dx1RCxynWgdMFP8PqPmOfpsFOIqeuKDi6EoWNwicHoV4I3VWN$$H+FxQ8xgIQFpj4wR)

plugin.public.icrm.ai.service.url.generate = /itech/doc/generate

plugin.public.ran.prodIdPath = 6872952355824349360/

plugin.public.te.prodIdPath = 6872952355824349357/
plugin.public.goldendbtech.prodIdPath = 567989959957302783/

plugin.public.vd.prodIdPath = 567989959957302782/

plugin.public.dc.prodIdPath = 567989959957302785/

plugin.public.prodTeam.prodLineIds.ran = 6872952355824349360/6872952355824349368/,6872952355824349360/7188264679487025229/

plugin.public.prodTeam.prodLineIds.mw = 6872952355824349360/6872952355824349370/

plugin.public.prodTeam.prodLineIds.wantong = 567989959957302784/567989959957302792/
plugin.public.prodTeam.prodLineIds.jinyi = 567989959957302779/567989959957302788/

plugin.public.prodTeam.prodLineIds.ids = 2594612964948697239/2594612964948697240/
plugin.public.prodTeam.prodLineIds.fm = 6872952355824349359/6872952355824349385/,6872952355824349359/6872952355824349376/,6872952355824349359/6872952355824349379/

plugin.public.prodTeam.prodLineIds.bn = 6872952355824349358/6872952355824349382/,6872952355824349358/6872952355824349384/,6872952355824349358/6872952355824349374/
plugin.public.prodTeam.prodLineIds.dc = 567989959957302785/567989959957302789/
plugin.public.prodTeam.prodLineIds.sdi = 567989959957302781/567989959957302794/

plugin.public.prodTeam.prodLineIds.ccn = 6872952355824349361/6872952355824349365/,6872952355824349361/6872952355824349369/,6872952355824349361/6872952355824349387/,6872952355824349361/6872952355824349388/

plugin.public.prodTeam.prodLineIds.te = 6872952355824349357/6872952355824349371/,6872952355824349357/7188264679487025228/

plugin.public.prodTeam.prodLineIds.energy = 567989959957302780/567989959957302795/
plugin.public.prodTeam.prodLineIds.vd = 567989959957302782/567989959957302790/,567989959957302782/567989959957302787/

plugin.public.prodTeam.prodLineIds.gdb = 567989959957302783/567989959957302791/

plugin.public.download.url = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/
plugin.public.zxrdc.upgradeForm.query.url = http://************/TestApi/srv/MOS/Operate/QueryPumAppInfoList

plugin.public.zxrdc.auth.value = KENC(mr7J44Qp7l552XY0I9kptx9cmPY9FRceEz9Rc17ZgSYaO3qAKFRm1/zhz2D/te8w5yZdZg==$$MpgIZ2QktCQk4FpM)

plugin.public.batchTask.view.pageUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&pageStatus=VIEW&instanceDataId=%s&lang=zh&type=app&bizObject=batch_network_assignment#/app/APP0984032412495249408/page/%s
plugin.public.subconBatchTask.view.pageUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&pageStatus=VIEW&instanceDataId=%s&lang=zh&type=app&bizObject=subcontractor_batch_task#/app/APP0984032412495249408/page/PAGE1014466079771930648

plugin.public.client.inet.empNo = JTn7u3WRQCD8fQJ6SmwP55OAxTTS+1INbvPwrbiSD+7I0Zo7

plugin.public.client.inet.authValue = KENC(Bk0kDZ4bFnw08RE/YEPMbcPq8hKTK0z4bwAM8iniYHb4Ee+nR8p53ncJf1tHDmVSiw6unl2FQo0rb+79FehxnNuHNboBQ6IS85SrpNLODB0e7ajf31M2i897a4/6z9OVEyfKzNgY9jQ=$$N7YeJlIBoEwirUTU)

plugin.public.client.inet.device = 1064164951058583552
plugin.public.client.inet.timestamp = 1711680858

plugin.public.rule.data.key.approval.ccnAreaTd.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025234,6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025235

plugin.public.rule.data.key.approval.repOfficeTd.inter.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849
plugin.public.rule.data.key.approval.repOfficeTd.inter.prodIdPath = 6872952355824349360/

plugin.public.rule.data.key.approval.tdNetOffice.inner.negative.prodIdPath = 6872952355824349357/

plugin.public.rule.data.key.approval.tdNetOffice.inter.negative.prodIdPath = 6872952355824349360/
plugin.public.rule.data.key.approval.tdNetOffice.inter.negative.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849

plugin.public.rule.data.key.approval.remoteCenterOwner.prodIdPath = 6872952355824349360/

plugin.public.rule.data.key.approval.testDeptApproval.prodIdPath = 6872952355824349360/6872952355824349368/

plugin.public.rule.data.key.ccn.prodIdPath = 6872952355824349361/
plugin.public.rule.data.key.bn.prodIdPath = 6872952355824349358/
plugin.public.rule.data.key.fm.prodIdPath = 6872952355824349359/

plugin.public.rule.data.key.ran.prodIdPath = 6872952355824349360/
plugin.public.rule.data.key.ran.4g.prodIdPath = 6872952355824349360/6872952355824349368/

plugin.public.rule.data.key.ran.5g.prodIdPath = 6872952355824349360/7188264679487025229/
plugin.public.rule.data.key.ran.mw.prodIdPath = 6872952355824349360/6872952355824349370/
plugin.public.rule.data.key.te.prodIdPath = 6872952355824349357/

plugin.public.rule.data.key.goldendbtech.prodIdPath = 567989959957302783/
plugin.public.rule.data.key.dc.prodIdPath = 567989959957302785/

plugin.public.rule.data.key.vd.prodIdPath = 567989959957302782/
plugin.public.rule.data.key.mmvs.prodIdPath = 6872952355824349359/6872952355824349383/

plugin.public.rule.data.key.domestic.sales.orgCodePath = **********/ORG0002700
plugin.public.rule.data.key.engineering.service.dept.short.orgCodePath = **********
plugin.public.rule.data.key.engineering.service.dept.orgCodePath = **********/**********
plugin.public.rule.data.key.engineering.service.dept.domestic.orgCodePath = **********/**********/ORG2223781

plugin.public.rule.data.key.engineering.service.dept.domestic.short.orgCodePath = ORG2223781
plugin.public.rule.data.key.bn.optical.transmission.prodIdPath = 6872952355824349358/6872952355824349382/
plugin.public.rule.data.key.bn.ip.data.bars.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025235/
plugin.public.rule.data.key.bn.ip.data.switch.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025234/
plugin.public.rule.data.key.ran.4g.fdd.lte.atg.prodIdPath = 6872952355824349360/6872952355824349368/2927813878575293036/

plugin.public.rule.data.key.ran.4g.fdd.sys.prodIdPath = 6872952355824349360/6872952355824349368/2927813878575293039/

plugin.public.rule.data.key.ran.4g.fdd.lte.post.prodIdPath = 6872952355824349360/6872952355824349368/2927813878575293037/

plugin.public.rule.data.key.ran.4g.fdd.td.lte.prodIdPath = 6872952355824349360/6872952355824349368/2927813878575293041/

plugin.public.rule.data.key.ccn.ccn.prodIdPath = 6872952355824349361/6872952355824349365/

plugin.public.rule.data.key.bn.ip.data.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/
plugin.public.rule.data.key.sdi.prodIdPath = 567989959957302781/
plugin.public.rule.data.key.ccn.integr.cloud.prodIdPath = 6872952355824349361/2117235887220158484/2117235887220158498/
plugin.public.rule.data.key.ccn.integr.device.prodIdPath = 6872952355824349361/2117235887220158484/2117235887220158497/
plugin.public.rule.data.key.ccn.integr.app.prodIdPath = 6872952355824349361/2117235887220158484/2117235887220158499/
plugin.public.rule.data.key.ccn.integr.security.prodIdPath = 6872952355824349361/2117235887220158484/2117235887220158495/

plugin.public.rule.data.key.energy.prodIdPath = 567989959957302780/

plugin.public.rule.data.key.zxwt.prodIdPath = 567989959957302784/
plugin.public.rule.data.key.china.code = 0001
plugin.public.rule.data.key.philippines.orgCodePath = **********/**********/**********/ORG2233669

plugin.public.rule.data.key.myanmar.orgCodePath = **********/**********/**********/ORG2223870
plugin.public.rule.data.key.libya.orgCodePath = **********/**********/ORG2223780/ORG2223821
plugin.public.rule.data.key.egypt.orgCodePath = **********/**********/ORG2223780/ORG2227329
plugin.public.rule.data.key.italy.orgCodePath = **********/**********/ORG2223778/ORG2223849
plugin.public.approval.repOfficeTd.inter.countryCode = 0219
plugin.public.approval.tdNetOffice.inter.negative.countryCode = 0219

plugin.APP0984032412495249408.skipValidateSwitch = true
plugin.public.email.host = https://icenter.uat.zte.com.cn/
plugin.public.email.service.name = zte-icenter-notice-message
plugin.public.email.service.url.send = /notice/template/send

plugin.public.email.pkey = KENC(/MAqplB2G4W9yfq6a+yP/vzts6KY/Z6ggLuwQNrA5YLFOsgq2NTpFM2P9y2PJD5RYDR7iAENsRY+JcU/riqOgKWRNXWvVeN8IhxqRg==$$imPMg8SZnU4AVBk3)

plugin.public.email.enable = true

plugin.public.email.appCode = KENC(kd6W/ZWglTB+uGPUlXvBBgRzZujAvOoHSTbu/8CG/hPw/q8ryQ8Y9ereskF12sWY$$0fwU06GkLCYg9Iwv)
plugin.public.service.host = https://itechcloud.test.zte.com.cn/
plugin.public.service.name = zte-iss-bobase-portalui

plugin.public.service.myCharge.suffix.path = /ztem_wait_task

plugin.public.default.approver.key = 10077093
plugin.public.icrm.ai.service.url.templateTransmission.async = /templateTransmission/async
plugin.public.rule.data.key.engineering.service.dept.domestic.network.orgCodePath = **********/**********/ORG2223781/ORG2223820
plugin.public.rule.data.key.ccn.ssp.prodIdPath = 6872952355824349361/6872952355824349387/
plugin.public.rule.data.key.ccn.cloud.ai.prodIdPath = 6872952355824349361/6872952355824349388/
flow.approval.find.person.enabled = false

plugin.public.rule.data.key.ccn.ccn.ims.data.prodIdPath = 6872952355824349361/6872952355824349365/8098010551841013786/

plugin.public.zxrdc.remoteApplyForm.query.url = http://************/TestApi/srv/GDPR/RmcApp/QueryAppDataInfo
plugin.public.rule.data.key.engineering.service.dept.1.orgCodePath = **********/**********/**********
plugin.public.rule.data.key.uzbekistan.orgCodePath=**********/**********/**********/ORG2223862
plugin.public.rule.data.key.engineering.service.dept.2.orgCodePath = **********/**********/ORG2223780
plugin.public.rule.data.key.engineering.service.dept.5.orgCodePath = **********/**********/ORG2223778
plugin.public.rule.data.key.engineering.service.dept.mto.orgCodePath = **********/**********/ORG2223785
# \u56FD\u9645\u8BD5\u70B9\u6253\u5361\u914D\u7F6E\u9879\u76EE
plugin.public.rule.data.key.internation.clockin.orgCodePath=**********/**********/**********/ORG2223862,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223778/ORG2223843,**********/**********/ORG2223778/ORG2226502

# \u503C\u5B88\u6253\u5361\u8F6C\u4EA4\u63A5\u53E3\u5E73\u66FF\uFF0C\u82E5\u4E3Atrue\u4F7F\u7528uac\u65B0\u63A5\u53E3\uFF0C\u5426\u5219\u4F7F\u7528uac\u8001\u63A5\u53E3\u67E5\u8BE2\u8F6C\u4EA4\u4EBA\u5458
plugin.public.clockInTask.queryTransfer.switch=true

plugin.public.icrm.ai.service.url.generate.progress = /itech/doc/process
plugin.public.rule.data.key.ccn.voice.message.prodIdPath = 6872952355824349361/6872952355824349369/
plugin.public.rule.data.key.engineering.service.office.orgCodePath = **********/**********/ORG2223781/ORG2223820
plugin.public.nis.service.url.queryProductClass = /api/productclassifications/tree/service/object

plugin.public.emdm.url.queryAreaInfo = http://configuat.zte.com.cn:8888/esbmule/services/custom/query
plugin.public.emdm.body.sysCode = KENC(xky52r1ch/U5JMIQGHIcw/KGmBzWc/pAZhJV4g==$$2DPRFvJXTgqH4t3W)
plugin.public.emdm.body.userCode = KENC(wd4QAtSn+iaOIOgjPygkBdGUUp0=$$1VpSbAQOQ7amx/VV)

plugin.public.emdm.body.word = KENC(wd4QAtSn+iaOIOgjPygkBdGUUp0=$$1VpSbAQOQ7amx/VV)
plugin.public.assignment.approve.pageUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/processDetail.html?tenantId=10001&flowId=%s&taskId=%s&taskType=APPROVAL#/app/%s/page/%s
plugin.public.assignment.href.iframeUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=app&instanceDataId=%s&pageStatus=%s&openType=newTab#/app/%s/page/%s

plugin.public.assignment.firstOrderCreateTime = 2020-05-15
plugin.public.assignment.maxSubLength = 10

plugin.public.assignment.threadTotal = 5
plugin.public.idop.check.enable = false
plugin.public.idop.host = https://idop.test.zte.com.cn/back/

plugin.public.idop.username = itech_cloud
plugin.public.idop.password = KENC(YSog+CreejgEQrtcIv+kYkbs4FNCSFdeVccSerZeWZwnmiJ3y/fOH7o86VnwepJ1elccMG8jryCEgu5m2j4A6uatwfpSMDqS3aUVWN/2ZB01RZEqeI8b4YBBbtsoCB3SOeoeREvY55olbljhJlvMAAMNj20RpI8igfTdln64NnPW0Cv5zyLkulrV8bW+WuW21MTGuo5sD1uK+CKqVnfo2yZnqPqZ9f2kuMBGLXaMK6K2e+X7+lIl7PmdZEo/whuiOOsFO/8dEsiCs75qYVKoH8xDOrrRmvUMJ1n12pMwlHAEZn2x/f+PJzUX7PPh9q0ePQY9331FIx0ZLHf0n+vICc7gVhB1BJqqIoENRqiRLB5N9lGzqVQpZP409kXkfxZ3mMy7iYtzuW0U/mRTcIBc+r+guNsr635mRGuwjeH9CTlnizqR4q+xNvQbgkgZkgiolU7ABz+xHwLiED+Udhte9d3wNlA4dGnf$$PQ811ZzDBqpiOdlU)
plugin.public.idop.product.line.code = 100000431337,100000100016,NIS001

plugin.public.sysAuth.accessKey[iDOP] = KENC(Gm6ETbatWRF7B3tnpq+cGYes/xKX2aSFHd8MQ06+zONZ+YHQPntktDDXlC/NhBc42XlcNQ==$$iy+CjE46/MC2W7jK)

plugin.public.csc.detail.url = https://csc.test.zte.com.cn:8443/zte-crm-csc-requestservice/common/request/detail

plugin.public.idop.create.tips.page = idop_create_tips
plugin.public.idop.create.page.address = https://idop.test.zte.com.cn/main/operation-center/oc/add

element.status.auto.enable = false
plugin.public.nis.service.url.queryLogicalNe = /api/cnconfig/logicalne/queries
plugin.public.rule.data.key.ccn.integration.devices.prodIdPath = 6872952355824349361/2117235887220158484/
plugin.public.nis.service.url.queryCustomerNe = /api/networks/networkelements/queries
plugin.public.nis.service.url.queryProdModels = /api/networks/%s/products/queries

upp.auth.prod.constraintId = 10830607

upp.auth.org.constraintId = 10741128
plugin.public.rule.data.key.bn.optical.transmission.otn.prodIdPath = 6872952355824349358/6872952355824349382/2927813878575292967/
plugin.public.rule.data.key.bn.optical.transmission.wdn.prodIdPath = 6872952355824349358/6872952355824349382/2927813878575293073/

plugin.public.zxrdc.clockInOption.query.url = http://************/testapi/srv/MOS/OcmINet/clockintasks/{id}/options
plugin.public.zxrdc.clockInTask.query.url = http://************/testapi/srv/MOS/OcmINet/queries
plugin.public.zxrdc.clockInTask.detail.query.url = http://************/testapi/srv/MOS/OcmINet/clockintasks/{id}

plugin.public.zxrdc.clockIn.submit.url = http://************/testapi/srv/MOS/OcmINet/clockintasks/{id}/clocks
plugin.public.zxrdc.clockIn.transfer.url = http://************/testapi/srv/MOS/OcmINet/clockintasks/{id}/owner/{userId}

plugin.public.zxrdc.clockIn.cancel.url = http://************/testapi/srv/MOS/OcmINet/clockintasks/{id}/clocks/latest
plugin.public.upp.role.codes = iTech0001,iTech0002,iTech0003,iTech0004,iTech0005,iTech0006,iTech0007,iTech0008,iTech0009,iTech0010,iTech0011,iTech0012,iTech0013,iTech0014,iTech0015

plugin.public.inone.code = KENC(EarWpp1UXglP3ofz607IPwGeXP4zVOnf6lN2rBOhP5d1qkG44SQUKYJLIdMVKlPG$$q5YpJFaWDeiKSh90)

plugin.public.inone.appName = iEPMS-WEB
plugin.public.inone.supplierName = ZXICCP_iEPMS300

plugin.public.icos.versionEnv = ONLINE
plugin.public.icos.connectTimeOut = 50000
plugin.public.icos.serviceHost = https://icos.dt.zte.com.cn
plugin.public.icos.changeOrder.queryId.apiId = 1061408502700867584
plugin.public.icos.changeOrder.queryAll.apiId = 1061416302965456896

plugin.public.icos.clockInTask.queryId.apiId = 1062405814889775104
plugin.public.icos.clockInTask.queryAll.apiId = 1062421054356488192

plugin.public.icos.netchange.report.domestic.day.apiId = 1109922808777965568
plugin.public.icos.netchange.report.org.day.apiId = 1111979640736481280
plugin.public.icos.netchange.report.org.week.apiId = 1111980225036582912
plugin.public.icos.netchange.report.internal.org.day.detail.apiId = 1111978834784190464
plugin.public.icos.netchange.report.internal.org.threeday.detail.apiId = 1112012243568984064
plugin.public.icos.netchange.report.internal.org.apiId = 1112885093158912000
plugin.public.icos.netchange.report.domestic.clock.prod.detail.apiId = 1123302797560807424
plugin.public.icos.netchange.report.domestic.clock.prod.total.apiId = 1122970045170876416
plugin.public.icos.netchange.report.domestic.clock.org.total.apiId = 1125484468297039872
plugin.public.icos.netchange.report.internal.yesterday.summary.apiId = 1122971059059982336
plugin.public.icos.netchange.report.internal.yesterday.fail.detail.apiId = 1122973932737036288
plugin.public.icos.netchange.report.internal.today.detail.apiId = 1122973188487151616
plugin.public.icos.netchange.report.internal.tomorrow.detail.apiId = 1122972793119473664
plugin.public.icos.netchange.report.global.today.detail.apiId = 1124425437683941376
plugin.public.icos.netchange.report.global.three.day.detail.apiId = 1124819680302759936
plugin.public.icos.netchange.report.global.support.detail.apiId = 1125033154991652864

plugin.public.inter.summary.push.cron = 0 0 12 * * ?
plugin.public.global.prod.line.push.cron = 0 0 7,16 * * ?
plugin.public.clock.in.org.push.cron = 0 0 8 * * ?
plugin.public.global.support.push.cron = 0 0 16 * * ?

plugin.public.fm.cpe.homedict.cpe.cloud.id.path = 4882300824853184533/4882300824853184633/4738190609975747399/3846597978400402689/
plugin.public.fm.mmvs.id.path = 4882300824853184533/4882300824853184636/

plugin.public.inter.summary.push.enable = true
plugin.public.global.prod.line.push.enable = true

plugin.public.india.org.id = **********/**********/**********/**********
plugin.public.india.common.mail = <EMAIL>
plugin.public.clockin.query.url = https://itechcloud.test.zte.com.cn/zte-iss-bobase-portalui/ztem_Check-in
plugin.public.netchange.query.url = https://itechcloud.test.zte.com.cn/zte-iss-bobase-portalui/ztem_NetworkChangeOrders

plugin.public.sysAuth.accessKey[iCOS] = KENC(NYucvdzmkdSSbF7tZGpsS9g2dZYSGvMQ8xIqgAGff/f8j6WjOU+YjAecMgQfVPacABBb+jSvn8ng2VdYDflDUPl1/LNye9y+Bf23np2xV04=$$/dAy/xqF634y17ij)

plugin.public.bn.mstp.itn.prodIds = 6872952355824349358/6872952355824349374/8215025589795999750/,6872952355824349358/6872952355824349374/2927813878575293062/
plugin.public.prodTeam.prodLineIds.tps = 6872952355824349357/6872952355824349371/
plugin.public.default.system.approver.key = 10189993,10077093

plugin.public.clockin.reviews.rectify.flow.handlers = 10031768,10082196
lcap.comment.mobile.sysCode = 204794

plugin.public.assignment.prefixUrl = https://itechcloud.test.zte.com.cn/zte-iss-bobase-portalui/zte-iccp-itech-netchange_test/page/link?code=%s&name=%s&url=%s&isTempTab=true
lcap.app.distribute.emp.whiteList = 10284287,10282740

msa.expose.info.enable = true
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn

msa.security.server.dt-kms.appName = ENC(t0vHEJLYP+4O1Y23MNXrB1jlu5n34vUq6Ur7sdF+kZQDxP1XVt3skx8vkQ==)
msa.security.server.dt-kms.userId = ENC(LFjvrNUMuC6sKSVhzjg9AIczUA1N0mpb71RrF75RXRKdfUXtMyRGsdDwR2A=)
msa.security.server.dt-kms.appSecret = ENC(NpwJ9GAX9U5pt8oZMZapckjFcr114wrVtJV+Rxf5pOR2rUwxtGi9QrbsYRgVlkj7kpK7)

msa.security.server.dt-kms.defaultMasterKey = itechcloud

msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:demoapi:demoapi:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = MjYzNGYyNDEwODMwYTU5MTExYWUzM2Q1M2YxOWMxM2RmMmViODliNWI2ZDRhY2E1OWM0Y2FmYzVkYTVkZjQ4YTNkOTIzODk5NTgxYjYwOGZiOTkwMjIzM2NiMmI1Njgy$$Y2E5OWUzZjM3Y2VlZjA4ZTljNDZlMWQwZWU4YzA5NDY=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3

msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.cipher = NDc0NjU5NmEzZmExNmFmZDE4ZDU3OTdkZGIwNzU4MGRlOTY1OWNlODkxMzQxYTM1MDJmYTc4ZmM4MTVkNjMwNzQ0OGU0YTUxYzk5NjE0NWI3ZTgxMjhiZGYyNDE1MTBj$$ZjBkYTM5NDk1NmVhNTlkYmUwMGRjM2Q3MWMwNDJmZTk=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.sensitiveData = ZTExM2NmNWI0OTcwOTQxOTdhOWU5ZWE1NGRhMDUwYTQ3ZTJiYzkwOTU0MTgwMDUzYTQ0YTdlYTRmZjU1ZTJjMzU5NTlkZTg1YTVmZDIyZmM5ZWExMTMxODM5ZTc3YTVj$$MDZmOTdkODgwMjc5NjZkM2VmNmQ4OWE2MGM3OGQyZjY=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3

jdbc1.username = KENC(Hx248VOuJEW99YSLJYFoMur3ChBanQ==$$QW9rleweS0E8qsZV)
jdbc1.password = KENC(vaYpTvUC1VIOFtieG6gPwK9WWh8lV9rB1e0UXNI=$$a0pE/PWKS7XHd8p9)
lcap.approval.sdk.app.secretKey = KENC(nDfdxTCSWCyBaWVTq/ApHFGFXGsmfurOS+BHfRDFiCu4XkDNtWJeeBO3yB+X/qDypZSi5TkOKS0F7+4SRoyTG22XJnDNbzmYfJYnOLOVjJg=$$58pZSI5bO8psZHlK)
approval.sdk.app.secretKey = KENC(vXcdqwoLm3KK/I8UFJqMOQxak5Jxvq9YfvjsfvjwzNIQMxnPzf8K4yvSSb0nRds23vl5Va3QZ6a0mPrsb/0uU1vvow3H47DNX8vWv9FDSQ4=$$Igrg9QC1k+YMPXy1)
upp.auth.encryptor = KENC(914iTcWpmlV71JJn82IgBUfyPkLWShmy1VtXGZtYHFA5yQPx8feHCU6T+5J9MN4rUphrhYt+JT2ovEJ8qn3UkXIna+kLVyH0W3fmj0vdHxKo6HjvIOZ3yA+8H7zswDI6H0jlmJuEGQdrmaqgaZNRGfsBYzSNvvGtzuogS2HvmjansF/oE820a2vsKyG6A2no62HVoE8inoxZUZL2URFURjcYIDEhZFAYLE054ue1OFffzz4kWeg9C3JGhfynrqRPThii/dlwAmXoQZWe7W8nWEBtkZYA88O1xqzlZGG7l2szBWZgpxRVhw==$$XW4cJiWOSvwQlTb/)

upp.sha256.salt = KENC(sicCcCRre7u1jBXSq/240o0yg+h0EjxKRRVorq18DSPjOB4EEKKZXhSzVbgcSjNa/Tm3RP5a9CpjFBHyTDXhkT5TLpd+Np8qn7oeCGcqii9bwtsi8EgNHkSWaMIyWnX6ZQl80ZkQVZUlr4jLu7I48akpR+ChVbdf9HnmJMQIun2lpMS4oHn14xU4BtjZxJiW$$NEmm9mCkFj3thr8j)
upp.auth.productSecretKey = KENC(K2TJm75S35KaJ6HShKrRjAAuMjd2zrpGEQPRnCtnMJVjyKn3JrGk2UelevX03Ww5$$URFHR4ofpcRzcsCg)

spring.redis.sentinel.password = KENC(D4W8xYQ9BPiTsOBeOVR4VXN4GCi9sv3UexLULsldHjs664+L3fI6jtqZmvTOBKrf/FdY5FEX6OOOka419+pfKn1Nbbm9faByDMTDAtJ2haI=$$JXW5fLe2O6XtwM4e)
spring.redis.password = KENC(ljRL4nhCdkAM6S2nfn74OETeojmPXtdZLZ6I84XG0tXwNDQSBD5cwyBYXVZEbQ==$$TmJKwoAClFXgPe6W)
ucs.accessKey = KENC(sOEvAgj5uqB1yeY57EAjwM4eWvdtmeWTyr40utiKB1mDwHnRWkyFB9NZleCEp6E91L7r8w==$$vLEvBNQwSDHWV6z+)
ucs.secretKey = KENC(g1QFkFkkFV/eDZQ2stKKMcErdWVTvFif9MLGFgs76Uk/58PSItFmS/obCFVsuqf9D5fvUw==$$K7/4lQG8pxDQ7eXw)

cloudDiskSDK.xSecretKey = KENC(fLbZmefUdOPDDu8RLGkeppSjtvL+Btj3cLZ62YaY6g==$$pkx58/7xfi0FeFR/)
cloudDiskSDK.xOrgId = KENC(+rUop6F202PsCFDtL/bakrSj5KYLSXmr1tmofuIUtQ==$$4zf51YP3Q1qHBHAl)

lcap.comment.icenter.type = KENC(EA8QIIpaUyuLR82j9tODmjhBgn9aE1ZRTu1qcUGvgKHd7RtWF9LhR2Zl+S3EYLTJ$$l/1Fsyb1K3aOLgqj)
apicaller.header.appKey = KENC(pjqEiB7j07DmM41GFiCcKGV/T+WZgXf5UO/JKyRJ5OhK/0yo$$sLo/Cxgd8sIWPbJ5)
apicaller.header.secretKey = KENC(NHYdh2e0c/SpZRI+oRMBgl/gvVaSn35GXZHQKTLFz9G+DMkxjQnj/xXJSKQyQk21a8Iu8+nzSmJMWKDgQn5/1E1y35dKrecG7yOJeJcL8mA=$$dZeGI4yL0QO5w1ZI)
inone.appCode = KENC(IM92hMhR9Xo10eTE76UxssddXwomm2NgQKUAuc6vGLfAdxrqNeoJgwUpTnO3srt6$$tjM8mCl/7iXJev3V)
uac.sdk.clientSecret = KENC(i2LKw9wF5PuPkyz6vkn2A9FnW70LsPMAE4LX7+NLcqXYxv54V61cxMGIVr5/M9sxyfa7XmgXPluSyMTk0i4dXd9N5UqGmLataW23bS6rjQ4=$$umgszLkWzQlhU/pz)
uac.sdk.accessKey = KENC(ce8U5NA+Uyek3fX6KjDoJS231AwLxiYZ0/d9bKOlHAu6+h4LsuLw0n+Smw2F2wMi$$I9YCdOMolAYPhlnH)
uac.sdk.accessSecret = KENC(YsBDW2thLtDCKzOdGKYPKILbN9UnFYtcO0Fyy7y6H5GMlTiaEF3GAD3KL9fktS6DxYsp5daJt6U5HtdX/DY2FTfnL9IIAl/w58dINmj4SJE=$$kgD8gy/f6ERUyzaZ)
hcp.accessKey = KENC(qOLNdx62z85CZT6KTz/CXzBCen4=$$ccqLHuVMFpoN79dn)
hcp.secretKey = KENC(lu17wZzcx+clTPGPvr9tF+Hvqql85XCwnTI9Tcao3w4dN0jfMUmfmWbgMH+zXX3v$$gT2m1jaV37k0HN/X)
datasource.config.gcm.encrypt.key = KENC(tNyLSKBdNWJWeTEpvbabTphC1XK1ypPKuT32NZSK6c9fGfr6rA1EsuFJCe+dFQoa$$O+nP6HpfU+Q/l9Ce)
notice.key = KENC(xl4vuJ6+Z8d5kR8RXjHbmHp4Huhe90/TozYQLlOYgzf+IhZzjpbI6/s5HAV2QLLecN+XpcHEsvH/yCgdJGv0JrtQeR7sheQsIy4+fQ==$$26+en/eu9HyomCGS)
uac.key = KENC(gri7wBfp2r4s6utjcCbYj2rOuS9e6D+Vutkr6rwvPos=$$EDX92DT9DkuKh4EC)
approval.sdk.app.rk = KENC(WtuMO4KjSq/oaTyZIp51pL4XPtuB2ksXqcNBx8584+/ULA90+HsYpRaCK4Ffm2+xrQiJQtj5pWYYnGs/VlMxUOjzxJOlREm+bfw7hpw35as2GQJzwausdLdE5uvECEb18VcmCeWVDdg=$$gp+S/G0Y962W/+AY)
common.app.param.secret = KENC(uDFEvheRy2x7jioSWGg2cQbD2P9G7bCzWa/tYW5//NmYLr9b4jJOYWOEbFVjJnPW$$+gL6y6uGEXyMwj63)
uac.sdk.appAuth.encryptKey = KENC(5Cw4hlNWaGvcQoUyN7JhRo9HrK5eIqYSdzLznDuEamc=$$wk3LaLcfDmpEgR78)
uac.sdk.token.encryptKey = KENC(TDrqrRVpyuchgXVsFAOF27ksf1awrep599qekF030F8=$$p7ye9VN1+MrMwRRq)
esConfig.encrypt.key = KENC(R38t6uvn9NCeb/ei4DHi8FF9GblWA6s0rh0YEcMqFLYgwl6drcgyIb56piN0yioKMUnEOEYDuXt1luKcaIg4T2IH+jBSf47DnUNE6QlSeegUOCMGM+tVERxiGHDw2OKgh6ynYgoMPC8=$$1bnw1d7b1LGh8F1p)
uac.sdk.secretKey = KENC(VotBXrUN6/PcvqQBt94T/Y2iQUxbnz82XhSkUqguBLYMeA9lLg6bNcsajZCxH38HdduwaeiJKZ4M2h9Xk/4FpANUUD7U/sVRWjbt/A==$$XTDM5ac2sj6azOAU)
encrypt.property.list = KENC(9NwXjawlDbdov1EXHa8OWRr4zH+dhy6Y6i+z4/M4QdKXAWpKxpmxwYw+xGOoEItjTA==$$3jt1+YYmzui3L5Ao)
lcap.uac.encrypt.key = KENC(ySt+v9wyNzR3bMIbqw8JBSbZZ+705Chrph5tSaeVGh4=$$jCmF0xO4YcJOkXhB)
spring.datasource.druid.stat-view-servlet.login-password = KENC(VaRs8Sfc0XAdLl6+hnD2lkH4Ogw=$$kab3pWZ006wK/wLj)

plugin.public.email.secretKey = KENC(rJyIFgtBsL+LTpZofGEqVfLOkCILLzrK+cKviO5upO8Eet8c9ZorGyc/T+UfuosHNzMiC2/yHzASmCiNqvXjAZP9RAgrV004u5/99C1o4pM=$$J3LJp/rRtFmmApz4)

biz.kit.extend.entity.schema = {"id":"##id##","type":"ExtendEntity","props":{"key":"##key##","name":{"type":"i18n","en_US":"##nameEn##","zh_CN":"##nameCn##"},"extendName":"##extendName##","indexField":[],"queryEngine":false,"createdByBizKit":true,"bizKitCode":"##bizKitCode##","usedByPages":[]},"fields":[],"nodeType":"Entity","parentId":"##parentId##"}

biz.kit.element.template = {"code":"OnboardingKit","icon":"DivOutlined","tags":[],"group":"Layouter","props":[{"name":"writeBack","type":"Boolean","required":true,"description":"","defaultValue":false},{"name":"behavior","type":"Behavior","required":true,"description":"","defaultValue":"NORMAL"},{"name":"rowSpan","type":"Number","description":"","defaultValue":null},{"name":"colSpan","type":"Number","description":"","defaultValue":null},{"name":"dataRewriteTime","type":"Behavior","required":false,"description":"","defaultValue":{}},{"name":"items","type":"object","required":true,"description":"","defaultValue":{}},{"name":"bizRules","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u4E1A\u52A1\u89C4\u5219\u5FEB\u7167","defaultValue":{}},{"name":"pageRules","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u9875\u9762\u89C4\u5219\u5FEB\u7167","defaultValue":{}},{"name":"fields","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u9875\u9762\u5B57\u6BB5\u5FEB\u7167","defaultValue":{}},{"name":"operations","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u64CD\u4F5C\u5FEB\u7167","defaultValue":{}},{"name":"label","type":"I18nExpression","required":true,"description":"","defaultValue":{"en_US":"BizKitEnName","zh_CN":"BizKitCnName"}}],"scope":["page","layout"],"title":{"en_US":"BizKitEnName","zh_CN":"BizKitCnName"},"package":"com.zte.paas.lcap.OnboardingKit","category":"\u4E1A\u52A1\u5957\u4EF6","keywords":[],"priority":"102","configure":{"props":[{"name":"behavior","type":"field","items":[],"scope":["page","layout"],"title":"\u72B6\u6001","setter":{"name":"ChoiceSetter","props":{"mode":"single","options":[{"label":"\u666E\u901A","value":"NORMAL"},{"label":"\u9690\u85CF","value":"HIDDEN"}],"behavior":"NORMAL"}},"display":"inline","condition":"","defaultValue":"NORMAL","supportVariable":false},{"name":"layout","type":"group","items":[{"name":"rowSpan","type":"field","scope":["page","layout"],"title":"\u884C\u95F4\u8DDD","setter":{"name":"SelectSetter","props":{"options":[{"label":"\u8D85\u5927(24px)","value":24},{"label":"\u5927(20px)","value":20},{"label":"\u4E2D(16px)","value":16},{"label":"\u5C0F(12px)","value":12},{"label":"\u8D85\u5C0F(8px)","value":8},{"label":"\u65E0(0px)","value":0}]}},"display":"inline","condition":"","defaultValue":"","supportVariable":false},{"name":"colSpan","type":"field","scope":["page","layout"],"title":"\u5217\u95F4\u8DDD","setter":{"name":"SelectSetter","props":{"options":[{"label":"\u8D85\u5927(24px)","value":24},{"label":"\u5927(20px)","value":20},{"label":"\u4E2D(16px)","value":16},{"label":"\u5C0F(12px)","value":12},{"label":"\u8D85\u5C0F(8px)","value":8},{"label":"\u65E0(0px)","value":0}]}},"display":"inline","condition":"","defaultValue":"","supportVariable":false}],"scope":["page","layout","serviceLayout"],"title":"\u5E03\u5C40","display":"block","supportVariable":false},{"name":"dataRewriteTime","type":"field","tip":"\u5F53\u5355\u636E\u72B6\u6001\u7B49\u4E8E\u6B64\u5904\u5B9A\u4E49\u65F6\uFF0C\u4F1A\u5C06\u5957\u4EF6\u4E2D\u6570\u636E\u56DE\u5199\u5230\u5957\u4EF6\u6240\u5C5E\u539F\u4E1A\u52A1\u5BF9\u8C61\u4E2D","items":[],"scope":["page","layout"],"title":"\u6570\u636E\u56DE\u5199\u65F6\u673A","setter":"DataRewriteSetter","display":"block","supportVariable":false,"condition":{"type":"JSFunction","value":"(field)=> {  return ##WriteBack## }"}},{"name":"items","type":"field","title":"\u5143\u7D20\u8BBE\u7F6E","setter":{"name":"DynamicFieldsSetter","props":{}},"display":"block","supportVariable":false},{"name":"advance","type":"group","items":[{"tip":"\u7531\u5B57\u6BCD\u3001\u4E0B\u5212\u7EBF\u548C\u6570\u5B57\u7EC4\u6210\uFF0C\u4EE5\u5B57\u6BCD\u5F00\u5934\uFF0C\u6700\u957F\u53EF\u8F93\u516564\u4E2A\u5B57\u7B26","name":"cid","type":"field","items":"","scope":["page","layout"],"title":"\u552F\u4E00\u6807\u8BC6","setter":{"name":"StringSetter","props":{"behavior":"NORMAL","maxLength":64,"copyWidget":true}},"display":"inline","condition":"","defaultValue":"","supportVariable":false}],"scope":["page","layout"],"title":"\u9AD8\u7EA7","setter":"","display":"block","supportVariable":false}],"supports":{"style":false},"component":{"isModal":false,"isContainer":false}},"subcategory":"","componentName":"RendererKit"}

biz.kit.sync.value.biz.rule = {"code":"##code##","name":{"type":"i18n","en_US":"SyncValues","zh_CN":"SyncValues"},"draft":false,"boCode":"##bizObjCode##","enabled":true,"category":"businessRule","entryKey":null,"priority":null,"condition":null,"createdBy":null,"ruleUnits":[{"type":"If","actions":[{"type":"Expression","expression":"syncBizKitValue(##bizKeyWithQuotation##)"}],"condition":{"expression":""}},{"type":"Else","actions":[],"condition":{"expression":""}}],"createDate":null,"extensions":null,"description":null,"trueActions":[],"falseActions":null,"descriptionCn":"","descriptionEn":"","isAggregation":false,"lastUpdatedBy":"system","mainEntryRule":true,"triggerEvents":["value_changed"],"currentTrigger":null,"lastUpdateDate":1732001353890,"parentEntryKey":null,"entryEntityRule":false,"runningRuleUnit":null,"dependencyFieldIds":["##bizKey##"],"subEntryEntityRule":false,"valueRangeRuleUnits":null}

menu.link.url.format = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&type=app#/app/{0}/page/{1}
plugin.public.cnop.changeOrder.view.pageUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=zh&type=app&instanceDataId=%s&pageStatus=VIEW&openType=newTab#/app/APP0984032412495249408/page/PAGE1010486823108829185

plugin.public.sysAuth.accessKey[CNOP] = KENC(3oLj7t1qgBf22D9yzf8NkIk1tCVJks1hm4uIEHe4e3vTGBekQ2ceaRYdqZTIzPdUMCvZ5Q==$$14wwJ7cVVpXaBjhh)

empty.value.strace.switch = true
plugin.public.change.network.check.enable = true

plugin.public.rule.data.key.bn.optical.transmission.mstp.prodIdPath = 6872952355824349358/6872952355824349374/8215025589795999750
plugin.public.rule.data.key.bn.optical.transmission.itn.mstp.prodIdPath = 6872952355824349358/6872952355824349374/2927813878575293062/1982225328142573783

# \u4EBA\u5458\u79EF\u5206\u6821\u9A8C\u767D\u540D\u5355\u8D26\u53F7
plugin.public.check.personnel.enable = true
plugin.public.check.personnel.white = 10282740


#\u76EE\u524D\u4F7F\u7528\u7684\u9A71\u52A8\u7248\u672C UME\u65E0\u6CD5\u91C7\u96C6SQL\uFF0C\u9700\u8981\u66F4\u6362\u6570\u636E\u5E93\u9A71\u52A8
lcap.dynamicDatasource.goldenDriverClassName = com.mysql.jdbc.Driver

# \u6392\u9664 upp \u67E5\u8BE2\u5FEB\u7801\u7EA6\u675F\u6570\u636E\u63A5\u53E3(\u6240\u6709\u7684\u73AF\u5883\u90FD\u662F\u540C\u4E00\u4E2A\u63A5\u53E3)\uFF0C\u914D\u7F6E\u9879\u6570\u7EC4\u4E0B\u6807\u6570\u5B57[?]\uFF0C\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u6309\u7167\u987A\u5E8F\u9012\u589E # \u751F\u4EA7\u73AF\u5883\uFF1A
web.interceptor.header-verification.excludePathPatterns[43] = /v1/app/*/fastCodeConstraint
web.interceptor.header-verification.excludePathPatterns[44] = /v1/app/*/fastCodeTreeDataConstraint
web.interceptor.header-verification.excludePathPatterns[45] = /v1/app/*/fastCodeConstraint
web.interceptor.header-verification.excludePathPatterns[46] = /v1/app/*/fastCodeTreeDataConstraint

# \u63A8\u9001\u65E5\u5FD7\u4E2D\u5FC3\u5BA1\u8BA1\u65E5\u5FD7\uFF0C\u9700\u914D\u7F6E\u7CFB\u7EDF\u552F\u4E00\u6807\u8BC6\uFF08pdm\u7F16\u53F7\uFF09\uFF0C\u5176\u4ED6\u7CFB\u7EDF\u914D\u7F6E\u81EA\u5DF1\u7684\u7F16\u53F7
lcap.sub.syscode = 6751046

# \u7F51\u7EDC\u53D8\u66F4\u4EFB\u52A1\u540C\u6B65\u5BFC\u51FA\u4E0A\u9650
plugin.public.change.order.sync.export.limit = 200

# \u662F\u5426\u5F00\u542F\u540C\u65F6\u7F16\u8F91\u6821\u9A8C\u51B2\u7A81\uFF0C\u9ED8\u8BA4\u5F00\u542F\u3002 false\u53D6\u6D88\u3002 \u6D4B\u8BD5\u73AF\u5883\u7EE7\u7EED\u590D\u73B0\u548C\u8DDF\u8FDB\u95EE\u9898
lcap.save.check.conflict=true


#\u5934\u4FE1\u606F\u8FC7\u5927\uFF0C\u5BFC\u81F4\u8BF7\u6C42400\u5F02\u5E38
server.max-http-header-size=40000