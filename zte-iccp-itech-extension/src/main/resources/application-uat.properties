#\u76EE\u524D\u4F7F\u7528\u7684\u9A71\u52A8\u7248\u672C UME\u65E0\u6CD5\u91C7\u96C6SQL\uFF0C\u9700\u8981\u66F4\u6362\u6570\u636E\u5E93\u9A71\u52A8
lcap.dynamicDatasource.goldenDriverClassName=com.mysql.jdbc.Driver

# \u6392\u9664 upp \u67E5\u8BE2\u5FEB\u7801\u7EA6\u675F\u6570\u636E\u63A5\u53E3(\u6240\u6709\u7684\u73AF\u5883\u90FD\u662F\u540C\u4E00\u4E2A\u63A5\u53E3)\uFF0C\u914D\u7F6E\u9879\u6570\u7EC4\u4E0B\u6807\u6570\u5B57[?]\uFF0C\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u6309\u7167\u987A\u5E8F\u9012\u589E # \u751F\u4EA7\u73AF\u5883\uFF1A
web.interceptor.header-verification.excludePathPatterns[43] = /v1/app/*/fastCodeConstraint 
web.interceptor.header-verification.excludePathPatterns[44] = /v1/app/*/fastCodeTreeDataConstraint 
web.interceptor.header-verification.excludePathPatterns[45] = /v1/app/*/fastCodeConstraint 
web.interceptor.header-verification.excludePathPatterns[46] = /v1/app/*/fastCodeTreeDataConstraint 

# \u63A8\u9001\u65E5\u5FD7\u4E2D\u5FC3\u5BA1\u8BA1\u65E5\u5FD7\uFF0C\u9700\u914D\u7F6E\u7CFB\u7EDF\u552F\u4E00\u6807\u8BC6\uFF08pdm\u7F16\u53F7\uFF09\uFF0C\u5176\u4ED6\u7CFB\u7EDF\u914D\u7F6E\u81EA\u5DF1\u7684\u7F16\u53F7
lcap.sub.syscode = 1561334


artifactory.url = https://artsz.zte.com.cn/artifactory/it-release-maven/com/zte/iccp/itech/zte-iccp-itech-extension/1.0-SNAPSHOT/

register.address = msruat.zte.com.cn:10081
register.node.ip = ************
register.node.port = 8888
register.version = v1
servicecenter = msb
apijson.db.version = 5.7.22

apijson.db.schema = lcap_pro_fangzhen_uat

# \u503C\u5B88\u6253\u5361\u8F6C\u4EA4\u63A5\u53E3\u5E73\u66FF\uFF0C\u82E5\u4E3Atrue\u4F7F\u7528uac\u65B0\u63A5\u53E3\uFF0C\u5426\u5219\u4F7F\u7528uac\u8001\u63A5\u53E3\u67E5\u8BE2\u8F6C\u4EA4\u4EBA\u5458
plugin.public.clockInTask.queryTransfer.switch=true

datasource.config.rsa.public-key = KENC(xNSi3sM3PT8tLDJWYcuPZxB++erMtXJ66+e7dZs8++vZiDYBFNrrx3Th4bJE45JrAludIHz098/cTIvxdN8FhKVBmemqAh5PPP63PS6/E3e8C5LNAZvpyRBdmbXIAKNbn/NBZDZJCKBLRHj5CIPDVEAHDPTjtNb2LB+dITnZvBFsJNUxfDG52FRBUEettqq+UAUBUgz9k6rAcV+Kqpd+588UZPHgnrkfxcgqcPGWI6emB1QZaatSgrpktG3qIoetOKnm8YtFuUamhRmQig3RLubdqzyiJtSBGNMbFNW8noGRQ0ezFiV5kQ==$$vK9bLUWOC7EJig0m)

jdbc1.type = com.alibaba.druid.pool.DruidDataSource
jdbc1.driverClassName = com.mysql.jdbc.Driver
jdbc1.url = *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

spring.datasource.druid.stat-view-servlet.enabled = true
spring.datasource.druid.stat-view-servlet.url-pattern = /druid/*
spring.datasource.druid.filter.stat.enabled = true

spring.datasource.druid.filter.stat.db-type = mysql

spring.datasource.druid.filter.stat.log-slow-sql = true
spring.datasource.druid.filter.stat.slow-sql-millis = 500

spring.servlet.multipart.max-file-size = 200MB

spring.servlet.multipart.max-request-size = 200MB

jdbc.maxActive = 100
jdbc.validationQuery = SELECT 1 FROM DUAL

jdbc.testOnBorrow = true
jdbc.testOnReturn = true
jdbc.minEvictableIdleTimeMillis = 600000
jdbc.druid.maxEvictableIdleTimeMillis = 3600000
jdbc.timeBetweenEvictionRunsMillis = 60000

jdbc.removeAbandoned = false
jdbc.poolPreparedStatements = false

jdbc.druid.keepAlive = true

spring.flyway.enabled = false
spring.flyway.encoding = UTF-8
spring.flyway.locations = classpath:db/migration/prod
spring.flyway.sql-migration-prefix = V

spring.flyway.sql-migration-separator = __
spring.flyway.validate-on-migrate = true
spring.flyway.baseline-on-migrate = true
spring.flyway.placeholder-replacement = false


inone.url = https://icosg.uat.zte.com.cn


uac.url = https://uactest.zte.com.cn

uac.sdk.tenantId = 10001
uac.sdk.appId = ************
uac.sdk.issuer = https://uactest.zte.com.cn/zte-sec-uac-iportalbff/oidc/10001
uac.sdk.baseUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff

uac.sdk.clientId = ************




uac.sdk.responseRule = msa
uac.sdk.cookie.secure = true


uac.idType = T0001
# uac.sdk.store = redis
# uac.sdk.env = prod
# uac.sdk.cookie.httpOnly = false

# uac.sdk.gateAuth.externalAccessEnabled = true


dtems.agent.amosBaseUrl = https://ume.uat.zte.com.cn

dtems.agent.applicationInsId = 4302919960120320

dtems.agent.enable = true
dtems.agent.codes.default = 504774601
dtems.agent.moc = cn.dt.business

# emdm.queryUrl = http://configuat.zte.com.cn:8888/esbmule/services/custom/queryPatchGJDQ
# emdm.usercode = dZQtL0T3WIQXlbdGDMDiBA==

ucs.tenantId = 10001

cacheCloud.server = http://mspuat.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4
redis.customCacheManager = true
msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-itech-uat
msa.redis.database = 7
msa.redis.driveType = jedis
msa.redis.serializerType = genericJackson2Json
msa.redis.pool.maxActive = 250
msa.redis.pool.maxIdle = 300
msa.redis.pool.minIdle = 300
# msa.redis.sotimeout = 1000
# msa.redis.maxAttempts = 2
# msa.redis.maxRedirects = 5
# msa.redis.pool.maxWait = 3000
# msa.redis.pool.minEvictableIdleTimeMillis = 1800000
# msa.redis.timeout = 3000

# msa.redis.cacheManager.expire = 86400

spring.redis.sentinel.master = zxiccp-iepms300-itech-uat

spring.redis.sentinel.nodes = *************:36379,*************:36379,*************:36379
cloudDiskSDK.host = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk
cloudDiskSDK.httpClientLogSwitch = true
cloudDiskSDK.xOriginServiceName = EPMS




cos.host.https = https://apigwtest.zte.com.cn:2443


upp.specialDataAuth.url.format = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=page&bizObject=specialDataAuth#/app/%s/page/PAGE0917349422860693510

upp.fieldAuth.url.format = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/dataPermission.html#/upp/%s/roleAuthorization/fieldAuth

upp.auth.productId = 1561334
upp.auth.moduleId = ************
upp.auth.tenantId = 10001


upp.auth.authUrl = https://uactest.zte.com.cn:8082/zte-sec-upp-authbff

upp.auth.manageUrl = https://uactest.zte.com.cn:8082/zte-sec-upp-bff


upp.auth.prod.constraintId = 1830298
upp.auth.org.constraintId = 1830297
# upp.auth.readTimeout = 2000
# upp.auth.connectTimeout = 2000

# upp.auth.cacheMaxSize = 10000
# upp.auth.cacheInitSize = 1000
# upp.auth.maxConnections = 30
# upp.auth.maxPerRoute = 10
# upp.auth.cacheResourceTime = 60


message.server = http://uat.sz-message.zcloud.zte.com.cn:8888/zte-itp-msp-message/v1
message.inoneAppCode = KENC(gewnhTaWFVtfSroa0L8+ewpGjR4LC/oHrAaJGGHUu4jhx/itNInaieB6E2xuAWsN$$7YsA8T8KzYPBh7uV)
spring.kafka.consumer.groupId = zte-iccp-itech-netchange
spring.kafka.enabled = true


approval.kafka.topic = zte-iss-approval-nj
approval.sdk.app.appId = ************
approval.sdk.app.appCode = ************

#approval.sdk.app.rk = ebea7ca618c7d86e11fa3f898b12bb49

approval.sdk.header.xEmpNo.default = iTechCloud
approval.sdk.kafka.receipt.enabled = false
approval.sdk.webhook.url = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/flow/webhookProcess
approval.sdk.use.signatureAlgorithm = SHA256


keypath = http://msptest.zte.com.cn/zte-itp-isoa-security/securityctl/getsk
fileFormat.limit = php,exe,asp,jsp,aspx

sync.interface.suffix = /ZXLCAP/LCAPPRO300/api/v1/sync

lcappro.design.auth.enabled = true

lcap.file.previewUrl = https://idrive.test.zte.com.cn/zte-km-cloududm-docview
lcap.employee.provider = uacEmployeeProvider
lcap.redis.exception.inject.enabled = true

lcap.redis.exception.inject.down = false

lcap.openapi.path.download = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/attachment/temp/download/

lcap.comment.email.address = <EMAIL>
lcap.comment.mobile.sysCode = 204794
lcap.comment.icenter.sysCode = ************

lcap.comment.serviceName = zte-iccp-itech-demoapi


web.interceptor.header-verification.excludePathPatterns[0] = *.js,*.gif,*.jpg,*.png,*.css,*.ico
web.interceptor.header-verification.excludePathPatterns[1] = /swagger-*/**
web.interceptor.header-verification.excludePathPatterns[2] = /v3/api-docs
web.interceptor.header-verification.excludePathPatterns[3] = /v1/app/*/page/*/constraint/*/list

web.interceptor.header-verification.excludePathPatterns[4] = /v1/app/*/model/*/constraint/*/list
web.interceptor.header-verification.excludePathPatterns[5] = /v1/models/formdata

web.interceptor.header-verification.excludePathPatterns[6] = /v1/models/formdata/*
web.interceptor.header-verification.excludePathPatterns[7] = /v1/attachment/tempurl
web.interceptor.header-verification.excludePathPatterns[8] = /v1/attachment/batch/tempurl

web.interceptor.header-verification.excludePathPatterns[9] = /v1/attachment/temp/download/*
web.interceptor.header-verification.excludePathPatterns[10] = /v1/flow/reassignTask


web.interceptor.header-verification.excludePathPatterns[11] = /v1/flow/pageFlowInstance
web.interceptor.header-verification.excludePathPatterns[12] = /v1/flowInstance/getFlowInstanceProcess
web.interceptor.header-verification.excludePathPatterns[13] = /v1/flowInstance/getOperateRecords
web.interceptor.header-verification.excludePathPatterns[14] = /v1/flowInstance/getPageInstanceDataByFlowInstance

web.interceptor.header-verification.excludePathPatterns[15] = /v1/flow/myTaskToDo
web.interceptor.header-verification.excludePathPatterns[16] = /v1/flow/saveFlowInstanceData

web.interceptor.header-verification.excludePathPatterns[17] = /v1/flow/submitTask
web.interceptor.header-verification.excludePathPatterns[18] = /v1/attachmentFile/queryInfo

web.interceptor.header-verification.excludePathPatterns[19] = /v1/attachmentFile/download
web.interceptor.header-verification.excludePathPatterns[20] = /prometheus

web.interceptor.header-verification.excludePathPatterns[21] = /error
web.interceptor.header-verification.excludePathPatterns[22] = /v1/flow/webhookProcess
web.interceptor.header-verification.excludePathPatterns[23] = /v1/bizobject/batchquery

web.interceptor.header-verification.excludePathPatterns[24] = /v1/bizobject/queryentity
web.interceptor.header-verification.excludePathPatterns[25] = /v1/application/batchquery
web.interceptor.header-verification.excludePathPatterns[26] = /v1/app/*/document/download/*



web.interceptor.header-verification.excludePathPatterns[27] = /v1/entryentity/redirect/*
web.interceptor.header-verification.excludePathPatterns[28] = /customComponents/resources/download/**
web.interceptor.header-verification.excludePathPatterns[29] = /v1/attachment/download


web.interceptor.header-verification.excludePathPatterns[30] = /v1/attachment/uploadFile
web.interceptor.header-verification.excludePathPatterns[31] = /components/resource/download

web.interceptor.header-verification.excludePathPatterns[32] = /v1/app/*/file/uncheck/downloadImage

web.interceptor.header-verification.excludePathPatterns[33] = /v1/app/file/uncheck/download

web.interceptor.header-verification.excludePathPatterns[34] = /v1/app/*/bizObj/*/treeDataConstraint
web.interceptor.header-verification.excludePathPatterns[35] = /v1/app/*/bizObj/*/dataConstraint

web.interceptor.header-verification.excludePathPatterns[36] = /zte-iccp-itech-demoapi/customComponents/resources/download/**
web.interceptor.header-verification.excludePathPatterns[37] = /zte-iccp-itech-demoapi/v1/flow/webhookProcess

web.interceptor.header-verification.excludePathPatterns[38] = /APP0984032412495249408/batchtasks/*/4auth
web.interceptor.header-verification.excludePathPatterns[39] = /zte-iccp-itech-demoapi/idop/**
# \u65B0\u589E\u4E0B\u8F7D\u52A0\u5BC6\u6587\u4EF6\u63A5\u53E3\uFF0C\u6DFB\u52A0\u5230\u767D\u540D\u5355\u4E2D
web.interceptor.header-verification.excludePathPatterns[40] = /v1/app/file/uncheck/encryptDownload




springfox.documentation.auto-startup = true
swagger.exclude.url = /v2/api-docs
fastcode.iss.system.code = IEPMS
sync.export.data.limit = 200
entryEntity.import.maxCount = 100

email.address = <EMAIL>

route.url = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/
ruleEngine.impl = inferenceRuleEngine
lcap.metadata.name = RadioField_1666706847500906498,TreeView_1666708164319424512,Button_1666708164319424513,FormContainer_1668511540921364481,FlexContainer_1668511540921364521,Page_1668511540921364522,RootHeader_1668511540921364523,RootContent_1668511540921364524,RootFooter_1668511540921364525,CheckboxField_1669157069015523330,CreatorField_1669159037637271554,ModifierField_1669160258221678594,ColumnsLayout_1669163100164628482,TextField_1669263997930668034,TextareaField_1669264775302971393,NumberField_1669264843577851906,SelectField_1669264989531242498,MultiSelectField_1669265075824852994,TimeRangeField_1669265341731143681,TimeField_1669265453039583233,CascadeDateField_1669265491434242050,DateField_1669265512862941186,AttachmentField_1669266526156455937,EmployeeField_1669267520932118529,DepartmentSelectField_1669267628729925634,Steps_1669267902223712258,Link_1669268060441247745,TablePc_1669268167463108610,FieldLayoutPanel_1669268524767477762,AdvancedContainer_1669269111378640898,TabsLayout_1669269190479020033,ServiceObject_1669269190479021128,ToolbarField_1669269324835160065,Column_1671396272621334529,Tag_1673211663472484353,BillEntity_1674597481403899905,FastCodeField_1674610451040423938,FilterControl_1675305711890087937,BillStatusField_1675305711890087938,BasicDataField_1675305721830084930,BasicDataProps_1675305770033407910,BillNumberField_1675315325835024930,EntryEntity_1677146589633851394,SubEntryEntity_1677152291462983682,MultiFastCodeField_1677590491235467265,ServiceEntity_1679010253945192449,ServiceEntryEntity_1679052327948005378,TableFieldNew_1679310696938815489,CountrySelectField_1679760740479250434,Search_1679760740479250489,TabPanel_1681843769295593473,AssociationList_1681960665247842306,GroupDataField_1682277538857496578,ParentBasicDataField_1683369541332328449,MyReviewField_1686935437505396737,ToolbarItem_1688373444119355393,Prompt_1688726315538108418,EditorField_1688872066648133634,TeamViewPageField_1689878053168603137,CommentField_1690904008878206978,TeamConfigPageField_1690971833915285505,ApproveHistoryField_1691002512976228353,FlowProcessField_1691003592871096322,ExtendEntity_1691063693937098754,ApproveCommunicationField_1691064211279331330,AdvancedContainerToolbar_1692425780766523393,BillTypeField_1693824780483575810,MultiBasicDataField_1694190376775278594,PictureField_1694623101919408129,ServiceObjectProps_1697926811336531969,BusinessObjectProps_1697926453910528002,RichText_1697446694240657410,BusinessObjectField_1697919178500169730,ConditionField_1699715635744354306,ApprovalProgress_1706205948884803585,OperationRecords_1716749328987996162,SubTableField_1716287217534500865,MyReviewTileField_1716718545015562241,FormStatusDiagram_1712444431648636930,FilterProgramme_1726514685908578306,SubButtonItem_1725407107878014978,TextField0113_1729106748838604802,AttachmentDownloader_1742397387786379266,InlinePage_1742891095489077250,FlowChart_1745733431735631873,SwitchField_1745733468167356417,TreeSelectField_1746880575654084610,QrCode_1762315391887990786,MultiBusinessObjectField_1762315391887990996,Breadcrumb_1765568886942736386,SlotComponent_1771435586834993154,Iframe_1782769796791902209,RefComplexCondition_1783127124235382785,AggregationField_1783694852430053378,Card_1792759766927376385,CardContainer_1792759926776496130,CardToolbar_1792760098340306946,MultilingualTextField_1805044544690065409,MultiServiceObject_1724134829869895678,Control_1669268214657445762,StdExtMultiSelectField_1675304721833081930,StdExtSelectField_1669263167465103610,Divider_1724032682457898940,FilterCustom_1726514685908574851,ChangeDifference_1727245121817569854,GridCol_1969269111378640584,GridLayouter_1969269111378684572,GridRow_1969269111378648957,DropdownListField_1669264989531244587,Dialog_1669268524767476832,Filter_1669268524767473747,ValidateContainer_1746880575654483612,Div_1969269111378640897,SelectNewField_1109438420029014016,RadioNewField_1666706847500906499,DepartmentField_1669267628724824854,MultiSelectNewField_1669265075824852995,CheckboxNewField_1669157069015523331

spring.mvc.throw-exception-if-no-handler-found = true
spring.resources.add-mappings = false
lcap.auth.service.register.boListUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/app/bo/pageQuery



rnUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo
dynamic.domain.form.model.expire.time = 28800

dynamic.domain.sub.page.expire.time = 28800
dynamic.domain.operationContext.expire.time = 28800

lcap.ddm.validator.disable = true


dynamic.domain.load.status.mark.header.expire.time = 28800
dynamic.domain.load.table.field.data.expire.time = 28800
lcap.db.open.duplicate = false
lcap.context.path.download = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi
isoalog.kafka.topic = ms-zte-crm-app-nj

lcap.oss.strategy = CloudUdm
lcap.oss.strategy.nas.path = /usr/local/app/lcap_pro/plugins/meta

lcap.oss.strategy.nas.file.max.size = 209715200
lcap.host = itech.uat.zte.com.cn
lcap.host.port = itech.uat.zte.com.cn/zte-iccp-itech-demobff/


encrypt.property.list = datasource.config.gcm.encrypt.key

jetcache.statIntervalMinutes = 60
jetcache.areaInCacheName = false


jetcache.hiddenPackages = com.zte
jetcache.local.default.type = caffeine
jetcache.local.default.keyConvertor = fastjson2


jetcache.local.default.limit = 500

jetcache.local.default.expireAfterAccessInMillis = 0
jetcache.local.default.expireAfterWriteInMillis = 1800000

jetcache.local.otherArea.type = caffeine
jetcache.local.otherArea.keyConvertor = fastjson2


jetcache.local.otherArea.limit = 100
jetcache.local.otherArea.expireAfterAccessInMillis = 0
jetcache.local.otherArea.expireAfterWriteInMillis = 1800000


jetcache.remote.default.type = redis.zlettuce
jetcache.remote.default.keyPrefix = lcap_
jetcache.remote.default.broadcastChannel = defaultDynamicDomainService
jetcache.remote.default.keyConvertor = fastjson2


jetcache.remote.default.valueEncoder = kryo5
jetcache.remote.default.valueDecoder = kryo5

jetcache.remote.default.expireAfterWriteInMillis = 3600000

jetcache.remote.default.uri = redis-sentinel://${spring.redis.password}@${spring.redis.sentinel.nodes}?timeout=10s&sentinelMasterId=${spring.redis.sentinel.master}&database=${redis.uri.database}
jetcache.remote.otherArea.type = redis.zlettuce

jetcache.remote.otherArea.keyPrefix = lcap_
jetcache.remote.otherArea.broadcastChannel = otherDynamicDomainService


jetcache.remote.otherArea.keyConvertor = fastjson2
jetcache.remote.otherArea.valueEncoder = kryo5
jetcache.remote.otherArea.valueDecoder = kryo5

jetcache.remote.otherArea.expireAfterWriteInMillis = 3600000
jetcache.remote.otherArea.uri = redis-sentinel://${spring.redis.password}@${spring.redis.sentinel.nodes}?timeout=10s&sentinelMasterId=${spring.redis.sentinel.master}&database=${redis.uri.database}
redis.uri.database = 1



page.running.url.format = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=app#/app/%s/page/%s


# dt.mockInfo.groupId = demoapi-1


# dt.mockInfo.isMockTest = true

lcap.approval.sdk.app.appId = KENC(k8cyQ2XA8CSOBnv/yduwO9KiuIf28vUikYYYtFJEdO8+3Efr$$jxu13yR+Ef73Z73n)
lcap.approval.sdk.app.appCode = zte-pass-lcap

lcap.flow.update.enable = true
notice.center.base.url = https://icenter-systest.test.zte.com.cn/zte-icenter-notice-message


lcap.app.map = {APP0984032412495249408:"7719680010979405857"}
approval.sdk.request.header.tenantId.source = true



chat.bot.config.roleCode = ai-bot-viewer

chat.bot.config.productId = 2251001
chat.bot.config.moduleId = 3420003
# bff.name = zte-iccp-itech-demobff
lcap.component.strategy = S3

hcp.bucket.non-anonymous = lcap







#plugin
# plugin.APP0984032412495249408.lazyLoadComplexObjSwitch = true

plugin.APP0984032412495249408.skipValidateSwitch = true

plugin.public.baseEntity.enhance = true


plugin.public.upp.role.codes = iTech0001,iTech0002,iTech0003,iTech0004,iTech0005,iTech0006,iTech0007,iTech0008,iTech0009,iTech0010,iTech0011,iTech0012,iTech0013,iTech0014,iTech0015

plugin.public.sysAuth.accessKey[iNet] = KENC(wprPJwPfxhpSEaXtQKNEWXKwajoEJC7swAAGB6KliCAT8RTJpY/pkViUfgJPk+7GrXGU9g==$$WRky5XUc5P4C/Z1w)

plugin.public.sysAuth.accessKey[iDOP] = KENC(zSOtNf88y6FnGlwkOV9R8HAAuKKTGpAHlXsblwIR4cD1bm1zMbrse0sWUwsw5rzurnKzqQ==$$ZJdlbA8upQl3BFvi)

plugin.public.sysAuth.accessKey[CNOP] = KENC(3n67F/****************************************************************==$$PW0aoT4g3WaHd2Yf)


plugin.public.ccn.prodIdPath = 6872952355824349361/

plugin.public.bn.prodIdPath = 6872952355824349358/
plugin.public.fm.prodIdPath = 6872952355824349359/
plugin.public.mmvs.prodIdPath = 567989959957302782/567989959957302787/

plugin.public.ran.prodIdPath = 6872952355824349360/
plugin.public.te.prodIdPath = 6872952355824349357/
plugin.public.goldendbtech.prodIdPath = 567989959957302783/


plugin.public.vd.prodIdPath = 567989959957302782/
plugin.public.dc.prodIdPath = 567989959957302785/
plugin.public.prodTeam.prodLineIds.ran = 6872952355824349360/6872952355824349368/,6872952355824349360/7188264679487025229/
plugin.public.prodTeam.prodLineIds.mw = 6872952355824349360/6872952355824349370/

plugin.public.prodTeam.prodLineIds.wantong = 567989959957302784/567989959957302792/
plugin.public.prodTeam.prodLineIds.jinyi = 567989959957302779/567989959957302788/

plugin.public.prodTeam.prodLineIds.ids = 2594612964948697239/2594612964948697240/
plugin.public.prodTeam.prodLineIds.fm = 6872952355824349359/6872952355824349385/,6872952355824349359/6872952355824349376/,6872952355824349359/6872952355824349379/
plugin.public.prodTeam.prodLineIds.bn = 6872952355824349358/6872952355824349382/,6872952355824349358/6872952355824349384/,6872952355824349358/6872952355824349374/

plugin.public.prodTeam.prodLineIds.dc = 567989959957302785/567989959957302789/
plugin.public.prodTeam.prodLineIds.sdi = 567989959957302781/567989959957302794/
plugin.public.prodTeam.prodLineIds.ccn = 6872952355824349361/6872952355824349365/,6872952355824349361/6872952355824349369/,6872952355824349361/6872952355824349387/,6872952355824349361/6872952355824349388/


plugin.public.prodTeam.prodLineIds.te = 6872952355824349357/6872952355824349371/,6872952355824349357/7188264679487025228/
plugin.public.prodTeam.prodLineIds.energy = 567989959957302780/567989959957302795/
plugin.public.prodTeam.prodLineIds.vd = 567989959957302782/567989959957302790/,567989959957302782/567989959957302787/

plugin.public.prodTeam.prodLineIds.gdb = 567989959957302783/567989959957302791/
plugin.public.approval.ccnAreaTd.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025234,6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025235


plugin.public.approval.repOfficeTd.inter.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849
plugin.public.approval.repOfficeTd.inter.countryCode = 0219
plugin.public.approval.repOfficeTd.inter.prodIdPath = 6872952355824349360/



plugin.public.approval.tdNetOffice.inner.negative.prodIdPath = 6872952355824349357/
plugin.public.approval.tdNetOffice.inter.negative.prodIdPath = 6872952355824349360/
plugin.public.approval.tdNetOffice.inter.negative.countryCode = 0219

plugin.public.approval.tdNetOffice.inter.negative.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849
plugin.public.approval.remoteCenterOwner.prodIdPath = 6872952355824349360/,6872952355824349361/
plugin.public.approval.testDeptApproval.prodIdPath = 6872952355824349360/6872952355824349368/
plugin.public.grantFile.secretKey.rsaPriKey = MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCSBG0HGfyIs+tMar9t6thZH3vfT3l8LQ16xddE4DqLn9EXOT4WgMkpLNY+K017MMCLROOGdO/1+XYRBCKrDU/LmbJ9atG8b7h0Zz5aYj+FECveFLPF0QyUIN6/rvatG6eCm9vHqKi/wSSmaaY5dQ3HZkazJnQw71QOS55SE81GVFrxiVaHDCka5COXG8Lekf+DmwffSTJ0EGJimGOCOJH13siTyNogOiNeo7jnPeDvv7dU3geaUs64b3x7f/DQpvWcSXzaJg9Yn37LL0k5aeTnmBO2iZbPoKkVMQXsWbUb9gJ/qS66PuKejvOwKfc56lVA23+COOCCHOGAGqkTmA0XAgMBAAECggEAV7io93c5K7dLSOFol9RanVA4zvOj/GHkkzmAFjTPd0pQBy+wiTxdnYr+OuMw7S1gv0pnlkNyVcfEknx1h2W75okznf0qe5ZyrhvIbexQVNReRlERn2nzsJn+uBGYyG6wt9qCsvQ0TN7v8bnOzv3vhTeAieg0LaHaUaxENPMIg5MId3AIJqqC27ZhSvc3XzoSWANOn8PnIGwA1D8kJemgwDxr47Y6hv59SbGTeRwmVdZKKmLfVgzPX4Hpbs1zUH7Bog+GZdTE+usleClcettlurNQh664YBaOnzRWjkKCZCs+Fy3uDMglpkw22VNdQI47xGGnYVZ5CavckBqQi/0lAQKBgQDWQmywT5ghMSh/3B0HHm0kv4eaXpQH7D0jYyfDZTFZ1iAVUmwyrTryl2mHXcxuNfX6+1hFEsYg56xbS1i9j2xFfRSF1M9n5XMY4fppipOUif+axgRzTI0nvlWeqKrqbEqIHrMTqKsZJXtsOUvNpV9sdbmw8p9dSX1uTJFHKBIytwKBgQCudp5bAk0I1bl5bqGbvpL2r3DHz0nWQu9NCmpoKaF1E04pTmeC0k+N8FqXoM0UQCSb3GPBe7BTvx7G4e1lXDZh/ND8ZVCMxHLaJY2wI2hE1I0IpF9fzpx/3xHGfXC7TEFgGDdGTYnIs+vH7dDHAlpm1r+ggbmkuA7Nm0N3xsMYoQKBgQCIXl6vGPnKpm8QchhHn6X37a9xISE2qbWAAGwegRmYBCEHcR5MYtVR8ezIq3qh6tZUmATmcbyI1Otub9ZbUB2pXKLjSz928SMAg1POONUVhX+aEs5iVCbDVPFJujCOY75MRL3VlVTQ/HpBt3X3vXmunn9BkCq+RTgAK9uLXoW2oQKBgQChD1TFw+wGGfiuSBndxCO8pN5DN/R8XzK4NsNhAf3SLU5dB8q7lRhoetnHXWW2KjkhwmY9esifp7Lr/CrDBkwWTeVI/OdN7RXymfSiBS3/TRIeRlDeC8miqvan/aoS3yUDZ5evyWlH5gHngn7ZGi0G/33M2bXegST5Xzr72nzFwQKBgD1pqKO9JPR70xj2jbsiYTI/OnffCYXxZ4xVem7Vqs1LZBz0kCQlqeLaB3VUNFiMX2d+kT3XFUkQzpcnmVrxKh66j8uWUKOFgUFvEUQyM5tzA57QqxSmHK09egWuWizoGGc+ah8XiPKoTPIfvHjz5S1PN9cHOivyBpewOLOsrKhR
plugin.public.grantFile.secretKey.rsaPubKey = KENC(57B+FM8MvX/VR2FycbUCUx92Z8vwDm6sPdxYwnlS12VlJHPiXPTZpAk7/p+y/WKp++1gt5338Pp7sf+zVgoT0Uum1oFrDyBdU4xaVnE9Fey84rMG9CZJ/PYovXaJ7/TlwkmjEIu/Ew4BmqIbVdW+XJo4L4kuAdG4ejdv/PLPDbqr61SOQ3GGgpivR4P8mI/FonlHs7EjdHilVH0TJqqHvLhJahLPEeWrifGNf8tV/SCuAudFLNfnOsSyvtaYU52K4+DCePNzCR9kQ2/eZ2JRG2ccmoGhEfzuXzu506RoIGMeQ2Bp649CBQI6NC3nb5XLFlDrv64pVIXNNKypjBSYMzR8G2nsdulvA8hFQHoOCl6GVS0asrzwGC7CYggqVZ7Vzkl9IowGZ07iDpJoPoRQOkVxlFOIqEEIUsbYHz23mki0hbDNv1HcaqIdrBxJ19P7FH2n8eFzjnn+hAu+nK15+6PbfautsEtxY9MIJRxBGrYJhtlPnUj8x446zBLdqAC6kk6v5dbGqws1zAbO0Qz5jkVjTT4Rl7fQ$$EzTYLO540OmsXBTx)
plugin.public.grantFile.downloadPageUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=zh&type=app&bizObject=null&sa=%s&entityType=%s&bizId=%s#/app/APP0984032412495249408/page/download_grant_file

plugin.public.cces.host = https://cces.zte.com.cn:9119/

plugin.public.cces.callings.url = api/aicall/callings
plugin.public.cces.callresult.url = api/aicall/callresult
plugin.public.cces.clientId = KENC(KvJ8KR1fOdmhn4f4zUfBzeUYbgl2eXM+FOt6croOfd4DORreca0SU7+Pg1OOD4Ec5D8z8Q==$$lt5Nx17qJ6A1xr1n)
plugin.public.cces.clientSecret = KENC(mT+OYEBTWb+rPmAGeEnn90L8iaLK1qFYqbFjfNDMjFFdR7plSI2ZmvlMUNYn90E1vfOfEQ==$$sfovp/XD14a9puiH)
plugin.public.cces.white.telphone = ***********,***********,***********,***********,***********,***********,***********,***********
plugin.public.crm.host = http://test.ssc.zte.com.cn:8888/

plugin.public.crm.service.name = zte-crm-account-info
plugin.public.crm.service.url.queryAccount = /selectAccountPageByKeyWords/v2
plugin.public.crm.service.url.getCustomerDetails = /noPermisonAccount

plugin.public.crm.service.url.querySimpleInfo = /api/customer/simple/info
plugin.public.crm.service.url.queryCustomerInfoList = /ZXISS_SS300/ACCOUNT_INFO/zte-crm-account-info/api/customer/batchDetail/v2
plugin.public.hr.host = https://ecsuat.zte.com.cn/

plugin.public.hr.service.name.person = zte-hrm-usercenter-pginfo

plugin.public.hr.service.name.organization = zte-hrm-usercenter-orginfo
plugin.public.hr.service.url.conditionQueryOrganizationInfo = /ihol/usercenter/orginfo/usercenter/queryOrgInfo
plugin.public.hr.service.url.queryOrganizationInfo = /ihol/usercenter/orginfo/usercenter/getOrgInfo
plugin.public.hr.service.url.queryPersonGeneralInfo = /ihol/usercenter/pginfo/usercenter/plain/getPersonGeneralInfo
plugin.public.hr.enable.service.url.queryCertificate = /enable-bff/srv/engineer/certificate/query
plugin.public.nis.host = http://************


plugin.public.nis.service.name = /zte-iccp-iepms-nis
plugin.public.gtdCenter.service.name = /zte-iccp-iepms-gtdcenter
plugin.public.nis.service.url.queryProductionTree = /api/productclassifications/tree

plugin.public.nis.service.url.queryOrganizationTree = /api/organizations/tree

plugin.public.nis.service.url.queryNetworks = /api/networks/queries
plugin.public.nis.service.url.header.xItpValue.secretKey = KENC(R2yYeSlG5+3vwWmOxaAfIgxkUuEplnlG9n+Ae3RyszZzv/mAVn4cvwikn6XiswsNhsOvN0QOPGB+an+hEcCYjoPwwFuxdtWzdFlx9Jr9mjg=$$usp3h2kJnZqD1Dwv)

plugin.public.nis.service.url.queryProductModel = /api/productmodels/queries
plugin.public.nis.service.url.queryCustomerNe = /api/networks/networkelements/queries
plugin.public.nis.service.url.queryProdModels = /api/networks/%s/products/queries

plugin.public.nis.service.url.queryCustomerFlag = /api/customer/queryCustomerFlag
plugin.public.nis.service.url.queryProductInfo = /api/productclassifications/selectbyidpath
plugin.public.nis.service.url.queryProductClass = /api/productclassifications/tree/service/object
plugin.public.nis.service.url.queryLogicalNe = /api/cnconfig/logicalne/queries

plugin.public.icrm.ai.host = https://boassistant.uat.zte.com.cn/
plugin.public.icrm.ai.service.name = zte-doc-ai-assistant
plugin.public.icrm.ai.service.url.templateTransmission = /templateTransmission

plugin.public.icrm.ai.service.url.templateTransmission.async = /templateTransmission/async
plugin.public.icrm.ai.service.url.generate = /itech/doc/generate
plugin.public.icrm.ai.service.url.generate.progress = /itech/doc/process

plugin.public.iVersion.host = https://iversion.test.zte.com.cn
plugin.public.iVersion.service.name = zte-plm-iversion-api
plugin.public.iVersion.url.getVersionInfoAndCorrelationInfo = https://iversion.test.zte.com.cn/zte-plm-iversion-api/publish/versionOutgo/getReleaseVersionInfo

plugin.public.iVersion.url.getBasicAndIncrementVerByCondition = https://iversion.test.zte.com.cn/zte-plm-iversion-api/publish/getBasicAndIncrementVerByCondition



plugin.public.clouddisk.url.sendPartData = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/file/sendPartData
plugin.public.clouddisk.url.getDownloadToken = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadToken
plugin.public.clouddisk.url.downloadByToken = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/{appCode}/{token}
plugin.public.clouddisk.url.queryFileStatus = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/file/queryFileStatus
plugin.public.clouddisk.url.finishPartObject = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/finishPartObject
plugin.public.clouddisk.url.queryFileInfo = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/queryFileInfo

plugin.public.pdm.auth.value = KENC(icEOGFS9iu4+jl7CuuoPoZlE/PWds3pG4OvwE74zcry57yDvTUdbBo1RQg1ezmJK$$Ol9y4pxBwZEH3BDZ)
plugin.public.pdm.url.queryProductInfoByLevel = https://pdmweb.test.zte.com.cn/zte-plm-iproduct-category/category/v1/query
plugin.public.pdm.url.queryProductInfoByLinkage = https://pdmweb.test.zte.com.cn/zte-plm-iproduct-category/relation/v1/query

plugin.public.zxrdc.upgradeForm.query.url = http://************/TestApi/srv/MOS/Operate/QueryPumAppInfoList
plugin.public.zxrdc.remoteApplyForm.query.url = http://************/TestApi/srv/GDPR/RmcApp/QueryAppDataInfo
plugin.public.zxrdc.clockInOption.query.url = http://************/TestApi/srv/MOS/OcmINet/clockintasks/{id}/options
plugin.public.zxrdc.clockInTask.query.url = http://************/TestApi/srv/MOS/OcmINet/queries
plugin.public.zxrdc.clockInTask.detail.query.url = http://************/TestApi/srv/MOS/OcmINet/clockintasks/{id}
plugin.public.zxrdc.clockIn.submit.url = http://************/TestApi/srv/MOS/OcmINet/clockintasks/{id}/clocks
plugin.public.zxrdc.clockIn.transfer.url = http://************/TestApi/srv/MOS/OcmINet/clockintasks/{id}/owner/{userId}

plugin.public.zxrdc.clockIn.cancel.url = http://************/TestApi/srv/MOS/OcmINet/clockintasks/{id}/clocks/latest
plugin.public.zxrdc.auth.value =  KENC(pCD/Q88N8Pz+0LE5rgmMK4Q9BuVPAXaJYt/3ThAPA9XiMJm36QzZWbXMhcTegPyg/D0efA==$$sAEOm0vCDm/MYUOR)
plugin.public.csc.detail.url = https://csc.uat.zte.com.cn:8443/zte-crm-csc-requestservice/common/request/detail
plugin.public.ucs.keyword.accessKey = KENC(paGNhLUCJtqsbwzVLsDUrQ8mmLa8oidaVE8dEwKpEx7sqNLzbct1rXAs7hHMWjnnRZ0eJw==$$3KyP8lrDvnvgMr3g)
plugin.public.ucs.keyword.secretKey = KENC(UnkqmopDQ8ooJol/jx6B33ZvbHxg3M6WmmYNnhLWvZQi1gGaWKHqAfm4w664jlMVfTLZAQ==$$OFqFp5DpKqy48KnQ)
plugin.public.ucs.keyword.appId = 10015

plugin.public.ucs.keyword.tenantId = 10001
plugin.public.ucs.host = http://test55.ucs.zte.com.cn:8888/
plugin.public.ucs.service.name = zte-bmt-ucs-api

plugin.public.ucs.url.queryAllUserInfo = /srv/v2/account/queryall
plugin.public.emdm.url.queryAreaInfo = http://configuat.zte.com.cn:8888/esbmule/services/custom/query
plugin.public.emdm.body.sysCode = KENC(yc/zw7jWruYfkdXKqQaCTg7Lv5IsFgziWKEpjA==$$drr22235OZtm9U2L)
plugin.public.emdm.body.userCode = KENC(M0FOz7cjLAu1PMTfNTjrT6lTfDo=$$oudJNgU/Uqqq32IV)

plugin.public.emdm.body.word = KENC(M0FOz7cjLAu1PMTfNTjrT6lTfDo=$$oudJNgU/Uqqq32IV)

plugin.public.batchTask.view.pageUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&pageStatus=VIEW&instanceDataId=%s&lang=zh&type=app&bizObject=batch_network_assignment#/app/APP0984032412495249408/page/PAGE1011221276299563009

plugin.public.subconBatchTask.view.pageUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&pageStatus=VIEW&instanceDataId=%s&lang=zh&type=app&bizObject=subcontractor_batch_task#/app/APP0984032412495249408/page/PAGE1014466079771930648
plugin.public.client.inet.empNo = JTn7u3WRQCD8fQJ6SmwP55OAxTTS+1INbvPwrbiSD+7I0Zo7
plugin.public.client.inet.authValue = KENC(nf7Dj9pnlzMVxGwjqWJ9JHcYazlIIxhl3wDTijwBttRtzDuGm4h0gbc6ieSoimkvoWLYH3TnBC/z+FX2+ayg8rmzcLVTCA+P+TPtS3ZvIWb5uvZC+DiDVO8NYrZzDocQQ1GtOBoz9Rs=$$hOKY4Vb5zwUntCrL)
plugin.public.client.inet.device = 1064164951058583552


plugin.public.client.inet.timestamp = 1711680858
plugin.public.rule.data.key.approval.ccnAreaTd.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025234,6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025235
plugin.public.rule.data.key.approval.repOfficeTd.inter.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849
plugin.public.rule.data.key.approval.repOfficeTd.inter.prodIdPath = 6872952355824349360/
plugin.public.rule.data.key.approval.tdNetOffice.inner.negative.prodIdPath = 6872952355824349357/
plugin.public.rule.data.key.approval.tdNetOffice.inter.negative.prodIdPath = 6872952355824349360/
plugin.public.rule.data.key.approval.tdNetOffice.inter.negative.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849

plugin.public.rule.data.key.approval.remoteCenterOwner.prodIdPath = 6872952355824349360/
plugin.public.rule.data.key.approval.testDeptApproval.prodIdPath = 6872952355824349360/6872952355824349368/
plugin.public.rule.data.key.ccn.prodIdPath = 6872952355824349361/
plugin.public.rule.data.key.bn.prodIdPath = 6872952355824349358/
plugin.public.rule.data.key.fm.prodIdPath = 6872952355824349359/

plugin.public.rule.data.key.ran.prodIdPath = 6872952355824349360/
plugin.public.rule.data.key.ran.4g.prodIdPath = 6872952355824349360/6872952355824349368/
plugin.public.rule.data.key.ran.5g.prodIdPath = 6872952355824349360/7188264679487025229/

plugin.public.rule.data.key.ran.mw.prodIdPath = 6872952355824349360/6872952355824349370/
plugin.public.rule.data.key.te.prodIdPath = 6872952355824349357/

plugin.public.rule.data.key.goldendbtech.prodIdPath = 567989959957302783/
plugin.public.rule.data.key.dc.prodIdPath = 567989959957302785/
plugin.public.rule.data.key.vd.prodIdPath = 567989959957302782/
plugin.public.rule.data.key.mmvs.prodIdPath = 567989959957302782/567989959957302787/
plugin.public.rule.data.key.domestic.sales.orgCodePath = **********/ORG0002700

plugin.public.rule.data.key.engineering.service.dept.short.orgCodePath = **********
plugin.public.rule.data.key.engineering.service.dept.orgCodePath = **********/**********

plugin.public.rule.data.key.engineering.service.dept.1.orgCodePath = **********/**********/**********
plugin.public.rule.data.key.engineering.service.dept.2.orgCodePath = **********/**********/ORG2223780
plugin.public.rule.data.key.engineering.service.dept.5.orgCodePath = **********/**********/ORG2223778

#\u4E4C\u5179\u522B\u514B\u5DE5\u670D\u5904
plugin.public.rule.data.key.uzbekistan.orgCodePath=**********/**********/**********/ORG2223862
plugin.public.rule.data.key.internation.clockin.orgCodePath=**********/**********/**********/ORG2223862,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223778/ORG2223843,**********/**********/ORG2223778/ORG2226502

plugin.public.rule.data.key.engineering.service.dept.mto.orgCodePath = **********/**********/ORG2223785
plugin.public.rule.data.key.engineering.service.dept.domestic.orgCodePath = **********/**********/ORG2223781
plugin.public.rule.data.key.engineering.service.dept.domestic.short.orgCodePath = ORG2223781

plugin.public.rule.data.key.engineering.service.dept.domestic.network.orgCodePath = **********/**********/ORG2223781/ORG2223820
plugin.public.rule.data.key.bn.optical.transmission.prodIdPath = 6872952355824349358/6872952355824349382/
plugin.public.rule.data.key.bn.ip.data.bars.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025235/
plugin.public.rule.data.key.bn.ip.data.switch.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/7188264679487025234/
plugin.public.rule.data.key.ran.4g.fdd.lte.atg.prodIdPath = 6872952355824349360/6872952355824349368/2927813878575293036/
plugin.public.rule.data.key.ran.4g.fdd.sys.prodIdPath = 6872952355824349360/6872952355824349368/2927813878575293039/
plugin.public.rule.data.key.ran.4g.fdd.lte.post.prodIdPath = 6872952355824349360/6872952355824349368/2927813878575293037/
plugin.public.rule.data.key.ran.4g.fdd.td.lte.prodIdPath = 6872952355824349360/6872952355824349368/2927813878575293041/
plugin.public.rule.data.key.ccn.ccn.prodIdPath = 6872952355824349361/6872952355824349365/
plugin.public.rule.data.key.ccn.ssp.prodIdPath = 6872952355824349361/6872952355824349387/
plugin.public.rule.data.key.ccn.cloud.ai.prodIdPath = 6872952355824349361/6872952355824349388/

plugin.public.rule.data.key.ccn.integration.devices.prodIdPath = 6872952355824349361/2117235887220158484/

plugin.public.rule.data.key.ccn.integr.cloud.prodIdPath = 6872952355824349361/2117235887220158484/2117235887220158498/
plugin.public.rule.data.key.ccn.integr.device.prodIdPath = 6872952355824349361/2117235887220158484/2117235887220158497/
plugin.public.rule.data.key.ccn.integr.app.prodIdPath = 6872952355824349361/2117235887220158484/2117235887220158499/
plugin.public.rule.data.key.ccn.integr.security.prodIdPath = 6872952355824349361/2117235887220158484/2117235887220158495/
plugin.public.rule.data.key.ccn.voice.message.prodIdPath = 6872952355824349361/6872952355824349369/
plugin.public.rule.data.key.ccn.ccn.ims.data.prodIdPath = 6872952355824349361/6872952355824349365/8098010551841013786/
plugin.public.rule.data.key.bn.ip.data.prodIdPath = 6872952355824349358/6872952355824349384/8215025589795999751/
plugin.public.rule.data.key.sdi.prodIdPath = 567989959957302781/
plugin.public.rule.data.key.energy.prodIdPath = 567989959957302780/
plugin.public.rule.data.key.zxwt.prodIdPath = 567989959957302784/

plugin.public.rule.data.key.china.code = 0001

plugin.public.rule.data.key.engineering.service.office.orgCodePath = **********/**********/ORG2223781/ORG2223820
plugin.public.rule.data.key.philippines.orgCodePath = **********/**********/**********/ORG2233669
plugin.public.rule.data.key.myanmar.orgCodePath = **********/**********/**********/ORG2223870

plugin.public.rule.data.key.libya.orgCodePath = **********/**********/ORG2223780/ORG2223821

plugin.public.rule.data.key.egypt.orgCodePath = **********/**********/ORG2223780/ORG2227329
plugin.public.rule.data.key.italy.orgCodePath = **********/**********/ORG2223778/ORG2223849
plugin.public.email.host = https://icenter.uat.zte.com.cn/
plugin.public.email.service.name = zte-icenter-notice-message
plugin.public.email.service.url.send = /notice/template/send

plugin.public.email.pkey = KENC(yWOb/ahjCKvmfqKmnJyc/oyHzHgI/J9B8ru3A/xk9k7GqcJegyh3+7cV/Cf6kjIQ3J8KGwtIaBcUgflCRgU2wOb1629CqZGkk5QK3g==$$/Pc2cDPIee3NpDE+)
plugin.public.email.enable = true
plugin.public.email.appCode = KENC(7hRM5A1GDY8hGAK2ThCbRaJKi+fgqlDqBo/E0r39MvAWgjEIV+kuFmanUBnzE63i$$ha8QDkwiS1SHzkZJ)

plugin.public.service.host = https://itech.uat.zte.com.cn/
plugin.public.service.name = zte-iss-bobase-portalui
plugin.public.service.myCharge.suffix.path = /ztem_wait_task
#TODO \u5F85\u6B63\u5F0F\u4F7F\u7528\u9020\u4E86\u6570\u636E\u540E\u518D\u8C03\u6574\uFF0C\u5148\u590D\u752855\u7684\u914D\u7F6E
plugin.public.assignment.firstOrderCreateTime = 2020-05-15
plugin.public.assignment.maxSubLength = 10
plugin.public.assignment.approve.pageUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/processDetail.html?tenantId=10001&flowId=%s&taskId=%s&taskType=APPROVAL#/app/%s/page/%s
plugin.public.assignment.href.iframeUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=app&instanceDataId=%s&pageStatus=%s&openType=newTab#/app/%s/page/%s
plugin.public.assignment.prefixUrl = https://itech.uat.zte.com.cn/zte-iss-bobase-portalui/ztea_************/page/link?code=%s&name=%s&url=%s&isTempTab=true
plugin.public.assignment.threadTotal = 5
plugin.public.default.approver.key = 10189993
plugin.public.idop.check.enable = false
plugin.public.idop.host = https://idop.test.zte.com.cn/back/
plugin.public.idop.username = itech_cloud
plugin.public.idop.password = KENC(V0b4X4qqWVKA/82rbEIOrrCUS7ZxupY4dPnWHHL+vGxasKEBlCeR0WfviFDFsFBEUyE3l4d0eKD8gIBG45028K+pp63e2ZuHfJm1OsI58wn8mxJtQqAiVNccPtO+f9//1z57Vh+AKFlnYZ0IWTiAxAxzz+qnX3vmNVk93njexrO4hhhE1w1pOWa7+9q7rM+9BQQVQfVEZ/pNPm+nDQQh0uX/6buijgMFBRtlYi2+oQAOllQxtM1d7ZwlEY4TL4BF80LJoWp3Q3woCfORUtAzaab+ALv5BiDZ634W5suHXsFPrEyaHljfTNpbbE9v7OBr6cz0W37GUP6wAAx0oyY53EcablVc7t5ICeWEMU3QeOayZK5Q5uvTadJ5wGWFKxlOA7gB8yJeFANjSnsv1ZzU7KseFVYEbL3gWlXGmXgKYkpGJ7eQf6WT8WcvQ0whbDz/PFDbk0CV0OJwwk+069KFZMGPZemo4X1s$$npDHoSAgJ+kDa2RP)
plugin.public.idop.product.line.code = 100000431337,100000100016,NIS001
plugin.public.idop.create.tips.page = idop_create_tips
plugin.public.idop.create.page.address = https://idop.test.zte.com.cn/main/operation-center/oc/add
plugin.public.rule.data.key.bn.optical.transmission.wdn.prodIdPath = 6872952355824349358/6872952355824349382/2927813878575293073/

plugin.public.rule.data.key.bn.optical.transmission.otn.prodIdPath = 6872952355824349358/6872952355824349382/2927813878575292967/
plugin.public.download.url = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/
spring.datasource.druid.stat-view-servlet.login-username = root
spring.flyway.out-of-order = true
alarm.code.operation = 171612669
alarm.businessId = 699328029902536704
upp.auth.appListConstraintCode = app_list
upp.auth.appListConstraintId = 2970006
query.distinct.switch = false
plugin.public.pro.appid = ZXISS_IAPPROVAL001
plugin.public.iss.review.approve.approveTeamConfigCid = approveTeamConfigCid
plugin.public.iss.review.approve.approveResultCid = approveResultCid
plugin.public.iss.review.approve.resultYkey = pass
plugin.public.iss.review.approve.resultNkey = noPass
plugin.public.iss.review.approve.resultCkey = cpass
plugin.public.iss.review.approve.resultRejectkey = refuse
alarm.code.rule = 565279899
alarm.code.privilege = 702085763
alarm.code.pluginLoading = **********
jdbc.minIdle = 50
jdbc.initialSize = 50
lcap.auth.mode = lcap
entryBillEntity.import.maxCount = 50000
lcappro.plugin.sysCode = pro
lcap.mc.service.name = zte-iccp-itech-promcapi
lcap.promgrapi.version = v2
lcap.mc.version.no = v2
debug.code.flag = flase
management.endpoint.health.enabled = false

lcap.save.operation = true
http.socketTimeout = 30000
basic.tree.fuzzy.query.page.size = 20
plugin.public.authTask.enabled = true
plugin.public.clockInTask.enabled = true
plugin.public.grantFile.downloadApiUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/APP0984032412495249408/grantfiles?sa=%s&entityType=%s&bizId=%s

flow.approval.find.person.enabled = false
element.status.auto.enable = false
plugin.public.inone.code = KENC(88SsPoQ4FmMdNdzmaPkLh4gS27WnKmiIxY6LwHrpBDAuOKXlsbRPlfryL/O//g53$$3rsCjJ+dSOHajSww)
plugin.public.inone.appName = iEPMS-WEB

plugin.public.inone.supplierName = ZXICCP_iEPMS300
plugin.public.icos.versionEnv = ONLINE
plugin.public.icos.connectTimeOut = 50000
plugin.public.icos.serviceHost = https://icos.dt.zte.com.cn
plugin.public.icos.changeOrder.queryId.apiId = 1061408502700867584

plugin.public.icos.changeOrder.queryAll.apiId = 1061416302965456896
plugin.public.icos.clockInTask.queryId.apiId = 1062405814889775104
plugin.public.icos.clockInTask.queryAll.apiId = 1062421054356488192
plugin.public.sysAuth.accessKey[iCOS] = KENC(Sf/luSq2icgwmDOvKR9va09BKQ3XAkU7i7yb3mlJKCccxabserKhkJFtDcok4fkXzZuzp+oMSgilh2rfE+tujkYx33xIEQBmUx77t/31ruo=$$t5V+LHXhds9bkF4x)
plugin.public.bn.mstp.itn.prodIds = 6872952355824349358/6872952355824349374/8215025589795999750/,6872952355824349358/6872952355824349374/2927813878575293062/
plugin.public.prodTeam.prodLineIds.tps = 6872952355824349357/6872952355824349371/
plugin.public.default.system.approver.key = 10189993,10077093
plugin.public.clockin.reviews.rectify.flow.handlers = 10031768,10082196


# \u5E94\u7528\u5206\u53D1\u64CD\u4F5C\u767D\u540D\u5355\uFF0C\u591A\u4E2A\u5DE5\u53F7\u4F7F\u7528\u82F1\u6587\u9017\u53F7\u5206\u9694(\u975E\u5FC5\u586B\uFF0C\u4E0D\u6D89\u53CA\u76F8\u5173\u529F\u80FD\u53EF\u4E0D\u6DFB\u52A0)\uFF0C\u5E94\u7528\u5206\u53D1\u529F\u80FD\u4ECB\u7ECD:https://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/a33077fb62aa490ea726f3dd95c7cabd/view
lcap.app.distribute.emp.whiteList = 10284287,10282740

# \u5065\u5EB7\u68C0\u67E5info\u63A5\u53E3
msa.expose.info.enable = true

#msa\u52A0\u5BC6
msa.encrypt.datakey = EywBJm/1SoRyQ30fRkdcrBJ5HNLqH2e4jIeTV6SKTxCubB5rf+YnIgdt3TWWvarAHP2uQr97emzxUH1PcQwIjw==
msa.rootkey.factor1 = zte-iccp-itech-demoapi-uat
msa.ccs.encrypt.factor = ENC(7PVXXT7ofmTaxGQbqsaXKGsfQbO++SsYGyI59cLQwhDlM4mwch346p5FfGXsdMw07FyEG5DYR5I=)

#kms\u76F8\u5173\u52A0\u5BC6\u53C2\u6570\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn
msa.security.server.dt-kms.appName = ENC(MWySMJVhEB7jMd0xhpqcIi6CwO8C9ZjNBuBJ47C1n2jlPu0XZJQGH2a5Yw==)
msa.security.server.dt-kms.userId = ENC(9i8cemkbT2JaPw/sTLZnQI+qvfueGZ679zZsPOrVESLiV4vbA3V2l+raG7o=)
msa.security.server.dt-kms.appSecret = ENC(u1LQ91GKo6I2YZ6Etf9yJUB0nPkda3FpcOiR5fOpHu/7fGH5TqIMMf8hUsoYa+5C3d4W)
msa.security.server.dt-kms.defaultMasterKey = itechcloud

#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:demoapi:demoapi:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = MThjNjFmMzNiNmRjYjU3MTQ2OTVjYjliMDUyNTI1ZTY4MjEwNDhjNDQ0NWE0MzA4ZDQ0NzhmM2RhMmQ4Zjk1Y2E5NjdlNzIxYzgxN2U5NzNhOGMwOGQ2YjQ3YWU4OTA1$$NzExMTAxZDBjYjFiNTU2ZDk2MGExYjBlY2M0Zjc3MDE=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3

msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.cipher = YWQ5ZTNkNWQxYWQwMzVlOGFjYzc5ZDc0ZTUxMTAyODU3YzgxZmU5MTYwMzllYWVlZDFlOGViMWJlNTZiMmQ5OGRiZDJlYTMyYzQ4NWZiZTczMDQxMjVkNTg3YjdhNTk4$$MzgxNzkzYzJiNDg2NGMxNmQ5NTUyZGFjYmNmNjA0MzY=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.sensitiveData = YWJlZDY5MzM5NzNlYWZlMzFhODJhNjgxOGFkODI2NTAyM2ZjMGNhMmM2ZmFhZGM2NmJmNjA1Y2NjMTRjMTBlMzZhOWIxMjJmM2MzNTM3MDhmMjY3OWVmMTIzOGQwNmVl$$NzQ4ZjMwMGJiMzZlZTExYmNmYzRlZDQ5ZGFjMDExYmY=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3

# \u4EE5\u4E0B\u53C2\u6570\u9700\u91CD\u65B0\u7528kms\u52A0\u5BC6
jdbc1.username = KENC(sslefgA0ldLbhUL/y7f38nZ6aeAvSg==$$ckV65fQyrN9e7YFf)

jdbc1.password = KENC(TsYwfnHIx54h5dC7Wwauz60Oovyuy1SQ0SoQYyY=$$/lGHiDL2YzLYwX6X)

lcap.approval.sdk.app.secretKey = KENC(T6HW7c89sIeiAuLDm7OMCPkjI3Ym5KptsN4H0zNUINGO9aKXSpndc15QeULQO/DNsa2mMl6wU/aaWbVh7ZAg0MNvT1HuKgqF4VJH2CWb/KM=$$+rkWxmyrfRSZ/nw7)

approval.sdk.app.secretKey = KENC(T3JNIr/HRucx7966mBnvuO4whAYeHetyD5rj2D7xFyHKjkt8eQjgIIe3DcP2s0dtw+Vmk2xn2EJxDKb3UJxqgqzv/Kmk+aTn++e3mVXyESE=$$ve93O9o/P5tBIDao)
upp.auth.encryptor = KENC(SLNdWxx78GkkUOcjgZwocrX/2PfkZS8MwfF649bb6qREf0vjIw8CONIdQ/M0jNZWPOKhhMMvGEgxGDK+LyMzAHPjOHXXFqnp4Ktp+TTsIrQ3YPy3gCTTVxjOL3gjVsFNuD+PaXlpvc7qC3BBEW/X8UQZc7WWgRwKVCJCD53uA3G/0tn8LWy7eMqN5s+drsZYDursrctjO0t/it2s9t03CemMZcd+sIQyVA1H/MsdTo42RIqr5lhVbX5fg+mAnuceOFn8Xhi4G9TU8AqBMUAQAJRj330i+3WdKWaKY9HLENeB6+YFXyVxUg==$$z7oCHQW7pD1pevSm)

upp.sha256.salt = KENC(petTMp9MzBL4p3Ao83mDKP1WgqT56BK2xiIWsP5T6CcJu8QIfeGA1uCQU/AzsPJC8AjyUm5eUH57D3G/o9f9KJlzN0uehi8eApRLMFwr8hyl7hyO+BmGgI35uYMO6dN98IHpRDQ5fI8KvYvMj0zCLhginbTa1qOEdRZCNYZLSkY/9Zmqrh52tBWUOmbZtKGS$$AGg6SjYUY/q+Vohs)
upp.auth.productSecretKey = KENC(oxOyLSQknHzbyE1KUebSqZwTP9rnBTkSJKzWIWcLDz74uZ4IEsGC2lOWQTovpxdM$$HpzHe94ZUbgUyjri)

spring.redis.sentinel.password = KENC(QpYSXgjLoM9eAmQ/XN227XjZze3CH3AxJNBSt4QDckRLtswWmkH7JQJJNQcz0kE2j5u5DfFKT6XrWfT3sQvSnDO0hsy8pQ+bFZjhEQ/GYEA=$$3RTP38mkWcwGy+v9)
spring.redis.password = KENC(NtYBfbSYb3JyA1iPvLCrZ9WqHmECaRP36cLjKQB6PtUXJPJLRsdH1aOeoM8hBg==$$lDe4JhJRw6vCMcel)

ucs.accessKey = KENC(wTKRJarqYlsStzl/VE4yb9VT+k5+zEb5WYR5tC9rKujI+gv9uyBV5RLQc4sqe6ZYC8fZmw==$$XXgKjjnVUHwSIUsH)
ucs.secretKey = KENC(tdjZ2ip6hiSUR6Cid410f/1w8gleVh4XMfXw5MU7IE86S9MxkBmdt/wQUFSz7d6GGU+bbg==$$okVsgCAi9zlhFvOM)

cloudDiskSDK.xSecretKey = KENC(TLN4JtyXscyLVQ9P17e3MYlvh+cSaV6pAA5dVZu5gQ==$$AA4DjTD2v9BpRS52)

cloudDiskSDK.xOrgId = KENC(yIDwYCaMAVI07qXmar48P8vHZ0i5cz8wf8PV2/TXJQ==$$ArrqwkwydpQsu9Lp)

lcap.comment.icenter.type = KENC(wVXffuyYrEgv4Y9Rnv8iGK3K20SPNJ4aq9mg39m6ysw9HqIIsMIrZI8H/YdTjqK5$$nIxlCuf2ZAckpL83)

apicaller.header.appKey = KENC(krymNuVxFs2yt7CzypLN/mCgTs5Pe0HVMq/YS812kIOrPjUh$$iwptjDJdMvsVzprm)

apicaller.header.secretKey = KENC(0n8XXgGG8358b+j+JSLl4oaTODTftNlYkJWi1E1OoyUVCAVU3iJGKPoLCIcWFuzPO9F8jAt9ZNGuVjpx5NCDQathvUmzIy4WE6sQfBQCX6I=$$P8vKSSKHAWPyr/8M)

inone.appCode = KENC(cVmi8aXp3SmTaFDnOtNgQyG1R0tkFxQpfLdiqIgEaLp6ODQ1CNsV1iDXbYirfT4U$$p88w5otJn/P5LTq9)

uac.sdk.clientSecret = KENC(nhl9qGECwex7V972lDUWxMV9eao/woDvr1grmwcYGT0ZakHna6V2DzjiUFN5RSU103lqH2VNcD6qe+yCUpHB7nA7g4MaNhvuprwSwkph8SQ=$$//Tov7FT1LuyFFOl)

uac.sdk.accessKey = KENC(gkwjC/PEWOYwEuBCPzOn+KyrmSoGbKguqb06WCf+DmYFEMJT/YygFUtCKu6tkHef$$qJJ/N+Ei1LRdF0vW)
uac.sdk.accessSecret = KENC(EyurNn/gCFU2JAkrbvBQaflRPMdKwkNwvV0ZB9HHV85J91bDVQwFGf5ttbucQVzOBRjhALLkXMwAdV6zwmSWKufAWe1PpE7H75bX7GHGRYY=$$IJADoR2TUSUQpkhi)

uac.key = KENC(4qOH9QLFvPyiFHTl+JJfFLBNqW4TbOHvPlVvq9osOCg=$$ossioxRIQxb6sRco)

approval.sdk.app.rk = KENC(4iQZ1inx5y/lmhdT8PQudfEPMxaG4J9sDfaZOZ5hfWOJFQcEgw4zuYCue1dH9ORy$$ioOP+N8FxAklsD1+)

uac.sdk.appAuth.encryptKey = KENC(qjpFbZAaPECdCI4ULLy3dQ0XaU5bMBJrYQpuQ98GS5M=$$Hd90LzyFzyTu7XX3)
uac.sdk.token.encryptKey = KENC(4JnRkr+eJuaLVJ8VGHvB0H6r/ImjdnOUfTkTlU0rcDQ=$$8kh+sgKqzpbRwWnk)

esConfig.encrypt.key = KENC(rILhL7ywy7l7V43ifjQ83xQ5dbkz2GbIKRYKLtPfHwYy8LXm4w5tfHBkeQ9RZDgp1QYTpCcvLuQb5oXqFPV+jX8HSAo6vao7hr4RHB0TeAJaUyx7GLPjzTXXX9ob5B8AXTx+/OkpKkU=$$zpBuPIYy0VaOS5Vg)

uac.sdk.secretKey = KENC(xw7SQisnG4vH7TPPTvsBYfoHw0aOiCxE98Ab2Vs0IVAEqSjvqSnOCCI5LbTnB/XW$$twfDBkSbGrV5f5jX)

lcap.uac.encrypt.key = KENC(XA4Zr0CR9BDlsm6/8pjcG2NeCMmCDNxEL13hD7XxLoU=$$2p/iHSeACavzzP3X)
spring.datasource.druid.stat-view-servlet.login-password = KENC(OJQiM+eZqmSd9j67lW8+mlYX18c=$$+et22hMHxfOjY+HV)

plugin.public.email.secretKey = KENC(ZnUS4HwzRIyrmYUOFr5JRT/fRPpRahx6XSBs3LWQ6Hny9fsfI+45y3pXr1q/eLZmDN2wBYuXqOVJxUEyV2xIpnsDS0IacPaLdYj5nwHM75c=$$h+3AxfqU8/urXyT7)


#\u4E1A\u52A1\u5957\u4EF6\u914D\u7F6E
biz.kit.extend.entity.schema = {"id":"##id##","type":"ExtendEntity","props":{"key":"##key##","name":{"type":"i18n","en_US":"##nameEn##","zh_CN":"##nameCn##"},"extendName":"##extendName##","indexField":[],"queryEngine":false,"createdByBizKit":true,"bizKitCode":"##bizKitCode##","usedByPages":[]},"fields":[],"nodeType":"Entity","parentId":"##parentId##"}
biz.kit.element.template = {"code":"OnboardingKit","icon":"DivOutlined","tags":[],"group":"Layouter","props":[{"name":"writeBack","type":"Boolean","required":true,"description":"","defaultValue":false},{"name":"behavior","type":"Behavior","required":true,"description":"","defaultValue":"NORMAL"},{"name":"rowSpan","type":"Number","description":"","defaultValue":null},{"name":"colSpan","type":"Number","description":"","defaultValue":null},{"name":"dataRewriteTime","type":"Behavior","required":false,"description":"","defaultValue":{}},{"name":"items","type":"object","required":true,"description":"","defaultValue":{}},{"name":"bizRules","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u4E1A\u52A1\u89C4\u5219\u5FEB\u7167","defaultValue":{}},{"name":"pageRules","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u9875\u9762\u89C4\u5219\u5FEB\u7167","defaultValue":{}},{"name":"fields","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u9875\u9762\u5B57\u6BB5\u5FEB\u7167","defaultValue":{}},{"name":"operations","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u64CD\u4F5C\u5FEB\u7167","defaultValue":{}},{"name":"label","type":"I18nExpression","required":true,"description":"","defaultValue":{"en_US":"BizKitEnName","zh_CN":"BizKitCnName"}}],"scope":["page","layout"],"title":{"en_US":"BizKitEnName","zh_CN":"BizKitCnName"},"package":"com.zte.paas.lcap.OnboardingKit","category":"\u4E1A\u52A1\u5957\u4EF6","keywords":[],"priority":"102","configure":{"props":[{"name":"behavior","type":"field","items":[],"scope":["page","layout"],"title":"\u72B6\u6001","setter":{"name":"ChoiceSetter","props":{"mode":"single","options":[{"label":"\u666E\u901A","value":"NORMAL"},{"label":"\u9690\u85CF","value":"HIDDEN"}],"behavior":"NORMAL"}},"display":"inline","condition":"","defaultValue":"NORMAL","supportVariable":false},{"name":"layout","type":"group","items":[{"name":"rowSpan","type":"field","scope":["page","layout"],"title":"\u884C\u95F4\u8DDD","setter":{"name":"SelectSetter","props":{"options":[{"label":"\u8D85\u5927(24px)","value":24},{"label":"\u5927(20px)","value":20},{"label":"\u4E2D(16px)","value":16},{"label":"\u5C0F(12px)","value":12},{"label":"\u8D85\u5C0F(8px)","value":8},{"label":"\u65E0(0px)","value":0}]}},"display":"inline","condition":"","defaultValue":"","supportVariable":false},{"name":"colSpan","type":"field","scope":["page","layout"],"title":"\u5217\u95F4\u8DDD","setter":{"name":"SelectSetter","props":{"options":[{"label":"\u8D85\u5927(24px)","value":24},{"label":"\u5927(20px)","value":20},{"label":"\u4E2D(16px)","value":16},{"label":"\u5C0F(12px)","value":12},{"label":"\u8D85\u5C0F(8px)","value":8},{"label":"\u65E0(0px)","value":0}]}},"display":"inline","condition":"","defaultValue":"","supportVariable":false}],"scope":["page","layout","serviceLayout"],"title":"\u5E03\u5C40","display":"block","supportVariable":false},{"name":"dataRewriteTime","type":"field","tip":"\u5F53\u5355\u636E\u72B6\u6001\u7B49\u4E8E\u6B64\u5904\u5B9A\u4E49\u65F6\uFF0C\u4F1A\u5C06\u5957\u4EF6\u4E2D\u6570\u636E\u56DE\u5199\u5230\u5957\u4EF6\u6240\u5C5E\u539F\u4E1A\u52A1\u5BF9\u8C61\u4E2D","items":[],"scope":["page","layout"],"title":"\u6570\u636E\u56DE\u5199\u65F6\u673A","setter":"DataRewriteSetter","display":"block","supportVariable":false,"condition":{"type":"JSFunction","value":"(field)=> {  return ##WriteBack## }"}},{"name":"items","type":"field","title":"\u5143\u7D20\u8BBE\u7F6E","setter":{"name":"DynamicFieldsSetter","props":{}},"display":"block","supportVariable":false},{"name":"advance","type":"group","items":[{"tip":"\u7531\u5B57\u6BCD\u3001\u4E0B\u5212\u7EBF\u548C\u6570\u5B57\u7EC4\u6210\uFF0C\u4EE5\u5B57\u6BCD\u5F00\u5934\uFF0C\u6700\u957F\u53EF\u8F93\u516564\u4E2A\u5B57\u7B26","name":"cid","type":"field","items":"","scope":["page","layout"],"title":"\u552F\u4E00\u6807\u8BC6","setter":{"name":"StringSetter","props":{"behavior":"NORMAL","maxLength":64,"copyWidget":true}},"display":"inline","condition":"","defaultValue":"","supportVariable":false}],"scope":["page","layout"],"title":"\u9AD8\u7EA7","setter":"","display":"block","supportVariable":false}],"supports":{"style":false},"component":{"isModal":false,"isContainer":false}},"subcategory":"","componentName":"RendererKit"}
biz.kit.sync.value.biz.rule = {"code":"##code##","name":{"type":"i18n","en_US":"SyncValues","zh_CN":"SyncValues"},"draft":false,"boCode":"##bizObjCode##","enabled":true,"category":"businessRule","entryKey":null,"priority":null,"condition":null,"createdBy":null,"ruleUnits":[{"type":"If","actions":[{"type":"Expression","expression":"syncBizKitValue(##bizKeyWithQuotation##)"}],"condition":{"expression":""}},{"type":"Else","actions":[],"condition":{"expression":""}}],"createDate":null,"extensions":null,"description":null,"trueActions":[],"falseActions":null,"descriptionCn":"","descriptionEn":"","isAggregation":false,"lastUpdatedBy":"system","mainEntryRule":true,"triggerEvents":["value_changed"],"currentTrigger":null,"lastUpdateDate":1732001353890,"parentEntryKey":null,"entryEntityRule":false,"runningRuleUnit":null,"dependencyFieldIds":["##bizKey##"],"subEntryEntityRule":false,"valueRangeRuleUnits":null}

# \u83DC\u5355\u7BA1\u7406 \u5185\u90E8\u9875\u9762\u94FE\u63A5url(\u4E0D\u540C\u73AF\u5883\u9700\u66FF\u6362\u57DF\u540D\u4E3A\u5F53\u524D\u73AF\u5883\u57DF\u540D\uFF0C\u72EC\u7ACB\u90E8\u7F72\u7684\u5E94\u7528\u9700\u66FF\u6362zte-paas-lcap-frontendrenderdemo\u4E3A\u81EA\u8EAB\u670D\u52A1\u540D)
menu.link.url.format = https://iepms.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&type=app#/app/{0}/page/{1}

common.app.param.secret = KENC(uDFEvheRy2x7jioSWGg2cQbD2P9G7bCzWa/tYW5//NmYLr9b4jJOYWOEbFVjJnPW$$+gL6y6uGEXyMwj63)
datasource.config.gcm.encrypt.key = KENC(5p1h7ZwV0z1Bs78hRnUtNRFXJFAMUFQqS+tcEwRoKCa4ldTH02+si3MYGii1R0up$$Z9uap7I170vMwj9K)
hcp.accessKey = KENC(nDJ4EkrsqHwknlWvqu2BzqjpSbU=$$Vbc7nrRtAAMDnwvr)
hcp.secretKey = KENC(t3rGFHp536XxlxkFRS/maJv+t686yYw5IIF5qlX3KuJcApkvjMJhPcRH6ZgYtInO$$609Ph1v/EkxOZYXP)
notice.key = KENC(X7oaCoiyY8sSR5/jstfIDKZvZkOlUVKiIWhLHopsjIzaoOJFdSye1oIMrtzkyDAUbcqz7hifAoD2s0rKAnRDndruwQyWTMgsrZxvjg==$$YkNExTCa/79yb/3C)

plugin.public.change.network.check.enable = true

plugin.public.rule.data.key.bn.optical.transmission.mstp.prodIdPath = 6872952355824349358/6872952355824349374/8215025589795999750
plugin.public.rule.data.key.bn.optical.transmission.itn.mstp.prodIdPath = 6872952355824349358/6872952355824349374/2927813878575293062/1982225328142573783

uac.sdk.cookie.httpOnly = false
plugin_load_type = Nas
plugin.public.cnop.changeOrder.view.pageUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=zh&type=app&instanceDataId=%s&pageStatus=VIEW&openType=newTab#/app/APP0984032412495249408/page/PAGE1010486823108829185
web.interceptor.header-verification.excludePathPatterns[41] = /zte-iccp-itech-demoapi/cnop/**
web.interceptor.header-verification.excludePathPatterns[42] = /zte-iccp-itech-demoapi/common/**

plugin.public.icos.netchange.report.domestic.day.apiId = 1109922808777965568
plugin.public.icos.netchange.report.org.day.apiId = 1111979640736481280
plugin.public.icos.netchange.report.org.week.apiId = 1111980225036582912
plugin.public.icos.netchange.report.internal.org.day.detail.apiId = 1111978834784190464
plugin.public.icos.netchange.report.internal.org.threeday.detail.apiId = 1112012243568984064
plugin.public.icos.netchange.report.internal.org.apiId = 1112885093158912000
plugin.public.icos.netchange.report.domestic.clock.prod.detail.apiId = 1123302797560807424
plugin.public.icos.netchange.report.domestic.clock.prod.total.apiId = 1122970045170876416
plugin.public.icos.netchange.report.domestic.clock.org.total.apiId = 1125484468297039872
plugin.public.icos.netchange.report.internal.yesterday.summary.apiId = 1122971059059982336
plugin.public.icos.netchange.report.internal.yesterday.fail.detail.apiId = 1122973932737036288
plugin.public.icos.netchange.report.internal.today.detail.apiId = 1122973188487151616
plugin.public.icos.netchange.report.internal.tomorrow.detail.apiId = 1122972793119473664
plugin.public.icos.netchange.report.global.today.detail.apiId = 1124425437683941376
plugin.public.icos.netchange.report.global.three.day.detail.apiId = 1124819680302759936
plugin.public.icos.netchange.report.global.support.detail.apiId = 1125033154991652864

plugin.public.inter.summary.push.cron = 0 0 12 * * ?
plugin.public.global.prod.line.push.cron = 0 0 7,16 * * ?
plugin.public.clock.in.org.push.cron = 0 0 8 * * ?
plugin.public.global.support.push.cron = 0 0 16 * * ?

plugin.public.fm.cpe.homedict.cpe.cloud.id.path = 4882300824853184533/4882300824853184633/4738190609975747399/3846597978400402689/
plugin.public.fm.mmvs.id.path = 4882300824853184533/4882300824853184636/

plugin.public.inter.summary.push.enable = true
plugin.public.global.prod.line.push.enable = true

plugin.public.india.org.id = **********/**********/**********/**********
plugin.public.india.common.mail = <EMAIL>

empty.value.strace.switch = true
plugin.public.clockin.query.url = https://itech.uat.zte.com.cn/zte-iss-bobase-portalui/ztem_Check-in
plugin.public.rule.data.key.cloudpc.prodIdPath = 8701575113589604354/
plugin.public.rule.data.key.zjt.prodIdPath = 567989959957302788/
plugin.public.netchange.query.url = https://itech.uat.zte.com.cn/zte-iss-bobase-portalui/ztem_NetworkChangeOrders

# \u4EBA\u5458\u79EF\u5206\u6821\u9A8C\u767D\u540D\u5355\u8D26\u53F7
plugin.public.check.personnel.enable = true
plugin.public.check.personnel.white = 10282740

# \u7F51\u7EDC\u53D8\u66F4\u4EFB\u52A1\u540C\u6B65\u5BFC\u51FA\u4E0A\u9650
plugin.public.change.order.sync.export.limit = 200

# \u662F\u5426\u5F00\u542F\u540C\u65F6\u7F16\u8F91\u6821\u9A8C\u51B2\u7A81\uFF0C\u9ED8\u8BA4\u5F00\u542F\u3002 false\u53D6\u6D88\u3002
lcap.save.check.conflict=false

#\u5934\u4FE1\u606F\u8FC7\u5927\uFF0C\u5BFC\u81F4\u8BF7\u6C42400\u5F02\u5E38
server.max-http-header-size=40000

# \u662F\u5426\u5F00\u542F\u670D\u52A1\u542F\u52A8kms\u89E3\u5BC6\u6821\u9A8C\uFF0C\u9ED8\u8BA4\u5F00\u542F\u5982\u679C\u6709\u52A0\u5BC6\u9519\u8BEF\u4F1A\u5BFC\u81F4\u670D\u52A1\u542F\u52A8\u5931\u8D25\u3002\u6700\u7EC8\u8981\u6253\u5F00\uFF0C\u5148\u6392\u67E5\u52A0\u5BC6\u95EE\u9898\u3002
msa.encrypt.jasypt.properties-check.enabled=false