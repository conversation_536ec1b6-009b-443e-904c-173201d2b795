package com.zte.iccp.itech.extension.domain.constant.entity.clockin.config;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/09/14
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OperationLvlOdDurationFreqConfigFieldConsts {
    public static final String OPERATION_LEVEL = "operation_level";

    public static final String TOTAL_DURATION_INNER = "total_duration_inner";

    public static final String TOTAL_DURATION_INTER = "total_duration_inter";

    public static final String FREQUENCY_INNER = "frequency_inner";

    public static final String FREQUENCY_INTER = "frequency_inter";
}
