package com.zte.iccp.itech.extension.domain.model.vo;

import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

@ApiModel("页面表格展示对象")
@Setter
@Getter
public class TableDisplayRows<T> {

    @ApiModelProperty("当前页")
    private Long current = CommonConstants.DEFAULT_PAGE_NO.longValue();

    @ApiModelProperty("总数")
    private Long total = CommonConstants.DEFAULT_EMPTY_TOTAL.longValue();

    @ApiModelProperty("记录")
    private List<T> records = Lists.newArrayList();
}
