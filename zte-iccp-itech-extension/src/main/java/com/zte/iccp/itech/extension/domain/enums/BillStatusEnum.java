package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 单据状态枚举
 *
 * <AUTHOR> 10347404
 * @date 2025-03-07
 **/
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum BillStatusEnum {

    /**暂存*/
    SAVE("0","暂存"),

    /**已提交*/
    SUBMITTED("1","已提交"),

    /**已审核*/
    APPROVED("2","已审核"),
    ;

    private final String code;

    private final String value;

    public static BillStatusEnum fromCode(String str) {
        return Arrays.stream(BillStatusEnum.values())
                .filter(item -> item.code.equals(str))
                .findFirst()
                .orElse(null);
    }
}
