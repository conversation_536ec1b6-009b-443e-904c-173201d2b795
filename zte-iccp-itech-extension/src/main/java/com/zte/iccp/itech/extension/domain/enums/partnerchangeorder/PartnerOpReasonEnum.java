package com.zte.iccp.itech.extension.domain.enums.partnerchangeorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/08/28
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PartnerOpReasonEnum implements SingletonTextValuePairsProvider {
    /** 公司发起 */
    COMPANY("公司发起", "Company"),
    /** 客户发起 */
    CUSTOMER("客户发起", "Customer"),
    ;

    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }
}
