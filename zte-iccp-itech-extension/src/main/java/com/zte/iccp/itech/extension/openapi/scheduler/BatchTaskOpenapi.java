package com.zte.iccp.itech.extension.openapi.scheduler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLogActionEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.util.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * 批次任务定时任务
 *
 * <AUTHOR>
 * @create 2024/6/11 下午7:26
 */
@Slf4j
public class BatchTaskOpenapi extends AbstractOpenApi {

    public static final int SEVEN_DAYS = 7;

    public static final int THIRTY_DAYS = 30;

    /*
     * 关闭挂起流程
     * （1）国际代表处超过30天的刷为关闭
     * （2）其他代表处超过七天的关闭
     * */
    public void closeSuspendedFlow() {
        // 先查询出挂起时长超过7天的
        List<IFilter> conditionObjFilters = Lists.newArrayList();
        conditionObjFilters.add(new Filter(CURRENT_STATUS, Comparator.EQ, AssignmentStatusEnum.SUSPENDED.getValue()));
        conditionObjFilters.add(new Filter(SUSPENDED_TIME, Comparator.LT, LocalDateTime.now().minusDays(SEVEN_DAYS)));
        List<BatchTask> batchTasks = QueryDataHelper.query(BatchTask.class,
                Lists.newArrayList(ID, CHANGE_ORDER_ID, ORGANIZATION_ID, SUSPENDED_TIME), conditionObjFilters);
        Date currentDate = new Date();
        for (BatchTask batchTask : batchTasks) {
            // 判断批次代表处是否为【国际代表处】
            // 若是，判断挂起时长是否超过30天；若没超过30天，则跳过，不触发关闭流程
            // 其他情况，触发关闭流程（国内代表处，或者国际挂起超过30天）
            DeptTypeEnum typeEnum = DeptTypeEnum.getDeptTypeById(batchTask.getOrganizationId());
            int dayDiff = DateUtils.dayDiff(currentDate, batchTask.getSuspendedTime());
            if (DeptTypeEnum.INTER == typeEnum && dayDiff < THIRTY_DAYS) {
                continue;
            }
            //结束审批流程，刷新状态
            try {
                FlowHelper.revokeFlow(batchTask.getId(), ApprovalConstants.BATCH_TASK_FLOW);
                BatchTaskAbility.updateCurrentStatus(batchTask.getId(), AssignmentStatusEnum.ABOLISH);
                // 判断是否是最后一个流程，更新变跟单状态，刷新处理人和当前节点
                BatchTaskAbility.flowEndUpadteAllStatus(ApprovalConstants.BATCH_TASK_FLOW, batchTask.getChangeOrderId(), batchTask.getId());
                // 挂起超期自动关闭 - 操作日志
                OperationLogRecordAbility.saveActionOperationLog(
                        batchTask.getId(), OperationLogActionEnum.BATCH_SUSPEND_AUTOMATIC_CLOSE.getOperationCode(), new Date());
            } catch (Exception e) {
                log.error("scheduler closeSuspendedFlow batch_task exception! id:{}， e:{}", batchTask.getId(), e);
            }
        }

        //分包商
        List<SubcontractorBatchTask> subBatchTasks = QueryDataHelper.query(SubcontractorBatchTask.class,
                Lists.newArrayList(ID, CHANGE_ORDER_ID, ORGANIZATION_ID, SUSPENDED_TIME), conditionObjFilters);
        for (SubcontractorBatchTask subBatchTask : subBatchTasks) {
            // 判断批次代表处是否为【国际代表处】
            // 若是，判断挂起时长是否超过30天；若没超过30天，则跳过，不触发关闭流程
            // 其他情况，触发关闭流程（国内代表处，或者国际挂起超过30天）
            DeptTypeEnum typeEnum = DeptTypeEnum.getDeptTypeById(subBatchTask.getOrganizationId());
            int dayDiff = DateUtils.dayDiff(currentDate, subBatchTask.getSuspendedTime());
            if (DeptTypeEnum.INTER == typeEnum && dayDiff < THIRTY_DAYS) {
                continue;
            }
            // 结束审批流程
            try {
                FlowHelper.revokeFlow(subBatchTask.getId(), ApprovalConstants.SUBCONTRACTOR_TASK_FLOW);
                // 刷新状态
                Map<String, Object> values = Maps.newHashMap();
                values.put(CURRENT_STATUS, AssignmentStatusEnum.ABOLISH.getValue());
                SaveDataHelper.update(SubcontractorBatchTask.class, subBatchTask.getId(), values);
                BatchTaskAbility.flowEndUpadteAllStatus(ApprovalConstants.SUBCONTRACTOR_TASK_FLOW, subBatchTask.getChangeOrderId(),subBatchTask.getId());
                // 挂起超期自动关闭 - 操作日志
                OperationLogRecordAbility.saveActionOperationLog(
                        subBatchTask.getId(), OperationLogActionEnum.HZF_BATCH_SUSPEND_AUTOMATIC_CLOSE.getOperationCode(), new Date());
            } catch (Exception e) {
                log.error("scheduler closeSuspendedFlow sub_batch_task exception! id:{}， e:{}", subBatchTask.getId(), e);
            }
        }
    }

    /*
     * 将流程推到待反馈操作结果节点
     * 当前状态为代操作执行，且当前时间大于操作开始时间任务
     * */
    public void pushResultTobeBack() {
        BatchTaskAbility.pushResultTobeBack();
    }
}
