package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.PropValueProvider;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 * @since 2024/08/21
 */
public interface IValueEnum<T> extends PropValueProvider<T> {
    T getValue();

    @Override
    default T getPropValue() {
        return getValue();
    }

    @Retention(RetentionPolicy.RUNTIME)
    @interface OthValueField {
        String value();
    }
}
