package com.zte.iccp.itech.extension.domain.model.subentity;

import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/1/9 下午3:07
 */
@Setter
@Getter
@BaseSubEntity.Info(value = "operation_stage_clock_in", parent = SubcontractorBatchTask.class)
public class SubcontractorOperationStageClockIn extends OperationStageClockIn {
}
