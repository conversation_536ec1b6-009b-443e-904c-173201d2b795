package com.zte.iccp.itech.extension.domain.enums.faultorder;

import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 故障管理任务进展
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum FaultProcessEnum {

    /** TODO: 待删除 - 待提交故障分析报告 */
    TO_SUBMIT_FAULT_ANALYSIS_REPORT("待提交故障分析报告", "Need submitting the fault analysis report"),

    /** 故障复盘确认 */
    FAULT_REVIEW_CONFIRMING("故障复盘确认", "Fault Review Confirming"),

    /** 故障复盘中 */
    FAULT_REVIEWING("故障复盘中", "Fault Reviewing"),

    /** TODO: 待删除 - 待提交故障复盘报告 */
    TO_SUBMIT_FAULT_REVIEW_REPORT("待提交故障复盘报告", "Need submitting the fault review report"),

    /** 待故障整改横推确认 */
    FAULT_RECTIFICATION_PROMOTION_CONFIRMING("待故障整改横推确认", "Fault Rectification and Promotion Confirming"),

    /** 待反馈客户满意度 */
    PENDING_FEEDBACK_CUSTOMER_SATISFACTION("待反馈客户满意度", "Pending Feedback on Customer Satisfaction"),

    /** 故障整改中 */
    FAULT_BEING_RECTIFIED("故障整改中", "Fault being Rectified"),

    /** 已关闭 */
    CLOSED("已关闭", "Closed"),

    /** 故障降级,任务关闭 */
    DEMOTION_CLOSED("故障降级,任务关闭", "Closed for Fault Degradation"),
    ;

    /** 中文描述 */
    private final String zhCn;

    /** 英文描述 */
    private final String enUs;

    /**
     * 获取当前进展
     * @param faultProcess
     * @return String
     */
    public static String getCurrentProcess(String faultProcess) {
        try {
            FaultProcessEnum currentProcess = FaultProcessEnum.valueOf(faultProcess);
            return LangUtils.get(currentProcess.getZhCn(), currentProcess.getEnUs());
        } catch (Exception e) {
            return CommonConstants.EMPTY_STRING;
        }
    }
}
