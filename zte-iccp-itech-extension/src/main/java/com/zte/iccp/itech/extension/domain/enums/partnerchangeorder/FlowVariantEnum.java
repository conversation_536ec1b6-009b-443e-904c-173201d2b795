package com.zte.iccp.itech.extension.domain.enums.partnerchangeorder;

import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/02
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum FlowVariantEnum implements BaseFlowPlugin.FlowVariant {
    /** 提交状态 */
    SUBMIT_STATE("submit_state")
    ;

    private final String key;
}
