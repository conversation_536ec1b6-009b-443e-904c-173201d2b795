package com.zte.iccp.itech.extension.openapi;

import com.zte.iccp.itech.extension.ability.authtask.BatchTask4AuthAbility;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.SysAuthUtils;
import com.zte.iccp.itech.extension.domain.model.vo.BatchTask4AuthVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.web.bind.annotation.PathVariable;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/08/19
 */
public class BatchTaskApi extends AbstractOpenApi {
    public ServiceData<Map<String, Object>> getBatchTask4Auth(
            HttpServletRequest request,
            @PathVariable("batchCode") String batchCode) {
        SysAuthUtils.auth(request);

        BatchTask4AuthVO vo = BatchTask4AuthAbility.find4Auth(batchCode);
        Map<String, Object> map = JsonUtils.parseObject(vo, Map.class);

        ServiceData<Map<String, Object>> serviceData = new ServiceData<>();
        serviceData.setBo(map);
        return serviceData;
    }
}
