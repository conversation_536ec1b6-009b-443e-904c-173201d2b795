package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * &#064;description:  流程类型枚举
 * &#064;date  2024/4/28 19:55
 * zte-iccp-itech-netchange
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ApproveFlowTypeEnum {
    /**
     * 网络变更单
     */
    NETWORK_CHANGE,

    /**
     * 分包商变更单
     */
    PARTNER_NETWORK_CHANGE;
}
