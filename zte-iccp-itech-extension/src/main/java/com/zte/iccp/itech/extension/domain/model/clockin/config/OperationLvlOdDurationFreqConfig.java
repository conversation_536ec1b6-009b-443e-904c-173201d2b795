package com.zte.iccp.itech.extension.domain.model.clockin.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.OperationLvlOdDurationFreqConfigFieldConsts.*;

/**
 * 操作等级值守时长与频次配置
 * <AUTHOR>
 * @since 2024/09/14
 */
@Getter
@Setter
@BaseEntity.Info("operation_lvl_od_duration_frequency_config")
public class OperationLvlOdDurationFreqConfig extends BaseEntity {
    @JsonProperty(value = OPERATION_LEVEL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private OperationLevelEnum operationLevel;

    @JsonProperty(value = TOTAL_DURATION_INNER)
    private Double totalDurationInner;

    @JsonProperty(value = TOTAL_DURATION_INTER)
    private Double totalDurationInter;

    @JsonProperty(value = FREQUENCY_INNER)
    private Integer frequencyInner;

    @JsonProperty(value = FREQUENCY_INTER)
    private Integer frequencyInter;
}
