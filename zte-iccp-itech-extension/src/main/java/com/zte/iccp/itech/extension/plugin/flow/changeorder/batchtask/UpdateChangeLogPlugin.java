package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.vo.ConfirmCopy;
import com.zte.iccp.itech.extension.domain.model.vo.OperatorCopy;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATE_PERSON;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATOR_ROLE;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME;

/**
 * 批次任务，提交后记录变更日志
 *
 * <AUTHOR>
 * @since 2025/01/06
 */
public class UpdateChangeLogPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String businessId = body.getBusinessId();
        IBatchTask batchTask;
        List<BatchTaskOperator> operators;
        Class clazz = BatchTask.class;
        // 内部单
        List<String> fields = Arrays.asList(ID, URGENT_FLAG, PLAN_OPERATION_START_TIME, PLAN_OPERATION_END_TIME, NOTICE_COPY);
        List<String> operatorFields = Arrays.asList(ID, OPERATOR_ROLE, OPERATE_PERSON);
        if (ApprovalConstants.BATCH_TASK_FLOW.equals(body.getFlowCode())) {
            batchTask = BatchTaskAbility.get(businessId, fields);
            operators = QueryDataHelper.query(BatchTaskOperator.class, operatorFields, businessId);
        } else {
            // 分包商
            batchTask = BatchTaskAbility.getSub(businessId, fields);
            operators = QueryDataHelper.query(SubcontractorBatchOperator.class, operatorFields, businessId);
            clazz = SubcontractorBatchTask.class;
        }
        ConfirmCopy confirmCopy = JsonUtils.parseObject(batchTask.getNoticeCopy(), ConfirmCopy.class);
        if (confirmCopy == null) {
            return false;
        }

        StringBuilder logZh = new StringBuilder();
        StringBuilder logUs = new StringBuilder();
        // 紧急操作
        if (isNotEquals(batchTask.getUrgentFlag(), confirmCopy.getUrgentFlag())) {
            appendLog(MsgUtils.getLangMessage(ZH_CN, IS_EMERGENCY_OPERATION), confirmCopy.getUrgentFlag().getZhCn(),
                    batchTask.getUrgentFlag().getZhCn(), logZh);
            appendLog(MsgUtils.getLangMessage(EN_US, IS_EMERGENCY_OPERATION), confirmCopy.getUrgentFlag().getEnUs(),
                    batchTask.getUrgentFlag().getEnUs(), logUs);
        }
        //  只有内部单才有封网管控
        if (ApprovalConstants.BATCH_TASK_FLOW.equals(body.getFlowCode())) {
            BatchTask task = BatchTaskAbility.get(businessId, Arrays.asList(ID, CONTROL_PERIOD_FLAG));
            if (isNotEquals(task.getControlPeriodFlag(), confirmCopy.getControlPeriodFlag())) {
                appendLog(MsgUtils.getLangMessage(ZH_CN, IS_MANAGEMENT_CONTROL_PERIOD), confirmCopy.getControlPeriodFlag().getZhCn(),
                        task.getControlPeriodFlag().getZhCn(), logZh);
                appendLog(MsgUtils.getLangMessage(EN_US, IS_MANAGEMENT_CONTROL_PERIOD), confirmCopy.getControlPeriodFlag().getEnUs(),
                        task.getControlPeriodFlag().getEnUs(), logUs);
            }
        }

        // 确认操作开始时间
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORM);
        if (isNotEquals(batchTask.getPlanOperationStartTime(), confirmCopy.getPlanOperationStartTime())) {
            appendLog(MsgUtils.getLangMessage(ZH_CN, START_TIME_CONFIRMATION_OPERATION),
                    dateFormat.format(confirmCopy.getPlanOperationStartTime()),
                    dateFormat.format(batchTask.getPlanOperationStartTime()), logZh);
            appendLog(MsgUtils.getLangMessage(EN_US, START_TIME_CONFIRMATION_OPERATION),
                    dateFormat.format(confirmCopy.getPlanOperationStartTime()),
                    dateFormat.format(batchTask.getPlanOperationStartTime()), logUs);
        }

        // 结束时间
        if (isNotEquals(batchTask.getPlanOperationEndTime(), confirmCopy.getPlanOperationEndTime())) {
            appendLog(MsgUtils.getLangMessage(ZH_CN, END_TIME_CONFIRMATION_OPERATION),
                    dateFormat.format(confirmCopy.getPlanOperationEndTime()),
                    dateFormat.format(batchTask.getPlanOperationEndTime()), logZh);
            appendLog(MsgUtils.getLangMessage(EN_US, END_TIME_CONFIRMATION_OPERATION),
                    dateFormat.format(confirmCopy.getPlanOperationEndTime()),
                    dateFormat.format(batchTask.getPlanOperationEndTime()), logUs);
        }

        // 操作人员
        List<OperatorCopy> orginOperators = confirmCopy.getOperators();
        if (!CollectionUtils.isEmpty(operators) && !CollectionUtils.isEmpty(orginOperators)) {
            Map<OperatorRoleEnum, List<String>> roleEmpNoMap =
                    operators.stream().collect(Collectors.groupingBy(BatchTaskOperator::getOperatorRole,
                            Collectors.mapping(BatchTaskOperator::getOperatePersonEmpNo, Collectors.toList())));
            Map<OperatorRoleEnum, List<String>> orginRoleEmpNoMap =
                    orginOperators.stream().collect(Collectors.groupingBy(OperatorCopy::getOperatorRole,
                            Collectors.mapping(OperatorCopy::getOperatePerson, Collectors.toList())));

            Map<OperatorRoleEnum, List<String>> delPersons = compareOperatorsMap(orginRoleEmpNoMap, roleEmpNoMap);
            Map<OperatorRoleEnum, List<String>> addPersons = compareOperatorsMap(roleEmpNoMap, orginRoleEmpNoMap);
            Map<String, String> empNoNameMap = getEmpNoNameMap(addPersons.values(), delPersons.values());
            if (!CollectionUtils.isEmpty(empNoNameMap)) {
                getRolePersonLog(MsgUtils.getLangMessage(ZH_CN, ADD), addPersons, empNoNameMap, logZh);
                getRolePersonLog(MsgUtils.getLangMessage(ZH_CN, REMOVE), delPersons, empNoNameMap, logZh);

                getRolePersonLog(MsgUtils.getLangMessage(EN_US, ADD), addPersons, empNoNameMap, logUs);
                getRolePersonLog(MsgUtils.getLangMessage(EN_US, REMOVE), delPersons, empNoNameMap, logUs);
            }
        }
        // 保存日志
        Map<String, Object> updateValues = Maps.newHashMap();
        updateValues.put(CHANGE_PLAN_DESC_ZH, logZh.toString());
        updateValues.put(CHANGE_PLAN_DESC_US, logUs.toString());
        SaveDataHelper.update(clazz, businessId, updateValues);
        return false;
    }

    private void getRolePersonLog(String tab,
                                  Map<OperatorRoleEnum, List<String>> personMap,
                                  Map<String, String> empNoNameMap,
                                  StringBuilder log) {
        if (CollectionUtils.isEmpty(personMap)) {
            return;
        }

        log.append(CommonConstants.HTML_P).append(tab).append(CommonConstants.COLON);
        for (OperatorRoleEnum role : personMap.keySet()) {
            log.append(CommonConstants.LEFT_SQUARE_BRACKET)
                    .append(role.getZhCn())
                    .append(CommonConstants.RIGHT_SQUARE_BRACKET)
                    .append(getEmpNoStr(personMap.get(role), empNoNameMap))
                    .append(CommonConstants.COMMA);
        }
        log.deleteCharAt(log.length() - 1);
        log.append(CommonConstants.HTML_R_P);
    }

    private boolean isNotEquals(Object target, Object orgin) {
        if (target == null) {
            return true;
        }

        if (target instanceof Date) {
            if (orgin == null) {
                return true;
            }
            return ((Date) target).getTime() != ((Date) orgin).getTime();
        }

        return !target.equals(orgin);
    }

    private void appendLog(String tab, String orgin, String change, StringBuilder log) {
        log.append(CommonConstants.HTML_P)
                .append(LEFT_SQUARE_BRACKET)
                .append(tab)
                .append(RIGHT_SQUARE_BRACKET)
                .append(CommonConstants.COLON)
                .append(orgin)
                .append(CommonConstants.CHANGE)
                .append(change)
                .append(CommonConstants.HTML_R_P);
    }

    /*
     * 对比两个map中相同角色，targetMap中存在，orginMap不存在的集合
     * */
    private Map<OperatorRoleEnum, List<String>> compareOperatorsMap(Map<OperatorRoleEnum, List<String>> targetMap,
                                                                    Map<OperatorRoleEnum, List<String>> orginMap) {
        Map<OperatorRoleEnum, List<String>> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(targetMap) || CollectionUtils.isEmpty(orginMap)) {
            return result;
        }
        for (OperatorRoleEnum role : targetMap.keySet()) {
            List<String> values = orginMap.get(role);
            if (CollectionUtils.isEmpty(values)) {
                result.put(role, targetMap.get(role));
                continue;
            }
            List<String> targetValues = new ArrayList<>(targetMap.get(role));
            targetValues.removeAll(values);
            if (!CollectionUtils.isEmpty(targetValues)) {
                result.put(role, targetValues);
            }
        }
        return result;
    }

    private Map<String, String> getEmpNoNameMap(Collection<List<String>> addPersons, Collection<List<String>> delPersons) {
        List<String> epmNos = new ArrayList<>();
        epmNos.addAll(addPersons.stream()
                .flatMap(List::stream)
                .collect(Collectors.toList()));
        epmNos.addAll(delPersons.stream()
                .flatMap(List::stream)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(epmNos)) {
            return null;
        }

        return HrClient.queryEmployeeNameInfo(epmNos);
    }

    private String getEmpNoStr(List<String> empNos, Map<String, String> empNoNameMap) {
        if (CollectionUtils.isEmpty(empNos)) {
            return "";
        }
        List<String> epmNos = new ArrayList<>();
        empNos.forEach(item -> {
            epmNos.add(empNoNameMap.get(item));
        });

        return String.join(CommonConstants.COMMA, epmNos);
    }

}
