package com.zte.iccp.itech.extension.domain.model.clockin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.JsonTextDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.JsonTextSerializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallReasonEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallStatusEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInCallPlanFieldConsts.*;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@Getter
@Setter
@BaseEntity.Info("clock_in_call_plan")
public class ClockInCallPlan extends BaseEntity implements Cloneable {
    @JsonProperty(value = OBJECT_ID)
    private String objectId;

    @JsonProperty(value = PREV_PLAN_ID)
    private String prevPlanId;

    @JsonProperty(value = REASON)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private CallReasonEnum reason;

    @JsonProperty(value = CALL_UTC_TIME)
    private Long callUtcTime;

    @JsonProperty(value = CALLEE)
    @JsonDeserialize(using = JsonTextDeserializer.class)
    @JsonSerialize(using = JsonTextSerializer.class)
    private ClockInCallee callee;

    @JsonProperty(value = CONTENT)
    private String content;

    @JsonProperty(value = CALL_STATUS)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private CallStatusEnum callStatus;

    @JsonProperty(value = RESULT_SEARCH_ID)
    private String resultSearchId;

    @SuppressWarnings("MethodDoesntCallSuperMethod")
    @Override
    @SneakyThrows
    public ClockInCallPlan clone() {
        return JsonUtils.parseObject(
                JsonUtils.toJsonString(this), ClockInCallPlan.class);
    }
}
