package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/28
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OperationLevelEnum implements SingletonTextValuePairsProvider {
    /** 一般 */
    NORMAL("1", "一般","Common","operation.level.normal"),
    /** 重要 */
    IMPORTANCE("2", "重要","Important","operation.level.importance"),
    /** 关键 */
    CRITICAL("3", "关键","Critical","operation.level.critical"),
    ;

    private final String value;

    private final String zhCn;

    private final String enUs;

    private final String msgKey;

    public static OperationLevelEnum fromValue(String value) {
        for (OperationLevelEnum operationLevel : OperationLevelEnum.values()) {
            if (operationLevel.getValue().equals(value)) {
                return operationLevel;
            }
        }

        return null;
    }

    public String getMsg(String lang) {
        return MsgUtils.getLangMessage(lang, this.msgKey);
    }

    public String getName(String lang) {
        return ZH_CN.equals(lang) ? this.zhCn : this.enUs;
    }
}
