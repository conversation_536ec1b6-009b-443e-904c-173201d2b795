package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 技术管理主任务实体常量
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/18
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ManageTaskFieldConsts {

    /**
     * id
     */
    public static final String TASK_ID = "id";

    /**
     * 任务分类
     */
    public static final String TASK_CATEGORY = "task_category";

    /**
     * 任务名称 -文本
     */
    public static final String TASK_NAME = "task_name";

    /**
     * 营销/代表处
     */
    public static final String ORGANIZATION_ID = "organization_id";

    /**
     * 客户标识
     */
    public static final String CUSTOMER_IDENTIFICATION = "customer_identification";

    /**
     * 产品分类
     */
    public static final String PRODUCT_ID = "product_id";

    /**
     * 责任人
     */
    public static final String RESPONSIBLE_PERSON = "responsible_person";

    /**
     * 验收人
     */
    public static final String ACCEPTOR_PERSON = "acceptor_person";

    /**
     * 要求完成日期
     */
    public static final String REQUIRED_COMPLETION_DATE = "required_completion_date";

    /**
     * 任务详情
     */
    public static final String TASK_DETAIL = "task_detail";

    /**
     * 附件
     */
    public static final String ATTACHMENT = "attachment";

    /**
     * 状态
     */
    public static final String STATUS = "status";

    /**
     * 任务进展
     */
    public static final String PROGRESS_DESCRIPTION = "progress_description";

    /**
     * 反馈类型
     */
    public static final String FEEDBACK_TYPE = "feedback_type";

    /**
     * 反馈附件
     */
    public static final String APPROVAL_ATTACHMENT = "approval_attachment";

    /**
     * 责任领导人
     */
    public static final String RESPONSIBLE_LEADER_PERSON = "responsible_leader_person";

    /**
     * 知会人
     */
    public static final String INFORMED_PERSON = "informed_person";

    /**
     * 目标要求
     */
    public static final String TARGET_REQUIRE = "target_require";

    /**
     * 计划开始时间
     */
    public static final String PLAN_START_TIME = "plan_start_time";

    /**
     * 主任务单据编号
     */
    public static final String CN_NO = "cn_no";

    /**
     * 变更单数据来源
     */
    public static final String SOURCE = "source";

    /**
     * 变更单数据来源id
     */
    public static final String SOURCE_ID = "source_id";

}
