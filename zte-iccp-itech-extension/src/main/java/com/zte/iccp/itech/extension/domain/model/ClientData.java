package com.zte.iccp.itech.extension.domain.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> 10335201
 * @date 2024-12-20 下午5:37
 **/
@ApiModel("外部接口数据结果集合")
@Setter
@Getter
public class ClientData {
    @ApiModelProperty("组织ID")
    private Set<String> organizationId;

    @ApiModelProperty("组织")
    private Map<String, String> organization;

    @ApiModelProperty("组织（全量名称）")
    private Map<String, List<String>> fullOrganization;

    @ApiModelProperty("区域编码")
    private Set<String> areaCode;

    @ApiModelProperty("区域信息")
    private Map<String, String> area;

    @ApiModelProperty("产品ID")
    private Set<String> productId;

    @ApiModelProperty("产品")
    private Map<String, String> product;

    @ApiModelProperty("网络ID")
    private Set<String> networkId;

    @ApiModelProperty("网络")
    private Map<String, String> network;

    @ApiModelProperty("用户ID")
    private Set<String> userId;

    @ApiModelProperty("用户")
    private Map<String, String> user;
}
