package com.zte.iccp.itech.extension.common.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import lombok.SneakyThrows;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/05
 */
public class DynamicDataEntitySerializer extends JsonSerializer<DynamicDataEntity> {
    @SneakyThrows
    @Override
    public void serialize(DynamicDataEntity value, JsonGenerator gen, SerializerProvider serializers) {
        gen.writeObject(value.toMap());
    }
}
