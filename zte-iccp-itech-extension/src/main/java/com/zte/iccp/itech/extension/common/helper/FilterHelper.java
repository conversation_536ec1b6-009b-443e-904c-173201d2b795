package com.zte.iccp.itech.extension.common.helper;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.paas.lcap.core.orm.query.*;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.paas.lcap.core.orm.query.LogicOperator.AND;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/12
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class FilterHelper {

    public static IFilter clone(IFilter origin) {
        if (origin instanceof FieldExtFilter) {
            return clone((FieldExtFilter) origin);
        }

        if (origin instanceof FunctionFilter) {
            return clone((FunctionFilter) origin);
        }

        if (origin instanceof ReFilter) {
            return clone((ReFilter) origin);
        }

        if (origin instanceof NativeJsonFilter) {
            return clone((NativeJsonFilter) origin);
        }

        if (origin instanceof JsonFilter) {
            return clone((JsonFilter) origin);
        }

        if (origin instanceof RawSubQueryFilter) {
            return clone((RawSubQueryFilter) origin);
        }

        if (origin instanceof SubQueryFilter) {
            return clone((SubQueryFilter) origin);
        }

        if (origin instanceof Filter) {
            return clone((Filter) origin);
        }

        if (origin instanceof MultiFilter) {
            return clone((MultiFilter) origin);
        }

        throw new IllegalArgumentException();
    }

    public static List<IFilter> clone(List<IFilter> origin) {
        return origin.stream()
                .map(FilterHelper::clone)
                .collect(Collectors.toList());
    }

    public static MultiFilter newMultiFilter(IFilter... filter) {
        return new MultiFilter(Lists.newArrayList(filter), AND);
    }

    public static MultiFilter multiMemberOfFilter(String fieldId, List<String> values) {
        if (StringUtils.isBlank(fieldId) || CollectionUtils.isEmpty(values)) {
            return null;
        }

        IFilter filter = new Filter(fieldId, Comparator.MEMBER_OF, values.get(0));
        for (int index = 1; index < values.size(); index++) {
            filter.or(new Filter(fieldId, Comparator.MEMBER_OF, values.get(index)));
        }

        return newMultiFilter(filter);
    }

    public static List<IFilter> handleFilters(Class<? extends BaseEntity> clazz, List<IFilter> filters) {
        List<IFilter> clone = clone(filters);
        String entityId = EntityHelper.getEntityId(clazz);

        if (BaseSubEntity.class.isAssignableFrom(clazz)) {
            handleFilters4Sub(entityId, clone);
            return clone;
        }

        handleFilters4Main(entityId, clone);
        return clone;
    }

    private static void handleFilters4Main(String entityId, List<IFilter> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            return;
        }

        filters.forEach(iFilter -> {
            if (iFilter instanceof MultiFilter) {
                MultiFilter multiFilter = (MultiFilter) iFilter;
                handleFilters4Main(entityId, multiFilter.getMultiFilers());
                return;
            }

            if (!(iFilter instanceof Filter)) {
                return;
            }

            Filter filter = (Filter) iFilter;

            handleNullValue(filter);

            if (filter.getField().equals(CREATE_BY)
                    || filter.getField().equals(CREATE_TIME)
                    || filter.getField().equals(LAST_MODIFIED_BY)
                    || filter.getField().equals(LAST_MODIFIED_TIME)) {
                filter.setField(String.format("%s_%s", entityId, filter.getField()));
            }

            handleFilters4Main(entityId, filter.getComboFilters());
        });
    }

    private static void handleFilters4Sub(String subOr2ndEntityId, List<IFilter> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            return;
        }

        filters.forEach(iFilter -> {
            if (iFilter instanceof MultiFilter) {
                MultiFilter multiFilter = (MultiFilter) iFilter;
                handleFilters4Sub(subOr2ndEntityId, multiFilter.getMultiFilers());
                return;
            }

            if (!(iFilter instanceof Filter)) {
                return;
            }

            Filter filter = (Filter) iFilter;

            handleNullValue(filter);

            if (filter.getField().equals(CREATE_BY)
                    || filter.getField().equals(CREATE_TIME)
                    || filter.getField().equals(LAST_MODIFIED_BY)
                    || filter.getField().equals(LAST_MODIFIED_TIME)) {
                filter.setField(String.format("%s.%s_%s", subOr2ndEntityId, subOr2ndEntityId, filter.getField()));
            } else {
                filter.setField(String.format("%s.%s", subOr2ndEntityId, filter.getField()));
            }

            handleFilters4Sub(subOr2ndEntityId, filter.getComboFilters());
        });
    }

    private static void handleNullValue(Filter filter) {
        // 兼容IN条件中有null值导致字符集转换报错的问题
        if (filter.getComparator() == Comparator.IN
                && filter.getValue() instanceof List) {
            List<?> list = (List<?>) filter.getValue();
            // 过滤掉所有null元素
            list.removeIf(Objects::isNull);

            // 如果是空集合则让该条件始终为false
            if (list.isEmpty()) {
                filter.setField(ID);
                filter.setComparator(Comparator.EQ);
                filter.setValue(StringUtils.EMPTY);
            }
        }

        if (filter.getValue() != null) {
            return;
        }

        // 兼容EQ/NE的值是null导致字符集转换报错问题
        switch (filter.getComparator()) {
            case EQ:
                filter.setComparator(Comparator.IS_NULL);
                break;
            case NE:
                filter.setComparator(Comparator.IS_NOT_NULL);
                break;
            default:
                break;
        }
    }

    private static FieldExtFilter clone(FieldExtFilter origin) {
        FieldExtFilter filter = new FieldExtFilter(
                origin.getField(),
                origin.getComparator(),
                origin.getValue(),
                origin.getSqlConnector().toString());
        filter.setNegate(origin.getNegate());
        filter.addComboFilter(clone(origin.getComboFilters()));
        return filter;
    }

    private static Filter clone(Filter origin) {
        Filter filter = new Filter(
                origin.getField(),
                origin.getComparator(),
                origin.getValue(),
                origin.getSqlConnector(),
                clone(origin.getComboFilters()));
        filter.setNegate(origin.getNegate());
        return filter;
    }

    private static FunctionFilter clone(FunctionFilter origin) {
        FunctionFilter filter = new FunctionFilter(
                origin.getField(),
                origin.getComparator(),
                origin.getValue(),
                origin.getSqlConnector(),
                origin.getIFunction().getSqlFun());
        filter.setNegate(origin.getNegate());
        filter.addComboFilter(clone(origin.getComboFilters()));
        return filter;
    }

    private static JsonFilter clone(JsonFilter origin) {
        JsonFilter filter = new JsonFilter(
                origin.getField(),
                origin.getComparator(),
                origin.getValue(),
                origin.getSqlConnector(),
                origin.getExtractIndex());
        filter.setNegate(origin.getNegate());
        filter.addComboFilter(clone(origin.getComboFilters()));
        return filter;
    }

    private static MultiFilter clone(MultiFilter origin) {
        MultiFilter filter = new MultiFilter(
                clone(origin.getMultiFilers()),
                origin.getSqlConnector());
        filter.setNegate(origin.getNegate());
        return filter;
    }

    private static NativeJsonFilter clone(NativeJsonFilter origin) {
        NativeJsonFilter filter = new NativeJsonFilter(
                origin.getField(),
                origin.getExtractKey(),
                origin.getComparator(),
                origin.getValue(),
                origin.getSqlConnector());
        filter.setNegate(origin.getNegate());
        filter.addComboFilter(clone(origin.getComboFilters()));
        return filter;
    }

    private static RawSubQueryFilter clone(RawSubQueryFilter origin) {
        RawSubQueryFilter filter = new RawSubQueryFilter(
                origin.getField(),
                origin.getComparator(),
                origin.getValue(),
                origin.getSqlConnector(),
                clone(origin.getComboFilters()),
                origin.getSubQuerySql(),
                origin.isNonSystemDef());
        filter.setNegate(origin.getNegate());
        return filter;
    }

    private static ReFilter clone(ReFilter origin) {
        ReFilter filter = new ReFilter(
                origin.getField(),
                origin.getComparator(),
                origin.getValue(),
                origin.getSqlConnector().toString());
        filter.setNegate(origin.getNegate());
        filter.addComboFilter(clone(origin.getComboFilters()));
        return filter;
    }

    private static SubQueryFilter clone(SubQueryFilter origin) {
        SubQueryFilter filter = new SubQueryFilter(
                origin.getField(),
                origin.getComparator(),
                origin.getValue(),
                origin.getSqlConnector(),
                clone(origin.getComboFilters()),
                origin.getSubQuerySql(),
                origin.isNonSystemDef());
        filter.setNegate(origin.getNegate());
        return filter;
    }
}