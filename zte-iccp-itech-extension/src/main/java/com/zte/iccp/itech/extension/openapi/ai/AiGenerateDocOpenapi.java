package com.zte.iccp.itech.extension.openapi.ai;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.model.AiGenerateDocCallback;
import com.zte.iccp.itech.extension.openapi.model.ai.AiGenerateDocCallbackDto;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.entity.AiGenerateDocCallbackFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * <AUTHOR>
 * @date 2024/6/13 下午4:35
 */
public class AiGenerateDocOpenapi extends AbstractOpenApi {

    public ServiceData<Boolean> aiCallBack(@RequestBody AiGenerateDocCallbackDto dto) {

        if (null == dto || StringUtils.isBlank(dto.getPageId()) || StringUtils.isBlank(dto.getPageSessionId())
                || StringUtils.isBlank(dto.getType()) || StringUtils.isBlank(dto.getStatus())) {
            return failure();
        }

        Filter filter1 = new Filter(PAGE_SESSION_ID, Comparator.EQ, dto.getPageSessionId());
        Filter filter2 = new Filter(PAGE_ID, Comparator.EQ, dto.getPageId());
        Filter filter3 = new Filter(TYPE, Comparator.EQ, dto.getType());
        AiGenerateDocCallback callback = QueryDataHelper.queryOne(AiGenerateDocCallback.class,
                Lists.newArrayList(ID, TYPE, DOC_ID, STATUS), Lists.newArrayList(filter1, filter2, filter3));

        if (null == callback) {
            AiGenerateDocCallback aiGenerateDocCallback = new AiGenerateDocCallback();
            aiGenerateDocCallback.setPageSessionId(dto.getPageSessionId());
            aiGenerateDocCallback.setPageId(dto.getPageId());
            aiGenerateDocCallback.setType(dto.getType());
            aiGenerateDocCallback.setDocId(dto.getDocId());
            aiGenerateDocCallback.setFileNameSuffix(dto.getFileNameSuffix());
            aiGenerateDocCallback.setStatus(dto.getStatus());
            aiGenerateDocCallback.setLog(dto.getLog());
            SaveDataHelper.create(aiGenerateDocCallback);
            return success();
        }

        Map<String, Object> values = Maps.newHashMap();
        values.put(DOC_ID, dto.getDocId());
        values.put(FILE_NAME_SUFFIX, dto.getFileNameSuffix());
        values.put(STATUS, dto.getStatus());
        values.put(LOG, dto.getLog());
        SaveDataHelper.update(AiGenerateDocCallback.class, callback.getId(), values);
        return success();
    }

    private static ServiceData<Boolean> success() {
        return new ServiceData<Boolean>() {{
            setBo(true);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
                setMsgId(RetCode.SUCCESS_MSGID);
                setMsg(RetCode.SUCCESS_MSG);
            }});
        }};
    }

    private static ServiceData<Boolean> failure() {
        return new ServiceData<Boolean>() {{
            setBo(false);
            setCode(new RetCode() {{
                setCode(RetCode.VALIDATIONERROR_CODE);
                setMsgId(RetCode.VALIDATIONERROR_MSGID);
                setMsg(RetCode.VALIDATIONERROR_MSG);
            }});
        }};
    }
}
