package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.model.StandardScheme;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.springframework.util.StringUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.StandardSchemeFieldConsts.*;

public class StandardSchemeAbility {

    /**
     * 单个检索标准方案信息
     * 检索条件 - 方案名称
     * @param schemeId
     * @return StandardScheme
     */
    public static StandardScheme queryOneBySchemeName(String schemeId) {
        if (!StringUtils.hasText(schemeId)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList(
                ID, SCHEME_NAME, SCHEME_FILE, FILE_SIZE, VERSION_NO, LAST_MODIFIED_BY, LAST_MODIFIED_TIME);

        Filter networkIdFilter = new Filter(ID, Comparator.EQ, schemeId);
        List<IFilter> conditionFilterList = Lists.newArrayList(networkIdFilter);

        return QueryDataHelper.queryOne(StandardScheme.class, fieldList, conditionFilterList);
    }

}
