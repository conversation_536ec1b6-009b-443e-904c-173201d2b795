package com.zte.iccp.itech.extension.common.helper;

import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.FILE_DOWNLOAD_URL_PREFIX;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.SUCCESS;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AttachmentFileHelper {

    public static byte[] download(AttachmentFile attachmentFile) {
        return CloudDiskHelper.download2Bytes(attachmentFile.getFileKey());
    }

    /**
     * 构建文档对象
     * @param docId 文档云id
     * @param fileName 文件名称
     * @return 文档对象
     */
    public static AttachmentFile buildAttachmentFile(String docId, String fileName) {
        AttachmentFile attachmentFile = new AttachmentFile();
        attachmentFile.setDownloadUrl(FILE_DOWNLOAD_URL_PREFIX + docId);
        attachmentFile.setFileKey(docId);
        attachmentFile.setId(docId);
        attachmentFile.setName(fileName);
        attachmentFile.setPercentage(0);
        attachmentFile.setStatus(SUCCESS);
        return attachmentFile;
    }
}
