package com.zte.iccp.itech.extension.ability.grantfile;

import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.subentity.SubconBatchSummary;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/26
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class GrantFileConfig {
    public static final Map<ApproveFlowCodeEnum, Config> ALL = MapUtils.unmodifiableMap(
            ApproveFlowCodeEnum.BATCH_TASK_FLOW, new Config(
                    ApproveFlowCodeEnum.BATCH_TASK_FLOW.getApproverEntity(),
                    BatchTask.class,
                    BatchSummary.class,
                    new String[] {
                            ID,
                            CREATE_BY,
                            ChangeOrderFieldConsts.ORDER_NO,
                            ChangeOrderFieldConsts.OPERATION_SUBJECT,
                            ChangeOrderFieldConsts.PRODUCT_CATEGORY,
                            ChangeOrderFieldConsts.RESPONSIBLE_DEPT,
                            ChangeOrderFieldConsts.NE_LIST_FILE,
                            ChangeOrderFieldConsts.CUSTOMER_ID,
                            ChangeOrderFieldConsts.ACCN_TYPE,
                            ChangeOrderFieldConsts.COUNTRY,
                            ChangeOrderFieldConsts.PROVINCE,
                            ChangeOrderFieldConsts.OPERATION_TYPE,
                            ChangeOrderFieldConsts.TIME_ZONE,
                            ChangeOrderFieldConsts.IS_NEED_AUTHORIZATION_FILE,
                            ChangeOrderFieldConsts.ApproverFieldConsts.OFFICE_SOLUTION_REVIEWER,
                            ChangeOrderFieldConsts.ApproverFieldConsts.OFFICE_SOLUTION_REVIEWER_MULTI
                    }),

            ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW, new Config(
                    ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW.getApproverEntity(),
                    SubcontractorBatchTask.class,
                    SubconBatchSummary.class,
                    new String[] {
                            ID,
                            CREATE_BY,
                            SubcontractorChangeOrderFieldConsts.ORDER_NO,
                            SubcontractorChangeOrderFieldConsts.OPERATION_SUBJECT,
                            SubcontractorChangeOrderFieldConsts.PRODUCT_ID,
                            SubcontractorChangeOrderFieldConsts.ORGANIZATION_ID,
                            SubcontractorChangeOrderFieldConsts.NE_LIST_FILE,
                            SubcontractorChangeOrderFieldConsts.CUSTOMER_ID,
                            SubcontractorChangeOrderFieldConsts.CUSTOMER_TYPE_FLAG,
                            SubcontractorChangeOrderFieldConsts.COUNTRY,
                            SubcontractorChangeOrderFieldConsts.PROVINCE,
                            SubcontractorChangeOrderFieldConsts.OPERATION_TYPE,
                            SubcontractorChangeOrderFieldConsts.TIME_ZONE,
                            SubcontractorChangeOrderFieldConsts.IS_AUTHORIZATION_FILE
                    }));

    @Getter
    public static class Config {
        private final Class<? extends BaseEntity> changeOrderEntity;

        private final Class<? extends BaseEntity> batchTaskEntity;

        private final Class<? extends BaseSubEntity> batchSummaryEntity;

        private final String batchTaskPidField = BatchTaskFieldConsts.CHANGE_ORDER_ID;

        private final String batchTaskNoField = BatchTaskFieldConsts.BATCH_NO;

        private final List<String> changeOrderFields;

        private final List<String> batchTaskFields;

        private final List<String> batchSummaryFields;

        public String getBatchSummaryBatchNoField() {
            return batchSummaryFields.get(0);
        }

        private Config(
                Class<? extends BaseEntity> changeOrderEntity,
                Class<? extends BaseEntity> batchTaskEntity,
                Class<? extends BaseSubEntity> batchSummaryEntity,
                String[] changeOrderFields) {
            this.changeOrderEntity = changeOrderEntity;
            this.batchTaskEntity = batchTaskEntity;
            this.batchSummaryEntity = batchSummaryEntity;
            this.changeOrderFields = Collections.unmodifiableList(Lists.newArrayList(changeOrderFields));
            this.batchTaskFields = Collections.unmodifiableList(Lists.newArrayList(
                    ID,
                    BatchTaskFieldConsts.CHANGE_ORDER_ID,
                    BatchTaskFieldConsts.BATCH_NO,
                    BatchTaskFieldConsts.CUSTOMER_VOUCHER_FILE,
                    BatchTaskFieldConsts.GRANT_FILE,
                    BatchTaskFieldConsts.SA_GRANT_FILE,
                    BatchTaskFieldConsts.BATCH_OPERATION_ACCOUNT,
                    BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME,
                    BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME));
            this.batchSummaryFields = Collections.unmodifiableList(Lists.newArrayList(
                    BatchSummaryFieldConsts.BATCH_NO,
                    BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME,
                    BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME));
        }
    }
}