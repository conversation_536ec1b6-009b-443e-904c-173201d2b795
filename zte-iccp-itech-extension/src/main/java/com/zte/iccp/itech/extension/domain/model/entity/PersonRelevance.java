package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.PersonRelevanceFieldConsts.*;

@ApiModel("任务人员关联关系")
@Setter
@Getter
@BaseEntity.Info("assignment_person_relevance")
@AllArgsConstructor
@NoArgsConstructor
public class PersonRelevance extends BaseEntity {

    @JsonProperty(value = ASSIGNMENT_ID)
    @ApiModelProperty("任务ID")
    private String assignmentId;

    @JsonProperty(value = RELEVANT)
    @ApiModelProperty("关联人员")
    private String relevant;

    @JsonProperty(value = SELF_HANDLER_FLAG)
    @ApiModelProperty("是否本人处理")
    private String selfHandlerFlag;

    @JsonProperty(value = APPROVAL_TASK_FLAG)
    @ApiModelProperty("是否审批任务")
    private String approvalTaskFlag;
}
