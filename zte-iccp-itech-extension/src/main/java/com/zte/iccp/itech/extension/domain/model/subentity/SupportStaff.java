package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.changeorder.EntityEnum.SupportModeEnum;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;

@ApiModel("支持人员")
@Setter
@Getter
public class SupportStaff {

    @ApiModelProperty("支持人员")
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee person;

    @ApiModelProperty("支持方式")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private SupportModeEnum supportMode;

    @ApiModelProperty("任务说明")
    private String taskDescription;

    @ApiModelProperty("所属部门")
    private String department;

    @ApiModelProperty("联系方式")
    private String telephoneNumber;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()){
            return false;
        }
        SupportStaff staff = (SupportStaff) o;

        if (staff.getPerson() == null || this.getPerson() ==null){
            return false;
        }
        return this.getPerson().getEmpUIID().equals(staff.getPerson().getEmpUIID());
    }

    @Override
    public int hashCode() {
        if (person != null && StringUtils.hasText(person.getEmpUIID())) {
            return person.getEmpUIID().hashCode();
        }
        return 0;
    }
}
