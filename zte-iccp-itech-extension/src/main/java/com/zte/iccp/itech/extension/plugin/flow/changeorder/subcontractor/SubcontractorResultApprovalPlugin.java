package com.zte.iccp.itech.extension.plugin.flow.changeorder.subcontractor;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.APPROVAL_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CURRENT_STATUS;

/**
 * 分包商批次任务，操作结果反馈
 *
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class SubcontractorResultApprovalPlugin extends BaseFlowOperationPlugin {

    @Override
    public void beforeOperate(ExecuteEvent executeEvent) {
        // 网络负责人确认审核
        String result = TextValuePairHelper.getValue(getModel().getValue("approve_result"));
        if (CommonConstants.PASS.equals(result)) {
            getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.CLOSE.getValue());
            getModel().setValue(APPROVAL_STATUS, BoolEnum.Y.getPropValue());
        } else {
            getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.RESULT_TOBE_BACK.getValue());
            getModel().setValue(APPROVAL_STATUS, BoolEnum.N.getPropValue());
        }
    }

}
