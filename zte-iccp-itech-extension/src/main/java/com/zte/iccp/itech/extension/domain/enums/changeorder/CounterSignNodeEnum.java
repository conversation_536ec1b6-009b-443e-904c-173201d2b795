package com.zte.iccp.itech.extension.domain.enums.changeorder;

import com.zte.iccp.itech.extension.domain.model.IntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdDevIntApproval;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdNetServIntApproval;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.enums.changeorder.FlowVariantEnum.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/03
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum CounterSignNodeEnum {

    /** 网服部一体化关联产品审核 */
    NET_SERVICE_INT(
            ApproveNodeEnum.NET_INTEGRATION,
            OpAssocProdNetServIntApproval.class,
            APPROVE_RESULT_NET_SERVICE_INT,
            "Activity_1t3a2gz","operation_assoc_prod_net_integration"),
    /** 研发一体化关联产品审批 */
    RD_INT(
            ApproveNodeEnum.RD_INTEGRATION,
            OpAssocProdDevIntApproval.class,
            APPROVE_RESULT_RD_INT,
            "Activity_0jw79ym","operation_assoc_prod_rd_integration"),
    /** 国际行政审批 */
    INTL_ADMIN(
            ApproveNodeEnum.INTL_ADMIN_APPROVAL,
            IntlAdminApproval.class,
            APPROVE_RESULT_INTL_ADMIN,
            "Activity_1mkkvue","intl_admin_approval"),
    ;

    private static final String RESULT_FIELD_PREFIX = "approve_result_";

    private final ApproveNodeEnum counterSignNode;

    private final Class<? extends BaseSubEntity> subEntity;

    private final FlowVariantEnum resultVariant;
    /**
     * 流程节点id
     */
    private final String flowNodeId;

    private final String mainTableEntityCid;

    public String getResultFieldId() {
        return RESULT_FIELD_PREFIX + counterSignNode.name().toLowerCase();
    }

    public static CounterSignNodeEnum fromNodeCode(String nodeCode) {
        for (CounterSignNodeEnum value : values()) {
            if (value.getCounterSignNode().name().equals(nodeCode)) {
                return value;
            }
        }

        throw new IllegalArgumentException(nodeCode);
    }
}
