package com.zte.iccp.itech.extension.openapi.scheduler;


import com.zte.iccp.itech.extension.ability.clockin.callplan.ClockInCallPlanAbility;
import com.zte.iccp.itech.extension.ability.clockin.reviews.ClockInReviewsAbility;
import com.zte.iccp.itech.extension.ability.clockin.reviews.ClockInReviewsCreateAbility;
import com.zte.iccp.itech.extension.common.utils.AlarmUtils;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.LocalCacheUtils;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> jiangjiawen
 * @date 2024/10/10
 */
@Slf4j
public class ClockInTaskOpenApi extends AbstractOpenApi {
    /**
     * 汇总呼叫定时任务(仅创建呼叫计划)
     * 22:35，0：35，4：35，这几个时间点查询的数据需要偏离30分钟
     * 6:35 这个时间点查询的数据为即时数据
     */
    public void clockInSummaryCallJob(){
        ClockInCallPlanAbility.handleClockInSummary();
    }

    /**
     * 多个定时任务的入口，每分钟执行
     */
    public void clockInSchedulerJob(){
        Runnable[] asyncTasks = new Runnable[] {
                // 刷新本地缓存
                LocalCacheUtils::refresh
        };

        Runnable[] syncTasks = new Runnable[] {
                // 处理呼叫计划
                ClockInCallPlanAbility::handleCallPlan,
                // 扫描符合条件的打卡任务来创建复盘任务
                ClockInReviewsCreateAbility::create,
                // 扫描整改中的复盘任务，将符合条件的推到已关闭
                ClockInReviewsAbility::checkRectificationReviews
        };

        for (Runnable task : asyncTasks) {
            AsyncExecuteUtils.execute(() -> {
                try {
                    task.run();
                } catch (Exception e) {
                    AlarmUtils.major("Clock In Scheduler Job Error", e);
                }
            });
        }

        for (Runnable task : syncTasks) {
            try {
                task.run();
            } catch (Exception e) {
                AlarmUtils.major("Clock In Scheduler Job Error", e);
            }
        }
    }
}
