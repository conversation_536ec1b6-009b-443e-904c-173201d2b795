package com.zte.iccp.itech.extension.ability.changeorder;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.AvailabilityEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.FORWARD_SLASH;
import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * 审批人能力类
 *
 * <AUTHOR> 10347404
 * @since 2024/04/28
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ApproverAbility {

    public static List<ApproverConfiguration> query(List<String> fieldList,
                                                    List<IFilter> conditionFilters) {
        if (CollectionUtils.isEmpty(conditionFilters)) {
            return Lists.newArrayList();
        }
        return QueryDataHelper.query(ApproverConfiguration.class, fieldList, conditionFilters);
    }

    /**
     * 检索一体化关联产品配置信息
     *
     * @param approvalNodeList 审批节点配置名称（必填）
     * @param ids 主键id (可为null)
     * @param productIdPath 产品小类
     * @return List<ApproverConfiguration>
     */
    public static List<ApproverConfiguration> getApproveConfiguration(List<String> approvalNodeList, List<String> ids, String productIdPath) {
        if (CollectionUtils.isEmpty(approvalNodeList)) {
            return new ArrayList<>();
        }
        List<String> fieldList = new ArrayList<>();
        List<IFilter> conditionFilters = new ArrayList<>();
        conditionFilters.add(new Filter(APPROVAL_NODE, Comparator.IN, approvalNodeList));
        // 状态启用
        conditionFilters.add(new Filter(BILLSTATUSFIELD_STATUS, Comparator.IN, Lists.newArrayList(AvailabilityEnum.ENABLED.name())));
        if (!CollectionUtils.isEmpty(ids)) {
            conditionFilters.add(new Filter(ID, Comparator.IN, ids));
        }
        // 增加产品小类过滤条件
        if (StringUtils.isNotEmpty(productIdPath)) {
            conditionFilters.add(new Filter(PRODUCT_SUB_CATEGORY, Comparator.IN, Lists.newArrayList(productIdPath)));
        }

        return QueryDataHelper.query(ApproverConfiguration.class, fieldList, conditionFilters);
    }

    /**
     * 检索一体化关联产品配置信息
     *
     * @param approveRoleEnum 角色 - 网络部、研发部
     * @param associatedProductIds 关联配置表主键 这里指的是associated_product的id
     * @return List<ApproverConfiguration>
     */
    public static List<ApproverConfiguration> query(ApproveRoleEnum approveRoleEnum, List<String> associatedProductIds, String productIdPath) {
        if (approveRoleEnum == null || CollectionUtils.isEmpty(associatedProductIds)) {
            return Collections.emptyList();
        }

        List<IFilter> conditionFilters = new ArrayList<>();
        conditionFilters.add(
                new Filter(INTEGRATED_ASSOCIATED_PRODUCT_ZH, Comparator.IN, associatedProductIds)
                        .and(new Filter(ROLE, Comparator.IN, Lists.newArrayList(approveRoleEnum.name())))
                        .and(new Filter(PRODUCT_SUB_CATEGORY, Comparator.IN, Lists.newArrayList(productIdPath)))
        );

        return QueryDataHelper.query(ApproverConfiguration.class, Lists.newArrayList(), conditionFilters);
    }

    /**
     * CCN 默认授权文件 - 检索代表处产品科长 / 项目TD
     * @param organizationIdList
     * @param productIdList
     * @return Map<String, List<Employee>>
     */
    public static Map<String, List<Employee>> getCcnApplicationLeader(List<String> organizationIdList, List<String> productIdList) {
        if (CollectionUtils.isEmpty(organizationIdList) || CollectionUtils.isEmpty(productIdList)) {
            return new HashMap<>();
        }

        // 1.代表处格式化
        Set<String> representativeOfficeSet = new HashSet<>();
        organizationIdList.forEach(item -> {
            String[] organizationArray = item.split(CommonConstants.FORWARD_SLASH);
            representativeOfficeSet.add(
                    StringUtils.join(Arrays.stream(organizationArray).limit(4).toArray(String[]::new), CommonConstants.FORWARD_SLASH));
        });

        // 2.产品线格式化
        Set<String> productLineSet = new HashSet<>();
        productIdList.forEach(item -> {
            String[] productArray = item.split(CommonConstants.FORWARD_SLASH);
            productLineSet.add(
                    StringUtils.join(Arrays.stream(productArray).limit(2).toArray(String[]::new), CommonConstants.FORWARD_SLASH) + FORWARD_SLASH);
        });

        // 3.检索字段
        List<String> fieldList = Lists.newArrayList(RESPONSIBLE_DEPT, PRODUCT_LINE, APPROVER_PERSON);

        // 4.检索条件
        List<IFilter> conditionFilters = Lists.newArrayList();
        conditionFilters.add(
                new Filter(APPROVAL_NODE, Comparator.IN,
                        Lists.newArrayList(ApprovalTypeEnum.PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE.name())));
        conditionFilters.add(
                new Filter(ROLE, Comparator.IN,
                        Lists.newArrayList(ApproveRoleEnum.PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE_PROJECT_TD.name())));
        conditionFilters.add(new Filter(RESPONSIBLE_DEPT, Comparator.IN, new ArrayList<>(representativeOfficeSet)));
        conditionFilters.add(new Filter(PRODUCT_LINE, Comparator.IN, new ArrayList<>(productLineSet)));

        // 5.检索审批人配置
        List<ApproverConfiguration> approverList = ApproverAbility.query(fieldList, conditionFilters);
        if (CollectionUtils.isEmpty(approverList)) {
            return new HashMap<>();
        }
        Map<String, List<ApproverConfiguration>> approverMap = approverList.stream()
                .collect(Collectors.groupingBy(item -> item.getResponsibleDeptId() + HYPHEN + item.getProdLine(), Collectors.toList()));

        // 6.包装审批人 Map
        Map<String, List<Employee>> leaderMap = new HashMap<>();
        for (Map.Entry<String, List<ApproverConfiguration>> entry : approverMap.entrySet()) {
            List<Employee> employees = entry.getValue().stream().filter(v -> !CollectionUtils.isEmpty(v.getApproverPersons()))
                    .map(approverConfiguration -> approverConfiguration.getApproverPersons().get(0)).collect(Collectors.toList());

            leaderMap.put(entry.getKey(), employees);
        }

        return leaderMap;
    }

    /**
     * 检索故障管理经理
     */
    private static List<Employee> getFaultManager(ApproveRoleEnum approveRole) {
        IFilter approvedNodeFilter = new Filter(
                APPROVAL_NODE,
                Comparator.EQ,
                Lists.newArrayList(ApprovalTypeEnum.FAULT_MANAGER.name()));
        IFilter roleFilter = new Filter(
                ROLE,
                Comparator.EQ,
                Lists.newArrayList(approveRole.name()));

        List<ApproverConfiguration> approvers = QueryDataHelper.query(
                ApproverConfiguration.class,
                Lists.newArrayList(ROLE, APPROVER_GROUP),
                Lists.newArrayList(approvedNodeFilter, roleFilter));
        return approvers.stream()
                .map(ApproverConfiguration::getApproverGroups)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 检索故障管理经理 - 全路径
     */
    public static List<Employee> getFaultManagerByWholePath(String wholeOrganization) {
        if (StringUtils.isBlank(wholeOrganization)) {
            return Lists.newArrayList();
        }

        DeptTypeEnum deptType = ResponsibleUtils.getDeptType(wholeOrganization);
        return getFaultManager(DeptTypeEnum.INNER.equals(deptType)
                ? ApproveRoleEnum.DOMESTIC_FAULT_MANAGEMENT_MANAGER
                : ApproveRoleEnum.INTERNATIONAL_FAULT_MANAGEMENT_MANAGER);
    }

    /**
     * 检索故障管理经理
     */
    public static List<Employee> getFaultManager(String organization) {
        if (StringUtils.isBlank(organization)) {
            return Lists.newArrayList();
        }

        Map<String, BasicOrganizationInfo> organizationMap
                = HrClient.queryOrganizationInfo(Lists.newArrayList(organization));
        BasicOrganizationInfo organizationInfo = organizationMap.get(organization);
        if (Objects.isNull(organizationInfo)) {
            return Lists.newArrayList();
        }

        String idPath = organizationInfo.getOrgIDPath();
        if (StringUtils.isBlank(idPath)) {
            return Lists.newArrayList();
        }

        String wholePath = idPath.replaceAll(SPECIAL_HYPHEN, FORWARD_SLASH);
        return getFaultManagerByWholePath(wholePath);
    }
}
