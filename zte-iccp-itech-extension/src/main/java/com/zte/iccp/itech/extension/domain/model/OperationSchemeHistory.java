package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperationSchemeHistoryFieldConsts.*;


/**
 * <AUTHOR>
 * @date 2024/7/3 下午2:27
 */
@Getter
@Setter
@BaseEntity.Info("operation_scheme_history")
public class OperationSchemeHistory extends BaseEntity {

    @JsonProperty(value = SCHEME_NAME)
    private String schemeName;

    @JsonProperty(value = VERSION_NO)
    private String versionNo;

    @JsonProperty(value = SCHEME_ATTACHMENT)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile schemeAttachment;

    @JsonProperty(value = OPERATION_SCHEME_ID)
    private String operationSchemeId;
}
