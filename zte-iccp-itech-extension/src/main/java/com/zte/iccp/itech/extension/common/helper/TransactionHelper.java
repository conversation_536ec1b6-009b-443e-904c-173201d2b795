package com.zte.iccp.itech.extension.common.helper;

import com.zte.paas.lcap.ddm.domain.helper.service.PluginTranslationHelper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TransactionHelper {
    public static void run(Runnable runnable) {
        PluginTranslationHelper.openTranslation(runnable);
    }

    public static <T> T run(Supplier<T> supplier) {
        List<T> wrapper = new ArrayList<>(1);
        PluginTranslationHelper.openTranslation(() -> {
            wrapper.add(supplier.get());
        });
        return wrapper.get(0);
    }
}
