package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.OperationCache;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/10/12
 */
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class BaseOperationCacheAbility {
    protected final OperationCache operationCache;

    protected void create(List<? extends BaseEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        if (operationCache == null) {
            SaveDataHelper.batchCreate(entities);
        } else {
            operationCache.create(entities);
        }
    }

    protected void update(List<? extends BaseEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        if (operationCache == null) {
            SaveDataHelper.batchUpdate(entities);
        } else {
            operationCache.update(entities);
        }
    }

    protected void update(Class<? extends BaseEntity> entityClass, List<Map<String, Object>> multiValues) {
        if (CollectionUtils.isEmpty(multiValues)) {
            return;
        }

        if (operationCache == null) {
            SaveDataHelper.batchUpdate(entityClass, multiValues);
        } else {
            operationCache.update(entityClass, multiValues);
        }
    }

    protected void delete(Class<? extends BaseEntity> entityClass, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        if (operationCache == null) {
            SaveDataHelper.batchDelete(entityClass, ids);
        } else {
            operationCache.delete(entityClass, ids);
        }
    }
}
