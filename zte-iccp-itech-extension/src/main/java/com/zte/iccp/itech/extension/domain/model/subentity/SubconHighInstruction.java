package com.zte.iccp.itech.extension.domain.model.subentity;

import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;

/**
 * <AUTHOR>
 * @since 2024/10/17
 */
@BaseSubEntity.Info(value = "hzs_high_risk_instruction_table", parent = SubcontractorChangeOrder.class)
public class SubconHighInstruction extends HighInstruction {
}
