package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.TechnicalSolutionCheckConsts.*;

/**
 * 基础资料--技术方案检查/核心网产品外场质量保证规范动作对象
 *
 * <AUTHOR> 10335201
 * @date 2024-05-15 上午11:35
 **/
@Setter
@Getter
@BaseEntity.Info("technical_solution_check")
public class TechnicalSolutionCheck extends BaseEntity {

    @JsonProperty(value = CHECK_CODE)
    private String checkCode;

    @JsonProperty(value = CHECK_CONTENT_ZH)
    private String checkContentZh;

    @JsonProperty(value = CHECK_CONTENT_EN)
    private String checkContentEn;

    @JsonProperty(value = CHECK_NODE)
    private String checkNode;

    @JsonProperty(value = SPECIAL_OPTION_ZH)
    private String specialOptionZh;

    @JsonProperty(value = SPECIAL_OPTION_EN)
    private String specialOptionEn;

    @JsonProperty(value = SORT)
    private String sort;

    @JsonProperty(value = IS_INVALIDATED)
    private String isInvalidated;

    @JsonProperty(value = UPGRADE_TASK_CHECK_FLAG)
    private String upgradeTaskCheckFlag;

    @JsonProperty(value = GROUP_ONE_ZH)
    private String groupOneZh;

    @JsonProperty(value = GROUP_ONE_EN)
    private String groupOneEn;

    @JsonProperty(value = GROUP_TWO_ZH)
    private String groupTwoZh;

    @JsonProperty(value = GROUP_TWO_EN)
    private String groupTwoEn;
}
