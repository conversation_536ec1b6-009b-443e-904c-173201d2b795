package com.zte.iccp.itech.extension.domain.enums.changeorder;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/01/16
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum BatchOperationResultEnum {
    /** 内部变更批次任务 */
    ALL_COMPLETED("1","全部完成且成功","All completed and successful"),
    /** 部分完成且达标 */
    PARTIALLY_COMPLETED("2","部分完成且达标","Partially completed and up to standard"),
    /** 部分失败（有异常） */
    PARTIAL_FAILURE("3","部分失败（有异常）","Partial failure (with errors)"),
    /** 操作失败（回退） */
    OPERATION_FAILED("4","操作失败（回退）","Operation failed (rollback)"),
    /** "操作已取消" */
    OPERATION_CANCELLED("5","操作已取消","Operation cancelled"),
    ;
    private final String value;
    private final String zhCn;
    private final String enUs;
}
