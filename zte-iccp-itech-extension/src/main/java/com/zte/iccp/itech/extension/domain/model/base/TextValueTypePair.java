package com.zte.iccp.itech.extension.domain.model.base;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 10309921
 * @since 2025/04/09
 */
@Getter
@Setter
@NoArgsConstructor
public class TextValueTypePair {

    private MultiLangTextType text;

    private String value;

    public TextValueTypePair(MultiLangTextType text, String value) {
        this.text = text;
        this.value = value;
    }

    public static List<TextValueTypePair> buildList(String value, String zhCn, String enUs, String type) {
        return new ArrayList<>(Collections.singletonList(new TextValueTypePair(
                new MultiLangTextType(zhCn, enUs, type),
                value
        )));
    }
}
