package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.enums.AvailabilityEnum;
import com.zte.iccp.itech.extension.domain.model.AssociatedProductAttribute;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssociatedProductConsts.BILLSTATUSFIELD_ASSOCIATED_PRODUCT;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * 关联产品基础配置
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/12
 */
public class AssociatedProductAbility {

    /**
     * 根据id检索基础配置信息
     *
     * @param idList idList
     * @return List<AssociatedProductAttribute>
     */
    public static List<AssociatedProductAttribute> query(List<String> idList, boolean isEnabled) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        Filter filter = new Filter(ID, Comparator.IN, idList);
        // 基础配置表可以设定是否启用，如果设定禁止 新增和编辑的时候不展示进展的数据
        if (isEnabled) {
            filter.and(new Filter(BILLSTATUSFIELD_ASSOCIATED_PRODUCT, Comparator.EQ, AvailabilityEnum.ENABLED.name()));
        }
        return QueryDataHelper.query(AssociatedProductAttribute.class, Lists.newArrayList(), Lists.newArrayList(filter));
    }

    public static List<AssociatedProductAttribute> query(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        Filter filter = new Filter(ID, Comparator.IN, idList);

        return QueryDataHelper.query(AssociatedProductAttribute.class, Lists.newArrayList(), Lists.newArrayList(filter));
    }
}
