package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.ApprovalAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.DEFAULT_APPROVER_KEY;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CHANGE_ORDER_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * <AUTHOR>
 * &#064;description:  获取审批人
 * &#064;date  2024/4/28 20:45
 * zte-iccp-itech-netchange
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RewardApproverAbility {

    /** 批次 - 操作结果审核 */
    private static final List<SingletonTextValuePairsProvider> BATCH_OPERATION_RESULT_REVIEWERS = Lists.newArrayList(
            ApproveNodeEnum.NET_SERVICE_DEPT_APP,
            ApproveNodeEnum.TD_NET_DEPT_APPROVE,
            PartnerApproveNodeEnum.PARTNER_NET_OWNER_APPROVAL);

    /** 内部批次 - 操作结果审核审核人获取规则 */
    private static final List<ApproveNodeEnum> BATCH_OPERATION_RESULT_REVIEWERS_NODE = Lists.newArrayList(
            // 1.当技术审批环节有网服部审核时，读取网服部审核节点的实际审核人；
            ApproveNodeEnum.NET_SERVICE_DEPT_APP,
            // 2.当技术审批环节没有网服部审核节点，但有 技术交付部/网络处审核时，取技术交付部/网络处审核节点的实际审核人；
            ApproveNodeEnum.TD_NET_DEPT_APPROVE,
            // 3.当技术审批环节即没有网服部审核节点，也没有技术交付部/网络处审核节点，只有代表处TD审核节点时，取代表处TD审核节点的实际审核人。
            ApproveNodeEnum.REP_PROD_TD_APPROVE);

    /**
     *
     * 通过节点和工单信息获取审批人
     * 团队节点审批人,返回格式已验证
     *if(body.getNodeElement()!=null
     *&&Lists.newArrayList("Activity_0id1qg5","Activity_1t3a2gz","Activity_0jw79ym","Activity_0d1nvyu")
     *.contains(body.getNodeElement().getNodeCode()))
     {
     *Map<String, String> m = MapUtils.newHashMap( * "a", "10284287,10282740",
     *"b", "10284287,10293704");
     *return new String[]{ * JsonUtils.toJsonString(m) *             };
     *}
     */
    @SneakyThrows
    public static List<String> getApprover(@NotNull ApproveFlowCodeEnum approveFlowEnum,
            @NotNull FlowClient body) {
        String nodeExtendName = body.getNodeElement().getNodeExtendName();
        if (isBatchOperationResultReviewers(approveFlowEnum, nodeExtendName)) {
            return getBatchOperationResultReviewers(body.getBusinessId(), approveFlowEnum, nodeExtendName);
        }

        AbstractRewardApproverHandler handler = null;
        List<String> fieldList = new ArrayList<>();
        //按照流程类型判断
        if(ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW == approveFlowEnum
                || ApproveFlowCodeEnum.BATCH_TASK_FLOW == approveFlowEnum){
            ApproveNodeEnum approveNodeEnum = ApproveNodeEnum.getApproveNodeEnum(nodeExtendName);
            if(null != approveNodeEnum){
                handler = approveNodeEnum.getHandler();
                fieldList = approveNodeEnum.getFieldList();
            }
        }else if(ApproveFlowCodeEnum.SUBCONTRACTOR_OC_FLOW == approveFlowEnum
                || ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowEnum){
            PartnerApproveNodeEnum approveNodeEnum = PartnerApproveNodeEnum.getApproveNodeEnum(nodeExtendName);
            if(null != approveNodeEnum){
                handler = approveNodeEnum.getHandler();
                fieldList = approveNodeEnum.getFieldList();
            }
        }
        /**
         * 获取流程实际使用的主体（子任务需要转换）
         */
        String businessId = getFlowEntityId(body.getBusinessId(), approveFlowEnum);
        List<String> approvalList = new ArrayList<>();
        if (handler != null) {
            //走个性化
            approvalList = AbstractRewardApproverHandler.rewardApproverByNode(handler, businessId, approveFlowEnum, body);
        } else if (fieldList != null) {
            // 字段直接获取
            JSONObject jsonObject = QueryDataHelper.getRaw(approveFlowEnum.getApproverEntity(), fieldList, businessId);
            approvalList = getApproverFromOrderField(jsonObject, fieldList, approveFlowEnum);
        }
        return approvalList;
    }

    /**
     * 转换位实际主体对象id
     * @param businessId
     * @param approveFlowEnum
     * @return
     */
    private static String getFlowEntityId(String businessId, ApproveFlowCodeEnum approveFlowEnum) {
        if(ApproveFlowCodeEnum.BATCH_TASK_FLOW == approveFlowEnum) {
            BatchTask baseEntity = QueryDataHelper.get(
                    BatchTask.class, Arrays.asList(ID, CHANGE_ORDER_ID), businessId);
            if(null != baseEntity){
                return baseEntity.getChangeOrderId();
            }
        }else if (ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowEnum){
            SubcontractorBatchTask baseEntity = QueryDataHelper.get(
                    SubcontractorBatchTask.class, Arrays.asList(ID, CHANGE_ORDER_ID), businessId);
            if(null != baseEntity){
                return baseEntity.getChangeOrderId();
            }
        }
        return  businessId;
    }

    /**
     * 通过字段名直接获取审批人
     */
    @SneakyThrows
    public static List<String> getApproverFromOrderField(JSONObject jsonObject,
            List<String> fieldList, ApproveFlowCodeEnum approveFlowEnum) {
        List<String> approvalList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fieldList) || null == jsonObject) {
            return approvalList;
        }
        for (String fieldKey : fieldList) {
            Field field = JsonUtils.findField(approveFlowEnum.getApproverEntity(), fieldKey);
            if (field == null) {
                log.error("未找到字段: " + fieldKey);
                throw new IllegalArgumentException(fieldKey);
            }
            if(null != jsonObject.get(fieldKey)){
                List<Employee> approver = new ArrayList<>();
                String userStr = jsonObject.get(fieldKey).toString();
                if(userStr.startsWith(CommonConstants.MEDIUM_PARENTHESES_START)){
                    // 兼容单个和多个处理
                    approver = JSON.parseArray(userStr, Employee.class);
                }else{
                    approver.add(JSON.parseObject(userStr, Employee.class));
                }
                approvalList.addAll(approver.stream()
                        .map(Employee::getEmpUIID)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList()));
            }
        }
        return approvalList;
    }

    /*
     * 获取流程节点当前审批人
     * */
    public static List<String> getFlowApproverList(FlowClient body, String nodeKey, ApproveFlowCodeEnum approveFlowEnum) {
        //获取审批人
        List<String> approverList = getApprover(approveFlowEnum, body);
        if (CollectionUtils.isEmpty(approverList)) {
            log.error("approver not found! nodeKey:{}， id:{}", nodeKey, body.getBusinessId());
            return ConfigHelper.getList(DEFAULT_APPROVER_KEY);
        }
        return approverList;
    }

    /**
     * 是否批次 - 操作结果审核节点
     * @param approveFlowEnum 流程编码对应流程信息
     * @param nodeExtendName 节点编码
     * @return 是否批次操作结果审核节点
     */
    private static boolean isBatchOperationResultReviewers(ApproveFlowCodeEnum approveFlowEnum, String nodeExtendName) {
        if (ApproveFlowCodeEnum.BATCH_TASK_FLOW == approveFlowEnum
                || ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowEnum) {
            SingletonTextValuePairsProvider approveNodeName = ApprovalAbility.getApproveNodeName(
                    approveFlowEnum.getFlowEntity(), nodeExtendName);
            // 当前为批次操作结果审核节点
            return BATCH_OPERATION_RESULT_REVIEWERS.contains(approveNodeName);
        }
        return false;
    }

    /**
     * 获取批次 - 操作结果审核节点审核人+审核组
     *
     * @param batchId 批次id
     * @param approveFlowEnum 流程编码对应流程信息
     * @param nodeExtendName 节点编码
     * @return 审核人+审核组
     */
    private static List<String> getBatchOperationResultReviewers(String batchId,
                                                                 ApproveFlowCodeEnum approveFlowEnum,
                                                                 String nodeExtendName) {
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(
                approveFlowEnum.getFlowEntity(), Arrays.asList(ID, CHANGE_ORDER_ID), batchId);
        String changeOrderId = batchTask.getChangeOrderId();

        List<ApproveRecord> approveRecordList = FlowHelper.getApprovedRecords(changeOrderId);
        Map<String, String> extendedCodeToApproverMap = approveRecordList.stream()
                .collect(Collectors.toMap(ApproveRecord::getExtendedCode, ApproveRecord::getApprover, (o1, o2) -> o1));

        if (ApproveFlowCodeEnum.BATCH_TASK_FLOW == approveFlowEnum) {
            return getBatchOperationResultApprover(changeOrderId, extendedCodeToApproverMap);
        }

        return getHzfBatchOperationResultApprover(changeOrderId, approveFlowEnum, nodeExtendName, extendedCodeToApproverMap);
    }

    /**
     * 获取内部批次 - 操作结果审核节点审核人+支持人员
     *
     * @param changeOrderId 网络变更单id
     * @param extendedCodeToApproverMap 节点编码和实际审核人map
     * @return 审核人+审核组人员
     */
    private static List<String> getBatchOperationResultApprover(String changeOrderId,
                                                                Map<String, String> extendedCodeToApproverMap) {
        List<String> approvalList = new ArrayList<>();
        for (ApproveNodeEnum approveNodeEnum : BATCH_OPERATION_RESULT_REVIEWERS_NODE) {
            String approver = extendedCodeToApproverMap.get(approveNodeEnum.name());
            if (StringUtils.isEmpty(approver)) {
                continue;
            }
            approvalList.add(approver);
            approvalList.addAll(ChangeOrderAbility.getSupportPersonnels(approveNodeEnum.getSupportStaffEntity(), changeOrderId));
            return approvalList;
        }
        return null;
    }

    /**
     * 获取合作方内部批次 - 操作结果审核节点支持人员
     *
     * @param changeOrderId 网络变更单id
     * @param approveFlowEnum 流程编码对应流程信息
     * @param nodeExtendName 当前节点编码
     * @param extendedCodeToApproverMap 节点编码和实际审核人map
     * @return 审核人+审核组人员
     */
    private static List<String> getHzfBatchOperationResultApprover(String changeOrderId,
                                                                   ApproveFlowCodeEnum approveFlowEnum,
                                                                   String nodeExtendName,
                                                                   Map<String, String> extendedCodeToApproverMap) {
        List<String> approvalList = new ArrayList<>();
        SingletonTextValuePairsProvider approveNodeName = ApprovalAbility.getApproveNodeName(
                approveFlowEnum.getFlowEntity(), nodeExtendName);
        PartnerApproveNodeEnum approveNodeEnum = (PartnerApproveNodeEnum) approveNodeName;
        String approver = extendedCodeToApproverMap.get(nodeExtendName);
        if (StringUtils.isEmpty(approver)) {
            return approvalList;
        }

        approvalList.add(approver);
        assert approveNodeEnum != null;
        approvalList.addAll(ChangeOrderAbility.getSupportPersonnels(approveNodeEnum.getSupportStaffEntity(),changeOrderId));
        return approvalList;
    }

}
