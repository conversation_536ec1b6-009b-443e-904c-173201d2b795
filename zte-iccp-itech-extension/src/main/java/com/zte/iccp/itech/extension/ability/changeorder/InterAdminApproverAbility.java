package com.zte.iccp.itech.extension.ability.changeorder;

import com.google.common.base.Functions;
import com.zte.iccp.itech.extension.ability.HolAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.FilterHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.ListUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.OrgLvlConsts;
import com.zte.iccp.itech.extension.domain.constant.ProdCateLvlConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.AvailabilityEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.IntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.vo.SubIntlAdminApproval;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.CUSTOMER_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.RISK_EVALUATION;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.PRODUCT_CATEGORY;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.IntlAdminApprovalFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum.*;
import static com.zte.paas.lcap.common.constant.StringConst.SLASH;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class InterAdminApproverAbility {
    public static boolean isIncomplete2Judge(ChangeOrder changeOrder) {
        return changeOrder.getResponsibleDept() == null
                || changeOrder.getProductCategory() == null
                || changeOrder.getIsEmergencyOperation() == null
                || changeOrder.getOperationStartTime() == null
                || changeOrder.getOperationEndTime() == null
                || changeOrder.getCustomerId() == null;
    }

    public static List<String> getInterCountersignApprover(ChangeOrder changeOrder) {
        if (isIncomplete2Judge(changeOrder)) {
            return null;
        }

        Map<ApproveRoleEnum, List<Employee>> approver = getInterCountersignApproverMap(changeOrder);
        return approver.values().stream()
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(Employee::getId)
                .distinct()
                .collect(Collectors.toList());
    }

    public static void saveInterCountersignApprover(ChangeOrder changeOrder) {
        Map<ApproveRoleEnum, List<Employee>> approver = getInterCountersignApproverMap(changeOrder);
        List<IntlAdminApproval> target = approver.entrySet().stream()
                .flatMap(e -> e.getValue().stream()
                        .map(person -> new IntlAdminApproval() {{
                            setPid(changeOrder.getId());
                            setRole(e.getKey());
                            setApprover(SingleEmployee.from(person));
                        }}))
                .collect(Collectors.toList());

        List<IntlAdminApproval> exists = QueryDataHelper.query(
                IntlAdminApproval.class,
                Lists.newArrayList(ID, ROLE_ID, APPROVED_BY),
                changeOrder.getId());

        List<String> toDelete = Lists.newArrayList();
        for (IntlAdminApproval e : exists) {
            if (target.stream().noneMatch(t -> roleUserEq(e, t))) {
                toDelete.add(e.getId());
            }
        }

        target.removeIf(toAdd -> exists.stream().anyMatch(e -> roleUserEq(e, toAdd)));

        SaveDataHelper.batchDelete(IntlAdminApproval.class, toDelete);
        SaveDataHelper.batchCreate(target);
    }

    /**
     * 批次创建国际会签审批记录
     * @param changeOrder
     * @param batchTask
     * return  是否有查询到会签审核人
     */
    public static boolean saveBatchInterCountersignApprover(ChangeOrder changeOrder, BatchTask batchTask) {
        // 国际会签审核人查询，批次任务需要根据批次任务的时间范围查询
        changeOrder.setOperationStartTime(batchTask.getPlanOperationStartTime());
        changeOrder.setOperationEndTime(batchTask.getPlanOperationEndTime());
        changeOrder.setIsEmergencyOperation(batchTask.getUrgentFlag());
        Map<ApproveRoleEnum, List<Employee>> approver = getInterCountersignApproverMap(changeOrder);
        List<SubIntlAdminApproval> target = new ArrayList<>();
        for (Map.Entry entry: approver.entrySet()) {
            ApproveRoleEnum roleEnum = (ApproveRoleEnum) entry.getKey();
            List<Employee> employees = (List<Employee>) entry.getValue();
            employees.forEach(item -> {
                SubIntlAdminApproval approval = new SubIntlAdminApproval();
                approval.setApprover(SingleEmployee.from(item));
                approval.setPid(batchTask.getId());
                approval.setRole(roleEnum);
                target.add(approval);
            });
        }

        List<SubIntlAdminApproval> exists = QueryDataHelper.query(
                SubIntlAdminApproval.class,
                Lists.newArrayList(ID, ROLE_ID, APPROVED_BY),
                batchTask.getId());

        List<String> toDelete = Lists.newArrayList();
        for (SubIntlAdminApproval e : exists) {
            if (target.stream().noneMatch(t -> roleUserEq(e, t))) {
                toDelete.add(e.getId());
            }
        }
        target.removeIf(toAdd -> exists.stream().anyMatch(e -> roleUserEq(e, toAdd)));
        SaveDataHelper.batchDelete(SubIntlAdminApproval.class, toDelete);
        SaveDataHelper.batchCreate(target);
        return !CollectionUtils.isEmpty(approver) || !CollectionUtils.isEmpty(exists);
    }

    /**
     * 批次判断是否需要国际会签审批
     * @param changeOrderId
     * @param batchTask
     * return  是否有查询到会签审核人
     */
    public static boolean isBatchSignApprover(String changeOrderId, BatchTask batchTask) {
        // 国际会签审核人查询，批次任务需要根据批次任务的时间范围查询
        ChangeOrder changeOrder = ChangeOrderAbility.get(changeOrderId,
                Lists.newArrayList(ChangeOrderFieldConsts.RESPONSIBLE_DEPT, CUSTOMER_ID, RISK_EVALUATION, PRODUCT_CATEGORY));
        if(changeOrder == null){
            return false;
        }
        changeOrder.setOperationStartTime(batchTask.getPlanOperationStartTime());
        changeOrder.setOperationEndTime(batchTask.getPlanOperationEndTime());
        changeOrder.setIsEmergencyOperation(batchTask.getUrgentFlag());
        Map<ApproveRoleEnum, List<Employee>> approver = getInterCountersignApproverMap(changeOrder);
        return !CollectionUtils.isEmpty(approver);
    }

    private static boolean roleUserEq(IntlAdminApproval a, IntlAdminApproval b) {
        return a.getRole() == b.getRole()
                && a.getApprover().getId().equals(b.getApprover().getId());
    }

    private static Map<ApproveRoleEnum, List<Employee>> getInterCountersignApproverMap(ChangeOrder changeOrder) {
        List<ApproveRoleEnum> roles = QueryDataHelper.query(
                        ApproverConfiguration.class,
                        Lists.newArrayList(ROLE, OPERATION_START_TIME, OPERATION_END_TIME),
                        buildRoleQueryFilters(changeOrder)).stream()
                .map(ApproverConfiguration.class::cast)
                .filter(config -> {
                    int start = 0;
                    if (config.getOperationStartTime() != null) {
                        start = config.getOperationStartTime();
                    }
                    int end = 24 * 60 * 60;
                    if (config.getOperationEndTime() != null) {
                        end = config.getOperationEndTime();
                    }

                    return DateUtils.isOverTimeRange(
                            changeOrder.getOperationStartTime(), changeOrder.getOperationEndTime(), start, end);
                }).map(ApproverConfiguration::getRole)
                .distinct()
                .collect(Collectors.toList());

        List<ApproverConfiguration> apprConfigs = queryProdConfigs(changeOrder, roles);
        apprConfigs.addAll(queryDeptConfigs(changeOrder, roles));

        Map<ApproveRoleEnum, List<Employee>> result = groupByRole(apprConfigs);
        roles.stream()
                .filter(r -> r.getOrgId() != null && r.getRoleId() != null)
                .forEach(r -> result.put(r, HolAbility.getOrgLeader(r.getOrgId(), r.getRoleId())));
        return result;
    }

    private static Map<ApproveRoleEnum, List<Employee>> groupByRole(
            List<ApproverConfiguration> appConfigs) {
        return appConfigs.stream()
                .collect(Collectors.toMap(
                        ApproverConfiguration::getRole,
                        conf -> {
                            List<Employee> persons = CollectionUtils.isEmpty(conf.getApproverPersons())
                                    ? Lists.newArrayList()
                                    : conf.getApproverPersons();
                            List<Employee> groups = CollectionUtils.isEmpty(conf.getApproverGroups())
                                    ? Lists.newArrayList()
                                    : conf.getApproverGroups();
                            persons.addAll(groups);
                            return ListUtils.distinct(persons, Employee::getId);
                        }, (r1, r2) -> {
                            r1.addAll(r2);
                            return ListUtils.distinct(r1, Employee::getId);
                        }));
    }

    private static List<IFilter> buildRoleQueryFilters(ChangeOrder changeOrder) {
        String[] prodIdParts = getProdIdPaths(changeOrder.getProductCategory());

        return Lists.newArrayList(
                new Filter(APPROVAL_NODE, Comparator.EQ, Lists.newArrayList(ADMINISTRATIVE_LEADER_INTERNATIONAL_COUNTERSIGNING)),
                emptyOrEquals(PROD_OPERATION_TEAM, Lists.newArrayList(prodIdParts[ProdCateLvlConsts.TEAM - 1])),
                emptyOrEquals(PRODUCT_LINE, Lists.newArrayList(prodIdParts[ProdCateLvlConsts.LINE - 1])),
                emptyOrEquals(PROD_MAIN_CATEGORY, Lists.newArrayList(prodIdParts[ProdCateLvlConsts.MAIN_CATEGORY - 1])),
                emptyOrEquals(PRODUCT_SUB_CATEGORY, Lists.newArrayList(prodIdParts[ProdCateLvlConsts.SUB_CATEGORY - 1])),
                emptyOrEquals(RESPONSIBLE_DEPT, Lists.newArrayList(changeOrder.getResponsibleDept())),
                emptyOrEquals(CUSTOMER, changeOrder.getCustomerId()),
                emptyOrEquals(RISK_ASSESSMENT,
                        ObjectUtils.isEmpty(changeOrder.getRiskEvaluation()) ? null : changeOrder.getRiskEvaluation().getValue()),
                emptyOrEquals(IS_EMERGENCY_OPERATION, Lists.newArrayList(changeOrder.getIsEmergencyOperation())),
                emptyOrEquals(STATUS, Lists.newArrayList(AvailabilityEnum.ENABLED)));
    }

    private static List<ApproverConfiguration> queryProdConfigs(
            ChangeOrder changeOrder,
            List<ApproveRoleEnum> roles) {
        String[] prodIdParts = getProdIdPaths(changeOrder.getProductCategory());
        String[] orgIdParts = getOrgIdPaths(changeOrder.getResponsibleDept());

        List<IFilter> filters = Lists.newArrayList(
                new Filter(APPROVAL_NODE, Comparator.EQ, Lists.newArrayList(ADMINISTRATIVE_LEADER_PRODUCT_COUNTERSIGNING)),
                new Filter(ROLE, Comparator.IN, roles),
                emptyOrEquals(ORGANIZATION_REGION, Lists.newArrayList(orgIdParts[OrgLvlConsts.REGION - 1])),
                emptyOrEquals(PROD_OPERATION_TEAM, Lists.newArrayList(prodIdParts[ProdCateLvlConsts.TEAM - 1])),
                emptyOrEquals(PRODUCT_LINE, Lists.newArrayList(prodIdParts[ProdCateLvlConsts.LINE - 1])),
                emptyOrEquals(PROD_MAIN_CATEGORY, Lists.newArrayList(prodIdParts[ProdCateLvlConsts.MAIN_CATEGORY - 1])));
        List<ApproverConfiguration> apprConfigs = QueryDataHelper.query(
                ApproverConfiguration.class,
                Lists.newArrayList(ROLE, APPROVER_PERSON, APPROVER_GROUP, PRODUCT_LINE, PROD_MAIN_CATEGORY),
                filters);
        return new ArrayList<>(apprConfigs.stream()
                .collect(Collectors.toMap(
                        ApproverConfiguration::getRole,
                        Functions.identity(),
                        InterAdminApproverAbility::mergeProdConfigs))
                .values());
    }

    private static List<ApproverConfiguration> queryDeptConfigs(
            ChangeOrder changeOrder,
            List<ApproveRoleEnum> roles) {
        String[] orgIdParts = getOrgIdPaths(changeOrder.getResponsibleDept());

        List<IFilter> filters = Lists.newArrayList(
                new Filter(APPROVAL_NODE, Comparator.EQ, Lists.newArrayList(ADMINISTRATIVE_LEADER_RESPONSIBLE_DEPT_COUNTERSIGNING)),
                new Filter(ROLE, Comparator.IN, roles),
                emptyOrEquals(SALES, Lists.newArrayList(orgIdParts[OrgLvlConsts.SALES - 1])),
                emptyOrEquals(ORGANIZATION_REGION, Lists.newArrayList(orgIdParts[OrgLvlConsts.REGION - 1])),
                emptyOrEquals(RESPONSIBLE_DEPT, Lists.newArrayList(orgIdParts[OrgLvlConsts.DEPT - 1])));
        return QueryDataHelper.query(
                ApproverConfiguration.class,
                Lists.newArrayList(ROLE, APPROVER_PERSON, APPROVER_GROUP),
                filters);
    }

    private static ApproverConfiguration mergeProdConfigs(
            @NonNull ApproverConfiguration cfg1,
            @NonNull ApproverConfiguration cfg2) {
        ApproverConfiguration cfg = getNotBlank(cfg1, cfg2, ApproverConfiguration::getProdMainCategory);
        if (cfg != null) {
            return cfg;
        }

        cfg = getNotBlank(cfg1, cfg2, ApproverConfiguration::getProdLine);
        if (cfg != null) {
            return cfg;
        }

        return doMerge(cfg1, cfg2);
    }

    private static ApproverConfiguration getNotBlank(
            ApproverConfiguration cfg1,
            ApproverConfiguration cfg2,
            Function<ApproverConfiguration, String> getter) {
        String prop1 = getter.apply(cfg1);
        String prop2 = getter.apply(cfg2);
        if (StringUtils.isNotBlank(prop1) && StringUtils.isNotBlank(prop2)) {
            return doMerge(cfg1, cfg2);
        }

        if (StringUtils.isBlank(prop1) && StringUtils.isBlank(prop2)) {
            return null;
        }

        return StringUtils.isNotBlank(prop1) ? cfg1 : cfg2;
    }

    private static ApproverConfiguration doMerge(
            ApproverConfiguration cfg1,
            ApproverConfiguration cfg2) {
        List<Employee> person1 = cfg1.getApproverPersons() == null
                ? Lists.newArrayList() : cfg1.getApproverPersons();
        List<Employee> person2 = cfg2.getApproverPersons() == null
                ? Lists.newArrayList() : cfg2.getApproverPersons();
        person1.addAll(person2);

        List<Employee> group1 = cfg1.getApproverGroups() == null
                ? Lists.newArrayList() : cfg1.getApproverGroups();
        List<Employee> group2 = cfg2.getApproverGroups() == null
                ? Lists.newArrayList() : cfg2.getApproverGroups();
        group1.addAll(group2);

        cfg1.setApproverPersons(person1);
        cfg1.setApproverGroups(group1);
        return cfg1;
    }

    private static String[] getProdIdPaths(String prodIdPath) {
        String[] parts = prodIdPath.split(SLASH);
        String team = parts[ProdCateLvlConsts.TEAM - 1] + SLASH;
        String line = String.join(StringUtils.EMPTY, team, parts[ProdCateLvlConsts.LINE - 1], SLASH);
        String mainCate = String.join(StringUtils.EMPTY, line, parts[ProdCateLvlConsts.MAIN_CATEGORY - 1], SLASH);
        String subCate = String.join(StringUtils.EMPTY, mainCate, parts[ProdCateLvlConsts.SUB_CATEGORY - 1], SLASH);
        return new String[] { team, line, mainCate, subCate };
    }

    private static String[] getOrgIdPaths(String orgIdPath) {
        String[] parts = orgIdPath.split(SLASH);
        String zte = parts[OrgLvlConsts.ZTE - 1];
        String sales = String.join(SLASH, zte, parts[OrgLvlConsts.SALES - 1]);
        String region = String.join(SLASH, sales, parts[OrgLvlConsts.REGION - 1]);
        String dept = String.join(SLASH, region, parts[OrgLvlConsts.DEPT - 1]);
        return new String[] { zte, sales, region, dept };
    }

    private static IFilter emptyOrEquals(String field, Object value) {
        Object emptyValue = Lists.newArrayList();
        if (value instanceof String) {
            emptyValue = StringUtils.EMPTY;
        }

        return FilterHelper.newMultiFilter(
                new Filter(field, Comparator.IS_NULL, Lists.newArrayList())
                        .or(new Filter(field, Comparator.EQ, emptyValue))
                        .or(new Filter(field, Comparator.EQ, value)));
    }

    /**
     * 检索国际行政审核记录 - 网络变更
     * @param changeOrderId
     * @return List<IntlAdminApproval>
     */
    public static List<IntlAdminApproval> getApprovalRecordList(String changeOrderId) {
        if (StringUtils.isBlank(changeOrderId)) {
            return Lists.newArrayList();
        }

        List<String> fieldList = Lists.newArrayList(ID, ROLE_ID, APPROVE_RESULT, APPROVED_BY, APPROVE_OPINION, APPROVED_TIME);
        return QueryDataHelper.query(IntlAdminApproval.class, fieldList, changeOrderId);
    }

    /**
     * 检索国际行政审核记录 - 批次任务
     * @param batchTaskId
     * @return List<SubIntlAdminApproval>
     */
    public static List<SubIntlAdminApproval> getBatchApprovalRecordList(String batchTaskId) {
        if (StringUtils.isBlank(batchTaskId)) {
            return Lists.newArrayList();
        }

        List<String> fieldList = Lists.newArrayList(ID, ROLE_ID, APPROVE_RESULT, APPROVED_BY, APPROVE_OPINION, APPROVED_TIME);
        return QueryDataHelper.query(SubIntlAdminApproval.class, fieldList, batchTaskId);
    }

    /**
     * 更新国际行政审核记录 - 网络变更
     * @param pid
     * @param id
     * @param value
     */
    public static void updateApprovalRecord(String pid, String id, Map<String, Object> value) {
        if (StringUtils.isBlank(pid) || StringUtils.isBlank(id) || CollectionUtils.isEmpty(value)) {
            return;
        }

        SaveDataHelper.update(IntlAdminApproval.class, pid, id, value);
    }

    /**
     * 更新国际行政审核记录 - 批次任务
     * @param pid
     * @param id
     * @param value
     */
    public static void updateBatchApprovalRecord(String pid, String id, Map<String, Object> value) {
        if (StringUtils.isBlank(pid) || StringUtils.isBlank(id) || CollectionUtils.isEmpty(value)) {
            return;
        }

        SaveDataHelper.update(SubIntlAdminApproval.class, pid, id, value);
    }

    /**
     * 转交会签记录 - 网络变更
     */
    public static void transferInternationalCountersigningStaff(
            String changeOrderId,
            String userId,
            String transferUserId) {

        // 1.检索国际会签结果记录
        List<IntlAdminApproval> approvalRecords = getApprovalRecordList(changeOrderId);

        // 2.过滤出用户对应记录，更新为转交人
        List<IntlAdminApproval> transferRecords = approvalRecords.stream()
                .filter(item -> item.getApprover().getEmpUIID().equals(userId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transferRecords)) {
            throw new LcapBusiException(
                    MsgUtils.getMessage(MessageConsts.INTERNATIONAL_COUNTERSIGN_RECORD_NOT_FOUND));
        }

        // 3.更新审批记录
        List<Map<String, Object>> updateInfo = new ArrayList<>();
        transferRecords.forEach(record -> updateInfo.add(MapUtils.newHashMap(
                CommonFieldConsts.ID, record.getId(),
                APPROVED_BY, EmployeeHelper.getSingle(transferUserId))));
        SaveDataHelper.batchUpdate(IntlAdminApproval.class, changeOrderId, updateInfo);
    }

    /**
     * 转交会签记录 - 批次任务
     */
    public static void transferBatchInternationalCountersigningStaff(
            String batchId,
            String userId,
            String transferUserId) {

        // 1.检索国际会签结果记录
        List<SubIntlAdminApproval> approvalRecords = getBatchApprovalRecordList(batchId);

        // 2.过滤出用户对应记录，更新为转交人
        List<SubIntlAdminApproval> transferRecords = approvalRecords.stream()
                .filter(item -> item.getApprover().getEmpUIID().equals(userId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transferRecords)) {
            throw new LcapBusiException(
                    MsgUtils.getMessage(MessageConsts.INTERNATIONAL_COUNTERSIGN_RECORD_NOT_FOUND));
        }

        // 3.更新审批记录
        List<Map<String, Object>> updateInfo = new ArrayList<>();
        transferRecords.forEach(record -> updateInfo.add(MapUtils.newHashMap(
                CommonFieldConsts.ID, record.getId(),
                APPROVED_BY, EmployeeHelper.getSingle(transferUserId))));
        SaveDataHelper.batchUpdate(SubIntlAdminApproval.class, batchId, updateInfo);
    }
}