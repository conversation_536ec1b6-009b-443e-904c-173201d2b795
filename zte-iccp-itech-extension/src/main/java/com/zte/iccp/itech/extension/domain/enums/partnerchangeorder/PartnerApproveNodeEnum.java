package com.zte.iccp.itech.extension.domain.enums.partnerchangeorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApproveFlowTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.subentity.support.SupportStaffSubNetOwner;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iccp.itech.extension.handler.approver.partner.OfficeProdChiefHandlerImpl;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.partnerchangeorder.constant.PartnerProcessNodeNameConstants;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;
import static com.zte.iccp.itech.extension.domain.enums.ApproveFlowTypeEnum.PARTNER_NETWORK_CHANGE;
import static com.zte.iccp.itech.extension.plugin.form.approvaldetails.changeorder.constant.ProcessNodeNameConstants.BATCH_CANCEL_OPERATION_REVIEW_PN_NODE;
import static com.zte.iccp.itech.extension.plugin.form.approvaldetails.partnerchangeorder.constant.PartnerProcessNodeNameConstants.*;

/**
 * <AUTHOR>
 * @description: 分包商审批节点枚举
 * @date 2024/6/27
 */

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PartnerApproveNodeEnum implements SingletonTextValuePairsProvider {

    // 办事处产品科长审核
    PARTNER_REPRESENTATIVE_CHIEF_APPROVAL(
            PARTNER_NETWORK_CHANGE,
            new OfficeProdChiefHandlerImpl(),
            null,
            "办事处产品科长审核",
            "Review by Product Section Chief of Representative Office",
            PARTNER_REPRESENTATIVE_CHIEF),

    // 网络责任人审核
    PARTNER_NET_OWNER_APPROVAL(
            PARTNER_NETWORK_CHANGE,
            null,
            Lists.newArrayList(
                    SubcontractorChangeOrderFieldConsts.NETWORK_RESPONSIBLE_PERSON,
                    SubcontractorChangeOrderFieldConsts.NETWORK_RESPONSIBLE_TEAM),
            "网络责任人审核",
            "Review by Network Responsible Person",
            PARTNER_NET_OWNER, SupportStaffSubNetOwner.class),

    // 办事处产品经理审核
    PARTNER_OFFICE_MANAGER_APPROVAL(
            PARTNER_NETWORK_CHANGE,
            null,
            Lists.newArrayList(OFFICE_PROD_MANAGER_CID, OFFICE_PROD_MANAGER_REVIEW_TEAM_CID),
            "办事处产品经理审核",
            "Review by Representative Office Product Manager",
            PARTNER_OFFICE_MANAGER),

    // 网络处审核
    PARTNER_NET_DEPT_APPROVAL(
            PARTNER_NETWORK_CHANGE,
            null,
            Lists.newArrayList(NET_DEPT_APPROVAL, NET_DEPT_APPROVE_TEAM),
            "网络处审核",
            "Review by Network Service Office",
            PARTNER_NET_DEPT),

    // 行政审批_办事处PD
    PARTNER_OFFICE_PD_APPROVAL(
            PARTNER_NETWORK_CHANGE,
            null,
            Collections.singletonList(REPRESENTATIVE_OFFICE_PD_CID),
            "行政审批_办事处PD",
            "Administrative Review by PD of Representative Office",
            PARTNER_OFFICE_PD),

    // 行政审批_办事处产品科长
    PARTNER_OFFICE_PROD_CHIEF_APPROVAL(
            PARTNER_NETWORK_CHANGE,
            new OfficeProdChiefHandlerImpl(),
            null,
            "行政审批_办事处产品科长",
            "Administrative Review by Product Section Chief of Representative Office",
            PARTNER_OFFICE_PROD_CHIEF),

    // 操作计划审核
    PARTNER_OPERATION_PLAN_APPROVAL(
            PARTNER_NETWORK_CHANGE,
            new OfficeProdChiefHandlerImpl(),
            null,
            "操作计划审核",
            "Review Operation plan",
            REVIEW_OPERATION_PLAN),

    //  当前进展状态  无实际审批节点,仅做当前进展状态展示

    /*批次任务执行中*/
    BATCH_APPROVALING(null, null, null, "批次任务执行中",
            "batch task is being executed", PartnerProcessNodeNameConstants.BATCH_APPROVALING),

    /*草稿*/
    DRAFT(null, null, null, "草稿", "draft", PartnerProcessNodeNameConstants.DRAFT),

    /*驳回待处理*/
    REJECTION(null, null, null, "驳回待处理",
            "rejection to be handled", PartnerProcessNodeNameConstants.REJECTION),

    /*流程终止*/
    TERMINATION(null, null, null, "流程终止",
            "process termination", PartnerProcessNodeNameConstants.TERMINATION),

    /*操作取消*/
    CANCEL(null, null, null, "操作取消",
            "operation cancel", OPERATION_CANCEL),

    /*待发通告*/
    PENDING_NOTIFICATION(null, null, null, "待发通告",
            "pending Notification", PartnerProcessNodeNameConstants.PENDING_NOTIFICATION),

    /* 操作取消审核 */
    OPERATION_CANCEL_REVIEW(PARTNER_NETWORK_CHANGE,  new OfficeProdChiefHandlerImpl(), null,
            "操作取消审核", "operation cancel review", BATCH_CANCEL_OPERATION_REVIEW_PN_NODE),
    /**
     * 待操作执行
     */
    OPERATION_EXECUTION(null,null,null, "待操作执行",
            "proxy operation execution", PartnerProcessNodeNameConstants.OPERATION_EXECUTION),

    /**
     * 待反馈结果
     */
    RESULT_TOBE_BACK(null, null, null,
            "待反馈结果", "result to be back", PartnerProcessNodeNameConstants.RESULT_TOBE_BACK),

    /*已关闭*/
    CLOSE(null, null, null,
            "已关闭", "closed", CLOSED),
    ;

    /**
     * 流程类型
     */
    private final ApproveFlowTypeEnum flowType;
    /**
     * 个性化取审批人实现
     */
    private final AbstractRewardApproverHandler handler;

    /**
     * approvalType为null，需要从字段获取时候，对应字段名称
     */
    private final List<String> fieldList;

    /**
     * 中文名称
     */
    private final String zhCn;

    /**
     * 英文名称
     */
    private final String enUs;

    /**
     * 编码
     */
    private final String value;

    /** 审核节点对应的支持人员实体 */
    private Class<? extends BaseSubEntity> supportStaffEntity;

    PartnerApproveNodeEnum(ApproveFlowTypeEnum flowType,
                           AbstractRewardApproverHandler handler,
                           List<String> fieldList, String zhCn, String enUs, String value,
                           Class<? extends BaseSubEntity> supportStaffEntity) {
        this.flowType = flowType;
        this.handler = handler;
        this.fieldList = fieldList;
        this.zhCn = zhCn;
        this.enUs = enUs;
        this.value = value;
        this.supportStaffEntity = supportStaffEntity;
    }

    /**
     * 获取节点类型，防止默认valueOf方法抛出异常
     */
    public static PartnerApproveNodeEnum getApproveNodeEnum(String enumName) {
        for (PartnerApproveNodeEnum value : PartnerApproveNodeEnum.values()) {
            if (value.name().equals(enumName)) {
                return value;
            }
        }
        return null;
    }

    public static String getApproveNodeName(String enumName, String lang) {
        PartnerApproveNodeEnum approveNodeEnum = getApproveNodeEnum(enumName);
        if (null == approveNodeEnum) {
            return EMPTY_STRING;
        }
        return approveNodeEnum.getName(lang);
    }


    @Override
    public String getMsgKey() {
        return value;
    }

    public String getName(String lang) {

        return MsgUtils.getLangMessage(lang, getMsgKey());
    }

    /**
     * 合作方审批节点
     */
    public static List<String> approvingNode() {
        return Lists.newArrayList(
                PARTNER_REPRESENTATIVE_CHIEF_APPROVAL.name(),
                PARTNER_NET_OWNER_APPROVAL.name(),
                PARTNER_OFFICE_MANAGER_APPROVAL.name(),
                PARTNER_NET_DEPT_APPROVAL.name(),
                PARTNER_OFFICE_PD_APPROVAL.name(),
                PARTNER_OFFICE_PROD_CHIEF_APPROVAL.name(),
                PARTNER_OPERATION_PLAN_APPROVAL.name());
    }

    public static Map<String, String> getNodeNames(String language) {
        Map<String, String> nodeNames = new HashMap<>();
        for (PartnerApproveNodeEnum approveNode : PartnerApproveNodeEnum.values()) {
            nodeNames.put(
                    approveNode.name(),
                    LangUtils.get(language, approveNode.getZhCn(), approveNode.getEnUs()));
        }

        return nodeNames;
    }
}
