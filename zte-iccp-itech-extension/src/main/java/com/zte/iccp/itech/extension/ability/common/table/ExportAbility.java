package com.zte.iccp.itech.extension.ability.common.table;

import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.model.export.BaseSheet;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class ExportAbility {

    private Workbook workbook;

    private CellStyle contentStyle;

    private CellStyle headStyle;

    private ExportAbility(Workbook workbook) {
        this.workbook = workbook;
        this.contentStyle = createContentStyle();
        this.headStyle = createHeadStyle();
    }

    private CellStyle createContentStyle() {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }

    private CellStyle createHyperlinkStyle() {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.cloneStyleFrom(contentStyle);

        Font font = workbook.createFont();
        font.setColor(IndexedColors.BLUE.index);
        cellStyle.setFont(font);

        return cellStyle;
    }

    private CellStyle createHeadStyle() {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.cloneStyleFrom(contentStyle);

        Font font = workbook.createFont();
        font.setBold(true);
        cellStyle.setFont(font);

        return cellStyle;
    }

    private void setCellValue(Row row, int colIndex, String value) {
        Cell cell = row.createCell(colIndex);
        cell.setCellStyle(contentStyle);
        cell.setCellValue(value);
    }

    private void setHyperlink(Workbook workbook, Row row, int colIndex, String url) {
        CreationHelper createHelper = workbook.getCreationHelper();
        Hyperlink hyperlink = createHelper.createHyperlink(HyperlinkType.URL);
        hyperlink.setAddress(url);

        Cell cell = row.getCell(colIndex);
        cell.setHyperlink(hyperlink);
        cell.setCellStyle(createHyperlinkStyle());
    }

    private void autoSizeAllColumns(Sheet sheet) {
        for (int i = 0; i < sheet.getRow(0).getLastCellNum(); ++i) {
            sheet.autoSizeColumn(i, true);
        }
    }

    public static <T> void write(BaseSheet<T> sheetInfo, Workbook workbook) {
        ExportAbility ability = new ExportAbility(workbook);
        write(ability, sheetInfo);
    }

    private static <T> void write(ExportAbility ability, BaseSheet<T> sheetInfo) {
        // 第 1 行开始写入数据前，需要先写表头
        // 非第 1 行写入数据，直接在 Sheet 页后补充即可，无需再次写表头
        if (sheetInfo.getStartRows() == 0) {
            ability.writeHeader(sheetInfo);
        }
        ability.writeElements(sheetInfo);
    }


    private <T> void writeHeader(BaseSheet<T> baseListInfo) {
        Sheet sheet = workbook.createSheet(baseListInfo.getSheetName());
        Row row = sheet.createRow(0);
        int i = 0;

        for (String field : baseListInfo.getFields()) {
            Cell cell = row.createCell(i++);
            cell.setCellStyle(headStyle);
            cell.setCellValue(field);
        }
    }

    @SneakyThrows
    private <T> void writeElements(BaseSheet<T> sheetInfo) {
        Sheet sheet = workbook.getSheet(sheetInfo.getSheetName());

        for (int i = 0; i < sheetInfo.getElements().size(); ++i) {
            Row row = sheet.createRow(i + sheetInfo.getStartRows() + 1);
            T element = sheetInfo.getElements().get(i);
            Field[] elementFields = element.getClass().getDeclaredFields();

            for (int j = 0; j < elementFields.length; ++j) {
                Field field = elementFields[j];
                field.setAccessible(true);

                Object value = elementFields[j].get(element);
                String cellValue = Objects.isNull(value) ? "" : value.toString();
                setCellValue(row, j, cellValue);

                List<String> hyperlinks = sheetInfo.getHyperlinks().get(j);
                if (!CollectionUtils.isEmpty(hyperlinks)
                        && i < hyperlinks.size()
                        && StringUtils.hasText(hyperlinks.get(i))) {
                    setHyperlink(workbook, row, j, hyperlinks.get(i));
                }
            }
        }

        autoSizeAllColumns(sheet);
    }

    /**
     * 基础导出信息
     */
    public static <T> BaseSheet<T> basicSheetInfo(
            List<String> sheetTitles,
            String sheetName) {

        BaseSheet<T> sheetInfo = new BaseSheet<>();

        // 1.Sheet 页表头
        List<String> fields = sheetTitles.stream()
                .map(MsgUtils::getMessage)
                .collect(Collectors.toList());
        sheetInfo.setFields(fields);

        // 2.Sheet 页名称
        sheetInfo.setSheetName(MsgUtils.getMessage(sheetName));

        return sheetInfo;
    }
}
