package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 技术管理子任务流程实体
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/4
 */
@ApiModel("技术管理子任务流程实体")
@Setter
@Getter
@BaseEntity.Info("manage_sub_task_flow")
public class ManageSubTaskFlow extends BaseEntity {

    /**
     * 父任务id
     */
    @JsonProperty(value = "parent_task_id")
    private String parentTaskId;

    /**
     * 子任务id
     */
    @JsonProperty(value = "sub_task_id")
    private String subTaskId;

    /**
     * 状态
     */
    @JsonProperty(value = "status")
    private String status;

    /**
     * 子任务名称
     */
    @JsonProperty(value = "subtask_name")
    private String subtaskName;

    /**
     * 反馈类型
     */
    @JsonProperty(value = "feedback_type")
    private List<MultiLangText> feedbackType;

    /**
     * 进展描述
     */
    @JsonProperty(value = "progress_description")
    private String progressDescription;
}
