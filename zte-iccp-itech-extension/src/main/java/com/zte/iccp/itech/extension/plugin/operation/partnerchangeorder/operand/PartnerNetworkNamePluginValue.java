package com.zte.iccp.itech.extension.plugin.operation.partnerchangeorder.operand;

import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.plugin.form.partnerchangeorder.valuechange.PartnerValueChangeBaseFormPlugin;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import java.util.List;
import java.util.Map;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.COMPONENT_OPERATION_OBJECT_PRODUCT_MODEL_ID_CID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.IS_MAIN_PRODUCT;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.*;

/**
 * 分包商网络变更单【客户网络名称】字段值变更联动插件
 *
 * <AUTHOR> 10347404
 * @date 2024-06-12
 */
public class PartnerNetworkNamePluginValue implements PartnerValueChangeBaseFormPlugin {

    private static final String NIS_NETWORK_SERVICE_OBJECT_NETWORK_ID_PROPERTY_KEY = "NetworkId";


    @Override
    public void operate(PartnerValueChangedEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();

        ChangeData changeData = args.getChangeData();
        Integer rowIndex = changeData.getRowIndex();
        Object netWorkNameObj = changeData.getNewValue();
        // 获取【主产品】字段值
        String isMainProduct = EMPTY_STRING;
        List<TextValuePair> chooseComponentInfo = ComponentUtils.getChooseComponentInfo(dataModel, IS_MAIN_PRODUCT, rowIndex);
        if (!CollectionUtils.isEmpty(chooseComponentInfo)) {
            isMainProduct = chooseComponentInfo.get(0).getValue();
        }
        IEntryTableSupport operationObjectTable = (IEntryTableSupport) formView.getControl(COMPONENT_OPERATION_OBJECT_ORDER_CID);

        // 如果【客户网络名称】新值为空，则清空该行【网络责任人&组】，并设置【产品类型】为禁用
        if (ObjectUtils.isEmpty(netWorkNameObj)) {
            dataModel.setValue(OperationObjectFieldConsts.NET_RESPONSIBLE_PERSON_AND_TEAM, null, rowIndex);
            operationObjectTable.getTableState().setCellAttribute(
                    rowIndex, COMPONENT_OPERATION_OBJECT_PRODUCT_MODEL_ID_CID, PROPS_BEHAVIOR, DISABLED);
            // 如果【主产品】为Y，则同时清空主单据【客户网络标识】字段值
            if (CommonConstants.Y.equals(isMainProduct)) {
                dataModel.setValue(FIELD_NETWORK_NAME, Lists.newArrayList());
            }
            return;
        }

        // 如果【客户网络名称】新值不为为空，根据【主产品】的值更新主单据的网络名称字段，设置【产品类型】为可用
        Map<String, String> map = JsonUtils.parseObject(netWorkNameObj, Map.class);
        String networkId = map.get(NIS_NETWORK_SERVICE_OBJECT_NETWORK_ID_PROPERTY_KEY);
        if (CommonConstants.Y.equals(isMainProduct)) {
            dataModel.setValue(FIELD_NETWORK_NAME, networkId);
        }
        // 设置产品型号列为普通
        operationObjectTable.getTableState().setCellAttribute(
                rowIndex, COMPONENT_OPERATION_OBJECT_PRODUCT_MODEL_ID_CID, PROPS_BEHAVIOR, NORMAL);

    }
}
