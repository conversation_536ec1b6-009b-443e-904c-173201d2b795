package com.zte.iccp.itech.extension.domain.enums.changeorder;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/6/18 下午6:22
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ChangeOrderAttachmentEnum {

    /**
     * 是否紧急操作附件
     */
    EMERGENCY_OPERATION_ATTACH("emergency_operation_attach", "AttachmentField_3j6d4drk", null),
    /**
     * 封网、管控期操作附件
     */
    CLOSE_OR_CONTROL_OPERATION_ATTACH("close_or_control_operation_attach", "AttachmentField_tfpj4vq8", null),
    /**
     * license文件核对举证
     */
    LICENSE_FILE("license_file", "AttachmentField_injixg02", null),
    /**
     * 网元清单
     */
    NE_LIST_FILE("ne_list_file", "AttachmentField_r3ytalhz", null),

    /**
     * 有线产品操作检查单
     */
    WIRE_PRODUCT_CHECKLIST("wire_product_checklist", "AttachmentField_ao3o8co0", null),
    /**
     * 核心网产品外场质量保证规范动作子表单-检查附件
     */
    CHECK_FILE("standard_action_check_result_attachment", "standard_action_check_result_attachment", "standard_action_check"),

    ;

    //实体唯一标识
    private final String propertyKey;

    //布局唯一标识
    private final String cid;

    //子表单实体唯一标识
    private final String tablePropertyKey;
}
