package com.zte.iccp.itech.extension.openapi.scheduler;

import com.zte.iccp.itech.extension.ability.ApprovalTimeOverAbility;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;

/**
 * 技术审批超时通知
 * <AUTHOR>
 * @create 2024/10/15 下午1:41
 */
public class ApprovalCallOpenApi extends AbstractOpenApi {

    public void  approvalTimeOutInfrom(){
        ApprovalTimeOverAbility.approvalTimeOutInfrom();
    }
}
