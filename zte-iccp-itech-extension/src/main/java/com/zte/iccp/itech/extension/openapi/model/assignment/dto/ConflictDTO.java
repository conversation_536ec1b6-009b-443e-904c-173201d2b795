package com.zte.iccp.itech.extension.openapi.model.assignment.dto;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024/8/27 下午1:46
 */
@ApiModel("冲突")
@Getter
@Setter
public class ConflictDTO {

    @NotNull
    private String changeOrderId;

    @NotNull
    /* 冲突*/
    private Boolean timeConflict;
}
