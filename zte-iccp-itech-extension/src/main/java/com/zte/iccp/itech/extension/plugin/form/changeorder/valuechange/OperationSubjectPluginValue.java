package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.CCN_PROD_ID_PATH;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.Y;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;

/**
 * 触发时机：变更单中【产品分类】【代表处】【是否政企】【地市】【省/州】
 * 【客户标识】【客户名称】【国家/地区】【逻辑网元】【操作类型】字段的值变化时
 * 插件功能：
 * 国内运营商（是否政企=否）：代表处_地市>省_客户标识(后2个字）_产品小类_操作类型。【地市】字段有值时取【地市】字段，【地市】字段无值时取【省/州】字段
 * 国内政企（是否政企=是）：代表处_客户名称_产品小类_操作类型
 * 国际：国家/地区_客户标识_产品小类_操作类型
 * 核心网特殊规则：产品经营团队=算力及核心网 时，如果【逻辑网元】字段显示且不为空，则用【逻辑网元】替换上述【产品小类】
 **/
@AllArgsConstructor
public class OperationSubjectPluginValue implements ValueChangeBaseFormPlugin {

    private boolean isPlan;

    private static final String DEFAULT_ORG_NAME = "representative.office";
    private static final String DEFAULT_AREA_NAME = "city.to.province";
    private static final String DEFAULT_ACCN_TYPE_NAME = "customer.label";
    private static final String DEFAULT_CUSTOMER_NAME = "customer.name";
    private static final String DEFAULT_PRODUCT_NAME = "power.speci.model.export.prodSubCategory";
    private static final String DEFAULT_OPERATION_TYPE = "operation.type";
    private static final String DEFAULT_COUNTRY_NAME = "country.region";

    /**
     * loadDataEvent统一封装方法
     */
    @Override
    public void loadData(LoadDataEventArgs args) {
        IFormView formView = args.getFormView();
        PageStatusEnum pageStatusEnum = formView.getFormShowParameter().getPageStatus();
        IDataModel dataModel = formView.getDataModel();
        boolean editable = PageStatusEnum.NEW == pageStatusEnum || PageStatusEnum.EDIT == pageStatusEnum;
        if (!DataSourceEnum.IDOP.name().equals(dataModel.getValue(SOURCE)) || !editable) {
            return;
        }
        String langId = ContextHelper.getLangId();
        String result = calculateOperationSubjectPrefix(dataModel, langId, false);
        dataModel.setValue(CidConstants.FIELD_OPERATION_SUBJECT_PREFIX_CID, result);
    }

    /**
     * propertyChanged统一封装方法
     * @param args 默认入参，args
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/6/21 上午9:21
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        IDataModel dataModel = args.getModel();
        String langId = ContextHelper.getLangId();
        String result = calculateOperationSubjectPrefix(dataModel, langId, false);
        dataModel.setValue(CidConstants.FIELD_OPERATION_SUBJECT_PREFIX_CID, result);
    }

    /**
     * 按规则拼接操作主题前缀
     */
    public String calculateOperationSubjectPrefix(IDataModel dataModel, String langId, boolean isPartner) {
        // 获取操作主题计算相关的字段信息
        OperationSubjectParameters params = getOperationSubjectParameters(dataModel, langId, isPartner);

        // 核心网特殊规则
        if (params.getProductId() != null
                && params.getProductId().startsWith(ConfigHelper.get(CCN_PROD_ID_PATH))
                && !Strings.isEmpty(params.getLogicNe())) {
            params.setProductName(params.getLogicNe());
        }

        // 判断场景
        DeptTypeEnum deptType = ResponsibleUtils.getDeptType(params.getOrgId());
        boolean isGovFlag = Y.equals(params.getIsGov());
        // 默认展示国内运营商——代表处_地市>省_客户标识(后2位)_产品小类_操作类型
        String cityOrProvince = !Strings.isEmpty(params.getArea()) ? params.getArea() : params.getProvince();
        String customerIdSuffix = (!Strings.isEmpty(params.getAccnType()) && params.getAccnType().length() >= 2)
                ? params.getAccnType().substring(params.getAccnType().length() - 2)
                : params.getAccnType();
        String result = joinWithUnderscore(getDefaultIfEmpty(DEFAULT_ORG_NAME, params.getOrgName()),
                getDefaultIfEmpty(DEFAULT_AREA_NAME, cityOrProvince),
                getDefaultIfEmpty(DEFAULT_ACCN_TYPE_NAME, customerIdSuffix),
                getDefaultIfEmpty(DEFAULT_PRODUCT_NAME, params.getProductName()),
                getDefaultIfEmpty(DEFAULT_OPERATION_TYPE, params.getOperationType()));
        if (deptType == DeptTypeEnum.INNER && isGovFlag) {
            // 国内政企——代表处_客户名称_产品小类_操作类型
            result = joinWithUnderscore(getDefaultIfEmpty(DEFAULT_ORG_NAME, params.getOrgName()),
                    getDefaultIfEmpty(DEFAULT_CUSTOMER_NAME, params.getCustomerName()),
                    getDefaultIfEmpty(DEFAULT_PRODUCT_NAME, params.getProductName()),
                    getDefaultIfEmpty(DEFAULT_OPERATION_TYPE, params.getOperationType()));
        } else if (deptType == DeptTypeEnum.INTER) {
            // 国际——国家/地区_客户标识_产品小类_操作类型
            result = joinWithUnderscore(
                    getDefaultIfEmpty(DEFAULT_COUNTRY_NAME, params.getCountry()),
                    getDefaultIfEmpty(DEFAULT_ACCN_TYPE_NAME, customerIdSuffix),
                    getDefaultIfEmpty(DEFAULT_PRODUCT_NAME, params.getProductName()),
                    getDefaultIfEmpty(DEFAULT_OPERATION_TYPE, params.getOperationType()));
        }
        return result;
    }

    private OperationSubjectParameters getOperationSubjectParameters(IDataModel dataModel, String langId, boolean isPartner) {
        OperationSubjectParameters params = new OperationSubjectParameters();
        Object orgObj = dataModel.getValue(CidConstants.FIELD_ORGANIZATION_CID);
        Object productObj = dataModel.getValue(CidConstants.FIELD_PRODUCT_CID);
        Object isGovObj = dataModel.getValue(CidConstants.FIELD_IS_GOV_ENT_CID);
        Object operationTypeObj = dataModel.getValue(CidConstants.FIELD_OPERATION_TYPE_CID);
        Object cityObj;
        Object provinceObj;
        Object countryObj;
        Object customerIdObj;
        Object accnTypeObj;
        if (isPlan) {
            cityObj = getFirstObject(dataModel.getEntryColumnObject(FIELD_COMPONENT_PARTNER_OPERATION_OBJECT_CID, FIELD_AREA_CID));
            provinceObj = getFirstObject(dataModel.getEntryColumnObject(FIELD_COMPONENT_PARTNER_OPERATION_OBJECT_CID, FIELD_PROVINCE_CID));
            countryObj = getFirstObject(dataModel.getEntryColumnObject(FIELD_COMPONENT_PARTNER_OPERATION_OBJECT_CID, FIELD_COUNTRY_CID));
            customerIdObj = getFirstObject(dataModel.getEntryColumnObject(FIELD_COMPONENT_PARTNER_OPERATION_OBJECT_CID, FIELD_CUSTOMER_ID_CID));
            accnTypeObj = getFirstObject(dataModel.getEntryColumnObject(FIELD_COMPONENT_PARTNER_OPERATION_OBJECT_CID, ACCN_TYPE_CID));
        } else {
            cityObj = dataModel.getValue(CidConstants.FIELD_AREA_CID);
            provinceObj = dataModel.getValue(CidConstants.FIELD_PROVINCE_CID);
            countryObj = dataModel.getValue(CidConstants.FIELD_COUNTRY_CID);
            customerIdObj = dataModel.getValue(CidConstants.FIELD_CUSTOMER_ID_CID);
            accnTypeObj = dataModel.getValue(CidConstants.ACCN_TYPE_CID);
            if (!isPartner) {
                Object logicNeObj = dataModel.getValue(ChangeOrderFieldConsts.LOGICAL_NE);
                params.setLogicNe(getJsonTextByLangId(logicNeObj, langId));
            }
        }

        params.setOrgId(getJsonValue(orgObj));
        params.setOrgName(getProductOrOrganizationName(orgObj, langId, CommonConstants.INTEGER_ZERO));
        params.setProductId(getJsonValue(productObj));
        params.setProductName(getCityOrProvince(getJsonTextByLangId(productObj, langId)));
        params.setIsGov(getJsonValue(isGovObj));
        params.setOperationType(getJsonTextByLangId(operationTypeObj, langId));
        params.setArea(getCityOrProvince(getAreaName(cityObj, langId)));
        params.setProvince(getCityOrProvince(getAreaName(provinceObj, langId)));
        params.setCountry(getAreaName(countryObj, langId));
        params.setCustomerName(getCustomerName(customerIdObj));
        params.setAccnType(getStringValue(accnTypeObj));
        return params;
    }

    /** 工具方法：获取字符串 */
    private static String getStringValue(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof String && !EMPTY_STRING.equals(obj)) {
            return (String) obj;
        }
        return null;
    }

    private static String getCustomerName(Object customerIdObj) {
        if (!(customerIdObj instanceof DynamicDataEntity)) {
            return null;
        }
        return (String) ((DynamicDataEntity) customerIdObj).get("accountName");
    }

    /** 根据‘/’分隔符获取最后一个字段 */
    private static String getCityOrProvince(String area) {
        if (Strings.isBlank(area)) {
            return null;
        }
        String[] parts = area.split(CommonConstants.FORWARD_SLASH);
        return parts[parts.length - 1];
    }

    /** 工具方法：根据语言环境获取json对象的text值 */
    private static String getJsonTextByLangId(Object jsonObj, String langId) {
        String operationTypeName = null;

        if (ObjectUtils.isEmpty(jsonObj)) {
            return operationTypeName;
        }
        List<JSONObject> jsonObjects = JsonUtils.parseArray(JSON.toJSONString(jsonObj), JSONObject.class);
        if (CollectionUtils.isEmpty(jsonObjects)) {
            return operationTypeName;
        }
        operationTypeName = jsonObjects.get(CommonConstants.INTEGER_ZERO).getJSONObject(CommonConstants.TEXT)
                .getString(langId);
        return operationTypeName;
    }

    private static String getJsonValue(Object jsonObj) {
        if (ObjectUtils.isEmpty(jsonObj)) {
            return null;
        }
        List<JSONObject> jsonObjects = JsonUtils.parseArray(JSON.toJSONString(jsonObj), JSONObject.class);
        if (CollectionUtils.isEmpty(jsonObjects)) {
            return null;
        }
        return jsonObjects.get(CommonConstants.INTEGER_ZERO).get(CommonConstants.VALUE).toString();
    }

    private static String getAreaName(Object jsonObj, String langId) {
        if (ObjectUtils.isEmpty(jsonObj)) {
            return null;
        }
        List<JSONObject> jsonObjects = JsonUtils.parseArray(JSON.toJSONString(jsonObj), JSONObject.class);
        if (CollectionUtils.isEmpty(jsonObjects)) {
            return null;
        }
        return jsonObjects.get(CommonConstants.INTEGER_ZERO).get(langId).toString();
    }

    /** 下划线拼接 */
    private static String joinWithUnderscore(String... parts) {
        StringBuilder sb = new StringBuilder();
        for (String part : parts) {
            if (sb.length() > 0) {
                sb.append(CommonConstants.UNDER_SCORE);
            }
            sb.append(part);
        }
        sb.append(CommonConstants.UNDER_SCORE);
        return sb.toString();
    }

    private static String getProductOrOrganizationName(Object obj, String langId, Integer index) {
        String name = getJsonTextByLangId(obj, langId);
        if (Strings.isEmpty(name) || name.split(CommonConstants.FORWARD_SLASH).length <= index) {
            return null;
        }
        return name.split(CommonConstants.FORWARD_SLASH)[index];
    }

    private static Object getFirstObject(List<Object> objects) {
        if (CollectionUtils.isEmpty(objects)) {
            return null;
        }
        return objects.get(0);
    }

    /** 如果为空返回默认值，否则返回对应的值 */
    private static String getDefaultIfEmpty(String msgId, String value) {
        if (StringUtils.isEmpty(value)) {
            return MsgUtils.getMessage(msgId);
        }
        return value;
    }

    /** 操作主题计算所需的相关字段 */
    @NoArgsConstructor
    @Getter
    @Setter
    private static class OperationSubjectParameters {
        /** 代表处id */
        String orgId;
        /** 代表处 */
        String orgName;
        /** 产品id */
        String productId;
        /** 产品小类名称 */
        String productName;
        /** 是否政企 */
        String isGov;
        /** 操作类型 */
        String operationType;
        /** 地市 */
        String area;
        /** 省/州 */
        String province;
        /** 国家/地区 */
        String country;
        /** 客户名称 */
        String customerName;
        /** 客户标识 */
        String accnType;
        /** 逻辑网元 */
        String logicNe;
    }
}
