package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFactorConsts.*;

/**
 * 操作类型系数
 * <AUTHOR> 10335201
 * @date 2024-05-13 下午2:59
 **/
@Setter
@Getter
@BaseEntity.Info("operation_type_factor")
public class OperationTypeFactor extends BaseEntity {
    @JsonProperty(value = SCORE)
    private Double score;

    @JsonProperty(value = PRODUCT_LINE)
    private List<TextValuePair> productLine;

    @JsonProperty(value = PRODUCT_OPERATION_TEAM)
    private List<TextValuePair> productOperationTeam;

    @JsonProperty(value = PRODUCT_CATEGORY)
    private List<TextValuePair> productCategory;

    @JsonProperty(value = PRODUCT_SUBCATEGORY)
    private List<TextValuePair> productSubcategory;

    @JsonProperty(value = OPERATION_TYPE)
    private String operationType;
}
