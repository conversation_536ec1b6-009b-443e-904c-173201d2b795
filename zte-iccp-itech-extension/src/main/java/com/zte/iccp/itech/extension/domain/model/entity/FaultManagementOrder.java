package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.FaultManageOrderFieldConsts.*;


@ApiModel("故障管理单")
@Setter
@Getter
@BaseEntity.Info("fault_management_task")
public class FaultManagementOrder extends BaseEntity {

    @ApiModelProperty("任务单号")
    @JsonProperty(value = TASK_CODE)
    private String taskCode;

    @ApiModelProperty("任务主题(Call Log 主题)")
    @JsonProperty(value = TASK_SUBJECT)
    private String taskSubject;

    @ApiModelProperty("WarRoom Id")
    @JsonProperty(value = WAR_ROOM_ID)
    private String warRoomId;

    @ApiModelProperty("客户Id")
    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;

    @ApiModelProperty("办事处 / 代表处")
    @JsonProperty(value = ORGANIZATION)
    private List<TextValuePair> organization;

    @ApiModelProperty("PDM产品Id")
    @JsonProperty(value = PDM_PRODUCT_ID)
    private String pdmProductId;

    @ApiModelProperty("产品经营团队Id")
    @JsonProperty(value = PRODUCT_TEAM)
    private List<TextValuePair> productTeam;

    @ApiModelProperty("产品线Id")
    @JsonProperty(value = PRODUCT_LINE)
    private List<TextValuePair> productLine;

    @ApiModelProperty("区域Id")
    @JsonProperty(value = AREA_CODE)
    private String areaCode;

    @ApiModelProperty("CSC 单据状态")
    @JsonProperty(value = CSC_TASK_STATUS)
    private List<TextValuePair> cscTaskStatus;

    @ApiModelProperty("故障复盘 - 是否需要故障复盘")
    @JsonProperty(value = FAULT_REVIEW)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum faultReview;

    @ApiModelProperty("故障复盘 - 不进行故障复盘原因")
    @JsonProperty(value = FAULT_REVIEW_REASON)
    private String faultReviewReason;

    @ApiModelProperty("故障复盘 - 故障复盘报告")
    @JsonProperty(value = REVIEW_REPORT)
    private Object reviewReport;

    @ApiModelProperty("故障复盘 - 是否故障整改")
    @JsonProperty(value = FAULT_RECTIFICATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum faultRectification;

    @ApiModelProperty("故障复盘 - 不进行故障整改原因")
    @JsonProperty(value = FAULT_RECTIFICATION_REASON)
    private String faultRectificationReason;

    @ApiModelProperty("故障复盘 - 节点提交时间")
    @JsonProperty(value = REVIEW_SUBMIT_TIME)
    private String reviewSubmitTime;

    @ApiModelProperty("故障整改 - 节点提交时间")
    @JsonProperty(value = RECTIFICATION_SUBMIT_TIME)
    private String rectificationSubmitTime;

    @ApiModelProperty("客户满意度 - 反馈方式")
    @JsonProperty(value = SATISFACTION_CONTENT_TYPE)
    private List<TextValuePair> satisfactionContentType;

    @ApiModelProperty("客户满意度 - 满意度")
    @JsonProperty(value = SATISFACTION)
    private List<TextValuePair> satisfaction;

    @ApiModelProperty("客户满意度 - 客户支持经理")
    @JsonProperty(value = CUSTOMER_SUPPORT_MANAGER)
    private List<Employee> customerSupportManager;

    @ApiModelProperty("客户满意度 - 在线反馈内容")
    @JsonProperty(value = SATISFACTION_CONTENT)
    private String satisfactionContent;

    @ApiModelProperty("客户满意度 - 知会人")
    @JsonProperty(value = INFORMED_PERSON)
    private List<Employee> informedPerson;

    @ApiModelProperty("客户满意度 - 给客户发的邮件截图")
    @JsonProperty(value = CUSTOMER_MAIL_TO)
    private AttachmentFile customerMailTo;

    @ApiModelProperty("客户满意度 - 客户反馈的邮件")
    @JsonProperty(value = CUSTOMER_MAIL_RECEIVE)
    private AttachmentFile customerMailReceive;

    @ApiModelProperty("客户满意度 - 节点提交时间")
    @JsonProperty(value = SATISFACTION_SUBMIT_TIME)
    private String satisfactionSubmitTime;

    @ApiModelProperty("故障复盘 - 提交人")
    @JsonProperty(value = REVIEW_SUBMITTER)
    private List<Employee> reviewSubmitter;

    @ApiModelProperty("故障整改 - 提交人")
    @JsonProperty(value = RECTIFICATION_SUBMITTER)
    private List<Employee> rectificationSubmitter;

    @ApiModelProperty("客户满意度 - 提交人")
    @JsonProperty(value = SATISFACTION_SUBMITTER)
    private List<Employee> satisfactionSubmitter;

    @ApiModelProperty("客户满意度责任人")
    @JsonProperty(value = SATISFACTION_RESPONSIBLE)
    private List<Employee> satisfactionResponsible;

    @ApiModelProperty("客户满意度责任人")
    @JsonProperty(value = SATISFACTION_RESPONSIBLE_FR)
    private List<Employee> satisfactionResponsibleFr;
}
