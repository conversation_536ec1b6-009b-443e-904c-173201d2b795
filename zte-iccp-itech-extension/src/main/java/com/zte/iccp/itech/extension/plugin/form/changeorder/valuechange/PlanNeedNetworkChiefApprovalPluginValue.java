package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.BatchHiddenUtils;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.BuilderViewEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.TimeConflictEnum;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.IS_UPGRADE_TECHNOLOGY;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.OPERATION_LEVEL;

/**
 * 需要网络处总工审核/ 是否需要技术交付/网络处审核
 *
 * <AUTHOR>
 * @date 2025-07-17 上午10:52
 **/
public class PlanNeedNetworkChiefApprovalPluginValue implements ValueChangeBaseFormPlugin {

    @Override
    public void operate(ValueChangedEventArgs args) {
        checkOperatorAndRole(args.getModel(), args.getFormView());
    }

    @Override
    public void loadData(LoadDataEventArgs args) {
        checkOperatorAndRole(args.getModel(), args.getFormView());
    }

    public void checkOperatorAndRole(IDataModel dataModel, IFormView formView) {
        String conflict = LookupValueHelper.getModelLookupCode(dataModel.getValue(TIME_CONFLICT));
        String operationLevel = TextValuePairHelper.getValue(dataModel.getValue(OPERATION_LEVEL));
        PageStatusEnum pageStatus = formView.getFormShowParameter().getPageStatus();
        BuilderViewEnum status = BuilderViewEnum.READONLY;
        if ((PageStatusEnum.EDIT == pageStatus || PageStatusEnum.NEW == pageStatus)) {
            status = BuilderViewEnum.NORMAL;
        }
        // 有冲突
        if (TimeConflictEnum.Y.name().equals(conflict)) {
            //  是否是否需要技术交付/网络处审核= Y
            BatchHiddenUtils.hiddenTool(formView, BuilderViewEnum.READONLY, Lists.newArrayList(IS_UPGRADE_TECHNOLOGY));
            BatchHiddenUtils.hiddenTool(formView, status,
                    Lists.newArrayList(COMPONENT_TECHNOLOGY_REVIEW_CID, COMPONENT_TECHNOLOGY_REVIEW_TEAM_CID));
            dataModel.setValue(IS_UPGRADE_TECHNOLOGY, BoolEnum.Y.getValue());

            BatchHiddenUtils.hiddenTool(formView, BuilderViewEnum.HIDDEN,
                    Lists.newArrayList(FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID, NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM));
            dataModel.setValue(FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID, BoolEnum.N.getValue());
        } else if (OperationLevelEnum.IMPORTANCE.getValue().equals(operationLevel) || OperationLevelEnum.CRITICAL.getValue().equals(operationLevel)) {
            BatchHiddenUtils.hiddenTool(formView, BuilderViewEnum.READONLY, Lists.newArrayList(FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID));
            BatchHiddenUtils.hiddenTool(formView, status, Lists.newArrayList(NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM));
            dataModel.setValue(FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID, BoolEnum.Y.getValue());

            BatchHiddenUtils.hiddenTool(formView, BuilderViewEnum.HIDDEN, Lists.newArrayList(IS_UPGRADE_TECHNOLOGY,
                    COMPONENT_TECHNOLOGY_REVIEW_CID, COMPONENT_TECHNOLOGY_REVIEW_TEAM_CID));
            dataModel.setValue(IS_UPGRADE_TECHNOLOGY, BoolEnum.N.getValue());
        } else {
            dataModel.setValue(FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID, BoolEnum.N.getValue());
            dataModel.setValue(IS_UPGRADE_TECHNOLOGY, BoolEnum.N.getValue());
            BatchHiddenUtils.hiddenTool(formView, BuilderViewEnum.HIDDEN, Lists.newArrayList(
                    FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID, NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM,
                    IS_UPGRADE_TECHNOLOGY, COMPONENT_TECHNOLOGY_REVIEW_CID, COMPONENT_TECHNOLOGY_REVIEW_TEAM_CID));
        }
    }
}
