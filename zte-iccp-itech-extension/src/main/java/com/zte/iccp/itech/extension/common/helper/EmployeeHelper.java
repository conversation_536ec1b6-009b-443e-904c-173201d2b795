package com.zte.iccp.itech.extension.common.helper;

import apijson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.UcsClient;
import com.zte.iccp.itech.extension.spi.model.ucs.vo.UcsUserInfo;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @create 2024/8/6 下午2:40
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EmployeeHelper {

    public static SingleEmployee getSingle(String empNo){
        if (!StringUtils.hasText(empNo)){
            return null;
        }

        List<UcsUserInfo> userInfos = UcsClient.getUserInfo(Lists.newArrayList(empNo));
        if (CollectionUtils.isEmpty(userInfos)) {
            return null;
        }

        UcsUserInfo userInfo = userInfos.get(0);
        SingleEmployee singleEmployee = new SingleEmployee();
        fillEmployee(singleEmployee, userInfo);
        return singleEmployee;
    }

    public static List<Employee> getEmployees(List<String> empNos){
        if (CollectionUtils.isEmpty(empNos)){
            return null;
        }

        List<UcsUserInfo> userInfos = UcsClient.getUserInfo(empNos);
        if (CollectionUtils.isEmpty(userInfos)) {
            return null;
        }

        List<Employee> employees = new ArrayList<>();
        for (UcsUserInfo userInfo : userInfos) {
            Employee employee = new Employee();
            fillEmployee(employee, userInfo);
            employees.add(employee);
        }
        return employees;
    }

    private static void fillEmployee(Employee employee, UcsUserInfo userInfo) {
        employee.setId(userInfo.getAccountId());
        employee.setEmpUIID(userInfo.getAccountId());
        employee.setUserName(userInfo.getAccountId());
        employee.setEmpName(userInfo.getPersonName() + userInfo.getAccountId());
        employee.setEmpNameCn(userInfo.getPersonName());
        employee.setEmpNameEn(userInfo.getPersonNameEn());
        employee.setOrgID(userInfo.getOrgId());
        employee.setOrgNamePath(userInfo.getOrgNamePath());
        employee.setOrgNamePathEn(userInfo.getOrgNamePathEn());
    }

    /*
    * 给页面审核人员字段塞值格式
    * */
    public static JSONArray getEmployeeJSONArray(String epmNo){
        if (!StringUtils.hasText(epmNo)){
            return null;
        }
        List<Employee> employees = HrClient.queryEmployeeInfo(Arrays.asList(epmNo));
        if(CollectionUtils.isEmpty(employees)){
            return null;
        }

        return JSON.parseArray(employees);
    }


    /**
     * 获取用户工号
     *
     * @param employeeInfo employeeInfo
     * @return List<String>
     */
    public static List<String> getEpmUIID(Object employeeInfo) {
        if (employeeInfo == null) {
            return new ArrayList<>();
        }
        List<Employee> employeeInfoList = JsonUtils.parseArray(employeeInfo, Employee.class);

        if (CollectionUtils.isEmpty(employeeInfoList)) {
            return new ArrayList<>();
        }

        return employeeInfoList.stream()
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取用户工号
     */
    public static String getFirstEmpUIID(Object employeeInfo) {
        if (employeeInfo == null) {
            return EMPTY_STRING;
        }

        List<Employee> employees = JsonUtils.parseArray(employeeInfo, Employee.class);
        List<String> employeeIds = employees.stream()
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(employees) ? EMPTY_STRING : employeeIds.get(0);
    }

    /**
     * 解析Employee对象，拼接name+工号
     *
     * @param employeeList employeeList
     * @return xxxx工号1,xxxx工号2
     */
    public static String getEpmNameUIID(List<Employee> employeeList) {
        MultiLangText formatEmployeeNames = getEmployeeFormatNames(employeeList);
        if (formatEmployeeNames == null) {
            return EMPTY_STRING;
        }

        return ZH_CN.equals(ContextHelper.getLangId()) ? formatEmployeeNames.getZhCN() : formatEmployeeNames.getEnUS();
    }


    /**
     * 解析Employee对象，拼接name+工号
     *
     * @param employeeList employeeList
     * @return xxxx工号1,xxxx工号2  左边为中文，右边为英文
     */
    public static MultiLangText getEmployeeFormatNames(List<Employee> employeeList) {
        if (CollectionUtils.isEmpty(employeeList)) {
            return null;
        }

        // 使用 toMap 进行去重，key 为 empUIID，value 为中文+英文的数组
        Map<String, String[]> processedEmployees = employeeList.stream()
                .collect(Collectors.toMap(
                        Employee::getEmpUIID,
                        employee -> {
                            String zhName = StringUtils.hasText(employee.getEmpNameCn())
                                    ? employee.getEmpNameCn()
                                    : employee.getEmpName();
                            String enName = employee.getEmpNameEn();
                            return new String[]{zhName + employee.getEmpUIID(), enName + employee.getEmpUIID()};
                        },
                        (existing, replacement) -> existing, LinkedHashMap::new));

        // 中文
        String zhResult = processedEmployees.values().stream()
                .map(arr -> arr[0])
                .collect(Collectors.joining(COMMA));

        // 英文
        String enResult = processedEmployees.values().stream()
                .map(arr -> arr[1])
                .collect(Collectors.joining(COMMA));

        return new MultiLangText(){{
            setZhCN(zhResult);
            setEnUS(enResult);
        }};
    }

    public static Map<String, Employee> getEmployeeInfos(List<String> empNos) {
        if (CollectionUtils.isEmpty(empNos)){
            return Maps.newHashMap();
        }

        List<UcsUserInfo> userInfos = UcsClient.getUserInfo(empNos);
        if (CollectionUtils.isEmpty(userInfos)) {
            return Maps.newHashMap();
        }

        Map<String, Employee> employeeInfos = Maps.newHashMap();

        for (UcsUserInfo userInfo : userInfos) {
            Employee employee = new Employee();
            fillEmployee(employee, userInfo);
            employeeInfos.put(userInfo.getAccountId(), employee);
        }
        return employeeInfos;
    }

    public static com.alibaba.fastjson2.JSONArray getEmployeeJSONArray(List<SingleEmployee> employees) {
       com.alibaba.fastjson2.JSONArray array = new com.alibaba.fastjson2.JSONArray();
        Set<String> userId = new HashSet<>();
        for (Employee employee : employees) {
            if (!userId.add(employee.getEmpUIID())) {
                continue;
            }
            JSONObject emp = new JSONObject();
            emp.put(ID_KEY, employee.getId());
            emp.put(ORG_ID_KEY, employee.getOrgID());
            emp.put(USER_NAME, employee.getUserName());
            emp.put(EMP_NAME_KEY, employee.getEmpName());
            emp.put(EMP_NAME_CN_KEY, employee.getEmpNameCn());
            emp.put(EMP_NAME_EN_KEY, employee.getEmpNameEn());
            emp.put(EMP_UIID_KEY, employee.getEmpUIID());
            emp.put(ORG_NAME_PATH_KEY, employee.getOrgNamePath());
            emp.put(ORG_NAME_PATH_EN_KEY, employee.getOrgNamePathEn());
            array.add(emp);
        }
        return array;
    }

    /**
     * 转换 Employee List
     * 由于 SingleEmployee 解析时会自动转换为 List，赋值 List<SingleEmployee> 会导致后台数据库数据外层多套一个 List
     */
    public static List<Employee> singletonEmployee2Employee(SingleEmployee singleEmployee) {
        Employee employee = new Employee();

        employee.setId(singleEmployee.getId());
        employee.setOrgID(singleEmployee.getOrgID());
        employee.setEmpName(singleEmployee.getEmpName());
        employee.setEmpUIID(singleEmployee.getEmpUIID());
        employee.setEmpNameCn(singleEmployee.getEmpNameCn());
        employee.setEmpNameEn(singleEmployee.getEmpNameEn());
        employee.setOrgNamePath(singleEmployee.getOrgNamePath());
        employee.setOrgNamePathEn(singleEmployee.getOrgNamePathEn());
        employee.setUserName(singleEmployee.getUserName());

        return Lists.newArrayList(employee);
    }


    /**
     * Employee去重
     *
     * @param employees employees
     * @return List<Employee>
     */
    public static List<Employee> uniqueEmployees(List<Employee> employees) {
        if (CollectionUtils.isEmpty(employees)) {
            return Collections.emptyList();
        }
        return new ArrayList<>(employees.stream()
                .collect(Collectors.toMap(
                        Employee::getEmpUIID,
                        item -> item,
                        (o1, o2) -> o1,
                        LinkedHashMap::new
                ))
                .values());
    }

    public static String getDisplayName(SingleEmployee singleEmployee) {
        if (singleEmployee == null) {
            return null;
        }
        String langId = ContextHelper.getLangId();
        boolean isChinese = ZH_CN.equals(langId);

        String name = isChinese ? firstNonEmpty(singleEmployee.getEmpNameCn(), singleEmployee.getEmpNameEn())
                : firstNonEmpty(singleEmployee.getEmpNameEn(), singleEmployee.getEmpNameCn());

        String userName = singleEmployee.getUserName();
        return name != null ? name + userName : userName;
    }

    private static String firstNonEmpty(String... values) {
        return Stream.of(values)
                .filter(StringUtils::hasText)
                .findFirst()
                .orElse(null);
    }

    public static List<Employee> getModelEmployees(Object object) {
        if (object == null) {
            return null;
        }

        return JsonUtils.parseArray(object, Employee.class);
    }
}