package com.zte.iccp.itech.extension.ability.orderresult;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.OperationSchemeAbility;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationSchemeStatusEnum;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/04
 */
@Slf4j
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class BaseUpdateStatusHandler extends BaseResultHandler {

    private final AssignmentStatusEnum assignmentStatus;

    private final OperationSchemeStatusEnum operationSchemeStatus;

    @Override
    protected JSONObject doHandle(FlowClient body) {
        if (assignmentStatus != null) {
            AssignmentAbility.updateStatusByBillId(body.getBusinessId(), assignmentStatus);
        }

        if (operationSchemeStatus != null) {
            OperationSchemeAbility.update(body.getBusinessId(), operationSchemeStatus);
        }
        log.error("BaseUpdateStatusHandler doHandle id:{},assignmentStatus:{},operationSchemeStatus:{}",
                body.getBusinessId(), assignmentStatus, operationSchemeStatus);
        return null;
    }
}
