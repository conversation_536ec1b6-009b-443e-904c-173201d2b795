package com.zte.iccp.itech.extension.common.helper;

import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.spi.client.HolClient;
import com.zte.iccp.itech.extension.spi.model.hol.CompanyUserResp;
import com.zte.iccp.itech.extension.spi.model.hol.GetOutSourcingReq;
import com.zte.iccp.itech.extension.spi.model.hol.HolMobile;
import com.zte.itp.msa.core.model.PageRows;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.STR_ONE;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class HrHelper {
    public static String queryPhoneNum(String userId) {
        HolMobile holMobile = queryMobile(userId);
        return holMobile == null ? null : holMobile.getNumber();
    }
    public static String queryCountryCodePhoneNum(String userId) {
        HolMobile holMobile = queryMobile(userId);
        return holMobile == null
                ? null
                : String.format("%s %s", holMobile.getCountryCode(), holMobile.getNumber());
    }

    private static HolMobile queryMobile(String userId) {
        PageRows<CompanyUserResp> users = HolClient.queryPersonOutsourcingInfo(
                new GetOutSourcingReq() {{
                    setQueryKey(userId);
                    setQueryVer("v1");
                    setPage(1);
                    setPageSize(1);
                }});
        if (CollectionUtils.isEmpty(users.getRows())) {
            return null;
        }

        List<HolMobile> mobiles = users.getRows().get(0).getMobile();
        if (CollectionUtils.isEmpty(mobiles)) {
            return null;
        }
        // 返回排序为1的电话，若不存在，则直接返回第一个电话；
        return mobiles.stream()
                .filter(v -> STR_ONE.equals(v.getNumberOrder()))
                .findFirst()
                .orElse(mobiles.get(0));
    }

    public static String queryResourceId(String userId) {
        PageRows<CompanyUserResp> users = HolClient.queryPersonOutsourcingInfo(
                new GetOutSourcingReq() {{
                    setQueryKey(userId);
                    setQueryVer("v1");
                    setPage(1);
                    setPageSize(1);
                }});

        return CollectionUtils.isEmpty(users.getRows())
                ? CommonConstants.EMPTY_STRING
                : users.getRows().get(0).getResourceId();
    }
}
