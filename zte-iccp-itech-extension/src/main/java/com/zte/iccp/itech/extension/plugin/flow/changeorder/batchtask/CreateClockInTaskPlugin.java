package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.clockin.ClockInTaskCreateAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.AlarmUtils;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.RetryUtils;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/09/05
 */
@Slf4j
public class CreateClockInTaskPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        if (!Boolean.parseBoolean(ConfigHelper.get("clockInTask.enabled", "false"))) {
            return false;
        }

        AsyncExecuteUtils.execute(() -> {
            try {
                RetryUtils.run(() ->
                        new ClockInTaskCreateAbility(getEntityClass(body), body.getBusinessId()).create());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                AlarmUtils.major("Create Clock-in Task Error", e);
            }
        });
        return false;
    }
}
