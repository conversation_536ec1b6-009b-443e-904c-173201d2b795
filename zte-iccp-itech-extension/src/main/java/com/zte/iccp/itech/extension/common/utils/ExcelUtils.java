package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.InputStream;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/20
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExcelUtils {

    @SneakyThrows
    public static Workbook getWorkbook(InputStream excelStream) {
        FileMagic fileMagic = FileMagic.valueOf(excelStream);
        switch (fileMagic) {
            case OLE2:
                return new HSSFWorkbook(excelStream);
            case OOXML:
                return new XSSFWorkbook(excelStream);
            default:
                return null;
        }
    }
}