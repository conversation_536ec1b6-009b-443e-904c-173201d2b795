package com.zte.iccp.itech.extension.domain.model.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10309921
 * @since 2025/04/09
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MultiLangTextType {

    @JsonProperty(value = "zh_CN")
    private String zhCN;

    @JsonProperty(value = "en_US")
    private String enUS;

    private String type;

    public String getTextByLanguage(String language) {
        return ZH_CN.equals(language) ? zhCN : enUS;
    }
}
