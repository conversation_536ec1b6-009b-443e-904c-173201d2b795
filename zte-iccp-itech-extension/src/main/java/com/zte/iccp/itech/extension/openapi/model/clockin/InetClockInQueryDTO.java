package com.zte.iccp.itech.extension.openapi.model.clockin;

import com.zte.iccp.itech.extension.openapi.model.networksecurity.dto.BaseQueryDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * iNet打卡信息查询入参
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/11/28
 */
@Getter
@Setter
public class InetClockInQueryDTO extends BaseQueryDTO {

    /** 打卡任务ids */
    private List<String> clockInIds;

    /** 变更单编码 */
    private String coNo;

    /**
     * 参数校验&设置日期条件
     */
    public void validateAndSetDateCondition() {
        if ((this.clockInIds == null || clockInIds.isEmpty())
                && coNo == null
                && super.isEmptyObject()) {
            super.setDefaultValueIfEmpty();

        }else {
            validateInput();
        }
    }

    /**
     * 参数合法性校验
     */
    public void validateInput() {
        super.validateInput();
    }
}
