package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.model.ProductTypeDataKey;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.paas.lcap.core.event.SearchEvent;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * 将树状产品类型拆成五个字段
 * @author: 李江斌 10318434
 * @date: 2024/9/13
 */
public class ProductTypeTreeSplitAbility {

    /**
     * 五个 datakey 或者 第一个和最后一个 datakey 需必填
     *
     * @param searchEvent     列表查询入参
     * @param dataKey         五层datakey
     */
    public static void splitProd(SearchEvent searchEvent, ProductTypeDataKey dataKey) {
        // 1.获取列表数据
        JSONObject output = searchEvent.getOutput();
        JSONArray recordArray = output.getJSONArray(RECORDS);
        List<JSONObject> tables = JsonUtils.parseArray(recordArray, JSONObject.class);

        for (JSONObject tableRow : tables) {
            // 获取产品分类层级名称
            String productTypeDataKey = dataKey.getProductTypeDataKey();
            JSONArray temp = tableRow.getJSONArray(productTypeDataKey);
            List<TextValuePair> prodTypes = JsonUtils.parseArray(temp, TextValuePair.class);
            if (prodTypes == null) {
                continue;
            }
            handleTableRow(tableRow, dataKey, prodTypes);
        }
        // 3.数据展示
        output.put(RECORDS, tables);
    }


    public static void handleTableRow(JSONObject tableRow, ProductTypeDataKey dataKey, List<TextValuePair> prodTypes) {
        // 产品经营团队（第一层）
        Set<String> prodTypeFirstNameSet = new HashSet<>();
        // 最后一层（五层的话就是规格型号）
        Set<String> prodTypeEndNameSet = new HashSet<>();
        // 产品线（第二层）
        Set<String> productLineSet = new HashSet<>();
        // 产品大类（第三层）
        Set<String> prodMainCategorySet = new HashSet<>();
        // 产品小类（第四层）
        Set<String> prodSubCategorySet = new HashSet<>();
        // 规格型号（第五层）
        Set<String> productModelSet = new HashSet<>();

        String language = ContextHelper.getLangId();
        for (TextValuePair prodType : prodTypes) {
            String productName = prodType.getTextByLanguage(language);
            if (StringUtils.isNotEmpty(productName)) {
                String[] partProductNames = productName.split(FORWARD_SLASH);
                prodTypeFirstNameSet.add(partProductNames[INTEGER_ZERO]);
                prodTypeEndNameSet.add(partProductNames[partProductNames.length - INTEGER_ONE]);
                addMiddleProdType(partProductNames, productLineSet, prodMainCategorySet, prodSubCategorySet, productModelSet);
            }
        }
        // 产品经营团队（自定义）
        tableRow.put(dataKey.getProductTeamDataKey(), String.join(COMMA, prodTypeFirstNameSet));
        if (dataKey.getLastLevelDataKey() == null) {
            // 产品线（自定义）
            tableRow.put(dataKey.getProductLineDataKey(), String.join(COMMA, productLineSet));
            // 产品大类（自定义）
            tableRow.put(dataKey.getProdMainCategoryDataKey(), String.join(COMMA, prodMainCategorySet));
            // 产品小类（自定义）
            tableRow.put(dataKey.getProdSubCategoryDataKey(), String.join(COMMA, prodSubCategorySet));
            // 规格型号（自定义）
            tableRow.put(dataKey.getProductModelDataKey(), String.join(COMMA, productModelSet));
        } else {
            // 产品类型（最后一层）（自定义）
            tableRow.put(dataKey.getLastLevelDataKey(),String.join(COMMA, prodTypeEndNameSet));
        }
    }

    private static void addMiddleProdType(String[] prodTypeZhNameList, Set<String> productLineSet, Set<String> prodMainCategorySet, Set<String> prodSubCategorySet, Set<String> productModelSet) {
        for (int i = 0; i < prodTypeZhNameList.length; i++) {
            switch (i) {
                case 1:
                    productLineSet.add(prodTypeZhNameList[i]);
                    break;
                case 2:
                    prodMainCategorySet.add(prodTypeZhNameList[i]);
                    break;
                case 3:
                    prodSubCategorySet.add(prodTypeZhNameList[i]);
                    break;
                case 4:
                    productModelSet.add(prodTypeZhNameList[i]);
                    break;
                default:
                    break;
            }
        }
    }
}
