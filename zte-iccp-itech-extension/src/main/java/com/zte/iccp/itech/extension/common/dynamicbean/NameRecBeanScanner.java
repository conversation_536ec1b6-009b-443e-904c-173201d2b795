package com.zte.iccp.itech.extension.common.dynamicbean;

import com.google.common.collect.Sets;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/04/24
 */
@Getter
public class NameRecBeanScanner extends ClassPathBeanDefinitionScanner {

    private final Set<String> scannedClassNames = Sets.newHashSet();

    public NameRecBeanScanner(BeanDefinitionRegistry registry) {
        super(registry);
    }

    @Override
    protected boolean isCandidateComponent(@NotNull AnnotatedBeanDefinition beanDefinition) {
        // First call the super method
        boolean isCandidate = super.isCandidateComponent(beanDefinition);

        if (isCandidate) {
            // Collect the scanned class names
            scannedClassNames.add(beanDefinition.getBeanClassName());
        }

        return isCandidate;
    }

}