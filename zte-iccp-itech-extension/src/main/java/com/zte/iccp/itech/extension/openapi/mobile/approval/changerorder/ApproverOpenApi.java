package com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder;

import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.ability.PartnerChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.changeorder.InterAdminApproverAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.ClientConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.IntlAdminApprovalFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.FlowVariantEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.IntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.PersonRelevance;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.vo.SubIntlAdminApproval;
import com.zte.iccp.itech.extension.handler.approver.partner.OfficeManagerHandlerImpl;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.dto.ApprovalSubmitDto;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.dto.ApproverByDto;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo.ApprovedByConfigVO;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.partnerchangeorder.helper.PartnerNetDeptApprovalHelper;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.ResetParameter;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import lombok.NonNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApproveResultConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * 移动端审批人openApi，包括内部网络变更单和合作商网络变更单
 *
 * <AUTHOR> 10347404
 * @date 2024-09-26
 */
public class ApproverOpenApi extends AbstractOpenApi {

    // 合作商网络变更单网络责任人审批中，办事处产品经理审核人/审核组openapi
    public ServiceData<ApprovedByConfigVO> partnerOfficeProdManager(@RequestBody ApproverByDto approverByDto)  {
        // 获取单据编号
        String coNo = approverByDto.getCoNo();
        if (!coNo.startsWith(PARTNER_CHANGE_ORDER_PREFIX_HZF)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }

        // 查询合作商变更单单据
        SubcontractorChangeOrder subcontractorChangeOrder = PartnerChangeOrderAbility.getByCoNo(coNo);
        if (null == subcontractorChangeOrder) {
            return new ServiceData<>();
        }

        OfficeManagerHandlerImpl officeManagerHandler = new OfficeManagerHandlerImpl();
        ApproverConfiguration approverConfig = officeManagerHandler.getApprover(subcontractorChangeOrder);
        if (null == approverConfig) {
            return new ServiceData<>();
        }

        return new ServiceData<ApprovedByConfigVO>() {{
            setBo(new ApprovedByConfigVO(approverConfig.getApproverPersons(), approverConfig.getApproverGroups()));
        }};
    }

    // 合作商网络变更单办事处经理审批中，网络处配置审核人/审核组openapi
    public ServiceData<ApprovedByConfigVO> partnerNetServiceReviewer(@RequestBody ApproverByDto approverByDto) {
        // 获取单据编号
        String coNo = approverByDto.getCoNo();
        if (!coNo.startsWith(PARTNER_CHANGE_ORDER_PREFIX_HZF)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }

        // 查询合作商变更单单据
        SubcontractorChangeOrder subcontractorChangeOrder = PartnerChangeOrderAbility.getByCoNo(coNo);
        if (null == subcontractorChangeOrder) {
            return new ServiceData<>();
        }

        List<ApproverConfiguration> approverConfigurations = PartnerNetDeptApprovalHelper.getNetworkDeptApprover(subcontractorChangeOrder);
        if (CollectionUtils.isEmpty(approverConfigurations)) {
            return new ServiceData<>();
        }
        ApproverConfiguration approverConfig = approverConfigurations.get(INTEGER_ZERO);
        if (null == approverConfig) {
            return new ServiceData<>();
        }

        return new ServiceData<ApprovedByConfigVO>() {{
            setBo(new ApprovedByConfigVO(approverConfig.getApproverPersons(), approverConfig.getApproverGroups()));
        }};
    }

    /**
     * OpenApi - 提交审批
     */
    public ServiceData<String> submitApprovalDetail(@RequestBody @NonNull ApprovalSubmitDto approvalSubmitDto) {
        ServiceData<String> serviceData = new ServiceData<>();

        // 1.检索任务信息
        NetworkChangeAssignment assignment =
                AssignmentAbility.querySpecificTypeAssignmentByCode(approvalSubmitDto.getOrderNo(), NetworkChangeAssignment.class);
        if (Objects.isNull(assignment)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ASSIGNMENT_NOT_EXISTS));
        }

        // 2.根据任务类型提交审批
        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        switch (assignmentType) {
            case NETWORK_CHANGE:
                submitNetworkChangeApproveDetail(assignment, approvalSubmitDto);
                break;

            case SUBCONTRACTOR_NETWORK_CHANGE:
                submitSubcontractNetworkChangeApproveDetail(assignment, approvalSubmitDto);
                break;

            case NETWORK_CHANGE_BATCH:
                submitBatchApproveDetail(assignment, approvalSubmitDto);
                break;

            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                submitSubcontractBatchApproveDetail(assignment, approvalSubmitDto);
                break;

            default:
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ERROR_ASSIGNMENT_TYPE));
        }

        // 3.更新我已处理结果
        String assignmentId = assignment.getId();
        String userId = ContextHelper.getEmpNo();
        PersonRelevance existPersonRelevance = AssignmentAbility.getPersonRelevance(assignmentId, userId);

        if (Objects.isNull(existPersonRelevance)) {
            PersonRelevance insertRelevance = new PersonRelevance();
            insertRelevance.setAssignmentId(assignmentId);
            insertRelevance.setRelevant(userId);
            insertRelevance.setApprovalTaskFlag(BoolEnum.Y.name());
            insertRelevance.setSelfHandlerFlag(BoolEnum.Y.name());

            AssignmentAbility.createAssignmentPersonRelevance(Lists.newArrayList(insertRelevance));

        } else {
            PersonRelevance updateRelevance = new PersonRelevance();
            updateRelevance.setId(existPersonRelevance.getId());
            updateRelevance.setApprovalTaskFlag(BoolEnum.Y.name());
            updateRelevance.setSelfHandlerFlag(BoolEnum.Y.name());

            AssignmentAbility.batchUpdateRelevance(Lists.newArrayList(updateRelevance));
        }

        serviceData.setBo(ClientConstants.SUCCESS);
        return serviceData;
    }

    /**
     * 提交审批结果 - 网络变更
     * @param assignment
     * @param approvalSubmitDto
     */
    private void submitNetworkChangeApproveDetail(
            NetworkChangeAssignment assignment,
            ApprovalSubmitDto approvalSubmitDto) {

        // 1.检索单据
        ChangeOrder changeOrder = ChangeOrderAbility.get(assignment.getEntityId(), Lists.newArrayList());
        if (Objects.isNull(changeOrder)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }

        // 2.校验审批结果
        ApproveNodeEnum approveNode
                = ApproveNodeEnum.getApproveNodeEnum(assignment.getCurrentProgress());
        checkNetworkChangeSubmitInfo(approveNode, approvalSubmitDto);

        // 3.提交审批
        submitApproveResult(assignment, changeOrder, approveNode, approvalSubmitDto);
    }

    /**
     * 提交审批结果 - 合作方网络变更
     */
    private void submitSubcontractNetworkChangeApproveDetail(NetworkChangeAssignment assignment, ApprovalSubmitDto approvalSubmitDto) {
        // 1.检索单据
        SubcontractorChangeOrder subcontractorChangeOrder = PartnerChangeOrderAbility.get(assignment.getBillId());
        if (Objects.isNull(subcontractorChangeOrder)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }
        String changeOrderId = subcontractorChangeOrder.getId();

        // 2.校验审批结果
        PartnerApproveNodeEnum approveNode = PartnerApproveNodeEnum.getApproveNodeEnum(assignment.getCurrentProgress());
        checkSubcontractorNetworkChangeSubmitInfo(approveNode, approvalSubmitDto);

        // 3.提交审批
        HashMap<String, ResetParameter> flowSubmitParameterMap = updateSubcontractorChangeOrderSubmitInfo(approveNode, changeOrderId, approvalSubmitDto);
        FlowHelper.submitSystemNode(changeOrderId, flowSubmitParameterMap);
    }

    /**
     * 提交审批结果 - 网络变更批次
     */
    private void submitBatchApproveDetail(
            NetworkChangeAssignment assignment,
            ApprovalSubmitDto approvalSubmitDto) {

        // 1.检索单据
        BatchTask batchTask = BatchTaskAbility.get(assignment.getEntityId(), Lists.newArrayList());
        if (Objects.isNull(batchTask)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }

        // 2.校验审批结果
        ApproveNodeEnum approveNode = ApproveNodeEnum.getApproveNodeEnum(assignment.getCurrentProgress());
        checkBatchSubmitInfo(approveNode, approvalSubmitDto);

        // 3.提交审批
        submitBatchApproveResult(assignment, batchTask, approveNode, approvalSubmitDto);
    }

    /**
     * 提交审批结果 - 合作方网络变更批次
     * @param assignment
     * @param approvalSubmitDto
     */
    private void submitSubcontractBatchApproveDetail(NetworkChangeAssignment assignment, ApprovalSubmitDto approvalSubmitDto) {
        // 1.检索单据
        SubcontractorBatchTask subcontractorBatchTask = BatchTaskAbility.getSub(assignment.getApproveBatchTaskId(), Lists.newArrayList());
        if (Objects.isNull(subcontractorBatchTask)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }
        String batchTaskId = subcontractorBatchTask.getId();

        // 2.校验审批结果
        PartnerApproveNodeEnum approveNode = PartnerApproveNodeEnum.getApproveNodeEnum(assignment.getCurrentProgress());
        checkSubcontractorBatchSubmitInfo(approveNode, approvalSubmitDto);

        // 3.提交审批
        HashMap<String, ResetParameter> flowSubmitParameterMap = updateSubcontractorBatchSubmitInfo(approveNode, batchTaskId, approvalSubmitDto);
        FlowHelper.submitSystemNode(batchTaskId, flowSubmitParameterMap);
    }

    /**
     * 校验审批结果 - 网络变更
     * @param currentNode
     * @param approvalSubmitDto
     */
    private void checkNetworkChangeSubmitInfo(ApproveNodeEnum currentNode, ApprovalSubmitDto approvalSubmitDto) {
        // 1.审批结果必填
        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        if (Objects.isNull(approveResult)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_NOT_EXISTS));
        }

        // 2.校验当前进展
        ApproveNodeEnum approveNode = ApproveNodeEnum.getApproveNodeEnum(approvalSubmitDto.getApprovalNode());
        if (Objects.isNull(approveNode) || (!approveNode.equals(currentNode))) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.NODE_APPROVED));
        }

        // 3.审批结果在对应范围内
        switch (approveNode) {
            // 审批结果类型一：审核通过 / 审核不通过，终止 (同实际页面展示内容有区别)
            // 涉及节点：代表处产品科长 / 网服务部三层 / 研发三层 / 工服三部部长 / 国际行政审核 / 研发领导审核 / 技术交付部网络处领导审核
            case ADMIN_REP_PROD_CHIEF:
            case ADMIN_NETSERVICE_LV3:
            case ADMIN_RD_DEPT_LV3:
            case ADMIN_ENG_SERVICE3:
            case INTL_ADMIN_APPROVAL:
            case RD_LEADER_APP:
            case TD_NET_DEPT_LEADER:
                if (!ApproveResultEnum.PASS.equals(approveResult) && !ApproveResultEnum.TERMINATE.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;

            // 审批结果类型二：审核通过，无需再审 / 审核通过 / 审核不通过，终止 (同实际页面展示内容有区别)
            // 涉及节点：办事处副经理 / 网络处主管经理 / 网服部四层 / 电信服务总监
            // Warning 网络服务处总监 / 网络服务处经理 原则上需要区分三营 / 政企校验
            case ADMIN_NET_PROD_DIR:
            case ADMIN_REP_DEPUTY_MNG:
            case ADMIN_NET_DEPT_MNG:
            case ADMIN_NETSERVICE_LV4:
            case ADMIN_DIR_TELE_SERVICE:
                if (!ApproveResultEnum.SKIP.equals(approveResult)
                        && !ApproveResultEnum.PASS.equals(approveResult)
                        && !ApproveResultEnum.TERMINATE.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;

            default:
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_NODE_ERROR));
        }
    }

    /**
     * 校验审批结果 - 批次任务
     * @param currentNode
     * @param approvalSubmitDto
     */
    private void checkBatchSubmitInfo(ApproveNodeEnum currentNode, ApprovalSubmitDto approvalSubmitDto) {
        // 1.审批结果必填
        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        if (Objects.isNull(approveResult)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_NOT_EXISTS));
        }

        // 2.校验当前进展
        ApproveNodeEnum approveNode = ApproveNodeEnum.getApproveNodeEnum(approvalSubmitDto.getApprovalNode());
        if (Objects.isNull(approveNode) || (!approveNode.equals(currentNode))) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.NODE_APPROVED));
        }

        // 3.审批结果在对应范围内
        switch (currentNode) {
            // 审批结果类型一：审核通过 / 审核驳回修改
            // 涉及节点：批次任务操作取消——代表处产品科长审核
            case OPERATION_CANCEL_REVIEW:
                if (!ApproveResultEnum.PASS.equals(approveResult) && !ApproveResultEnum.REJECT.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;
            // 审批结果类型一：审核通过 / 审核不通过，终止 (同实际页面展示内容有区别)
            // 涉及节点：代表处产品科长 / 网服务部三层 / 研发三层 / 工服三部部长 / 操作计划审核
            case ADMIN_REP_PROD_CHIEF:
            case ADMIN_NETSERVICE_LV3:
            case ADMIN_RD_DEPT_LV3:
            case ADMIN_ENG_SERVICE3:
            case CHANGED_BY_REP_PROD_CHIEF:
            case BATCH_INTL_ADMIN_APPROVAL:
                if (!ApproveResultEnum.PASS.equals(approveResult) && !ApproveResultEnum.TERMINATE.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;

            // 审批结果类型二：审核通过，无需再审 / 审核通过 / 审核不通过，终止 (同实际页面展示内容有区别)
            // 涉及节点：网络处产品总监 / 办事处副经理 / 网络处主管经理 / 网服部四层 / 电信服务总监
            // Warning 网络服务处总监 / 网络服务处经理 原则上需要区分三营 / 政企校验
            case ADMIN_NET_PROD_DIR:
            case ADMIN_REP_DEPUTY_MNG:
            case ADMIN_NET_DEPT_MNG:
            case ADMIN_NETSERVICE_LV4:
            case ADMIN_DIR_TELE_SERVICE:
                if (!ApproveResultEnum.SKIP.equals(approveResult)
                        && !ApproveResultEnum.PASS.equals(approveResult)
                        && !ApproveResultEnum.TERMINATE.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;

            default:
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_NODE_ERROR));
        }
    }

    /**
     * 校验审批结果 - 合作方网络变更
     * @param currentNode
     * @param approvalSubmitDto
     */
    private void checkSubcontractorNetworkChangeSubmitInfo(PartnerApproveNodeEnum currentNode, ApprovalSubmitDto approvalSubmitDto) {
        // 1.审批结果必填
        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        if (Objects.isNull(approveResult)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_NOT_EXISTS));
        }

        // 2.校验当前进展
        PartnerApproveNodeEnum approveNode = PartnerApproveNodeEnum.getApproveNodeEnum(approvalSubmitDto.getApprovalNode());
        if (Objects.isNull(approveNode) || (!approveNode.equals(currentNode))) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.NODE_APPROVED));
        }

        // 3.节点特殊校验
        switch (currentNode) {
            // (1) 代表处产品科长 / 办事处PD / 办事产品科长 - 校验审批结果范围
            // 审批结果类型一：审核通过 / 审核取消 (同实际页面展示内容有区别)
            case PARTNER_REPRESENTATIVE_CHIEF_APPROVAL:
            case PARTNER_OFFICE_PD_APPROVAL:
            case PARTNER_OFFICE_PROD_CHIEF_APPROVAL:
                if (!ApproveResultEnum.PASS.equals(approveResult) && !ApproveResultEnum.CANCEL.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;

            // 涉及节点：网络处 / 网络责任人 / 办事处产品经理
            // (2) 网络处 - 校验审批结果范围
            // 审批结果类型二：审核通过 / 审核驳回修改 / 审核取消 (同实际页面展示内容有区别)
            case PARTNER_NET_DEPT_APPROVAL:
                if (!ApproveResultEnum.PASS.equals(approveResult)
                        && !ApproveResultEnum.REJECT.equals(approveResult)
                        && !ApproveResultEnum.CANCEL.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;

            // (3) 网络责任人 - 校验审批结果范围 + 是否需要办事处产品经理审核
            // 审批结果类型二：审核通过 / 审核驳回修改 / 审核取消 (同实际页面展示内容有区别)
            case PARTNER_NET_OWNER_APPROVAL:
                checkPartnerNetOwnerNode(approveResult, approvalSubmitDto);
                break;

            // (4) 办事处产品经理 - 校验审批结果范围 + 是否升级至网络处
            // 审批结果类型二：审核通过 / 审核驳回修改 / 审核取消 (同实际页面展示内容有区别)
            case PARTNER_OFFICE_MANAGER_APPROVAL:
                checkPartnerOfficeManagerNode(approveResult, approvalSubmitDto);
                break;

            default:
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_NODE_ERROR));
        }
    }

    /**
     * 校验审批结果 - 合作方批次任务
     * @param currentNode
     * @param approvalSubmitDto
     */
    private void checkSubcontractorBatchSubmitInfo(PartnerApproveNodeEnum currentNode, ApprovalSubmitDto approvalSubmitDto) {
        // 1.审批结果必填
        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        if (Objects.isNull(approveResult)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_NOT_EXISTS));
        }

        // 2.校验当前进展
        PartnerApproveNodeEnum approveNode = PartnerApproveNodeEnum.getApproveNodeEnum(approvalSubmitDto.getApprovalNode());
        if (Objects.isNull(approveNode) || (!approveNode.equals(currentNode))) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.NODE_APPROVED));
        }

        // 3.审批结果在对应范围内
        switch (currentNode) {
            // 审批结果类型一：审核通过 / 审核驳回修改
            // 涉及节点：批次任务操作取消——代表处产品科长审核
            case OPERATION_CANCEL_REVIEW:
                if (!ApproveResultEnum.PASS.equals(approveResult) && !ApproveResultEnum.REJECT.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;
            // 审批结果类型一：审核通过 / 审核不通过，终止 (同实际页面展示内容有区别)
            // 涉及节点：代表处产品科长 / 网服务部三层 / 研发三层 / 工服三部部长 / 操作计划审核
            case PARTNER_OFFICE_PROD_CHIEF_APPROVAL:
            case PARTNER_OFFICE_PD_APPROVAL:
            case PARTNER_OPERATION_PLAN_APPROVAL:
                if (!ApproveResultEnum.PASS.equals(approveResult) && !ApproveResultEnum.TERMINATE.equals(approveResult)) {
                    throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
                }
                break;

            default:
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_NODE_ERROR));
        }
    }

    /**
     * 校验审批结果 - 节点 - 网络责任人
     * @param approveResult
     * @param approvalSubmitDto
     */
    private void checkPartnerNetOwnerNode(ApproveResultEnum approveResult, ApprovalSubmitDto approvalSubmitDto) {
        // 1.审批结果在对应范围内
        if (!ApproveResultEnum.PASS.equals(approveResult)
                && !ApproveResultEnum.REJECT.equals(approveResult)
                && !ApproveResultEnum.CANCEL.equals(approveResult)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
        }

        // 2.审批通过 额外校验
        if (ApproveResultEnum.PASS.equals(approveResult)) {
            // (1) 是否需要办事处产品经理审核 必填
            if (Objects.isNull(approvalSubmitDto.getNeedProductManagerApprove())) {
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.NEED_PRODUCT_MANAGER_APPROVE_NOT_EXISTS));
            }

            // (2) 需要办事处产品经理审核，审核人 + 审核组必填
            if (approvalSubmitDto.getNeedProductManagerApprove() && CollectionUtils.isEmpty(approvalSubmitDto.getProductManager())) {
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.REVIEWER_OR_APPROVE_TEAM_NOT_EXISTS));
            }
        }
    }

    /**
     * 校验审批结果 - 节点 - 办事处产品经理
     * @param approveResult
     * @param approvalSubmitDto
     */
    private void checkPartnerOfficeManagerNode(ApproveResultEnum approveResult, ApprovalSubmitDto approvalSubmitDto) {
        // 1.审批结果在对应范围内
        if (!ApproveResultEnum.PASS.equals(approveResult)
                && !ApproveResultEnum.REJECT.equals(approveResult)
                && !ApproveResultEnum.CANCEL.equals(approveResult)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.APPROVAL_RESULT_ERROR));
        }

        // 2.审批通过 额外校验
        if (ApproveResultEnum.PASS.equals(approveResult)) {
            // (1) 是否升级至网络处 必填
            if (Objects.isNull(approvalSubmitDto.getNeedNetworkDepartmentApprove())) {
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.NEED_NETWORK_DEPARTMENT_APPROVE_NOT_EXISTS));
            }

            // (2) 升级至网络处，审核人 + 审核组必填
            if (approvalSubmitDto.getNeedNetworkDepartmentApprove() && CollectionUtils.isEmpty(approvalSubmitDto.getNetworkDepartmentReviewer())) {
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.REVIEWER_OR_APPROVE_TEAM_NOT_EXISTS));
            }
        }
    }

    /**
     * 更新审批结果 - 网络变更
     */
    private void submitApproveResult(
            NetworkChangeAssignment assignment,
            ChangeOrder changeOrder,
            ApproveNodeEnum approveNode,
            ApprovalSubmitDto approvalSubmitDto) {

        ChangeOrder updateChangeOrder = new ChangeOrder();
        updateChangeOrder.setId(changeOrder.getId());

        // 1.检索 + 包装审批人信息
        String approvedUserId = RequestContextHolder.getEmpNo();
        List<Employee> approvedUserInfo = HrClient.queryEmployeeInfo(Lists.newArrayList(approvedUserId));

        // 2.审批时间取当前时间
        Date approvedTime = new Date();

        // 3.审批结果 + 审批意见
        ApproveResultEnum approveResultEnum
                = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        List<TextValuePair> approvalResult
                = calculateSavingApproveResult(changeOrder, approveNode, approveResultEnum);
        String approvalOpinion = approvalSubmitDto.getApprovalOpinion();

        // 4.更新单据提交信息
        String processVariable;
        String opinionProcessVariable;
        switch (approveNode) {
            case RD_LEADER_APP:
                updateChangeOrder.setApproveResultRdLeader(approvalResult);
                updateChangeOrder.setApproveOpinionRdLeaderApp(approvalOpinion);
                updateChangeOrder.setApprovedByRdLeader(approvedUserInfo);
                updateChangeOrder.setApprovedTimeRdLeader(approvedTime);

                processVariable = ChangeOrderFieldConsts.RdLeaderAppConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.RdLeaderAppConsts.APPROVE_OPINION;
                break;

            case TD_NET_DEPT_LEADER:
                updateChangeOrder.setApproveResultTdNetDeptLeader(approvalResult);
                updateChangeOrder.setApproveOpinionTdNetDeptLeader(approvalOpinion);
                updateChangeOrder.setApprovedByTdNetDeptLeader(approvedUserInfo);
                updateChangeOrder.setApprovedTimeTdNetDeptLeader(approvedTime);

                processVariable = ChangeOrderFieldConsts.TdNetDeptLeaderConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.TdNetDeptLeaderConsts.APPROVE_OPINION;
                break;

            case ADMIN_REP_PROD_CHIEF:
                updateChangeOrder.setApproveResultAdminRepProdChief(approvalResult);
                updateChangeOrder.setApproveOpinionAdminRepProdChief(approvalOpinion);
                updateChangeOrder.setApprovedByAdminRepProdChief(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminRepProdChief(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminRepProdChiefFieldConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminRepProdChiefFieldConsts.APPROVE_OPINION;
                break;

            case ADMIN_NETSERVICE_LV3:
                updateChangeOrder.setApproveResultAdminNetServiceLV3(approvalResult);
                updateChangeOrder.setApproveOpinionAdminNetServiceLV3(approvalOpinion);
                updateChangeOrder.setApprovedByAdminNetServiceLV3(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminNetServiceLV3(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminNetServiceLV3FieldConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminNetServiceLV3FieldConsts.APPROVE_OPINION;
                break;

            case ADMIN_RD_DEPT_LV3:
                updateChangeOrder.setApproveResultAdminRdDeptLV3(approvalResult);
                updateChangeOrder.setApproveOpinionAdminRdDeptLV3(approvalOpinion);
                updateChangeOrder.setApprovedByAdminRdDeptLV3(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminRdDeptLV3(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminRdDeptLV3FieldConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminRdDeptLV3FieldConsts.APPROVE_OPINION;
                break;

            case ADMIN_ENG_SERVICE3:
                updateChangeOrder.setApproveResultAdminEngService3Leader(approvalResult);
                updateChangeOrder.setApproveOpinionAdminEngService3Leader(approvalOpinion);
                updateChangeOrder.setApprovedByAdminEngService3Leader(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminEngService3Leader(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminEngService3FieldConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminEngService3FieldConsts.APPROVE_OPINION_FIELD_NAME;
                break;

            case ADMIN_NET_PROD_DIR:
                updateChangeOrder.setApproveResultAdminNetProdDirector(approvalResult);
                updateChangeOrder.setApproveOpinionAdminNetProdDirector(approvalOpinion);
                updateChangeOrder.setApprovedByAdminNetProdDirector(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminNetProdDirector(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminNetProdDirFieldConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminNetProdDirFieldConsts.APPROVE_OPINION;
                break;

            case ADMIN_REP_DEPUTY_MNG:
                updateChangeOrder.setApproveResultAdminRepDeputyManager(approvalResult);
                updateChangeOrder.setApproveOpinionAdminRepDeputyManager(approvalOpinion);
                updateChangeOrder.setApprovedByAdminRepDeputyManager(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminRepDeputyManager(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminRepDeputyMngFieldConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminRepDeputyMngFieldConsts.APPROVE_OPINION;
                break;

            case ADMIN_NET_DEPT_MNG:
                updateChangeOrder.setApproveResultAdminNetDeptMng(approvalResult);
                updateChangeOrder.setApproveOpinionAdminNetDeptMng(approvalOpinion);
                updateChangeOrder.setApprovedByAdminNetDeptMng(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminNetDeptMng(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminNetDeptMngFieldConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminNetDeptMngFieldConsts.APPROVE_OPINION;
                break;

            case ADMIN_NETSERVICE_LV4:
                updateChangeOrder.setApproveResultAdminNetServiceLV4(approvalResult);
                updateChangeOrder.setApproveOpinionAdminNetServiceLV4(approvalOpinion);
                updateChangeOrder.setApprovedByAdminNetServiceLV4(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminNetServiceLV4(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminNetServiceLV4FieldConsts.APPROVE_RESULT;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminNetServiceLV4FieldConsts.APPROVE_OPINION;
                break;

            case ADMIN_DIR_TELE_SERVICE:
                updateChangeOrder.setApproveResultAdminDirTeleSerDirector(approvalResult);
                updateChangeOrder.setApproveOpinionAdminDirTeleSerDirector(approvalOpinion);
                updateChangeOrder.setApprovedByAdminDirTeleSerDirector(approvedUserInfo);
                updateChangeOrder.setApprovedTimeAdminDirTeleSerDirector(approvedTime);

                processVariable = ChangeOrderFieldConsts.AdminDirTeleSerFieldConsts.APPROVE_RESULT_FIELD_NAME;
                opinionProcessVariable = ChangeOrderFieldConsts.AdminDirTeleSerFieldConsts.APPROVE_OPINION_FIELD_NAME;
                break;

            case INTL_ADMIN_APPROVAL:
                submitChangeOrderInterAdminApproval(
                        assignment, approvedUserId, approvedTime, approvalSubmitDto);
                return;

            default:
                return;
        }
        ChangeOrderAbility.update(updateChangeOrder);

        // 5.包装流程提交信息
        String approvalResultCn = CollectionUtils.isEmpty(approvalResult)
                ? EMPTY_STRING : approvalResult.get(INTEGER_ZERO).getTextByLanguage(ZH_CN);
        String approvalResultEn = CollectionUtils.isEmpty(approvalResult)
                ? EMPTY_STRING : approvalResult.get(INTEGER_ZERO).getTextByLanguage(EN_US);

        HashMap<String, ResetParameter> parameterMap = assembleParameterMap(processVariable,
                opinionProcessVariable, approvalResultCn, approvalResultEn, approvalSubmitDto);

        FlowHelper.submitSystemNode(changeOrder.getId(), parameterMap);
    }

    /**
     * 更新审批结果 - 合作方网络变更
     * @param approveNode
     * @param changeOrderId
     * @param approvalSubmitDto
     * @return HashMap<String, ResetParameter>
     */
    private HashMap<String, ResetParameter> updateSubcontractorChangeOrderSubmitInfo(PartnerApproveNodeEnum approveNode,
                                                                                     String changeOrderId,
                                                                                     ApprovalSubmitDto approvalSubmitDto) {
        SubcontractorChangeOrder updateChangeOrder = new SubcontractorChangeOrder();
        updateChangeOrder.setId(changeOrderId);

        // 1.检索 + 包装审批人信息
        String approvedUserId = RequestContextHolder.getEmpNo();
        List<Employee> approvedUserInfo = HrClient.queryEmployeeInfo(Lists.newArrayList(approvedUserId));

        // 2.审批时间取当前时间
        Date approvedTime = new Date();

        // 3.更新单据提交信息
        String opinion = approvalSubmitDto.getApprovalOpinion();
        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        assert approveResult != null;

        List<TextValuePair> approveResultZhEn;
        String processVariable;
        String opinionProcessVariable;
        switch (approveNode) {
            case PARTNER_REPRESENTATIVE_CHIEF_APPROVAL:
                approveResultZhEn = convertSubcontractDefaultTypeApproveResult(approveResult);
                updateChangeOrder.setApproveResultRepProdChief(approveResultZhEn);
                updateChangeOrder.setApproveOpinionRepProdChief(opinion);
                updateChangeOrder.setApprovedByRepProdChief(approvedUserInfo);
                updateChangeOrder.setApprovedTimeRepProdChief(approvedTime);

                processVariable = SubcontractorChangeOrderFieldConsts.RepProdChiefFieldConsts.APPROVE_RESULT;
                opinionProcessVariable = SubcontractorChangeOrderFieldConsts.RepProdChiefFieldConsts.APPROVE_OPINION;
                break;

            case PARTNER_NET_DEPT_APPROVAL:
                approveResultZhEn = convertSubcontractMultiChooseApproveResult(approveResult);
                updateChangeOrder.setApproveResultNetDept(approveResultZhEn);
                updateChangeOrder.setApproveOpinionNetDept(opinion);
                updateChangeOrder.setApprovedByNetDept(approvedUserInfo);
                updateChangeOrder.setApprovedTimeNetDept(approvedTime);

                processVariable = SubcontractorChangeOrderFieldConsts.NetDeptFieldConsts.APPROVE_RESULT;
                opinionProcessVariable = SubcontractorChangeOrderFieldConsts.NetDeptFieldConsts.APPROVE_OPINION;
                break;

            case PARTNER_OFFICE_PD_APPROVAL:
                approveResultZhEn = convertDefaultTypeApproveResult(approveResult);
                updateChangeOrder.setApproveResultOfficePd(approveResultZhEn);
                updateChangeOrder.setApproveOpinionOfficePd(opinion);
                updateChangeOrder.setApprovedByOfficePd(approvedUserInfo);
                updateChangeOrder.setApprovedTimeOfficePd(approvedTime);

                processVariable = SubcontractorChangeOrderFieldConsts.OfficePdFieldConsts.APPROVE_RESULT;
                opinionProcessVariable = SubcontractorChangeOrderFieldConsts.OfficePdFieldConsts.APPROVE_OPINION;
                break;

            case PARTNER_OFFICE_PROD_CHIEF_APPROVAL:
                approveResultZhEn = convertDefaultTypeApproveResult(approveResult);
                updateChangeOrder.setApproveResultOfficeProdChief(approveResultZhEn);
                updateChangeOrder.setApproveOpinionOfficeProdChief(opinion);
                updateChangeOrder.setApprovedByOfficeProdChief(approvedUserInfo);
                updateChangeOrder.setApprovedTimeOfficeProdChief(approvedTime);

                processVariable = SubcontractorChangeOrderFieldConsts.OfficeProdChiefFieldConsts.APPROVE_RESULT;
                opinionProcessVariable = SubcontractorChangeOrderFieldConsts.OfficeProdChiefFieldConsts.APPROVE_OPINION;
                break;

            case PARTNER_NET_OWNER_APPROVAL:
                return updateSubcontractorNetOwnerApproval(
                        updateChangeOrder,
                        approvedUserInfo,
                        approvedTime,
                        approvalSubmitDto);

            case PARTNER_OFFICE_MANAGER_APPROVAL:
                return updateSubcontractorOfficeManagerApproval(
                        updateChangeOrder,
                        approvedUserInfo,
                        approvedTime,
                        approvalSubmitDto);

            default:
                return new HashMap<>();
        }
        PartnerChangeOrderAbility.update(updateChangeOrder);

        // 4.包装流程提交信
        TextValuePair textValuePair = approveResultZhEn.get(0);
        return assembleParameterMap(processVariable, opinionProcessVariable,
                textValuePair.getTextByLanguage(ZH_CN), textValuePair.getTextByLanguage(EN_US), approvalSubmitDto);
    }

    /**
     * 更新审批结果 - 批次任务
     */
    private void submitBatchApproveResult(
            NetworkChangeAssignment assignment,
            BatchTask batchTask,
            ApproveNodeEnum approveNode,
            ApprovalSubmitDto approvalSubmitDto) {

        BatchTask updateBatchTask = new BatchTask();
        updateBatchTask.setId(batchTask.getId());

        // 1.检索 + 包装审批人信息
        String approvedUserId = RequestContextHolder.getEmpNo();
        List<Employee> approvedUserInfo = HrClient.queryEmployeeInfo(Lists.newArrayList(approvedUserId));

        // 2.审批时间取当前时间
        Date approvedTime = new Date();

        // 3.审批结果 + 审批意见
        ChangeOrder changeOrder = ChangeOrderAbility.get(batchTask.getChangeOrderId(), Lists.newArrayList());
        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        List<TextValuePair> approvalResult = calculateSavingApproveResult(changeOrder, approveNode, approveResult);
        String approvalOpinion = approvalSubmitDto.getApprovalOpinion();

        // 4.更新单据提交信息
        String processVariable;
        String opinionProcessVariable;
        switch (approveNode) {
            case OPERATION_CANCEL_REVIEW:
                if (ApproveResultEnum.PASS.equals(approveResult)){
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.ABOLISH.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.N);
                } else {
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.Y);
                    updateBatchTask.setRejectNode(approvalSubmitDto.getApprovalNode());
                    updateBatchTask.setPersonRejection(approvedUserInfo);
                    updateBatchTask.setOpinionRejection(approvalOpinion);
                    updateBatchTask.setResultRejection(ApproveResultEnum.REJECT);
                }
                updateBatchTask.setOcApproveResult(approvalResult);
                updateBatchTask.setOcReviewOpinion(approvalOpinion);

                processVariable = BatchTaskFieldConsts.OC_APPROVE_RESULT;
                opinionProcessVariable = BatchTaskFieldConsts.OC_REVIEW_OPINION;
                break;

            case ADMIN_REP_PROD_CHIEF:
                updateBatchTask.setApproveResultAdminRepProdChief(approvalResult);
                updateBatchTask.setApproveOpinionAdminRepProdChief(approvalOpinion);
                updateBatchTask.setApprovedByAdminRepProdChief(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminRepProdChief(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_PROD_CHIEF;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_PROD_CHIEF;
                break;

            case ADMIN_NET_PROD_DIR:
                updateBatchTask.setApproveResultAdminNetProdDirector(approvalResult);
                updateBatchTask.setApproveOpinionAdminNetProdDirector(approvalOpinion);
                updateBatchTask.setApprovedByAdminNetProdDirector(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminNetProdDirector(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_PROD_DIRECTOR;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_PROD_DIRECTOR;
                break;

            case ADMIN_REP_DEPUTY_MNG:
                updateBatchTask.setApproveResultAdminRepDeputyManager(approvalResult);
                updateBatchTask.setApproveOpinionAdminRepDeputyManager(approvalOpinion);
                updateBatchTask.setApprovedByAdminRepDeputyManager(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminRepDeputyManager(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_DEPUTY_MANAGER;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_DEPUTY_MANAGER;
                break;

            case ADMIN_NET_DEPT_MNG:
                updateBatchTask.setApproveResultAdminNetDeptMng(approvalResult);
                updateBatchTask.setApproveOpinionAdminNetDeptMng(approvalOpinion);
                updateBatchTask.setApprovedByAdminNetDeptMng(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminNetDeptMng(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_DEPT_MNG;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_DEPT_MNG;
                break;

            case ADMIN_NETSERVICE_LV4:
                updateBatchTask.setApproveResultAdminNetServiceLV4(approvalResult);
                updateBatchTask.setApproveOpinionAdminNetServiceLV4(approvalOpinion);
                updateBatchTask.setApprovedByAdminNetServiceLV4(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminNetServiceLV4(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_SERVCIE_LV4;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_SERVCIE_LV4;
                break;

            case ADMIN_NETSERVICE_LV3:
                updateBatchTask.setApproveResultAdminNetServiceLV3(approvalResult);
                updateBatchTask.setApproveOpinionAdminNetServiceLV3(approvalOpinion);
                updateBatchTask.setApprovedByAdminNetServiceLV3(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminNetServiceLV3(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_SERVCIE_LV3;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_SERVCIE_LV3;
                break;

            case ADMIN_RD_DEPT_LV3:
                updateBatchTask.setApproveResultAdminRdDeptLV3(approvalResult);
                updateBatchTask.setApproveOpinionAdminRdDeptLV3(approvalOpinion);
                updateBatchTask.setApprovedByAdminRdDeptLV3(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminRdDeptLV3(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_RD_DEPT_LV3;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_RD_DEPT_LV3;
                break;

            case ADMIN_ENG_SERVICE3:
                updateBatchTask.setApproveResultAdminEngService3Leader(approvalResult);
                updateBatchTask.setApproveOpinionAdminEngService3Leader(approvalOpinion);
                updateBatchTask.setApprovedByAdminEngService3Leader(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminEngService3Leader(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_ENG_SERVICE3_LEADER;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_ENG_SERVICE3_LEADER_FIELD_NAME;
                break;

            case ADMIN_DIR_TELE_SERVICE:
                updateBatchTask.setApproveResultAdminDirTeleSerDirector(approvalResult);
                updateBatchTask.setApproveOpinionAdminDirTeleSerDirector(approvalOpinion);
                updateBatchTask.setApprovedByAdminDirTeleSerDirector(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminDirTeleSerDirector(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME;
                break;

            case CHANGED_BY_REP_PROD_CHIEF:
                updateBatchTask.setApproveResultRepProdChief(approvalResult);
                updateBatchTask.setApproveOpinionRepProdChief(approvalOpinion);
                updateBatchTask.setApprovedByRepProdChief(approvedUserInfo);
                updateBatchTask.setApprovedTimeRepProdChief(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_REP_PROD_CHIEF;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_REP_PROD_CHIEF;
                break;

            case BATCH_INTL_ADMIN_APPROVAL:
                submitBatchInterAdminApproval(assignment, approvedUserId, approvedTime, approvalSubmitDto);
                return;

            default:
                return;
        }
        BatchTaskAbility.batchUpdate(Lists.newArrayList(updateBatchTask));

        String approvalResultCn = CollectionUtils.isEmpty(approvalResult)
                ? EMPTY_STRING : approvalResult.get(INTEGER_ZERO).getTextByLanguage(ZH_CN);
        String approvalResultEn = CollectionUtils.isEmpty(approvalResult)
                ? EMPTY_STRING : approvalResult.get(INTEGER_ZERO).getTextByLanguage(EN_US);

        // 4.包装流程提交信息
        HashMap<String, ResetParameter> parameterMap = assembleParameterMap(processVariable,
                opinionProcessVariable, approvalResultCn, approvalResultEn, approvalSubmitDto);
        FlowHelper.submitSystemNode(batchTask.getId(), parameterMap);
    }

    /**
     * 兼容web端审批日志功能
     * 组装相关信息：审核结果及其中英文、审核意见
     */
    private HashMap<String, ResetParameter> assembleParameterMap(
            String processVariable,
            String opinionProcessVariable,
            String approvalResultCn,
            String approvalResultEn,
            ApprovalSubmitDto approvalSubmitDto) {
        HashMap<String, ResetParameter> parameterMap = new HashMap<>();
        // 审核结果及其中英文
        ResetParameter resultParameter = assembleResetParameter(CommonConstants.STRING, approvalSubmitDto.getApprovalResult());
        parameterMap.put(processVariable, resultParameter);
        ResetParameter resultEnParameter = assembleResetParameter(CommonConstants.STRING, approvalResultEn);
        parameterMap.put(processVariable + FLOW_EN_US, resultEnParameter);
        ResetParameter resultCnParameter = assembleResetParameter(CommonConstants.STRING, approvalResultCn);
        parameterMap.put(processVariable + FLOW_ZH_CN, resultCnParameter);
        // 审核意见
        ResetParameter opinionParameter = assembleResetParameter(CommonConstants.STRING, approvalSubmitDto.getApprovalOpinion());
        parameterMap.put(opinionProcessVariable, opinionParameter);
        return parameterMap;
    }

    private ResetParameter assembleResetParameter(String paramType, Object paramValue) {
        ResetParameter resetParameter = new ResetParameter();
        resetParameter.setParameterType(paramType);
        resetParameter.setParameterValue(paramValue);
        return resetParameter;
    }

    /**
     * 更新审批结果 - 合作方网络变更
     * @param approveNode
     * @param batchTaskId
     * @param approvalSubmitDto
     * @return HashMap<String, ResetParameter>
     */
    private HashMap<String, ResetParameter> updateSubcontractorBatchSubmitInfo(PartnerApproveNodeEnum approveNode,
                                                                               String batchTaskId,
                                                                               ApprovalSubmitDto approvalSubmitDto) {
        SubcontractorBatchTask updateBatchTask = new SubcontractorBatchTask();
        updateBatchTask.setId(batchTaskId);

        // 1.检索 + 包装审批人信息
        String approvedUserId = RequestContextHolder.getEmpNo();
        List<Employee> approvedUserInfo = HrClient.queryEmployeeInfo(Lists.newArrayList(approvedUserId));

        // 2.审批时间取当前时间
        Date approvedTime = new Date();

        // 3.更新单据提交信息
        String opinion = approvalSubmitDto.getApprovalOpinion();
        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        assert approveResult != null;

        String processVariable;
        String opinionProcessVariable;
        List<TextValuePair> approveResultZhEn;
        switch (approveNode) {
            case OPERATION_CANCEL_REVIEW:
                if (ApproveResultEnum.PASS.equals(approveResult)){
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.ABOLISH.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.N);
                } else {
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.Y);
                    updateBatchTask.setRejectNode(approvalSubmitDto.getApprovalNode());
                    updateBatchTask.setPersonRejection(approvedUserInfo);
                    updateBatchTask.setOpinionRejection(approvalSubmitDto.getApprovalOpinion());
                    updateBatchTask.setResultRejection(ApproveResultEnum.REJECT);
                }
                approveResultZhEn = convertCancelOperationApproveResult(approveResult);
                updateBatchTask.setOcApproveResult(approveResultZhEn);
                updateBatchTask.setOcReviewOpinion(opinion);

                processVariable = BatchTaskFieldConsts.OC_APPROVE_RESULT;
                opinionProcessVariable = BatchTaskFieldConsts.OC_REVIEW_OPINION;
                break;

            case PARTNER_OFFICE_PROD_CHIEF_APPROVAL:
                if (ApproveResultEnum.PASS.equals(approveResult)){
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.OPERATION_EXECUTION.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.Y);
                } else {
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.N);
                }

                approveResultZhEn = convertDefaultTypeApproveResult(approveResult);
                updateBatchTask.setApproveResultAdminRepProdChief(approveResultZhEn);
                updateBatchTask.setApproveOpinionAdminRepProdChief(opinion);
                updateBatchTask.setApprovedByAdminRepProdChief(approvedUserInfo);
                updateBatchTask.setApprovedTimeAdminRepProdChief(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_PROD_CHIEF;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_PROD_CHIEF;
                break;

            case PARTNER_OFFICE_PD_APPROVAL:
                if (ApproveResultEnum.PASS.equals(approveResult)){
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.NOTIFICATION_UNDER_REVIEW.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.Y);
                } else {
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.N);
                }

                approveResultZhEn = convertDefaultTypeApproveResult(approveResult);
                updateBatchTask.setApproveResultOfficePd(approveResultZhEn);
                updateBatchTask.setApproveOpinionOfficePd(opinion);
                updateBatchTask.setApprovedByOfficePd(approvedUserInfo);
                updateBatchTask.setApprovedTimeOfficePd(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_OFFICE_PD;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_OFFICE_PD;
                break;

            case PARTNER_OPERATION_PLAN_APPROVAL:
                if (ApproveResultEnum.PASS.equals(approveResult)){
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.OPERATION_EXECUTION.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.Y);
                } else {
                    updateBatchTask.setCurrentStatus(AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());
                    updateBatchTask.setApprovalStatus(BoolEnum.N);
                }

                approveResultZhEn = convertSpecialDefaultTypeApproveResult(approveResult);
                updateBatchTask.setApproveResultOperationPlan(approveResultZhEn);
                updateBatchTask.setApproveOpinionOperationPlan(opinion);
                updateBatchTask.setApprovedByOperationPlan(approvedUserInfo);
                updateBatchTask.setApprovedTimeOperationPlan(approvedTime);

                processVariable = BatchTaskFieldConsts.APPROVE_RESULT_OPERATION_PLAN;
                opinionProcessVariable = BatchTaskFieldConsts.APPROVE_OPINION_OPERATION_PLAN;
                break;

            default:
                return new HashMap<>();
        }
        BatchTaskAbility.batchUpdateSub(Lists.newArrayList(updateBatchTask));

        TextValuePair textValuePair = approveResultZhEn.get(0);


        // 4.包装流程提交信息
        return assembleParameterMap(processVariable,
                opinionProcessVariable, textValuePair.getTextByLanguage(ZH_CN), textValuePair.getTextByLanguage(EN_US), approvalSubmitDto);
    }

    /**
     * 网络责任人提交 - 分包商网络变更
     * @param updateChangeOrder
     * @param approvedUserInfo
     * @param approvalDate
     * @param approvalSubmitDto
     */
    private HashMap<String, ResetParameter> updateSubcontractorNetOwnerApproval(SubcontractorChangeOrder updateChangeOrder,
                                                                                List<Employee> approvedUserInfo,
                                                                                Date approvalDate,
                                                                                ApprovalSubmitDto approvalSubmitDto) {
        // 1.更新审批结果基础信息信息
        updateChangeOrder.setApproveOpinionNetOwner(approvalSubmitDto.getApprovalOpinion());
        updateChangeOrder.setApprovedByNetOwner(approvedUserInfo);
        updateChangeOrder.setApprovedTimeNetOwner(approvalDate);

        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        List<TextValuePair> approvalResult = convertSubcontractMultiChooseApproveResult(approveResult);
        updateChangeOrder.setApproveResultNetOwner(approvalResult);

        // 2.办事处产品经理 / 办事处产品经理审核组用户信息转换
        List<Employee> officeProdManagerEmployee = HrClient.queryEmployeeInfo(approvalSubmitDto.getProductManager());
        List<Employee> officeProdManagerReviewTeamEmployee = HrClient.queryEmployeeInfo(approvalSubmitDto.getProductManagerApproveTeam());

        // 3.更新办事处产品经理 / 办事处产品经理审核组信息
        if (ApproveResultEnum.PASS.equals(approveResult)) {
            if (approvalSubmitDto.getNeedProductManagerApprove()) {
                updateChangeOrder.setIsReviewOfficeProdManager(BoolEnum.Y);
                updateChangeOrder.setOfficeProdManager(officeProdManagerEmployee);
                updateChangeOrder.setOfficeProdManagerReviewTeam(officeProdManagerReviewTeamEmployee);
            } else {
                updateChangeOrder.setIsReviewOfficeProdManager(BoolEnum.N);
            }
        }

        // 4.更新合作方网络变更单
        PartnerChangeOrderAbility.update(updateChangeOrder);

        // 5.包装流程提交信息
        String approvalResultCn = CollectionUtils.isEmpty(approvalResult)
                ? EMPTY_STRING : approvalResult.get(INTEGER_ZERO).getTextByLanguage(ZH_CN);
        String approvalResultEn = CollectionUtils.isEmpty(approvalResult)
                ? EMPTY_STRING : approvalResult.get(INTEGER_ZERO).getTextByLanguage(EN_US);
        HashMap<String, ResetParameter> parameterMap = assembleParameterMap(
                SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.APPROVE_RESULT,
                SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.APPROVE_OPINION,
                approvalResultCn,
                approvalResultEn,
                approvalSubmitDto);

        ResetParameter needProdManagerResetParameter = new ResetParameter();
        needProdManagerResetParameter.setParameterType(CommonConstants.STRING);
        if (ApproveResultEnum.PASS.equals(approveResult)) {
            needProdManagerResetParameter.setParameterValue(updateChangeOrder.getIsReviewOfficeProdManager().getValue());
        }

        parameterMap.put(SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.IS_REVIEW_OFFICE_PROD_MANAGER, needProdManagerResetParameter);

        return parameterMap;
    }

    /**
     * 办事处产品经理提交 - 分包商网络变更
     * @param updateChangeOrder
     * @param approvedUserInfo
     * @param approvalDate
     * @param approvalSubmitDto
     */
    private HashMap<String, ResetParameter> updateSubcontractorOfficeManagerApproval(SubcontractorChangeOrder updateChangeOrder,
                                                                                     List<Employee> approvedUserInfo,
                                                                                     Date approvalDate,
                                                                                     ApprovalSubmitDto approvalSubmitDto) {
        // 1.更新审批结果信息

        updateChangeOrder.setApproveOpinionOfficeProdManager(approvalSubmitDto.getApprovalOpinion());
        updateChangeOrder.setApprovedByOfficeProdManager(approvedUserInfo);
        updateChangeOrder.setApprovedTimeOfficeProdManager(approvalDate);

        ApproveResultEnum approveResult = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        List<TextValuePair> approvalResult = convertSubcontractMultiChooseApproveResult(approveResult);
        updateChangeOrder.setApproveResultOfficeProdManager(approvalResult);

        // 2.网络处审核人 / 网络处审核组用户信息转换
        List<Employee> netDeptApprovalEmployee = HrClient.queryEmployeeInfo(approvalSubmitDto.getNetworkDepartmentReviewer());
        List<Employee> netDeptApprovalTeamEmployee = HrClient.queryEmployeeInfo(approvalSubmitDto.getNetworkDepartmentApproveTeam());

        // 3.更新网络处审核人 / 网络处审核组信息
        if (ApproveResultEnum.PASS.equals(approveResult)) {
            if (approvalSubmitDto.getNeedNetworkDepartmentApprove()) {
                updateChangeOrder.setIsUpgradeNetDepartment(BoolEnum.Y);
                updateChangeOrder.setNetDeptApproval(netDeptApprovalEmployee);
                updateChangeOrder.setNetDeptApproveTeam(netDeptApprovalTeamEmployee);
            } else {
                updateChangeOrder.setIsUpgradeNetDepartment(BoolEnum.N);
            }
        }

        // 4.更新合作方网络变更单
        PartnerChangeOrderAbility.update(updateChangeOrder);

        // 5.包装流程提交信息
        String approvalResultCn = CollectionUtils.isEmpty(approvalResult)
                ? EMPTY_STRING : approvalResult.get(INTEGER_ZERO).getTextByLanguage(ZH_CN);
        String approvalResultEn = CollectionUtils.isEmpty(approvalResult)
                ? EMPTY_STRING : approvalResult.get(INTEGER_ZERO).getTextByLanguage(EN_US);
        HashMap<String, ResetParameter> parameterMap = assembleParameterMap(
                SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.APPROVE_RESULT,
                SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.APPROVE_OPINION,
                approvalResultCn,
                approvalResultEn,
                approvalSubmitDto);

        ResetParameter needNetDepartmentResetParameter = new ResetParameter();
        needNetDepartmentResetParameter.setParameterType(CommonConstants.STRING);
        if (ApproveResultEnum.PASS.equals(approveResult)) {
            needNetDepartmentResetParameter.setParameterValue(updateChangeOrder.getIsUpgradeNetDepartment().getValue());
        }

        parameterMap.put(SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.IS_UPGRADE_NET_DEPARTMENT, needNetDepartmentResetParameter);

        return parameterMap;
    }

    /**
     * 国际行政审批提交 - 网络变更
     */
    private void submitChangeOrderInterAdminApproval(
            NetworkChangeAssignment assignment,
            String approvalUserId,
            Date approvalDate,
            ApprovalSubmitDto approvalSubmitDto) {
        List<ApproveTask> taskList = FlowHelper.getSystemNodeTask(assignment.getEntityId());
        ApproveTask approveTask = taskList.stream()
                .filter(approve -> approvalUserId.equals(approve.getApprover()))
                .findFirst()
                .orElseThrow(() -> new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.REVIEWER_OR_APPROVE_TEAM_NOT_EXISTS)));
        // 1.检索国际会签人记录
        List<IntlAdminApproval> approvalRecords
                = InterAdminApproverAbility.getApprovalRecordList(assignment.getEntityId());
        List<String> updateRecordIds = approvalRecords.stream()
                .filter(item -> approvalUserId.equals(item.getApprover().getEmpUIID()))
                .map(IntlAdminApproval::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateRecordIds)) {
            throw new LcapBusiException(
                    MsgUtils.getMessage(MessageConsts.MobileApprove.REVIEWER_OR_APPROVE_TEAM_NOT_EXISTS));
        }

        // 2.更新记录
        ApproveResultEnum approveResultEnum
                = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        for (String updateRecordId : updateRecordIds) {
            Map<String, Object> valueMap = MapUtils.newHashMap(
                    IntlAdminApprovalFieldConsts.APPROVE_RESULT, convertDefaultTypeApproveResult(approveResultEnum),
                    IntlAdminApprovalFieldConsts.APPROVE_OPINION, approvalSubmitDto.getApprovalOpinion(),
                    IntlAdminApprovalFieldConsts.APPROVED_TIME, approvalDate);
            InterAdminApproverAbility.updateApprovalRecord(assignment.getEntityId(), updateRecordId, valueMap);
        }

        // 3.提交会签结果
        if (ApproveResultEnum.TERMINATE.equals(approveResultEnum)) {
            disagreeCountersign(assignment.getEntityId());
        } else {
            agreeCountersign(assignment, approvalUserId, false);
        }

        // 发送国际行政会签 -操作日志
        AsyncExecuteUtils.execute(() ->
                OperationLogRecordAbility.saveSignatureOperationLog(approvalDate, assignment.getEntityId(), approveTask.getTaskId()));
    }

    /**
     * 国际行政审批提交 - 批次任务
     */
    private void submitBatchInterAdminApproval(
            NetworkChangeAssignment assignment,
            String approvalUserId,
            Date approvalDate,
            ApprovalSubmitDto approvalSubmitDto) {

        String batchTaskId = assignment.getEntityId();
        List<ApproveTask> taskList = FlowHelper.getSystemNodeTask(batchTaskId);
        ApproveTask approveTask = taskList.stream()
                .filter(approve -> approvalUserId.equals(approve.getApprover()))
                .findFirst()
                .orElseThrow(() -> new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.REVIEWER_OR_APPROVE_TEAM_NOT_EXISTS)));


        // 1.检索国际会签人记录
        List<SubIntlAdminApproval> approvalRecords
                = InterAdminApproverAbility.getBatchApprovalRecordList(batchTaskId);
        List<String> updateRecordIds = approvalRecords.stream()
                .filter(item -> approvalUserId.equals(item.getApprover().getEmpUIID()))
                .map(IntlAdminApproval::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateRecordIds)) {
            throw new LcapBusiException(
                    MsgUtils.getMessage(MessageConsts.MobileApprove.REVIEWER_OR_APPROVE_TEAM_NOT_EXISTS));
        }

        // 2.更新记录
        ApproveResultEnum approveResultEnum
                = ApproveResultEnum.getApproveResultEnum(approvalSubmitDto.getApprovalResult());
        for (String updateRecordId : updateRecordIds) {
            Map<String, Object> valueMap = MapUtils.newHashMap(
                    IntlAdminApprovalFieldConsts.APPROVE_RESULT, convertDefaultTypeApproveResult(approveResultEnum),
                    IntlAdminApprovalFieldConsts.APPROVE_OPINION, approvalSubmitDto.getApprovalOpinion(),
                    IntlAdminApprovalFieldConsts.APPROVED_TIME, approvalDate);
            InterAdminApproverAbility.updateBatchApprovalRecord(batchTaskId, updateRecordId, valueMap);
        }

        // 3.提交会签结果
        if (ApproveResultEnum.TERMINATE.equals(approveResultEnum)) {
            disagreeCountersign(assignment.getEntityId());
        } else {
            agreeCountersign(assignment, approvalUserId, true);
        }

        // 发送国际行政会签 -操作日志
        AsyncExecuteUtils.execute(() ->
                OperationLogRecordAbility.saveSignatureOperationLog(approvalDate, assignment.getEntityId(), approveTask.getTaskId()));
    }

    /**
     * 会签结果处理 - 同意
     */
    private void agreeCountersign(
            NetworkChangeAssignment assignment,
            String approvalUserId,
            boolean batchFlag) {

        String entityId = assignment.getEntityId();

        // 当前处理人清除审批人
        List<Employee> currentProcessors = assignment.getCurrentProcessorEmployee();
        List<Employee> newCurrentProcessors = currentProcessors.stream()
                .filter(item -> !approvalUserId.equals(item.getEmpUIID()))
                .collect(Collectors.toList());

        NetworkChangeAssignment updateAssignment = new NetworkChangeAssignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setCurrentProcessorEmployee(newCurrentProcessors);
        AssignmentAbility.update(updateAssignment);

        // 所有人均会签，设置会签结果流程变量
        List<? extends IntlAdminApproval> approvalRecords = batchFlag
                ? InterAdminApproverAbility.getBatchApprovalRecordList(entityId)
                : InterAdminApproverAbility.getApprovalRecordList(entityId);
        List<IntlAdminApproval> uncountersignedList = approvalRecords.stream()
                .filter(item -> Objects.isNull(item.getResult()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uncountersignedList)) {
            FlowHelper.changeVariables(entityId, MapUtils.newHashMap(
                    FlowVariantEnum.APPROVE_RESULT_INTL_ADMIN, ApproveResultEnum.PASS));
        }

        // 提交流程
        ResetParameter resetParameter = new ResetParameter();
        resetParameter.setParameterType(CommonConstants.STRING);
        resetParameter.setParameterValue(ApproveResultEnum.PASS.getValue());

        HashMap<String, ResetParameter> parameterMap = new HashMap<>();
        parameterMap.put(EntityHelper.getEntityId(IntlAdminApproval.class), resetParameter);
        FlowHelper.submitSystemNode(assignment.getEntityId(), parameterMap);
    }

    /**
     * 会签结果处理 - 不同意
     */
    private void disagreeCountersign(String entityId) {
        // 设置流程变量终止
        FlowHelper.changeVariables(entityId, MapUtils.newHashMap(
                FlowVariantEnum.APPROVE_RESULT_INTL_ADMIN, ApproveResultEnum.TERMINATE));

        // 推送至下一节点
        AsyncExecuteUtils.execute(() -> {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            FlowHelper.pushSystemNode(entityId, FlowHelper.getCurrentNodeId(entityId));
        });
    }

    /**
     * 计算保存审批结果 - 网络变更
     * @param changeOrder
     * @param approveNode
     * @param approveResult
     * @return List<TextValuePair>
     */
    private List<TextValuePair> calculateSavingApproveResult(ChangeOrder changeOrder,
                                                             ApproveNodeEnum approveNode,
                                                             ApproveResultEnum approveResult) {
        switch (approveNode) {
            case ADMIN_NET_DEPT_MNG:
            case ADMIN_REP_DEPUTY_MNG:
                return getChangeDeptType(changeOrder)
                        ? convertUpgradeTypeApproveResult(approveResult)
                        : convertDefaultTypeApproveResult(approveResult);

            case ADMIN_NETSERVICE_LV4:
                return convertUpgradeTypeApproveResult(approveResult);

            case ADMIN_REP_PROD_CHIEF:
            case ADMIN_NETSERVICE_LV3:
            case ADMIN_RD_DEPT_LV3:
            case ADMIN_ENG_SERVICE3:
                return convertDefaultTypeApproveResult(approveResult);

            case RD_LEADER_APP:
            case TD_NET_DEPT_LEADER:
                return convertLeaderTypeApproveResult(approveResult);

            case ADMIN_NET_PROD_DIR:
            case ADMIN_DIR_TELE_SERVICE:
                return convertEndTypeApproveResult(approveResult);

            case CHANGED_BY_REP_PROD_CHIEF:
                return convertSpecialDefaultTypeApproveResult(approveResult);

            case OPERATION_CANCEL_REVIEW:
                return convertCancelOperationApproveResult(approveResult);

            default:
                return Lists.newArrayList();
        }
    }

    private List<TextValuePair> convertLeaderTypeApproveResult(ApproveResultEnum approveResult) {
        String approveResultCode = approveResult.getValue();
        return ApproveResultEnum.PASS.equals(approveResult)
                ? TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_PASS, EnglishText.AGREE)
                : TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_TERMINATE, EnglishText.DISAGREE);
    }

    private List<TextValuePair> convertCancelOperationApproveResult(ApproveResultEnum approveResult) {
        String approveResultCode = approveResult.getValue();
        return ApproveResultEnum.PASS.equals(approveResult)
                ? TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_PASS, EnglishText.PASS)
                : TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_REJECT_MODIFY, EnglishText.APPROVE_REJECT);
    }

    private List<TextValuePair> convertDefaultTypeApproveResult(ApproveResultEnum approveResult) {
        String approveResultCode = approveResult.getValue();
        return ApproveResultEnum.PASS.equals(approveResult)
                ? TextValuePairHelper.buildList(approveResultCode, ChineseText.AGREE, EnglishText.AGREE)
                : TextValuePairHelper.buildList(approveResultCode, ChineseText.DISAGREE, EnglishText.DISAGREE);
    }

    private List<TextValuePair> convertSpecialDefaultTypeApproveResult(ApproveResultEnum approveResult) {
        String approveResultCode = approveResult.getValue();
        return ApproveResultEnum.PASS.equals(approveResult)
                ? TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_PASS, EnglishText.APPROVE_PASS)
                : TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_REJECT_MODIFY, EnglishText.APPROVE_REJECT_MODIFY);
    }

    private List<TextValuePair> convertEndTypeApproveResult(ApproveResultEnum approveResult) {
        String approveResultCode = approveResult.getValue();

        if (ApproveResultEnum.SKIP.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode, ChineseText.AGREE_CONCLUDE_AUDIT, EnglishText.AGREE_CONCLUDE_AUDIT);
        }

        if (ApproveResultEnum.PASS.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode, ChineseText.AGREE_UPGRADE_REVIEW_REQUIRED, EnglishText.AGREE_UPGRADE_REVIEW_REQUIRED);
        }

        if (ApproveResultEnum.TERMINATE.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode, ChineseText.DISAGREE, EnglishText.DISAGREE);
        }

        return Lists.newArrayList();
    }

    private List<TextValuePair> convertUpgradeTypeApproveResult(ApproveResultEnum approveResult) {
        String approveResultCode = approveResult.getValue();

        if (ApproveResultEnum.SKIP.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode,
                    ChineseText.AGREE_N0_THREE_LEVEL_REVIEW_REQUIRED, EnglishText.AGREE_N0_THREE_LEVEL_REVIEW_REQUIRED);
        }

        if (ApproveResultEnum.PASS.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode,
                    ChineseText.AGREE_THREE_LEVEL_REVIEW_REQUIRED, EnglishText.AGREE_THREE_LEVEL_REVIEW_REQUIRED);
        }

        if (ApproveResultEnum.TERMINATE.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode, ChineseText.DISAGREE, EnglishText.DISAGREE);
        }

        return Lists.newArrayList();
    }

    private List<TextValuePair> convertSubcontractDefaultTypeApproveResult(ApproveResultEnum approveResult) {
        String approveResultCode = approveResult.getValue();
        return ApproveResultEnum.PASS.equals(approveResult)
                ? TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_PASS, EnglishText.APPROVE_PASS)
                : TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_TERMINATE, EnglishText.APPROVE_TERMINATE);
    }

    private List<TextValuePair> convertSubcontractMultiChooseApproveResult(ApproveResultEnum approveResult) {
        String approveResultCode = approveResult.getValue();

        if (ApproveResultEnum.PASS.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_PASS, EnglishText.APPROVE_PASS);
        }

        if (ApproveResultEnum.REJECT.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_REJECT_MODIFY, EnglishText.APPROVE_REJECT);
        }

        if (ApproveResultEnum.CANCEL.equals(approveResult)) {
            return TextValuePairHelper.buildList(approveResultCode, ChineseText.APPROVE_TERMINATE, EnglishText.APPROVE_TERMINATE);
        }

        return Lists.newArrayList();
    }

    /**
     * 校验国内政企 / 三营
     * @param changeOrder
     * @return getChangeDeptType
     */
    private boolean getChangeDeptType(ChangeOrder changeOrder) {
        DeptTypeEnum deptType = ChangeOrderAbility.getDeptType(changeOrder);
        return DeptTypeEnum.INNER.equals(deptType) && BoolEnum.N.getValue().equals(changeOrder.getIsGovEnt().getValue());
    }
}
