package com.zte.iccp.itech.extension.domain.enums.clockin;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum CallStatusEnum implements SingletonTextValuePairsProvider {
    /** 等待中 */
    WAITING,
    /** 呼叫中 */
    CALLING,
    /** 呼叫失败 */
    FAILED,
    /** 已接通 */
    CONNECTED,
    /** 未接通 */
    NOT_CONNECTED,
    /** 已废弃 */
    ABOLISHED,
    ;

    @Override
    public String getMsgKey() {
        return name();
    }

    @Override
    public String getValue() {
        return name();
    }
}
