package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.domain.enums.LinkTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.paas.lcap.common.api.metadata.engine.manage.model.ObjectInstanceLinkDO;
import com.zte.paas.lcap.common.api.metadata.engine.manage.model.ObjectInstanceLinkDTO;
import com.zte.paas.lcap.common.api.metadata.engine.manage.repository.IObjectInstanceLink;
import com.zte.paas.lcap.ddm.domain.helper.orm.BaseQueryDataHelper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.SYSTEM_USER;

public class ObjectLinkInstanceAbility {

    /**
     * 包装关系对象
     * @param appId
     * @param bizObjId
     * @param billIdIn
     * @param billIdOut
     * @param linkTypeEnum
     * @return ObjectInstanceLinkDTO
     */
    private static ObjectInstanceLinkDTO convertInstanceLink(String appId,
                                                             String bizObjId,
                                                             String billIdIn,
                                                             String billIdOut,
                                                             LinkTypeEnum linkTypeEnum) {
        ObjectInstanceLinkDTO instanceLink = new ObjectInstanceLinkDTO();

        instanceLink.setAppIdIn(appId);
        instanceLink.setAppIdOut(appId);
        instanceLink.setBizObjIn(bizObjId);
        instanceLink.setBizObjOut(bizObjId);
        instanceLink.setCreateBy(SYSTEM_USER);
        instanceLink.setLastModifiedBy(SYSTEM_USER);

        instanceLink.setBillIdIn(billIdIn);
        instanceLink.setBillIdOut(billIdOut);
        instanceLink.setLinkTypeId(linkTypeEnum.getId());

        return instanceLink;
    }

    /**
     * 新建实体父子级关系
     * @param entityEnum     实体类型
     * @param billIdIn       父级实体ID
     * @param billIdOutList  子级实体ID
     */
    public static void createParentChildrenLinkInstance(Class<? extends BaseEntity> entityEnum,
                                                        String billIdIn,
                                                        List<String> billIdOutList) {
        if (!StringUtils.hasText(billIdIn) || CollectionUtils.isEmpty(billIdOutList)) {
            return;
        }

        // 1.数据准备
        String appId = ContextHelper.getAppId();
        String bizObjId = EntityHelper.getEntityId(entityEnum);

        // 2.关联关系包装
        // 关联关系需要同时封装 2 条数据
        // 正向关系(Positive): 主任务对各批次任务父级关系
        // 逆向关系(Negative): 各批次任务对主任务子级关系
        IObjectInstanceLink objectInstanceLink = SpringContextUtil.getBeanFactory().getBean(IObjectInstanceLink.class);
        for (String billIdOut : billIdOutList) {
            objectInstanceLink.save(convertInstanceLink(appId, bizObjId, billIdIn, billIdOut, LinkTypeEnum.PARENT));
            objectInstanceLink.save(convertInstanceLink(appId, bizObjId, billIdOut, billIdIn, LinkTypeEnum.CHILD));
        }
    }

    /**
     * 新建实体关联关系
     * @param entityEnum     实体类型
     * @param billIdIn       父级实体ID
     * @param billIdOutList  子级实体ID
     */
    public static void createAssociateLinkInstance(Class<? extends BaseEntity> entityEnum,
                                                   String billIdIn,
                                                   List<String> billIdOutList) {
        if (!StringUtils.hasText(billIdIn) || CollectionUtils.isEmpty(billIdOutList)) {
            return;
        }

        // 1.数据准备
        String appId = ContextHelper.getAppId();
        String bizObjId = EntityHelper.getEntityId(entityEnum);

        // 2.关联关系包装
        // 关联关系需要同时封装 2 条数据
        // 正向关系(Positive): 主任务对各批次任务父级关系
        // 逆向关系(Negative): 各批次任务对主任务子级关系
        IObjectInstanceLink objectInstanceLink = SpringContextUtil.getBeanFactory().getBean(IObjectInstanceLink.class);
        for (String billIdOut : billIdOutList) {
            objectInstanceLink.save(convertInstanceLink(appId, bizObjId, billIdIn, billIdOut, LinkTypeEnum.ASSOCIATING));
            objectInstanceLink.save(convertInstanceLink(appId, bizObjId, billIdOut, billIdIn, LinkTypeEnum.ASSOCIATED));
        }
    }

    /**
     * 新建实体绑定关系
     * @param entityEnum     实体类型
     * @param billIdIn       被绑定实体ID
     * @param billIdOutList  绑定实体ID
     */
    public static void createBindingLinkInstance(Class<? extends BaseEntity> entityEnum,
                                                 String billIdIn,
                                                 List<String> billIdOutList) {
        if (!StringUtils.hasText(billIdIn) || CollectionUtils.isEmpty(billIdOutList)) {
            return;
        }

        // 1.数据准备
        String appId = ContextHelper.getAppId();
        String bizObjId = EntityHelper.getEntityId(entityEnum);

        // 2.关联关系包装
        // 关联关系需要同时封装 2 条数据
        // 正向关系(Positive): 主实体对各子实体 - 被绑定关系
        // 逆向关系(Negative): 各子实体对主实体 - 绑定关系
        IObjectInstanceLink objectInstanceLink = SpringContextUtil.getBeanFactory().getBean(IObjectInstanceLink.class);
        for (String billIdOut : billIdOutList) {
            objectInstanceLink.save(convertInstanceLink(appId, bizObjId, billIdIn, billIdOut, LinkTypeEnum.BOUND));
            objectInstanceLink.save(convertInstanceLink(appId, bizObjId, billIdOut, billIdIn, LinkTypeEnum.BINDING));
        }
    }

    /**
     * 检索关联数据
     * @param linkTypeEnum
     * @param entityIdList
     * @return List<ObjectInstanceLinkDO>
     */
    public static List<ObjectInstanceLinkDO> queryLinkInstance(LinkTypeEnum linkTypeEnum, List<String> entityIdList) {
        if (Objects.isNull(linkTypeEnum) || CollectionUtils.isEmpty(entityIdList)) {
            return Lists.newArrayList();
        }

        return BaseQueryDataHelper.queryAssociationList(linkTypeEnum.getId(), entityIdList);
    }
}
