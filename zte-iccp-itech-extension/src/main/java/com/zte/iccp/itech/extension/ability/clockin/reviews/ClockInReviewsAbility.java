package com.zte.iccp.itech.extension.ability.clockin.reviews;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.EmailAbility;
import com.zte.iccp.itech.extension.ability.ObjectLinkInstanceAbility;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.enums.LinkTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInReviewsNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.paas.lcap.common.api.metadata.engine.manage.model.ObjectInstanceLinkDO;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.TABLE_RELATION_RECTIFY_ASSIGNMENT_CID;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ASSIGNMENT_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInReviewsFieldConsts.REPLAY_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInReviewsFieldConsts.REVIEWS_NO;
import static com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum.CLOSE;
import static com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum.REVIEWS_RECTIFICATION;

/**
 * @author: 李江斌 10318434
 * @date: 2024/12/13
 */
public class ClockInReviewsAbility {

    /**
     * 查询打卡复盘单 - ID
     */
    public static ClockInReviews get(String reviewId, List<String> fields) {
        if (StringUtils.isEmpty(reviewId)) {
            return null;
        }

        return QueryDataHelper.get(ClockInReviews.class, fields, reviewId);
    }

    /**
     * 查询打卡复盘单 - ID
     */
    public static List<ClockInReviews> get(List<String> reviewIds) {
        if (CollectionUtils.isEmpty(reviewIds)) {
            return Lists.newArrayList();
        }

        return QueryDataHelper.get(ClockInReviews.class, Lists.newArrayList(), reviewIds);
    }

    /**
     * 查询打卡复盘单 - 操作单号（复盘单单号）
     * @param reviewsNo
     * @return ClockInReviews
     */
    public static ClockInReviews queryOneByReviewsNo(String reviewsNo) {
        if (StringUtils.isEmpty(reviewsNo)) {
            return null;
        }

        Filter codeFilter = new Filter(REVIEWS_NO, Comparator.EQ, reviewsNo);
        List<IFilter> conditionFilters = Lists.newArrayList(codeFilter);

        return QueryDataHelper.queryOne(ClockInReviews.class, Lists.newArrayList(), conditionFilters);
    }

    /**
     * 更新打卡复盘单
     */
    public static void update(ClockInReviews clockInReview) {
        if (null == clockInReview) {
            return;
        }
        SaveDataHelper.update(clockInReview);
    }

    public static void checkRectificationReviews() {
        // 1.查询整改中的打卡复盘单据
        List<IFilter> conditionFilters = new ArrayList<>();
        conditionFilters.add(new Filter(REPLAY_STATUS, Comparator.EQ, Lists.newArrayList(REVIEWS_RECTIFICATION.getValue())));
        List<ClockInReviews> clockInReviews = QueryDataHelper.query(
                ClockInReviews.class, Lists.newArrayList(), conditionFilters);
        List<String> clockInReviewsIds = clockInReviews.stream().map(ClockInReviews::getId).collect(Collectors.toList());

        // 2.查询关联表，找出关联的任务ids
        List<ObjectInstanceLinkDO> instances =
                ObjectLinkInstanceAbility.queryLinkInstance(LinkTypeEnum.BINDING, clockInReviewsIds);
        List<String> assignmentIds = instances.stream().map(ObjectInstanceLinkDO::getBillIdOut).collect(Collectors.toList());
        Map<String, List<String>> groupedByReviewsIdsMap = instances.stream()
                .collect(Collectors.groupingBy(
                        ObjectInstanceLinkDO::getBillIdIn,
                        Collectors.mapping(ObjectInstanceLinkDO::getBillIdOut, Collectors.toList())
                ));

        // 3.查询所有已废止、已关闭的关联任务
        List<IFilter> iFilters = Lists.newArrayList();
        iFilters.add(new Filter(ID, Comparator.IN, assignmentIds));
        List<String> status = Arrays.asList(
                AssignmentStatusEnum.ABOLISH.getValue(), CLOSE.getValue());
        iFilters.add(new Filter(ASSIGNMENT_STATUS, Comparator.IN, status));
        List<TechnologyManagementAssignment> assignments = QueryDataHelper.query(
                TechnologyManagementAssignment.class,
                Lists.newArrayList(ID),
                iFilters);
        List<String> allCloseAssignmentIds = assignments.stream().map(TechnologyManagementAssignment::getId).collect(Collectors.toList());

        // 4.判断是否都是“已废弃”或者“已关闭”状态
        List<String> closableReviewsIds = new ArrayList<>();
        groupedByReviewsIdsMap.forEach((clockInReviewsId, billOutAssignmentIds) -> {
            if (allCloseAssignmentIds.containsAll(billOutAssignmentIds)) {
                closableReviewsIds.add(clockInReviewsId);
            }
        });

        // 5.将这个可关闭的打卡复盘任务状态走到【已关闭】，流程也推到下一个节点，也就是已关闭节点
        List<ClockInReviews> closableClockInReviews = new ArrayList<>();
        for (String closableReviewsId : closableReviewsIds) {
            ClockInReviews reviews = new ClockInReviews();
            reviews.setId(closableReviewsId);
            reviews.setReplayStatus(CLOSE);
            closableClockInReviews.add(reviews);
            FlowHelper.pushSystemNode(closableReviewsId, FlowHelper.getCurrentNodeId(closableReviewsId));
            // 整改任务完成 - 给复盘单创建人发送一个邮件：【网络变更操作复盘通知/{单据编号}】{操作主题}（批次{批次号}），已进入{复盘状态}，请知悉！
            EmailAbility.sendClockInReviewInformMail(closableReviewsId,
                    ClockInReviewsNodeEnum.CLOCKIN_REVIEWS_SYSTEM.name(), ApproveFlowCodeEnum.CLOCK_IN_REVIEW_FLOW);
        }
        SaveDataHelper.batchUpdate(closableClockInReviews);
    }

    /**
     * 创建打卡复盘任务相关联系
     * @param formView
     * @param clockReviewsId
     * @param technologyAssignmentId
     */
    public static void createClockReviewsAssociateRelation(IFormView formView, String clockReviewsId, String technologyAssignmentId) {
        // 1.创建任务绑定关系
        ObjectLinkInstanceAbility.createBindingLinkInstance(Assignment.class, technologyAssignmentId, Lists.newArrayList(clockReviewsId));

        // 2.刷新任务列表
        refreshTable(formView, TABLE_RELATION_RECTIFY_ASSIGNMENT_CID);
    }

    /**
     * 刷新父页面列表
     * @param formView
     * @param tableId
     */
    private static void refreshTable(IFormView formView, String tableId) {
        IFormView parentView = formView.getParentView();
        IClientViewProxy parentViewProxy = parentView.getClientViewProxy();
        parentViewProxy.refreshData(parentView.getPageSessionId(), tableId, parentView.getPageId());
        formView.getClientViewProxy().sendParentViewCmd(parentViewProxy);
    }

    /**
     * 获取打卡复盘邮件抄送人
     *
     * @param clockInReviews clockInReviews
     * @return 邮件抄送人id
     */
    public static List<String> getEmailTos(ClockInReviews clockInReviews) {
        List<Employee> employeeList = Stream.of(
                        clockInReviews.getStEmail(),
                        clockInReviews.getArEmail(),
                        clockInReviews.getReEmail())
                //过滤掉为null的集合数据
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(employeeList)) {
            return Collections.emptyList();
        }

        return EmployeeHelper.getEpmUIID(employeeList);
    }
}
