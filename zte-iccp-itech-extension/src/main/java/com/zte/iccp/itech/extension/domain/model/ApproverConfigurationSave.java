package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.*;

@ApiModel("审核人保存类")
@Setter
@Getter
@BaseEntity.Info("Approver_Config")
public class ApproverConfigurationSave extends BaseEntity {

    @JsonProperty(value = APPROVAL_NODE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApprovalTypeEnum approvalNode;

    @JsonProperty(value = ORGANIZATION_REGION)
    private List<TextValuePair> organizationRegion;
}
