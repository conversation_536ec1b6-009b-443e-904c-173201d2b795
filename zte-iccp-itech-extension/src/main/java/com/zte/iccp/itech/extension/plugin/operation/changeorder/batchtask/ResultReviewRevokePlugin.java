package com.zte.iccp.itech.extension.plugin.operation.changeorder.batchtask;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ApprovalAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchApprNodeMappStatusEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteTransactionalEvent;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.ITECH_CLOUD_CUSTOM_BATCHTASK_REFRESH_BUTTON_CID;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_TWO;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.REFRESH;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.BILL_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * <AUTHOR>
 * @date 2025/8/1 下午5:33
 */
public class ResultReviewRevokePlugin extends BaseOperationPlugin {

    @Override
    public void afterExecuteTransactional(ExecuteTransactionalEvent executeEvent) {
        String batchId = (String) getModel().getRootDataEntity().getPkValue();
        if (StringUtils.isEmpty(batchId)) {
            return;
        }

        boolean isBatch = EntityHelper.getEntityId(BatchTask.class).equals(getModel().getMainEntityType().getKey());

        Class<? extends BaseEntity> batchTaskClass = isBatch ? BatchTask.class : SubcontractorBatchTask.class;
        IBatchTask iBatchTask = (IBatchTask) QueryDataHelper.get(batchTaskClass, Lists.newArrayList(), batchId);

        //校验是否能撤销
        AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(iBatchTask.getCurrentStatus());
        List<ApproveRecord> approveRecords = FlowHelper.getAfterSubmitApprovedRecords(batchId);
        ApproveRecord resultReviewRecord = approveRecords.stream().filter(item -> BatchApprNodeMappStatusEnum.RESULT_UNDER_REVIEW
                        == BatchApprNodeMappStatusEnum.fromValue(ApprovalAbility.getApproveNodeName(batchTaskClass, item.getExtendedCode())))
                .max(Comparator.comparing(ApproveRecord::getApprovalDate)).orElse(null);
        if (!BatchTaskAbility.isRevokeAccessible(resultReviewRecord, assignmentStatusEnum, iBatchTask.getResultReviewFlag())) {
            return;
        }

        List<? extends BaseEntity> batchTaskList = QueryDataHelper.query(batchTaskClass, Lists.newArrayList(ID),
                Lists.newArrayList(new Filter(CHANGE_ORDER_ID, com.zte.paas.lcap.core.orm.query.Comparator.EQ, iBatchTask.getChangeOrderId())));

        //更新所有批次步骤条
        Map<String, Object> values = Maps.newHashMap();
        batchTaskList.forEach(item -> {
            values.put(ID, item.getId());
            values.put(STEP, INTEGER_TWO);
        });
        SaveDataHelper.batchUpdate(batchTaskClass, Lists.newArrayList(values));

        //更新当前批次的批次状态及审批状态
        SaveDataHelper.update(batchTaskClass, batchId,
                MapUtils.newHashMap(CURRENT_STATUS, AssignmentStatusEnum.RESULT_UNDER_REVIEW.getValue(),
                        APPROVAL_STATUS, BoolEnum.Y));

        updateAssignment(iBatchTask, resultReviewRecord, batchId);


        //撤回到操作结果审核节点
        String flowCode = isBatch ? ApprovalConstants.BATCH_TASK_FLOW : ApprovalConstants.SUBCONTRACTOR_TASK_FLOW;
        FlowHelper.rollbackToNode(batchId, flowCode, resultReviewRecord.getNodeId(), null);

        getView().getClientViewProxy().setControlState(ITECH_CLOUD_CUSTOM_BATCHTASK_REFRESH_BUTTON_CID,
                MapUtils.newHashMap(REFRESH, true));
    }

    private static void updateAssignment(IBatchTask iBatchTask, ApproveRecord resultReviewRecord, String batchId) {
        //更新当前批次对应的任务信息、主变更单任务相关信息
        List<Assignment> assignments = QueryDataHelper.query(Assignment.class, Lists.newArrayList(),
                Lists.newArrayList(new Filter(BILL_ID, com.zte.paas.lcap.core.orm.query.Comparator.EQ, iBatchTask.getChangeOrderId())));

        Assignment mainAssignment = new Assignment();
        mainAssignment.setCurrentProgress(ApproveNodeEnum.BATCH_APPROVALING.name());
        mainAssignment.setAssignmentStatus(AssignmentStatusEnum.EXECUTE.getValue());

        List<Employee> currentBatchEmployee = HrClient.queryEmployeeInfo(Lists.newArrayList(resultReviewRecord.getApprover()));
        List<Employee> mainCurrentProcessorEmployee = Lists.newArrayList();
        mainCurrentProcessorEmployee.addAll(currentBatchEmployee);

        Assignment currentAssignment = new Assignment();
        List<Assignment> updateAssignments = Lists.newArrayList();
        assignments.forEach(item -> {
            if (iBatchTask.getChangeOrderId().equals(item.getEntityId())) {
                mainAssignment.setId(item.getId());
                return;
            }
            if (batchId.equals(item.getEntityId())) {
                BeanUtils.copyProperties(item, currentAssignment);
                Assignment updateAssignment = new Assignment();
                updateAssignment.setId(item.getId());
                updateAssignment.setCurrentProgress(resultReviewRecord.getExtendedCode());
                updateAssignment.setCurrentProcessorEmployee(currentBatchEmployee);
                updateAssignment.setApprovalTaskFlag(BoolEnum.Y);
                updateAssignments.add(updateAssignment);
                return;
            }
            mainCurrentProcessorEmployee.addAll(item.getCurrentProcessorEmployee());
        });

        mainAssignment.setCurrentProcessorEmployee(EmployeeHelper.uniqueEmployees(mainCurrentProcessorEmployee));
        updateAssignments.add(mainAssignment);

        AssignmentAbility.batchUpdate(updateAssignments);


        //记录日志
        OperationLogRecordAbility.saveResultReviewRevokeLog(currentAssignment);
    }
}
