package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.subentity.BaseOpAssocProdConfirmEntity;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR> 10284287
 * @since 2024/07/18
 */
@RequiredArgsConstructor
public class RelProdCountersignHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    private final ApproveRoleEnum role;

    private final Class<? extends BaseSubEntity> subEntity;

    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        Map<String, String> ret = MapUtils.newHashMap();

        QueryDataHelper.query(subEntity, Lists.newArrayList(), changeOrder.getId()).stream()
                .map(BaseOpAssocProdConfirmEntity.class::cast)
                .filter(p -> p.getApprover() != null || p.getApprovalTeam() != null)
                .filter(p -> p.getIsRelatedProdApproval() == BoolEnum.Y)
                .forEach(opAssocProd -> {
                    // 1.获取审核人+审核组的信息
                    List<String> persons = new ArrayList<>();
                    // 1.1 审核人配置不为空
                    persons.add(opAssocProd.getApprover().getEmpUIID());

                    if (!CollectionUtils.isEmpty(opAssocProd.getApprovalTeam())) {
                        persons.addAll(EmployeeHelper.getEpmUIID(opAssocProd.getApprovalTeam()));
                    }

                    ret.put(opAssocProd.getId(), persons.stream().distinct().collect(Collectors.joining(",")));
                });

        return Lists.newArrayList(JsonUtils.toJsonString(ret));
    }
}
