package com.zte.iccp.itech.extension.domain.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ChangeOrderExportVO {

    private String entityId;

    // ====================== 网络变更操作申请 =======================//
    @ApiModelProperty("任务单号")
    private String assignmentCode;

    @ApiModelProperty("任务名称 - 需设置超链接")
    private String assignmentName;

    @ApiModelProperty("属于GDPR管控项目")
    private String isGdpr;

    @ApiModelProperty("交付方式")
    private String deliveryMode;

    @ApiModelProperty("产品经营团队")
    private String productTeam;

    @ApiModelProperty("产品线")
    private String productLine;

    @ApiModelProperty("产品大类")
    private String productMainCategory;

    @ApiModelProperty("产品小类")
    private String productSubCategory;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("保障方式")
    private String guaranteeMode;

    @ApiModelProperty("是否需提供详细保障方案")
    private String isGuaranteeSolution;

    @ApiModelProperty("操作等级")
    private String operationLevel;

    @ApiModelProperty("操作封装")
    private String operationEncapsulation;

    @ApiModelProperty("操作原因")
    private String operationReason;

    @ApiModelProperty("计划操作开始时间")
    private String operationStartTime;

    @ApiModelProperty("计划操作结束时间")
    private String operationEndTime;

    @ApiModelProperty("网元数量")
    private Integer neCount;

    @ApiModelProperty("计划操作开始时间（北京时间）")
    private String operationStartTimeUtc8;

    @ApiModelProperty("计划操作结束时间（北京时间）")
    private String operationEndTimeUtc8;

    @ApiModelProperty("客户网络名称")
    private String customerNetworkName;

    @ApiModelProperty("局点名称")
    private String officeName;

    @ApiModelProperty("商用局")
    private String isCommercialOffice;

    @ApiModelProperty("有商务收费合同")
    private String isCommercialChargeContract;

    @ApiModelProperty("CSC请求单号")
    private String cscNo;

    @ApiModelProperty("重要程度")
    private String importance;

    @ApiModelProperty("风险评估")
    private String riskEvaluation;

    @ApiModelProperty("预计业务中断时长（分钟）")
    private String serviceDisconnectDuration;

    @ApiModelProperty("触发类型")
    private String triggerType;

    @ApiModelProperty("是否带业务操作")
    private String isBusinessOperation;

    @ApiModelProperty("是否是技术通知单实施")
    private String isTechnicalNotice;

    @ApiModelProperty("是否特殊场景")
    private String isSpecialScenario;

    @ApiModelProperty("客户特殊业务")
    private String customerSpecialService;

    @ApiModelProperty("是否有任务书")
    private String isTaskStatement;

    @ApiModelProperty("任务书是否超过一个月")
    private String taskStatementOverMonth;

    @ApiModelProperty("是否多模")
    private String isMultiMode;

    @ApiModelProperty("操作说明")
    private String operationDesc;

    @ApiModelProperty("国家/地区")
    private String country;

    @ApiModelProperty("省/州")
    private String province;

    @ApiModelProperty("地市")
    private String area;

    @ApiModelProperty("营销")
    private String marketing;

    @ApiModelProperty("片区")
    private String region;

    @ApiModelProperty("代表处")
    private String representativeOffice;

    @ApiModelProperty("是否政企")
    private String isGovernmentEnterprise;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户标识")
    private String accnType;

    @ApiModelProperty("代表处方案审核人")
    private String officeSolutionReviewer;

    @ApiModelProperty("附件说明")
    private String attachmentDesc;

    @ApiModelProperty("需要升级至技术交付部/网络处")
    private String isUpgradeTechnology;

    @ApiModelProperty("对技术交付部/网络处要求")
    private String upgradeTechnologyRequire;

    @ApiModelProperty("申请人")
    private String applyBy;

    @ApiModelProperty("申请时间")
    private String applyTime;

    @ApiModelProperty("是否紧急操作")
    private String isEmergencyOperation;

    @ApiModelProperty("是否封网、管控期操作")
    private String isNetCloseOrControlOperation;

    @ApiModelProperty("是否大区操作")
    private String isRegionalOperation;

    @ApiModelProperty("多产品联动保障")
    private String isProductLinkageGuarantee;

    @ApiModelProperty("产品用途BCN需要多产品联动保障")
    private String isProductLinkageGuaranteeBcn;

    @ApiModelProperty("需要网管授权文件")
    private String isAuthorizationFile;

    @ApiModelProperty("变更操作来源")
    private String changeOperationSource;

    @ApiModelProperty("是否涉及license文件加载")
    private String licenseLoad;

    @ApiModelProperty("工具落地状态")
    private String toolUse;

    @ApiModelProperty("工具名称")
    private String toolName;

    @ApiModelProperty("未使用工具原因")
    private String notUseToolReason;

    @ApiModelProperty("时区")
    private String timeZone;

    @ApiModelProperty("操作场景")
    private String operationScenario;

    @ApiModelProperty("获得客户扫描许可")
    private String isCustomerScanPermission;

    @ApiModelProperty("关联无线产品升级单")
    private String isAssociateWirelessUpgrade;

    @ApiModelProperty("无线产品升级单")
    private String wirelessUpgradeTicket;

    @ApiModelProperty("不关联原因")
    private String notAssociateReason;

    @ApiModelProperty("升级光模块")
    private String isUpgradingOptical;

    @ApiModelProperty("有第三方终端")
    private String isThirdPartyTerminal;

    @ApiModelProperty("影响ToB业务")
    private String isAffectToB;

    @ApiModelProperty("oVDC/NDVI项目")
    private String radiofieldOvdcNfviRdVerify;

    @ApiModelProperty("行政领导审核")
    private String isAdministrationLeaderApproval;

    @ApiModelProperty("是否首次应用")
    private String isFirstApplication;

    // ====================== 网络责任人审核 =======================//
    @ApiModelProperty("审核结果")
    private String approveResultNetOwner;

    @ApiModelProperty("审核意见")
    private String reviewCommentNetOwner;

    @ApiModelProperty("是否需要办事处产品经理审核")
    private String isReviewOfficeProdManager;

    @ApiModelProperty("审核人")
    private String approvedByNetOwner;

    @ApiModelProperty("审核时间")
    private String approvedTimeNetOwner;

    // ====================== 办事处产品经理审核 =======================//
    @ApiModelProperty("审核结果")
    private String approveResultOfficeProdManager;

    @ApiModelProperty("审核意见")
    private String reviewCommentOfficeProdManager;

    @ApiModelProperty("需要升级至网络处")
    private String isUpgradeNetDept;

    @ApiModelProperty("审核人")
    private String approvedByOfficeProdManager;

    @ApiModelProperty("审核时间")
    private String approvedTimeOfficeProdManager;

    // ====================== 核心网大区TD审核 =======================//
    @ApiModelProperty("审核结果")
    private String approveResultRegionalTdApp;

    @ApiModelProperty("审核意见")
    private String approveOpinionRegionalTdApp;

    @ApiModelProperty("审核人")
    private String approvedByRegionalTdApp;

    @ApiModelProperty("审核时间")
    private String approvedTimeRegionalTdApp;

    // ====================== 代表处TD审核 =======================//
    @ApiModelProperty("审核结果")
    private String approveResultTdApp;

    @ApiModelProperty("审核意见")
    private String approveOpinionTdApp;

    @ApiModelProperty("审核人")
    private String approvedByTdApp;

    @ApiModelProperty("审核时间")
    private String approvedTimeTdApp;

    // ====================== 技术交付部/网络处审核 =======================//
    @ApiModelProperty("审核结果")
    private String approveResultTdNetDeptApp;

    @ApiModelProperty("审核意见")
    private String approveOpinionTdNetDeptApp;

    @ApiModelProperty("需要领导审核")
    private String isTdNetOfficeLeaderApproval;

    @ApiModelProperty("技术交付部/网络处领导")
    private String tdNetOfficeLeaderApprovalTeam;

    @ApiModelProperty("需要升级至网络服务部")
    private String isNetDeptApproval;

    @ApiModelProperty("对网络服务部的要求")
    private String reqForNetServiceDept;

    @ApiModelProperty("是否远程中心执行")
    private String isRemoteCenterSupport;

    @ApiModelProperty("网络变更操作是否成熟")
    private String isNetworkChangeOperMature;

    @ApiModelProperty("需要升级到服务产品支持部（IT外购件支持）")
    private String isServProdSupportDeptApproval;

    @ApiModelProperty("需要升级到SSP支持组")
    private String isSspSupportTeamApproval;

    @ApiModelProperty("需要容量性能评估")
    private String isCapacityPerformanceEvaluation;

    @ApiModelProperty("审核人")
    private String approvedByTdNetDeptApp;

    @ApiModelProperty("审核时间")
    private String approvedTimeTdNetDeptApp;

    @ApiModelProperty("是否首次应用")
    private String isFirstApplicationNetDeptApp;

    // ====================== 网络服务部审核 =======================//
    @ApiModelProperty("审核结果")
    private String approveResultNetServiceDeptApp;

    @ApiModelProperty("此版本是否首次商用")
    private String isCommercialUseFirstTime;

    @ApiModelProperty("是否首次应用")
    private String isFirstApplicationNet;

    @ApiModelProperty("审核意见")
    private String approveOpinionNetServiceDept;

    @ApiModelProperty("需要升级至研发")
    private String isDevDeptApproval;

    @ApiModelProperty("对研发的要求")
    private String rdReq;

    @ApiModelProperty("早期局")
    private String isEarlySite;

    @ApiModelProperty("高风险版本")
    private String isHighRiskVersion;

    @ApiModelProperty("重大版本")
    private String isMajorVersion;

    @ApiModelProperty("承载专项保障")
    private String isDedicatedBearerGuarantee;

    @ApiModelProperty("方案审核投入人天")
    private String solutionReviewInputManday;

    @ApiModelProperty("中心管理干部挂钩项目")
    private String isProjectLinkedCenterManagCadre;

    @ApiModelProperty("需要升级至SSP支持组")
    private String isSspSupportTeamNet;

    @ApiModelProperty("操作方案由测试部提供")
    private String isOperPlanFromTestDept;

    @ApiModelProperty("操作方案需要验证测试")
    private String isOperPlanNeedTestVerify;

    @ApiModelProperty("审核人")
    private String approvedByNetServiceDept;

    @ApiModelProperty("审核时间")
    private String approvedTimeNetServiceDept;

    // ====================== 研发经理审核 =======================//
    @ApiModelProperty("审核结果")
    private String approveResultRdManager;

    @ApiModelProperty("审核意见")
    private String approveOpinionRdManager;

    @ApiModelProperty("操作验证测试")
    private String operTestVerifyRdManager;

    @ApiModelProperty("联合测试验证")
    private String jointVerifyTestRdManager;

    @ApiModelProperty("需要研发领导审核")
    private String isDevLeaderApproval;

    @ApiModelProperty("需要容量性能评估")
    private String capPerfEvalResultRdManager;

    @ApiModelProperty("测试验证结论")
    private String testVerifyConclusionRdManager;

    @ApiModelProperty("审核人")
    private String approvedByRdManager;

    @ApiModelProperty("审核时间")
    private String approvedTimeRdManager;

    // ====================== 研发领导审核 =======================//
    @ApiModelProperty("审核意见")
    private String approveOpinionRdLeader;

    @ApiModelProperty("审核结果")
    private String approveResultRdLeader;

    @ApiModelProperty("审核人")
    private String approvedByRdLeader;

    @ApiModelProperty("审核时间")
    private String approvedTimeRdLeader;

    // ====================== 操作计划确认与通告 =======================//
    @ApiModelProperty("确认操作开始时间")
    private String planOperationStartTime;

    @ApiModelProperty("确认操作结束时间")
    private String planOperationEndTime;

    @ApiModelProperty("通告发布人")
    private String announcer;

    @ApiModelProperty("通告发布时间")
    private String announcementReleaseTime;

    // ====================== 反馈操作结果 =======================//
    @ApiModelProperty("操作已回退")
    private String isReturned;

    @ApiModelProperty("计划操作/升级对象（局点/站点、OMC）全部完成")
    private String isAllCompleted;

    @ApiModelProperty("计划实现的需求全部达成（对已完成操作/升级的局点/站点等对象）")
    private String isAllReach;

    @ApiModelProperty("有无重大故障遗留")
    private String majorFaultsBehind;

    @ApiModelProperty("操作后（7天内）重要网络指标正常（无显著下降）")
    private String networkIndicators;

    @ApiModelProperty("是第一次操作/升级完成")
    private String isFirstOperation;

    @ApiModelProperty("操作结果")
    private String operationResult;

    @ApiModelProperty("实际操作开始时间")
    private String factOperationStartDate;

    @ApiModelProperty("实际操作结束时间")
    private String factOperationEndDate;

    @ApiModelProperty("多次操作原因")
    private String multipleOperationReason;

    @ApiModelProperty("失败原因分类")
    private String failureReason;

    @ApiModelProperty("操作手册齐套性")
    private String isOperationManual;

    @ApiModelProperty("操作文档指导性得分")
    private String operationDocScore;

    @ApiModelProperty("操作文档可获取时间")
    private String canOperationTime;

    @ApiModelProperty("版本及配套文档可获取时间")
    private String versionTime;

    @ApiModelProperty("操作实际投入人天")
    private String factInputDay;

    @ApiModelProperty("操作总结")
    private String operationSummary;

    @ApiModelProperty("操作人")
    private String operationPerson;

    @ApiModelProperty("操作时间")
    private String resultOperationDate;

    // ====================== 审核确认 =======================//
    @ApiModelProperty("多次操作原因")
    private String appMultipleOperationReason;

    @ApiModelProperty("失败原因分类")
    private String failureCategory;

    @ApiModelProperty("远程支持情况")
    private String remoteSupport;

    @ApiModelProperty("操作人员操作能力评估")
    private String operatorEvaluation;

    @ApiModelProperty("网络变更操作是否成熟")
    private String operationMature;

    @ApiModelProperty("操作实际投入人天")
    private String actualManDays;

    @ApiModelProperty("审核确认备注")
    private String auditRemark;

    @ApiModelProperty("审核确认人")
    private String reviewedBy;

    @ApiModelProperty("审核确认时间")
    private String reviewTime;

    @ApiModelProperty("复盘是否超期")
    private String isReviewOverdue;

    @ApiModelProperty("复盘天数")
    private String reviewDays;

    // ====================== 操作及支持人员列表 =======================//
    @ApiModelProperty("操作负责人 姓名")
    private String operationManagerName;

    @ApiModelProperty("操作负责人 归属部门")
    private String operationManagerDepartment;

    @ApiModelProperty("操作负责人 人员属性")
    private String operationManagerOperatorAttribute;

    @ApiModelProperty("操作人员 姓名")
    private String operatorName;

    @ApiModelProperty("操作人员 归属部门")
    private String operatorDepartment;

    @ApiModelProperty("操作人员 人员属性")
    private String operatorOperatorAttribute;

    @ApiModelProperty("值守人员 姓名")
    private String onSiteDutyPersonName;

    @ApiModelProperty("值守人员 归属部门")
    private String onSiteDutyPersonDepartment;

    @ApiModelProperty("值守人员 人员属性")
    private String onSiteDutyPersonOperatorAttribute;

    @ApiModelProperty("外包人员 姓名")
    private String outsourcingPersonName;

    @ApiModelProperty("外包人员 归属部门")
    private String outsourcingPersonDepartment;

    @ApiModelProperty("外包人员 人员属性")
    private String outsourcingPersonOperatorAttribute;

    @ApiModelProperty("现场支持 姓名")
    private String fieldSupportName;

    @ApiModelProperty("现场支持 归属部门")
    private String fieldSupportDepartment;

    @ApiModelProperty("现场支持 人员属性")
    private String fieldSupportOperatorAttribute;

    @ApiModelProperty("远程电话支持 姓名")
    private String remoteSupportName;

    @ApiModelProperty("远程电话支持 归属部门")
    private String remoteSupportDepartment;

    @ApiModelProperty("远程电话支持 人员属性")
    private String remoteSupportOperatorAttribute;

    @ApiModelProperty("辅助操作人员 姓名")
    private String assistantOperatorName;

    @ApiModelProperty("辅助操作人员 归属部门")
    private String assistantOperatorDepartment;

    @ApiModelProperty("辅助操作人员 人员属性")
    private String assistantOperatorOperatorAttribute;

    @ApiModelProperty("测试人员 姓名")
    private String testerName;

    @ApiModelProperty("测试人员 归属部门")
    private String testerDepartment;

    @ApiModelProperty("测试人员 人员属性")
    private String testerOperatorAttribute;

    @ApiModelProperty("交叉检查人员 姓名")
    private String crossCheckerName;

    @ApiModelProperty("交叉检查人员 归属部门")
    private String crossCheckerDepartment;

    @ApiModelProperty("交叉检查人员 人员属性")
    private String crossCheckerOperatorAttribute;

    @ApiModelProperty("远程操作负责人姓名")
    private String operationManagerNameRemote;

    @ApiModelProperty("远程操作负责人部门")
    private String operationManagerDepartmentRemote;

    @ApiModelProperty("远程操作负责人 人员属性")
    private String operationManagerOperatorAttributeRemote;

    @ApiModelProperty("远程操作人员姓名")
    private String operatorNameRemote;

    @ApiModelProperty("远程操作人员部门")
    private String operatorDepartmentRemote;

    @ApiModelProperty("远程操作人员 人员属性")
    private String operatorOperatorAttributeRemote;

    @ApiModelProperty("远程交叉检查人员姓名")
    private String crossCheckerNameRemote;

    @ApiModelProperty("远程交叉检查人员部门")
    private String crossCheckerDepartmentRemote;

    @ApiModelProperty("远程交叉检查人员 人员属性")
    private String crossCheckerOperatorAttributeRemote;

    @ApiModelProperty("远程值班人员姓名")
    private String onSiteDutyPersonNameRemote;

    @ApiModelProperty("远程值班人员部门")
    private String onSiteDutyPersonDepartmentRemote;

    @ApiModelProperty("远程值班人员 人员属性")
    private String onSiteDutyPersonOperatorAttributeRemote;

    @ApiModelProperty("远程外包人员姓名")
    private String outsourcingPersonNameRemote;

    @ApiModelProperty("远程外包人员部门")
    private String outsourcingPersonDepartmentRemote;

    @ApiModelProperty("远程外包人员 人员属性")
    private String outsourcingPersonOperatorAttributeRemote;

    @ApiModelProperty("远程测试人员姓名")
    private String testerNameRemote;

    @ApiModelProperty("远程测试人员部门")
    private String testerDepartmentRemote;

    @ApiModelProperty("远程测试人员 人员属性")
    private String testerOperatorAttributeRemote;

    @ApiModelProperty("远程中心支持姓名")
    private String supportNameRemoteCenter;

    @ApiModelProperty("远程中心支持部门")
    private String supportDepartmentRemoteCenter;

    @ApiModelProperty("远程中心支持 人员属性")
    private String supportOperatorAttributeRemoteCenter;

    // ====================== 局点信息 =======================//
    @ApiModelProperty("局点名称")
    private String officeNameBatch;

    @ApiModelProperty("现网版本")
    private String currentOnlineVersion;

    @ApiModelProperty("目标版本")
    private String targetVersion;

    @ApiModelProperty("操作对象数")
    private String operationObjectCount;

    // ====================== CCN Checklist =======================//
    @ApiModelProperty("操作时间点是否满足")
    private String operationTimeMet;

    @ApiModelProperty("操作时间不满足请提供说明及风险评估")
    private String operationTimeIssue;

    @ApiModelProperty("备板检查点是否满足")
    private String prepCheckMet;

    @ApiModelProperty("备板检查不满足请提供说明及风险评估")
    private String prepCheckIssue;

    // ====================== 其他信息 =======================//
    @ApiModelProperty("当前进展")
    private String currentProgress;

    @ApiModelProperty("当前处理人")
    private String currentProcessor;

    @ApiModelProperty("任务状态")
    private String assignmentStatus;

    @ApiModelProperty("操作开始时间")
    private String operationStartTimeForOther;

    @ApiModelProperty("操作结束时间")
    private String operationEndTimeForOther;

    @ApiModelProperty("操作起止时间")
    private String startAndEndTimeOfOperation;

    @ApiModelProperty("是否外籍独立操作")
    private String isForeignIndependentOperation;

    @ApiModelProperty("是否本地独立操作")
    private String isLocalIndependentOperation;
}