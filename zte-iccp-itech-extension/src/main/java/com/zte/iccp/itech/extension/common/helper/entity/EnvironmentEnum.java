package com.zte.iccp.itech.extension.common.helper.entity;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/09/30
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum EnvironmentEnum {
    /** 55 */
    TEST(true, "msrtest.zte.com.cn:10081"),
    /** 81 */
    UAT(true, "msruat.zte.com.cn:10081"),
    /** 南京 */
    NJ(false, "nj-msr.msp.zte.com.cn:10081"),
    /** 南非 */
    SA(false, "zamsr.zte.com.cn:10081"),
    /** 德国 */
    GE(false, "demsr.zte.com.cn:10081"),
    /** 巴西 */
    BR(false, "bamsr.zte.com.cn:10081"),
    ;

    private static EnvironmentEnum current;

    private final boolean isTest;

    private final String registerAddress;

    public static EnvironmentEnum current() {
        if (current != null) {
            return current;
        }

        String registerAddress = ConfigHelper.getRaw("register.address");
        for (EnvironmentEnum value : values()) {
            if (registerAddress.equals(value.getRegisterAddress())) {
                return current = value;
            }
        }

        throw new IllegalArgumentException();
    }
}
