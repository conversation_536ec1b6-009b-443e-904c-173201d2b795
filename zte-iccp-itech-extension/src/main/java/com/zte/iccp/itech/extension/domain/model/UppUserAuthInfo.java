package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/11/18 下午8:04
 */
@Getter
@Setter
public class UppUserAuthInfo implements Serializable {
    @ApiModelProperty("模块名称")
    @JsonProperty(value = "custom_9mn181ub")
    private String modelName;

    @ApiModelProperty("用户ID")
    @JsonProperty(value = "custom_srk4d74s")
    private String userNo;

    @ApiModelProperty("用户名称")
    @JsonProperty(value = "custom_6r0zvbwi")
    private String userName;

    @ApiModelProperty("用户组ID")
    private Long groupId;

    @ApiModelProperty("用户组")
    @JsonProperty(value = "custom_mmqz3epu")
    private String groupName;

    @ApiModelProperty("角色Code")
    private String roleCode;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("角色")
    @JsonProperty(value = "custom_l2ubh779")
    private String roleName;

    @ApiModelProperty("有效期")
    @JsonProperty(value = "custom_394ec17d")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty("状态")
    @JsonProperty(value = "custom_9hviu16f")
    private String isEffective;

    @ApiModelProperty("产品数据权限")
    @JsonProperty(value = "custom_p15hzkts")
    private String productPermissions;

    @ApiModelProperty("组织数据权限")
    @JsonProperty(value = "custom_a288valn")
    private String organizationPermissions;

    @ApiModelProperty("组织IDpath")
    private List<String> organizationIdpaths;

    @ApiModelProperty("产品IDpath")
    private List<String> productIdpaths;

    private String roleLastUpdateBy;

    private String roleLastUpdateDate;
}
