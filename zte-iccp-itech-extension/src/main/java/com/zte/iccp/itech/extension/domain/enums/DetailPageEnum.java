package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务中心详情页映射页面
 *
 * <AUTHOR> jiang<PERSON><PERSON>en
 * @date 2024/9/23
 */
@Getter
public enum DetailPageEnum {

    /**
     * 网络变更单页面:待启动、审批详情页面
     */
    NETWORK_CHANGE_PAGE(AssignmentTypeEnum.NETWORK_CHANGE,"PAGE1126520872430673921"),

    /**
     * 批次任务
     */
    NETWORK_CHANGE_BATCH(AssignmentTypeEnum.NETWORK_CHANGE_BATCH, "PAGE1093827357111214080"),

    /**
     * 合作方网络变更单页面:待启动、审批详情页面
     */
    SUBCONTRACTOR_NETWORK_CHANGE_PAGE(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE,"PAGE1012012457422307329"),

    /**
     * 合作方批次任务
     */
    SUBCONTRACT_NETWORK_CHANGE_BATCH(AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, "PAGE1100835855524651009"),

    /**
     * 技术管理任务详情页面
     */
    TECHNOLOGY_MANAGEMENT_PAGE(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT,"PAGE1004349069858136065"),

    /**
     * 技术管理子任务详情页面
     */
    TECHNOLOGY_MANAGEMENT_SUB_PAGE(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB,"PAGE1010949797683027969"),

    /**
     * 故障管理任务详情页
     */
    FAULT_MANAGEMENT_PAGE(AssignmentTypeEnum.FAULT_MANAGEMENT, "PAGE1022551814320730116"),

    /**
     * 打卡复盘任务详情页
     */
    CLOCK_REVIEW_PAGE(AssignmentTypeEnum.CLOCK_REVIEW, "PAGE1061237587929899009"),
    ;

    /**
     * 任务类型
     */
    private final AssignmentTypeEnum assignmentTypeEnum;

    /**
     * 页面
     */
    private final String pageId;

    /**
     * 有参构造
     * @param assignmentTypeEnum assignmentTypeEnum
     * @param pageId pageId
     */
    DetailPageEnum(AssignmentTypeEnum assignmentTypeEnum, String pageId) {
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.pageId = pageId;
    }

    /**
     * 根据类型匹配详情页面
     *
     * @param assignmentTypeEnum assignmentTypeEnum
     * @return DetailPageEnum
     */
    public static DetailPageEnum getPageIdByType(AssignmentTypeEnum assignmentTypeEnum){
        return Arrays.stream(DetailPageEnum.values()).filter(item -> assignmentTypeEnum == item.assignmentTypeEnum).findFirst().orElse(null);
    }
}
