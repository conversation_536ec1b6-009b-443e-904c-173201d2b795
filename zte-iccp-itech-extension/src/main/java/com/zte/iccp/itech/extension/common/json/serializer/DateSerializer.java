package com.zte.iccp.itech.extension.common.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORM;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/11
 */
public class DateSerializer extends JsonSerializer<Date> {
    private final SimpleDateFormat fmt = new SimpleDateFormat(DATE_FORM);

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeObject(fmt.format(value));
    }
}
