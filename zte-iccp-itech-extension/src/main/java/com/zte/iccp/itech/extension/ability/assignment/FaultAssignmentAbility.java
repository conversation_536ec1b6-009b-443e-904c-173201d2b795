package com.zte.iccp.itech.extension.ability.assignment;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.FaultManagementOrderAbility;
import com.zte.iccp.itech.extension.ability.ObjectLinkInstanceAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ApproverAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.*;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.LinkTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultProcessEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.PhaseStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.kafka.ServiceRequestMessage;
import com.zte.iccp.itech.extension.spi.client.CscClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.PdmClient;
import com.zte.iccp.itech.extension.spi.model.csc.dto.CscDetailQuery;
import com.zte.iccp.itech.extension.spi.model.csc.vo.BasicInfoVo;
import com.zte.iccp.itech.extension.spi.model.csc.vo.CscDetailInfoVo;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.pdm.dto.vo.ProductInfoByLevelVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.ProcessRecordVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomNodeVo;
import com.zte.iss.approval.sdk.bean.FlowStartDTO;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.paas.lcap.common.api.metadata.engine.manage.model.ObjectInstanceLinkDO;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.ddm.domain.flow.dto.RevokeFlowInstanceDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.VARIABLE_CSC_CODE;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

public class FaultAssignmentAbility {

    /** 合法 CSC 编码前缀 - CI - CSC 一线故障单 */
    private static final String LEGAL_CSC_CODE_PREFIX_CI = "CI";

    /** 合法 CSC 编码前缀 - EI - eSupport 一线故障单 */
    private static final String LEGAL_CSC_CODE_PREFIX_EI = "EI";

    /** 合法 CSC 编码前缀 - PI - SSP 一线故障单 */
    private static final String LEGAL_CSC_CODE_PREFIX_PI = "PI";

    /** 系统节点标识 - 复盘任务执行中 */
    private static final String FAULT_REVIEWING_NODE_KEY = "Activity_0gi38fo";

    /** 系统节点标识 - 整改横推任务执行中 */
    private static final String FAULT_BEING_RECTIFIED_NODE_KEY = "Activity_1hmekdd";

    /**
     * CSC 服务请求消息处理 - 创建 / 更新故障管理任务
     */
    public static void consumeServiceRequestMsg(ServiceRequestMessage message) {
        Assignment assignment = AssignmentAbility.queryByAssignmentCode(
                message.getRequestNo(),
                Lists.newArrayList(
                        CommonFieldConsts.ID, CommonFieldConsts.LAST_MODIFIED_BY,
                        AssignmentFieldConsts.ENTITY_ID, AssignmentFieldConsts.CURRENT_PROGRESS),
                Assignment.class);

        if (Objects.isNull(assignment)) {
            // 无故障管理任务 - 新建任务
            createFaultAssignment(message);
        } else if (checkFaultLevel(message.getSeverityCn())) {
            // 故障等级仍为关键 - 更新单据信息
            updateFaultOrder(assignment, message);
        } else {
            // 故障等级为 严重 / 一般 - 故障降级，任务关闭
            demotionCloseFaultAssignment(assignment);
        }
    }

    /**
     * 创建故障管理任务
     */
    private static void createFaultAssignment(ServiceRequestMessage message) {
        // 1.信息校验
        CreateFormatData formatData = checkRequestAndConvertFormatData(message);
        ContextHelper.setEmpNo(formatData.requestDetail.getBasicInfo().getCreatorIdCardAb());

        // 2.创建故障管理单
        FaultManagementOrder faultOrder = convertFaultOrder(formatData);
        String faultOrderId = createFaultExecuteFlow(faultOrder);

        // 3.创建故障管理任务
        FaultManagementAssignment faultAssignment
                = convertFaultAssignment(faultOrderId, message, formatData);
        String assignmentId = AssignmentAbility.insert(faultAssignment);

        // 4.故障管理任务关联人员
        List<String> relevant = faultAssignment.getCurrentProcessorEmployee().stream()
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
        AssignmentAbility.createAssignmentPersonRelevance(assignmentId, relevant);
    }

    /**
     * 校验创建信息 + 包装创建标准数据
     */
    private static CreateFormatData checkRequestAndConvertFormatData(ServiceRequestMessage message) {
        CreateFormatData formatData = new CreateFormatData();

        // 1.单据编号: 仅 CI / EI / PI 支持创建故障管理任务
        String requestCode = message.getRequestNo();
        if (!requestCode.startsWith(LEGAL_CSC_CODE_PREFIX_CI)
                && !requestCode.startsWith(LEGAL_CSC_CODE_PREFIX_EI)
                && !requestCode.startsWith(LEGAL_CSC_CODE_PREFIX_PI)) {
            throw new LcapBusiException("Invalid Code: " + requestCode);
        }

        // 2.检索 CSC 服务请求详情: 基础信息存在
        formatData.requestDetail = getServiceRequestDetail(requestCode);

        // 3.故障等级 - 关键一级 / 关键二级 / 关键三级
        if (!checkFaultLevel(message.getSeverityCn())) {
            throw new LcapBusiException("Invalid fault level: " + requestCode);
        }

        // 4.PDM 产品: 数据转换 + 校验
        checkAndTransferPdmProductInfo(formatData, message.getPdmNo(), requestCode);

        // 5.代表处: 数据转换 + 校验
        checkAndTransferOrganizationInfo(
                formatData,
                formatData.requestDetail.getSupportOrgInfo().getCurrSupportGroupHrDeptOrg(),
                requestCode);

        // 6.区域: 数据转换
        transferEmdmAreaInfo(formatData, message.getAreaNo());

        return formatData;
    }

    /**
     * 检索 CSC 服务请求详情
     */
    private static CscDetailInfoVo getServiceRequestDetail(String requestCode) {
        CscDetailQuery cscDetailQuery = new CscDetailQuery();
        cscDetailQuery.setRequestNo(requestCode);
        CscDetailInfoVo cscDetail = CscClient.getCscDetail(cscDetailQuery);

        if (Objects.isNull(cscDetail)
                || Objects.isNull(cscDetail.getBasicInfo())
                || Objects.isNull(cscDetail.getBasicInfo().getFaultInfo())
                || Objects.isNull(cscDetail.getSupportOrgInfo())) {
            throw new LcapBusiException("Null Service Request Detail: " + requestCode);
        }

        return cscDetail;
    }

    /**
     * 故障等级: 校验
     */
    private static boolean checkFaultLevel(String severityCn) {
        FaultLevelEnum faultLevel = FaultLevelEnum.fromZhCn(severityCn);
        return FaultLevelEnum.CRITICAL_FIRST.equals(faultLevel)
                || FaultLevelEnum.CRITICAL_SECOND.equals(faultLevel)
                || FaultLevelEnum.CRITICAL_THIRD.equals(faultLevel);
    }

    /**
     * PDM 产品: 转换 + 校验
     */
    private static void checkAndTransferPdmProductInfo(
            CreateFormatData formatData,
            String pdmProductNo,
            String requestCode) {

        if (!StringUtils.hasText(pdmProductNo)) {
            throw new LcapBusiException("Null PDM Product: " + requestCode);
        }

        List<ProductInfoByLevelVo> productModels = CacheUtils.get(
                CidConstants.PDM_PRODUCT_MODEL, new TypeReference<List<ProductInfoByLevelVo>>() {});
        if (!CollectionUtils.isEmpty(productModels)) {
            // 缓存中有产品型号数据，根据缓存中数据做转换
            setPdmProductByCache(formatData, pdmProductNo, productModels);
        } else {
            // 缓存中无产品型号数据，从 PDM 获取该数据
            setPdmProductBySystem(formatData, pdmProductNo);

            // 异步查询产品型号以设置缓存
            AsyncExecuteUtils.execute(() -> CacheUtils.set(
                    CidConstants.PDM_PRODUCT_MODEL,
                    ProductUtils.getPdmProductModels(),
                    30 * 24 * 60 * 60));
        }

        // 3.PDM 产品: 属于 NIS 产品经营团队范围
        if (!StringUtils.hasText(formatData.pdmProductTeam)) {
            throw new LcapBusiException("Invalid Product Team: " + requestCode);
        }
    }

    /**
     * 转换 PDM 信息 - 缓存
     */
    private static void setPdmProductByCache(
            CreateFormatData formatData,
            String pdmProductNo,
            List<ProductInfoByLevelVo> productModels) {

        // 1.获取产品型号
        Map<String, ProductInfoByLevelVo> productModelMap = productModels.stream()
                .collect(Collectors.toMap(ProductInfoByLevelVo::getItemNo, Function.identity()));
        ProductInfoByLevelVo productModel = productModelMap.get(pdmProductNo);
        if (Objects.isNull(productModel)) {
            return;
        }

        // 2.数据转换
        String fullPath = productModel.getFullPath();
        String[] productNos = fullPath.split(CommonConstants.FORWARD_SLASH);
        Map<String, String> teamLineIdMap = ProductUtils.getPdmProductTeamAndLineIdMap();

        formatData.pdmProductTeam = teamLineIdMap.get(productNos[ProdCateLvlConsts.TEAM - 1]);
        formatData.pdmProductLine = teamLineIdMap.get(productNos[ProdCateLvlConsts.LINE - 1]);
        formatData.pdmProductNo = pdmProductNo;
        formatData.pdmWholeProduct = fullPath;
    }

    /**
     * 转换 PDM 信息 - 外部系统查询
     */
    private static void setPdmProductBySystem(CreateFormatData formatData, String pdmProductNo) {
        // 1.检索 产品小类 + 产品大类 + 产品经营团队 + 产品线 编码
        String productSubclassNo = getProductParentNo(pdmProductNo);
        String productClassNo = getProductParentNo(productSubclassNo);
        String productLineNo = getProductParentNo(productClassNo);
        String productManageTeamNo = getProductParentNo(productLineNo);

        // 2.检索 NIS 获取 产品经营团队 + 产品线 编码
        Map<String, String> teamLineIdMap = ProductUtils.getPdmProductTeamAndLineIdMap();

        // 3.数据转换
        formatData.pdmProductLine = teamLineIdMap.get(productLineNo);
        formatData.pdmProductTeam = teamLineIdMap.get(productManageTeamNo);
        formatData.pdmProductNo = pdmProductNo;
        formatData.pdmWholeProduct = String.join(CommonConstants.FORWARD_SLASH,
                productManageTeamNo, productLineNo, productClassNo, productSubclassNo, pdmProductNo);
    }

    /**
     * 获取产品父级编码
     */
    private static String getProductParentNo(String productNo) {
        if (!StringUtils.hasText(productNo)) {
            return "";
        }

        List<ProductInfoByLevelVo> productInfo
                = PdmClient.queryProductInfo(Lists.newArrayList(productNo));
        return CollectionUtils.isEmpty(productInfo)
                ? ""
                : productInfo.get(0).getParentNo();
    }

    /**
     * 组织: 转换 + 校验
     */
    private static void checkAndTransferOrganizationInfo(
            CreateFormatData formatData,
            String organizationId,
            String requestCode) {

        if (!StringUtils.hasText(organizationId)) {
            throw new LcapBusiException("Null Organization: " + requestCode);
        }

        // 1.检索组织信息
        Map<String, BasicOrganizationInfo> organizationMap
                = HrClient.queryOrganizationInfo(Lists.newArrayList(organizationId));
        BasicOrganizationInfo organizationInfo = organizationMap.get(organizationId);
        if (Objects.isNull(organizationInfo)) {
            throw new LcapBusiException("Null Organization: " + requestCode);
        }

        // 2.代表处校验
        String fullPath = organizationInfo.getOrgIDPath();

        // (1) 非代表处组织, 不创建故障管理单及对应任务
        String[] wholePathArray = fullPath.split(CommonConstants.SPECIAL_HYPHEN);
        if (wholePathArray.length < 4) {
            throw new LcapBusiException("Invalid Organization: " + requestCode);
        }

        // (2) 代表处需属于国内政企 / 工程服务经营部
        String[] innerOrgPath = BusinessConsts.OFFICE_ORG_CODE_PATH;
        String[] marketingArray = innerOrgPath[0].split(CommonConstants.FORWARD_SLASH);
        String[] representativeOfficeArray = innerOrgPath[1].split(CommonConstants.FORWARD_SLASH);
        if (!marketingArray[1].equals(wholePathArray[1])
                && !representativeOfficeArray[1].equals(wholePathArray[1])) {
            throw new LcapBusiException("Invalid Organization: " + requestCode);
        }

        // 3.数据转换
        formatData.organization = wholePathArray[3];
        formatData.wholeOrganization = String.join(CommonConstants.FORWARD_SLASH, wholePathArray);
    }

    /**
     * EMDM 区域: 转换
     * EMDM 区域编码固定为 4 位，且国家 4 位在开头
     */
    private static void transferEmdmAreaInfo(CreateFormatData formatData, String areaCode) {
        if (!StringUtils.hasText(areaCode)
                || areaCode.length() < CommonConstants.INTEGER_FOUR) {
            return;
        }

        formatData.area = areaCode.substring(CommonConstants.INTEGER_ZERO, CommonConstants.INTEGER_FOUR);
    }

    /**
     * 包装 故障管理单 审批实体
     */
    private static FaultManagementOrder convertFaultOrder(CreateFormatData formatData) {

        BasicInfoVo basicInfo = formatData.requestDetail.getBasicInfo();
        FaultManagementOrder faultOrder = new FaultManagementOrder();

        // 1.基础属性
        // 任务单号 + 任务主题 + 客户ID + PDM 产品ID
        faultOrder.setTaskCode(basicInfo.getRequestNo());
        faultOrder.setTaskSubject(basicInfo.getSubject());
        faultOrder.setCustomerId(basicInfo.getClientNo());

        // 2.特殊属性
        // csc 任务状态
        PhaseStatusEnum cscStatusEnum = PhaseStatusEnum.fromValue(basicInfo.getPhaseStatusId());
        faultOrder.setCscTaskStatus(Objects.isNull(cscStatusEnum)
                ? Lists.newArrayList()
                : TextValuePairHelper.buildList(
                cscStatusEnum.getValue(),
                cscStatusEnum.getZhCn(),
                cscStatusEnum.getEnUs()));

        // 代表处
        faultOrder.setOrganization(
                TextValuePairHelper.buildList(Lists.newArrayList(formatData.organization)));

        // 产品
        faultOrder.setPdmProductId(formatData.pdmProductNo);
        faultOrder.setProductTeam(
                TextValuePairHelper.buildList(Lists.newArrayList(formatData.pdmProductTeam)));
        faultOrder.setProductLine(
                TextValuePairHelper.buildList(Lists.newArrayList(formatData.pdmProductLine)));

        // 区域
        faultOrder.setAreaCode(formatData.area);

        return faultOrder;
    }

    /**
     * 创建 故障管理任务 流程
     */
    private static String createFaultExecuteFlow(FaultManagementOrder faultOrder) {
        // 1.故障管理单入库
        String faultOrderId = SaveDataHelper.create(faultOrder);

        // 2.流程对象 - 基础参数
        FlowStartDTO flow = new FlowStartDTO();
        flow.setFlowCode(ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name());
        flow.setBusinessId(faultOrderId);
        flow.setAppId(ContextHelper.getAppId());
        flow.setAppCode(ContextHelper.getUacAppId());
        flow.setHandler(ContextHelper.getEmpNo());

        // 3.流程对象 - 自定义参数
        Map<String, Object> faultOrderInfo = JsonUtils.parseObject(faultOrder, Map.class);
        Map<String, Object> paramInfo = new HashMap<>();
        paramInfo.put(VARIABLE_CSC_CODE, faultOrder.getTaskCode());

        paramInfo.putAll(faultOrderInfo);
        flow.setParams(paramInfo);
        ApprovalFlowClient.start(flow);

        // 4.创建流程
        return faultOrderId;
    }

    /**
     * 包装 故障管理任务 实体对象
     */
    private static FaultManagementAssignment convertFaultAssignment(
            String faultOrderId,
            ServiceRequestMessage cscDetailMessage,
            CreateFormatData formatData) {

        BasicInfoVo basicInfo = formatData.requestDetail.getBasicInfo();
        FaultManagementAssignment faultAssignment = new FaultManagementAssignment();

        // 故障管理单属性
        faultAssignment.setBillId(faultOrderId);
        faultAssignment.setEntityId(faultOrderId);

        // 服务请求属性
        faultAssignment.setAssignmentName(basicInfo.getSubject());
        faultAssignment.setAssignmentCode(basicInfo.getRequestNo());
        faultAssignment.setCustomer(basicInfo.getClientNo());
        faultAssignment.setFaultLevel(
                FaultLevelEnum.getPropValue(cscDetailMessage.getSeverityCn()));
        faultAssignment.setOccurrenceTime(
                DateUtils.stringToDate(
                        basicInfo.getFaultInfo().getBreakdownDate(), CommonConstants.DATE_FORM));

        // 转换数据属性
        faultAssignment.setRegion(formatData.area);
        faultAssignment.setPdmProduct(formatData.pdmWholeProduct);
        faultAssignment.setProductManagementTeam(
                TextValuePairHelper.buildList(Lists.newArrayList(formatData.pdmProductTeam)));
        faultAssignment.setRepresentativeOffice(
                TextValuePairHelper.buildList(Lists.newArrayList(formatData.organization)));

        // 后台配置属性
        faultAssignment.setCurrentProcessorEmployee(
                ApproverAbility.getFaultManagerByWholePath(formatData.wholeOrganization));

        // 默认属性
        faultAssignment.setAssignmentType(AssignmentTypeEnum.FAULT_MANAGEMENT.getPropValue());
        faultAssignment.setBillType(BillTypeEnum.FAULT_MANAGEMENT.getPropValue());
        faultAssignment.setAssignmentStatus(AssignmentStatusEnum.EXECUTE.getValue());
        faultAssignment.setCurrentProgress(FaultProcessEnum.FAULT_REVIEW_CONFIRMING.name());
        faultAssignment.setApprovalTaskFlag(BoolEnum.N);

        return faultAssignment;
    }

    /**
     * 更新故障管理任务
     */
    private static void updateFaultOrder(
            Assignment assignment,
            ServiceRequestMessage message) {

        // 1.校验任务进展
        // 已关闭 / 降级关闭 则无需做任何处理
        String currentProgress = assignment.getCurrentProgress();
        if (FaultProcessEnum.CLOSED.name().equals(currentProgress)
                || FaultProcessEnum.DEMOTION_CLOSED.name().equals(currentProgress)) {
            return;
        }

        // 设置线程用户
        String userId = assignment.getLastModifiedBy();
        RequestContextHolder.setEmpNo(userId);

        CscDetailInfoVo requestDetail = getServiceRequestDetail(message.getRequestNo());
        String cscStatusId = requestDetail.getBasicInfo().getPhaseStatusId();
        List<TextValuePair> requestStatus = PhaseStatusEnum.getPropValue(cscStatusId);

        // 2.更新 故障管理单
        FaultManagementOrder updateOrder = new FaultManagementOrder();
        updateOrder.setId(assignment.getEntityId());
        updateOrder.setCscTaskStatus(requestStatus);
        FaultManagementOrderAbility.batchUpdate(Lists.newArrayList(updateOrder));
    }

    /**
     * 故障降级，任务关闭
     */
    private static void demotionCloseFaultAssignment(Assignment assignment) {
        // 1.校验任务进展
        // 已关闭 / 降级关闭 则无需做任何处理
        String currentProgress = assignment.getCurrentProgress();
        if (FaultProcessEnum.CLOSED.name().equals(currentProgress)
                || FaultProcessEnum.DEMOTION_CLOSED.name().equals(currentProgress)) {
            return;
        }

        // 2.更新 故障管理任务
        FaultManagementAssignment updateAssignment = new FaultManagementAssignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setAssignmentStatus(AssignmentStatusEnum.CLOSE.getValue());
        updateAssignment.setCurrentProgress(FaultProcessEnum.DEMOTION_CLOSED.name());
        updateAssignment.setCurrentProcessorEmployee(Lists.newArrayList());
        updateAssignment.setHistoryProgress(currentProgress);
        AssignmentAbility.update(updateAssignment);

        // 3.终止 故障管理任务 审批流
        FlowHelper.revokeFlow(assignment.getEntityId(), ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name());

        // 4.检索整改 / 横推任务
        demotionCloseTechnicalAssignments(assignment.getId());
    }

    /**
     * 降级关闭关联技术管理任务
     */
    private static void demotionCloseTechnicalAssignments(String faultAssignmentId) {
        // 1.检索关联关系
        List<ObjectInstanceLinkDO> linkInstances = ObjectLinkInstanceAbility.queryLinkInstance(
                LinkTypeEnum.BOUND, Lists.newArrayList(faultAssignmentId));
        if (CollectionUtils.isEmpty(linkInstances)) {
            return;
        }

        // 2.检索复盘 / 整改横推任务
        List<String> bindingIds = linkInstances.stream()
                .map(ObjectInstanceLinkDO::getBillIdOut)
                .distinct()
                .collect(Collectors.toList());
        List<Assignment> technicalAssignments = AssignmentAbility.get(
                bindingIds,
                Lists.newArrayList(CommonFieldConsts.ID, AssignmentFieldConsts.ASSIGNMENT_STATUS,
                        AssignmentFieldConsts.ENTITY_ID),
                Assignment.class);

        // 3.执行中 / 审批中 任务，进行废止，并终止审批流
        List<Assignment> approvingAssignments = technicalAssignments.stream()
                .filter(item -> AssignmentStatusEnum.EXECUTE.getValue().equals(item.getAssignmentStatus())
                        || AssignmentStatusEnum.APPROVE.getValue().equals(item.getAssignmentStatus()))
                .collect(Collectors.toList());
        abolishTechnicalAssignments(approvingAssignments);
    }

    /**
     * 废止关联技术管理任务
     */
    private static void abolishTechnicalAssignments(List<Assignment> assignments) {
        if (CollectionUtils.isEmpty(assignments)) {
            return;
        }

        List<Assignment> updateAssignments = Lists.newArrayList();
        List<String> abolishEntityIds = Lists.newArrayList();

        // 1.记录 主任务 待更新审批流 / 任务信息
        List<String> billIds = Lists.newArrayList();
        for (Assignment assignment : assignments) {
            String entityId = assignment.getEntityId();
            abolishEntityIds.add(entityId);
            billIds.add(entityId);

            Assignment updateAssignment = new Assignment();
            updateAssignment.setId(assignment.getId());
            updateAssignment.setAssignmentStatus(AssignmentStatusEnum.ABOLISH.getValue());
            updateAssignment.setCurrentProcessorEmployee(Lists.newArrayList());
            updateAssignments.add(updateAssignment);
        }

        // 2.检索 + 过滤待废止子任务
        List<Assignment> subTechnicalAssignments = AssignmentAbility.querySpecificTypeAssignment(
                billIds, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB, Assignment.class);
        List<Assignment> abolishSubAssignments = subTechnicalAssignments.stream()
                .filter(item -> AssignmentStatusEnum.EXECUTE.getValue().equals(item.getAssignmentStatus())
                        || AssignmentStatusEnum.APPROVE.getValue().equals(item.getAssignmentStatus()))
                .collect(Collectors.toList());
        for (Assignment subAssignment : abolishSubAssignments) {
            abolishEntityIds.add(subAssignment.getEntityId());

            Assignment updateAssignment = new Assignment();
            updateAssignment.setId(subAssignment.getId());
            updateAssignment.setAssignmentStatus(AssignmentStatusEnum.ABOLISH.getValue());
            updateAssignment.setCurrentProcessorEmployee(Lists.newArrayList());
            updateAssignments.add(updateAssignment);
        }

        // 3.废止任务流程
        // 废止意见统一使用中文
        RevokeFlowInstanceDTO mainRevokeInfo = new RevokeFlowInstanceDTO();
        mainRevokeInfo.setBusinessIdList(abolishEntityIds);
        mainRevokeInfo.setOpinion(FaultProcessEnum.DEMOTION_CLOSED.getZhCn());
        mainRevokeInfo.setTenantId(ClientConstants.DEFAULT_TENANT_ID);
        FlowServiceHelper.batchRevoke(mainRevokeInfo);

        // 4.更新任务
        AssignmentAbility.batchUpdate(updateAssignments);
    }

    /**
     * 故障复盘任务完成 执行步骤
     */
    public static void faultReviewAssignmentFinished(String reviewAssignmentId) {
        // 1.检索故障管理任务
        Assignment faultAssignment = getBindingFaultAssignment(reviewAssignmentId);
        if (Objects.isNull(faultAssignment)) {
            return;
        }

        // 2.审批流进入 确认整改横推 节点
        FlowHelper.pushSystemNode(faultAssignment.getEntityId(), FAULT_REVIEWING_NODE_KEY);

        // 3.更新 故障管理任务 当前进展
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(faultAssignment.getId());
        updateAssignment.setCurrentProgress(
                FaultProcessEnum.FAULT_RECTIFICATION_PROMOTION_CONFIRMING.name());
        AssignmentAbility.update(updateAssignment);
    }

    /**
     * 故障整改横推任务完成 执行步骤
     */
    public static void faultRectifyAssignmentFinished(String rectifyAssignmentId) {
        // 1.检索故障管理任务
        Assignment faultAssignment = getBindingFaultAssignment(rectifyAssignmentId);
        if (Objects.isNull(faultAssignment)) {
            return;
        }

        // 2.检索关联整改横推任务
        List<TechnologyManagementAssignment> bindingAssignments
                = AssignmentAbility.queryFaultTechAssignments(faultAssignment.getId(), Lists.newArrayList());
        List<TechnologyManagementAssignment> executeAssignments = bindingAssignments.stream()
                .filter(item -> !rectifyAssignmentId.equals(item.getId()))
                .filter(item -> !AssignmentStatusEnum.CLOSE.getValue().equals(item.getAssignmentStatus())
                        && !AssignmentStatusEnum.ABOLISH.getValue().equals(item.getAssignmentStatus()))
                .collect(Collectors.toList());

        // 仍有在途执行的任务，继续保持当前进展
        if (!CollectionUtils.isEmpty(executeAssignments)) {
            return;
        }

        // 无在途执行的任务，关闭任务
        FlowHelper.pushSystemNode(faultAssignment.getEntityId(), FAULT_BEING_RECTIFIED_NODE_KEY);

        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(faultAssignment.getId());
        updateAssignment.setAssignmentStatus(AssignmentStatusEnum.CLOSE.getValue());
        updateAssignment.setCurrentProgress(FaultProcessEnum.CLOSED.name());
        AssignmentAbility.update(updateAssignment);
    }

    /**
     * 检索关联故障管理任务
     */
    private static Assignment getBindingFaultAssignment(String technicalAssignmentId) {
        List<ObjectInstanceLinkDO> instances = ObjectLinkInstanceAbility.queryLinkInstance(
                LinkTypeEnum.BINDING, Lists.newArrayList(technicalAssignmentId));
        if (CollectionUtils.isEmpty(instances)) {
            return null;
        }

        String faultAssignmentId = instances.get(0).getBillIdOut();
        return AssignmentAbility.querySpecificTypeAssignment(
                faultAssignmentId,
                Lists.newArrayList(CommonFieldConsts.ID, AssignmentFieldConsts.ENTITY_ID),
                Assignment.class);
    }

    /**
     * 更新故障管理任务 WarRoom 地铁图节点时长
     */
    public static void updateFaultWarRoomTimeInfo(
            String faultOrderId,
            CscDetailInfoVo requestDetail,
            WarRoomNodeVo warRoomNodeInfo) {

        // 1.申告时间
        Date declareDate = DateUtils.stringToDate(
                requestDetail.getBasicInfo().getDeclareDate(),
                CommonConstants.DATE_FORM);

        // 2.检索故障管理任务
        Assignment assignment = AssignmentAbility.queryAssignment(
                faultOrderId,
                Lists.newArrayList(ID),
                Assignment.class);
        if (Objects.isNull(assignment)) {
            return;
        }

        // 3.任务更新
        FaultManagementAssignment updateAssignment = new FaultManagementAssignment();
        updateAssignment.setId(assignment.getId());

        // 故障响应时长: 首次响应-响应时长（分钟）
        updateAssignment.setFaultResponseTime(
                getProcessRecordDuration(
                        warRoomNodeInfo.getResponse(), ProcessRecordVo::getResponseDuration));

        // 人员到位时长: 现场人员到位-到位时长（分钟）
        updateAssignment.setPersonInPlaceTime(
                getProcessRecordDuration(
                        warRoomNodeInfo.getInPlace(), ProcessRecordVo::getInPlaceDuration));

        // 故障定位时长: 故障定位时间 - 故障申告时间
        updateAssignment.setFaultLocationTime(
                calculateProcessRecordDuration(
                        declareDate,
                        warRoomNodeInfo.getFaultLocating(),
                        ProcessRecordVo::getFaultLocatingResultFilledTime));

        // 故障恢复时长: 故障恢复时间 - 故障申告时间
        updateAssignment.setFaultRecoveryTime(
                calculateProcessRecordDuration(
                        declareDate,
                        warRoomNodeInfo.getBusinessRecover(),
                        ProcessRecordVo::getBusinessRecoverTime));

        AssignmentAbility.update(updateAssignment);
    }

    /**
     * 获取进展记录时长
     */
    private static Integer getProcessRecordDuration(
            List<ProcessRecordVo> processRecords,
            Function<ProcessRecordVo, Integer> getDuration) {

        if (CollectionUtils.isEmpty(processRecords)) {
            return null;
        }

        return processRecords.stream()
                .map(getDuration)
                .min(Integer::compare)
                .orElse(null);
    }

    /**
     * 计算进展记录时长
     */
    private static Integer calculateProcessRecordDuration(
            Date startDate,
            List<ProcessRecordVo> processRecords,
            Function<ProcessRecordVo, Date> getDateFunction) {

        if (CollectionUtils.isEmpty(processRecords)) {
            return null;
        }

        Date endDate = processRecords.stream()
                .map(getDateFunction)
                .min(Date::compareTo)
                .orElse(null);
        Long seconds = DateUtils.getBetween(startDate, endDate);
        return Objects.isNull(seconds)
                ? null
                : (int) (seconds / 60);
    }

    /**
     * 格式化数据 - 用于创建故障管理任务
     */
    private static class CreateFormatData {

        /** CSC 服务请求详情 */
        private CscDetailInfoVo requestDetail;

        /** PDM 产品编号 */
        private String pdmProductNo;

        /** PDM 产品编号全路径 */
        private String pdmWholeProduct;

        /** PDM 产品经营团队 */
        private String pdmProductTeam;

        /** PDM 产品线 */
        private String pdmProductLine;

        /** 代表处 */
        private String organization;

        /** 代表处全路径 */
        private String wholeOrganization;

        /** 区域 */
        private String area;
    }
}
