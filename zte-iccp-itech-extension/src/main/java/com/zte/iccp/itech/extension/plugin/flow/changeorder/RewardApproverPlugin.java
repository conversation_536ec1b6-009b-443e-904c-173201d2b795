package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.RewardApproverAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.platform.domain.i18n.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.DEFAULT_APPROVER_KEY;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.APPROVAL_FLOW_NOT_FOUND;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/4/28 19:35
 * zte-iccp-itech-netchange
 */
@Slf4j
public class RewardApproverPlugin extends BaseFlowPlugin {

    @Override
    public String[] getOperatorIds(FlowClient body) {
        //同时记录审批节点
        String nodeKey = body.getNodeElement().getNodeExtendName();
        List<String> approverList = Lists.newArrayList();
        ApproveFlowCodeEnum approveFlowEnum = null;
        try {
            approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(body.getFlowCode());
            if (null == approveFlowEnum) {
                throw new LcapBusiException(I18nUtil.getI18nEntryMsg(body.getAppId(), APPROVAL_FLOW_NOT_FOUND));
            }
            // 保持 1.审批人 2.审批组 顺序，去重不使用 HashSet，改用 ArrayList.stream
            approverList = RewardApproverAbility.getFlowApproverList(body, nodeKey, approveFlowEnum);
            approverList = approverList.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            if (CollectionUtils.isEmpty(approverList)) {
                approverList = ConfigHelper.getList(DEFAULT_APPROVER_KEY);
            }
            log.error("getOperatorIds fail:" ,e);
        }finally {
            AssignmentAbility.updateProgressByType(nodeKey, approverList, approveFlowEnum, body.getBusinessId());
        }
        return  approverList.toArray(new String[0]);
    }

}
