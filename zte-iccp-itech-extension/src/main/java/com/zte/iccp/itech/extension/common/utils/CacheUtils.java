package com.zte.iccp.itech.extension.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.paas.lcap.common.cache.IDistributeCache;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.common.spring.ApplicationContextHolder;
import com.zte.paas.lcap.ddm.common.cache.DynamicRedisCache;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024/12/05
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CacheUtils {
    private static final IDistributeCache CACHE = ApplicationContextHolder.getBean(DynamicRedisCache.class);

    public static <T> boolean set(String key, T value) {
        return CACHE.set(key, value);
    }

    public static <T> boolean set(String key, T value, long expireSeconds) {
        return CACHE.set(key, value, expireSeconds);
    }

    public static <T> T get(String key, Supplier<T> supplier, TypeReference<T> typeReference) {
        return get(key, supplier, 0, typeReference);
    }

    public static <T> T get(String key, Supplier<T> supplier, Class<T> clazz) {
        return get(key, supplier, 0, clazz);
    }

    public static String get(String key, Supplier<String> supplier) {
        return get(key, supplier, 0);
    }

    public static <T> T get(String key, Supplier<T> supplier, long expireSeconds, TypeReference<T> typeReference) {
        T value = get(key, typeReference);
        if (value != null) {
            return value;
        }

        value = supplier.get();
        if (expireSeconds == 0) {
            set(key, value);
        } else {
            set(key, value, expireSeconds);
        }

        return value;
    }

    public static <T> T get(String key, Supplier<T> supplier, long expireSeconds, Class<T> clazz) {
        T value = get(key, clazz);
        if (value != null) {
            return value;
        }

        value = supplier.get();
        if (expireSeconds == 0) {
            set(key, value);
        } else {
            set(key, value, expireSeconds);
        }

        return value;
    }

    public static String get(String key, Supplier<String> supplier, long expireSeconds) {
        String value = get(key);
        if (value != null) {
            return value;
        }

        value = supplier.get();
        if (expireSeconds == 0) {
            set(key, value);
        } else {
            set(key, value, expireSeconds);
        }

        return value;
    }

    public static <T> T get(String key, TypeReference<T> typeReference) {
        // 低代码41版本升级后，对平台redis缓存类进行了优化，需要在请求头中添加appId
        RequestContextHolder.setAppId(ContextHelper.getAppId());
        return JsonUtils.parseObject(CACHE.get(key), typeReference);
    }

    public static <T> T get(String key, Class<T> clazz) {
        // 低代码41版本升级后，对平台redis缓存类进行了优化，需要在请求头中添加appId
        RequestContextHolder.setAppId(ContextHelper.getAppId());
        T value = CACHE.get(key);
        return clazz.isInstance(value)
            ? value : JsonUtils.parseObject(value, clazz);
    }

    public static String get(String key) {
        // 低代码41版本升级后，对平台redis缓存类进行了优化，需要在请求头中添加appId
        RequestContextHolder.setAppId(ContextHelper.getAppId());
        return JsonUtils.toJsonString(CACHE.get(key));
    }

    public static void del(String key) {
        CACHE.del(key);
    }

    public static <T> boolean setIfAbsent(String key, T value, long time) {
        return CACHE.setIfAbsent(key, value, time);
    }
}
