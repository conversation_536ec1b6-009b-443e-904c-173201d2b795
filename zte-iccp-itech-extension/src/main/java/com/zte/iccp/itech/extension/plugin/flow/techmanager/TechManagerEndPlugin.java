package com.zte.iccp.itech.extension.plugin.flow.techmanager;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.assignment.FaultAssignmentAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.TaskCategoryEnum;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.spi.client.NetChangeClient;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.Date;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts.SOURCE;

public class TechManagerEndPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        // 1.人员积分任务 - 更新完成时间
        String source = (String) body.getVariables().get(SOURCE);
        if (DataSourceEnum.PERSONNEL.name().equals(source)) {
            NetChangeClient.updateTaskCompleteTime(body.getBusinessId(), new Date());
            return false;
        }

        // 检索技术管理任务
        String entityId = body.getBusinessId();
        TechnologyManagementAssignment assignment = AssignmentAbility.queryAssignment(
                entityId,
                Lists.newArrayList(
                        CommonFieldConsts.ID,
                        AssignmentFieldConsts.TechnologyManagementFieldConsts.TASK_CATEGORY),
                TechnologyManagementAssignment.class);
        if (Objects.isNull(assignment)) {
            return false;
        }

        // 2.故障复盘任务 处理步骤
        if (TaskCategoryEnum.FAULT_REVIEW.getValue().equals(assignment.getTaskCategory())) {
            FaultAssignmentAbility.faultReviewAssignmentFinished(assignment.getId());
            return false;
        }

        // 3.整改横推任务 处理步骤
        if (TaskCategoryEnum.FAULT_RECTIFY.getValue().equals(assignment.getTaskCategory())
                || TaskCategoryEnum.HORIZONTAL_PUSHING_TASK.getValue().equals(assignment.getTaskCategory())) {
            FaultAssignmentAbility.faultRectifyAssignmentFinished(assignment.getId());
            return false;
        }

        return false;
    }

}
