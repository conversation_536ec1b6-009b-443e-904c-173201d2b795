package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.enums.OperationLogTypeEnum;
import com.zte.iccp.itech.extension.domain.model.OperationLog;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/08/06
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OperationLogAbility {

    public static void addLog(OperationLogTypeEnum type, String targetId, String details) {
        SaveDataHelper.create(new OperationLog() {{
            setType(type);
            setTargetId(targetId);
            setDetails(details);
        }});
    }

    public static void addLog(OperationLogTypeEnum type, String targetId) {
        addLog(type, targetId, null);
    }

    public static void addLog(OperationLogTypeEnum type) {
        addLog(type, null, null);
    }
}