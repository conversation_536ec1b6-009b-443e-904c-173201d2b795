package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.HrHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.EnvironmentUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.common.utils.TimeRangeCalculatorUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.EmailConsts;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.ApprovalLogTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.entity.ApprovalTimeoutCallLog;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.spi.client.CcesClient;
import com.zte.iccp.itech.extension.spi.client.ICenterClient;
import com.zte.iccp.itech.extension.spi.model.cces.dto.CcesAiCallingReq;
import com.zte.iss.approval.sdk.bean.NodeApprover;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.metadata.engine.common.enums.LanguageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.SYSTEM_USER;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.INNER_OFFICE_ORG_CODE_PATH;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.ClockIn.TECHNICAL_APPROVAL_TIMEOUT_NOTIFICATION;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CURRENT_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.OPERATION_START_TIME;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.RESPONSIBLE_DEPT;
import static com.zte.iccp.itech.extension.domain.constant.entity.CustomizedNetworkSetConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME;

/**
 * 技术审批超时通知
 *
 * <AUTHOR>
 * @create 2024/10/15 下午1:47
 */
@Slf4j
public class ApprovalTimeOverAbility {

    /*
     * 超时时间4小时
     * */
    private static final int OVER_TIME_MINUTES = 240;


    public static void approvalTimeOutInfrom() {
        // 1.查询审批中的流程ID
        List<String> approvalIds = getApprovalIds();
        // 2.查询审批中节点
        List<NodeApprover> activeNodes = FlowHelper.queryActiveNodes(approvalIds);
        if (CollectionUtils.isEmpty(activeNodes)) {
            return;
        }
        //  过滤超过4小时，
        Map<String, Long> personMap = activeNodes.stream()
                .filter(v -> StringUtils.hasText(v.getApprover()) && !SYSTEM_USER.equals(v.getApprover()))
                .filter(v -> TimeRangeCalculatorUtils.calculateDuration(v.getCreateDate()) > OVER_TIME_MINUTES)
                .collect(Collectors.groupingBy(NodeApprover::getApprover, Collectors.counting()));

        // 消息通知
        callMessage(personMap);
    }

    /*
     * 查询审批中的任务ID
     * */
    private static List<String> getApprovalIds() {
        // 变更单  审批中+计划时间>当前时间
        List<IFilter> assignmentFilters = new ArrayList<>();
        assignmentFilters.add(new Filter(ASSIGNMENT_STATUS, Comparator.EQ, AssignmentStatusEnum.APPROVE.getValue()));
        assignmentFilters.add(new Filter(ASSIGNMENT_TYPE, Comparator.R_CONTAINS, Arrays.asList(AssignmentTypeEnum.NETWORK_CHANGE.getValue())));
        List<Assignment> assignments = QueryDataHelper.query(Assignment.class, Arrays.asList(ID, BILL_ID), assignmentFilters);

        List<String> approvalIds = new ArrayList<>();
        List<String> changeIds = assignments.stream().map(Assignment::getBillId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(changeIds)) {
            for (List<String> ids : Lists.partition(changeIds, CommonConstants.SQL_MAX_PAGE_SIZE)) {
                List<IFilter> changeFilters = new ArrayList<>();
                changeFilters.add(new Filter(ID, Comparator.IN, ids));
                changeFilters.add(new Filter(OPERATION_START_TIME, Comparator.GT, LocalDateTime.now()));
                List<ChangeOrder> changeOrders = QueryDataHelper.query(ChangeOrder.class, Arrays.asList(ID, RESPONSIBLE_DEPT), changeFilters);
                approvalIds.addAll(changeOrders.stream().filter(i -> i.getResponsibleDept() != null
                                && DeptTypeEnum.INNER == ChangeOrderAbility.getDeptType(i))
                        .map(ChangeOrder::getId)
                        .collect(Collectors.toList()));
            }
        }

        // 批次单  审批中+计划时间>当前时间
        List<IFilter> batchFilters = new ArrayList<>();
        batchFilters.add(new Filter(CURRENT_STATUS, Comparator.IN, Arrays.asList(AssignmentStatusEnum.NOTIFICATION_UNDER_REVIEW.getValue(),
                AssignmentStatusEnum.RESULT_UNDER_REVIEW.getValue())));
        batchFilters.add(new Filter(PLAN_OPERATION_START_TIME, Comparator.GT, LocalDateTime.now()));
        List<BatchTask> batchs = QueryDataHelper.query(BatchTask.class, Arrays.asList(ID, RESPONSIBLE_DEPT), batchFilters);
        if (CollectionUtils.isEmpty(batchs)) {
            return approvalIds;
        }

        approvalIds.addAll(batchs.stream()
                .filter(i -> StringUtils.hasText(i.getOrganizationId())
                        && Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).anyMatch(target -> i.getOrganizationId().startsWith(target)))
                .map(BatchTask::getId).collect(Collectors.toList()));
        return approvalIds;
    }

    /*
     * 发送icenter消息+远程呼叫
     * */
    private static void callMessage(Map<String, Long> personMap) {
        for (Map.Entry<String, Long> entry : personMap.entrySet()) {
            String person = entry.getKey();
            String messgae = MsgUtils.getLangMessage(LanguageEnum.ZH.getLanguage(),
                    TECHNICAL_APPROVAL_TIMEOUT_NOTIFICATION, entry.getValue());
            String telephone = "";
            try {
                CcesAiCallingReq ccesAiCallingReq = new CcesAiCallingReq();
                telephone = HrHelper.queryPhoneNum(person);
                ccesAiCallingReq.setTel(telephone);
                ccesAiCallingReq.setName("");
                ccesAiCallingReq.setContent(messgae);
                CcesClient.aiCallings(Arrays.asList(ccesAiCallingReq));
                ApprovalTimeoutCallLog log = new ApprovalTimeoutCallLog(person, CommonConstants.SUCCESS, messgae, ApprovalLogTypeEnum.SLA, telephone);
                SaveDataHelper.create(log);
            } catch (Exception e) {
                log.error("scheduler ApprovalTimeOverAbility.callMessage.call.send exception! person:{}，message e:{}", person, messgae, e);
                ApprovalTimeoutCallLog log = new ApprovalTimeoutCallLog(person, CommonConstants.FAIL, messgae, ApprovalLogTypeEnum.SLA, telephone);
                log.setErrorInfo(e.toString());
                SaveDataHelper.create(log);
            } finally {
                telephone = "";
            }
            // 发送icenter消息
            Map<String, Object> data = MapUtils.newHashMap(
                    EmailConsts.ICENTER_MESSAGE_INFO, messgae,
                    EmailConsts.ICENTER_INFORM_URL, EnvironmentUtils.getUniworkUrl());
            ICenterClient.sendICenter(TemplateIdEnum.APPROVAL_TIMEOUT_NOTIFICATION, Lists.newArrayList(person), data);
        }
    }
}
