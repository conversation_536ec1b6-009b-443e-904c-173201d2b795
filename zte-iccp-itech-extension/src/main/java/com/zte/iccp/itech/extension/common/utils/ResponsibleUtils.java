package com.zte.iccp.itech.extension.common.utils;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.INNER_OFFICE_ORG_CODE_PATH;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.NET_SERVICE_SALES;
import static com.zte.paas.lcap.metadata.engine.common.constant.NumberConst.THREE;
import static com.zte.paas.lcap.metadata.engine.common.constant.NumberConst.TWO;

/**
 * 代表处截取
 *
 * <AUTHOR>
 * @create 2024/7/17 上午9:56
 */
public class ResponsibleUtils {

    /*
     *根据代表处path获取营销单位
     * */
    public static String getSales(String responsible) {
        if (!StringUtils.hasText(responsible)) {
            return "";
        }
        String[] res = responsible.split(CommonConstants.FORWARD_SLASH);
        return String.join(CommonConstants.FORWARD_SLASH, res[0], res[1]);
    }

    /*
     * 根据代表处path获取片区
     * */
    public static String getRegion(String responsible) {
        if (!StringUtils.hasText(responsible)) {
            return "";
        }
        String[] res = responsible.split(CommonConstants.FORWARD_SLASH);
        return String.join(CommonConstants.FORWARD_SLASH, res[0], res[1], res[TWO]);
    }

    public static String getRegionName(String responsible) {
        return getIndex(responsible, THREE);
    }

    public static String getIndex(String responsible, int index) {
        if (!StringUtils.hasText(responsible)) {
            return "";
        }

        String[] res = responsible.split(CommonConstants.FORWARD_SLASH);
        if (res.length <= index) {
            return "";
        }

        return res[index];
    }

    /**
     * 根据代表处path获取代表处id
     * @param responsible
     * @return
     */
    public static String getResponsible(String responsible) {
        if (!StringUtils.hasText(responsible)) {
            return "";
        }
        String[] res = responsible.split(CommonConstants.FORWARD_SLASH);
        return res.length >= 4 ? res[3] : "";
    }

    /*
     * 国内和国际
     * */
    public static DeptTypeEnum getDeptType(String responsible) {
        if (!StringUtils.hasText(responsible)) {
            return DeptTypeEnum.OTHER;
        }
        if (Arrays.stream(INNER_OFFICE_ORG_CODE_PATH)
                .anyMatch(responsible::startsWith)) {
            return DeptTypeEnum.INNER;
        }

        return DeptTypeEnum.INTER;
    }

    /**
     * 网络服务处（DeptTypeEnum.NET_SERVICE）
     * 国内，但并非网络服务处（DeptTypeEnum.INNER_WITHOUT_NET_SERVICE）
     * 国际（DeptTypeEnum.INTER）
     */
    public static DeptTypeEnum getDeptTypeWithoutNetService(String responsible) {
        if (!StringUtils.hasText(responsible)) {
            return DeptTypeEnum.OTHER;
        }
        if (NET_SERVICE_SALES.equals(responsible)) {
            return DeptTypeEnum.NET_SERVICE;
        } else if (Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).anyMatch(responsible::startsWith)
                && !NET_SERVICE_SALES.equals(responsible)) {
            return DeptTypeEnum.INNER_WITHOUT_NET_SERVICE;
        }
        return DeptTypeEnum.INTER;
    }

    /**
     * 获取组织ID - 最小层级
     */
    public static String minLevelId(String organizationId) {
        if (!StringUtils.hasText(organizationId)) {
            return "";
        }

        String[] ids = organizationId.split(CommonConstants.FORWARD_SLASH);
        return ids[ids.length - 1];
    }

    /**
     * 获取组织ID - 指定层级
     */
    public static String specificLevelId(String organizationId, Integer treeLevel) {
        if (!StringUtils.hasText(organizationId)) {
            return "";
        }

        String[] ids = organizationId.split(CommonConstants.FORWARD_SLASH);
        if (ids.length < treeLevel) {
            return "";
        }

        return ids[treeLevel - 1];
    }

    /**
     * 组织查询条件 - 可根据父级获取子级
     */
    public static Filter organizationFilter(
            IDataModel dataModel,
            String organizationIdCid,
            String organizationIdField,
            boolean onlyLeaf,
            Function<OrganizationTreeVo, String> propertyGetFunction) {

        // 1.获取 组织 组件填写数据
        List<TextValuePair> organizationInfo
                = ComponentUtils.getChooseComponentInfo(dataModel, organizationIdCid);
        if (CollectionUtils.isEmpty(organizationInfo)) {
            return null;
        }

        // 2.检索组织树
        List<OrganizationTreeVo> treeData = NisClient.queryOrganizationTree();

        // 3.获取组织填写数据对应子节点路径
        Set<String> leafPaths = new HashSet<>();
        organizationInfo.forEach(item ->
                leafPaths.addAll(getOrganizationLeafNodePath(
                        treeData, item.getValue(), onlyLeaf, propertyGetFunction)));

        // 4.包装过滤条件
        return new Filter(organizationIdField, Comparator.IN, new ArrayList<>(leafPaths));
    }

    /**
     * 获取组织叶子节点全路径 - 定位当前节点
     */
    public static List<String> getOrganizationLeafNodePath(
            List<OrganizationTreeVo> treeData,
            String organizationPath,
            boolean onlyLeaf,
            Function<OrganizationTreeVo, String> propertyGetFunction) {

        for (OrganizationTreeVo treeNode : treeData) {
            String nodeFullPath = treeNode.getOrgIdPath();

            if (organizationPath.equals(nodeFullPath)) {
                // 1.找到当前节点，向下获取子节点
                List<String> paths = Lists.newArrayList(organizationPath);
                paths.addAll(getOrganizationLeafNodePath(treeNode, onlyLeaf, propertyGetFunction));
                return paths;
            } else if (organizationPath.startsWith(nodeFullPath)) {
                // 2.找到节点的父节点，继续查找当前节点
                return getOrganizationLeafNodePath(
                        treeNode.getChild(), organizationPath, onlyLeaf, propertyGetFunction);
            }
        }

        return Lists.newArrayList();
    }

    /**
     * 获取组织叶子节点全路径 - 检索路径
     */
    private static List<String> getOrganizationLeafNodePath(
            OrganizationTreeVo treeNode,
            boolean onlyLeaf,
            Function<OrganizationTreeVo, String> propertyGetFunction) {

        List<OrganizationTreeVo> childrenNodes = treeNode.getChild();

        List<String> leafPath = Lists.newArrayList();
        if (CollectionUtils.isEmpty(childrenNodes)) {
            // 1.叶子节点返回全路径
            leafPath.add(propertyGetFunction.apply(treeNode));
        } else {
            // 2.非叶子节点, 查叶子节点全路径
            if (!onlyLeaf) {
                leafPath.add(propertyGetFunction.apply(treeNode));
            }
            childrenNodes.forEach(item -> leafPath.addAll(
                    getOrganizationLeafNodePath(item, onlyLeaf, propertyGetFunction)));
        }

        return leafPath;
    }

    public static String getResponsibleName(String namePath) {
        if (!StringUtils.hasText(namePath)) {
            return "";
        }
        String[] res = namePath.split(CommonConstants.FORWARD_SLASH);
        return res[0];
    }
}
