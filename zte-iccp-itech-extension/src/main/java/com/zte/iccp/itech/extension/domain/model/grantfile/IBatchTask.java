package com.zte.iccp.itech.extension.domain.model.grantfile;

import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInStateEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiAttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> ********
 * @since 2024/07/23
 */
public interface IBatchTask {
    String getId();

    void setId(String id);

    String getChangeOrderId();

    String getBatchNo();

    String getBatchCode();

    String getBatchName();

    String getCurrentStatus();

    String getBatchOperationAccount();

    List<MultiAttachmentFile> getCustomerVoucherFile();

    Date getPlanOperationStartTime();

    Date getPlanOperationEndTime();

    String getOperationDescription();

    String getOperationPlanDesc();

    String getGrantFile();

    void setGrantFile(String grantFile);

    String getSaGrantFile();

    void setSaGrantFile(String saFileKey);

    ClockInStateEnum getClockInState();

    void setClockInState(ClockInStateEnum clockInState);

    BoolEnum getIsCanceled();

    void setIsCanceled(BoolEnum isCanceled);

    BoolEnum getIsRollbackDone();

    void setIsRollbackDone(BoolEnum isRollbackDone);

    BoolEnum getIsOneTimeSucceed();

    void setIsOneTimeSucceed(BoolEnum isOneTimeSucceed);

    Date getActualOperationStartTime();

    void setActualOperationStartTime(Date actualOperationStartTime);

    Date getActualOperationEndTime();

    void setActualOperationEndTime(Date actualOperationEndTime);

    Date getTestFinishTime();

    void setTestFinishTime(Date testFinishTime);

    List<Employee> getResultMail();

    void setResultMail(List<Employee> resultMails);

    List<Employee> getEmailCc();

    List<Employee> getApprovalEmail();

    void setApprovalEmail(List<Employee> approvalEmails);

    String getSource();

    String getNoticeCopy();

    BoolEnum getUrgentFlag();

    /** 获取步骤条数值 */
    Integer getStep();

    /** 操作计划变更说明-中文 */
    String getChangePlanDescZh();

    /** 操作计划变更说明-英文 */
    String getChangePlanDescUs();

    /** 获取驳回时节点自定义编码*/
    String getRejectNode();

    /** 获取驳回结果 */
    ApproveResultEnum getResultRejection();

    /** 驳回时审核意见 */
    String getOpinionRejection();

    /** 驳回时审核人 */
    List<Employee> getPersonRejection();

    /** 通告说明 */
    String getNotificationDesc();

    /** 审批状态 */
    BoolEnum getApprovalStatus();

    /** 驳回附件 */
    Object getAttachmentRejection();

    void setResultRejection(ApproveResultEnum approveResultEnum);

    /** 获取审批次数 */
    Long getApprovalNum();

    /** 网络变更单 - 责任单位 */
    String getOrganizationId();

    /** 邮件抄送（操作取消审核） */
    List<Employee> getOcEmailCc();

    /** 操作结果 */
    List<TextValuePair> getOperationResult();

    String getCreateBy();

    /** 审核结果（操作取消审核） */
    List<TextValuePair> getOcApproveResult();

    /** 操作变更说明（操作取消审核） */
    String getOcOperationChangeDesc();

    void setStep(Integer step);

    /** 操作结果审核标识 */
    String getResultReviewFlag();
}
