package com.zte.iccp.itech.extension.domain.constant;

public class ApproverConfigConstants {

    // Excel解析相关常量
    public static final int HEADER_ROW_INDEX = 2;
    public static final int DATA_START_ROW_INDEX = 3;
    public static final int BATCH_SIZE = 500;
    public static final int HEADER_ROW_NUMBER = 0;

    // UI显示文本
    public static final String DIALOG_TITLE = "导入审批人配置";
    public static final String CONFIRM_BUTTON_TITLE = "确认";
    public static final String CANCEL_BUTTON_TITLE = "取消";

    // 消息提示
    public static final String MSG_FILE_GET_FAILED = "获取文件失败";
    public static final String MSG_FILE_EMPTY = "文件内容为空";
    public static final String PLEASE_WAITING = "正在解析文件，请稍等...";
    public static final String MSG_IMPORT_START = "开始导入数据...";
    public static final String MSG_IMPORT_FAILED = "导入失败：";

    // 错误消息
    public static final String ERROR_INVALID_FIELD_MAPPING = "无效字段映射: ";
    public static final String ERROR_ROW_PREFIX = "第";
    public static final String ERROR_ROW_SUFFIX = "行错误: ";
}
