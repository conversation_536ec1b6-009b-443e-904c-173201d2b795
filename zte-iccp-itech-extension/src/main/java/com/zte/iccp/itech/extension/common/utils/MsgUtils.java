package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.domain.constant.RedisKeys;
import com.zte.paas.lcap.platform.domain.i18n.I18nUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/24
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MsgUtils {

    private static final long MSG_CACHE_EXPIRE_SECONDS = 30 * 60;

    public static String getLangMessage(String langId, String msgId, Object... args) {
        String origLangId = ContextHelper.getLangId();
        try {
            ContextHelper.setLangId(langId);
            return getMessage(msgId, args);
        } finally {
            ContextHelper.setLangId(origLangId);
        }
    }

    /*
     * [type]:msgId 格式信息
     * */
    public static String getTypedMessage(String type, String msgId, Object... args) {
        return String.format("[%s]:%s", type, getMessage(msgId, args));
    }

    public static String getMessage(String msgId, Object... args) {
        emptyHandler(args);

        String key = RedisKeys.msg(ContextHelper.getLangId(), msgId);
        String msg = CacheUtils.get(
                key,
                () -> {
                    String message = I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), msgId);
                    return message.replace("\\n", "\n");
                },
                MSG_CACHE_EXPIRE_SECONDS);
        return args.length == 0
                ? msg : MessageFormat.format(msg, args);
    }

    /**
     * 空参处理
     *
     * @param args args
     */
    private static void emptyHandler(Object... args) {
        for (int i = 0; i < args.length; i++) {
            if (args[i] == null) {
                args[i] = EMPTY_STRING;
            }
        }
    }
}