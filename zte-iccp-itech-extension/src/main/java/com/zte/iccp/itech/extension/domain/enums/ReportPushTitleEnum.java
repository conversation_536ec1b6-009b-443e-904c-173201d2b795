package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/17 下午2:36
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ReportPushTitleEnum {
    /**
     * 中国移动
     */
    CM("中国移动"),

    /**
     * 中国电信
     */
    CT( "中国电信"),

    /**
     * 中国联通
     */
    CU("中国联通"),

    /**
     * 中国广电
     */
    CBN( "中国广电"),

    /**
     * 其他
     */
    OTHER("其他"),

    /**
     * 关键
     */
    CRITICAL( "关键"),

    /**
     * 重要
     */
    IMPORTANT( "重要"),

    /**
     * 一般
     */
    NORMAL( "一般"),

    /**
     * 六星
     */
    SIX_STAR("六星"),

    /**
     * 五星
     */
    FIVE_STAR("五星"),

    /**
     * 四星
     */
    FOUR_STAR( "四星"),

    /**
     * 三星
     */
    THREE_STAR("三星"),

    /**
     * 二星
     */
    TWO_STAR("二星"),

    /**
     * 一星
     */
    ONE_STAR("一星"),

    /**
     * 单
     */
    ORDER("单"),
    /**
     * 合计
     */
    SUM_COUNT("合计"),

    ;

    private final String name;

}
