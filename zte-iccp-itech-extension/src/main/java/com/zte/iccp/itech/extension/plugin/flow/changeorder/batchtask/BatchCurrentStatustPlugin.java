package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CURRENT_STATUS;

/**
 * 批次任务，审核通过不通过，刷新批次审批状态
 *
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class BatchCurrentStatustPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String batchId = body.getBusinessId();
        String currentStatus = (String) body.getVariables().get(CURRENT_STATUS);
        String flowCode = body.getFlowCode();
        if (ApprovalConstants.BATCH_TASK_FLOW.equals(flowCode)) {
            BatchTaskAbility.updateCurrentStatus(batchId, AssignmentStatusEnum.fromValue(currentStatus));
        } else {
            Map<String, Object> values = Maps.newHashMap();
            values.put(CURRENT_STATUS, currentStatus);
            SaveDataHelper.update(SubcontractorBatchTask.class, batchId, values);
        }

        return false;
    }
}
