package com.zte.iccp.itech.extension.ability.common.export;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.export.DataRangeEnum;
import com.zte.iccp.itech.extension.domain.enums.export.ExportFieldEnum;
import com.zte.iccp.itech.extension.domain.enums.export.ExportFieldGroupEnum;
import com.zte.iccp.itech.extension.domain.model.export.ExportTreeNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.FORWARD_SLASH;
import static com.zte.iccp.itech.extension.domain.constant.ShuttleBoxDataConstants.*;

/**
 * 导出模板字段能力类
 * 用于构建导出字段树结构
 */
@Slf4j
public class ShuttleBoxDataLoadAbility {

    /**
     * 构建导出字段树结构
     *
     * @param dataRange 数据范围
     * @return 导出字段树结构的JSON数组
     */
    public static JSONArray buildExportFieldsTreeInternal(DataRangeEnum dataRange) {
        List<ExportTreeNode> treeNodes = buildExportFieldsTree(dataRange);
        return convertToJsonArray(treeNodes);
    }

    /**
     * 构建导出字段树结构
     *
     * @param dataRange 数据范围
     * @return 导出字段树结构的领域对象列表
     */
    public static List<ExportTreeNode> buildExportFieldsTree(DataRangeEnum dataRange) {
        List<ExportTreeNode> treeData = new ArrayList<>();

        // 预先计算所有分组的国际化名称，避免重复调用
        Map<ExportFieldGroupEnum, String> groupNameMap = new HashMap<>(ExportFieldGroupEnum.values().length);
        for (ExportFieldGroupEnum group : ExportFieldGroupEnum.values()) {
            groupNameMap.put(group, MsgUtils.getMessage(group.getDisplayName()));
        }

        // 预先计算所有字段的显示名称，避免重复调用
        Map<ExportFieldEnum, String> fieldDisplayNameMap = new HashMap<>(ExportFieldEnum.values().length);
        for (ExportFieldEnum field : ExportFieldEnum.values()) {
            fieldDisplayNameMap.put(field, field.getDisplayName());
        }

        // 使用更高效的方式对字段进行分组
        Map<ExportFieldGroupEnum, List<ExportFieldEnum>> groupedFields = new EnumMap<>(ExportFieldGroupEnum.class);
        for (ExportFieldGroupEnum group : ExportFieldGroupEnum.values()) {
            groupedFields.put(group, new ArrayList<>());
        }

        // 根据数据范围过滤字段
        for (ExportFieldEnum field : ExportFieldEnum.values()) {
            if (isFieldInDataRange(field, dataRange)) {
                groupedFields.get(field.getGroup()).add(field);
            }
        }

        // 为每个分组创建一个树节点
        for (Map.Entry<ExportFieldGroupEnum, List<ExportFieldEnum>> entry : groupedFields.entrySet()) {
            ExportFieldGroupEnum group = entry.getKey();
            List<ExportFieldEnum> fields = entry.getValue();

            if (fields.isEmpty()) {
                continue;
            }

            String groupName = groupNameMap.get(group);

            ExportTreeNode groupNode = ExportTreeNode.builder()
                    .id(group.name())
                    .code(group.name())
                    .level(CommonConstants.STR_ONE)
                    .status(STANDARD)
                    .nameCn(groupName)
                    .nameEn(groupName)
                    .namePathCn(groupName)
                    .namePathEn(groupName)
                    .isSelect(false)
                    .children(new ArrayList<>(fields.size()))
                    .build();

            // 为分组下的每个字段创建一个子节点
            for (ExportFieldEnum field : fields) {
                String fieldDisplayName = fieldDisplayNameMap.get(field);

                ExportTreeNode fieldNode = ExportTreeNode.builder()
                        .id(field.name())
                        .code(field.name())
                        .value(field.name())
                        .level(CommonConstants.STR_TWO)
                        .nameCn(fieldDisplayName)
                        .nameEn(fieldDisplayName)
                        .namePathCn(groupName + FORWARD_SLASH + fieldDisplayName)
                        .namePathEn(groupName + FORWARD_SLASH + fieldDisplayName)
                        .isSelect(true)
                        .children(new ArrayList<>(0))
                        .build();

                groupNode.getChildren().add(fieldNode);
            }

            treeData.add(groupNode);
        }
        return treeData;
    }

    /**
     * 判断字段是否属于指定数据范围
     * 根据数据范围过滤字段
     *
     * @param field 字段
     * @param dataRange 数据范围
     * @return 是否属于该数据范围
     */
    private static boolean isFieldInDataRange(ExportFieldEnum field, DataRangeEnum dataRange) {
        if (field == null || dataRange == null) {
            return false;
        }
        return field.isInDataRange(dataRange);
    }

    /**
     * 对象列表转换为JSON数组
     *
     * @param treeNodes 领域对象列表
     * @return JSON数组
     */
    private static JSONArray convertToJsonArray(List<ExportTreeNode> treeNodes) {
        JSONArray jsonArray = new JSONArray(treeNodes.size());

        for (ExportTreeNode node : treeNodes) {
            JSONObject jsonNode = new JSONObject();
            jsonNode.put(ID, node.getId());
            jsonNode.put(CODE, node.getCode());
            if (node.getValue() != null) {
                jsonNode.put(VALUE, node.getValue());
            }
            jsonNode.put(LEVEL, node.getLevel());
            if (node.getStatus() != null) {
                jsonNode.put(STATUS, node.getStatus());
            }
            jsonNode.put(NAME_CN, node.getNameCn());
            jsonNode.put(NAME_EN, node.getNameEn());
            jsonNode.put(NAME_PATH_CN, node.getNamePathCn());
            jsonNode.put(NAME_PATH_EN, node.getNamePathEn());
            jsonNode.put(IS_SELECT, node.isSelect());

            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                jsonNode.put(CHILD, convertToJsonArray(node.getChildren()));
            } else {
                jsonNode.put(CHILD, new JSONArray(0));
            }

            jsonArray.add(jsonNode);
        }

        return jsonArray;
    }

    /**
     * 在树结构中查找指定字段的路径信息
     *
     * @param treeData  树结构数据
     * @param fieldCode 字段代码
     * @return 包含中英文路径的数组，如果未找到则返回null
     */
    public static String[] findFieldPathInfo(JSONArray treeData, String fieldCode) {
        List<ExportTreeNode> treeNodes = convertToTreeNodes(treeData);
        return findFieldPathInfo(treeNodes, fieldCode);
    }

    /**
     * 在树结构中查找指定字段的路径信息
     *
     * @param treeNodes 树结构数据
     * @param fieldCode 字段代码
     * @return 包含中英文路径的数组，如果未找到则返回null
     */
    public static String[] findFieldPathInfo(List<ExportTreeNode> treeNodes, String fieldCode) {
        if (CollectionUtils.isEmpty(treeNodes) || fieldCode == null) {
            return null;
        }

        for (ExportTreeNode groupNode : treeNodes) {
            List<ExportTreeNode> children = groupNode.getChildren();

            if (children != null) {
                for (ExportTreeNode fieldNode : children) {
                    String code = fieldNode.getCode();

                    if (fieldCode.equals(code)) {
                        return new String[]{fieldNode.getNamePathCn(), fieldNode.getNamePathEn()};
                    }
                }
            }
        }

        return null;
    }

    /**
     * 将JSON数组转换为领域对象列表
     *
     * @param jsonArray JSON数组
     * @return 领域对象列表
     */
    private static List<ExportTreeNode> convertToTreeNodes(JSONArray jsonArray) {
        if (jsonArray == null) {
            return Collections.emptyList();
        }

        List<ExportTreeNode> treeNodes = new ArrayList<>(jsonArray.size());

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonNode = jsonArray.getJSONObject(i);

            ExportTreeNode.ExportTreeNodeBuilder builder = ExportTreeNode.builder()
                    .id(jsonNode.getString(ID))
                    .code(jsonNode.getString(CODE))
                    .level(jsonNode.getString(LEVEL))
                    .nameCn(jsonNode.getString(NAME_CN))
                    .nameEn(jsonNode.getString(NAME_EN))
                    .namePathCn(jsonNode.getString(NAME_PATH_CN))
                    .namePathEn(jsonNode.getString(NAME_PATH_EN))
                    .isSelect(jsonNode.getBooleanValue(IS_SELECT));

            if (jsonNode.containsKey(VALUE)) {
                builder.value(jsonNode.getString(VALUE));
            }

            if (jsonNode.containsKey(STATUS)) {
                builder.status(jsonNode.getString(STATUS));
            }

            ExportTreeNode treeNode = builder.build();

            JSONArray childArray = jsonNode.getJSONArray(CHILD);
            if (childArray != null && !childArray.isEmpty()) {
                treeNode.setChildren(convertToTreeNodes(childArray));
            } else {
                treeNode.setChildren(new ArrayList<>(0));
            }

            treeNodes.add(treeNode);
        }

        return treeNodes;
    }
}