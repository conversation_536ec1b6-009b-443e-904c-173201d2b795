package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.SneakyThrows;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.common.utils.CollectionUtilsEx.single;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/09
 */
public class SingletonTextValuePairsDeserializer<T> extends JsonDeserializer<T> {

    private static final Map<Class<?>, Method> METHOD_MAP = new HashMap<>();

    private static final String[] METHOD_NAMES = new String[] {
            "nilValueOf",
            "fromValue",
            "valueOf"
    };

    @SneakyThrows
    @Override
    public T deserialize(JsonParser p, DeserializationContext ctxt) {
        /* Started by AICoder, pid:1a675c2d24a24f78936675f04a069ec5 */
        List<TextValuePair> textValuePairs = p.getCodec().readValue(p, new TypeReference<List<TextValuePair>>() {});
        /* Ended by AICoder, pid:1a675c2d24a24f78936675f04a069ec5 */
        if (CollectionUtils.isEmpty(textValuePairs)) {
            return null;
        }

        Field field = JsonUtils.findField(p.getCurrentValue().getClass(), p.getCurrentName());
        if (field == null) {
            return null;
        }

        return parse(single(textValuePairs), field.getType());
    }

    @SuppressWarnings("unchecked")
    @SneakyThrows
    public static <T> T parse(TextValuePair textValuePair, Class<?> clazz) {
        if (textValuePair == null) {
            return null;
        }

        String val = textValuePair.getValue();
        if (val == null) {
            return null;
        }

        if (clazz == String.class) {
            return (T) val;
        }

        Method method = findParseMethod(clazz);
        if (method == null) {
            return null;
        }

        return (T) method.invoke(null, val);
    }

    private static Method findParseMethod(Class<?> clazz) {
        if (METHOD_MAP.containsKey(clazz)) {
            return METHOD_MAP.get(clazz);
        }

        for (String name : METHOD_NAMES) {
            try {
                Method method = clazz.getMethod(name, String.class);
                METHOD_MAP.put(clazz, method);
                return method;
            } catch (NoSuchMethodException e) {
                // ignore
            }
        }

        return null;
    }
}
