package com.zte.iccp.itech.extension.domain.constant;

import com.zte.iccp.itech.extension.common.utils.EncryptorUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.paas.lcap.core.entity.MainEntityType;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 下午4:54
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RedisKeys {

    private static final String PREFIX = "itech_cloud:";

    /** 用户类型Key*/
    public static String userType(String userId) {
        return key("userType", userId);
    }

    /** 变更任务导出Key*/
    public static String assignmentExExportKey(String userId) {
        return key("assignmentExExportKey", userId);
    }

    /** 操作日志key */
    public static String operationLogKey(String businessId, String operationLogEnumName, String userId) {
        return key("operationLog", businessId, operationLogEnumName, userId);
    }

    public static String msg(String langId, String msgId) {
        return key(langId, msgId);
    }

    public static String entityCount(MainEntityType entityType, List<IFilter> filters) {
        String entityId = entityType.getKey();
        String sha256 = EncryptorUtils.sha256(JsonUtils.toJsonString(filters));
        return key(entityId, "count", sha256);
    }

    public static String entityCount(MainEntityType entityType, String countSql) {
        String entityId = entityType.getKey();
        String sha256 = EncryptorUtils.sha256(countSql);
        return key(entityId, "sqlCount", sha256);
    }

    public static String reportPushKey(String report) {
        return key("reportPush", report);
    }

    private static String key(String... parts) {
        return PREFIX + String.join(":", parts);
    }
}
