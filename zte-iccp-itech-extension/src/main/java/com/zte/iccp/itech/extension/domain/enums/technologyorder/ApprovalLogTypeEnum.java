package com.zte.iccp.itech.extension.domain.enums.technologyorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/17
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ApprovalLogTypeEnum implements SingletonTextValuePairsProvider {
    /** 审批SLA
     */
    SLA("SLA", "SLA"),
    ;

    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }
}
