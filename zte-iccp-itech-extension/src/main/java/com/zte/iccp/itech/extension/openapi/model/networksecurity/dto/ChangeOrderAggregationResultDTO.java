package com.zte.iccp.itech.extension.openapi.model.networksecurity.dto;


import lombok.Getter;
import lombok.Setter;

/**
 * 网络变更单实体（三营稽核聚合）
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/5
 */
@Getter
@Setter
public class ChangeOrderAggregationResultDTO {
    /** 变更单id	 */
    private String changeOrderId;

    /** 变更单code */
    private String coNo;

    /** 任务类型 */
    private String assignmentType;

    /** 操作主题 */
    private String operationSubject;

    /** 产品经营团队 */
    private String prodTeam;

    /** 产品线 */
    private String prodLine;

    /** 产品大类 */
    private String prodMainCategory;

    /** 产品小类 */
    private String prodSubCategory;

    /** 产品分类id */
    private String productId;

    /** 产品分类名称 */
    private String productName;

    /** 营销 */
    private String sales;

    /** 片区 */
    private String organizationRegion;

    /** 代表处 */
    private String responsibleDeptId;

    /** 代表处id */
    private String organizationId;

    /** 代表处名称 */
    private String organizationName;

    /** 操作类型 */
    private String operationType;

    /** 操作原因 */
    private String operationReason;

    /** 保障方式 */
    private String guaranteeMode;

    /** 操作等级 */
    private String operationLevel;

    /** 风险评估 */
    private String riskEvaluation;

    /** 重要程度 */
    private String importance;

    /** 是否紧急操作 */
    private String isEmergencyOperation;

    /** 是否封网、管控期操作 */
    private String isNetCloseOrControlOperation;

    /** 触发类型 */
    private String triggerType;

    /** 变更操作来源 */
    private String changeOperationSource;

    /** 交付方式 */
    private String deliveryMode;

    /** 时区（TimeZoneEnum获取）*/
    private String timeZone;

    /** 国家/地区 */
    private String country;

    /** 省/州 */
    private String province;

    /** 地市 */
    private String area;

    /** 客户id */
    private String customerId;

    /** 客户标识 */
    private String accnType;

    /** 客户名称 */
    private String customerName;

    /** 操作封装 */
    private String operationEncapsulation;

    /** 是否首次应用 */
    private String isFirstApplication;

    /** 特殊场景 */
    private String isSpecialScenario;

    /** 是否需提供详细保障方案 */
    private String isGuaranteeSolution;

    /** 商用局 */
    private String isCommercialOffice;

    /** 有商务收费合同 */
    private String isCommercialChargeContract;

    /** 预计业务中断时长(分钟) */
    private String serviceDisconnectDuration;

    /** 是否带业务操作 */
    private String isBusinessOperation;

    /** 是否是技术通知单实施 */
    private String isTechnicalNotice;

    /** 客户特殊业务 */
    private String customerSpecialService;

    /** 是否大区操作 */
    private String isRegionalOperation;

    /** 涉及license文件加载 */
    private String licenseLoad;

    /** 工具名称（下拉多选） */
    private String toolNameSelected;

    /** 工具名称（多行文本） */
    private String toolName;

    /** 未使用工具原因 */
    private String notUseToolReason;

    /** 工具落地状态 */
    private String toolUse;

    /** 整体计划操作开始时间 */
    private String operationStartTime;

    /** 整体计划操作结束时间 */
    private String operationEndTime;

    /** 是否升级到网络服务部 */
    private String isNetDeptApproval;

    /** 对网络服务部的要求 */
    private String reqForNetServiceDept;

    /** 当前处理人 */
    private String currentProcessor;

    /** 主单据状态 */
    private String assignmentStatus;

    /** 当前进展 */
    private String currentProgress;

    /** 网络服务部审核结果 */
    private String approveResultNetServiceDeptApp;

    /** 网络服务部审核人 */
    private String approvedByNetServiceDept;

    /** 提交人工号 */
    private String commitById;

    /** 提交人 */
    private String commitBy;

    /** 提交人归属组织（中文） */
    private String orgCnName;

    /** 提交人归属组织（英文） */
    private String orgEnName;

    /** 提交时间 */
    private String commitTime;

    /** 更新人 */
    private String lastModifiedBy;

    /** 更新时间 */
    private String lastModifiedTime;

    /** 创建时间 */
    private String createTime;

    /** 批次id */
    private String batchId;

    /** 批次任务code */
    private String batchCode;

    /** 批次号 */
    private String batchNo;

    /** 批次名称 */
    private String batchOperationSubject;

    /** 计划操作开始时间 */
    private String changeOperationTimeStart;

    /** 计划操作结束时间 */
    private String changeOperationTimeEnd;

    /** 确认操作开始时间 */
    private String planOperationStartTime;

    /** 确认操作结束时间 */
    private String planOperationEndTime;

    /** 实际操作开始时间 */
    private String factOperationDateStart;

    /** 实际操作开始时间 */
    private String factOperationDateEnd;

    /** 通告发布时间 */
    private String releaseNotifyTime;

    /** 操作通告确认人 */
    private String releaseNotifier;

    /** 操作结果反馈人 */
    private String feedbackOperator;

    /** 操作结果反馈时间 */
    private String feedbackOperationTime;

    /** 操作结果 */
    private String operationResult;

    /** 批次失败原因 */
    private String batchFailureReason;

    /** 批次概要id */
    private String batchSummaryId;

    /** 网元数量 */
    private String neCount;

    /** 操作对象id */
    private String operationObjectId;

    /** 网络id */
    private String networkId;

    /** 网络code */
    private String networkCode;

    /** 客户网络名称 */
    private String networkName;

    /** 产品型号id */
    private String productModel;

    /** 产品型号名称 */
    private String productModelName;

    /** 局点名称（局点别名） */
    private String officeName;

    /** 当前版本id */
    private String currentVersionId;

    /** 当前版本名称 */
    private String currentVersion;

    /** 目标版本id */
    private String targetVersionId;

    /** 目标版本名称 */
    private String targetVersion;

    /** 批次最后更新人 */
    private String batchLastModifiedBy;

    /** 批次最后更新时间 */
    private String batchLastModifiedTime;

    /** 批次状态 */
    private String batchStatus;
}
