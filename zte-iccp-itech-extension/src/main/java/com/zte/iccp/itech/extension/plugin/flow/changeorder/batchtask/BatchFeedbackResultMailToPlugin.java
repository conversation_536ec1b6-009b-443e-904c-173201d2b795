package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.InformEmailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;


/**
 * 批次任务/分包商批次任务 - 按批次反馈结果-知会通知
 * 内部和合作方批次任务都仅在待反馈结果后进行提交操作
 * 【${操作主题}${是否紧急操作}（批次${批次号}）】${操作者}提交反馈操作结果
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/23
 */
public class BatchFeedbackResultMailToPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        InformEmailPlugin plugin = new InformEmailPlugin();
        JSONObject variables = body.getVariables();
        variables.put(ApprovalConstants.NODE_OPERATION, ApproveResultEnum.PASS.name());

        variables.put(ApprovalConstants.NODE_NAME, ApproveNodeEnum.RESULT_TOBE_BACK.name());
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(body.getFlowCode());
        if (ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowEnum) {
            variables.put(ApprovalConstants.NODE_NAME, PartnerApproveNodeEnum.RESULT_TOBE_BACK.name());
        }
        body.setVariables(variables);
        return plugin.anyTrigger(body, out);
    }
}
