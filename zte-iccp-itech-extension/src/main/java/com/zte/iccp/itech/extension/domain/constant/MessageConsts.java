package com.zte.iccp.itech.extension.domain.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/13
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MessageConsts {
    /** 最多选择20个 */
    public static final String MAX_LENGTH = "max.length";

    /** 标题是否审批 */
    public static final String TITLE_IS_APPROVAL= "title.is.approval";
    public static final String CLOUD_DISK_UPLOAD_FAILED = "clouddisk.upload.failed";

    public static final String BATCH_NO_NOT_EXISTS = "batch.no.not.exists";

    public static final String OPERATION_TYPE_NOT_EXISTS = "operation.type.not.exists";

    public static final String OPERATION_REASON_NOT_EXISTS = "operation.reason.not.exists";

    public static final String CURRENT_OPERATION_DURATION_ERROR = "current.operation.duration.error";

    public static final String CURRENT_START_TIME_ERROR = "current.start.time.error";

    public static final String DURATION_LESS_THAN_0_ERROR = "duration.less.than.0.error";

    public static final String VERSION_UPGRADE_FAILED = "version.upgrade.failed";

    public static final String OPERATION_ACCOUNT_NOT_EXISTS = "operation.account.not.exists";

    public static final String CURRENT_PROCESSOR = "current_processor";

    public static final String APPROVAL_BILL_NOT_FOUND = "approval.bill.not.found";

    public static final String CURRENT_REVIEWER_NODE = "current.review.node";

    public static final String REJECT_MODIFICATION = "reject.modification";
    public static final String REJECTION_REASON = "rejection.reason";

    public static final String PROCESSION_TIME = "procession.time";

    public static final String PROCESSION_PERSON = "procession.person";

    public static final String APPROVAL_ATTACHMENT = "approval.attachment";

    public static final String REJECTION_NODE = "rejection.node";

    public static final String APPROVAL_OF_NETWORK_CHANGE_OPERATIONS = "approval_of_network_change_operations";

    public static final String NETWORK_CHANGE_OPERATION_PLAN_CHANGE = "network_change_operation_plan_change";

    public static final String OPERATION_CANCALLATION_REVIEW = "operation.cancellation.review";

    public static final String APPROVAL_OF_PARTNER_NETWORK_CHANGE_OPERATIONS = "approval_of_partner_network_change_operations";

    public static final String PARTNER_NETWORK_CHANGE_OPERATION_PLAN_CHANGE = "partner_network_change_operation_plan_change";

    public static final String SIX_STAR_CHANGE_ORDER = "six_star_change_order";

    /** 单据【{0}】已进入批次，请转交对应批次！*/
    public static final String TODO_TRANSFER_PROMPT = "todo.transfer.prompt";

    /**
     * 分包商_技术方案检查_列_准备工作描述
     */
    public static final String HZS_PREPARATION_WORK_DESCRIPTION = "preparation.work.description";

    /**
     * 分包商_技术方案检查_列_检查内容_【操作方案是否按模板拟制】
     */
    public static final String HZS_TECH_SOLUTION_CHECK_CONTENT_FIRST = "hzs.tech.solution.check.content.first";
    /**
     * 分包商_技术方案检查_列_检查内容_【重大操作概述，简要描述其背景、目的】
     */
    public static final String HZS_TECH_SOLUTION_CHECK_CONTENT_SECOND = "hzs.tech.solution.check.content.second";
    /**
     * 操作阶段打卡子表单_列_操作阶段_操作准备
     */
    public static final String OPERATION_PREPARATION = "operation.preparation";
    /**
     * 操作阶段打卡子表单_列_操作阶段_操作实施
     */
    public static final String OPERATION_IMPLEMENTATION = "operation.implementation";
    /**
     * 操作阶段打卡子表单_列_操作阶段_测试验证
     */
    public static final String TEST_VERIFICATION = "test.verification";
    /**
     * 操作阶段打卡子表单_列_操作阶段_业务值守
     */
    public static final String SERVICE_OBSERVATION = "service.observation";
    /**
     * 分包商网络变更_确认提示_操作等级为关键时，用户不能提交单据
     */
    public static final String CONFIRM_MESSAGE_OPERATION_LEVEL = "confirm.message.operation.level";
    /**
     * 大区相关代表处表_错误提示_邮件抄送不能超过50人
     */
    public static final String MAIL_CARBON_COPY_ERROR = "mail.carbon.copy.error";

    /**
     * 网络变更申请单
     */
    public static final String NETWORK_CHANGE_APPLICATION_FORM = "network.change.application.form";

    /**
     * 提交申请
     */
    public static final String SUBMIT_APPLICATION = "submit.application";

    /**
     * 最多选择10个
     */
    public static final String MAX_LENGTH_10= "maxLength10";

    /**
     * 最多选择100个
     */
    public static final String MAX_LENGTH_100= "maxLength100";
    /**
     * 操作人员列表_角色_错误提示_操作负责人角色不能重复选择
     */
    public static final String OPERATOR_ROLE_ERROR = "operation.role.error";

    /**
     * 操作人员列表_角色_错误提示_同一批次下，本地值守人员不能重复选择
     */
    public static final String LOCAL_ON_DUTY_ROLE_ERROR = "operation.role.local.duty.error";

    /**
     * 操作人员列表_员工_错误提示_交叉检查人员与操作人员不能是同一个人
     */
    public static final String OPERATOR_NAME_ERROR = "operation.name.error";
    /**
     * 操作人员列表_员工_同批次下存在相同角色人员
     */
    public static final String OPERATION_NAME_ROLE_BATCH_ERROR = "operation.name.role.batch.error";
    /**
     * 操作步骤打卡_时间检查_错误提示_请先填写计划开始时间和计划结束时间
     */
    public static final String OPERATOR_TIME_CHECK_ERROR = "operator.time.check.error";
    /**
     * 操作步骤打卡_时间检查_错误提示_打卡时间必须在计划开始时间和计划结束时间之间
     */
    public static final String STEP_TIME_CHECK_ERROR = "step_time_check_error";
    /**
     * 操作步骤打卡_时间检查_错误提示_打卡开始时间必须在打卡结束时间之前
     */
    public static final String STEP_TIME_EARLY_CHECK_ERROR = "step.time.early.check.error";

    /**
     * 技术管理任务不能申请关闭提示
     */
    public static final String MANAGE_TASK_NOT_APPLY_CLOSE = "manage.task.not.apply.close";

    /**
     * 属于核心网的变更单请在IDOP系统创建
     */
    public static final String CCN_ORDERS_ON_IDOP_CREATE = "ccn.orders.on.idop.create";

    /**
     * 您修改了冲突检测相关的字段，会触发iDOP系统重做冲突检测，请关注最新的冲突检测结果。
     */
    public static final String IDOP_CHANGE_CONFLICT_FIELDS = "idop.change.conflict.fields";

    /**
     * 技术管理任务列表-前置废止校验提示
     */
    public static final String MANAGE_TASK_ABOLISH_BEFORE_CHECK = "manage_task_abolish_before_check";

    /**
     * 技术管理任务列表-前置草稿校验提示
     */
    public static final String MANAGE_TASK_START_BEFORE_CHECK = "manage_task_start_before_check";

    /**
     * 废止成功提示
     */
    public static final String MANAGE_TASK_ABOLISH_SUCCESS = "manage_task_abolish_success";

    /** 提交审核、重新提交 - 提交成功提示 */
    public static final String MESSAGE_SUBMIT_SUCCESS_REFRESH = "submit.success.";

    /** 内部/合作方变更单，关闭按钮提示语 */
    public static final String CONFIRM_DOCUMENT_SAVE = "confirm.the.document.has.been.saved";

    /** 关闭页面 */
    public static final String CLOSE_PAGE = "close.page";

    /**
     * 技术管理任务责任转交信息
     */
    public static final String MANAGE_TASK_RESPONSIBILITY_TRANSFERENCE_INFO = "manage_task_responsibility_transference_info";

    /**
     * 技术管理子任务数量阈值，999
     */
    public static final String MANAGE_SUB_TASK_MAXVALUE_999_ERROR = "manage_sub_task_number_error";

    /**
     * AI生成操作方案 选择的标准方案不可用于生成操作方案
     */
    public static final String GENERATE_OPERATION_SOLUTION_NOT_HAVA_TAG_DOC_ID_ERROR = "generate.operation.solution.not.hava.tag.doc.id.error";

    /**
     * AI生成操作方案 产品类型选择不正确
     */
    public static final String GENERATE_OPERATION_SOLUTION_PRODUCT_TYPE_ERROR = "generate.operation.solution.product.type.error";

    /**
     * 标准方案 - 查看日志 - 页面标题
     */
    public static final String VIEW_LOG_TITLE = "standard.scheme.view.Log.title";

    /**
     * 标准方案 - 查看历史版本 - 页面标题
     */
    public static final String VIEW_HISTORY_TITLE = "standard.scheme.view.history.title";

    /**
     * 操作方案生成失败
     */
    public static final String OPERATION_SCHEME_GENERATE_FAILURE = "operation.scheme.generate.failure";

    /**
     * 操作方案生成中，请稍后刷新
     */
    public static final String OPERATION_SCHEME_GENERATE_ONGONING = "operation.scheme.generate.ongoing";

    /**
     * 操作方案生成成功
     */
    public static final String OPERATION_SCHEME_GENERATE_SUCCESS = "operation.scheme.generate.success";

    /**
     * 任务管理-任务不存在提示
     */
    public static final String ASSIGNMENT_MANAGE_NON_EXISTENT_WARNING = "assignment.manage.non.existent.warning";

    /**
     * 任务管理 / 任务中心 - 编辑 - 网络变更 / 分包商网络变更任务状态异常
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_NETWORK_CHANGE = "assignment.operation.warning.edit.errorStatus.networkChange";

    /**
     * 任务管理 / 任务中心 - 编辑 - 技术管理任务状态异常
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_TECHNOLOGY = "assignment.operation.warning.edit.errorStatus.technology";

    /** 任务管理 / 任务中心 - 编辑 - 权限申请任务状态异常 */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_PERMISSION = "assignment.operation.warning.edit.errorStatus.permission";

    /**
     * 任务管理 / 任务中心 - 编辑 - 任务类型错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_TYPE = "assignment.operation.warning.edit.errorType";

    /**
     * 任务管理 / 任务中心 - 编辑 - 网络变更 / 分包商网络变更任务责任人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_RESPONSIBLE_NETWORK_CHANGE = "assignment.operation.warning.edit.errorResponsible.networkChange";

    /** 任务管理 / 任务中心 - 编辑 - 技术管理任务责任人错误 */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_RESPONSIBLE_TECHNOLOGY = "assignment.operation.warning.edit.errorResponsible.technology";

    /** 任务管理 / 任务中心 - 编辑 - 权限申请任务责任人错误 */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_RESPONSIBLE_PERMISSION = "assignment.operation.warning.edit.errorResponsible.permission";

    /**
     * 任务管理 / 任务中心 - 编辑 - 操作计划任务状态错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_OPERATION_PLAN = "assignment.operation.warning.edit.errorStatus.operationPlan";

    /**
     * 任务管理 / 任务中心 - 编辑 - 操作计划任务创建人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_CREATED_BY_OPERATION_PLAN = "assignment.operation.warning.edit.errorCreatedBy.operationPlan";

    /**
     * 任务管理 / 任务中心 - 废止 - 任务类型错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_ABOLISH_ERROR_TYPE = "assignment.operation.warning.abolish.errorType";

    /**
     * 任务管理 / 任务中心 - 废止 - 网络变更 / 分包商网络变更任务状态异常
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_ABOLISH_ERROR_STATUS_NETWORK_CHANGE = "assignment.operation.warning.abolish.errorStatus.networkChange";

    /**
     * 任务管理 / 任务中心 - 废止 - 技术管理任务状态异常
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_ABOLISH_ERROR_STATUS_TECHNOLOGY = "assignment.operation.warning.abolish.errorStatus.technology";

    /**
     * 任务管理 / 任务中心 - 废止 - 人员积分技术管理任务不能废止
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_ABOLISH_ERROR_TECHNOLOGY_PERSONNEL = "assignment.operation.warning.abolish.technology.personnel";

    /**
     * 任务管理 / 任务中心 - 废止 - 网络变更 / 分包商网络变更任务责任人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_ABOLISH_ERROR_RESPONSIBLE_NETWORK_CHANGE = "assignment.operation.warning.abolish.errorResponsible.networkChange";

    /**
     * 任务管理 / 任务中心 - 废止 - 技术管理任务责任人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_ABOLISH_ERROR_RESPONSIBLE_TECHNOLOGY = "assignment.operation.warning.abolish.errorResponsible.technology";

    /**
     * 任务管理 / 任务中心 - 废止 - 技术管理子任务责任人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_ABOLISH_ERROR_RESPONSIBLE_SUB = "assignment.operation.warning.abolish.errorResponsible.sub";

    /**
     * 任务管理 / 任务中心 - 删除 - 网络变更 / 分包商网络变更任务驳回待启动错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_DELETE_ERROR_STATUS_NETWORK_CHANGE_APPROVE_START = "assignment.operation.warning.delete.errorStatus.networkChange.approveStart";

    /**
     * 任务管理 / 任务中心 - 删除 - 网络变更 / 分包商网络变更任务责任人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_DELETE_ERROR_RESPONSIBLE_NETWORK_CHANGE = "assignment.operation.warning.delete.errorResponsible.networkChange";

    /**
     * 任务管理 / 任务中心 - 删除 - 技术管理任务责任人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_DELETE_ERROR_RESPONSIBLE_TECHNOLOGY = "assignment.operation.warning.delete.errorResponsible.technology";

    /**
     * 任务管理 / 任务中心 - 转交 - 技术管理任务状态错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_TRANSFER_ERROR_STATUS_TECHNOLOGY = "assignment.operation.warning.transfer.errorStatus.technology";

    /**
     * 任务管理 / 任务中心 - 转交 - 技术管理任务责任人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_TRANSFER_ERROR_RESPONSIBLE_TECHNOLOGY = "assignment.operation.warning.transfer.errorResponsible.technology";

    /**
     * 任务管理 / 任务中心 - 转交 - 故障管理任务责任人错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_TRANSFER_ERROR_FAULT_MANAGER = "assignment.operation.warning.transfer.error.fault.manager";

    /**
     * 任务管理 / 任务中心 - 查看关联 - 任务类型错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_ASSOCIATE_ERROR_TYPE = "assignment.operation.warning.associate.errorType";

    /**
     * 任务管理 / 任务中心 - 复制 - 任务类型错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_COPY_ERROR_TYPE = "assignment.operation.warning.copy.errorType";

    /**
     * 任务管理 / 任务中心 - 复制 - 核心网网络变更任务请前往iDOP系统创建
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_IDOP_COPY_ERROR_TYPE = "assignment.operation.warning.idop.copy.errorType";

    /**
     * 保障变更单不能复制
     */
    public static final String GUARANTEE_CHANGE_ORDER_NOT_COPY_ERROR = "guarantee.change.order.not.copy.error";

    /**
     * 任务管理 / 任务中心 - 催办 - 30分钟没不能重复催办
     */
    public static final String ASSIGNMENT_OPERATION_PRESS_WARNING = "assignment.operation.press.warning";

    /**
     * 任务管理 / 任务中心 - 催办 - 任务状态异常，仅支持催办 执行中 / 审批中 状态的任务
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_PRESS_STATUS_ERROR= "assignment.operation.warning.press.status.error";

    /**
     * 任务管理 / 任务中心 - 催办成功
     */
    public static final String ASSIGNMENT_OPERATION_PRESS_SUCCESS= "assignment.operation.press.success";

    /**
     * 任务管理 / 任务中心 - 催办 - 任务无收件人或待系统处理中
     */
    public static final String ASSIGNMENT_OPERATION_NOT_HANDLER_PRESS_WARNING = "assignment.operation.not.handler.press.warning";

    /**
     * 任务管理 / 任务中心 - 处理 - 任务状态异常，仅支持处理 执行中 / 审批中 状态的任务
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_APPROVE_STATUS_ERROR= "assignment.operation.warning.approve.status.error";

    /**
     * 任务管理 / 任务中心 - 处理 - 不是当前任务处理人
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_NOT_TASK_HANDLER_ERROR= "assignment.operation.warning.not.task.handler.error";

    /**
     * 任务管理 / 任务中心 - 处理 - 任务类型错误
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_HANDLER_TYPE_ERROR = "assignment.operation.warning.handler.type.error";

    /**
     * 任务管理 / 任务中心 - 处理 - 待系统自动处理中
     */
    public static final String ASSIGNMENT_OPERATION_WARNING_PENDING_SYSTEM_PROCESS_ERROR = "assignment.operation.warning.pending.system.process.error";

    /**
     * 任务管理-删除按钮提示
     */
    public static final String ASSIGNMENT_MANAGE_DETELE_WARNING = "assignment.manage.delete.warning";

    /**
     * 网络集网络选择-权限不足提示
     */
    public static final String PERMISSION_NOT_SET_WARNING = "permission.not.set.warning";

    /**
     * 网络集网络选择-权限不足提示
     */
    public static final String DURATION_RANGE_CHECK = "duration.range.check";

    /**
     * 选项配置-操作类型配置-操作类型未配置分组信息提示
     */
    public static final String OPERATION_TYPE_GROUP_LOOKUP_ERROR="operation.type.group.lookup.error";

    /**
     * 分级保障-局点系数配置-国家/地区+客户必须唯一提示
     */
    public static final String COUNTRY_CUSTOMER_REPEAT_ERROR="country.customer.repeat.error";

    /**
     * 批次任务->授权文件暂未生成
     */
    public static final String BATCH_TASK_NOT_FIND_GRANT_FILE = "batch.task_not.find.grant.file";

    /**
     * 批次任务->只有代操作执行状态才能操作 Proxy Operation Execution
     */
    public static final String BATCH_TASK_ONLY_PROXY_OPERATION_EXECUTION = "batch.task.only.proxy.operation.execution";

    /**
     * 批次 取消操作审核节点（操作变更说明前缀）
     */
    public static final String BATCH_CANCEL_REVIEW_OPERATION_CHANGE_DESC_PREFIX = "batch.cancelReview.operationChangeDesc.prefix";

    /**
     * 故障管理任务 - 有效故障复盘任务为空
     */
    public static final String FAULT_ORDER_SUBMIT_NULL_VALID_FAULT_REVIEW = "faultOrder.submit.nullValidFaultReviewAssignment";

    /** 故障管理任务 - 故障整改任务为空 */
    public static final String FAULT_ORDER_SUBMIT_NULL_VALID_FAULT_RECTIFY = "faultOrder.submit.nullValidFaultRectifyAssignment";

    /**
     * 故障管理任务 - 代表处没有配置客户满意度节点审核人提示
     */
    public static final String TIPS_OF_HAS_NO_CUSTOMER_SATISFACTION_PROCESSORS = "tips.of.has.no.customer.satisfaction.processors";

    /** 待办中心 - 任务已审批 */
    public static final String BACKLOG_APPROVED = "Backlog.TaskApprovedError";

    /** 仅支持责任人撤回未被审核过且状态为审核中的任务*/
    public static final String ASSIGNMENT_OPERATION_REVOKE_ERROR = "assignment.operation.revoke.error";

    /** 撤回成功 */
    public static final String ASSIGNMENT_OPERATION_REVOKE_SUCCESS = "assignment.operation.revoke.success";

    /** 关联产品支持方式不完整校验 */
    public static final String ASSOCIATED_PRODUCT_SUPPORT_INFO_INCOMPLETE = "associated.product.support.info.incomplete";

    //********** email ***************

    /**
     * 邮件模板id不能为空
     */
    public static final String EMAIL_TEMPLATEID_NOT_NULL= "email.templateId.error";

    /**
     * 审批流程未找到
     */
    public static final String APPROVAL_FLOW_NOT_FOUND = "approval.flow.not.found";
    /**
     * 审批人未找到
     */
    public static final String APPROVER_NOT_FOUND = "approver.not.found";

    public static final String FLOW_NODE_CLOSE_FAILED = "approval.flow.node.closeFailed";

    /**
     * 请选择操作人员后查看认证信息
     */
    public static final String OPERATOR_NAME_NOT_FOUND = "operator.name.not.found";

    /** 网元清单文件不是Excel格式 */
    public static final String NE_LIST_FILE_NOT_A_EXCEL = "nelist.file.not.a.excel";

    /** 表单至少保留一行 */
    public static final String AT_LEAST_ONE_LINE_CHECK = "at.least.one.line.check";

    /** 超级授权 */
    public static final String SUPER_AUTHORIZATION = "super.authorization";

    /** 客户支持经理和产品科长至少填写其中一项 */
    public static final String MANAGER_AND_SECTION_NULL_ERROR = "manager.and.section.null.error";

    /** 同一个【代表处】【产品】【国家】仅能存在一条数据*/
    public static final String SATISFACTION_UNIQUENESS_CHECK = "satisfaction.uniqueness.check";

    /*变更单ID 没找到数据*/
    public static final String CHANGE_ORDER_ID_NOT_FIND = "change.order.data.not.find";

    /*变更单不能编辑状态*/
    public static final String CHANGE_ORDER_CANNOT_EDIT = "change.order.cannot.edit";

    /*产品类型名称未找到数据*/
    public static final String PRODUCT_DATA_NOT_FIND = "product.data.not.find";

    /*国家名称未找到数据*/
    public static final String COUNTRY_DATA_NOT_FIND = "country.data.not.find";

    /*变更单当前状态不能修改计划时间*/
    public static final String CHANGE_ORDER_CANNOT_UPDATE_TIME = "change.order.cannot.update.time";

    /*代表处名称未找到数据*/
    public static final String DEPARTMENT_DATA_NOT_FIND = "department.data.not.find";

    /*客户名称未找到数据*/
    public static final String CUSTOMER_NAME_DATA_NOT_FIND = "customer.name.data.not.find";

    /*操作类型编码未找到数据*/
    public static final String OPERATE_TYPE_DATA_NOT_FIND = "operate.type.data.not.find";

    /*操作原因编码未找到数据*/
    public static final String OPERATE_REASON_DATA_NOT_FIND = "operate.reason.data.not.find";

    /*网络id在NIS系统未找到*/
    public static final String NETWORK_ID_NOT_FIND_IN_NIS = "network.id.not.find.in.nis";

    /** 多产品联动保障子表单删除数据confirm提示语 */
    public static final String MULTI_PROD_DELETE_SHOW_CONFIRM_MESSAGE = "multi.prod.delete.confirm.message";

    /** 多产品联动保障子表单删除关联关系confirm提示语 */
    public static final String MULTI_PROD_DELETE_RELATION_SHOW_CONFIRM_MESSAGE = "multi.prod.delete.relation.confirm.message";

    /** 以下字段没有填写 */
    public static final String FOLLOWING_FIELDS_NOT_FILLED_IN = "following.fields.not.filled.in";

    /** 操作主题 */
    public static final String OPERATION_SUBJECT = "operation.subject";

    /** 产品分类 */
    public static final String PRODUCT_CATEGORY = "product.category";

    /** 代表处 */
    public static final String REPRESENTATIVE_OFFICE = "representative.office";

    /** 操作类型 */
    public static final String OPERATION_TYPE = "operation.type";

    /** 操作原因 */
    public static final String OPERATION_REASON_MSG = "operation.reason";

    /** 属于GDPR管控项目 */
    public static final String GDPR_CONTROLLED_PROJECT = "GDPR.controlled.project";

    /** GDPR要求 */
    public static final String GDPR_REQUIREMENT = "GDPR.requirement";

    /** 政企 */
    public static final String GOVERNMENT_ENTERPRISE = "government.enterprise";

    /** 多产品联动保障子表单主任务不允许删除 */
    public static final String MULTI_PROD_DELETE_MAIN_TASK_ERROR = "multi.prod.delete.main.task.error";

    /** 当前变更单为保障主任务，且存在未完成的关联子保障任务，暂不能提交 */
    public static final String MULTI_PROD_NOT_COMMIT_ERROR = "multi.prod.not.commit.error";

    /** idop网元清单模板名称 */
    public static final String IDOP_NETWORK_FILE_NAME= "idop.network.file.name";

    /** 【操作负责人】只能有一个 */
    public static final String OPERATING_SUPERVISOR_ONLY_ONE = "operating.supervisor.only.one";

    /** 【交叉检查人员】有重复人员 */
    public static final String CROSS_CHECKER_REPEATED = "cross.checker.repeated";

    /** 【操作人员】有重复人员 */
    public static final String OPERATOR_REPEATED = "operator.repeated";

    /** 【值守人员】有重复人员 */
    public static final String WATCHMAN_REPEATED = "watchman.repeated";

    /** 【测试人员】有重复人员 */
    public static final String TESTER_REPEATED = "tester.repeated";

    /** 【操作负责人、操作人员】至少有一条 */
    public static final String PART_OPERATOR_NOT_NULL = "sub.batch.operators.not.null";

    /** 【辅助操作人员】有重复人员 */
    public static final String AUXILIARY_OPERATOR_REPEATED = "auxiliary.operator.repeated";

    /** 【外包人员】有重复人员 */
    public static final String OUTSOURCING_PERSONNEL_REPEATED = "outsourcing.personnel.repeated";

    /** 【支持人员】有重复人员 */
    public static final String SUPPORT_PERSONNEL_REPEATED = "support.personnel.repeated";

    /** 国际会签记录不存在 */
    public static final String INTERNATIONAL_COUNTERSIGN_RECORD_NOT_FOUND = "internationalCounterSignRecord.notFound";

    /**
     * 操作关联产品删除错误提示
     * 以下数据已经被引用，请删除引用数据后再尝试删除：
     */
    public static final String OPERATION_ASSOCIATED_PRODUCT_DELETE_ERROR = "operation.associated.product.delete.error";

    /** 产品型号唯一性校验：当申请单的产品分类属于【算力及核心网-核心网】【算力及核心网-云及智算】【算力及核心网-集成第三方与组网设备】：（弹窗点击确定时弹出提示词）同一个网络下，局点名称（局点别名）不可重复 */
    public static final String NETWORK_OFFICE_UNIQUE_CHECK_TIPS = "network.office.unique.check.tips";

    /** 产品型号唯一性校验：当申请单的产品分类不属于上述：（弹窗点击确定时弹出提示词）同一个网络下，产品型号不可重复 */
    public static final String NETWORK_MODEL_UNIQUE_CHECK_TIPS = "network.model.unique.check.tips";

    /** 变更单提交前置校验提示：请至少填写一行操作对象 */
    public static final String OPERATION_OBJECT_NOT_NULL_ERROR = "operation.object.not.null.error";

    /** 操作对象产品型号选择提交前置校验提示：主产品所属的产品分类必须与提单填写的产品分类一致 */
    public static final String OPERATION_OBJECT_MAIN_PRODUCT_MODEL_ERROR = "operation.object.main.product.model.error";

    /** 以下操作批次没有操作对象：{0} */
    public static final String OPERATION_OBJECT_CHECK_BATCH_SUMMARY_ERROR = "operation.object.check.batch.summary.error";

    /** 批次概要的操作批次号必须唯一 */
    public static final String CHECK_BATCH_SUMMARY_REPEAT_ERROR = "check.batch.summary.repeat.error";

    /** 批次概要不能为空 */
    public static final String CHECK_BATCH_SUMMARY_EMPTY_ERROR = "check.batch.summary.empty";

    /** 开始日期不能大于当前日期*/
    public static final String CHECK_DATE_IS_AFTER_ERROR = "check.date.is.after.error";

    /** 开始日期最多往前推三个月*/
    public static final String CHECK_DATE_IS_BEFORE_THREE_ERROR = "check.date.is.before.three.error";

    /** 结束日期不能早于开始日期*/
    public static final String CHECK_DATE_IS_BEFORE_ERROR = "check.date.is.before.error";

    /** 开始日期和结束日期之间最多间隔三个月*/
    public static final String CHECK_DATE_MONTHS_BETWEEN_ERROR = "check.date.months.between.error";

    /** 客户标识最大阈值为20*/
    public static final String ACCN_TYPE_MAX_NUMBER_ERROR = "accn.type.max.number.error";

    /** 操作类型最大阈值为20*/
    public static final String OPERATION_TYPE_MAX_NUMBER_ERROR = "operation.type.max.number.error";

    /** 接口调用异常*/
    public static final String INTERFACE_INVOK_FAILED = "interface.invok.failed";

    /** 变更单提交前置校验提示：请填写至少一行电源规则型号物料 */
    public static final String POWER_SPECIFICATION_MODEL_MIX_NUMBER_ERROR = "power.specification.model.mix.number.error";

    /** 变更单提交前置校验提示：电源规则型号物料最多填写50行 */
    public static final String POWER_SPECIFICATION_MODEL_MAX_NUMBER_ERROR = "power.specification.model.max.number.error";

    /** 变更单提交前置校验提示：电源规则型号 - 自定义物料名称不能为空 */
    public static final String POWER_SPECIFICATION_MODEL_MATERIAL_NAME_REQUIRED_ERROR = "power.specification.model.material.name.required.error";

    /** 变更单提交前置校验提示：电源规则型号 - 硬件版本不能为空 */
    public static final String POWER_SPECIFICATION_MODEL_HARDWARE_VERSION_REQUIRED_ERROR = "power.specification.model.hardware.version.required.error";

    /** 导出数据为空警告 */
    public static final String EXPORT_EMPTY_WARNING = "export.empty.warning";

    /** 已连续操作2天 */
    public static final String HIGH_LOAD_NIGHT_OPERATION = "high.load.night.operation";

    /** 一周内已操作n天 */
    public static final String HIGH_LOAD_CONSECUTIVE_NIGHT_OPERATION = "high.load.consecutive.night.operation";

    /** 【xxx】已连续n天作为操作人或操作负责人，请合理安排操作。 */
    public static final String HIGH_LOAD_OPERATOR_CONSECUTIVE_DAYS = "high.load.operator.consecutive.days";

    /** 【yyy】最近一周已操作了x个夜班，请合理安排操作。 */
    public static final String HIGH_LOAD_NIGHT_SHIFT_OPERATIONS_REMINDER = "high.load.night.shift.operations.reminder";

    /** 请填写操作批次 */
    public static final String CHECK_BATCH_NO_EMPTY_ERROR = "check.batch.no.empty";

    /** 客户网络名称不能为空，请点击查询 */
    public static final String CHECK_CUSTOMER_NETWORK_NAME_EMPTY_ERROR = "check.customer.network.name.empty";

    /** 产品型号不能为空 */
    public static final String CHECK_PRODUCT_MODEL_ID_EMPTY_ERROR = "check.product.model.id.empty";

    /** 请补充第N行批次概要的操作批次、计划操作开始时候和计划操作结束时间数据 */
    public static final String BATCH_SUMMARY_TIMING_FIELDS_MISSING = "batch.summary.timing.fields.missing";

    /**
     * 双语化公共关键字
     */
    public static class CommonKeyword {

        /** 天 */
        public static final String DAY = "commonConstants.day";

        /** 时 */
        public static final String HOUR = "commonConstants.hour";

        /** 分 */
        public static final String MINUTE = "commonConstants.minute";

        /** 启用 */
        public static final String ENABLED = "option_enable";

        /** 停用 */
        public static final String DISABLED = "option_disabled";
    }

    /**
     * 任务中心
     */
    public static class AssignmentCenter {

        /** 提示信息 - 转交状态异常 - 网络变更 */
        public static final String TRANSFER_ERROR_STATUS_NETWORK_CHANGE = "assignment.notice.transferErrorStatus.networkChange";

        /** 提示信息 - 转交状态异常 - 批次任务 */
        public static final String TRANSFER_ERROR_STATUS_BATCH = "assignment.notice.transferErrorStatus.batch";

        /** 提示信息 - 转交责任人异常 */
        public static final String TRANSFER_ERROR_RESPONSIBLE = "assignment.notice.transferErrorResponsible";

        /** 提示信息 - 导出无数据 */
        public static final String EXPORT_NO_DATA = "assignment.notice.exportNoData";

        /** 提示信息 - 未选择导出字段 */
        public static final String NO_EXPORT_FIELD_SELECTED = "assignment.notice.noFieldSelected";

        /** 提示信息 - 标准模板不可删除 */
        public static final String STANDARD_TEMPLATE_NOT_DELETE = "assignment.notice.notDelete";

        /** 提示信息 - 模板名称重复，请重新输入 */
        public static final String TEMPLATE_NAME_EXIST = "template.name.exist";

        /** 提示信息 - 标准模板不可编辑 */
        public static final String STANDARD_TEMPLATE_NOT_EDIT = "assignment.notice.notEdit";

        /** 提示信息 - 数据超过10w条，暂不支持导出 */
        public static final String MORE_THAN_100000_PIECES_OF_DATA = "assignment.notice.moreThan100000";

        /** 导出文件名 - 网络变更任务 */
        public static final String EXPORT_FILE_NAME = "network.change.operation.ticket";

        /**
         * 操作计划表头
         */
        public static final String OPERATION_PLAN_ORDER_FILE_NAME = "operation.plan.order";

        /** 表头 - 任务编码*/
        public static final String TITLE_ASSIGNMENT_CODE = "task.no.";

        /** 表头 - 任务名称 */
        public static final String TITLE_ASSIGNMENT_NAME = "task.name";

        /** 表头 - 任务类型 */
        public static final String TITLE_ASSIGNMENT_TYPE = "task.type";

        /** 表头 - 营销 */
        public static final String TITLE_MARKETING = "marketing";

        /** 表头 - 代表处 */
        public static final String TITLE_REPRESENTATIVE_OFFICE = "representative.office";

        /** 表头 - 产品分类 */
        public static final String TITLE_PRODUCT_CATEGORY = "product.category";

        /** 表头 - 国家 / 地区 */
        public static final String TITLE_COUNTRY_REGION = "country.region";

        /** 表头 - 操作类型 */
        public static final String TITLE_OPERATION_TYPE = "power.speci.model.export.operationType";

        /** 表头 - 操作原因 */
        public static final String TITLE_OPERATION_REASON = "operation.reason";

        /** 表头 - 操作结果 */
        public static final String TITLE_OPERATION_RESULT = "operation.result";

        /** 表头 - 操作等级 */
        public static final String TITLE_OPERATION_LEVEL = "operation.level";

        /** 表头 - 重要程度 */
        public static final String TITLE_IMPORTANCE = "importance.degree";

        /** 表头 - 风险评估 */
        public static final String TITLE_RISK_EVALUATION = "risk.assessment";

        /** 表头 - 客户网络名称 */
        public static final String TITLE_CUSTOMER_NETWORK_NAME = "customer.network.name";

        /** 表头 - 节点名称 */
        public static final String TITLE_OFFICE_NAME = "office.name";

        /** 表头 - 客户标识 */
        public static final String TITLE_CUSTOMER_CLASSIFICATION = "customer.label";

        /** 表头 - 计划开始时间 */
        public static final String TITLE_PLAN_START_TIME = "planned.start.date";

        /** 表头 - 任务状态 */
        public static final String TITLE_ASSIGNMENT_STATUS = "power.speci.model.export.assignmentStatus";

        /** 表头 - 当前进展 */
        public static final String TITLE_CURRENT_PROGRESS = "current.progress";

        /** 表头 - 当前处理人 */
        public static final String TITLE_CURRENT_PROCESSOR = "current.handler";

        /** 表头 - 责任人 */
        public static final String TITLE_RESPONSIBLE = "responsible.person";

        /** 表头 - 是否超期 */
        public static final String TITLE_OVERDUE_FLAG = "overdue.or.not";

        /** 表头 - 超期时间 */
        public static final String TITLE_OVERDUE_TIME = "overdued.time";

        /** 表头 - 创建人 */
        public static final String TITLE_CREATE_BY = "created.by";

        /** 表头 - 创建时间 */
        public static final String TITLE_CREATE_TIME = "creation.time";

        /** 表头 - 最后更新人 */
        public static final String TITLE_LAST_MODIFIED_BY = "updated.by";

        /** 表头 - 最后更新时间 */
        public static final String TITLE_LAST_MODIFIED_TIME = "updated.time";
    }

    /**
     * 故障管理任务
     */
    public static class FaultAssignment {

        /** 故障复盘确认 */
        public static final String FAULT_MANAGEMENT_TASK_CURRENT_PROGRESS_2 = "fault_management_task_current_progress_2";

        /** 待提交故障复盘报告 */
        public static final String FAULT_MANAGEMENT_TASK_CURRENT_PROGRESS_3 = "fault_management_task_current_progress_3";

        /** 待故障整改横推 */
        public static final String FAULT_MANAGEMENT_TASK_CURRENT_PROGRESS_4 = "fault_management_task_current_progress_4";

        /** 待客户满意度 */
        public static final String FAULT_MANAGEMENT_TASK_CURRENT_PROGRESS_5 = "fault_management_task_current_progress_5";

        /** 故障复盘确认 */
        public static final String FAULT_REVIEW_CONFIRM = "fault_management_task_current_progress_2";

        /** 故障复盘报告 */
        public static final String FAULT_REVIEW_REPORT = "fault.review.report";

        /** 故障整改横推 */
        public static final String FAULT_RECTIFICATION = "fault.rectification";

        /** 客户满意度 */
        public static final String CUSTOMER_SATISFACTION = "customer.satisfaction";

    }

    /**
     * 待办中心
     */
    public static class Backlog {

        /** 提示信息 - 草稿权限申请任务处理 */
        public static final String DRAFT_PERMISSION_NOTICE = "backlog.notice.draftPermission";

        /** 提示信息 - 转交执行中网络变更任务 */
        public static final String TRANSFER_EXECUTE_NETWORK_CHANGE = "backlog.notice.transferExecuteNetworkChange";

        /** 提示信息 - 待转交人在当前处理人中 */
        public static final String EXIST_CURRENT_PROCESSOR = "backlog.notice.existCurrentProcessor";

        /** 日志 - 转交当前处理人 */
        public static final String LOG_TRANSFER_CURRENT_PROCESSOR = "log.transfer.currentProcessor";

        /** 日志 - 转交责任人 */
        public static final String LOG_TRANSFER_RESPONSIBLE = "log.transfer.responsible";
    }

    public static class ClockIn {

        /** 打卡任务不存在 */
        public static final String TASK_NOT_EXISTS = "clockInTask.notExists";

        /** 打卡任务-已废止，请刷新列表重新获取任务 */
        public static final String TASK_IS_ABOLISH = "clockInTask.isAbolish";

        /** 任务打卡人不匹配 */
        public static final String TASK_OPERATOR_NOT_MATCH = "clockInTask.operatorNotMatch";

        /** 打卡选项不可用 */
        public static final String OPTION_DISABLED = "clockInTask.optionDisabled";

        /** 只能转交值守任务 */
        public static final String CAN_ONLY_CHANGE_ON_DUTY_TASK_OWNER = "clockInTask.canOnlyChangeOnDutyTaskOwner";

        /** 只能转交自己的打卡任务 */
        public static final String CAN_ONLY_CHANGE_TASK_OWNER_FROM_YOURSELF = "clockInTask.canOnlyChangeTaskOwnerFromYourself";

        /** 转交对象不能是转交前的人 */
        public static final String CAN_ONLY_CHANGE_TASK_OWNER_TO_OTHER_USER = "clockInTask.canOnlyChangeTaskOwnerToOtherUser";

        /** 只能撤销最后一次打卡记录 */
        public static final String NOT_LAST_CLOCK_IN_RECORD = "clockInTask.notLastClockInRecord";

        /** 没有任何打卡记录 */
        public static final String NOT_EXISTS_ANY_CLOCK_IN_RECORD = "clockInTask.notExistsAnyClockInRecord";

        /** 不允许连续撤销 */
        public static final String CANNOT_REVOKE_CONSECUTIVELY = "clockInTask.cannotRevokeConsecutively";

        /** 不允许撤销他人的打卡记录 */
        public static final String CANNOT_REVOKE_OTHERS_RECORD = "clockInTask.cannotRevokeOthersRecord";

        /** 不能撤销超过10分钟的记录 */
        public static final String CANNOT_REVOKE_RECORD_OVER_10_MINUTES = "clockInTask.cannotRevokeRecordOver10Minutes";

        /** 不能撤销，已有值守打卡记录 */
        public static final String CANNOT_REVOKE_ON_DUTY_RECORD_EXISTS = "clockInTask.cannotRevokeOnDutyRecordExists";

        /** 您处【%s】单操作偏离30分钟未执行打卡，请您及时介入了解情况，谢谢! */
        public static final String CALLPLAN_CONTENT_OFFSET_SUMMARY_CALL = "callPlan.content.offset.summary.call";

        /** 您处【%s】单操作未执行值守打卡，请您及时介入了解情况，谢谢！ */
        public static final String CALLPLAN_CONTENT_SUMMARY_CALL = "callPlan.content.summary.call";

        /** 您有{0}个网络变更操作需要审批，请及时完成，谢谢! */
        public static final String TECHNICAL_APPROVAL_TIMEOUT_NOTIFICATION = "technical.approval.timeout.notification";

        /** 【紧急操作、封网管控期操作】变更为是，将会触发行政领导审核 */
        public static final String CHANGE_URGENT_OR_CONTROL_APPROVAL = "change.urgent.or.control.approval";

        /** 【确认操作开始时间、确认操作结束时间】变更，将会触发远程中心负责人确认审核 */
        public static final String CHANGE_CONFIRMATION_TIME_APPROVAL = "change.confirmation.time.approval";

        /** 【紧急操作】变更为是，将会触发紧急操作行政审核 */
        public static final String CHANGE_URGENT_APPROVAL = "change.urgent.approval";

        /** 操作单号 */
        public static final String EXPORT_OPERATION_CODE = "clockInTask.export.operationCode";

        /** 操作主题 */
        public static final String EXPORT_OPERATION_SUBJECT = "clockInTask.export.operationSubject";

        /** 批次号 */
        public static final String EXPORT_BATCH_NO = "clockInTask.export.batchNo";

        /** 客户网络名称 */
        public static final String EXPORT_CUSTOMER_NETWORK_NAME = "clockInTask.export.customerNetworkName";

        /** 产品经营团队 */
        public static final String EXPORT_PRODUCT_TEAM = "clockInTask.export.productTeam";

        /** 产品线 */
        public static final String EXPORT_PRODUCT_LINE = "clockInTask.export.productLine";

        /** 产品大类 */
        public static final String EXPORT_PRODUCT_MAIN_CATEGORY = "clockInTask.export.prodMainCategory";

        /** 产品小类 */
        public static final String EXPORT_PRODUCT_SUB_CATEGORY = "clockInTask.export.prodSubCategory";

        /** 风险等级 */
        public static final String EXPORT_RISK_EVALUATION = "clockInTask.export.riskEvaluation";

        /** 操作等级 */
        public static final String EXPORT_OPERATION_LEVEL = "clockInTask.export.operationLevel";

        /** 营销 */
        public static final String EXPORT_MARKETING = "clockInTask.export.marketing";

        /** 代表处 */
        public static final String EXPORT_RESPONSIBLE_DEPT = "clockInTask.export.responsibleDept";

        /** 操作类型 */
        public static final String EXPORT_OPERATION_TYPE = "clockInTask.export.operationType";

        /** 打卡时间 */
        public static final String RECORD_EXPORT_CLOCK_IN_TIME = "clockInRecord.export.clockInTime";

        /** 打卡人 */
        public static final String RECORD_EXPORT_OPERATOR = "clockInRecord.export.operator";

        /** 打卡项 */
        public static final String RECORD_EXPORT_CLOCK_IN_OPTION = "clockInRecord.export.clockInOption";

        /** 是否已撤销 */
        public static final String RECORD_EXPORT_REVOKED = "clockInRecord.export.revoked";

        /** 操作开始时间 */
        public static final String EXPORT_OPERATION_START_TIME = "clockInTask.export.operationStartTime";

        /** 操作结束时间 */
        public static final String EXPORT_OPERATION_END_TIME = "clockInTask.export.operationEndTime";

        /** 打卡状态 */
        public static final String EXPORT_CLOCK_IN_STATUS = "clockInTask.export.clockInStatus";

        /** 操作负责人 */
        public static final String EXPORT_OPERATING_SUPERVISOR = "clockInTask.export.operatingSupervisor";

        /** 转交处理人 */
        public static final String EXPORT_TASK_RECEIVER = "clockInTask.export.taskReceiver";

        /** 值守时长 */
        public static final String EXPORT_ON_DUTY_DURATION_HOURS = "clockInTask.export.onDutyDurationHours";

        /** 是否需要复盘 */
        public static final String EXPORT_CLOCK_IN_REVIEWS_FLAG = "clockInTask.export.clockInReviewsFlag";

        /** 时区 */
        public static final String EXPORT_TIME_ZONE = "clockInTask.export.timeZone";

        /** 导出表格名称（打卡单列表） */
        public static final String EXPORT_FILENAME = "clockInTask.export.filename";

        /** 导出表格名称（打卡记录列表） */
        public static final String RECORD_EXPORT_FILENAME = "clockInRecord.export.filename";

        /** 导出异常 */
        public static final String EXPORT_ERROE = "export.error";

        /**
         * 审批人配置 - 网络处总工配置 - 删除成功提示
         */
        public static final String CONFIG_OPERATION_DELETE_SUCCESS = "config.operation.delete.success";
    }

    public static class PowerSpeciModel{

        /** 序号 */
        public static final String PSM_SEQ_NUM = "power.speci.model.export.seqNum";

        /** 单据编号 */
        public static final String PSM_ORDER_NO = "power.speci.model.export.orderNo";

        /** 操作主题 */
        public static final String PSM_OPERATION_SUBJECT = "power.speci.model.export.operationSubject";

        /** 任务状态 */
        public static final String PSM_ASSIGNMENT_STATUS = "power.speci.model.export.assignmentStatus";

        /** 操作类型 */
        public static final String PSM_OPERATION_TYPE = "power.speci.model.export.operationType";

        /** 代表处 */
        public static final String PSM_ORESPONSIBLE_DEPT = "power.speci.model.export.oresponsibleDept";

        /** 产品经营团队 */
        public static final String PSM_PRODUCT_TEAM = "power.speci.model.export.productTeam";

        /** 产品线 */
        public static final String PSM_PRODUCT_LINE = "power.speci.model.export.productLine";

        /** 产品大类 */
        public static final String PSM_PROD_MAIN_CATEGORY = "power.speci.model.export.prodMainCategory";

        /** 产品小类 */
        public static final String PSM_PROD_SUB_CATEGORY = "power.speci.model.export.prodSubCategory";

        /** 计划操作开始时间 */
        public static final String PSM_OPERATION_START_TIME = "power.speci.model.export.operationStartTime";

        /** 物料名称 */
        public static final String PSM_MATERIAL_NAME = "power.speci.model.export.materialName";

        /** 硬件版本 */
        public static final String PSM_HARDWARE_VERSION = "power.speci.model.export.hardwareVersion";

        /** 数量 */
        public static final String PSM_COUNT = "power.speci.model.export.psmCount";

        /** 序列号 */
        public static final String PSM_SERIAL_NUMBER = "power.speci.model.export.serialNumber";

        /** 软件版本 */
        public static final String PSM_SOFTWARE_VERSION = "power.speci.model.export.softwareVersion";

        /** 生产批次 */
        public static final String PSM_PRODUCTION_BATCH = "power.speci.model.export.productionBatch";

        /** 备注说明 */
        public static final String PSM_DESCRIPTION = "power.speci.model.export.psmDescription";

        /** 导出表格名称（能源查询列表） */
        public static final String EXPORT_POWER_SPECI_MODEL_FILENAME = "power.speci.model.filename";
    }

    /**
     * 配置中心 - 邮件推送配置
     */
    public static class EmailSendGroup {

        /** 提示信息 - 群组不存在 */
        public static final String GROUP_NOT_EXIST = "emailSendGroup.notice.groupNotExist";

        /** 提示信息 - 启用数据无法删除 */
        public static final String CANNOT_DELETE_ENABLED_GROUP = "emailSendGroup.notice.cannotDeleteEnabledGroup";

        /** 导出文件名 - 默认抄送配置 */
        public static final String EXPORT_FILE_NAME = "emailSendGroup.exportTitle";

        /** 表头 - 抄送类型 */
        public static final String TITLE_CC_TYPE = "emailSendGroup.ccType";

        /** 表头 - 产品经营团队 */
        public static final String TITLE_PRODUCT_TEAM = "ProductOperationTeam";

        /** 表头 - 产品线 */
        public static final String TITLE_PRODUCT_LINE = "ProductLine";

        /** 表头 - 产品大类 */
        public static final String TITLE_PRODUCT_CATEGORY = "ProductCategory";

        /** 表头 - 产品小类 */
        public static final String TITLE_PRODUCT_SUBCATEGORY = "ProductSubcategory";

        /** 表头 - 所属组织 */
        public static final String TITLE_ORGANIZATION = "organization1";

        /** 表头 - 政企 */
        public static final String TITLE_GOVERNMENT = "government.enterprise";

        /** 表头 - 抄送人员 */
        public static final String TITLE_CC_PERSON = "emailSendGroup.ccPerson";

        /** 表头 - 备注说明 */
        public static final String TITLE_REMARKS = "remarks";

        /** 表头 - 状态 */
        public static final String TITLE_STATUS = "status.";

        /** 表头 - 更新人 */
        public static final String TITLE_UPDATE_BY = "UpdatedBy";

        /** 表头 - 更新时间 */
        public static final String TITLE_UPDATE_TIME = "UpdatedTime";
    }

    /**
     * 配置中心 - 用户自定义群组配置
     */
    public static class UserGroup {

        /** 提示信息 - 群组不存在 */
        public static final String GROUP_NOT_EXIST = "userGroup.notice.groupNotExist";

        /** 提示信息 - 重名群组 */
        public static final String SAME_NAME_GROUP = "userGroup.notice.sameNameGroup";

        /** 提示信息 - 群组人数超上限 */
        public static final String MEMBERS_OVER_LIMIT = "userGroup.notice.membersOverLimit";
    }

    /**
     * 提示信息 - 移动端审批
     */
    public static class MobileApprove {
        /** 任务不存在 */
        public static final String ASSIGNMENT_NOT_EXISTS = "MobileApprove.AssignmentNotExists";

        /** 异常任务类型 */
        public static final String ERROR_ASSIGNMENT_TYPE = "MobileApprove.ErrorAssignmentType";

        /** 单据不存在 */
        public static final String ORDER_NOT_EXISTS = "MobileApprove.OrderNotExists";

        /** 审批结果不存在 */
        public static final String APPROVAL_RESULT_NOT_EXISTS = "MobileApprove.ApprovalResultNotExists";

        /** 审批结果异常 */
        public static final String APPROVAL_RESULT_ERROR = "MobileApprove.ApprovalResultError";

        /** 审批节点异常 */
        public static final String APPROVAL_NODE_ERROR = "MobileApprove.ApprovalNodeError";

        /** 节点已审批 */
        public static final String NODE_APPROVED = "MobileApprove.NodeApprovedError";

        /** 审批意见不存在 */
        public static final String APPROVAL_OPINION_NOT_EXISTS = "MobileApprove.ApprovalOpinionNotExists";

        /** 办事处产品经理审核标识不存在 */
        public static final String NEED_PRODUCT_MANAGER_APPROVE_NOT_EXISTS = "MobileApprove.NeedProductManagerApproveFlagNotExists";

        /** 升级至网络处标识不存在 */
        public static final String NEED_NETWORK_DEPARTMENT_APPROVE_NOT_EXISTS = "MobileApprove.NeedNetworkDepartmentApproveFlagNotExists";

        /** 审核人 / 审核组不存在 */
        public static final String REVIEWER_OR_APPROVE_TEAM_NOT_EXISTS = "MobileApprove.ReviewerOrApproveTeamNotExists";


        /**确认操作结束时间不可早于确认操作开始时间 */
        public static final String ENDTIME_MUST_LATER_THAN_STARTTIME = "endTime.must.later.than.startTime";

        /**确认操作开始时间不可早于系统当前时间 */
        public static final String STARTTIME_MUST_LATER_THAN_CURRENTTIME = "startTime.must.later.than.currentTime";

        /**时区不能为空 */
        public static final String TIME_ZONE_EMPTY = "time.zone.empty";

        /** 时区为空或者错误 */
        public static final String TIME_ZONE_EMPTY_OR_ERROR = "time.zone.empty.or.error";

        /** 操作时段存在冲突 */
        public static final String IDOP_BATCH_TIME_CONFLICT = "idop.batch.summary.time.conflict";

        /**
         * 非紧急操作，确认开始时间不得早于第一次提交时间，或早于首次提单时间后的72小时
         */
        public static final String NOT_URGENT_FORBIDDEN_72_HOURS = "not.urgent.Forbidden.72.hours";

        /**当前时间超过原来操作计划开始时间，提交失败 */
        public static final String CURRENT_TIME_LATER_PLANNED_START_TIME = "current.time.later.planned.start.time";

        /**
         * 非紧急操作，计划开始时间不得早于第一次提交时间，或早于首次提单时间后的72小时
         */
        public static final String NOT_CHANGE_URGENT_FORBIDDEN_72_HOURS = "not.urgent.change.Forbidden.72.hours";

        /**
         * 非紧急操作保障类操作，计划开始时间不得早于第一次提交时间，或早于首次提单时间后的24小时
         */
        public static final String NOT_CHANGE_URGENT_FORBIDDEN_24_HOURS = "not.urgent.change.Forbidden.24.hours";

        /**
         * 计划操作结束时间不可早于计划操作开始时间
         */
        public static final String CHANGE_ENDTIME_MUST_LATER_THAN_STARTTIME = "change.endTime.must.later.than.startTime";

        /**
         * 计划操作开始时间不可早于系统当前时间
         */
        public static final String CHANGE_STARTTIME_MUST_LATER_THAN_CURRENTTIME = "change.startTime.must.later.than.currentTime";

        /**
         * 非紧急操作计划操作开始时间不可早于系统当前时间往后72h内
         */
        public static final String CHANGE_NOT_URGENT_FORBIDDEN_72_HOURS = "change.not.urgent.Forbidden.72.hours";

    }

    /**
     * 邮件、iCenter正文内容
     */
    public static class EmailNotice {

        /** {单据编号}/{操作主题}（批次{批次号}），已进入{复盘状态}，请知悉！ */
        public static final String MESSAGE_CLOCKIN_REVIEWS_NOTIFY_CONTENT = "message.clockIn.reviews.notify.content";

        /** {单据编号}/{操作主题}（批次{批次号}），已指派给您，请及时处理！ */
        public static final String MESSAGE_CLOCKIN_REVIEWS_PRESS_CONTENT = "message.clockIn.reviews.press.content";

        /** {单据编号}/{操作主题}{是否紧急}已进入{下一流程节点名称环节}环节，请您及时处理！ */
        public static final String MESSAGE_CHANGE_PRESS_CONTENT = "message.change.press.content";

        /** {操作者}提交{单据编号}/{操作主题}{是否紧急} */
        public static final String MESSAGE_CHANGE_SUBMIT_APPLY_CONTENT = "message.change.submit.apply.content";

        /** {单据编号}/{操作者}审核通过{操作主题}{是否紧急} */
        public static final String MESSAGE_CHANGE_APPROVAL_PASS_CONTENT = "message.change.approval.pass.content";

        /** {操作者}审核驳回{单据编号}/{操作主题}{是否紧急} */
        public static final String MESSAGE_CHANGE_APPROVAL_REJECT_CONTENT = "message.change.approval.reject.content";

        /** {操作者}撤回{单据编号}/{操作主题}{是否紧急} */
        public static final String MESSAGE_CHANGE_APPROVAL_REVOKE_CONTENT = "message.change.approval.revoke.content";

        /** {单据编号}/{操作主题}（批次{批次号}）将于北京时间{当前时间}进行{操作类型}网络变更操作，请相关远程支持专家保持通讯畅通！  */
        public static final String MESSAGE_CHANGE_PUB_NOTIFY_CONTENT = "message.change.pub.notify.content";

        /** 【{单据编号}/{操作主题}{是否紧急操作}（批次{批次号}）】{操作者}通过操作结果审核 */
        public static final String MESSAGE_BATCH_APPROVAL_PASS_CONTENT = "message.batch.approval.pass.content";

        /** {操作者}审核驳回{单据编号}/{操作主题}{是否紧急操作}（批次{批次号}），请您及时处理！*/
        public static final String MESSAGE_BATCH_APPROVAL_REJECT_CONTENT = "message.batch.approval.reject.content";

        /** {单据编号}/{操作主题}{是否紧急操作}（批次{批次号}）已进入{当前节点名称}环节，请您及时处理！*/
        public static final String MESSAGE_BATCH_PRESS_CONTENT = "message.batch.press.content";

        /**【{单据编号}/{操作主题}{是否紧急操作}（批次{批次号}）】{操作者}提交反馈操作结果 */
        public static final String MESSAGE_BATCH_FEEDBACK_RESULT_CONTENT = "message.batch.feedback.result.content";

        /** {单据编号}/{操作主题}（批次{批次号}） */
        public static final String MESSAGE_BATCH_CANCEL_SUSPENDED_CONTENT = "message.batch.cancel.suspended.content";

        /** {单据编号}/{操作主题} */
        public static final String MESSAGE_CHANGE_MAINTASK_CLOSE_CONTENT = "message.change.mainTask.close.content";

        /** 【iTech Cloud 技术管理任务】{创建人}创建了任务：【{单据编号}/{任务名称}】，您被指派为任务的责任人，请及时处理！*/
        public static final String MESSAGE_MANAGETASK_CREATE_PRESS_CONTENT = "message.manageTask.create.press.content";

        /** 【iTech Cloud 技术管理任务】${创建人}创建了任务：【{单据编号}/{任务名称}】，您被指派为任务的知会人，请知悉！*/
        public static final String MESSAGE_MANAGETASK_CREATE_NOTICE_CONTENT = "message.manageTask.create.notice.content";

        /** 【iTech Cloud 技术管理任务】【{单据编号}/{任务名称}】由{操作者}发起关闭申请，请及时处理！*/
        public static final String MESSAGE_MANAGETASK_APPLY_CLOSE_PRESS_CONTENT = "message.manageTask.apply.close.press.content";

        /** 【iTech Cloud 技术管理任务】【{单据编号}/{任务名称}】由{操作者}发起关闭申请，请知悉！*/
        public static final String MESSAGE_MANAGETASK_APPLY_CLOSE_NOTICE_CONTENT = "message.manageTask.apply.close.notice.content";

        /** 【iTech Cloud 技术管理任务】验收人{操作者}已同意关闭【{单据编号}/{任务名称}】,请知悉! */
        public static final String MESSAGE_MANAGETASK_PASS_CLOSE_CONTENT = "message.manageTask.pass.close.content";

        /** 【&{任务单号}】【&{任务分类}】&{任务名称}由&{验收人}驳回关闭申请，请您及时处理 */
        public static final String MESSAGE_MANAGETASK_REJECT_CONTENT = "message.manageTask.reject.content";

        /** 【iTech Cloud 技术管理任务】【{单据编号}/{任务名称}】责任人发生变更，由{旧责任人}变更为{新责任人} */
        public static final String MESSAGE_MANAGETASK_TRANSFER_CONTENT = "message.manageTask.transfer.content";

        /** 【iTech Cloud {任务类型}】【{单据编号}/{任务名称}】由{操作者}发起跟催，请及时处理 */
        public static final String MESSAGE_COMMON_LIST_PRESS_CONTENT = "message.common.list.press.content";

        /** 保障任务-${taskName}(任务单号：${taskCode})已由${operator}解除，请关注 */
        public static final String MESSAGE_MULTI_PROD_GUARANTEE_RELEASE_CONTENT = "message.multi.prod.guarantee.release.content";

        /** 保障任务-${taskName}(任务单号：${taskCode})已由${operator}删除，请关注 */
        public static final String MESSAGE_MULTI_PROD_GUARANTEE_DELETE_CONTENT = "message.multi.prod.guarantee.delete.content";

        /** 保障任务-${taskName}(任务单号：${taskCode})已由${operator}废止，请关注 */
        public static final String MESSAGE_MULTI_PROD_GUARANTEE_ABOLISH_CONTENT = "message.multi.prod.guarantee.abolish.content";

        /** 保障任务 - {单据编号}/{taskName}已由{operator}指派给您，请及时处理 */
        public static final String MESSAGE_MULTI_PROD_GUARANTEE_CREATE_INFOM_CONTENT = "message.multi.prod.guarantee.create.infom.content";

        /** 保障任务-${taskName}(任务单号：${taskCode})已由${operator}转交给您，请及时处理 */
        public static final String MESSAGE_MULTI_PROD_GUARANTEE_TRANSFER_CONTENT = "message.multi.prod.guarantee.transfer.content";

        /** 【故障管理任务知会】{单据编号}/{任务主题} 已由{操作者} 提交客户满意度 */
        public static final String MESSAGE_FAULTTASK_SUBMIT_SATISFACTION_CONTENT = "message.faultTask.submit.satisfaction.content";

    }

    /**
     * 提示信息 - CCN 默认授权文件申请
     */
    public static class ApplyCcnAuthorization {
        /** 操作类型 */
        public static final String OPERATION_TYPE = "ApplyCcnAuthorization.OperationType";

        /** 申请单不存在 */
        public static final String APPLICATION_NOT_EXISTS = "ApplyCcnAuthorization.ApplicationNotExists";

        /** 申请单状态异常 */
        public static final String APPLICATION_STATUS_ERROR = "ApplyCcnAuthorization.ApplicationStatusError";

        /** 申请人异常 */
        public static final String APPLICATION_APPLY_BY_ERROR = "ApplyCcnAuthorization.ApplicationApplyByError";
    }

    public static final String DATA_NOT_FOUND = "data.not.found";

    public static final String DATA_DELETE_NOT_DISABLED = "data.delete.not.disabled";

    public static final String DATA_BATCH_DELETE_NOT_DISABLED = "data.batch.delete.not.disabled";

    public static final String DATA_ENABLED_NOT_DISABLED = "data.enable.not.disabled";

    public static final String DATA_DISABLED_NOT_ENABLED = "data.disable.not.enabled";

    public static final String MAX_NUM_RECEIVER_20 = "max.num.receiver.20";

    public static final String INTERGRTION_ATLEAST_ONE_SELECT = "integration.related.product.atleast.one.select";

    public static final String MAIN_PRODUCT_ATLEAST_ONE_SELECT = "main.product.atleast.one.select";

    public static final String MAIN_PRODUCT_CAN_NOT_DELETE = "main.product.can.not.delete";

    // 行政领导审批提示：建议以下层级的角色完成审核：{0}
    public static final String ADMIN_LEADER_APPROVE_NOTE = "admin.leader.approve.note";

    /** 申请CCN权限反馈操作结果 */
    public static final String APPLY_CCN_AUTHORIZATION_FEEDBACK_AUTHORIZATION_RESULTS = "ApplyCcnAuthorization.Feedback.Authorization.Results";

    /** 申请默认授权文件*/
    public static final String APPLY_DEFAULT_AUTHORIZATION_FILE = "ApplyCcnAuthorization.Default.Authorization.File";

    public static final String PERMISSION_APPLICATION_OPERATION_STATUS_ERROR = "pa.operation.status.error";

    /** 代表处不能重复选择  */
    public static final String PERMISSION_APPLICATION_ORGANIZATION_REPEATED_ERROR = "pa.organization.repeated.error";

    /** 申请人撤销 */
    public static final String APPLICANT_RETURN_APPROVAL = "applicant.return.approval";

    /**
     * 审核结果
     */
    public static final String REVIEW_RESULT = "review_result";

    /**
     * BCN需要多产品联动保障
     */
    public static final String BCN_MULTI_PRODUCT_LINKAGE_SUPPORT = "bcn_multi_product_linkage_support";

    /**
     * 远程中心执行
     */
    public static final String REMOTE_CENTER_ENABLED_OR_NOT = "remote_center_enabled_or_not";

    /**
     * 提交操作方案
     */
    public static final String SUBMIT_OPERATION_PLAN = "submit_operation_plan";

    /**
     * 当前数据已存在
     */
    public static final String THE_CURRENT_DATA_ALREADY_EXISTS = "the.current.data.already.exists";

    /**
     * 操作等级=关键
     */
    public static final String CHANGE_ORDER_HIGH_RISK_KEY_OPERATION = "change.order.high.risk.key.operation";

    /**
     * 风险评估>=三星
     */
    public static final String CHANGE_ORDER_HIGH_RISK_RISK_GREATER_THREE = "change.order.high.risk.risk.greater.three";

    /**
     * 重要程度+风险评估≥五星
     */
    public static final String CHANGE_ORDER_HIGH_RISK_IMPORTANCE_RISK_GREATER_FIVE
            = "change.order.high.risk.importance.risk.greater.five";


    /**
     * 高风险操作，操作负责人、操作人员、交叉检查人员，每个角色均至少保留一行人员信息（{0}）
     */
    public static final String HIGH_RISK_OPERATION_SUPERVISOR_OPERATOR_CROSSCHECKER_NOT_NULL
            = "high.risk.operation.supervisor.operator.crosschecker.not.null";

    /**
     * 打卡复盘任务分类必须是整改或者横推
     */
    public static final String CLOCK_REVIEWS_TASK_CATEGORY_ERROR = "clock.reviews.task.category.error";

    /**
     * 整改任务不能为空
     */
    public static final String RELATION_RECTIFY_ASSIGNMENT_NULL = "relation.rectify.assignment.null";

    /**
     * 保障单任务网元清单只能上传一个操作批次
     */
    public static final String GUARANTEE_TASK_ONLY_ONE_BATCH = "guarantee.task.only.one.batch";

    /**
     * 是否紧急操作
     */
    public static final String IS_EMERGENCY_OPERATION = "is.emergency.operation";

    /**
     * 是否封网管控期操作
     */
    public static final String IS_MANAGEMENT_CONTROL_PERIOD = "is.management.control.period";

    /**
     * 确认操作开始时间
     */
    public static final String START_TIME_CONFIRMATION_OPERATION = "start.time.confirmation.operation";

    /**
     * 确认操作结束时间
     */
    public static final String END_TIME_CONFIRMATION_OPERATION = "end.time.confirmation.operation";

    /**
     * 移除
     */
    public static final String REMOVE = "remove";

    /**
     * 新增
     */
    public static final String ADD = "add";

    /**
     * 维护前字段格式错误，请按规则填写: ORG0000000/ORG0002700/ORG2230110/ORG2230111
     */
    public static final String FIELD_BEFORE_MAINTENANCE = "field.before.maintenance";

    /**
     * 维护后字段格式错误，请按规则填写: ORG0000000/ORG0002700/ORG2230110/ORG2230111
     */
    public static final String FIELD_AFTER_MAINTENANCE = "field.after.maintenance";

    /**
     * 维护后字段错误，找不到对应组织
     */
    public static final String NOT_FIND_AFTER_MAINTENANCE = "not.find.after.maintenance.orgnization";

    /**
     * 原因归类字段输入校验-请选择具体原因
     */
    public static final String PLEASE_CHOOSE_SPECIFIC_REASON = "please.choose.specific.reason";

    /** 变更通告 */
    public static final String UPDATE_NOTIFICATION = "update.notification";

    /* 批次任务已生成 */
    public static final String BATCH_TASK_HAS_BEEN_CREATED = "batch.task.has.been.created";

    /** 任务状态已变更，请刷新后再操作 */
    public static final String TASK_STATUS_CHANGED_PLEASE_REFRESH = "task.status.changed.please.refresh";

    /** 故障管理任务_故障分析报告上传提交提示语 */
    public static final String FAULT_ANALYSIS_REPORT_UPLOAD_CONFIRM = "fault.analysis.report.upload.confirm";

    public static class OperationLog{

        /** 会签：同意or不同意 */
        /** 同意 */
        public static final String APPROVE_RESULT_AGREE = "agree";

        /** 不同意 */
        public static final String APPROVE_RESULT_DISAGREE = "disagree";

        /** 操作日志页签标题 */
        public static final String LOG_TAB_TITLE = "log.tab";

        /** 【审核结果】{0}; 【审核意见】{1} */
        public static final String LOG_COMMON = "log.common";

        /** 【由{0}自动同步信息】{1} 说明：其他的{1}从主单据直接取到操作日志拼接  */
        public static final String LOG_GUARANTEE_APPROVER= "log.guarantee.approver";

        /** 批次- 待反馈操作结果 【操作结果】{0};【备注说明】{1}*/
        public static final String LOG_BATCH_RESULT_TOBE_BACK_EXT= "log.batch.result.tobe.back.ext";

        /** 批次 - 系统自动节点 -发布通告   成功发布操作通告*/
        public static final String LOG_SYSTEM_NODE_RELEASED_NOTICE= "log.system.node.released.notice";

        /** 批次 - 发布通告 【通告说明】{0} */
        public static final String LOG_RELEASE_NOTIFY= "log.release.notify";

        /** 提交失败，请保存后刷新再重新提交 */
        public static final String SUBMISSION_FAILED_PLEASE_SAVE_REFRESH_AGAIN = "submission.failed.please.save.refresh.again";

        /*
         * 调用人员资质管理服务异常
         */
        public static final String   SPECIFICATION_EXAM_ERVICE_FAILED = "specification.exam.ervice.failed";
    }

    /** 网络变更单异步导出提示语 */
    public static final String NETWORK_CHANGE_TASK_ASYNC_EXPORT_CONFIRM = "network.change.task.async.export.confirm";

    /** 网络变更单同步导出提示语 */
    public static final String NETWORK_CHANGE_TASK_SYNC_EXPORT_CONFIRM = "network.change.task.sync.export.confirm";

    /**
     * 操作人员校验失败，个数大于1
     */
    public static final String CHECK_OPERATORS_COUNT_FAILED = "check.operators.count.failed";

    /**
     * 请勿重复操作
     */
    public static final String DO_NOT_REPEAT_THE_OPERATION = "repeat.operation";

    /**
     * 操作计划开始时间和操作计划结束时间不能为空
     */
    public static final String OPERATION_START_AND_END_TIME_CANNOT_NULL = "operation.start.and.end.time.cannot.null";

    /**
     * 所有批次里，最迟的批次 计划结束时间 - 最早的批次 计划开始时间 <= 7天
     */
    public static final String OPERATION_START_AND_END_TIME_MAX_7_DAY = "operation.start.and.end.time.max.7.day";

    /**
     * 提交失败，请保证至少存在一个产品型号属于产品分类下
     */
    public static final String PRODUCT_MODEL_BELONG_PRODUCT_CATEGORY = "product.model.belong.product.category";

    /* 操作计划已生成 */
    public static final String OPERATION_PLAN_HAS_BEEN_CREATED = "operation.plan.has.been.created";
}
