package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import lombok.Getter;

import java.util.Arrays;


/**
 * 审批页面枚举
 * 技术管理任务、技术管理子任务、网络变更单、批次任务等
 * 1.网络变更单、分包商网络变更单只有一个审批页面
 * 2.技术管理任务、批次任务会根据不同的状态跳转到不同的审批页面
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/9/4
 */
@Getter
public enum ApprovePageEnum {

    /**
     * 网络变更单页面
     */
    NETWORK_CHANGE_PAGE(AssignmentTypeEnum.NETWORK_CHANGE, "PAGE1010486823108829185","oc_apply"),

    /**
     * 分包商网络变更单页面
     */
    SUBCONTRACTOR_NETWORK_CHANGE_PAGE(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, "PAGE1012012457422307329","subcontractor_oc"),

    /**
     * 技术管理任务 详情页面
     */
    TECHNOLOGY_MANAGEMENT_EXECUTE_PAGE(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT, AssignmentStatusEnum.EXECUTE, "PAGE1004349069858136065","createTechnicalManagementTask"),
    /**
     * 技术管理任务 审批页面
     */
    TECHNOLOGY_MANAGEMENT_APPROVE_PAGE(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT, AssignmentStatusEnum.APPROVE, "PAGE1007594852601933825","createTechnicalManagementTask"),

    /**
     * 技术管理子任务 详情页面
     */
    TECHNOLOGY_MANAGEMENT_SUB_EXECUTE_PAGE(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB, AssignmentStatusEnum.EXECUTE, "PAGE1010949797683027969","manage_sub_task_flow"),
    /**
     * 技术管理子任务 审批页面
     */
    TECHNOLOGY_MANAGEMENT_SUB_APPROVE_PAGE(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB, AssignmentStatusEnum.APPROVE, "PAGE1004792844811550724","manage_sub_task_flow"),

    /**
     * 批次任务
     */
    NETWORK_CHANGE_BATCH_PAGE(AssignmentTypeEnum.NETWORK_CHANGE_BATCH,"PAGE1093827357111214080","batch_network_assignment"),

    /**
     * 分包商批次任务
     */
    SUBCONTRACT_NETWORK_CHANGE_BATCH(AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,"PAGE1100835855524651009","subcontractor_batch_task"),


    /** 故障管理-执行中 */
    FAULT_MANAGE_EXECUTE(AssignmentTypeEnum.FAULT_MANAGEMENT, AssignmentStatusEnum.EXECUTE, "PAGE1022551814320730116", "fault_management_task"),

    /** 打卡复盘 */
    CLOCK_REVIEW_PAGE(AssignmentTypeEnum.CLOCK_REVIEW, "PAGE1074421707979010049", "clock_in_review"),

    /** 权限申请 */
    PERMISSION_APPLICATION(AssignmentTypeEnum.PERMISSION_APPLICATION, "PAGE1050778320520175616", "permission_application"),

    /** 故障管理 */
    FAULT_MANAGEMENT(AssignmentTypeEnum.FAULT_MANAGEMENT, "PAGE1022551814320730116", "fault_management_task"),

    /** 操作计划 */
    OPERATION_PLAN_TASK(AssignmentTypeEnum.OPERATION_PLAN_TASK, "PAGE1148996033356578822", "plan_operation_order")
    ;

    private final AssignmentTypeEnum assignmentTypeEnum;

    private final String pageId;

    private final String bizObjectCode;

    private AssignmentStatusEnum assignmentStatusEnum;

    ApprovePageEnum(AssignmentTypeEnum assignmentTypeEnum, String pageId, String bizObjectCode) {
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.pageId = pageId;
        this.bizObjectCode = bizObjectCode;
    }

    ApprovePageEnum(AssignmentTypeEnum assignmentTypeEnum, AssignmentStatusEnum assignmentStatusEnum, String pageId,String bizObjectCode) {
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.assignmentStatusEnum = assignmentStatusEnum;
        this.pageId = pageId;
        this.bizObjectCode = bizObjectCode;
    }

    /**
     * 根据类型获取值，取第一个
     *
     * @param assignmentTypeEnum assignmentTypeEnum
     * @return ApprovePageEnum
     */
    public static ApprovePageEnum getPageIdByType(AssignmentTypeEnum assignmentTypeEnum) {
        return Arrays.stream(ApprovePageEnum.values()).filter(item -> assignmentTypeEnum == item.assignmentTypeEnum).findFirst().orElse(null);
    }


    /**
     * 根据类型获取值，再根据状态取值
     *
     * @param assignmentTypeEnum   assignmentTypeEnum
     * @param assignmentStatusEnum assignmentStatusEnum
     * @return ApprovePageEnum
     */
    public static ApprovePageEnum getPageIdByTypeStatus(AssignmentTypeEnum assignmentTypeEnum, AssignmentStatusEnum assignmentStatusEnum) {
        return Arrays.stream(ApprovePageEnum.values())
                .filter(item -> assignmentTypeEnum == item.assignmentTypeEnum)
                .filter(item -> assignmentStatusEnum == item.getAssignmentStatusEnum())
                .findFirst().orElse(null);
    }
}
