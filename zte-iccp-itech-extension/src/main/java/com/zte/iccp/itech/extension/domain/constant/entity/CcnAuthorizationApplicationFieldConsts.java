package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CcnAuthorizationApplicationFieldConsts {

    /** 单据编号 */
    public static final String AD_NO = "ad_no";

    /** 单据状态 */
    public static final String AD_STATUS = "ad_status";

    /** 责任单位 */
    public static final String ORGANIZATION_ID = "organization_id";

    /** 产品分类 */
    public static final String PRODUCT_ID = "product_id";

    /** 网络ID */
    public static final String NETWORK_ID = "network_id";

    /** 网络名称 */
    public static final String NETWORK_NAME = "network_name";

    /** 客户网络名称 */
    public static final String CUSTOMER_NETWORK_NAME = "customer_network_name";

    /** 申请人 */
    public static final String APPLY_BY = "apply_by";

    /** 申请时间 */
    public static final String APPLY_TIME = "apply_time";

    /** 国家 / 地区 */
    public static final String COUNTRY = "country";

    /** 省 / 州 */
    public static final String PROVINCE = "province";

    /** 客户标识 */
    public static final String ACCN_TYPE = "accn_type";

    /** 局点名称（客户网络名称） */
    public static final String OFFICE_NAME = "office_name";

    /** 默认授权文件key */
    public static final String DEFAULT_AUTHORIZATION_FILE_KEY = "default_authorization_file_key";

    /** 默认授权文件 */
    public static final String DEFAULT_AUTHORIZATION_FILE = "default_authorization_file";

    /** 已上传默认授权文件举证 */
    public static final String UPLOAD_DEFAULT_AUTHORIZATION_FILE = "upload_default_authorization_file";

    /** 知会人 */
    public static final String NOTIFIER = "notifier";

    /** 抄送人 */
    public static final String EMAIL_CC = "email_cc";

    /** 操作账号 */
    public static final String OPERATION_ACCOUNT = "operation_account";
}
