package com.zte.iccp.itech.extension.openapi.mobile.approval.permission;

import com.zte.iccp.itech.extension.ability.permissionapplication.MobileApprovalAbility;
import com.zte.iccp.itech.extension.openapi.mobile.approval.permission.dto.PermissionApplyDetailDto;
import com.zte.iccp.itech.extension.openapi.mobile.approval.permission.vo.PermissionApplyDetailVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 权限申请--移动审批接口
 * <AUTHOR> 10335201
 * @date 2024-11-28 下午2:42
 **/
public class PermissionApplyDetailOpenApi extends AbstractOpenApi {

    /**
     * openApi 移动审批-查询权限申请单详情
     * @param permissionApplyDetailDto 入参-单据编号
     * @return: com.zte.itp.msa.core.model.ServiceData<com.zte.iccp.itech.extension.openapi.mobile.approvaldetail.permission.vo.PermissionApplyDetailVO>
     * @author: 朱小安 10335201
     * @date: 2024/11/28 下午4:55
     */
    public ServiceData<PermissionApplyDetailVO> queryPermissionApplyDetail(@RequestBody PermissionApplyDetailDto permissionApplyDetailDto)  {
        return new ServiceData<PermissionApplyDetailVO>() {{
            setBo(MobileApprovalAbility.queryPermissionApplyDetail(permissionApplyDetailDto));
        }};
    }
}
