package com.zte.iccp.itech.extension.domain.model.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.lang.annotation.*;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.PID;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/23
 */
@Getter
@Setter
public abstract class BaseSubEntity extends BaseEntity {
    @JsonProperty(value = PID)
    private String pid;

    @Inherited
    @Target(ElementType.TYPE)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface Info {
        /**
         * subEntityId
         */
        String value();

        Class<? extends BaseEntity> parent();
    }

    @JsonIgnore
    public void clearEntityValue() {
        setCreateBy(null);
        setCreateTime(null);
        setLastModifiedBy(null);
        setLastModifiedTime(null);
    }
}