package com.zte.iccp.itech.extension.domain.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: 李江斌 10318434
 * @date: 2024/7/17
 */


@ApiModel("人员技能认证信息VO")
@Setter
@Getter
public class Certification {

    @ApiModelProperty("认证分类")
    private String certificationCategoryName;

    @ApiModelProperty("证书等级")
    private String certificationLevelName;

    @ApiModelProperty("认证产品类型")
    private String certificateName;

    @ApiModelProperty("证书号")
    private String certificateCode;

    @ApiModelProperty("认证通过日期")
    private String certificateStartTime;

    @ApiModelProperty("认证有效日期")
    private String certificateEndTime;

}
