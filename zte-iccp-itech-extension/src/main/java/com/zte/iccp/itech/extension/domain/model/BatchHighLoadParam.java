package com.zte.iccp.itech.extension.domain.model;

import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 批次 - 高负荷参数信息
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/6/24
 */
@Setter
@Getter
public class BatchHighLoadParam {

    /**
     * 根据用户id分组操作人员列表
     */
    private Map<String, List<BatchTaskOperator>> groupedByOperatePerson;
    /**
     * 基准日期
     */
    private LocalDate baseDate;

    /**
     * 批次id - 用户工号在操作记录表出现过的数据集
     */
    private Map<String, ClockInRecord> batchIdAndEmployeeIdGrouped;

    /**
     * 批次表id和批次数据集
     */
    private Map<String, IBatchTask> batchTaskIdToMap;

    /**
     * 用户id和范围区间结果map
     */
    private Map<String, Map<LocalDate, Boolean>> userIdToRangeResult;
    /**
     * 时区
     */
    private TimeZoneEnum timeZoneEnum;
}
