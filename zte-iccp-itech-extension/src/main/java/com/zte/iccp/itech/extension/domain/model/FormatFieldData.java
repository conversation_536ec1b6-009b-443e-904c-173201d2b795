package com.zte.iccp.itech.extension.domain.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> 10335201
 * @date 2024-12-20 下午5:38
 **/
@ApiModel("待补充字段转换数据")
@Setter
@Getter
public class FormatFieldData {
    @ApiModelProperty("营销ID")
    private List<String> marketing;

    @ApiModelProperty("代表处ID")
    private List<String> representativeOffice;

    @ApiModelProperty("区域编码")
    private List<String> areaCode;

    @ApiModelProperty("产品ID")
    private List<String> product;

    @ApiModelProperty("网络ID")
    private List<String> network;

    @ApiModelProperty("责任人ID")
    private List<String> responsible;

    @ApiModelProperty("当前处理人ID")
    private List<String> currentProcessor;

    @ApiModelProperty("创建人ID")
    private String createBy;

    @ApiModelProperty("修改人ID")
    private String lastModifiedBy;
}
