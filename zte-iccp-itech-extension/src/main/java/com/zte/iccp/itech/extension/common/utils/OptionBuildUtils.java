package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.RiskEvaluationEnum;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * 操作等级，风险评估自动计算时，根据结果封装组件value
 *
 * <AUTHOR> 10335201
 * @date 2024-08-28 下午4:38
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OptionBuildUtils {

    public static List<Map<String, Object>> getLevel(String val) {
        if (!StringUtils.hasText(val)) {
            return new OptionsBuilder().addOption(new Option(OperationLevelEnum.NORMAL.getValue(),
                    new Text(OperationLevelEnum.NORMAL.getZhCn(), OperationLevelEnum.NORMAL.getEnUs()))).build();
        }
        List<Map<String, Object>> level = new OptionsBuilder().build();
        if(STR_ONE.compareTo(val) >= 0){
            // val小于等于1
            level = new OptionsBuilder().addOption(new Option(OperationLevelEnum.NORMAL.getValue(),
                    new Text(OperationLevelEnum.NORMAL.getZhCn(), OperationLevelEnum.NORMAL.getEnUs()))).build();
        }else if(STR_TWO.equals(val)){
            level = new OptionsBuilder().addOption(new Option(OperationLevelEnum.IMPORTANCE.getValue(),
                    new Text(OperationLevelEnum.IMPORTANCE.getZhCn(), OperationLevelEnum.IMPORTANCE.getEnUs()))).build();
        }else if(STR_THREE.equals(val)){
            level = new OptionsBuilder().addOption(new Option(OperationLevelEnum.CRITICAL.getValue(),
                    new Text(OperationLevelEnum.CRITICAL.getZhCn(), OperationLevelEnum.CRITICAL.getEnUs()))).build();
        }
        return level;
    }

    public static List<Map<String, Object>> getRisk(String val) {
        if (!StringUtils.hasText(val)) {
            return new OptionsBuilder().addOption(new Option(RiskEvaluationEnum.STAR1.getValue(),
                    new Text(RiskEvaluationEnum.STAR1.getZhCn(), RiskEvaluationEnum.STAR1.getEnUs()))).build();
        }
        List<Map<String, Object>> risk = new OptionsBuilder().build();
        if(STR_ONE.compareTo(val) >= 0){
            // val小于等于1
            risk = new OptionsBuilder().addOption(new Option(RiskEvaluationEnum.STAR1.getValue(),
                     new Text(RiskEvaluationEnum.STAR1.getZhCn(), RiskEvaluationEnum.STAR1.getEnUs()))).build();
        }else if(STR_TWO.equals(val)){
            risk = new OptionsBuilder().addOption(new Option(RiskEvaluationEnum.STAR2.getValue(),
                     new Text(RiskEvaluationEnum.STAR2.getZhCn(), RiskEvaluationEnum.STAR2.getEnUs()))).build();
        }else if(STR_THREE.equals(val)){
            risk = new OptionsBuilder().addOption(new Option(RiskEvaluationEnum.STAR3.getValue(),
                     new Text(RiskEvaluationEnum.STAR3.getZhCn(), RiskEvaluationEnum.STAR3.getEnUs()))).build();
        }
        return risk;
    }
}
