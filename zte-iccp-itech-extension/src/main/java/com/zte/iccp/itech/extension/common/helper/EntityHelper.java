package com.zte.iccp.itech.extension.common.helper;

import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.paas.lcap.core.entity.MainEntityType;
import com.zte.paas.lcap.ddm.common.util.MetadataUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/18
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class EntityHelper {
    public static MainEntityType getMainEntityType(Class<? extends BaseEntity> entityClass) {
        return getMainEntityType(getMainEntityId(entityClass));
    }

    public static Class<? extends BaseEntity> getMainClass(Class<? extends BaseSubEntity> entityClass) {
        Class<? extends BaseEntity> parent = getParentClass(entityClass);
        while (BaseSubEntity.class.isAssignableFrom(parent)) {
            //noinspection unchecked
            parent = getParentClass((Class<? extends BaseSubEntity>) parent);
        }
        return parent;
    }

    public static Class<? extends BaseEntity> getParentClass(Class<? extends BaseSubEntity> entityClass) {
        BaseSubEntity.Info info = entityClass.getAnnotation(BaseSubEntity.Info.class);
        if (info == null) {
            throw new IllegalArgumentException(entityClass.getName());
        }

        return info.parent();
    }

    public static String getMainEntityId(Class<? extends BaseEntity> entityClass) {
        if (BaseSubEntity.class.isAssignableFrom(entityClass)) {
            BaseSubEntity.Info info = entityClass.getAnnotation(BaseSubEntity.Info.class);
            if (info == null) {
                throw new IllegalArgumentException(entityClass.getName());
            }

            return getMainEntityId(info.parent());
        }

        BaseEntity.Info info = entityClass.getAnnotation(BaseEntity.Info.class);
        if (info == null) {
            throw new IllegalArgumentException(entityClass.getName());
        }

        return info.value();
    }

    public static String getParentEntityId(Class<? extends BaseSubEntity> entityClass) {
        BaseSubEntity.Info info = entityClass.getAnnotation(BaseSubEntity.Info.class);
        if (info == null) {
            throw new IllegalArgumentException(entityClass.getName());
        }

        Class<? extends BaseEntity> pClass = info.parent();
        return getEntityId(pClass);
    }

    public static String getEntityId(Class<? extends BaseEntity> entityClass) {
        if (BaseSubEntity.class.isAssignableFrom(entityClass)) {
            BaseSubEntity.Info info = entityClass.getAnnotation(BaseSubEntity.Info.class);
            if (info == null) {
                throw new IllegalArgumentException(entityClass.getName());
            }

            return info.value();
        }

        BaseEntity.Info info = entityClass.getAnnotation(BaseEntity.Info.class);
        if (info == null) {
            throw new IllegalArgumentException(entityClass.getName());
        }

        return info.value();
    }

    private static MainEntityType getMainEntityType(String mainEntityId) {
        return MetadataUtils.getMainEntityType(mainEntityId, ContextHelper.getAppId());
    }
}