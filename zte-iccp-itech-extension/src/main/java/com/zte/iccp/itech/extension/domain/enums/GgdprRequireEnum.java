package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 满足GDPR要求
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum GgdprRequireEnum implements SingletonTextValuePairsProvider {
    /**
     * 上传信息满足GDPR要求、不含可逆的个人数据信息
     */
    GDPR_REQUIRE("1", "上传信息满足GDPR要求、不含可逆的个人数据信息", "The uploaded information meets the GDPR requirements, and reversible personal data information is not included."),
    ;

    private final String value;

    private final String zhCn;

    private final String enUs;

}
