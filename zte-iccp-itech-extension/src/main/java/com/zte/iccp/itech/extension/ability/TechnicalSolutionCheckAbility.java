package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.entity.OrderBy;
import com.zte.iccp.itech.extension.common.helper.entity.OrderEnum;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.TechnicalSolutionCheck;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.domain.model.subentity.StandardActionCheck;
import com.zte.iccp.itech.extension.domain.model.subentity.TechSolutionCheck;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.dto.orm.Range;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.domain.constant.entity.TechnicalSolutionCheckConsts.*;

/**
 * 技术方案检查/核心网产品外场质量保证规范动作基础资料查询
 *
 * <AUTHOR> 10335201
 * @date 2024-05-15 上午11:10
 **/
public final class TechnicalSolutionCheckAbility {
    public static List<TechnicalSolutionCheck> queryTechnicalSolutionCheckList(String checkNode) {
        List<String> fields = Lists.newArrayList(CHECK_CODE, CHECK_CONTENT_ZH, CHECK_CONTENT_EN,
                GROUP_ONE_ZH, GROUP_ONE_EN, CHECK_NODE, IS_INVALIDATED, SORT);
        List<IFilter> filters = Lists.newArrayList(new Filter(CHECK_NODE, Comparator.EQ, checkNode));
        filters.add(new Filter(IS_INVALIDATED, Comparator.IS_NULL, null));
        Range range = new Range(CommonConstants.INTEGER_999);
        List<TechnicalSolutionCheck> technicalSolutionChecks = QueryDataHelper
                .query(TechnicalSolutionCheck.class, fields, filters, range, new OrderBy(SORT, OrderEnum.ASC));
        return !CollectionUtils.isEmpty(technicalSolutionChecks) ? technicalSolutionChecks : Lists.newArrayList();
    }

    /*
     * 保存checkList
     * */
    public static void saveCheckList(String changeOrderId, String productIdPath, String operationTypeGroup) {
        // 核心网产品外场质量保证规范动作checklist
        List<TechnicalSolutionCheck> ccnOutFieldCheckList = queryTechnicalSolutionCheckList(NODE_OF_OPERATION_APPLY_CCN);
        List<String> ccnPathids = ConfigHelper.get(Arrays.asList(CCN_CCN_PROD_ID_PATH, CCN_SSP_PROD_ID_PATH, CCN_CLOUD_AI_PROD_ID_PATH));
        List<StandardActionCheck> createStandardActionChecks = Lists.newArrayList();
        // 核心网
        if (ccnPathids.stream().anyMatch(productIdPath::startsWith)) {
            if (!OPERATION_TYPE_UPGRADE_CODE.equals(operationTypeGroup)
                    || !productIdPath.startsWith(ConfigHelper.get(CCN_CCN_IMS_PROD_ID_PATH))) {
                ccnOutFieldCheckList.removeIf(item -> CHECK_CODE_025.equals(item.getCheckCode()));
            }
            ccnOutFieldCheckList.forEach(item -> {
                StandardActionCheck check = new StandardActionCheck();
                check.setStandardActionCheckType(item.getGroupOneZh());
                check.setStandardActionCheckTypeEn(item.getGroupOneEn());
                check.setStandardActionCheckContent(item.getCheckContentZh());
                check.setStandardActionCheckContentEn(item.getCheckContentEn());
                check.setPid(changeOrderId);
                createStandardActionChecks.add(check);
            });

            saveStandardActionChecks(changeOrderId, createStandardActionChecks);
            return;
        }

        // 技术方案检查checklist所属节点
        List<TechnicalSolutionCheck> technicalProposalCheckList = queryTechnicalSolutionCheckList(NODE_OF_OPERATION_APPLY);
        List<TechSolutionCheck> createTechSolutionChecks = Lists.newArrayList();
        if (!productIdPath.startsWith(ConfigHelper.get(RAN_PROD_ID_PATH))
                && !productIdPath.startsWith(ConfigHelper.get(SDI_PROD_ID_PATH))) {
            technicalProposalCheckList.removeIf(item -> CHECK_CODE_013.equals(item.getCheckCode()));
        }
        technicalProposalCheckList.forEach(item -> {
            TechSolutionCheck check = new TechSolutionCheck();
            check.setTechSolutionCheckContent(item.getCheckContentZh());
            check.setTechSolutionCheckContentEn(item.getCheckContentEn());
            check.setPid(changeOrderId);
            createTechSolutionChecks.add(check);
        });
        saveTechSolutionChecks(changeOrderId, createTechSolutionChecks);
    }

    private static void saveStandardActionChecks(String changeOrderId, List<StandardActionCheck> newChecks) {
        List<StandardActionCheck> hisChecks = QueryDataHelper.query(StandardActionCheck.class, new ArrayList<>(), changeOrderId);
        List<String> toDelete = new ArrayList<>();
        for (StandardActionCheck item : hisChecks) {
            if (newChecks.stream().noneMatch(t -> eqStandardActionCheck(item, t))) {
                toDelete.add(item.getId());
            }
        }
        newChecks.removeIf(toAdd -> hisChecks.stream().anyMatch(e -> eqStandardActionCheck(e, toAdd)));
        SaveDataHelper.batchDelete(StandardActionCheck.class, toDelete);
        SaveDataHelper.batchCreate(newChecks);
    }

    private static boolean eqStandardActionCheck(StandardActionCheck a, StandardActionCheck b) {
        return a.getStandardActionCheckContent().equals(b.getStandardActionCheckContent())
                && a.getStandardActionCheckType().equals(b.getStandardActionCheckType());
    }

    private static void saveTechSolutionChecks(String changeOrderId, List<TechSolutionCheck> newChecks) {
        List<TechSolutionCheck> hisChecks = QueryDataHelper.query(TechSolutionCheck.class, new ArrayList<>(), changeOrderId);
        List<String> toDelete = new ArrayList<>();
        for (TechSolutionCheck item : hisChecks) {
            if (newChecks.stream().noneMatch(t -> item.getTechSolutionCheckContent().equals(t.getTechSolutionCheckContent()))) {
                toDelete.add(item.getId());
            }
        }

        newChecks.removeIf(toAdd -> hisChecks.stream()
                .anyMatch(e -> e.getTechSolutionCheckContent().equals(toAdd.getTechSolutionCheckContent())));
        SaveDataHelper.batchDelete(TechSolutionCheck.class, toDelete);
        SaveDataHelper.batchCreate(newChecks);
    }

}
