package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/11
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum AvailabilityEnum implements SingletonTextValuePairsProvider {
    /** 启用 */
    ENABLED("启用", "Enabled"),
    /** 停用 */
    DISABLED("停用", "Disabled"),
    ;

    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }
}
