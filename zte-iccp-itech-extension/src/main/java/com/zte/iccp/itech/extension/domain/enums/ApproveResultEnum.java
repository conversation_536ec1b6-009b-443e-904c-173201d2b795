package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/03
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ApproveResultEnum implements SingletonTextValuePairsProvider {
    /** 审核通过 */
    PASS("审核通过", "Approved"),
    /** 审核驳回修改 */
    REJECT("审核驳回修改", "Rejected modification"),
    /** 审核不通过，终止 */
    TERMINATE("审核不通过，终止", "Rejected terminated"),
    /** 审核通过，无需再审 */
    SKIP("审核通过，无需再审", "Approved, no further review required"),
    /** 审批取消 */
    CANCEL("审批取消", "Cancel"),
    ;

    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }

    /**
     * 检索审批结果类型 - 枚举名称
     * (防止默认 valueOf 方法抛出异常)
     * @param enumName
     * @return ApproveResultEnum
     */
    public static ApproveResultEnum getApproveResultEnum(String enumName) {
        for (ApproveResultEnum value : ApproveResultEnum.values()) {
            if (value.name().equals(enumName)) {
                return value;
            }
        }

        return null;
    }

    /**
     * 根据语言环境获取对应描述信息
     * @param language
     * @return String
     */
    public String getTextByLanguage(String language) {
        return ZH_CN.equals(language) ? zhCn : enUs;
    }
}
