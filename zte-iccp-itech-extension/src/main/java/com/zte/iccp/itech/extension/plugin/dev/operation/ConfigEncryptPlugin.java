package com.zte.iccp.itech.extension.plugin.dev.operation;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;

/**
 * <AUTHOR> 10284287
 * @since 2024/08/06
 */
public class ConfigEncryptPlugin extends BaseOperationPlugin {
    @Override
    public void afterExecute(ExecuteEvent executeEvent) {
        String content = getModel().getValue("content").toString();
        String encrypted = ConfigHelper.createEncrypted(content);
        getModel().setValue("encrypted", encrypted);
    }
}
