package com.zte.iccp.itech.extension.ability.clockin;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInRecordFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInStateEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInOptionVO;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CLOCK_IN_STATE;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.*;

/**
 * 获取打卡任务可用的打卡选项
 *
 * <AUTHOR>
 * @since 2024/09/13
 */
public class ClockInOptionAbility {

    private final ClockInTask clockInTask;

    public ClockInOptionAbility(String clockInTaskId) {
        clockInTask = QueryDataHelper.get(
                ClockInTask.class,
                Lists.newArrayList(
                        ENTITY_TYPE,
                        BATCH_TASK_ID,
                        TASK_TYPE,
                        TASK_STATUS,
                        TIME_ZONE,
                        PLAN_PREPARE_START_TIME,
                        PLAN_EXECUTE_START_TIME,
                        TOTAL_ON_DUTY_DURATION,
                        ON_DUTY_FREQUENCY,
                        STAGE_ON_DUTY_END_TIME,
                        ALREADY_ON_DUTY_FREQUENCY),
                clockInTaskId);
    }

    public List<ClockInOptionVO> getClockInOptions() {
        Set<ClockInOptionEnum> options = clockInTask.getTaskStatus() == null
                ? ClockInOptionEnum.initOptions(clockInTask.getTaskType())
                : ClockInOptionEnum.nextStep(clockInTask.getTaskStatus());
        Date zonedNow = clockInTask.getTimeZone().pollute(new Date());

        // 操作打卡
        if (clockInTask.getTaskType() == ClockInTaskTypeEnum.OPERATION) {
            // 未到计划准备开始时间，只能打取消卡
            if (zonedNow.before(clockInTask.getPlanPrepareStartTime())) {
                return option2VO(options, e -> e == ClockInOptionEnum.CANCEL);
            }

            // 未到计划执行开始时间，只能打开始准备及之前的卡
            if (zonedNow.before(clockInTask.getPlanExecuteStartTime())) {
                return option2VO(options, e -> e.ordinal() <= ClockInOptionEnum.PREPARE_START.ordinal());
            }

            return option2VO(options, e -> true);
        }

        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(
                clockInTask.getEntityType().getEntityClass(),
                Lists.newArrayList(CLOCK_IN_STATE),
                clockInTask.getBatchTaskId());

        // 没到值守阶段，不能打值守卡
        if (batchTask.getClockInState() != ClockInStateEnum.ON_DUTY_GOING) {
            return option2VO(options, e -> false);
        }

        // 频次不够，或者值守时长不够，移除值守结束卡选项
        if (clockInTask.getAlreadyOnDutyFrequency() + 1 < clockInTask.getOnDutyFrequency()
                || zonedNow.before(clockInTask.getStageOnDutyEndTime())) {
            options.remove(ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_END);
        }

        // 最后一个卡是【异常处理中】，下一个卡不受值守时间约束
        ClockInOptionEnum lastOption = ClockInQueryAbility.getLastClockInRecords(
                        clockInTask.getId(), 1, BoolEnum.N, ClockInRecordFieldConsts.CLOCK_IN_OPTION)
                .stream()
                .findAny()
                .map(ClockInRecord::getClockInOption)
                .orElse(null);
        if (lastOption == ClockInOptionEnum.INDICATOR_ERROR_HANDLING) {
            return option2VO(options, e -> true);
        }

        if (options.contains(ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_END)) {
            // 可以打值守结束卡的时候，移除继续值守卡
            options.remove(ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE);
        }

        return option2VO(options, e -> true);
    }

    private static List<ClockInOptionVO> option2VO(
            Set<ClockInOptionEnum> options,
            Function<ClockInOptionEnum, Boolean> enabledNow) {
        return options.stream()
                .sorted(Comparator.naturalOrder())
                .map(e -> new ClockInOptionVO() {{
                    setOption(e);
                    setEnabledNow(enabledNow.apply(e));
                }}).collect(Collectors.toList());
    }
}
