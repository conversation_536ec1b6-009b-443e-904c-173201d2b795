package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiModeProduct;
import com.zte.iccp.itech.extension.handler.approver.MultimodeProductHandlerImpl;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.MultiModeProdConsts.APPROVED_TIME_MULTIMODE_PRODUCT;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.MultiModeProdConsts.MULTIMODE_REVIEWER_SUBMITTER;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

public class MultiModeProductBeforePlugin extends BaseFlowPlugin {
    private static String applyText(TextValuePair item) {
        return item.getTextByLanguage(RequestHeaderUtils.getLangId());
    }

    private static String applyValue(TextValuePair item) {
        return item.getValue();
    }

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        Object pkIdObj = body.getBusinessId();
        if (Objects.isNull(pkIdObj)) {
            return false;
        }
        String pkId = pkIdObj.toString();
        ChangeOrder changeOrder = ChangeOrderAbility.get(pkId, Lists.newArrayList(ID, MULTIMODE_PRODUCT, IS_GOV_ENT,
                RESPONSIBLE_DEPT, NETWORK_SERVICE_DEPT_APPROVER, NETWORK_SERVICE_DEPT_APPROVER_TEAM));

        if (changeOrder == null) {
            return false;
        }
        // 获取多模从属产品
        List<TextValuePair> multiModeProductsPairs = changeOrder.getMultiProductMode();
        if (CollectionUtils.isEmpty(multiModeProductsPairs)) {
            return false;
        }
        List<Pair<String, String>> multiModeProductPairs =
                multiModeProductsPairs.stream().map(multiModeProducts -> {
                    String text = applyText(multiModeProducts);
                    String value = applyValue(multiModeProducts);
                    return Pair.of(value, text);
                }).collect(Collectors.toList());

        Map<String, List<String>> approversMap = Maps.newHashMap();
        // 遍历每一个多模产品
        for(Pair<String, String> multiModeProductCodePair : multiModeProductPairs) {
            List<String> approvers =
                    getApprover(changeOrder, multiModeProductCodePair.getRight());
            approversMap.put(multiModeProductCodePair.getKey(), approvers);
        }
        initBill(pkId, multiModeProductPairs, approversMap);
        return false;
    }

    private void initBill(String pkId,
            List<Pair<String, String>> multiModeProductPairs,
            Map<String, List<String>> approversMap) {
        if (CollectionUtils.isEmpty(multiModeProductPairs)) {
            return;
        }

        List<MultiModeProduct> multiModeProducts = Lists.newArrayList();
        for (int i = 0; i < multiModeProductPairs.size(); i++) {
            MultiModeProduct multiModeProduct = new MultiModeProduct();
            multiModeProduct.setMultimodeProducts(multiModeProductPairs.get(i).getValue());
            String multiModeId = multiModeProductPairs.get(i).getKey();
            List<String> approvers = approversMap.get(multiModeId);
            String approver = String.join(COMMA + SPACE, approvers);
            multiModeProduct.setMultiModeNetApproveTeam(approver);
            multiModeProduct.setPid(pkId);
            multiModeProducts.add(multiModeProduct);
        }
        List<String> fields = Lists.newArrayList();
        List<MultiModeProduct> multiModeProductOlds = QueryDataHelper.query(MultiModeProduct.class, fields, pkId);
        // 避免重复入库
        if (multiModeProductOlds.size() == multiModeProducts.size()) {
            // 审核人/审核时间清空
            List<Map<String, Object>> multiValues = new ArrayList<>();
            for (MultiModeProduct multiModeProduct : multiModeProductOlds) {
                multiValues.add(MapUtils.newHashMap(ID, multiModeProduct.getId(),
                        MULTIMODE_REVIEWER_SUBMITTER, null, APPROVED_TIME_MULTIMODE_PRODUCT, null));
            }
            // 更新库
            SaveDataHelper.batchUpdate(MultiModeProduct.class, multiValues);
            return;
        }
        SaveDataHelper.batchCreate(multiModeProducts);
    }

    private List<String> getApprover(ChangeOrder changeOrder, String productName) {
        MultimodeProductHandlerImpl multimodeProduct = new MultimodeProductHandlerImpl();
        Map<String, String> approverMaps = multimodeProduct.getMultiModeProductApprovers(changeOrder);
        String approverIds = approverMaps.get(productName);
        List<String> stringList = Arrays.asList(approverIds.split(COMMA));
        List<Employee> employees = HrClient.queryEmployeeInfo(stringList);
        List<String> approvers = Lists.newArrayList();
        for(Employee employee : employees) {
            String name = ZH_CN.equals(RequestHeaderUtils.getLangId()) ? employee.getEmpNameCn() : employee.getEmpNameEn();
            approvers.add(name + SPACE + employee.getEmpUIID());
        }
        return approvers;
    }
}
