package com.zte.iccp.itech.extension.handler.approver.partner;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.Arrays;
import java.util.List;

import static com.zte.iccp.itech.extension.ability.ApproverConfigAbility.getApprovalPersonsByType;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DOMESTIC_SALES_ORG_CODE_PATH;

/**
 * 网络处审核
 * <AUTHOR>
 * @create 2024/7/15 下午1:54
 */
public class SubNetworkDeptHandlerImpl extends AbstractRewardApproverHandler<SubcontractorChangeOrder> {
    @Override
    public List<String> rewardApproverByNodeExtend(SubcontractorChangeOrder changeOrder, FlowClient bod) {
        ApproverConfiguration approverConfiguration = getApprover(changeOrder);
        return getApprovalPersonsByType(approverConfiguration, null);
    }

    public ApproverConfiguration getApprover(SubcontractorChangeOrder changeOrder) {
        ApproverConfiguration approverConfiguration;
        ApproverConfiguration queryParam = new ApproverConfiguration();
        // 技术交付部/网络处配置
        queryParam.setApprovalNode(ApprovalTypeEnum.TECHNOLOGY_DELIVERY_DEPT_NETWORK_OFFICE);
        //存在对应产品类型（如果存在多个产品类型满足条件，则优先从产品小类→产品大类→产品线匹配）+国内营销 +运营商+操作类型 国内（国际跳过该规则）
        queryParam.setSales(ConfigHelper.get(DOMESTIC_SALES_ORG_CODE_PATH));
        queryParam.setOperator(OperatorEnum.getCnnValue(changeOrder.getCustomerTypeFlag()));
        queryParam.setOperationType(Arrays.asList(changeOrder.getOperationType()));
        approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, changeOrder.getProductCategory(), 0);
        //（如果存在多个产品类型满足条件，则优先从产品小类→产品大类→产品线匹配）+国内营销 +运营商 --- 国内（国际跳过该规则）
        if (null == approverConfiguration) {
            queryParam.setOperationType(null);
            approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, changeOrder.getProductCategory(), 0);
        }
        //（如果存在多个产品类型满足条件，则优先从产品小类→产品大类→产品线匹配）+ 责任单位
        if (null == approverConfiguration) {
            queryParam.setOperationType(null);
            queryParam.setOperator(null);
            queryParam.setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
            queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
            queryParam.setResponsibleDeptId(changeOrder.getResponsibleDept());
            approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, changeOrder.getProductCategory(), 0);
        }
        //（如果存在多个产品类型满足条件，则优先从产品小类→产品大类→产品线匹配）+ /片区 （优先片区>营销）
        if (null == approverConfiguration) {
            queryParam.setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
            queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
            queryParam.setResponsibleDeptId(null);
            approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, changeOrder.getProductCategory(), 0);
        }
        // 营销单位
        if (null == approverConfiguration) {
            queryParam.setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
            queryParam.setOrganizationRegion(null);
            queryParam.setResponsibleDeptId(null);
            approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, changeOrder.getProductCategory(), 0);
        }

        return approverConfiguration;
    }

}
