package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 业务中断时长实体字段信息
 *
 * <AUTHOR> 10335201
 * @date 2024-05-13 上午10:53
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BusinessInterruptionDurationFactorConsts {
    /**
     * 主键id
     */
    public static final String ID = "id";
    /**
     * 业务中断时长上限（分钟）
     */
    public static final String MAXIMUM_SERVICE_INTERRUPTION_DURATION = "maximum_service_interruption_duration";
    /**
     * 业务中断时长下限（分钟）
     */
    public static final String MINIMUM_SERVICE_INTERRUPTION_DURATION = "minimum_service_interruption_duration";
    /**
     * 分值
     */
    public static final String SCORE = "score";
    /**
     * 产品经营团队
     */
    public static final String PRODUCT_OPERATION_TEAM = "product_operation_team";


}
