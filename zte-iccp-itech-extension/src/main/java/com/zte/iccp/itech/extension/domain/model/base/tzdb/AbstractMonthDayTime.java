package com.zte.iccp.itech.extension.domain.model.base.tzdb;

import com.zte.iccp.itech.extension.domain.constant.CommonConstants;

import java.time.*;
import java.time.chrono.IsoChronology;
import java.time.temporal.TemporalAdjusters;
import java.time.zone.ZoneOffsetTransitionRule;

import static com.zte.iccp.itech.extension.domain.model.base.tzdb.TzdbConstant.*;
import static com.zte.iccp.itech.extension.domain.model.base.tzdb.TzdbConstant.CHAR_EN_COLON;

abstract class AbstractMonthDayTime {
    /** The month of the cutover. */
    Month month = Month.JANUARY;

    /** The day-of-month of the cutover. */
    int dayOfMonth = 1;

    /** Whether to adjust forwards. */
    boolean adjustForwards = true;

    /** The day-of-week of the cutover. */
    DayOfWeek dayOfWeek;

    /** The time of the cutover, in second of day */
    int secsOfDay = 0;

    /** Whether this is midnight end of day. */
    boolean endOfDay;

    /** The time definition of the cutover. */
    ZoneOffsetTransitionRule.TimeDefinition timeDefinition = ZoneOffsetTransitionRule.TimeDefinition.WALL;

    void adjustToForwards(int year) {
        if (!adjustForwards && dayOfMonth > 0) {
            // weekDay<=monthDay case, don't have it in tzdb data for now
            LocalDate adjustedDate = LocalDate.of(year, month, dayOfMonth).minusDays(6);
            dayOfMonth = adjustedDate.getDayOfMonth();
            month = adjustedDate.getMonth();
            adjustForwards = true;
        }
    }

    LocalDateTime toDateTime(int year) {
        LocalDate date;
        if (dayOfMonth < 0) {
            int monthLen = month.length(IsoChronology.INSTANCE.isLeapYear(year));
            date = LocalDate.of(year, month, monthLen + 1 + dayOfMonth);
            if (dayOfWeek != null) {
                date = date.with(TemporalAdjusters.previousOrSame(dayOfWeek));
            }
        } else {
            date = LocalDate.of(year, month, dayOfMonth);
            if (dayOfWeek != null) {
                date = date.with(TemporalAdjusters.nextOrSame(dayOfWeek));
            }
        }
        if (endOfDay) {
            date = date.plusDays(1);
        }
        return LocalDateTime.of(date, LocalTime.ofSecondOfDay(secsOfDay));
    }

    /**
     * Parses the MonthDaytime segment of a tzdb line.
     */
    void parse(String[] tokens, int off) {
        month = parseMonth(tokens[off++]);
        if (off >= tokens.length) {
            return;
        }

        String dayRule = tokens[off++];
        if (dayRule.regionMatches(true, 0, LAST, 0, CommonConstants.INTEGER_FOUR)) {
            dayOfMonth = -1;
            dayOfWeek = parseDayOfWeek(dayRule.substring(CommonConstants.INTEGER_FOUR));
            adjustForwards = false;
        } else if (dayRule.indexOf(">=") > 0) {
            int index = dayRule.indexOf(">=");
            dayOfWeek = parseDayOfWeek(dayRule.substring(0, index));
            dayRule = dayRule.substring(index + 2);
            dayOfMonth = Integer.parseInt(dayRule);
        } else if (dayRule.indexOf("<=") > 0) {
            int index = dayRule.indexOf("<=");
            dayOfWeek = parseDayOfWeek(dayRule.substring(0, index));
            adjustForwards = false;
            dayRule = dayRule.substring(index + 2);
            dayOfMonth = Integer.parseInt(dayRule);
        }

        if (off >= tokens.length) {
            return;
        }

        String timeStr = tokens[off++];
        secsOfDay = parseSecs(timeStr);
        if (secsOfDay == DAY_SECOND) {
            // time must be midnight when end of day flag is true
            endOfDay = true;
            secsOfDay = 0;
        } else if (secsOfDay < 0 || secsOfDay > DAY_SECOND) {
            // beyond 0:00-24:00 range. Adjust the cutover date.
            int beyondDays = secsOfDay / DAY_SECOND;
            secsOfDay %= DAY_SECOND;
            if (secsOfDay < 0) {
                secsOfDay = DAY_SECOND + secsOfDay;
                beyondDays -= 1;
            }

            // leap-year
            LocalDate date = LocalDate.of(2004, month, dayOfMonth).plusDays(beyondDays);
            month = date.getMonth();
            dayOfMonth = date.getDayOfMonth();
            if (dayOfWeek != null) {
                dayOfWeek = dayOfWeek.plus(beyondDays);
            }
        }
        timeDefinition = parseTimeDefinition(timeStr.charAt(timeStr.length() - 1));
    }

    int parseYear(String year, int defaultYear) {
        int len = year.length();

        if (year.regionMatches(true, 0, MINIMUM, 0, len)) {
            return 1900;
        }
        if (year.regionMatches(true, 0, MAXIMUM, 0, len)) {
            return Year.MAX_VALUE;
        }
        if (year.regionMatches(true, 0, ONLY, 0, len)) {
            return defaultYear;
        }

        return Integer.parseInt(year);
    }

    Month parseMonth(String mon) {
        int len = mon.length();
        for (Month month : Month.values()) {
            if (mon.regionMatches(true, 0, month.name(), 0 , len)) {
                return month;
            }
        }

        throw new IllegalArgumentException("Unknown month: " + mon);
    }

    DayOfWeek parseDayOfWeek(String dow) {
        int len = dow.length();
        for (DayOfWeek day : DayOfWeek.values()) {
            if (dow.regionMatches(true, 0, day.name(), 0 , len)) {
                return day;
            }
        }

        throw new IllegalArgumentException("Unknown day-of-week: " + dow);
    }

    String parseOptional(String str) {
        return CommonConstants.HYPHEN.equals(str) ? null : str;
    }

    static boolean isDigit(char c) {
        return c >= '0' && c <= '9';
    }

    int parseSecs(String time) {
        if (CommonConstants.HYPHEN.equals(time)) {
            return 0;
        }

        // faster hack
        int secs = 0;
        int sign = 1;
        int off = 0;
        int len = time.length();
        if (off < len && time.charAt(off) == CHAR_HYPHEN) {
            sign = -1;
            off++;
        }

        char c0, c1;
        if (off >= len || !isDigit(c0 = time.charAt(off++))) {
            throw new IllegalArgumentException("[" + time + "]");
        }

        int hour = c0 - '0';
        if (off < len && isDigit(c1 = time.charAt(off))) {
            hour = hour * 10 + c1 - '0';
            off++;
        }

        secs = hour * 60 * 60;
        if (off >= len || time.charAt(off++) != CHAR_EN_COLON) {
            return secs * sign;
        }

        if (off + 1 >= len
                || !isDigit(c0 = time.charAt(off++))
                || !isDigit(c1 = time.charAt(off++))) {
            return secs * sign;
        }

        // minutes
        secs += ((c0 - '0') * 10 + c1 - '0') * 60;
        if (off >= len || time.charAt(off++) != CHAR_EN_COLON) {
            return secs * sign;
        }

        // seconds
        if (off + 1 < len
                && isDigit(c0 = time.charAt(off++))
                && isDigit(c1 = time.charAt(off++))) {
            secs += ((c0 - '0') * 10 + c1 - '0');
        }

        return secs * sign;
    }

    ZoneOffsetTransitionRule.TimeDefinition parseTimeDefinition(char c) {
        char upperChar = Character.toUpperCase(c);
        switch (upperChar) {
            case 'S':
                // standard time
                return ZoneOffsetTransitionRule.TimeDefinition.STANDARD;
            case 'U':
            case 'G':
            case 'Z':
                // UTC
                return ZoneOffsetTransitionRule.TimeDefinition.UTC;
            default:
                // wall time
                return ZoneOffsetTransitionRule.TimeDefinition.WALL;
        }
    }
}
