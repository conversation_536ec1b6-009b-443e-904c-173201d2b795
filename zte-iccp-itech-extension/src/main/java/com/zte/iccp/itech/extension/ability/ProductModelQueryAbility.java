package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.common.table.DataAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.vo.NetChangeProdModelVo;
import com.zte.iccp.itech.extension.domain.model.vo.TableDisplayRows;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.ProductModel;
import com.zte.iccp.itech.extension.spi.model.query.ProductModelQuery;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import com.zte.paas.lcap.ddm.domain.model.elementstatus.ElementStatusEnum;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.*;



/**
 * <AUTHOR>
 * @date 2024/5/30 下午2:35
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductModelQueryAbility {

    private static final String SELF_PAGE_OPERATION_OBJ_PROD_MODEL_OFFICE_NAME_CID = "customerNeName";
    private static final String SELF_PAGE_OPERATION_OBJ_PROD_MODEL_OFFICE_NAME_ALIAS_CID = "customerNeNameAlias";

    public static void afterSearch(IDataModel dataModel, IFormView formView, int currentPage, int pageSize) {
        // 1.获取网络Id
        String networkId
                = PropertyValueConvertUtil.getString(dataModel.getValue(CONDITION_QUERY_PARAM_NETWORK_ID_CID));

        // 2.获取产品分类Id
        String prodClassIdPath = getProductPathId(dataModel);
        if (StringUtils.isBlank(networkId) || StringUtils.isBlank(prodClassIdPath)) {
            return;
        }

        // 3.特殊产品分类操作
        // 非核心网 需隐藏局点名称数据列
        boolean ccnFlag = prodClassIdPath.startsWith(ConfigHelper.get(CommonConstants.CCN_CCN_PROD_ID_PATH))
                || prodClassIdPath.startsWith(ConfigHelper.get(CommonConstants.CCN_CLOUD_AI_PROD_ID_PATH))
                || prodClassIdPath.startsWith(ConfigHelper.get(CommonConstants.CCN_INTEGRATION_DEVICES_PROD_ID_PATH));
        if (!ccnFlag) {
            DataAbility.hiddenTableColumn(
                    formView,
                    CidConstants.SELF_PAGE_OPERATION_OBJ_PROD_MODEL_TABLE_ID,
                    Lists.newArrayList(
                            SELF_PAGE_OPERATION_OBJ_PROD_MODEL_OFFICE_NAME_CID,
                            SELF_PAGE_OPERATION_OBJ_PROD_MODEL_OFFICE_NAME_ALIAS_CID));
        }

        // 4.检索产品型号数据
        PageRows<ProductModel> pageRows
                = queryProductModelRows(dataModel, prodClassIdPath, networkId, ccnFlag, currentPage, pageSize);

        // 5.无数据，展示页面提示语
        displayProductModelInfo(formView, pageRows);
    }

    /**
     * 获取 产品idPath
     */
    private static String getProductPathId(IDataModel dataModel) {
        // 1.获取 产品小类 idPath
        String prodClassIdPath = TextValuePairHelper.getValue(
                ComponentUtils.getChooseComponentInfo(dataModel, CidConstants.COMPONENT_PRODUCT_SUB_CATEGORY_CID));

        // 2.若 产品小类idPath 不存在，检索 产品大类idPath 作为产品条件
        if (StringUtils.isBlank(prodClassIdPath)) {
            prodClassIdPath = PropertyValueConvertUtil.getString(
                    dataModel.getValue(CidConstants.COMPONENT_PRODUCT_MAIN_CATEGORY_CID));
        }

        return prodClassIdPath;
    }

    /**
     * 检索产品信号工信息
     */
    private static PageRows<ProductModel> queryProductModelRows(
            IDataModel dataModel,
            String prodClassIdPath,
            String networkId,
            boolean ccnFlag,
            int currentPage,
            int pageSize) {

        // 1.产品 + 网络 不存在，直接返回
        if (StringUtils.isBlank(prodClassIdPath) || StringUtils.isBlank(networkId)) {
            return new PageRows<>();
        }

        // 2.获取 产品型号 关键词
        String productModelName
                = PropertyValueConvertUtil.getString(dataModel.getValue(CONDITION_PRODUCT_MODEL_CID));

        // 3.检索 NIS 获取产品型号信息
        ProductModelQuery productModelQuery = new ProductModelQuery();
        productModelQuery.setKeyword(productModelName);
        productModelQuery.setIsCcn(ccnFlag);
        productModelQuery.setProdClassIdPath(prodClassIdPath);
        productModelQuery.setNetworkId(networkId);
        productModelQuery.setPageNum(currentPage);
        productModelQuery.setPageSize(pageSize);

        return NisClient.queryProductModels(productModelQuery);
    }

    /**
     * 产品型号列表数据展示
     */
    private static void displayProductModelInfo(IFormView formView, PageRows<ProductModel> pageRows) {
        if (CollectionUtils.isEmpty(pageRows.getRows())) {
            return;
        }

        // 1.有数据时，隐藏页面提示语
        Map<String, Object> props = new HashMap<>();
        props.put(PROPS_BEHAVIOR, ElementStatusEnum.HIDDEN.getValue());
        formView.getClientViewProxy().setControlState(CONDITION_PROMPT_CID, props);

        // 2.包装列表展示数据
        TableDisplayRows<NetChangeProdModelVo> tableDisplayRows = convertDisplayRows(pageRows);

        // 3.展示数据
        Map<String, Object> dataMap = MapUtils.newHashMap(CommonConstants.DATA, tableDisplayRows);
        Map<String, Object> viewMap = MapUtils.newHashMap(SELF_PAGE_OPERATION_OBJ_PROD_MODEL_TABLE_ID, dataMap);
        formView.getClientViewProxy().setProps(viewMap);
    }

    /**
     * 包装列表展示数据
     */
    private static TableDisplayRows<NetChangeProdModelVo> convertDisplayRows(PageRows<ProductModel> pageRows) {
        TableDisplayRows<NetChangeProdModelVo> tableDisplayRows = new TableDisplayRows<>();

        List<NetChangeProdModelVo> displayList = Lists.newArrayList();
        for (ProductModel productModel : pageRows.getRows()) {
            NetChangeProdModelVo vo = new NetChangeProdModelVo();

            // (1) 基础网络属性
            vo.setProdModelId(productModel.getProdModelId());
            vo.setProdModel(productModel.getProdModel());
            vo.setCustomerNeName(productModel.getCustomerNeName());
            vo.setNeAlias(productModel.getNeAlias());
            vo.setProdModelFullIdPath(productModel.getProdModelFullIdPath());

            displayList.add(vo);
        }

        tableDisplayRows.setTotal(pageRows.getTotal());
        tableDisplayRows.setCurrent(pageRows.getCurrent());
        tableDisplayRows.setRecords(displayList);

        return tableDisplayRows;
    }
}
