package com.zte.iccp.itech.extension.domain.model.subentity;

import com.zte.iccp.itech.extension.domain.model.PlanOperationOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

@ApiModel("批次概要信息")
@Setter
@Getter
@BaseSubEntity.Info(value = "batch_summary", parent = PlanOperationOrder.class)
public class PlanBatchSummary extends BatchSummary {
}
