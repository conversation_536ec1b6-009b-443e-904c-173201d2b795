package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.Operator;
import com.zte.iccp.itech.extension.domain.model.PlanOperationOrder;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.PlanOperationAssignment;
import com.zte.iccp.itech.extension.domain.model.subentity.*;
import com.zte.iccp.itech.extension.domain.model.vo.ChangeOrderSave;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.common.helper.TaskNoHelper.NETWORK_CHANGE_TASK_CODE;

/**
 * 操作计划单
 *
 * <AUTHOR>
 * @create 2025/7/24 下午2:57
 */
public class PlanOperationAbility {

    public static PlanOperationOrder get(String id, List<String> fields) {
        return QueryDataHelper.get(PlanOperationOrder.class, fields, id);
    }

    /**
     * 操作计划单转网络变更单
     */
    public static ChangeOrderSave planOrder2ChangeOrder(String planId) {
        PlanOperationOrder planOrder = get(planId, new ArrayList<>());
        ChangeOrderSave changeOrder = new ChangeOrderSave();
        changeOrder.setSource(DataSourceEnum.PLAN.name());
        changeOrder.setOrderNo(TaskNoHelper.createTaskNo(NETWORK_CHANGE_TASK_CODE));
        changeOrder.setPlanOperationOrderId(planId);
        changeOrder.setOperationSubject(planOrder.getOperationSubject());
        changeOrder.setOperationSubjectPrefix(planOrder.getOperationSubjectPrefix());
        changeOrder.setOperationSubjectSuffix(planOrder.getOperationSubjectSuffix());
        changeOrder.setIsGovEnt(planOrder.getIsGovEnt());
        changeOrder.setGdprRequire(planOrder.getGdprRequire());
        changeOrder.setProductCategory(planOrder.getProductCategory());
        changeOrder.setResponsibleDept(planOrder.getResponsibleDept());
        changeOrder.setOperationType(planOrder.getOperationType());
        changeOrder.setOperationTypeGroup(planOrder.getOperationTypeGroup());
        changeOrder.setOperationReason(planOrder.getOperationReason());
        changeOrder.setIsFirstApplication(planOrder.getIsFirstTimeApply());
        changeOrder.setIsSpecialScenario(planOrder.getIsSpecialScenario());
        changeOrder.setIsEmergencyOperation(planOrder.getIsEmergencyOperation());
        changeOrder.setEmergencyOperationReason(planOrder.getEmergencyOperationReason());
        changeOrder.setEmergencyOperationAttach(planOrder.getEmergencyOperationAttach());
        changeOrder.setIsNetCloseOrControlOperation(planOrder.getIsNetCloseOrControlOperation());
        changeOrder.setCloseOrControlOperationAttach(planOrder.getCloseOrControlOperationAttach());
        changeOrder.setNetCloseOrControlOperationReason(planOrder.getNetCloseOrControlOperationReason());
        changeOrder.setIsTechnicalNotice(planOrder.getIsTechnicalNotice());
        changeOrder.setTriggerType(tranEnum(planOrder.getTriggerType()));
        changeOrder.setChangeOperationSource(planOrder.getChangeOperationSource());
        changeOrder.setDeliveryMode(tranEnum(planOrder.getDeliveryMode()));
        changeOrder.setOperationDesc(planOrder.getOperationDesc());
        changeOrder.setTimeZone(planOrder.getTimeZone() == null ? null : planOrder.getTimeZone().getValue());
        changeOrder.setServiceDisconnectDuration(planOrder.getBusiInterruptDuration());
        changeOrder.setInternalOperationSolution(Lists.newArrayList(planOrder.getInternalOperationSolution()));
        changeOrder.setIsNeedAuthorizationFile(planOrder.getIsNeedAuthorizationFile());
        changeOrder.setOperationLevel(tranEnum(planOrder.getOperationLevel()));
        changeOrder.setImportance(tranEnum(planOrder.getImportance()));
        changeOrder.setRiskEvaluation(tranEnum(planOrder.getRiskEvaluation()));
        changeOrder.setConflict(planOrder.getConflict());
        changeOrder.setNeListFile(planOrder.getNeListFile());
        changeOrder.setOperationStartTime(planOrder.getOperationStartTime());
        changeOrder.setOperationEndTime(planOrder.getOperationEndTime());

        // 国家 省 地区  客户ID 客户标识 逻辑网元
        List<PlanOperationObject> planObjects = QueryDataHelper.query(PlanOperationObject.class, new ArrayList<>(), planId);
        if (!CollectionUtils.isEmpty(planObjects)) {
            changeOrder.setCountry(Lists.newArrayList(planObjects.get(0).getCountry()));
            changeOrder.setProvince(Lists.newArrayList(planObjects.get(0).getProvince()));
            changeOrder.setCity(Lists.newArrayList(planObjects.get(0).getCity()));
            changeOrder.setCustomerId(planObjects.get(0).getCustomerId());
            changeOrder.setCustomerTypeFlag(planObjects.get(0).getCustomerTypeFlag());
        }
        String changeOrderId = SaveDataHelper.create(changeOrder);
        changeOrder.setId(changeOrderId);

        // 批次概要
        List<PlanBatchSummary> planBatchSummaries = QueryDataHelper.query(PlanBatchSummary.class, new ArrayList<>(), planId);
        List<BatchSummary> batchSummaries = new ArrayList<>();
        planBatchSummaries.forEach(item -> {
            BatchSummary summary = new BatchSummary();
            BeanUtils.copyProperties(item, summary);
            summary.setId(null);
            summary.setPid(changeOrderId);
            summary.clearEntityValue();
            batchSummaries.add(summary);
        });

        // 操作对象
        List<OperationObject> objects = new ArrayList<>();
        planObjects.forEach(item -> {
            OperationObject object = new OperationObject();
            BeanUtils.copyProperties(item, object);
            object.setId(null);
            object.setPid(changeOrderId);
            object.clearEntityValue();
            objects.add(object);
        });

        // 操作人员
        List<PlanOperator> planOperators = QueryDataHelper.query(PlanOperator.class, new ArrayList<>(), planId);
        List<Operator> operators = new ArrayList<>();
        planOperators.forEach(item -> {
            Operator operator = new Operator();
            BeanUtils.copyProperties(item, operator);
            operator.setId(null);
            operator.setPid(changeOrderId);
            operator.clearEntityValue();
            operators.add(operator);
        });
        SaveDataHelper.batchCreate(batchSummaries);
        SaveDataHelper.batchCreate(objects);
        SaveDataHelper.batchCreate(operators);
        return changeOrder;
    }

    /**
     * 操作计划任务单 复制成网络变更单
     */
    public static void saveChangeAssignment(PlanOperationAssignment assignment, ChangeOrderSave changeOrder) {
        NetworkChangeAssignment netAssignment = new NetworkChangeAssignment();
        netAssignment.setAssignmentName(assignment.getAssignmentName());
        netAssignment.setAssignmentCode(changeOrder.getOrderNo());
        netAssignment.setAssignmentStatus(AssignmentStatusEnum.START.getValue());
        netAssignment.setCurrentProgress(ApproveNodeEnum.DRAFT.name());
        netAssignment.setApprovalTaskFlag(BoolEnum.N);
        netAssignment.setCurrentProcessorEmployee(netAssignment.getResponsibleEmployee());
        netAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE.getPropValue());
        netAssignment.setBillType(BillTypeEnum.NETWORK_CHANGE.getPropValue());
        netAssignment.setMarketing(assignment.getMarketing());
        netAssignment.setRepresentativeOffice(assignment.getRepresentativeOffice());
        netAssignment.setProductManagementTeam(assignment.getProductManagementTeam());
        netAssignment.setProductClassification(assignment.getProductClassification());
        netAssignment.setCustomerClassification(assignment.getCustomerClassification());
        netAssignment.setOperationType(assignment.getOperationType());
        netAssignment.setNetwork(assignment.getNetwork());
        netAssignment.setOfficeName(assignment.getOfficeName());
        netAssignment.setResponsibleEmployee(assignment.getResponsibleEmployee());
        netAssignment.setCompany(assignment.getCompany());
        netAssignment.setPlanStartTime(assignment.getPlanStartTime());
        netAssignment.setCountry(assignment.getCountry());
        netAssignment.setOperationReason(assignment.getOperationReason());
        netAssignment.setImportance(assignment.getImportance());
        netAssignment.setRiskEvaluation(assignment.getRiskEvaluation());
        netAssignment.setOperationLevel(assignment.getOperationLevel());
        netAssignment.setTimeZone(assignment.getTimeZone());
        netAssignment.setOperationStartTimeUtc8(assignment.getOperationStartTimeUtc8());
        //netAssignment.setConflict(assignment.getConflict()); todo
        netAssignment.setBillId(changeOrder.getId());
        netAssignment.setEntityId(changeOrder.getId());
        SaveDataHelper.create(netAssignment);
    }

    /**
     * 操作计划单撤回
     *
     * @param assignment 任务信息
     */
    public static void revoke(Assignment assignment) {
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        // 1.撤销审批流程
        FlowHelper.rollbackToStarter(
                assignment.getEntityId(),
                Objects.requireNonNull(assignmentTypeEnum).getApproveFlowCodeEnum().name(),
                MsgUtils.getMessage(MessageConsts.APPLICANT_RETURN_APPROVAL));

        // 2.更新任务状态为草稿、当前处理人 = 责任人（只有责任人可以进行撤回操作），当前进展为DRAFT
        Assignment toUpdateAssignment = new Assignment();
        toUpdateAssignment.setId(assignment.getId());
        toUpdateAssignment.setCurrentProgress(ApproveNodeEnum.DRAFT.name());
        toUpdateAssignment.setCurrentProcessorEmployee(assignment.getResponsibleEmployee());
        toUpdateAssignment.setAssignmentStatus(AssignmentStatusEnum.START.getValue());
        SaveDataHelper.update(toUpdateAssignment);

        // todo 4.操作日志


        // todo 5.知会邮件

    }

    /**
     * 撤回前置校验
     *
     * @param assignment 任务信息
     * @return true 允许撤回  false 不允许撤回
     */
    public static boolean revokeBeforeCheck(Assignment assignment) {
        AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        // 操作用户为责任人
        return EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee()).contains(ContextHelper.getEmpNo())
                // 任务状态为审核中
                && AssignmentStatusEnum.APPROVE == assignmentStatusEnum;
    }

    private static Object tranEnum(SingletonTextValuePairsProvider obejctEnum) {
        if (obejctEnum == null) {
            return null;
        }
        return obejctEnum.getPropValue();
    }
}
