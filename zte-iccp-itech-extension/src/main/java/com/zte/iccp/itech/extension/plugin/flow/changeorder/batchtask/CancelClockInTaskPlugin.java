package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInTaskUpdateAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.AlarmUtils;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.RetryUtils;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@Slf4j
public class CancelClockInTaskPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        log.info("{} {}", body.getNodeElement().getNodeExtendName(), body.getBusinessId());

        if (!Boolean.parseBoolean(ConfigHelper.get("clockInTask.enabled", "false"))) {
            return false;
        }
        List<String> batchIds = new ArrayList<>();
        batchIds.add(body.getBusinessId());
        // 取消关联的保障单批次
        List<BatchTask> batchTasks = BatchTaskAbility.getGuarantyBatchTask(body.getBusinessId());
        List<String> ids = batchTasks.stream().map(BatchTask::getId).collect(Collectors.toList());
        batchIds.addAll(ids);
        ClockInTaskUpdateAbility.batchCancelClockInTasks(batchIds);
        return false;
    }

}
