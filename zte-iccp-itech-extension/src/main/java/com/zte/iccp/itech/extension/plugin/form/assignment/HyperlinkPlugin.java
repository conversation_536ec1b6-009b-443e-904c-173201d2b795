package com.zte.iccp.itech.extension.plugin.form.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.enums.DetailPageEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.*;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.event.RowClickEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.TABLE_ASSIGNMENT_CID;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts.TASK_ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFieldConsts.TASK_PID;
import static com.zte.paas.lcap.ddm.domain.enums.OpenTypeEnum.NEW_TAB;

public class HyperlinkPlugin extends BaseFormPlugin {

    // =========== 表格列 DataKey ============
    /**
     * 任务 - 任务名称
     */
    private static final String COMMON_ASSIGNMENT_NAME = "textfield_tz852y9c";

    /**
     * 故障管理任务 - WarRoom 名称
     */
    private static final String FAULT_WAR_ROOM_NAME = "textfield_ll7l09os";

    @Override
    public void afterRowClick(RowClickEvent clickEvent) {
        IFormView iFormView = getView();
        showForm(clickEvent, iFormView, TABLE_ASSIGNMENT_CID);
    }

    /**
     * 页面跳转
     *
     * @param iFormView iFormView
     * @param assignmentId 任务id
     * @param clickField clickField
     */
    public void showForm(IFormView iFormView, String assignmentId,String clickField) {
        // 1.移除默认 showForm 页面跳转
        iFormView.getClientViewProxy().getActionResult().clear();

        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(assignmentId, Assignment.class);
        if (Objects.isNull(assignment)) {
            return;
        }

        // 3.包装指令，跳转详情查询表单
        // (1) 获取跳转参数
        Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo = getForwardTargetInfo(assignment, clickField);
        Map<String, String> basicForwardInfo = forwardTargetInfo.getLeft();
        Map<String, Object> customParameters = new HashMap<>();

        // 无基本参数，不跳转
        if (CollectionUtils.isEmpty(basicForwardInfo)) {
            return;
        }

        // (2) 跳转基本参数
        FormShowParameter formShowParameter = iFormView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setPkId(basicForwardInfo.get(PK_ID));
        formShowParameter.setPageId(basicForwardInfo.get(PAGE_ID));
        formShowParameter.setBizObjectCode(basicForwardInfo.get(BIZ_OBJ_CODE));
        formShowParameter.setPageStatus(PageStatusEnum.valueOf(basicForwardInfo.get(PAGE_STATUS)));

        // (3) 跳转扩展参数
        customParameters.put(HIDDEN_OPERATION, true);
        customParameters.put(FULL_SCREEN, true);
        customParameters.put(OPEN_TYPE, NEW_TAB.getValue());
        customParameters.putAll(forwardTargetInfo.getRight());
        formShowParameter.setCustomParameters(customParameters);

        iFormView.showForm(formShowParameter);
    }

    /**
     * 页面超链接跳转
     *
     * @param clickEvent clickEvent
     * @param iFormView iFormView
     * @param tableAssignment tableAssignment
     */
    public void showForm(RowClickEvent clickEvent, IFormView iFormView, String tableAssignment) {
        // 1.移除默认 showForm 页面跳转
        iFormView.getClientViewProxy().getActionResult().clear();

        // 2.获取任务数据
        TablePc tableInfo = (TablePc) iFormView.getControl(tableAssignment);
        String assignmentId = clickEvent.getArgs().getJSONObject(VALUE).getString(tableInfo.getPkId());
        String clickField = clickEvent.getArgs().getJSONObject(VALUE).getString(COL_KEY);

        showForm(iFormView, assignmentId, clickField);
    }

    /**
     * 获取目标页面跳转参数
     * left - 跳转基本参数   right - 跳转扩展参数
     * @param assignment
     * @param clickField
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getForwardTargetInfo(Assignment assignment, String clickField) {
        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());

        switch (assignmentType) {
            // 网络变更 / 网络批次变更跳转相同位置
            case NETWORK_CHANGE:
            case NETWORK_CHANGE_BATCH:
                return getInteriorNetworkChangeInfo(assignment.getId(), clickField);

            case TECHNOLOGY_MANAGEMENT:
                return getMainTechnologyTaskInfo(assignment.getId(), clickField);

            case TECHNOLOGY_MANAGEMENT_SUB:
                return getSubTechnologyTaskInfo(assignment.getId(), clickField);

            // 分包商网络变更 / 分包商网络批次变更跳转相同位置
            case SUBCONTRACTOR_NETWORK_CHANGE:
            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                return getSubcontractNetworkChangeInfo(assignment.getId(), clickField);

            case FAULT_MANAGEMENT:
                return getForwardFaultHandleInfo(assignment.getId(), clickField);

            case OPERATION_PLAN_TASK:
                return getInteriorOperationPlanInfo(assignment.getId(), clickField);
            default:
                return Pair.of(new HashMap<>(), new HashMap<>());
        }
    }

    /**
     * 获取 网络变更任务 跳转参数
     * @param assignmentId
     * @param clickField
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getInteriorNetworkChangeInfo(String assignmentId, String clickField) {
        // 1.点击字段校验
        if (!COMMON_ASSIGNMENT_NAME.equals(clickField)) {
            return Pair.of(new HashMap<>(), new HashMap<>());
        }

        // 2.检索网络变更任务数据
        NetworkChangeAssignment assignment = AssignmentAbility.querySpecificTypeAssignment(assignmentId, NetworkChangeAssignment.class);
        if (Objects.isNull(assignment)) {
            return Pair.of(new HashMap<>(), new HashMap<>());
        }

        // 3.校验任务状态
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (AssignmentStatusEnum.START.equals(assignmentStatus)) {
            return getInteriorMainNetworkChangeInfo(assignment, PAGE_NETWORK_CHANGE_BILL);
        }
        if (AssignmentStatusEnum.APPROVE_START.equals(assignmentStatus)) {
            return getInteriorMainNetworkChangeInfo(assignment, PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL);
        }
        if (AssignmentStatusEnum.APPROVE.equals(assignmentStatus)) {
            return getInteriorMainNetworkChangeInfo(assignment, ApproveNodeEnum.getApproveNodePageId(assignment.getCurrentProgress()));
        } else {
            return getInteriorBatchNetworkChangeInfo(assignment);
        }
    }

    /**
     * 跳转 网络变更 - 主任务 详情
     * @param assignment
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>,
            Map<String, Object>> getInteriorMainNetworkChangeInfo(NetworkChangeAssignment assignment, String pageId) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID
        basicForwardInfo.put(PK_ID, assignment.getBillId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(ChangeOrder.class));
        basicForwardInfo.put(PAGE_ID, pageId);

        // (2) 页面状态
        // 用户是责任人 + 任务状态草稿待启动 / 驳回待启动
        String userId = ContextHelper.getEmpNo();
        String responsible = CollectionUtils.isEmpty(assignment.getResponsibleEmployee())
                ? assignment.getCreateBy()
                : assignment.getResponsibleEmployee().get(0).getEmpUIID();
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());

        if (userId.equals(responsible) &&
                (AssignmentStatusEnum.START.equals(assignmentStatus) || AssignmentStatusEnum.APPROVE_START.equals(assignmentStatus))) {
            basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.EDIT.name());
        } else {
            basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.VIEW.name());
        }

        // 2.包装扩展跳转参数
        // (1) 页面标题 + 任务状态 + 提交人 + 操作类型
        customerForwardInfo.put(PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(CREATE_BY, assignment.getCreateBy());
        customerForwardInfo.put(OPERATION_KEY, OPERATION_VIEW);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转 网络变更 - 批次任务 详情
     * @param assignment
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getInteriorBatchNetworkChangeInfo(NetworkChangeAssignment assignment) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.检索批次任务信息
        String networkChangeOrderId = assignment.getBillId();
        List<? extends BaseEntity> batchTaskList = QueryDataHelper.query(BatchTask.class,
                Lists.newArrayList(ID, BATCH_NO, SOURCE, CURRENT_STATUS, BATCH_CODE),
                Lists.newArrayList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, networkChangeOrderId)));

        // 无批次任务信息，继续跳转主任务详情
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return getInteriorMainNetworkChangeInfo(assignment, PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL);
        }

        // 2.检索待跳转的批次任务
        IBatchTask batchTask = BatchTaskAbility.getHyperLinkBatchTask(
                batchTaskList, assignment.getAssignmentType(), assignment.getEntityId(), assignment.getEntityId());
        if (batchTask == null) {
            return getInteriorMainNetworkChangeInfo(assignment, PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL);
        }

        // 3.检索主任务信息
        Assignment mainAssignment = AssignmentAbility.queryAssignment(
                networkChangeOrderId, Lists.newArrayList(ASSIGNMENT_CODE), Assignment.class);

        // 4.包装基本跳转参数
        // 主键 + 实体对象编码 + 页面ID + 页面状态
        basicForwardInfo.put(PK_ID, batchTask.getId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(BatchTask.class));
        basicForwardInfo.put(PAGE_ID, DetailPageEnum.NETWORK_CHANGE_BATCH.getPageId());
        basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 5.包装扩展跳转参数
        // 页面标题 + 任务状态 + 提交人 + 操作类型
        customerForwardInfo.put(PAGE_TITLE, mainAssignment.getAssignmentCode());
        customerForwardInfo.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(CREATE_BY, assignment.getCreateBy());
        customerForwardInfo.put(OPERATION_KEY, OPERATION_VIEW);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 获取 技术管理 - 主任务 跳转参数
     * @param assignmentId
     * @param clickField
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getMainTechnologyTaskInfo(String assignmentId, String clickField) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.点击字段校验
        if (!COMMON_ASSIGNMENT_NAME.equals(clickField)) {
            return Pair.of(basicForwardInfo, customerForwardInfo);
        }

        // 2.检索技术管理任务实例
        TechnologyManagementAssignment assignment =
                AssignmentAbility.querySpecificTypeAssignment(assignmentId, TechnologyManagementAssignment.class);
        if (Objects.isNull(assignment)) {
            return Pair.of(basicForwardInfo, customerForwardInfo);
        }

        // 3.基础信息
        // (1) 主键 + 实体对象编码 + 页面状态
        basicForwardInfo.put(PK_ID, assignment.getBillId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(TechnologyManagementOrder.class));
        basicForwardInfo.put(PAGE_ID, PAGE_BILL_MAIN_TECHNOLOGY_DETAIL_BILL);
        basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 4.补充信息
        // (1) 页面标题 + 任务状态 + 任务ID
        customerForwardInfo.put(PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(TASK_ID, assignment.getBillId());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 获取 技术管理 - 子任务 跳转参数
     * @param assignmentId
     * @param clickField
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getSubTechnologyTaskInfo(String assignmentId, String clickField) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.点击字段校验
        if (!COMMON_ASSIGNMENT_NAME.equals(clickField)) {
            return Pair.of(basicForwardInfo, customerForwardInfo);
        }

        // 2.检索技术管理任务实例
        TechnologyManagementAssignment assignment =
                AssignmentAbility.querySpecificTypeAssignment(assignmentId, TechnologyManagementAssignment.class);
        if (Objects.isNull(assignment)) {
            return Pair.of(basicForwardInfo, customerForwardInfo);
        }

        // 3.基础信息
        // (1) 页面ID + 页面状态 + 实体对象编码
        basicForwardInfo.put(PK_ID, assignment.getApproveSubTaskId());
        basicForwardInfo.put(PAGE_ID, PAGE_BILL_SUB_TECHNOLOGY_DETAIL_BILL);
        basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.VIEW.name());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(ManageSubTaskFlow.class));

        // 4.扩展信息
        customerForwardInfo.put(PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(TASK_ID, assignment.getApproveSubTaskId());
        customerForwardInfo.put(TASK_PID, assignment.getBillId());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 获取 合作方网络变更任务 跳转参数
     * @param assignmentId
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getSubcontractNetworkChangeInfo(String assignmentId, String clickField) {
        // 1.点击字段校验
        if (!COMMON_ASSIGNMENT_NAME.equals(clickField)) {
            return Pair.of(new HashMap<>(), new HashMap<>());
        }

        // 2.检索合作方网络变更任务数据
        NetworkChangeAssignment assignment = AssignmentAbility.querySpecificTypeAssignment(assignmentId, NetworkChangeAssignment.class);
        if (Objects.isNull(assignment)) {
            return Pair.of(new HashMap<>(), new HashMap<>());
        }

        // 3.校验任务状态
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (AssignmentStatusEnum.START.equals(assignmentStatus)) {
            return getSubcontractMainNetworkChangeInfo(assignment, PAGE_PARTNER_NETWORK_CHANGE_BILL);
        }
        if (AssignmentStatusEnum.APPROVE.equals(assignmentStatus)
                || AssignmentStatusEnum.APPROVE_START.equals(assignmentStatus)) {
            return getSubcontractMainNetworkChangeInfo(assignment, PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL);
        } else {
            return getSubcontractBatchNetworkChangeInfo(assignment);
        }
    }

    /**
     * 跳转 合作方网络变更 - 主任务 详情
     * @param assignment
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>,
            Map<String, Object>> getSubcontractMainNetworkChangeInfo(NetworkChangeAssignment assignment, String PageId) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID
        basicForwardInfo.put(PK_ID, assignment.getBillId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(SubcontractorChangeOrder.class));
        basicForwardInfo.put(PAGE_ID, PageId);

        // (2) 页面状态
        // 用户是责任人 + 任务状态草稿待启动 / 驳回待启动
        String userId = ContextHelper.getEmpNo();
        String responsible = CollectionUtils.isEmpty(assignment.getResponsibleEmployee())
                ? assignment.getCreateBy()
                : assignment.getResponsibleEmployee().get(0).getEmpUIID();
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());

        if (userId.equals(responsible) &&
                (AssignmentStatusEnum.START.equals(assignmentStatus) || AssignmentStatusEnum.APPROVE_START.equals(assignmentStatus))) {
            basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.EDIT.name());
        } else {
            basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.VIEW.name());
        }

        // 2.包装扩展跳转参数
        // (1) 页面标题 + 任务状态 + 提交人 + 操作类型
        customerForwardInfo.put(PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(CREATE_BY, assignment.getCreateBy());
        customerForwardInfo.put(OPERATION_KEY, OPERATION_VIEW);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转 合作方网络变更 - 批次任务 详情
     * @param assignment
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getSubcontractBatchNetworkChangeInfo(NetworkChangeAssignment assignment) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.检索批次任务信息
        String networkChangeOrderId = assignment.getBillId();

        List<? extends BaseEntity> batchTaskList = QueryDataHelper.query(SubcontractorBatchTask.class,
                Lists.newArrayList(ID, BATCH_NO, CURRENT_STATUS, BATCH_CODE),
                Lists.newArrayList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, networkChangeOrderId)));
        // 无批次任务信息，继续跳转主任务详情
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return getSubcontractMainNetworkChangeInfo(assignment, PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL);
        }

        // 2.检索待跳转的批次任务
        IBatchTask batchTask = BatchTaskAbility.getHyperLinkBatchTask(
                batchTaskList, assignment.getAssignmentType(), assignment.getEntityId(), assignment.getEntityId());
        if (batchTask == null) {
            return getSubcontractMainNetworkChangeInfo(assignment, PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL);
        }

        // 3.检索主任务信息
        Assignment mainAssignment = AssignmentAbility.queryAssignment(
                networkChangeOrderId, Lists.newArrayList(ASSIGNMENT_CODE), Assignment.class);

        // 2.包装基本跳转参数
        // 主键 + 实体对象编码 + 页面ID + 页面状态
        basicForwardInfo.put(PK_ID, batchTask.getId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(SubcontractorBatchTask.class));
        basicForwardInfo.put(PAGE_ID, DetailPageEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.getPageId());
        basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 5.包装扩展跳转参数
        // 页面标题 + 任务状态 + 提交人 + 操作类型
        customerForwardInfo.put(PAGE_TITLE, mainAssignment.getAssignmentCode());
        customerForwardInfo.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(CREATE_BY, assignment.getCreateBy());
        customerForwardInfo.put(OPERATION_KEY, OPERATION_VIEW);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 获取故障处理任务跳转参数
     * @param assignmentId
     * @param clickField
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getForwardFaultHandleInfo(String assignmentId, String clickField) {
        // 1.检索故障管理任务
        FaultManagementAssignment assignment = AssignmentAbility.querySpecificTypeAssignment(assignmentId, FaultManagementAssignment.class);
        if (Objects.isNull(assignment)) {
            return Pair.of(new HashMap<>(), new HashMap<>());
        }

        // 2.跳转对应页面
        switch (clickField) {
            case COMMON_ASSIGNMENT_NAME:
                return getFaultInfo(assignment);

            case FAULT_WAR_ROOM_NAME:
                return getWarRoomInfo(assignment);

            default:
                return Pair.of(new HashMap<>(), new HashMap<>());
        }
    }

    /**
     * 跳转 故障管理任务 详情
     * @param assignment
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getFaultInfo(FaultManagementAssignment assignment) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID
        basicForwardInfo.put(PK_ID, assignment.getBillId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(FaultManagementOrder.class));
        basicForwardInfo.put(PAGE_ID, PAGE_BILL_FAULT_MANAGEMENT_BILL);
        basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.EDIT.name());

        // 2.包装扩展跳转参数
        // (1) 页面标题 + 当前进展
        customerForwardInfo.put(PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(CURRENT_PROGRESS, assignment.getCurrentProgress());
        customerForwardInfo.put(ASSIGNMENT_CODE, assignment.getAssignmentCode());
        customerForwardInfo.put(FaultManagementFieldConsts.WAR_ROOM, assignment.getWarRoom());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转 WarRoom 详情
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getWarRoomInfo(FaultManagementAssignment assignment) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID
        basicForwardInfo.put(PK_ID, assignment.getBillId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(FaultManagementOrder.class));
        basicForwardInfo.put(PAGE_ID, PAGE_BILL_WAR_ROOM_DETAIL);
        basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 2.包装扩展跳转参数
        // (1) 页面标题 + WarRoomId
        customerForwardInfo.put(PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(FaultManagementFieldConsts.WAR_ROOM, assignment.getWarRoom());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转 操作计划 详情页
     * @param assignmentId
     * @param clickField
     * @return Pair<Map<String, String>, Map<String, Object>>
     * left - 跳转基本参数map， map - key 页面参数名称  value 页面参数值
     * right - 跳转扩展参数map， map - key 页面参数名称  value 页面参数值
     */
    private Pair<Map<String, String>, Map<String, Object>> getInteriorOperationPlanInfo(String assignmentId, String clickField) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.点击字段校验
        if (!COMMON_ASSIGNMENT_NAME.equals(clickField)) {
            return Pair.of(basicForwardInfo, customerForwardInfo);
        }

        // 2.查询操作计划任务实例
        PlanOperationAssignment assignment =
                AssignmentAbility.querySpecificTypeAssignment(assignmentId, PlanOperationAssignment.class);
        if (Objects.isNull(assignment)) {
            return Pair.of(basicForwardInfo, customerForwardInfo);
        }

        // 3.基础信息
        // 主键 + 实体对象编码 + 页面状态
        basicForwardInfo.put(PK_ID, assignment.getBillId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(PlanOperationOrder.class));
        basicForwardInfo.put(PAGE_ID, PAGE_PLAN_OPERATION_BILL);
        basicForwardInfo.put(PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 4.补充信息
        // 页面标题 + 任务状态 + 任务ID
        customerForwardInfo.put(PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(TASK_ID, assignment.getBillId());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }
}
