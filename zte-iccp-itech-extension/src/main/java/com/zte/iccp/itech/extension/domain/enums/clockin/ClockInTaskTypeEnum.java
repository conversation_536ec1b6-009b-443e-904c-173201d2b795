package com.zte.iccp.itech.extension.domain.enums.clockin;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/09/05
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ClockInTaskTypeEnum implements SingletonTextValuePairsProvider {
    /** 操作任务 */
    OPERATION("操作任务", "operation"),
    /** 值守任务 */
    ON_DUTY("值守任务", "on-duty"),
    ;

    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }
}
