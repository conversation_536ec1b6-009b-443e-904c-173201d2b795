package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.RESULT_REVIEW_FLAG;

/**
 * <AUTHOR>
 * @date 2025/7/31 下午4:23
 */
public class UpdateResultReviewFlagPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String batchId = body.getBusinessId();
        String resultReviewFlag = (String) body.getVariables().get(RESULT_REVIEW_FLAG);
        String flowCode = body.getFlowCode();
        Map<String, Object> values = Maps.newHashMap();
        if (ApprovalConstants.BATCH_TASK_FLOW.equals(flowCode)) {
            values.put(RESULT_REVIEW_FLAG, resultReviewFlag);
            SaveDataHelper.update(BatchTask.class, batchId, values);
        } else {
            values.put(RESULT_REVIEW_FLAG, resultReviewFlag);
            SaveDataHelper.update(SubcontractorBatchTask.class, batchId, values);
        }

        return false;
    }
}
