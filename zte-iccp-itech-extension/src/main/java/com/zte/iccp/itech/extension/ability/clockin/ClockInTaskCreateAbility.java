package com.zte.iccp.itech.extension.ability.clockin;

import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.clockin.callplan.ClockInCallPlanCreateAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.*;
import com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallReasonEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationStageClockIn;
import com.zte.iccp.itech.extension.domain.model.subentity.SubcontractorOperationStageClockIn;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.platform.domain.i18n.I18nUtil;
import com.zte.paas.lcap.platform.util.IdGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.LookupValueConstant.OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ENTITY_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.ABOLISHED;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.BATCH_TASK_ID;

/**
 * <AUTHOR>
 * @since 2024/09/07
 */
@Slf4j
public class ClockInTaskCreateAbility {

    private static final int INTERVAL_HOURS = 2;

    private final Class<? extends BaseEntity> entity;

    private final IBatchTask batchTask;

    private final IChangeOrder changeOrder;

    private final List<BatchTaskOperator> operators;

    private final List<OperationStageClockIn> operationStageClockIns;

    private final Map<String, Set<OperatorRoleEnum>> operationGroup = MapUtils.newHashMap();

    private final Map<String, Set<OperatorRoleEnum>> onDutyGroup = MapUtils.newHashMap();

    private static final Map<Class<? extends BaseEntity>, Config> CONFIGS = MapUtils.newHashMap(
            BatchTask.class, new Config(
                    ChangeOrder.class,
                    Lists.newArrayList(
                            ChangeOrderFieldConsts.OPERATION_LEVEL,
                            ChangeOrderFieldConsts.OPERATION_TYPE_GROUP,
                            ChangeOrderFieldConsts.RESPONSIBLE_DEPT,
                            ChangeOrderFieldConsts.PRODUCT_CATEGORY,
                            ChangeOrderFieldConsts.OPERATION_SUBJECT,
                            ChangeOrderFieldConsts.OPERATION_TYPE,
                            ChangeOrderFieldConsts.GUARANTEE_MODE,
                            ChangeOrderFieldConsts.TIME_ZONE,
                            ChangeOrderFieldConsts.IS_BUSINESS_OPERATION,
                            ChangeOrderFieldConsts.IS_FIRST_APPLICATION,
                            ChangeOrderFieldConsts.CUSTOMER_ID,
                            ChangeOrderFieldConsts.TRIGGER_TYPE),
                    BatchTaskOperator.class,
                    OperationStageClockIn.class),
            SubcontractorBatchTask.class, new Config(
                    SubcontractorChangeOrder.class,
                    Lists.newArrayList(
                            SubcontractorChangeOrderFieldConsts.OPERATION_LEVEL,
                            SubcontractorChangeOrderFieldConsts.OPERATION_TYPE_GROUP,
                            SubcontractorChangeOrderFieldConsts.ORGANIZATION_ID,
                            SubcontractorChangeOrderFieldConsts.PRODUCT_ID,
                            SubcontractorChangeOrderFieldConsts.OPERATION_SUBJECT,
                            SubcontractorChangeOrderFieldConsts.OPERATION_TYPE,
                            ChangeOrderFieldConsts.GUARANTEE_MODE,
                            SubcontractorChangeOrderFieldConsts.TIME_ZONE,
                            SubcontractorChangeOrderFieldConsts.IS_BUSINESS_OPERATION,
                            // 分包商变更单没有“是否首次应用”字段，默认为“否”
                            SubcontractorChangeOrderFieldConsts.CUSTOMER_ID,
                            SubcontractorChangeOrderFieldConsts.TRIGGER_TYPE),
                    SubcontractorBatchOperator.class,
                    SubcontractorOperationStageClockIn.class));

    public ClockInTaskCreateAbility(Class<? extends BaseEntity> entity, String batchTaskId) {
        Config cfg = CONFIGS.get(entity);

        this.entity = entity;
        batchTask = (IBatchTask) QueryDataHelper.get(
                entity, cfg.batchTaskFields, batchTaskId);
        changeOrder = (IChangeOrder) QueryDataHelper.get(
                cfg.changeOrderEntity, cfg.changeOrderFields, batchTask.getChangeOrderId());
        operators = QueryDataHelper.query(
                cfg.operatorEntity, cfg.operatorFields, batchTaskId);
        operationStageClockIns = QueryDataHelper.query(
                cfg.operationStageClockInEntity, cfg.operationStageClockInFields, batchTaskId);
    }

    public void create() {
        DeptTypeEnum deptType = ChangeOrderAbility.getDeptType(changeOrder);
        // 部分国际办事处不创建打卡任务 & 避免重复创建 & 配合保障+远程交付（内部网络变更单）不创建打卡任务
        boolean notCreateClockInTaskFlag = (deptType == DeptTypeEnum.INTER
                    && !BatchTaskAbility.getInterClockInOrgIds().contains(changeOrder.getResponsibleDept()))
                || alreadyExists() || isRemoteCooperationGuarantee();
        if (notCreateClockInTaskFlag) {
            return;
        }

        for (BatchTaskOperator operator : operators) {
            switch (operator.getOperatorRole()) {
                case OPERATING_SUPERVISOR:
                    addCheckRemote(operationGroup, operator);
                    break;
                case OPERATOR:
                    addRole(operationGroup, operator);
                    break;
                case CROSS_CHECKER:
                    handleCrossChecker(operator);
                    break;
                case WATCHMAN:
                    handleWatchMan(operator);
                    break;
                default:
                    //其他角色不创建打卡任务
                    break;
            }
        }

        Map<String, SingleEmployee> userId2Employee = getUserId2Employee();
        List<ClockInTask> clockInTasks = Lists.newArrayList();

        // 操作打卡任务
        operationGroup.forEach((userId, roles) -> {
            ClockInTask clockInTask = newClockInTask();
            clockInTask.setTaskType(ClockInTaskTypeEnum.OPERATION);
            clockInTask.setOperator(userId2Employee.get(userId));
            clockInTask.setOperatorRoles(new ArrayList<>(roles));

            fillOperationInfos(clockInTask);
            clockInTasks.add(clockInTask);
        });

        // 值守打卡任务
        onDutyGroup.forEach((userId, roles) -> {
            ClockInTask clockInTask = newClockInTask();
            clockInTask.setTaskType(ClockInTaskTypeEnum.ON_DUTY);
            clockInTask.setOperator(userId2Employee.get(userId));
            clockInTask.setOperatorRoles(new ArrayList<>(roles));

            fillOnDutyInfos(clockInTask);
            clockInTasks.add(clockInTask);
        });
        // 若存在打卡任务，则初始化create_clock_in_task字段
        if (!CollectionUtils.isEmpty(clockInTasks)) {
            updateCreateClockInTask(batchTask.getId());
            updateCreateClockInTask(batchTask.getChangeOrderId());
        }
        // 初始化clock_in_task表中的last_check_in_utc_time_batch_task字段
        clockInTasks.forEach(task -> task.setLastCheckInUtcTimeBatchTask(System.currentTimeMillis()));

        TransactionHelper.run(() -> {
            SaveDataHelper.batchCreate(clockInTasks);
            createCallPlan(clockInTasks);
        });
    }

    public static void updateCreateClockInTask(String entityId) {
        // 获取到对应的任务对应的assignment id
        IFilter entityIdFilter = new Filter(ENTITY_ID, Comparator.EQ, entityId);
        NetworkChangeAssignment assignment = QueryDataHelper.queryOne(NetworkChangeAssignment.class,
                Lists.newArrayList(),
                Lists.newArrayList(entityIdFilter));
        if (null == assignment) {
            return;
        }

        NetworkChangeAssignment batchTaskCopy = new NetworkChangeAssignment();
        batchTaskCopy.setId(assignment.getId());
        // 赋值create_clock_in_task
        batchTaskCopy.setCreateClockInTaskOrNot(INTEGER_ONE);
        SaveDataHelper.update(batchTaskCopy);
    }

    private boolean isRemoteCooperationGuarantee() {
        // 保障方式==远程保障 不创建打卡任务
        List<String> guaranteeModeList = TextValuePairHelper.getValueList(changeOrder.getGuaranteeMode());

        return !guaranteeModeList.isEmpty()
                && STR_TWO.equals(guaranteeModeList.get(INTEGER_ZERO));
    }

    private boolean isHistory() {
        OperationStageClockIn operationPrepStage = operationStageClockIns.stream()
                .filter(item -> Lists.newArrayList(MsgUtils.getLangMessage(ZH_CN, OPERATION_PREPARATION),
                        MsgUtils.getLangMessage(EN_US, OPERATION_PREPARATION)).contains(item.getOperationPhase()))
                .findAny().orElse(null);

        if (operationPrepStage == null) {
            String zhCached = MsgUtils.getLangMessage(ZH_CN, OPERATION_PREPARATION);
            String enCached = MsgUtils.getLangMessage(EN_US, OPERATION_PREPARATION);
            String zhDirect = I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), OPERATION_PREPARATION);
            log.error("OPERATION_PREPARATION stage not found - batchTaskId: {}, available stages: {}, " +
                            "cached messages: ZH='{}', EN='{}', direct message: '{}'",
                    batchTask.getId(),
                    operationStageClockIns.stream().map(OperationStageClockIn::getOperationPhase).collect(Collectors.toList()),
                    zhCached, enCached, zhDirect);
            throw new NullPointerException("OPERATION_PREPARATION stage not found");
        }

        OperationStageClockIn operationImplStage = operationStageClockIns.stream()
                .filter(item -> Lists.newArrayList(MsgUtils.getLangMessage(ZH_CN, OPERATION_IMPLEMENTATION),
                        MsgUtils.getLangMessage(EN_US, OPERATION_IMPLEMENTATION)).contains(item.getOperationPhase()))
                .findAny().orElse(null);

        if (operationImplStage == null) {
            String zhCached = MsgUtils.getLangMessage(ZH_CN, OPERATION_IMPLEMENTATION);
            String enCached = MsgUtils.getLangMessage(EN_US, OPERATION_IMPLEMENTATION);
            String zhDirect = I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), OPERATION_IMPLEMENTATION);
            log.error("OPERATION_IMPLEMENTATION stage not found - batchTaskId: {}, available stages: {}, " +
                            "cached messages: ZH='{}', EN='{}', direct message: '{}'",
                    batchTask.getId(),
                    operationStageClockIns.stream().map(OperationStageClockIn::getOperationPhase).collect(Collectors.toList()),
                    zhCached, enCached, zhDirect);
            throw new NullPointerException("OPERATION_IMPLEMENTATION stage not found");
        }

        OperationStageClockIn operationTestStage = operationStageClockIns.stream()
                .filter(item -> Lists.newArrayList(MsgUtils.getLangMessage(ZH_CN, TEST_VERIFICATION),
                        MsgUtils.getLangMessage(EN_US, TEST_VERIFICATION)).contains(item.getOperationPhase()))
                .findAny().orElse(null);

        if (operationTestStage == null) {
            String zhCached = MsgUtils.getLangMessage(ZH_CN, TEST_VERIFICATION);
            String enCached = MsgUtils.getLangMessage(EN_US, TEST_VERIFICATION);
            String zhDirect = I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), TEST_VERIFICATION);
            log.error("TEST_VERIFICATION stage not found - batchTaskId: {}, available stages: {}, " +
                            "cached messages: ZH='{}', EN='{}', direct message: '{}'",
                    batchTask.getId(),
                    operationStageClockIns.stream().map(OperationStageClockIn::getOperationPhase).collect(Collectors.toList()),
                    zhCached, enCached, zhDirect);
            throw new NullPointerException("TEST_VERIFICATION stage not found");
        }

        return null == operationPrepStage.getStageStartTime() || null == operationPrepStage.getStageEndTime()
                || null == operationImplStage.getStageStartTime() || null == operationImplStage.getStageEndTime()
                || null == operationTestStage.getStageStartTime() || null == operationTestStage.getStageEndTime();
    }

    private boolean alreadyExists() {
        return QueryDataHelper.queryCount(ClockInTask.class, Lists.newArrayList(
                new Filter(BATCH_TASK_ID, Comparator.EQ, batchTask.getId()),
                new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(BoolEnum.N)))) > 0;
    }

    private void handleCrossChecker(BatchTaskOperator operator) {
        if (changeOrder.getOperationLevel() != OperationLevelEnum.CRITICAL) {
            return;
        }

        addCheckRemote(operationGroup, operator);
    }

    private void handleWatchMan(BatchTaskOperator operator) {
        if (OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE.equals(changeOrder.getOperationTypeGroup())) {
            return;
        }

        addCheckRemote(onDutyGroup, operator);
    }

    private Map<String, SingleEmployee> getUserId2Employee() {
        return operators.stream()
                .distinct()
                .collect(Collectors.toMap(
                        e -> e.getOperatePerson().getId(),
                        BatchTaskOperator::getOperatePerson,
                        (a, b) -> a));
    }

    private ClockInTask newClockInTask() {
        ClockInTask clockInTask = new ClockInTask();
        clockInTask.setId(IdGenerator.genInstanceDataId());
        clockInTask.setEntityType(BatchTaskTypeEnum.fromEntityClass(entity));
        clockInTask.setChangeOrderId(changeOrder.getId());
        clockInTask.setBatchTaskId(batchTask.getId());
        clockInTask.setAbolished(BoolEnum.N);
        clockInTask.setBatchCode(batchTask.getBatchCode());
        clockInTask.setResponsibleDept(changeOrder.getResponsibleDept());
        clockInTask.setProductClassification(changeOrder.getProductCategory());
        clockInTask.setOperationSubject(changeOrder.getOperationSubject());
        clockInTask.setOperationType(changeOrder.getOperationType());
        clockInTask.setOperationLevel(changeOrder.getOperationLevel());
        clockInTask.setTimeZone(changeOrder.getTimeZone());
        return clockInTask;
    }

    private void fillOperationInfos(ClockInTask clockInTask) {
        if (isHistory()) {
            Date t1 = batchTask.getPlanOperationStartTime();
            Date t2 = batchTask.getPlanOperationEndTime();

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(t1);
            calendar.add(Calendar.HOUR, -INTERVAL_HOURS);
            Date t0 = calendar.getTime();

            calendar.setTime(t2);
            calendar.add(Calendar.HOUR, INTERVAL_HOURS);
            Date t3 = calendar.getTime();

            clockInTask.setPlanPrepareStartTime(t0);
            clockInTask.setPlanPrepareEndTime(t1);
            clockInTask.setPlanExecuteStartTime(t1);
            clockInTask.setPlanExecuteEndTime(t2);
            clockInTask.setPlanTestStartTime(t2);
            clockInTask.setPlanTestEndTime(t3);
            return;
        }

        OperationStageClockIn operationPrepStage = operationStageClockIns.stream()
                .filter(item -> Lists.newArrayList(MsgUtils.getLangMessage(ZH_CN, OPERATION_PREPARATION),
                        MsgUtils.getLangMessage(EN_US, OPERATION_PREPARATION)).contains(item.getOperationPhase()))
                .findAny().orElseThrow(NullPointerException::new);

        OperationStageClockIn operationImplStage = operationStageClockIns.stream()
                .filter(item -> Lists.newArrayList(MsgUtils.getLangMessage(ZH_CN, OPERATION_IMPLEMENTATION),
                        MsgUtils.getLangMessage(EN_US, OPERATION_IMPLEMENTATION)).contains(item.getOperationPhase()))
                .findAny().orElseThrow(NullPointerException::new);

        OperationStageClockIn operationTestStage = operationStageClockIns.stream()
                .filter(item -> Lists.newArrayList(MsgUtils.getLangMessage(ZH_CN, TEST_VERIFICATION),
                        MsgUtils.getLangMessage(EN_US, TEST_VERIFICATION)).contains(item.getOperationPhase()))
                .findAny().orElseThrow(NullPointerException::new);

        clockInTask.setPlanPrepareStartTime(operationPrepStage.getStageStartTime());
        clockInTask.setPlanPrepareEndTime(operationPrepStage.getStageEndTime());
        clockInTask.setPlanExecuteStartTime(operationImplStage.getStageStartTime());
        clockInTask.setPlanExecuteEndTime(operationImplStage.getStageEndTime());
        clockInTask.setPlanTestStartTime(operationTestStage.getStageStartTime());
        clockInTask.setPlanTestEndTime(operationTestStage.getStageEndTime());
    }

    private void fillOnDutyInfos(ClockInTask clockInTask) {
        ClockInConfigAbility.OnDutyParams onDutyParams = ClockInConfigAbility.calcOnDutyParams(changeOrder);
        if (onDutyParams == null) {
            return;
        }

        double onDutyHours = onDutyParams.getTotalDurationHours();
        int onDutyFrequency = onDutyParams.getFrequency();

        OperationStageClockIn operationObservationStage = operationStageClockIns.stream()
                .filter(item -> Lists.newArrayList(MsgUtils.getLangMessage(ZH_CN, SERVICE_OBSERVATION),
                        MsgUtils.getLangMessage(EN_US, SERVICE_OBSERVATION)).contains(item.getOperationPhase()))
                .findAny().orElse(null);

        if (isHistory()) {
            Date t2 = batchTask.getPlanOperationEndTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(t2);
            calendar.add(Calendar.HOUR, INTERVAL_HOURS);
            Date t3 = calendar.getTime();

            calendar.add(Calendar.MINUTE, (int) (onDutyHours * 60));
            Date t4 = calendar.getTime();

            clockInTask.setPlanOnDutyStartTime(t3);
            clockInTask.setPlanOnDutyEndTime(t4);
        } else {
            Objects.requireNonNull(operationObservationStage);
            clockInTask.setPlanOnDutyStartTime(operationObservationStage.getStageStartTime());
            clockInTask.setPlanOnDutyEndTime(operationObservationStage.getStageEndTime());
        }

        clockInTask.setTotalOnDutyDuration(onDutyHours);
        clockInTask.setOnDutyFrequency(onDutyFrequency);
    }

    private void addCheckRemote(Map<String, Set<OperatorRoleEnum>> group, BatchTaskOperator operator) {
        Predicate<BatchTaskOperator> p = o ->
                o.getOperatorRole() == operator.getOperatorRole()
                        && o.getRemoteFlag() == RemoteEnum.LOCAL;
        if (operator.getRemoteFlag() == RemoteEnum.REMOTE
                && operators.stream().anyMatch(p)) {
            return;
        }

        addRole(group, operator);
    }

    private static void addRole(
            Map<String, Set<OperatorRoleEnum>> group,
            BatchTaskOperator operator) {
        String userId = operator.getOperatePersonEmpNo();
        OperatorRoleEnum role = operator.getOperatorRole();

        if (group.containsKey(userId)) {
            group.get(userId).add(role);
        } else {
            group.put(userId, Sets.newHashSet(role));
        }
    }

    private void createCallPlan(List<ClockInTask> clockInTasks) {
        // 仅国内工服处创建呼叫计划
        if (DeptTypeEnum.INNER != ResponsibleUtils.getDeptType(changeOrder.getResponsibleDept())) {
            return;
        }

        List<ClockInTask> operationTasks = clockInTasks.stream()
                .filter(t -> t.getTaskType() == ClockInTaskTypeEnum.OPERATION)
                .collect(Collectors.toList());

        // 兼容operaterPhone为null的情况
        Map<String, String> userPhoneNums = operators.stream()
                .collect(Collectors.toMap(
                        o -> o.getOperatePerson().getId(),
                        o -> o.getOperatorPhone() == null ? "" : o.getOperatorPhone(),
                        (a, b) -> a));

        Map<ClockInTask, String> operationPhoneNums = operationTasks.stream()
                .collect(Collectors.toMap(
                        Functions.identity(),
                        t -> userPhoneNums.getOrDefault(t.getOperator().getId(), StringUtils.EMPTY)));

        new ClockInCallPlanCreateAbility()
                .create4ClockInNotify(CallReasonEnum.PREPARE_START, operationPhoneNums);
    }

    @RequiredArgsConstructor
    private static class Config {
        private final Class<? extends BaseEntity> changeOrderEntity;

        private final List<String> changeOrderFields;

        private final Class<? extends BaseSubEntity> operatorEntity;

        private final Class<? extends BaseSubEntity> operationStageClockInEntity;

        private final List<String> batchTaskFields = Collections.unmodifiableList(Lists.newArrayList(
                BatchTaskFieldConsts.CHANGE_ORDER_ID,
                BatchTaskFieldConsts.BATCH_CODE,
                BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME,
                BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME
        ));

        private final List<String> operatorFields = Collections.unmodifiableList(Lists.newArrayList(
                OperatorFieldConsts.OPERATOR_ROLE,
                OperatorFieldConsts.OPERATE_PERSON,
                OperatorFieldConsts.OPERATOR_PHONE,
                OperatorFieldConsts.REMOTE_FLAG
        ));

        private final List<String> operationStageClockInFields = Collections.unmodifiableList(Lists.newArrayList(
                OperationStageClockInFieldConsts.OPERATION_PHASE,
                OperationStageClockInFieldConsts.OPERATION_DURATION,
                OperationStageClockInFieldConsts.STAGE_START_TIME,
                OperationStageClockInFieldConsts.STAGE_END_TIME
        ));
    }
}
