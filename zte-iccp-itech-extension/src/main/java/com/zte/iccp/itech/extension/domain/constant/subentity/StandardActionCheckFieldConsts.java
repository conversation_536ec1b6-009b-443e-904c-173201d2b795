package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/7 下午7:15
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class StandardActionCheckFieldConsts {

    /**
     * 检查分类
     */
    public static final String STANDARD_ACTION_CHECK_TYPE = "standard_action_check_type";

    /**
     * 检查分类 EN
     */
    public static final String STANDARD_ACTION_CHECK_TYPE_EN = "standard_action_check_type_en";

    /**
     * 检查内容
     */
    public static final String STANDARD_ACTION_CHECK_CONTENT = "standard_action_check_content";

    /**
     * 检查内容 en
     */
    public static final String STANDARD_ACTION_CHECK_CONTENT_EN = "standard_action_check_content_en";

    /**
     * 检查结果
     */
    public static final String STANDARD_ACTION_CHECK_RESULT = "standard_action_check_result";

    /**
     * 检查意见
     */
    public static final String STANDARD_ACTION_CHECK_RESULT_DESC = "standard_action_check_result_desc";

}
