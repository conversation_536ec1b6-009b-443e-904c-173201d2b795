package com.zte.iccp.itech.extension.plugin.form.faultmanagement.propertychanged;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.configuration.SatisfactionResponsiblePersonAbility;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.model.FaultManagementDetails;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.SatisfactionResponsiblePerson;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.PropertyChangedBaseFormPlugin;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.FIELD_SATISFACTION_RESPONSIBLE_PERSON_CID;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.FIELD_SATISFACTION_RESPONSIBLE_PERSON_FR_CID;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids.*;

/**
 * 【客户满意度责任人】插件
 * 规则不变，业务要求在【故障复盘】阶段显示展示
 *
 * 【客户满意度责任人】显示规则（隐藏/展示）由规则控制，该插件只计算值
 *
 * 和字段【需要故障复盘】/[故障横推整改]有关，当【需要故障复盘】为“否”或者用户进入【故障横推整改】时，
 * 需要计算和展示，用户可修改
 */
public class SatisfactionResponsiblePersonPlugin implements LoadDataBaseFormPlugin, PropertyChangedBaseFormPlugin {
    @Override
    public void operate(ValueChangedEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();
        setSatisfactionResponsiblePerson(formView, dataModel);
    }

    @Override
    public void loadData(LoadDataEventArgs args, FaultManagementDetails faultManagementDetails) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();
        setSatisfactionResponsiblePerson(formView, dataModel);
    }

    private void setSatisfactionResponsiblePerson(IFormView formView, IDataModel dataModel) {
        String faultReview = TextValuePairHelper.getValue(dataModel.getValue(FIELD_FAULT_REVIEW_CID));
        String faultRectification = TextValuePairHelper.getValue(dataModel.getValue(FIELD_FAULT_RECTIFICATION_CID));
        if ((!StringUtils.hasText(faultReview) || Y.equals(faultReview))
                && !StringUtils.hasText(faultRectification) ) {
            return;
        }

        // 国家
        Object areaCodeObj = dataModel.getValue(FIELD_AREA_CODE_CID);
        String areaCode = ObjectUtils.isEmpty(areaCodeObj) ? EMPTY_STRING : areaCodeObj.toString();

        // 产品线
        Object productLineObj = dataModel.getValue(FIELD_PRODUCT_LINE_CID);
        String productLine = TextValuePairHelper.getValue(productLineObj);

        // 产品经营团队
        Object productTeamObj = dataModel.getValue(FIELD_PRODUCT_TEAM_CID);
        String productTeam = TextValuePairHelper.getValue(productTeamObj);

        // 代表处
        Object orgabizationObj = dataModel.getValue(FIELD_ORGANIZATION_CID);
        String organization = TextValuePairHelper.getValue(orgabizationObj);

        List<SatisfactionResponsiblePerson> satisfactionResponsiblePersons
                = SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(areaCode, organization, productTeam, productLine);

        if (CollectionUtils.isEmpty(satisfactionResponsiblePersons)) {
            return;
        }
        List<Employee> managers = satisfactionResponsiblePersons.get(INTEGER_ZERO).getManager();
        List<Employee> productSectionChiefs = satisfactionResponsiblePersons.get(INTEGER_ZERO).getProductSectionChief();
        List<Employee> mergedEmployees = Stream.concat(managers.stream(), productSectionChiefs.stream())
                .collect(Collectors.toMap(
                        Employee::getEmpUIID,
                        employee -> employee,
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 回写到员工组件
        JSONArray employees = getEmployeeForm(dataModel, mergedEmployees);
        dataModel.setValue(FIELD_SATISFACTION_RESPONSIBLE_PERSON_CID, employees);
        dataModel.setValue(FIELD_SATISFACTION_RESPONSIBLE_PERSON_FR_CID, employees);

    }

    // 获取员工组件格式信息【客户满意度责任人】
    public JSONArray getEmployeeForm(IDataModel dataModel,
                                     List<Employee> mergedEmployees)
    {
        JSONArray array = new JSONArray();
        for (int i = 0; i < mergedEmployees.size(); i++) {
            Employee mergedEmployee = mergedEmployees.get(i);
            // 组装回写到员工组件中的数据
            JSONObject emp = new JSONObject();
            emp.put(ID_KEY, mergedEmployee.getId());
            emp.put(USER_NAME, mergedEmployee.getId());
            emp.put(ORG_ID_KEY, mergedEmployee.getOrgID());
            emp.put(EMP_NAME_KEY, mergedEmployee.getEmpNameCn());
            emp.put(EMP_NAME_CN_KEY, mergedEmployee.getEmpNameCn());
            emp.put(EMP_NAME_EN_KEY, mergedEmployee.getEmpNameEn());
            emp.put(EMP_UIID_KEY, mergedEmployee.getEmpUIID());
            emp.put(ORG_NAME_PATH_CN_KEY, mergedEmployee.getOrgNamePath());
            emp.put(ORG_NAME_PATH_EN_KEY, mergedEmployee.getOrgNamePathEn());
            array.add(emp);
        }
        return array;
    }
}
