package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.ability.ApproverConfigAbility.findApproverConfiguration;
import static com.zte.iccp.itech.extension.ability.ApproverConfigAbility.getApprovalPersonsByType;
import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.DEFAULT_APPROVER_KEY;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.PRODUCT_MODEL;

@Deprecated
public class RemoteDeliveryCenterHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {

    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        List<ApproverConfiguration> approverConfigurations = getApprovers(changeOrder);
        List<String> persons = new ArrayList<>();
        approverConfigurations.forEach(item ->
                persons.addAll(getApprovalPersonsByType(item, null)));
        if (CollectionUtils.isEmpty(approverConfigurations)) {
            // 先返回临时管理员账号
            return ConfigHelper.getList(DEFAULT_APPROVER_KEY);
        }
        return persons.stream().distinct().collect(Collectors.toList());
    }

    public List<ApproverConfiguration> getApprovers(ChangeOrder changeOrder) {
        // 查询变跟单操作对象的产品型号idpath
        List<String> idPaths = new ArrayList<>();
        List<OperationObject> operationObjects = QueryDataHelper.
                query(OperationObject.class, Arrays.asList(PRODUCT_MODEL), changeOrder.getId());
        ApproverConfiguration queryParam = new ApproverConfiguration();
        if (!CollectionUtils.isEmpty(operationObjects)) {
            List<String> productIds = operationObjects.stream().map(OperationObject::getProductModel)
                    .collect(Collectors.toList());
            idPaths = NisAbility.queryProductModelIdPath(productIds);
            if (!CollectionUtils.isEmpty(idPaths)) {
                queryParam.setProductModelId(idPaths.get(CommonConstants.INTEGER_ZERO));
            }
        }

        List<ApproverConfiguration> approverConfigurations = Lists.newArrayList();
        String responsibleDept = changeOrder.getResponsibleDept();
        String productCategory = changeOrder.getProductCategory();
        // 代表处属于国内：【产品】（优先级：产品型号>产品小类）+【营销/片区】=国内营销+【是否政企】精确匹配，
        queryParam.setApprovalNode(ApprovalTypeEnum.REMOTE_CENTER_OPERATIONS);
        queryParam.setProdTeam(ProductUtils.getTeam(productCategory));
        queryParam.setProdLine(ProductUtils.getLine(productCategory));
        queryParam.setProdMainCategory(ProductUtils.getMain(productCategory));
        queryParam.setProdSubCategory(productCategory);
        queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));
        if (DeptTypeEnum.INNER == ResponsibleUtils.getDeptType(responsibleDept)) {
            queryParam.setIsGov(changeOrder.getIsGovEnt());
        } else {
            queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
        }
        return findApproverConfiguration(queryParam);
    }
}
