package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xaiowei 10309921
 * @since 2025/03/22
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class WireBusinessCheckFieldConsts {

    /**
     * 产品经营团队
     */
    public static final String PRODUCT_OPERATION_TEAM = "product_operation_team";

    /**
     * 产品线
     */
    public static final String PRODUCT_LINE = "product_line";

    /**
     * 产品大类
     */
    public static final String PRODUCT_MAIN_CATEGORY = "product_main_category";

    /**
     * 产品小类
     */
    public static final String PRODUCT_SUB_CATEGORY = "product_sub_category";

    /**
     * 产品类型
     */
    public static final String PRODUCT_MODEL = "product_model";

    /**
     * 文档名称
     */
    public static final String DOCUMENT_NAME = "document_name";

    /**
     * 发布链接
     */
    public static final String LINK = "link";

    /**
     * 备注说明
     */
    public static final String REMARK = "remark";
}