package com.zte.iccp.itech.extension.plugin.flow.changeorder.subcontractor;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;

/**
 * 分包商批次任务，操作计划审核通过
 *
 * <AUTHOR>
 * @since 2024/11/22
 */
public class SubcontractorPlanApprovalPlugin extends BaseFlowOperationPlugin {

    @Override
    public void beforeOperate(ExecuteEvent executeEvent) {
        // 操作结果
        String result = TextValuePairHelper.getValue(getModel().getValue(APPROVE_RESULT_OPERATION_PLAN));
        if (CommonConstants.PASS.equals(result)) {
            getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.OPERATION_EXECUTION.getValue());
            getModel().setValue(APPROVAL_STATUS, BoolEnum.Y.getPropValue());
        } else {
            getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());
            getModel().setValue(APPROVAL_STATUS, BoolEnum.N.getPropValue());
        }
    }
}
