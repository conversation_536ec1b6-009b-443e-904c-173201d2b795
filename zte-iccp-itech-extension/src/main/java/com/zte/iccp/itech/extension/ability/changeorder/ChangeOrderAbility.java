package com.zte.iccp.itech.extension.ability.changeorder;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.*;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.grantfile.ISupportStaffBase;
import com.zte.iccp.itech.extension.domain.model.subentity.*;
import com.zte.iccp.itech.extension.domain.model.vo.ChangeOrderSave;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.IdopChangeOrderDto;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.NetworkOfficeDTO;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.NetworkElementVo;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.common.helper.QueryDataHelper.queryOne;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.MobileApprove.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATOR_NAME;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ChangeOrderAbility {
    public static ChangeOrder get(String id, List<String> fields) {
        return QueryDataHelper.get(ChangeOrder.class, fields, id);
    }

    public static void update(ChangeOrder changeOrder) {
        if (Objects.isNull(changeOrder)) {
            return;
        }

        SaveDataHelper.update(changeOrder);
    }

    /**
     * 检索特定子实体数据
     *
     * @param pid
     * @param subEntityEnum
     * @param <T>
     * @return List<T>
     */
    public static <T extends BaseSubEntity> List<T> listSpecificChangeOrderSubEntity(String pid, Class<? extends BaseSubEntity> subEntityEnum) {
        // 1.子实体信息校验
        // (1) 父ID
        if (!StringUtils.hasText(pid)) {
            return Lists.newArrayList();
        }

        // (2) 是否网络变更单子类型
        if (!ChangeOrder.class.equals(EntityHelper.getMainClass(subEntityEnum))
                && !SubcontractorChangeOrder.class.equals(EntityHelper.getMainClass(subEntityEnum))) {
            return Lists.newArrayList();
        }

        // 2.检索子实体数据
        return QueryDataHelper.query(subEntityEnum, Lists.newArrayList(), pid);
    }

    /**
     * 检索特定子实体数据
     * @param pIds pIds
     * @param subEntityEnum subEntityEnum
     * @return List<T> T extends BaseSubEntity
     * @param <T> BaseSubEntity
     */
    public static <T extends BaseSubEntity> List<T> listSpecificChangeOrderSubEntity(List<String> pIds, Class<? extends BaseSubEntity> subEntityEnum) {
        // 1.子实体信息校验
        // (1) 父ID
        if (CollectionUtils.isEmpty(pIds)) {
            return Lists.newArrayList();
        }

        // (2) 是否网络变更单子类型
        if (Objects.isNull(subEntityEnum) || !ChangeOrder.class.equals(EntityHelper.getMainClass(subEntityEnum))) {
            return Lists.newArrayList();
        }

        // 2.检索子实体数据
        return QueryDataHelper.queryByPid(subEntityEnum, Lists.newArrayList(), pIds);
    }

    /**
     * 检索特定二级子实体数据
     *
     * @param mainEntityId
     * @param secondSubEntityEnum
     * @param <T>
     * @return List<T>
     */
    public static <T extends BaseSubEntity> List<T> listSpecificSecondSubEntity(String mainEntityId,
                                                                                Class<? extends BaseSubEntity> secondSubEntityEnum) {
        // 1.二级子实体信息校验
        // (1) 父ID
        if (!StringUtils.hasText(mainEntityId)) {
            return Lists.newArrayList();
        }

        // (2) 是否网络变更单二级子类型
        if (!ChangeOrder.class.equals(EntityHelper.getMainClass(secondSubEntityEnum))
                && !SubcontractorChangeOrder.class.equals(EntityHelper.getMainClass(secondSubEntityEnum))) {
            return Lists.newArrayList();
        }

        // 2.检索子实体数据
        //noinspection unchecked
        Class<? extends BaseSubEntity> subEntityEnum = (Class<? extends BaseSubEntity>) EntityHelper.getParentClass(secondSubEntityEnum);
        List<BaseSubEntity> subEntityList = QueryDataHelper.query(subEntityEnum, Lists.newArrayList(), mainEntityId);
        if (CollectionUtils.isEmpty(subEntityList)) {
            return Lists.newArrayList();
        }

        // 3.检索二级子实体数据
        List<String> pidList = subEntityList.stream().map(BaseSubEntity::getId).collect(Collectors.toList());
        return QueryDataHelper.query(secondSubEntityEnum, Lists.newArrayList(), pidList, Lists.newArrayList());
    }

    @SneakyThrows
    public static ChangeOrder getApprover(String id) {
        /* Started by AICoder, pid:w591fic09fbc322140160abd80a9080b9b594224 */
        Field[] fields = ChangeOrderFieldConsts.ApproverFieldConsts.class.getDeclaredFields();
        List<String> fieldNameList = new ArrayList<>();
        for (Field field : fields) {
            if (field.getType().isPrimitive() || field.getType().equals(String.class)) {
                fieldNameList.add((String) field.get(null));
            }
        }
        /* Ended by AICoder, pid:w591fic09fbc322140160abd80a9080b9b594224 */
        return QueryDataHelper.get(ChangeOrder.class, fieldNameList, id);
    }


    /**
     * 获取变更单支持人员
     *
     * @param changeOrderId 变更单id
     * @param supportStaffClass 变更单支持人员实体
     */
    public static List<String> getSupportStaffList(String changeOrderId, Class<? extends BaseSubEntity> supportStaffClass) {
        List<String> resultList = new ArrayList<>();
        if (changeOrderId == null) {
            return resultList;
        }
        List<SupportStaff> supportStaffList = CollectionUtilsEx.copyListProperties(ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                changeOrderId, supportStaffClass), SupportStaff.class);
        if (CollectionUtils.isEmpty(supportStaffList)) {
            return resultList;
        }
        return supportStaffList.stream()
                .filter(item -> item.getPerson() != null)
                .map(item -> item.getPerson().getEmpUIID()).collect(Collectors.toList());
    }

    /**
     * 获取变更单邮件人员集合
     *
     * @param iChangeOrder changeOrder
     * @return 人员集合
     */
    public static List<String> getChangeOrderPersonList(IChangeOrder iChangeOrder) {
        List<String> resultList = new ArrayList<>();
        if (iChangeOrder == null) {
            return resultList;
        }

        // 公共字段
        List<Employee> employeeList = Stream.of(
                        iChangeOrder.getMailCopyExt(),
                        iChangeOrder.getMailCopy1Ext(),
                        iChangeOrder.getMailCopy2Ext(),
                        iChangeOrder.getMailCopy3Ext(),
                        iChangeOrder.getMailCopy4Ext(),
                        iChangeOrder.getMailCopy5Ext(),
                        iChangeOrder.getMailCopy6Ext())
                //过滤掉为null的集合数据
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        // 内部变更单自己单独的字段
        if (iChangeOrder instanceof ChangeOrder) {
            List<Employee> changeSpecialEmployees = getChangeSpecialEmployees((ChangeOrder) iChangeOrder);
            if (!CollectionUtils.isEmpty(changeSpecialEmployees)) {
                if (CollectionUtils.isEmpty(employeeList)) {
                    employeeList = new ArrayList<>();
                }
                employeeList.addAll(changeSpecialEmployees);
            }
        }

        if (!CollectionUtils.isEmpty(employeeList)) {
            resultList = employeeList.stream().map(Employee::getEmpUIID).collect(Collectors.toList());
        }
        return resultList;
    }

    /**
     * 变更单自己特殊的人
     *
     * @param changeOrder changeOrder
     * @return List<Employee>
     */
    private static List<Employee> getChangeSpecialEmployees(ChangeOrder changeOrder) {
        return Stream.of(
                        changeOrder.getOfficeSolutionReviewer(),
                        changeOrder.getManagerExt(),
                        changeOrder.getMailCopy7Ext(),
                        changeOrder.getMailCopy8Ext(),
                        changeOrder.getMailCopy9Ext(),
                        changeOrder.getMailCopy10Ext(),
                        changeOrder.getMailCopy11Ext(),
                        changeOrder.getMailCopy12Ext(),
                        changeOrder.getMailCopy13Ext(),
                        changeOrder.getMailCopy14Ext(),
                        changeOrder.getMailCopy15Ext(),
                        changeOrder.getMailCopy16Ext(),
                        changeOrder.getNetworkChiefEngineerEmail())
                //过滤掉为null的集合数据
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /* IDOP 创建网络变更单 */
    public static void createChangeOrder(IdopChangeOrderDto dto) {
        // 创建批次概要
        BatchSummary batchSummary = new BatchSummary();
        batchSummary.setPid(dto.getChangeOrderId());
        batchSummary.setBatchNo(TextValuePairHelper.buildList("1", "1", "1"));
        batchSummary.setNetworkElementCount(dto.getNeCount());
        batchSummary.setPlanOperationStartTime(dto.getOperationStartTime());
        batchSummary.setPlanOperationEndTime(dto.getOperationEndTime());
        batchSummary.setTimeConflict(BoolEnum.valueOf(dto.isTimeConflict()).getValue());
        SaveDataHelper.create(batchSummary);

        // 操作对象
        List<NetworkOfficeDTO> networks = dto.getNetwork();
        List<String> officeIds = networks.stream()
                .map(NetworkOfficeDTO::getOfficeId)
                .filter(StringUtils::hasText).distinct()
                .collect(Collectors.toList());
        Map<String, NetworkElementVo> networkElementVoMap = NisAbility.queryNisElmentMap(officeIds);
        if (!CollectionUtils.isEmpty(networks)) {
            List<OperationObject> operationObjectList = new ArrayList<>();
            for (int rowIndex = 0; rowIndex < networks.size(); rowIndex++) {
                NetworkOfficeDTO item = networks.get(rowIndex);
                OperationObject operationObject = new OperationObject();
                operationObject.setBatchNo(TextValuePairHelper.buildList("1", "1", "1"));
                // 首个操作对象设置为【主产品】
                if (rowIndex == 0) {
                    operationObject.setIsMainProduct(TextValuePairHelper.buildList(Y, EMPTY_STRING, EMPTY_STRING));
                }
                operationObject.setPid(dto.getChangeOrderId());
                operationObject.setNetworkId(item.getNetworkId());
                operationObject.setOfficeName(item.getOfficeName());
                NetworkElementVo networkElementVo = networkElementVoMap.get(item.getOfficeId());
                if(networkElementVo != null ){
                    operationObject.setProductModel(networkElementVo.getProdModelId());
                }
                operationObjectList.add(operationObject);
            }
            SaveDataHelper.batchCreate(operationObjectList);
        }
        // 操作人员
        List<String> operators = dto.getOperators();
        List<TextValuePair> btach = TextValuePairHelper.buildList("1", "1", "1");
        if (!CollectionUtils.isEmpty(operators)) {
            List<Employee> employees = HrClient.queryEmployeeInfo(operators);
            Map<String, Employee> epmNoMap = employees.stream()
                    .collect(Collectors.toMap(Employee::getEmpUIID, Function.identity()));
            List<Operator> operatorList = new ArrayList<>();
            operators.forEach(item -> {
                Employee employee = epmNoMap.get(item);
                if (employee != null) {
                    Operator operator = new Operator();
                    SingleEmployee singleEmployee = new SingleEmployee();
                    BeanUtils.copyProperties(employee, singleEmployee);
                    operator.setOperatorName(singleEmployee);
                    operator.setPid(dto.getChangeOrderId());
                    operator.setOperatorBatchNo(btach);
                    operator.setOperatorRole(OperatorRoleEnum.OPERATOR);
                    operator.setOperatorDepartment(LangUtils.get(employee.getOrgNamePath(), employee.getOrgNamePathEn()));
                    operatorList.add(operator);
                }
            });
            // 预制其他角色数据
            List<OperatorRoleEnum> prefabrications = new ArrayList<>(Arrays.asList(OperatorRoleEnum.OPERATING_SUPERVISOR,
                    OperatorRoleEnum.CROSS_CHECKER, OperatorRoleEnum.WATCHMAN, OperatorRoleEnum.TESTER));
            if (CollectionUtils.isEmpty(operatorList)) {
                prefabrications.add(OperatorRoleEnum.OPERATOR);
            }
            prefabrications.forEach(item -> {
                Operator operator = new Operator();
                operator.setPid(dto.getChangeOrderId());
                operator.setOperatorRole(item);
                operator.setOperatorBatchNo(btach);
                operatorList.add(operator);
            });
            SaveDataHelper.batchCreate(operatorList);
        }
    }

    public static void saveAssignmentTask(ChangeOrderSave changeOrder, IdopChangeOrderDto dto, String id) {
        String createBy = dto.getCreateBy();
        List<Employee> responsible = HrClient.queryEmployeeInfo(Lists.newArrayList(createBy));
        // 创建任务表
        NetworkChangeAssignment assignment = new NetworkChangeAssignment();
        assignment.setEntityId(dto.getChangeOrderId());
        assignment.setBillType(BillTypeEnum.NETWORK_CHANGE.getPropValue());
        assignment.setApprovalTaskFlag(BoolEnum.N);
        assignment.setAssignmentName(dto.getOrderName());
        assignment.setAssignmentCode(dto.getOrderNo());
        assignment.setBillId(dto.getChangeOrderId());
        assignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE.getPropValue());
        assignment.setAssignmentStatus(AssignmentStatusEnum.START.getValue());
        assignment.setResponsibleEmployee(responsible);
        assignment.setPlanStartTime(dto.getOperationStartTime());

        String timezone = TimeZoneEnum.historyLookupCode2New(dto.getTimeZone());
        assignment.setTimeZone(timezone);
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneEnum(timezone);
        // 如果不是北京时间，需要根据时区转换成北京时间;否则，直接取用户输入的计划操作开始时间即可
        if (timeZoneEnum != null && TimeZoneEnum.BEIJING != timeZoneEnum) {
            assignment.setOperationStartTimeUtc8(
                    TimeZoneEnum.BEIJING.pollute(
                            timeZoneEnum.fix(dto.getOperationStartTime())));
        } else {
            assignment.setOperationStartTimeUtc8(dto.getOperationStartTime());
        }

        assignment.setCurrentProgress(ApproveNodeEnum.DRAFT.name());
        assignment.setCurrentProcessorEmployee(responsible);
        assignment.setProductClassification(TextValuePairHelper.objectTransferList(changeOrder.getProductCategory()));
        assignment.setCustomerClassification(TextValuePairHelper.buildList(changeOrder.getCustomerTypeFlag(),
                changeOrder.getCustomerTypeFlag(), changeOrder.getCustomerTypeFlag()));
        assignment.setOperationType(TextValuePairHelper.objectTransferList(changeOrder.getOperationType()));

        String organize = TextValuePairHelper.getValue(changeOrder.getResponsibleDept());
        String[] organizes = organize.split(CommonConstants.FORWARD_SLASH);
        assignment.setMarketing(TextValuePairHelper.buildList(organizes[1], organizes[1], organizes[1]));
        assignment.setRepresentativeOffice(TextValuePairHelper.buildList(organizes[INTEGER_THREE],
                organizes[INTEGER_THREE], organizes[INTEGER_THREE]));
        assignment.setProductManagementTeam(changeOrder.getProductManagementTeam());
        assignment.setPlanStartTime(changeOrder.getOperationStartTime());
        if (!CollectionUtils.isEmpty(dto.getNetwork())) {
            List<String> networkIds = dto.getNetwork().stream()
                    .map(NetworkOfficeDTO::getNetworkId)
                    .filter(StringUtils::hasText)
                    .distinct().collect(Collectors.toList());
            List<TextValuePair> networkValues = new ArrayList<>();
            networkIds.forEach(item -> {
                networkValues.addAll(TextValuePairHelper.buildList(item, item, item));
            });
            assignment.setNetwork(networkValues);
        }
        if (StringUtils.hasText(id)) {
            assignment.setId(id);
            SaveDataHelper.update(assignment);

        } else {
            id = SaveDataHelper.create(assignment);
        }
        // 创建任务 - 人员关联关系
        AssignmentAbility.createAssignmentPersonRelevance(id, Arrays.asList(createBy));
    }

    /* IDOP 更新网络变更单 */
    public static void updateChangeOrder(IdopChangeOrderDto dto) {
        String changeOrderId = dto.getChangeOrderId();
        // 修改批次概要
        List<BatchSummary> batchSummaries =
                BatchSummaryAbility.listBatchSummary(changeOrderId, BatchSummary.class);
        if (!CollectionUtils.isEmpty(batchSummaries)) {
            BatchSummary batchSummary = batchSummaries.get(0);
            batchSummary.setNetworkElementCount(dto.getNeCount());
            batchSummary.setPlanOperationStartTime(dto.getOperationStartTime());
            batchSummary.setPlanOperationEndTime(dto.getOperationEndTime());
            batchSummary.setTimeConflict(BoolEnum.valueOf(dto.isTimeConflict()).getValue());
            batchSummary.clearEntityValue();
            SaveDataHelper.update(batchSummary);
        }

        // 更新操作对象
        updateOperationObjects(dto);
        // 更新操作人员
        updateOperators(dto);
    }

    private static void updateOperators(IdopChangeOrderDto dto) {
        List<String> operators = dto.getOperators();
        if (CollectionUtils.isEmpty(operators)) {
            return;
        }

        List<Employee> employees = HrClient.queryEmployeeInfo(operators);
        Map<String, Employee> epmNoMap = employees.stream()
                .collect(Collectors.toMap(Employee::getEmpUIID, Function.identity()));
        List<Operator> operatorList = new ArrayList<>();
        operators.forEach(item -> {
            Employee employee = epmNoMap.get(item);
            if (employee != null) {
                Operator operator = new Operator();
                SingleEmployee singleEmployee = new SingleEmployee();
                BeanUtils.copyProperties(employee, singleEmployee);
                operator.setOperatorName(singleEmployee);
                operator.setPid(dto.getChangeOrderId());
                operator.setOperatorRole(OperatorRoleEnum.OPERATOR);
                operator.setOperatorDepartment(LangUtils.get(employee.getOrgNamePath(), employee.getOrgNamePathEn()));
                operatorList.add(operator);
            }
        });
        List<Operator> hisOperators = listSpecificChangeOrderSubEntity(dto.getChangeOrderId(), Operator.class);
        hisOperators = hisOperators.stream()
                .filter(item -> OperatorRoleEnum.OPERATOR == item.getOperatorRole())
                .collect(Collectors.toList());

        List<String> toDelete = new ArrayList<>();
        for (Operator item : hisOperators) {
            if (operatorList.stream().noneMatch(t -> t.getOperatorEpmNo().equals(item.getOperatorEpmNo()))) {
                toDelete.add(item.getId());
            }
        }
        List<Operator> finalHisOperators = hisOperators;
        operatorList.removeIf(toAdd -> finalHisOperators.stream().anyMatch(t -> t.getOperatorEpmNo().equals(toAdd.getOperatorEpmNo())));
        SaveDataHelper.batchDelete(Operator.class, toDelete);
        SaveDataHelper.batchCreate(operatorList);
    }

    private static void updateOperationObjects(IdopChangeOrderDto dto) {
        List<NetworkOfficeDTO> networks = dto.getNetwork();
        List<String> officeIds = networks.stream()
                .map(NetworkOfficeDTO::getOfficeId)
                .filter(StringUtils::hasText).distinct()
                .collect(Collectors.toList());
        Map<String, NetworkElementVo> networkElementVoMap = NisAbility.queryNisElmentMap(officeIds);
        if (CollectionUtils.isEmpty(networks)) {
            return;
        }
        List<OperationObject> operationObjectList = new ArrayList<>();
        networks.forEach(item -> {
            OperationObject operationObject = new OperationObject();
            operationObject.setBatchNo(TextValuePairHelper.buildList("1", "1", "1"));
            operationObject.setPid(dto.getChangeOrderId());
            operationObject.setNetworkId(item.getNetworkId());
            operationObject.setOfficeName(item.getOfficeName());
            NetworkElementVo networkElementVo = networkElementVoMap.get(item.getOfficeId());
            if(networkElementVo != null ){
                operationObject.setProductModel(networkElementVo.getProdModelId());
            }
            operationObjectList.add(operationObject);
        });

        List<OperationObject> hisObjects = OperationObjectAbility.listOperationObject(dto.getChangeOrderId(), OperationObject.class);
        List<String> toDelete = new ArrayList<>();
        for (OperationObject item : hisObjects) {
            if (operationObjectList.stream().noneMatch(t -> networkEq(item, t))) {
                toDelete.add(item.getId());
            }
        }
        operationObjectList.removeIf(toAdd -> hisObjects.stream().anyMatch(e -> networkEq(e, toAdd)));
        SaveDataHelper.batchDelete(OperationObject.class, toDelete);
        SaveDataHelper.batchCreate(operationObjectList);
    }

    private static boolean networkEq(OperationObject a, OperationObject b) {
        return a.getNetworkId() .equals(b.getNetworkId())
                && a.getOfficeName().equals(b.getOfficeName())
                && a.getProductModel().equals(b.getProductModel());
    }

    public static ChangeOrderAll get(String id) {
        return QueryDataHelper.get(ChangeOrderAll.class, Lists.newArrayList(), id);
    }

    public static List<ChangeOrderAll> get(List<String> changeOrderIds) {
        if (CollectionUtils.isEmpty(changeOrderIds)) {
            return new ArrayList<>();
        }
        return QueryDataHelper.get(ChangeOrderAll.class, Lists.newArrayList(), changeOrderIds);
    }

    /**
     * 检索变更单数据集
     *
     * @param changeOrderIds 主键ids
     * @param entityEnum CHANGE_ORDER
     * @return 结果集
     * @param <T> <T>
     */
    public static <T extends BaseEntity> List<T> get(List<String> changeOrderIds, Class<T> entityEnum) {
        if (CollectionUtils.isEmpty(changeOrderIds)) {
            return new ArrayList<>();
        }
        return QueryDataHelper.get(entityEnum, Lists.newArrayList(), changeOrderIds);
    }

    /**
     * 检索变更单数据集
     *
     * @param changeOrderIds 主键ids
     * @param fields
     * @param entityEnum CHANGE_ORDER
     * @return 结果集
     * @param <T> <T>
     */
    public static <T extends BaseEntity> List<T> get(List<String> changeOrderIds, List<String> fields, Class<T> entityEnum) {
        if (CollectionUtils.isEmpty(changeOrderIds)) {
            return new ArrayList<>();
        }
        return QueryDataHelper.get(entityEnum, fields, changeOrderIds);
    }


    public static void batchUpdate(List<ChangeOrderAll> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        SaveDataHelper.batchUpdate(updateList);
    }

    public static List<String> batchCreate(List<ChangeOrderAll> changeOrderAlls) {
        if (CollectionUtils.isEmpty(changeOrderAlls)) {
            return new ArrayList<>();
        }
        return SaveDataHelper.batchCreate(changeOrderAlls);
    }

    public static DeptTypeEnum getDeptType(IChangeOrder changeOrder) {
        if (changeOrder.getResponsibleDept() == null) {
            return null;
        }

        if (Arrays.stream(INNER_OFFICE_ORG_CODE_PATH)
                .anyMatch(target -> changeOrder.getResponsibleDept().startsWith(target))) {
            return DeptTypeEnum.INNER;
        }

        return DeptTypeEnum.INTER;
    }

    /**
     * 检索内部网络变更单 - 单据编号
     * @param orderNo
     * @return ChangeOrder
     */
    public static ChangeOrder getByCoNo(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            return null;
        }
        List<String> fieldList = com.zte.iccp.itech.zlic.util.Lists.newArrayList();
        return queryOne(ChangeOrder.class,
                fieldList,
                com.google.common.collect.Lists.newArrayList(new Filter(ORDER_NO, Comparator.EQ, orderNo)));
    }

    /*
     * 提交  开始时间 结束时间 校验
     * */
    public static boolean checkTime(IDataModel model, IFormView view) {
        Object startTime = model.getValue(ChangeOrderFieldConsts.OPERATION_START_TIME);
        Object endTime = model.getValue(ChangeOrderFieldConsts.OPERATION_END_TIME);
        Object timeZone = model.getValue(ChangeOrderFieldConsts.TIME_ZONE);
        if (timeZone == null || TimeZoneEnum.getTimeZoneEnum(((JSONObject) timeZone).getString("lookupCode")) == null) {
            view.showMessage(STARTTIME_MUST_LATER_THAN_CURRENTTIME, MsgType.ERROR);
            return false;
        }

        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneEnum(((JSONObject) timeZone).getString("lookupCode"));
        String urgentFlag = TextValuePairHelper.getValue(model.getValue(ChangeOrderFieldConsts.IS_EMERGENCY_OPERATION));
        String billSource = (String) model.getValue(SOURCE);
        if (startTime != null) {
            // 将开始时间转为 系统时间
            long startDate = timeZoneEnum.fix((Date) startTime).getTime();
            if (System.currentTimeMillis() > startDate) {
                view.showMessage(CHANGE_STARTTIME_MUST_LATER_THAN_CURRENTTIME, MsgType.ERROR);
                return false;
            }

            //校验开始时间
            if (checkNotUrgentFlag(model, view, urgentFlag, startDate, billSource, timeZoneEnum)) {
                return false;
            }
        }

        if (endTime != null) {
            if (startTime == null || ((Date) startTime).getTime() > ((Date) endTime).getTime()) {
                view.showMessage(CHANGE_ENDTIME_MUST_LATER_THAN_STARTTIME, MsgType.ERROR);
                model.setValue(ChangeOrderFieldConsts.OPERATION_END_TIME, null);
                return false;
            }
        }
        return true;
    }

    private static boolean checkNotUrgentFlag(IDataModel model, IFormView view,
                                              String urgentFlag, long startDate,
                                              String billSource,
                                              TimeZoneEnum timeZoneEnum) {
        // 紧急操作不做校验、多产品联动保障不做校验
        if (!CommonConstants.DISABLED_FLAG.equals(urgentFlag)
                || CommonConstants.GUARANTEE.equals(billSource)) {
            return false;
        }
        Object gorupObj = model.getValue(OPERATION_TYPE_GROUP);
        String operationTypeGroup = gorupObj == null ? "" : gorupObj.toString();
        // 配合保障不做校验
        if (LookupValueConstant.OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE.equals(operationTypeGroup)){
            return false;
        }

        String orgId = TextValuePairHelper.getValue(model.getValue(FIELD_ORGANIZATION_CID));
        // 代表处不属于国内，不校验
        if (null != orgId && Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).noneMatch(orgId::startsWith)) {
            return false;
        }

        /* 其他普通场景，校验72小时 */
        //未提交过的，初次提交时间默认当前时间
        Object pkValue = model.getRootDataEntity().getPkValue();
        Date flowCreateDate = FlowHelper.getFlowCreateDate(pkValue == null ? "" : pkValue.toString());
        //之前提交过，取审批流程创建时间
        if (flowCreateDate != null) {
            if (CommonConstants.HOURS_72 > (startDate - flowCreateDate.getTime())) {
                String message = MsgUtils.getMessage(NOT_CHANGE_URGENT_FORBIDDEN_72_HOURS,
                        DateUtils.dateToString(timeZoneEnum.pollute(flowCreateDate), DATE_FORM));
                view.showMessage(message, MsgType.ERROR);
                return true;
            }
        } else {
            if (CommonConstants.HOURS_72 > (startDate - System.currentTimeMillis())) {
                view.showMessage(CHANGE_NOT_URGENT_FORBIDDEN_72_HOURS, MsgType.ERROR);
                return true;
            }
        }
        /* 其他普通场景，校验72小时 end */
        return false;
    }

    /*
    * 给操作人员 批次字段赋值
    * */
    public static void setOperatorsBatch(IDataModel dataModel, List<String> batchNos) {
        if (CollectionUtils.isEmpty(batchNos)) {
            return;
        }

        IDataEntityCollection operators = dataModel.getEntryRowEntities(FIELD_OPERATOR_CID);
        List<TextValuePair> textValuePairs = TextValuePairHelper.buildList(batchNos);
        operators.forEach(d -> {
            int rowIndex = ((DynamicDataEntity) d).getRowIndex();
            dataModel.setValue(FIELD_OPERATOR_BATCH_NO_KEY, textValuePairs, rowIndex);
        });
    }

    /*
     * 通过网络ID，查询保障单changOrderIds
     */
    public static List<String> getGuaranteeIdsByChangeOrderId(String id) {
        ChangeOrder changeOrder = get(id, Arrays.asList(ID, MULTI_PROD_GUARANTEE));
        // 是否为多产品保障单
        if (changeOrder == null || BoolEnum.Y != changeOrder.getMultiProdGuarantee()) {
            return new ArrayList<>();
        }
        List<MultiProductLinkageGuarantee> guarantees = MultiProdGuaranteeAbility.query(Arrays.asList(id));
        List<String> assignmentIds = guarantees.stream()
                .filter(item -> item.getIsMainTask() != null && !item.getIsMainTask())
                .map(MultiProductLinkageGuarantee::getAssignmentId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assignmentIds)) {
            return new ArrayList<>();
        }

        List<NetworkChangeAssignment> assignments =
                AssignmentAbility.querySpecificTypeAssignment(assignmentIds, NetworkChangeAssignment.class);
        return assignments.stream()
                .filter(i -> !AssignmentStatusEnum.ABOLISH.getValue().equals(i.getAssignmentStatus()))
                .map(Assignment::getBillId)
                .collect(Collectors.toList());
    }

    /* 改变国内行政审核结果选项：为同意、不同意*/
    public static void changeAdminOption(String changeOrderId, IFormView formView, String cid) {
        ChangeOrder changeOrder = get(changeOrderId, Arrays.asList(RESPONSIBLE_DEPT, IS_GOV_ENT));
        if (changeOrder == null) {
            return;
        }

        DeptTypeEnum deptType = getDeptType(changeOrder);
        if (DeptTypeEnum.INNER != deptType || BoolEnum.Y != changeOrder.getIsGovEnt()) {
            return;
        }
        //  只有国内政企 需要修改审核选项
        OptionsBuilder optionsBuilder = new OptionsBuilder()
                .addOption(new Option(ApprovalResultEnum.PASS.getCode(),
                        new Text(ApprovalResultEnum.PASS.getZhCn(), ApprovalResultEnum.PASS.getEnUs())))
                .addOption(new Option(ApprovalResultEnum.TERMINATE.getCode(),
                        new Text(ApprovalResultEnum.TERMINATE.getZhCn(), ApprovalResultEnum.TERMINATE.getEnUs())));
        formView.getClientViewProxy().setOptions(cid, optionsBuilder.build());
    }

    /*
     * 提交 校验操作人员
     * */
    public static boolean checkOperators(IDataModel model, IFormView view, Class<? extends BaseSubEntity> subEntityEnum) {
        // 获取表单填写 操作人员数据
        IDataEntityCollection dataEntityCollection = model.getEntryRowEntities(EntityHelper.getEntityId(subEntityEnum));
        if (dataEntityCollection == null) {
            return false;
        }

        Map<OperatorRoleEnum, List<String>> rolePersonMap = getOperatorRoleEnumListMap(dataEntityCollection);
        List<String> supervisor = rolePersonMap.get(OperatorRoleEnum.OPERATING_SUPERVISOR);
        List<String> operator = rolePersonMap.get(OperatorRoleEnum.OPERATOR);
        List<String> crossChecker = rolePersonMap.get(OperatorRoleEnum.CROSS_CHECKER);

        // 内部单
        if (subEntityEnum == Operator.class) {
            ChangeOrder changeOrder = JsonUtils.parseObject(model.getRootDataEntity(), ChangeOrder.class);
            String highRiskOperationsPrompt = getHighRiskOperationsPrompt(changeOrder);
            // 高风险操作时，需要保证双人操作，即操作负责人、操作人员、交叉检查人至少各一条
            if (StringUtils.hasText(highRiskOperationsPrompt)
                    && (CollectionUtils.isEmpty(supervisor) || CollectionUtils.isEmpty(operator) || CollectionUtils.isEmpty(crossChecker))) {
                String message = MsgUtils.getMessage(HIGH_RISK_OPERATION_SUPERVISOR_OPERATOR_CROSSCHECKER_NOT_NULL, highRiskOperationsPrompt);
                view.showMessage(message, MsgType.ERROR);
                return true;
            }
        }

        // 提交时，非高风险操作，至少保证：操作负责人、操作人员，每个角色均至少保留一行人员信息
        if ((CollectionUtils.isEmpty(supervisor) || CollectionUtils.isEmpty(operator))) {
            view.showMessage(PART_OPERATOR_NOT_NULL, MsgType.ERROR);
            return true;
        }
        return false;
    }

    private static Map<OperatorRoleEnum, List<String>> getOperatorRoleEnumListMap(IDataEntityCollection dataEntityCollection) {
        Map<OperatorRoleEnum, List<String>> rolePersonMap = Maps.newHashMap();
        for (int i =0 ; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            OperatorRoleEnum operatorRole = OperatorRoleEnum.fromValue(TextValuePairHelper.getValue(dynamicDataEntity.get(OperatorFieldConsts.OPERATOR_ROLE)));
            List<SingleEmployee> employees = JsonUtils.parseArray(dynamicDataEntity.get(OPERATOR_NAME), SingleEmployee.class);
            if (CollectionUtils.isEmpty(employees)) {
                continue;
            }
            if (!CollectionUtils.isEmpty(employees)) {
                List<String> operators = rolePersonMap.get(operatorRole);
                if (CollectionUtils.isEmpty(operators)) {
                    operators = Lists.newArrayList(employees.get(0).getEmpUIID());
                } else {
                    operators.add(employees.get(0).getEmpUIID());
                }
                rolePersonMap.put(operatorRole, operators);
            }
        }
        return rolePersonMap;
    }

    /*
    * 获取变更单 高风险操作提示语
    * */
    public static String getHighRiskOperationsPrompt(ChangeOrder changeOrder) {
        if (changeOrder == null) {
            return null;
        }

        DeptTypeEnum deptTypeEnum = ChangeOrderAbility.getDeptType(changeOrder);
        //①国内非政企：操作等级=关键
        if (DeptTypeEnum.INNER == deptTypeEnum
                && BoolEnum.N == changeOrder.getIsGovEnt()
                && OperationLevelEnum.CRITICAL == changeOrder.getOperationLevel()) {
            return MsgUtils.getMessage(CHANGE_ORDER_HIGH_RISK_KEY_OPERATION);
        }

        int importance = changeOrder.getImportance().getIntegerValue();
        int riskEvaluation = changeOrder.getRiskEvaluation().getIntValue();
        // ②国内政企：重要程度+风险评估≥五星
        if (DeptTypeEnum.INNER == deptTypeEnum
                && BoolEnum.Y == changeOrder.getIsGovEnt()
                && (importance + riskEvaluation) >= CommonConstants.INTEGER_FIVE) {
            return MsgUtils.getMessage(CHANGE_ORDER_HIGH_RISK_IMPORTANCE_RISK_GREATER_FIVE);
        }

        //③国际：风险评估 > 三星
        if (DeptTypeEnum.INTER == deptTypeEnum
                && riskEvaluation >= INTEGER_THREE) {
            return MsgUtils.getMessage(CHANGE_ORDER_HIGH_RISK_RISK_GREATER_THREE);
        }
        return null;
    }

    /**
     * 待办转交前置校验
     * 1.网络变更单执行任务时给予提示
     *
     * @param assignments 任务
     * @return 提示信息
     */
    public static String todoTransferBeforeCheck(List<Assignment> assignments) {
        List<Assignment> assignmentList = assignments.stream()
                .filter(assignment -> {
                    AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
                    AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
                    return (AssignmentTypeEnum.NETWORK_CHANGE == assignmentType
                            || AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE == assignmentType)
                            && AssignmentStatusEnum.EXECUTE == assignmentStatusEnum;
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(assignmentList)) {
            return null;
        }

        return assignmentList.stream()
                .map(Assignment::getAssignmentCode)
                .collect(Collectors.joining(COMMA));
    }

    /**
     * 获取时区提示语dataMap（包含时区提示语和IDOP提示语同时展示的情况）
     * @param originTimeValue 原始时间（一般是指用户页面上输入的操作时间）
     * @param timeZonePrefix 市区提示信息前缀（如GMT+8：00）
     * @param timeZoneEnum 时区枚举
     * @param deptId 代表处ID
     * @param otherTips 其他提示语（一般是指IDOP提示语）
     * @return Map<String, Object> 低代码组件【主要提示】属性赋值的dataMap<属性值， 对应的value>
     */
    public static Map<String, Object> getTimeZoneTipsDataMap(Date originTimeValue,
                                                             String timeZonePrefix,
                                                             TimeZoneEnum timeZoneEnum,
                                                             String deptId, String otherTips) {
        // 获取页面用户输入的指定时区时间，先转换为服务器时间，再转换为北京时间
        Date beiJingDate = null;
        if (!ObjectUtils.isEmpty(originTimeValue)) {
            Date fixDate = timeZoneEnum.fix(originTimeValue);
            beiJingDate = TimeZoneEnum.BEIJING.pollute(fixDate);
        }

        String timeTips = timeZonePrefix + SPACE + (ObjectUtils.isEmpty(beiJingDate)
                ? EMPTY_STRING
                : DateUtils.dateToString(beiJingDate, DATE_FORM));

        // 如果代表处是国际代表处，则提示语需要以【亮蓝色】显示；否则默认展示
        String format = DeptTypeEnum.getDeptTypeById(deptId) == DeptTypeEnum.INTER ? BLUE_FONT : NORMAL_FONT;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(CommonConstants.TYPE, CommonConstants.I18N);
        // 这里是考虑到特殊情况下IDOP批次的提示语【操作时段存在冲突】和时区提示语同时展示的情况
        // 如果otherTips不为空（表示是IDOP提示语），则提示语以【橙色】展示
        // 非IDOP提示语的情况下，如果时区为东八区北京时区，则不展示提示语
        if (StringUtils.hasText(otherTips)) {
            dataMap.put(CommonConstants.ZH_CN, String.format(ORANGE_FONT, otherTips));
            dataMap.put(CommonConstants.EN_US, String.format(ORANGE_FONT, otherTips));
        } else if (timeZoneEnum != TimeZoneEnum.BEIJING) {
            dataMap.put(CommonConstants.ZH_CN, String.format(format, timeTips));
            dataMap.put(CommonConstants.EN_US, String.format(format, timeTips));
        }

        return dataMap;
    }

    /**
     * 获取时区提示语的标准格式化字符串和其他提示语（IDOP提示语）
     * @param source 单据数据来源（IDOP、CNOP等）--取自数据库原始字段，字段类型为String
     * @param timeConflict 时间冲突--取自页面组件值，组件类型为快码
     * @Return String Idop提示语（若不满足提示语的展示条件，则返回空）
     */
    public static String getIdopTimeTips(String source, Object timeConflict) {
        // 如果数据来源不是IDOP或者【时间冲突】为空，直接返回空
        if (!DataSourceEnum.IDOP.name().equals(source) || ObjectUtils.isEmpty(timeConflict)) {
            return EMPTY_STRING;
        }
        // 如果【时间冲突】不为【Y】，直接返回空
        Map<String,String> timeZoneMap = JsonUtils.parseObject(timeConflict, Map.class);
        String timeConflictValue = timeZoneMap.get(LookupValueConstant.LOOKUP_CODE);
        if (!Y.equals(timeConflictValue)) {
            return EMPTY_STRING;
        }
        // 其余情况：数据来源是IDOP，且【时间冲突】字段快码值为【Y】，展示IDOP提示语：【操作时段存在冲突】
        return MsgUtils.getMessage(IDOP_BATCH_TIME_CONFLICT);
    }

    /**
     * 获取子表单下拉单选所有选项值
     *
     * @param entryRowEntities 子表单行数据
     * @param propertyKey 列属性名称
     * @return List<String>
     */
    public static List<String> getEntryAllColumnData(IDataEntityCollection entryRowEntities, String propertyKey) {
        return (List<String>) entryRowEntities
                .stream()
                .map(entity -> {
                    DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) entity;
                    return TextValuePairHelper.getValue(dynamicDataEntity.get(propertyKey));
                })
                .collect(Collectors.toList());

    }

    public static boolean isGroupDirectManagementNetwork(String changeOrderId, String batchNo) {
        ChangeOrder changeOrder = get(changeOrderId, Arrays.asList(ID, RESPONSIBLE_DEPT, IS_GOV_ENT));
        return isGroupDirectManagementNetwork(changeOrder, batchNo);
    }

    /**
     * 是否三营网络服务处集团直管网络
     */
    public static boolean isGroupDirectManagementNetwork(ChangeOrder changeOrder, String batchNo) {
        if (changeOrder == null) {
            return false;
        }

        //  国内 and 是否政企 =否 and 包含直网网络
        DeptTypeEnum deptType = getDeptType(changeOrder);
        if (DeptTypeEnum.INNER != deptType || BoolEnum.N != changeOrder.getIsGovEnt()) {
            return false;
        }

        List<OperationObject> opObjects = OperationObjectAbility.listOperationObject(changeOrder.getId(),
                OperationObject.class);
        // 如果是批次，只需要当前批次
        Predicate<OperationObject> filter = i -> {
            if (StringUtils.hasText(batchNo)) {
                return batchNo.equals(TextValuePairHelper.getValue(i.getBatchNo()));
            }
            return true;
        };
        List<String> networkIds = opObjects.stream()
                .filter(filter)
                .map(OperationObject::getNetworkId)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        List<NisNetwork> nisNetworks = NisClient.queryNetwork(networkIds);
        return !CollectionUtils.isEmpty(nisNetworks.stream()
                .filter(i -> BoolEnum.Y == i.getGrpDrctMngFlag())
                .collect(Collectors.toList()));
    }

    /**
     * 获取支持人员时，支持人员实体需继承ISupportStaffBase接口，否则不能使用该方法
     *
     * @param changeOrderId 网络变更单id
     * @return 支持人员工号
     */
    public static List<String> getSupportPersonnels(Class<? extends BaseSubEntity> baseSubEntityClass, String changeOrderId) {
        List<? extends BaseSubEntity> supportPersonnelList = QueryDataHelper.query(baseSubEntityClass, Lists.newArrayList(), changeOrderId);
        if (CollectionUtils.isEmpty(supportPersonnelList)) {
            return Collections.emptyList();
        }

        return supportPersonnelList.stream()
                .map(a -> {
                    ISupportStaffBase a1 = (ISupportStaffBase) a;
                    return a1.getPerson().getEmpUIID();
                }).collect(Collectors.toList());
    }

    /**
     * 网络变更单撤回
     *
     * @param assignment 任务信息
     */
    public static void revoke(Assignment assignment) {
        String changeOrderId = assignment.getEntityId();
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        // 1.撤销审批流程实例approval/flow/revoke
        FlowHelper.revokeFlow(changeOrderId, Objects.requireNonNull(assignmentTypeEnum).getApproveFlowCodeEnum().name());

        // 2.更新任务状态为草稿、当前处理人 = 责任人（只有责任人可以进行撤回操作），当前进展为DRAFT
        SaveDataHelper.update(new Assignment() {{
            setId(assignment.getId());
            setCurrentProgress(ApproveNodeEnum.DRAFT.name());
            setCurrentProcessorEmployee(assignment.getResponsibleEmployee());
            setAssignmentStatus(AssignmentStatusEnum.START.getValue());
        }});

        // 3.更新网络变更单主表billstatus状态为暂存
        Class<? extends BaseEntity> changeOrderEntityClass = assignmentTypeEnum.getApproveFlowCodeEnum().getFlowEntity();
        SaveDataHelper.update(changeOrderEntityClass,
                changeOrderId,
                MapUtils.newHashMap(ChangeOrderFieldConsts.BILL_STATUS, BillStatusEnum.SAVE.getCode()));

        // 4.操作日志
        OperationLogRecordAbility.saveActionOperationLog(
                changeOrderId, OperationLogActionEnum.REVOKE.getOperationCode(), new Date());

        // 5.知会邮件
        EmailAbility.asyncSendRevokeMail(assignment);

        // 6.当前单据为idop，撤回需要同步idop状态
        IdopAbility.syncIdopStatus(changeOrderId, assignmentTypeEnum, AssignmentStatusEnum.START, IdopCancelEnum.REJECT);
    }

    /**
     * 撤回前置校验
     *
     * @param assignment 任务信息
     * @return true 允许撤回  false 不允许撤回
     */
    public static boolean revokeBeforeCheck(Assignment assignment) {
        AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        // 操作用户为责任人
        return EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee()).contains(ContextHelper.getEmpNo())
                // 任务状态为审核中
                && AssignmentStatusEnum.APPROVE == assignmentStatusEnum
                // 单据未被审核过
                && !FlowHelper.isApproved(assignment.getEntityId());
    }

    /**
     * 操作人员是否大于1个
     */
    public static boolean checkOperatorsCount(IDataModel dataModel, Class<? extends BaseSubEntity> entity) {
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(EntityHelper.getEntityId(entity));
        if (dataEntityCollection == null) {
            return true;
        }

        for (int i = CommonConstants.INTEGER_ZERO; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            Object operatorObj = dynamicDataEntity.get(OPERATOR_NAME);
            if (operatorObj == null) {
                continue;
            }
            List<SingleEmployee> employees = JsonUtils.parseArray(operatorObj, SingleEmployee.class);
            if (employees.size() > 1) {
                log.error("operators > 1 , id:" + dataModel.getRootDataEntity().getPkValue());
                return false;
            }
        }
        return true;
    }

}
