package com.zte.iccp.itech.extension.handler;

import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/4/29 2:55
 * zte-iccp-itech-netchange
 */
public abstract class AbstractRewardApproverHandler<T> {
    /**
     * 审批人配置类型（通过配置取数，此字段非空）
     */
    public static List<String> rewardApproverByNode(
            AbstractRewardApproverHandler handler, String businessId,
            ApproveFlowCodeEnum approveFlowEnum,
            FlowClient body)
            throws IllegalAccessException {
        return handler.rewardApproverByNodeExtend(QueryDataHelper.get(
                approveFlowEnum.getApproverEntity(), new ArrayList<>(), businessId), body);
    }

    protected abstract List<String> rewardApproverByNodeExtend(T entity, FlowClient body);

}
