package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CURRENT_STATUS_NAME;

@ApiModel("网络批次任务vo")
@Setter
@Getter
public class BatchTaskVO extends BatchTask {

    @JsonProperty(value = CURRENT_STATUS_NAME)
    @ApiModelProperty("当前状态名称")
    private String currentStatusName;

    /** 是否聚焦（批次树） */
    private Boolean isFocus;
}
