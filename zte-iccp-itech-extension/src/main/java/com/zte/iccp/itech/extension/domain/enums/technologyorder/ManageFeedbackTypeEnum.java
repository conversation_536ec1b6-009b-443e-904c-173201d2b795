package com.zte.iccp.itech.extension.domain.enums.technologyorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;


/**
 * 技术管理任务操作记录枚举
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/4
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ManageFeedbackTypeEnum implements SingletonTextValuePairsProvider {

    /**
     * 申请废止
     */
    TERMINATE("申请废止", "Apply for Invalidation"),

    /**
     * 反馈进展
     */
    SAVE("反馈进展", "Progress Feedback"),

    /**
     * 完成任务，申请关闭
     */
    SUBMIT("完成任务，申请关闭", "Task Completed, Apply for Closure"),
    ;

    /**
     * 操作说明中文
     */
    private final String zhCn;

    /**
     * 操作说明英文
     */
    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }
}
