package com.zte.iccp.itech.extension.plugin.form.assignment;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FilterHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.CacheUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.PlanOperationAssignment;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.event.OptionEvent;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.ddm.component.context.RequestEventContext;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.orm.BaseQueryDataHelper;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ASSIGNMENT_TYPE;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.CUSTOMER_CLASSIFICATION;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.NetworkChangeFieldConsts.OFFICE_NAME;

/**
 * <AUTHOR>
 * @since 2025/06/26
 */
public class LoadQueryCondDropDownOptionsPlugin extends BaseFormPlugin {

    private static final String CACHE_KEY_FORMAT = "itech_netchange:assignment:changeorder:dropdown:%s";

    @Override
    public void invoked(OptionEvent optionEvent) {
        String componentCid = RequestEventContext.get().getCid();
        String cacheKey = String.format(CACHE_KEY_FORMAT, componentCid);

        List<TextValuePair> textValues = CacheUtils.get(cacheKey, new TypeReference<List<TextValuePair>>() {});
        if (textValues == null) {
            textValues = getTextValuePairs(componentCid);

            if (textValues == null) {
                return;
            }

            CacheUtils.set(cacheKey, textValues, 5 * 60);
        }

        JSONObject args = optionEvent.getDynamicDomainArgsDTO().getArgs();
        String filterKey = args.getString(CommonConstants.FILTER_KEY).toLowerCase();
        Integer pageNumber = args.getInteger(CommonConstants.PAGE_NO_2);
        Integer pageSize = args.getInteger(CommonConstants.PAGE_SIZE);
        textValues = getPageRows(textValues, filterKey, pageNumber, pageSize);

        // 3.设置下拉框
        String operationKey = args.getString(CommonConstants.OPERATION_KEY);
        setOptions(textValues, componentCid, operationKey);
    }

    private List<TextValuePair> getTextValuePairs(String componentCid) {
        // 5.局点名称
        if (OFFICE_NAME.equals(componentCid)) {
            return getDropDownEntityTextPairValue(
                    NetworkChangeAssignment.class,
                    NetworkChangeAssignment::getOfficeName,
                    OFFICE_NAME,
                    Lists.newArrayList());
        }

        // 6.客户标识
        if (CUSTOMER_CLASSIFICATION.equals(componentCid)) {
            return getDropDownEntityTextPairValue(
                    Assignment.class,
                    Assignment::getCustomerClassification,
                    CUSTOMER_CLASSIFICATION,
                    Lists.newArrayList(
                            AssignmentTypeEnum.NETWORK_CHANGE.getValue(),
                            AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue()));
        }

        // 7.操作计划-局点名称
        if (AssignmentFieldConsts.OperationPlanFieldConsts.OFFICE_NAME.equals(componentCid)) {
            return getDropDownEntityTextPairValue(
                    PlanOperationAssignment.class,
                    PlanOperationAssignment::getOfficeName,
                    componentCid,
                    Lists.newArrayList());
        }

        return null;
    }

    private void setOptions(
            List<TextValuePair> textValues,
            String componentCid,
            String operationKey) {
        OptionsBuilder optionsBuilder = new OptionsBuilder();
        for (TextValuePair textValuePair : textValues) {
            optionsBuilder.addOption(
                    new Option(textValuePair.getValue(),
                            new Text(
                                    textValuePair.getTextByLanguage(CommonConstants.ZH_CN),
                                    textValuePair.getTextByLanguage(CommonConstants.EN_US))));
        }

        IClientViewProxy clientViewProxy = getView().getClientViewProxy();
        Map<String, Object> value = MapUtils.newHashMap(
                CommonConstants.OPTIONS, optionsBuilder.build(), CommonConstants.TARGET, componentCid);
        if (CommonConstants.OPERATION_SEARCH.equals(operationKey)) {
            // 新增
            clientViewProxy.setOptions(value);
        } else {
            // 下拉到底追加数据
            clientViewProxy.appendOptions(value);
        }
    }

    private List<TextValuePair> getPageRows(
            List<TextValuePair> textValues,
            String filterKey,
            Integer pageNum,
            Integer pageSize) {
        return textValues.stream()
                .filter(value ->
                        value.getTextByLanguage(ContextHelper.getLangId())
                            .toLowerCase()
                            .contains(filterKey))
                .skip((pageNum - 1L) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
    }

    /**
     * 设置下拉选项 - 实体字段 - 下拉选项类型
     */
    private <T extends BaseEntity> List<TextValuePair> getDropDownEntityTextPairValue(
            Class<T> clazz,
            Function<T, List<TextValuePair>> propertyGetFunction,
            String field,
            List<String> assignmentTypes) {

        if (OFFICE_NAME.equals(field)) {
            return getOfficeNameDropDownOptions();
        }

        // 1.检索字段值
        List<IFilter> iFilters = Lists.newArrayList();

        //(1) 字段非空筛选条件
        Filter hasValueFilter = new Filter(field, Comparator.IS_NOT_NULL, null)
                .and(new Filter(field, Comparator.NE, Lists.newArrayList()))
                .and(new Filter(field, Comparator.NE, Lists.newArrayList(CommonConstants.EMPTY_STRING)));
        iFilters.add(hasValueFilter);

        //(2) 任务类型筛选条件
        IFilter filter = FilterHelper.multiMemberOfFilter(ASSIGNMENT_TYPE, assignmentTypes);
        if (!Objects.isNull(filter)) {
            iFilters.add(filter);
        }
        List<T> entities = QueryDataHelper.queryDistinct(clazz, Lists.newArrayList(field), iFilters);
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }

        // 2.获取字段值
        List<TextValuePair> pairs = Lists.newArrayList();
        List<String> values = Lists.newArrayList();
        for (T entity : entities) {
            List<TextValuePair> fieldPairs = propertyGetFunction.apply(entity);
            if (CollectionUtils.isEmpty(fieldPairs)) {
                continue;
            }

            fieldPairs.forEach(item -> {
                if (StringUtils.hasText(item.getTextByLanguage(CommonConstants.ZH_CN))
                        && StringUtils.hasText(item.getTextByLanguage(CommonConstants.EN_US))
                        && !values.contains(item.getValue())) {
                    pairs.add(item);
                    values.add(item.getValue());
                }
            });
        }

        return pairs;
    }

    private List<TextValuePair> getOfficeNameDropDownOptions() {
        List<JSONObject> officeNames = BaseQueryDataHelper.query(
                "SELECT DISTINCT regexp_replace(office_name_ext->'$[0]', '^\"|\"$', '') name"
                        + " FROM assignment_network_change_ex FORCE INDEX (ix_office_name_ext)"
                        + " HAVING name > ''"
                        + " ORDER BY name",
                Collections.emptyMap());
        return officeNames.stream()
                .map(j -> {
                    String name = j.getString("name");
                    TextValuePair pair = new TextValuePair();
                    pair.setValue(name);
                    pair.setText(new MultiLangText(name, name, name));
                    return pair;
                }).collect(Collectors.toList());
    }
}
