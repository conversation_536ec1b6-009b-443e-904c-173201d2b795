package com.zte.iccp.itech.extension.ability.changeorder;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.NetworkConfigurationAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.LocalCacheUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.NetworkConfigurationFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.NetworkConfiguration;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.OperationScenario;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.changeorder.enums.NodeWithInlinePageEnum;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.utils.LocalCacheUtils.LOCAL_THREAD_EXPIRE_MINUTES;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ZERO;
import static com.zte.iccp.itech.extension.domain.constant.LookupValueConstant.OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE;
import static com.zte.iccp.itech.extension.domain.constant.ProdCategoryConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.NetworkConfigurationFieldConsts.NETWORK_ATTRIBUTE;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationScenarioFieldConsts.APPROVAL_LEVEL;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ApprovalAbility {

    /** 配置项key：需要核心网大区TD审核的产品分类ID路径 */
    private static final String CCN_AREA_TD_PROD_ID_PATH = "approval.ccnAreaTd.prodIdPath";

    /** 配置项key：需要代表处TD审核的海外国家编码 */
    private static final String REP_OFFICE_TD_INTER_COUNTRY_CODE = "approval.repOfficeTd.inter.countryCode";

    /** 配置项key：需要代表处TD审核的海外组织编码路径 */
    private static final String REP_OFFICE_TD_INTER_ORG_CODE_PATH = "approval.repOfficeTd.inter.orgCodePath";

    /** 配置项key：需要代表处TD审核的海外产品ID路径 */
    private static final String REP_OFFICE_TD_INTER_PROD_ID_PATH = "approval.repOfficeTd.inter.prodIdPath";

    /** 配置项key：国内可以绕过【技术交付部/网络处审核】的产品分类ID路径 */
    private static final String TD_NO_INNER_NEG_PROD_ID_PATH = "approval.tdNetOffice.inner.negative.prodIdPath";

    /** 配置项key：国际可以绕过【技术交付部/网络处审核】的产品分类ID路径 */
    private static final String TD_NO_INTER_NEG_PROD_ID_PATH = "approval.tdNetOffice.inter.negative.prodIdPath";

    /** 配置项key：国际可以绕过【技术交付部/网络处审核】的国家 */
    private static final String TD_NO_INTER_NEG_COUNTRY_CODE = "approval.tdNetOffice.inter.negative.countryCode";

    /** 配置项key：国际可以绕过【技术交付部/网络处审核】的组织编码路径 */
    private static final String TD_NO_INTER_NEG_ORG_CODE_PATH = "approval.tdNetOffice.inter.negative.orgCodePath";

    /** 配置项key：需远程中心负责人审核的产品ID路径 */
    private static final String REMOTE_CENTER_OWNER_APPROVAL_PROD_CATE_ID_PATH_KEY = "approval.remoteCenterOwner.prodIdPath";

    /** 配置项key：需测试部审核的产品ID路径 */
    private static final String TEST_DEPT_APPROVAL_PROD_CATE_ID_PATH_KEY = "approval.testDeptApproval.prodIdPath";

    /** 配置项key：需研发经理审核的产品ID路径 */
    private static final String DEV_DEPT_APPROVAL_PROD_CATE_ID_PATH_KEY = "approval.devDeptApproval.prodIdPath";

    /** 配置项key：“保障”类 操作类型 快码 */
    private static final String GUARANTEE_OPERATION_TYPE_LOOKUP_CODE = "guarantee.operationType.lookupCode";

    public static BoolEnum isCcnAreaTdApproval(ChangeOrder changeOrder) {
        // 是否三营
        if (!isDomesticSales(changeOrder)
                || CommonConstants.GUARANTEE.equals(changeOrder.getSource())
                || changeOrder.getIsGovEnt() == BoolEnum.Y) {
            return BoolEnum.N;
        }

        // 是否符合产品分类要求
        String[] prodCategoryIdPaths = ConfigHelper.getArray(CCN_AREA_TD_PROD_ID_PATH);
        boolean isProdMatch = Arrays.stream(prodCategoryIdPaths)
                .anyMatch(target -> changeOrder.getProductCategory().startsWith(target));
        if (!isProdMatch) {
            return BoolEnum.N;
        }

        // 是否勾选了【是否产品用途BCN需要多产品联动保障】
        return changeOrder.getMultiProdGuarantee();
    }

    /**
     * 仅判断是否国内营销，不包含工程服务国内部
     */
    public static boolean isDomesticSales(ChangeOrder changeOrder) {
        if (changeOrder.getResponsibleDept() == null) {
            return false;
        }
        return changeOrder.getResponsibleDept().startsWith(DOMESTIC_SALES);
    }

    public static BoolEnum isRepOfficeTdApproval(ChangeOrder changeOrder) {
        DeptTypeEnum deptType = ChangeOrderAbility.getDeptType(changeOrder);
        if (deptType == DeptTypeEnum.INNER) {
            return BoolEnum.Y;
        }
        // 国际暂时都不走TD审核直接到技术交付部
        return BoolEnum.N;

        /* todo  待2期优化
        boolean countryMatched = Arrays.stream(ConfigHelper.getArray(REP_OFFICE_TD_INTER_COUNTRY_CODE))
                .anyMatch(target -> target.equals(changeOrder.getCountry().getValue()));
        boolean orgMatched = Arrays.stream(ConfigHelper.getArray(REP_OFFICE_TD_INTER_ORG_CODE_PATH))
                .anyMatch(target -> changeOrder.getResponsibleDept().startsWith(target));
        if (!countryMatched && !orgMatched) {
            return BoolEnum.N;
        }

        String[] interProdCateIdPaths = ConfigHelper.getArray(REP_OFFICE_TD_INTER_PROD_ID_PATH);
        return BoolEnum.valueOf(
                Arrays.stream(interProdCateIdPaths)
                        .anyMatch(target -> changeOrder.getProductCategory().startsWith(target)));*/
    }

    public static BoolEnum isTdNetOfficeApproval(ChangeOrder changeOrder) {
        DeptTypeEnum deptType = ChangeOrderAbility.getDeptType(changeOrder);

        // 国内场景
        if (deptType == DeptTypeEnum.INNER) {
            boolean prodMatched = Arrays.stream(ConfigHelper.getArray(TD_NO_INNER_NEG_PROD_ID_PATH))
                    .anyMatch(target -> changeOrder.getResponsibleDept().startsWith(target));
            if (changeOrder.getIsGovEnt() == BoolEnum.N
                    && prodMatched
                    && changeOrder.getImportance() == ImportanceEnum.STAR1
                    && changeOrder.getRiskEvaluation() == RiskEvaluationEnum.STAR1) {
                return BoolEnum.N;
            }

            return BoolEnum.Y;
        }

        // 国际场景
        boolean prodMatched = Arrays.stream(ConfigHelper.getArray(TD_NO_INTER_NEG_PROD_ID_PATH))
                .anyMatch(target -> changeOrder.getProductCategory().startsWith(target));
        boolean countryMatched = Arrays.stream(ConfigHelper.getArray(TD_NO_INTER_NEG_COUNTRY_CODE))
                .anyMatch(target -> target.equals(changeOrder.getCountry().getValue()));
        boolean orgMatched = Arrays.stream(ConfigHelper.getArray(TD_NO_INTER_NEG_ORG_CODE_PATH))
                .anyMatch(target -> changeOrder.getResponsibleDept().startsWith(target));
        if (prodMatched
                && (countryMatched || orgMatched)
                && changeOrder.getImportance() == ImportanceEnum.STAR1
                && changeOrder.getRiskEvaluation() == RiskEvaluationEnum.STAR1
                && changeOrder.getOperationLevel() == OperationLevelEnum.NORMAL) {
            return BoolEnum.N;
        }

        return BoolEnum.Y;
    }

    public static BoolEnum isIntegrationTeamApproval(ChangeOrder changeOrder) {
        //TODO feng 集成团队待需求澄清
        return BoolEnum.N;
    }

    /**
     * 申请单中【产品分类】仅选择无线、核心网下所有产品
     * 审批节点【技术交付部/网络处审核】中的【对网络服务部的要求】字段选择【需远程中心提供方案并执行】；
     */
    public static BoolEnum isRemoteCenterSupport(String prodCategory, ReqForNetServiceDeptEnum reqForNetServiceDept) {
        String[] prodCateIdPaths = ConfigHelper.getArray(REMOTE_CENTER_OWNER_APPROVAL_PROD_CATE_ID_PATH_KEY);
        if (Arrays.stream(prodCateIdPaths)
                .noneMatch(prodCategory::startsWith)) {
            return BoolEnum.N;
        }
        return BoolEnum.valueOf(ReqForNetServiceDeptEnum.NEED_REMOTE_CENTER_PROVIDE_SOLUTION_AND_EXECUTE == reqForNetServiceDept);
    }

    /**
     * 申请单中【产品类型】选择电信云核心网下的核心网，SSP，云平台中的一种
     * 【网络服务部审核】审批节点中的【操作方案是否由测试部提供】字段选择【是】或者【操作方案是否需要验证测试】字段选择【是】；
     */
    /* Started by AICoder, pid:bbd7cb4f999042658659d74b757334ab */
    public static BoolEnum isTestDeptApproval(ChangeOrder changeOrder) {
        String[] prodCateIdPaths = ConfigHelper.getArray(TEST_DEPT_APPROVAL_PROD_CATE_ID_PATH_KEY);
        if (Arrays.stream(prodCateIdPaths)
                .noneMatch(changeOrder.getProductCategory()::startsWith)) {
            return BoolEnum.N;
        }
        if (BoolEnum.Y.equals(changeOrder.getIsOperPlanFromTestDept())
                || BoolEnum.Y.equals(changeOrder.getIsOperPlanNeedTestVerify())) {
            return BoolEnum.Y;
        }
        return BoolEnum.N;
    }
    /* Ended by AICoder, pid:bbd7cb4f999042658659d74b757334ab */

    /**
     * 申请单中选择【产品类型】是4G、5G、无线大数据、SSP、核心网、集成第三方与组网设备、业务产品、云平台、承载网、云电脑、中兴金易（ZJT）中的一种；
     * 【网络服务部审核】审批节点中的【是否需要升级至研发】字段选择【是】
     * @param changeOrder
     * @return
     */
    public static BoolEnum isDevDeptApproval(ChangeOrder changeOrder) {
        String[] prodCateIdPaths = ConfigHelper.getArray(DEV_DEPT_APPROVAL_PROD_CATE_ID_PATH_KEY);
        if (Arrays.stream(prodCateIdPaths)
                .noneMatch(changeOrder.getProductCategory()::startsWith)) {
            return BoolEnum.N;
        }
        if (BoolEnum.Y.equals(changeOrder.getIsDevDeptApproval())) {
            return BoolEnum.Y;
        }
        return BoolEnum.N;
    }

    /**
     * 计算条件不足时返回null
     */
    public static BoolEnum isAdministrationLeaderApproval(
            ChangeOrder changeOrder,
            List<OperationObject> opObjects) {
        //如果操作类型属于配合保障单，不需行政领导审核
        if (OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE.equals(changeOrder.getOperationTypeGroup())) {
            return BoolEnum.N;
        }

        DeptTypeEnum deptTypeEnum = ChangeOrderAbility.getDeptType(changeOrder);

        // 三营
        if (DeptTypeEnum.INNER.equals(deptTypeEnum)
                && changeOrder.getIsGovEnt() == BoolEnum.N) {
            return getInnerAdminApprovalFlag(changeOrder, opObjects);
        }

        // 国内政企
        if (DeptTypeEnum.INNER.equals(deptTypeEnum)
                && changeOrder.getIsGovEnt() == BoolEnum.Y) {
            return getGovEntAdminApprovalFlag(changeOrder);
        }

        // 国际
        if (DeptTypeEnum.INTER.equals(deptTypeEnum)) {
            if (InterAdminApproverAbility.isIncomplete2Judge(changeOrder)) {
                return BoolEnum.N;
            }

            List<String> approvers = InterAdminApproverAbility.getInterCountersignApprover(changeOrder);
            return BoolEnum.valueOf(!CollectionUtils.isEmpty(approvers));
        }

        return BoolEnum.N;
    }

    public static NodeWithInlinePageEnum getRecommendAdminApprovalLvl(ChangeOrder changeOrder) {
        final String cacheKey = String.format("changeOrder:%s:recommendLvl", changeOrder.getId());

        NodeWithInlinePageEnum recommendLvl = LocalCacheUtils.get(cacheKey);
        if (recommendLvl != null) {
            return recommendLvl;
        }

        recommendLvl = ((Supplier<NodeWithInlinePageEnum>) () -> {
            DeptTypeEnum deptTypeEnum = ChangeOrderAbility.getDeptType(changeOrder);
            // 三营
            if (DeptTypeEnum.INNER.equals(deptTypeEnum)
                    && changeOrder.getIsGovEnt() == BoolEnum.N) {
                return getSd3RecommendLevel(changeOrder);
            }

            // 国内政企
            if (DeptTypeEnum.INNER.equals(deptTypeEnum)
                    && changeOrder.getIsGovEnt() == BoolEnum.Y) {
                return getGovEntRecommendLevel(changeOrder);
            }

            return null;
        }).get();
        LocalCacheUtils.set(cacheKey, recommendLvl, LOCAL_THREAD_EXPIRE_MINUTES);
        return recommendLvl;
    }

    public static NodeWithInlinePageEnum getSd3RecommendLevel(ChangeOrder changeOrder) {
        ApprovalLevelEnum approvalLevel = getApprovalLevel(changeOrder.getOperationScenario());
        int netAttribute = getNetworkAttribute(changeOrder.getId());

        // 1.八层工服三部部长
        NodeWithInlinePageEnum esd3MinisterRecommended = getIfEsd3MinisterRecommended(
                changeOrder, netAttribute, approvalLevel);
        if (esd3MinisterRecommended != null) {
            return esd3MinisterRecommended;
        }

        // 申请单中产品分类属算力及核心网则是否CCN操作为是
        boolean isCcn = changeOrder.getProductCategory().startsWith(ConfigHelper.get(CCN_PROD_IDPATH_KEY));
        boolean isEmerOrNetClose = isEmerOrNetClose(changeOrder);
        // 2.五层网服部四层
        // 2.1 是否紧急 = 是或封网操作 = 是，是否CCN操作 = 是，且操作等级 = 关键
        if (isEmerOrNetClose
                && isCcn
                && changeOrder.getOperationLevel() == OperationLevelEnum.CRITICAL) {
            return NodeWithInlinePageEnum.ADMIN_NETSERVICE_LV4_NODE;
        }

        // 3.四层网络处主管经理
        NodeWithInlinePageEnum netOfficeMgrRecommended = getIfNetOfficeMgrRecommended(
                changeOrder, isCcn, isEmerOrNetClose, netAttribute, approvalLevel);
        if (netOfficeMgrRecommended != null) {
            return netOfficeMgrRecommended;
        }

        // 4 三层办事处副经理
        // 4.1 是否紧急 = 是或封网操作 = 是，且是否CCN操作 = 否，且风险等级 = 1星
        if (isEmerOrNetClose && !isCcn && changeOrder.getRiskEvaluation() == RiskEvaluationEnum.STAR1) {
            return NodeWithInlinePageEnum.ADMIN_REP_DEPUTY_MNG_NODE;
        }

        // 5.二层网络处产品总监
        NodeWithInlinePageEnum netOfficeProdDirectorRecommended = getIfNetOfficeProdDirectorRecommended(
                changeOrder, netAttribute, approvalLevel);
        if (netOfficeProdDirectorRecommended != null) {
            return netOfficeProdDirectorRecommended;
        }

        // 无推荐层级
        return null;
    }

    /**
     * 是否紧急 = 是或封网操作 = 是
     *
     * @param changeOrder changeOrder
     * @return boolean
     */
    private static boolean isEmerOrNetClose(ChangeOrder changeOrder) {
        return BoolEnum.Y == changeOrder.getIsEmergencyOperation()
                || BoolEnum.Y == changeOrder.getIsNetCloseOrControlOperation();
    }

    private static NodeWithInlinePageEnum getIfNetOfficeProdDirectorRecommended(
            ChangeOrder changeOrder,
            int netAttribute,
            ApprovalLevelEnum approvalLevel) {
        // 1.网络属性 = 重要网络，且操作等级 = 一般
        if (netAttribute == NetworkAttributeEnum.IMPORTANT.getValue()
                && changeOrder.getOperationLevel() == OperationLevelEnum.NORMAL) {
            return NodeWithInlinePageEnum.ADMIN_NET_PROD_DIR_NODE;
        }

        // 2.网络属性 = 普通网络，且操作等级 = 关键,且操作场景 = 一级审批
        if (netAttribute == NetworkAttributeEnum.NORMAL.getValue()
                && changeOrder.getOperationLevel() == OperationLevelEnum.CRITICAL
                && approvalLevel == ApprovalLevelEnum.LVL1) {
            return NodeWithInlinePageEnum.ADMIN_NET_PROD_DIR_NODE;
        }
        return null;
    }

    private static NodeWithInlinePageEnum getIfNetOfficeMgrRecommended(
            ChangeOrder changeOrder,
            boolean isCcn,
            boolean isEmerOrNetClose,
            int netAttribute,
            ApprovalLevelEnum approvalLevelEnum) {

        // 1.网络属性 = 重要网络，且操作等级 = 重要
        if ((netAttribute == NetworkAttributeEnum.IMPORTANT.getValue()
                && changeOrder.getOperationLevel() == OperationLevelEnum.IMPORTANCE)
                // 2.网络属性 = 普通网络，且操作等级 = 关键，且操作场景 = 二级审批
                || (netAttribute == NetworkAttributeEnum.NORMAL.getValue() &&
                changeOrder.getOperationLevel() == OperationLevelEnum.CRITICAL
                && ApprovalLevelEnum.LVL2 == approvalLevelEnum)
                // 3.是否紧急 = 是或封网操作 = 是，是否CCN操作 = 是，且操作等级 = 重要、一般
                || (isEmerOrNetClose
                && isCcn
                && (changeOrder.getOperationLevel() == OperationLevelEnum.IMPORTANCE
                || changeOrder.getOperationLevel() == OperationLevelEnum.NORMAL))
                // 4. 是否紧急 = 是或封网操作 = 是，是否CCN操作 = 否，且风险等级 = 2星、3星
                || (isEmerOrNetClose
                && !isCcn
                && (changeOrder.getRiskEvaluation() == RiskEvaluationEnum.STAR2
                || changeOrder.getRiskEvaluation() == RiskEvaluationEnum.STAR3))) {
            return NodeWithInlinePageEnum.ADMIN_NET_DEPT_MNG_NODE;
        }
        return null;
    }

    private static NodeWithInlinePageEnum getIfEsd3MinisterRecommended(
            ChangeOrder changeOrder,
            int netAttribute,
            ApprovalLevelEnum approvalLevel) {
        // 1.网络属性 = 风险网络
        if (netAttribute == NetworkAttributeEnum.RISK.getValue()) {
            return NodeWithInlinePageEnum.ADMIN_ENG_SERVICE3_NODE;
        }
        // 2.网络属性 = 重要网络，且操作等级 = 关键
        if (changeOrder.getOperationLevel() == OperationLevelEnum.CRITICAL) {
            if (netAttribute == NetworkAttributeEnum.IMPORTANT.getValue()) {
                return NodeWithInlinePageEnum.ADMIN_ENG_SERVICE3_NODE;
            }

            // 3.网络属性 = 普通网络，且操作等级 = 关键，且操作场景 = 三级审批
            if (netAttribute == NetworkAttributeEnum.NORMAL.getValue()
                    && approvalLevel == ApprovalLevelEnum.LVL3) {
                return NodeWithInlinePageEnum.ADMIN_ENG_SERVICE3_NODE;
            }
        }
        return null;
    }

    private static NodeWithInlinePageEnum getGovEntRecommendLevel(ChangeOrder changeOrder) {
        int score = changeOrder.getImportance().getIntegerValue()
                + changeOrder.getRiskEvaluation().getIntValue();
        int max = ImportanceEnum.STAR3.getIntegerValue()
                + RiskEvaluationEnum.STAR3.getIntValue();

        return score == max
                ? NodeWithInlinePageEnum.ADMIN_ENG_SERVICE3_NODE
                : NodeWithInlinePageEnum.ADMIN_NET_DEPT_MNG_NODE;
    }

    private static ApprovalLevelEnum getApprovalLevel(String opScenarioId) {
        if (StringUtils.isBlank(opScenarioId)) {
            return null;
        }

        OperationScenario opScenario = QueryDataHelper.get(
                OperationScenario.class, Lists.newArrayList(APPROVAL_LEVEL), opScenarioId);
        return opScenario.getApprovalLevel();
    }

    public static int getNetworkAttribute(String changeOrderId) {
        final String cacheKey = String.format("changeOrder:%s:networkAttribute", changeOrderId);

        Integer networkAttribute = LocalCacheUtils.get(cacheKey);
        if (networkAttribute != null) {
            return networkAttribute;
        }

        List<String> networkIds = QueryDataHelper.query(
                        OperationObject.class,
                        Lists.newArrayList(OperationObjectFieldConsts.NETWORK_ID),
                        changeOrderId)
                .stream()
                .map(oo -> ((OperationObject) oo).getNetworkId())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(networkIds)) {
            networkAttribute = NetworkAttributeEnum.NORMAL.getValue();
            LocalCacheUtils.set(cacheKey, networkAttribute, LOCAL_THREAD_EXPIRE_MINUTES);
            return networkAttribute;
        }

        networkAttribute = QueryDataHelper.query(
                        NetworkConfiguration.class,
                        Lists.newArrayList(NETWORK_ATTRIBUTE),
                        Lists.newArrayList(
                                new Filter(NetworkConfigurationFieldConsts.NETWORK_ID, Comparator.IN, networkIds)))
                .stream()
                .filter(nc -> !CollectionUtils.isEmpty(nc.getNetworkAttribute()))
                .map(nc -> Integer.parseInt(nc.getNetworkAttribute().get(0).getValue()))
                .min(Integer::compareTo)
                .orElse(NetworkAttributeEnum.NORMAL.getValue());
        LocalCacheUtils.set(cacheKey, networkAttribute, LOCAL_THREAD_EXPIRE_MINUTES);
        return networkAttribute;
    }

    /**
     * 三营,判断是否需要行政审批（以下条件满足其一即可）：
     * 1、【紧急or封网操作】选择【是】；
     * 2、网络属性是【重要网络】；
     * 3、网络属性是【风险网络】；
     * 4、网络属性是【普通网络】，且操作等级是【关键】；
     * @param changeOrder
     * @return
     */
    private static BoolEnum getInnerAdminApprovalFlag(ChangeOrder changeOrder, List<OperationObject> opObjects) {

        // 是紧急操作 或 是封网、管控期操作
        if (changeOrder.getIsEmergencyOperation() == BoolEnum.Y
                || changeOrder.getIsNetCloseOrControlOperation() == BoolEnum.Y) {
            return BoolEnum.Y;
        }

        if (CollectionUtils.isEmpty(opObjects)) {
            return BoolEnum.N;
        }

        List<String> networkIds = opObjects.stream()
                .map(OperationObject::getNetworkId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(networkIds)) {
            return BoolEnum.N;
        }

        /* Started by AICoder, pid:c7c7cd82cb0b4aff81c0faf3495e7d9d */
        // 获取 等级最严重的网络属性（1-风险网络，2-重要网络，3-普通网络）
        List<NetworkConfiguration> networkConfigurationList =
                NetworkConfigurationAbility.queryByNetworkId(networkIds);
        int networkAttributeMax = networkConfigurationList.stream()
                .filter(attribute -> !CollectionUtils.isEmpty(attribute.getNetworkAttribute()))
                .mapToInt(attribute -> Integer.parseInt(attribute.getNetworkAttribute().get(INTEGER_ZERO).getValue()))
                .min()
                .orElse(NetworkAttributeEnum.NORMAL.getValue());
        /* Ended by AICoder, pid:c7c7cd82cb0b4aff81c0faf3495e7d9d */

        // 网络属性是【风险网络】/【重要网络】
        if (NetworkAttributeEnum.RISK.getValue() == networkAttributeMax
                || NetworkAttributeEnum.IMPORTANT.getValue() == networkAttributeMax) {
            return BoolEnum.Y;
        }

        // 网络属性是【普通网络】，且 操作等级为关键
        if (NetworkAttributeEnum.NORMAL.getValue() == networkAttributeMax
                && OperationLevelEnum.CRITICAL == changeOrder.getOperationLevel()) {
            return BoolEnum.Y;
        }

        return BoolEnum.N;
    }

    /**
     * 政企,判断是否需要行政审批：
     * 特殊：操作类型为 保障类操作 且【是否需提供详细保障方案】选择【否】，不需要。
     * 以下满足其一，则需要：
     * 1、是否封网、管控期操作字段选择为【是】
     * 2、是否紧急操作字段选择为【是】
     * 3、重要程度+风险评估的星级 大于等于五星
     * @param changeOrder
     * @return
     */
    public static BoolEnum getGovEntAdminApprovalFlag(ChangeOrder changeOrder) {
        if (changeOrder.getOperationType() == null
                || changeOrder.getImportance() == null
                || changeOrder.getRiskEvaluation() == null
                || (changeOrder.getIsEmergencyOperation() == null
                && changeOrder.getIsNetCloseOrControlOperation() == null)) {
            return null;
        }

        boolean isGuaranteeOperationType = OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE.equals(changeOrder.getOperationTypeGroup());
        // 保障类操作 且【是否需提供详细保障方案】选择【否】，不需要 行政审批
        if (isGuaranteeOperationType && changeOrder.getIsGuaranteeSolution() == BoolEnum.N) {
            return BoolEnum.N;
        }

        // 是紧急操作 或 是封网、管控期操作
        if (changeOrder.getIsEmergencyOperation() == BoolEnum.Y
                || changeOrder.getIsNetCloseOrControlOperation() == BoolEnum.Y) {
            return BoolEnum.Y;
        }

        // 获取 重要程度，风险评估的值
        /* Started by AICoder, pid:9f837e6ce36140a4baeea30f540e8596 */
        Integer importanceValue = changeOrder.getImportance().getIntegerValue();
        Integer riskEvaluationValue = changeOrder.getRiskEvaluation().getIntValue();
        /* Ended by AICoder, pid:9f837e6ce36140a4baeea30f540e8596 */
        // 重要程度 + 风险评估的星级 ≥ 5
        if (importanceValue + riskEvaluationValue >= CommonConstants.GOV_ENT_NEED_ADMIN_APPROVAL_STAR_NUM) {
            return BoolEnum.Y;
        }

        return BoolEnum.N;
    }

    /**
     * 三营：是否能匹配到电信服务总监角色   + 变更单是否是 中国电信
     * 根据当前申请单【产品】（优先级：产品小类>产品小类>产品线）+ 【代表处】+申请单【是否政企为否】条件匹配
     *
     * @param changeOrder 变更单
     * @return BoolEnum
     */
    public static BoolEnum isMatchDirTeleServiceRole(ChangeOrder changeOrder) {
        DeptTypeEnum deptTypeEnum = ChangeOrderAbility.getDeptType(changeOrder);
        // 为三营才进行处理，否则直接返回false
        if (DeptTypeEnum.INNER.equals(deptTypeEnum) && OperatorEnum.isChinaTelecom(changeOrder.getCustomerTypeFlag())) {
            if (changeOrder.getIsGovEnt() == BoolEnum.N) {
                // 三营电信服务总监匹配
                ApproverConfiguration queryParam = new ApproverConfiguration();
                // 行政领导-三营审核配置
                queryParam.setApprovalNode(ApprovalTypeEnum.ADMINISTRATIVE_LEADER_DIVISION_THREE);
                // 代表处
                queryParam.setResponsibleDeptId(changeOrder.getResponsibleDept());
                // 片区
                queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
                // 营销单位
                queryParam.setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
                // 是否政企为否
                queryParam.setIsGov(BoolEnum.N);
                // 角色：电信服务总监
                queryParam.setRole(ApproveRoleEnum.DIRECTOR_OF_TELECOM_SERVICES);
                ApproverConfiguration approvalConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, changeOrder.getProductCategory(), 0);
                if (approvalConfiguration != null) {
                    return BoolEnum.Y;
                }
            }
        }
        return BoolEnum.N;
    }

    /**
     * 根据类型和节点名称获取不同的审批枚举
     *
     * @param entityClass 实体类（变更单/批次）
     * @param extendedCode 节点名称
     * @return 审批枚举
     */
   public static SingletonTextValuePairsProvider getApproveNodeName(Class<? extends BaseEntity> entityClass,
                                                              String extendedCode) {
        if (entityClass == ChangeOrder.class
                || entityClass == BatchTask.class) {
            return ApproveNodeEnum.getApproveNodeEnum(extendedCode);
        } else if (entityClass == SubcontractorChangeOrder.class
                || entityClass == SubcontractorBatchTask.class) {
            return PartnerApproveNodeEnum.getApproveNodeEnum(extendedCode);
        }
        return null;
    }

    /**
     * 技术交付/网络处 审核提交重新计算是否需要行政领导审核
     */
    public static BoolEnum againIsAdminApproval(ChangeOrder changeOrder) {
        ChangeOrder dataChangeOrder = ChangeOrderAbility.get(changeOrder.getId(), new ArrayList<>());
        if (dataChangeOrder == null) {
            return null;
        }

        dataChangeOrder.setIsEmergencyOperation(changeOrder.getIsEmergencyOperation());
        dataChangeOrder.setIsNetCloseOrControlOperation(changeOrder.getIsNetCloseOrControlOperation());
        dataChangeOrder.setImportance(changeOrder.getImportance());
        dataChangeOrder.setRiskEvaluation(changeOrder.getRiskEvaluation());
        dataChangeOrder.setOperationLevel(changeOrder.getOperationLevel());
        List<OperationObject> opObjects = OperationObjectAbility.listOperationObject(changeOrder.getId(),
                OperationObject.class);
        return isAdministrationLeaderApproval(changeOrder, opObjects);
    }
}
