package com.zte.iccp.itech.extension.domain.enums.changeorder;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/7/17 下午5:30
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum TimeConflictEnum {
    /**
     * 有冲突
     */
    Y("有冲突", "conflict"),

    /**
     * 无冲突
     */
    N("无冲突", "no conflict"),

    /**
     * 无需检测
     */
    NO_NEED("无需检测", "No Need"),

    /**
     * 检测中
     */
    DETECTING("检测中", "Detecting"),

    /**
     * 未检测
     */
    NO_DETECTION("未检测", "no detection"),
    ;

    /**
     * 中文名称
     */
    private final String zhCn;

    /**
     * 英文名称
     */
    private final String enUs;

}
