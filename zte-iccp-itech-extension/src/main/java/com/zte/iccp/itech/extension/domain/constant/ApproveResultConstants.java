package com.zte.iccp.itech.extension.domain.constant;

/**
 * 审批结果文本 - 用于后台保存构建复选实体
 */
public class ApproveResultConstants {

    /** 中文文本 */
    public static class ChineseText {
        public static final String AGREE = "同意";

        public static final String DISAGREE = "不同意";

        public static final String AGREE_CONCLUDE_AUDIT = "同意&终结审核";

        public static final String AGREE_UPGRADE_REVIEW_REQUIRED = "同意&需升级审核";

        public static final String AGREE_N0_THREE_LEVEL_REVIEW_REQUIRED = "同意&无需三层审核";

        public static final String AGREE_THREE_LEVEL_REVIEW_REQUIRED = "同意&需三层领导审核";

        public static final String PASS = "通过";

        public static final String APPROVE_PASS = "审核通过";

        public static final String APPROVE_REJECT_MODIFY = "审核驳回修改";

        public static final String APPROVE_TERMINATE = "审核不通过，终止";
    }

    /** 英文文本 */
    public static class EnglishText {

        /** 同意 */
        public static final String AGREE = "PASS";

        /** 不同意 */
        public static final String DISAGREE = "TERMINATE";

        /** 同意&终结审核 */
        public static final String AGREE_CONCLUDE_AUDIT = "SKIP";

        /** 同意&需升级审核 */
        public static final String AGREE_UPGRADE_REVIEW_REQUIRED = "PASS";

        /** 同意&无需三层审核 */
        public static final String AGREE_N0_THREE_LEVEL_REVIEW_REQUIRED = "SKIP";

        /** 同意&需三层领导审核 */
        public static final String AGREE_THREE_LEVEL_REVIEW_REQUIRED = "PASS";

        /** 通过 */
        public static final String PASS = "Pass";

        /** 审核驳回修改 */
        public static final String APPROVE_REJECT = "Reject";

        /** 审核通过 */
        public static final String APPROVE_PASS = "PASS";

        public static final String APPROVE_REJECT_MODIFY = "TERMINATE";

        /** 审核不通过，终止 */
        public static final String APPROVE_TERMINATE = "CANCEL";

        /** 驳回 */
        public static final String REJECT = "REJECT";
    }
}
