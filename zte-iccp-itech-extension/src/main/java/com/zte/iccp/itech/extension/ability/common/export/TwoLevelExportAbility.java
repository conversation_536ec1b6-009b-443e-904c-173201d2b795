package com.zte.iccp.itech.extension.ability.common.export;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.enums.export.ExportFieldEnum;
import com.zte.iccp.itech.extension.domain.enums.export.ExportFieldGroupEnum;
import com.zte.iccp.itech.extension.domain.model.export.TwoLevelHeaderSheet;
import com.zte.iccp.itech.extension.spi.client.EmailClient;
import com.zte.paas.lcap.helper.util.FileStorageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 二级表头导出工具类
 * 用于创建带有分组的二级表头Excel导出
 * 支持自定义样式、超链接等功能
 */
@Slf4j
public class TwoLevelExportAbility {

    private final Workbook workbook;
    private final CellStyle contentStyle;
    private final CellStyle headStyle;
    private final Map<ExportFieldGroupEnum, CellStyle> groupHeadStyleMap = new HashMap<>();
    private static final int HEADER_ROW_COUNT = 2;
    private final Font boldFont;
    private final Font whiteBoldFont;
    private final Font hyperlinkFont;
    private static final int ERROR_REPORT_COLUMN_COUNT = 4;
    private static final int MAX_STACK_TRACE_LENGTH = 32000;

    private TwoLevelExportAbility(Workbook workbook) {
        this.workbook = workbook;

        // 初始化字体对象
        this.boldFont = createBoldFont();
        this.whiteBoldFont = createWhiteBoldFont();
        this.hyperlinkFont = createHyperlinkFont();

        this.contentStyle = createContentStyle();
        this.headStyle = createHeadStyle();
        initGroupHeadStyles();
    }

    /**
     * 创建粗体字体
     *
     * @return 粗体字体
     */
    private Font createBoldFont() {
        Font font = workbook.createFont();
        font.setBold(true);
        return font;
    }

    /**
     * 创建白色粗体字体
     *
     * @return 白色粗体字体
     */
    private Font createWhiteBoldFont() {
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        return font;
    }

    /**
     * 创建超链接字体
     *
     * @return 超链接字体
     */
    private Font createHyperlinkFont() {
        Font font = workbook.createFont();
        font.setColor(IndexedColors.BLUE.index);
        return font;
    }

    /**
     * 创建内容单元格样式
     * 设置四周边框为细线
     *
     * @return 内容单元格样式
     */
    private CellStyle createContentStyle() {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        return cellStyle;
    }

    /**
     * 创建表头单元格样式
     * 基于内容样式，增加粗体字体，居中对齐
     *
     * @return 表头单元格样式
     */
    private CellStyle createHeadStyle() {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.cloneStyleFrom(contentStyle);
        cellStyle.setFont(boldFont);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return cellStyle;
    }

    /**
     * 初始化各分组的表头样式
     * 为不同的分组设置不同的背景颜色
     */
    private void initGroupHeadStyles() {
        // 网络变更操作申请 --紫色
        createGroupHeadStyle(ExportFieldGroupEnum.NETWORK_CHANGE_APPLICATION, IndexedColors.LAVENDER);

        // 技术交付部/网络处审核 --绿色
        createGroupHeadStyle(ExportFieldGroupEnum.TECH_DELIVERY_DEPT_REVIEW, IndexedColors.LIGHT_GREEN);

        // 网络服务部审核 --橘色
        createGroupHeadStyle(ExportFieldGroupEnum.NETWORK_SERVICE_DEPT_REVIEW, IndexedColors.LIGHT_ORANGE);

        // 研发经理审批 --淡蓝色
        createGroupHeadStyle(ExportFieldGroupEnum.RD_MANAGER_REVIEW, IndexedColors.LIGHT_BLUE);

        // 研发领导审批 --淡黄色
        createGroupHeadStyle(ExportFieldGroupEnum.RD_LEADER_REVIEW, IndexedColors.LIGHT_YELLOW);

        // 操作计划确认与通告 --深蓝色
        createGroupHeadStyle(ExportFieldGroupEnum.OPERATION_PLAN_CONFIRMATION, IndexedColors.BLUE, true);

        // 反馈操作结果 --天蓝色
        createGroupHeadStyle(ExportFieldGroupEnum.OPERATION_RESULT_FEEDBACK, IndexedColors.LIGHT_TURQUOISE);

        // 审核确认 --淡紫色
        createGroupHeadStyle(ExportFieldGroupEnum.REVIEW_CONFIRMATION, IndexedColors.PINK);

        // 操作人员及支持人员列表 ---淡绿色
        createGroupHeadStyle(ExportFieldGroupEnum.OPERATION_PERSONNEL, IndexedColors.BRIGHT_GREEN);

        // 其他信息 --橘色
        createGroupHeadStyle(ExportFieldGroupEnum.OTHER_INFO, IndexedColors.LIGHT_ORANGE);
        
        // 代表处TD审批 -- 淡紫色
        createGroupHeadStyle(ExportFieldGroupEnum.TD_REVIEW, IndexedColors.PALE_BLUE);

        // 核心网大区TD审核 -- 金色
        createGroupHeadStyle(ExportFieldGroupEnum.CN_REGIONAL_TD_REVIEW, IndexedColors.GOLD);
        
        // 局点信息 -- 淡绿黄色
        createGroupHeadStyle(ExportFieldGroupEnum.OFFICE_INFO, IndexedColors.LIGHT_GREEN);
        
        // CCN Checklist -- 淡橙色
        createGroupHeadStyle(ExportFieldGroupEnum.CCN_CHECKLIST, IndexedColors.LIGHT_CORNFLOWER_BLUE);

        // 网络责任人审核 -- 土色
        createGroupHeadStyle(ExportFieldGroupEnum.NET_OWNER_REVIEW, IndexedColors.TAN);

        // 办事处产品经理审核 -- 绿色
        createGroupHeadStyle(ExportFieldGroupEnum.OFFICE_MANAGER, IndexedColors.SEA_GREEN);
    }

    /**
     * 创建分组表头样式
     *
     * @param group     分组枚举
     * @param color     背景颜色
     * @param whiteFont 是否使用白色字体（深色背景时使用）
     */
    private void createGroupHeadStyle(ExportFieldGroupEnum group, IndexedColors color, boolean whiteFont) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.cloneStyleFrom(headStyle);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 设置背景颜色
        cellStyle.setFillForegroundColor(color.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 对于深色背景，使用白色字体
        if (whiteFont) {
            cellStyle.setFont(whiteBoldFont);
        }

        groupHeadStyleMap.put(group, cellStyle);
    }

    /**
     * 创建分组表头样式（默认使用黑色字体）
     *
     * @param group 分组枚举
     * @param color 背景颜色
     */
    private void createGroupHeadStyle(ExportFieldGroupEnum group, IndexedColors color) {
        createGroupHeadStyle(group, color, false);
    }

    /**
     * 创建超链接样式
     * 设置蓝色字体，保留边框
     *
     * @return 超链接样式
     */
    private CellStyle createHyperlinkStyle() {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.cloneStyleFrom(contentStyle);
        cellStyle.setFont(hyperlinkFont);
        return cellStyle;
    }

    /**
     * 设置单元格值
     *
     * @param row      行对象
     * @param colIndex 列索引
     * @param value    单元格值
     */
    private void setCellValue(Row row, int colIndex, String value) {
        Cell cell = row.createCell(colIndex);
        cell.setCellStyle(contentStyle);
        cell.setCellValue(value);
    }

    /**
     * 设置单元格超链接
     *
     * @param workbook 工作簿
     * @param row      行对象
     * @param colIndex 列索引
     * @param url      超链接URL
     */
    private void setHyperlink(Workbook workbook, Row row, int colIndex, String url) {
        CreationHelper createHelper = workbook.getCreationHelper();
        Hyperlink hyperlink = createHelper.createHyperlink(HyperlinkType.URL);
        hyperlink.setAddress(url);
        Cell cell = row.getCell(colIndex);
        cell.setHyperlink(hyperlink);
        cell.setCellStyle(createHyperlinkStyle());
    }

    public static <T> void write(TwoLevelHeaderSheet<T> sheetInfo, Workbook workbook) {
        if (sheetInfo == null || workbook == null) {
            return;
        }

        TwoLevelExportAbility ability = new TwoLevelExportAbility(workbook);
        ability.writeTwoLevelHeader(sheetInfo);
        ability.writeElements(sheetInfo);
    }

    /**
     * 写入二级表头
     * 创建分组行和字段行
     *
     * @param sheetInfo 表头信息
     * @param <T>       数据类型
     */
    private <T> void writeTwoLevelHeader(TwoLevelHeaderSheet<T> sheetInfo) {
        String sheetName = sheetInfo.getSheetName();
        Sheet sheet = workbook.getSheet(sheetName);
        boolean isNewSheet = false;
        if (sheet == null) {
            sheet = workbook.createSheet(sheetName);
            isNewSheet = true;
        }

        if (isNewSheet) {
            // 创建第一行 - 分组表头
            Row groupRow = sheet.createRow(0);
            groupRow.setHeight((short)(sheet.getDefaultRowHeight() * 1.3));
            
            // 创建第二行 - 字段表头
            Row fieldRow = sheet.createRow(1);
            fieldRow.setHeight((short)(sheet.getDefaultRowHeight() * 2.0));

            int columnIndex = 0;

            for (ExportFieldEnum.ExportHeaderInfo headerInfo : sheetInfo.getHeaderInfos()) {
                CellStyle groupStyle = groupHeadStyleMap.getOrDefault(
                        headerInfo.getGroup(), headStyle);
                Cell groupCell = groupRow.createCell(columnIndex);
                groupCell.setCellStyle(groupStyle);

                String groupTitle = MsgUtils.getMessage(headerInfo.getGroup().getDisplayName());
                groupCell.setCellValue(groupTitle);
                int startCol = columnIndex;
                int endCol = columnIndex + headerInfo.getFieldCount() - 1;
                if (startCol != endCol) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, startCol, endCol));
                }
                for (int i = 0; i < headerInfo.getFieldCount(); i++) {
                    Cell fieldCell = fieldRow.createCell(columnIndex + i);
                    fieldCell.setCellStyle(groupStyle);
                    ExportFieldEnum field = findFieldByIndex(sheetInfo.getExportFields(), startCol + i);
                    if (field != null) {
                        // 使用消息键获取国际化消息
                        fieldCell.setCellValue(field.getDisplayName());
                    }
                }

                columnIndex += headerInfo.getFieldCount();
            }
            setDynamicColumnWidths(sheet);
        } else {
            int lastRowNum = sheet.getLastRowNum();
            sheetInfo.setStartRows(lastRowNum + 1 - HEADER_ROW_COUNT);
        }
    }

    /**
     * 根据索引查找导出字段
     *
     * @param fields 字段列表
     * @param index  索引
     * @return 对应索引的导出字段，如果不存在返回null
     */
    private ExportFieldEnum findFieldByIndex(List<ExportFieldEnum> fields, int index) {
        if (fields == null || index < 0 || index >= fields.size()) {
            return null;
        }
        return fields.get(index);
    }

    /**
     * 写入数据元素
     * 通过反射获取对象属性值并写入Excel
     *
     * @param sheetInfo 表头信息
     * @param <T>       数据类型
     */
    private <T> void writeElements(TwoLevelHeaderSheet<T> sheetInfo) {
        try {
            Sheet sheet = workbook.getSheet(sheetInfo.getSheetName());
            List<ExportFieldEnum> exportFields = sheetInfo.getExportFields();

            // 设置当前行索引：表头占用的行数 + 额外的起始行数
            int currentRowIndex = HEADER_ROW_COUNT + sheetInfo.getStartRows();

            Map<String, Field> fieldCache = new HashMap<>();
            if (!sheetInfo.getElements().isEmpty()) {
                for (ExportFieldEnum field : exportFields) {
                    String propertyName = field.getPropertyName();
                    try {
                        Field elementField = sheetInfo.getElements().get(0).getClass().getDeclaredField(propertyName);
                        elementField.setAccessible(true);
                        fieldCache.put(propertyName, elementField);
                    } catch (NoSuchFieldException e) {
                        log.warn("field {} not exist", propertyName);
                    }
                }
            }

            for (int i = 0; i < sheetInfo.getElements().size(); ++i) {
                Row row = sheet.createRow(currentRowIndex + i);
                row.setHeight((short)(sheet.getDefaultRowHeight() * 1.2));
                T element = sheetInfo.getElements().get(i);

                for (int j = 0; j < exportFields.size(); ++j) {
                    ExportFieldEnum field = exportFields.get(j);
                    String propertyName = field.getPropertyName();

                    // 使用缓存的字段
                    Field elementField = fieldCache.get(propertyName);
                    if (elementField != null) {
                        Object value = elementField.get(element);
                        String cellValue = Objects.isNull(value) ? "" : value.toString();

                        setCellValue(row, j, cellValue);

                        // 设置超链接
                        Map<Integer, List<String>> hyperlinks = sheetInfo.getHyperlinks();
                        if (!CollectionUtils.isEmpty(hyperlinks.get(j))
                                && i < hyperlinks.get(j).size()
                                && StringUtils.hasText(hyperlinks.get(j).get(i))) {
                            setHyperlink(workbook, row, j, hyperlinks.get(j).get(i));
                        }
                    } else {
                        setCellValue(row, j, "");
                    }
                }
            }
        } catch (Exception e) {
            log.error("write data fail", e);
            throw new RuntimeException("write data fail", e);
        }
    }

    /**
     * 创建二级表头导出信息
     *
     * @param exportFields 导出字段列表
     * @param sheetName    工作表名称（国际化消息键）
     * @param <T>          数据类型
     * @return 二级表头导出信息
     */
    public static <T> TwoLevelHeaderSheet<T> createTwoLevelSheet(
            List<ExportFieldEnum> exportFields,
            String sheetName) {

        TwoLevelHeaderSheet<T> sheetInfo = new TwoLevelHeaderSheet<>();

        // 设置Sheet页名称
        sheetInfo.setSheetName(MsgUtils.getMessage(sheetName));

        // 对导出字段按照分组顺序进行排序
        List<ExportFieldEnum> sortedFields = ExportFieldEnum.sortFieldsByGroup(exportFields);

        // 设置导出字段
        sheetInfo.setExportFields(sortedFields);

        // 生成分组信息
        List<ExportFieldEnum.ExportHeaderInfo> headerInfos =
                ExportFieldEnum.generateTwoLevelHeaders(sortedFields);
        sheetInfo.setHeaderInfos(headerInfos);

        return sheetInfo;
    }

    /**
     * 向现有表单追加行数据，不创建新样式
     *
     * @param sheetInfo 表头信息
     * @param workbook  工作簿
     * @param sheet     工作表
     * @throws Exception 处理异常
     */
    public static <T> void appendRowsToExistingSheet(
            TwoLevelHeaderSheet<T> sheetInfo,
            Workbook workbook, 
            Sheet sheet) throws Exception {
        
        // 获取和复用现有单元格样式
        CellStyle contentStyle = getOrCreateContentStyle(workbook, sheet);
        CellStyle hyperlinkStyle = null;

        // 当前行索引和字段列表
        int currentRowIndex = sheet.getLastRowNum() + 1;
        List<ExportFieldEnum> exportFields = sheetInfo.getExportFields();

        // 预缓存字段反射信息
        Class<?> clazz = sheetInfo.getElements().isEmpty() ? null : sheetInfo.getElements().get(0).getClass();
        Map<String, Field> fieldCache = new HashMap<>();
        if (clazz != null) {
            for (ExportFieldEnum field : exportFields) {
                Field elementField = clazz.getDeclaredField(field.getPropertyName());
                elementField.setAccessible(true);
                fieldCache.put(field.getPropertyName(), elementField);
            }
        }

        // 创建超链接帮助类和批处理参数
        CreationHelper createHelper = workbook.getCreationHelper();
        int totalElements = sheetInfo.getElements().size();
        int batchSize = 1000;

        // 分批处理数据
        for (int batchStart = 0; batchStart < totalElements; batchStart += batchSize) {
            int batchEnd = Math.min(batchStart + batchSize, totalElements);

            // 处理当前批次
            for (int i = batchStart; i < batchEnd; i++) {
                int rowIndex = currentRowIndex + i - batchStart;
                Row row = sheet.createRow(rowIndex);
                Object element = sheetInfo.getElements().get(i);

                for (int j = 0; j < exportFields.size(); j++) {
                    ExportFieldEnum field = exportFields.get(j);
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(contentStyle);

                    // 设置单元格值
                    Field elementField = fieldCache.get(field.getPropertyName());
                    if (elementField != null) {
                        Object value = elementField.get(element);
                        cell.setCellValue(Objects.isNull(value) ? "" : value.toString());

                        // 处理超链接
                        Map<Integer, List<String>> hyperlinks = sheetInfo.getHyperlinks();
                        List<String> columnHyperlinks = hyperlinks.get(j);
                        if (columnHyperlinks != null && i < columnHyperlinks.size() &&
                            StringUtils.hasText(columnHyperlinks.get(i))) {

                            // 延迟创建超链接样式
                            if (hyperlinkStyle == null) {
                                hyperlinkStyle = workbook.createCellStyle();
                                hyperlinkStyle.cloneStyleFrom(contentStyle);
                                Font hyperlinkFont = workbook.createFont();
                                hyperlinkFont.setColor(IndexedColors.BLUE.index);
                                hyperlinkStyle.setFont(hyperlinkFont);
                            }

                            Hyperlink hyperlink = createHelper.createHyperlink(HyperlinkType.URL);
                            hyperlink.setAddress(columnHyperlinks.get(i));
                            cell.setHyperlink(hyperlink);
                            cell.setCellStyle(hyperlinkStyle);
                        }
                    } else {
                        cell.setCellValue("");
                    }
                }
            }

            // 更新当前行索引
            currentRowIndex += (batchEnd - batchStart);
        }

        // 更新起始行索引
        sheetInfo.setStartRows(currentRowIndex);
    }

    /**
     * 获取或创建内容单元格样式
     * 尝试从现有单元格获取样式，如果无法获取则创建新样式
     *
     * @param workbook 工作簿
     * @param sheet    工作表
     * @return 内容单元格样式
     */
    private static CellStyle getOrCreateContentStyle(Workbook workbook, Sheet sheet) {
        CellStyle contentStyle = null;

        // 尝试从现有行获取样式
        if (sheet.getLastRowNum() >= 2) {
            Row existingRow = sheet.getRow(2);
            if (existingRow != null && existingRow.getFirstCellNum() >= 0) {
                Cell cell = existingRow.getCell(existingRow.getFirstCellNum());
                contentStyle = cell.getCellStyle();
            }
        }

        // 如果无法获取样式，创建新样式
        if (contentStyle == null) {
            contentStyle = workbook.createCellStyle();
            contentStyle.setBorderTop(BorderStyle.THIN);
            contentStyle.setBorderBottom(BorderStyle.THIN);
            contentStyle.setBorderLeft(BorderStyle.THIN);
            contentStyle.setBorderRight(BorderStyle.THIN);
        }

        return contentStyle;
    }

    /**
     * 创建错误报告Excel
     * 当导出过程中发生错误时，创建一个包含错误信息的Excel文件，并发送邮件通知
     *
     * @param e          异常
     * @param templateId 邮件模板ID
     */
    public static void createErrorReportExcel(Exception e, TemplateIdEnum templateId) {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream os = new ByteArrayOutputStream()) {

            Sheet sheet = createErrorReportSheet(workbook, e);

            // 自动调整列宽
            for (int i = 0; i < ERROR_REPORT_COLUMN_COUNT; i++) {
                sheet.autoSizeColumn(i);
            }

            // 保存、上传并发送邮件
            workbook.write(os);
            String fileName = "Export_Error_Report_" + System.currentTimeMillis() + ".xlsx";
            String key = FileStorageUtils.uploadExportFile(fileName, os.toByteArray());
            Map<String, Object> urlData = new HashMap<>();
            urlData.put(CommonConstants.URL, FileStorageUtils.buildDownLoadUrl(key, fileName));
            EmailClient.sendMail(templateId,
                    Collections.singletonList(ContextHelper.getEmpNo()), null, urlData, null);

        } catch (Exception ex) {
            log.error("Failed to create error report", ex);
        }
    }

    /**
     * 创建错误报告工作表
     *
     * @param workbook 工作簿
     * @param e 异常
     * @return 工作表
     */
    private static Sheet createErrorReportSheet(Workbook workbook, Exception e) {
        Sheet sheet = workbook.createSheet("Error Report");

        // 创建表头和设置样式
        Row headerRow = sheet.createRow(0);
        String[] headers = {"Error Time", "Error Type", "Error Message", "Stack Trace"};

        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充错误数据
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue(new Date().toString());
        dataRow.createCell(1).setCellValue(e.getClass().getName());
        dataRow.createCell(2).setCellValue(e.getMessage() != null ? e.getMessage() : "No error message");

        // 获取并限制堆栈信息长度
        String stackTrace = getStackTraceAsString(e);
        dataRow.createCell(3).setCellValue(stackTrace);

        return sheet;
    }

    /**
     * 获取异常的堆栈信息
     *
     * @param e 异常
     * @return 堆栈信息字符串
     */
    private static String getStackTraceAsString(Exception e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw));
        String stackTrace = sw.toString();
        if (stackTrace.length() > MAX_STACK_TRACE_LENGTH) {
            stackTrace = stackTrace.substring(0, MAX_STACK_TRACE_LENGTH) + "...";
        }
        return stackTrace;
    }

    /**
     * 为工作表设置动态列宽，基于表头内容
     * 仅对任务名称列进行特殊处理（加宽），其他列使用标准宽度
     *
     * @param sheet 工作表
     */
    public static void setDynamicColumnWidths(Sheet sheet) {
        if (sheet == null || sheet.getRow(0) == null || sheet.getRow(1) == null) {
            log.warn("Cannot set dynamic column widths for null sheet or sheet without header rows");
            return;
        }

        try {
            // 获取字段表头行（第二行）
            Row fieldHeaderRow = sheet.getRow(1);
            
            // 列宽特殊处理映射表（字段枚举 -> 列宽倍数）
            Map<String, Double> specialWidthMultipliers = new HashMap<>();
            
            String assignmentNameDisplay = ExportFieldEnum.ASSIGNMENT_NAME.getDisplayName();
            String assignmentCodeDisplay = ExportFieldEnum.ASSIGNMENT_CODE.getDisplayName();
            
            specialWidthMultipliers.put(assignmentNameDisplay, 10.0);
            specialWidthMultipliers.put(assignmentCodeDisplay, 3.0);
            
            for (int i = 0; i < fieldHeaderRow.getLastCellNum(); i++) {
                Cell cell = fieldHeaderRow.getCell(i);
                String cellValue = cell.getStringCellValue();
                double multiplier = 1.1;
                if (specialWidthMultipliers.containsKey(cellValue)) {
                    multiplier = specialWidthMultipliers.get(cellValue);
                }
                int headerLength = calculateTextLength(cellValue);
                int columnWidth = (int) (headerLength * multiplier * 256);
                int minWidth = 10 * 256;
                int maxWidth = 35 * 256;

                columnWidth = Math.min(Math.max(columnWidth, minWidth), maxWidth);

                try {
                    sheet.setColumnWidth(i, columnWidth);
                } catch (Exception e) {
                    log.warn("Failed to set column width for column {}: {}", i, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.warn("Exception while setting dynamic column widths: {}", e.getMessage());
        }
    }
    
    /**
     * 计算文本长度，中文字符计为2个单位
     * 
     * @param text 需要计算的文本
     * @return 计算后的长度
     */
    private static int calculateTextLength(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        
        int length = 0;
        for (char c : text.toCharArray()) {
            if (c >= 0x4E00 && c <= 0x9FA5) {
                length += 2;
            } else {
                length += 1;
            }
        }
        return length;
    }

    /**
     * 创建并配置SXSSFWorkbook实例
     *
     * @param rowAccessWindowSize 内存中保留的行数
     * @return 配置好的SXSSFWorkbook实例
     */
    public static SXSSFWorkbook createSXSSFWorkbook(int rowAccessWindowSize) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowAccessWindowSize);
        workbook.setCompressTempFiles(true);
        return workbook;
    }
}