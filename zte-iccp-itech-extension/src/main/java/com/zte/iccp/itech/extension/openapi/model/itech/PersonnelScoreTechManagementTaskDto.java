package com.zte.iccp.itech.extension.openapi.model.itech;

import com.zte.iccp.itech.extension.domain.enums.PersonnelScoreLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class PersonnelScoreTechManagementTaskDto {

    @ApiModelProperty("任务等级")
    private PersonnelScoreLevelEnum taskLevel;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务说明")
    private String taskDetail;

    @ApiModelProperty("扣分员工号")
    private String deductionEpmNo;

    @ApiModelProperty("扣分员工组织ID")
    private String orgId;

    @ApiModelProperty("子公司维护人员")
    private List<String> subsidiaryMaintenancePersons;

    @ApiModelProperty("数据来源id，技术管理任务日志主键id")
    private String sourceId;
}
