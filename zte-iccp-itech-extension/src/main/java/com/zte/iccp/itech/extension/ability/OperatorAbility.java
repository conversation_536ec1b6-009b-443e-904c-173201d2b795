package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.CollectionUtilsEx;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.changeorder.EntityEnum.SupportModeEnum;
import com.zte.iccp.itech.extension.domain.model.Operator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.domain.model.subentity.SupportStaff;
import com.zte.iccp.itech.extension.domain.model.subentity.support.*;
import com.zte.iccp.itech.extension.domain.model.vo.BatchTaskOperatorVO;
import com.zte.iccp.itech.extension.domain.model.vo.SubcontractorOperator;
import com.zte.iccp.itech.extension.plugin.form.changeorder.batchtask.helper.OperatorAttributeHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.LEFT_CORE_BRACKET;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.RIGHT_CORE_BRACKET;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.BATCH_NO;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATE_PERSON;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATOR_NAME;
import static com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum.*;

@Slf4j
public class OperatorAbility {

    /**
     * 网络变更 - 批次任务保存操作人员
     * @param changeOrderId
     * @param isSecondaryGuarantyOrder 是否关联保障单
     */
    public static void createBatchTaskOperator(String changeOrderId, boolean isSecondaryGuarantyOrder) {
        try {
            List<BatchTaskOperatorVO> taskOperators = getOperator(changeOrderId);
            List<BatchTask> batchTasks = BatchTaskAbility.listBatchTaskByChangeOrderId(changeOrderId, Arrays.asList(CommonFieldConsts.ID, BATCH_NO));
            if (CollectionUtils.isEmpty(batchTasks)) {
                return;
            }

            List<BatchTaskOperator> allBatchTaskOperators = new ArrayList<>();
            // 遍历将网络变更单操作对象，变为批次任务子对象
            batchTasks.forEach(batchtask -> {
                for (BatchTaskOperatorVO taskOperator : taskOperators) {
                    // 支持人员  + 批次对应
                    if (isNeedCopy(batchtask, taskOperator, isSecondaryGuarantyOrder)) {
                        continue;
                    }
                    BatchTaskOperator newTaskOperator = new BatchTaskOperator();
                    BeanUtils.copyProperties(taskOperator, newTaskOperator);
                    newTaskOperator.setPid(batchtask.getId());
                    newTaskOperator.setId(null);
                    OperatorAttributeEnum operatorAttribute = OperatorAttributeHelper.calculateOperatorAttribute(taskOperator.getOperatePersonEmpNo());
                    if (operatorAttribute == null) {
                        //获取不到
                        operatorAttribute = OperatorAttributeEnum.UNKNOWN;
                    }
                    newTaskOperator.setOperatorAttribute(BatchOperatorAttrEnum.fromValue(operatorAttribute.getValue()));
                    newTaskOperator.clearEntityValue();
                    allBatchTaskOperators.add(newTaskOperator);
                }
            });

            BatchTaskAbility.batchInsertSub(allBatchTaskOperators);
        } catch (Exception e) {
            log.error("changeOrder end create batch operator error! id:{}", changeOrderId);
        }
    }

    private static boolean isNeedCopy(BatchTask batchtask, BatchTaskOperatorVO taskOperator, boolean isSecondaryGuarantyOrder) {
        // 没有操作人员 错误
        if (taskOperator.getOperatePerson() == null) {
            return true;
        }

        // 支持人员没有批次 ，每个批次都需要复
        if (OperatorRoleEnum.SUPPORT_PERSONNEL == taskOperator.getOperatorRole()) {
            return false;
        }
        // 非保障单 批次号要对应
        if (!isSecondaryGuarantyOrder) {
            return CollectionUtils.isEmpty(taskOperator.getOperatorBatchNo())
                    || !TextValuePairHelper.getValueList(taskOperator.getOperatorBatchNo()).contains(batchtask.getBatchNo());
        }

        return false;
    }

    /**
     * 网络变更 - 查询操作人员
     * @param changeOrderId
     * @return List<BatchTaskOperator>
     */
    public static List<BatchTaskOperatorVO>  getOperator(String changeOrderId) {
        List<BatchTaskOperatorVO> batchTaskOperatorList = Lists.newArrayList();

        // todo 后续优化成并行检索，当前暂时串行
        // 1.检索并包装操作人员数据
        // (1) 检索基础操作人员
        List<Operator> basicOperatorList =
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(changeOrderId, Operator.class);
        batchTaskOperatorList.addAll(basicOperatorConvertBatchTaskOperator(basicOperatorList));

        // (2) 支持人员 - 代表处TD审核
        List<SupportStaff> supportStaffs = new ArrayList<>();
        List<SupportStaff> representativeTdStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffRepProdTdApp.class),
                SupportStaff.class);
        supportStaffs.addAll(representativeTdStaff);

        // (3) 支持人员 - 研发经理审批
        List<SupportStaff> rdManagerStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffRdManager.class),
                SupportStaff.class);
        supportStaffs.addAll(rdManagerStaff);

        // (4) 支持人员 - SSP 产品支持团队审批
        List<SupportStaff> sspProductSupportStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffSspProdSupport.class),
                SupportStaff.class);
        supportStaffs.addAll(sspProductSupportStaff);

        // (5) 支持人员 - 服务产品支持部审批
        List<SupportStaff> serviceProductSupportStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffServiceProdSupport.class),
                SupportStaff.class);
        supportStaffs.addAll(serviceProductSupportStaff);

        // (6) 支持人员 - 技术交付部 / 网络处审核远程方案
        List<SupportStaff> tdNetDepartmentSolutionStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffTdNetDeptAppSolution.class),
                SupportStaff.class);
        supportStaffs.addAll(tdNetDepartmentSolutionStaff);

        // (7) 支持人员 - 远程中心方案提交
        List<SupportStaff> remoteCenterSchemeStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffRemoteCenterScheme.class),
                SupportStaff.class);
        supportStaffs.addAll(remoteCenterSchemeStaff);

        // (8) 支持人员 - 远程中心操作实施指派
        List<SupportStaff> remoteCenterOperationAssignStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffTdRemoteCenterOperAssign.class),
                SupportStaff.class);
        supportStaffs.addAll(remoteCenterOperationAssignStaff);

        // (9) 支持人员 - 技术交付部 / 网络处审核
        List<SupportStaff> tdNetDepartmentStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffNetDeptApprove.class),
                SupportStaff.class);
        supportStaffs.addAll(tdNetDepartmentStaff);

        // (10) 支持人员 - 网络服务部审批
        List<SupportStaff> netServiceDepartmentStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(
                        changeOrderId, SupportStaffNetServiceDeptApprove.class),
                SupportStaff.class);
        supportStaffs.addAll(netServiceDepartmentStaff);

        // (11) 支持人员 - 网络一体化
        List<SupportStaff> netServiceIntegrationStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificSecondSubEntity(
                        changeOrderId, SupportStaffNetServiceIntegration.class),
                SupportStaff.class);
        supportStaffs.addAll(netServiceIntegrationStaff);

        // (12) 支持人员 - 研发一体化
        List<SupportStaff> rdIntegrationStaff = CollectionUtilsEx.copyListProperties(
                ChangeOrderAbility.listSpecificSecondSubEntity(
                        changeOrderId, SupportStaffPersonIdRdIntegration.class),
                SupportStaff.class);
        supportStaffs.addAll(rdIntegrationStaff);

        // 支持人员去重
        supportStaffs = supportStaffs.stream().distinct().collect(Collectors.toList());
        batchTaskOperatorList.addAll(supportStaffConvertBatchTaskOperator(supportStaffs));
        batchTaskOperatorList = batchTaskOperatorList.stream()
                .filter(item -> item.getOperatePerson() != null)
                .collect(Collectors.toList());

        return batchTaskOperatorList;
    }

    /**
     * 分包商网络变更 - 批次任务保存操作人员
     * @param changeOrderId
     */
    public static void createSubcontractBatchTaskOperator(String changeOrderId) {
        if (!StringUtils.hasText(changeOrderId)) {
            return;
        }

        // 1.查询 + 包装操作人员数据
        List<BatchTaskOperatorVO> operatorList = getSubcontractOperator(changeOrderId);
        if (CollectionUtils.isEmpty(operatorList)) {
            return;
        }

        // 2.检索分包商网络变更批次任务
        List<String> fieldList = Lists.newArrayList(ID, BATCH_NO);
        List<SubcontractorBatchTask> batchTaskList = BatchTaskAbility.batchGetByChangeOrderId(changeOrderId, SubcontractorBatchTask.class, fieldList);
        // 3.操作人员关联批次任务
        List<SubcontractorBatchOperator> allBatchTaskOperatorList = new ArrayList<>();
        for (SubcontractorBatchTask batchTask : batchTaskList) {
            for (BatchTaskOperatorVO operator : operatorList) {
                boolean notBacth = OperatorRoleEnum.SUPPORT_PERSONNEL == operator.getOperatorRole()
                        || (!CollectionUtils.isEmpty(operator.getOperatorBatchNo())
                        && TextValuePairHelper.getValueList(operator.getOperatorBatchNo()).contains(batchTask.getBatchNo()));
                if (operator.getOperatePerson() == null || !notBacth) {
                    continue;
                }
                SubcontractorBatchOperator batchOperator = new SubcontractorBatchOperator();
                BeanUtils.copyProperties(operator, batchOperator);
                batchOperator.setPid(batchTask.getId());
                batchOperator.setId(null);
                batchOperator.clearEntityValue();
                allBatchTaskOperatorList.add(batchOperator);
            }
        }

        // 4.新增操作人员数据
        BatchTaskAbility.batchInsertSub(allBatchTaskOperatorList);
    }

    /**
     * 分包商网络变更 - 查询操作人员
     * @param changeOrderId
     * @return List<BatchTaskOperator>
     */
    public static List<BatchTaskOperatorVO> getSubcontractOperator(String changeOrderId) {
        List<BatchTaskOperatorVO> batchTaskOperatorList = Lists.newArrayList();

        // todo 后续优化成并行检索，当前暂时串行
        // 1.基础操作人员
        List<String> fieldList = Lists.newArrayList();
        List<SupportStaff> supportStaffs = new ArrayList<>();
        List<Operator> basicOperatorList = CollectionUtilsEx.copyListProperties(PartnerChangeOrderAbility.listSpecificSubEntity(
                changeOrderId, SubcontractorOperator.class, fieldList), Operator.class);
        batchTaskOperatorList.addAll(basicOperatorConvertBatchTaskOperator(basicOperatorList));

        // 2.支持人员 - 网络责任人
        List<SupportStaff> representativeTdStaff = CollectionUtilsEx.copyListProperties(
                PartnerChangeOrderAbility.listSpecificSubEntity(
                        changeOrderId, SupportStaffSubNetOwner.class, Lists.newArrayList()),
                SupportStaff.class);
        supportStaffs.addAll(representativeTdStaff);

        // 3.支持人员 - 办事处产品经理
        List<SupportStaff> rdManagerStaff = CollectionUtilsEx.copyListProperties(
                PartnerChangeOrderAbility.listSpecificSubEntity(
                        changeOrderId, SupportStaffSubOfficeProdManager.class, Lists.newArrayList()),
                SupportStaff.class);
        supportStaffs.addAll(rdManagerStaff);

        // 4.支持人员 - 网络处审核
        List<SupportStaff> sspProductSupportStaff = CollectionUtilsEx.copyListProperties(
                PartnerChangeOrderAbility.listSpecificSubEntity(
                        changeOrderId, SupportStaffSubNetDept.class, Lists.newArrayList()),
                SupportStaff.class);
        supportStaffs.addAll(sspProductSupportStaff);

        supportStaffs = supportStaffs.stream().distinct().collect(Collectors.toList());
        batchTaskOperatorList.addAll(supportStaffConvertBatchTaskOperator(supportStaffs));

        batchTaskOperatorList = batchTaskOperatorList.stream()
                .filter(item -> item.getOperatePerson() != null)
                .collect(Collectors.toList());
        return batchTaskOperatorList;
    }

    /**
     * 支持人员 包装 批次任务操作人员
     * @param supportStaffList
     * @return List<BatchTaskOperator>
     */
    private static List<BatchTaskOperatorVO> supportStaffConvertBatchTaskOperator(List<SupportStaff> supportStaffList) {
        if (CollectionUtils.isEmpty(supportStaffList)) {
            return Lists.newArrayList();
        }

        List<BatchTaskOperatorVO> batchTaskOperatorList = Lists.newArrayList();
        for (SupportStaff supportStaff : supportStaffList) {
            BatchTaskOperatorVO batchTaskOperator = new BatchTaskOperatorVO();
            batchTaskOperator.setOperatorRole(OperatorRoleEnum.SUPPORT_PERSONNEL);
            batchTaskOperator.setOperatePerson(supportStaff.getPerson());
            batchTaskOperator.setOperatorPhone(supportStaff.getTelephoneNumber());
            batchTaskOperator.setOperatorDepartment(supportStaff.getDepartment());

            batchTaskOperator.setRemoteFlag(
                    SupportModeEnum.ONSITE_SUPPORT.equals(supportStaff.getSupportMode()) ? RemoteEnum.LOCAL : RemoteEnum.REMOTE);
            batchTaskOperator.setTaskDesc(supportStaff.getTaskDescription());

            batchTaskOperatorList.add(batchTaskOperator);
        }

        return batchTaskOperatorList;
    }

    /**
     * 基础操作人员 包装 批次任务操作人员
     * @param basicOperatorList
     * @return List<BatchTaskOperator>
     */
    private static List<BatchTaskOperatorVO> basicOperatorConvertBatchTaskOperator(List<Operator> basicOperatorList) {
        if (CollectionUtils.isEmpty(basicOperatorList)) {
            return Lists.newArrayList();
        }

        List<BatchTaskOperatorVO> batchTaskOperatorList = Lists.newArrayList();
        for (Operator operator : basicOperatorList) {
            BatchTaskOperatorVO batchTaskOperator = new BatchTaskOperatorVO();

            // 1.基础属性
            // 角色 + 姓名 + 联系电话 + 归属部门 + 任务说明 + 操作账号 + 操作批次
            BeanUtils.copyProperties(operator, batchTaskOperator);
            batchTaskOperator.setOperatePerson(operator.getOperatorName());

            // 2.特殊处理属性
            // (1) 操作账号
            if(operator.getOperatorAccount()!= null ){
                String operatorAccount = TextValuePairHelper.getValueList(operator.getOperatorAccount()).toString();
                operatorAccount = operatorAccount.replace(LEFT_CORE_BRACKET, "");
                operatorAccount = operatorAccount.replace(RIGHT_CORE_BRACKET, "");
                batchTaskOperator.setOperatorAccount(operatorAccount);
            }

            // (2) 是否远程
            RemoteEnum remoteFlag = BoolEnum.Y.equals(operator.getRemoteFlag()) ? RemoteEnum.REMOTE : RemoteEnum.LOCAL;
            batchTaskOperator.setRemoteFlag(remoteFlag);

            batchTaskOperatorList.add(batchTaskOperator);
            if (operator.getOperatorAttribute() == null) {
                continue;
            }
            batchTaskOperator.setOperatorAttribute(
                    BatchOperatorAttrEnum.fromValue(operator.getOperatorAttribute().getValue()));
        }

        return batchTaskOperatorList;
    }

    public static <T extends BaseSubEntity> List<T> getBatchOperators(String batchId, Class<? extends BaseSubEntity> clazz) {
        if (clazz != BatchTaskOperator.class && clazz != SubcontractorBatchOperator.class) {
            return new ArrayList<>();
        }

        return QueryDataHelper.query(clazz, Lists.newArrayList(), batchId);
    }

    public static <T extends BaseSubEntity> List<T> getBatchObject(List<String> batchIds) {
        return QueryDataHelper.query(OperationObject.class, Lists.newArrayList(), batchIds, Lists.newArrayList());
    }

    /**
     * 根据批次id获取当前批次操作人员列表内的所有人员
     */
    public static List<String> getBatchOperatorPersonsList(String batchId, Class<? extends BaseSubEntity> clazz) {
        List<String> resultList = new ArrayList<>();
        if (clazz != BatchTaskOperator.class && clazz != SubcontractorBatchOperator.class) {
            return resultList;
        }

        List<BatchTaskOperator> batchTaskOperatorList = CollectionUtilsEx.copyListProperties(
                QueryDataHelper.query(clazz, Lists.newArrayList(OPERATE_PERSON), batchId), BatchTaskOperator.class);

        if (CollectionUtils.isEmpty(batchTaskOperatorList)) {
            return resultList;
        }

        return batchTaskOperatorList.stream().filter(item -> item.getOperatePerson() != null).map(item -> {
            SingleEmployee operatePerson = item.getOperatePerson();
            return operatePerson.getEmpUIID();
        }).collect(Collectors.toList());
    }


    /**
     * 获取变更单操作人员集合
     */
    public static List<String> getOperatorPersonsList(String changeOrderId, Class<? extends BaseSubEntity> clazz) {
        List<String> resultList = new ArrayList<>();
        if (changeOrderId == null) {
            return resultList;
        }
        List<Operator> operatorList = CollectionUtilsEx.copyListProperties(
                QueryDataHelper.query(clazz, Lists.newArrayList(OPERATOR_NAME), changeOrderId), Operator.class);

        if (!CollectionUtils.isEmpty(operatorList)) {
            resultList = operatorList.stream().filter(item -> item.getOperatorName() != null)
                    .map(item -> {
                        SingleEmployee operatorName = item.getOperatorName();
                        return operatorName.getEmpUIID();
                    }).collect(Collectors.toList());
        }
        return resultList;
    }

    public static void initCreate(List<String> changeOrderIds, String mainChangeOrderId) {
        List<Operator> operators = new ArrayList<>();
        List<OperatorRoleEnum> roleEnumList = Lists.newArrayList(OPERATING_SUPERVISOR, OPERATOR, CROSS_CHECKER, WATCHMAN, TESTER);
        List<Operator> orgOperators = QueryDataHelper.query(Operator.class, new ArrayList<>(), mainChangeOrderId);
        List<TextValuePair> batchOne = TextValuePairHelper.buildList(CommonConstants.STR_ONE, CommonConstants.STR_ONE, CommonConstants.STR_ONE);
        changeOrderIds.forEach(id -> {
            if (CollectionUtils.isEmpty(orgOperators)) {
                roleEnumList.forEach(role -> {
                    Operator operator = new Operator();
                    operator.setPid(id);
                    operator.setOperatorRole(role);
                    operators.add(operator);
                });
            } else {
                orgOperators.forEach(item -> {
                    Operator operator = new Operator();
                    // 同一个对象的复制
                    BeanUtils.copyProperties(item, operator);
                    operator.setId(null);
                    operator.clearEntityValue();
                    operator.setPid(id);
                    operator.setOperatorBatchNo(batchOne);
                    operators.add(operator);
                });
            }
        });
        SaveDataHelper.batchCreate(operators);
    }
}
