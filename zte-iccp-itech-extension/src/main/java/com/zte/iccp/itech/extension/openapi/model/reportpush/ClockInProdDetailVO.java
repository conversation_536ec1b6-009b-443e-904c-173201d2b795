package com.zte.iccp.itech.extension.openapi.model.reportpush;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/24 下午4:00
 */
@Setter
@Getter
public class ClockInProdDetailVO {

    /**
     * 单号
     */
    private String coNo;

    /**
     * 操作主题
     */
    private String operationSubject;

    /**
     * 客户网络名称
     */
    private String customerNetworkName;

    /**
     * 产品经营团队
     */
    private String prodTeam;

    /**
     * 产品经营团队id
     */
    private String prodTeamId;

    /**
     * 产品线
     */
    private String prodLine;

    /**
     * 产品线id
     */
    private String prodLineId;

    /**
     * 风险等级
     */
    private String fxdj;

    /**
     * 操作类型
     */
    private String opTypeCn;

    /**
     * 打卡人
     */
    private String opCnName;

    /**
     * 打卡人工号
     */
    private String opEmpNo;

    /**
     * 打卡人姓名工号
     */
    private String opEmpCombine;

    /**
     * 打卡状态
     */
    private String checkStatusZh;

    /**
     * 打卡详情
     */
    private String checkDetailZh;

    /**
     * 产品路径
     */
    private String prodClassPath;

    /**
     * 产品id路径
     */
    private String prodClassIdPath;

    /**
     * 组织路径
     */
    private String organizationPath;

    /**
     * 组织id路径
     */
    private String organizationIdPath;


}
