package com.zte.iccp.itech.extension.spi.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.openapi.model.clockin.InetClockInQueryDTO;
import com.zte.iccp.itech.extension.openapi.model.clockin.InetClockInResultDTO;
import com.zte.iccp.itech.extension.openapi.model.networksecurity.dto.*;
import com.zte.iccp.itech.extension.openapi.model.reportpush.*;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.idatashare.client.clientfactory.DataServiceClient;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.idatashare.client.config.ClientProperties;
import com.zte.idatashare.client.dataservice.QueryResult;
import com.zte.idatashare.client.dataservice.RequestParam;
import com.zte.idatashare.client.enumeration.ApiInvokeTotalEnum;
import com.zte.idatashare.client.enumeration.DbEnvTypeEnum;
import com.zte.idatashare.client.enumeration.VersionTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.I_VERSION_SYSTEM_NAME;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;


/**
 * 数字中台_外部接口
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/4
 */
@Slf4j
public class IcosClient {

    /**
     * 网络变更单ids
     */
    private static final String ICOS_QUERY_CHANGE_ORDERIDS = "changeOrderIds";

    /**
     * 客户标识
     */
    private static final String ICOS_QUERY_ACCN_TYPES = "accnTypes";

    /**
     * 网络变更单状态
     */
    private static final String ICOS_QUERY_ASSIGNMENT_STATUS = "assignmentStatus";

    /**
     * 任务类型：1内部网络变更单、6分包商网络变更单
     */
    private static final String ICOS_QUERY_ASSIGNMENT_TYPE = "assignmentType";

    /**
     * 网络变更单编码
     */
    private static final String ICOS_QUERY_CONOS = "coNos";

    /**
     * 创建时间（开始）
     */
    private static final String ICOS_QUERY_CREATE_TIME_START = "createTimeStart";

    /**
     * 创建时间（结束）
     */
    private static final String ICOS_QUERY_CREATE_TIME_END = "createTimeEnd";

    /**
     * 更新时间（开始）
     */
    private static final String ICOS_QUERY_UPDATE_TIME_START = "updateTimeStart";

    /**
     * 更新时间（结束）
     */
    private static final String ICOS_QUERY_UPDATE_TIME_END = "updateTimeEnd";

    /**
     * 操作类型
     */
    private static final String ICOS_QUERY_OPERATION_TYPES = "operationTypes";

    /**
     * 打卡任务ids
     */
    private static final String ICOS_QUERY_CLOCKIN_IDS = "clockInIds";

    /**
     * 打卡任务-变更单编码
     */
    private static final String ICOS_QUERY_CONO = "coNo";

    /**
     * 排序条件 - 最后更新时间
     */
    private static final String ICOS_ORDERBY_LAST_MODIFIED_TIME = "lastModifiedTime,DESC";

    /**
     * 排序条件 - 最后更新时间
     */
    private static final String ICOS_ORDERBY_BATCHID = "batchId,DESC";

    /**
     * 排序条件 - 变更单id
     */
    private static final String ICOS_ORDERBY_CHANGE_ORDERID_1 = "changeOrderId,DESC";

    /**
     * 排序条件 - 变更单id
     */
    private static final String ICOS_ORDERBY_CHANGE_ORDERID = "change_order_id,DESC";

    /**
     * 排序条件 - 打卡任务id
     */
    private static final String ICOS_ORDERBY_CLOCK_IN_TASK = "clock_in_id,DESC";

    /**
     * 排序条件 - 打卡任务id
     */
    private static final String ICOS_CLOCKIN_ID = "clockInId,DESC";

    /**
     * 排序条件 - 打卡记录id
     */
    private static final String ICOS_ORDERBY_CLOCKIN_RECORDID = "clockInRecordId,DESC";

    /**
     * 外部接口的最大查询每页条数
     */
    private static final int MAX_EXTERNAL_PAGE_SIZE = 2000;

    /**
     * 实际查询的每页最大条数
     */
    private static final int MAX_INTERNAL_PAGE_SIZE = 1000;

    /**
     * Inone应用code
     */
    private static final String INONE_CODE = "inone.code";

    /**
     * Inone应用名
     */
    private static final String INONE_APPNAME = "inone.appName";

    /**
     * Inone厂商名
     */
    private static final String INONE_SUPPLIER_NAME = "inone.supplierName";

    /**
     * 中台调用环境 - 生产：ONLINE  测试环境：TEST
     */
    private static final String ICOS_VERSION_ENV = "icos.versionEnv";

    /**
     * 中台超时时间，默认50000，单位：毫秒
     */
    public static final String ICOS_CONNECT_TIME_OUT = "icos.connectTimeOut";

    /**
     * 中台host
     */
    private static final String ICOS_SERVICE_HOST = "icos.serviceHost";

    /**
     * 三营查询网络变更单ids的接口apiId
     */
    private static final String ICOS_CHANGEORDER_QUERYID_APIID = "icos.changeOrder.queryId.apiId";

    /**
     * 三营根据网络变更单id检索数据的apiId
     */
    private static final String ICOS_CHANGEORDER_QUERYALL_APIID = "icos.changeOrder.queryAll.apiId";

    /**
     * 三营查询Inet打卡任务id的接口apiId
     */
    private static final String ICOS_CLOCKINTASK_QUERYID_APIID = "icos.clockInTask.queryId.apiId";

    /**
     * 三营根据网络变更单id检索数据的apiId
     */
    private static final String ICOS_CLOCKINTASK_QUERYALL_APIID = "icos.clockInTask.queryAll.apiId";

    /**
     * 网络变更单报表国际代表处维度，apiId
     */
    private static final String ICOS_NETCHANGE_REPORT_INTERNAL_ORG_APIID = "icos.netchange.report.internal.org.apiId";

    private static final String REPORT_DEMOSTIC_CLOCK_DETAIL_APIID = "icos.netchange.report.domestic.clock.prod.detail.apiId";

    private static final String DEMOSTIC_CLOCK_TOTAL_APIID = "icos.netchange.report.domestic.clock.prod.total.apiId";

    private static final String DEMOSTIC_CLOCK_ORG_TOTAL_APIID = "icos.netchange.report.domestic.clock.org.total.apiId";

    private static final String INTERNAL_YESTERDAY_SUMMARY_APIID =  "icos.netchange.report.internal.yesterday.summary.apiId";

    private static final String INTERNAL_YESTERDAY_FAIK_DETAIL_APIID =  "icos.netchange.report.internal.yesterday.fail.detail.apiId";

    private static final String GLOBAL_SUPPORT_DETAIL_APIID =  "icos.netchange.report.global.support.detail.apiId";


    /** 对接数方只有一套环境，api接口直接写死 */
    /** iTechCloud网络变更单信息分页查询（中文） apiId */
    private static final String DM_CHANG_ORDER_ZH_API_ID = "549759279198580736";

    /** iTechCloud网络变更单信息分页查询（英文） apiId */
    private static final String DM_CHANG_ORDER_EN_API_ID = "550916118658457600";

    /** iTechCloud批次操作人员信息查询 apiId */
    private static final String DM_NETWORK_BATCH_OPERATOR_API_ID = "549768084327276544";

    /**iTechCloud批次操作阶段信息查询 apiId */
    private static final String DM_OPERATION_STAGE_CLOCK_API_ID = "549768715381284864";

    /** 根据条件查询网络变更单ids apiId */
    private static final String DM_CHANG_ORDER_ID_QUERY_API_ID = "549757105153548288";

    /**
     * 构建基础查询项，一些配置方面的参数
     *
     * @return Map<String, Object>
     */
    private static RequestParam buildBaseQueryParam(Long apiId) {
        RequestParam.Builder builder = new RequestParam.Builder();
        return builder
                .apiId(apiId)
                // Inone应用code
                .appCode(ConfigHelper.get(INONE_CODE))
                // Inone应用名
                .appEnName(ConfigHelper.get(INONE_APPNAME))
                // Inone厂商名
                .vendorName(ConfigHelper.get(INONE_SUPPLIER_NAME))
                // 版本，环境类型
                .versionAndDbType(VersionTypeEnum.PUBLISHED,
                        ConfigHelper.get(ICOS_VERSION_ENV).equals(DbEnvTypeEnum.ONLINE.name()) ? DbEnvTypeEnum.ONLINE : DbEnvTypeEnum.TEST)
                // 员工工号 (选填，用于日志审计)  -- 当前传递固定的系统名字
                .empNo(I_VERSION_SYSTEM_NAME)
                .build();
    }

    /**
     * 构建网络变更单条件参数
     *
     * @param aggregationQueryDTO aggregationQueryDTO
     * @return Map<String, String>
     */
    private static Map<String, String> buildChangeOrderConditionParam(ChangeOrderAggregationQueryDTO aggregationQueryDTO) {
        return MapUtils.newHashMap(
                // 网络变更单ids
                ICOS_QUERY_CHANGE_ORDERIDS, transferStr(aggregationQueryDTO.getChangeOrderIds()),
                // 客户标识
                ICOS_QUERY_ACCN_TYPES, transferStr(aggregationQueryDTO.getAccnTypes()),
                // 任务状态
                ICOS_QUERY_ASSIGNMENT_STATUS, aggregationQueryDTO.getAssignmentStatus(),
                // 任务类型
                ICOS_QUERY_ASSIGNMENT_TYPE, aggregationQueryDTO.getAssignmentType(),
                // 网络变更单编号
                ICOS_QUERY_CONOS, transferStr(aggregationQueryDTO.getCoNos()),
                // 创建时间
                ICOS_QUERY_CREATE_TIME_START, DateUtils.dateToString(aggregationQueryDTO.getCreateTimeStart(), CommonConstants.DATE_FORM),
                ICOS_QUERY_CREATE_TIME_END, DateUtils.dateToString(aggregationQueryDTO.getCreateTimeEnd(), CommonConstants.DATE_FORM),
                // 更新时间
                ICOS_QUERY_UPDATE_TIME_START, DateUtils.dateToString(aggregationQueryDTO.getUpdateTimeStart(), CommonConstants.DATE_FORM),
                ICOS_QUERY_UPDATE_TIME_END, DateUtils.dateToString(aggregationQueryDTO.getUpdateTimeEnd(), CommonConstants.DATE_FORM),
                // 操作类型
                ICOS_QUERY_OPERATION_TYPES, transferStr(aggregationQueryDTO.getOperationTypes())
        );
    }

    /**
     * 根据变更单检索匹配的变更单、批次等信息
     *
     * @param changeOrderIds 变更单id
     * @param langId 语言（根据语言检查中文库或英文库）
     * @return List<ChangeOrderAggregationResultDTO>
     */
    public static List<ChangeOrderAggregationResultDTO> queryChangeOrder(List<String> changeOrderIds, String langId) {
        return queryAggregationInfo(
                Lists.newArrayList("changeOrderId,DESC", "batchSummaryId,DESC", "operationObjectId,DESC"),
                ZH_CN.equals(langId) ? DM_CHANG_ORDER_ZH_API_ID : DM_CHANG_ORDER_EN_API_ID,
                MapUtils.newHashMap("changeOrderIds", transferStr(changeOrderIds)),
                new TypeReference<List<ChangeOrderAggregationResultDTO>>() {
                });
    }

    /**
     * 根据批次id检索批次操作人员信息
     *
     * @param batchIds 批次ids
     * @return List<BatchOperatorDTO>
     */
    public static List<BatchOperatorDTO> queryBatchOperatorInfo(List<String> batchIds) {

        return queryAggregationInfo(
                Lists.newArrayList("operatorId,ASC"),
                DM_NETWORK_BATCH_OPERATOR_API_ID,
                MapUtils.newHashMap("batchIds", transferStr(batchIds)),
                new TypeReference<List<BatchOperatorDTO>>() {});
    }


    /**
     * 根据批次id检索批次操作阶段人员信息
     *
     * @param batchIds 批次ids
     * @return List<BatchOperationStageClockDTO>
     */
    public static List<BatchOperationStageClockDTO> queryBatchOperationStageClockInfo(List<String> batchIds) {

        return queryAggregationInfo(
                Lists.newArrayList("operationClockId,ASC"),
                DM_OPERATION_STAGE_CLOCK_API_ID,
                MapUtils.newHashMap("batchIds", transferStr(batchIds)),
                new TypeReference<List<BatchOperationStageClockDTO>>() {});
    }

    /**
     * 通用检索外部聚合方法
     *
     * @param orderByList 排序
     * @param apiId 外部apiId
     * @param paramMap 其他条件参数
     * @param typeRef 外部返回类型
     * @return List<T>
     * @param <T> 外部返回类型
     */
    public static <T> List<T> queryAggregationInfo(List<String> orderByList,
                                                   String apiId,
                                                   Map<String, String> paramMap,
                                                   TypeReference<List<T>> typeRef) {
        // 1. 构建基础参数
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(apiId));
        // 2. 构建其他条件参数
        requestParam.setParamMap(paramMap);
        // 3. 构建排序参数
        requestParam.setOrderByList(orderByList);

        // 4. 调用方法
        List<Map<String, Object>> results = invoke(requestParam);
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }

        // 5. 解析结果（使用明确的TypeReference）
        return JsonUtils.parseObject(results, typeRef);
    }

    /**
     * 根据打卡任务ids检索打卡信息
     *
     * @param clockInIds 变更单id
     * @return List<ChangeOrderAggregation>
     */
    public static List<InetClockInResultDTO> queryClockInTask(List<String> clockInIds) {
        // 1.构建基础参数
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(ICOS_CLOCKINTASK_QUERYALL_APIID)));
        // 2.构建其他条件参数
        requestParam.setParamMap(MapUtils.newHashMap(ICOS_QUERY_CLOCKIN_IDS, transferStr(clockInIds)));
        // 3.构建排序参数
        requestParam.setOrderByList(Lists.newArrayList(ICOS_ORDERBY_LAST_MODIFIED_TIME,
                ICOS_CLOCKIN_ID, ICOS_ORDERBY_CHANGE_ORDERID_1, ICOS_ORDERBY_CLOCKIN_RECORDID));

        // 4.调用方法
        List<Map<String, Object>> clockInTasks = invoke(requestParam);
        if (CollectionUtils.isEmpty(clockInTasks)) {
            return Collections.emptyList();
        }

        return JsonUtils.parseObject(clockInTasks, new TypeReference<List<InetClockInResultDTO>>() {
        });
    }

    /**
     * 根据分页条件查询中台数据（第二次查询数据时使用）
     *
     * @param requestParam requestParam
     * @return List<Map<String, Object>>
     */
    private static List<Map<String, Object>> invoke(RequestParam requestParam) {
        // 1.第一次查询当前页为1，查询总量为1000，需要查询count总数
        requestParam.setHasTotal(ApiInvokeTotalEnum.YES.getKey());
        QueryResult firstQueryResult = fetchFromApi(requestParam, 1, MAX_INTERNAL_PAGE_SIZE);
        if (firstQueryResult == null) {
            return Collections.emptyList();
        }

        // 2.查询总数低于1000直接返回
        if (firstQueryResult.getTotal() <= MAX_INTERNAL_PAGE_SIZE) {
            return firstQueryResult.getRows();
        }

        // 3.查询总数大于1000
        // 3.1 不在查询总量
        requestParam.setHasTotal(ApiInvokeTotalEnum.NO.getKey());
        // 总记录数
        Long totalRecords = firstQueryResult.getTotal();
        List<Map<String,Object>> resultList = new ArrayList<>(totalRecords.intValue());
        resultList.addAll(firstQueryResult.getRows());
        // 计算需要查询的总页数
        int totalPages = (int) Math.ceil((double) totalRecords / MAX_INTERNAL_PAGE_SIZE);

        // 循环查询每一页的数据,从第二页开始（异步查询，数据汇总）
        List<Supplier<QueryResult>> suppliers = Lists.newArrayList();
        for (int page = 2; page <= totalPages; page++) {
            int finalPage = page;
            suppliers.add(() -> fetchFromApi(requestParam, finalPage, MAX_INTERNAL_PAGE_SIZE));
        }
        List<QueryResult> partTotal = AsyncExecuteUtils.asyncQuery(suppliers);

        for (QueryResult queryResult : partTotal) {
            resultList.addAll(queryResult.getRows());
        }
        return resultList;
    }

    private static List<Map<String, Object>> retryInvoke(RequestParam requestParam) {
       return RetryUtils.get(() -> invoke(requestParam));
    }

    /**
     * 获取中台数据ids
     *
     * @param queryDTO queryDTO
     * @return 结果ids+命中总量
     */
    public static Pair<List<String>, Long> firstIdsQuery(BaseQueryDTO queryDTO) {
        long apiId = 0L;
        List<String> orderByList = new ArrayList<>();
        Map<String, String> otherConditionParam = null;
        // 网络变更单
        if (queryDTO instanceof ChangeOrderAggregationQueryDTO) {
            apiId = Long.parseLong(DM_CHANG_ORDER_ID_QUERY_API_ID);
            orderByList = Lists.newArrayList(ICOS_ORDERBY_CHANGE_ORDERID);
            otherConditionParam = buildChangeOrderConditionParam((ChangeOrderAggregationQueryDTO) queryDTO);

            //Inet打卡
        } else if (queryDTO instanceof InetClockInQueryDTO) {
            apiId = Long.parseLong(ConfigHelper.get(ICOS_CLOCKINTASK_QUERYID_APIID));
            orderByList = Lists.newArrayList(ICOS_ORDERBY_CLOCK_IN_TASK);
            otherConditionParam = buildClockInTaskConditionParam((InetClockInQueryDTO) queryDTO);
        }

        // 1.构建基础参数（每个API接口的apiId都不一样，单独传入）
        RequestParam requestParam = buildBaseQueryParam(apiId);
        // 2.构建排序条件
        requestParam.setOrderByList(orderByList);
        // 3.构建其他条件参数
        requestParam.setParamMap(otherConditionParam);
        // 4.是否返回总数
        requestParam.setHasTotal(ApiInvokeTotalEnum.YES.getKey());
        // 5.检索数据
        QueryResult queryResult = invoke(requestParam, queryDTO.getPageNum(), queryDTO.getPageSize());
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getRows())) {
            return null;
        }
        return Pair.of(queryResult.getRows().stream()
                .flatMap(map -> map.values().stream())
                .map(Object::toString)
                .collect(Collectors.toList()), queryResult.getTotal());
    }

    /**
     * 构建Inet打卡任务条件参数
     *
     * @param inetClockInQueryDTO inetClockInQueryDTO
     * @return Map<String, String>
     */
    private static Map<String, String> buildClockInTaskConditionParam(InetClockInQueryDTO inetClockInQueryDTO) {
        return MapUtils.newHashMap(
                // 打卡任务ids
                ICOS_QUERY_CLOCKIN_IDS, transferStr(inetClockInQueryDTO.getClockInIds()),
                // 客户标识
                ICOS_QUERY_CONO, inetClockInQueryDTO.getCoNo(),
                // 创建时间
                ICOS_QUERY_CREATE_TIME_START, DateUtils.dateToString(inetClockInQueryDTO.getCreateTimeStart(), CommonConstants.DATE_FORM),
                ICOS_QUERY_CREATE_TIME_END, DateUtils.dateToString(inetClockInQueryDTO.getCreateTimeEnd(), CommonConstants.DATE_FORM),
                // 更新时间
                ICOS_QUERY_UPDATE_TIME_START, DateUtils.dateToString(inetClockInQueryDTO.getUpdateTimeStart(), CommonConstants.DATE_FORM),
                ICOS_QUERY_UPDATE_TIME_END, DateUtils.dateToString(inetClockInQueryDTO.getUpdateTimeEnd(), CommonConstants.DATE_FORM)
        );
    }

    /**
     * 根据分页条件查询中台数据（第一次查询ids时使用）
     *
     * @param requestParam 请求参数
     * @param externalPage 当前页
     * @param externalSize 每页展示条数
     * @return QueryResult
     */
    private static QueryResult invoke(RequestParam requestParam,int externalPage, int externalSize) {
        QueryResult result = new QueryResult();
        // 1.每页展示条数小于1000的逻辑
        if (externalSize <= MAX_INTERNAL_PAGE_SIZE) {
            return fetchFromApi(requestParam, externalPage, externalSize);
        }

        List<Map<String, Object>> rows = new ArrayList<>();
        Long total = 0L;
        // 2.中台分页接口单次限制最大为1000，如果每页查询条件超过1000，特殊处理当前页和查询条件，进行多次查询
        // 如1，2000  实际查询为1，1000   2，1000
        // 如2，2000  实际查询为3，1000   4，1000
        List<PageInfo> pageInfos = getPages(externalPage, externalSize);
        for (int i = 0; i < pageInfos.size(); i++) {
            PageInfo pageInfo = pageInfos.get(i);
            QueryResult queryResult = fetchFromApi(requestParam, pageInfo.getPage(), pageInfo.getSize());
            // 3.第一次查询时检索出总数，如命中数小于1000，直接返回
            if (i == 0) {
                if (queryResult.getTotal() <= MAX_INTERNAL_PAGE_SIZE) {
                    return queryResult;
                }
                total = queryResult.getTotal();
            }
            requestParam.setHasTotal(ApiInvokeTotalEnum.NO.getKey());
            rows.addAll(queryResult.getRows());
        }
        result.setRows(rows);
        result.setTotal(total);
        return result;
    }

    /**
     * 根据外部接口的最大查询每页条数和实际查询的每页最大条数，分页查询数据。
     *
     * @param externalPage  外部接口请求的页码（从1开始）
     * @param externalSize  外部接口请求的每页条数
     * @return 一个包含实际查询页码和每页条数的列表
     */
    private static List<PageInfo> getPages(int externalPage, int externalSize) {
        List<PageInfo> pages = new ArrayList<>();
        int internalSize = Math.min(externalSize, MAX_INTERNAL_PAGE_SIZE);
        // 计算实际查询的起始页码
        int startInternalPage = (externalPage - 1) * externalSize / internalSize + 1;
        // 计算需要查询的次数
        int totalInternalPages = (int) Math.ceil((double) externalSize / internalSize);
        for (int i = 0; i < totalInternalPages; i++) {
            int currentInternalPage = startInternalPage + i;
            pages.add(new PageInfo(currentInternalPage, internalSize));
        }

        return pages;
    }

    /**
     * 查询中台底层接口
     *
     * @param requestParam 请求参数
     * @param page 当前页
     * @param pageSize 每页展示条数
     * @return 查询结果
     */
    private static QueryResult fetchFromApi(RequestParam requestParam, int page, int pageSize){
        // 当前页
        requestParam.setPageNum(page);
        // 每次展示条数
        requestParam.setPageSize(pageSize);
        try {
            ClientProperties clientProperties = new ClientProperties();
            clientProperties.setDataServiceHost(ConfigHelper.get(ICOS_SERVICE_HOST));
            // 连接超时配置（默认50000，单位毫秒）
            clientProperties.setConnectTimeOut(Integer.valueOf(ConfigHelper.get(ICOS_CONNECT_TIME_OUT)));

            DataServiceClient dataServiceClient = DataServiceClientV1.getInstance(clientProperties);

            return dataServiceClient.invoke(requestParam);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(MessageConsts.INTERFACE_INVOK_FAILED, e);
        }
    }

    public static List<NetChangeReportOrgVO> getSummaryReportVos(String apiId) {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(apiId)));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<NetChangeReportOrgVO>>() {});

    }

    public static List<NetChangeReportInterOrgVO> getNetChangeReportInterOrg() {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(ICOS_NETCHANGE_REPORT_INTERNAL_ORG_APIID)));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<NetChangeReportInterOrgVO>>() {});

    }

    public static List<ReportInterOrgDetailVO> getReportInterOrgDetailVos(String apiId) {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(apiId)));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<ReportInterOrgDetailVO>>() {});

    }

    public static List<ClockInProdTotalVO> getReportClockInTotalVos() {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(DEMOSTIC_CLOCK_TOTAL_APIID)));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<ClockInProdTotalVO>>() {});

    }

    public static List<ClockInOrgTotalVO> getClockInOrgTotalVos() {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(DEMOSTIC_CLOCK_ORG_TOTAL_APIID)));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<ClockInOrgTotalVO>>() {});

    }

    public static List<ClockInProdDetailVO> getReportClockInDetailVos() {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(REPORT_DEMOSTIC_CLOCK_DETAIL_APIID)));
        requestParam.setOrderByList(Lists.newArrayList("coNo,DESC", "opEmpNo,DESC"));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<ClockInProdDetailVO>>() {});

    }

    public static List<InterYesterdaySummaryVO> getInterYesterdaySummaryVos() {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(INTERNAL_YESTERDAY_SUMMARY_APIID)));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<InterYesterdaySummaryVO>>() {});
    }

    public static List<InterYesterdayFailVO> getInterYesterdayFailDetailVos() {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(INTERNAL_YESTERDAY_FAIK_DETAIL_APIID)));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<InterYesterdayFailVO>>() {});
    }

    public static List<InterDetailVO> getInterDetailVos(String apiId) {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(apiId));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<InterDetailVO>>() {});
    }

    public static List<GlobalDetailVO> getGlobalDetailVos(String apiId) {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(apiId));
        requestParam.setOrderByList(Lists.newArrayList("batchCode,DESC"));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<GlobalDetailVO>>() {});
    }

    public static List<GlobalSupportDetailVO> getGlobalSupportDetailVos() {
        RequestParam requestParam = buildBaseQueryParam(Long.parseLong(ConfigHelper.get(GLOBAL_SUPPORT_DETAIL_APIID)));
        requestParam.setOrderByList(Lists.newArrayList("batchCode,DESC", "supportStaffNo,DESC"));
        List<Map<String, Object>> results = retryInvoke(requestParam);
        return JsonUtils.parseObject(results, new TypeReference<List<GlobalSupportDetailVO>>() {});
    }

    private static String transferStr(List<String> param) {
        if (CollectionUtils.isEmpty(param)) {
            return null;
        }
        return String.join(",", param);
    }

    @Getter
    @Setter
    public static class PageInfo {
        private final int page;
        private final int size;

        public PageInfo(int page, int size) {
            this.page = page;
            this.size = size;
        }
    }
}
