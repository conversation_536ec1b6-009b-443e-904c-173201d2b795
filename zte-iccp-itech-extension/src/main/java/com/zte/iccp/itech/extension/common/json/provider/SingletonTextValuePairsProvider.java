package com.zte.iccp.itech.extension.common.json.provider;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/11
 */
public interface SingletonTextValuePairsProvider extends PropValueProvider<List<TextValuePair>> {

    default String getMsgKey() {
        return null;
    }

    String getValue();

    default String getZhCn() {
        return MsgUtils.getLangMessage(ZH_CN, getMsgKey());
    }

    default String getEnUs() {
        return MsgUtils.getLangMessage(EN_US, getMsgKey());
    }

    @Override
    default List<TextValuePair> getPropValue() {
        return TextValuePairHelper.buildList(getValue(), getZhCn(), getEnUs());
    }
}
