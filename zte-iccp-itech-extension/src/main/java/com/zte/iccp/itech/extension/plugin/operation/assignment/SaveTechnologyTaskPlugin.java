package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.*;
import com.zte.iccp.itech.extension.ability.clockin.reviews.ClockInReviewsAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultProcessEnum;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.TaskCategoryEnum;
import com.zte.iccp.itech.extension.domain.model.ManageSubTask;
import com.zte.iccp.itech.extension.domain.model.ManageSubTaskFlow;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteTransactionalEvent;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.CLOCK_REVIEWS_TASK_CATEGORY_ERROR;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PARAM_CLOCK_IN_REVIEWS_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts.ACCEPTOR_PERSON;
import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum.FAULT_MANAGE_FLOW;
import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids.FIELD_FAULT_REVIEW_CID;

@Slf4j
public class SaveTechnologyTaskPlugin extends BaseOperationPlugin {

    /**
     * 故障管理任务参数ID
     */
    private static final String FAULT_ASSIGNMENT_ID = "fault_assignment_id";

    /** 系统节点标识 - 确认故障复盘 */
    private static final String FAULT_REVIEW_CONFIRMING_NODE_KEY = "Activity_1kwh29g";


    /**
     * 插件 - 提交
     * 新建 / 待启动提交 - 新增 / 更新主任务 + 新增子任务
     * 执行中提交 - 新增 / 更新主任务 + 新增补充子任务
     */
    public void execute(IDataModel dataModel, IFormView formView) {
        // 1.获取父页面传递参数
        Map<String, Object> customerParmeterMap = formView.getFormShowParameter().getCustomParameters();
        String faultAssignmentId = (String) customerParmeterMap.get(FAULT_ASSIGNMENT_ID);
        String currentProgress = (String) customerParmeterMap.get(CURRENT_PROGRESS);
        String clockReviewsId = (String) customerParmeterMap.get(PARAM_CLOCK_IN_REVIEWS_ID);
        // 打卡复盘任务分类必须是整改或者横推
        if (StringUtils.hasText(clockReviewsId)) {
            if (!isRectifyOrHorizontalPushing(dataModel)) {
                formView.showMessage(CLOCK_REVIEWS_TASK_CATEGORY_ERROR, MsgType.ERROR);
                return;
            }
        }

        // 2.生成主任务
        String billId = (String) dataModel.getRootDataEntity().getPkValue();
        TechnologyManagementAssignment newAssignment
                = createOrUpdateMainAssignment(dataModel, billId, currentProgress, false);

        // 3.新增子任务
        createSubAssignment(dataModel, newAssignment);

        // 4.任务中心跳转 - 刷新任务中心列表
        if (!StringUtils.hasText(faultAssignmentId) && !StringUtils.hasText(clockReviewsId)) {
            refreshTable(formView, TABLE_ASSIGNMENT_CID);
            return;
        }

        // 5.故障管理任务跳转 - 任务关系处理
        if (StringUtils.hasText(faultAssignmentId)) {
            if (FaultProcessEnum.FAULT_REVIEW_CONFIRMING.name().equals(currentProgress)) {
                faultReviewAssignmentCreated(formView, faultAssignmentId, newAssignment);
            } else {
                faultRectifyAssignmentCreated(formView, faultAssignmentId, newAssignment);
            }
        }

        // 6.打卡复盘任务跳转 - 任务关系处理
        if (StringUtils.hasText(clockReviewsId)) {
            ClockInReviewsAbility.createClockReviewsAssociateRelation(formView, clockReviewsId, newAssignment.getId());
        }
    }

    /**
     * 整改任务 / 横推任务校验
     */
    private boolean isRectifyOrHorizontalPushing(IDataModel dataModel) {
        Map<String, String> taskCategoryMap = JsonUtils.parseObject(
                dataModel.getValue(ManageTaskFieldConsts.TASK_CATEGORY), Map.class);
        String taskCategory = taskCategoryMap.get(LookupValueConstant.LOOKUP_CODE);
        return TaskCategoryEnum.FAULT_RECTIFY.getValue().equals(taskCategory)
                || TaskCategoryEnum.HORIZONTAL_PUSHING_TASK.getValue().equals(taskCategory);
    }

    /**
     * 插件 - 保存草稿
     * 仅新增 / 更新主任务
     */
    @Override
    public void afterExecuteTransactional(ExecuteTransactionalEvent executeTransactionalEvent) {
        IDataModel dataModel = getModel();
        IFormView formView = getView();

        // 1.获取父页面传递参数
        Map<String, Object> customerParmeterMap = getView().getFormShowParameter().getCustomParameters();
        String currentProgress = (String) customerParmeterMap.get(CURRENT_PROGRESS);
        String clockReviewsId = (String) customerParmeterMap.get(PARAM_CLOCK_IN_REVIEWS_ID);
        // 打卡复盘任务分类必须是整改或者横推
        if (StringUtils.hasText(clockReviewsId)) {
            if (!isRectifyOrHorizontalPushing(dataModel)) {
                formView.showMessage(CLOCK_REVIEWS_TASK_CATEGORY_ERROR, MsgType.ERROR);
                return;
            }
        }

        // 2.生成主任务
        String billId = getPkId();
        TechnologyManagementAssignment technologyAssignment
                = createOrUpdateMainAssignment(dataModel, billId, currentProgress, true);

        // 3.任务中心跳转 - 刷新任务中心列表
        if (!StringUtils.hasText(clockReviewsId)) {
            refreshTable(formView, TABLE_ASSIGNMENT_CID);
            return;
        }

        // 4.打卡复盘任务跳转 - 任务关系处理
        if (StringUtils.hasText(clockReviewsId)) {
            ClockInReviewsAbility.createClockReviewsAssociateRelation(formView, clockReviewsId, technologyAssignment.getId());
        }
    }

    /**
     * 创建 / 更新主任务
     */
    private TechnologyManagementAssignment createOrUpdateMainAssignment(
            IDataModel dataModel,
            String billId,
            String currentProgress,
            boolean draftFlag) {

        // 1.获取提单主键, 检索已生成主任务数据
        TechnologyManagementAssignment existAssignment = AssignmentAbility.querySpecificTypeAssignment(
                billId, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT, TechnologyManagementAssignment.class);

        // 2.获取并包装页面填报信息
        TechnologyManagementAssignment mainAssignment
                = getAndTransferFillingInformation(dataModel, billId, existAssignment, currentProgress, draftFlag);

        // 3.生成主任务
        String assignmentId;
        if (Objects.isNull(existAssignment)) {
            // (1) 任务不存在，新增网络任务数据
            assignmentId = AssignmentAbility.insert(mainAssignment);
            mainAssignment.setId(assignmentId);
            // 主任务创建邮件发送
            //知会人
            List<String> mailCCs = EmployeeHelper.getEpmUIID(dataModel.getValue(FIELD_INFORMED_PERSON));
            sendMail(billId, (String) dataModel.getValue(FIELD_TASK_NO),
                    (String) dataModel.getValue(FIELD_TASK_NAME), mailCCs, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT);
        } else {
            // (2) 任务存在，更新网络任务数据
            assignmentId = existAssignment.getId();
            mainAssignment.setId(assignmentId);
            AssignmentAbility.update(mainAssignment);
        }

        // 4.创建任务 - 人员关联关系
        // 创建人 + 责任人 + 验收人
        List<Employee> acceptors = ComponentUtils.getEmployeeComponentInfo(dataModel, ACCEPTOR_PERSON);
        List<String> acceptorIds = acceptors.stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toList());

        List<String> responsibleIds = EmployeeHelper.getEpmUIID(mainAssignment.getResponsibleEmployee());

        Set<String> relevantSet = new HashSet<>();
        relevantSet.add(ContextHelper.getEmpNo());
        relevantSet.addAll(acceptorIds);
        relevantSet.addAll(responsibleIds);

        AssignmentAbility.createAssignmentPersonRelevance(assignmentId, new ArrayList<>(relevantSet));

        return mainAssignment;
    }

    /**
     * 获取并转换页面填报信息
     */
    private TechnologyManagementAssignment getAndTransferFillingInformation(
            IDataModel dataModel,
            String billId,
            TechnologyManagementAssignment existAssignment,
            String currentProgress,
            boolean draftFlag) {

        TechnologyManagementAssignment assignment = new TechnologyManagementAssignment();

        // 1.技术管理任务分类
        assignment.setTaskCategory(calculateTaskCategory(dataModel, currentProgress));

        // 2.任务名称
        assignment.setAssignmentName(
                PropertyValueConvertUtil.getString(dataModel.getValue(FIELD_TASK_NAME)));

        // 3.任务编码
        assignment.setAssignmentCode(PropertyValueConvertUtil.getString(dataModel.getValue(FIELD_TASK_NO)));

        // 4.任务状态
        assignment.setAssignmentStatus(calculateAssignmentStatus(draftFlag, existAssignment));

        // 5.任务类型 + 单据类型
        assignment.setAssignmentType(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT.getPropValue());
        assignment.setBillType(BillTypeEnum.TECHNOLOGY_MANAGEMENT.getPropValue());

        // 6.责任单位
        // 技术管理任务会选择到第四层组织架构，组织ID，需查询 HR 系统获取全路径
        // 营销获取第二层组织id, 代表处获取第四层组织id
        Pair<List<TextValuePair>, List<TextValuePair>> organizationInfo = getOrganizationInfo(dataModel);
        assignment.setMarketing(organizationInfo.getLeft());
        assignment.setRepresentativeOffice(organizationInfo.getRight());

        // 7.客户标识ID
        assignment.setCustomerClassification(ComponentUtils.getChooseComponentInfo(dataModel, FIELD_CUSTOMER_IDENTIFICATION_CID));

        // 8.产品分类
        Pair<List<TextValuePair>, List<TextValuePair>> productInfo = getProductInfo(dataModel);
        assignment.setProductManagementTeam(productInfo.getLeft());
        assignment.setProductClassification(productInfo.getRight());

        // 9.责任人
        List<Employee> responsible = ComponentUtils.getEmployeeComponentInfo(dataModel, FIELD_RESPONSIBLE_PERSON);
        assignment.setResponsibleEmployee(responsible);

        // 10.公司
        List<String> wholeUserIds = Lists.newArrayList(ContextHelper.getEmpNo());
        List<String> responsibleIds = responsible.stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toList());
        wholeUserIds.addAll(responsibleIds);
        assignment.setCompany(AssignmentAbility.getAssignmentCompany(wholeUserIds));

        // 11.当前处理人
        assignment.setCurrentProcessorEmployee(draftFlag
                ? HrClient.queryEmployeeInfo(Lists.newArrayList(ContextHelper.getEmpNo()))
                : assignment.getResponsibleEmployee());

        // 12.要求完成时间
        assignment.setRequiredCompletionTime(ComponentUtils.getDateComponentInfo(dataModel, FIELD_REQUIRED_COMPLETION_DATE));

        // 13.业务实体对象ID
        assignment.setBillId(billId);
        assignment.setEntityId(billId);

        // 14.是否审批任务
        assignment.setApprovalTaskFlag(BoolEnum.N);

        return assignment;
    }

    /**
     * 子任务创建邮件发送
     *
     * @param assignmentList assignmentList
     */
    private void sendSubTaskCreateEmail(List<TechnologyManagementAssignment> assignmentList) {
        AsyncExecuteUtils.execute(() -> {
            for (TechnologyManagementAssignment assignment : assignmentList) {
                sendMail(assignment.getApproveSubTaskId(), assignment.getAssignmentCode(),
                        assignment.getAssignmentName(), null, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB);
            }
        });
    }


    /**
     * 技术管理任务创建邮件发送
     *
     * @param pkId 主键id
     * @param orderNo 单据编号
     * @param taskName 任务名称
     * @param mailCCs 抄送人
     * @param assignmentTypeEnum 任务类型
     */
    private void sendMail(String pkId,
                          String orderNo,
                          String taskName,
                          List<String> mailCCs,
                          AssignmentTypeEnum assignmentTypeEnum) {
        //【iTech Cloud 技术管理任务】{创建人}创建了任务：【{单据编号}/{任务名称}】，您被指派为任务的责任人，请及时处理！
        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(ContextHelper.getEmpNo());

        Map<String, Object> data = EmailAbility.buildMessageZhContent(
                MessageConsts.EmailNotice.MESSAGE_MANAGETASK_CREATE_PRESS_CONTENT, employeeFormatNames.getZhCN(), orderNo, taskName);
        data.putAll(EmailAbility.buildMessageEnContent(
                MessageConsts.EmailNotice.MESSAGE_MANAGETASK_CREATE_PRESS_CONTENT, employeeFormatNames.getEnUS(), orderNo, taskName));

        // 催办邮件
        EmailAbility.asyncSendMail(EmailAbility.EmailParam.builder()
                .pkId(pkId)
                .templateIdEnum(TemplateIdEnum.EMAIL_PRESS_1)
                .data(data)
                .assignmentTypeEnum(assignmentTypeEnum)
                .isApprovePage(true)
                .build());

        // 知会人知会邮件
        if (!CollectionUtils.isEmpty(mailCCs)) {
            //【iTech Cloud 技术管理任务】${创建人}创建了任务：【{单据编号}/{任务名称}】，您被指派为任务的知会人，请知悉！
            data = EmailAbility.buildMessageZhContent(
                    MessageConsts.EmailNotice.MESSAGE_MANAGETASK_CREATE_NOTICE_CONTENT, employeeFormatNames.getZhCN(), orderNo, taskName);
            data.putAll(EmailAbility.buildMessageEnContent(
                    MessageConsts.EmailNotice.MESSAGE_MANAGETASK_CREATE_NOTICE_CONTENT, employeeFormatNames.getEnUS(), orderNo, taskName));
            EmailAbility.asyncSendMail(EmailAbility.EmailParam.builder()
                    .pkId(pkId)
                    .templateIdEnum(TemplateIdEnum.EMAIL_INFOM_1)
                    .data(data)
                    .mailTos(mailCCs)
                    .assignmentTypeEnum(assignmentTypeEnum)
                    .isApprovePage(false)
                    .build());
        }
    }

    /**
     * 计算任务分类
     * @param dataModel
     * @param currentProgress
     * @return String
     */
    private String calculateTaskCategory(IDataModel dataModel, String currentProgress) {
        // 1.直接复制 - 故障复盘任务
        if (FaultProcessEnum.FAULT_REVIEW_CONFIRMING.name().equals(currentProgress)) {
            return TaskCategoryEnum.FAULT_REVIEW.getValue();
        }

        // 2.获取页面填报信息
        Map<String, String> taskCategoryMap = JsonUtils.parseObject(
                dataModel.getValue(ManageTaskFieldConsts.TASK_CATEGORY), Map.class);
        if (CollectionUtils.isEmpty(taskCategoryMap)) {
            return CommonConstants.EMPTY_STRING;
        }
        String taskCategory = taskCategoryMap.get(LookupValueConstant.LOOKUP_CODE);

        // 3.特殊校验 - 故障整改 / 横推任务
        if (FaultProcessEnum.FAULT_RECTIFICATION_PROMOTION_CONFIRMING.name().equals(currentProgress)) {
            return (!TaskCategoryEnum.FAULT_RECTIFY.getValue().equals(taskCategory) && !TaskCategoryEnum.HORIZONTAL_PUSHING_TASK.getValue().equals(taskCategory))
                    ? CommonConstants.EMPTY_STRING
                    : taskCategory;
        }

        // 4.普通技术管理任务
        return taskCategory;
    }

    /**
     * 计算任务状态
     * @param draftFlag
     * @param existAssignment
     * @return String
     */
    private String calculateAssignmentStatus(boolean draftFlag, TechnologyManagementAssignment existAssignment) {
        // 1.新建技术管理任务
        if (Objects.isNull(existAssignment)) {
            return draftFlag ? AssignmentStatusEnum.START.getValue() : AssignmentStatusEnum.EXECUTE.getValue();
        }

        // 2.修改技术管理任务 - 历史任务保存草稿
        if (AssignmentStatusEnum.START.getValue().equals(existAssignment.getAssignmentStatus())) {
            return draftFlag ? AssignmentStatusEnum.START.getValue() : AssignmentStatusEnum.EXECUTE.getValue();
        }

        // 3.修改技术管理任务 - 历史任务已提交
        return AssignmentStatusEnum.EXECUTE.getValue();
    }

    /**
     * 获取组织信息
     * left - 营销   right - 代表处
     * @return Pair<List<TextValuePair>, List<TextValuePair>>
     */
    private Pair<List<TextValuePair>, List<TextValuePair>> getOrganizationInfo(IDataModel dataModel) {
        // 1.获取组织信息
        List<TextValuePair> organizationInfo = ComponentUtils.getChooseComponentInfo(dataModel, FIELD_ORGANIZATION_CID);
        if (CollectionUtils.isEmpty(organizationInfo)) {
            return Pair.of(Lists.newArrayList(), Lists.newArrayList());
        }

        // 2.组织信息拆分 + 信息包装
        Set<String> marketingPathSet = new HashSet<>();
        Set<String> representativeOfficePathSet = new HashSet<>();

        List<TextValuePair> marketingList = Lists.newArrayList();
        List<TextValuePair> representativeOfficeList = Lists.newArrayList();
        for (TextValuePair organization : organizationInfo) {
            List<String> hrOrgIdList = Lists.newArrayList(organization.getValue().split(FORWARD_SLASH));
            if (hrOrgIdList.size() < INTEGER_FOUR) {
                continue;
            }

            String marketing = hrOrgIdList.get(INTEGER_ONE);
            String representativeOffice = hrOrgIdList.get(INTEGER_THREE);

            if (!marketingPathSet.contains(marketing)) {
                marketingList.addAll(TextValuePairHelper.buildList(marketing, EMPTY_STRING, EMPTY_STRING));
                marketingPathSet.add(marketing);
            }

            if (!representativeOfficePathSet.contains(representativeOffice)) {
                representativeOfficeList.addAll(TextValuePairHelper.buildList(representativeOffice, EMPTY_STRING, EMPTY_STRING));
                representativeOfficePathSet.add(representativeOffice);
            }
        }

        return Pair.of(marketingList, representativeOfficeList);
    }

    /**
     * 获取产品信息
     * left - 产品经营团队   right - 产品分类
     * @param dataModel
     * @return Pair<List<TextValuePair>, List<TextValuePair>>
     */
    private Pair<List<TextValuePair>, List<TextValuePair>> getProductInfo(IDataModel dataModel) {
        // 1.获取产品信息
        List<TextValuePair> productClassificationInfo = ComponentUtils.getChooseComponentInfo(dataModel, FIELD_PRODUCT_CID);
        if (CollectionUtils.isEmpty(productClassificationInfo)) {
            return Pair.of(Lists.newArrayList(), Lists.newArrayList());
        }

        // 2.拆分产品信息
        List<TextValuePair> productManagementTeamList = Lists.newArrayList();
        List<String> productManagementTeamId = Lists.newArrayList();
        for (TextValuePair productClassification : productClassificationInfo) {
            String[] productIdArray = productClassification.getValue().split(FORWARD_SLASH);
            String productManagementTeam = productIdArray[INTEGER_ZERO];
            if (!productManagementTeamId.contains(productManagementTeam)) {
                productManagementTeamId.add(productManagementTeam);
                productManagementTeamList.addAll(TextValuePairHelper.buildList(productManagementTeam, EMPTY_STRING, EMPTY_STRING));
            }
        }

        // 3.包装产品信息
        return Pair.of(productManagementTeamList, productClassificationInfo);
    }

    /**
     * 刷新列表
     * @param formView
     * @param tableId
     */
    private void refreshTable(IFormView formView, String tableId) {
        IFormView parentView = formView.getParentView();
        IClientViewProxy parentViewProxy = parentView.getClientViewProxy();
        parentViewProxy.refreshData(parentView.getPageSessionId(), tableId, parentView.getPageId());
        formView.getClientViewProxy().sendParentViewCmd(parentViewProxy);
    }

    /**
     * 创建子任务数据
     */
    private void createSubAssignment(IDataModel dataModel, TechnologyManagementAssignment assignment) {
        // 1.草稿状态暂时不生成子任务 - 子任务信息未固定，会有删减变更
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (AssignmentStatusEnum.START.equals(assignmentStatus)) {
            return;
        }

        // 2.读取子表单数据信息
        IDataEntityCollection<DynamicDataEntity> dataEntityCollection
                = dataModel.getEntryRowEntities(EntityHelper.getEntityId(ManageSubTask.class));
        if (CollectionUtils.isEmpty(dataEntityCollection)) {
            return;
        }

        // 3.包装基础子任务实体数据
        List<String> approveSubTaskIds = Lists.newArrayList();
        List<TechnologyManagementAssignment> subAssignments = Lists.newArrayList();
        for (DynamicDataEntity dataEntity : dataEntityCollection) {
            TechnologyManagementAssignment subAssignment = new TechnologyManagementAssignment();
            subAssignment.setBillId(assignment.getBillId());

            // (1) 审批实体主键
            List<ManageSubTaskFlow> taskFlowList = ManageTaskAbility.query(dataEntity.getPkValue().toString(), assignment.getBillId());
            ManageSubTaskFlow taskFlow = taskFlowList.get(INTEGER_ZERO);

            String subTaskId = taskFlow.getId();
            approveSubTaskIds.add(subTaskId);
            subAssignment.setApproveSubTaskId(subTaskId);
            subAssignment.setEntityId(subTaskId);

            // (2) 子任务名称
            subAssignment.setAssignmentName(dataEntity.getString(SUBTASK_NAME));

            // (3) 子任务网络
            DynamicDataEntity networkInfo = dataEntity.getDataEntity(NETWORK_ID);
            subAssignment.setNetwork(TextValuePairHelper.buildList(networkInfo.getString("NetworkId"), EMPTY_STRING, EMPTY_STRING));

            // (4) 子任务责任人 + 当前处理人
            Object responsibleInfo = dataEntity.get(SUBTASK_RESPONSIBLE_PERSON, Object.class);
            List<Employee> responsible = JsonUtils.parseArray(responsibleInfo, Employee.class);
            subAssignment.setResponsibleEmployee(responsible);
            subAssignment.setCurrentProcessorEmployee(responsible);

            // (5) 要求完成日期
            subAssignment.setRequiredCompletionTime(dataEntity.getDate(SUBTASK_COMPLETION_DATE));

            // (6) 主任务一致 - 产品经营团队 + 代表处 + 客户标识
            subAssignment.setMarketing(assignment.getMarketing());
            subAssignment.setRepresentativeOffice(TextValuePairHelper.buildList((String) dataEntity.get(RESPONSIBLE_ORG_ID), null, null));
            subAssignment.setCustomerClassification(assignment.getCustomerClassification());
            subAssignment.setProductClassification(assignment.getProductClassification());
            subAssignment.setProductManagementTeam(assignment.getProductManagementTeam());

            subAssignments.add(subAssignment);
        }

        // 4.检索已存在的子任务
        List<TechnologyManagementAssignment> existSubAssignment = AssignmentAbility.queryExistSubAssignment(approveSubTaskIds);
        List<String> existSubTaskIdList = existSubAssignment.stream()
                .map(TechnologyManagementAssignment::getApproveSubTaskId)
                .collect(Collectors.toList());

        // 5.过滤出需要新增的子任务
        List<TechnologyManagementAssignment> needInsetSubAssignment = subAssignments.stream()
                .filter(item -> !existSubTaskIdList.contains(item.getApproveSubTaskId()))
                .collect(Collectors.toList());

        // 6.新增子任务包装
        Integer beginCode = calculateSubBeginCode(existSubAssignment);
        for (TechnologyManagementAssignment subAssignment : needInsetSubAssignment) {
            // (1) 任务编码
            subAssignment.setAssignmentCode(assignment.getAssignmentCode() + HYPHEN + String.format("%03d", beginCode));
            beginCode ++;

            // (2) 任务状态 + 任务类型 + 单据类型
            subAssignment.setAssignmentStatus(AssignmentStatusEnum.EXECUTE.getValue());
            subAssignment.setAssignmentType(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB.getPropValue());
            subAssignment.setBillType(BillTypeEnum.TECHNOLOGY_MANAGEMENT.getPropValue());

            // (3) 任务分类
            subAssignment.setTaskCategory(assignment.getTaskCategory());

            // (4) 是否审批任务
            subAssignment.setApprovalTaskFlag(BoolEnum.N);
        }

        // 7.新增子任务数据
        List<String> subAssignmentIdList = AssignmentAbility.batchInsert(needInsetSubAssignment);

        // 子任务邮件发送
        sendSubTaskCreateEmail(needInsetSubAssignment);

        // 8.新增权限控制关系
        List<TechnologyManagementAssignment> insertSubAssigmentList =
                AssignmentAbility.querySpecificTypeAssignment(subAssignmentIdList, TechnologyManagementAssignment.class);
        for (TechnologyManagementAssignment subAssignment : insertSubAssigmentList) {
            List<String> responsible = EmployeeHelper.getEpmUIID(subAssignment.getResponsibleEmployee());

            Set<String> relevanceSet = new HashSet<>();
            relevanceSet.add(ContextHelper.getEmpNo());
            relevanceSet.addAll(responsible);

            // 权限控制：转换成主任务对责任人 / 创建人关联关系
            AssignmentAbility.createAssignmentPersonRelevance(assignment.getId(), new ArrayList<>(relevanceSet));

            // 待办控制：子任务对责任人 / 创建人关联关系
            AssignmentAbility.createAssignmentPersonRelevance(subAssignment.getId(), new ArrayList<>(relevanceSet));
        }

        // 9.新增子任务主任务关联关系
        ObjectLinkInstanceAbility.createParentChildrenLinkInstance(
                TechnologyManagementAssignment.class, assignment.getId(), subAssignmentIdList);

        // 10.更新主任务网络数据 - 影响列表查询
        List<TextValuePair> networks = integrateSubAssignmentNetworkInfo(subAssignments);
        assignment.setNetwork(networks);
        AssignmentAbility.update(assignment);
    }

    /**
     * 计算新增子任务起始编码
     * @param subAssignmentList
     * @return Integer
     */
    private Integer calculateSubBeginCode(List<TechnologyManagementAssignment> subAssignmentList) {
        if (CollectionUtils.isEmpty(subAssignmentList)) {
            return INTEGER_ONE;
        }

        Integer lastCode = INTEGER_ZERO;
        for (TechnologyManagementAssignment subAssignment : subAssignmentList) {
            String assignmentCode = subAssignment.getAssignmentCode();
            String[] codeArray = assignmentCode.split(HYPHEN);
            Integer code = Integer.valueOf(codeArray[INTEGER_ONE]);

            if (lastCode < code) {
                lastCode = code;
            }
        }

        return lastCode + INTEGER_ONE;
    }

    /**
     * 汇总子任务网络信息
     * @param subAssignmentList
     * @return List<TextValuePair>
     */
    private List<TextValuePair> integrateSubAssignmentNetworkInfo(List<TechnologyManagementAssignment> subAssignmentList) {
        if (CollectionUtils.isEmpty(subAssignmentList)) {
            return Lists.newArrayList();
        }

        Set<String> networkIdSet = new HashSet<>();
        List<TextValuePair> networkList = Lists.newArrayList();
        for (TechnologyManagementAssignment subAssignment : subAssignmentList) {
            String networkId = TextValuePairHelper.getValue(subAssignment.getNetwork());
            if (networkIdSet.contains(networkId)) {
                continue;
            }

            networkList.addAll(TextValuePairHelper.buildList(networkId, EMPTY_STRING, EMPTY_STRING));
            networkIdSet.add(networkId);
        }

        return networkList;
    }

    /**
     * 故障复盘任务 创建后续步骤
     */
    private void faultReviewAssignmentCreated(
            IFormView formView,
            String faultAssignmentId,
            TechnologyManagementAssignment reviewAssignment) {

        // 1.创建关联关系
        ObjectLinkInstanceAbility.createBindingLinkInstance(
                Assignment.class, faultAssignmentId, Lists.newArrayList(reviewAssignment.getId()));

        // 2.更新故障管理任务当前进展
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(faultAssignmentId);
        updateAssignment.setCurrentProcessorEmployee(Lists.newArrayList());
        updateAssignment.setCurrentProgress(FaultProcessEnum.FAULT_REVIEWING.name());
        AssignmentAbility.update(updateAssignment);

        // 3.推送故障管理任务审批流
        Assignment faultAssignment = AssignmentAbility.querySpecificTypeAssignment(
                faultAssignmentId,
                Lists.newArrayList(AssignmentFieldConsts.ENTITY_ID, AssignmentFieldConsts.ASSIGNMENT_CODE),
                Assignment.class);
        String assignmentCode = faultAssignment.getAssignmentCode();
        List<FaultManagementOrder> faultManagementOrders
                = FaultManagementOrderAbility.queryOrderByCscCode(Lists.newArrayList(assignmentCode));
        if (CollectionUtils.isEmpty(faultManagementOrders)) {
            return;
        }
        if (null != faultAssignment) {
            // 故障管理单 - 故障复盘提交人 / 提交时间
            FaultManagementOrder updateOrder = new FaultManagementOrder();
            FaultManagementOrder faultManagementOrder = faultManagementOrders.get(INTEGER_ZERO);
            updateOrder.setId(faultManagementOrder.getId());
            updateOrder.setReviewSubmitter(
                    EmployeeHelper.getEmployees(Lists.newArrayList(ContextHelper.getEmpNo())));
            updateOrder.setReviewSubmitTime(
                    DateUtils.dateToString(new Date(), CommonConstants.DATE_FORM));
            FaultManagementOrderAbility.batchUpdate(Lists.newArrayList(updateOrder));
            FlowHelper.changeFlowParams(faultAssignment.getEntityId(),
                    MapUtils.newHashMap(FIELD_FAULT_REVIEW_CID, BoolEnum.Y.name()), FAULT_MANAGE_FLOW);
            FlowHelper.pushSystemNode(faultAssignment.getEntityId(), FAULT_REVIEW_CONFIRMING_NODE_KEY);

            // 4.关闭页面
            IClientViewProxy parentViewProxy = formView.getParentView()
                    .getClientViewProxy();
            parentViewProxy.closeWindow(new HashMap<>());
            formView.getClientViewProxy()
                    .sendParentViewCmd(parentViewProxy);
        }
    }

    /**
     * 故障整改横推任务 创建后续步骤
     */
    private void faultRectifyAssignmentCreated(
            IFormView formView,
            String faultAssignmentId,
            TechnologyManagementAssignment rectifyAssignment) {

        // 1.创建关联关系
        ObjectLinkInstanceAbility.createBindingLinkInstance(
                Assignment.class, faultAssignmentId, Lists.newArrayList(rectifyAssignment.getId()));

        // 2.主任务责任人 同 故障管理任务 权限
        List<String> relevant = rectifyAssignment.getResponsibleEmployee().stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toList());
        AssignmentAbility.createAssignmentPersonRelevance(faultAssignmentId, relevant);

        // 3.刷新任务列表
        refreshTable(formView, TABLE_FAULT_RECTIFY_ASSIGNMENT_CID);
    }
}
