package com.zte.iccp.itech.extension.domain.model.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/5/28 下午2:28
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Employee {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("组织id")
    private String orgID;

    @ApiModelProperty("员工姓名")
    private String empName;

    @ApiModelProperty("emp账号")
    private String empUIID;

    @ApiModelProperty("员工姓名")
    private String empNameCn;

    @ApiModelProperty("员工英文姓名")
    private String empNameEn;

    @ApiModelProperty("组织级")
    private String orgNamePath;

    @ApiModelProperty("组织级英文")
    private String orgNamePathEn;

    /**
     * 组装数据插入数据库回显时，userName = 工号，必须传值
     */
    @ApiModelProperty("工号")
    private String userName;

    @Override
    public boolean equals(Object object) {
        if (object == null || getClass() != object.getClass()) {
            return false;
        }

        // 当前仅校验 用户ID，后续有必要再补充其它字段
        Employee employee = (Employee) object;
        return empUIID.equals(employee.getEmpUIID());
    }

    @Override
    public int hashCode() {
        return Objects.hash(empUIID);
    }
}
