package com.zte.iccp.itech.extension.domain.enums.changeorder;

import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/19
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum BatchTaskTypeEnum {
    /** 内部变更批次任务 */
    BATCH_TASK(BatchTask.class),
    /** 分包商变更批次任务 */
    SUBCONTRACT_BATCH_TASK(SubcontractorBatchTask.class),
    ;

    private final Class<? extends BaseEntity> entityClass;

    public static BatchTaskTypeEnum fromEntityClass(Class<? extends BaseEntity> entityClass) {
        for (BatchTaskTypeEnum value : values()) {
            if (value.entityClass == entityClass) {
                return value;
            }
        }

        return null;
    }
}
