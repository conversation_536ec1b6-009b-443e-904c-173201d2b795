package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.CoreNetConfigVo;
import com.zte.iccp.itech.extension.spi.model.query.LogicalNeQuery;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import java.util.*;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.INNER_OFFICE_ORG_CODE_PATH;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @date 2024/10/29 下午2:36
 */
public class LogicalNePlugin implements ValueChangeBaseFormPlugin {

    @Override
    public void operate(ValueChangedEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();
        Pair<Boolean, List<CoreNetConfigVo>> pair = getIsShowVoPair(dataModel);
        setAttributeAndValue(pair.getLeft(), pair.getRight(), formView, dataModel);
    }

    @Override
    public void loadData(LoadDataEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();
        Pair<Boolean, List<CoreNetConfigVo>> pair = getIsShowVoPair(dataModel);

        PageStatusEnum pageStatusEnum = formView.getFormShowParameter().getPageStatus();
        if (PageStatusEnum.EDIT.equals(pageStatusEnum)
                || PageStatusEnum.NEW.equals(pageStatusEnum)) {
            setAttributeAndValue(pair.getLeft(), pair.getRight(), formView, dataModel);
            return;
        }

        if (Boolean.TRUE.equals(pair.getLeft())) {
            formView.getClientViewProxy().setControlState(LOGICAL_NE_CID, new PageStatusAttributeBuilder().readOnly().build());
            // 修复逻辑网元只读态情况下展示为id的问题
            OptionsBuilder optionsBuilder = new OptionsBuilder();
            for (CoreNetConfigVo vo : pair.getRight()) {
                optionsBuilder.addOption(new Option(vo.getId(), new Text(vo.getCoreConfigAttributeCn(), vo.getCoreConfigAttributeEn())));
            }
            formView.getClientViewProxy().setOptions(MapUtils.newHashMap(OPTIONS, optionsBuilder.build(),
                    TARGET, LOGICAL_NE_CID));
        } else {
            formView.getClientViewProxy().setControlState(LOGICAL_NE_CID, new PageStatusAttributeBuilder().hidden().build());
        }
    }

    private static void setAttributeAndValue(Boolean isShow, List<CoreNetConfigVo> vos, IFormView formView, IDataModel dataModel) {
        if (Boolean.TRUE.equals(isShow)) {
            // 【逻辑网元】字段，国内代表处：必填；国际代表处：非必填
            String orgId = TextValuePairHelper.getValue(dataModel.getValue(FIELD_ORGANIZATION_CID));
            Boolean requiredFlag = !StringUtils.isEmpty(orgId)
                    && Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).anyMatch(orgId::startsWith);
            OptionsBuilder optionsBuilder = new OptionsBuilder();
            for (CoreNetConfigVo vo : vos) {
                optionsBuilder.addOption(new Option(vo.getId(), new Text(vo.getCoreConfigAttributeCn(), vo.getCoreConfigAttributeEn())));
            }
            Map<String, Object> value = MapUtils.newHashMap(OPTIONS, optionsBuilder.build(), TARGET, LOGICAL_NE_CID);

            formView.getClientViewProxy().setControlState(LOGICAL_NE_CID, new PageStatusAttributeBuilder().normal().build());
            formView.getClientViewProxy().setControlState(LOGICAL_NE_CID, new BasicAttributeBuilder().attribute(REQUIRED, requiredFlag).build());
            formView.getClientViewProxy().setOptions(value);
        } else {
            formView.getClientViewProxy().setControlState(LOGICAL_NE_CID, new PageStatusAttributeBuilder().hidden().build());
            dataModel.setValue(LOGICAL_NE_PROPERTY_KEY, new OptionsBuilder().build());
        }
    }

    private static Pair<Boolean, List<CoreNetConfigVo>> getIsShowVoPair(IDataModel dataModel) {
        String productIdPath = TextValuePairHelper.getValue(dataModel.getValue(FIELD_PRODUCT_CID));
        // 若主单据产品分类为空或者非【算力及核心网】下属分类，则隐藏逻辑网元组件
        if (StringUtils.isEmpty(productIdPath) || !productIdPath.startsWith(ConfigHelper.get(CCN_PROD_ID_PATH))) {
            return Pair.of(false, Lists.newArrayList());
        }

        // 根据产品小类去NIS查询逻辑网元，若查询不到，则隐藏【逻辑网元】组件
        LogicalNeQuery query = new LogicalNeQuery();
        query.setProdClassId(ProductUtils.getProductIdByLevel(productIdPath, INTEGER_FOUR));
        List<CoreNetConfigVo> vos = NisClient.getLogicalNes(query);
        if (CollectionUtils.isEmpty(vos)) {
            return Pair.of(false, Lists.newArrayList());
        }

        return Pair.of(true, vos);
    }

}
