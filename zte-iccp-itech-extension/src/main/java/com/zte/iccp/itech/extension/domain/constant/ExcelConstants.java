package com.zte.iccp.itech.extension.domain.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/3 上午11:45
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExcelConstants {

    public static class QueryUserPermission {

        /** 查询用户权限导出文件名 */
        public static final String QUERY_USER_PERMISSION_EXPORT_FILE_NAME = "query.user.permission.export.file.name";

        /** 模块名称 */
        public static final String MODULE_NAME = "query.user.permission.export.module.name";

        /** 账号 */
        public static final String ACCOUNT = "query.user.permission.export.account";

        /** 姓名 */
        public static final String NAME = "query.user.permission.export.name";

        /** 角色 */
        public static final String ROLE = "query.user.permission.export.role";

        /** 产品 */
        public static final String PRODUCT = "query.user.permission.export.product";

        /** 代表处 */
        public static final String ORGANIZATION = "query.user.permission.export.organization";

        /** 有效日期 */
        public static final String EXPIRATION_TIME = "query.user.permission.export.expiration.time";

    }
}
