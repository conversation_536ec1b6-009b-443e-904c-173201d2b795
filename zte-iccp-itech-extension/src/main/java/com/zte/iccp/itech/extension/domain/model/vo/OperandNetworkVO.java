package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("操作对象 - 网络(dataKey)")
@Setter
@Getter
public class OperandNetworkVO {

    @JsonProperty("custom_32nt4lds")
    @ApiModelProperty()
    private String networkId;

    @JsonProperty("custom_nvtlssnr")
    @ApiModelProperty("网络ID(网络编码)")
    private String networkCode;

    @JsonProperty("custom_fyomujry")
    @ApiModelProperty("客户网络名称")
    private String customerNetworkName;

    @JsonProperty("custom_cj0u2go7")
    @ApiModelProperty("网络名称")
    private String networkName;

    @JsonProperty("custom_qta8z66u")
    @ApiModelProperty("责任单位")
    private String responsibleDept;

    @JsonProperty("custom_vcwsaspt")
    @ApiModelProperty("产品类型")
    private String prodClass;

    @JsonProperty("custom_ouxfb44z")
    @ApiModelProperty("客户名称")
    private String customerName;

    @JsonProperty("custom_d3jqi6r9")
    @ApiModelProperty("网络责任人")
    private String responsiblePerson;

    @JsonProperty("custom_tuh9a3g7")
    @ApiModelProperty("网络负责组")
    private String responsibleTeam;

    @JsonProperty("custom_xa4nv7ks")
    @ApiModelProperty("是否受限主体")
    private String restrictedFlag;

    @JsonProperty("custom_ycch0w5t")
    @ApiModelProperty("是否政企")
    private String govEntFlag;

    @JsonProperty("custom_j9pnlrsp")
    @ApiModelProperty("是否运营商集团直管")
    private String operatorGroupNetwork;
}
