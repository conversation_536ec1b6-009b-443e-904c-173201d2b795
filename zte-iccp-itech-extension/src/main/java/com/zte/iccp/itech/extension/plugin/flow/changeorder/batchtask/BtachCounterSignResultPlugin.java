package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.model.vo.SubIntlAdminApproval;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin.FlowVariant;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.CHANGE_ORDERBATCH_INTL_ADMIN_NODE_KEY;
import static com.zte.iccp.itech.extension.domain.constant.entity.CustomizedNetworkSetConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.IntlAdminApprovalFieldConsts.APPROVED_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.IntlAdminApprovalFieldConsts.APPROVE_RESULT;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.FlowVariantEnum.APPROVE_RESULT_INTL_ADMIN;

/**
 * 批次 行政团队会签节点的会签结果计算插件
 *
 * <AUTHOR> 10284287
 * @since 2024/07/24
 */
public class BtachCounterSignResultPlugin extends BaseFlowOperationPlugin {

    @SneakyThrows
    @Override
    public void beforeOperate(ExecuteEvent event) {
        // 当前页面的审批结果
        List<ApproveResultEnum> results = getModel().getEntryColumnObject(
                        EntityHelper.getEntityId(SubIntlAdminApproval.class), APPROVE_RESULT).stream()
                .filter(i -> i != null)
                .map(is -> TextValuePairHelper.get(ApproveResultEnum.class, is))
                .collect(Collectors.toList());

        List<SubIntlAdminApproval> approvals =
                QueryDataHelper.query(SubIntlAdminApproval.class, Arrays.asList(ID, APPROVE_RESULT, APPROVED_BY), getPkId());
        for (SubIntlAdminApproval item : approvals) {
            if (item.getResult() != null) {
                results.add(item.getResult());
            }
        }

        // 默认为 通过
        ApproveResultEnum counterSignResult = getApproveResultEnum(results);

        Map<FlowVariant, Object> params = MapUtils.newHashMap(APPROVE_RESULT_INTL_ADMIN, counterSignResult);
        FlowHelper.changeVariables(getPkId(), params);
        // 会签节点拒绝时获取活跃节点，异步关闭节点
        if (ApproveResultEnum.TERMINATE == counterSignResult) {
            AsyncExecuteUtils.execute(() -> {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                if (CHANGE_ORDERBATCH_INTL_ADMIN_NODE_KEY.equals(FlowHelper.getCurrentNodeId(getPkId()))) {
                    FlowHelper.pushSystemNode(getPkId(), CHANGE_ORDERBATCH_INTL_ADMIN_NODE_KEY);
                }
            });
        }

        // 更新会签节点当前审核人的审核时间
        List<String> adminApprovalIds = approvals.stream()
                .filter(item -> item.getApprover() != null && ContextHelper.getEmpNo().equals(item.getApprover().getEmpUIID()))
                .map(SubIntlAdminApproval::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adminApprovalIds)) {
            return;
        }

        List<SubIntlAdminApproval> updateIntlApprovals = new ArrayList<>();
        adminApprovalIds.forEach(item -> {
            SubIntlAdminApproval updateIntlApproval = new SubIntlAdminApproval();
            updateIntlApproval.setId(item);
            updateIntlApproval.setApprovedDate(new Date());
            updateIntlApprovals.add(updateIntlApproval);
        });
        SaveDataHelper.batchUpdate(updateIntlApprovals);
    }

    private ApproveResultEnum getApproveResultEnum(List<ApproveResultEnum> results) {
        ApproveResultEnum counterSignResult = ApproveResultEnum.PASS;
        FOR: for (ApproveResultEnum result : results) {
            switch (result) {
                case TERMINATE:
                    // 有一个 终止 则结果为 终止
                    counterSignResult = result;
                    break FOR;
                case PASS:
                default:
                    break;
            }
        }
        return counterSignResult;
    }
}
