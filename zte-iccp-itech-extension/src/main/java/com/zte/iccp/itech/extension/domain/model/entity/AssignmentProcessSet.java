package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ASSIGNMENT_SET;

@ApiModel("流程任务集")
@Setter
@Getter
@BaseEntity.Info("assignment_process_set")
public class AssignmentProcessSet extends BaseEntity {

    @ApiModelProperty("任务集")
    @JsonProperty(value = ASSIGNMENT_SET)
    private List<TextValuePair> assignmentSet;
}
