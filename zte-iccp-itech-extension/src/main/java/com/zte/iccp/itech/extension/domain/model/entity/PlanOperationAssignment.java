package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.TIME_ZONE;

@ApiModel("网络变更任务实体")
@Setter
@Getter
@BaseEntity.Info("assignment")
public class PlanOperationAssignment extends Assignment {

    @ApiModelProperty("操作类型")
    @JsonProperty(value = "plan_operation_type")
    private List<TextValuePair> operationType;

    @ApiModelProperty("计划开始时间")
    @JsonProperty("plan_order_start_time")
    private Date planStartTime;

    @ApiModelProperty("时区")
    @JsonProperty(value = TIME_ZONE)
    private String timeZone;

    @ApiModelProperty("国家 / 地区")
    @JsonProperty(value = "plan_country")
    private List<MultiLangText> country;

    @ApiModelProperty("操作原因")
    @JsonProperty(value = "plan_operation_reason")
    private List<TextValuePair> operationReason;

    @ApiModelProperty("重要程度")
    @JsonProperty(value = "plan_importance")
    private List<TextValuePair> importance;

    @ApiModelProperty("风险评估")
    @JsonProperty(value = "plan_risk_evaluation")
    private List<TextValuePair> riskEvaluation;

    @ApiModelProperty("操作等级")
    @JsonProperty(value = "plan_operation_level")
    private List<TextValuePair> operationLevel;

    @ApiModelProperty("局点名称")
    @JsonProperty(value = "plan_office_name")
    private List<TextValuePair> officeName;

    @ApiModelProperty("冲突检查结果")
    @JsonProperty(value = "plam_time_conflict")
    private String conflict;

    @ApiModelProperty("操作开始时间（UTC+8）")
    @JsonProperty(value = "plan_operation_start_time_utc_8")
    private Date operationStartTimeUtc8;

    @ApiModelProperty("网络变跟单编号")
    @JsonProperty(value = "cn_no")
    private String cnNo;
}
