package com.zte.iccp.itech.extension.ability.changeorder;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.EmailAbility;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.ability.OperationSchemeAbility;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.enums.OperationLogActionEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ChangeOrderTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiProductLinkageGuarantee;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ABOLISH_FLAG;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.MultiProductLinkageGuaranteeFieldConsts.ASSIGNMENT_ID;

/**
 * <AUTHOR>
 * @date 2024/7/29 下午5:00
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MultiProdGuaranteeAbility {

    public static List<MultiProductLinkageGuarantee> query(List<String> changeOrderIds) {
        if (CollectionUtils.isEmpty(changeOrderIds)) {
            return Lists.newArrayList();
        }
        return QueryDataHelper.queryByPid(MultiProductLinkageGuarantee.class, Lists.newArrayList(), changeOrderIds);
    }

    public static List<MultiProductLinkageGuarantee> queryByAssignmentIds(List<String> assignmentIds) {
        if (CollectionUtils.isEmpty(assignmentIds)) {
            return Lists.newArrayList();
        }
        Filter assignmentFilter = new Filter(ASSIGNMENT_ID, Comparator.IN, assignmentIds);
        return QueryDataHelper.querySub(MultiProductLinkageGuarantee.class,
                Lists.newArrayList(), Lists.newArrayList(assignmentFilter));
    }

    public static void batchCreate(List<MultiProductLinkageGuarantee> multiProdGuarantees) {
        if (CollectionUtils.isEmpty(multiProdGuarantees)) {
            return;
        }

        SaveDataHelper.batchCreate(multiProdGuarantees);
    }

    public static void batchUpdate(List<MultiProductLinkageGuarantee> multiProdGuarantees) {
        if (CollectionUtils.isEmpty(multiProdGuarantees)) {
            return;
        }

        SaveDataHelper.batchUpdate(multiProdGuarantees);
    }

    public static void batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        SaveDataHelper.batchDelete(MultiProductLinkageGuarantee.class, ids);
    }

    /**
     * 保障主单据废止后置处理
     *
     * @param changeOrderId 网络变更单id
     * @param operationKey 操作日志key
     * @param operationTime 操作时间
     */
    public static void abolishAfterProcessor(String changeOrderId, String operationKey, Date operationTime) {
        abolishAfterProcessor(changeOrderId, operationKey, operationTime, false);
    }

    /**
     * 保障主单据废止后置处理
     *
     * @param changeOrderId 网络变更单id
     * @param operationKey 操作日志key
     * @param operationTime 操作时间
     * @param mainTaskDeletedFlag 保障主任务删除标识（为true则说明主保障任务也需要删除）
     */
    private static void abolishAfterProcessor(String changeOrderId, String operationKey, Date operationTime, boolean mainTaskDeletedFlag) {
        // 1.查询多联动产品保障数据
        NetworkChangeAssignment networkChangeAssignment = AssignmentAbility.querySpecificTypeAssignment(
                changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);

        if (networkChangeAssignment == null || ChangeOrderTypeEnum.NORMAL.name().equals(networkChangeAssignment.getType())||
                //增加判断，判断当前为子保障任务则不进行操作
                ChangeOrderTypeEnum.MULTI_PRODUCT_GUARANTEE_SUB_TASK.name().equals(networkChangeAssignment.getType())) {
            return;
        }

        List<MultiProductLinkageGuarantee> multiProductLinkageGuarantees = query(Lists.newArrayList(changeOrderId));

        // 2.获取任务中心状态
        List<String> assignmentIds = multiProductLinkageGuarantees.stream()
                .filter(item -> !item.getIsMainTask() && item.getAssignmentId() != null)
                .map(MultiProductLinkageGuarantee::getAssignmentId)
                .collect(Collectors.toList());

        List<NetworkChangeAssignment> assignmentList = AssignmentAbility.querySpecificTypeAssignment(assignmentIds, NetworkChangeAssignment.class);

        // 3.若保障任务处于待启动且从未提交审核过，则该保障任务删除，发送邮件
        delete(assignmentList, multiProductLinkageGuarantees, networkChangeAssignment, mainTaskDeletedFlag);

        // 4.若保障任务已经提交且未废止或关闭，则该保障任务废止，废止说明与主任务废止说明一致， 且保障任务全部批次任务均废止，发送邮件
        abolish(assignmentList, changeOrderId, operationKey, operationTime);
    }


    /**
     * 若保障任务处于待启动且从未提交审核过，则该保障任务删除，发送邮件
     *
     * @param assignmentList assignmentList
     */
    private static void delete(List<NetworkChangeAssignment> assignmentList,
                               List<MultiProductLinkageGuarantee> multiProductLinkageGuarantees,
                               NetworkChangeAssignment mainAssignment,
                               boolean mainTaskDeletedFlag) {
        List<NetworkChangeAssignment> filteredAssignments = assignmentList.stream()
                .filter(item -> StringUtils.equals(AssignmentStatusEnum.START.getValue(), item.getAssignmentStatus()))
                .collect(Collectors.toList());

        // 主保障单据撤回后，解除主单据和全部的多产品联动保障子单据体之间的关联关系
        if (mainTaskDeletedFlag) {
            filteredAssignments.add(mainAssignment);
        }

        if (CollectionUtils.isEmpty(filteredAssignments)) {
            return;
        }

        // (1)删除多产品联动保障：删除分为两部分：第一部分是删除主变更单关联的草稿保障任务，第二部分是删除其他保障任务关联的草稿状态的保障任务
        // 第一部分是删除主变更单关联的草稿保障任务
        List<String> assignments = filteredAssignments.stream().map(BaseEntity::getId).collect(Collectors.toList());
        Map<String, String> assignmentMap = multiProductLinkageGuarantees.stream()
                .collect(Collectors.toMap(MultiProductLinkageGuarantee::getAssignmentId, BaseEntity::getId));

        List<String> multiProductIds = new ArrayList<>();
        assignments.forEach(item -> multiProductIds.add(assignmentMap.get(item)));

        // 第二部分是删除其他保障任务关联的草稿状态的保障任务（同时兼容撤回后主保障单据删除逻辑）
        multiProductIds.addAll(getSubMultiProductDelIds(filteredAssignments, assignmentList));
        MultiProdGuaranteeAbility.batchDelete(multiProductIds);

        // (2)删除变更单对应操作方案
        OperationSchemeAbility.deletes(assignments);

        // (3)删除任务
        AssignmentAbility.batchDelete(assignments);

        // (4).发送邮件, 保障任务-xxx(任务单号：xxx)已由xxx删除，请关注
        EmailAbility.sendDeleteMail(filteredAssignments,null);
    }


    /**
     * 获取子保障任务待删除的ids
     *
     * @param deleteAssignmentList 待删除任务
     * @param allAssignmentList 全部任务，除主任务之前的全部任务
     * @return ids
     */
    private static List<String> getSubMultiProductDelIds(List<NetworkChangeAssignment> deleteAssignmentList, List<NetworkChangeAssignment> allAssignmentList) {
        List<String> resultList = new ArrayList<>();
        List<String> assignments = deleteAssignmentList.stream().map(BaseEntity::getId).collect(Collectors.toList());
        List<String> changeOrderIds = allAssignmentList.stream().map(Assignment::getBillId).collect(Collectors.toList());
        List<MultiProductLinkageGuarantee> multiProductLinkageGuaranteeList = MultiProdGuaranteeAbility.query(changeOrderIds);
        if (CollectionUtils.isEmpty(multiProductLinkageGuaranteeList)) {
            return resultList;
        }

        multiProductLinkageGuaranteeList = multiProductLinkageGuaranteeList.stream()
                .filter(item -> assignments.contains(item.getAssignmentId()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(multiProductLinkageGuaranteeList)) {
            List<String> deleteIds = multiProductLinkageGuaranteeList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            resultList.addAll(deleteIds);
        }
        return resultList;
    }

    /**
     * 主任务废止数据
     * 1.支持废止执行中、审批中、驳回待启动的数据
     * 2.
     *
     * @param assignmentList assignmentList
     */
    private static void abolish(List<NetworkChangeAssignment> assignmentList, String changeOrderId, String operationKey, Date operationTime) {
        // 1.支持废止执行中、审批中、驳回待启动的数据
        List<NetworkChangeAssignment> filteredAssignments = assignmentList.stream()
                .filter(assignment -> Arrays.asList(
                        AssignmentStatusEnum.EXECUTE.getValue(),
                        AssignmentStatusEnum.APPROVE.getValue(),
                        AssignmentStatusEnum.APPROVE_START.getValue()).contains(assignment.getAssignmentStatus()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredAssignments)) {
            return;
        }
        // 2.更新状态为废止
        List<NetworkChangeAssignment> updateAssignmentList = filteredAssignments.stream().map(assignment -> {
            NetworkChangeAssignment updateAssignment = new NetworkChangeAssignment();
            updateAssignment.setId(assignment.getId());
            updateAssignment.setAssignmentStatus(AssignmentStatusEnum.ABOLISH.getValue());
            // 清除当前进展和当前处理人
            updateAssignment.setCurrentProcessorEmployee(new ArrayList<>());
            updateAssignment.setCurrentProgress("");
            return updateAssignment;
        }).collect(Collectors.toList());
        AssignmentAbility.batchUpdate(updateAssignmentList);

        // 3.审批中、驳回待处理的数据，额外支持废止流程
        List<NetworkChangeAssignment> approveStatusAssignments = filteredAssignments.stream()
                .filter(assignment -> AssignmentStatusEnum.APPROVE.getValue().equals(assignment.getAssignmentStatus())
                        || AssignmentStatusEnum.APPROVE_START.getValue().equals(assignment.getAssignmentStatus()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(approveStatusAssignments)) {
            approveStatusAssignments.forEach(assignment -> FlowHelper.revokeFlow(assignment.getBillId(), ApprovalConstants.CHANGE_ORDER_COMP_FLOW));
        }

        // 4.执行中的数据已经进入批次，支持终止下属批次流程
        List<NetworkChangeAssignment> executeStatusAssignments = filteredAssignments.stream()
                .filter(assignment -> AssignmentStatusEnum.EXECUTE.getValue().equals(assignment.getAssignmentStatus()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(executeStatusAssignments)) {
            List<String> billIds = executeStatusAssignments.stream().map(Assignment::getBillId).collect(Collectors.toList());
            abolishBatchTasks(billIds);
        }

        // 5.发送邮件, 保障任务-xxx(任务单号：xxx)已由xxx废止，请关注
        EmailAbility.sendDeleteMail(filteredAssignments, ABOLISH_FLAG);

        // 6.增加日志 - （主任务手动废止 - 同步保障单据废止）
        OperationLogRecordAbility.saveGuaranteeAbolishOperationLog(filteredAssignments, changeOrderId, operationKey, operationTime);
    }

    /**
     * 保障任务全部批次任务均废止
     *
     * @param billIds 多联动产品保障变更单id
     */
    private static void abolishBatchTasks(List<String> billIds) {
        List<IFilter> conditionFilters = Lists.newArrayList(new Filter(CHANGE_ORDER_ID, Comparator.IN, billIds));
        List<BatchTask> batchTaskList = QueryDataHelper.query(BatchTask.class, new ArrayList<>(), conditionFilters);
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }

        // 1.过滤批次任务中状态非已关闭、已废止的数据
        batchTaskList = batchTaskList.stream()
                .filter(batchTask -> !Arrays.asList(AssignmentStatusEnum.ABOLISH.getValue(),
                        AssignmentStatusEnum.CLOSE.getValue()).contains(batchTask.getCurrentStatus()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }

        // 2.终止批次任务流程，同时更新状态为已废止
        List<Map<String, Object>> multiValues = new ArrayList<>();
        for (BatchTask batchTask : batchTaskList) {
            multiValues.add(MapUtils.newHashMap(ID, batchTask.getId(),
                    CURRENT_STATUS, AssignmentStatusEnum.ABOLISH.getValue()));

            //废止批次任务流程
            FlowHelper.revokeFlow(batchTask.getId(), ApprovalConstants.BATCH_TASK_FLOW);
        }
        //更新批次任务状态
        SaveDataHelper.batchUpdate(BatchTask.class, multiValues);
    }


    /**
     * 根据子保障变更单id获取主单据最近处理的批次信息
     *
     * @param changeOrderId 变更单id
     * @return 主单据最近处理的批次信息
     */
    public static BatchTask getMainLatestProcessedBatchInfo(String changeOrderId) {
        List<MultiProductLinkageGuarantee> multiProductLinkageGuarantees = query(Lists.newArrayList(changeOrderId));

        MultiProductLinkageGuarantee multiProductLinkageGuarantee = multiProductLinkageGuarantees.stream()
                .filter(MultiProductLinkageGuarantee::getIsMainTask)
                .findFirst()
                .orElse(null);
        if (multiProductLinkageGuarantee == null) {
            return null;
        }

        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                multiProductLinkageGuarantee.getAssignmentId(), Assignment.class);

        List<BatchTask> batchTasks = BatchTaskAbility.batchGetByChangeOrderId(
                Objects.requireNonNull(assignment).getEntityId(), BatchTask.class, Lists.newArrayList());

        return CollectionUtils.isEmpty(batchTasks)
                ? null
                : batchTasks.stream()
                .filter(task -> task.getOperationUpdateTime() != null)
                .max(java.util.Comparator.comparing(BatchTask::getOperationUpdateTime))
                .orElse(null);
    }

    /**
     * 根据网络变更单主变更单id获取下述子保障任务
     *
     * @param changeOrderId 主网络变更单id
     * @return List<Assignment>
     */
    public static List<Assignment> getSubTakByMainId(String changeOrderId) {
        // 1.当前网络变更单为保障主任务
        NetworkChangeAssignment networkChangeAssignment = AssignmentAbility.querySpecificTypeAssignment(
                changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);

        if (networkChangeAssignment == null
                || !networkChangeAssignment.isMatchType(ChangeOrderTypeEnum.MULTI_PRODUCT_GUARANTEE_MAIN_TASK.name())) {
            return Collections.emptyList();
        }

        // 2.获取除主任务以外的全部保障子单据任务
        List<MultiProductLinkageGuarantee> guaranteeList = QueryDataHelper.query(
                MultiProductLinkageGuarantee.class, Lists.newArrayList(), changeOrderId);

        List<String> assignmentIds = guaranteeList.stream()
                .filter(guarantee -> !guarantee.getIsMainTask())
                .map(MultiProductLinkageGuarantee::getAssignmentId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(assignmentIds)) {
            return Collections.emptyList();
        }

        return AssignmentAbility.querySpecificTypeAssignment(assignmentIds, Assignment.class);
    }


    /**
     * 多产品联动保障单据 - 删除
     *
     * @param assignmentId 任务id
     * @param assignmentTypeEnum 任务类型
     */
    public static void multiProdGuaranteeDelete(String assignmentId, AssignmentTypeEnum assignmentTypeEnum) {
        // 1.方法只支持内部网络变更单
        if (AssignmentTypeEnum.NETWORK_CHANGE != assignmentTypeEnum) {
            return;
        }

        NetworkChangeAssignment networkChangeAssignment = AssignmentAbility.querySpecificTypeAssignment(
                assignmentId, NetworkChangeAssignment.class);

        // 2.保障子单据删除，解除关联关系
        assert networkChangeAssignment != null;
        if (ChangeOrderTypeEnum.MULTI_PRODUCT_GUARANTEE_SUB_TASK.name().equals(networkChangeAssignment.getType())) {
            deleteMultiProductGuaranteeSubtasks(networkChangeAssignment);

            // 3.保障主单据删除，子保障任务废止或草稿删除
        } else if (ChangeOrderTypeEnum.MULTI_PRODUCT_GUARANTEE_MAIN_TASK.name().equals(networkChangeAssignment.getType())) {
            abolishAfterProcessor(networkChangeAssignment.getEntityId(),
                    OperationLogActionEnum.ABOLISH.getOperationCode(),
                    new Date(),
                    true);
        }
    }



    /**
     * 保障子单据删除，解除关联关系（原逻辑迁移）
     *
     * @param assignment 网络变更单任务信息
     */
    private static void deleteMultiProductGuaranteeSubtasks(NetworkChangeAssignment assignment) {
        // 1.获取当前单据的多产品联动保障单子单据体数据
        List<MultiProductLinkageGuarantee> multiProductLinkageGuaranteeList = QueryDataHelper.query(
                MultiProductLinkageGuarantee.class, Lists.newArrayList(ID, ASSIGNMENT_ID), assignment.getEntityId());

        List<String> assignmentIds = multiProductLinkageGuaranteeList.stream()
                .map(MultiProductLinkageGuarantee::getAssignmentId)
                .collect(Collectors.toList());

        // 2.检索任务表，获取在任务表中的数据
        List<NetworkChangeAssignment> networkChangeAssignmentList =  QueryDataHelper.get(
                NetworkChangeAssignment.class,
                // 责任人、任务名称、任务编码、任务类型和billId均为邮件中使用
                Lists.newArrayList(ID, ENTITY_ID, RESPONSIBLE_EMPLOYEE_FIELD, ASSIGNMENT_NAME, ASSIGNMENT_CODE, BILL_ID, ASSIGNMENT_TYPE),
                assignmentIds);

        List<String> changeOrderList = networkChangeAssignmentList.stream()
                .map(NetworkChangeAssignment::getEntityId)
                .collect(Collectors.toList());

        // 3 变更单、全部子保障任务均维护了一条关联数据，获取全部的子单据体保障关联数据
        List<MultiProductLinkageGuarantee> allMultiProductLinkageGuarantees = QueryDataHelper.queryByPid(
                MultiProductLinkageGuarantee.class, Lists.newArrayList(ID, ASSIGNMENT_ID), changeOrderList);

        // 3.1 当前草稿保障单据在其他关联下的id集（待删除的）
        List<String> deleteGuaranteeIds = allMultiProductLinkageGuarantees.stream()
                .filter(item -> assignment.getId().equals(item.getAssignmentId()))
                .map(BaseEntity::getId)
                .collect(Collectors.toList());

        // 4.3 发邮件：删除关联关系
        EmailAbility.sendDelRelevanceMailByAssignments(networkChangeAssignmentList);

        // 4.4 发邮件：删除的任务
        EmailAbility.sendDeleteMail(Lists.newArrayList(assignment), null);

        // 4.3 删除关联关系
        SaveDataHelper.batchDelete(MultiProductLinkageGuarantee.class, deleteGuaranteeIds);
    }
}
