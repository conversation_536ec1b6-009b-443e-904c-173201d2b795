package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.PersonnelAbilty;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.PowerSpecificationModelFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ChangeOrderTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.Operator;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.extension.plugin.operation.assignment.helper.AssignmentHelper;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteTransactionalEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.OperationLog.SUBMISSION_FAILED_PLEASE_SAVE_REFRESH_AGAIN;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.POWER_SPECIFICATION_MODEL;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATOR_NAME;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATOR_ROLE;
import static com.zte.iccp.itech.extension.domain.constant.entity.SatisfactionResponsiblePersonConsts.ORGANIZATION_ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.IntegrationRelatedProductFieldConsts.IS_SELECT;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.BATCH_INFO_NO;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.NETWORK_ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.PowerSpecificationModelFieldConsts.*;

@Slf4j
public class SaveNetworkMainTaskPlugin extends BaseOperationPlugin {

    /**
     * 物料名称 - 其他值
     */
    private static final String MATERIAL_NAME_OTHER = "OTHER";

    /* 提交按钮key */
    private static final List<String> COMMIT_BUTTON_KEYS = Arrays.asList("submit_approval_newest", "resubmit");

    // 是否属于GDPR管控项目、产品分类、责任单位、是否政企、操作原因、操作类型、操作等级、重要程度、风险评估、是否首次应用、是否紧急操作、操作封装
    // 是否技术通知单实施、触发类型、是否带业务操作、是否需要多产品联动保障、变更操作来源、交付方式、封网管控、计划操作开始时间、计划操作结束时间
    // 预计业务中断时长（分钟）、网元清单文件、客户id、 内部操作方案、是否需要升级至技术交付部、是否行政领导审批
    private static final List<String> REQUIRE_FIELDS = Lists.newArrayList(IS_GDPR, PRODUCT_CATEGORY, RESPONSIBLE_DEPT, IS_GOV_ENT, OPERATION_REASON,
            OPERATION_TYPE, OPERATION_LEVEL, IMPORTANCE, RISK_EVALUATION, IS_FIRST_APPLICATION, IS_EMERGENCY_OPERATION,
            OPERATION_ENCAPSULATION, IS_TECHNICAL_NOTICE, TRIGGER_TYPE, MULTI_PROD_GUARANTEE,
            CHANGE_OPERATION_SOURCE, DELIVERY_MODE, IS_NET_CLOSE_OR_CONTROL_OPERATION, OPERATION_START_TIME, OPERATION_END_TIME,
            SERVICE_DISCONNECT_DURATION, CUSTOMER_ID, IS_UPGRADE_TECHNOLOGY,
            IS_ADMINISTRATION_LEADER_APPROVAL);

    // 批次概要：批次号、计划操作开始时间、计划操作结束时间、网元数量
    private static final List<String> BATCH_REQUIRE_FIELDS = Lists.newArrayList(BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY, FIELD_PLAN_OPERATION_START_TIME_CID,
            FIELD_PLAN_OPERATION_END_TIME_CID, BATCH_SUMMARY_TABLE_NE_COUNT_PROPERTY_KEY);

    // 操作对象：批次号
    private static final List<String> OBJECT_REQUIRE_FIELDS = Lists.newArrayList(BATCH_INFO_NO, FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, NETWORK_ID);

    /*
     * 校验idop是否能提交
     * */
    @Override
    public boolean beforeExecuteValidate(ExecuteEvent executeEvent) {
        // 只有提交和重新提交按钮 需要校验
        if (!COMMIT_BUTTON_KEYS.contains(executeEvent.getOperationKey())) {
            return true;
        }

        // 操作对象【主产品】必填校验（所有操作对象行必须有一个设置为【主产品】）
        // 这里先校验【主产品】字段必填，因为主产品字段没填会导致页面部分自动计算的必填字段为空
        List<Object> isMainProductObjects = getModel().getEntryColumnObject(
                OPERATION_OBJECT_TABLE_PROPERTY_KEY, OperationObjectFieldConsts.IS_MAIN_PRODUCT);
        if (isMainProductObjects.stream().noneMatch(item -> Y.equals(TextValuePairHelper.getValue(item)))) {
            getView().showMessage(MessageConsts.MAIN_PRODUCT_ATLEAST_ONE_SELECT, MsgType.ERROR);
            return false;
        }

        // 测试环境必填字段校验
        List<String> nullFields = checkRequireFields();
        if (!CollectionUtils.isEmpty(nullFields)) {
            getView().showMessage(SUBMISSION_FAILED_PLEASE_SAVE_REFRESH_AGAIN, MsgType.ERROR);
            return false;
        }

        // idop 校验
        if (ConfigHelper.getBoolean(IDOP_CHECK_ENABLE)) {
            Object source = getModel().getValue(ChangeOrderFieldConsts.SOURCE);
            String government = TextValuePairHelper.getValue(getModel().getValue(ChangeOrderFieldConsts.IS_GOV_ENT));
            String product = TextValuePairHelper.getValue(getModel().getValue(FIELD_PRODUCT_CID));
            String organization = TextValuePairHelper.getValue(getModel().getValue(ChangeOrderFieldConsts.RESPONSIBLE_DEPT));
            boolean isIdop = source == null || !DataSourceEnum.IDOP.name().equals(source.toString());
            if (isIdop && DISABLED_FLAG.equals(government) && DeptTypeEnum.INNER == ResponsibleUtils.getDeptType(organization)
                    && ProductUtils.isBelongIdopProduct(product)) {
                getView().showMessage(MessageConsts.CCN_ORDERS_ON_IDOP_CREATE, MsgType.ERROR);
                return false;
            }
        }
        //时间校验
        if (!ChangeOrderAbility.checkTime(getModel(), getView())) {
            return false;
        }
        List<Object> isSelectObjects = getModel().getEntryColumnObject(
                INTEGRATION_RELATED_PRODUCT_PROPERTY_KEY, IS_SELECT);
        String ovdcNfvi = TextValuePairHelper.getValue(getModel().getValue(OVDC_NFVI_PROPERTY_KEY));
        if (BoolEnum.Y.name().equals(ovdcNfvi)
                && isSelectObjects.stream().noneMatch(item -> BoolEnum.Y.name().equals(TextValuePairHelper.getValue(item)))) {
            getView().showMessage(MessageConsts.INTERGRTION_ATLEAST_ONE_SELECT, MsgType.ERROR);
            return false;
        }

        //人员积分校验
        String orgId = TextValuePairHelper.getValue(getModel().getValue(ORGANIZATION_ID));
        IDataEntityCollection dataEntityCollection = getModel().getEntryRowEntities(EntityHelper.getEntityId(Operator.class));
        String msg = PersonnelAbilty.commitPersonCheck(orgId, dataEntityCollection, OPERATOR_ROLE, OPERATOR_NAME);
        if (StringUtils.hasText(msg)) {
            getView().showMessage(msg, MsgType.ERROR);
            return false;
        }

        //操作人员校验
        if (ChangeOrderAbility.checkOperators(getModel(), getView(), Operator.class)
                || !beforeExecuteValidateExt(getModel(), getView())) {
            return false;
        }

        // 操作人员大于1校验
        if (!ChangeOrderAbility.checkOperatorsCount(getModel(), Operator.class)) {
            getView().showMessage(MessageConsts.CHECK_OPERATORS_COUNT_FAILED, MsgType.ERROR);
            return false;
        }

        return true;
    }

    /**
     * 1.检查操作对象必须有一行校验
     * 2.检查电源规格型号字段为不涉及之外的其他字段时，校验下属子表单数量
     *
     * @param model model
     * @param view  view
     * @return boolean
     */
    private boolean beforeExecuteValidateExt(IDataModel model, IFormView view) {
        // 操作对象必须有一行校验
        IDataEntityCollection dataEntityCollection = getModel().getEntryRowEntities(CidConstants.OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        if (dataEntityCollection.isEmpty()) {
            view.showMessage(MessageConsts.OPERATION_OBJECT_NOT_NULL_ERROR, MsgType.ERROR);
            return false;
        }
        // 在这个基础上增加一个校验，那就是每一个批次概要的(操作批次)必须对应操作对象一行
        // 获取批次概要的操作批次集
        IDataEntityCollection batchSummaryEntryRowEntities = getModel().getEntryRowEntities(FIELD_BATCH_SUMMARY_CID);
        if (batchSummaryEntryRowEntities.isEmpty()) {
            getView().showMessage(MessageConsts.CHECK_BATCH_SUMMARY_EMPTY_ERROR, MsgType.ERROR);
            return false;
        }

        List<String> batchNoList = ChangeOrderAbility.getEntryAllColumnData(
                batchSummaryEntryRowEntities, BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY);

        // 批次概要的操作批次是唯一的，不能重复：增加校验（批次概要不能存在相同的操作批次）
        if (batchNoList.size() != batchNoList.stream().distinct().count()) {
            getView().showMessage(MessageConsts.CHECK_BATCH_SUMMARY_REPEAT_ERROR, MsgType.ERROR);
            return false;
        }
        // 获取操作对象的操作批次集
        List<String> batchNos = ChangeOrderAbility.getEntryAllColumnData(
                getModel().getEntryRowEntities(OPERATION_OBJECT_TABLE_PROPERTY_KEY), BATCH_INFO_NO);
        // 如果批次概要的【操作批次】所有元素有一个不在操作对象【操作批次】中，则给一个提示“每一个批次概要操作批次必须对应一条操作对象操作批次”
        batchNoList.removeAll(batchNos);
        if (!CollectionUtils.isEmpty(batchNoList)) {
            getView().showMessage(MsgUtils.getMessage(
                    MessageConsts.OPERATION_OBJECT_CHECK_BATCH_SUMMARY_ERROR, String.join(COMMA, batchNoList)), MsgType.ERROR);
            return false;
        }

        // 电源规格型号为非必填项，会存在为空的情况
        String powerSpeciModelValue = TextValuePairHelper.getValue(model.getValue(POWER_SPECIFICATION_MODEL));
        // 如果不为空，并且值为非不涉及时，电源规格型号子表单必填一行，最大阈值为50
        if (!StringUtils.isEmpty(powerSpeciModelValue)
                && !BoolEnum.N.name().equals(powerSpeciModelValue)) {
            dataEntityCollection = model.getEntryRowEntities(CidConstants.ENTRYENTITY_POWER_SPECIFICATION_MODEL);
            // 请填写至少一行电源规则型号物料
            if (dataEntityCollection.isEmpty()) {
                view.showMessage(MessageConsts.POWER_SPECIFICATION_MODEL_MIX_NUMBER_ERROR, MsgType.ERROR);
                return false;
                // 电源规则型号物料最多填写50行
            } else if (dataEntityCollection.size() > INTEGER_FIFTY) {
                view.showMessage(MessageConsts.POWER_SPECIFICATION_MODEL_MAX_NUMBER_ERROR, MsgType.ERROR);
                return false;
            }
            // 电源规格型号子表单硬件版本会根据物料名称可能为下拉单选字段，也可能为单行文本字段，增加必填校验
            return checkPsmHardwareVersion(view, dataEntityCollection);
        }
        return true;
    }

    /**
     * 电源规格型号表单 - 硬件版本校验
     *
     * @param view                 view
     * @param dataEntityCollection dataEntityCollection
     * @return boolean
     */
    private boolean checkPsmHardwareVersion(IFormView view, IDataEntityCollection dataEntityCollection) {
        for (Object o : dataEntityCollection) {
            DynamicDataEntity dynamicData = (DynamicDataEntity) o;
            List<TextValuePair> materialNames = JsonUtils.parseArray(dynamicData.get(MATERIAL_NAME), TextValuePair.class);
            String materialName = materialNames.get(0).getValue();
            if (MATERIAL_NAME_OTHER.equals(materialName)) {
                // 自定义物料名称不能为空
                if (ObjectUtils.isEmpty(dynamicData.get(PowerSpecificationModelFieldConsts.MATERIAL_NAME_OTHER))) {
                    view.showMessage(MessageConsts.POWER_SPECIFICATION_MODEL_MATERIAL_NAME_REQUIRED_ERROR, MsgType.ERROR);
                    return false;
                }
            }
            // 如果物料名称为其他，校验自定义硬件版本不能为空
            if ((MATERIAL_NAME_OTHER.equals(materialName)
                    && ObjectUtils.isEmpty(dynamicData.get(HARDWARE_VERSION_OTHER)))
                    // 如果物料名称为下拉单选，校验硬件版本下拉单选不能为空
                    || (!MATERIAL_NAME_OTHER.equals(materialName)
                    && ObjectUtils.isEmpty(dynamicData.get(HARDWARE_VERSION)))) {
                view.showMessage(MessageConsts.POWER_SPECIFICATION_MODEL_HARDWARE_VERSION_REQUIRED_ERROR, MsgType.ERROR);
                return false;
            }
        }
        return true;
    }

    @Override
    public void afterExecuteTransactional(ExecuteTransactionalEvent executeTransactionalEvent) {
        // 1.获取提单主键, 检索已生成主任务数据
        IDataModel dataModel = getModel();
        String billId = getPkId();
        NetworkChangeAssignment existAssignment =
                AssignmentAbility.querySpecificTypeAssignment(billId, AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);

        // 2.生成主任务
        String buttonId = executeTransactionalEvent.getCid();
        NetworkChangeAssignment newAssignment = createOrUpdateMainAssignment(dataModel, billId, buttonId, existAssignment);

        // 3.对应按钮特殊操作
        if (BUTTON_SAVE_DRAFT_CID.equals(buttonId)) {
            saveDraftAssignmentOperation(newAssignment, billId);
        } else {
            submitAssignmentOperation(dataModel, newAssignment, billId);
        }

        if (COMMIT_BUTTON_KEYS.contains(executeTransactionalEvent.getOperationKey())) {
            // 2000L为提示停留时候，非等待时间
            getView().showMessage(MessageConsts.MESSAGE_SUBMIT_SUCCESS_REFRESH, MsgType.SUCCESS, 2000L, true);
        }
    }

    /**
     * 创建 / 更新主任务
     */
    private NetworkChangeAssignment createOrUpdateMainAssignment(IDataModel dataModel,
                                                                 String billId,
                                                                 String buttonId,
                                                                 NetworkChangeAssignment existAssignment) {
        NetworkChangeAssignment mainAssignment = new NetworkChangeAssignment();

        // 1.获取用户信息
        String userId = ContextHelper.getEmpNo();
        List<Employee> userInfo = HrClient.queryEmployeeInfo(Lists.newArrayList(userId));

        // 2.获取系统自动生成数据
        // (1) 任务名称(操作主题) + 任务编码(单据编号)
        mainAssignment.setAssignmentName(
                PropertyValueConvertUtil.getString(dataModel.getValue(ChangeOrderFieldConsts.OPERATION_SUBJECT)));
        mainAssignment.setAssignmentCode(
                PropertyValueConvertUtil.getString(dataModel.getValue(ChangeOrderFieldConsts.ORDER_NO)));

        // (2) 任务状态 + 当前进展是否审批任务
        switch (buttonId) {
            case BUTTON_SAVE_DRAFT_CID:
                mainAssignment.setAssignmentStatus(AssignmentStatusEnum.START.getValue());
                mainAssignment.setCurrentProgress(ApproveNodeEnum.DRAFT.name());
                mainAssignment.setApprovalTaskFlag(BoolEnum.N);
                break;

            case BUTTON_SAVE_APPROVE_DRAFT_CID:
                mainAssignment.setAssignmentStatus(AssignmentStatusEnum.APPROVE_START.getValue());
                mainAssignment.setCurrentProgress(ApproveNodeEnum.DRAFT.name());
                mainAssignment.setApprovalTaskFlag(BoolEnum.N);
                break;

            default:
                break;
        }

        // (3) 当前处理人
        mainAssignment.setCurrentProcessorEmployee(userInfo);

        // (4) 任务类型 + 单据类型
        mainAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE.getPropValue());
        mainAssignment.setBillType(BillTypeEnum.NETWORK_CHANGE.getPropValue());

        // 3.获取用户填报数据
        // (1) 责任单位
        // 网络变更操作单会选择到第四层组织架构，返回全路径
        // 营销获取第二层组织id, 代表处获取第四层组织id
        Pair<List<TextValuePair>, List<TextValuePair>> organizationInfo = getOrganizationInfo(dataModel);
        mainAssignment.setMarketing(organizationInfo.getLeft());
        mainAssignment.setRepresentativeOffice(organizationInfo.getRight());

        // (2) 产品分类
        // 网络变更操作单会选择到第四层产品分类，返回全路径
        // 产品经营团队选择第一层产品id，产品分类按全路径存储
        Pair<List<TextValuePair>, List<TextValuePair>> productInfo = getProductInfo(dataModel);
        mainAssignment.setProductManagementTeam(productInfo.getLeft());
        mainAssignment.setProductClassification(productInfo.getRight());

        // (3) 客户标识
        String customerClassification = PropertyValueConvertUtil.getString(dataModel.getValue(ACCN_TYPE_CID));
        mainAssignment.setCustomerClassification(TextValuePairHelper.buildList(customerClassification,
                customerClassification, customerClassification));

        // (4) 操作类型
        mainAssignment.setOperationType(
                AssignmentHelper.getLookupValueDropDownResult(
                        dataModel, ChangeOrderFieldConsts.OPERATION_TYPE, LookupValueConstant.OPERATE_TYPE_ENUM));

        // (5) 操作对象相关属性
        // 网络信息 / 局点名称
        mainAssignment.setNetwork(getNetworkInfo(dataModel));
        mainAssignment.setOfficeName(getOfficeNameInfo(dataModel));

        // (6) 责任人 + 公司
        mainAssignment.setResponsibleEmployee(Objects.isNull(existAssignment)
                ? userInfo
                : existAssignment.getResponsibleEmployee());
        mainAssignment.setCompany(AssignmentAbility.getAssignmentCompany(Lists.newArrayList(userId)));

        // (7) 计划开始时间
        mainAssignment.setPlanStartTime(
                ComponentUtils.getDateComponentInfo(dataModel, ChangeOrderFieldConsts.OPERATION_START_TIME));

        // (8) 国家/地区
        mainAssignment.setCountry(
                JsonUtils.parseArray(dataModel.getValue(ChangeOrderFieldConsts.COUNTRY), MultiLangText.class));

        // (9) 内部网络变更单类型
        mainAssignment.setType(null == existAssignment || null == existAssignment.getType()
                ? ChangeOrderTypeEnum.NORMAL.name()
                : existAssignment.getType());

        // (10) 操作原因
        mainAssignment.setOperationReason(
                AssignmentHelper.getLookupValueDropDownResult(
                        dataModel, ChangeOrderFieldConsts.OPERATION_REASON, LookupValueConstant.OPERATE_REASON_ENUM));

        // (11) 重要程度
        mainAssignment.setImportance(
                ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.IMPORTANCE));

        // (12) 风险评估
        mainAssignment.setRiskEvaluation(
                ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.RISK_EVALUATION));

        // (13) 操作等级
        mainAssignment.setOperationLevel(
                ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.OPERATION_LEVEL));

        // (14) 时区
        Object timeZoneObj = dataModel.getValue(CidConstants.TIME_ZONE_KEY);
        String timeZone = null;
        if (!ObjectUtils.isEmpty(timeZoneObj)) {
            Map<String, String> timeZoneMap = JsonUtils.parseObject(timeZoneObj, Map.class);
            timeZone = timeZoneMap.get(LookupValueConstant.LOOKUP_CODE);
            mainAssignment.setTimeZone(timeZone);
        }

        // (15) 操作开始时间（UTC+8）
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneEnum(timeZone);
        Date originTime = mainAssignment.getPlanStartTime();
        // 如果不是北京时间，需要根据时区转换成北京时间;否则，直接取用户输入的计划操作开始时间即可
        if (timeZoneEnum != null && TimeZoneEnum.BEIJING != timeZoneEnum) {
            mainAssignment.setOperationStartTimeUtc8(TimeZoneEnum.BEIJING.pollute(timeZoneEnum.fix(originTime)));
        } else {
            mainAssignment.setOperationStartTimeUtc8(originTime);
        }


        // 4.生成主任务
        if (Objects.isNull(existAssignment)) {
            // (1) 任务不存在，新增网络任务数据
            mainAssignment.setBillId(billId);
            mainAssignment.setEntityId(billId);
            String id = AssignmentAbility.insert(mainAssignment);
            mainAssignment.setId(id);
        } else {
            // (2) 任务存在，更新网络任务数据
            mainAssignment.setId(existAssignment.getId());
            AssignmentAbility.update(mainAssignment);
        }

        return mainAssignment;
    }

    /**
     * 获取组织信息
     * left - 营销   right - 代表处
     */
    public Pair<List<TextValuePair>, List<TextValuePair>> getOrganizationInfo(IDataModel dataModel) {
        // 1.获取组织信息
        List<TextValuePair> organizationInfo = ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.RESPONSIBLE_DEPT);
        if (CollectionUtils.isEmpty(organizationInfo)) {
            return Pair.of(Lists.newArrayList(), Lists.newArrayList());
        }

        // 2.组织信息拆分
        TextValuePair organization = organizationInfo.get(INTEGER_ZERO);
        List<String> hrOrgIdList = Lists.newArrayList(organization.getValue().split(FORWARD_SLASH));

        // 3.组织信息包装
        return Pair.of(
                TextValuePairHelper.buildList(hrOrgIdList.get(INTEGER_ONE), EMPTY_STRING, EMPTY_STRING),
                TextValuePairHelper.buildList(hrOrgIdList.get(INTEGER_THREE), EMPTY_STRING, EMPTY_STRING));
    }

    /**
     * 获取产品信息
     * left - 产品经营团队   right - 产品分类
     */
    public Pair<List<TextValuePair>, List<TextValuePair>> getProductInfo(IDataModel dataModel) {
        // 1.获取产品信息
        List<TextValuePair> productClassificationInfo = ComponentUtils.getChooseComponentInfo(dataModel, PRODUCT_CATEGORY);
        if (CollectionUtils.isEmpty(productClassificationInfo)) {
            return Pair.of(Lists.newArrayList(), Lists.newArrayList());
        }

        // 2.拆分产品信息
        List<TextValuePair> productManagementTeamList = Lists.newArrayList();
        List<String> productManagementTeamId = Lists.newArrayList();
        for (TextValuePair productClassification : productClassificationInfo) {
            String[] productIdArray = productClassification.getValue().split(FORWARD_SLASH);
            String productManagementTeam = productIdArray[INTEGER_ZERO];
            if (!productManagementTeamId.contains(productManagementTeam)) {
                productManagementTeamId.add(productManagementTeam);
                productManagementTeamList.addAll(TextValuePairHelper.buildList(productManagementTeam, EMPTY_STRING, EMPTY_STRING));
            }
        }

        // 3.包装产品信息
        return Pair.of(productManagementTeamList, productClassificationInfo);
    }

    /**
     * 操作对象信息 - 网络
     */
    public List<TextValuePair> getNetworkInfo(IDataModel dataModel) {
        // 1.获取操作对象子表单 - 网络信息列数据
        List<Object> networkInfo = dataModel.getEntryColumnObject(
                EntityHelper.getEntityId(OperationObject.class), NETWORK_ID);
        if (CollectionUtils.isEmpty(networkInfo)) {
            return Lists.newArrayList();
        }

        // 2.合并网络ID
        List<TextValuePair> networkIdProp = Lists.newArrayList();
        for (Object network : networkInfo) {
            if (Objects.isNull(network)) {
                continue;
            }

            DynamicDataEntity networkEntity = (DynamicDataEntity) network;
            networkIdProp.addAll(
                    TextValuePairHelper.buildList(networkEntity.getString(SERVICE_NETWORK_ID_CID), EMPTY_STRING, EMPTY_STRING));
        }

        return networkIdProp;
    }

    /**
     * 操作对象信息 - 局点名称
     */
    public List<TextValuePair> getOfficeNameInfo(IDataModel dataModel) {
        // 1.获取 操作对象子表单 - 局点名称 列数据
        List<Object> officeNameInfo = dataModel.getEntryColumnObject(
                EntityHelper.getEntityId(OperationObject.class), OperationObjectFieldConsts.OFFICE_NAME);
        if (CollectionUtils.isEmpty(officeNameInfo)) {
            return Lists.newArrayList();
        }

        // 2.合并局点名称
        List<TextValuePair> saveInfo = Lists.newArrayList();
        for (Object officeName : officeNameInfo) {
            if (Objects.isNull(officeName) || !StringUtils.hasText(officeName.toString())) {
                continue;
            }

            String officeNameStr = officeName.toString();
            saveInfo.addAll(TextValuePairHelper.buildList(officeNameStr, officeNameStr, officeNameStr));
        }

        return saveInfo;
    }

    /**
     * 额外操作 - 保存草稿
     *
     * @param assignment
     * @param billId
     */
    private void saveDraftAssignmentOperation(NetworkChangeAssignment assignment, String billId) {
        // 1.责任人授权
        List<String> responsible = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());
        String assignmentId = assignment.getId();
        AssignmentAbility.createAssignmentPersonRelevance(assignmentId, responsible);

        // 2.案例提示 - 我已知晓 置否
        ChangeOrder updateOrder = new ChangeOrder();
        updateOrder.setId(billId);
        updateOrder.setAwareSubmission(BoolEnum.N);
        updateOrder.setAwareSubmissionHidden(BoolEnum.N.getValue());

        ChangeOrderAbility.update(updateOrder);
    }

    /**
     * 额外操作 - 提交审批
     *
     * @param assignment
     * @param billId
     */
    private void submitAssignmentOperation(IDataModel dataModel, NetworkChangeAssignment assignment, String billId) {
        // 1.相关人授权
        // 授权范围：任务责任人 + 邮件知会人 + 操作人员
        // 审批人员在对应审批节点授权
        // (1) 任务责任人
        List<String> responsible = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());

        // (2) 邮件知会人
        List<Employee> emailNotifier = ComponentUtils.getEmployeeComponentInfo(dataModel, ChangeOrderFieldConsts.MAIL_COPY);
        List<String> emailNotifierId = emailNotifier.stream().map(Employee::getEmpUIID).collect(Collectors.toList());

        // (3) 操作人员
        List<Operator> operator = ChangeOrderAbility.listSpecificChangeOrderSubEntity(billId, Operator.class);
        List<String> operatorId = operator.stream().map(Operator::getOperatorEpmNo).collect(Collectors.toList());

        Set<String> relevanceSet = new HashSet<>(responsible);
        relevanceSet.addAll(emailNotifierId);
        relevanceSet.addAll(operatorId);
        AssignmentAbility.createAssignmentPersonRelevance(assignment.getId(), new ArrayList<>(relevanceSet));
    }

    private List<String> checkRequireFields() {
        if (!ConfigHelper.getBoolean(CHANGE_NETWORK_CHECK_ENABLE)) {
            return null;
        }
        List<String> nullFields = new ArrayList();
        REQUIRE_FIELDS.forEach(i -> {
            Object value = getModel().getValue(i);
            if (value == null || !StringUtils.hasText(value.toString()) || EMPTY_SET.equals(value.toString())) {
                nullFields.add(i);
            }
        });
        // 批次概要：批次号、计划操作开始时间、计划操作结束时间、网元数量
        nullFields.addAll(checkFromTwoFields(getModel(), BatchSummary.class, BATCH_REQUIRE_FIELDS));
        // 操作对象: 批次号
        nullFields.addAll(checkFromTwoFields(getModel(), OperationObject.class, OBJECT_REQUIRE_FIELDS));
        return nullFields;
    }

    /**
     * 二级子表单必填字段校验
     */
    private static List<String> checkFromTwoFields(IDataModel dataModel,
                                                   Class<? extends BaseSubEntity> entity,
                                                   List<String> requireFields) {
        List<String> nullFields = new ArrayList<>();
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(EntityHelper.getEntityId(entity));
        if (dataEntityCollection == null) {
            return nullFields;
        }

        for (int i = CommonConstants.INTEGER_ZERO; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            for (String v : requireFields) {
                Object value = dynamicDataEntity.get(v);
                if (value == null || !StringUtils.hasText(value.toString()) || EMPTY_SET.equals(value.toString())) {
                    nullFields.add(String.join(CommonConstants.EN_COLON, EntityHelper.getEntityId(entity), String.valueOf(i), v));
                }
            }

        }
        return nullFields;
    }
}
