package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.OperationTypeAttribute;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.vo.BatchTaskOperatorVO;
import com.zte.iccp.itech.extension.domain.model.vo.ChangeOrderSave;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.ConflictDTO;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.IdopChangeOrderDto;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.NetworkOfficeDTO;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.TimeConflictDTO;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.LookupValueVO;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.OperationTypeAttributeVO;
import com.zte.iccp.itech.extension.spi.client.CrmClient;
import com.zte.iccp.itech.extension.spi.client.EmdmClient;
import com.zte.iccp.itech.extension.spi.client.IdopClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.ProductClassificationDto;
import com.zte.iccp.itech.extension.spi.model.crm.BasicCustomerInfo;
import com.zte.iccp.itech.extension.spi.model.emdm.vo.EmdmAreaDetail;
import com.zte.iccp.itech.extension.spi.model.idop.*;
import com.zte.iccp.itech.extension.spi.model.nis.DeviceLocation;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.iccp.itech.extension.spi.model.nis.ProductClassification;
import com.zte.iccp.itech.extension.spi.model.query.NisNetworkQuery;
import com.zte.iccp.itech.zlic.ability.GrantFileWriteAbility;
import com.zte.iccp.itech.zlic.model.NeListInfo;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.component.util.BillNumberGenerateUtil;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.dto.BillNumberGeneratorDTO;
import com.zte.paas.lcap.ddm.domain.constant.CommonConstant;
import com.zte.paas.lcap.platform.domain.i18n.I18nUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.FIELD_IS_FIRST_APPLICATION_CID;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.LookupValueConstant.OPERATE_REASON_ENUM;
import static com.zte.iccp.itech.extension.domain.constant.LookupValueConstant.OPERATE_TYPE_ENUM;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CHANGE_ORDER_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CURRENT_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.PID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFieldConsts.*;

/**
 * <AUTHOR>
 * @create 2024/8/27 上午10:42
 */
@Slf4j
public class IdopAbility {

    /* IDOP 所属产品线CDOE*/
    private static final String IDOP_PRODUCT_LINE_CODE = "idop.product.line.code";

    /* 可编辑网络变更单状态*/
    private static final List<String> CAN_EDIT_CHANGE_ORDER_STATUS = Arrays.asList(
            AssignmentStatusEnum.START.getValue(), AssignmentStatusEnum.APPROVE_START.getValue());

    /* 可变更时间状态*/
    private static final List<String> CAN_UPDATE_TIME_ORDER_STATUS = Arrays.asList(
            AssignmentStatusEnum.START.getValue(), AssignmentStatusEnum.APPROVE_START.getValue(),
            AssignmentStatusEnum.APPROVE.getValue(), AssignmentStatusEnum.PENDING_NOTIFICATION.getValue(),
            AssignmentStatusEnum.SUSPENDED.getValue()
    );

    /*
     * idop查询操作类型原因下拉值
     * */
    public static List<OperationTypeAttributeVO> getOperationType(String productLine) {
        List<OperationTypeAttributeVO> result = new ArrayList<>();
        // NIS查询产品线详情
        ProductClassification product = NisAbility.getProductClassification(productLine);
        if(product == null){
            return new ArrayList<>();
        }
        List<String> fieldList = Lists.newArrayList(ID, OPERATE_TYPE, IS_CUSTOMIZE_OPERATION_REASON, OPERATE_REASON, OPERATE_TYPE_GROUP);
        List<IFilter> conditionFilters = Lists.newArrayList();
        String fullPath = product.getIdPath() + product.getId() + CommonConstants.FORWARD_SLASH;
        conditionFilters.add(new Filter(PRODUCT_LINE, Comparator.IN, Arrays.asList(fullPath)));
        conditionFilters.add(new Filter("multiselectfield_prod_operation_team", Comparator.IN, Arrays.asList(product.getIdPath())));
        List<OperationTypeAttribute> operationTypeAttributes = OperationTypeAbility.query(fieldList, conditionFilters);
        if (CollectionUtils.isEmpty(operationTypeAttributes)) {
            return null;
        }
        List<String> opertionTypes = operationTypeAttributes.stream()
                .map(OperationTypeAttribute::getOperateType)
                .collect(Collectors.toList());
        List<LookupValueVO> typeValues = getLookupValues(OPERATE_TYPE_ENUM, opertionTypes);
        Map<String, LookupValueVO> typeCodeMap = typeValues.stream()
                .collect(Collectors.toMap(LookupValueVO::getCode, Function.identity()));

        List<LookupValue> reasonValues = LookupValueHelper.getLookupValuesZhAndUs(OPERATE_REASON_ENUM, new ArrayList<>());
        List<LookupValueVO> reasonValueVOs = new ArrayList<>();
        reasonValues.forEach(item ->
                reasonValueVOs.add(new LookupValueVO(item.getLookupCode(), item.getMeaningCn(), item.getMeaningEn()))
        );
        Map<String, LookupValueVO> reasonCodeMap = reasonValueVOs.stream()
                .collect(Collectors.toMap(LookupValueVO::getCode, Function.identity()));

        Map<String, List<LookupValue>> typeReasonMap = reasonValues.stream()
                .collect(Collectors.groupingBy(LookupValue::getParentLookupCode));

        operationTypeAttributes.forEach(item -> {
            OperationTypeAttributeVO vo = new OperationTypeAttributeVO();
            vo.setCode(item.getOperateType());
            LookupValueVO typeValue = typeCodeMap.get(item.getOperateType());
            if (typeValue != null) {
                vo.setNameEn(typeValue.getNameEn());
                vo.setNameZh(typeValue.getNameZh());
            }
            List<LookupValueVO> reasons = new ArrayList<>();
            if (ENABLED_FLAG.equals(item.getIsCustomizOperationReason())
                    && !CollectionUtils.isEmpty(item.getOperateReason())) {
                List<String> operateReasons = item.getOperateReason();
                operateReasons.forEach(v ->
                        reasons.add(reasonCodeMap.get(v))
                );
            } else {
                List<LookupValue> operateReasons = new ArrayList<>();
                // 操作类型分组为升级
                if (UPGRADE_OPERATE_TYPE_GROUP.equals(item.getOperateTypeGroup())) {
                    // 只能选操作原因分组为升级类的操作原因
                    operateReasons = typeReasonMap.get(UPGRADE_OPERATE_REASON_GROUP);
                } else if (NETWORK_OPTIMIZATION_OPERATE_TYPE_GROUP.equals(item.getOperateTypeGroup())) {
                    // 只能选操作原因分组为网优类的操作原因
                    operateReasons = typeReasonMap.get(NETWORK_OPTIMIZATION_OPERATE_REASON_GROUP);
                } else {
                    // 剩余选择升级类和非升级类操作原因
                    operateReasons.addAll(typeReasonMap.get(UPGRADE_OPERATE_REASON_GROUP));
                    operateReasons.addAll(typeReasonMap.get(NON_UPGRADE_OPERATE_REASON_GROUP));
                }

                operateReasons.forEach(v ->
                        reasons.add(reasonCodeMap.get(v.getLookupCode()))
                );
            }
            vo.setOptReason(reasons);
            result.add(vo);
        });

        return result;
    }

    public static void batchUpdateConflict(List<ConflictDTO> conflictDTOS) {
        if (CollectionUtils.isEmpty(conflictDTOS)) {
            return;
        }

        Map<String, Boolean> orderIdConflictMap = conflictDTOS.stream().
                collect(Collectors.toMap(ConflictDTO::getChangeOrderId, ConflictDTO::getTimeConflict));
        List<String> orderIds = conflictDTOS.stream().map(ConflictDTO::getChangeOrderId).distinct().collect(Collectors.toList());
        List<BatchSummary> batchSummaries = BatchSummaryAbility.listBatchSummary(orderIds, Arrays.asList(ID, PID));
        if (CollectionUtils.isEmpty(batchSummaries)) {
            return;
        }
        batchSummaries.forEach(item ->
                item.setTimeConflict(BoolEnum.valueOf(orderIdConflictMap.get(item.getPid())).getValue())
        );
        TransactionHelper.run(() -> {
            SaveDataHelper.batchUpdate(batchSummaries);

            List<BatchTask> batchTasks = BatchTaskAbility.listBatchTasks(orderIds, Arrays.asList(ID, CHANGE_ORDER_ID));
            if (CollectionUtils.isEmpty(batchTasks)) {
                return;
            }
            batchTasks.forEach(item ->
                    item.setTimeConflict(BoolEnum.valueOf(orderIdConflictMap.get(item.getChangeOrderId())).getValue())
            );

            SaveDataHelper.batchUpdate(batchTasks);
        });
    }

    public static void batchUpdateConflict(TimeConflictDTO timeConflictDTO) {
        String changeStatus = getChangeStatus(timeConflictDTO.getChangeOrderId());
        if (!CAN_UPDATE_TIME_ORDER_STATUS.contains(changeStatus)) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(AssignmentStatusEnum.fromValue(changeStatus).getZhCn(), CHANGE_ORDER_CANNOT_UPDATE_TIME));
        }
        ChangeOrder changeOrder = ChangeOrderAbility.get(timeConflictDTO.getChangeOrderId(), Arrays.asList(ID));
        if (changeOrder == null) {
            throw new LcapBusiException(MsgUtils.getMessage(CHANGE_ORDER_ID_NOT_FIND));
        }

        List<String> changeOrderIds = Arrays.asList(timeConflictDTO.getChangeOrderId());
        TransactionHelper.run(() -> {
            if (timeConflictDTO.isConfirmTime()) {
                updateBatchTime(timeConflictDTO, changeOrderIds);
                return;
            }

            if (!AssignmentStatusEnum.SUSPENDED.getValue().equals(changeStatus)) {
                changeOrder.setOperationStartTime(timeConflictDTO.getOperationStartTime());
                changeOrder.setOperationEndTime(timeConflictDTO.getOperationEndTime());
                SaveDataHelper.update(changeOrder);
                AssignmentAbility.updatePlanStartTimeByEntityIds(Lists.newArrayList(changeOrder.getId()),
                        timeConflictDTO.getOperationStartTime());

                // 更新批次概要时间
                List<BatchSummary> batchSummaries = BatchSummaryAbility.listBatchSummary(changeOrderIds, Arrays.asList(ID, PID));
                if (CollectionUtils.isEmpty(batchSummaries)) {
                    return;
                }
                batchSummaries.forEach(item -> {
                            if (timeConflictDTO.getTimeConflict() != null) {
                                item.setTimeConflict(BoolEnum.valueOf(timeConflictDTO.getTimeConflict()).getValue());
                            }
                            item.setPlanOperationStartTime(timeConflictDTO.getOperationStartTime());
                            item.setPlanOperationEndTime(timeConflictDTO.getOperationEndTime());
                        }
                );
                SaveDataHelper.batchUpdate(batchSummaries);
                return;
            }

            // 更新批次任务时间
            updateBatchTime(timeConflictDTO, changeOrderIds);
        });
    }

    private static void updateBatchTime(TimeConflictDTO timeConflictDTO, List<String> changeOrderIds) {
        List<BatchTask> batchTasks = BatchTaskAbility.listBatchTasks(changeOrderIds, Arrays.asList(ID, CHANGE_ORDER_ID));
        if (CollectionUtils.isEmpty(batchTasks)) {
            return;
        }
        batchTasks.forEach(item -> {
                    if (timeConflictDTO.getTimeConflict() != null) {
                        item.setTimeConflict(BoolEnum.valueOf(timeConflictDTO.getTimeConflict()).getValue());
                    }
                    item.setPlanOperationStartTime(timeConflictDTO.getOperationStartTime());
                    item.setPlanOperationEndTime(timeConflictDTO.getOperationEndTime());
                }
        );

        AssignmentAbility.updatePlanStartTimeByEntityIds(batchTasks.stream().map(BatchTask::getId).collect(Collectors.toList()),
                timeConflictDTO.getOperationStartTime());
        SaveDataHelper.batchUpdate(batchTasks);
    }

    /**
     * idop 创建网络变更单
     */
    public static IdopChangeOrderDto createChangeOrder(IdopChangeOrderDto dto) {
        log.info("IdopAbility.createChangeOrder dto:{}", JsonUtils.toJsonString(dto));
        // 产品类型
        ProductClassificationDto productClass = getProductClassificationDto(dto.getProduct());
        // 组织代表处
        OrganizationTreeVo organization = getOrganizationTreeVo(dto.getDepartment());
        // 国家地区
        EmdmAreaDetail country = getCountry(dto.getCountry());
        // 客户名称
        BasicCustomerInfo basicCustomerInfo = getBasicCustomerInfo(dto.getCustomerName());
        // 操作类型
        LookupValue operateTypeValue = getLookupValues(OPERATE_TYPE_ENUM, dto.getOperationType());
        // 操作原因
        LookupValue operateReasonValue = getLookupValues(OPERATE_REASON_ENUM, dto.getOperationReason());
        ChangeOrderSave changeOrder = new ChangeOrderSave();
        if (StringUtils.hasText(dto.getCreateBy())) {
            RequestContextHolder.setEmpNo(dto.getCreateBy());
        }

        if (dto.getNeListInfo() != null) {
            changeOrder.setNeListFile(uploadToDocCloud(dto.getNeListInfo()));
        }
        BillNumberGeneratorDTO billNumberGeneratorDTO = new BillNumberGeneratorDTO();
        BeanUtils.copyProperties(dto, changeOrder);
        // 获取单号编码
        billNumberGeneratorDTO.setRuleCode("iTechCloud_OC_ticket_number");
        List<BillNumberGeneratorDTO.Param> params = new ArrayList<>();
        BillNumberGeneratorDTO.Param param = new BillNumberGeneratorDTO.Param();
        param.setCount(1);
        params.add(param);
        billNumberGeneratorDTO.setParams(params);
        List<List<String>> coNos = BillNumberGenerateUtil.generateBatch(billNumberGeneratorDTO);
        if (!CollectionUtils.isEmpty(coNos) && !CollectionUtils.isEmpty(coNos.get(0))) {
            changeOrder.setOrderNo(coNos.get(0).get(0));
            dto.setOrderNo(coNos.get(0).get(0));
        }

        // 创建网络变跟单
        changeOrder.setSource(DataSourceEnum.IDOP.name());
        changeOrder.setCustomerId(basicCustomerInfo.getCustNo());
        changeOrder.setOperationDesc(dto.getDescription());
        List<TextValuePair> responsibleDept = TextValuePairHelper
                .buildList(organization.getOrgIdPath(), organization.getHrOrgNamePath(), organization.getHrOrgNamePath());
        String productNameCn = String.join(CommonConstants.FORWARD_SLASH, productClass.getOperationTeamNameZh(), productClass.getProductLineNameZh(),
                productClass.getProductMainCategoryNameZh(), productClass.getProductSubCategoryNameZh());
        String productNameUs = String.join(CommonConstants.FORWARD_SLASH, productClass.getOperationTeamNameEn(), productClass.getProductLineNameEn(),
                productClass.getProductMainCategoryNameEn(), productClass.getProductSubCategoryNameEn());
        List<TextValuePair> productCategory = TextValuePairHelper
                .buildList(productClass.getSubCategoryFullPath(), productNameCn, productNameUs);
        List<TextValuePair> operateType = TextValuePairHelper
                .buildList(dto.getOperationType(), operateTypeValue.getMeaningCn(), operateTypeValue.getMeaningEn());
        List<TextValuePair> operateReason = TextValuePairHelper
                .buildList(dto.getOperationReason(), operateReasonValue.getMeaningCn(), operateReasonValue.getMeaningEn());
        MultiLangText countryValue = new MultiLangText();
        changeOrder.setIsGovEnt(BoolEnum.N);
        if (dto.getIsGovEnt() != null) {
            changeOrder.setIsGovEnt(BoolEnum.valueOf(dto.getIsGovEnt()));
        }
        changeOrder.setProductManagementTeam(TextValuePairHelper.buildList(productClass.getOperationTeamId(),
                productClass.getOperationTeamId(), productClass.getOperationTeamId()));
        countryValue.setValue(country.getAreaCode());
        countryValue.setZhCN(country.getAreaNameZh());
        countryValue.setEnUS(country.getAreaNameEn());
        changeOrder.setCountry(Arrays.asList(countryValue));
        changeOrder.setResponsibleDept(responsibleDept);
        changeOrder.setProductCategory(productCategory);
        changeOrder.setOperationType(operateType);
        changeOrder.setOperationTypeGroup(operateTypeValue.getParentLookupCode());
        changeOrder.setOperationReason(operateReason);
        changeOrder.setTimeZone(TimeZoneEnum.historyLookupCode2New(dto.getTimeZone()));
        changeOrder.setCustomerTypeFlag(CrmClient.getAccnType(basicCustomerInfo));
        changeOrder.setDeliveryMode(DeliveryModeEnum.fromValue(String.valueOf(dto.getDeliveryMode())));
        changeOrder.setIsGdpr(BoolEnum.valueOf(dto.getIsGdpr()));
        changeOrder.setIsEmergencyOperation(BoolEnum.valueOf(dto.getIsEmergencyOperation()));
        changeOrder.setIsFirstApplication(BoolEnum.valueOf(dto.getIsFirstApplication()));
        changeOrder.setIsNeedAuthorizationFile(BoolEnum.valueOf(dto.getIsNeedAuthorizationFile()));
        changeOrder.setGdprRequire(null);
        setProvinceAndCity(dto, changeOrder);
        if (dto.getGdprRequire() != null && dto.getGdprRequire()) {
            changeOrder.setGdprRequire(GgdprRequireEnum.GDPR_REQUIRE.getPropValue());
        }
        changeOrder.setCreateBy(null);
        String namePrefix = String.join(UNDER_SCORE, organization.getHrOrgName(), basicCustomerInfo.getCustName(),
                productClass.getProductSubCategoryNameZh(), operateTypeValue.getMeaningCn());
        String name = String.join(UNDER_SCORE, organization.getHrOrgName(), basicCustomerInfo.getCustName(),
                productClass.getProductSubCategoryNameZh(), operateTypeValue.getMeaningCn(), dto.getOperationSubjectSuffix());
        changeOrder.setOperationSubject(name);
        changeOrder.setOperationSubjectPrefix(namePrefix);
        // 创建子表单
        TransactionHelper.run(() -> {
        String changeOrderId = SaveDataHelper.create(changeOrder);
            dto.setChangeOrderId(changeOrderId);
            dto.setOrderName(name);
            ChangeOrderAbility.createChangeOrder(dto);
            ChangeOrderAbility.saveAssignmentTask(changeOrder, dto, null);
            TechnicalSolutionCheckAbility.saveCheckList(changeOrderId, productClass.getSubCategoryFullPath(), changeOrder.getOperationTypeGroup());
        });
        return dto;
    }

    private static void setProvinceAndCity(IdopChangeOrderDto dto, ChangeOrderSave changeOrder) {
        List<NetworkOfficeDTO> network = dto.getNetwork();
        String nisNetworkId = network.get(INTEGER_ZERO).getNetworkId();
        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();
        nisNetworkQuery.setNetworkIds(Lists.newArrayList(nisNetworkId));
        PageRows<NisNetwork> pageRows = NisClient.queryNisNetworkList(nisNetworkQuery);
        List<NisNetwork> list = pageRows.getRows();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        NisNetwork nisNetwork = list.get(INTEGER_ZERO);
        List<DeviceLocation> deviceLocations = nisNetwork.getDeviceLocations();
        if (CollectionUtils.isEmpty(deviceLocations)) {
            return;
        }

        DeviceLocation firstDeviceLocation = deviceLocations.get(CommonConstants.INTEGER_ZERO);
        String provinceCode = firstDeviceLocation.getProvinceStateCode();
        String cityCode = firstDeviceLocation.getCityCode();

        setProvince(provinceCode, changeOrder);
        setCity(cityCode, changeOrder);
    }

    private static void setProvince(String provinceCode, ChangeOrderSave changeOrder) {
        if (!StringUtils.hasText(provinceCode)) {
            return;
        }
        List<EmdmAreaDetail> provinces = EmdmClient.queryAreaDetailByCode(Lists.newArrayList(provinceCode));
        if (CollectionUtils.isEmpty(provinces)) {
            return;
        }
        MultiLangText provinceValue = new MultiLangText();
        provinceValue.setValue(provinceCode);
        provinceValue.setZhCN(provinces.get(INTEGER_ZERO).getAreaNameZh());
        provinceValue.setEnUS(provinces.get(INTEGER_ZERO).getAreaNameEn());

        changeOrder.setProvince(Arrays.asList(provinceValue));
    }

    private static void setCity(String cityCode, ChangeOrderSave changeOrder) {
        if (!StringUtils.hasText(cityCode)) {
            return;
        }
        List<EmdmAreaDetail> cities = EmdmClient.queryAreaDetailByCode(Lists.newArrayList(cityCode));
        if (CollectionUtils.isEmpty(cities)) {
            return;
        }
        MultiLangText cityValue = new MultiLangText();
        cityValue.setValue(cityCode);
        cityValue.setZhCN(cities.get(INTEGER_ZERO).getAreaNameZh());
        cityValue.setEnUS(cities.get(INTEGER_ZERO).getAreaNameEn());

        changeOrder.setCity(Arrays.asList(cityValue));
    }

    /**
     * idop 修改网络变更单
     */
    public static void updateChangeOrder(IdopChangeOrderDto dto) {
        log.info("IdopAbility.updateChangeOrder dto:{}", JsonUtils.toJsonString(dto));
        String changeStatus = getChangeStatus(dto.getChangeOrderId());
        if (!CAN_EDIT_CHANGE_ORDER_STATUS.contains(changeStatus)) {
            throw new LcapBusiException(MsgUtils.getMessage(CHANGE_ORDER_CANNOT_EDIT));
        }
        // 产品类型
        ProductClassificationDto productClass = getProductClassificationDto(dto.getProduct());
        // 国家地区
        EmdmAreaDetail country = getCountry(dto.getCountry());
        // 组织代表处
        OrganizationTreeVo organization = getOrganizationTreeVo(dto.getDepartment());
        // 客户名称
        BasicCustomerInfo basicCustomerInfo = getBasicCustomerInfo(dto.getCustomerName());
        // 操作类型
        LookupValue operateTypeValue = getLookupValues(OPERATE_TYPE_ENUM, dto.getOperationType());
        // 操作原因
        LookupValue operateReasonValue = getLookupValues(OPERATE_REASON_ENUM, dto.getOperationReason());
        ChangeOrderSave changeOrder = QueryDataHelper.queryOne(ChangeOrderSave.class,
                new ArrayList<>(), Arrays.asList(new Filter(ID, Comparator.EQ, dto.getChangeOrderId())));
        if (changeOrder == null) {
            throw new LcapBusiException(MsgUtils.getMessage(CHANGE_ORDER_ID_NOT_FIND));
        }

        if (StringUtils.hasText(dto.getCreateBy())) {
            RequestContextHolder.setEmpNo(dto.getCreateBy());
        }
        // 创建网络变跟单
        changeOrder.setOperationSubjectSuffix(dto.getOperationSubjectSuffix());
        changeOrder.setTimeZone(dto.getTimeZone());
        changeOrder.setOperationDesc(dto.getDescription());
        changeOrder.setCustomerId(basicCustomerInfo.getCustNo());
        changeOrder.setOperationStartTime(dto.getOperationStartTime());
        changeOrder.setOperationEndTime(dto.getOperationEndTime());
        List<TextValuePair> responsibleDept = TextValuePairHelper
                .buildList(organization.getOrgIdPath(), organization.getHrOrgNamePath(), organization.getHrOrgNamePath());
        String productNameCn = String.join(CommonConstants.FORWARD_SLASH, productClass.getOperationTeamNameZh(), productClass.getProductLineNameZh(),
                productClass.getProductMainCategoryNameZh(), productClass.getProductSubCategoryNameZh());
        String productNameUs = String.join(CommonConstants.FORWARD_SLASH, productClass.getOperationTeamNameEn(), productClass.getProductLineNameEn(),
                productClass.getProductMainCategoryNameEn(), productClass.getProductSubCategoryNameEn());
        List<TextValuePair> productCategory = TextValuePairHelper
                .buildList(productClass.getSubCategoryFullPath(), productNameCn, productNameUs);
        List<TextValuePair> operateType = TextValuePairHelper
                .buildList(dto.getOperationType(), operateTypeValue.getMeaningCn(), operateTypeValue.getMeaningEn());
        List<TextValuePair> operateReason = TextValuePairHelper
                .buildList(dto.getOperationReason(), operateReasonValue.getMeaningCn(), operateReasonValue.getMeaningEn());
        MultiLangText countryValue = new MultiLangText();
        if (dto.getIsGovEnt() != null) {
            changeOrder.setIsGovEnt(BoolEnum.valueOf(dto.getIsGovEnt()));
        }
        changeOrder.setProductManagementTeam(TextValuePairHelper.buildList(productClass.getOperationTeamId(),
                productClass.getOperationTeamId(), productClass.getOperationTeamId()));
        countryValue.setValue(country.getAreaCode());
        countryValue.setZhCN(country.getAreaNameZh());
        countryValue.setEnUS(country.getAreaNameEn());
        changeOrder.setCountry(Arrays.asList(countryValue));
        changeOrder.setResponsibleDept(responsibleDept);
        changeOrder.setProductCategory(productCategory);
        changeOrder.setOperationType(operateType);
        changeOrder.setOperationTypeGroup(operateTypeValue.getParentLookupCode());
        changeOrder.setOperationReason(operateReason);
        changeOrder.setCustomerTypeFlag(CrmClient.getAccnType(basicCustomerInfo));
        changeOrder.setDeliveryMode(DeliveryModeEnum.fromValue(String.valueOf(dto.getDeliveryMode())));
        changeOrder.setIsGdpr(BoolEnum.valueOf(dto.getIsGdpr()));
        changeOrder.setIsEmergencyOperation(BoolEnum.valueOf(dto.getIsEmergencyOperation()));
        changeOrder.setIsFirstApplication(BoolEnum.valueOf(dto.getIsFirstApplication()));
        changeOrder.setIsNeedAuthorizationFile(BoolEnum.valueOf(dto.getIsNeedAuthorizationFile()));
        if (dto.getGdprRequire() != null && dto.getGdprRequire()) {
            changeOrder.setGdprRequire(GgdprRequireEnum.GDPR_REQUIRE.getPropValue());
        }
        changeOrder.clearEntityValue();
        // 网元清单不为空，上传云文档，构建文档对象
        if (dto.getNeListInfo() != null) {
            changeOrder.setNeListFile(uploadToDocCloud(dto.getNeListInfo()));
        }
        // 创建子表单
        String namePrefix = String.join(UNDER_SCORE, organization.getHrOrgName(), basicCustomerInfo.getCustName(),
                productClass.getProductSubCategoryNameZh(), operateTypeValue.getMeaningCn());
        String name = String.join(UNDER_SCORE, organization.getHrOrgName(), basicCustomerInfo.getCustName(),
                productClass.getProductSubCategoryNameZh(), operateTypeValue.getMeaningCn(), dto.getOperationSubjectSuffix());
        dto.setOrderName(name);
        changeOrder.setOperationSubjectPrefix(namePrefix);
        changeOrder.setOperationSubject(name);
        setProvinceAndCity(dto, changeOrder);
        TransactionHelper.run(() -> {
            SaveDataHelper.update(changeOrder);
            ChangeOrderAbility.updateChangeOrder(dto);
            TechnicalSolutionCheckAbility.saveCheckList(dto.getChangeOrderId(), productClass.getSubCategoryFullPath(),
                    changeOrder.getOperationTypeGroup());
            // 更新任务表
            Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                    dto.getChangeOrderId(), AssignmentTypeEnum.NETWORK_CHANGE, Assignment.class);
            if (assignment == null) {
                return;
            }
            ChangeOrderAbility.saveAssignmentTask(changeOrder, dto, assignment.getId());
        });
    }

    /**
     * 上传云文档
     *
     * @param neListInfo neListInfo
     * @return 文档云id
     */
    @SneakyThrows(IOException.class)
    private static AttachmentFile uploadToDocCloud(NeListInfo neListInfo) {
        String networkFileName = I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), IDOP_NETWORK_FILE_NAME);
        networkFileName = networkFileName + XLSX;
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try (Workbook workbook = new XSSFWorkbook()) {
            GrantFileWriteAbility.write(neListInfo, workbook);
            workbook.write(os);
        }
        String docId = CloudDiskHelper.upload(networkFileName, os.toByteArray());
        return AttachmentFileHelper.buildAttachmentFile(docId, networkFileName);
    }

    /*
    * 批次提交审批后更新idop接口
    * */
    public static void updateConfirmOptTime(BatchTask batchTask) {
        ConfirmOptBatchVO params = new ConfirmOptBatchVO();
        params.setConfirmBeginTime(DateUtils.dateToString(batchTask.getPlanOperationStartTime(), "yyyy-MM-dd HH:mm:ss"));
        params.setConfirmEndTime(DateUtils.dateToString(batchTask.getPlanOperationEndTime(), "yyyy-MM-dd HH:mm:ss"));
        params.setChangeOrderId(batchTask.getChangeOrderId());
        params.setStatus(batchTask.getCurrentStatus());
        params.setEmergency(BoolEnum.valueOf(batchTask.getUrgentFlag()));
        IdopClient.updateConfirmOptTime(params);
    }

    /*
     * 变更单任务(内部：变更单+批次) 取消发送IDOP
     * */
    public static void taskCancelSendIdop(String id, ApproveFlowCodeEnum approveFlowCode, IdopCancelEnum cancelEnum) {
        if (!StringUtils.hasText(id) || approveFlowCode == null || cancelEnum == null) {
            return;
        }
        // 变更单+批次
        if (ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW != approveFlowCode &&
                ApproveFlowCodeEnum.BATCH_TASK_FLOW != approveFlowCode) {
            return;
        }

        ChangeOrder changeOrder;
        if (ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW == approveFlowCode) {
            changeOrder = ChangeOrderAbility.get(id, Arrays.asList(ID, SOURCE));
        } else {
            BatchTask batchTask = BatchTaskAbility.get(id, Arrays.asList(ID, CHANGE_ORDER_ID));
            changeOrder = ChangeOrderAbility.get(batchTask.getChangeOrderId(), Arrays.asList(ID, SOURCE));
        }
        // 来源不是idop 不需要更新
        if (changeOrder == null || !DataSourceEnum.IDOP.name().equals(changeOrder.getSource())) {
            return;
        }

        // 将状态取消状态发送给idop
        CancelChangeVO param = new CancelChangeVO();
        OrderPlanVO planVO = new OrderPlanVO();
        planVO.setChangeOrderId(changeOrder.getId());
        if (IdopCancelEnum.DELETE != cancelEnum) {
            planVO.setStatus(getChangeStatus(changeOrder.getId()));
        }
        param.setChangeOrderIds(Arrays.asList(planVO));
        param.setOperation(cancelEnum.getValue());
        IdopClient.updatePlanStatus(param);
    }

    /*更新网络变更单提交IDOP*/
    public static void updateIdopChangeOrder(String id) {
        List<String> flieds = Arrays.asList(ID, SOURCE, OPERATION_SUBJECT_SUFFIX, IS_GDPR, FIELD_IS_FIRST_APPLICATION_CID,
                DELIVERY_MODE, OPERATION_REASON, TIME_ZONE, GDPR_REQUIRE, OPERATION_START_TIME, OPERATION_END_TIME, ORDER_NO);
        ChangeOrder changeOrder = ChangeOrderAbility.get(id, flieds);
        if (changeOrder == null || !DataSourceEnum.IDOP.name().equals(changeOrder.getSource())) {
            return;
        }
        UpdateChangeOrderVO param = new UpdateChangeOrderVO();
        param.setChangeOrderId(id);
        param.setChangeOrderIdDisplay(changeOrder.getOrderNo());
        param.setStatus(AssignmentStatusEnum.APPROVE.getValue());
        param.setTimeZone(changeOrder.getTimeZone().getLookupCode());
        param.setOperationSubjectSuffix(changeOrder.getOperationSubjectSuffix());
        param.setIsGdpr(BoolEnum.valueOf(changeOrder.getIsGdpr()));
        param.setGdprRequire(changeOrder.getGdprRequire() == null ? false : true);
        param.setDeliveryMode(Integer.parseInt(changeOrder.getDeliveryMode().getValue()));
        if (StringUtils.hasText(changeOrder.getOperationReason())) {
            LookupValue lookupValues = getLookupValues(OPERATE_REASON_ENUM, changeOrder.getOperationReason());
            param.setOperationReason(new ReasonValueVO(lookupValues.getLookupCode(), lookupValues.getMeaningCn(), lookupValues.getMeaningEn()));
        }
        List<BatchSummary> batchSummaries = BatchSummaryAbility.listBatchSummary(id, BatchSummary.class);
        if (!CollectionUtils.isEmpty(batchSummaries)) {
            OptBatch optBatch = new OptBatch();
            optBatch.setOptBeginTime(batchSummaries.get(0).getPlanOperationStartTime());
            optBatch.setOptEndTime(batchSummaries.get(0).getPlanOperationEndTime());
            param.setOptBatch(optBatch);
        }
        List<String> operators = OperatorAbility.getOperator(id).stream()
                .filter(item -> OperatorRoleEnum.OPERATOR == item.getOperatorRole())
                .map(BatchTaskOperatorVO::getOperatePerson)
                .map(SingleEmployee::getEmpUIID)
                .collect(Collectors.toList());
        param.setOperators(operators);
        Date flowCreateDate = FlowHelper.getFlowCreateDate(id);
        param.setSubmitTime(flowCreateDate);
        IdopClient.updateSubmittedPlan(param);
    }

    /*
     * 查快码详情
     */
    private static LookupValue getLookupValues(String lookUpType, String code) {
        List<LookupValue> lookupValues = LookupValueHelper.getLookupValuesZhAndUs(lookUpType, Arrays.asList(code));
        if (CollectionUtils.isEmpty(lookupValues)) {
            if (OPERATE_TYPE_ENUM.equals(lookUpType)) {
                throw new LcapBusiException(MsgUtils.getTypedMessage(code, OPERATE_TYPE_DATA_NOT_FIND));
            } else {
                throw new LcapBusiException(MsgUtils.getTypedMessage(code, OPERATE_REASON_DATA_NOT_FIND));
            }
        }
        return lookupValues.get(0);
    }

    public static void checkNisNetwork(List<String> networkIds) {
        Map<String, String> networkMap = NisAbility.queryNisNetworkMap(networkIds);
        StringBuilder notFindIds = new StringBuilder();
        networkIds.forEach(i->{
            if(!StringUtils.hasText(networkMap.get(i))) {
                notFindIds.append(CommonConstant.COMMA).append(i);
            }
        });
        if(!StringUtils.hasText(notFindIds)){
            return;
        }

        throw new LcapBusiException(MsgUtils.getTypedMessage(notFindIds.substring(1, notFindIds.length()), NETWORK_ID_NOT_FIND_IN_NIS));
    }

    /**
     * 获取 客户名称信息
     * @param customerName
     * @return
     */
    private static BasicCustomerInfo getBasicCustomerInfo(String customerName) {
        List<String> customerNames = StringUtils.hasText(customerName) ? Arrays.asList(customerName) : null;
        Map<String, BasicCustomerInfo> customerNameMap = CrmClient.queryBasicCustomerInfo(customerNames);
        BasicCustomerInfo basicCustomerInfo = customerNameMap.get(customerName);
        if (basicCustomerInfo == null) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(customerName, CUSTOMER_NAME_DATA_NOT_FIND));
        }
        return basicCustomerInfo;
    }

    /**
     * 获取 组织结构--代表处
     * @param department
     * @return
     */
    private static OrganizationTreeVo getOrganizationTreeVo(String department) {
        OrganizationTreeVo organization = NisAbility.getOrganization(department);
        if (organization == null) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(department, DEPARTMENT_DATA_NOT_FIND));
        }
        return organization;
    }

    /**
     * 获取 产品小类详情
     * @param productName
     * @return
     */
    @NotNull
    private static ProductClassificationDto getProductClassificationDto(String productName) {
        List<String> productNames = new ArrayList<>();
        productNames.add(productName);
        Map<String, ProductClassificationDto> productModelMap =
                NisAbility.getProductModelMap(productNames, getIdopProductLineCode());
        ProductClassificationDto productClass = productModelMap.get(productName);
        if (productClass == null) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(productName, PRODUCT_DATA_NOT_FIND));
        }
        return productClass;
    }

    @NotNull
    private static List<LookupValueVO> getLookupValues(String type, List<String> lookupCodes) {
        List<LookupValueVO> result = new ArrayList<>();
        List<LookupValue> typeValues = LookupValueHelper.getLookupValuesZhAndUs(type, lookupCodes);
        typeValues.forEach(item ->
                result.add(new LookupValueVO(item.getLookupCode(), item.getMeaningCn(), item.getMeaningEn()))
        );
        return result;
    }

    /*
     * 查询变更单当前状态
     */
    private static String getChangeStatus(String changeOrderId) {
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, Assignment.class);
        if(assignment == null ){
            throw new LcapBusiException(MsgUtils.getMessage(CHANGE_ORDER_ID_NOT_FIND));
        }
        // 到了发布通告环节
        if (AssignmentStatusEnum.EXECUTE.getValue().equals(assignment.getAssignmentStatus())) {
            BatchTask batchTasks = getBatchTaskByChangeOrderId(changeOrderId, Arrays.asList(ID, CURRENT_STATUS));
            return batchTasks == null ? "" : batchTasks.getCurrentStatus();
        }

        return assignment.getAssignmentStatus();
    }

    private static BatchTask getBatchTaskByChangeOrderId(String changeOrderId, List<String> fields) {
        List<BatchTask> batchTasks = BatchTaskAbility.listBatchTaskByChangeOrderId(changeOrderId, fields);
        if (CollectionUtils.isEmpty(batchTasks)) {
            return null;
        }
        return batchTasks.get(0);
    }

    private static List<String> getIdopProductLineCode() {
        return Arrays.asList(ConfigHelper.get(IDOP_PRODUCT_LINE_CODE).split(CommonConstants.COMMA));
    }

    /**
     * 根据名称查询国家
     * @param country
     * @return
     */
    private static EmdmAreaDetail getCountry(String country) {
        List<EmdmAreaDetail> countrys = EmdmClient.queryAreaDetailByName(country, EmdmClient.COUNTRY_CODE);
        if (CollectionUtils.isEmpty(countrys)) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(country, COUNTRY_DATA_NOT_FIND));
        }
        return countrys.get(0);
    }


    /**
     * 与idop同步状态
     *
     * @param changeOrderId 单据id
     * @param assignmentTypeEnum 任务类型
     * @param syncAssignmentStatusEnum 同步的任务状态
     * @param syncIdopEnum 同步idop类型
     */
    public static void syncIdopStatus(String changeOrderId,
                                      AssignmentTypeEnum assignmentTypeEnum,
                                      AssignmentStatusEnum syncAssignmentStatusEnum,
                                      IdopCancelEnum syncIdopEnum) {
        Class<? extends BaseEntity> changeOrderEntityClass = assignmentTypeEnum.getApproveFlowCodeEnum().getFlowEntity();
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                changeOrderEntityClass, Lists.newArrayList(ID, SOURCE), changeOrderId);

        if (changeOrder.getSource() != null
                && DataSourceEnum.IDOP.name().equals(changeOrder.getSource())) {
            IdopClient.updatePlanStatus(new CancelChangeVO() {{
                setChangeOrderIds(Lists.newArrayList(new OrderPlanVO() {{
                    setChangeOrderId(changeOrder.getId());
                    setStatus(syncAssignmentStatusEnum.getValue());
                }}));
                setOperation(syncIdopEnum.getValue());
            }});
        }
    }
}
