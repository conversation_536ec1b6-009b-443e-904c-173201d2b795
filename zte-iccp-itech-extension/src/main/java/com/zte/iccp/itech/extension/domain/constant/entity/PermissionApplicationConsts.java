package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/9 下午6:29
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PermissionApplicationConsts {

    public static final String MODULE_NAME = "module_name";

    public static final String ROLE_NAME = "role_name";

    public static final String PRODUCT = "product";

    public static final String CROSS_PARTNER_PERMISSION = "cross_partner_permission";

    public static final String EXPIRATION_TIME = "expiration_time";

    public static final String DIRECT_MANAGEMENT_LEADER = "direct_management_leader";

    public static final String REPRESENTATIVE_OFFICE_GROUP_MANAGER = "representative_office_group_manager";

    public static final String PRODUCT_TECHNOLOGY_SECTION_CHIEF = "product_technology_section_chief";

    public static final String APPLICATION_REASON = "application_reason";

    public static final String FILE = "file";

    public static final String STATUS = "status";

    public static final String BILL_NO = "bill_no";

    public static final String BILL_NAME = "bill_name";

    public static final String ORGANIZATION_HZS = "organization_hzs";

    public static final String TYPE = "type";

    public static final String ORGANIZATIONS = "organizations";

    public static final String APPLICANT = "applicant";

    public static final String APPLICATION_TIME = "application_time";

    public static final String ZHONG_ZHI_HL = "zhong_zhi_high_lease";

}
