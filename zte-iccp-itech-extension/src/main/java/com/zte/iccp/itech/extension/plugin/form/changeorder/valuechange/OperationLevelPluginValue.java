package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.GradingGuaranteeAbility;
import com.zte.iccp.itech.extension.ability.NetworkConfigurationAbility;
import com.zte.iccp.itech.extension.ability.OperationTypeAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.FormModelProxyHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.OptionBuildUtils;
import com.zte.iccp.itech.extension.common.utils.PageStatusEnumUtil;
import com.zte.iccp.itech.extension.domain.enums.ImportanceEnum;
import com.zte.iccp.itech.extension.domain.model.NetworkConfiguration;
import com.zte.iccp.itech.extension.domain.model.OperationTypeAttribute;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.spi.model.crm.SvcCustomerInfo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ProductTypeFactorConsts.SCORE;

/**
 * 触发时机：网络变更单页面上的业务字段填写时，触发字段包括：产品分类，代表处，是否政企，操作类型，客户，国家，网络名称，产品型号
 * 插件功能：当变更单中字段填写时触发【操作等级】字段自动计算
 * <AUTHOR> 10335201
 * @date 2024-05-13 下午4:12
 **/
@AllArgsConstructor
public class OperationLevelPluginValue implements ValueChangeBaseFormPlugin {

    private boolean isPlan;

    /**
     * 编辑页面初始化时触发
     * @param args
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/7/31 下午5:22
     */
    @Override
    public void loadBillTableData(LoadBillTableDataEventArgs args) {
        if (!COMPONENT_OPERATION_OBJECT_ORDER_CID.equals(args.getTableCid())) {
            return;
        }
        IDataModel dataModel = args.getModel();
        IFormView formView = args.getFormView();
        String pageStatus = formView.getFormShowParameter().getPageStatus().getValue();
        if (!PageStatusEnumUtil.isNew(pageStatus)) {
            return;
        }
        invoke(dataModel, formView);
    }

    /**
     * 新增页面数据change时触发
     * @param args
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/7/31 下午5:22
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        IDataModel dataModel = args.getModel();
        IFormView formView = args.getFormView();
        invoke(dataModel, formView);
    }

    private void invoke(IDataModel dataModel, IFormView formView) {
        // 如果页面是查看态
        String pageStatus = formView.getFormShowParameter().getPageStatus().getValue();
        if (PageStatusEnum.VIEW.getValue().equals(pageStatus)) {
            formView.getClientViewProxy().setControlState(COMPONENT_OPERATION_LEVEL_COMPONENT_CID, new PageStatusAttributeBuilder().readOnly().build());
            return;
        }

        // 主表单--责任单位
        String organizationIdPath = getOrganizationIdPath(dataModel);

        // 主表单--产品分类
        String productIdPath = getProductIdPath(dataModel);

        // 主表单--是否政企
        String isGovernmentEnterprise = getIsGovernmentEnterprise(dataModel);

        // 主表单--是否特殊场景
        String isSpecialScenario = getIsSpecialScenario(dataModel);

        // 主表单--是否首次操作
        String isFirstApplication = getIsFirstApplication(dataModel);

        // 主表单--操作类型
        String operationType = getOperationType(dataModel);

        // 主表单--客户
        String customerId;

        // 主表单--国家
        String countryCode = FormModelProxyHelper.getValueLangFirstValue(FIELD_COUNTRY_CID,OPERATION_OBJECT_TABLE_PROPERTY_KEY, dataModel);
        if (isPlan) {
            customerId = FormModelProxyHelper.getValueLangFirstValue(FIELD_CUSTOMER_NAME_CID,OPERATION_OBJECT_TABLE_PROPERTY_KEY, dataModel);
        } else {
            customerId = getCustomerId(dataModel);
        }

        // 主表单--业务中断时长
        String serviceDisconnectDuration = PropertyValueConvertUtil.getString(dataModel.getValue(FIELD_SERVICE_DISCONNECT_DURATION_CID));

        // 主表单--操作类型分组
        String operationTypeGroup = PropertyValueConvertUtil.getString(dataModel.getValue(FIELD_OPERATION_TYPE_GROUP_CID));

        // 操作对象子表单--产品类型id
        List<String> productModelIds = FormModelProxyHelper.getSubFormServiceColumnValueList(dataModel, OPERATION_OBJECT_TABLE_PROPERTY_KEY, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, SERVICE_PRODUCT_MODEL_FULL_ID_PATH_CID);

        // 操作对象子表单--网络id
        List<String> networkIds = FormModelProxyHelper.getSubFormServiceColumnValueList(dataModel, OPERATION_OBJECT_TABLE_PROPERTY_KEY, FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, SERVICE_NETWORK_ID_CID);

        // 产品分类，代表处没填完之前，不触发自动计算
        if (StringUtils.isBlank(organizationIdPath) || StringUtils.isBlank(productIdPath)) {
            return;
        }

        // 提取产品系数、局点系数、特殊场景系数、业务中断时长系数、操作类型系数、网络系数
        List<String> productOperationTeamList = Lists.newArrayList(productIdPath.split(FORWARD_SLASH)[INTEGER_ZERO]+ FORWARD_SLASH);
        List<String> productSubcategoryList = Lists.newArrayList(productIdPath);
        double isSpecialScenarioFactor = ENABLED_FLAG.equals(isSpecialScenario) ? DOUBLE_THREE_QUARTERS : INTEGER_ONE;
        double siteFactor = GradingGuaranteeAbility.getLocalFactor(countryCode, customerId, Lists.newArrayList(SCORE));
        double productFactor = GradingGuaranteeAbility.getProductTypeFactor(productModelIds, productSubcategoryList,Lists.newArrayList(SCORE));
        double businessInterruptionDurationFactor = GradingGuaranteeAbility.queryBusinessInterruptionDurationFactor(serviceDisconnectDuration,productOperationTeamList);
        double operationTypeFactor = GradingGuaranteeAbility.queryOperationTypeFactor(productSubcategoryList,operationType);
        // 网络系数
        List<NetworkConfiguration> networkConfigurationList = NetworkConfigurationAbility.queryByNetworkId(networkIds);
        double networkFactor = networkConfigurationList.stream()
                .filter(a -> !CollectionUtils.isEmpty(a.getGradeScore()))
                .mapToDouble(attribute -> Double.parseDouble(attribute.getGradeScore().get(INTEGER_ZERO).getValue()))
                .max()
                .orElse(ImportanceEnum.STAR1.getIntegerValue());

        // 风险评估、操作等级最终结果，0：代表用户自动选择（默认），1：一般；2：重要；3：关键
        double operationLevelResult = INTEGER_ZERO;


        // 先根据不同的责任单位来拆分业务场景，
        // 1、（（责任单位属于【国内营销】||【工程服务经营部-工程服务三部】）&&【是否政企】选择是）|| 工程服务经营部/非工程服务国内部
        // 2、（责任单位属于【国内营销】||【工程服务经营部-工程服务三部】）&&【是否政企】选择否
        // 3、其他


        // 抽取判断条件：责任单位属于【国内营销】||【工程服务经营部-工程服务国内部】
        boolean organizationFlag = organizationIdPath.startsWith(ConfigHelper.get(DOMESTIC_SALES_ORG_CODE_PATH)) || organizationIdPath.startsWith(ConfigHelper.get(ENGINEERING_SERVICE_DEPT_DOMESTIC_ORG_CODE_PATH));
        // 抽取判断条件：责任单位属于工程服务经营部-非工程服务国内部（是工程服务经营部下面除了工程服务国内部其他的责任单位）
        boolean organizationFlag1 = organizationIdPath.startsWith(ConfigHelper.get(ENGINEERING_SERVICE_DEPT_ORG_CODE_PATH)) && !organizationIdPath.startsWith(ConfigHelper.get(ENGINEERING_SERVICE_DEPT_DOMESTIC_ORG_CODE_PATH));

        if ((organizationFlag && ENABLED_FLAG.equals(isGovernmentEnterprise)) || organizationFlag1) {
            operationLevelResult = getOperationLevelResult(productIdPath, productFactor, siteFactor, isSpecialScenarioFactor, businessInterruptionDurationFactor, operationTypeFactor, operationLevelResult);
        } else if (organizationFlag && DISABLED_FLAG.equals(isGovernmentEnterprise)) {
            operationLevelResult = getOperationLevelResult(isFirstApplication, operationTypeGroup, productIdPath, productFactor, networkFactor, siteFactor, isSpecialScenarioFactor, businessInterruptionDurationFactor, operationTypeFactor, operationLevelResult);
        }
        // 根据result数据给操作等级、风险评估字段赋值或保持用户手动填写
        setValueAndStatus(dataModel, formView, operationLevelResult, productIdPath, operationType);
    }

    private void setValueAndStatus(IDataModel dataModel,IFormView formView, double operationLevelResult,String productIdPath,String operationType) {

        // 满足自动计算条件，result不是默认值0，则给【操作等级】赋值并且设置为禁用组件
        if (operationLevelResult != DOUBLE_ZERO) {
            double operationLevel = operationLevelResult > DOUBLE_THREE ? DOUBLE_THREE : operationLevelResult;
            String levelStr = String.valueOf((int) Math.floor(operationLevel));
            dataModel.setValue(FIELD_OPERATION_LEVEL_CID, OptionBuildUtils.getLevel(levelStr));
            formView.getClientViewProxy().setControlState(COMPONENT_OPERATION_LEVEL_COMPONENT_CID, new PageStatusAttributeBuilder().disable().build());
            return;
        }

        // 不满足自动计算条件，去选项配置里查询当前操作类型的风险评估默认值进行赋值，如果没有默认值则赋空值。并且设置为普通组件，让用户可以手动选择（产品线+操作类型为联合主键）
        if(StringUtils.isBlank(operationType)){
            dataModel.setValue(FIELD_OPERATION_LEVEL_CID,new OptionsBuilder().build());
            formView.getClientViewProxy().setControlState(COMPONENT_OPERATION_LEVEL_COMPONENT_CID, new PageStatusAttributeBuilder().normal().build());
            return;
        }

        List<String> fieldList = Lists.newArrayList(RISK_ASSESSMENT,OPERATE_LEVEL,IMPORTANCE_LEVEL);
        List<IFilter> conditionFilters = Lists.newArrayList();
        List<String> productLines = Lists.newArrayList(productIdPath.split(FORWARD_SLASH)[INTEGER_ZERO] + FORWARD_SLASH + productIdPath.split(FORWARD_SLASH)[INTEGER_ONE] + FORWARD_SLASH);
        conditionFilters.add(new Filter(PRODUCT_LINE, Comparator.IN, productLines));
        conditionFilters.add(new Filter(OPERATE_TYPE, Comparator.EQ, operationType));
        List<OperationTypeAttribute> operationTypeAttributes = OperationTypeAbility.query(fieldList, conditionFilters);
        if (!CollectionUtils.isEmpty(operationTypeAttributes)
                && !CollectionUtils.isEmpty(operationTypeAttributes.get(INTEGER_ZERO).getOperateLevel())) {
            List<TextValuePair> operateLevel = operationTypeAttributes.get(INTEGER_ZERO).getOperateLevel();
            String val = operateLevel.get(INTEGER_ZERO).getValue();
            dataModel.setValue(FIELD_OPERATION_LEVEL_CID, OptionBuildUtils.getLevel(val));
        }
        formView.getClientViewProxy().setControlState(COMPONENT_OPERATION_LEVEL_COMPONENT_CID, new PageStatusAttributeBuilder().normal().build());

    }



    /**
     *   自动计算操作等级
     *   1、产品分类属于【算力及核心网】：a、操作类型分类属于配置保障：一般；b、【是否首次应用】选项选择是：关键；c、其他条件下：（网络系数*产品系数+操作类型系数）/2 向下取整
     *   2、产品分类属于【承载网】或【固网及多媒体】：a、操作类型分类属于配置保障：一般；b、【是否首次应用】选项选择是：关键；c、其他条件下：（网络系数*产品系数+操作类型系数*中断时长系数 * （if 特殊场景=Y then 1.5 else 1 end））/2 向下取整
     *   3、产品分类属于【视频-多媒体视讯系统】：（局点系数*产品系数+操作类型系数*中断时长系数 * （if 特殊场景=Y then 1.5 else 1 end））/2 向下取整
     *   4、产品分类属于其他：手动选择
     */
    private double getOperationLevelResult(
            String isFirstApplication,
            String operationTypeGroup,
            String productIdPath,
            double productFactor,
            double networkFactor,
            double siteFactor,
            double isSpecialScenarioFactor,
            double businessInterruptionDurationFactor,
            double operationTypeFactor,
            double operationLevelResult) {
        if(productIdPath.startsWith(ConfigHelper.get(CCN_PROD_ID_PATH))){
            operationLevelResult = getOperationLevelResult(isFirstApplication, operationTypeGroup, productFactor, networkFactor, operationTypeFactor);
        } else if (productIdPath.startsWith(ConfigHelper.get(BN_PROD_ID_PATH)) || productIdPath.startsWith(ConfigHelper.get(FM_PROD_ID_PATH))) {
            operationLevelResult = getOperationLevelResult(isFirstApplication, operationTypeGroup, productFactor, networkFactor, isSpecialScenarioFactor, businessInterruptionDurationFactor, operationTypeFactor);
        } else if (productIdPath.startsWith(ConfigHelper.get(MMVS_PROD_ID_PATH))){
            operationLevelResult = Math.floor((siteFactor * productFactor + operationTypeFactor * businessInterruptionDurationFactor * isSpecialScenarioFactor) / INTEGER_TWO);
        }
        return operationLevelResult;
    }
    /**
     *   自动计算操作等级
     *   2、产品分类属于【承载网】或【固网及多媒体】：a、操作类型分类属于配置保障：一般；b、【是否首次应用】选项选择是：关键；c、其他条件下：（网络系数*产品系数+操作类型系数*中断时长系数 * （if 特殊场景=Y then 1.5 else 1 end））/2 向下取整
     */
    private double getOperationLevelResult(
            String isFirstApplication,
            String operationTypeGroup,
            double productFactor,
            double networkFactor,
            double isSpecialScenarioFactor,
            double businessInterruptionDurationFactor,
            double operationTypeFactor) {
        if(ENABLED_FLAG.equals(isFirstApplication)){
            return INTEGER_THREE;
        }
        if(COOPERATION_GUARANTEE.equals(operationTypeGroup)){
            return INTEGER_ONE;
        }
        return Math.floor((networkFactor * productFactor + operationTypeFactor * businessInterruptionDurationFactor * isSpecialScenarioFactor) / INTEGER_TWO);
    }

    /**
     *   自动计算操作等级
     *   1、产品分类属于【算力及核心网】：a、操作类型分类属于配置保障：一般；b、【是否首次应用】选项选择是：关键；c、其他条件下：（网络系数*产品系数+操作类型系数）/2 向下取整
     */
    private double getOperationLevelResult(
            String isFirstApplication,
            String operationTypeGroup,
            double productFactor,
            double networkFactor,
            double operationTypeFactor) {
        if(ENABLED_FLAG.equals(isFirstApplication)){
            return INTEGER_THREE;
        }
        if(COOPERATION_GUARANTEE.equals(operationTypeGroup)){
            return INTEGER_ONE;
        }
        return Math.floor((networkFactor * productFactor + operationTypeFactor) / INTEGER_TWO);

    }

    /**
     * 自动计算操作等级
     * 1、产品分类属于【算力及核心网】：（局点系数*产品系数+操作类型系数*中断时长系数）/2 向下取整
     * 2、产品分类属于【承载网】、【固网及多媒体】或【视频-多媒体视讯系统】：（（局点系数*产品系数+操作类型系数*中断时长系数） * （if 特殊场景=Y then 1.5 else 1 end））/2 向下取整
     * 3、产品分类属于其他：手动选择
     */
    private double getOperationLevelResult(
            String productIdPath,
            double productFactor,
            double siteFactor,
            double isSpecialScenarioFactor,
            double businessInterruptionDurationFactor,
            double operationTypeFactor,
            double operationLevelResult) {
        if(productIdPath.startsWith(ConfigHelper.get(CCN_PROD_ID_PATH))){
            operationLevelResult = Math.floor((siteFactor * productFactor + operationTypeFactor * businessInterruptionDurationFactor)/INTEGER_TWO);
        } else if (productIdPath.startsWith(ConfigHelper.get(BN_PROD_ID_PATH)) || productIdPath.startsWith(ConfigHelper.get(FM_PROD_ID_PATH)) || productIdPath.startsWith(ConfigHelper.get(MMVS_PROD_ID_PATH))) {
            operationLevelResult = Math.floor((siteFactor * productFactor + operationTypeFactor * businessInterruptionDurationFactor * isSpecialScenarioFactor)/INTEGER_TWO);
        }
        return operationLevelResult;
    }


    @Nullable
    private static String getCustomerId(IDataModel dataModel) {
        Object customerObj = dataModel.getValue(FIELD_CUSTOMER_ID_CID);
        String customerId = null;
        if(!ObjectUtils.isEmpty(customerObj)){
            SvcCustomerInfo cust = JsonUtils.parseObject(customerObj, SvcCustomerInfo.class);
            customerId = cust.getCustomerId();
        }
        return customerId;
    }

    @Nullable
    private static String getOperationType(IDataModel dataModel) {
        Object operationTypeObj = dataModel.getValue(FIELD_OPERATION_TYPE_CID);
        String operationType = null;
        if(!ObjectUtils.isEmpty(operationTypeObj)){
            List<TextValuePair> operationTypeList = JsonUtils.parseArray(operationTypeObj, TextValuePair.class);
            operationType = operationTypeList.get(INTEGER_ZERO).getValue();
        }
        return operationType;
    }

    @Nullable
    private static String getIsFirstApplication(IDataModel dataModel) {
        Object isFirstApplicationObj = dataModel.getValue(FIELD_IS_FIRST_APPLICATION_CID);
        String isFirstApplication = null;
        if(!ObjectUtils.isEmpty(isFirstApplicationObj)){
            List<TextValuePair> isFirstApplicationList = JsonUtils.parseArray(isFirstApplicationObj, TextValuePair.class);
            isFirstApplication = isFirstApplicationList.get(INTEGER_ZERO).getValue();
        }
        return isFirstApplication;
    }

    @Nullable
    private static String getIsSpecialScenario(IDataModel dataModel) {
        Object isSpecialScenarioObj = dataModel.getValue(FIELD_IS_SPECIAL_SCENARIO_CID);
        String isSpecialScenario = null;
        if(!ObjectUtils.isEmpty(isSpecialScenarioObj)){
            List<TextValuePair> isSpecialScenarioList = JsonUtils.parseArray(isSpecialScenarioObj, TextValuePair.class);
            isSpecialScenario = isSpecialScenarioList.get(INTEGER_ZERO).getValue();
        }
        return isSpecialScenario;
    }

    @Nullable
    private static String getIsGovernmentEnterprise(IDataModel dataModel) {
        Object isGovernmentEnterpriseObj = dataModel.getValue(FIELD_IS_GOV_ENT_CID);
        String isGovernmentEnterprise = null;
        if (!ObjectUtils.isEmpty(isGovernmentEnterpriseObj)){
            List<TextValuePair> isGovernmentEnterpriseList = JsonUtils.parseArray(isGovernmentEnterpriseObj, TextValuePair.class);
            isGovernmentEnterprise = isGovernmentEnterpriseList.get(INTEGER_ZERO).getValue();
        }
        return isGovernmentEnterprise;
    }

    @Nullable
    private static String getProductIdPath(IDataModel dataModel) {
        Object productIdObj = dataModel.getValue(FIELD_PRODUCT_CID);
        String productIdPath = null;
        if (!ObjectUtils.isEmpty(productIdObj)){
            List<TextValuePair> productJsonObjects = JsonUtils.parseArray(productIdObj, TextValuePair.class);
            productIdPath = productJsonObjects.get(INTEGER_ZERO).getValue();
        }
        return productIdPath;
    }

    @Nullable
    private static String getOrganizationIdPath(IDataModel dataModel) {
        Object organizationIdObj = dataModel.getValue(FIELD_ORGANIZATION_CID);
        String organizationIdPath = null;
        if (!ObjectUtils.isEmpty(organizationIdObj)){
            List<TextValuePair> organizationJsonObjects = JsonUtils.parseArray(organizationIdObj, TextValuePair.class);
            organizationIdPath = organizationJsonObjects.get(INTEGER_ZERO).getValue();
        }
        return organizationIdPath;
    }
}
