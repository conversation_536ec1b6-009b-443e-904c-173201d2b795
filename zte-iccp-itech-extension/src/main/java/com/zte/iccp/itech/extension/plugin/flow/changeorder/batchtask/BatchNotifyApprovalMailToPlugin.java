package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.PressEmailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;


/**
 * 按批次申请发通告 - 通告审核
 * 标题示例：【网络变更操作催办】中国联通云南省分公司_4&5G_UME网管MR&CDT&MOS采集任务（紧急）（批次1）已进入通告审核环节，请您及时处理！
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/22
 */
public class BatchNotifyApprovalMailToPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        PressEmailPlugin plugin = new PressEmailPlugin();
        return plugin.anyTrigger(body, out);
    }
}
