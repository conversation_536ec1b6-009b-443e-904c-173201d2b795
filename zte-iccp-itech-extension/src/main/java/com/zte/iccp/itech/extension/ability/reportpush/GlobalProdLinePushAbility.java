package com.zte.iccp.itech.extension.ability.reportpush;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.enums.RiskEvaluationEnum;
import com.zte.iccp.itech.extension.openapi.model.reportpush.GlobalDetailVO;
import com.zte.iccp.itech.extension.spi.client.IcosClient;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.TemplateConstants.*;

/**
 * <AUTHOR>
 * @date 2025/6/5 上午11:20
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GlobalProdLinePushAbility {
    private static final String TODAY_APIID = "icos.netchange.report.global.today.detail.apiId";
    private static final String THREE_DAY_APIID = "icos.netchange.report.global.three.day.detail.apiId";

    public static void globalProdLinePush() {
        if (!ConfigHelper.getBoolean("global.prod.line.push.enable")) {
            return;
        }
        //多媒体视讯产品线：需要统计固网终端下的智慧家庭云；固网终端产品线：剔除其下的智慧家庭云，
        // 即将固网终端下的智慧家庭云的产品线调整成多媒体视讯再用于推送
        String fmMmvsIdPath = ConfigHelper.get("fm.mmvs.id.path");
        String fmCpeHomeDictCpeCloudIdPath = ConfigHelper.get("fm.cpe.homedict.cpe.cloud.id.path");
        List<GlobalDetailVO> todayDetails = IcosClient.getGlobalDetailVos(ConfigHelper.get(TODAY_APIID));
        todayDetails.stream()
                .filter(vo -> fmCpeHomeDictCpeCloudIdPath.equals(vo.getProductPath()))
                .forEach(vo -> vo.setProdLineIdPath(fmMmvsIdPath));

        List<GlobalDetailVO> threeDayDetails = IcosClient.getGlobalDetailVos(ConfigHelper.get(THREE_DAY_APIID));
        threeDayDetails.stream()
                .filter(vo -> fmCpeHomeDictCpeCloudIdPath.equals(vo.getProductPath()))
                .forEach(vo -> vo.setProdLineIdPath(fmMmvsIdPath));

        Map<String, List<GlobalDetailVO>> prodLineId2TodayDetailsMap = todayDetails.stream()
                .collect(Collectors.groupingBy(GlobalDetailVO::getProdLineIdPath));
        Map<String, List<GlobalDetailVO>> prodLineId2ThreeDayDetailsMap = threeDayDetails.stream()
                .collect(Collectors.groupingBy(GlobalDetailVO::getProdLineIdPath));

        String template = CommonUtils.getTemplateString(GLOBAL_PUSH_LOCATION);
        String dayTrTemplate = CommonUtils.getTemplateString(TODAY_DETAIL_LOCATION);
        String threeDayTrTemplate = CommonUtils.getTemplateString(THREE_DAY_LOCATION);
        String todayStr = DateUtils.dateToString(new Date(), DATE_FORMAT);
        String url = ConfigHelper.get(NETCHANGE_QUERY_URL);
        prodLineId2TodayDetailsMap.forEach((k, v) -> {
            String prodLine = v.stream().map(GlobalDetailVO::getProdLine).filter(Objects::nonNull).findFirst().orElse(EMPTY_STRING);
            String todayTotal = String.valueOf(v.size());
            List<GlobalDetailVO> threeDayDetailVos = prodLineId2ThreeDayDetailsMap.getOrDefault(k, Lists.newArrayList());

            Map<String, String> replacements = MapUtils.newHashMap(TODAY, todayStr,
                    PROD_LINE, prodLine,
                    TOTAL_COUNT, todayTotal,
                    TODAY_DETAIL, getDetailStr(v, dayTrTemplate),
                    STYLE, CollectionUtils.isEmpty(threeDayDetailVos) ? DISPLAY_HIDEEN : EMPTY_STRING,
                    THREE_DAY_COUNT, String.valueOf(threeDayDetailVos.size()),
                    THREE_DAY_DETAIL, getDetailStr(threeDayDetailVos, threeDayTrTemplate)
            );
            String templateStr = template;
            ReportPushAbility.sendMail(MsgUtils.getLangMessage(ZH_CN, GLOBAL_PROD_LINE_PUSH_TITLE, prodLine, todayStr, todayTotal),
                    StringBuilderUtils.replaceAll(templateStr, replacements), url,
                    ReportPushAbility.getEmpNosByPordLine(null, Lists.newArrayList(k)));
        });

    }

    private static String getDetailStr(List<GlobalDetailVO> vos, String dayTrTemplate) {
        vos = vos.stream()
                .sorted(Comparator.comparing(GlobalDetailVO::getProductPath, Comparator.nullsFirst(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < vos.size(); i++) {
            GlobalDetailVO vo = vos.get(i);
            String rowColor = RiskEvaluationEnum.STAR3.getZhCn().equals(vo.getRiskLevel()) ? RED : EMPTY_STRING;
            String noColor = RiskEvaluationEnum.STAR3.getZhCn().equals(vo.getRiskLevel()) ? RED : BLUE;
            Map<String, String> replacements = MapUtils.newHashMap(NUMBER, String.valueOf(i + 1),
                    BATCH_CODE, vo.getBatchCode(),
                    PLAN_OPERATION_START_TIME, vo.getPlanOperationStartTime(),
                    ORGANIZATION4, vo.getRepresentativeOffice(),
                    ORGANIZATION3, vo.getMarketing(),
                    PROD_SUB_CATEGORY, vo.getProductSubCategory(),
                    RISK_LEVEL, vo.getRiskLevel(),
                    OPERATION_SUBJECT, vo.getOperationSubject(),
                    OP_TYPE_ZH, vo.getOperationType(),
                    OPERATION_DESC, vo.getOperationDesc(),
                    SITE_PERSON, vo.getSitePerson(),
                    NETWORK_SUPPORT_STAFF, vo.getNetworkSupportStaff(),
                    DEVELOPMENT_SUPPORT_STAFF, vo.getDevelopmentSupportStaff(),
                    ROW_COLOR, rowColor,
                    NO_COLOR, noColor
            );
            String templateStr = dayTrTemplate;
            stringBuilder.append(StringBuilderUtils.replaceAll(templateStr, replacements));
        }
        return stringBuilder.toString();
    }
}
