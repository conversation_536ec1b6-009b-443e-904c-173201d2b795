package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperationSchemeFieldConsts.*;


/**
 * <AUTHOR>
 * @date 2024/7/1 下午4:45
 */
@Setter
@Getter
@BaseEntity.Info("operation_scheme")
public class OperationScheme extends BaseEntity {

    @JsonProperty(value = SCHEME_CODE)
    private String schemeCode;

    @JsonProperty(value = SCHEME_NAME)
    private String schemeName;

    @JsonProperty(value = PRODUCT_TYPE)
    private List<TextValuePair> productType;

    @JsonProperty(value = OPERATION_TYPE)
    private String operationType;

    @JsonProperty(value = COUNTRY)
    private List<MultiLangText> country;

    @JsonProperty(value = SCHEME_ATTACHMENT)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile schemeAttachment;

    @JsonProperty(value = SCHEME_TYPE)
    private List<TextValuePair> schemeType;

    @JsonProperty(value = BILL_ID)
    private String billId;

    @JsonProperty(value = BILL_TYPE)
    private String billType;

    @JsonProperty(value = SCHEME_STATUS)
    private List<TextValuePair> schemeStatus;

    @JsonProperty(value = PRODUCT_CLASS)
    private String productClass;

    @JsonProperty(value = VERSION_NO)
    private String versionNo;

}
