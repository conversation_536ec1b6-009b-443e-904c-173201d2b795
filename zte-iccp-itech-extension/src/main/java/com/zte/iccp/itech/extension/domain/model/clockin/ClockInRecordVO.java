package com.zte.iccp.itech.extension.domain.model.clockin;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.LookupValueDeserializer;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * @author: 李江斌 10318434
 * @date: 2024/9/18
 */
@Getter
@Setter
public class ClockInRecordVO extends BaseSubEntity {

    /** 打卡选项 */
    @JsonDeserialize(using = LookupValueDeserializer.class)
    private ClockInOptionEnum option;

    /** 打卡时间 */
    private Date clockInTime;

    /** 打卡人工号 */
    private String operatorId;

    /** 打卡人姓名 */
    private String operatorName;

    /** 打卡人电话 */
    private String operatorPhoneNum;

    /** 打卡说明 */
    private String description;

    /** 照片ID清单 */
    private List<String> photos;

    /** 打卡经度 */
    private String longitude;

    /** 打卡纬度 */
    private String latitude;
}
