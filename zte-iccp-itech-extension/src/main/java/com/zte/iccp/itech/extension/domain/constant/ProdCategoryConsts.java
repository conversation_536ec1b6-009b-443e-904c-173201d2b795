package com.zte.iccp.itech.extension.domain.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/08
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ProdCategoryConsts {
    /**
     * 算力及核心网常量key
     */
    public static final String CCN_PROD_IDPATH_KEY = "ccn.prodIdPath";

    /**
     * 承载网常量key
     */
    public static final String BN_PROD_IDPATH_KEY = "bn.prodIdPath";

    /**
     * 固网及多媒体常量key
     */
    public static final String FM_PROD_IDPATH_KEY = "fm.prodIdPath";

    /**
     * 视频-多媒体视讯系统常量key
     */
    public static final String MMVS_PROD_IDPATH_KEY = "mmvs.prodIdPath";

    /**
     * 承载网 - 综合传送产品 - MSTP ;承载网 - 综合传送产品 网管系统-ITN
     */
    public static final String BN_MSTP_ITN_PRODIDS_KEY = "bn.mstp.itn.prodIds";

    /**
     * 通信能源 - 通信电源
     */
    public static final String TE_POWER_SUPPLY_KEY = "prodTeam.prodLineIds.tps";
}
