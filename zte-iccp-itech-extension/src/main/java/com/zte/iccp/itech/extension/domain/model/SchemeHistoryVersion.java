package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.StandardSchemeFieldConsts.*;

@ApiModel("标准方案-历史版本")
@Setter
@Getter
@BaseSubEntity.Info(value = "scheme_history_version", parent = StandardScheme.class)
public class SchemeHistoryVersion extends BaseSubEntity {

    @JsonProperty(value = VERSION_NO_COPY)
    @ApiModelProperty("版本号")
    private String versionNoCopy;

    @JsonProperty(value = SCHEME_NAME_COPY)
    @ApiModelProperty("方案名称")
    private String schemeNameCopy;

    @JsonProperty(value = FILE_UPLOAD_USER)
    @ApiModelProperty("上传人")
    private String fileUploadUser;

    @JsonProperty(value = FILE_UPLOAD_DATE)
    @ApiModelProperty("上传时间")
    private String fileUploadDate;

    @JsonProperty(value = FILE_SIZE_COPY)
    @ApiModelProperty("文件大小")
    private BigDecimal fileSizeCopy;

    @JsonProperty(value = SCHEME_FILE_COPY)
    @ApiModelProperty("方案附件")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private List<AttachmentFile> schemeFile;

    @JsonProperty(value = SCHEME_FILE_COPY_EXT)
    @ApiModelProperty("方案附件-名称")
    private List<String> schemeFileExt;

}
