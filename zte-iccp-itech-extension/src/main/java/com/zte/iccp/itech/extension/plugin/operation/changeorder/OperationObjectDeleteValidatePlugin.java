package com.zte.iccp.itech.extension.plugin.operation.changeorder;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.apache.commons.collections.CollectionUtils;
import java.util.List;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;

/**
 * 网络变更操作单（内部+合作方）操作对象子表单【删除】按钮绑定校验插件
 *
 * <AUTHOR>
 * @since 2025/07/31
 */
public class OperationObjectDeleteValidatePlugin extends BaseOperationPlugin {

    @Override
    public boolean beforeExecuteValidate(ExecuteEvent executeEvent) {
        IDataModel dataModel = getModel();
        IFormView formView = getView();

        // 获取操作对象子表单需要删除的行号
        IEntryTableSupport operationObjectTable = (IEntryTableSupport) formView.getControl(COMPONENT_OPERATION_OBJECT_ORDER_CID);
        List<Integer> selectedRowIndexes = operationObjectTable.getTableState().getSelectedRows();
        if (CollectionUtils.isEmpty(selectedRowIndexes)) {
            return true;
        }

        // 获取子表单所有数据
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(CidConstants.OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        if (CollectionUtils.isEmpty(dataEntityCollection)) {
            return true;
        }

        // 如果选中待删除的操作对象中包含【主产品】数据行，则报错不允许删除
        for (int i = 0; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity d = (DynamicDataEntity) dataEntityCollection.get(i);
            Object isMainProductObj = d.get(OperationObjectFieldConsts.IS_MAIN_PRODUCT);
            int rowIndex = d.getRowIndex();
            String isMainProduct = TextValuePairHelper.getValue(isMainProductObj);
            if (selectedRowIndexes.stream().anyMatch(s -> s.equals(rowIndex))
                    && CommonConstants.Y.equals(isMainProduct)) {
                formView.showMessage(MessageConsts.MAIN_PRODUCT_CAN_NOT_DELETE, MsgType.ERROR);
                return false;
            }
        }

        return true;

    }
}
