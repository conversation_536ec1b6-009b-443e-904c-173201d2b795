package com.zte.iccp.itech.extension.domain.constant;

public class ShuttleBoxDataConstants {

    /**
     * 选择树数据
     */
    public static final String TREE_DATA = "treeData";

    /**
     * 已选字段
     */
    public static final String SELECTED_FIELDS = "selectedFields";

    /**
     * 字段id
     */
    public static final String ID = "id";

    /**
     * 字段编码
     */
    public static final String CODE = "code";

    /**
     * 字段值
     */
    public static final String VALUE = "value";

    /**
     * 字段等级
     */
    public static final String LEVEL = "level";

    /**
     * 字段状态
     */
    public static final String STATUS = "status";

    /**
     * 字段中文名称
     */
    public static final String NAME_CN = "nameCn";

    /**
     * 字段英文名称
     */
    public static final String NAME_EN = "nameEn";

    /**
     * 字段全路径中文名称
     */
    public static final String NAME_PATH_CN = "namePathCn";

    /**
     * 字段全路径英文名称
     */
    public static final String NAME_PATH_EN = "namePathEn";

    /**
     * 字段是否已选择
     */
    public static final String IS_SELECT = "isSelect";

    /**
     * 子节点
     */
    public static final String CHILD = "child";


    /**
     * 标准模板
     */
    public static final String STANDARD = "STANDARD";

    /**
     * 自定义模板
     */
    public static final String CUSTOM = "CUSTOM";
}
