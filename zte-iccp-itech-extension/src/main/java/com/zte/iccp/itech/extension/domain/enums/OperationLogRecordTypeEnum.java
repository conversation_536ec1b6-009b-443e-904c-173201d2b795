package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 *
 * 操作日志记录类型枚举
 *
 * <AUTHOR> jiang<PERSON>awen
 * @date 2024/6/4
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OperationLogRecordTypeEnum {

    /**
     * 管理任务-主任务
     */
    MANAGE_TASK("manage_type"),

    /**
     * 管理任务-子任务
     */
    MANAGE_SUBTASK("manage_sub_type"),

    /** 网络变更单 */
    NETWORK_CHANGE("NETWORK_CHANGE"),

    /** 合作方网络变更单 */
    SUBCONTRACTOR_NETWORK_CHANGE("SUBCONTRACTOR_NETWORK_CHANGE"),

    /** 保障单据、保障单对应的主单据的批次操作（发布通告、变更通告提交、取消、挂起）时给每一个保障单据冗余一份，每一个保障单据均为关联id */
    GUARANTEE("GUARANTEE"),
    ;

    /**
     * 操作类型
     */
    private final String operationType;
}
