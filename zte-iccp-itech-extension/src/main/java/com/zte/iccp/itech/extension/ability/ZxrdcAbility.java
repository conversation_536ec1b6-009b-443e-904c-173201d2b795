package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson2.JSON;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.vo.TableDisplayRows;
import com.zte.iccp.itech.extension.spi.client.ZxrdcClient;
import com.zte.iccp.itech.extension.spi.model.zxrdc.dto.GdprTaskDto;
import com.zte.iccp.itech.extension.spi.model.zxrdc.dto.UpgradeFormDto;
import com.zte.iccp.itech.extension.spi.model.zxrdc.vo.GdprTaskTableVo;
import com.zte.iccp.itech.extension.spi.model.zxrdc.vo.UpgradeFormTableVo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATA;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ZERO;

/**
 * ZXRDC查询ability
 *
 * <AUTHOR> 10335201
 * @date 2024-06-19 上午9:54
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)

public class ZxrdcAbility {

    public static void queryGdprApplyTaskList(IDataModel dataModel, IFormView formView, int currentPage, int pageSize) {
        GdprTaskDto gdprTaskDto = new GdprTaskDto();
        // 1.获取表格分页参数
        gdprTaskDto.setPageNo(currentPage);
        gdprTaskDto.setPageSize(pageSize);

        // 2.封装业务参数
        handleApplyParam(dataModel, gdprTaskDto);

        // 3.查询zxrdc接口获取数据
        TableDisplayRows<GdprTaskTableVo> gdprTaskTableVoTableDisplayRows = ZxrdcClient.queryGdprApplyTaskList(gdprTaskDto);

        // 4.页面数据展示
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(DATA, JsonUtils.parseObject(JSON.toJSONString(gdprTaskTableVoTableDisplayRows)));
        Map<String, Object> viewMap = new HashMap<>();
        viewMap.put(CidConstants.SELF_PAGE_REMOTE_FORM_TABLE_ID, dataMap);
        formView.getClientViewProxy().setProps(viewMap);
    }

    /**
     * 查询zxrdc无线产品升级单数据
     * @param dataModel dataModel
     * @param formView formView
     * @param currentPage 当前页
     * @param pageSize 页面容量
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/6/19 上午11:46
     */
    public static void queryUpgradeFormList(IDataModel dataModel, IFormView formView, int currentPage, int pageSize) {
        UpgradeFormDto upgradeFormDto = new UpgradeFormDto();
        // 1.获取表格分页参数
        upgradeFormDto.setPageNo(currentPage);
        upgradeFormDto.setPageSize(pageSize);

        // 2.封装业务参数
        handleParam(dataModel, upgradeFormDto);

        // 3.查询zxrdc接口获取数据
        TableDisplayRows<UpgradeFormTableVo> upgradeFormVoPageRows = ZxrdcClient.queryUpgradeFormList(upgradeFormDto);

        // 4.页面数据展示
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(DATA, JsonUtils.parseObject(JSON.toJSONString(upgradeFormVoPageRows)));
        Map<String, Object> viewMap = new HashMap<>();
        viewMap.put(CidConstants.SELF_PAGE_UPGRADE_FORM_TABLE_ID, dataMap);
        formView.getClientViewProxy().setProps(viewMap);
    }


    private static void handleApplyParam(IDataModel dataModel, GdprTaskDto gdprTaskDto) {
        String langId = RequestHeaderUtils.getLangId();
        // 默认参数
        gdprTaskDto.setQueryType(CommonConstants.ZXRDC_DEFAULT_QUERY_TYPE);

        // 业务参数处理
        getParamForRemoteForm(dataModel, gdprTaskDto, langId);

        // 责任人（接口模糊匹配，这里传工号）
        Object personObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_PERSON_ID);
        if(!ObjectUtils.isEmpty(personObj)){
            List<Employee> personJsonObjects = JsonUtils.parseArray(personObj, Employee.class);
            String person = personJsonObjects.get(INTEGER_ZERO).getEmpUIID();
            gdprTaskDto.setAppDutyEmpName(person);
        }

        // 参与人员（接口模糊匹配，这里传工号）
        Object participantsObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_PARTICIPANTS_ID);
        if(!ObjectUtils.isEmpty(participantsObj)){
            List<Employee> participantsJsonObjects = JsonUtils.parseArray(participantsObj, Employee.class);
            String participants = participantsJsonObjects.get(INTEGER_ZERO).getEmpUIID();
            gdprTaskDto.setAppJoinEmpNames(participants);
        }

        // 一线联系人（接口模糊匹配，这里传工号）
        Object contactObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_CONTACT_ID);
        if(!ObjectUtils.isEmpty(contactObj)){
            List<Employee> contactJsonObjects = JsonUtils.parseArray(contactObj, Employee.class);
            String contact = contactJsonObjects.get(INTEGER_ZERO).getEmpUIID();
            gdprTaskDto.setAppContactEmpName(contact);
        }

        // 开始时间，结束时间
        Map<String, LocalDateTime> startTime = PropertyValueConvertUtil.getDateRange(dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_START_ID));
        Map<String, LocalDateTime> endTime = PropertyValueConvertUtil.getDateRange(dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_END_ID));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT);
        if(startTime.get(CommonConstants.DATERANGE_START)!=null&&startTime.get(CommonConstants.DATERANGE_END)!=null){
            gdprTaskDto.setAppSupportBeginDate1(formatter.format(startTime.get(CommonConstants.DATERANGE_START)));
            gdprTaskDto.setAppSupportBeginDate2(formatter.format(startTime.get(CommonConstants.DATERANGE_END)));
        }

        if(endTime.get(CommonConstants.DATERANGE_START)!=null&&endTime.get(CommonConstants.DATERANGE_END)!=null){
            gdprTaskDto.setAppSupportEndDate1(formatter.format(endTime.get(CommonConstants.DATERANGE_START)));
            gdprTaskDto.setAppSupportEndDate2(formatter.format(endTime.get(CommonConstants.DATERANGE_END)));
        }

    }

    private static void getParamForRemoteForm(IDataModel dataModel, GdprTaskDto gdprTaskDto, String langId) {
        // 申请单号
        Object applyNoObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_CODE_ID);
        if(!ObjectUtils.isEmpty(applyNoObj)){
            gdprTaskDto.setAppCode(applyNoObj.toString());
        }

        // 经营部
        Object marketingObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_OPERATOR_ID);
        if(!ObjectUtils.isEmpty(marketingObj)){
            List<TextValuePair> marketingJsonObjects = JsonUtils.parseArray(marketingObj, TextValuePair.class);
            String marketing = marketingJsonObjects.get(INTEGER_ZERO).getTextByLanguage(langId);
            gdprTaskDto.setAppCenterName(marketing);
        }

        // 产品团队
        Object teamObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_TEAM_ID);
        if(!ObjectUtils.isEmpty(teamObj)){
            List<TextValuePair> teamJsonObjects = JsonUtils.parseArray(teamObj, TextValuePair.class);
            String team = teamJsonObjects.get(INTEGER_ZERO).getTextByLanguage(langId);
            gdprTaskDto.setAppTeamName(team);
        }

        // 安全物理区域
        Object areaObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_AREA_ID);
        if(!ObjectUtils.isEmpty(areaObj)){
            List<TextValuePair> areaJsonObjects = JsonUtils.parseArray(areaObj, TextValuePair.class);
            String area = areaJsonObjects.get(INTEGER_ZERO).getTextByLanguage(langId);
            gdprTaskDto.setAppAddressName(area);
        }

        // 国家
        Object countryObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_COUNTRY_ID);
        if(!ObjectUtils.isEmpty(countryObj)){
            List<MultiLangText> countryJsonObjects = JsonUtils.parseArray(countryObj, MultiLangText.class);
            String country = countryJsonObjects.get(INTEGER_ZERO).getTextByLanguage(langId);
            gdprTaskDto.setAppCountryName(country);
        }

        // 项目名称
        Object projectObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_PROJECT_ID);
        if(!ObjectUtils.isEmpty(projectObj)){
            gdprTaskDto.setAppProjectName(projectObj.toString());
        }

        // 单据状态
        Object statusObj = dataModel.getValue(CidConstants.SELF_PAGE_REMOTE_FORM_QUERY_STATUS_ID);
        if(!ObjectUtils.isEmpty(statusObj)){
            List<TextValuePair> statusJsonObjects = JsonUtils.parseArray(statusObj, TextValuePair.class);
            String status = statusJsonObjects.get(INTEGER_ZERO).getValue();
            gdprTaskDto.setStid(Integer.valueOf(status));
        }
    }

    /**
     * 处理查询入参
     * @param dataModel dataModel
     * @param upgradeFormDto 入参对象
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/6/19 上午11:47
     */
    private static void handleParam(IDataModel dataModel, UpgradeFormDto upgradeFormDto) {
        String langId = RequestHeaderUtils.getLangId();

        // 默认参数
        upgradeFormDto.setQueryType(CommonConstants.ZXRDC_DEFAULT_QUERY_TYPE);
        upgradeFormDto.setStid(CommonConstants.ZXRDC_DEFAULT_QUERY_STID);

        // 页面过滤条件
        Object applyNoObj = dataModel.getValue(CidConstants.SELF_PAGE_UPGRADE_FORM_QUERY_APPLY_NO_ID);
        if(!ObjectUtils.isEmpty(applyNoObj)){
            upgradeFormDto.setAppCode(applyNoObj.toString());
        }

        Object applySubjectObj = dataModel.getValue(CidConstants.SELF_PAGE_UPGRADE_FORM_QUERY_APPLY_SUBJECT_ID);
        if(!ObjectUtils.isEmpty(applySubjectObj)){
            upgradeFormDto.setAppSubject(applySubjectObj.toString());
        }

        Object marketingObj = dataModel.getValue(CidConstants.SELF_PAGE_UPGRADE_FORM_QUERY_MARKETING_ID);
        if(!ObjectUtils.isEmpty(marketingObj)){
            List<TextValuePair> marketingJsonObjects = JsonUtils.parseArray(marketingObj, TextValuePair.class);
            String marketing = marketingJsonObjects.get(INTEGER_ZERO).getTextByLanguage(langId);
            upgradeFormDto.setOrgName(marketing);
        }

        Object officeObject = dataModel.getValue(CidConstants.SELF_PAGE_UPGRADE_FORM_QUERY_OFFICE_ID);
        if(!ObjectUtils.isEmpty(officeObject)){
            upgradeFormDto.setOfficeName(officeObject.toString());
        }

        Object operatorObject = dataModel.getValue(CidConstants.SELF_PAGE_UPGRADE_FORM_QUERY_OPERATOR_ID);
        if(!ObjectUtils.isEmpty(operatorObject)){
            upgradeFormDto.setCustomName(operatorObject.toString());
        }

        Object mainProductObject = dataModel.getValue(CidConstants.SELF_PAGE_UPGRADE_FORM_QUERY_MAIN_PRODUCT_ID);
        if(!ObjectUtils.isEmpty(mainProductObject)){
            List<TextValuePair> mainProductJsonObjects = JsonUtils.parseArray(mainProductObject, TextValuePair.class);
            String mainProduct = mainProductJsonObjects.get(INTEGER_ZERO).getTextByLanguage(langId);
            upgradeFormDto.setProductName(mainProduct);
        }

        handlePlanDate(dataModel, upgradeFormDto);
    }

    /**
     * 处理计划开始时间，计划结束时间
     * @param dataModel dataModel
     * @param upgradeFormDto 入参
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/6/19 下午12:11
     */
    private static void handlePlanDate(IDataModel dataModel, UpgradeFormDto upgradeFormDto) {
        Map<String, LocalDateTime> startTime = PropertyValueConvertUtil.getDateRange(dataModel.getValue(CidConstants.SELF_PAGE_UPGRADE_FORM_QUERY_START_DATE_ID));
        Map<String, LocalDateTime> endTime = PropertyValueConvertUtil.getDateRange(dataModel.getValue(CidConstants.SELF_PAGE_UPGRADE_FORM_QUERY_END_DATE_ID));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT);
        if(startTime.get(CommonConstants.DATERANGE_START)!=null&&startTime.get(CommonConstants.DATERANGE_END)!=null){
            upgradeFormDto.setUpgradeBeginDate1(formatter.format(startTime.get(CommonConstants.DATERANGE_START)));
            upgradeFormDto.setUpgradeBeginDate2(formatter.format(startTime.get(CommonConstants.DATERANGE_END)));
        }

        if(endTime.get(CommonConstants.DATERANGE_START)!=null&&endTime.get(CommonConstants.DATERANGE_END)!=null){
            upgradeFormDto.setUpgradeEndDate1(formatter.format(endTime.get(CommonConstants.DATERANGE_START)));
            upgradeFormDto.setUpgradeEndDate2(formatter.format(endTime.get(CommonConstants.DATERANGE_END)));
        }
    }
}
