package com.zte.iccp.itech.extension.ability.clockin.reviews;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.PartnerChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.changeorder.OperationObjectAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInReviewsFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInReviewsNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.entity.ClockInReviewsAssignment;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.core.orm.query.*;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.ddm.domain.flow.dto.SaveBizAndStartFlowDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.common.api.RequestHeaderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.utils.CommonUtils.*;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.DOMESTIC_SALES;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.ENGINEERING_SERVICE_THREE_PARTS_OPERATION;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.BASIC_FIELD_BUSINESS_ID;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_CLOCK_IN_REVIEWS;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum.OPERATING_SUPERVISOR;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum.BATCH_TASK;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.FlowVariantEnum.IS_REVIEW_INIT;
import static com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum.CANCEL;
import static com.zte.iccp.itech.extension.domain.enums.clockin.ClockInReviewsNodeEnum.CLOCKIN_REVIEWS_INIT;
import static com.zte.iccp.itech.extension.domain.enums.clockin.ClockInReviewsNodeEnum.CLOCKIN_REVIEWS_SUBMIT;

/**
 * 打卡复盘
 *
 * @author: 李江斌 10318434
 * @date: 2024/12/9
 */
@Slf4j
public class ClockInReviewsCreateAbility {
    private static final int INTERVAL_MINUTE = 12;
    private static final int TWO_MINUTE = 2;
    public static final String DEFAULT_SUB_QUERY_SQL = "select distinct p_id FROM clock_in_record where is_deleted = 0 and clock_in_time > '%s' and clock_in_time < '%s' and clock_in_option in ('CANCEL','INDICATOR_NORMAL_ON_DUTY_END') and JSON_CONTAINS(revoked_ext,'[\"N\"]')";

    public static final String OPERATION_END_SUB_QUERY_SQL = "select distinct p_id FROM clock_in_record where is_deleted = 0 and clock_in_time > '%s' and clock_in_time < '%s' and clock_in_option = 'TEST_SUCCESS_OPERATION_END' and JSON_CONTAINS(revoked_ext,'[\"N\"]')";


    public static final String DEFAULT_SUB_QUERY_SQL_NO_TIME = "select distinct p_id FROM clock_in_record where is_deleted = 0 and clock_in_option in ('CANCEL','INDICATOR_NORMAL_ON_DUTY_END') and JSON_CONTAINS(revoked_ext,'[\"N\"]')";

    public static final String OPERATION_END_SUB_QUERY_SQL_NO_TIME = "select distinct p_id FROM clock_in_record where is_deleted = 0 and clock_in_option = 'TEST_SUCCESS_OPERATION_END' and JSON_CONTAINS(revoked_ext,'[\"N\"]')";

    // 操作取消卡
    private static final Set<ClockInOptionEnum> CANCEL_OPTIONS = Collections.unmodifiableSet(Sets.newHashSet(
            ClockInOptionEnum.CANCEL));
    // 操作准备开始
    private static final Set<ClockInOptionEnum> PREPARE_START_OPTIONS = Collections.unmodifiableSet(Sets.newHashSet(
            ClockInOptionEnum.PREPARE_START));

    public static void create() {
        // 1、查询需要建复盘单的批次任务ID-打卡任务
        // 执行阶段打了操作取消的打卡任务
        Map<String, List<ClockInTask>> cancelBatchTaskIdClockMap = new HashMap<>();
        // 操作执行、测试、值守阶段打了异常卡的打卡任务
        Map<String, List<ClockInTask>> abnormalBatchTaskIdClockMap = new HashMap<>();
        // 批次任务下最早一条异常打卡记录
        Map<String, ClockInRecord> recordMap = new HashMap<>();
        findClockInTask(cancelBatchTaskIdClockMap, abnormalBatchTaskIdClockMap, recordMap);
        if (cancelBatchTaskIdClockMap.isEmpty() && abnormalBatchTaskIdClockMap.isEmpty()){
            return;
        }
        List<ClockInTask> clockInTasks = cancelBatchTaskIdClockMap
                .values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        clockInTasks.addAll(abnormalBatchTaskIdClockMap
                .values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toList()));

        // 2、查询变更单和批次任务等信息（用于补充其他字段信息）
        ClockInParam clockInParam = new ClockInParam(clockInTasks);

        // 3、创建复盘单
        // 操作执行阶段由操作负责人打了“操作取消”卡（未撤销），则创建复盘单，并触发复盘初审
        createReviews(clockInParam, cancelBatchTaskIdClockMap, recordMap, CLOCKIN_REVIEWS_INIT);
        // 在操作、测试、值守阶段由操作负责人、值守人、值守转交人打了异常卡（未撤销），则创建复盘单，状态为“提交复盘”
        createReviews(clockInParam, abnormalBatchTaskIdClockMap, recordMap, CLOCKIN_REVIEWS_SUBMIT);
    }

    private static void createReviews(
            ClockInParam clockInParam,
            Map<String, List<ClockInTask>> batchTaskIdClockMap,
            Map<String, ClockInRecord> recordMap,
            ClockInReviewsNodeEnum clockInReviewsNodeEnum) {
        batchTaskIdClockMap.forEach((batchTaskId, clockInTaskList) -> {
            // 打卡任务信息（操作负责人）
            ClockInTask clockInTask = clockInTaskList
                    .stream().filter(t -> t.getOperatorRoles().contains(OPERATING_SUPERVISOR)).collect(Collectors.toList()).get(0);
            // 批次任务信息
            IBatchTask batchTask = clockInParam.getIBatchTask(batchTaskId);
            // 变更单操作对象信息（客户网络信息）
            List<NisNetwork> nisNetworks = clockInParam.getNisNetworkList(clockInTask.getChangeOrderId());
            // 产品信息
            String prodClassTemp = clockInTask.getProductClassification();
            String prodClassId = prodClassTemp.substring(0, prodClassTemp.length() - 1);
            BasicProductInfo productInfo = clockInParam.getProductInfo(prodClassId);
            // 组织信息（代表处和营销）
            BasicOrganizationInfo organizationInfo = clockInParam.getOrganizationInfo(last(clockInTask.getResponsibleDept()));
            BasicOrganizationInfo organizationInfoEn = clockInParam.getOrganizationInfoEn(last(clockInTask.getResponsibleDept()));
            String newOrgIDPath = organizationInfo.getOrgIDPath().replaceAll(SPECIAL_HYPHEN, FORWARD_SLASH);
            // 变更单信息
            IChangeOrder changeOrder = clockInParam.getChangeOrder(clockInTask.getChangeOrderId());

            ClockInReviews clockInReviews = new ClockInReviews();
            clockInReviews.setReplayStatus(clockInReviewsNodeEnum.getAssignmentStatusEnum());
            String orderNo = clockInTask.getBatchCode().split(HYPHEN)[0];
            clockInReviews.setReviewsNo(CLOCK_IN_REVIEWS_FP + HYPHEN + orderNo);
            clockInReviews.setOperationSubject(clockInTask.getOperationSubject());
            clockInReviews.setBatchNo(batchTask.getBatchNo());
            // 客户网络名称只有一个
            if (CollectionUtils.isNotEmpty(nisNetworks)) {
                NisNetwork nisNetwork = nisNetworks.get(0);
                clockInReviews.setNetworkName(
                        TextValuePairHelper.buildList(nisNetwork.getNetworkId(),
                                nisNetwork.getCustomerNetworkName(),
                                nisNetwork.getCustomerNetworkName()));
            }
            // 有的产品信息在NIS查不到
            if (productInfo != null) {
                // 最后加上正斜杠，兼容待办那边数据
                clockInReviews.setProductClassification(TextValuePairHelper.buildList(
                        prodClassId + FORWARD_SLASH,
                        productInfo.getNamePathZh(),
                        productInfo.getNamePathEn()));
            }
            // 代表处
            clockInReviews.setResponsibleDept(TextValuePairHelper.buildList(
                    organizationInfo.getHrOrgID(),
                    organizationInfo.getHrOrgName(),
                    organizationInfoEn.getHrOrgName()));
            // 营销
            clockInReviews.setMarketing(
                    TextValuePairHelper.buildList(
                            subIndex(newOrgIDPath, INTEGER_ONE, INTEGER_ONE),
                            subIndex(organizationInfo.getHrOrgNamePath(), INTEGER_TWO, INTEGER_TWO),
                            subIndex(organizationInfoEn.getHrOrgNamePath(), INTEGER_TWO, INTEGER_TWO)
                    )
            );
            clockInReviews.setOperationLevel(clockInTask.getOperationLevel());
            clockInReviews.setOperationType(clockInTask.getOperationType());
            LookupValue operateTypeLookup = clockInParam.getOperateTypeLookup(clockInTask.getOperationType());
            clockInReviews.setOperationTypeSelect(TextValuePairHelper.buildList(operateTypeLookup.getLookupCode(),
                    operateTypeLookup.getMeaningCn(),
                    operateTypeLookup.getMeaningEn()));
            clockInReviews.setPlanOperationStartTime(changeOrder.getOperationStartTime());
            clockInReviews.setPlanOperationEndTime(changeOrder.getOperationEndTime());
            clockInReviews.setOperationOwners(EmployeeHelper.singletonEmployee2Employee(clockInTask.getOperator()));
            clockInReviews.setChangeOrderId(clockInTask.getChangeOrderId());
            clockInReviews.setBatchTaskId(clockInTask.getBatchTaskId());
            clockInReviews.setAssignmentType(
                    clockInTask.getEntityType() == BATCH_TASK ? TextValuePairHelper.buildList(Lists.newArrayList(STR_ONE))
                            : TextValuePairHelper.buildList(Lists.newArrayList(STR_SIX)));
            // 打卡任务信息（值守任务）
            List<ClockInTask> onDutyClockInTaskList = clockInTaskList
                    .stream().filter(t -> t.getTaskType() == ClockInTaskTypeEnum.ON_DUTY).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(onDutyClockInTaskList)) {
                clockInReviews.setOnDutyDurationHours((int) (onDutyClockInTaskList.get(0).getTotalOnDutyDuration() * 60));
            }
            // 复盘单的创建人就是第一个打了异常卡的人
            clockInReviews.setCreateBy(recordMap.get(batchTaskId).getCreateBy());

            // 创建流程
            createFlow(clockInReviews);

            // 插入任务表
            List<Employee> currentProcessors
                    = ClockInReviewsNodeEnum.CLOCKIN_REVIEWS_INIT.equals(clockInReviewsNodeEnum)
                    ? getClockInReviewsInitCurrentProcessors(clockInReviews)
                    : getClockInReviewsSubmitCurrentProcessors(clockInReviews);
            ClockInReviewsAssignment assignment
                    = AssignmentAbility.convertClockInReviewAssignment(clockInReviews, currentProcessors);
            AssignmentAbility.insert(assignment);
        });
    }

    /**
     * 创建 复盘任务 流程
     *
     * @param clockInReviews
     */
    private static void createFlow(ClockInReviews clockInReviews) {
        // 设置线程用户
        RequestContextHolder.setEmpNo(clockInReviews.getCreateBy());
        RequestHeaderUtils.setEmpNo(clockInReviews.getCreateBy());
        // 1.流程对象 - 基础参数
        SaveBizAndStartFlowDTO flow = new SaveBizAndStartFlowDTO();
        flow.setTenantId(ContextHelper.getTenantId());
        flow.setAppId(ContextHelper.getAppId());
        flow.setPageId(PAGE_CLOCK_IN_REVIEWS);
        flow.setBizObjCode(EntityHelper.getEntityId(ClockInReviews.class));

        // 2.流程对象 - 自定义参数
        Map<String, Object> clockInReviewsInfo = JsonUtils.parseObject(clockInReviews, Map.class);

        clockInReviewsInfo.put(IS_REVIEW_INIT.getKey(), AssignmentStatusEnum.REVIEWS_INIT != clockInReviews.getReplayStatus()
                ? DISABLED_FLAG
                : ENABLED_FLAG);
        flow.setParams(clockInReviewsInfo);

        // 3.创建流程
        clockInReviews.setId(FlowServiceHelper.saveBizAndStartFlow(flow).getString(BASIC_FIELD_BUSINESS_ID));
    }

    /**
     * 计算当前处理人 - 复盘初审
     */
    public static List<Employee> getClockInReviewsInitCurrentProcessors(ClockInReviews clockInReviews) {
        AssignmentTypeEnum changeOrderType
                = AssignmentTypeEnum.fromTextValuePair(clockInReviews.getAssignmentType());
        String changeOrderId = clockInReviews.getChangeOrderId();

        // 1.合作方网络变更单
        // 代表处人员配置（办事处产品科长 / 项目TD）
        if (AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(changeOrderType)) {
            ApproverConfiguration configuration
                    = getApproverConfiguration(changeOrderId, SubcontractorChangeOrder.class);
            return getApprovers(configuration);
        }

        // 2.网络变更单
        // 技术交付部 / 网络处配置审核人配置
        List<String> fields = Lists.newArrayList(
                CommonFieldConsts.ID,
                ChangeOrderFieldConsts.IS_GOV_ENT,
                ChangeOrderFieldConsts.OPERATION_TYPE,
                ChangeOrderFieldConsts.PRODUCT_CATEGORY,
                ChangeOrderFieldConsts.RESPONSIBLE_DEPT,
                ChangeOrderFieldConsts.ACCN_TYPE,
                ChangeOrderFieldConsts.LOGICAL_NE);
        ChangeOrder changeOrder = QueryDataHelper.get(ChangeOrder.class, fields, changeOrderId);

        // 查询operation_object操作对象表获取【主产品】操作对象的产品型号
        String productModelId = null;
        List<OperationObject> operationObjects
                = OperationObjectAbility.listOperationObject(changeOrderId, OperationObject.class);
        if (!CollectionUtils.isEmpty(operationObjects)) {
            // 取【主产品】操作对象的产品型号
            List<String> productIds = operationObjects
                    .stream()
                    .filter(item -> CollectionUtils.isNotEmpty(item.getIsMainProduct())
                            && CommonConstants.Y.equals(item.getIsMainProduct().get(0).getValue()))
                    .map(OperationObject::getProductModel)
                    .collect(Collectors.toList());
            productModelId = CollectionUtils.isEmpty(productIds)
                    ? operationObjects.get(INTEGER_ZERO).getProductModel()
                    : productIds.get(0);
        }

        // 根据是否政企、操作类型、产品分类、代表处、客户标识和变更单操作对象中第一行产品型号获取技术交付部/网络处审核人配置
        ApproverConfiguration configuration = ApproverConfigAbility.getApprover(
                changeOrder, productModelId, ApprovalTypeEnum.TECHNOLOGY_DELIVERY_DEPT_NETWORK_OFFICE);
        return getApprovers(configuration);
    }

    /**
     * 计算当前处理人 - 复盘提交
     */
    public static List<Employee> getClockInReviewsSubmitCurrentProcessors(ClockInReviews clockInReviews) {
        AssignmentTypeEnum changeOrderType
                = AssignmentTypeEnum.fromTextValuePair(clockInReviews.getAssignmentType());
        String changeOrderId = clockInReviews.getChangeOrderId();

        // 1.合作方网络变更单
        // 网络责任人
        if (AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(changeOrderType)) {
            SubcontractorChangeOrder changeOrder = PartnerChangeOrderAbility.get(changeOrderId);
            return Objects.requireNonNull(changeOrder).getNetworkResponsiblePerson();
        }

        // 2.内部网络变更单
        // 代表处人员配置（办事处产品科长 / 项目TD）
        ApproverConfiguration configuration = getApproverConfiguration(changeOrderId, ChangeOrder.class);
        return getApprovers(configuration);
    }

    /**
     * 获取审批人配置
     */
    private static ApproverConfiguration getApproverConfiguration(
            String changeOrderId,
            Class<? extends BaseEntity> changeOrderClass) {

        // 1.检索网络变更单
        IChangeOrder changeOrder
                = (IChangeOrder) QueryDataHelper.get(changeOrderClass, Lists.newArrayList(), changeOrderId);

        // 2.获取审批人配置
        // 根据代表处、产品分类、角色、是否政企获取代表处审核人配置
        return ApproverConfigAbility.getRepProdTdApprover(
                changeOrder.getResponsibleDept(),
                changeOrder.getProductCategory(),
                ApproveRoleEnum.PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE_PROJECT_TD,
                changeOrder.getIsGovEnt());
    }

    /**
     * 获取审批人
     */
    private static List<Employee> getApprovers(ApproverConfiguration configuration) {
        if (Objects.isNull(configuration)) {
            return Lists.newArrayList();
        }

        List<Employee> approvers = Lists.newArrayList();
        approvers.addAll(configuration.getApproverPersons());
        approvers.addAll(configuration.getApproverGroups());
        return approvers;
    }

    private static void findClockInTask(
            Map<String, List<ClockInTask>> cancelBatchTaskIdClockMap,
            Map<String, List<ClockInTask>> abnormalBatchTaskIdClockMap,
            Map<String, ClockInRecord> recordMap) {
        // 1、找出已经结束打卡的批次任务
        String xEmpNo = ContextHelper.getEmpNo();
        //【取消操作】、【指标正常，值守结束】
        List<String> batchTaskIdList = getClockInTask(DEFAULT_SUB_QUERY_SQL);
        //【测试正常，操作结束】（没有值守任务）
        List<String> batchTaskIdOperationEnd = getClockInTask(OPERATION_END_SUB_QUERY_SQL);
        log.info("cancel end batch ids：" + batchTaskIdList);
        List<ClockInTask> onDutyClockInTasks = QueryDataHelper.query(ClockInTask.class,
                Arrays.asList(BATCH_TASK_ID),
                Arrays.asList(
                        new Filter(BATCH_TASK_ID, Comparator.IN, batchTaskIdOperationEnd),
                        new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(ClockInTaskTypeEnum.ON_DUTY))
                ));
        batchTaskIdOperationEnd.removeAll(
                onDutyClockInTasks.stream().map(ClockInTask::getBatchTaskId).collect(Collectors.toList())
        );
        log.info("test end batch ids：" + batchTaskIdOperationEnd);
        batchTaskIdList.addAll(batchTaskIdOperationEnd);

        // 2、剔除已经建单的批次任务
        List<ClockInReviews> reviewsList = QueryDataHelper.query(ClockInReviews.class,
                Arrays.asList(ClockInReviewsFieldConsts.BATCH_TASK_ID),
                Arrays.asList(
                        new Filter(ClockInReviewsFieldConsts.BATCH_TASK_ID, Comparator.IN, batchTaskIdList)
                ));
        batchTaskIdList.removeAll(
                reviewsList.stream().map(ClockInReviews::getBatchTaskId).collect(Collectors.toList())
        );

        // 3、筛选出打过异常卡的批次任务（操作执行阶段打了操作取消卡， 或操作执行、测试、值守阶段打了异常卡）
        findRecord(batchTaskIdList,cancelBatchTaskIdClockMap,abnormalBatchTaskIdClockMap,recordMap);
    }

    private static void findRecord(
            List<String> batchTaskIdList,
            Map<String, List<ClockInTask>> cancelBatchTaskIdClockMap,
            Map<String, List<ClockInTask>> abnormalBatchTaskIdClockMap,
            Map<String, ClockInRecord> recordMap) {
        IFilter filter = new MultiFilter(new Filter(
                OPERATOR_ROLES, Comparator.CONTAINS, Lists.newArrayList(OPERATING_SUPERVISOR.getValue())))
                .or(new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(ClockInTaskTypeEnum.ON_DUTY)));
        // 仅国内工服处单据开放故障复盘任务
        IFilter domesticSalesFilter =FilterHelper.newMultiFilter(new Filter(RESPONSIBLE_DEPT, Comparator.LIKE, DOMESTIC_SALES + PERCENT)
                        .or(new Filter(RESPONSIBLE_DEPT, Comparator.LIKE, ENGINEERING_SERVICE_THREE_PARTS_OPERATION + PERCENT)));
        List<ClockInTask> clockInTaskList = QueryDataHelper.query(ClockInTask.class,
                Arrays.asList(),
                Arrays.asList(
                        new Filter(BATCH_TASK_ID, Comparator.IN, batchTaskIdList),
                        filter,
                        domesticSalesFilter
                ));
        List<String> clockInTaskIds = clockInTaskList.stream().map(ClockInTask::getId).collect(Collectors.toList());
        Map<String, List<ClockInRecord>> recordMapTemp = ClockInQueryAbility.getClockInRecordList(clockInTaskIds);
        Map<String, List<ClockInTask>> batchTaskIdClockInTaskMap = clockInTaskList
                .stream().collect(Collectors.groupingBy(ClockInTask::getBatchTaskId));

        Map<String, List<ClockInRecord>> abnormalBatchTaskIdClockRecordMap = new HashMap<>();
        for (String batchTaskId : batchTaskIdClockInTaskMap.keySet()) {
            // 同一个批次任务ID，应该只有一条操作负责人操作打卡任务，应该只有一条值守打卡任务，所以这里的 tempClockList 是两条
            List<ClockInTask> tempClockList = batchTaskIdClockInTaskMap.get(batchTaskId);
            putRecord(tempClockList, recordMapTemp, abnormalBatchTaskIdClockRecordMap, cancelBatchTaskIdClockMap, recordMap);
            List<ClockInRecord> clockInRecords = abnormalBatchTaskIdClockRecordMap.get(batchTaskId);
            if (CollectionUtils.isEmpty(clockInRecords)) {
                continue;
            }
            List<ClockInRecord> sortRecords = clockInRecords
                    .stream()
                    .filter(t -> t.getClockInOption().isException())
                    .sorted(java.util.Comparator.comparing(ClockInRecord::getClockInTime))
                    .collect(Collectors.toList());
            recordMap.put(batchTaskId, sortRecords.get(0));
            if (cancelBatchTaskIdClockMap.get(batchTaskId) == null) {
                abnormalBatchTaskIdClockMap.put(batchTaskId, tempClockList);
            }
        }
    }

    private static void putRecord(
            List<ClockInTask> tempClockList,
            Map<String, List<ClockInRecord>> recordMapTemp,
            Map<String, List<ClockInRecord>> abnormalBatchTaskIdClockRecordMap,
            Map<String, List<ClockInTask>> cancelBatchTaskIdClockMap,
            Map<String, ClockInRecord> recordMap) {
        for (ClockInTask clockInTask : tempClockList) {
            String batchTaskId = clockInTask.getBatchTaskId();
            List<ClockInRecord> clockInRecords = recordMapTemp.get(clockInTask.getId());
            if (CollectionUtils.isEmpty(clockInRecords)) {
                continue;
            }
            // 如果在执行阶段打了操作取消卡
            if (hasClockInOption(clockInRecords, CANCEL_OPTIONS) && hasClockInOption(clockInRecords, PREPARE_START_OPTIONS)) {
                List<ClockInRecord> sortRecords = clockInRecords
                        .stream()
                        .filter(t -> t.getClockInOption() == CANCEL)
                        .sorted(java.util.Comparator.comparing(ClockInRecord::getClockInTime))
                        .collect(Collectors.toList());
                // 只有准备和执行阶段能打取消卡，并且这里查到的打卡任务同一批次ID下操作类只有一条，值守类也只有一条，所以同一批次ID下不会有其他打卡任务存在操作取消卡
                recordMap.put(batchTaskId, sortRecords.get(0));
                cancelBatchTaskIdClockMap.put(batchTaskId, tempClockList);
            }
            // 如果在操作执行、测试、值守阶段打了异常卡
            boolean hasExceptionOption = clockInRecords.stream()
                    .anyMatch(record -> record.getClockInOption().isException());
            if (hasExceptionOption && hasClockInOption(clockInRecords, PREPARE_START_OPTIONS)) {
                // 这个异常卡可能在操作打卡任务或者值守打卡任务中都有
                List<ClockInRecord> records = abnormalBatchTaskIdClockRecordMap.getOrDefault(batchTaskId, new ArrayList<>());
                records.addAll(clockInRecords);
                abnormalBatchTaskIdClockRecordMap.put(batchTaskId, records);
            }
        }
    }

    private static boolean hasClockInOption(List<ClockInRecord> clockInRecords, Set<ClockInOptionEnum> options) {
        return clockInRecords.stream()
                .anyMatch(record -> options.contains(record.getClockInOption()));
    }

    private static List<String> getClockInTask(String defaultChildQuerySql) {
        // 当前时间
        Calendar calendar = Calendar.getInstance();
        // 十二分钟之前的时间
        calendar.add(Calendar.MINUTE, -INTERVAL_MINUTE);
        // 获取调整后的时间
        Date startDate = calendar.getTime();
        // 十分钟之前的时间
        calendar.add(Calendar.MINUTE, TWO_MINUTE);
        // 获取调整后的时间
        Date endDate = calendar.getTime();
        String formatSql = String.format(defaultChildQuerySql,
                DateUtils.dateToString(startDate, DATE_FORM), DateUtils.dateToString(endDate, DATE_FORM));
        return queryClockInTask(formatSql);
    }

    private static List<String> queryClockInTask(String defaultChildQuerySql) {
        log.info("query end sql：" + defaultChildQuerySql);
        SubQueryFilter childFilter = new SubQueryFilter(new Filter(
                CommonFieldConsts.ID,
                Comparator.IN,
                Arrays.asList(INTEGER_ONE)
        ), defaultChildQuerySql);
        // 只取操作负责人、值守人/值守转交人的打卡
        IFilter filter = new MultiFilter(new Filter(
                OPERATOR_ROLES, Comparator.CONTAINS, Lists.newArrayList(OPERATING_SUPERVISOR.getValue())))
                .or(new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(ClockInTaskTypeEnum.ON_DUTY)));
        List<ClockInTask> clockInTasks = QueryDataHelper.query(ClockInTask.class,
                Arrays.asList(BATCH_TASK_ID),
                Arrays.asList(childFilter,filter));
        return clockInTasks.stream().map(ClockInTask::getBatchTaskId).distinct().collect(Collectors.toList());
    }


}
