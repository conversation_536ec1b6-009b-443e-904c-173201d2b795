package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10318434
 * @since 2024/05/30
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class StandardSchemeFieldConsts {
    /**
     * 方案编码
     */
    public static final String SCHEME_CODE = "scheme_code";

    /**
     * 方案名称
     */
    public static final String SCHEME_NAME = "scheme_name";

    /**
     * 方案类型
     */
    public static final String SCHEME_TYPE = "scheme_type";

    /**
     * 可用于AI生成操作方案
     */
    public static final String AI_FLAG = "ai_flag";

    /**
     * 方案状态
     */
    public static final String SCHEME_STATE = "scheme_state";

    /**
     * 产品类型
     */
    public static final String PROD_TYPE = "prod_type";

    /**
     * 地区
     */
    public static final String REGION = "region";

    /**
     * 方案附件
     */
    public static final String SCHEME_FILE = "scheme_file";

    /**
     * 文件大小
     */
    public static final String FILE_SIZE = "file_size";

    /**
     * 任务类型
     */
    public static final String TASK_TYPE = "task_type";

    /**
     * 操作类型
     */
    public static final String OPERATE_TYPE = "fastcodefield_operate_type";

    /**
     * 语言
     */
    public static final String LANGUAGE = "language";

    /**
     * 版本号
     */
    public static final String VERSION_NO = "version_no";

    /**
     * 历史版本-版本号
     */
    public static final String VERSION_NO_COPY = "version_no_copy";

    /**
     * 历史版本-方案名称
     */
    public static final String SCHEME_NAME_COPY = "scheme_name_copy";

    /**
     * 历史版本-上传人
     */
    public static final String FILE_UPLOAD_USER = "file_upload_user";

    /**
     * 历史版本-上传时间
     */
    public static final String FILE_UPLOAD_DATE = "file_upload_date";

    /**
     * 历史版本-文件大小
     */
    public static final String FILE_SIZE_COPY = "file_size_copy";

    /**
     * 历史版本-方案附件
     */
    public static final String SCHEME_FILE_COPY = "scheme_file_copy";

    /**
     * 历史版本-方案附件-名称
     */
    public static final String SCHEME_FILE_COPY_EXT = "scheme_file_copy_ext";
}