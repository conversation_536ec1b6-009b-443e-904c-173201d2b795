package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.OperationLogRecordConsts;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;


/**
 * 操作日志记录实体
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/4
 */
@ApiModel("操作日志记录")
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@BaseEntity.Info("operation_log_record")
public class OperationLogRecord extends BaseEntity {

    /**
     * 关联id
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_RECORD_RELATION_ID)
    private String relationId;

    /**
     * 父关联id 新增字段，作为操作日志记录表自关联字段
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_RECORD_PARENT_RELATION_ID)
    private String parentRelationId;


    /**
     * 操作名称： 创建任务、反馈进展、申请关闭、申请作废、同意关闭、责任转交、驳回任务、已作废
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_NAME)
    private String operationName;

    /**
     * 操作类型：技术管理任务（manage_task）,
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_LOG_OPERATION_TYPE)
    private String operationType;

    /**
     * 操作描述
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_LOG_OPERATION_DESC)
    private String operationDescription;

    /**
     * 附件
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_LOG_ATTACHMENT)
    private Object attachment;

    /**
     * 拓展人员字段,如责任人、处理人等
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_LOG_RESPONSIBLE_PERSON)
    private String responsiblePerson;

    /**
     * 子名称
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_LOG_CHILD_NAME)
    private String childName;

    /**
     * 操作名称前缀，如批次单号全路径，如存在数据用_拼接操作名称，如为空不进行处理
     */
    @JsonProperty(value = OperationLogRecordConsts.OPERATION_LOG_OPERATION_NAME_PREFIX)
    private String operationNamePrefix;


    /**
     * 设置操作日志
     *
     * @param zhCn 中文
     * @param enUs 英文
     */
    public void setOperationDesc(String zhCn, String enUs) {
        // 存在部分没有操作日志的动作
        if (StringUtils.isEmpty(zhCn)
                || StringUtils.isEmpty(enUs)) {
            return;
        }
        this.setOperationDescription(
                JsonUtils.toJsonString(MapUtils.newHashMap(ZH_CN, zhCn, EN_US, enUs)));
    }


    /**
     * 根据语言获取日志
     *
     * @param language 语言
     * @return 对应语言的操作日志
     */
    public String getOperationDescByLanguage(String language) {
        if (StringUtils.isEmpty(this.operationDescription)) {
            return null;
        }

        Map<String, String> operationDescMap = JsonUtils.parseObject(this.operationDescription, Map.class);
        return operationDescMap.get(language);
    }
}
