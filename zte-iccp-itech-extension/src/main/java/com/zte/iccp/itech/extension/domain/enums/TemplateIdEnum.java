package com.zte.iccp.itech.extension.domain.enums;


import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;


/**
 * 邮件模板id枚举
 *
 * <AUTHOR>
 * @date 2024/7/17 上午10:08
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum TemplateIdEnum {

    //*************** 邮件 ***************
    /**
     * 催办（标题+正文一起映射文案管理） 例如：${messageInfoZh}
     */
    EMAIL_PRESS_1("Z2024071717030123254"),

    /**
     * 知会（标题+正文一起映射文案管理）
     */
    EMAIL_INFOM_1("Z2024102817462431286"),


    /**
     * 催办（标题和正文分开映射） 例如：【${emailTitleZh}】${messageInfoZh}
     */
    EMAIL_PRESS_2("Z2024102609391941325"),

    /**
     * 知会 （标题和正文分开映射）
     */
    EMAIL_INFORM_2("Z2024102813430868101"),


    /**
     * 通知（发布通告、取消、挂起）   拓展操作等级+标题+正文，例如：【${operateLevelZh}${emailTitleZh}】${messageInfoZh}
     */
    EMAIL_NOTIFY_1("Z2024071909471029511"),

    // ****************授权文件********************
    /** 下载超级授权文件 */
    DOWNLOAD_SA_GRANT_FILE("Z2024080710035895893"),
    /** 授权文件生成失败 */
    GRANT_FILE_CREATE_FAILED("Z2024080710085679725"),

    /**
     * 技术审批超时icenter通知
     */
    APPROVAL_TIMEOUT_NOTIFICATION("W2024101613372115909"),

    /**
     * 网络变更操作审批通知 - iCenter
     */
    ICENTER_PRESS_NOTIFY("W2024102609450983238"),

    /**
     * 批次通知（发布通告、取消、挂起） - iCenter
     */
    ICENTER_PUB_NOTIFY("W2024102815404707843"),

    /**
     * 申请用户授权凭证
     */
    APPLY_AUTHORIZATION("Z2024101710305449200"),

    /**
     * 申请核心网默认授权文件
     */
    APPLY_CCN_AUTHORIZATION("Z2024110720122657209"),


    /**
     * 权限申请（发给审批人）
     */
    PERMISSION_APPLICATION_APPROVER("Z2024111416344798157"),

    /**
     * 权限申请（通过）
     */
    PERMISSION_APPLICATION_AGREE("Z2024111510121264469"),

    /**
     * 权限申请（不通过）
     */
    PERMISSION_APPLICATION_RETURN("Z2024111510312155539"),

    /**
     * 网络变更单报表推送
     */
    NETWORK_CHANGE_REPORT_PUSH("W2025041109151783894"),

    /**
     * 网络变更单导出提醒
     */
    CHANGE_ORDER_EXPORT("Z2025041313534469480"),

    /**
     * 打卡单导出提醒
     */
    CHECK_IN_TICKET_EXPORT("Z2025050615533305888"),

    /**
     * 打卡记录导出提醒
     */
    CHECK_IN_RECORD_EXPORT("Z2025050615551904794"),

    /**
     * 故障管理催办
     */
    FAULT_MANAGEMENT_PRESS("Z2025041721092642450"),

    /**
     * 故障管理知会
     */
    FAULT_MANAGEMENT_NOTICE("Z2025041816452390735"),

    /**
     * 邮件报表推送
     */
    REPORT_MAIL_PUSH("Z2025042209324351543"),

    ;

    private final String templateId;
}