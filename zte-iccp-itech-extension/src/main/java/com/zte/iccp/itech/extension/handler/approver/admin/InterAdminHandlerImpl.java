package com.zte.iccp.itech.extension.handler.approver.admin;

import com.zte.iccp.itech.extension.ability.changeorder.InterAdminApproverAbility;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.List;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/17
 */
public class InterAdminHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        // 1.初始化intl_admin_approval行政会签单据体数据（先删后增逻辑）
        AsyncExecuteUtils.execute(() -> InterAdminApproverAbility.saveInterCountersignApprover(changeOrder));

        // 2.获取会签处理人
        return InterAdminApproverAbility.getInterCountersignApprover(changeOrder);
    }
}
