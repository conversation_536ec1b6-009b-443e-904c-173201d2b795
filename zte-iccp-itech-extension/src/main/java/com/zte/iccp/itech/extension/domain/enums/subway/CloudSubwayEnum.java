package com.zte.iccp.itech.extension.domain.enums.subway;

import com.zte.paas.lcap.common.constant.LangConst;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * warroom地铁图节点枚举
 *
 * <AUTHOR> 10335201
 * @date 2024-08-13 下午3:27
 **/
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum CloudSubwayEnum {

    /**
     * 故障复盘
     */
    FAULT_REPORTING("2","故障复盘", "Fault Review"),
    /**
     * 故障整改
     */
    BUILD_WARROOM("3","故障整改", "Fault Rectification"),
    /**
     * 客户满意度
     */
    REMOTE_ACCESS("4","客户满意度", "Customer Satisfaction"),
    /**
     * 关闭
     */
    PERSONNEL_IN_PLACE("5","关闭", "Closed"),
    ;


    private final String code;

    private final String nameZh;

    private final String nameEn;


    public String getName(String language) {
        return LangConst.ZH_CN.equals(language) ? this.getNameZh() : this.getNameEn();
    }
}
