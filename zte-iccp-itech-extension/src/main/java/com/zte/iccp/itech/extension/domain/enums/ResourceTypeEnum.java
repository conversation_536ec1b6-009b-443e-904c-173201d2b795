 package com.zte.iccp.itech.extension.domain.enums;



 import java.util.Objects;

 /**
  * 资源类型枚举
  * <AUTHOR>
  * @date 2024/11/20
  */
 public enum ResourceTypeEnum {
     /**
      * 1 菜单
      */
     MENU(1,"菜单"),
     /**
      * 2 URL
      */
     URL(2,"URL"),
     /**
      * 3  控件
      */
     CONTROL(3," 控件");
     
     private final Integer code;

     private final String desc;


     ResourceTypeEnum(Integer code, String desc) {
         this.code = code;
         this.desc = desc;
     }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ResourceTypeEnum fromCode(Integer code) {
        for (ResourceTypeEnum resourceType : ResourceTypeEnum.values()) {
            if (Objects.equals(code, resourceType.code)) {
                return resourceType;
            }
        }
        return null;
    }
}
