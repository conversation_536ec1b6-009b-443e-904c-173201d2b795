package com.zte.iccp.itech.extension.ability.clockin.callplan;

import com.zte.iccp.itech.extension.ability.BaseOperationCacheAbility;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.OperationCache;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallReasonEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallStatusEnum;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInCallPlan;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.LAST_MODIFIED_TIME;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInCallPlanFieldConsts.*;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
public final class ClockInCallPlanAbolishAbility extends BaseOperationCacheAbility {

    public ClockInCallPlanAbolishAbility() {
        this(null);
    }

    public ClockInCallPlanAbolishAbility(OperationCache operationCache) {
        super(operationCache);
    }

    public void abolish(List<String> clockInTaskIds) {
        List<ClockInCallPlan> callPlans = QueryDataHelper.query(
                ClockInCallPlan.class,
                Lists.newArrayList(ID),
                Lists.newArrayList(
                        new Filter(OBJECT_ID, Comparator.IN, clockInTaskIds),
                        new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.WAITING))));

        callPlans.forEach(p -> p.setCallStatus(CallStatusEnum.ABOLISHED));
        update(callPlans);
    }

    public void recover(Date abolishTime, List<String> clockInTaskIds) {
        List<ClockInCallPlan> callPlans = QueryDataHelper.query(
                ClockInCallPlan.class,
                Lists.newArrayList(ID, CALL_UTC_TIME),
                Lists.newArrayList(
                        new Filter(OBJECT_ID, Comparator.IN, clockInTaskIds),
                        new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.ABOLISHED)),
                        new Filter(LAST_MODIFIED_TIME, Comparator.GE, abolishTime)));

        callPlans.forEach(p -> p.setCallStatus(CallStatusEnum.WAITING));
        update(callPlans);
    }

    public void recover(List<String> ids) {
        List<ClockInCallPlan> callPlans = ids.stream()
                .map(id -> new ClockInCallPlan() {{
                    setId(id);
                    setCallStatus(CallStatusEnum.WAITING);
                }}).collect(Collectors.toList());
        update(callPlans);
    }

    /**
     * 废止相关呼叫计划
     */
    public void abolish(String clockInTaskId, CallReasonEnum reason) {
        List<ClockInCallPlan> callPlans = QueryDataHelper.query(
                ClockInCallPlan.class,
                Lists.newArrayList(ID),
                Lists.newArrayList(
                        new Filter(OBJECT_ID, Comparator.EQ, clockInTaskId),
                        new Filter(REASON, Comparator.EQ, Lists.newArrayList(reason)),
                        new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.WAITING))));

        callPlans.forEach(p -> p.setCallStatus(CallStatusEnum.ABOLISHED));
        update(callPlans);
    }
}
