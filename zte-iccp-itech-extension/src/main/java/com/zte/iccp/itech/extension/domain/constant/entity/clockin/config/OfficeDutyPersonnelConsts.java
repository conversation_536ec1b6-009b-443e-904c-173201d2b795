package com.zte.iccp.itech.extension.domain.constant.entity.clockin.config;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> jiangjiawen
 * @date 2024/10/15
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OfficeDutyPersonnelConsts {

    /** 代表处 */
    public static final String OFFICE = "office";

    /** 产品类型 */
    public static final String PROD_TYPE = "prod_type";

    /** 客户标识 */
    public static final String CUSTOMER_FLAG = "customer_flag";

    /** 客户id */
    public static final String CUSTOMER_ID = "customer_id";
}
