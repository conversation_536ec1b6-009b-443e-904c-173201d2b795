package com.zte.iccp.itech.extension.ability.authtask;

import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.AlarmUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.authtask.CancelSceneEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.*;
import com.zte.iccp.itech.extension.domain.model.vo.BatchTask4AuthVO;
import com.zte.iccp.itech.extension.domain.model.vo.PdmProductVO;
import com.zte.iccp.itech.extension.spi.client.CrmClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.INetClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.enums.inet.IsRemoteEnum;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.crm.SimpleCustomerInfo;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.inet.CancelAuthTaskReq;
import com.zte.iccp.itech.extension.spi.model.inet.OperatorVO;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.extension.spi.model.nis.PdmProdModelResp;
import com.zte.iccp.itech.extension.spi.model.query.NisNetworkQuery;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.utils.StackTraceUtils.getStackTrace;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.OPERATION_TYPE_NOT_EXISTS;

/**
 * <AUTHOR>
 * @since 2024/08/20
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class BatchTask4AuthAbility {
    private static final List<Config> CONFIGS = Lists.newArrayList(
            new Config(
                    BatchTask.class,
                    ChangeOrder.class,
                    OperationObject.class,
                    BatchTaskOperator.class,
                    HighInstruction.class,
                    "CO",
                    "batchTask.view.pageUrl",
                    Collections.unmodifiableList(Lists.newArrayList(
                            ChangeOrderFieldConsts.RESPONSIBLE_DEPT,
                            ChangeOrderFieldConsts.COUNTRY,
                            ChangeOrderFieldConsts.CITY,
                            ChangeOrderFieldConsts.OPERATION_LEVEL,
                            ChangeOrderFieldConsts.CUSTOMER_ID,
                            ChangeOrderFieldConsts.TIME_ZONE,
                            ChangeOrderFieldConsts.OPERATION_TYPE,
                            ChangeOrderFieldConsts.OPERATION_REASON,
                            ChangeOrderFieldConsts.BUSI_INTERRUPT_DURATION))),
            new Config(
                    SubcontractorBatchTask.class,
                    SubcontractorChangeOrder.class,
                    SubconOperationObject.class,
                    SubcontractorBatchOperator.class,
                    SubconHighInstruction.class,
                    "HZF",
                    "subconBatchTask.view.pageUrl",
                    Collections.unmodifiableList(Lists.newArrayList(
                            SubcontractorChangeOrderFieldConsts.ORGANIZATION_ID,
                            SubcontractorChangeOrderFieldConsts.COUNTRY,
                            SubcontractorChangeOrderFieldConsts.CITY,
                            SubcontractorChangeOrderFieldConsts.OPERATION_LEVEL,
                            SubcontractorChangeOrderFieldConsts.CUSTOMER_ID,
                            SubcontractorChangeOrderFieldConsts.TIME_ZONE,
                            SubcontractorChangeOrderFieldConsts.OPERATION_TYPE,
                            SubcontractorChangeOrderFieldConsts.OPERATION_REASON,
                            SubcontractorChangeOrderFieldConsts.BUSI_INTERRUPT_DURATION))));

    /**
     * 获取批次任务信息用于操作鉴权任务
     * @param batchCode 批次编号
     * @return 批次信息
     */
    public static BatchTask4AuthVO find4Auth(String batchCode) {
        Config cfg = config(batchCode);
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.queryOne(
                cfg.entity, cfg.fields, Lists.newArrayList(
                        new Filter(cfg.batchCodeField, Comparator.EQ, batchCode)));
        return build(cfg, batchTask);
    }

    public static void createAuthTask(Class<? extends BaseEntity> entity, String batchTaskId) {
        try {
            Config cfg = config(entity);
            IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(entity, cfg.fields, batchTaskId);
            BatchTask4AuthVO batchTaskVo = build(cfg, batchTask);

            // 打印iNet鉴权任务入参，为空也打印出来
            log.info("iNet 鉴权任务生成入参 / iNet.createAuthTask.batchTaskVo ：{}", JsonUtils.toJsonString(batchTaskVo));
            if (batchTaskVo == null) {
                return;
            }
            INetClient.createAuthTask(Lists.newArrayList(batchTaskVo));
        } catch (Exception e) {
            log.error("create inet tasks exception! batchTaskId:{} e:{}", batchTaskId, e);
            AlarmUtils.major("create inet tasks exception! exception", e);
        }
    }

    public static void cancelAuthTask(CancelSceneEnum cancelScene, Class<? extends BaseEntity> entity, String batchTaskId) {
        log.info("cancelAuthTask: {} {}\n{}", cancelScene, batchTaskId, getStackTrace("com.zte"));

        Config cfg = config(entity);
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(entity, Lists.newArrayList(cfg.batchCodeField), batchTaskId);
        INetClient.cancelAuthTask(Lists.newArrayList(new CancelAuthTaskReq() {{
            setOperateType(cancelScene.getCode());
            setOperateCode(batchTask.getBatchCode());
        }}));
    }

    private static BatchTask4AuthVO build(Config cfg, IBatchTask batchTask) {
        if (batchTask == null) {
            return null;
        }

        AssignmentStatusEnum status = AssignmentStatusEnum.fromValue(batchTask.getCurrentStatus());
        if (status == null || status.ordinal() < AssignmentStatusEnum.OPERATION_EXECUTION.ordinal()) {
            throw new LcapBusiException("batch task status error");
        }

        BatchTask4AuthVO batchTaskVo = new BatchTask4AuthVO();
        fillBatchTask(batchTaskVo, batchTask);
        String pageId = PageConstants.DEFAULT_BATCH_TASK_INFO_PAGE_ID;
        String  batchNo = batchTask.getBatchNo();
        if (DataSourceEnum.GUARANTEE.name().equals(batchTask.getSource())) {
            pageId = PageConstants.GUARANTEE_BATCH_TASK_INFO_PAGE_ID;
            batchNo = STR_ONE;
        }
        batchTaskVo.setBatchTaskUrl(String.format(ConfigHelper.get(cfg.urlCfgKey), batchTask.getId(), pageId));

        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                cfg.coEntity, cfg.coFields, batchTask.getChangeOrderId());
        fillChangeOrder(batchTaskVo, changeOrder);

        List<OperationObject> opObjects = QueryDataHelper.query(
                cfg.opObjEntity, cfg.opObjFields, batchTask.getChangeOrderId(), Lists.newArrayList(
                        new Filter(cfg.opObjBatchNoField, Comparator.EQ, Lists.newArrayList(batchNo))));
        fillProducts(batchTaskVo, opObjects);
        fillNetwork(batchTaskVo, opObjects);

        List<BatchTaskOperator> operators = QueryDataHelper.query(
                cfg.operatorEntity, cfg.operatorFields, batchTask.getId());
        fillOperators(batchTaskVo, operators);

        List<HighInstruction> instructions = QueryDataHelper.query(
                cfg.highRiskInstrEntity, cfg.highRiskInstrFields, changeOrder.getId());
        fillInstructions(batchTaskVo, instructions);

        return batchTaskVo;
    }

    private static void fillBatchTask(BatchTask4AuthVO batchTaskVo, IBatchTask batchTask) {
        batchTaskVo.setBatchCode(batchTask.getBatchCode());
        batchTaskVo.setBatchName(batchTask.getBatchName());
        batchTaskVo.setPlanStartTime(batchTask.getPlanOperationStartTime());
        batchTaskVo.setPlanEndTime(batchTask.getPlanOperationEndTime());
        batchTaskVo.setOperationDescription(batchTask.getOperationDescription());
        batchTaskVo.setPlanDescription(batchTask.getOperationPlanDesc());
    }

    private static void setTimeZone(BatchTask4AuthVO batchTaskVo, IChangeOrder changeOrder) {
        // 兼容夏令时，若属于夏令时，调整传给inet的时区
        TimeZoneEnum timeZone = changeOrder.getTimeZone();
        if (timeZone == null) {
            return;
        }
        ZoneId zoneId = timeZone.getZoneId();
        String offsetZoneId = zoneId.getRules().getOffset(batchTaskVo.getPlanStartTime().toInstant()).getId();
        // 获取夏令时调整后对应的时区信息
        batchTaskVo.setTimeZone(TIME_ZONE_PREFIX + offsetZoneId);
    }

    private static void fillChangeOrder(BatchTask4AuthVO batchTaskVo, IChangeOrder changeOrder) {
        batchTaskVo.setCountryCode(changeOrder.getCountry().getValue());
        batchTaskVo.setCountryNameZh(changeOrder.getCountry().getZhCN());
        batchTaskVo.setCountryNameEn(changeOrder.getCountry().getEnUS());
        setTimeZone(batchTaskVo, changeOrder);

        //TODO 临时判空处理
        if (changeOrder.getOperationLevel() == null) {
            log.error("OperationLevel=null: {}, {}", changeOrder.getId(), changeOrder.getOrderNo());
            changeOrder.setOperationLevel(OperationLevelEnum.NORMAL);
        }
        batchTaskVo.setOperationLevelZh(changeOrder.getOperationLevel().getZhCn());
        batchTaskVo.setOperationLevelEn(changeOrder.getOperationLevel().getEnUs());

        batchTaskVo.setBusiInterruptDuration(String.valueOf(changeOrder.getBusiInterruptDuration()));
        batchTaskVo.setOperationReasonZh(changeOrder.getOperationReasonZh());
        batchTaskVo.setOperationReasonEn(changeOrder.getOperationReasonEn());

        if (changeOrder.getCity() != null) {
            batchTaskVo.setCityNameZh(last(changeOrder.getCity().getZhCN()));
            batchTaskVo.setCityNameEn(last(changeOrder.getCity().getEnUS()));
        }

        // 代表处中英文
        String deptId = last(changeOrder.getResponsibleDept());
        batchTaskVo.setResponsibleDeptId(deptId);

        String langId = ContextHelper.getLangId();
        ContextHelper.setLangId(ZH_CN);
        Map<String, BasicOrganizationInfo> orgNameMap = HrClient.queryOrganizationInfo(Lists.newArrayList(deptId));
        batchTaskVo.setResponsibleDeptNameZh(
                first(orgNameMap.get(deptId).getHrOrgNamePath()));

        ContextHelper.setLangId(EN_US);
        orgNameMap = HrClient.queryOrganizationInfo(Lists.newArrayList(deptId));
        batchTaskVo.setResponsibleDeptNameEn(
                first(orgNameMap.get(deptId).getHrOrgNamePath()));

        ContextHelper.setLangId(langId);

        // 客户
        List<SimpleCustomerInfo> customer = CrmClient.queryCustomerSimpleInfo(
                Lists.newArrayList(changeOrder.getCustomerId()));
        batchTaskVo.setCustomerId(customer.get(0).getCustomerId());
        batchTaskVo.setCustomerNameZh(customer.get(0).getCustomerName());
        batchTaskVo.setCustomerNameEn(customer.get(0).getCustomerEnglishName());

        // 操作类型
        LookupValue lookupValue = LookupValueHelper.getLookupValue(
                ZH_CN,
                LookupValueConstant.OPERATE_TYPE_ENUM,
                changeOrder.getOperationType());
        if (lookupValue == null) {
            throw new LcapBusiException(OPERATION_TYPE_NOT_EXISTS);
        }
        batchTaskVo.setOperationTypeZh(lookupValue.getMeaning());

        lookupValue = LookupValueHelper.getLookupValue(
                EN_US,
                LookupValueConstant.OPERATE_TYPE_ENUM,
                changeOrder.getOperationType());
        if (lookupValue == null) {
            throw new LcapBusiException(OPERATION_TYPE_NOT_EXISTS);
        }
        batchTaskVo.setOperationTypeEn(lookupValue.getMeaning());
    }

    private static void fillProducts(BatchTask4AuthVO batchTaskVo, List<OperationObject> opObjects) {
        if (CollectionUtils.isEmpty(opObjects)) {
            return;
        }

        List<String> prodModelIds = opObjects.stream()
                .map(OperationObject::getProductModel).distinct()
                .collect(Collectors.toList());

        List<PdmProdModelResp> pdmProdModels = NisClient.getPdmProdModels(prodModelIds);
        List<PdmProductVO> pdmProdModelVos = Lists.newArrayList();
        Set<String> codeSet = new HashSet<>();
        for (PdmProdModelResp pdm : pdmProdModels) {
            String code = pdm.getCode();
            if (code == null || codeSet.contains(code)) {
                continue;
            }
            codeSet.add(code);
            PdmProductVO vo = new PdmProductVO();
            vo.setProdModelCode(pdm.getCode());
            vo.setProdModelNameZh(pdm.getNameZh());
            vo.setProdModelNameEn(pdm.getNameEnAbbr());
            pdmProdModelVos.add(vo);
        }

        batchTaskVo.setPdmProdModels(pdmProdModelVos);
    }

    private static void fillNetwork(BatchTask4AuthVO batchTaskVo, List<OperationObject> opObjects) {
        if (CollectionUtils.isEmpty(opObjects)) {
            return;
        }

        PageRows<NisNetwork> networks = NisClient.queryNisNetworkList(new NisNetworkQuery() {{
            setNetworkIds(opObjects.stream()
                    .map(OperationObject::getNetworkId)
                    .distinct()
                    .collect(Collectors.toList()));
        }});
        if (CollectionUtils.isEmpty(networks.getRows())) {
            return;
        }

        NisNetwork network = networks.getRows().get(0);
        batchTaskVo.setApproverNetworkId(network.getNetworkId());

        batchTaskVo.setNetworkCode(network.getNetworkCode());
        batchTaskVo.setNetworkNameZh(network.getNameZh());
        batchTaskVo.setNetworkNameEn(network.getNameEn());
        batchTaskVo.setCustomerNetworkName(network.getCustomerNetworkName());
    }

    private static void fillOperators(BatchTask4AuthVO batchTaskVo, List<BatchTaskOperator> operators) {
        List<OperatorVO> vos = operators.stream()
                .map(orig -> {
                    SingleEmployee employee = orig.getOperatePerson();
                    OperatorVO vo = new OperatorVO();
                    vo.setRoleId(orig.getOperatorRole().getValue());
                    vo.setRoleZh(orig.getOperatorRole().getZhCn());
                    vo.setRoleEn(orig.getOperatorRole().getEnUs());
                    vo.setIsRemote(IsRemoteEnum.valueOf(orig.getRemoteFlag().name()));
                    vo.setOperatorUserId(orig.getOperatePerson().getId());
                    vo.setOperatorNameZh(buildOperatorName(employee.getEmpNameCn(), employee.getEmpName(), batchTaskVo));
                    vo.setOperatorNameEn(buildOperatorName(employee.getEmpNameEn(), employee.getEmpName(), batchTaskVo));
                    vo.setDepartmentZh(first(orig.getOperatePerson().getOrgNamePath()));
                    vo.setDepartmentEn(first(orig.getOperatePerson().getOrgNamePathEn()));
                    vo.setContactNumber(orig.getOperatorPhone());
                    vo.setTaskDesc(orig.getTaskDesc());

                    if (orig.getOperatorAttribute() != null) {
                        vo.setEmpTypeZh(orig.getOperatorAttribute().getZhCn());
                        vo.setEmpTypeEn(orig.getOperatorAttribute().getEnUs());
                    }

                    return vo;
                }).collect(Collectors.toList());
        batchTaskVo.setOperators(vos);
    }

    /**
     * 构建操作人员名称
     *
     * @param empNameCnOrZh 中文或英文
     * @param empName empName
     * @param batchTaskVo batchTaskVo
     * @return 操作人员
     */
    private static String buildOperatorName(String empNameCnOrZh, String empName, BatchTask4AuthVO batchTaskVo) {
        if (StringUtils.isEmpty(empNameCnOrZh)) {
            log.error("BatchTask4AuthAbility#fillOperators batchCode:{}, batchName:{} get empNameEn isEmpty",
                    batchTaskVo.getBatchCode(), batchTaskVo.getBatchName());
            return empName;
        }
        return empNameCnOrZh;
    }

    private static void fillInstructions(BatchTask4AuthVO batchTaskVo, List<HighInstruction> instructions) {
        List<String> instrTexts = instructions.stream()
                .map(HighInstruction::getInstructions)
                .collect(Collectors.toList());
        batchTaskVo.setHighRiskInstructions(instrTexts);
    }

    private static Config config(Class<? extends BaseEntity> entity) {
        return CONFIGS.stream()
                .filter(cfg -> cfg.entity == entity)
                .findFirst()
                .orElse(null);
    }

    private static Config config(String code) {
        return CONFIGS.stream()
                .filter(cfg -> code.startsWith(cfg.codePrefix))
                .findFirst()
                .orElseThrow(() -> new LcapBusiException("invalid code"));
    }

    private static String first(String path) {
        return path == null ? null : path.split(FORWARD_SLASH)[0];
    }

    private static String last(String path) {
        if (path == null) {
            return null;
        }

        String[] array = path.split(FORWARD_SLASH);
        return array[array.length - 1];
    }

    @AllArgsConstructor
    private static class Config {
        private final Class<? extends BaseEntity> entity;

        private final Class<? extends BaseEntity> coEntity;

        private final Class<? extends BaseSubEntity> opObjEntity;

        private final Class<? extends BaseSubEntity> operatorEntity;

        private final Class<? extends BaseSubEntity> highRiskInstrEntity;

        private final String codePrefix;

        private final String urlCfgKey;

        private final List<String> coFields;

        private final String batchCodeField = BatchTaskFieldConsts.BATCH_CODE;

        private final String opObjBatchNoField = OperationObjectFieldConsts.BATCH_INFO_NO;

        private final List<String> fields = Collections.unmodifiableList(Lists.newArrayList(
                BatchTaskFieldConsts.CHANGE_ORDER_ID,
                BatchTaskFieldConsts.BATCH_CODE,
                BatchTaskFieldConsts.BATCH_NO,
                BatchTaskFieldConsts.BATCH_NAME,
                BatchTaskFieldConsts.CURRENT_STATUS,
                BatchTaskFieldConsts.OPERATION_PLAN_DESC,
                BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME,
                BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME,
                BatchSummaryFieldConsts.OPERATION_DESCRIPTION,
                ChangeOrderFieldConsts.SOURCE));

        private final List<String> opObjFields = Collections.unmodifiableList(Lists.newArrayList(
                OperationObjectFieldConsts.NETWORK_ID,
                OperationObjectFieldConsts.PRODUCT_MODEL));

        private final List<String> operatorFields = Collections.unmodifiableList(Lists.newArrayList(
                OperatorFieldConsts.OPERATOR_ROLE,
                OperatorFieldConsts.REMOTE_FLAG,
                OperatorFieldConsts.OPERATE_PERSON,
                OperatorFieldConsts.OPERATOR_PHONE,
                OperatorFieldConsts.OPERATOR_ATTRIBUTE,
                OperatorFieldConsts.TASK_DESC));

        private final List<String> highRiskInstrFields = Collections.unmodifiableList(Lists.newArrayList(
                ChangeOrderFieldConsts.HIGH_RISK_INSTRUCTION));
    }
}
