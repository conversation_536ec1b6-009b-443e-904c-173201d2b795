package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.common.helper.QueryDataHelper.queryOne;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.ORDER_NO;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PartnerChangeOrderAbility {

    /**
     * 检索分包商网络变更单 - 主键
     * @param changeOrderId
     * @return SubcontractorChangeOrder
     */
    public static SubcontractorChangeOrder get(String changeOrderId) {
        if (!StringUtils.hasText(changeOrderId)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList();
        return QueryDataHelper.get(SubcontractorChangeOrder.class, fieldList, changeOrderId);
    }

    public static SubcontractorChangeOrder get(String id, List<String> fields) {
        return QueryDataHelper.get(SubcontractorChangeOrder.class, fields, id);
    }

    /**
     * 检索分包商网络变更单 - 单据编号
     * @param orderNo
     * @return SubcontractorChangeOrder
     */
    public static SubcontractorChangeOrder getByCoNo(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            return null;
        }
        List<String> fieldList = Lists.newArrayList();
        return queryOne(SubcontractorChangeOrder.class,
                fieldList,
                com.google.common.collect.Lists.newArrayList(new Filter(ORDER_NO, Comparator.EQ, orderNo)));
    }

    /**
     * 检索特定子实体数据
     * @param pid
     * @param subEntityEnum
     * @param fieldList
     * @return List<T>
     * @param <T>
     */
    public static <T extends BaseSubEntity> List<T> listSpecificSubEntity(String pid,
                                                                          Class<? extends BaseSubEntity> subEntityEnum,
                                                                          List<String> fieldList) {
        // 1.子实体信息校验
        if (!StringUtils.hasText(pid)
                || Objects.isNull(subEntityEnum)
                || !SubcontractorChangeOrder.class.equals(EntityHelper.getMainClass(subEntityEnum))) {
            return Lists.newArrayList();
        }

        // 2.检索子实体数据
        return QueryDataHelper.query(subEntityEnum, fieldList, pid);
    }

    /**
     * 更新合作方网络变更单
     * @param subcontractorChangeOrder
     */
    public static void update(SubcontractorChangeOrder subcontractorChangeOrder) {
        if (Objects.isNull(subcontractorChangeOrder)) {
            return;
        }

        SaveDataHelper.update(subcontractorChangeOrder);
    }
}
