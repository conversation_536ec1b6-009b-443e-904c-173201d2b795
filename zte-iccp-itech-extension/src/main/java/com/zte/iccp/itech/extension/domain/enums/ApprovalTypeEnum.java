package com.zte.iccp.itech.extension.domain.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 审批人配置类型
 * <AUTHOR>
 * @date 2024/4/23 上午9:02
 */
@Getter
public enum ApprovalTypeEnum {
    /**
     * 核心网大区TD审核
     */
    CN_REGION_TD("PAGE0987049584062627840", "", "", ""),

    /**
     * 代表处人员配置
     */
    PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE("PAGE0987087891521863681", "", "", ""),

    /**
     * 技术交付部/网络处配置
     */
    TECHNOLOGY_DELIVERY_DEPT_NETWORK_OFFICE("PAGE0989546883556835330", "multiselectfield_20ssk5ab", "selectfield_3scqrpg6", "custom_ray53sa3"),

    /**
     * 远程中心方案配置
     */
    REMOTE_CENTER_SOLUTION("PAGE0990181617974640641", "multiselectfield_20ssk5ab", "selectfield_3scqrpg6", "custom_v5b81q7m"),

    /**
     * 远程中心操作配置
     */
    REMOTE_CENTER_OPERATIONS("PAGE0990635724250284033", "multiselectfield_20ssk5ab", "selectfield_3scqrpg6", "custom_yerfm7ic"),

    /**
     * 网络部配置
     */
    NETWORK_DEPT("PAGE0990640868614471681", "multiselectfield_20ssk5ab", "selectfield_3scqrpg6", "custom_dkidap47"),

    /**
     * 一体化关联产品配置
     */
    INTEGRATED_ASSOCIATED_PRODUCT("PAGE0990650180900487169", "", "", ""),

    /**
     * 研发部配置
     */
    RD_DEPARTMENT("PAGE0990927416924831745", "multiselectfield_20ssk5ab", "selectfield_3scqrpg6", "custom_g3vubvu9"),

    /**
     * 研发领导配置
     */
    RD_LEADER("PAGE0990661264080371714", "multiselectfield_20ssk5ab", "selectfield_3scqrpg6", "custom_f4ayt1g2"),

    /**
     * IT外购件配置
     */
    PURCHASED_IT_COMPONENTS("PAGE0990664394587242497", "", "", ""),

    /**
     * 行政领导-三营审核配置
     */
    ADMINISTRATIVE_LEADER_DIVISION_THREE("PAGE0990697172703903745", "", "", ""),

    /**
     * 行政领导-国际会签配置
     */
    ADMINISTRATIVE_LEADER_INTERNATIONAL_COUNTERSIGNING("PAGE0990989618474123266", "", "", ""),

    /**
     * 行政领导-产品会签配置
     */
    ADMINISTRATIVE_LEADER_PRODUCT_COUNTERSIGNING("PAGE0990915024820109313", "", "", ""),

    /**
     * 行政领导-代表处会签配置
     */
    ADMINISTRATIVE_LEADER_RESPONSIBLE_DEPT_COUNTERSIGNING("PAGE0990918466204598273", "", "", ""),

    /**
     * 集成关联产品配置
     */
    INTEGRATE_ASSOCIATED_PRODUCT("integrated_associated_product", "", "", ""),

    /**
     * 网络处总工
     */
    NETWORK_CHIEF_ENGINEER("network_chief_engineer", "", "", ""),

    /**
     * 故障经理配置
     */
    FAULT_MANAGER("PAGE1118636599888949251", "", "", ""),

    ;

    private final String pageId;

    /**
     * 产品型号DataKey
     */
    private final String productModel;

    /**
     * 逻辑网元DataKey
     */
    private final String logicNe;

    /**
     * 产品型号/逻辑网元DataKey
     */
    private final String modelOrLogic;

    ApprovalTypeEnum(String pageId, String productModel, String logicNe, String modelOrLogic) {
        this.pageId = pageId;
        this.productModel = productModel;
        this.logicNe = logicNe;
        this.modelOrLogic = modelOrLogic;
    }

    /**
     * 通过审批节点页面id 获取对应的默认列表过滤条件
     */
    public static List<String> getApproverConfigSearchFilterParam(String currentPageId) {
        for (ApprovalTypeEnum approvalTypeEnum : ApprovalTypeEnum.values()) {
            if (approvalTypeEnum.pageId.equals(currentPageId)) {
                return Lists.newArrayList(approvalTypeEnum.name());
            }

        }
        return new ArrayList<>();
    }

    public static ApprovalTypeEnum getApprovalTypeEnum(String currentPageId) {
        for (ApprovalTypeEnum approvalTypeEnum : ApprovalTypeEnum.values()) {
            if (approvalTypeEnum.pageId.equals(currentPageId)) {
                return approvalTypeEnum;
            }

        }
        return null;
    }

    public static ApprovalTypeEnum getApprovalTypeEnumByName(String name) {
        for (ApprovalTypeEnum approvalTypeEnum : ApprovalTypeEnum.values()) {
            if (approvalTypeEnum.name().equals(name)) {
                return approvalTypeEnum;
            }
        }
        return null;
    }

    public static List<String> getAllApprovalType() {
        List<String> approvalTypes = new ArrayList<>();
        for (ApprovalTypeEnum approvalTypeEnum : ApprovalTypeEnum.values()) {
            approvalTypes.add(approvalTypeEnum.name());
        }
        return approvalTypes;
    }
}
