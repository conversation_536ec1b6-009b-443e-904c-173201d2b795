package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.PressEmailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.extern.slf4j.Slf4j;


/**
 * 提交申请 - 代办通知
 * 规则：【网络变更操作催办】操作主题+（是否紧急）+已进入下一流程节点名称环节，请您及时处理！
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/22
 */
@Slf4j
public class SubmitApplyAgencyMailToPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        PressEmailPlugin plugin = new PressEmailPlugin();
        return plugin.anyTrigger(body, out);
    }
}
