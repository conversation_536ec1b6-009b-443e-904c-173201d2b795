package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.OperationSchemeStatusEnum;
import com.zte.iccp.itech.extension.domain.model.OperationScheme;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperationSchemeFieldConsts.BILL_ID;

/**
 * <AUTHOR>
 * @date 2024/7/4 下午2:10
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OperationSchemeAbility {

    /**
     * 变更单状态变更，需要更新操作方案状态 （所有描述包括 变更单 和 分包商-变更单）
     * 主任务完成后，更新对应操作方案的状态： 已发布 OperationSchemeStatusEnum.RELEASED
     * 主任务完成审批前流程中止或操作取消，更新对应操作方案的状态： 已废止 OperationSchemeStatusEnum.ABOLISHED
     *
     * @param billId     变更单id（change_order 主键id）
     * @param statusEnum 操作方案状态枚举
     */
    public static void update(String billId, OperationSchemeStatusEnum statusEnum) {
        List<OperationScheme> operationSchemes = get(billId);
        operationSchemes.forEach(item -> item.setSchemeStatus(TextValuePairHelper.buildList(
                statusEnum.name(), statusEnum.getZhCn(), statusEnum.getEnUs())));
        SaveDataHelper.batchUpdate(operationSchemes);
    }

    /**
     * 删除任务所属操作方案
     */
    public static void delete(Assignment assignment) {
        List<OperationScheme> operationSchemes = get(assignment.getEntityId());
        List<String> ids = operationSchemes.stream()
                .map(OperationScheme::getId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        SaveDataHelper.batchDelete(OperationScheme.class, ids);
    }

    /**
     * @param assignmentIds 任务中心ids
     */
    public static void deletes(List<String> assignmentIds) {
        List<Assignment> assignments = AssignmentAbility.querySpecificTypeAssignment(assignmentIds, Assignment.class);
        if (CollectionUtils.isEmpty(assignments)) {
            return;
        }
        List<String> billIds = assignments.stream().map(Assignment::getBillId).collect(Collectors.toList());

        Filter filter = new Filter(BILL_ID, Comparator.IN, billIds);
        List<OperationScheme> operationSchemes = QueryDataHelper.query(OperationScheme.class, Lists.newArrayList(), Lists.newArrayList(filter));

        List<String> ids = operationSchemes.stream().map(OperationScheme::getId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        SaveDataHelper.batchDelete(OperationScheme.class, ids);
    }

    /**
     * 通过变更单id 查询操作方案
     *
     * @param billId 变更单id
     */
    public static List<OperationScheme> get(String billId) {
        if (StringUtils.isBlank(billId)) {
            return new ArrayList<>();
        }
        Filter filter = new Filter(BILL_ID, Comparator.EQ, billId);
        return QueryDataHelper.query(OperationScheme.class, Lists.newArrayList(), Lists.newArrayList(filter));

    }
}
