package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.authtask.BatchTask4AuthAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

/**
 * <AUTHOR>
 * @since 2024/08/20
 */
public class CreateAuthTaskPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        if (!Boolean.parseBoolean(ConfigHelper.get("authTask.enabled", "false"))) {
            return false;
        }

        AsyncExecuteUtils.execute(() ->
                BatchTask4AuthAbility.createAuthTask(getEntityClass(body), body.getBusinessId()));
        return false;
    }
}
