package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.*;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.changeorder.TriggerTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.SUBMIT_TIME;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.*;

/**
 * 计划操作单
 *
 * <AUTHOR>
 * @since 2025/07/11
 */
@Getter
@Setter
@BaseEntity.Info("plan_operation_order")
@Slf4j
public class PlanOperationOrder extends BaseEntity {

    @JsonProperty(value = "pl_no")
    private String no;

    @JsonProperty(value = ORDER_NO)
    private String orderNo;

    @JsonProperty(value = BILL_STATUS)
    private String billStatus;

    @ApiModelProperty("操作主题 - 网络变更单名称")
    @JsonProperty(value = OPERATION_SUBJECT)
    private String operationSubject;

    @ApiModelProperty("主题前缀")
    @JsonProperty(value = OPERATION_SUBJECT_PREFIX)
    private String operationSubjectPrefix;

    @JsonProperty(value = RESPONSIBLE_DEPT)
    @ApiModelProperty("代表处")
    private List<TextValuePair> responsibleDept;

    @JsonProperty(value = PRODUCT_CATEGORY)
    @ApiModelProperty("产品分类")
    private List<TextValuePair> productCategory;

    @JsonProperty(value = TRIGGER_TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private TriggerTypeEnum triggerType;

    @JsonProperty(value = IS_GOV_ENT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGovEnt;

    @ApiModelProperty("是否属于GDPR管控项目，Y，是；N，否")
    @JsonProperty(value = IS_GDPR)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGdpr;

    @ApiModelProperty("交付方式，1，远程交付；2，本地交付")
    @JsonProperty(value = DELIVERY_MODE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private DeliveryModeEnum deliveryMode;

    @JsonProperty(value = IMPORTANCE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ImportanceEnum importance;

    @JsonProperty(value = RISK_EVALUATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private RiskEvaluationEnum riskEvaluation;

    @JsonProperty(value = OPERATION_LEVEL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private OperationLevelEnum operationLevel;

    @JsonProperty(value = IS_EMERGENCY_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isEmergencyOperation;

    @ApiModelProperty("紧急操作原因简述")
    @JsonProperty(value = EMERGENCY_OPERATION_REASON)
    private String emergencyOperationReason;

    /**
     * 扩充实体属性 - 是否封网、管控期操作
     */
    @JsonProperty(value = IS_NET_CLOSE_OR_CONTROL_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNetCloseOrControlOperation;

    /**
     * 扩充实体属性 - 封网、管控期操作原因简述
     */
    @JsonProperty(value = NET_CLOSE_OR_CONTROL_OPERATION_REASON)
    private String netCloseOrControlOperationReason;

    @ApiModelProperty("封网、管控期操作附件")
    @JsonProperty(value = CLOSE_OR_CONTROL_OPERATION_ATTACH)
    private Object closeOrControlOperationAttach;

    @JsonProperty(value = OPERATION_TYPE)
    @ApiModelProperty("操作类型")
    private List<TextValuePair> operationType;

    @ApiModelProperty("操作类型分组")
    @JsonProperty(value = OPERATION_TYPE_GROUP)
    private String operationTypeGroup;

    @JsonProperty(value = OPERATION_REASON)
    @ApiModelProperty("操作原因")
    private List<TextValuePair> operationReason;

    @JsonProperty(value = TIME_ZONE)
    @JsonDeserialize(using = LookupValueDeserializer.class)
    private TimeZoneEnum timeZone;

    @ApiModelProperty("计划操作开始时间")
    @JsonProperty(value = OPERATION_START_TIME)
    private Date operationStartTime;

    @ApiModelProperty("计划操作结束时间")
    @JsonProperty(value = OPERATION_END_TIME)
    private Date operationEndTime;

    @JsonProperty(value = INTERNAL_OPERATION_SOLUTION)
    @ApiModelProperty("内部操作方案")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile internalOperationSolution;

    @JsonProperty(value = NE_LIST_FILE)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile neListFile;

    @ApiModelProperty("是否需要授权文件")
    @JsonProperty(value = IS_NEED_AUTHORIZATION_FILE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNeedAuthorizationFile;

    @ApiModelProperty("操作预计投入人天")
    @JsonProperty(value = ESTIMATED_INVESTMENT_TIME)
    private Long estimatedInvestmentTime;

    @JsonProperty(value = EMERGENCY_OPERATION_ATTACH)
    @ApiModelProperty("紧急操作附件")
    private Object emergencyOperationAttach;

    @ApiModelProperty("邮件抄送人")
    @JsonProperty(value = MAIL_COPY)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopyExt;

    @JsonProperty(value = BUSI_INTERRUPT_DURATION)
    @ApiModelProperty("预计业务中断时长")
    private Integer busiInterruptDuration;

    @JsonProperty(value = OPERATION_SUBJECT_SUFFIX)
    @ApiModelProperty("操作主题后缀")
    private String operationSubjectSuffix;

    @ApiModelProperty("是否首次应用（首次应用需发起方案会签）")
    @JsonProperty(value = CidConstants.FIELD_IS_FIRST_APPLICATION_CID)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isFirstTimeApply;

    @JsonProperty(value = GDPR_REQUIRE)
    @ApiModelProperty("GDPR要求")
    private Object gdprRequire;

    @JsonProperty(value = OPERATION_DESC)
    @ApiModelProperty("操作说明")
    private String operationDesc;

    @JsonProperty(value = OFFICE_SOLUTION_REVIEWER)
    @ApiModelProperty("代表处产品TD审核人")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> officeSolutionReviewer;

    @ApiModelProperty("代表处产品TD审核意见")
    @JsonProperty(value = RepProdTdApproveConsts.APPROVE_OPINION)
    private String approveOpinionTdNetDeptApprove;

    @ApiModelProperty("代表处产品TD审核结果")
    @JsonProperty(value = RepProdTdApproveConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultRepProdTdApp;

    @JsonProperty(value = IS_TECHNICAL_NOTICE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否是技术通知单实施")
    private BoolEnum isTechnicalNotice;

    @JsonProperty(value = CHANGE_OPERATION_SOURCE)
    @ApiModelProperty("变更操作来源")
    private List<TextValuePair> changeOperationSource;

    @ApiModelProperty("邮件抄送（代表处产品TD审核）")
    @JsonProperty(value = EMAIL_CC_REP_PROD_TD_APP)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy4Ext;

    @ApiModelProperty("需要网络处总工审核")
    @JsonProperty(value = FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNetworkDeptChiefEngineerApproval;

    @JsonProperty(value = NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM)
    @ApiModelProperty("审核组")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> networkDeptChiefEngineerApprovalTeam;

    @ApiModelProperty("审核结果(网络处总工)")
    @JsonProperty(value = NetworkChiefEngineerConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultNetworkChiefEngineerConsts;

    @ApiModelProperty("审核意见(网络处总工)")
    @JsonProperty(value = NetworkChiefEngineerConsts.APPROVE_OPINION)
    private String approveOpinionNetworkChiefEngine;

    @ApiModelProperty("邮件抄送（网络部总工）")
    @JsonProperty(value = NetworkChiefEngineerConsts.EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> networkChiefEngineerEmail;

    @JsonProperty(value = IS_UPGRADE_TECHNOLOGY)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否需要升级至技术交付部/网络处")
    private BoolEnum isUpgradeTechnology;

    @JsonProperty(value = UPGRADE_TECHNOLOGY_REVIEWER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("技术交付部/网络处审核人")
    private List<Employee> upgradeTechnologyReviewerExt;

    @JsonProperty(value = UPGRADE_TECHNOLOGY_REVIEWER_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("技术交付部/网络处审核组")
    private List<Employee> upgradeTechnologyReviewerTeamExt;

    @ApiModelProperty("技术交付部/网络处意见")
    @JsonProperty(value = TdNetDeptApproveConsts.APPROVE_OPINION)
    private String approveOpinionRepProdTdApp;

    @ApiModelProperty("技术交付部/网络处审核结果")
    @JsonProperty(value = TdNetDeptApproveConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultTdNetDeptApprove;

    @ApiModelProperty("邮件抄送（技术交付部/网络处审核）")
    @JsonProperty(value = EMAIL_CC_TD_NET_DEPT_APP)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy2Ext;

    @ApiModelProperty("驳回时审核结果，PASS：通过；REJECT：驳回")
    @JsonProperty(value = RESULT_REJECTION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveResultEnum resultRejection;

    @ApiModelProperty("驳回时审核意见")
    @JsonProperty(value = OPINION_REJECTION)
    private String opinionRejection;

    @ApiModelProperty("驳回时审核附件")
    @JsonProperty(value = ATTACHMENT_REJECTION)
    private Object attachmentRejection;

    @ApiModelProperty("驳回时审核人")
    @JsonProperty(value = PERSON_REJECTION)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> personRejection;

    @ApiModelProperty("驳回时节点的自定义编码")
    @JsonProperty(value = REJECTION_EXTEND_CODE)
    private String rejectionExtendCode;

    @ApiModelProperty("冲突")
    @JsonProperty(value = "time_conflict")
    private String conflict;

    @ApiModelProperty("特殊场景")
    @JsonProperty(value = IS_SPECIAL_SCENARIO)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isSpecialScenario;

    @ApiModelProperty("提交时间")
    @JsonProperty(value = SUBMIT_TIME)
    private Date submitTime;
}
