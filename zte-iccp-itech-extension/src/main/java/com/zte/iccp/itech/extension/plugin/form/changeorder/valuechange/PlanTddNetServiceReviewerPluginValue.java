package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.plugin.form.changeorder.helper.ReviewerHelper;
import com.zte.iccp.itech.extension.plugin.form.changeorder.helper.TDDNetServiceReviewerHelper;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.COMPONENT_TECHNOLOGY_REVIEW_CID;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.COMPONENT_TECHNOLOGY_REVIEW_TEAM_CID;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ZERO;

/**
 * 技术交付部/网络处配置审核人（组）插件
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public class PlanTddNetServiceReviewerPluginValue implements ValueChangeBaseFormPlugin {

    /**
     * 页面重新加载触发
     *
     * @param args 重新加载参数
     * @return
     */
    @Override
    public void loadData(LoadDataEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = formView.getDataModel();
        List<Employee> employees = EmployeeHelper.getModelEmployees(dataModel.getValue(COMPONENT_TECHNOLOGY_REVIEW_CID));
        if (!CollectionUtils.isEmpty(employees)) {
            return;
        }
        List<ApproverConfiguration> approverConfigurations = TDDNetServiceReviewerHelper.getTechnicalReviewerInfo(dataModel, true);
        setTechnicalReviewer(approverConfigurations, formView, COMPONENT_TECHNOLOGY_REVIEW_CID, false);
        setTechnicalReviewer(approverConfigurations, formView, COMPONENT_TECHNOLOGY_REVIEW_TEAM_CID, true);
    }

    /**
     * 监听事件触发
     *
     * @param args 重新加载参数
     * @return
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = formView.getDataModel();
        List<ApproverConfiguration> approverConfigurations = TDDNetServiceReviewerHelper.getTechnicalReviewerInfo(dataModel, true);
        setTechnicalReviewer(approverConfigurations, formView, COMPONENT_TECHNOLOGY_REVIEW_CID, false);
        setTechnicalReviewer(approverConfigurations, formView, COMPONENT_TECHNOLOGY_REVIEW_TEAM_CID, true);
    }

    public void setTechnicalReviewer(List<ApproverConfiguration> approverConfigurations, IFormView formView, String cid, boolean isTeam) {
        List<Employee> employees = new ArrayList<>();
        if (!CollectionUtils.isEmpty(approverConfigurations)) {
            ApproverConfiguration approverConfiguration = approverConfigurations.get(INTEGER_ZERO);
            employees = approverConfiguration.getApproverPersons();
            if (isTeam) {
                employees = approverConfiguration.getApproverGroups();
            }
        }
        ReviewerHelper.setEmployeeByCid(formView, employees, cid);
    }
}
