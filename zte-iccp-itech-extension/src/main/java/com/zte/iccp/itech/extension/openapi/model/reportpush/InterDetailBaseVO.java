package com.zte.iccp.itech.extension.openapi.model.reportpush;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/5/27 下午7:51
 */
@Setter
@Getter
public class InterDetailBaseVO {
    /**
     * 操作单号
     */
    private String batchCode;

    /**
     * 操作主题
     */
    private String operationSubject;

    /**
     * 操作类型
     */
    private String opTypeZh;

    /**
     * 产品大类
     */
    private String prodMainCategory;

    /**
     * 产品小类
     */
    private String prodSubCategory;

    /**
     * 计划开始操作时间
     */
    private String planOperationStartTime;

    /**
     * 三层组织
     */
    private String organization3;

    /**
     * 三层组织id
     */
    private String organization3Id;

    /**
     * 四层组织
     */
    private String organization4;

    /**
     * 当前状态
     */
    private String currentStatus;

    /**
     * 操作负责人
     */
    private String opRespEmp;

    /**
     * 操作人员
     */
    private String opEmp;

    /**
     * 值守人员
     */
    private String dutyEmp;

    /**
     * 产品经营团队
     */
    private String prodTeam;

    /**
     * 产品线
     */
    private String prodLine;

    /**
     * 产品线id
     */
    private String prodLineId;

    /**
     * 产品分类
     */
    private String prodClassPath;

    /**
     * 产品分类id
     */
    private String prodClassIdPath;

    /**
     * 组织
     */
    private String organizationPath;

    /**
     * 组织id
     */
    private String organizationIdPath;
}
