package com.zte.iccp.itech.extension.plugin.form.faultmanagement;

import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.enums.AfterPropertyChangedEnum;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.PropertyChangedBaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata.CloudSubwayInitPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata.FaultTaskDetailPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata.FaultTaskStatusPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata.WarRoomSubwayInitPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.propertychanged.FaultReviewTaskButtonPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.propertychanged.SatisfactionResponsiblePersonPlugin;
import com.zte.paas.lcap.core.event.LoadBillTableEvent;
import com.zte.paas.lcap.core.event.LoadDataEvent;
import com.zte.paas.lcap.core.event.PropertyChangedEvent;
import com.zte.paas.lcap.core.event.SearchEvent;

import java.util.Set;

public class FormPlugin extends BaseFormPlugin {

    private static final Set<LoadDataBaseFormPlugin> LOAD_DATA_PLUGINS = Sets.newHashSet(
            new FaultTaskDetailPlugin(),
            new FaultTaskStatusPlugin(),
            new CloudSubwayInitPlugin(),
            new WarRoomSubwayInitPlugin(),
            new FaultReviewTaskButtonPlugin(),
            new SatisfactionResponsiblePersonPlugin()
    );

    @Override
    public void afterLoadData(LoadDataEvent e) {
        for (LoadDataBaseFormPlugin loadDataBaseFormPlugin : LOAD_DATA_PLUGINS) {
            loadDataBaseFormPlugin.loadData(new LoadDataBaseFormPlugin.LoadDataEventArgs(this, e,getModel()));
        }
    }

    @Override
    public void afterLoadBillTable(LoadBillTableEvent e) {
    }

    @Override
    public void propertyChanged(PropertyChangedEvent event) {
        String propId = event.getChangeSet()[CommonConstants.INTEGER_ZERO].getProperty().getKey();
        for (PropertyChangedBaseFormPlugin valueChangeBaseFormPlugin
                : AfterPropertyChangedEnum.getValueChangedEventPlugins(propId)) {
            valueChangeBaseFormPlugin
                    .operate(new PropertyChangedBaseFormPlugin.ValueChangedEventArgs(this, getModel(), event, propId));
        }
    }

    @Override
    public void afterSearch(SearchEvent searchEvent) {
        System.out.println("zpc");
    }
}
