package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/28
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum RiskEvaluationEnum implements SingletonTextValuePairsProvider {
    /** 一星 */
    STAR1("1", "★", "★"),
    /** 二星 */
    STAR2("2", "★★", "★★"),
    /** 三星 */
    STAR3("3", "★★★", "★★★"),
    ;

    private final String value;

    private final String zhCn;

    private final String enUs;

    public int getIntValue() {
        return Integer.parseInt(value);
    }

    public static RiskEvaluationEnum fromValue(String value) {
        for (RiskEvaluationEnum riskEvaluation : RiskEvaluationEnum.values()) {
            if (String.valueOf(riskEvaluation.getValue()).equals(value)) {
                return riskEvaluation;
            }
        }

        return null;
    }

    public String getName(String lang) {
        return ZH_CN.equals(lang) ? this.zhCn : this.enUs;
    }

    public static String getStarStr(String value) {
        for (RiskEvaluationEnum riskEvaluation : RiskEvaluationEnum.values()) {
            if (riskEvaluation.getValue().equals(value)) {
                return riskEvaluation.getZhCn();
            }
        }

        return "";
    }
}
