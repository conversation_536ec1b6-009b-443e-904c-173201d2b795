package com.zte.iccp.itech.extension.ability.common.table;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.FilterHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.vo.TableDisplayRows;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import com.zte.paas.lcap.ddm.form.list.FilterCombinedCondition;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.*;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class DataAbility {

    /**
     * 列表补充数据
     * entitySupplementaryData 外层 Map - Key: 实体主键   value: 实体待补充数据
     * entitySupplementaryData 内层 Map - Key: 列 dataKey   value: 对应属性数据
     */
    public static void setTableData(
            JSONArray entityArray,
            String idKey,
            Map<String, Map<String, String>> entitySupplementaryData) {

        if (CollectionUtils.isEmpty(entityArray)) {
            return;
        }

        for (int i = 0; i < entityArray.size(); i++) {
            JSONObject entityInfo = entityArray.getJSONObject(i);
            String id = entityInfo.getString(idKey);

            Map<String, String> supplementaryData = entitySupplementaryData.getOrDefault(id, new HashMap<>());
            entityInfo.putAll(supplementaryData);
        }
    }

    /**
     * 未绑定实体列表展示数据
     *
     */
    public static <T> void setNoneEntityTableData(
            IFormView formView,
            String tableCid,
            TableDisplayRows<T> tableRows) {

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(DATA, JSON.parseObject(JSON.toJSONString(tableRows)));

        Map<String, Object> viewMap = new HashMap<>();
        viewMap.put(tableCid, dataMap);

        formView.getClientViewProxy().setProps(viewMap);
    }

    /**
     * 获取记录主键
     */
    public static List<String> getSearchRecordsPkId(JSONArray recordArray, String pkIdKey) {
        List<String> pkIds = Lists.newArrayList();
        if (recordArray.isEmpty()) {
            return pkIds;
        }

        for (int i = 0; i < recordArray.size(); i++) {
            JSONObject record = recordArray.getJSONObject(i);
            pkIds.add(record.getString(pkIdKey));
        }

        return pkIds;
    }

    /**
     * 设置表格查询条件
     */
    public static void setTableQueryCondition(
            IFormView formView,
            String tableCid,
            List<IFilter> iFilters,
            Class<? extends BaseEntity> entityClass) {

        // 1.去除空条件
        iFilters = iFilters.stream().filter(Objects::nonNull).collect(Collectors.toList());

        // 2.获取表格
        TablePc tableInfo = (TablePc) formView.getControl(tableCid);

        // 3.包装自定义条件集合
        iFilters = FilterHelper.handleFilters(entityClass, iFilters);

        // 4.设置表格查询默认参数
        FilterCombinedCondition condition = new FilterCombinedCondition();
        condition.getCustomFilters().addAll(iFilters);
        tableInfo.setFilters(condition, false);
    }

    /**
     * 隐藏列
     */
    public static void hiddenTableColumn(
            IFormView formView,
            String tableCid,
            List<String> columnFieldIds) {

        if (!StringUtils.hasText(tableCid) || CollectionUtils.isEmpty(columnFieldIds)) {
            return;
        }

        List<Map<String, Object>> columnAttributes = columnFieldIds.stream()
                .map(item -> new BasicAttributeBuilder()
                        .attribute(TYPE, COLUMNS)
                        .attribute(CID, item)
                        .attribute(PROPS, ImmutableMap.of(PROPS_BEHAVIOR, HIDDEN))
                        .build())
                .collect(Collectors.toList());

        Map<String, Object> args = MapUtils.newHashMap(TARGET, tableCid, VALUE, columnAttributes);
        formView.getClientViewProxy().changeProps(Collections.singletonList(args));
    }
}
