package com.zte.iccp.itech.extension.domain.model.clockin;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @author: 李江斌 10318434
 * @date: 2025/1/2
 */
@Getter
@Setter
public class OnDutyStageTime {

    private Integer stageNumber;

    private Date startTimeDate;

    private Date endTimeDate;

    // 每个值守阶段的打卡记录（正常就一次正常卡，有异常卡的情况下里面包含且仅包含一条正常卡（此正常卡不包括恢复卡））
    private List<ClockInRecord> stageRecords = new ArrayList<>();
}
