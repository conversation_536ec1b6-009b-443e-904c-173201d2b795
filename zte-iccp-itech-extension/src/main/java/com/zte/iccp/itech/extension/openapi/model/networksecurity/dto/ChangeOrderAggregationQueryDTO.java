package com.zte.iccp.itech.extension.openapi.model.networksecurity.dto;


import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 变更单查询DTO（三营）
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/3
 */
@Getter
@Setter
public class ChangeOrderAggregationQueryDTO extends BaseQueryDTO {
    /** 变更单ids */
    private List<String> changeOrderIds;

    /** 变更操作单codes */
    private List<String> coNos;

    /** 任务类型：1内部变更单  6合作方变更单 */
    private String assignmentType;

    /** 任务名称 / 单号 */
    private String assignmentName;

    /** 主任务状态 */
    private String assignmentStatus;

    /** 客户标识 */
    private List<String> accnTypes;

    /** 操作类型 */
    private List<String> operationTypes;

    /** 提交人 */
    private List<String> commitBys;

    /**
     * 参数校验&设置日期条件
     */
    public void validateAndSetDateCondition() {
        // 1.如果当前对象的属性值为空并且父BaseQueryDTO的属性值也为空（不包含分页条件），则增加更新时间作为默认查询条件，默认查询一个月
        if ((this.changeOrderIds == null || this.changeOrderIds.isEmpty())
                && (this.coNos == null || this.coNos.isEmpty())
                && (this.accnTypes == null || this.accnTypes.isEmpty())
                && (this.operationTypes == null || this.operationTypes.isEmpty())
                && (this.commitBys == null || this.commitBys.isEmpty())
                && this.assignmentType == null
                && this.assignmentName == null
                && this.assignmentStatus == null
                && super.isEmptyObject()) {

            super.setDefaultValueIfEmpty();

            // 2.存在任意一个查询条件时
        } else {
            // 1.参数合法性校验
            validateInput();
        }
    }

    /**
     * 参数合法性校验
     */
    public void validateInput() {
        if (!CollectionUtils.isEmpty(this.accnTypes) && this.accnTypes.size() > CommonConstants.INTEGER_TWENTY) {
            throw new IllegalArgumentException(MsgUtils.getLangMessage(CommonConstants.ZH_CN, MessageConsts.ACCN_TYPE_MAX_NUMBER_ERROR));
        }

        if (!CollectionUtils.isEmpty(this.operationTypes) && this.operationTypes.size() > CommonConstants.INTEGER_TWENTY) {
            throw new IllegalArgumentException(MsgUtils.getLangMessage(CommonConstants.ZH_CN, MessageConsts.OPERATION_TYPE_MAX_NUMBER_ERROR));
        }

        super.validateInput();
    }
}
