package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.*;
import com.zte.iccp.itech.extension.domain.model.PlanOperationOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;

@ApiModel("操作对象")
@Setter
@Getter
@BaseSubEntity.Info(value = "operation_object", parent = PlanOperationOrder.class)
public class PlanOperationObject extends OperationObject {

    @JsonProperty(value = COUNTRY)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText country;

    @JsonProperty(value = PROVINCE)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText province;

    @JsonProperty(value = CITY)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText city;

    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;

    @JsonProperty(value = ACCN_TYPE)
    @ApiModelProperty("客户标识")
    private String customerTypeFlag;


}
