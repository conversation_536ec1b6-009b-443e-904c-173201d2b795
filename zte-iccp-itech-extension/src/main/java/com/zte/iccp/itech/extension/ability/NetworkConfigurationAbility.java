package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.model.NetworkConfiguration;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.NetworkConfigurationFieldConsts.*;

public class NetworkConfigurationAbility {
    /**
     * 批量检索网络配置信息
     * 检索条件 - 网络ID
     * @param networkIds
     * @return List<NetworkConfiguration>
     */
    public static List<NetworkConfiguration> queryByNetworkId(List<String> networkIds) {
        if (CollectionUtils.isEmpty(networkIds)) {
            return Lists.newArrayList();
        }

        List<String> fieldList = Lists.newArrayList(
                ID, NETWORK_ID, NETWORK_ATTRIBUTE, GRADE_SCORE, LAST_MODIFIED_BY, LAST_MODIFIED_TIME);

        Filter networkIdFilter = new Filter(NETWORK_ID, Comparator.IN, networkIds);
        List<IFilter> conditionFilterList = Lists.newArrayList(networkIdFilter);

        return QueryDataHelper.query(NetworkConfiguration.class, fieldList, conditionFilterList);
    }

    /**
     * 单个检索网络配置信息
     * 检索条件 - 网络ID
     * @param networkId
     * @return List<NetworkConfiguration>
     */
    public static NetworkConfiguration queryOneByNetworkId(String networkId) {
        if (!StringUtils.hasText(networkId)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList(
                ID, NETWORK_ID, NETWORK_ATTRIBUTE, GRADE_SCORE, LAST_MODIFIED_BY, LAST_MODIFIED_TIME);

        Filter networkIdFilter = new Filter(NETWORK_ID, Comparator.EQ, networkId);
        List<IFilter> conditionFilterList = Lists.newArrayList(networkIdFilter);

        return QueryDataHelper.queryOne(NetworkConfiguration.class, fieldList, conditionFilterList);
    }

    /**
     * 新增网络配置信息
     * @param networkConfiguration
     */
    public static void insertNetworkConfiguration(NetworkConfiguration networkConfiguration) {
        if (Objects.isNull(networkConfiguration)) {
            return;
        }

        SaveDataHelper.create(networkConfiguration);
    }

    /**
     * 更新网络配置信息
     * @param networkConfiguration
     */
    public static void updateNetworkConfiguration(NetworkConfiguration networkConfiguration) {
        if (Objects.isNull(networkConfiguration)) {
            return;
        }

        SaveDataHelper.update(networkConfiguration);
    }
}
