package com.zte.iccp.itech.extension.ability.clockin;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.clockin.callplan.ClockInCallPlanAbolishAbility;
import com.zte.iccp.itech.extension.ability.clockin.callplan.ClockInCallPlanCreateAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.AlarmUtils;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.RetryUtils;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.utils.StackTraceUtils.getStackTrace;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ZERO;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.ClockIn.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ASSIGNMENT_TYPE;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ENTITY_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.NetworkChangeFieldConsts.CREATE_CLOCK_IN_TASK;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CLOCK_IN_STATE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum.BATCH_TASK;

/**
 * <AUTHOR>
 * @since 2024/09/05
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class ClockInTaskUpdateAbility {

    public static void cancelClockInTasks(String batchTaskId) {
        log.info("cancelClockInTasks: {}\n{}", batchTaskId, getStackTrace("com.zte"));

        List<ClockInTask> clockInTasks = QueryDataHelper.query(
                ClockInTask.class,
                Lists.newArrayList(ID, BATCH_TASK_ID, ENTITY_TYPE),
                Lists.newArrayList(
                        new Filter(BATCH_TASK_ID, Comparator.EQ, batchTaskId),
                        new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(BoolEnum.N))));
        clockInTasks.forEach(t -> t.setAbolished(BoolEnum.Y));
        if (CollectionUtils.isEmpty(clockInTasks)) {
            return;
        }
        // 清空打卡阶段
        List<Map<String, Object>> multiValues = new ArrayList<>();
        List<Map<String, Object>> subMultiValues = new ArrayList<>();
        if (clockInTasks.get(INTEGER_ZERO).getEntityType() == BATCH_TASK) {
            multiValues.add(MapUtils.newHashMap(ID, batchTaskId,
                    CLOCK_IN_STATE, null));
        } else {
            subMultiValues.add(MapUtils.newHashMap(ID, batchTaskId,
                    CLOCK_IN_STATE, null));
        }
        // 重置任务表中的create_clock_in_task字段（变更通告、操作取消、挂起的单据不在打卡单页面展示）
        Assignment assignment = QueryDataHelper.queryOne(
                Assignment.class,
                Lists.newArrayList(ID),
                Lists.newArrayList(new Filter(ENTITY_ID, Comparator.EQ, batchTaskId)));
        List<Map<String, Object>> assignmentMultiValue = Lists.newArrayList(
                MapUtils.newHashMap(ID, assignment.getId(), CREATE_CLOCK_IN_TASK, null));

        TransactionHelper.run(() -> {
            SaveDataHelper.batchUpdate(clockInTasks);

            List<String> taskIds = clockInTasks.stream()
                    .map(BaseEntity::getId)
                    .collect(Collectors.toList());
            new ClockInCallPlanAbolishAbility().abolish(taskIds);
            //清空【是否创建打卡任务】字段
            SaveDataHelper.batchUpdate(Assignment.class, assignmentMultiValue);
            //清空批次任务打卡阶段
            SaveDataHelper.batchUpdate(BatchTask.class, multiValues);
            //清空分包商批次任务打卡阶段
            SaveDataHelper.batchUpdate(SubcontractorBatchTask.class, subMultiValues);
        });
    }

    public static void batchCancelClockInTasks(List<String> batchTaskIds) {
        AsyncExecuteUtils.execute(() -> {
            batchTaskIds.forEach(item -> {
                try {
                    RetryUtils.run(() -> cancelClockInTasks(item));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    AlarmUtils.major("Cancel Clock-in Task Error", e);
                }
            });
        });
    }

    public static boolean changeOwner(String clockInTaskId, String targetUserId) {
        ClockInTask clockInTask = QueryDataHelper.get(
                ClockInTask.class, Lists.newArrayList(TASK_TYPE, OPERATOR, ID, ENTITY_TYPE, OPERATION_SUBJECT,
                        BATCH_TASK_ID, CHANGE_ORDER_ID, STAGE_ON_DUTY_END_TIME, TRANSFER_FREQUENCY), clockInTaskId);
        if (clockInTask == null) {
            throw new LcapBusiException(TASK_NOT_EXISTS);
        }

        if (clockInTask.getTaskType() != ClockInTaskTypeEnum.ON_DUTY) {
            throw new LcapBusiException(CAN_ONLY_CHANGE_ON_DUTY_TASK_OWNER);
        }

        if (!clockInTask.getOperator().getId().equals(ContextHelper.getEmpNo())) {
            throw new LcapBusiException(CAN_ONLY_CHANGE_TASK_OWNER_FROM_YOURSELF);
        }
        // 判断转交对象是不是转交前的人
        if (clockInTask.getOperator().getId().equals(targetUserId)) {
            throw new LcapBusiException(CAN_ONLY_CHANGE_TASK_OWNER_TO_OTHER_USER);
        }
        SingleEmployee sourceOperator = clockInTask.getOperator();
        clockInTask.setOperator(EmployeeHelper.getSingle(targetUserId));
        Integer transferFrequency = clockInTask.getTransferFrequency();
        clockInTask.setTransferFrequency(transferFrequency == null ? 1 : transferFrequency + 1);
        boolean updateFlag = SaveDataHelper.update(clockInTask);
        if (updateFlag) {
            // 将转交后的责任人，记录为变更任务的关联人员
            fillRelevanceByChangeOrderId(clockInTask.getChangeOrderId(), targetUserId);
            try {
                // 转交以及创建呼叫计划
                new ClockInCallPlanCreateAbility().transferAiCall(clockInTask, sourceOperator);
            } catch (Exception e) {
                log.error("transferAiCall exception! clockInTaskId:{}, e:{}", clockInTaskId, e);
            }
        }
        return updateFlag;
    }

    private static void fillRelevanceByChangeOrderId(String changeOrderId, String person) {
        // 1、根据网络变更单id查询对应的任务id
        Filter entityIdFilter = new Filter(ENTITY_ID, Comparator.EQ, changeOrderId);
        Filter netChangeTaskFilter = new Filter(ASSIGNMENT_TYPE, Comparator.R_CONTAINS,
                Arrays.asList(AssignmentTypeEnum.NETWORK_CHANGE.getValue(),
                        AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue()));
        List<IFilter> conditionFilterList = Lists.newArrayList(entityIdFilter, netChangeTaskFilter);

        Assignment assignment = QueryDataHelper.queryOne(Assignment.class,
                Arrays.asList(ID, ENTITY_ID), conditionFilterList);
        if (null == assignment) {
            return;
        }
        // 2、新增关联关系
        AssignmentAbility.createAssignmentPersonRelevance(assignment.getId(), Lists.newArrayList(person));
    }
}
