package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.event.ChangeData;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.*;
import static org.springframework.util.CollectionUtils.isEmpty;

/**
 * 同步操作对象计划时间= 批次概要的计划时间
 */
public class PlanBatchSummaryDatePluginValue implements ValueChangeBaseFormPlugin {

    @Override
    public void operate(ValueChangedEventArgs args) {
        ChangeData change = args.getEvent().getChangeSet()[0];
        String prop = change.getProperty().getKey();
        switch (prop) {
            case PLAN_OPERATION_START_TIME:
                sync(args, change, PLAN_OPERATION_START_TIME_OBJ, change.getNewValue());
                break;
            case PLAN_OPERATION_END_TIME:
                sync(args, change, PLAN_OPERATION_END_TIME_OBJ, change.getNewValue());
                break;
            case BATCH_NO:
                syncBatch(args, change);
                break;
            default:
                break;
        }
    }

    private void sync(ValueChangedEventArgs args, ChangeData change, String targetField, Object value) {
        String batchNo = resolveBatchNo(args, change.getRowIndex());
        if (batchNo == null) {
            return;
        }
        IDataEntityCollection objectEntities = args.getModel().getEntryRowEntities(OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        if (isEmpty(objectEntities)) {
            return;
        }
        objectEntities.forEach(d -> {
            Object batchNoObj = ((DynamicDataEntity) d).get(FIELD_OPERATION_OBJECT_BATCH_NO_PROPERTY_KEY);
            if (batchNo.equals(TextValuePairHelper.getValue(batchNoObj))) {
                int index = ((DynamicDataEntity) d).getRowIndex();
                args.getModel().setValue(targetField, value, index);
            }
        });
    }

    private void syncBatch(ValueChangedEventArgs args, ChangeData change) {
        int row = change.getRowIndex();
        String batchNo = resolveBatchNo(args, row);
        if (batchNo == null) {
            return;
        }

        List<Object> start = args.getModel().getEntryColumnObject(FIELD_BATCH_SUMMARY_CID, PLAN_OPERATION_START_TIME, row);
        List<Object> end = args.getModel().getEntryColumnObject(FIELD_BATCH_SUMMARY_CID, PLAN_OPERATION_END_TIME, row);
        if (isEmpty(start) || isEmpty(end)) {
            return;
        }

        IDataEntityCollection objectEntities = args.getModel().getEntryRowEntities(OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        if (isEmpty(objectEntities)) {
            return;
        }
        objectEntities.forEach(d -> {
            Object batchNoObj = ((DynamicDataEntity) d).get(FIELD_OPERATION_OBJECT_BATCH_NO_PROPERTY_KEY);
            if (batchNo.equals(TextValuePairHelper.getValue(batchNoObj))) {
                int index = ((DynamicDataEntity) d).getRowIndex();
                args.getModel().setValue(PLAN_OPERATION_START_TIME_OBJ, start.get(0), index);
                args.getModel().setValue(PLAN_OPERATION_END_TIME_OBJ, end.get(0), index);
            }
        });

    }

    private String resolveBatchNo(ValueChangedEventArgs args, int row) {
        List<Object> textObject = args.getModel().getEntryColumnObject(FIELD_BATCH_SUMMARY_CID, BATCH_NO, row);
        if (CollectionUtils.isEmpty(textObject)) {
            return null;
        }
        List<MultiLangText> valueMultiLangListList = JsonUtils.parseArray(textObject, MultiLangText.class);
        return CollectionUtils.isEmpty(valueMultiLangListList) ? null : valueMultiLangListList.get(row).getValue();
    }
}