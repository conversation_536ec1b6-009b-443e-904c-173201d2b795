package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.enums.IValueEnum;
import lombok.SneakyThrows;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2024/08/21
 */
@SuppressWarnings("rawtypes")
public class IValueEnumDeserializer extends JsonDeserializer<IValueEnum> {
    @SneakyThrows
    @Override
    public IValueEnum deserialize(JsonParser p, DeserializationContext ctxt) {
        Field field = JsonUtils.findField(p.getCurrentValue().getClass(), p.getCurrentName());
        if (field == null) {
            return null;
        }

        Object value = p.getCodec().readValue(p, Object.class);
        if (value == null) {
            return null;
        }

        Class<?> fieldType = field.getType();

        Field valueField = null;
        IValueEnum.OthValueField othValueFieldName = field.getAnnotation(IValueEnum.OthValueField.class);
        if (othValueFieldName != null) {
            valueField = fieldType.getDeclaredField(othValueFieldName.value());
            valueField.setAccessible(true);
        }

        for (Object enumConst : fieldType.getEnumConstants()) {
            IValueEnum<?> valueEnum = (IValueEnum<?>) enumConst;
            Object enumValue = valueField == null
                    ? valueEnum.getValue() : valueField.get(valueEnum);
            if (value.equals(enumValue)) {
                return valueEnum;
            }
        }

        return null;
    }
}
