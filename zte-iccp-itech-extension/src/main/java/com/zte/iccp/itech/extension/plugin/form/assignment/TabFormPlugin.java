package com.zte.iccp.itech.extension.plugin.form.assignment;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.ManageTaskAbility;
import com.zte.iccp.itech.extension.ability.common.table.DataAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.common.table.DataKeyEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultProcessEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.*;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.*;
import com.zte.paas.lcap.core.event.SearchEvent;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.*;


@Slf4j
public class TabFormPlugin extends BaseFormPlugin {

    // =========== 表格列 DataKey ============
    // todo DataKey 当前低代码平台暂不支持修改，用常量标识

    /** 公共字段 - 任务状态 */
    private static final String COMMON_ASSIGNMENT_STATUS = "fastcodefield_cov2v4j6";

    /** 全部任务 - 当前进展 */
    public static final String WHOLE_CURRENT_PROCESS = "custom_qa8hc43u";

    /** 全部任务 - 当前处理人 */
    public static final String WHOLE_CURRENT_PROCESSOR = "custom_83h7ba15";

    /** 故障管理任务 - 代表处 */
    private static final String FAULT_REPRESENTATIVE_OFFICE = "custom_d51eh0zd";

    /** 故障管理任务 - PDM 产品 */
    private static final String FAULT_PDM_PRODUCT = "custom_88wbui99";

    /** 故障管理任务 - 区域 */
    private static final String FAULT_AREA = "custom_7q91tvy5";

    /** 故障管理任务 - 当前进展 */
    private static final String FAULT_CURRENT_PROGRESS = "custom_ai25pw20";

    /** 故障管理任务 - 当前处理人 */
    private static final String FAULT_CURRENT_PROCESSOR = "custom_aekvyzz5";

    /** 权限申请 - 产品 */
    private static final String APPLICATION_PRODUCT= "custom_9dlfxa9c";

    /** 权限申请 - 代表处 */
    private static final String APPLICATION_ORGANIZATION = "custom_ju979zy7";

    /** 权限申请 - 状态 */
    private static final String APPLICATION_STATUS = "fastcodefield_cov2v4j6";

    /** 权限申请 - 处理时间 */
    private static final String APPLICATION_DEAL_TIME = "custom_lcmf1wix";

    @Override
    public void afterSearch(SearchEvent searchEvent) {
        JSONArray assignmentArray = searchEvent.getOutput().getJSONArray(RECORDS);

        TablePc tableInfo = (TablePc) getView().getControl(TABLE_ASSIGNMENT_CID);
        String pkIdKey = tableInfo.getPkId();

        // 1.获取 dataKey
        String pageCid = getView().getPageId();
        Map<String, String> dataKeys = DataKeyEnum.getPageTableDataKey(pageCid);

        Map<String, Map<String, String>> supplementaryDataMap;
        switch (pageCid) {
            case PAGE_WEB_NETWORK_CHANGE_ASSIGNMENT:
            case PAGE_WEB_NETWORK_CHANGE_INITIATED_BY_ME:
            case PAGE_WEB_NETWORK_CHANGE_TO_BE_HANDLED_BY_ME:
            case PAGE_WEB_NETWORK_CHANGE_HANDLED_BY_ME:
            case PARTNER_CHANGE_ORDER_INITIATED_BY_ME:
            case PARTNER_CHANGE_ORDER_TO_BE_HANDLED:
            case PARTNER_CHANGE_ORDER_HANDLED_BY_ME:
                supplementaryDataMap
                        = getNetworkChangeTableSupplementaryData(assignmentArray, pkIdKey, pageCid, dataKeys);
                break;

            case PAGE_WEB_TECHNOLOGY_MANAGEMENT_ASSIGNMENT:
            case PAGE_WEB_TECHNOLOGY_INITIATED_BY_ME:
            case PAGE_WEB_TECHNOLOGY_TO_BE_HANDLED_BY_ME:
            case PAGE_WEB_TECHNOLOGY_HANDLED_BY_ME:
                supplementaryDataMap
                        = getTechnologySupplementaryData(pageCid, assignmentArray, pkIdKey, dataKeys);
                break;

            case PAGE_WEB_FAULT_MANAGEMENT_ASSIGNMENT:
                supplementaryDataMap = getFaultManageTableSupplementaryData(assignmentArray, pkIdKey);
                break;

            case PAGE_WEB_APPLICATION_INITIATED_BY_ME:
            case PAGE_WEB_APPLICATION_TO_BE_HANDLED_BY_ME:
            case PAGE_WEB_APPLICATION_HANDLED_BY_ME:
                supplementaryDataMap = getApplicationTableSupplementaryData(assignmentArray, pkIdKey, pageCid);
                break;

            case PAGE_WEB_ABNORMAL_REVIEW_INITIATED_BY_ME:
            case PAGE_WEB_ABNORMAL_REVIEW_TO_BE_HANDLED_BY_ME:
            case PAGE_WEB_ABNORMAL_REVIEW_HANDLED_BY_ME:
                supplementaryDataMap
                        = getClockReviewTableSupplementaryData(assignmentArray, pkIdKey, pageCid, dataKeys);
                break;

            case PAGE_WEB_INITIATED_BY_ME:
            case PAGE_WEB_TO_BE_HANDLED_BY_ME:
            case PAGE_WEB_HANDLED_BY_ME:
                supplementaryDataMap
                        = getBacklogTableSupplementaryData(assignmentArray, pkIdKey, pageCid, dataKeys);
                break;

            case PAGE_WEB_OPERATION_PLAN_MANAGEMENT:
            case PAGE_WEB_OPERATION_PLAN_TO_BE_HANDLED_BY_ME:
            case PAGE_WEB_OPERATION_PLAN_HANDLED_BY_ME:
            case PAGE_WEB_OPERATION_INITIATED_BY_ME:
                supplementaryDataMap = getOperationPlanTableSupplementaryData(assignmentArray, pkIdKey, dataKeys);
                break;

            default:
                supplementaryDataMap = getWholeAssignmentTableSupplementaryData(assignmentArray, pkIdKey);
                break;
        }

        // 2.包装补充数据指令
        for (int i = 0; i < assignmentArray.size(); i++) {
            JSONObject assignmentInfo = assignmentArray.getJSONObject(i);
            String id = assignmentInfo.getString(pkIdKey);

            Map<String, String> supplementaryData = supplementaryDataMap.getOrDefault(id, new HashMap<>());
            assignmentInfo.putAll(supplementaryData);
        }
    }

    /**
     * 获取网络变更任务页补充数据
     * @return Map<String, Map<String, String>>
     */
    public Map<String, Map<String, String>> getNetworkChangeTableSupplementaryData(
            JSONArray assignmentArray,
            String pkIdKey,
            String pageCid,
            Map<String, String> dataKeys) {

        if (assignmentArray.isEmpty()) {
            return new HashMap<>();
        }

        // 1.查询任务数据
        List<String> assignmentIds = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<NetworkChangeAssignment> assignments
                = AssignmentAbility.querySpecificTypeAssignment(assignmentIds, NetworkChangeAssignment.class);
        if (CollectionUtils.isEmpty(assignments)) {
            return new HashMap<>();
        }

        // 2.获取外部数据信息
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair
                = AssignmentAbility.getNetworkChangeClientCondition(assignments);
        AssignmentAbility.queryNetworkChangeClientData(clientDataPair.getLeft());

        // 3.获取内部数据信息
        InteriorData interiorData = AssignmentAbility.getNetworkChangeInteriorCondition(assignments);
        AssignmentAbility.queryNetworkChangeInteriorData(interiorData);
        interiorData.setDealingTime(
                AssignmentAbility.getUserDealingTime(assignmentIds, ContextHelper.getEmpNo()));

        // 4.包装补充数据信息
        return convertNetworkChangeData(assignments, clientDataPair, interiorData, pageCid, dataKeys);
    }

    /**
     * 获取技术管理任务页补充数据
     * @return Map<String, Map<String, String>>
     */
    private Map<String, Map<String, String>> getTechnologySupplementaryData(
            String pageCid,
            JSONArray assignmentArray,
            String pkIdKey,
            Map<String, String> dataKeys) {

        if (assignmentArray.isEmpty()) {
            return new HashMap<>();
        }

        // 1.查询任务数据
        List<String> assignmentIds = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<TechnologyManagementAssignment> assignments =
                AssignmentAbility.querySpecificTypeAssignment(assignmentIds, TechnologyManagementAssignment.class);
        if (CollectionUtils.isEmpty(assignments)) {
            return new HashMap<>();
        }

        // 2.获取外部数据信息
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair = getTechnologyClientCondition(assignments);
        queryTechnologyClientData(clientDataPair.getLeft());

        // 3.获取内部数据信息
        InteriorData interiorData = getTechnologyInteriorCondition(assignments);
        queryTechnologyInteriorData(interiorData);
        interiorData.setDealingTime(
                AssignmentAbility.getUserDealingTime(assignmentIds, ContextHelper.getEmpNo()));

        // 4.包装补充数据信息
        return convertTechnologyManageData(assignments, clientDataPair, interiorData, pageCid, dataKeys);
    }

    /**
     * 获取全部任务页补充数据
     * @return Map<String, Map<String, String>>
     */
    public Map<String, Map<String, String>> getWholeAssignmentTableSupplementaryData(JSONArray assignmentArray,
                                                                                     String pkIdKey) {
        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();

        if (assignmentArray.isEmpty()) {
            return supplementaryDataMap;
        }

        // 1.查询任务数据
        List<String> assignmentIdList = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<Assignment> assignmentList = AssignmentAbility.querySpecificTypeAssignment(assignmentIdList, Assignment.class);
        if (CollectionUtils.isEmpty(assignmentList)) {
            return supplementaryDataMap;
        }

        // 2. 技术管理任务审批节点数据补充
        processTechManageAssignment(assignmentList, supplementaryDataMap);
        // 3. 变更单、批次任务数据补充
        processChangeAssignment(assignmentList, supplementaryDataMap);
        // 4. 故障管理任务数据补充
        processFaultManageAssignment(assignmentList, supplementaryDataMap);
        return supplementaryDataMap;
    }


    /**
     * 故障管理任务数据处理
     *
     * @param assignmentList assignmentList
     * @param supplementaryDataMap supplementaryDataMap
     */
    private void processFaultManageAssignment(List<Assignment> assignmentList, Map<String, Map<String, String>> supplementaryDataMap) {
        List<FaultManagementAssignment> faultManagementAssignmentList = querySpecificTypeAssignment(
                assignmentList, Lists.newArrayList(AssignmentTypeEnum.FAULT_MANAGEMENT), FaultManagementAssignment.class);
        if (CollectionUtils.isEmpty(faultManagementAssignmentList)) {
            return;
        }
        // 2.包装外部信息查询基本条件
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair = getFaultClientCondition(faultManagementAssignmentList);

        // 3.包装补充数据信息
        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        // 2.数据包装
        for (FaultManagementAssignment assignment : faultManagementAssignmentList) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }
            // (1) 当前进展
            supplementaryData.put(WHOLE_CURRENT_PROCESS, FaultProcessEnum.getCurrentProcess(assignment.getCurrentProgress()));

            // (2) 当前处理人
            supplementaryData.put(WHOLE_CURRENT_PROCESSOR,
                    ConvertUtil.convertNameInfo(clientData.getUser(), formatFieldData.getCurrentProcessor(), COMMA, false));

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }
    }


    /**
     * 变更单、批次任务数据补充 - 全部任务
     *
     * @param assignmentList assignmentList
     * @param supplementaryDataMap supplementaryDataMap
     */
    private void processChangeAssignment(List<Assignment> assignmentList,
                                         Map<String, Map<String, String>> supplementaryDataMap) {
        List<AssignmentTypeEnum> assignmentTypeEnums = Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
                AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH);
        // 1.变更单、批次和合作方变更单、批次
        List<NetworkChangeAssignment> networkChangeAssignmentList = querySpecificTypeAssignment(
                assignmentList, assignmentTypeEnums, NetworkChangeAssignment.class);
        if (CollectionUtils.isEmpty(networkChangeAssignmentList)) {
            return;
        }

        // 2.获取外部数据信息
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair = AssignmentAbility.getNetworkChangeClientCondition(networkChangeAssignmentList);
        ClientData clientData = clientDataPair.getLeft();
        // 2.1 HR - 责任人
        Set<String> userId = clientData.getUserId();
        Map<String, String> userNameMap
                = HrClient.queryEmployeeNameInfo(CollectionUtils.isEmpty(userId)
                ? Lists.newArrayList() : Lists.newArrayList(userId));
        clientData.setUser(userNameMap);

        // 3.获取内部数据信息
        InteriorData interiorData = AssignmentAbility.getNetworkChangeInteriorCondition(networkChangeAssignmentList);
        AssignmentAbility.queryNetworkChangeInteriorData(interiorData);

        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();
        // 3.数据包装
        for (NetworkChangeAssignment assignment : networkChangeAssignmentList) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }

            // (1) 任务状态 - 批次任务 / 分包商批次任务
            AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
            if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)) {
                supplementaryData.put(COMMON_ASSIGNMENT_STATUS, interiorData.getBatchStatus().get(assignment.getApproveBatchTaskId()));
            }else if (AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.equals(assignmentType)) {
                supplementaryData.put(COMMON_ASSIGNMENT_STATUS, interiorData.getSubcontractBatchStatus().get(assignment.getApproveBatchTaskId()));
            }

            // (2) 当前处理人 - 全部任务
            supplementaryData.put(WHOLE_CURRENT_PROCESSOR,
                    ConvertUtil.convertNameInfo(clientData.getUser(), formatFieldData.getCurrentProcessor(), COMMA, false));

            // (3) 当前进展 - 全部任务
            supplementaryData.put(WHOLE_CURRENT_PROCESS,
                    AssignmentAbility.getNetWorkCurrentProcess(assignmentType, assignment.getCurrentProgress()));

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }
    }

    /**
     * 全部任务 - 技术管理任务数据加工（当前进展、当前处理人）
     *
     * @param assignmentList assignmentList
     * @param supplementaryDataMap supplementaryDataMap
     */
    private void processTechManageAssignment(List<Assignment> assignmentList, Map<String, Map<String, String>> supplementaryDataMap) {

        List<AssignmentTypeEnum> assignmentTypeEnums = Lists.newArrayList(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT,
                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB);
        List<TechnologyManagementAssignment> technologyManagementAssignments = querySpecificTypeAssignment(
                assignmentList, assignmentTypeEnums, TechnologyManagementAssignment.class);
        if (CollectionUtils.isEmpty(technologyManagementAssignments)) {
            return;
        }

        Map<AssignmentTypeEnum, List<TechnologyManagementAssignment>> groupedAssignments = technologyManagementAssignments.stream()
                .collect(Collectors.groupingBy(assignment ->
                        StringUtils.isEmpty(assignment.getApproveSubTaskId()) ? AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT
                                : AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB
                ));

        if (CollectionUtils.isEmpty(groupedAssignments)) {
            return;
        }

        // 分组后的主/子任务加工处理
        for (Map.Entry<AssignmentTypeEnum, List<TechnologyManagementAssignment>> entry : groupedAssignments.entrySet()) {
            if (entry.getKey() == AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB) {
                processAssignments(entry.getValue(), supplementaryDataMap, false);
            } else if (entry.getKey() == AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT) {
                processAssignments(entry.getValue(), supplementaryDataMap, true);
            }
        }
    }

    /**
     * 处理技术管理任务数据
     *
     * @param assignments assignments
     * @param supplementaryDataMap supplementaryDataMap
     * @param flag flag
     */
    private void processAssignments(List<TechnologyManagementAssignment> assignments,
                                    Map<String, Map<String, String>> supplementaryDataMap,
                                    boolean flag) {
        // 1.当前处理人获取
        // 1.1 审批中状态数据处理，获取验收人
        List<TechnologyManagementAssignment> approveStatusAssignmentList= assignments.stream()
                .filter(item -> AssignmentStatusEnum.APPROVE.getValue().equals(item.getAssignmentStatus()))
                .collect(Collectors.toList());
        Map<String, String> acceptorMap = getAcceptorMap(approveStatusAssignmentList);

        // 1.2.执行中状态数据处理，获取责任人
        List<String> userIdList= assignments.stream()
                .filter(item -> AssignmentStatusEnum.EXECUTE.getValue().equals(item.getAssignmentStatus()))
                .filter(item -> !CollectionUtils.isEmpty(item.getResponsibleEmployee()))
                .flatMap(assignment -> assignment.getResponsibleEmployee().stream())
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
        // 1.2.1 调用HR接口获取员工姓名和工号
        Map<String, String> userNameMap = HrClient.queryEmployeeNameInfo(userIdList);

        // 3.主/子任务当前进展获取
        Map<String, String> currentProgressMap = getCurrentProgressMap(assignments, flag);

        // 主/子任务当前进展、当前处理人数据处理（主任务状态 == 执行，当前处理人为责任人。 主任务状态 == 审核中，当前处理人为主任务验收人）
        for (TechnologyManagementAssignment assignment : assignments) {
            Map<String, String> processMap = MapUtils.newHashMap();
            if (AssignmentStatusEnum.APPROVE.getValue().equals(assignment.getAssignmentStatus())) {
                processMap.put(WHOLE_CURRENT_PROCESSOR, acceptorMap.get(assignment.getBillId()));
            } else if (AssignmentStatusEnum.EXECUTE.getValue().equals(assignment.getAssignmentStatus())) {
                processMap.put(WHOLE_CURRENT_PROCESSOR, Objects.requireNonNull(assignment.getResponsibleEmployee()).stream()
                        .map(item -> userNameMap.getOrDefault(item.getEmpUIID(), EMPTY_STRING))
                        .collect(Collectors.joining(COMMA)));
            }
            // 当前进展
            if (!CollectionUtils.isEmpty(currentProgressMap)) {
                processMap.put(WHOLE_CURRENT_PROCESS, currentProgressMap.get(flag ? assignment.getBillId() : assignment.getApproveSubTaskId()));
            }
            supplementaryDataMap.put(assignment.getId(), processMap);
        }
    }

    /**
     * 获取当前进展（技术管理主/子任务）
     *
     * @param assignments assignments
     * @param flag true：主，false：子
     * @return 当前进展map
     */
    private Map<String, String> getCurrentProgressMap(List<TechnologyManagementAssignment> assignments, boolean flag) {
        if (flag) {
            List<String> billIds = assignments.stream()
                    .map(TechnologyManagementAssignment::getBillId)
                    .collect(Collectors.toList());
            return ManageTaskAbility.queryMainCurrentProgress(billIds);

        } else {
            List<String> approveSubTaskIds = assignments.stream()
                    .map(TechnologyManagementAssignment::getApproveSubTaskId)
                    .collect(Collectors.toList());
            return ManageTaskAbility.querySubCurrentProgress(approveSubTaskIds);
        }
    }

    /**
     * 获取技术管理任务验收人map，id为key，value为验收人名字+工号，多个用逗号分开
     *
     * @param assignmentList assignmentList
     * @return Map<String, String>
     */
    private Map<String, String> getAcceptorMap(List<TechnologyManagementAssignment> assignmentList) {
        if (CollectionUtils.isEmpty(assignmentList)) {
            return Collections.emptyMap();
        }
        List<String> billIds = assignmentList.stream()
                .map(Assignment::getBillId)
                .distinct()
                .collect(Collectors.toList());
        List<TechnologyManagementOrder> techManageList = ManageTaskAbility.query(billIds);
        return techManageList.stream().collect(Collectors.toMap(BaseEntity::getId, item ->
                item.getAcceptorPerson().stream()
                        .map(employee -> (employee.getEmpName() + employee.getEmpUIID()))
                        .collect(Collectors.joining(COMMA))));
    }

    /**
     * 过滤任务类型，检索数据
     *
     * @param assignmentList assignmentList
     * @param assignmentTypeEnumList assignmentTypeEnumList
     * @param assignmentClass assignmentClass
     * @return <T extends Assignment> List<T>
     * @param <T> ? extends Assignment
     */
    private <T extends Assignment> List<T> querySpecificTypeAssignment(List<Assignment> assignmentList,
                                                                       List<AssignmentTypeEnum> assignmentTypeEnumList,
                                                                       Class<T> assignmentClass) {
        List<String> assignmentIds = assignmentList.stream()
                .filter(assignment -> assignmentTypeEnumList.contains(AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType())))
                .map(BaseEntity::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assignmentIds)) {
            return Collections.emptyList();
        }

        return AssignmentAbility.querySpecificTypeAssignment(assignmentIds, assignmentClass);
    }

    /**
     * 获取 故障管理任务 补充数据
     * @param assignmentArray
     * @param pkIdKey
     * @return Map<String, Map<String, String>>
     */
    private Map<String, Map<String, String>> getFaultManageTableSupplementaryData(JSONArray assignmentArray,
                                                                                  String pkIdKey) {
        if (assignmentArray.isEmpty()) {
            return new HashMap<>();
        }

        // 1.查询任务数据
        List<String> assignmentIdList = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<FaultManagementAssignment> assignmentList =
                AssignmentAbility.querySpecificTypeAssignment(assignmentIdList, FaultManagementAssignment.class);
        if (CollectionUtils.isEmpty(assignmentList)) {
            return new HashMap<>();
        }

        // 2.包装外部信息查询基本条件
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair = getFaultClientCondition(assignmentList);
        queryFaultClientData(clientDataPair.getLeft());

        // 3.包装补充数据信息
        return convertFaultManageData(assignmentList, clientDataPair);
    }

    /**
     * 获取 权限申请任务 补充数据
     * key - assignmentId   value - 待补充数据 Map(页面指令格式)
     * 补充数据 Map： key - 表格 dataKey  value - 展示值
     */
    private Map<String, Map<String, String>> getApplicationTableSupplementaryData(
            JSONArray assignmentArray,
            String pkIdKey,
            String pageCid) {

        if (assignmentArray.isEmpty()) {
            return new HashMap<>();
        }

        // 1.查询任务数据
        List<String> assignmentIds = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<PermissionApplicationAssignment> assignments =
                AssignmentAbility.querySpecificTypeAssignment(assignmentIds, PermissionApplicationAssignment.class);
        if (CollectionUtils.isEmpty(assignments)) {
            return new HashMap<>();
        }

        // 2.包装外部信息查询基本条件
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair
                = getApplicationClientCondition(assignments);
        queryApplicationClientData(clientDataPair.getLeft());

        // 3.我已处理 - 检索处理时间
        Map<String, String> handledTimeMap = new HashMap<>();
        if (PAGE_WEB_APPLICATION_HANDLED_BY_ME.equals(pageCid)) {
            List<PersonRelevance> relevance
                    = AssignmentAbility.getAssignmentHandledTime(assignmentIds, ContextHelper.getEmpNo());
            handledTimeMap = relevance.stream()
                    .collect(Collectors.toMap(
                            PersonRelevance::getAssignmentId,
                            item -> DateUtils.dateToString(item.getLastModifiedTime(), DATE_FORM)));
        }

        // 3.包装补充数据信息
        return convertApplicationData(assignments, clientDataPair, handledTimeMap);
    }

    /**
     * 获取 权限申请任务 补充数据
     * key - assignmentId   value - 待补充数据 Map(页面指令格式)
     * 补充数据 Map： key - 表格 dataKey  value - 展示值
     */
    private Map<String, Map<String, String>> getClockReviewTableSupplementaryData(
            JSONArray assignmentArray,
            String pkIdKey,
            String pageCid,
            Map<String, String> dataKeys) {

        if (assignmentArray.isEmpty()) {
            return new HashMap<>();
        }

        // 1.查询任务数据
        List<String> assignmentIds = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<Assignment> assignments =
                AssignmentAbility.querySpecificTypeAssignment(assignmentIds, Assignment.class);
        if (CollectionUtils.isEmpty(assignments)) {
            return new HashMap<>();
        }

        // 2.包装外部信息查询基本条件
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair
                = getClockReviewClientCondition(assignments);
        queryClockReviewClientData(clientDataPair.getLeft());

        // 3.我已处理 - 检索处理时间
        Map<String, String> dealingTime = new HashMap<>();
        AssignmentAbility.getUserDealingTaskInfo(
                assignmentIds, ContextHelper.getEmpNo(), new HashMap<>(), dealingTime);
        InteriorData interiorData = new InteriorData();
        interiorData.setDealingTime(dealingTime);

        // 3.包装补充数据信息
        return convertClockReviewData(assignments, clientDataPair, interiorData, pageCid, dataKeys);
    }

    /**
     * 获取 待办中心 补充数据
     * key - assignmentId   value - 待补充数据 Map(页面指令格式)
     * 补充数据 Map： key - 表格 dataKey  value - 展示值
     */
    private Map<String, Map<String, String>> getBacklogTableSupplementaryData(
            JSONArray assignmentArray,
            String pkIdKey,
            String pageCid,
            Map<String, String> dataKeys) {

        if (assignmentArray.isEmpty()) {
            return new HashMap<>();
        }

        // 1.查询任务数据
        List<String> assignmentIds = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<Assignment> assignments
                = AssignmentAbility.querySpecificTypeAssignment(assignmentIds, Assignment.class);
        if (CollectionUtils.isEmpty(assignments)) {
            return new HashMap<>();
        }

        // 2.包装外部信息查询基本条件
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair
                = getBacklogClientCondition(assignments);
        queryApplicationClientData(clientDataPair.getLeft());

        // 3.特殊处理 - 故障管理任务
        // 故障管理任务产品为 PDM 产品，为独立字段，需要单独处理
        replenishFaultPdmProductInfo(assignments, clientDataPair.getLeft(), clientDataPair.getRight());

        // 3.我已处理 - 检索处理时间
        Map<String, String> approvalTaskFlag = new HashMap<>();
        Map<String, String> dealingTime = new HashMap<>();
        AssignmentAbility.getUserDealingTaskInfo(
                assignmentIds, ContextHelper.getEmpNo(), approvalTaskFlag, dealingTime);
        InteriorData interiorData = new InteriorData();
        interiorData.setApprovalTaskFlag(approvalTaskFlag);
        interiorData.setDealingTime(dealingTime);

        // 3.包装补充数据信息
        return convertBacklogData(assignments, clientDataPair, interiorData, pageCid, dataKeys);
    }

    private void replenishFaultPdmProductInfo(
            List<Assignment> assignments,
            ClientData clientData,
            Map<String, FormatFieldData> formatFieldDataMap) {

        // 1.筛选故障管理任务
        List<String> faultAssignmentIds = assignments.stream()
                .filter(item ->
                        AssignmentTypeEnum.FAULT_MANAGEMENT.getValue().equals(item.getAssignmentType().get(0).getValue()))
                .map(Assignment::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(faultAssignmentIds)) {
            return;
        }

        // 2.检索 PDM 信息
        List<FaultManagementAssignment> faultAssignments
                = AssignmentAbility.querySpecificTypeAssignment(faultAssignmentIds, FaultManagementAssignment.class);

        // 3.汇总 PDM 编号 + 记录任务对应 PDM 编号
        Set<String> pdmProducts = new HashSet<>();
        faultAssignments.forEach(item -> {
            if (StringUtils.hasText(item.getPdmProduct())) {
                String[] productNoArray = item.getPdmProduct().split(FORWARD_SLASH);
                List<String> pdmProductNos = Lists.newArrayList(productNoArray[0]);
                pdmProducts.addAll(pdmProductNos);

                FormatFieldData formatFieldData = formatFieldDataMap.get(item.getId());
                formatFieldData.setProduct(pdmProductNos);
            }
        });

        // 4.检索 PDM
        // 3.PDM - 产品信息
        Map<String, String> productMap = PdmClient.getProductNameInfo(new ArrayList<>(pdmProducts));
        clientData.getProduct().putAll(productMap);
    }

    /**
     * 技术交付 - 汇总外部接口数据查询条件
     * left - 外部接口数据   right - 任务下数据ID信息
     * @param assignmentList
     * @return Pair<ClientData, Map<String, FormatFieldData>>
     */
    private Pair<ClientData, Map<String, FormatFieldData>> getTechnologyClientCondition(List<TechnologyManagementAssignment> assignmentList) {
        if (CollectionUtils.isEmpty(assignmentList)) {
            return Pair.of(new ClientData(), new HashMap<>());
        }

        Set<String> organizationIdSet = new HashSet<>();
        Set<String> productIdSet = new HashSet<>();
        Set<String> networkIdSet = new HashSet<>();

        // 1.汇总外部接口查询条件 + 任务下数据ID信息
        Map<String, FormatFieldData> formatFieldDataMap = new HashMap<>();
        for (TechnologyManagementAssignment assignment : assignmentList) {
            FormatFieldData formatFieldData = new FormatFieldData();

            // 1.组织ID - 营销ID + 代表处ID
            List<String> marketingIdList = TextValuePairHelper.getValueList(assignment.getMarketing());
            List<String> representativeOfficeIdList = TextValuePairHelper.getValueList(assignment.getRepresentativeOffice());
            organizationIdSet.addAll(marketingIdList);
            organizationIdSet.addAll(representativeOfficeIdList);
            formatFieldData.setMarketing(marketingIdList);
            formatFieldData.setRepresentativeOffice(representativeOfficeIdList);

            // 2.产品分类ID
            List<String> productClassificationIds = TextValuePairHelper.getValueList(assignment.getProductClassification());
            List<String> formatIds= productClassificationIds.stream()
                    .map(ProductUtils::trimEndSlash)
                    .collect(Collectors.toList());
            productIdSet.addAll(formatIds);
            formatFieldData.setProduct(formatIds);

            // 3.网络ID
            List<String> networkIdList = TextValuePairHelper.getValueList(assignment.getNetwork());
            networkIdSet.addAll(networkIdList);
            formatFieldData.setNetwork(networkIdList);

            formatFieldDataMap.put(assignment.getId(), formatFieldData);
        }

        ClientData clientData = new ClientData();
        clientData.setOrganizationId(organizationIdSet);
        clientData.setProductId(productIdSet);
        clientData.setNetworkId(networkIdSet);

        return Pair.of(clientData, formatFieldDataMap);
    }

    /**
     * 技术管理 - 查询外部接口数据
     * @param clientData
     */
    private void queryTechnologyClientData(ClientData clientData) {
        // 1.HR - 组织数据
        Map<String, String> organizationNameMap = HrClient.queryOrganizationNameInfo(new ArrayList<>(clientData.getOrganizationId()));
        clientData.setOrganization(organizationNameMap);

        // 2.NIS - 产品分类
        Map<String, String> productNameMap = NisClient.queryProductPathName(new ArrayList<>(clientData.getProductId()));
        clientData.setProduct(productNameMap);

        // 3.NIS - 网络
        Map<String, String> networkNameMap = NisClient.queryNetworkName(new ArrayList<>(clientData.getNetworkId()));
        clientData.setNetwork(networkNameMap);
    }

    /**
     * 故障管理 - 汇总外部接口数据查询条件
     * left - 外部接口数据   right - 任务下数据ID信息
     */
    private Pair<ClientData, Map<String, FormatFieldData>> getFaultClientCondition(
            List<FaultManagementAssignment> assignmentList) {

        if (CollectionUtils.isEmpty(assignmentList)) {
            return Pair.of(new ClientData(), new HashMap<>());
        }

        Set<String> areaCodeSet = new HashSet<>();
        Set<String> pdmProductSet = new HashSet<>();
        Set<String> organizationIdSet = new HashSet<>();

        // 汇总外部接口查询条件 + 任务下数据ID信息
        Map<String, FormatFieldData> formatFieldDataMap = new HashMap<>();
        for (FaultManagementAssignment assignment : assignmentList) {
            FormatFieldData formatFieldData = new FormatFieldData();

            // 区域
            List<String> areaCodeList = Lists.newArrayList(assignment.getRegion());
            areaCodeSet.addAll(areaCodeList);
            formatFieldData.setAreaCode(areaCodeList);

            // PDM 产品
            if (StringUtils.hasText(assignment.getPdmProduct())) {
                String[] productNoArray = assignment.getPdmProduct().split(FORWARD_SLASH);
                List<String> pdmProductNoList = Lists.newArrayList(productNoArray);
                pdmProductSet.addAll(pdmProductNoList);
                formatFieldData.setProduct(pdmProductNoList);
            }

            // 组织ID
            List<String> representativeOfficeIdList
                    = TextValuePairHelper.getValueList(assignment.getRepresentativeOffice());
            organizationIdSet.addAll(representativeOfficeIdList);
            formatFieldData.setRepresentativeOffice(representativeOfficeIdList);

            formatFieldDataMap.put(assignment.getId(), formatFieldData);
        }

        ClientData clientData = new ClientData();
        clientData.setAreaCode(areaCodeSet);
        clientData.setProductId(pdmProductSet);
        clientData.setOrganizationId(organizationIdSet);

        return Pair.of(clientData, formatFieldDataMap);
    }

    /**
     * 故障管理 - 查询外部接口数据
     */
    private void queryFaultClientData(ClientData clientData) {
        // 1.EMDM - 区域信息
        clientData.setArea(
                EmdmClient.queryAreaNameInfo(new ArrayList<>(clientData.getAreaCode())));

        // 2.PDM - 产品信息
        clientData.setProduct(
                PdmClient.getProductNameInfo(new ArrayList<>(clientData.getProductId())));

        // 3.HR - 组织信息
        clientData.setOrganization(
                HrClient.queryOrganizationNameInfo(new ArrayList<>(clientData.getOrganizationId())));
    }

    /**
     * 权限申请 - 汇总外部接口数据查询条件
     * left - 外部接口数据   right - 任务下数据ID信息
     */
    private Pair<ClientData, Map<String, FormatFieldData>> getApplicationClientCondition(
            List<PermissionApplicationAssignment> assignments) {

        Set<String> products = new HashSet<>();
        Set<String> organizations = new HashSet<>();

        // 1.汇总外部接口查询条件 + 任务下数据ID信息
        Map<String, FormatFieldData> formatFieldDataMap = new HashMap<>();
        for (Assignment assignment : assignments) {
            FormatFieldData formatFieldData = new FormatFieldData();

            // 2.产品分类ID
            List<String> productIds = TextValuePairHelper.getValueList(assignment.getProductClassification());
            List<String> formatProductIds = productIds.stream()
                    .map(ProductUtils::trimEndSlash)
                    .collect(Collectors.toList());
            products.addAll(formatProductIds);
            formatFieldData.setProduct(formatProductIds);

            // 4. 代表处ID
            List<String> organizationIds
                    = TextValuePairHelper.getValueList(assignment.getRepresentativeOffice());
            List<String> formatOrganizationIds = organizationIds.stream()
                    .map(ResponsibleUtils::minLevelId)
                    .collect(Collectors.toList());
            organizations.addAll(formatOrganizationIds);
            formatFieldData.setRepresentativeOffice(formatOrganizationIds);

            formatFieldDataMap.put(assignment.getId(), formatFieldData);
        }

        ClientData clientData = new ClientData();
        clientData.setProductId(products);
        clientData.setOrganizationId(organizations);

        return Pair.of(clientData, formatFieldDataMap);
    }

    /**
     * 待办中心 - 汇总外部接口数据查询条件
     * left - 外部接口数据   right - 任务下数据ID信息
     */
    private Pair<ClientData, Map<String, FormatFieldData>> getBacklogClientCondition(
            List<Assignment> assignments) {

        Set<String> products = new HashSet<>();
        Set<String> organizations = new HashSet<>();

        // 1.汇总外部接口查询条件 + 任务下数据ID信息
        Map<String, FormatFieldData> formatFieldDataMap = new HashMap<>();
        for (Assignment assignment : assignments) {
            FormatFieldData formatFieldData = new FormatFieldData();

            // 2.产品经营团队ID
            List<String> productClassificationIds
                    = TextValuePairHelper.getValueList(assignment.getProductClassification());
            Set<String> productLines = new HashSet<>();
            for (String productId : productClassificationIds) {
                String productLine = ProductUtils.getTeam(productId);
                productLines.add(ProductUtils.trimEndSlash(productLine));
            }
            products.addAll(productLines);
            formatFieldData.setProduct(new ArrayList<>(productLines));

            // 4. 代表处ID
            List<String> organizationIds
                    = TextValuePairHelper.getValueList(assignment.getRepresentativeOffice());
            List<String> formatOrganizationIds = organizationIds.stream()
                    .map(ResponsibleUtils::minLevelId)
                    .collect(Collectors.toList());
            organizations.addAll(formatOrganizationIds);
            formatFieldData.setRepresentativeOffice(formatOrganizationIds);

            formatFieldDataMap.put(assignment.getId(), formatFieldData);
        }

        ClientData clientData = new ClientData();
        clientData.setProductId(products);
        clientData.setOrganizationId(organizations);

        return Pair.of(clientData, formatFieldDataMap);
    }

    /**
     * 权限申请 - 查询外部接口数据
     */
    private void queryApplicationClientData(ClientData clientData) {
        // 1.NIS - 产品信息
        clientData.setProduct(
                NisClient.queryProductName(new ArrayList<>(clientData.getProductId())));

        // 4.HR - 组织信息
        clientData.setOrganization(
                HrClient.queryOrganizationNameInfo(new ArrayList<>(clientData.getOrganizationId())));
    }

    /**
     * 打卡复盘 - 汇总外部接口数据查询条件
     * left - 外部接口数据   right - 任务下数据ID信息
     */
    private Pair<ClientData, Map<String, FormatFieldData>> getClockReviewClientCondition(
            List<Assignment> assignments) {

        if (CollectionUtils.isEmpty(assignments)) {
            return Pair.of(new ClientData(), new HashMap<>());
        }

        Set<String> organizationIdSs= new HashSet<>();
        Set<String> productIds = new HashSet<>();
        Set<String> networkIds = new HashSet<>();

        // 1.汇总外部接口查询条件 + 任务下数据ID信息
        Map<String, FormatFieldData> formatFieldDataMap = new HashMap<>();
        for (Assignment assignment : assignments) {
            FormatFieldData formatFieldData = new FormatFieldData();

            // 1.组织ID - 营销ID + 代表处ID
            List<String> marketingIds
                    = TextValuePairHelper.getValueList(assignment.getMarketing());
            List<String> representativeOfficeIds
                    = TextValuePairHelper.getValueList(assignment.getRepresentativeOffice());
            organizationIdSs.addAll(marketingIds);
            organizationIdSs.addAll(representativeOfficeIds);
            formatFieldData.setMarketing(marketingIds);
            formatFieldData.setRepresentativeOffice(representativeOfficeIds);

            // 2.产品线
            List<String> productClassificationIds
                    = TextValuePairHelper.getValueList(assignment.getProductClassification());
            Set<String> productLines = new HashSet<>();
            for (String productId : productClassificationIds) {
                String productLine = ProductUtils.getLine(productId);
                productLines.add(ProductUtils.trimEndSlash(productLine));
            }
            productIds.addAll(productLines);
            formatFieldData.setProduct(new ArrayList<>(productLines));

            // 3.网络ID
            List<String> networkId = TextValuePairHelper.getValueList(assignment.getNetwork());
            networkIds.addAll(networkId);
            formatFieldData.setNetwork(networkId);

            formatFieldDataMap.put(assignment.getId(), formatFieldData);
        }

        ClientData clientData = new ClientData();
        clientData.setOrganizationId(organizationIdSs);
        clientData.setProductId(productIds);
        clientData.setNetworkId(networkIds);

        return Pair.of(clientData, formatFieldDataMap);
    }

    /**
     * 打卡复盘 - 查询外部接口数据
     * @param clientData
     */
    private void queryClockReviewClientData(ClientData clientData) {
        // 1.HR - 组织数据
        Map<String, String> organizationNames
                = HrClient.queryOrganizationNameInfo(new ArrayList<>(clientData.getOrganizationId()));
        clientData.setOrganization(organizationNames);

        // 2.NIS - 产品线
        Map<String, String> productNames
                = NisClient.queryProductName(new ArrayList<>(clientData.getProductId()));
        clientData.setProduct(productNames);

        // 3.NIS - 网络
        Map<String, String> networkNames
                = NisClient.queryNetworkName(new ArrayList<>(clientData.getNetworkId()));
        clientData.setNetwork(networkNames);
    }

    /**
     * 技术管理 - 汇总内部数据查询条件
     */
    private InteriorData getTechnologyInteriorCondition(List<TechnologyManagementAssignment> assignmentList) {
        List<String> mainIds = Lists.newArrayList();
        List<String> subIds = Lists.newArrayList();
        Set<String> approveIds = new HashSet<>();

        for (TechnologyManagementAssignment assignment : assignmentList) {
            // 1.当前进展 - 主任务 / 子任务单据ID
            AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
            if (AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT.equals(assignmentType)) {
                mainIds.add(assignment.getBillId());
            }

            if (AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB.equals(assignmentType)) {
                subIds.add(assignment.getApproveSubTaskId());
            }

            // 2.验收人 - 审批中任务ID
            if (AssignmentStatusEnum.APPROVE.getValue().equals(assignment.getAssignmentStatus())) {
                approveIds.add(assignment.getBillId());
            }
        }

        InteriorData interiorData = new InteriorData();
        interiorData.setMainId(mainIds);
        interiorData.setSubId(subIds);
        interiorData.setBatchApproveId(approveIds);

        return interiorData;
    }

    /**
     * 技术管理 - 查询内部数据
     * @param interiorData
     */
    private void queryTechnologyInteriorData(InteriorData interiorData) {
        // 1.主任务 - 当前进展
        Map<String, String> mainCurrentProgress = ManageTaskAbility.queryMainCurrentProgress(interiorData.getMainId());
        interiorData.setMainCurrentProgress(mainCurrentProgress);

        // 2.子任务 - 当前进展
        Map<String, String> subCurrentProgress = ManageTaskAbility.querySubCurrentProgress(interiorData.getSubId());
        interiorData.setSubCurrentProgress(subCurrentProgress);

        // 3.审批中任务 - 验收人
        List<TechnologyManagementOrder> orders
                = ManageTaskAbility.query(new ArrayList<>(interiorData.getBatchApproveId()));
        Map<String, String> acceptors = orders.stream()
                .collect(Collectors.toMap(
                        TechnologyManagementOrder::getId,
                        item -> EmployeeHelper.getEpmNameUIID(
                                JsonUtils.parseArray(item.getAcceptorPerson(), Employee.class)),
                        (v1, v2) -> v1));
        interiorData.setAcceptPerson(acceptors);
    }

    /**
     * 包装网络变更补充数据
     */
    private Map<String, Map<String, String>> convertNetworkChangeData(
            List<NetworkChangeAssignment> assignments,
            Pair<ClientData, Map<String, FormatFieldData>> clientDataPair,
            InteriorData interiorData,
            String pageCid,
            Map<String, String> dataKeys) {

        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();
        for (NetworkChangeAssignment assignment : assignments) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }

            // (1) 营销 + 代表处信息
            List<String> fullOrganizations = CollectionUtils.isEmpty(formatFieldData.getRepresentativeOffice())
                    ? Lists.newArrayList()
                    : clientData.getFullOrganization().getOrDefault(formatFieldData.getRepresentativeOffice().get(0), Lists.newArrayList());
            if (!CollectionUtils.isEmpty(fullOrganizations)) {
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_MARKETING), fullOrganizations.get(0));
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_REGION), fullOrganizations.get(1));
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_REPRESENTATIVE_OFFICE), fullOrganizations.get(2));
            }

            // (2) 产品分类
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_PRODUCT_CLASSIFICATION),
                    ConvertUtil.convertNameInfo(
                            clientData.getProduct(), formatFieldData.getProduct(), COMMA, false));

            // (3) 网络数据
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_NETWORK),
                    ConvertUtil.convertNameInfo(
                            clientData.getNetwork(), formatFieldData.getNetwork(), COMMA, false));

            // (4) 指定网络变更类型属性
            // 任务状态 / 当前进展 / 操作结果
            convertSpecificNetworkChangeTypeAssignment(
                    assignment, supplementaryData, dataKeys, interiorData);

            // (5) 特殊处理 - 处理时间
            // 仅 我已处理 展示
            if (PageConstants.PAGE_WEB_NETWORK_CHANGE_HANDLED_BY_ME.equals(pageCid)
                    || PageConstants.PARTNER_CHANGE_ORDER_HANDLED_BY_ME.equals(pageCid)) {
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_DEALING_TIME),
                        interiorData.getDealingTime().getOrDefault(assignment.getId(), EMPTY_STRING));
            }

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }

        return supplementaryDataMap;
    }

    /**
     * 包装特定网络变更类型任务相关属性
     */
    private void convertSpecificNetworkChangeTypeAssignment(
            NetworkChangeAssignment assignment,
            Map<String, String> supplementaryData,
            Map<String, String> dataKeys,
            InteriorData interiorData) {

        AssignmentTypeEnum assignmentType
                = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        if (Objects.isNull(assignmentType)) {
            return;
        }

        switch (assignmentType) {
            case NETWORK_CHANGE:
                // 网络变更任务: 当前进展 + 操作结果
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_CURRENT_PROGRESS),
                        interiorData.getMainCurrentProgress().get(assignment.getCurrentProgress()));
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_OPERATION_RESULT), CommonConstants.EMPTY_STRING);
                break;

            case NETWORK_CHANGE_BATCH:
                // 网络变更 - 批次任务: 任务状态 + 当前进展
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_ASSIGNMENT_STATUS),
                        interiorData.getBatchStatus().get(assignment.getEntityId()));
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_CURRENT_PROGRESS),
                        interiorData.getMainCurrentProgress().get(assignment.getCurrentProgress()));
                break;

            case SUBCONTRACTOR_NETWORK_CHANGE:
                // 合作方网络变更任务: 当前进展 + 操作结果
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_CURRENT_PROGRESS),
                        interiorData.getSubCurrentProgress().get(assignment.getCurrentProgress()));
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_OPERATION_RESULT), CommonConstants.EMPTY_STRING);
                break;

            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                // 合作方网络变更 - 批次任务: 任务状态 + 当前进展
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_ASSIGNMENT_STATUS),
                        interiorData.getSubcontractBatchStatus().get(assignment.getEntityId()));
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_CURRENT_PROGRESS),
                        interiorData.getSubCurrentProgress().get(assignment.getCurrentProgress()));
                break;

            default:
                break;
        }
    }

    /**
     * 包装补充数据 - 技术管理
     * key - 任务ID   value - 补充数据(key - dataKey   value - dataValue)
     */
    private Map<String, Map<String, String>> convertTechnologyManageData(
            List<TechnologyManagementAssignment> assignmentList,
            Pair<ClientData, Map<String, FormatFieldData>> clientDataPair,
            InteriorData interiorData,
            String pageCid,
            Map<String, String> dataKeys) {

        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();
        for (TechnologyManagementAssignment assignment : assignmentList) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }

            // (1) 营销 + 代表处信息
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_MARKETING),
                    ConvertUtil.convertNameInfo(
                            clientData.getOrganization(), formatFieldData.getMarketing(), COMMA, false));
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_REPRESENTATIVE_OFFICE),
                    ConvertUtil.convertNameInfo(
                            clientData.getOrganization(), formatFieldData.getRepresentativeOffice(), COMMA, false));

            // (2) 产品分类
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_PRODUCT_CLASSIFICATION),
                    ConvertUtil.convertNameInfo(
                            clientData.getProduct(), formatFieldData.getProduct(), COMMA, true));

            // (3) 网络 - 仅子任务展示，主任务不展示
            AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
            boolean subFlag = AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB.equals(assignmentType);
            if (subFlag) {
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_NETWORK),
                        ConvertUtil.convertNameInfo(clientData.getNetwork(), formatFieldData.getNetwork(), COMMA, false));
            }

            // (4) 查看进展
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_CURRENT_PROGRESS),
                    subFlag ? interiorData.getSubCurrentProgress().get(assignment.getApproveSubTaskId())
                            : interiorData.getMainCurrentProgress().get(assignment.getBillId()));

            // (5) 是否超期，判断预计完成日期是否大于当前日期，requiredCompletionTime为预计完成日期
            Pair<String, String> overduePair = calculateOverdue(assignment);
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_OVER_FLAG),
                    overduePair.getLeft());
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_OVERDUE_TIME),
                    overduePair.getRight());

            // (6) 特殊处理 - 处理时间
            // 仅 我已处理 展示
            if (PageConstants.PAGE_WEB_TECHNOLOGY_HANDLED_BY_ME.equals(pageCid)) {
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_DEALING_TIME),
                        interiorData.getDealingTime().getOrDefault(assignment.getId(), EMPTY_STRING));
            }

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }

        return supplementaryDataMap;
    }

    /**
     * 包装 故障管理 补充数据
     */
    private Map<String, Map<String, String>> convertFaultManageData(
            List<FaultManagementAssignment> assignmentList,
            Pair<ClientData, Map<String, FormatFieldData>> clientDataPair) {

        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();
        for (FaultManagementAssignment assignment : assignmentList) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }

            // 代表处 / 办事处
            supplementaryData.put(
                    FAULT_REPRESENTATIVE_OFFICE, ConvertUtil.convertNameInfo(
                            clientData.getOrganization(),
                            formatFieldData.getRepresentativeOffice(),
                            COMMA,
                            false));

            // PDM 产品
            supplementaryData.put(
                    FAULT_PDM_PRODUCT, ConvertUtil.convertNameInfo(
                            clientData.getProduct(),
                            formatFieldData.getProduct(),
                            FORWARD_SLASH,
                            false));

            // 区域
            supplementaryData.put(
                    FAULT_AREA, ConvertUtil.convertNameInfo(
                            clientData.getArea(),
                            formatFieldData.getAreaCode(),
                            COMMA,
                            false));

            // 当前进展
            supplementaryData.put(
                    FAULT_CURRENT_PROGRESS,
                    FaultProcessEnum.getCurrentProcess(assignment.getCurrentProgress()));

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }

        return supplementaryDataMap;
    }

    /**
     * 包装 权限申请 补充数据
     */
    private Map<String, Map<String, String>> convertApplicationData(
            List<PermissionApplicationAssignment> assignments,
            Pair<ClientData, Map<String, FormatFieldData>> clientDataPair,
            Map<String, String> handledTimeMap) {

        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();
        for (PermissionApplicationAssignment assignment : assignments) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }

            // 1.代表处 / 办事处
            supplementaryData.put(APPLICATION_ORGANIZATION, ConvertUtil.convertNameInfo(
                    clientData.getOrganization(),
                    formatFieldData.getRepresentativeOffice(),
                    CommonConstants.COMMA,
                    false));

            // 2.产品
            supplementaryData.put(APPLICATION_PRODUCT, ConvertUtil.convertNameInfo(
                    clientData.getProduct(),
                    formatFieldData.getProduct(),
                    CommonConstants.COMMA,
                    false));

            // 3.状态
            AssignmentStatusEnum assignmentStatus
                    = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
            if (AssignmentStatusEnum.VALID.equals(assignmentStatus)) {
                supplementaryData.put(APPLICATION_STATUS,
                        calculateApplicationPermissionStatus(assignment.getExpirationTime(), assignmentStatus));
            }

            // 4.处理时间
            supplementaryData.put(APPLICATION_DEAL_TIME, handledTimeMap.get(assignment.getId()));

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }

        return supplementaryDataMap;
    }

    /**
     * 包装补充数据 - 打卡复盘
     * key - 任务ID   value - 补充数据(key - dataKey   value - dataValue)
     */
    private Map<String, Map<String, String>> convertClockReviewData(
            List<Assignment> assignments,
            Pair<ClientData, Map<String, FormatFieldData>> clientDataPair,
            InteriorData interiorData,
            String pageCid,
            Map<String, String> dataKeys) {

        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();
        for (Assignment assignment : assignments) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }

            // (1) 营销 + 代表处信息
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_MARKETING),
                    ConvertUtil.convertNameInfo(
                            clientData.getOrganization(), formatFieldData.getMarketing(), COMMA, false));
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_REPRESENTATIVE_OFFICE),
                    ConvertUtil.convertNameInfo(
                            clientData.getOrganization(), formatFieldData.getRepresentativeOffice(), COMMA, false));

            // (2) 产品线
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_PRODUCT_CLASSIFICATION),
                    ConvertUtil.convertNameInfo(
                            clientData.getProduct(), formatFieldData.getProduct(), COMMA, true));

            // (4) 网络
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_NETWORK),
                    ConvertUtil.convertNameInfo(
                            clientData.getNetwork(), formatFieldData.getNetwork(), COMMA, false));

            // (5) 特殊处理 - 处理时间
            // 仅 我已处理 展示
            if (PageConstants.PAGE_WEB_ABNORMAL_REVIEW_HANDLED_BY_ME.equals(pageCid)) {
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_DEALING_TIME),
                        interiorData.getDealingTime().getOrDefault(assignment.getId(), EMPTY_STRING));
            }

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }

        return supplementaryDataMap;
    }

    /**
     * 包装 待办中心 补充数据
     */
    private Map<String, Map<String, String>> convertBacklogData(
            List<Assignment> assignments,
            Pair<ClientData, Map<String, FormatFieldData>> clientDataPair,
            InteriorData interiorData,
            String pageCid,
            Map<String, String> dataKeys) {

        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();
        for (Assignment assignment : assignments) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }

            // 1.代表处 / 办事处
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_REPRESENTATIVE_OFFICE),
                    ConvertUtil.convertNameInfo(
                            clientData.getOrganization(),
                            formatFieldData.getRepresentativeOffice(),
                            CommonConstants.COMMA,
                            false));

            // 2.产品
            boolean faultFlag = AssignmentTypeEnum.FAULT_MANAGEMENT.getValue()
                    .equals(TextValuePairHelper.getValue(assignment.getAssignmentType()));
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_PRODUCT_CLASSIFICATION),
                    ConvertUtil.convertNameInfo(
                            clientData.getProduct(),
                            formatFieldData.getProduct(),
                            faultFlag ? CommonConstants.FORWARD_SLASH : CommonConstants.COMMA,
                            false));

            // (7) 特殊处理 - 处理时间、是否审批任务
            // 仅 我已处理 展示
            if (PageConstants.PAGE_WEB_HANDLED_BY_ME.equals(pageCid)) {
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_DEALING_TIME),
                        interiorData.getDealingTime().getOrDefault(assignment.getId(), CommonConstants.EMPTY_STRING));

                String approvalTaskFlag = interiorData.getApprovalTaskFlag().get(assignment.getId());
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_APPROVAL_TASK_FLAG),
                        StringUtils.hasText(approvalTaskFlag)
                                ? BoolEnum.valueOf(approvalTaskFlag).getName(ContextHelper.getLangId())
                                : CommonConstants.EMPTY_STRING);
            }

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }

        return supplementaryDataMap;
    }

    /**
     * 技术管理任务 - 计算是否超期 / 超期时间
     * left - 是否超期   right - 超期时间
     * @param assignment
     * @return Pair<String, String>
     */
    private Pair<String, String> calculateOverdue(TechnologyManagementAssignment assignment) {
        String language = ContextHelper.getLangId();

        // 1.校验任务状态
        // 已废止 / 待启动 不展示是否超期
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (AssignmentStatusEnum.START.equals(assignmentStatus)
                || AssignmentStatusEnum.ABOLISH.equals(assignmentStatus)
                || AssignmentStatusEnum.APPROVE_START.equals(assignmentStatus)) {
            return Pair.of(EMPTY_STRING, EMPTY_STRING);
        }

        // 2.计算是否超期
        // 要求完成日期需包含当天
        Date requiredCompletionTime = DateUtils.addDay(assignment.getRequiredCompletionTime(), 1);
        if (Objects.isNull(requiredCompletionTime)) {
            return Pair.of(EMPTY_STRING, EMPTY_STRING);
        }

        Date deadlineTime = AssignmentStatusEnum.CLOSE.equals(assignmentStatus) ? assignment.getLastModifiedTime() : new Date();
        long overdueMillis = requiredCompletionTime.getTime() - deadlineTime.getTime();

        // (1) 未超期
        if (overdueMillis > 0) {
            return Pair.of(BoolEnum.N.getName(language), EMPTY_STRING);
        }

        // (2) 超期，计算超期时间
        long diffInDays = Math.abs(overdueMillis / (24 * 60 * 60 * 1000));
        long diffInHours = Math.abs(overdueMillis / (60 * 60 * 1000) % 24);
        long diffInMinutes = Math.abs(overdueMillis / (60 * 1000) % 60);
        return Pair.of(
                BoolEnum.Y.getName(language),
                String.format("%d %s %d %s %d %s",
                        diffInDays, MsgUtils.getLangMessage(language, MessageConsts.CommonKeyword.DAY),
                        diffInHours, MsgUtils.getLangMessage(language, MessageConsts.CommonKeyword.HOUR),
                        diffInMinutes, MsgUtils.getLangMessage(language, MessageConsts.CommonKeyword.MINUTE)));
    }

    /**
     * 权限申请任务 - 计算任务状态
     */
    private String calculateApplicationPermissionStatus(
            Date expirationTime,
            AssignmentStatusEnum assignmentStatus) {

        // 判断有效日期
        // 异常仍沿用之前的状态
        try {
            return DateUtils.compareDate(new Date(), expirationTime)
                    ? AssignmentStatusEnum.INVALID.getZhCn()
                    : assignmentStatus.getZhCn();
        } catch (Exception e) {
            return assignmentStatus.getZhCn();
        }
    }

    /**
     * 获取操作计划任务页补充数据
     *
     * @return Map<String, Map<String, String>>
     * key - 任务主键id
     * value - Map<String, String>  key - 低代码表格字段dataKey， value - 业务数据（营销 / 片区 / 代表处 / 产品分类 / 网络数据）
     */
    private Map<String, Map<String, String>> getOperationPlanTableSupplementaryData(
            JSONArray assignmentArray,
            String pkIdKey,
            Map<String, String> dataKeys) {

        if (assignmentArray.isEmpty()) {
            return new HashMap<>();
        }

        // 1.查询任务数据
        List<String> assignmentIds = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<PlanOperationAssignment> assignments
                = AssignmentAbility.querySpecificTypeAssignment(assignmentIds, PlanOperationAssignment.class);
        if (CollectionUtils.isEmpty(assignments)) {
            return new HashMap<>();
        }

        // 2.获取外部数据信息
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair = getPlanOperationClientCondition(assignments);
        AssignmentAbility.queryNetworkChangeClientData(clientDataPair.getLeft());

        // 4.包装补充数据信息
        return convertOperationPlanData(assignments, clientDataPair,  dataKeys);
    }

    /**
     * 操作计划 - 汇总外部接口数据查询条件
     *
     * @return Pair<ClientData, Map<String, FormatFieldData>>
     * left - ClientData 存储 外部数据id
     * right - Map<String, FormatFieldData>  key - 任务的主键  value - FormatFieldData 存储外部数据id
     */
    private static Pair<ClientData, Map<String, FormatFieldData>> getPlanOperationClientCondition(
            List<PlanOperationAssignment> assignmentList) {
        if (CollectionUtils.isEmpty(assignmentList)) {
            return Pair.of(new ClientData(), new HashMap<>());
        }

        Set<String> organizationIdSet = new HashSet<>();
        Set<String> productIdSet = new HashSet<>();
        Set<String> networkIdSet = new HashSet<>();

        // 汇总外部接口查询条件
        Map<String, FormatFieldData> formatFieldDataMap = new HashMap<>();
        for (PlanOperationAssignment assignment : assignmentList) {
            FormatFieldData formatFieldData = new FormatFieldData();

            // 1.组织ID - 代表处ID
            List<String> representativeOfficeIdList
                    = TextValuePairHelper.getValueList(assignment.getRepresentativeOffice());
            organizationIdSet.addAll(representativeOfficeIdList);
            formatFieldData.setRepresentativeOffice(representativeOfficeIdList);

            // 2.产品分类ID
            List<String> productClassificationIdList
                    = TextValuePairHelper.getValueList(assignment.getProductClassification());
            List<String> formatIdList = Lists.newArrayList();
            for (String productId : productClassificationIdList) {
                formatIdList.add(productId.substring(INTEGER_ZERO, productId.length() - INTEGER_ONE));
            }
            productIdSet.addAll(formatIdList);
            formatFieldData.setProduct(formatIdList);

            // 3.网络ID
            List<String> networkIdList = TextValuePairHelper.getValueList(assignment.getNetwork());
            networkIdSet.addAll(networkIdList);
            formatFieldData.setNetwork(networkIdList);

            formatFieldDataMap.put(assignment.getId(), formatFieldData);
        }

        ClientData clientData = new ClientData();
        clientData.setOrganizationId(organizationIdSet);
        clientData.setProductId(productIdSet);
        clientData.setNetworkId(networkIdSet);

        return Pair.of(clientData, formatFieldDataMap);
    }

    /**
     * 包装 操作计划 补充数据
     *
     * @return Map<String, Map<String, String>>
     * key - 任务主键id
     * value - Map<String, String>  key - 低代码表格字段dataKey， value - 业务数据（营销 / 片区 / 代表处 / 产品分类 / 网络数据）
     */
    private Map<String, Map<String, String>> convertOperationPlanData(
            List<PlanOperationAssignment> assignments,
            Pair<ClientData, Map<String, FormatFieldData>> clientDataPair,
            Map<String, String> dataKeys) {

        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();
        for (PlanOperationAssignment assignment : assignments) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }

            // 1.营销 + 片区 + 代表处信息
            List<String> fullOrganizations = CollectionUtils.isEmpty(formatFieldData.getRepresentativeOffice())
                    ? Lists.newArrayList()
                    : clientData.getFullOrganization().getOrDefault(formatFieldData.getRepresentativeOffice().get(0), Lists.newArrayList());
            if (!CollectionUtils.isEmpty(fullOrganizations)) {
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_MARKETING), fullOrganizations.get(0));
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_REGION), fullOrganizations.get(1));
                supplementaryData.put(
                        dataKeys.get(CommonConstants.KEY_REPRESENTATIVE_OFFICE), fullOrganizations.get(2));
            }

            // 2.产品分类
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_PRODUCT_CLASSIFICATION),
                    ConvertUtil.convertNameInfo(
                            clientData.getProduct(), formatFieldData.getProduct(), COMMA, false));

            // 3.网络数据
            supplementaryData.put(
                    dataKeys.get(CommonConstants.KEY_NETWORK),
                    ConvertUtil.convertNameInfo(
                            clientData.getNetwork(), formatFieldData.getNetwork(), COMMA, false));

            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }

        return supplementaryDataMap;
    }
}
