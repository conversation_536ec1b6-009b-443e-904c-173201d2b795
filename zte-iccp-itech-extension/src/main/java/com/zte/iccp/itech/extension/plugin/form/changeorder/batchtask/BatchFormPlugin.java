package com.zte.iccp.itech.extension.plugin.form.changeorder.batchtask;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ApprovalAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.MultiProdGuaranteeAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchApprNodeMappStatusEnum;
import com.zte.iccp.itech.extension.domain.model.ActiveApproverNode;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.helper.RejectionProps;
import com.zte.iccp.itech.extension.plugin.form.changeorder.batchtask.helper.CurrentReviewNodeHepler;
import com.zte.iccp.itech.extension.plugin.form.changeorder.helper.RejectionReSubmitHelper;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.paas.lcap.core.event.LoadDataEvent;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.SYSTEM_USER;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.CURRENT_REVIEWER_NODE;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.IS_NEED_AUTHORIZATION_FILE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.LABEL;

/**
 * 批次页面
 * 1.高级容器展示;
 * 2.高级容器下的iFrame组件赋地址值（详情或审批URL）
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/2/7
 */
public class BatchFormPlugin extends BaseFormPlugin {

    /**
     * 保障批次场景下提示
     */
    private static final String PROMPT_GUARANTEE_CID = "Prompt_guarantee_cid";

    /**
     * 需要展示下载授权文件按钮的的状态
     */
    private static final List<AssignmentStatusEnum> NEED_SHOW_DOWN_FILE_BUTTON_STATUS = Lists.newArrayList(
            AssignmentStatusEnum.OPERATION_EXECUTION, AssignmentStatusEnum.RESULT_TOBE_BACK, AssignmentStatusEnum.RESULT_UNDER_REVIEW);

    @Override
    public void afterLoadData(LoadDataEvent event) {
        boolean isGuarantee = false;
        Class<? extends BaseEntity> batchEntityClass =
                EntityHelper.getEntityId(BatchTask.class).equals(getModel().getMainEntityType().getKey())
                ? BatchTask.class
                : SubcontractorBatchTask.class;

        IBatchTask iBatchTask = (IBatchTask) QueryDataHelper.get(batchEntityClass, Lists.newArrayList(), getPkId());
        // （1）如为保障批次，这里的值为保障任务id（用于待操作执行状态下下载授权文件按钮判断使用）
        String changeOrderId = iBatchTask.getChangeOrderId();
        // （2） 如为保障批次，这里的状态为保障批次的状态（因为保障批次必须在保障批次发布通告之后才能下载授权文件）
        AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(iBatchTask.getCurrentStatus());
        // （3） 如为保障批次，当前进展、当前处理人、变更内容、高级容器等均展示主单据最近操作批次的信息
        if (StringUtils.isNotEmpty(iBatchTask.getSource())
                && DataSourceEnum.GUARANTEE.name().equals(iBatchTask.getSource())) {
            // （4） 如为保障批次，页面需获取主单据最近操作时间的批次信息
            iBatchTask = MultiProdGuaranteeAbility.getMainLatestProcessedBatchInfo(iBatchTask.getChangeOrderId());
            isGuarantee = true;
        }

        // 1.按钮初始化展示隐藏
        assert iBatchTask != null;

        List<ApproveRecord> approvedRecords = processNotifyUnderReviewRecord(
                FlowHelper.getAfterSubmitApprovedRecords(iBatchTask.getId()), batchEntityClass);

        List<String> normals = getShowButton(batchEntityClass, assignmentStatusEnum, isGuarantee, iBatchTask, approvedRecords);
        // 2.当前进展、当前处理人展示隐藏
        setRichTextCurrentNode(batchEntityClass, normals, iBatchTask.getId());
        // 3.变更内容展示隐藏
        setChangeLog(iBatchTask, normals);
        // 4.设置高级容器Iframe地址值
        setAdvContainerIframeUrlValue(batchEntityClass, iBatchTask.getId(), isGuarantee, approvedRecords);
        // 5.驳回信息展示
        setRejectionInfo(iBatchTask);
        // 6.拓展 保障批次提示
        if (isGuarantee) {
            normals.add(PROMPT_GUARANTEE_CID);
        }
        // 按钮展示
        BatchHiddenUtils.hiddenTool(getView(), BuilderViewEnum.NORMAL, normals);

    }

    /**
     * 设置驳回信息
     *
     * @param batchTask 批次信息
     */
    private void setRejectionInfo(IBatchTask batchTask) {
        if (batchTask.getResultRejection() == null
                || ApproveResultEnum.PASS == batchTask.getResultRejection()) {
            return;
        }
        RejectionReSubmitHelper.showRejectionPrompt(getView(),
                batchTask.getId(),
                new RejectionProps() {{
                    // 审核结果：驳回
                    setResultRejectionEnum(ApproveResultEnum.REJECT);
                    // 审核意见
                    setOpinionRejection(batchTask.getOpinionRejection());
                    // 审核人
                    setPersonRejection(batchTask.getPersonRejection());
                    // 自定义编码
                    setRejectionExtendCode(batchTask.getRejectNode());
                    // 附件
                    setAttachmentRejection(batchTask.getAttachmentRejection());
                }},
                COMPONENT_PROMPT_REJECTION_CID, ApproveNodeEnum::getApproveNodeName);
    }

    /**
     * 设置高级容器Iframe地址值
     *
     * @param batchEntityClass batchEntityClass
     * @param batchId 批次id
     * @param isGuarantee 是否保障批次
     */
    private void setAdvContainerIframeUrlValue(Class<? extends BaseEntity> batchEntityClass, String batchId,
                                               boolean isGuarantee, List<ApproveRecord> approvedRecords) {
        String userId = ContextHelper.getEmpNo();

        for (ApproveRecord approveRecord : approvedRecords) {
            BatchApprNodeMappStatusEnum batchApprNodeMappStatusEnum = BatchApprNodeMappStatusEnum.fromValue(
                    ApprovalAbility.getApproveNodeName(batchEntityClass, approveRecord.getExtendedCode()));
            if (batchApprNodeMappStatusEnum == null) {
                continue;
            }
            // 1. 高级容器展示
            getView().getClientViewProxy().setControlState(batchApprNodeMappStatusEnum.getAdvContainerCid(),
                    new PageStatusAttributeBuilder().normal().build());
            // 2. 详情或审核Iframe组件赋地址值
            String hrefUrl = null;
            if (ACTIVE_TASK_STATUS.equals(approveRecord.getTaskStatus())) {
                List<FlowHandler> flowHandlers = FlowHelper.getFlowHandlerByFlowEntityIds(Lists.newArrayList(batchId));
                FlowHandler flowHandler = flowHandlers.get(0);
                Map<String, ApproveTask> filteredMap = flowHandler.getApproveTaskList().stream()
                        .filter(item -> item.getApprover().contains(userId))
                        .collect(Collectors.toMap(ApproveTask::getApprover, item -> item, (o1, o2) -> o1));
                // 2.1 当前用户为当前活跃节点处理人，则拼接审核URL赋值给低码Iframe组件，否则拼接详情URL
                if (!CollectionUtils.isEmpty(filteredMap)) {
                    ApproveTask approveTask = filteredMap.get(userId);
                    // 后续批次只有一个页面
                    hrefUrl = String.format(ConfigHelper.get(APPROVE_HREF_URL), flowHandler.getFlowInstanceId(),
                            approveTask.getTaskId(), ContextHelper.getAppId(), batchApprNodeMappStatusEnum.getPageId());

                    // 未审批的节点，高级容器设置为展开
                    getView().getClientViewProxy().setExpand(batchApprNodeMappStatusEnum.getAdvContainerCid(), true);
                }
            } else {
                // 已完成节点标题重写（高级容器原标题+审批时间+审批人）
                setBatchNodeTitle(batchEntityClass, approveRecord, batchApprNodeMappStatusEnum, batchId);

            }
            // 2.2 当前用户不为活跃节点处理人或为完成状态，均给详情URL
            if (StringUtils.isEmpty(hrefUrl) || isGuarantee) {
                hrefUrl = String.format(ConfigHelper.get(HREF_IFRAME_URL),
                        ZH_CN.equals(RequestHeaderUtils.getLangId()) ? ZH : EN, batchId,
                        PageStatusEnum.VIEW.name(), ContextHelper.getAppId(), batchApprNodeMappStatusEnum.getPageId());
            }

            // 2.3 低代码Iframe组件赋地址值
            getView().getClientViewProxy().setControlState(batchApprNodeMappStatusEnum.getIframeCid()
                    , MapUtils.newHashMap(URL, hrefUrl));
        }
    }

    /**
     * 通告审核存在多个节点，对应一个容器CID
     * （1）进行过滤 如果数据中存在通告审核的节点，过滤出活跃节点（优先级1为当前用户能命中的）
     * （2）否则取首条数据
     *
     * @param approvedRecords approvedRecords
     * @param batchEntityClass batchEntityClass
     * @return List<ApproveRecord>
     */
    private List<ApproveRecord> processNotifyUnderReviewRecord(List<ApproveRecord> approvedRecords,
                                                               Class<? extends BaseEntity> batchEntityClass) {
        String currentUserId = ContextHelper.getEmpNo();
        List<SingletonTextValuePairsProvider> notifyUnderReviews = Lists.newArrayList(
                BatchApprNodeMappStatusEnum.NOTIFICATION_UNDER_REVIEW.getPairsProviders());
        notifyUnderReviews.addAll(BatchApprNodeMappStatusEnum.PARTNER_NOTIFICATION_UNDER_REVIEW.getPairsProviders());

        Map<Boolean, List<ApproveRecord>> partitionedRecords = approvedRecords.stream()
                .collect(Collectors.partitioningBy(approvedRecord -> {
                    SingletonTextValuePairsProvider pairsProvider = ApprovalAbility.getApproveNodeName(
                            batchEntityClass, approvedRecord.getExtendedCode());
                    return notifyUnderReviews.contains(pairsProvider);
                }));

        // 其他审批记录
        List<ApproveRecord> otherApproveRecords = partitionedRecords.get(false);
        // 通过审核记录
        List<ApproveRecord> notifyUnderReviewRecords = partitionedRecords.get(true);

        if (CollectionUtils.isEmpty(notifyUnderReviewRecords)) {
            return approvedRecords;
        }

        otherApproveRecords.add(notifyUnderReviewRecords.stream()
                // 使用流式 API 查找优先级最高的记录
                .filter(item -> item.getApprover().equals(currentUserId)
                        && ACTIVE_TASK_STATUS.equals(item.getTaskStatus()))
                .findFirst()
                // 未找到则取第一条数据
                .orElseGet(() -> notifyUnderReviewRecords.get(0)));

        return otherApproveRecords;
    }

    /**
     * 设置批次节点标题
     *
     * @param approveRecord               审批记录
     * @param batchApprNodeMappStatusEnum batchApprNodeMappStatusEnum
     */
    private void setBatchNodeTitle(Class<? extends BaseEntity> batchEntityClass,
                                   ApproveRecord approveRecord,
                                   BatchApprNodeMappStatusEnum batchApprNodeMappStatusEnum,
                                   String batchId) {
        if (approveRecord.getApprovalDate() == null
                || StringUtils.isEmpty(approveRecord.getApprover())) {
            return;
        }

        String advancedContainerTitle;
        // 特殊场景1：待发通告走了操作取消审核节点，展示【操作取消申请】
        if (BatchApprNodeMappStatusEnum.PENDING_NOTIFICATION == batchApprNodeMappStatusEnum
                || BatchApprNodeMappStatusEnum.PARTNER_PENDING_NOTIFICATION == batchApprNodeMappStatusEnum) {
            advancedContainerTitle = BatchTaskAbility.getPendingNotifyTitle(
                    batchEntityClass, approveRecord, batchApprNodeMappStatusEnum, batchId);

            // 特殊场景2：操作取消审核需展示审核结果
        } else if (BatchApprNodeMappStatusEnum.OPERATION_CANCEL_REVIEW == batchApprNodeMappStatusEnum
                || BatchApprNodeMappStatusEnum.PARTNER_OPERATION_CANCEL_REVIEW == batchApprNodeMappStatusEnum) {
            advancedContainerTitle = BatchTaskAbility.getOperationCancelTitle(
                    batchEntityClass, approveRecord, batchApprNodeMappStatusEnum, batchId);

        } else {
            advancedContainerTitle = BatchTaskAbility.getNormalAdvancedContainerTitle(approveRecord, batchApprNodeMappStatusEnum);
        }

        getView().getClientViewProxy().setProps(
                MapUtils.newHashMap(batchApprNodeMappStatusEnum.getAdvContainerCid(), MapUtils.newHashMap(LABEL, advancedContainerTitle)));
    }

    /**
     * 批次各状态下按钮的展示和隐藏
     * 发布变更通告、下载授权文件、挂起、操作取消、超级授权文件（优化需求没这个按钮）
     *
     * @param batchEntityClass batchEntityClass
     * @param batchTask batchTask.getChangeOrderId() 变更单id（如为保障批次，则当前为保障单据id）
     * @param assignmentStatusEnum 批次状态
     * @param isGuarantee 是否保障批次
     */
    private List<String> getShowButton(Class<? extends BaseEntity> batchEntityClass,
                                       AssignmentStatusEnum assignmentStatusEnum,
                                       boolean isGuarantee,
                                       IBatchTask batchTask,
                                       List<ApproveRecord> approvedRecords) {
        List<String> normals = new ArrayList<>();
        // 1.保障单批次逻辑：仅当保障单批次状态为待操作执行，并且对应的保障单是否授权文件为是时展示下载授权文件按钮（任何人都可以下载）
        if (NEED_SHOW_DOWN_FILE_BUTTON_STATUS.contains(assignmentStatusEnum)) {
            addAuthorizationFileButtonIfNeeded(normals, batchEntityClass, batchTask.getChangeOrderId());
        }

        if (isGuarantee) {
            return normals;
        }

        ApproveRecord resultReviewRecord = approvedRecords.stream().filter(item -> BatchApprNodeMappStatusEnum.RESULT_UNDER_REVIEW
                        == BatchApprNodeMappStatusEnum.fromValue(ApprovalAbility.getApproveNodeName(batchEntityClass, item.getExtendedCode())))
                .findFirst().orElse(null);

        //满足条件展示撤销按钮条件
        if (BatchTaskAbility.isRevokeAccessible(resultReviewRecord, assignmentStatusEnum, batchTask.getResultReviewFlag())) {
            normals.add(BUTTON_BATCH_REVOKE_CID);
        }


        // 2.其他正常批次按钮逻辑
        // 当前用户非责任人，不展示按钮
        NetworkChangeAssignment assignment = AssignmentAbility.queryBatchAssignment(getPkId());
        if (Objects.isNull(assignment)
                || !StringUtils.equals(
                        ContextHelper.getEmpNo(),
                        EmployeeHelper.getFirstEmpUIID(assignment.getResponsibleEmployee()))) {
            return normals;
        }

        switch (assignmentStatusEnum) {
            case PENDING_NOTIFICATION:
                normals.addAll(Lists.newArrayList(BUTTON_SUSPENDED_CID, BUTTON_CANCEL_CID));
                break;
            case SUSPENDED:
                normals.add(BUTTON_CANCEL_CID);
                break;
            case OPERATION_EXECUTION:
                normals.addAll(Lists.newArrayList(BUTTON_SUSPENDED_CID, BUTTON_CANCEL_CID, BUTTON_CHANGE_CID));
                break;
            case RESULT_TOBE_BACK:
                // 历史版本没有 反馈操作结果 变更按钮, 撤回的不能变更通告
                if (FlowHelper.getCustomizeFlowVersion(getPkId()) > 0
                        && !ResultReviewFlagEnum.RESULT_REVIEW_PASSED.name().equals(batchTask.getResultReviewFlag())) {
                    normals.addAll(Lists.newArrayList(BUTTON_RESULT_CHANGE_CID));
                }
                break;
            default:
                // The button is hidden by default and does not require any action.
                break;
        }

        return normals;
    }


    /**
     * 下载授权文件按钮
     *
     * @param normals 显示按钮集
     * @param batchEntityClass 批次类型
     * @param changeOrderId 变更单id
     */
    private void addAuthorizationFileButtonIfNeeded(List<String> normals,
                                                    Class<? extends BaseEntity> batchEntityClass,
                                                    String changeOrderId) {
        Class<? extends BaseEntity> changeOrderEntityClass = ChangeOrder.class;
        List<String> changeOrderFields = Lists.newArrayList(ID, IS_NEED_AUTHORIZATION_FILE);
        if (batchEntityClass == SubcontractorBatchTask.class) {
            changeOrderEntityClass = SubcontractorChangeOrder.class;
            changeOrderFields = Lists.newArrayList(ID, SubcontractorChangeOrderFieldConsts.IS_AUTHORIZATION_FILE);
        }
        // 生成授权文件才可以展示 否则不展示
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(batchEntityClass, Lists.newArrayList(ID, GRANT_FILE), getPkId());
        if (batchTask == null || StringUtils.isEmpty(batchTask.getGrantFile())) {
            return;
        }

        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(changeOrderEntityClass, changeOrderFields, changeOrderId);
        if (changeOrder != null
                && BoolEnum.Y == changeOrder.getIsNeedAuthorizationFile()) {
            normals.add(BUTTON_DOWNLOAD_GRANT_FILE_CID);
        }
    }

    /**
     * 初始化当前进展和当前处理人
     * 
     * @param batchEntityClass 批次类型
     * @param normals 显示图文集
     * @param batchId 展示按钮集
     */
    private void setRichTextCurrentNode(Class<? extends BaseEntity> batchEntityClass, List<String> normals, String batchId) {
        ActiveApproverNode approvalNode = FlowHelper.getApprovalNodeCurrentHandlers(batchId);
        if (CollectionUtils.isEmpty(approvalNode.getApprover())) {
            return;
        }

        // 批次当前进展图文
        normals.add(RICHTEXT_CURRENT_NODE_CID);
        // 批次、合作方批次
        SingletonTextValuePairsProvider pairsProvider = ApprovalAbility.getApproveNodeName(
                batchEntityClass, approvalNode.getExtendedCode());
        // 批次特殊处理（兼容历史流程逻辑）
        if ( pairsProvider  == null) {
            pairsProvider = ApproveNodeEnum.OPERATION_EXECUTION;
        }

        getView().getClientViewProxy().setControlState(RICHTEXT_CURRENT_NODE_CID,
                VALUE, MsgUtils.getMessage(CURRENT_REVIEWER_NODE) + COLON + MsgUtils.getMessage(pairsProvider.getMsgKey()));
        // 非系统节点：展示当前处理人
        if (!approvalNode.getApprover().contains(SYSTEM_USER)) {
            // 批次当前处理人图文
            normals.add(RICHTEXT_CURRENT_REVIEWER_CID);
            CurrentReviewNodeHepler.setRichTextCurrentReviewer(getView(),
                    HrClient.queryEmployeeNameInfo(approvalNode.getApprover()), RICHTEXT_CURRENT_REVIEWER_CID);
        }
    }

    /**
     * 展示变更内容
     *
     * @param batchTask 批次任务
     * @param normals normals
     */
    private void setChangeLog(IBatchTask batchTask, List<String> normals) {
        String langId = ContextHelper.getLangId();
        String message =null;
        if (ZH_CN.equals(langId) && StringUtils.isNotEmpty(batchTask.getChangePlanDescZh())) {
            message = batchTask.getChangePlanDescZh();
        } else if (EN_US.equals(langId) && StringUtils.isNotEmpty(batchTask.getChangePlanDescUs())) {
            message = batchTask.getChangePlanDescUs();
        }

        if (StringUtils.isEmpty(message)) {
            return;
        }
        normals.add(CHANGE_PLAN_DESC);
        String result = new StringBuilder().append(DIV)
                .append(SPAN)
                .append(message)
                .append(R_SPAN)
                .append(R_DIV).toString();
        getView().getClientViewProxy().setProps(MapUtils.newHashMap(CHANGE_PLAN_DESC, MapUtils.newHashMap(CONTENT, result)));
    }
}
