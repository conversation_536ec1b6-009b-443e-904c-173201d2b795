package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.PlanOperationAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.permissionapplication.PermissionApplicationStatusEnum;
import com.zte.iccp.itech.extension.domain.model.PermissionApplication;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteTransactionalEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;

import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.ASSIGNMENT_OPERATION_REVOKE_SUCCESS;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.ASSIGNMENT_OPERATION_WARNING_HANDLER_TYPE_ERROR;

public class RevokeAssignmentPlugin extends BaseOperationPlugin {

    /**
     * 任务主键ID - dataKey
     */
    private static final String ASSIGNMENT_ID = "pk_25049603";

    /**
     * 撤销任务
     */
    @Override
    public void afterExecuteTransactional(ExecuteTransactionalEvent executeEvent) {
        IFormView formView = getView();
        String langId = ContextHelper.getLangId();

        // 1.获取任务主键
        JSONObject assignmentInfo = (JSONObject) executeEvent.getArgs().get(CommonConstants.VALUE);
        String assignmentId = assignmentInfo.getString(ASSIGNMENT_ID);

        // 2.检索任务信息
        Assignment assignment
                = AssignmentAbility.querySpecificTypeAssignment(assignmentId, Assignment.class);
        // 撤回前置校验
        if (!revokeBeforeCheck(assignment)) {
            return;
        }

        assert assignment != null;
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        switch (Objects.requireNonNull(assignmentTypeEnum)) {
            case PERMISSION_APPLICATION:
                // 3.撤销 权限申请 任务
                revokePermissionApplicationAssignment(assignment);
                break;
            case NETWORK_CHANGE:
            case SUBCONTRACTOR_NETWORK_CHANGE:
                ChangeOrderAbility.revoke(assignment);
                break;

            case OPERATION_PLAN_TASK:
                PlanOperationAbility.revoke(assignment);
                break;

            default:
                break;
        }

        formView.showMessage(MsgUtils.getLangMessage(langId, ASSIGNMENT_OPERATION_REVOKE_SUCCESS), MsgType.SUCCESS);
        // 4.刷新页面
        formView.getClientViewProxy().refreshData(
                executeEvent.getPageSessionId(), CidConstants.TABLE_ASSIGNMENT_CID, executeEvent.getPageId());

    }

    /**
     * 撤回前置校验
     *
     * @param assignment 任务信息
     * @return true：通过，false：不通过
     */
    private boolean revokeBeforeCheck(Assignment assignment) {
        if (assignment == null) {
            getView().showMessage(MessageConsts.ASSIGNMENT_MANAGE_NON_EXISTENT_WARNING, MsgType.WARNING);
            return false;
        }

        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        switch (Objects.requireNonNull(assignmentTypeEnum)) {
            case PERMISSION_APPLICATION:
                AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
                return handlePermissionApplication(assignmentStatus);

            case NETWORK_CHANGE:
            case SUBCONTRACTOR_NETWORK_CHANGE:
                return handleNetworkChange(assignment);

            case OPERATION_PLAN_TASK:
                return handleOperationPlan(assignment);

            default:
                // 不支持的任务类型
                getView().showMessage(ASSIGNMENT_OPERATION_WARNING_HANDLER_TYPE_ERROR, MsgType.WARNING);
                return false;
        }
    }

    /**
     * 权限申请前缀校验
     *
     * @param assignmentStatusEnum 任务状态枚举
     * @return true：通过  false:不通过
     */
    private boolean handlePermissionApplication(AssignmentStatusEnum assignmentStatusEnum) {
        if (!AssignmentStatusEnum.APPROVE.equals(assignmentStatusEnum)) {
            getView().showMessage(MessageConsts.PERMISSION_APPLICATION_OPERATION_STATUS_ERROR, MsgType.WARNING);
            return false;
        }
        return true;
    }


    /**
     * 网络变更单前置校验
     *
     * @param assignment 任务信息
     * @return true：通过  false:不通过
     */
    private boolean handleNetworkChange(Assignment assignment) {
        if (!ChangeOrderAbility.revokeBeforeCheck(assignment)) {
            getView().showMessage(MessageConsts.ASSIGNMENT_OPERATION_REVOKE_ERROR, MsgType.WARNING);
            return false;
        }
        return true;
    }

    /**
     * 撤销任务 - 权限申请
     */
    private void revokePermissionApplicationAssignment(Assignment assignment) {
        // 2.撤销审批流程
        FlowHelper.rollbackToStarter(
                assignment.getEntityId(),
                ApprovalConstants.PERMISSION_APPLICATION,
                MsgUtils.getMessage(MessageConsts.APPLICANT_RETURN_APPROVAL));

        // 3.更新任务信息
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setAssignmentStatus(AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.getValue());
        updateAssignment.setCurrentProcessorEmployee(
                HrClient.queryEmployeeInfo(Lists.newArrayList(ContextHelper.getEmpNo())));
        AssignmentAbility.update(updateAssignment);

        // 4.更新实体状态
        PermissionApplication updateApplication = new PermissionApplication();
        updateApplication.setId(assignment.getEntityId());
        updateApplication.setStatus(PermissionApplicationStatusEnum.APPROVAL_RETURN_DRAFT.getStatus());
        PermissionApplicationAbility.update(updateApplication);
    }

    /**
     * 操作计划单 撤销前置校验
     *
     * @param assignment 任务信息
     * @return true：通过  false:不通过
     */
    private boolean handleOperationPlan(Assignment assignment) {
        if (!PlanOperationAbility.revokeBeforeCheck(assignment)) {
            getView().showMessage(MessageConsts.OPERATION_PLAN_REVOKE_ERROR, MsgType.WARNING);
            return false;
        }
        return true;
    }
}
