package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.*;

/**
 * 包商网络变更操作单 用于保存更新类
 * <AUTHOR>
 * @create 2025/2/8 下午2:22
 */
@ApiModel("分包商网络变更操作单")
@Setter
@Getter
@BaseEntity.Info("subcontractor_oc")
public class SubcontractorChangeOrderAll extends BaseEntity {
    @JsonProperty(value = ORDER_NO)
    @ApiModelProperty("单据编号")
    private String orderNo;

    @JsonProperty(value = OPERATION_TYPE)
    @ApiModelProperty("操作类型")
    private List<TextValuePair> operationType;

    @JsonProperty(value = OPERATION_REASON)
    @ApiModelProperty("操作原因")
    private List<TextValuePair> operationReason;

    @JsonProperty(value = ORGANIZATION_ID)
    @ApiModelProperty("代表处")
    private  List<TextValuePair> responsibleDept;
}
