package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.VARIABLE_NET_SERVICE_APPROVAL;

/**
 * 内部批次驳回后置流程插件
 *
 * 1.批次【操作结果审核】节点存在两个节点：技术交付部/网络处审核、网服部审核人
 * 2.在实体业务规则进行审核结果驳回时不能确定当前节点的自定义编码（需要在手动更新reject_node的值）
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/2/25
 */
public class BatchResUndReviewRejectAfterFlowPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        JSONObject extra = body.getVariables();
        String netServiceApproval = (String) extra.get(VARIABLE_NET_SERVICE_APPROVAL);
        ApproveNodeEnum approveNodeEnum = ApproveNodeEnum.TD_NET_DEPT_APPROVE;
        // 是否网服部审核为Y：走技术交付部/网络处审核，为N走网服部审核人
        if (BoolEnum.N == BoolEnum.convertStringToBoolean(netServiceApproval)) {
            approveNodeEnum = ApproveNodeEnum.NET_SERVICE_DEPT_APP;
        }
        BatchTask batchTask = new BatchTask();
        batchTask.setId(body.getBusinessId());
        batchTask.setRejectNode(approveNodeEnum.name());
        SaveDataHelper.update(batchTask);
        return false;
    }
}
