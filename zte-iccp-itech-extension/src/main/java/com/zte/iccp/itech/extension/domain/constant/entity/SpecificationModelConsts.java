package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 基础配置 - 规则型号配置常量
 *
 * <AUTHOR> jiang<PERSON><PERSON>en
 * @date 2024/12/23
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SpecificationModelConsts {

    /**
     * 规格型号
     */
    public static final String SPECIFICATION_MODEL_NAME = "specification_model_name";

    /**
     * 单据状态  ENABLED/启用   DISABLED/停用
     */
    public static final String SPECIFICATION_MODEL_BILL_STATUS = "bill_status";
}
