package com.zte.iccp.itech.extension.domain.enums.changeorder;

import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin.CSR_VAR_PREFIX;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/06
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum FlowVariantEnum implements BaseFlowPlugin.FlowVariant {
    /** 办事处类型 */
    DEPT_TYPE("dept_type"),

    /** 是否CCN大区TD审核 */
    IS_CCN_AREA_TD_APPROVAL("is_ccn_area_td_approval"),
    /** 是否代表处TD审核 */
    IS_REP_OFFICE_TD_APPROVAL("is_rep_office_td_approval"),
    /** 是否技术交付部/网络处审核 */
    IS_TD_NET_OFFICE_APPROVAL("is_td_net_office_approval"),
    /** 是否集成团队审核 */
    IS_INTEGRATION_TEAM_APPROVAL("is_integration_team_approval"),
    /** 是否远程中心支持 */
    IS_REMOTE_CENTER_SUPPORT("is_remote_center_support_result"),
    /** 是否行政领导审批 */
    IS_ADMINISTRATION_LEADER_APPROVAL("is_administration_leader_approval"),
    /** 行政审批推荐层级 */
    ADMIN_APPROVAL_RECOMMEND_LEVEL("admin_approval_recommend_level"),
    /** 是否研发一体化关联产品审核 */
    IS_RELATED_PROD_APPROVAL_RD_INTEGRATION("is_related_prod_approval_rd_integration"),
    /** 是否网服部一体化关联产品审核 */
    IS_RELATED_PROD_APPROVAL_NET_SRV_INTEG("is_related_prod_approval_net_srv_integ"),
    /** 是否匹配电信服务总监角色 */
    IS_MATCH_DIR_TELE_SERVICE_ROLE("is_match_dir_tele_service_role"),
    /** 推荐层级数字 */
    ADMIN_APPROVAL_RECOMMEND_LEVEL_NUM("admin_approval_recommend_level_num"),
    /** 是否算力及核心 */
    IS_CCN("is_ccn"),
    /** 网络属性 */
    MIN_NETWORK_ATTRIBUTE("min_network_attribute"),
    /** 重要程度+风险评估的星级 */
    GOV_ENT_NEED_ADMIN_APPROVAL_STAR_NUM("gov_ent_need_admin_approval_star_num"),
    /** 网服部一体化关联产品会签结果 */
    APPROVE_RESULT_NET_SERVICE_INT(CSR_VAR_PREFIX + "net_service_integration"),
    /** 研发一体化关联产品会签结果 */
    APPROVE_RESULT_RD_INT(CSR_VAR_PREFIX + "rd_integration"),
    /** 国际行政会签结果 */
    APPROVE_RESULT_INTL_ADMIN(CSR_VAR_PREFIX + "intl_admin_approval"),
    /** 故障复盘任务是否完成 */
    IS_FAULT_REVIEW_TASK_FINISH("is_fault_review_task_finish"),
    /** 权限申请，内部用户代表处会签 是否全部驳回 */
    PA_IS_ALL_REJECT("isAllReject"),
    /** 是否网络产品总工审核 */
    IS_NETWORK_CHIEF_ENGINEER("is_network_chief_engineer"),
    /** 是否进入复盘初审 */
    IS_REVIEW_INIT("is_review_init"),

    /** 部门是否网络服务处 */
    IS_DEPT_NET_SERVICE("is_dept_net_service"),

    /** 是否高负荷 */
    IS_HIGH_LOAD("is_high_load"),
    ;
    private final String key;
}
