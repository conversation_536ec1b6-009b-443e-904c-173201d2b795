package com.zte.iccp.itech.extension.common.utils;

import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2024/7/23 上午9:28
 */
public class IdWorkerUtils {
    private long workerId;
    private AtomicLong timestampAndSequence;

    public IdWorkerUtils(Long workerId) {
        this.initTimestampAndSequence();
        this.initWorkerId(workerId);
    }

    private void initTimestampAndSequence() {
        long timestamp = this.getNewestTimestamp();
        long timestampWithSequence = timestamp << 12;
        this.timestampAndSequence = new AtomicLong(timestampWithSequence);
    }

    private void initWorkerId(Long workerId) {
        if (workerId == null) {
            workerId = this.generateWorkerId();
        }

        if (workerId <= 1023L && workerId >= 0L) {
            this.workerId = workerId << 53;
        } else {
            String message = String.format("worker Id can't be greater than %d or less than 0", 1023);
            throw new IllegalArgumentException(message);
        }
    }

    public long nextId() {
        this.waitIfNecessary();
        long next = this.timestampAndSequence.incrementAndGet();
        long timestampWithSequence = next & 9007199254740991L;
        return this.workerId | timestampWithSequence;
    }

    private void waitIfNecessary() {
        long currentWithSequence = this.timestampAndSequence.get();
        long current = currentWithSequence >>> 12;
        long newest = this.getNewestTimestamp();
        if (current >= newest) {
            try {
                Thread.sleep(5L);
            } catch (InterruptedException var8) {
            }
        }

    }

    private long getNewestTimestamp() {
        return System.currentTimeMillis() - 1588435200000L;
    }

    private long generateWorkerId() {
        try {
            return this.generateWorkerIdBaseOnMac();
        } catch (Exception var2) {
            return this.generateRandomWorkerId();
        }
    }

    private long generateWorkerIdBaseOnMac() throws Exception {
        Enumeration<NetworkInterface> all = NetworkInterface.getNetworkInterfaces();
        if (null != all) {
            while(all.hasMoreElements()) {
                NetworkInterface networkInterface = all.nextElement();
                boolean isLoopback = networkInterface.isLoopback();
                boolean isVirtual = networkInterface.isVirtual();
                if (!isLoopback && !isVirtual) {
                    byte[] mac = networkInterface.getHardwareAddress();
                    if (null != mac) {
                        return (mac[4] & 3) << 8 | mac[5] & 255;
                    }
                }
            }
        }

        throw new RuntimeException("no available mac found");
    }

    private long generateRandomWorkerId() {
        return (new Random()).nextInt(1024);
    }
}
