package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.spi.client.NetChangeClient;
import com.zte.iccp.itech.extension.spi.enums.PersonnelTaskTypeEnum;
import com.zte.iccp.itech.extension.spi.model.highcode.CheckPersonnel;
import com.zte.iccp.itech.extension.spi.model.highcode.CheckPersonnelResultVo;
import com.zte.paas.lcap.core.entity.IDataEntity;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @create 2025/6/25 下午8:56
 */
public class PersonnelAbilty {

    /**
     * 网络变更单、分包商（批次） 提交人员积分校验
     */
    public static String commitPersonCheck(String orgId, IDataEntityCollection dataEntityCollection,
                                           String roleCid, String persionCid) {
        if (dataEntityCollection == null || !StringUtils.hasText(orgId)) {
            return null;
        }
        List<String> operators = new ArrayList<>();
        Map<OperatorRoleEnum, List<String>> rolePersonMap = Maps.newHashMap();
        for (int i = CommonConstants.INTEGER_ZERO; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity entity = (DynamicDataEntity) dataEntityCollection.get(i);
            OperatorRoleEnum operatorRole = OperatorRoleEnum.fromValue(TextValuePairHelper.getValue(entity.get(roleCid)));
            List<SingleEmployee> employees = JsonUtils.parseArray(entity.get(persionCid), SingleEmployee.class);
            if (CollectionUtils.isNotEmpty(employees)) {
                List<String> persons = rolePersonMap.get(operatorRole);
                if (CollectionUtils.isEmpty(persons)) {
                    persons = Lists.newArrayList(employees.get(0).getEmpUIID());
                } else {
                    persons.add(employees.get(0).getEmpUIID());
                }
                rolePersonMap.put(operatorRole, persons);
            }
        }
        List<String> supervisors = rolePersonMap.get(OperatorRoleEnum.OPERATING_SUPERVISOR);
        if (CollectionUtils.isNotEmpty(supervisors)) {
            operators.addAll(supervisors);
        }

        List<String> tableOperators = rolePersonMap.get(OperatorRoleEnum.OPERATOR);
        if (CollectionUtils.isNotEmpty(tableOperators)) {
            operators.addAll(tableOperators);
        }

        return checkPersonnel(Lists.newArrayList(orgId), ContextHelper.getEmpNo(), operators, PersonnelTaskTypeEnum.CHANGE_ORDER);
    }

    public static String checkPersonnel(List<String> orgIds, String currentPerson,
                                        List<String> operator, PersonnelTaskTypeEnum typeEnum) {
        // 开关
        if (!ConfigHelper.getBoolean(CHECK_PERSONNEL_ENABLE)) {
            return null;
        }

        // 白名单创建人员
        List<String> white = ConfigHelper.getList(CHECK_PERSONNEL_WHITE);
        if (CollectionUtils.isNotEmpty(white) && white.contains(ContextHelper.getEmpNo())) {
            return null;
        }

        if (CollectionUtils.isEmpty(orgIds)) {
            return null;
        }
        // 操作人员去重
        if (CollectionUtils.isNotEmpty(operator)) {
            operator = operator.stream().distinct().collect(Collectors.toList());
        }
        CheckPersonnel checkPersonnel = new CheckPersonnel(orgIds, currentPerson, operator, typeEnum);
        List<CheckPersonnelResultVo> checkPersonnelResult = NetChangeClient.checkPersonnel(checkPersonnel);
        return checkPersonnelResult.stream()
                .filter(item -> StringUtils.hasText(item.getMsg()))
                .map(CheckPersonnelResultVo::getMsg)
                .distinct()
                .collect(Collectors.joining(LINE_FEED));
    }

    /**
     * 判断操作人员是否是 操作人员或者操作负责人 返回操作人账号
     */
    public static String listenOperator(IDataEntity entity, String roleCid, String persionCid) {
        if (entity == null) {
            return null;
        }
        OperatorRoleEnum operatorRole = OperatorRoleEnum.fromValue(TextValuePairHelper.getValue(entity.get(roleCid)));
        // 不是操作负责人 和操作人员 跳过
        if (OperatorRoleEnum.OPERATING_SUPERVISOR != operatorRole && OperatorRoleEnum.OPERATOR != operatorRole) {
            return null;
        }
        List<SingleEmployee> employees = JsonUtils.parseArray(entity.get(persionCid), SingleEmployee.class);
        if (CollectionUtils.isEmpty(employees)) {
            return null;
        }
        return employees.get(0).getEmpUIID();
    }
}
