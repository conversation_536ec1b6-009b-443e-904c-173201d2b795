package com.zte.iccp.itech.extension.plugin;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.dto.AppendRowsCmd;
import com.zte.paas.lcap.ddm.common.api.dto.MoveRowCmd;
import com.zte.paas.lcap.ddm.common.api.dto.UpdateRowsCmd;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/24
 */
public class ClientViewProxyEx implements IClientViewProxy {

    private final IWebPlugin plugin;

    private final IClientViewProxy base;

    public ClientViewProxyEx(IWebPlugin plugin, IClientViewProxy base) {
        this.plugin = plugin;
        if (base instanceof ClientViewProxyEx) {
            this.base = ((ClientViewProxyEx) base).base;
        } else {
            this.base = base;
        }
    }

    @Override
    public synchronized void sendParentViewCmd(IClientViewProxy parentClientViewProxy) {
        base.sendParentViewCmd(new ClientViewProxyEx(plugin, parentClientViewProxy));
    }

    @Override
    public synchronized void showMessage(String message, MsgType type) {
        base.showMessage(MsgUtils.getMessage(message), type);
    }

    @Override
    public void showMessage(String s, MsgType msgType, Long aLong, Boolean aBoolean) {
        base.showMessage(MsgUtils.getMessage(s), msgType, aLong, aBoolean);
    }

    @Override
    public synchronized void showDeleteConfirm(String message, MsgType type) {
        base.showDeleteConfirm(MsgUtils.getMessage(message), type);
    }

    @Override
    public synchronized void showValidateMessageWithSource(JSONArray args, String source) {
        base.showValidateMessageWithSource(args, MsgUtils.getMessage(source));
    }

    @Override
    public synchronized void showTableValidateMessage(String tableCid, String cid, int rowIndex, String message) {
        base.showTableValidateMessage(tableCid, cid, rowIndex, MsgUtils.getMessage(message));
    }

    @Override
    public synchronized void showValidateMessage(String cid, String message) {
        base.showValidateMessage(cid, MsgUtils.getMessage(message));
    }

    // 透传到base BEGIN
    @Override
    public void showForm(Map<String, Object> map) {
        base.showForm(map);
    }

    @Override
    public void showForward(Map<String, Object> map) {
        base.showForward(map);
    }

    @Override
    public void setValue(Map<String, Object> map) {
        base.setValue(map);
    }

    @Override
    public void setValueWithSource(Map<String, Object> map, String s) {
        base.setValueWithSource(map, s);
    }

    @Override
    public void setLastModifyTime(String s) {
        base.setLastModifyTime(s);
    }

    @Override
    public void showConflictConfirm() {
        base.showConflictConfirm();
    }

    @Override
    public void setValue(String s, Object o) {
        base.setValue(s, o);
    }

    @Override
    public void setValue(String s, String s1, Map<String, Object> map) {
        base.setValue(s, s1, map);
    }

    @Override
    public void setValue(String s, String s1, List<Object> list) {
        base.setValue(s, s1, list);
    }

    @Override
    public void updateValue(String s, String s1, Object o) {
        base.updateValue(s, s1, o);
    }

    @Override
    public void closeWindow(Map<String, Object> map) {
        base.closeWindow(map);
    }

    @Override
    public void closeForm(String s, String s1, Map<String, Object> map) {
        base.closeForm(s, s1, map);
    }

    @Override
    public void showConfirm(List<Object> list) {
        base.showConfirm(list);
    }

    @Override
    public void setVisible(String s, Boolean aBoolean) {
        base.setVisible(s, aBoolean);
    }

    @Override
    public void setExpand(String s, Boolean aBoolean) {
        base.setExpand(s, aBoolean);
    }

    @Override
    public void setEnable(String s, Boolean aBoolean) {
        base.setEnable(s, aBoolean);
    }

    @Override
    public void setBehavior(String s, String s1) {
        base.setBehavior(s, s1);
    }

    @Override
    public void appendRows(String s, List<AppendRowsCmd> list) {
        base.appendRows(s, list);
    }

    @Override
    public void appendRows(Map<String, List<AppendRowsCmd>> map) {
        base.appendRows(map);
    }

    @Override
    public void appendRows(String s, List<AppendRowsCmd> list, String s1, String s2) {
        base.appendRows(s, list, s1, s2);
    }

    @Override
    public void deleteRows(String s, List<Integer> list) {
        base.deleteRows(s, list);
    }

    @Override
    public void updateRows(String s, List<UpdateRowsCmd> list) {
        base.updateRows(s, list);
    }

    @Override
    public void updateRows(String s, String s1, List<Object> list) {
        base.updateRows(s, s1, list);
    }

    @Override
    public void updateRows(String s, String s1, String s2, List<UpdateRowsCmd> list) {
        base.updateRows(s, s1, s2, list);
    }

    @Override
    public void updateRows(Map<String, Object> map) {
        base.updateRows(map);
    }

    @Override
    public void updateRowsWithSource(Collection<Map<String, Object>> collection, String s) {
        base.updateRowsWithSource(collection, s);
    }

    @Override
    public void setCellColor(String s, Map<String, Object> map) {
        base.setCellColor(s, map);
    }

    @Override
    public void setCellColor(String s, Map<String, Object> map, boolean b) {
        base.setCellColor(s, map, b);
    }

    @Override
    public void setFont(String s, Map<String, Object> map) {
        base.setFont(s, map);
    }

    @Override
    public void setNumbers(String s, Map<String, Object> map) {
        base.setNumbers(s, map);
    }

    @Override
    public void setPropsWithSource(Collection<Map<String, Object>> collection, String s) {
        base.setPropsWithSource(collection, s);
    }

    @Override
    public void resetPage(String s) {
        base.resetPage(s);
    }

    @Override
    public void resetPage(String s, Boolean aBoolean) {
        base.resetPage(s, aBoolean);
    }

    @Override
    public void selectRows(String s, List<Integer> list) {
        base.selectRows(s, list);
    }

    @Override
    public void selectRows(String s, List<Integer> list, String s1, String s2) {
        base.selectRows(s, list, s1, s2);
    }

    @Override
    public void setOptions(Map<String, Object> map) {
        base.setOptions(map);
    }

    @Override
    public void setOptions(String s, List<Map<String, Object>> list) {
        base.setOptions(s, list);
    }

    @Override
    public void setTreeOptions(Map<String, Object> map) {
        base.setTreeOptions(map);
    }

    @Override
    public void setAllTreeOptions(JSONObject jsonObject) {
        base.setAllTreeOptions(jsonObject);
    }

    @Override
    public void expandTreeNode(Map<String, Object> map) {
        base.expandTreeNode(map);
    }

    @Override
    public void setTreeCurrentKey(String s, String s1) {
        base.setTreeCurrentKey(s, s1);
    }

    @Override
    public void appendOptions(Map<String, Object> map) {
        base.appendOptions(map);
    }

    @Override
    public void selectRowsByKeyId(String s, List<String> list) {
        base.selectRowsByKeyId(s, list);
    }

    @Override
    public void entryRowSplitInput(Map<String, Object> map) {
        base.entryRowSplitInput(map);
    }

    @Override
    public void refreshData(String s, String s1, Boolean aBoolean) {
        base.refreshData(s, s1, aBoolean);
    }

    @Override
    public void refreshData(String s, String s1, String s2) {
        base.refreshData(s, s1, s2);
    }

    @Override
    public void refreshData(String s, String s1, String s2, Boolean aBoolean) {
        base.refreshData(s, s1, s2, aBoolean);
    }

    @Override
    public void setFieldProps(List<Object> list, String s, String s1) {
        base.setFieldProps(list, s, s1);
    }

    @Override
    public void setFieldProps(List<Object> list) {
        base.setFieldProps(list);
    }

    @Override
    public void setFieldPropsWithSource(List<Object> list, String s) {
        base.setFieldPropsWithSource(list, s);
    }

    @Override
    public void setFieldOptions(List<Object> list) {
        base.setFieldOptions(list);
    }

    @Override
    public void appendFieldOptions(List<Object> list) {
        base.appendFieldOptions(list);
    }

    @Override
    public void expandFieldTreeNode(List<Object> list) {
        base.expandFieldTreeNode(list);
    }

    @Override
    public void setFieldTreeOptions(List<Object> list) {
        base.setFieldTreeOptions(list);
    }

    @Override
    public void setProps(String s, String s1, List<Object> list) {
        base.setProps(s, s1, list);
    }

    @Override
    public void setFieldAllTreeOptions(JSONArray jsonArray) {
        base.setFieldAllTreeOptions(jsonArray);
    }

    @Override
    public void showVisitor(List<Object> list, String s, String s1) {
        base.showVisitor(list, s, s1);
    }

    @Override
    public void setChildren(String s, Object o) {
        base.setChildren(s, o);
    }

    @Override
    public void setFieldValue(List<Object> list) {
        base.setFieldValue(list);
    }

    @Override
    public void setFieldValue(List<Object> list, String s, String s1) {
        base.setFieldValue(list, s, s1);
    }

    @Override
    public void showF7(Map<String, Object> map) {
        base.showF7(map);
    }

    @Override
    public void setId(String s, Object o) {
        base.setId(s, o);
    }

    @Override
    public void initPage(Map<String, Object> map) {
        base.initPage(map);
    }

    @Override
    public void downloadFile(Map<String, Object> map) {
        base.downloadFile(map);
    }

    @Override
    public void doAction(String s, String s1) {
        base.doAction(s, s1);
    }

    @Override
    public void changePropsWithSource(List<Object> list, String s) {
        base.changePropsWithSource(list, s);
    }

    @Override
    public void setChildPropsWithSource(List<Object> list, String s) {
        base.setChildPropsWithSource(list, s);
    }

    @Override
    public void openWindow(String s, String s1) {
        base.openWindow(s, s1);
    }

    @Override
    public void resetTable(String s) {
        base.resetTable(s);
    }

    @Override
    public void resetTable(String s, Boolean aBoolean) {
        base.resetTable(s, aBoolean);
    }

    @Override
    public void resetTable(List<String> list) {
        base.resetTable(list);
    }

    @Override
    public void resetTable(String s, List<String> list) {
        base.resetTable(s, list);
    }

    @Override
    public void resetTable(List<String> list, List<String> list1) {
        base.resetTable(list, list1);
    }

    @Override
    public void markRepeats(String s, String s1, Set<Integer> set) {
        base.markRepeats(s, s1, set);
    }

    @Override
    public void setExistsItemDisabled(String s, List<Pair<String, Set<String>>> list) {
        base.setExistsItemDisabled(s, list);
    }

    @Override
    public void moveRow(String s, List<MoveRowCmd> list) {
        base.moveRow(s, list);
    }

    @Override
    public void setEntity(String s, String s1, String s2, Map<String, Object> map, Object o) {
        base.setEntity(s, s1, s2, map, o);
    }

    @Override
    public void createComments(List<Object> list, String s, String s1) {
        base.createComments(list, s, s1);
    }

    @Override
    public void showRecords(List<Object> list) {
        base.showRecords(list);
    }

    @Override
    public void focusRow(String s, String s1, Integer integer) {
        base.focusRow(s, s1, integer);
    }

    @Override
    public void focusRow(String s, String s1, String s2, String s3, Integer integer) {
        base.focusRow(s, s1, s2, s3, integer);
    }

    @Override
    public void setTableChildren(String s, Map<String, Object> map) {
        base.setTableChildren(s, map);
    }

    @Override
    public void clearInitPageCmd() {
        base.clearInitPageCmd();
    }

    @Override
    public void clearValidateMessageWithSource(JSONArray jsonArray, String s) {
        base.clearValidateMessageWithSource(jsonArray, s);
    }

    @Override
    public void showTableRepeatValidateMessage(String s, List<Integer> list) {
        base.showTableRepeatValidateMessage(s, list);
    }

    @Override
    public void clearValidateMessage(String s) {
        base.clearValidateMessage(s);
    }

    @Override
    public void clearPageValidateMessage() {
        base.clearPageValidateMessage();
    }

    @Override
    public void clearTableValidateMessage(String s, String s1, int i) {
        base.clearTableValidateMessage(s, s1, i);
    }

    @Override
    public void reloadPage(JSONObject jsonObject) {
        base.reloadPage(jsonObject);
    }

    @Override
    public void showDialog(Map<String, Object> map) {
        base.showDialog(map);
    }

    @Override
    public void clearControlsState() {
        base.clearControlsState();
    }

    @Override
    public Object getControlState(String s) {
        return base.getControlState(s);
    }

    @Override
    public void setControlState(String s, Map<String, Object> map) {
        base.setControlState(s, map);
    }

    @Override
    public void afterControlStateChanged(String s, Map<String, Object> map) {
        base.afterControlStateChanged(s, map);
    }

    @Override
    public void afterChildPropsChanged(String s, String s1, Map<String, Object> map) {
        base.afterChildPropsChanged(s, s1, map);
    }

    @Override
    public void afterCellPropsChanged(String s, String s1, int i, Map<String, Object> map) {
        base.afterCellPropsChanged(s, s1, i, map);
    }

    @Override
    public void afterActionBarItemPropsChanged(String s, String s1, Map<String, Object> map) {
        base.afterActionBarItemPropsChanged(s, s1, map);
    }

    @Override
    public void afterColumnsItemPropsChanged(String s, String s1, Map<String, Object> map) {
        base.afterColumnsItemPropsChanged(s, s1, map);
    }

    @Override
    public void afterOperationItemPropsChanged(String s, String s1, int i, Map<String, Object> map) {
        base.afterOperationItemPropsChanged(s, s1, i, map);
    }

    @Override
    public Object getFieldState(String s) {
        return base.getFieldState(s);
    }

    @Override
    public void setFieldState(String s, Object o) {
        base.setFieldState(s, o);
    }

    @Override
    public void postBack(String s, Map<String, Object> map) {
        base.postBack(s, map);
    }

    @Override
    public void setFieldState(String s, String s1, Object o) {
        base.setFieldState(s, s1, o);
    }

    @Override
    public Object getFieldState(String s, String s1) {
        return base.getFieldState(s, s1);
    }

    @Override
    public void setControlState(String s, String s1, Object o) {
        base.setControlState(s, s1, o);
    }

    @Override
    public Object getControlState(String s, String s1) {
        return base.getControlState(s, s1);
    }

    @Override
    public void setChildProps(List<Object> list, String s, String s1) {
        base.setChildProps(list, s, s1);
    }

    @Override
    public void showImportDialog(JSONObject jsonObject) {
        base.showImportDialog(jsonObject);
    }

    @Override
    public void setImportValue(JSONObject jsonObject) {
        base.setImportValue(jsonObject);
    }

    @Override
    public List<Object> getShowValidateMessageResult() {
        return base.getShowValidateMessageResult();
    }

    @Override
    public void showApprovalDialog(JSONObject jsonObject) {
        base.showApprovalDialog(jsonObject);
    }

    @Override
    public void showCustomassociation(FormShowParameter formShowParameter) {
        base.showCustomassociation(formShowParameter);
    }

    @Override
    public void updateTreeNode(String s, Object o) {
        base.updateTreeNode(s, o);
    }

    @Override
    public void updateTreeNode(String s, Object o, String s1, String s2) {
        base.updateTreeNode(s, o, s1, s2);
    }

    @Override
    public void setFieldLang(List<Object> list) {
        base.setFieldLang(list);
    }

    @Override
    public void setLang(Map<String, Object> map) {
        base.setLang(map);
    }

    @Override
    public void sendOperationResult(Map<String, Object> map) {
        base.sendOperationResult(map);
    }

    @Override
    public void setColumns(Map<String, Object> map) {
        base.setColumns(map);
    }

    @Override
    public void adjustForceRefresh(List<Object> list) {
        base.adjustForceRefresh(list);
    }

    @Override
    public void requestFollowPayload(String s, String s1, String s2) {
        base.requestFollowPayload(s, s1, s2);
    }

    @Override
    public void requestFollowPayload(Map<String, Object> map) {
        base.requestFollowPayload(map);
    }

    @Override
    public void executeFrontendCallBackFunc(String s, Map<String, Object> map) {
        base.executeFrontendCallBackFunc(s, map);
    }

    @Override
    public List<Object> getActionResult() {
        return base.getActionResult();
    }

    @Override
    public void changeProps(List<Object> list) {
        base.changeProps(list);
    }

    @Override
    public void setChildProps(List<Object> list) {
        base.setChildProps(list);
    }

    @Override
    public void addCommand(String s, List<Object> list) {
        base.addCommand(s, list);
    }

    @Override
    public void addCommand(List<Object> list) {
        base.addCommand(list);
    }

    @Override
    public void addCommand(String s, String s1, String s2, List<Object> list) {
        base.addCommand(s, s1, s2, list);
    }

    @Override
    public void addCommand(String s, String s1, String s2, List<Object> list, Boolean aBoolean) {
        base.addCommand(s, s1, s2, list, aBoolean);
    }

    @Override
    public void setProps(Map<String, Object> map) {
        base.setProps(map);
    }

    @Override
    public void setPermissions(List<Object> list) {
        base.setPermissions(list);
    }
    // 透传到base END
}
