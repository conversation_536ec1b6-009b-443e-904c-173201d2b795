package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.subentity.TechSolutionCheckFieldConsts.TECH_SOLUTION_CHECK_CONTENT;
import static com.zte.iccp.itech.extension.domain.constant.subentity.TechSolutionCheckFieldConsts.TECH_SOLUTION_CHECK_CONTENT_EN;


/**
 * <AUTHOR>
 * @date 2024/9/7 下午7:19
 */
@Setter
@Getter
@BaseSubEntity.Info(value = "tech_solution_check", parent = ChangeOrder.class)
public class TechSolutionCheck extends BaseSubEntity {

    @JsonProperty(value = TECH_SOLUTION_CHECK_CONTENT)
    private String techSolutionCheckContent;

    @JsonProperty(value = TECH_SOLUTION_CHECK_CONTENT_EN)
    private String techSolutionCheckContentEn;
}
