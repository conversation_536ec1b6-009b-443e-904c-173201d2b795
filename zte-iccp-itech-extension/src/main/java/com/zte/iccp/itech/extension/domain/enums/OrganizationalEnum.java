package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.clockin.config.OfficeDutyPersonnel;
import com.zte.iccp.itech.extension.domain.model.entity.*;
import com.zte.iccp.itech.extension.domain.model.subentity.OrganizationApprover;
import com.zte.iccp.itech.extension.domain.model.vo.ApproverConfigurationAll;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.enums.FieldType.*;

/**
 * <AUTHOR>
 * @create 2025/2/17 下午8:23
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OrganizationalEnum implements SingletonTextValuePairsProvider {
    /**
     * 审核人配置
     */
    APPROVER_CONFIG("审核人配置", "approver config", SPECIAL, ApproverConfigurationAll.class, "", ""),

    /**
     * 网络变更单
     */
    CHANGE_ORDER("网络变更单", "change order", SINGLE_OPTION_PATH, ChangeOrderAll.class,
            "responsibleDept", "organization_id"),

    /**
     * 合作方网络变更单
     */
    SUBCONTRACTOR_OC("合作方网络变更单", "subcontractor oc", SINGLE_OPTION_PATH, SubcontractorChangeOrderAll.class,
            "responsibleDept", "organization_id"),

    /**
     * 批次任务
     */
    BATCH_NETWORK_ASSIGNMENT("批次任务", "batch network assignment", ALL_TEXT, BatchTask.class,
            "organizationId", "organization_id"),

    /**
     * 合作方批次任务
     */
    SUBCONTRACTOR_BATCH_TASK("合作方批次任务", "subcontractor batch task", ALL_TEXT, SubcontractorBatchTask.class,
            "organizationId", "organization_id"),

    /**
     * 任务中心
     */
    ASSIGNMENT("任务中心", "assignment", SINGLE_OPTION_CODE, Assignment.class, "representativeOffice",
            "representative_office"),

    /**
     * CCN默认授权文件申请
     */
    APPLY_CCN_AUTHORIZATION("CCN默认授权文件申请", "apply ccn authorization", SINGLE_OPTION_PATH,
            CcnAuthorizationApplication.class, "organization", "organization_id"),

    /**
     * 代表处审批人
     */
    ORGANIZATION_APPROVER("代表处审批人", "organization approver", SINGLE_TEXT, OrganizationApprover.class,
            "organization", "organization"),

    /**
     * 打卡复盘
     */
    CLOCK_IN_REVIEWS("打卡复盘", "clock in reviews", SINGLE_OPTION_CODE, ClockInReviews.class,
            "responsibleDept", "responsible_dept"),

    /**
     * 打卡任务
     */
    CLOCK_IN_TASK("打卡任务", "clock in task", ALL_TEXT, ClockInTask.class, "responsibleDept", "responsible_dept"),

    /**
     * 办事处值班人员
     */
    OFFICE_DUTY_PERSONNEL("办事处值班人员", "office duty personnel", SINGLE_OPTION_PATH,
            OfficeDutyPersonnel.class, "office", "office"),

    /**
     * 故障管理单
     */
    FAULT_MANAGEMENT_TASK("故障管理单", "fault_management_task", SINGLE_OPTION_CODE,
            FaultManagementOrder.class, "organization", "organization"),

    /**
     * 客户满意度责任人配置
     */
    SATISFACTION_RESPONSIBLE_PERSON("客户满意度责任人配置", "satisfaction responsible person", SINGLE_TEXT,
            SatisfactionResponsiblePerson.class, "organizationId", "organization_id"),

    /**
     * 权限申请
     */
    PERMISSION_APPLICATION("权限申请", "permission application", SPECIAL, PermissionApplication.class,
            "", ""),

    /**
     * 标准网络集
     */
    STANDARD_NETWORK_SET("标准网络集", "standard network set", SINGLE_OPTION_PATH, StandardNetworkSet.class,
            "organization", "organization"),

    /**
     * 技术管理任务
     */
    TECHNICAL_MANAGEMENT_TASK("技术管理任务", "technical management task", MULTIPLE_OPTION,
            TechnicalManagementTask.class, "organizationId", "organization_id"),
    ;

    private final String zhCn;

    private final String enUs;

    /**
     * 组织存储类型
     */
    private final FieldType fieldType;

    /**
     * 表对应的对象class
     */
    private final Class<? extends BaseEntity> clazz;

    /**
     * 实体entity对应组织的字段名
     */
    private final String orgField;

    /**
     * 查询表过滤字段
     */
    private final String filter;

    @Override
    public String getValue() {
        return name();
    }
}
