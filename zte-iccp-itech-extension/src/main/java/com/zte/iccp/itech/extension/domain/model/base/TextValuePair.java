package com.zte.iccp.itech.extension.domain.model.base;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@Getter
@Setter
public class TextValuePair {
    private MultiLangText text;

    private String value;

    /**
     * 根据语言环境获取对应描述信息
     * @param language
     * @return String
     */
    public String getTextByLanguage(String language) {
        if (ObjectUtil.isNull(text)) {
            return null;
        }
        if (ZH_CN.equals(language)) {
            return text.getZhCN();
        }
        String englishText = text.getEnUS();
        return StringUtils.hasText(englishText) ? englishText : text.getZhCN();
    }
}