package com.zte.iccp.itech.extension.domain.model.clockin.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInCustomerFlagEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.OfficeDutyPersonnelConsts.*;

/**
 * 办事处值班人员
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/15
 */
@Getter
@Setter
@BaseEntity.Info("office_duty_personnel")
public class OfficeDutyPersonnel extends BaseWeeklyDutyPerson {

    @JsonProperty(value = OFFICE)
    @ApiModelProperty("代表处")
    private List<TextValuePair> office;

    @JsonProperty(value = PROD_TYPE)
    @ApiModelProperty("产品类型")
    private List<TextValuePair> prodType;

    @ApiModelProperty("客户标识")
    @JsonProperty(value = CUSTOMER_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ClockInCustomerFlagEnum customerFlag;

    @ApiModelProperty("客户id")
    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;
}
