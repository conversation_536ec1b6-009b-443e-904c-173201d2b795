package com.zte.iccp.itech.extension.openapi.scheduler;

import com.zte.iccp.itech.extension.ability.reportpush.ClockInProdPushAbility;
import com.zte.iccp.itech.extension.ability.reportpush.NetChangeBatchTaskReturnPushAbility;
import com.zte.iccp.itech.extension.ability.reportpush.NetChangeInterPushAbility;
import com.zte.iccp.itech.extension.ability.reportpush.NetChangeSummaryPushAbility;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;

/**
 * <AUTHOR>
 * @date 2025/4/9 下午3:32
 */
public class NetworkChangeReportOpenApi extends AbstractOpenApi {

    public void domesticBatchTaskReturnPush() {
        AsyncExecuteUtils.execute(NetChangeBatchTaskReturnPushAbility::domesticBatchTaskReturnPush);
    }

    public void domesticDayPush() {
        AsyncExecuteUtils.execute(NetChangeSummaryPushAbility::domesticDayPush);
    }

    public void orgDayPush() {
        AsyncExecuteUtils.execute(NetChangeSummaryPushAbility::orgDayPush);
    }

    public void orgWeekPush() {
        AsyncExecuteUtils.execute(NetChangeSummaryPushAbility::orgWeekPush);
    }

    public void clockInProdPush() {
        AsyncExecuteUtils.execute(ClockInProdPushAbility::clockInProdPush);
    }

    public void internalOrgDayPush() {
        AsyncExecuteUtils.execute(NetChangeInterPushAbility::interOrgDayPush);
    }
}
