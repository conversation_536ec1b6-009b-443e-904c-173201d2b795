package com.zte.iccp.itech.extension.openapi.cnop;

import com.zte.iccp.itech.extension.ability.CnopAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.SysAuthUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.*;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.CnopChangeOrderVO;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.CreateOrderVO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 对接CNOP接口api
 *
 * <AUTHOR>
 * @create 2025/3/20 上午9:09
 */
public class CnopOpenApi extends AbstractOpenApi {

    /*
     * 创建网络变更单
     * */
    public ServiceData<CreateOrderVO> createChangeOrder(HttpServletRequest request,
                                                        @RequestPart("cnopChangeOrderDto") CnopChangeOrderDto cnopChangeOrderDto,
                                                        @RequestPart("otherAttachments") List<MultipartFile> otherAttachments) throws IOException {
        SysAuthUtils.auth(request);
        // 校验必填字段
        checkRequired(cnopChangeOrderDto);
        // 校验文件大小数量，和网络变更单提单页面约束保持一致
        checkFile(otherAttachments);
        ServiceData<CreateOrderVO> result = new ServiceData<>();
        CreateOrderVO createOrderVO = CnopAbility.createChangeOrder(cnopChangeOrderDto, otherAttachments);
        result.setCode(success());
        result.setBo(createOrderVO);
        return result;
    }

    /*
     * 查询单据信息（一阶段仅查询单据状态）
     * */
    public ServiceData<List<CnopChangeOrderVO>> getOrderInfo(HttpServletRequest request,
                                                             @RequestBody List<CnopChangeOrderQueryDto> queryDtoList) {
        SysAuthUtils.auth(request);
        if (CollectionUtils.isEmpty(queryDtoList)) {
            throw new LcapBusiException("queryList is not empty");
        }
        ServiceData<List<CnopChangeOrderVO>> result = new ServiceData<>();
        result.setBo(CnopAbility.getOrderInfo(queryDtoList));
        result.setCode(success());
        return result;
    }

    /*
    * 校验必填字段
    * */
    private void checkRequired(CnopChangeOrderDto dto) {
        List<String> emptyFields = new ArrayList<>();
        if (!StringUtils.hasText(dto.getOperationSubjectSuffix())) {
            emptyFields.add("orderName");
        }
        if (dto.getIsGdpr() == null) {
            emptyFields.add("isGdpr");
        }
        if (dto.getIsGovernmentEnterprise() == null) {
            emptyFields.add("isGovernmentEnterprise");
        }
        if (dto.getGdprRequire() == null) {
            emptyFields.add("gdprRequire");
        }
        if (!StringUtils.hasText(dto.getDepartmentCode())) {
            emptyFields.add("department");
        }
        if (!StringUtils.hasText(dto.getProductIdPath())) {
            emptyFields.add("product");
        }
        if (!StringUtils.hasText(dto.getOperationType())) {
            emptyFields.add("operationType");
        }
        if (!StringUtils.hasText(dto.getOperationReason())) {
            emptyFields.add("operationReason");
        }
        if (!StringUtils.hasText(dto.getCreateBy())) {
            emptyFields.add("createBy");
        }
        if (!CollectionUtils.isEmpty(emptyFields)) {
            throw new LcapBusiException(String.format("%s can not be null", String.join(CommonConstants.COMMA, emptyFields)));
        }
        // 新增产品分类层级校验
        if (!StringUtils.hasText(ProductUtils.getProductIdByLevel(dto.getProductIdPath(), 4))) {
            throw new LcapBusiException("productIdPath format error, must has 4 level.");
        }

    }

    /*
     * 校验必填字段和文件约束
     * */
    private void checkFile(List<MultipartFile> otherAttachments) {
        if (CollectionUtils.isEmpty(otherAttachments)) {
            return;
        }
        if (otherAttachments.size() > 50) {
            throw new LcapBusiException("file count can not exceed 50");
        }
        boolean oversizeFileFlag = otherAttachments.stream().anyMatch(file -> file.getSize() > 200*1024*1024);
        if (oversizeFileFlag) {
            throw new LcapBusiException("single file size can not exceed 200M");
        }
    }

    private static RetCode success() {
        return new RetCode() {{
            setCode(RetCode.SUCCESS_CODE);
            setMsgId(RetCode.SUCCESS_MSGID);
            setMsg(RetCode.SUCCESS_MSG);
        }};
    }
}
