package com.zte.iccp.itech.extension.common.dynamicbean;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.paas.lcap.common.spring.ApplicationContextHolder;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.support.GenericApplicationContext;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/04/23
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DynamicBeanLoader {

    private static final String[] BASE_PACKAGES
            = ConfigHelper.getRaw(
                    "dynamicBeans.basePackages",
                    "com.zte.iccp.itech.extension")
                    .replaceAll("\\s", "")
                    .split(",");

    private static final ClassLoader BEAN_CLASS_LOADER = DynamicBeanLoader.class.getClassLoader();

    private static GenericApplicationContext applicationContext;

    static {
        log.info("DynamicBeans basePackages:\n{}", String.join("\n", BASE_PACKAGES));

        // 初次加载帮助类时扫描加载动态Bean
        scanBeans();
        launchDynamicBeans();
        registerControllers();
        registerDynamicAspects();
    }

    /**
     * 用于触发静态块
     */
    public static void load() {
    }

    @SneakyThrows
    private static void scanBeans() {
        GenericApplicationContext applicationContext = getApplicationContext();
        NameRecBeanScanner scanner = getScanner(applicationContext);

        // 执行扫描
        scanner.scan(BASE_PACKAGES);

        log.info("DynamicBeans loaded beans:\n{}", String.join("\n", scanner.getScannedClassNames()));
    }

    @SneakyThrows
    private static @NotNull GenericApplicationContext getApplicationContext() {
        if (applicationContext != null) {
            return applicationContext;
        }

        // 从平台工具类中反射拿到ApplicationContext实例
        Field field = ApplicationContextHolder.class
                .getDeclaredField("applicationContext");
        field.setAccessible(true);

        applicationContext = (GenericApplicationContext) field.get(null);

        // 单应用独立部署环境，所有插件包共用同一个loader，父级是默认loader
        applicationContext.setClassLoader(BEAN_CLASS_LOADER);

        return applicationContext;
    }

    private static @NotNull NameRecBeanScanner getScanner(GenericApplicationContext applicationContext) {
        // 通过ApplicationContext获取BeanFactory
        DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory)
                applicationContext.getBeanFactory();
        beanFactory.setBeanClassLoader(BEAN_CLASS_LOADER);

        // 创建ClassPathBeanDefinitionScanner实例
        NameRecBeanScanner scanner = new NameRecBeanScanner(beanFactory);
        scanner.setResourceLoader(new DefaultResourceLoader(BEAN_CLASS_LOADER));

        scanner.setIncludeAnnotationConfig(true);

        // 添加需要扫描的注解过滤器
        scanner.addIncludeFilter(new AnnotationTypeFilter(Component.class));

        return scanner;
    }

    /**
     * 实例化DynamicBean，用于启动其功能，包括：
     * 1、定时任务 Scheduled；
     * 2、kafka消费者 MessageConsumer
     */
    private static void launchDynamicBeans() {
        ApplicationContextHolder.getBeansWithAnnotation(DynamicComponent.class);
    }

    private static void registerControllers() {
        ApplicationContextHolder.getBeansWithAnnotation(Controller.class)
                .values().stream()
                .filter(bean -> Arrays.stream(BASE_PACKAGES)
                        .anyMatch(pkg -> bean.getClass().getName().startsWith(pkg)))
                .forEach(DynamicBeanLoader::registerRequestMapping);
    }

    @SneakyThrows
    private static void registerRequestMapping(Object controller) {
        // 获取请求映射注册器
        RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping)
                ApplicationContextHolder.getBean("requestMappingHandlerMapping");

        // 获取RequestMappingInfo构造方法
        Method getMappingForMethod = RequestMappingHandlerMapping.class.getDeclaredMethod(
                "getMappingForMethod", Method.class, Class.class);
        getMappingForMethod.setAccessible(true);

        Class<?> controllerClass = controller.getClass();
        for (Method method : controllerClass.getDeclaredMethods()) {
            RequestMappingInfo mappingInfo = (RequestMappingInfo) getMappingForMethod.invoke(
                    requestMappingHandlerMapping, method, controllerClass);
            requestMappingHandlerMapping.registerMapping(
                    mappingInfo, controllerClass, method);
        }
    }

    private static void registerDynamicAspects() {
        Map<String, Object> aspectBeans = ApplicationContextHolder.getBeansWithAnnotation(DynamicAspect.class);
        if (CollectionUtils.isEmpty(aspectBeans)) {
            return;
        }

        GenericApplicationContext applicationContext = getApplicationContext();
        DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory) applicationContext.getBeanFactory();

        aspectBeans.forEach((aspectBeanName, aspectBean) -> {
            Class<?> aspectClass = aspectBean.getClass();
            DynamicAspect dynamicAspect = getAnnotation(aspectClass, DynamicAspect.class);
            if (dynamicAspect == null) {
                log.warn("切面bean[{}]未找到@DynamicAspect注解", aspectClass.getName());
                return;
            }

            Class<?> targetType = dynamicAspect.value();
            String[] targetBeanNames = applicationContext.getBeanNamesForType(targetType);
            for (String targetBeanName : targetBeanNames) {
                if (!applicationContext.containsBeanDefinition(targetBeanName)) {
                    continue;
                }

                Object targetBean = applicationContext.getBean(targetBeanName);
                applicationContext.removeBeanDefinition(targetBeanName);
                beanFactory.destroySingleton(targetBeanName);

                // 用切面bean织入代理
                AspectJProxyFactory proxyFactory = new AspectJProxyFactory(targetBean);
                proxyFactory.addAspect(aspectBean);
                Object aspectProxy = proxyFactory.getProxy();
                beanFactory.registerSingleton(targetBeanName, aspectProxy);

                log.info("已用AOP代理重新注册目标bean: {}({}) 以支持动态切面: {}",
                        targetBeanName, targetType.getName(), aspectClass.getName());
            }
        });
    }

    @Nullable
    private static <T extends Annotation> T getAnnotation(
            Class<?> beanClass, Class<T> annotationClass) {
        T annotation = beanClass.getAnnotation(annotationClass);
        if (annotation == null) {
            // 兼容CGLIB代理类
            Class<?> superClass = beanClass.getSuperclass();
            if (superClass != null) {
                annotation = superClass.getAnnotation(annotationClass);
            }
        }
        return annotation;
    }
}
