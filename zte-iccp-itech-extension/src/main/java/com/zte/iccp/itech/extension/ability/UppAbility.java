package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.ResourceTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ResourceDto;
import com.zte.iccp.itech.extension.domain.model.UppUserAuthInfo;
import com.zte.iccp.itech.extension.domain.model.UserPermissionDto;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.client.UcsClient;
import com.zte.iccp.itech.extension.spi.model.TreeServiceObjectVo;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.iccp.itech.extension.spi.model.query.TreeServiceObjectQuery;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.client.UppClient;
import com.zte.itp.authorityclient.dto.UppRequest;
import com.zte.itp.authorityclient.dto.constraint.ConstraintQueryVO;
import com.zte.itp.authorityclient.dto.resource.ResourceEnDTO;
import com.zte.itp.authorityclient.dto.user.UserByRoleDataResDTO;
import com.zte.itp.authorityclient.entity.input.*;
import com.zte.itp.authorityclient.entity.input.PersonRoleDataAuthoriAddEntity;
import com.zte.itp.authorityclient.entity.input.RoleUserListEntity;
import com.zte.itp.authorityclient.entity.output.*;
import com.zte.itp.authorityclient.entity.output.UserVO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.itp.authorityclient.constants.RetCodeConstant.SUCCESS_CODE;

/**
 * <AUTHOR>
 * @date 2024/11/12 上午11:45
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UppAbility {

    public static final String UPP_PERSONAL_PERMISSIONS = "upp_personal_permissions";
    /* 角色-用户 用户查询页面 查询框cid*/
    public static final String SELECT_ROLE = "select_role";
    /* 查询类型 用户查询页面 查询框cid*/
    public static final String QUERY_TYPE = "query_type";

    public static final String TYPE_GROUP = "group";

    public static final String TYPE_USER = "user";

    private static final String UPP_MODULE_ID = ConfigHelper.getRaw("upp.auth.moduleId");

    private static final Long UPP_PRODUCT_ID = Long.parseLong(ConfigHelper.getRaw("upp.auth.productId"));

    private static final Long UPP_TENANT_ID = Long.parseLong(ConfigHelper.getRaw("upp.auth.tenantId"));

    private static final Long PROD_CONSTRAINT_ID = Long.parseLong(ConfigHelper.getRaw("upp.auth.prod.constraintId"));

    private static final Long ORG_CONSTRAINT_ID = Long.parseLong(ConfigHelper.getRaw("upp.auth.org.constraintId"));

    private static final String PRECISE_CONTAIN = "PreciseContain";

    private static final String UPP_ROLE_CONSTRAINT_RESP_MAP_KEY = "constraintList";

    private static final String UPP_DATA_ORGANIZATION_MAP_KEY = "ORGANIZATION";

    private static final String UPP_DATA_PRODUCT_MAP_KEY = "PRODUCT";

    private static final String UPP_DATA_NAME_MAP_KEY = "NAME";

    private static final String UPP_DATA_IDPATH_MAP_KEY = "IDPATH";

    /* UPP langID   语言ID(1033 英文2052 中文) */
    private static final String UPP_LANG_ZH = "2052";

    private static final String UPP_LANG_US = "1033";

    public static List<UserRoleVO> getAllRoles() {
        CommonModuleIdEntity commonModuleIdEntity = new CommonModuleIdEntity();
        commonModuleIdEntity.setTenantId(UPP_TENANT_ID);
        commonModuleIdEntity.setProductId(UPP_PRODUCT_ID);
        commonModuleIdEntity.setModuleId(UPP_MODULE_ID);
        commonModuleIdEntity.setEmpidui(ContextHelper.getEmpNo());
        commonModuleIdEntity.setToken(StringUtils.hasText(ContextHelper.getToken())
                ? ContextHelper.getToken() : UcsClient.getToken(ContextHelper.getEmpNo()));
        return JsonUtils.parseArray(getBoWithServiceDataCheck(AuthorityClient.getAllRoleListByModuleId(commonModuleIdEntity)),
                UserRoleVO.class);
    }

    public static void addRoleDataAuthori(String roleId, String userId, Date endDate,
                                          Map<String, String> prodConstraintMap, Map<String, String> orgConstraintMap) {

        if (CollectionUtils.isEmpty(prodConstraintMap) && CollectionUtils.isEmpty(orgConstraintMap)) {
            return;
        }

        //首先构建ConstraintDataAuthori对象
        List<ConstrainDataAuthori> constrainDataAuthoris = new ArrayList<>();
        //多个同类型数据约束,约束值只能存储在一个ConstrainDataAuthori对象中
        if (!CollectionUtils.isEmpty(prodConstraintMap)) {
            ConstrainDataAuthori prodDataAuthori = new ConstrainDataAuthori();
            prodDataAuthori.setConstraintId(PROD_CONSTRAINT_ID);
            prodDataAuthori.setConstraintModel(PRECISE_CONTAIN);
            prodDataAuthori.setConstraintValue(String.join(COMMA, prodConstraintMap.keySet()));
            prodDataAuthori.setShowName(String.join(COMMA, prodConstraintMap.values()));
            constrainDataAuthoris.add(prodDataAuthori);
        }
        if (!CollectionUtils.isEmpty(orgConstraintMap)) {
            ConstrainDataAuthori orgDataAuthori = new ConstrainDataAuthori();
            orgDataAuthori.setConstraintId(ORG_CONSTRAINT_ID);
            orgDataAuthori.setConstraintModel(PRECISE_CONTAIN);
            orgDataAuthori.setConstraintValue(String.join(COMMA, orgConstraintMap.keySet()));
            orgDataAuthori.setShowName(String.join(COMMA, orgConstraintMap.values()));
            constrainDataAuthoris.add(orgDataAuthori);
        }

        //构建UserAuthoriDetailEntity对象,整合约束和成员信息(可能为多个成员)
        List<UserAuthoriDetailEntity> userAuthoriDetailEntities = new ArrayList<>();
        UserAuthoriDetailEntity userAuthoriDetailEntity = new UserAuthoriDetailEntity();
        userAuthoriDetailEntity.setUserId(userId);
        userAuthoriDetailEntity.setEndDate(DateUtils.dateToString(endDate, CommonConstants.DATE_FORMAT));
        userAuthoriDetailEntity.setConstrainVos(constrainDataAuthoris);
        userAuthoriDetailEntities.add(userAuthoriDetailEntity);

        //构建PersonRoleAuthoriDetailEntity对象,整合角色信息
        List<PersonRoleAuthoriDetailEntity> personRoleAuthoriDetailEntities = new ArrayList<>();
        PersonRoleAuthoriDetailEntity personRoleAuthoriDetailEntity = new PersonRoleAuthoriDetailEntity();
        personRoleAuthoriDetailEntity.setRoleId(roleId);
        personRoleAuthoriDetailEntity.setUserAuthoriDetailEntitys(userAuthoriDetailEntities);
        personRoleAuthoriDetailEntities.add(personRoleAuthoriDetailEntity);

        //构建PersonRoleDataAuthoriAddEntity对象,整合操作人信息
        PersonRoleDataAuthoriAddEntity entity = new PersonRoleDataAuthoriAddEntity();
        entity.setEmpidui(ContextHelper.getEmpNo());
        entity.setToken(StringUtils.hasText(ContextHelper.getToken())
                ? ContextHelper.getToken() : UcsClient.getToken(ContextHelper.getEmpNo()));
        entity.setModuleId(UPP_MODULE_ID);
        entity.setPersonRoleAuthoriDetailEntitys(personRoleAuthoriDetailEntities);
        log.info("addRoleDataAuthori userId :{}, addRoleDataAuthori param :{}", userId, JsonUtils.toJsonString(entity));

        getBoWithServiceDataCheck(AuthorityClient.addBatchPersonRoleDataAuthori(entity));
    }

    /* 根据条件查询人员数据 */
    public static List<UppUserAuthInfo> getPermissionFromGroupAuth(UserPermissionDto queryDto) {
        if (TYPE_GROUP.equals(queryDto.getQueryType())) {
            if (!StringUtils.hasText(queryDto.getRoleCode())) {
                return new ArrayList<>();
            }
            if (StringUtils.hasText(queryDto.getOrganization()) || StringUtils.hasText(queryDto.getProduct())) {
                return queryUserByConstraint(queryDto);
            } else {
                return queryUsersByRoleNum(queryDto.getRoleCode());
            }
        }
        return queryUppUserAuthInfosByUser(queryDto);
    }

    /*
     * 根据角色+数据id查询人员角色信息
     * */
    private static List<UppUserAuthInfo> queryUserByConstraint(UserPermissionDto queryDto) {
        UppRequest<RoleDataConstraintEntity> request = new UppRequest<>();
        RolePlusEntity rolePlusEntity = getRoleByRolecode(queryDto.getRoleCode());
        List<UppUserAuthInfo> result = new ArrayList<>();
        if (rolePlusEntity == null) {
            return result;
        }

        String langId = ContextHelper.getLangId();
        RoleDataConstraintEntity body = new RoleDataConstraintEntity();
        body.setTenantId(UPP_TENANT_ID);
        body.setEmpidui(ContextHelper.getEmpNo());
        body.setToken(ContextHelper.getToken());
        // 该接口模块id从配置文件读取,属于必填项
        // 角色id
        body.setRoleId(rolePlusEntity.getId());
        // 约束条件

        List<ConstraintQueryVO> values = new ArrayList<>();
        // 组织约束
        if (StringUtils.hasText(queryDto.getOrganization())) {
            ConstraintQueryVO value = new ConstraintQueryVO();
            value.setConstraintId(ORG_CONSTRAINT_ID);
            value.setValues(new String[]{queryDto.getOrganization()});
            values.add(value);
        }
        // 产品约束
        if (StringUtils.hasText(queryDto.getProduct())) {
            ConstraintQueryVO value = new ConstraintQueryVO();
            value.setConstraintId(PROD_CONSTRAINT_ID);
            value.setValues(new String[]{queryDto.getProduct()});
            values.add(value);
        }
        if (CollectionUtils.isEmpty(values)) {
            return result;
        }
        body.setValues(values);
        request.setBody(body);
        List<UserByRoleDataResDTO> rodeUserList = getBoWithServiceDataCheck(AuthorityClient.queryUserByConstraint(request));
        if (CollectionUtils.isEmpty(rodeUserList)) {
            return result;
        }

        rodeUserList.forEach(item -> {
            UppUserAuthInfo info = new UppUserAuthInfo();
            info.setModelName(ZH_CN.equals(langId) ? CommonConstants.ITECHCLOUD_NAME_CN : CommonConstants.ITECHCLOUD_NAME_EN);
            info.setRoleCode(queryDto.getRoleCode());
            info.setUserNo(item.getCode());
            info.setUserName(ZH_CN.equals(langId) ? item.getCnName() : item.getEnName());
            info.setOrganizationPermissions(queryDto.getOrganizationName());
            info.setProductPermissions(queryDto.getProductName());
            if (rolePlusEntity != null) {
                info.setRoleName(ZH_CN.equals(langId) ? rolePlusEntity.getRoleNameCN() : rolePlusEntity.getRoleNameEN());
            }
            result.add(info);
        });
        return result;
    }

    /*
     * 根据角色code查询人员角色信息
     * */
    public static List<UppUserAuthInfo> queryUsersByRoleNum(String roleCode) {
        CommonRoleEntity commonRoleEntity = new CommonRoleEntity();
        commonRoleEntity.setTenantId(UPP_TENANT_ID);
        commonRoleEntity.setEmpidui(ContextHelper.getEmpNo());
        commonRoleEntity.setToken(ContextHelper.getToken());
        List<String> roleCodeList = Arrays.asList(roleCode);
        // 语言ID(1033 英文2052 中文)
        String langId = ZH_CN.equals(ContextHelper.getLangId()) ? UPP_LANG_ZH : UPP_LANG_US;
        Map<String, List<UserVO>> nodeUserMap = getBoWithServiceDataCheck(AuthorityClient.queryUsersByRoleCode(commonRoleEntity, roleCodeList, langId));
        List<UppUserAuthInfo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(nodeUserMap) || !nodeUserMap.containsKey(roleCode)) {
            return result;
        }
        RolePlusEntity rolePlusEntity = getRoleByRolecode(roleCode);
        List<UserVO> userVOS = nodeUserMap.get(roleCode);
        userVOS.forEach(item -> {
            UppUserAuthInfo info = new UppUserAuthInfo();
            info.setModelName(ZH_CN.equals(ContextHelper.getLangId()) ? CommonConstants.ITECHCLOUD_NAME_CN : CommonConstants.ITECHCLOUD_NAME_EN);
            info.setRoleCode(roleCode);
            info.setUserNo(item.getEmpidui());
            info.setUserName(item.getName());
            if (rolePlusEntity != null) {
                info.setRoleName(ZH_CN.equals(ContextHelper.getLangId()) ? rolePlusEntity.getRoleNameCN() : rolePlusEntity.getRoleNameEN());
            }
            result.add(info);
        });
        return result;
    }

    /* 按用户查询数据 */
    private static List<UppUserAuthInfo> queryUppUserAuthInfosByUser(UserPermissionDto queryDto) {
        String langId = ContextHelper.getLangId();
        List<UppUserAuthInfo> infos = new ArrayList<>();
        //个人权限
        RoleUserListEntity userListEntity = new RoleUserListEntity();
        userListEntity.setPage(1);
        userListEntity.setPageSize(CommonConstants.INTEGER_2000);
        userListEntity.setUserList(Arrays.asList(queryDto.getAccountId()));
        userListEntity.setEmpidui(ContextHelper.getEmpNo());
        userListEntity.setToken(ContextHelper.getToken());
        if (StringUtils.hasText(queryDto.getRoleCode())) {
            userListEntity.setRoleCodeList(Arrays.asList(queryDto.getRoleCode()));
        }
        RoleUserListPagingEntity roleList = getBoWithServiceDataCheck(AuthorityClient.getRoleListByRoleAndUserList(userListEntity));
        if (CollectionUtils.isEmpty(roleList.getRecords())) {
            return infos;
        }

        List<com.zte.itp.authorityclient.entity.output.RoleUserListEntity> roleUserListEntities = roleList.getRecords();
        roleUserListEntities.forEach(
                roleUserEntity -> roleUserEntity.getMemberRoleList().forEach(
                        memberRole -> {
                            UppUserAuthInfo info = new UppUserAuthInfo();
                            info.setRoleName(ZH_CN.equals(langId) ? memberRole.getNameCn() : memberRole.getNameEn());
                            info.setEndDate(memberRole.getEndDate());
                            info.setRoleCode(memberRole.getRoleCode());
                            info.setRoleId(memberRole.getId());
                            info.setUserNo(roleUserEntity.getUserNo());
                            infos.add(info);
                        }));
        arrangeUppUserAuthInfo(infos);
        List<UppUserAuthInfo> result = infos;
        if (StringUtils.hasText(queryDto.getProduct())) {
            result = result.stream()
                    .filter(i -> !CollectionUtils.isEmpty(i.getProductIdpaths()))
                    .filter(i -> i.getProductIdpaths().contains(queryDto.getProduct()))
                    .collect(Collectors.toList());
        }
        if (StringUtils.hasText(queryDto.getOrganization())) {
            result = result.stream()
                    .filter(i -> !CollectionUtils.isEmpty(i.getOrganizationIdpaths()))
                    .filter(i -> i.getOrganizationIdpaths().contains(queryDto.getOrganization()))
                    .collect(Collectors.toList());
        }
        // 兼容英文环境转英文
        translationLangua(result);
        return result;
    }

    public static void translationLangua(List<UppUserAuthInfo> uppuserauthinfos) {
        if (CollectionUtils.isEmpty(uppuserauthinfos)) {
            return;
        }

        String langId = ContextHelper.getLangId();
        List<String> prodIds = uppuserauthinfos.stream()
                .map(UppUserAuthInfo::getProductIdpaths)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .map(s -> Optional.ofNullable(s).map(ss -> ss.replaceAll("/$", "")).orElse(""))
                .collect(Collectors.toList());
        List<BasicProductInfo> productInfos = NisClient.queryProductInfo(prodIds);
        Map<String, String> prodMap = productInfos.stream()
                .collect(Collectors.toMap(BasicProductInfo::getId,
                        (ZH_CN.equals(langId) ? BasicProductInfo::getNameZh : BasicProductInfo::getNameEn)));
        List<String> organizationIds = uppuserauthinfos.stream()
                .map(UppUserAuthInfo::getOrganizationIdpaths)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .map(s -> Optional.ofNullable(s).map(ss -> ss.substring(ss.lastIndexOf("/") + 1)).orElse(""))
                .collect(Collectors.toList());
        TreeServiceObjectQuery query = new TreeServiceObjectQuery();
        query.setCodeList(organizationIds);
        List<TreeServiceObjectVo> organizationClass = NisClient.queryOrganizationClass(query);
        Map<String, String> orgMap = organizationClass.stream()
                .collect(Collectors.toMap(TreeServiceObjectVo::getCode, TreeServiceObjectVo::getName));


        uppuserauthinfos.forEach(item -> {
            if (!CollectionUtils.isEmpty(item.getProductIdpaths())) {
                List<String> prodNames = item.getProductIdpaths().stream()
                        .map(v -> prodMap.get(v.replaceAll("/$", "")))
                        .collect(Collectors.toList());
                item.setProductPermissions(String.join(",", prodNames));
            }
            if (!CollectionUtils.isEmpty(item.getOrganizationIdpaths())) {
                List<String> orgNames = item.getOrganizationIdpaths().stream()
                        .map(v -> orgMap.get(v.substring(v.lastIndexOf("/") + 1)))
                        .collect(Collectors.toList());
                item.setOrganizationPermissions(String.join(",", orgNames));
            }
        });
    }

    /*
     * 组装角色对应数据权限
     * */
    private static void arrangeUppUserAuthInfo(List<UppUserAuthInfo> list) {
        String langId = ContextHelper.getLangId();
        List<String> userNos = list.stream().map(UppUserAuthInfo::getUserNo).distinct().collect(Collectors.toList());
        Map<String, String> userNoMap = HrClient.queryEmployeeName(userNos);
        list.forEach(item -> {
            item.setModelName(ZH_CN.equals(langId) ? CommonConstants.ITECHCLOUD_NAME_CN : CommonConstants.ITECHCLOUD_NAME_EN);
            item.setUserName(userNoMap.get(item.getUserNo()));
            Map<String, Map<String, List<String>>> map = getDataPermissionMap(item.getRoleCode(), item.getUserNo());
            Map<String, List<String>> nameMap = map.get(UPP_DATA_NAME_MAP_KEY);
            Map<String, List<String>> idPathMap = map.get(UPP_DATA_IDPATH_MAP_KEY);
            if (!CollectionUtils.isEmpty(nameMap)) {
                item.setOrganizationPermissions(showDataString(nameMap.get(UPP_DATA_ORGANIZATION_MAP_KEY)));
                item.setProductPermissions(showDataString(nameMap.get(UPP_DATA_PRODUCT_MAP_KEY)));
            }
            if (!CollectionUtils.isEmpty(idPathMap)) {
                item.setOrganizationIdpaths(tranList(idPathMap.get(UPP_DATA_ORGANIZATION_MAP_KEY)));
                item.setProductIdpaths(tranList(idPathMap.get(UPP_DATA_PRODUCT_MAP_KEY)));
            }
        });
    }

    private static Map<String, Map<String, List<String>>> getDataPermissionMap(String roleCod, String userNo) {
        Map<String, List<String>> nameMap = Maps.newHashMap();
        Map<String, List<String>> idMap = Maps.newHashMap();
        ConstraintAuthorizationAInput constraintAuthorizationAinput = new ConstraintAuthorizationAInput();
        constraintAuthorizationAinput.setLang(ContextHelper.getLangId());
        constraintAuthorizationAinput.setEmpidui(ContextHelper.getEmpNo());
        constraintAuthorizationAinput.setRoleCode(roleCod);
        constraintAuthorizationAinput.setUserId(userNo);
        constraintAuthorizationAinput.setToken(ContextHelper.getToken());
        constraintAuthorizationAinput.setTenantId(UPP_TENANT_ID);
        constraintAuthorizationAinput.setProductId(UPP_PRODUCT_ID);
        Map<String, JSONArray> map = getBoWithServiceDataCheck(AuthorityClient.
                getUserConstraintAuthorizationByRoleCodeAndUserId(constraintAuthorizationAinput));
        JSONArray objects = map.get(UPP_ROLE_CONSTRAINT_RESP_MAP_KEY);
        objects.forEach(i -> {
            JSONObject value = (JSONObject) i;
            String constraintCode = value.get("constraintCode").toString();
            String showNames = value.get("showName").toString();
            String constraintValues = value.get("constraintValue").toString();
            List<String> namses = nameMap.get(constraintCode);
            List<String> ids = idMap.get(constraintCode);
            if (CollectionUtils.isEmpty(namses)) {
                nameMap.put(constraintCode, new ArrayList<>(Collections.singleton(showNames)));
            } else {
                namses.add(showNames);
                nameMap.put(constraintCode, namses);
            }
            if (CollectionUtils.isEmpty(ids)) {
                idMap.put(constraintCode, new ArrayList<>(Collections.singleton(constraintValues)));
            } else {
                ids.add(constraintValues);
                idMap.put(constraintCode, ids);
            }
        });
        return MapUtils.newHashMap(UPP_DATA_NAME_MAP_KEY, nameMap,
                UPP_DATA_IDPATH_MAP_KEY, idMap);
    }

    /* 查询项目角色菜单tree*/
    public static Map<Long, List<ResourceDto>> queryResourceTreeMap() {
        List<RolePlusEntity> rolePlusEntity = getRolePlusEntity();
        if (CollectionUtils.isEmpty(rolePlusEntity)) {
            return null;
        }
        log.info("UppAbility queryResourceTreeMap rolePlusEntity size :{}", rolePlusEntity.size());
        return queryPermissionsByRoleIds(rolePlusEntity.stream().map(RolePlusEntity::getId).map(Object::toString).collect(Collectors.toList()));
    }

    /**
     * 查询所有角色
     */
    public static List<RolePlusEntity> getRolePlusEntity() {
        CommonModuleIdEntity entity = new RoleUserListEntity();
        entity.setModuleId(UPP_MODULE_ID);
        entity.setProductId(UPP_PRODUCT_ID);
        entity.setEmpidui(ContextHelper.getEmpNo());
        entity.setToken(ContextHelper.getToken());
        return getBoWithServiceDataCheck(UppClient.getRoleListByModuleId(entity));
    }

    /**
     * 根据角色code查询角色详情,
     */
    public static RolePlusEntity getRoleByRolecode(String rolecode) {
        List<RolePlusEntity> rolePlusEntitys = UppAbility.getRolePlusEntity();
        if (CollectionUtils.isEmpty(rolePlusEntitys)) {
            return null;
        }

        Map<String, RolePlusEntity> roleMap = rolePlusEntitys.stream()
                .collect(Collectors.toMap(RolePlusEntity::getRoleCode, Function.identity()));
        return roleMap.get(rolecode);
    }

    /*根据角色ID查询角色对应菜单资源权限 key：roleID,value ：tree*/
    public static Map<Long, List<ResourceDto>> queryPermissionsByRoleIds(List<String> roleIds) {
        log.info("UppAbility queryPermissionsByRoleIds roleIds :{}", roleIds);
        ResourceAuthEntity entity = new ResourceAuthEntity();
        entity.setAuthorizeObjectList(roleIds);
        entity.setEmpidui(ContextHelper.getEmpNo());
        entity.setToken(ContextHelper.getToken());
        Map<Long, List<ResourceDto>> result = Maps.newHashMap();
        Map<Long, List<ResourceEnDTO>> notMatchedRoleResourceMap = getBoWithServiceDataCheck(UppClient.queryResourceByRoleIds(entity));
        log.info("UppAbility queryPermissionsByRoleIds notMatchedRoleResourceMap :{}", notMatchedRoleResourceMap.keySet());
        for (Map.Entry<Long, List<ResourceEnDTO>> resourceEnDtos : notMatchedRoleResourceMap.entrySet()) {
            Map<String, List<ResourceEnDTO>> extResultMap = Maps.newHashMap();
            Set<Long> resourceIdSet = Sets.newHashSet();
            extResultMap.put(String.valueOf(resourceEnDtos.getKey()), resourceEnDtos.getValue());
            log.info("UppAbility queryPermissionsByRoleIds extResultMap :{}", JsonUtils.parseObject(extResultMap));
            List<ResourceEnDTO> resultList = resourceEnDtos.getValue().stream()
                    .filter(resourceDto -> resourceIdSet.add(resourceDto.getId()))
                    .sorted(Comparator.comparing(ResourceEnDTO::getResourceOrder))
                    .collect(Collectors.toList());
            List<ResourceDto> resourceVos = transDtos2VoTree(resultList);
            result.put(resourceEnDtos.getKey(), resourceVos);
        }
        log.info("UppAbility queryPermissionsByRoleIds result :{}", JsonUtils.parseObject(result));
        return result;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private static <T> T getBoWithServiceDataCheck(ServiceData serviceData) {

        if (Objects.isNull(serviceData) || Objects.isNull(serviceData.getCode())) {
            throw new LcapBusiException("invoke upp sdk error: return null");
        }
        RetCode code = serviceData.getCode();
        if (!Objects.equals(SUCCESS_CODE, code.getCode())) {
            throw new LcapBusiException("invoke upp sdk error:{0}", code.getMsg());
        }
        return (T) serviceData.getBo();
    }

    /* 组装菜单树*/
    private static List<ResourceDto> transDtos2VoTree(List<ResourceEnDTO> resources) {
        List<ResourceDto> menusList = Lists.newArrayList();
        wrapResourceWithTree(resources, resourceVo -> {
            if (Long.valueOf(0).equals(resourceVo.getParentId())) {
                menusList.add(resourceVo);
            }
        });
        return sortByResourceOrder(menusList);
    }

    /* 整理菜单树父级关系*/
    private static void wrapResourceWithTree(List<ResourceEnDTO> resources, Consumer<ResourceDto> consumer) {
        Map<Long, ResourceDto> userMenuItemMap = Maps.newHashMap();
        for (ResourceEnDTO resource : resources) {
            Long id = resource.getId();
            Long parentId = resource.getParentId();
            ResourceDto userMenuItem = tranResource(resource);
            ResourceDto existsUserMenuItem = userMenuItemMap.get(id);
            if (existsUserMenuItem == null) {
                userMenuItemMap.put(id, userMenuItem);
            } else if (org.apache.commons.lang3.StringUtils.isBlank(existsUserMenuItem.getResourceCode())) {
                userMenuItem.setChildren(existsUserMenuItem.getChildren());
                userMenuItemMap.put(id, userMenuItem);
            }
            ResourceDto parentUserMenuItem = userMenuItemMap.get(parentId);
            if (parentUserMenuItem == null) {
                parentUserMenuItem = new ResourceDto();
                parentUserMenuItem.setId(parentId);
                parentUserMenuItem.setChildren(Lists.newArrayList(userMenuItem));
                userMenuItemMap.put(parentId, parentUserMenuItem);
            } else {
                parentUserMenuItem.getChildren().add(userMenuItem);
                sortByResourceOrder(parentUserMenuItem.getChildren());
            }
            consumer.accept(userMenuItem);
        }
    }

    public static List<ResourceDto> sortByResourceOrder(List<ResourceDto> resourceVos) {
        resourceVos.sort(((Comparator<ResourceDto>) (o1, o2) -> {
            return o1.getResourceOrder() - o2.getResourceOrder();
        }).thenComparing(ResourceDto::getId));
        return resourceVos;
    }

    private static ResourceDto tranResource(ResourceEnDTO source) {
        ResourceDto target = new ResourceDto();
        if (source == null) {
            return target;
        }
        BeanUtils.copyProperties(source, target);
        target.setChildren(new ArrayList<>());
        target.setResourceType(ResourceTypeEnum.fromCode(source.getResourceType()));
        target.setName(ZH_CN.equals(ContextHelper.getLangId()) ? source.getNameCn() : source.getNameEn());
        return target;
    }

    private static String showDataString(List<String> strings) {
        List<String> list = tranList(strings);
        Set<String> setStr = new LinkedHashSet<>();
        if (list.contains(ALL_CN)) {
            setStr.add(ALL_CN);
        }
        if (list.contains(ALL_EN)) {
            setStr.add(ALL_EN);
        }
        setStr.addAll(list);
        return String.join(COMMA, setStr);
    }

    private static List<String> tranList(List<String> strings) {
        if (CollectionUtils.isEmpty(strings)) {
            return new ArrayList<>();
        }

        StringBuilder stringBuilder = new StringBuilder();
        strings.forEach(i -> stringBuilder.append(COMMA).append(i));
        List<String> distinctStr = Arrays.stream(stringBuilder.toString().split(COMMA))
                .distinct()
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
        return distinctStr;
    }
}
