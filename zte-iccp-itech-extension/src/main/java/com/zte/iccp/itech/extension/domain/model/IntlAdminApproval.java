package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.grantfile.IApprovalRecord;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.entity.IntlAdminApprovalFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/03
 */
@Getter
@Setter
@BaseSubEntity.Info(value = "intl_admin_approval", parent = ChangeOrder.class)
public class IntlAdminApproval extends BaseSubEntity implements IApprovalRecord {
    @JsonProperty(ROLE_ID)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveRoleEnum role;

    @JsonProperty(APPROVE_RESULT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveResultEnum result;

    @JsonProperty(APPROVE_OPINION)
    private String opinion;

    @JsonProperty(APPROVED_BY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee approver;

    @JsonProperty(APPROVED_TIME)
    private Date approvedDate;
}