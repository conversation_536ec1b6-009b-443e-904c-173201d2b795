package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.BatchSummaryAbility;
import com.zte.iccp.itech.extension.ability.ObjectLinkInstanceAbility;
import com.zte.iccp.itech.extension.ability.OperatorAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.changeorder.OperationObjectAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.PersonRelevance;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.domain.model.vo.BatchTaskOperatorVO;
import com.zte.iccp.itech.extension.domain.model.vo.ConfirmCopy;
import com.zte.iccp.itech.extension.domain.model.vo.OperatorCopy;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.common.api.RequestHeaderUtils;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.BATCH_TASK_HAS_BEEN_CREATED;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

@Slf4j
public class SaveBatchTaskPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String changeOrderId = body.getBusinessId();

        // 1.检索主任务
        NetworkChangeAssignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);
        if (Objects.isNull(mainAssignment)) {
            return true;
        }

        // 2.设置线程用户
        String empNo = CollectionUtils.isEmpty(mainAssignment.getResponsibleEmployee())
                ? mainAssignment.getCreateBy()
                : mainAssignment.getResponsibleEmployee().get(0).getEmpUIID();
        RequestContextHolder.setEmpNo(empNo);
        RequestHeaderUtils.setEmpNo(empNo);

        // 3.检索网络变更单
        ChangeOrder changeOrder = ChangeOrderAbility.get(changeOrderId, Lists.newArrayList());

        // 校验是否生成批次，如果已经生成（保障任务除外），防止重复生成
        List<BatchTask> batchTasks = BatchTaskAbility
                .batchGetByChangeOrderId(changeOrderId, BatchTask.class, Lists.newArrayList(ID));
        if (!CollectionUtils.isEmpty(batchTasks) && !changeOrder.isSecondaryGuarantyOrder()) {
            throw new LcapBusiException(MsgUtils.getMessage(BATCH_TASK_HAS_BEEN_CREATED));
        }

        // 4.检索变更单批次概要
        List<BatchSummary> batchSummaryList = BatchSummaryAbility.listBatchSummary(changeOrderId, BatchSummary.class);

        // 5.检索变更单操作对象
        Map<String, List<OperationObject>> batchOpeationObjectMap =
                OperationObjectAbility.getBatchOperationObject(changeOrderId, BatchSummary.class);

        // 属于自动生成保障网络变更单 不创建批次任务
        boolean guarantyOrderFlag = changeOrder.isSecondaryGuarantyOrder();
        if(!guarantyOrderFlag) {
            // 6.新增审批批次任务
            Map<String, String> approveAssignmentIdMap = createApproveBatchAssignment(changeOrder, batchSummaryList);

            // 7.新增列表批次任务
            List<String> batchIds = createListBatchAssignment(
                    mainAssignment, batchSummaryList, batchOpeationObjectMap, approveAssignmentIdMap, changeOrder);

            // 8.新增列表主任务 / 批次任务关联关系
            ObjectLinkInstanceAbility.createParentChildrenLinkInstance(
                    NetworkChangeAssignment.class, mainAssignment.getId(), batchIds);
        }

        // 9.主任务变更
        // 状态: 执行中, 当前进展: 批次审批中, 当前处理人: 主任务责任人, 是否审批任务: 否
        NetworkChangeAssignment newAssignment = new NetworkChangeAssignment();
        newAssignment.setId(mainAssignment.getId());
        newAssignment.setAssignmentStatus(AssignmentStatusEnum.EXECUTE.getValue());
        newAssignment.setCurrentProgress(ApproveNodeEnum.BATCH_APPROVALING.name());
        newAssignment.setCurrentProcessorEmployee(guarantyOrderFlag
                ? Lists.newArrayList()
                : EmployeeHelper.uniqueEmployees(mainAssignment.getResponsibleEmployee()));
        newAssignment.setApprovalTaskFlag(BoolEnum.N);

        AssignmentAbility.update(newAssignment);

        return false;
    }

    /**
     * 新增审批批次任务
     */
    private Map<String, String> createApproveBatchAssignment(ChangeOrder changeOrder,
                                                             List<BatchSummary> batchSummaryList) {
        if (CollectionUtils.isEmpty(batchSummaryList)) {
            return new HashMap<>();
        }

        // 1.新增主实体数据 - 批次任务
         Map<String, String> batchNoIdMap = createBatchTask(changeOrder, batchSummaryList);

        // 2.新增关联实体数据 - 操作人员
        OperatorAbility.createBatchTaskOperator(changeOrder.getId() ,false);

        // 关联保障单 结束需要生成 保障产品单的批次任务
        if(changeOrder.isPrimaryGuarantyOrder()) {
            List<String> changeOrderIds = ChangeOrderAbility.getGuaranteeIdsByChangeOrderId(changeOrder.getId());
            List<ChangeOrder> changeOrders = ChangeOrderAbility.get(changeOrderIds, ChangeOrder.class);
            changeOrders.forEach(item -> {
                createBatchTask(item, batchSummaryList);
            });
        }
        return batchNoIdMap;
    }

    /**
     * 新增主实体数据 - 批次任务
     * @return Map<String, String>
     */
    private Map<String, String> createBatchTask(ChangeOrder changeOrder, List<BatchSummary> batchSummaryList) {
        // 1.批次任务实体包装
        List<BatchTask> batchAssignmentList = Lists.newArrayList();
        for (BatchSummary batchSummary : batchSummaryList) {
            BatchTask batchTask = new BatchTask();

            // (1) 网络变更单相关属性
            // 网络变更单ID + 是否紧急操作 + 紧急操作原因 + 是否封网管控期操作 + 封网管控期操作原因
            batchTask.setChangeOrderId(changeOrder.getId());
            batchTask.setUrgentFlag(changeOrder.getIsEmergencyOperation());
            batchTask.setEmergencyOperationFlag(changeOrder.getEmergencyOperationFlag());
            batchTask.setUrgentResaon(changeOrder.getEmergencyOperationReason());
            batchTask.setControlPeriodFlag(changeOrder.getIsNetCloseOrControlOperation());
            batchTask.setNetworkSealingOperationFlag(changeOrder.getNetWorkSealingOperationFlag());
            batchTask.setControlPeriodReason(changeOrder.getNetCloseOrControlOperationReason());
            batchTask.setControlPeriodFile(changeOrder.getCloseOrControlOperationAttach());
            batchTask.setNotificationDesc(changeOrder.getOperationDesc());

            // 操作预计投入人天 + 是否需要操作步骤打卡 + 操作类型 + 操作类型分组 + 计划开始时间 + 计划结束时间
            batchTask.setPlanOperationDay(changeOrder.getEstimatedInvestmentTime());
            batchTask.setOperationType(changeOrder.getOperationType());
            batchTask.setOperationTypeGroup(changeOrder.getOperationTypeGroup());
            batchTask.setChangeOperationTimeStart(batchSummary.getPlanOperationStartTime());
            batchTask.setChangeOperationTimeEnd( batchSummary.getPlanOperationEndTime());

            // 行政领导审批 + 是否远程中心审批 + 是否上传网元清单
            batchTask.setIsChangeAdminApproval(changeOrder.getIsAdministrationLeaderApproval());
            batchTask.setIsChangeRemoteApproval(changeOrder.getIsRemoteCenterSupport());
            batchTask.setIsChangeUploadElement(Objects.isNull(changeOrder.getNeListFile()) ? BoolEnum.N : BoolEnum.Y);

            batchTask.setIsUrgenFile(changeOrder.getEmergencyOperationAttach());
            batchTask.setOperationPlanFile(changeOrder.getInternalOperationSolution());
            batchTask.setCustomerOperationSolution(changeOrder.getCustomerOperationSolution());

            // (2) 批次概要相关属性
            // 批次号 + 批次名称 + 操作账号 + 计划开始时间 + 操作描述 +审批状态
            String batchNo = batchSummary.getBatchNo().get(INTEGER_ZERO).getValue();
            String batchNoSuffix = String.format("%03d", Integer.valueOf(batchNo));
            batchTask.setBatchNo(batchNo);
            batchTask.setBatchName(changeOrder.getOperationSubject() + HYPHEN + batchNoSuffix);
            batchTask.setBatchCode(changeOrder.getOrderNo() + HYPHEN + batchNoSuffix);
            batchTask.setBatchOperationAccount(batchSummary.getOperationAccount());
            batchTask.setPlanOperationStartTime(batchSummary.getPlanOperationStartTime());
            batchTask.setPlanOperationEndTime(batchSummary.getPlanOperationEndTime());
            batchTask.setOperationDescription(batchSummary.getOperationDescription());
            batchTask.setApprovalStatus(BoolEnum.Y);
            batchTask.setCurrentStatus(AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());
            batchTask.setStep(INTEGER_TWO);//批次步骤条默认2
            batchTask.setIsNeedRemoteApproval(BoolEnum.N);
            batchTask.setPersonChangeFlag(BoolEnum.N);
            batchTask.setTimeConflict(batchSummary.getTimeConflict());
            batchTask.setApprovalNum(0L);
            batchTask.setOrganizationId(changeOrder.getResponsibleDept());
            batchTask.setSource(changeOrder.getSource());
            if (!changeOrder.isSecondaryGuarantyOrder()) {
                // 构造副本数据
                String history = assembleConfirmCopy(changeOrder, batchNo, batchSummary.getPlanOperationStartTime(),
                        batchSummary.getPlanOperationEndTime());
                batchTask.setConfirmCopy(history);
                batchTask.setNoticeCopy(history);
            }
            batchAssignmentList.add(batchTask);
        }

        // 2.新增批次任务数据
        List<String> batchAssignmentIdList = BatchTaskAbility.createFlowAndSave(changeOrder, batchAssignmentList);

        // 3.重新反查批次任务信息，构建 批次号 + ID 关系
        List<BatchTask> existBatchTaskList = BatchTaskAbility.batchGet(batchAssignmentIdList, Lists.newArrayList(), BatchTask.class);
        return existBatchTaskList.stream().collect(Collectors.toMap(BatchTask::getBatchNo, BatchTask::getId));
    }

    private String assembleConfirmCopy(ChangeOrder changeOrder,String batch ,Date startTime, Date endTime) {
        ConfirmCopy confirmCopy = new ConfirmCopy();
        confirmCopy.setUrgentFlag(changeOrder.getIsEmergencyOperation());
        confirmCopy.setControlPeriodFlag(changeOrder.getIsNetCloseOrControlOperation());
        confirmCopy.setPlanOperationStartTime(startTime);
        confirmCopy.setPlanOperationEndTime(endTime);
        List<BatchTaskOperatorVO> operators = OperatorAbility.getOperator(changeOrder.getId());
        if (!CollectionUtils.isEmpty(operators)) {
            List<OperatorCopy> operatorCopies = new ArrayList<>();
            operators.forEach(item -> {
                if (OperatorRoleEnum.SUPPORT_PERSONNEL == item.getOperatorRole()
                        || (!CollectionUtils.isEmpty(item.getOperatorBatchNo())
                        && TextValuePairHelper.getValueList(item.getOperatorBatchNo()).contains(batch))) {
                    OperatorCopy op = new OperatorCopy();
                    op.setOperatorRole(item.getOperatorRole());
                    op.setOperatePerson(item.getOperatePersonEmpNo());
                    operatorCopies.add(op);
                }
            });
            confirmCopy.setOperators(operatorCopies);
        }
        return JSON.toJSONString(confirmCopy);
    }

    /**
     * 新增列表批次任务
     */
    public List<String> createListBatchAssignment(
            NetworkChangeAssignment mainAssignment,
            List<BatchSummary> batchSummaries,
            Map<String, List<OperationObject>> batchOperandMap,
            Map<String, String> batchNoIdMap, ChangeOrder changeOrder) {

        if (CollectionUtils.isEmpty(batchSummaries)) {
            return Lists.newArrayList();
        }

        // 1.数据准备 - 当前处理人
        String userId = ContextHelper.getEmpNo();
        List<Employee> currentProcessorEmployee = HrClient.queryEmployeeInfo(Lists.newArrayList(userId));

        // 2.列表批次任务包装
        List<NetworkChangeAssignment> batchAssignments = Lists.newArrayList();
        for (BatchSummary batchSummary : batchSummaries) {
            NetworkChangeAssignment batchAssignment = new NetworkChangeAssignment();

            // (1) 默认数据
            // 任务类型 + 单据类型 + 任务状态
            batchAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getPropValue());
            batchAssignment.setBillType(BillTypeEnum.NETWORK_CHANGE.getPropValue());
            batchAssignment.setAssignmentStatus(AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());

            // (2) 主任务数据
            // 单据ID + 营销 + 代表处 + 客户 + 操作类型 + 责任人 + 产品分类 + 操作等级 + 管理团队 + 公司 + 时区
            batchAssignment.setBillId(mainAssignment.getBillId());
            batchAssignment.setMarketing(mainAssignment.getMarketing());
            batchAssignment.setRepresentativeOffice(mainAssignment.getRepresentativeOffice());
            batchAssignment.setCustomerClassification(mainAssignment.getCustomerClassification());
            batchAssignment.setOperationType(mainAssignment.getOperationType());
            batchAssignment.setResponsibleEmployee(mainAssignment.getResponsibleEmployee());
            batchAssignment.setProductClassification(mainAssignment.getProductClassification());
            batchAssignment.setOperationLevel(mainAssignment.getOperationLevel());
            batchAssignment.setProductManagementTeam(mainAssignment.getProductManagementTeam());
            batchAssignment.setCompany(mainAssignment.getCompany());
            batchAssignment.setTimeZone(changeOrder.getTimeZone().getLookupCode());

            // (3) 批次信息
            // 任务名称 + 任务编码 + 审批批次任务实体 + 操作开始时间（UTC+8）
            String batchNo = batchSummary.getBatchNo().get(INTEGER_ZERO).getValue();
            String formatBatchNo = String.format("%03d", Integer.valueOf(batchNo));
            String batchTaskId = batchNoIdMap.get(batchNo);

            batchAssignment.setAssignmentName(mainAssignment.getAssignmentName() + HYPHEN + formatBatchNo);
            batchAssignment.setAssignmentCode(mainAssignment.getAssignmentCode() + HYPHEN + formatBatchNo);
            batchAssignment.setEntityId(batchTaskId);
            batchAssignment.setApproveBatchTaskId(batchTaskId);
            batchAssignment.setPlanStartTime(batchSummary.getPlanOperationStartTime());
            // 非北京时间需要转成北京时间再存储
            TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneEnum(batchAssignment.getTimeZone());
            if (timeZoneEnum != null) {
                batchAssignment.setOperationStartTimeUtc8(TimeZoneEnum.BEIJING == timeZoneEnum
                        ? batchSummary.getPlanOperationStartTime()
                        : TimeZoneEnum.BEIJING.pollute(timeZoneEnum.fix(batchSummary.getPlanOperationStartTime())));
            }
            // (4) 操作对象 - 网络
            List<OperationObject> operationObjectList = batchOperandMap.getOrDefault(batchNo, Lists.newArrayList());
            List<TextValuePair> networkList = Lists.newArrayList();
            for (OperationObject operationObject : operationObjectList) {
                networkList.addAll(
                        TextValuePairHelper.buildList(operationObject.getNetworkId(), EMPTY_STRING, EMPTY_STRING));
            }
            batchAssignment.setNetwork(networkList);

            // (5) 批次当前进展 + 处理人 + 是否审批任务
            batchAssignment.setCurrentProgress(ApproveNodeEnum.PENDING_NOTIFICATION.name());
            batchAssignment.setCurrentProcessorEmployee(currentProcessorEmployee);
            batchAssignment.setApprovalTaskFlag(BoolEnum.N);

            batchAssignments.add(batchAssignment);
        }

        // 2.新增列表批次任务
        List<String> assignmentIds = AssignmentAbility.batchInsert(batchAssignments);

        // 3.1 新增任务 - 变更单主任务人员关联关系
        List<PersonRelevance> relevance = assembleRelevance(mainAssignment.getId(), userId, batchAssignments);
        // 3.2 新增任务 - 批次任务人员关联关系
        for (String assignmentId : assignmentIds) {
            PersonRelevance assignmentRelevance = new PersonRelevance();
            assignmentRelevance.setAssignmentId(assignmentId);
            assignmentRelevance.setRelevant(userId);
            assignmentRelevance.setSelfHandlerFlag(BoolEnum.N.name());
            assignmentRelevance.setApprovalTaskFlag(BoolEnum.N.name());
            relevance.add(assignmentRelevance);
        }

        AssignmentAbility.createAssignmentPersonRelevance(relevance);

        return assignmentIds;
    }

    private List<PersonRelevance> assembleRelevance(
            String netChangeAssignmentId,
            String userId,
            List<NetworkChangeAssignment> batchAssignments) {
        Set<String> relevants = new HashSet<>();
        relevants.add(userId);
        for (NetworkChangeAssignment batchTask : batchAssignments) {
            // 查询批次任务的操作人员列表
            List<String> operators = OperatorAbility.getBatchOperatorPersonsList(batchTask.getEntityId(), BatchTaskOperator.class);
            relevants.addAll(operators);
        }
        // 关联变更任务id与相关人员
        List<PersonRelevance> relevanceInfo = Lists.newArrayList();
        for (String relevant : relevants) {
            PersonRelevance assignmentRelevance = new PersonRelevance();
            assignmentRelevance.setAssignmentId(netChangeAssignmentId);
            assignmentRelevance.setRelevant(relevant);
            assignmentRelevance.setSelfHandlerFlag(BoolEnum.N.name());
            assignmentRelevance.setApprovalTaskFlag(BoolEnum.N.name());
            relevanceInfo.add(assignmentRelevance);
        }
        return relevanceInfo;
    }
}
