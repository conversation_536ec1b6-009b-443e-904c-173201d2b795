package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum BoolEnum implements SingletonTextValuePairsProvider {
    /** 是 */
    Y("是", "Yes"),
    /** 否 */
    N("否", "No"),
    ;

    private final String zhCn;

    private final String enUs;

    public static BoolEnum nilValueOf(String name) {
        return name == null ? null : valueOf(name);
    }

    public static BoolEnum valueOf(boolean value) {
        return value ? Y : N;
    }

    @Override
    public String getValue() {
        return name();
    }

    /**
     * 获取到是或者否对应的中英文
     *
     * @param langId 语言
     * @return 中英文是否
     */
    public String getName(String langId) {
        if (CommonConstants.ZH_CN.equals(langId)) {
            return zhCn;
        }
        return enUs;
    }

    public static Boolean valueOf(BoolEnum value) {
        if (value == null) {
            return null;
        }
        return BoolEnum.Y == value;
    }

    public static BoolEnum convertStringToBoolean(String str) {
        return valueOf(str != null && str.equalsIgnoreCase(Y.name()));
    }

    public static BoolEnum convertNameCnToBoolean(String nameCn) {
        return valueOf(nameCn != null && nameCn.equalsIgnoreCase(Y.getZhCn()));
    }
}
