package com.zte.iccp.itech.extension.domain.constant;

/**
 *  邮件常量类
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/16
 */
public class EmailConsts {
    /**
     * 模板id
     */
    public static final String EMAIL_TEMPLATE_ID = "templateId";

    /**
     * 对应渠道的通用参数（发件人，收件人等)
     */
    public static final String EMAIL_INFO = "info";

    /**
     * 业务数据（模板配置参数）
     */
    public static final String EMAIL_DATA = "data";

    /**
     * 发送时间
     */
    public static final String EMAIL_SENDTIME = "sendTime";

    /**
     * 收件人为邮箱地址或工号 ，多个收件人，使用逗号（,）分隔。
     */
    public static final String EMAIL_MAILTO = "mailTo";

    /**
     * 自定义发件人，需要具体的邮箱/工号。不传则发件人使用应用信息中的公共邮箱地址；传入 则以该值作为发件人
     */
    public static final String EMAIL_MAILFROM = "mailFrom";

    /**
     * 抄送，使用逗号（,）分隔。
     */
    public static final String EMAIL_MAILCC = "mailCC";

    /**
     * 密送，使用逗号（,）分隔。
     */
    public static final String EMAIL_MAILBCC = "mailBCC";

    /**
     * 服务url
     */
    public static final String EMAIL_SERVICE_URL = "serviceUrl";

    /**
     * 中文服务url
     */
    public static final String EMAIL_SERVICE_ZH_URL = "serviceZhUrl";

    /**
     * 英文服务url
     */
    public static final String EMAIL_SERVICE_EN_URL = "serviceEnUrl";

    /**
     * 操作者
     */
    public static final String OPERATOR = "operator";

    /**
     * 邮件发送时间
     */
    public static final String SEND_TIME = "sendTime";

    /**
     * 邮件标题（中文）
     */
    public static final String EMAIL_MESSAGE_TITLE_ZH = "emailTitleZh";

    /**
     * 邮件标题（英文）
     */
    public static final String EMAIL_MESSAGE_TITLE_EN = "emailTitleEn";

    /**
     * iCenter标题（中文）
     */
    public static final String ICENTER_MESSAGE_TITLE_ZH = "iCenterTitleZh";

    /**
     * iCenter标题（英文）
     */
    public static final String ICENTER_MESSAGE_TITLE_EN = "iCenterTitleEn";

    /**
     * 消息正文（中文）
     */
    public static final String MESSAGE_INFO_ZH= "messageInfoZh";

    /**
     * 消息正文（英文）
     */
    public static final String MESSAGE_INFO_EN= "messageInfoEn";

    /**
     * 操作等级（中文）
     */
    public static final String MESSAGE_OPERATE_LEVEL_ZH = "operateLevelZh";

    /**
     * 操作等级（英文）
     */
    public static final String MESSAGE_OPERATE_LEVEL_EN = "operateLevelEn";

    /**
     * iCenter内容（中英文一致）
     */
    public static final String ICENTER_MESSAGE_INFO= "messageInfo";

    /**
     * iCenter超链接url（跳首页）
     */
    public static final String ICENTER_INFORM_URL= "informUrl";

    /**
     * 申请客户授权凭证邮件主题
     */
    public static final String APPLY_AUTHORIZATION_THEME = "theme";

    /**
     * 申请客户授权凭证邮件主题（中文）
     */
    public static final String APPLY_AUTHORIZATION_EMAIL_CONTENT_ZH = "email_content_zh";

    /**
     * 申请客户授权凭证邮件主题（英文）
     */
    public static final String APPLY_AUTHORIZATION_EMAIL_CONTENT_EN = "email_content_en";

    /**
     * 核心网默认授权文件发送邮件知会人_单据编号
     */
    public static final String AD_NO = "adNo";

    /**
     * 核心网默认授权文件发送邮件知会人_申请人
     */
    public static final String APPLICANT = "applicant";

    /**
     * 核心网默认授权文件发送邮件知会人_操作账号
     */
    public static final String OPERATION_ACCOUNT = "operationAccount";

    /**
     * 核心网默认授权文件发送邮件知会人_操作账号
     */
    public static final String APPLICANT_ZH = "operationAccount";

    // ========== 权限申请 模板配置参数 ===========
    /**
     * 申请人中文
     */
    public static final String PA_APPLICANT_ZH = "applicantZh";

    /**
     * 申请人英文
     */
    public static final String PA_APPLICANT_EN = "applicantEn";

    /**
     * 申请人部门中文
     */
    public static final String PA_APPLICANT_DEPARTMENT_ZH = "applicantDepartmentZh";

    /**
     * 申请人部门英文
     */
    public static final String PA_APPLICANT_DEPARTMENT_EN = "applicantDepartmentEn";

    /**
     * 角色中文
     */
    public static final String ROLE_NAME_ZH = "roleNameZh";

    /**
     * 角色英文
     */
    public static final String ROLE_NAME_EN = "roleNameEn";

    /**
     * 申请时间
     */
    public static final String APPLICATION_TIME = "applicationTime";

    /**
     * 超链接url
     */
    public static final String LINK_URL = "serviceUrl";

    /**
     * 故障管理_下一环节（中文）
     */
    public static final String NEXT_LEVEL_ZH = "nextLevelZh";

    /**
     * 故障管理_下一环节（英文）
     */
    public static final String NEXT_LEVEL_EN = "nextLevelEn";

    /**
     * 故障管理_上一环节（中文）
     */
    public static final String LAST_LEVEL_ZH = "lastLevelZh";

    /**
     * 故障管理_上一环节（英文）
     */
    public static final String LAST_LEVEL_EN = "lastLevelEn";


    /**
     * 故障管理_任务名称（中文）
     */
    public static final String TASK_NAME_ZH = "taskNameZh";

    /**
     * 故障管理_任务名称（英文）
     */
    public static final String TASK_NAME_EN = "taskNameEn";

    /**
     * 故障管理_审核人（中文）
     */
    public static final String APPROVER_ZH = "approverZh";

    /**
     * 故障管理_审核人（英文）
     */
    public static final String APPROVER_EN = "approverEn";

}
