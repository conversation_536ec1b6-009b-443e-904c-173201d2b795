package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024/08/19
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class HeaderUtils {
    public static String fromItpValue(String itpValue, String key) {
        if (StringUtils.isBlank(itpValue)) {
            return StringUtils.EMPTY;
        }

        Pattern pattern = Pattern.compile(String.format("(?:^|;)%s=([^;]*)(?:;|$)", key));
        Matcher matcher = pattern.matcher(itpValue);
        return matcher.find()
                ? matcher.group(1) : StringUtils.EMPTY;
    }
}
