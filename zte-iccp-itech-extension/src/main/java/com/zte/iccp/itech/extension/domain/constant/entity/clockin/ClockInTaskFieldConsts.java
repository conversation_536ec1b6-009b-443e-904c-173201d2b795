package com.zte.iccp.itech.extension.domain.constant.entity.clockin;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/09/06
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ClockInTaskFieldConsts {

    public static final String ENTITY_TYPE = "entity_type";

    public static final String CHANGE_ORDER_ID = "change_order_id";

    public static final String BATCH_TASK_ID = "batch_task_id";

    public static final String TASK_TYPE = "task_type";

    public static final String TASK_STATUS = "task_status";

    public static final String ABOLISHED = "abolished";

    public static final String BATCH_CODE = "batch_code";

    public static final String RESPONSIBLE_DEPT = "responsible_dept";

    public static final String PRODUCT_CLASSIFICATION = "product_classification";

    public static final String OPERATION_SUBJECT = "operation_subject";

    public static final String OPERATION_TYPE = "operation_type";

    public static final String OPERATION_LEVEL = "operation_level";

    public static final String TIME_ZONE = "time_zone";

    public static final String OPERATOR = "operator";

    public static final String OPERATOR_ROLES = "operator_roles";

    public static final String PLAN_CALL_UTC_TIME = "plan_call_utc_time";

    public static final String LAST_CHECK_IN_UTC_TIME_BATCH_TASK = "last_check_in_utc_time_batch_task";

    public static final String PLAN_PREPARE_START_TIME = "plan_prepare_start_time";

    public static final String PLAN_PREPARE_END_TIME = "plan_prepare_end_time";

    public static final String PLAN_EXECUTE_START_TIME = "plan_execute_start_time";

    public static final String PLAN_EXECUTE_END_TIME = "plan_execute_end_time";

    public static final String PLAN_TEST_START_TIME = "plan_test_start_time";

    public static final String PLAN_TEST_END_TIME = "plan_test_end_time";

    public static final String PLAN_ON_DUTY_START_TIME = "plan_on_duty_start_time";

    public static final String PLAN_ON_DUTY_END_TIME = "plan_on_duty_end_time";

    public static final String TOTAL_ON_DUTY_DURATION = "total_on_duty_duration";

    public static final String ON_DUTY_FREQUENCY = "on_duty_frequency";

    public static final String STAGE_ON_DUTY_END_TIME = "stage_on_duty_end_time";

    public static final String ALREADY_ON_DUTY_FREQUENCY = "already_on_duty_frequency";

    public static final String TRANSFER_FREQUENCY = "transfer_frequency";
}
