package com.zte.iccp.itech.extension.domain.model.clockin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.JsonTextDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.LookupValueDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInRecordFieldConsts.*;

/**
 * <AUTHOR>
 * @since 2024/09/06
 */
@Getter
@Setter
@BaseSubEntity.Info(value = "clock_in_record", parent = ClockInTask.class)
public class ClockInRecord extends BaseSubEntity {
    @JsonProperty(value = CLOCK_IN_TIME)
    private Date clockInTime;

    @JsonProperty(value = CLOCK_IN_OPTION)
    @JsonDeserialize(using = LookupValueDeserializer.class)
    private ClockInOptionEnum clockInOption;

    @JsonProperty(value = DESCRIPTION)
    private String description;

    @JsonProperty(value = PHOTOS)
    @JsonDeserialize(using = JsonTextDeserializer.class)
    private List<String> photos;

    @JsonProperty(value = REVOKED)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private BoolEnum revoked;

    @JsonProperty(value = LONGITUDE)
    private String longitude;

    @JsonProperty(value = LATITUDE)
    private String latitude;
}
