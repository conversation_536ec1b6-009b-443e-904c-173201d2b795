package com.zte.iccp.itech.extension.domain.model.export;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class BaseSheet<T> {

    /** 表格数据 */
    private List<T> elements;

    /** 表头 */
    private List<String> fields;

    /** 超链接 - key 列标   value - url */
    private Map<Integer, List<String>> hyperlinks = new HashMap<>();

    /** Sheet 页名称 */
    private String sheetName;

    /** 数据起始行偏移量 - 默认第 1 行 */
    private int startRows = 0;
}
