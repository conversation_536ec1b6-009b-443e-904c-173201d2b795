package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/03
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IntlAdminApprovalFieldConsts {
    public static final String ROLE_ID = "role_id_intl_admin_approval";

    public static final String APPROVE_RESULT = "approve_result_intl_admin_approval";

    public static final String APPROVE_OPINION = "approve_opinion_intl_admin_approval";

    public static final String APPROVED_BY = "approved_by_intl_admin_approval";

    public static final String APPROVED_TIME = "approved_time_intl_admin_approval";
}
