package com.zte.iccp.itech.extension.ability.reportpush;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.openapi.model.reportpush.NetChangeReportOrgVO;
import com.zte.iccp.itech.extension.spi.client.IcosClient;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.NET_SERVICE_SALES;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @date 2025/4/9 下午5:37
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class NetChangeSummaryPushAbility {

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    private static final String DOMESTIC_SALES = "domestic.sales";

    private static final String CLOCK_IN_QUERY_URL = "clockin.query.url";

    private static final String ICOS_NETCHANGE_REPORT_DOMESTIC_DAY_APIID = "icos.netchange.report.domestic.day.apiId";
    private static final String ICOS_NETCHANGE_REPORT_ORG_DAY_APIID = "icos.netchange.report.org.day.apiId";
    private static final String ICOS_NETCHANGE_REPORT_ORG_WEEK_APIID = "icos.netchange.report.org.week.apiId";
    private static final String TABLE_TITLE = "netchange.report.push.summary.table.title";

    public static void domesticDayPush() {
        List<NetChangeReportOrgVO> reportVos = IcosClient.getSummaryReportVos(ICOS_NETCHANGE_REPORT_DOMESTIC_DAY_APIID);

        if (CollectionUtils.isEmpty(reportVos)) {
            log.info("{} domesticDayPush total is 0", new Date());
            return;
        }

        String titleDate = DateUtils.dateToString(DateUtils.addDay(new Date(), 1), DATE_FORMAT);
        sendMessageAndMail(reportVos, titleDate, NET_SERVICE_SALES, MsgUtils.getLangMessage(ZH_CN, DOMESTIC_SALES));
    }

    public static void orgDayPush() {
        List<NetChangeReportOrgVO> reportVos = IcosClient.getSummaryReportVos(ICOS_NETCHANGE_REPORT_ORG_DAY_APIID);
        if (CollectionUtils.isEmpty(reportVos)) {
            log.info("{} orgDayPush total is 0", new Date());
            return;
        }

        String titleDate = DateUtils.dateToString(DateUtils.addDay(new Date(), 1), DATE_FORMAT);
        Map<String, List<NetChangeReportOrgVO>> orgIdEntityMap = reportVos.stream()
                .collect(Collectors.groupingBy(NetChangeReportOrgVO::getOrganizationIdPath));

        orgIdEntityMap.forEach((k, v) -> {
            String orgName = v.stream().map(NetChangeReportOrgVO::getOrganization4).filter(Objects::nonNull).findFirst().orElse(EMPTY_STRING);
            sendMessageAndMail(v, titleDate, k, orgName);
        });
    }

    public static void orgWeekPush() {
        List<NetChangeReportOrgVO> reportVos = IcosClient.getSummaryReportVos(ICOS_NETCHANGE_REPORT_ORG_WEEK_APIID);
        if (CollectionUtils.isEmpty(reportVos)) {
            log.info("{} orgWeekPush total is 0", new Date());
            return;
        }

        String titleDateStart = DateUtils.dateToString(new Date(), DATE_FORMAT);
        String titleDateEnd = DateUtils.dateToString(DateUtils.addDay(new Date(), INTEGER_SEVEN), DATE_FORMAT);
        String titleDate = String.format("%s~%s", titleDateStart, titleDateEnd);
        Map<String, List<NetChangeReportOrgVO>> orgIdEntityMap = reportVos.stream()
                .collect(Collectors.groupingBy(NetChangeReportOrgVO::getOrganizationIdPath));

        orgIdEntityMap.forEach((k, v) -> {
            String orgName = v.stream().map(NetChangeReportOrgVO::getOrganization4).filter(Objects::nonNull).findFirst().orElse(EMPTY_STRING);
            String title = ReportPushAbility.getTitle(v, titleDate, orgName);
            ReportMailContentBuilder builder = new ReportMailContentBuilder();
            String mailInfo = builder.buildSummaryMailContent(v);
            String tableTitle = ReportPushAbility.getTableTitle(MsgUtils.getLangMessage(ZH_CN, TABLE_TITLE, titleDate, orgName));
            List<String> empNos = ReportPushAbility.getEmpNos(k, null);
            ReportPushAbility.sendMail(title, tableTitle + mailInfo, ConfigHelper.get(CLOCK_IN_QUERY_URL), empNos);
        });
    }

    private static void sendMessageAndMail(List<NetChangeReportOrgVO> reportVos, String titleDate, String orgId, String orgName) {
        String title = ReportPushAbility.getTitle(reportVos, titleDate, orgName);
        String tableTitle = ReportPushAbility.getTableTitle(MsgUtils.getLangMessage(ZH_CN, TABLE_TITLE, titleDate, orgName));
        ReportMailContentBuilder builder = new ReportMailContentBuilder();
        String mailInfo = builder.buildSummaryMailContent(reportVos);
        List<String> empNos = ReportPushAbility.getEmpNos(orgId, null);
        ReportPushAbility.sendMessage(title, empNos);
        ReportPushAbility.sendMail(title, tableTitle + mailInfo, ConfigHelper.get(CLOCK_IN_QUERY_URL), empNos);
    }

}
