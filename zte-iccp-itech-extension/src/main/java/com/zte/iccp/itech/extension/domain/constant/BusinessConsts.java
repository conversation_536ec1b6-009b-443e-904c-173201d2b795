package com.zte.iccp.itech.extension.domain.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class BusinessConsts {

    /** 业务侧系统管理员 */
    public static final String BUSI_SYSTEM_ADMIN_KEY = "busiSysAdmin";

    /* 国内营销*/
    public static final String DOMESTIC_SALES = "ORG0000000/ORG0002700/";

    /* 国内营销（仅营销层级）*/
    public static final String DOMESTIC_MARKETING = "ORG0002700";

    /*工程服务经营部-工程服务国内部-工程服务国内部 */
    public static final String DOMESTIC_DOMESTIC_OFFICE = "ORG2224925";

    /*工程服务经营部-工程服务国内部-网络服务处 */
    public static final String DOMESTIC_NETWORK_OFFICE = "ORG2223820";

    /*工程服务经营部-工程服务国内部-服务经营处 */
    public static final String DOMESTIC_SERVICE_OFFICE = "ORG2227054";

    /*工程服务经营部-工程服务国内部-工程服务处 */
    public static final String DOMESTIC_ENGINEERING_OFFICE = "ORG2223819";

    /* 工程服务经营部*/
    public static final String ENGINEERING_SERVICE_OPERATION = "ORG0000000/ORG2223728/";

    /* 工程服务经营部 - 工程服务部内部（工程服务三部）*/
    public static final String ENGINEERING_SERVICE_THREE_PARTS_OPERATION = "ORG0000000/ORG2223728/ORG2223781/";

    /** 工程服务经营部 - 工程服务一部 */
    public static final String ENGINEERING_SERVICE_DEPARTMENT_ONE = "ORG0000000/ORG2223728/ORG2223786/";

    /** 工程服务经营部 - 工程服务二部 */
    public static final String ENGINEERING_SERVICE_DEPARTMENT_TWO = "ORG0000000/ORG2223728/ORG2223780/";

    /** 工程服务经营部 - 工程服务五部 */
    public static final String ENGINEERING_SERVICE_DEPARTMENT_FIVE = "ORG0000000/ORG2223728/ORG2223778/";

    /** 工程服务经营部 - MTO */
    public static final String ENGINEERING_SERVICE_DEPARTMENT_MTO = "ORG0000000/ORG2223728/ORG2223785/";

    /** 工程服务经营部 - 工程服务一部 */
    public static final String ENGINEERING_SERVICE_DEPARTMENT_ONE_ID = "ORG2223786";

    /** 工程服务经营部 - 工程服务二部 */
    public static final String ENGINEERING_SERVICE_DEPARTMENT_TWO_ID = "ORG2223780";

    /** 工程服务经营部 - 工程服务五部 */
    public static final String ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID = "ORG2223778";

    /** 工程服务经营部 - MTO */
    public static final String ENGINEERING_SERVICE_DEPARTMENT_MTO_ID = "ORG2223785";

    /** 技术交付部 idPath */
    public static final String TECHNOLOGY_DELIVERY_DEPT = "ORG0000000/ORG2223728/ORG2223784/ORG2224922";

    /** 工程服务处 idPath */
    public static final String ENGINEERING_SERVICE_SALES = "ORG0000000/ORG2223728/ORG2223781/ORG2223819";

    /**
     * NIS生产环境：工程服务经营部-工程服务国内部-网络服务处（idpath：ORG0000000/ORG2223728/ORG2223781/ORG2223820）
     * NIS测试环境：工程服务经营部-工程服务三部-网络服务处 （idpath：ORG0000000/ORG2223728/ORG2223781/ORG2223820）
     */
    public static final String NET_SERVICE_SALES = "ORG0000000/ORG2223728/ORG2223781/ORG2223820";

    /** 综合技术交付部 idPath */
    public static final String INTEGRATED_TECHNOLOGY_DEPARTMENT = "ORG0000000/ORG2223728/ORG2223784/ORG2223815";

    /** 工服代表处组织编码路径 */
    public static final String[] OFFICE_ORG_CODE_PATH = {
            DOMESTIC_SALES,
            ENGINEERING_SERVICE_OPERATION
    };

    /** 国内代表处组织编码路径 */
    public static final String[] INNER_OFFICE_ORG_CODE_PATH = {
            DOMESTIC_SALES,
            ENGINEERING_SERVICE_THREE_PARTS_OPERATION
    };
}