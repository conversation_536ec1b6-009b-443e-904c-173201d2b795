package com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.BatchSummaryAbility;
import com.zte.iccp.itech.extension.ability.PartnerChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.changeorder.*;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.IntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.Operator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.*;
import com.zte.iccp.itech.extension.domain.model.vo.SubIntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.vo.SubcontractorOperator;
import com.zte.iccp.itech.extension.handler.approver.admin.InnerAdminHandlerImpl;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.dto.ApprovalDetailDto;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo.*;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.changeorder.enums.NodeWithInlinePageEnum;
import com.zte.iccp.itech.extension.spi.client.CrmClient;
import com.zte.iccp.itech.extension.spi.client.HolClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.crm.SimpleCustomerInfo;
import com.zte.iccp.itech.extension.spi.model.hol.GetPersonGeneralInfoReq;
import com.zte.iccp.itech.extension.spi.model.hol.InfoBlock;
import com.zte.iccp.itech.extension.spi.model.hol.PersonGeneralInfo;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.extension.spi.model.query.NisNetworkQuery;
import com.zte.iss.approval.sdk.bean.ApprovalProcessDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.ddm.domain.flow.dto.FlowQueryDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import com.zte.paas.lcap.platform.domain.i18n.I18nUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum.*;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum.OPERATION_CANCEL_REVIEW;
import static com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum.*;
import static com.zte.iccp.itech.extension.spi.model.hol.InfoBlock.BlockEnum.*;
import static com.zte.iccp.itech.extension.spi.model.hol.InfoBlock.VerEnum.v1;
import static com.zte.iccp.itech.extension.spi.model.hol.InfoBlock.VerEnum.v2;

public class ApprovalDetailOpenApi extends AbstractOpenApi {

    private static final Integer SIX_STAR = 6;


    // 批次任务执行节点
    private static final String EXECUTION_NODE_TYPE = "EXECUTION";

    // 内部网络变更单和批次任务会签节点审批记录特殊处理
    private static final Map<String, Function<String, List<ApprovalProgressVO>>> SIGNING_NODE_EXTEND_CODE_MAP
            = new HashMap<String, Function<String, List<ApprovalProgressVO>>>() {{
                // 多模产品
                put(MULTIMODE_PRODUCT_OWNER.name(), ApprovalDetailOpenApi::getMultiModeProdApproveRecords);
                // 国际会签
                put(INTL_ADMIN_APPROVAL.name(), ApprovalDetailOpenApi::getIntelApproveRecords);
                // 研发一体化
                put(RD_INTEGRATION.name(), ApprovalDetailOpenApi::getRdIntegrationApproveRecords);
                // 网服一体化
                put(NET_INTEGRATION.name(), ApprovalDetailOpenApi::getNetIntegrationApproveRecords);
                // 批次国际会签
                put(BATCH_INTL_ADMIN_APPROVAL.name(), ApprovalDetailOpenApi::getBatchIntelApproveRecords);
    }};

    /**
     * 内部网络变更单详情OpenApi
     */
    public ServiceData<ReviewDetailVO> approvalDetail(@RequestBody ApprovalDetailDto approvalDetailDto)  {
        ReviewDetailVO reviewDetailVO = getApprovalDetail(approvalDetailDto);
        return new ServiceData<ReviewDetailVO>() {{
            setBo(reviewDetailVO);
        }};

    }

    /**
     * 分包商网络变更单详情OpenApi
     */
    public ServiceData<PartnerReviewDetailVO> partnerApprovalDetail(@RequestBody ApprovalDetailDto approvalDetailDto)  {
        PartnerReviewDetailVO partnerReviewDetailVO = getPartnerApprovalDetail(approvalDetailDto);
        return new ServiceData<PartnerReviewDetailVO>() {{
            setBo(partnerReviewDetailVO);
        }};
    }

    // 内部网络变更单
    private ReviewDetailVO getApprovalDetail(ApprovalDetailDto approvalDetailDto) {
        // 单据编号
        String coNo = approvalDetailDto.getCoNo();
        if (!coNo.startsWith(CHANGE_ORDER_PREFIX_CO)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }

        // 内部网络变更单和内部网络变更单批次任务兼容
        // 批次任务的单号（batch_code） = 对应的内部网络变更单单号（co_no）+ "-" + 批次编号（0001）
        boolean isBatchTask = false;

        // 主单据编号
        String co = EMPTY_STRING;
        BatchTask batchTask = null;
        NetworkChangeAssignment assignment =
                AssignmentAbility.querySpecificTypeAssignmentByCode(approvalDetailDto.getCoNo(), NetworkChangeAssignment.class);
        if (Objects.isNull(assignment)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ASSIGNMENT_NOT_EXISTS));
        }
        List<TextValuePair> assignmentTypes = assignment.getAssignmentType();
        if (CollectionUtils.isEmpty(assignmentTypes)) {
            return new ReviewDetailVO();
        }
        String assignmentType = assignmentTypes.get(INTEGER_ZERO).getValue();
        if (String.valueOf(INTEGER_TWO).equals(assignmentType)) {
            co = coNo.substring(INTEGER_ZERO, coNo.lastIndexOf(HYPHEN));
            // 查询批次任务信息
            batchTask = BatchTaskAbility.queryBatchTask(coNo, BatchTask.class);
            isBatchTask = true;
        } else {
            co = coNo;
        }

        // 查询内部网络变更单单据
        ChangeOrder changeOrder = ChangeOrderAbility.getByCoNo(co);
        if (null == changeOrder) {
            return new ReviewDetailVO();
        }

        // 内部网络变更单_主键id
        String id = changeOrder.getId();

        ReviewDetailVO reviewDetailVO = new ReviewDetailVO();

        // 批次任务取消操作审批固定字段
        reviewDetailVO.setCancelOperationApproval(MsgUtils.getMessage(OPERATION_CANCALLATION_REVIEW));

        // 兼容批次任务
        if (isBatchTask) {
            if (null == batchTask) {
                return new ReviewDetailVO();
            }
            // 批次任务
            String batchTaskId = batchTask.getId();
            // 网络变更操作计划变更（固定字段）
            reviewDetailVO.setOperationsApproval(I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), NETWORK_CHANGE_OPERATION_PLAN_CHANGE));
            // 是否紧急操作
            reviewDetailVO.setEmergencyOperation(batchTask.getEmergencyOperationFlag());
            // 是否“封网、管控期操作”
            reviewDetailVO.setNetCloseorControlOperation(batchTask.getNetworkSealingOperationFlag());
            // 计划操作时间
            reviewDetailVO.setPlanOperateDates(queryBatchTask(batchTask));
            // 批次任务取消原因
            reviewDetailVO.setReasonOfCancellation(batchTask.getOcOperationChangeDesc());

            // 内部网络变更单_批次任务，获取审批意见map集合（会签节点和没有审批意见的节点除外）
            Map<String, String> batchTaskReviewOpinionMap = getBatchTaskReviewOpinionMap(batchTask);

            // 内部网络变更单_批次任务，获取审批结果map集合（会签节点和没有审批结果的节点除外）
            Map<String, List<TextValuePair>> batchTaskReviewResultMap = getBatchTaskReviewResultMap(batchTask);

            // 内部网络变更单_批次任务_审批进展
            List<ApprovalProgressVO> approvalProgress = queryBatchTaskApprovalProgress(batchTaskId, batchTaskReviewOpinionMap, batchTaskReviewResultMap);
            reviewDetailVO.setApprovalProgress(approvalProgress);

            ApprovalProcessDTO approvalProcessDTO = getInstanceApprovalProcess(coNo);

            // 申请时间
            reviewDetailVO.setSubmitDate(approvalProcessDTO.getApplicationDate());

            // 申请人
            reviewDetailVO.setSubmitter(getSubmitter(approvalProcessDTO.getApplicant()));

            // 设置推荐层级
            // 获取激活节点的extendCode
            changeOrder.setIsEmergencyOperation(batchTask.getUrgentFlag());
            changeOrder.setIsNetCloseOrControlOperation(batchTask.getControlPeriodFlag());
            setRecommendationLevel(changeOrder, reviewDetailVO, approvalProgress, batchTask.getBatchNo());
        } else {
            // 内部网络变更单
            // 网络变更操作审批（固定字段）
            reviewDetailVO.setOperationsApproval(I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), APPROVAL_OF_NETWORK_CHANGE_OPERATIONS));
            // 是否紧急操作
            reviewDetailVO.setEmergencyOperation(changeOrder.getEmergencyOperationFlag());
            // 是否“封网、管控期操作”
            reviewDetailVO.setNetCloseorControlOperation(changeOrder.getNetWorkSealingOperationFlag());
            // 计划操作时间
            reviewDetailVO.setPlanOperateDates(queryBatchSummary(id, BatchSummary.class));
            // 操作说明（批次任务不展示）
            reviewDetailVO.setOperationDesc(changeOrder.getOperationDesc());
            // 申请时间
            reviewDetailVO.setSubmitDate(changeOrder.getCreateTime());

            // 申请人
            reviewDetailVO.setSubmitter(getSubmitter(changeOrder.getCreateBy()));

            // 获取评审意见map集合（不包含会签节点）
            Map<String, String> reviewOpinionMap =  getReviewOpinionMap(changeOrder);

            // 获取评审结论map集合（不包含会签节点）
            Map<String, List<TextValuePair>> reviewResultMap = getReviewResultMap(changeOrder);

            // 内部网络变更单_审批进展
            List<ApprovalProgressVO> approvalProgress = queryApprovalProgress(id, reviewOpinionMap, reviewResultMap);
            reviewDetailVO.setApprovalProgress(approvalProgress);

            // 设置推荐层级
            // 获取激活节点的extendCode
            setRecommendationLevel(changeOrder, reviewDetailVO, approvalProgress, null);
        }

        // 变更单类型
        reviewDetailVO.setChangeOrderType(getChangeDeptType(changeOrder));

        // 单据编号
        reviewDetailVO.setCoNo(approvalDetailDto.getCoNo());

        // 是否政企
        reviewDetailVO.setIsGovEnt(BoolEnum.valueOf(changeOrder.getIsGovEnt()));

        // 操作主题
        reviewDetailVO.setOperationSubject(changeOrder.getOperationSubject());

        // 产品分类
        reviewDetailVO.setProdClassification(queryProductInfo(changeOrder.getProductCategory()));

        // 操作类型
        reviewDetailVO.setOperationType(changeOrder.getOperationTypeName(ContextHelper.getLangId()));

        // 操作等级
        OperationLevelEnum operationLevelEnum = changeOrder.getOperationLevel();
        if (null != operationLevelEnum) {
            reviewDetailVO.setOperateLevel(operationLevelEnum.getName(ContextHelper.getLangId()));
        }

        // 客户标识
        reviewDetailVO.setCustomerIdentification(changeOrder.getCustomerTypeFlag());

        // 重要程度
        ImportanceEnum importanceEnum = changeOrder.getImportance();
        if (null != importanceEnum) {
            reviewDetailVO.setImportanceLevel(importanceEnum.getName(ContextHelper.getLangId()));
        }

        // 风险评估
        RiskEvaluationEnum riskEvaluationEnum = changeOrder.getRiskEvaluation();
        if (null != riskEvaluationEnum) {
            reviewDetailVO.setRiskAssessment(riskEvaluationEnum.getName(ContextHelper.getLangId()));
        }

        // 是否大区操作
        reviewDetailVO.setIsRegionalOperation(null == changeOrder.getIsRegionalOperation()
                ? EMPTY_STRING : changeOrder.getIsRegionalOperation().getName(ContextHelper.getLangId()));

        // 审批说明
        reviewDetailVO.setApprovalReminder(getApprovalReminder(changeOrder));

        // 紧急原因操作简述
        reviewDetailVO.setEmergencyOperationReason(changeOrder.getEmergencyOperationReason());

        // 封网，管控操作原因
        reviewDetailVO.setNetCloseorControlOperationReason(changeOrder.getNetCloseOrControlOperationReason());

        // 是否行政领导审核
        reviewDetailVO.setIsAdministrationLeaderApproval(null == changeOrder.getIsAdministrationLeaderApproval()
                ? EMPTY_STRING : changeOrder.getIsAdministrationLeaderApproval().getName(ContextHelper.getLangId()));

        // 代表处
        reviewDetailVO.setDepartment(queryDepartment(changeOrder.getResponsibleDept()));

        // 客户网络名称
        reviewDetailVO.setNetworkName(queryNetworkName(id));

        // 操作负责人
        reviewDetailVO.setOperationOwners(queryOperationOwner(id));

        // 附件
        reviewDetailVO.setAttachFiles(getAttachFiles(changeOrder));

        // 操作计划变更说明（批次任务）


        return reviewDetailVO;
    }

    private PartnerReviewDetailVO getPartnerApprovalDetail(ApprovalDetailDto approvalDetailDto) {
        // 获取单据编号
        String coNo = approvalDetailDto.getCoNo();

        if (!coNo.startsWith(PARTNER_CHANGE_ORDER_PREFIX_HZF)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }

        // 内部网络变更单和内部网络变更单批次任务兼容
        // 批次任务的单号（batch_code） = 对应的内部网络变更单单号（co_no）+ "-" + 批次编号（0001）
        boolean isBatchTask = false;

        // 主单据编号
        String co = EMPTY_STRING;
        SubcontractorBatchTask subcontractorBatchTask = null;

        NetworkChangeAssignment assignment =
                AssignmentAbility.querySpecificTypeAssignmentByCode(approvalDetailDto.getCoNo(), NetworkChangeAssignment.class);
        if (Objects.isNull(assignment)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ASSIGNMENT_NOT_EXISTS));
        }
        List<TextValuePair> assignmentTypes = assignment.getAssignmentType();
        if (CollectionUtils.isEmpty(assignmentTypes)) {
            return new PartnerReviewDetailVO();
        }
        String assignmentType = assignmentTypes.get(INTEGER_ZERO).getValue();

        if (String.valueOf(INTEGER_SEVEN).equals(assignmentType)) {
            co = coNo.substring(INTEGER_ZERO, coNo.lastIndexOf(HYPHEN));
            // 查询批次任务信息
            subcontractorBatchTask = BatchTaskAbility.queryBatchTask(coNo, SubcontractorBatchTask.class);
            isBatchTask = true;
        } else {
            co = coNo;
        }

        // 查询合作方网络变更单单据
        SubcontractorChangeOrder subcontractorChangeOrder = PartnerChangeOrderAbility.getByCoNo(co);
        if (null == subcontractorChangeOrder) {
            return new PartnerReviewDetailVO();
        }

        PartnerReviewDetailVO partnerReviewDetailVO = new PartnerReviewDetailVO();

        // 合作方网络变更单_主键id
        String id = subcontractorChangeOrder.getId();

        // 批次任务取消操作审批固定字段
        partnerReviewDetailVO.setCancelOperationApproval(MsgUtils.getMessage(OPERATION_CANCALLATION_REVIEW));

        // 兼容批次任务
        if (isBatchTask) {
            if (null == subcontractorChangeOrder) {
                return new PartnerReviewDetailVO();
            }
            // 合作方网络变更单_批次任务
            String batchTaskId = subcontractorBatchTask.getId();
            // 合作方网络变更单 - 批次任务
            // 合作方网络变更操作计划变更（固定字段）
            partnerReviewDetailVO.setPratnerOperationsApproval(I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), PARTNER_NETWORK_CHANGE_OPERATION_PLAN_CHANGE));
            // 是否紧急操作
            partnerReviewDetailVO.setIsEmergencyOperation(subcontractorBatchTask.getEmergencyOperationFlag());

            // 批次任务取消原因
            partnerReviewDetailVO.setReasonOfCancellation(subcontractorBatchTask.getOcOperationChangeDesc());

            // 计划操作时间
            partnerReviewDetailVO.setPlanOperateDate(queryPartnerBacthTask(subcontractorBatchTask));

            // 分包商网络变更单_批次任务，获取审批意见map集合
            Map<String, String> batchTaskPartnerReviewOpinionMap = getBatchTaskPartnerReviewOpinionMap(subcontractorBatchTask);
            // 分包商网络变更单_批次任务，获取审批结果map集合
            Map<String, List<TextValuePair>> batchTaskPartnerReviewResultMap = getBatchTaskPartnerReviewResultMap(subcontractorBatchTask);
            // 审批进展
            partnerReviewDetailVO.setApprovalProgress(queryBatchTaskApprovalProgress(batchTaskId,
                    batchTaskPartnerReviewOpinionMap, batchTaskPartnerReviewResultMap));

            ApprovalProcessDTO approvalProcessDTO = getInstanceApprovalProcess(coNo);

            // 紧急操作原因简述
            partnerReviewDetailVO.setEmergencyOperationReason(subcontractorBatchTask.getUrgentResaon());

            // 操作方案描述
            partnerReviewDetailVO.setOperationPlanDesc(subcontractorBatchTask.getOperationPlanDesc());

            // 附件
            partnerReviewDetailVO.setAttachFiles(getSubcontractorBatchTaskAttachFiles(subcontractorBatchTask));

            // 申请时间
            partnerReviewDetailVO.setSubmitDate(approvalProcessDTO.getApplicationDate());

            // 申请人
            partnerReviewDetailVO.setSubmitter(getSubmitter(approvalProcessDTO.getApplicant()));

            // 操作主题
            partnerReviewDetailVO.setOperationSubject(subcontractorBatchTask.getBatchName());

            // 单据编号
            partnerReviewDetailVO.setCoNo(subcontractorBatchTask.getBatchCode());

        } else {
            // 合作方网络变更单
            // 合作方网络变更操作审批（固定字段）
            partnerReviewDetailVO.setPratnerOperationsApproval(I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), APPROVAL_OF_PARTNER_NETWORK_CHANGE_OPERATIONS));

            // 是否紧急操作
            partnerReviewDetailVO.setIsEmergencyOperation(subcontractorChangeOrder.getEmergencyOperationFlag());

            // 操作说明（批次任务没有）
            partnerReviewDetailVO.setOperationDesc(subcontractorChangeOrder.getOperationDesc());

            // 获取评审意见map集合
            Map<String, String> partnerReviewOpinionMap = getPartnerReviewOpinionMap(subcontractorChangeOrder);

            Map<String, List<TextValuePair>> partnerReviewResultMap = getPartnerReviewResultMap(subcontractorChangeOrder);

            // 审批进展
            partnerReviewDetailVO.setApprovalProgress(queryApprovalProgress(id, partnerReviewOpinionMap, partnerReviewResultMap));

            // 计划操作时间
            partnerReviewDetailVO.setPlanOperateDate(queryBatchSummary(id, SubconBatchSummary.class));

            // 紧急操作原因简述
            partnerReviewDetailVO.setEmergencyOperationReason(subcontractorChangeOrder.getEmergencyOperationReason());

            // 操作方案描述
            partnerReviewDetailVO.setOperationPlanDesc(subcontractorChangeOrder.getOperationSolutionDesc());

            // 附件
            partnerReviewDetailVO.setAttachFiles(getSubcontractorAttachFiles(subcontractorChangeOrder));

            // 申请时间
            partnerReviewDetailVO.setSubmitDate(subcontractorChangeOrder.getCreateTime());

            // 申请人
            partnerReviewDetailVO.setSubmitter(getSubmitter(subcontractorChangeOrder.getCreateBy()));

            // 操作主题
            partnerReviewDetailVO.setOperationSubject(subcontractorChangeOrder.getOperationSubject());

            // 单据编号
            partnerReviewDetailVO.setCoNo(subcontractorChangeOrder.getOrderNo());

        }
        // 是否政企
        partnerReviewDetailVO.setIsGovEnt(BoolEnum.valueOf(subcontractorChangeOrder.getIsGovEnt()));

        // 客户名称
        partnerReviewDetailVO.setCustomerName(getCustomerName(subcontractorChangeOrder.getCustomerId()));

        // 客户联系人
        partnerReviewDetailVO.setCustomerContacts(subcontractorChangeOrder.getCustomerContractPerson());

        // 客户网络名称
        partnerReviewDetailVO.setNetworkName(queryPartnerNetworkName(subcontractorChangeOrder.getNetworkName()));

        // 网络负责人
        partnerReviewDetailVO.setNetOwners(subcontractorChangeOrder.getNetworkResponsiblePerson());

        // 操作负责人
        partnerReviewDetailVO.setOperationOwners(queryPartnerOperationOwner(id));

        return partnerReviewDetailVO;
    }

    // 内部网络变更单_批次任务，获取审批结果map集合（会签节点和没有审批结果的节点除外）
    private Map<String, List<TextValuePair>> getBatchTaskReviewResultMap(BatchTask batchTask) {
        if (null == batchTask) {
            return new HashMap<>();
        }
        return com.zte.iccp.itech.extension.common.utils.MapUtils.newHashMap(
                // 批次任务取消操作审核
                OPERATION_CANCEL_REVIEW.name(), batchTask.getOcApproveResult(),
                // 远程中心负责人审核
                REMOTE_CENTER_OWNER.name(), batchTask.getApproveResultTdNetDeptAppSolu(),
                // 操作计划审核
                CHANGED_BY_REP_PROD_CHIEF.name(), batchTask.getApproveResultRepProdChief(),
                // 办事处产品科长
                ADMIN_REP_PROD_CHIEF.name(), batchTask.getApproveResultAdminRepProdChief(),
                // 办事处副经理
                ADMIN_REP_DEPUTY_MNG.name(), batchTask.getApproveResultAdminRepDeputyManager(),
                // 网络处产品总监
                ADMIN_NET_PROD_DIR.name(), batchTask.getApproveResultAdminNetProdDirector(),
                // 办事处产品经理
                ADMIN_REP_DEPUTY_MNG.name(), batchTask.getApproveResultAdminRepDeputyManager(),
                // 网络处主管经理
                ADMIN_NET_DEPT_MNG.name(), batchTask.getApproveResultAdminNetDeptMng(),
                // 网服部四层
                ADMIN_NETSERVICE_LV4.name(), batchTask.getApproveResultAdminNetServiceLV4(),
                // 网服部三层
                ADMIN_NETSERVICE_LV3.name(), batchTask.getApproveResultAdminNetServiceLV3(),
                // 研发三层
                ADMIN_RD_DEPT_LV3.name(), batchTask.getApproveResultAdminRdDeptLV3(),
                // 工服三部部长
                ADMIN_ENG_SERVICE3.name(), batchTask.getApproveResultAdminEngService3Leader(),
                // 行政审核_电信服务总监
                ADMIN_DIR_TELE_SERVICE.name(), batchTask.getApproveResultAdminDirTeleSerDirector()
        );
    }

    // 内部网络变更单_批次任务，获取审批意见map集合（会签节点和没有审批意见的节点除外）
    private Map<String, String> getBatchTaskReviewOpinionMap(BatchTask batchTask) {
        if (null == batchTask) {
            return new HashMap<>();
        }
        return com.zte.iccp.itech.extension.common.utils.MapUtils.newHashMap(
                // 批次任务取消操作审核
                OPERATION_CANCEL_REVIEW.name(), batchTask.getOcReviewOpinion(),
                // 远程中心负责人审核
                REMOTE_CENTER_OWNER.name(), batchTask.getApproveOpinionTdNetDeptAppSolu(),
                // 操作计划审核
                CHANGED_BY_REP_PROD_CHIEF.name(), batchTask.getApproveOpinionRepProdChief(),
                // 办事处产品科长
                ADMIN_REP_PROD_CHIEF.name(), batchTask.getApproveOpinionAdminRepProdChief(),
                // 办事处副经理
                ADMIN_REP_DEPUTY_MNG.name(), batchTask.getApproveOpinionAdminRepDeputyManager(),
                // 网络处产品总监
                ADMIN_NET_PROD_DIR.name(), batchTask.getApproveOpinionAdminNetProdDirector(),
                // 办事处产品经理
                ADMIN_REP_DEPUTY_MNG.name(), batchTask.getApproveOpinionAdminRepDeputyManager(),
                // 网络处主管经理
                ADMIN_NET_DEPT_MNG.name(), batchTask.getApproveOpinionAdminNetDeptMng(),
                // 网服部四层
                ADMIN_NETSERVICE_LV4.name(), batchTask.getApproveOpinionAdminNetServiceLV4(),
                // 网服部三层
                ADMIN_NETSERVICE_LV3.name(), batchTask.getApproveOpinionAdminNetServiceLV3(),
                // 研发三层
                ADMIN_RD_DEPT_LV3.name(), batchTask.getApproveOpinionAdminRdDeptLV3(),
                // 工服三部部长
                ADMIN_ENG_SERVICE3.name(), batchTask.getApproveOpinionAdminEngService3Leader(),
                // 行政审核_电信服务总监
                ADMIN_DIR_TELE_SERVICE.name(), batchTask.getApproveOpinionAdminDirTeleSerDirector()
        );
    }
    // 内部网络变更单，获取审批结果map集合（会签节点和没有审批结果的节点除外）
    private Map<String, List<TextValuePair>> getReviewResultMap(ChangeOrder changeOrder) {
        if (null == changeOrder) {
            return new HashMap<>();
        }
        BoolEnum isRemoteCenterSupport = changeOrder.getIsRemoteCenterSupport();
        BoolEnum isBcnLinkageGuarantee = changeOrder.getIsBcnLinkageGuarantee();
        return com.zte.iccp.itech.extension.common.utils.MapUtils.newHashMap(
                // 大区TD审核
                REGIONAL_TD_CONFIRM.name(), null == isBcnLinkageGuarantee
                        ? Lists.newArrayList() : TextValuePairHelper.buildList(isBcnLinkageGuarantee.getValue(),
                        isBcnLinkageGuarantee.getZhCn(), isBcnLinkageGuarantee.getEnUs()),
                // 远程中心负责人审核
                REMOTE_CENTER_OWNER.name(), null == isRemoteCenterSupport
                        ? Lists.newArrayList() : TextValuePairHelper.buildList(isRemoteCenterSupport.getValue(),
                        isRemoteCenterSupport.getZhCn(), isRemoteCenterSupport.getEnUs()),
                // 集成团队审核
                INTEGRATION_TEAM_APP.name(), changeOrder.getApproveResultIntegrationTeam(),
                // 研发领导审核
                RD_LEADER_APP.name(), changeOrder.getApproveResultRdLeader(),
                // 代表处产品TD审核
                REP_PROD_TD_APPROVE.name(), changeOrder.getApproveResultRepProdTdApp(),
                // 研发经理审核
                RD_MANAGER.name(), changeOrder.getApproveResultRDManager(),
                // 测试部审核
                TEST_DEPT.name(), changeOrder.getApproveResultTestDept(),
                // SSP产品支持团队审核
                SSP_PROD_SUPPORT.name(), changeOrder.getApproveResultSSPProdSupport(),
                // 服务产品支持部审核
                SERVICE_PROD_SUPPORT.name(), changeOrder.getApproveResultServiceProdSupport(),
                // 技术交付部/网络处审核远程方案
                NET_DEPT_REMOTE_SCHEME.name(), changeOrder.getApproveResultNetDeptRemoteScheme(),
                // 技术交付部/网络处领导审核
                TD_NET_DEPT_LEADER.name(), changeOrder.getApproveResultTdNetDeptLeader(),
                // 技术交付部/网络处审核
                TD_NET_DEPT_APPROVE.name(), changeOrder.getApproveResultTdNetDeptApprove(),
                // 网络服务部审核
                NET_SERVICE_DEPT_APP.name(), changeOrder.getApproveResultNetServiceDeptApp(),
                // 行政审核_代表处产品科长
                ADMIN_REP_PROD_CHIEF.name(), changeOrder.getApproveResultAdminRepProdChief(),
                // 行政审核_网络处产品总监
                ADMIN_NET_PROD_DIR.name(), changeOrder.getApproveResultAdminNetProdDirector(),
                // 行政审核_办事处副经理
                ADMIN_REP_DEPUTY_MNG.name(), changeOrder.getApproveResultAdminRepDeputyManager(),
                // 行政审核_电信服务总监
                ADMIN_DIR_TELE_SERVICE.name(), changeOrder.getApproveResultAdminDirTeleSerDirector(),
                // 行政审核_网络处主管经理
                ADMIN_NET_DEPT_MNG.name(), changeOrder.getApproveResultAdminNetDeptMng(),
                // 行政审核_网服部四层
                ADMIN_NETSERVICE_LV4.name(), changeOrder.getApproveResultAdminNetServiceLV4(),
                // 行政审核_网服部三层
                ADMIN_NETSERVICE_LV3.name(), changeOrder.getApproveResultAdminNetServiceLV3(),
                // 行政审核_研发三层
                ADMIN_RD_DEPT_LV3.name(), changeOrder.getApproveResultAdminRdDeptLV3(),
                // 行政审核_工服三部部长
                ADMIN_ENG_SERVICE3.name(), changeOrder.getApproveResultAdminEngService3Leader(),
                // 网络处总工
                NETWORK_CHIEF_ENGINEER.name(),changeOrder.getApproveResultNetworkChiefEngineerConsts()
        );
    }


    // 内部网络变更单，获取审批意见map集合（会签节点和没有审批结果的节点除外）
    private Map<String, String> getReviewOpinionMap(ChangeOrder changeOrder) {
        if (null == changeOrder) {
            return new HashMap<>();
        }
        return com.zte.iccp.itech.extension.common.utils.MapUtils.newHashMap(
                // CCN大区TD确认
                REGIONAL_TD_CONFIRM.name(), changeOrder.getApproveOpinionRegionalTDConfirm(),
                // 代表处TD审批
                REP_PROD_TD_APPROVE.name(), changeOrder.getApproveOpinionRepProdTdApprove(),
                // 技术交付部/网络处审批
                TD_NET_DEPT_APPROVE.name(), changeOrder.getApproveOpinionTdNetDeptApprove(),
                // 网络服务部审批
                NET_SERVICE_DEPT_APP.name(), changeOrder.getApproveOpinionNetServiceDept(),
                // 技术交付部/网络处领导
                TD_NET_DEPT_LEADER.name(), changeOrder.getApproveOpinionTdNetDeptLeader(),
                // 远程中心负责人确认
                REMOTE_CENTER_OWNER.name(), changeOrder.getApproveOpinionRemoteCenterOwner(),
                // 技术交付部网络处远程方案
                NET_DEPT_REMOTE_SCHEME.name(), changeOrder.getApproveOpinionNetDeptRemoteScheme(),
                // 服务产品支持部
                SERVICE_PROD_SUPPORT.name(), changeOrder.getApproveOpinionServiceProdSupport(),
                // SSP产品支持团队
                SSP_PROD_SUPPORT.name(), changeOrder.getApproveOpinionSSPProdSupport(),
                // 测试部
                TEST_DEPT.name(), changeOrder.getApproveOpinionTestDept(),
                // 研发经理
                RD_MANAGER.name(), changeOrder.getApproveOpinionRDManager(),
                // 集成团队
                INTEGRATION_TEAM_APP.name(), changeOrder.getApproveOpinionIntegrationTeamApp(),
                // 研发领导
                RD_LEADER_APP.name(), changeOrder.getApproveOpinionRdLeaderApp(),
                // 远程中心操作方案实施指派
                REMOTE_CENTER_OPER.name(), changeOrder.getApproveOpinionRemoteCenterOper(),
                // 行政审批_代表处产品科长
                ADMIN_REP_PROD_CHIEF.name(), changeOrder.getApproveOpinionAdminRepProdChief(),
                // 行政审批_网络处产品总监
                ADMIN_NET_PROD_DIR.name(), changeOrder.getApproveOpinionAdminNetProdDirector(),
                // 行政审批_办事处副经理
                ADMIN_REP_DEPUTY_MNG.name(), changeOrder.getApproveOpinionAdminRepDeputyManager(),
                // 行政审核_电信服务总监
                ADMIN_DIR_TELE_SERVICE.name(), changeOrder.getApproveOpinionAdminDirTeleSerDirector(),
                // 行政审批_网络处主管经理
                ADMIN_NET_DEPT_MNG.name(), changeOrder.getApproveOpinionAdminNetDeptMng(),
                // 行政审批_网服部四层
                ADMIN_NETSERVICE_LV4.name(), changeOrder.getApproveOpinionAdminNetServiceLV4(),
                // 行政审批_网服部三层
                ADMIN_NETSERVICE_LV3.name(), changeOrder.getApproveOpinionAdminNetServiceLV3(),
                // 行政审批_研发三层
                ADMIN_RD_DEPT_LV3.name(), changeOrder.getApproveOpinionAdminRdDeptLV3(),
                // 行政审批_工服三部部长
                ADMIN_ENG_SERVICE3.name(), changeOrder.getApproveOpinionAdminEngService3Leader(),
                // 网络处总工
                NETWORK_CHIEF_ENGINEER.name(),changeOrder.getApproveOpinionNetworkChiefEngine());
    }
    // 分包商网络变更单_批次任务，获取审批结果map集合
    private Map<String, List<TextValuePair>> getBatchTaskPartnerReviewResultMap(SubcontractorBatchTask subcontractorBatchTask) {
        return com.zte.iccp.itech.extension.common.utils.MapUtils.newHashMap(
                // 批次任务取消操作审核
                PartnerApproveNodeEnum.OPERATION_CANCEL_REVIEW.name(), subcontractorBatchTask.getOcApproveResult(),
                // 代表处产品科长
                PARTNER_OFFICE_PROD_CHIEF_APPROVAL.name(), subcontractorBatchTask.getApproveResultAdminRepProdChief(),
                // 办事处PD
                PARTNER_OFFICE_PD_APPROVAL.name(), subcontractorBatchTask.getApproveResultOfficePd(),
                // 网络负责人确认
                PARTNER_NET_OWNER_APPROVAL.name(), subcontractorBatchTask.getApprovalResult(),
                // 操作计划审核
                PARTNER_OPERATION_PLAN_APPROVAL.name(), subcontractorBatchTask.getApproveResultOperationPlan()
        );
    }

    // 分包商网络变更单_批次任务，获取审批意见map集合
    private Map<String, String> getBatchTaskPartnerReviewOpinionMap(SubcontractorBatchTask subcontractorBatchTask) {
        if (null == subcontractorBatchTask) {
            return new HashMap<>();
        }
        return com.zte.iccp.itech.extension.common.utils.MapUtils.newHashMap(
                // 批次任务取消操作审核
                PartnerApproveNodeEnum.OPERATION_CANCEL_REVIEW.name(), subcontractorBatchTask.getOcReviewOpinion(),
                // 代表处产品科长
                PARTNER_OFFICE_PROD_CHIEF_APPROVAL.name(), subcontractorBatchTask.getApproveOpinionAdminRepProdChief(),
                // 办事处PD
                PARTNER_OFFICE_PD_APPROVAL.name(), subcontractorBatchTask.getApproveOpinionOfficePd(),
                // 网络负责人确认
                PARTNER_NET_OWNER_APPROVAL.name(), subcontractorBatchTask.getApprovalDescription(),
                // 操作计划审核
                PARTNER_OPERATION_PLAN_APPROVAL.name(), subcontractorBatchTask.getApproveOpinionOperationPlan()
        );
    }

    // 分包商网络变更单，获取审批结果map集合
    private Map<String, List<TextValuePair>> getPartnerReviewResultMap(SubcontractorChangeOrder subcontractorChangeOrder) {
        if (null == subcontractorChangeOrder) {
            return new HashMap<>();
        }
        return com.zte.iccp.itech.extension.common.utils.MapUtils.newHashMap(
                // 代表处产品科长审批
                PARTNER_REPRESENTATIVE_CHIEF_APPROVAL.name(), subcontractorChangeOrder.getApproveResultRepProdChief(),
                // 网络责任人审批
                PARTNER_NET_OWNER_APPROVAL.name(), subcontractorChangeOrder.getApproveResultNetOwner(),
                // 办事处经理审批
                PARTNER_OFFICE_MANAGER_APPROVAL.name(), subcontractorChangeOrder.getApproveResultOfficeProdManager(),
                // 网络处审批
                PARTNER_NET_DEPT_APPROVAL.name(), subcontractorChangeOrder.getApproveResultNetDept(),
                // 办事处PD_紧急操作行政审批
                PARTNER_OFFICE_PD_APPROVAL.name(), subcontractorChangeOrder.getApproveResultOfficePd(),
                // 办事产品科长_紧急操作行政审批
                PARTNER_OFFICE_PROD_CHIEF_APPROVAL.name(), subcontractorChangeOrder.getApproveResultOfficeProdChief()
        );
    }

    // 分包商网络变更单，获取审批意见map集合
    private Map<String, String> getPartnerReviewOpinionMap(SubcontractorChangeOrder subcontractorChangeOrder) {
        if (null == subcontractorChangeOrder) {
            return new HashMap<>();
        }
        return com.zte.iccp.itech.extension.common.utils.MapUtils.newHashMap(
                // 代表处产品科长审批
                PARTNER_REPRESENTATIVE_CHIEF_APPROVAL.name(), subcontractorChangeOrder.getApproveOpinionRepProdChief(),
                // 网络责任人审批
                PARTNER_NET_OWNER_APPROVAL.name(), subcontractorChangeOrder.getApproveOpinionNetOwner(),
                // 办事处经理审批
                PARTNER_OFFICE_MANAGER_APPROVAL.name(), subcontractorChangeOrder.getApproveOpinionOfficeProdManager(),
                // 网络处审批
                PARTNER_NET_DEPT_APPROVAL.name(), subcontractorChangeOrder.getApproveOpinionNetDept(),
                // 办事处PD_紧急操作行政审批
                PARTNER_OFFICE_PD_APPROVAL.name(), subcontractorChangeOrder.getApproveOpinionOfficePd(),
                // 办事产品科长_紧急操作行政审批
                PARTNER_OFFICE_PROD_CHIEF_APPROVAL.name(), subcontractorChangeOrder.getApproveOpinionOfficeProdChief()
        );
    }

    private String getCustomerName(String customerId) {
        if (StringUtils.isEmpty(customerId)) {
            return EMPTY_STRING;
        }
        List<SimpleCustomerInfo> customerInfo = CrmClient.queryCustomerSimpleInfo(Lists.newArrayList(customerId));
        if (CollectionUtils.isEmpty(customerInfo)) {
            return EMPTY_STRING;
        }
        return ZH_CN.equals(ContextHelper.getLangId())
                ? customerInfo.get(INTEGER_ZERO).getCustomerName()
                : customerInfo.get(INTEGER_ZERO).getCustomerEnglishName();
    }

    // 获取产品分类（内部网络变更单）
    private String queryProductInfo(String productId) {
        if (StringUtils.isEmpty(productId)) {
            return EMPTY_STRING;
        }
        // produtId要去掉最后一个"/"
        if (productId.endsWith(FORWARD_SLASH)) {
            productId = productId.substring(INTEGER_ZERO, productId.lastIndexOf(FORWARD_SLASH));
        }
        List<BasicProductInfo> basicProductInfos = NisClient.queryProductInfo(Lists.newArrayList(productId));
        if (CollectionUtils.isEmpty(basicProductInfos)) {
            return EMPTY_STRING;
        }
        BasicProductInfo basicProductInfo = basicProductInfos.get(INTEGER_ZERO);
        return ZH_CN.equals(ContextHelper.getLangId()) ? basicProductInfo.getNamePathZh() : basicProductInfo.getNamePathEn();
    }

    // 代表处（内部网络变更单）
    private String queryDepartment(String responsibleDeptId) {
        if (StringUtils.isEmpty(responsibleDeptId)) {
            return EMPTY_STRING;
        }
        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();
        nisNetworkQuery.setResponsibleDept(Lists.newArrayList(responsibleDeptId));
        PageRows<NisNetwork> pageRows = NisClient.queryNisNetworkList(nisNetworkQuery);
        if (CollectionUtils.isEmpty(pageRows.getRows())) {
            return EMPTY_STRING;
        }
        List<NisNetwork> nisNetworks = pageRows.getRows();
        return nisNetworks.get(INTEGER_ZERO).getResponsibleDeptName();
    }

    // 查询批次任务（分包商网络变更单）
    private List<PlanOperateVO> queryPartnerBacthTask(SubcontractorBatchTask subcontractorBatchTask) {
        if (subcontractorBatchTask == null) {
            return Lists.newArrayList();
        }
        List<PlanOperateVO> planOperateVOS = Lists.newArrayList();
        PlanOperateVO planOperateVO = new PlanOperateVO();
        planOperateVO.setBatchNo(subcontractorBatchTask.getBatchNo());
        planOperateVO.setPlanStartDate(subcontractorBatchTask.getPlanOperationStartTime());
        planOperateVO.setPlanEndDate(subcontractorBatchTask.getPlanOperationEndTime());
        planOperateVOS.add(planOperateVO);
        return planOperateVOS;
    }
    // 查询批次任务（内部网络变更单）
    private List<PlanOperateVO> queryBatchTask(BatchTask batchTask) {
        if (batchTask == null) {
            return Lists.newArrayList();
        }
        List<PlanOperateVO> planOperateVOS = Lists.newArrayList();
        PlanOperateVO planOperateVO = new PlanOperateVO();
        planOperateVO.setBatchNo(batchTask.getBatchNo());
        planOperateVO.setPlanStartDate(batchTask.getPlanOperationStartTime());
        planOperateVO.setPlanEndDate(batchTask.getPlanOperationEndTime());
        planOperateVOS.add(planOperateVO);
        return planOperateVOS;
    }

    // 查询批次任务信息（内部网络变更单）
    private List<PlanOperateVO> queryBatchSummary(String changeOrderId, Class<? extends BaseSubEntity> subEntityEnum) {
        if (StringUtils.isEmpty(changeOrderId)) {
            return Lists.newArrayList();
        }
        List<BatchSummary> batchSummaries = BatchSummaryAbility.listBatchSummary(changeOrderId, subEntityEnum);
        List<PlanOperateVO> planOperateVOS = Lists.newArrayList();
        batchSummaries.stream().forEach(v -> {
            PlanOperateVO planOperateVO = new PlanOperateVO();
            List<TextValuePair> batchNos = v.getBatchNo();
            if (!CollectionUtils.isEmpty(batchNos)) {
                planOperateVO.setBatchNo(batchNos.get(CommonConstants.INTEGER_ZERO).getValue());
            }
            planOperateVO.setPlanStartDate(v.getPlanOperationStartTime());
            planOperateVO.setPlanEndDate(v.getPlanOperationEndTime());
            planOperateVOS.add(planOperateVO);
        });
        return planOperateVOS;
    }

    // 获取操作说明
    private String getApprovalReminder(ChangeOrder changeOrder) {
        // 是否政企=是 且 风险评估+重要程度>=6星 时显示这个提示语：6星级需要管理干部现场坐镇
        // 是否政企
        Boolean isGovEnt = BoolEnum.valueOf(changeOrder.getIsGovEnt());
        Integer riskAssessment = changeOrder.getRiskEvaluation().getIntValue();
        Integer importanceLevel = changeOrder.getImportance().getIntegerValue();
        if (isGovEnt && riskAssessment + importanceLevel >= SIX_STAR) {
            return I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), SIX_STAR_CHANGE_ORDER);
        }
        return EMPTY_STRING;
    }

    // 客户网络名称（合作方网络变更单）
    private String queryPartnerNetworkName(String networkId) {
        if (StringUtils.isEmpty(networkId)) {
            return EMPTY_STRING;
        }
        Map<String, String> map = NisClient.queryNetworkName(Lists.newArrayList(networkId));
        return map.get(networkId);

    }

    // 客户网络名称（内部网络变更单）
    private List<String> queryNetworkName(String changeOrderId) {
        if (StringUtils.isEmpty(changeOrderId)) {
            return Lists.newArrayList();
        }
        // 获取network_id
        List<OperationObject> operationObjects
                = OperationObjectAbility.listOperationObject(changeOrderId, OperationObject.class);
        List<String> networkIds = operationObjects.stream().map(OperationObject::getNetworkId).collect(Collectors.toList());
        Map<String, String> map = NisClient.queryNetworkName(networkIds);
        // 获取客户网络名称
        return MapUtils.isEmpty(map)
                ? Lists.newArrayList(EMPTY_STRING)
                : map.values().stream().collect(Collectors.toList());

    }

    // 操作负责人（内部网络变更单）
    private List<Employee> queryOperationOwner(String changeOrderId) {
        if (StringUtils.isEmpty(changeOrderId)) {
            return Lists.newArrayList();
        }
        // 操作负责人
        List<Operator> operationOwners =
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(changeOrderId, Operator.class);
        operationOwners = operationOwners.stream().filter(item -> OperatorRoleEnum.OPERATING_SUPERVISOR == item.getOperatorRole())
                .collect(Collectors.toList());
        return operationOwners.stream().map(Operator::getOperatorName).collect(Collectors.toList());
    }

    // 操作负责人（合作方网络变更单）
    private List<Employee> queryPartnerOperationOwner(String subChangeOrderId) {
        if (StringUtils.isEmpty(subChangeOrderId)) {
            return Lists.newArrayList();
        }
        // 操作负责人
        List<SubcontractorOperator> operationOwners =
                ChangeOrderAbility.listSpecificChangeOrderSubEntity(subChangeOrderId, SubcontractorOperator.class);
        operationOwners = operationOwners.stream().filter(item -> OperatorRoleEnum.OPERATING_SUPERVISOR == item.getOperatorRole())
                .collect(Collectors.toList());
        return operationOwners.stream().map(SubcontractorOperator::getOperatorName).collect(Collectors.toList());
    }

    // 申请人（内部/合作方网络变更单）
    public static PersonGeneralInfo getSubmitter(String createById) {
        // 查询申请人信息
        Map<String, PersonGeneralInfo> info = getPersonGeneralInfos(Lists.newArrayList(createById));
        if (!MapUtils.isEmpty(info)) {
            return info.get(createById);
        }
        return null;
    }


    // 合作方网络变更单 - 附件
    private List<AttachFilesVO> getSubcontractorBatchTaskAttachFiles(SubcontractorBatchTask subcontractorBatchTask) {
        List<AttachmentFile> attachmentFiles = Lists.newArrayList();

        // 内部操作方案
        AttachmentFile internalOperationSolution = subcontractorBatchTask.getOperationPlanFile();
        if (null != internalOperationSolution) {
            attachmentFiles.add(internalOperationSolution);
        }

        // 客户操作方案
        AttachmentFile customerOperationSolution = subcontractorBatchTask.getCustomerOperationSolution();
        if (null != customerOperationSolution) {
            attachmentFiles.add(customerOperationSolution);
        }

        // 其他操作附件
        Object otherOperationSolutionObj = subcontractorBatchTask.getAttachment();
        if (!ObjectUtils.isEmpty(otherOperationSolutionObj)) {
            List<AttachmentFile> otherOperationSolutions = JsonUtils.parseArray(otherOperationSolutionObj, AttachmentFile.class);
            attachmentFiles.addAll(otherOperationSolutions);
        }
        return getAttachFiles(attachmentFiles);

    }

    // 合作方网络变更单 - 附件
    private List<AttachFilesVO> getSubcontractorAttachFiles(SubcontractorChangeOrder subcontractorChangeOrder) {
        List<AttachmentFile> attachmentFiles = Lists.newArrayList();
        // 紧急操作原因附件
        Object emergencyOperationAttachmentObj = subcontractorChangeOrder.getEmergencyOperationAttachment();
        if (!ObjectUtils.isEmpty(emergencyOperationAttachmentObj)) {
            List<AttachmentFile> emergencyOperationAttachments
                    = JsonUtils.parseArray(emergencyOperationAttachmentObj, AttachmentFile.class);
            attachmentFiles.addAll(emergencyOperationAttachments);
        }

        // 内部操作方案
        AttachmentFile internalOperationSolution = subcontractorChangeOrder.getInternalOperationSolution();
        if (null != internalOperationSolution) {
            attachmentFiles.add(internalOperationSolution);
        }

        // 客户操作方案
        AttachmentFile customerOperationSolution = subcontractorChangeOrder.getCustomerOperationSolution();
        if (null != customerOperationSolution) {
            attachmentFiles.add(customerOperationSolution);
        }

        // 其他操作附件
        Object otherOperationSolutionObj = subcontractorChangeOrder.getOtherAttachment();
        if (!ObjectUtils.isEmpty(otherOperationSolutionObj)) {
            List<AttachmentFile> otherOperationSolutions = JsonUtils.parseArray(otherOperationSolutionObj, AttachmentFile.class);
            attachmentFiles.addAll(otherOperationSolutions);
        }

        // 有线产品操作检查单
        Object wireCheckListObj = subcontractorChangeOrder.getWireProductChecklist();
        if (!ObjectUtils.isEmpty(wireCheckListObj)) {
            List<AttachmentFile> wireCheckList = JsonUtils.parseArray(wireCheckListObj, AttachmentFile.class);
            attachmentFiles.addAll(wireCheckList);
        }
        return getAttachFiles(attachmentFiles);
    }

    /** 内部网络变更单 - 附件 */
    private List<AttachFilesVO> getAttachFiles(ChangeOrder changeOrder) {
        List<AttachmentFile> attachmentFiles = Lists.newArrayList();
        // 紧急操作原因附件
        Object emergencyOperationAttachObj = changeOrder.getEmergencyOperationAttach();
        if (!ObjectUtils.isEmpty(emergencyOperationAttachObj)) {
            List<AttachmentFile> emergencyOperationAttachs
                    = JsonUtils.parseArray(emergencyOperationAttachObj, AttachmentFile.class);
            attachmentFiles.addAll(emergencyOperationAttachs);
        }

        // 封网、管控期操作原因附件
        Object closeOrControlOperationAttachObj = changeOrder.getCloseOrControlOperationAttach();
        if (!ObjectUtils.isEmpty(closeOrControlOperationAttachObj)) {
            List<AttachmentFile> closeOrControlOperationAttachs
                    = JsonUtils.parseArray(closeOrControlOperationAttachObj, AttachmentFile.class);
            attachmentFiles.addAll(closeOrControlOperationAttachs);
        }

        // 有线产品操作检查单
        Object wireProductCheckObj = changeOrder.getWireProductChecks();
        if (!ObjectUtils.isEmpty(wireProductCheckObj)) {
            List<AttachmentFile> wireProductChecks
                    = JsonUtils.parseArray(wireProductCheckObj, AttachmentFile.class);
            attachmentFiles.addAll(wireProductChecks);
        }

        // license文件核对举证
        Object licenseFileObj = changeOrder.getLicenseFiles();
        if (!ObjectUtils.isEmpty(licenseFileObj)) {
            List<AttachmentFile> licenseFiles
                    = JsonUtils.parseArray(licenseFileObj, AttachmentFile.class);
            attachmentFiles.addAll(licenseFiles);
        }

        // 内部操作方案
        AttachmentFile internalOperationSolution = changeOrder.getInternalOperationSolution();
        if (null != internalOperationSolution) {
            attachmentFiles.add(internalOperationSolution);
        }

        // 客户操作方案
        AttachmentFile customerOperationSolution = changeOrder.getCustomerOperationSolution();
        if (null != customerOperationSolution) {
            attachmentFiles.add(customerOperationSolution);
        }

        // 前后方线下评审记录
        Object offlineReviewRecordsObj = changeOrder.getOfflineReviewRecordsObj();
        if (!ObjectUtils.isEmpty(offlineReviewRecordsObj)) {
            List<AttachmentFile> offlineReviewRecords = JsonUtils.parseArray(offlineReviewRecordsObj, AttachmentFile.class);
            attachmentFiles.addAll(offlineReviewRecords);
        }

        // 操作checklist
        Object operationCheckListObj = changeOrder.getOperationCheckListObj();
        if (!ObjectUtils.isEmpty(operationCheckListObj)) {
            List<AttachmentFile> operationCheckList = JsonUtils.parseArray(operationCheckListObj, AttachmentFile.class);
            attachmentFiles.addAll(operationCheckList);
        }

        // 其他操作附件
        Object otherOperationSolutionObj = changeOrder.getOtherOperationSolutionObj();
        if (!ObjectUtils.isEmpty(otherOperationSolutionObj)) {
            List<AttachmentFile> otherOperationSolutions = JsonUtils.parseArray(otherOperationSolutionObj, AttachmentFile.class);
            attachmentFiles.addAll(otherOperationSolutions);
        }
        return getAttachFiles(attachmentFiles);
    }

    // 内部/合作方网络变更单
    private List<AttachFilesVO> getAttachFiles(List<AttachmentFile> attachmentFiles) {
        List<AttachFilesVO> attachFilesVOS = Lists.newArrayList();
        for (AttachmentFile attachmentFile : attachmentFiles) {
            AttachFilesVO attachFilesVO = new AttachFilesVO();
            attachFilesVO.setId(attachmentFile.getId());
            attachFilesVO.setAttachFileName(attachmentFile.getName());
            attachFilesVO.setAttachFileSize(attachmentFile.getSize());
            attachFilesVO.setFileKey(attachmentFile.getFileKey());
            attachFilesVOS.add(attachFilesVO);
        }
        return attachFilesVOS;
    }

    // 审批进展（内部/合作方网络变更单_批次任务）
    private List<ApprovalProgressVO> queryBatchTaskApprovalProgress(String batchTaskId,
                                                                    Map<String, String> reviewOpinionMap,
                                                                    Map<String, List<TextValuePair>> reviewResultMap) {
        // 批次任务查询所有的审核节点，和web端保持一致
        List<ApproveRecord> approveRecords = FlowHelper.getAfterSubmitApprovedRecords(batchTaskId);
        if (CollectionUtils.isEmpty(approveRecords)) {
            return Lists.newArrayList();
        }
        // 过滤执行节点
        approveRecords = approveRecords.stream().filter(v -> !EXECUTION_NODE_TYPE.equals(v.getNodeType())).collect(Collectors.toList());

        return getApprovalProgress(batchTaskId, approveRecords, reviewOpinionMap, reviewResultMap);
    }

    // 审批进展（内部/合作方网络变更单）
    private List<ApprovalProgressVO> queryApprovalProgress(String changeOrderId,
                                                           Map<String, String> reviewOpinionMap,
                                                           Map<String, List<TextValuePair>> reviewResultMap) {
        List<ApproveRecord> approveRecords = FlowHelper.getApprovedRecords(changeOrderId);
        if (CollectionUtils.isEmpty(approveRecords)) {
            return Lists.newArrayList();
        }

        return getApprovalProgress(changeOrderId, approveRecords, reviewOpinionMap, reviewResultMap);
    }

    private List<ApprovalProgressVO> getApprovalProgress(String id, List<ApproveRecord> approveRecords,
                                                         Map<String, String> reviewOpinionMap,
                                                         Map<String, List<TextValuePair>> reviewResultMap) {
        List<String> approverIds = approveRecords.stream().map(ApproveRecord::getApprover).collect(Collectors.toList());
        Map<String, PersonGeneralInfo> info = getPersonGeneralInfos(approverIds);
        List<ApprovalProgressVO> approvalProgressVOS = Lists.newArrayList();
        for (ApproveRecord approveRecord : approveRecords) {
            ApprovalProgressVO approvalProgressVO = new ApprovalProgressVO();
            String approvalNode = approveRecord.getExtendedCode();
            // approvalNode为空，跳过
            if (StringUtils.isEmpty(approvalNode)) {
                continue;
            }
            if (SIGNING_NODE_EXTEND_CODE_MAP.containsKey(approvalNode)) {
                // 只能记录已完成的节点
                List<ApprovalProgressVO> approvalProgressVOS1 = SIGNING_NODE_EXTEND_CODE_MAP.get(approvalNode).apply(id);
                approvalProgressVOS.addAll(approvalProgressVOS1);
                continue;
            }
            // 如果有多个人可以审批改节点，一起带出
            if (ACTIVE_TASK_STATUS.equals(approveRecord.getTaskStatus())) {
                FlowQueryDTO flowQueryDTO = new FlowQueryDTO();
                flowQueryDTO.setTenantId(ContextHelper.getTenantId());
                flowQueryDTO.setBusinessId(id);
                List<String> currentReviewers = FlowServiceHelper.queryFlowHandlers(flowQueryDTO).stream().distinct().collect(Collectors.toList());
                Map<String, PersonGeneralInfo> currentReviewGeneralInfo = getPersonGeneralInfos(currentReviewers);
                approvalProgressVO.setApprovers(currentReviewGeneralInfo.values().stream().collect(Collectors.toList()));
            } else {
                approvalProgressVO.setApprovers(Lists.newArrayList(info.get(approveRecord.getApprover())));
            }
            approvalProgressVO.setApprovalNode(approvalNode);

            approvalProgressVO.setApprovalStatus(approveRecord.getTaskStatus());
            approvalProgressVO.setApprovalDate(approveRecord.getApprovalDate());
            // 审核结论
            List<TextValuePair> approvalResultPair = reviewResultMap.get(approveRecord.getExtendedCode());
            Optional<String> approvalResult = Optional.ofNullable(approvalResultPair)
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(INTEGER_ZERO))
                    .map(v -> v.getTextByLanguage(ContextHelper.getLangId()));
            approvalProgressVO.setApprovalResult(approvalResult.orElse(EMPTY_STRING));
            // 审核意见
            approvalProgressVO.setApprovalOpinion(reviewOpinionMap.getOrDefault(approveRecord.getExtendedCode(), EMPTY_STRING));
            // --------------- //
            approvalProgressVOS.add(approvalProgressVO);
        }
        // 按照时间排序，没审批的节点和之后审批的节点，排前面。
        approvalProgressVOS.sort((t1, t2) -> {
            if (null == t1.getApprovalDate() && null == t2.getApprovalDate()) {
                return INTEGER_ZERO;
            }
            if (null != t1.getApprovalDate() && null == t2.getApprovalDate()) {
                return INTEGER_ONE;
            }
            if (null == t1.getApprovalDate() && null != t2.getApprovalDate()) {
                return INTEGER_NEGATIVE_1;
            }
            return t2.getApprovalDate().compareTo(t1.getApprovalDate());
        });
        return approvalProgressVOS;
    }

    public static Map<String, PersonGeneralInfo> getPersonGeneralInfos(List<String> approverIds) {
        if (CollectionUtils.isEmpty(approverIds)) {
            return new HashMap<>();
        }
        return HolClient.getPersonGeneralInfo(ContextHelper.getLangId(),
                new GetPersonGeneralInfoReq() {{
                    setIds(approverIds);
                    setInfoBlocks(Lists.newArrayList(
                            new InfoBlock(v1, B0000),
                            new InfoBlock(v1, B0001),
                            new InfoBlock(v2, B0002),
                            new InfoBlock(v1, B0004)));
                }});
    }

    /**
     * 获取批次国际会签审批记录
     */
    private static List<ApprovalProgressVO> getBatchIntelApproveRecords(String batchTaskId) {
        List<SubIntlAdminApproval> subEntities = InterAdminApproverAbility.getBatchApprovalRecordList(batchTaskId);
        if (CollectionUtils.isEmpty(subEntities)) {
            return Lists.newArrayList();
        }
        subEntities = subEntities.stream()
                .filter(v ->null != v.getApprover()).collect(Collectors.toList());
        List<String> approverIds = subEntities.stream()
                .map(v -> v.getApprover().getEmpUIID()).collect(Collectors.toList());
        Map<String, PersonGeneralInfo> info = getPersonGeneralInfos(approverIds);
        List<ApprovalProgressVO> approvalProgressVOS = Lists.newArrayList();
        for (SubIntlAdminApproval subEntitie : subEntities) {
            ApprovalProgressVO approvalProgressVO = new ApprovalProgressVO();
            approvalProgressVO.setApprovalOpinion(subEntitie.getOpinion());
            approvalProgressVO.setApprovalResult(null == subEntitie.getResult()
                    ? EMPTY_STRING : subEntitie.getResult().getTextByLanguage(ContextHelper.getLangId()));
            approvalProgressVO.setApprovalNode(BATCH_INTL_ADMIN_APPROVAL.name());
            approvalProgressVO.setApprovalStatus(null == subEntitie.getResult() ? ACTIVE_TASK_STATUS :COMPLETED_TASK_STATUS);
            approvalProgressVO.setApprovalDate(subEntitie.getApprovedDate());
            approvalProgressVO.setApprovers(Lists.newArrayList(info.get(subEntitie.getApprover().getEmpUIID())));
            approvalProgressVOS.add(approvalProgressVO);
        }
        return approvalProgressVOS;
    }

    /**
     * 获取网服一体化会签审批记录
     */
    private static List<ApprovalProgressVO> getNetIntegrationApproveRecords(String changeOrderId) {
        List<OpAssocProdNetServIntApproval> subEntities = ChangeOrderAbility.listSpecificChangeOrderSubEntity(changeOrderId, OpAssocProdNetServIntApproval.class);
        if (CollectionUtils.isEmpty(subEntities)) {
            return Lists.newArrayList();
        }
        subEntities = subEntities.stream()
                .filter(v ->null != v.getApprover()).collect(Collectors.toList());
        List<String> approverIds = subEntities.stream()
                .map(v -> v.getApprover().getEmpUIID()).collect(Collectors.toList());
        Map<String, PersonGeneralInfo> info = getPersonGeneralInfos(approverIds);
        List<ApprovalProgressVO> approvalProgressVOS = Lists.newArrayList();
        for (OpAssocProdNetServIntApproval subEntitie : subEntities) {
            ApprovalProgressVO approvalProgressVO = new ApprovalProgressVO();
            approvalProgressVO.setApprovalOpinion(subEntitie.getOpinion());
            approvalProgressVO.setApprovalResult(null == subEntitie.getResult() ? EMPTY_STRING
                    : subEntitie.getResult().getTextByLanguage(ContextHelper.getLangId()));
            approvalProgressVO.setApprovalNode(NET_INTEGRATION.name());
            approvalProgressVO.setApprovalStatus(null == subEntitie.getResult() ? ACTIVE_TASK_STATUS :COMPLETED_TASK_STATUS);
            approvalProgressVO.setApprovalDate(subEntitie.getApprovedDate());
            approvalProgressVO.setApprovers(Lists.newArrayList(info.get(subEntitie.getApprover().getEmpUIID())));
            approvalProgressVOS.add(approvalProgressVO);
        }
        return approvalProgressVOS;
    }

    /**
     * 获取研发一体化会签审批记录
     */
    private static List<ApprovalProgressVO> getRdIntegrationApproveRecords(String changeOrderId) {
        List<OpAssocProdDevIntApproval> subEntities = ChangeOrderAbility.listSpecificChangeOrderSubEntity(changeOrderId, OpAssocProdDevIntApproval.class);
        if (CollectionUtils.isEmpty(subEntities)) {
            return Lists.newArrayList();
        }
        subEntities = subEntities.stream()
                .filter(v ->null != v.getApprover()).collect(Collectors.toList());
        List<String> approverIds = subEntities.stream()
                .map(v -> v.getApprover().getEmpUIID()).collect(Collectors.toList());
        Map<String, PersonGeneralInfo> info = getPersonGeneralInfos(approverIds);
        List<ApprovalProgressVO> approvalProgressVOS = Lists.newArrayList();
        for (OpAssocProdDevIntApproval subEntitie : subEntities) {
            ApprovalProgressVO approvalProgressVO = new ApprovalProgressVO();
            approvalProgressVO.setApprovalOpinion(subEntitie.getOpinion());
            approvalProgressVO.setApprovalResult(null == subEntitie.getResult()
                    ? EMPTY_STRING : subEntitie.getResult().getTextByLanguage(ContextHelper.getLangId()));
            approvalProgressVO.setApprovalNode(RD_INTEGRATION.name());
            approvalProgressVO.setApprovalStatus(null == subEntitie.getResult() ? ACTIVE_TASK_STATUS :COMPLETED_TASK_STATUS);
            approvalProgressVO.setApprovalDate(subEntitie.getApprovedDate());
            approvalProgressVO.setApprovers(Lists.newArrayList(info.get(subEntitie.getApprover().getEmpUIID())));
            approvalProgressVOS.add(approvalProgressVO);
        }
        return approvalProgressVOS;
    }

    /**
     * 获取国际会签角色审批记录
     */
    private static List<ApprovalProgressVO> getIntelApproveRecords(String changeOrderId) {
        List<IntlAdminApproval> subEntities = InterAdminApproverAbility.getApprovalRecordList(changeOrderId);
        if (CollectionUtils.isEmpty(subEntities)) {
            return Lists.newArrayList();
        }
        subEntities = subEntities.stream()
                .filter(v ->null != v.getApprover()).collect(Collectors.toList());
        List<String> approverIds = subEntities.stream()
                .map(v -> v.getApprover().getEmpUIID()).collect(Collectors.toList());
        Map<String, PersonGeneralInfo> info = getPersonGeneralInfos(approverIds);
        List<ApprovalProgressVO> approvalProgressVOS = Lists.newArrayList();
        for (IntlAdminApproval subEntitie : subEntities) {
            ApprovalProgressVO approvalProgressVO = new ApprovalProgressVO();
            approvalProgressVO.setApprovalOpinion(subEntitie.getOpinion());
            approvalProgressVO.setApprovalResult(null == subEntitie.getResult()
                    ? EMPTY_STRING : subEntitie.getResult().getTextByLanguage(ContextHelper.getLangId()));
            approvalProgressVO.setApprovalNode(INTL_ADMIN_APPROVAL.name());
            approvalProgressVO.setApprovalStatus(null == subEntitie.getResult() ? ACTIVE_TASK_STATUS :COMPLETED_TASK_STATUS);
            approvalProgressVO.setApprovalDate(subEntitie.getApprovedDate());
            approvalProgressVO.setApprovers(Lists.newArrayList(info.get(subEntitie.getApprover().getEmpUIID())));
            approvalProgressVOS.add(approvalProgressVO);
        }
        return approvalProgressVOS;
    }

    /**
     * 获取多模会签角色审批记录
     */
    private static List<ApprovalProgressVO> getMultiModeProdApproveRecords(String changeOrderId) {
        List<MultiModeProduct> subEntities = ChangeOrderAbility.listSpecificChangeOrderSubEntity(changeOrderId, MultiModeProduct.class);
        if (CollectionUtils.isEmpty(subEntities)) {
            return Lists.newArrayList();
        }
        subEntities = subEntities.stream()
                .filter(v ->null != v.getLastModifiedBy()).collect(Collectors.toList());
        List<String> approverIds = subEntities.stream()
                .map(v -> v.getLastModifiedBy()).collect(Collectors.toList());
        Map<String, PersonGeneralInfo> info = getPersonGeneralInfos(approverIds);
        List<ApprovalProgressVO> approvalProgressVOS = Lists.newArrayList();
        for (MultiModeProduct subEntitie : subEntities) {
            ApprovalProgressVO approvalProgressVO = new ApprovalProgressVO();
            approvalProgressVO.setApprovalOpinion(subEntitie.getOpinion());
            approvalProgressVO.setApprovalResult(null == subEntitie.getResult()
                    ? EMPTY_STRING : subEntitie.getResult().getTextByLanguage(ContextHelper.getLangId()));
            approvalProgressVO.setApprovalNode(MULTIMODE_PRODUCT_OWNER.name());
            approvalProgressVO.setApprovalStatus(null == subEntitie.getResult() ? ACTIVE_TASK_STATUS :COMPLETED_TASK_STATUS);
            approvalProgressVO.setApprovalDate(subEntitie.getApprovedDate());
            approvalProgressVO.setApprovers(Lists.newArrayList(info.get(subEntitie.getLastModifiedBy())));
            approvalProgressVOS.add(approvalProgressVO);
        }
        return approvalProgressVOS;
    }

    /**
     * 获取审批流程进展信息
     * @param orderNo
     * @return ApprovalProcessDTO
     */
    private ApprovalProcessDTO getInstanceApprovalProcess(String orderNo) {
        // 1.检索任务信息
        NetworkChangeAssignment assignment =
                AssignmentAbility.querySpecificTypeAssignmentByCode(orderNo, NetworkChangeAssignment.class);
        if (Objects.isNull(assignment)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ASSIGNMENT_NOT_EXISTS));
        }

        // 2.获取审批实体ID + 审批流程编码
        String bizId;
        ApproveFlowCodeEnum approveFlowCode;

        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        switch (assignmentType) {
            case NETWORK_CHANGE:
                bizId = assignment.getBillId();
                approveFlowCode = ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW;
                break;

            case SUBCONTRACTOR_NETWORK_CHANGE:
                bizId = assignment.getBillId();
                approveFlowCode = ApproveFlowCodeEnum.SUBCONTRACTOR_OC_FLOW;
                break;

            case NETWORK_CHANGE_BATCH:
                bizId = assignment.getApproveBatchTaskId();
                approveFlowCode = ApproveFlowCodeEnum.BATCH_TASK_FLOW;
                break;

            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                bizId = assignment.getApproveBatchTaskId();
                approveFlowCode = ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW;
                break;

            default:
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ERROR_ASSIGNMENT_TYPE));
        }

        // 3.检索审批流程信息
        return FlowHelper.getApprovalProcessInfo(bizId, approveFlowCode);
    }

    /*
     * 获取网络变更单审核类型
     */
    private static String getChangeDeptType(ChangeOrder changeOrder) {
        Integer changeOrderType = 0;
        DeptTypeEnum deptType = ChangeOrderAbility.getDeptType(changeOrder);
        if (DeptTypeEnum.INNER == deptType && BoolEnum.N.getValue().equals(changeOrder.getIsGovEnt().getValue())) {
            changeOrderType = 1;
        }
        if (DeptTypeEnum.INTER == deptType) {
            changeOrderType = 2;
        }
        return Integer.toString(changeOrderType);
    }

    private static void setRecommendationLevel(ChangeOrder changeOrder,
                                               ReviewDetailVO reviewDetailVO,
                                               List<ApprovalProgressVO> approvalProgress,
                                               String batchNo) {

        // 电信服务、网络服务处总监 满足集团直管网络
        boolean isOperatorGroupNetwork = ChangeOrderAbility.isGroupDirectManagementNetwork(changeOrder, batchNo);
        reviewDetailVO.setOperatorGroupNetwork(isOperatorGroupNetwork);
        if (!CollectionUtils.isEmpty(approvalProgress)) {
            List<ApprovalProgressVO> approveRecords = approvalProgress.stream().filter(v -> ACTIVE_TASK_STATUS.equals(v.getApprovalStatus())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(approveRecords)) {
                String activeExtendCode = approveRecords.get(INTEGER_ZERO).getApprovalNode();
                reviewDetailVO.setRecommendationLevel(getRecommendationLevel(changeOrder, activeExtendCode,isOperatorGroupNetwork));
            }
        }
    }
    // 获取推荐层级
    private static String getRecommendationLevel(ChangeOrder changeOrder, String extendedCode, boolean isOperatorGroupNetwork) {
        if (changeOrder == null) {
            return EMPTY_STRING;
        }

        // 获取推荐层级
        NodeWithInlinePageEnum nodeWithInlinePageEnum = ApprovalAbility.getRecommendAdminApprovalLvl(changeOrder);
        if (nodeWithInlinePageEnum == null) {
            return EMPTY_STRING;
        }
        NodeWithInlinePageEnum currentNode = NodeWithInlinePageEnum.fromValue(extendedCode);
        // 获取当前节点的层级，如果当前节点层级 >= 推荐层级，则不进行提示
        if (currentNode == null || currentNode.getLevel() > nodeWithInlinePageEnum.getLevel()) {
            return EMPTY_STRING;
        }

        // 电信服务、网络服务处总监 满足集团直管网络不需要推荐层级 and 推荐层级计算结果=网络服务处总监
        if (ApproveNodeEnum.teleServiceAndNetProdRDirNodes().contains(extendedCode)
                && isOperatorGroupNetwork
                && NodeWithInlinePageEnum.ADMIN_NET_PROD_DIR_NODE == currentNode) {
            return EMPTY_STRING;
        }

        // 提示框内容赋值
        ApproveNodeEnum nodeEnum = ApproveNodeEnum.getApproveNodeEnum(nodeWithInlinePageEnum.getNodeExtendId());
        if (nodeEnum == null || nodeEnum.getHandler() == null) {
            return EMPTY_STRING;
        }
        InnerAdminHandlerImpl handler = (InnerAdminHandlerImpl) nodeEnum.getHandler();
        String msg = ZH_CN.equals(ContextHelper.getLangId()) ? handler.getRole().getZhCn() : handler.getRole().getEnUs();

        return msg;
    }
}
