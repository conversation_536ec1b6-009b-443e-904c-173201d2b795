package com.zte.iccp.itech.extension.domain.model.subentity.support;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.changeorder.EntityEnum.SubSupportModeEnum;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.grantfile.ISupportStaffBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.subentity.SupportStaffFieldConsts.*;

@ApiModel("分包商 -支持人员 -网络责任人")
@Setter
@Getter
@BaseSubEntity.Info(value = "support_person_net_owner", parent = SubcontractorChangeOrder.class)
public class SupportStaffSubNetOwner extends BaseSubEntity implements ISupportStaffBase {

    @ApiModelProperty("支持人员")
    @JsonProperty(value = PERSON_ID_NET_OWNER)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee person;

    @ApiModelProperty("支持方式")
    @JsonProperty(value = SUPPORT_WAY_NET_OWNER)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private SubSupportModeEnum supportMode;

    @ApiModelProperty("任务说明")
    @JsonProperty(value = TASK_DESC_NET_OWNER)
    private String taskDescription;

    @ApiModelProperty("所属部门")
    @JsonProperty(value = DEPT_ID_NET_OWNER)
    private String department;

    @ApiModelProperty("联系方式")
    @JsonProperty(value = TEL_NO_NET_OWNER)
    private String telephoneNumber;
}
