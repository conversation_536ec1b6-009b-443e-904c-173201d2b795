package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;

@ApiModel("故障管理任务实体")
@Setter
@Getter
@BaseEntity.Info("assignment")
public class FaultManagementAssignment extends Assignment {

    @ApiModelProperty("WarRoom")
    @JsonProperty(value = FaultManagementFieldConsts.WAR_ROOM)
    private String warRoom;

    @ApiModelProperty("WarRoom 主题")
    @JsonProperty(value = FaultManagementFieldConsts.WAR_ROOM_TITLE)
    private String warRoomTitle;

    @ApiModelProperty("故障程度")
    @JsonProperty(value = FaultManagementFieldConsts.FAULT_LEVEL)
    private List<TextValuePair> faultLevel;

    @ApiModelProperty("客户")
    @JsonProperty(value = FaultManagementFieldConsts.CUSTOMER)
    private String customer;

    @ApiModelProperty("PDM产品")
    @JsonProperty(value = FaultManagementFieldConsts.PDM_PRODUCT)
    private String pdmProduct;

    @ApiModelProperty("区域")
    @JsonProperty(value = FaultManagementFieldConsts.REGION)
    private String region;

    @ApiModelProperty("故障发生时间")
    @JsonProperty(value = FaultManagementFieldConsts.OCCURRENCE_TIME)
    private Date occurrenceTime;

    @ApiModelProperty("备注")
    @JsonProperty(value = FaultManagementFieldConsts.REMARK)
    private String remark;

    @ApiModelProperty("故障响应时长（分钟）")
    @JsonProperty(value = FaultManagementFieldConsts.FAULT_RESPONSE_TIME)
    private Integer faultResponseTime;

    @ApiModelProperty("人员到位时长（分钟）")
    @JsonProperty(value = FaultManagementFieldConsts.PERSON_IN_PLACE_TIME)
    private Integer personInPlaceTime;

    @ApiModelProperty("故障定位时长（分钟）")
    @JsonProperty(value = FaultManagementFieldConsts.FAULT_LOCATION_TIME)
    private Integer faultLocationTime;

    @ApiModelProperty("故障恢复时长（分钟）")
    @JsonProperty(value = FaultManagementFieldConsts.FAULT_RECOVERY_TIME)
    private Integer faultRecoveryTime;

    @ApiModelProperty("历史进展")
    @JsonProperty(value = FaultManagementFieldConsts.HISTORY_PROGRESS)
    private String historyProgress;
}
