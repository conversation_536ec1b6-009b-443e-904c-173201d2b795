package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.FlowConstants;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;

/**
 * 批次取消后置插件
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/3/19
 */
public class BatchCancelAfterPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String approveResult = (String) body.getVariables().get(OC_APPROVE_RESULT);
        if (StringUtils.isEmpty(approveResult)) {
            return false;
        }
        String batchId = body.getBusinessId();
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(body.getFlowCode());
        Class<? extends BaseEntity> batchClassEntity = ApproveFlowCodeEnum.BATCH_TASK_FLOW == approveFlowEnum
                ? BatchTask.class
                : SubcontractorBatchTask.class;

        ApproveResultEnum approveResultEnum = ApproveResultEnum.getApproveResultEnum(approveResult);
        switch (Objects.requireNonNull(approveResultEnum)) {
            // 取消操作审核 同意后的操作
            case PASS:
                BatchTaskAbility.processBatchCancel(batchClassEntity, batchId, approveFlowEnum);
                break;
            // 取消操作审核 驳回后的操作（更新自定义流程变量为默认的N） 如果不更新流程会重新走到取消操作审核节点去
            case REJECT:
                FlowHelper.changeFlowParams(batchId,
                        MapUtils.newHashMap(FlowConstants.PENDING_NOTIFICATION_CANCEL, BoolEnum.N.name()), approveFlowEnum);
                break;
            default:
                break;

        }

        return false;
    }
}
