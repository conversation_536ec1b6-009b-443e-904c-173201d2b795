package com.zte.iccp.itech.extension.ability.clockin;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.OperatorAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.helper.entity.OrderBy;
import com.zte.iccp.itech.extension.common.helper.entity.OrderEnum;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.PageRowsUtils;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.ProdCateLvlConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInStateEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.clockin.*;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.domain.model.subentity.SubconOperationObject;
import com.zte.iccp.itech.extension.openapi.model.UserTypeEnum;
import com.zte.iccp.itech.extension.openapi.model.UserVO;
import com.zte.iccp.itech.extension.openapi.model.clockin.*;
import com.zte.iccp.itech.extension.openapi.model.clockin.vo.BatchInfoVO;
import com.zte.iccp.itech.extension.openapi.model.clockin.vo.ClockInStatusVO;
import com.zte.iccp.itech.extension.openapi.model.clockin.vo.InetClockInVO;
import com.zte.iccp.itech.extension.plugin.operation.assignment.GetPermissionFilter;
import com.zte.iccp.itech.extension.spi.client.*;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.hol.CompanyUserResp;
import com.zte.iccp.itech.extension.spi.model.hol.GetOrgInfoReq;
import com.zte.iccp.itech.extension.spi.model.hol.GetOutSourcingReq;
import com.zte.iccp.itech.extension.spi.model.hol.OrgInfo;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.extension.spi.model.uac30.FuzzyQueryUserResp;
import com.zte.iccp.itech.extension.spi.model.uac30.UserResp;
import com.zte.iccp.itech.extension.spi.model.ucs.vo.UcsUserInfo;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.core.orm.query.SubQueryFilter;
import com.zte.paas.lcap.ddm.common.api.dto.orm.Range;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.helper.TreeNodeHelper.findBottomPaths;
import static com.zte.iccp.itech.extension.common.helper.entity.OrderEnum.DESC;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.INNER_OFFICE_ORG_CODE_PATH;
import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.ClockIn.TASK_NOT_EXISTS;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.PRODUCT_CLASSIFICATION;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.OPERATION_TYPE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInRecordFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum.OPERATING_SUPERVISOR;
import static com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum.OPERATION_EXECUTION;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum.BATCH_TASK;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum.SUBCONTRACT_BATCH_TASK;

/**
 * <AUTHOR>
 * @since 2024/09/11
 */
public final class ClockInQueryAbility implements GetPermissionFilter {

    /**
     * 机构前缀
     */
    private static final String ORG_PREFIX = "ORG";

    /**
     * 打卡任务默认字段
     */
    private static final List<String> CLOCK_IN_DETAIL_FIELDS = Lists.newArrayList(
            ID,
            TASK_TYPE,
            OPERATION_LEVEL,
            TASK_STATUS,
            PRODUCT_CLASSIFICATION,
            OPERATION_SUBJECT,
            BATCH_CODE,
            TIME_ZONE,
            OPERATOR,
            RESPONSIBLE_DEPT,
            BATCH_TASK_ID,
            OPERATION_TYPE,
            PLAN_PREPARE_START_TIME,
            PLAN_ON_DUTY_START_TIME,
            PLAN_PREPARE_END_TIME,
            PLAN_EXECUTE_START_TIME,
            PLAN_EXECUTE_END_TIME,
            PLAN_TEST_START_TIME,
            PLAN_TEST_END_TIME,
            PLAN_ON_DUTY_END_TIME,
            TOTAL_ON_DUTY_DURATION,
            ENTITY_TYPE,
            ON_DUTY_FREQUENCY,
            CHANGE_ORDER_ID
    );

    /**
     * 获取打卡任务下未撤销的打卡记录
     */
    public static List<ClockInRecord> getClockInRecords(String clockInTaskId, String... fields) {
        return QueryDataHelper.query(
                ClockInRecord.class,
                Lists.newArrayList(fields),
                clockInTaskId,
                Lists.newArrayList(
                        new Filter(REVOKED, Comparator.EQ, Lists.newArrayList(BoolEnum.N))));
    }

    /**
     * 批量获取打卡任务下未撤销的打卡记录
     */
    public static Map<String, List<ClockInRecord>> getClockInRecordList(List<String> clockInTaskIds, String... fields) {
        if (CollectionUtils.isEmpty(clockInTaskIds)) {
            return new HashMap<>();
        }
        List<ClockInRecord> recordList = QueryDataHelper.query(
                ClockInRecord.class,
                Lists.newArrayList(fields),
                clockInTaskIds,
                Lists.newArrayList(
                        new Filter(REVOKED, Comparator.EQ, Lists.newArrayList(BoolEnum.N))));
        return recordList.stream().collect(Collectors.groupingBy(ClockInRecord::getPid));
    }

    /**
     * 获取打卡任务下最后N条打卡记录（含已撤销的记录）
     */
    public static List<ClockInRecord> getLastClockInRecords(
            String clockInTaskId,
            int n,
            BoolEnum revoked,
            String... fields) {
        List<IFilter> filters = com.zte.iccp.itech.zlic.util.Lists.newArrayList(
                new Filter(PID, Comparator.EQ, clockInTaskId));
        if (revoked != null) {
            filters.add(new Filter(REVOKED, Comparator.EQ, com.zte.iccp.itech.zlic.util.Lists.newArrayList(revoked)));
        }

        return QueryDataHelper.query(
                ClockInRecord.class,
                com.zte.iccp.itech.zlic.util.Lists.newArrayList(fields),
                filters,
                new Range(n),
                new OrderBy(CLOCK_IN_TIME, DESC));
    }

    public static OnDutyInfo getOnDutyInfo(String clockInTaskId) {
        OnDutyInfo onDutyInfo = new OnDutyInfo();

        // 还没到值守阶段
        ClockInTask onDutyTask = QueryDataHelper.get(
                ClockInTask.class, Lists.newArrayList(ENTITY_TYPE, BATCH_TASK_ID, TOTAL_ON_DUTY_DURATION, ON_DUTY_FREQUENCY), clockInTaskId);
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(
                onDutyTask.getEntityType().getEntityClass(),
                Lists.newArrayList(BatchTaskFieldConsts.CLOCK_IN_STATE),
                onDutyTask.getBatchTaskId());
        if (batchTask == null) {
            return onDutyInfo;
        }
        if (batchTask.getClockInState() == null || batchTask.getClockInState().ordinal() < ClockInStateEnum.ON_DUTY_GOING.ordinal()) {
            return onDutyInfo;
        }

        // 查询操作负责人的操作任务最后一个打卡时间
        ClockInTask operationTask = getKeyTask(onDutyTask.getBatchTaskId(), ClockInTaskTypeEnum.OPERATION, ID);
        onDutyInfo.operationEndTime = getClockInRecords(operationTask.getId(), CLOCK_IN_OPTION, CLOCK_IN_TIME)
                .stream()
                .max(java.util.Comparator.comparing(ClockInRecord::getClockInTime))
                .map(ClockInRecord::getClockInTime)
                .orElse(null);

        // 查所有值守打卡记录
        List<ClockInRecord> clockInRecords = getClockInRecords(
                        clockInTaskId, CLOCK_IN_OPTION, CLOCK_IN_TIME)
                .stream()
                .sorted((a, b) -> b.getClockInTime().compareTo(a.getClockInTime()))
                .collect(Collectors.toList());

        dealErrorHandling(onDutyInfo, clockInRecords, onDutyTask);

        return onDutyInfo;
    }

    public static Map<String, OnDutyInfo> getOnDutyInfos(List<ClockInTask> clockInTasks, Boolean needsDetailedInfo) {
        Map<String, OnDutyInfo> onDutyInfoMap = new HashMap<>();
        if (CollectionUtils.isEmpty(clockInTasks)) {
            return onDutyInfoMap;
        }
        // 组装查询入参
        List<String> clockInTaskIdList = new ArrayList<>();
        List<String> batchTaskIdList = new ArrayList<>();
        List<String> subBatchTaskIdList = new ArrayList<>();
        buildParam(clockInTasks,clockInTaskIdList,batchTaskIdList,subBatchTaskIdList);

        // 查询批次任务
        Map<String, IBatchTask> iBatchTaskMap = queryBatchTasks(batchTaskIdList, subBatchTaskIdList, Lists.newArrayList(BatchTaskFieldConsts.CLOCK_IN_STATE));

        // 查所有值守打卡任务的打卡记录
        Map<String, List<ClockInRecord>> recordMap = getClockInRecordList(clockInTaskIdList, CLOCK_IN_OPTION, CLOCK_IN_TIME, PID);

        // 查询批次任务下操作负责人的操作打卡任务（一次批次任务对应一个操作负责人的操作打卡任务）
        List<String> allBatchTaskIdList = new ArrayList<>();
        allBatchTaskIdList.addAll(batchTaskIdList);
        allBatchTaskIdList.addAll(subBatchTaskIdList);
        List<ClockInTask> operationClockInTasks = getKeyTasks(allBatchTaskIdList, ClockInTaskTypeEnum.OPERATION, ID, BATCH_TASK_ID);
        Map<String, String> batchTaskIdOperationClockIdMap = operationClockInTasks.stream().collect(Collectors.toMap(ClockInTask::getBatchTaskId, ClockInTask::getId, (o1, o2) -> o1));
        List<String> operationClockInTaskIdList = operationClockInTasks.stream().map(ClockInTask::getId).collect(Collectors.toList());
        // 查操作负责人的操作打卡任务对应的打卡记录
        Map<String, List<ClockInRecord>> operationRecordMap = getClockInRecordList(operationClockInTaskIdList, CLOCK_IN_OPTION, CLOCK_IN_TIME, PID);

        for (ClockInTask clockInTask : clockInTasks) {
            if (checkData(clockInTask)) {
                continue;
            }
            OnDutyInfo onDutyInfo = new OnDutyInfo();
            String clockInTaskId = clockInTask.getId();

            // 还没到值守阶段
            if (!isOnDuty(iBatchTaskMap, clockInTask)) {
                continue;
            }

            // 批次任务下的操作打卡任务的打卡记录
            List<ClockInRecord> operationRecord = operationRecordMap.get(batchTaskIdOperationClockIdMap.get(clockInTask.getBatchTaskId()));
            if (CollectionUtils.isEmpty(operationRecord)) {
                continue;
            }
            onDutyInfo.operationEndTime = operationRecord
                    .stream()
                    .max(java.util.Comparator.comparing(ClockInRecord::getClockInTime))
                    .map(ClockInRecord::getClockInTime)
                    .orElse(null);
            onDutyInfoMap.put(clockInTaskId, onDutyInfo);
            // 不需要展示值守详细信息就不去计算值守打卡计划
            if (!needsDetailedInfo) {
                continue;
            }
            // 获取此打卡任务下所有打卡记录
            List<ClockInRecord> records = recordMap.get(clockInTaskId);
            List<ClockInRecord> sortRecords = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(records)) {
                sortRecords = records
                        .stream()
                        .sorted((a, b) -> b.getClockInTime().compareTo(a.getClockInTime()))
                        .collect(Collectors.toList());
            }
            dealErrorHandling(onDutyInfo, sortRecords, clockInTask);
        }
        return onDutyInfoMap;
    }

    private static boolean checkData(ClockInTask clockInTask) {
        if (clockInTask.getTotalOnDutyDuration() == null
                || clockInTask.getOnDutyFrequency() == null) {
            return true;
        }

        return clockInTask.getTotalOnDutyDuration().equals(DOUBLE_ZERO)
                || clockInTask.getOnDutyFrequency().equals(0);
    }

    private static boolean isOnDuty(Map<String, IBatchTask> iBatchTaskMap, ClockInTask clockInTask) {
        IBatchTask batchTask = iBatchTaskMap.get(clockInTask.getBatchTaskId());
        if (batchTask == null) {
            return false;
        }

        return batchTask.getClockInState() != null
                && batchTask.getClockInState().ordinal() >= ClockInStateEnum.ON_DUTY_GOING.ordinal();
    }

    private static void buildParam(List<ClockInTask> clockInTasks, List<String> clockInTaskIdList, List<String> batchTaskIdList, List<String> subBatchTaskIdList) {
        for (ClockInTask clockInTask : clockInTasks) {
            clockInTaskIdList.add(clockInTask.getId());
            if (clockInTask.getEntityType() == BATCH_TASK) {
                batchTaskIdList.add(clockInTask.getBatchTaskId());
            } else if (clockInTask.getEntityType() == SUBCONTRACT_BATCH_TASK){
                subBatchTaskIdList.add(clockInTask.getBatchTaskId());
            }
        }
    }

    /**
     * 根据批次任务ids和分包商批次任务ids 查询批次任务和分包商批次任务
     */
    public static Map<String, IBatchTask> queryBatchTasks(List<String> batchTaskIdList, List<String> subBatchTaskIdList, List<String> fieldList) {
        List<BatchTask> batchTaskList = BatchTaskAbility.batchGet(batchTaskIdList, fieldList, BatchTask.class);
        List<SubcontractorBatchTask> subBatchTaskList = BatchTaskAbility.batchGet(subBatchTaskIdList, fieldList, SubcontractorBatchTask.class);
        List<IBatchTask> iBatchTaskList = batchTaskList.stream()
                .map(BatchTask.class::cast)
                .collect(Collectors.toList());
        List<IBatchTask> iSubBatchTaskList = subBatchTaskList.stream()
                .map(SubcontractorBatchTask.class::cast)
                .collect(Collectors.toList());
        iBatchTaskList.addAll(iSubBatchTaskList);
        return iBatchTaskList.stream().collect(Collectors.toMap(IBatchTask::getId, item -> item, (v1, v2) -> v1));
    }

    /**
     * 根据变更单ids和分包商变更单ids 查询变更单和分包商变更单
     */
    public static Map<String, IChangeOrder> queryChangeOrders(List<String> changeOrderIds, List<String> subChangeOrderIds) {
        List<ChangeOrder> changeOrderList = ChangeOrderAbility.get(changeOrderIds, ChangeOrder.class);
        List<SubcontractorChangeOrder> subcontractorChangeOrders = ChangeOrderAbility.get(subChangeOrderIds, SubcontractorChangeOrder.class);
        List<IChangeOrder> iChangeOrders = changeOrderList.stream()
                .map(ChangeOrder.class::cast)
                .collect(Collectors.toList());
        List<IChangeOrder> iSubChangeOrders = subcontractorChangeOrders.stream()
                .map(SubcontractorChangeOrder.class::cast)
                .collect(Collectors.toList());
        iChangeOrders.addAll(iSubChangeOrders);
        return iChangeOrders.stream().collect(Collectors.toMap(IChangeOrder::getId, item -> item, (v1, v2) -> v1));
    }

    public static ClockInTask getKeyTask(String batchTaskId, ClockInTaskTypeEnum taskType, String... fields) {
        if (taskType == ClockInTaskTypeEnum.OPERATION) {
            return QueryDataHelper.queryOne(
                    ClockInTask.class,
                    Lists.newArrayList(fields),
                    Lists.newArrayList(
                            new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(N)),
                            new Filter(BATCH_TASK_ID, Comparator.EQ, batchTaskId),
                            new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(taskType)),
                            new Filter(OPERATOR_ROLES, Comparator.CONTAINS, Lists.newArrayList(OPERATING_SUPERVISOR.getValue()))));
        }

        if (taskType == ClockInTaskTypeEnum.ON_DUTY) {
            return QueryDataHelper.queryOne(
                    ClockInTask.class,
                    Lists.newArrayList(fields),
                    Lists.newArrayList(
                            new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(N)),
                            new Filter(BATCH_TASK_ID, Comparator.EQ, batchTaskId),
                            new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(taskType))));
        }

        throw new IllegalArgumentException("taskType");
    }
    public static List<ClockInTask> getKeyTasks(List<String> batchTaskIds, ClockInTaskTypeEnum taskType, String... fields) {
        if (taskType == ClockInTaskTypeEnum.OPERATION) {
            // 操作任务根据批次任务ID查询，应该只有一条是操作负责人的打卡任务
            return QueryDataHelper.query(
                    ClockInTask.class,
                    Lists.newArrayList(fields),
                    Lists.newArrayList(
                            new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(N)),
                            new Filter(BATCH_TASK_ID, Comparator.IN, batchTaskIds),
                            new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(taskType)),
                            new Filter(OPERATOR_ROLES, Comparator.CONTAINS, Lists.newArrayList(OPERATING_SUPERVISOR.getValue()))));
        }

        if (taskType == ClockInTaskTypeEnum.ON_DUTY) {
            // 同一个批次任务ID，应该只有一条值守任务
            return QueryDataHelper.query(
                    ClockInTask.class,
                    Lists.newArrayList(fields),
                    Lists.newArrayList(
                            new Filter(BATCH_TASK_ID, Comparator.IN, batchTaskIds),
                            new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(taskType))));
        }

        throw new IllegalArgumentException("taskType");
    }

    public static ClockInTaskDetailVO getClockInTaskDetail(String clockInTaskId) {
        ClockInTask clockInTask = QueryDataHelper.get(
                ClockInTask.class,
                new ArrayList<>(),
                clockInTaskId);
        if (clockInTask == null) {
            throw new LcapBusiException(TASK_NOT_EXISTS);
        }

        return buildVo(Lists.newArrayList(clockInTask), clockInTaskId).get(INTEGER_ZERO);
    }

    /**
     * 重构，查询主表为clcok_in_task表；将权限查询修改为子查询；
     */
    public PageRows<ClockInTaskDetailVO> getRelatedTasks(RelatedClockInTaskQueryDTO queryDTO) {
        List<IFilter> iFilterList = Lists.newArrayList();
        // 查询条件——产品分类，展示到第二层
        addProductsFilters(queryDTO.getProductList(), iFilterList);
        // 查询条件——打卡状态
        addStatusFilter(queryDTO.getCheckInStatusList(), iFilterList);
        // 查询条件——计划开始结束时间
        addOperationTimeFilter(queryDTO.getOperationStartTime(), queryDTO.getOperationEndTime(), iFilterList);
        // 查询条件——操作等级
        addOperationLevelFilter(queryDTO.getOperationLevel(), iFilterList);
        // 查询条件——代表处，与web端展示维度一致
        addOfficeFilter(queryDTO.getRepresentativeOfficeList(), iFilterList);
        // 查询条件——Nis网络ID列表
        addNetworkIdFilter(queryDTO.getCustomerNetworkIdList(), iFilterList);
        // 查询条件——操作主题
        addOperationSubjectFilter(queryDTO.getOperationSubject(), iFilterList);
        // 查询条件——操作负责人工号
        addResponsiblePersonFilter(queryDTO.getPersonInChargeOfOperationList(), iFilterList);
        // 查询条件——打卡人工号
        addClockInPersonFilter(queryDTO.getClockInPerson(), iFilterList);
        // 查询条件——批次任务id列表
        addBatchTaskIdFilter(queryDTO.getBatchTaskIdList(), iFilterList);
        // 增加数据权限控制
        iFilterList.add(userPermissionFilter(ContextHelper.getEmpNo()));
        // 不返回已废止状态的打卡任务
        iFilterList.add(new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(N)));
        // 查询打卡任务表
        PageRows<ClockInTaskDetailVO> pageRows = new PageRows<>();
        Range range = new Range(queryDTO.getPageNum(), queryDTO.getPageSize());
        OrderBy checkInOrderBy = new OrderBy(LAST_CHECK_IN_UTC_TIME_BATCH_TASK, DESC);
        OrderBy modifyTimeOrderBy = new OrderBy(LAST_MODIFIED_TIME, DESC);
        List<ClockInTask> tasks = QueryDataHelper.query(
                ClockInTask.class,
                CLOCK_IN_DETAIL_FIELDS,
                iFilterList,
                range,
                checkInOrderBy,
                modifyTimeOrderBy);
        int totalCount = QueryDataHelper.queryCount(ClockInTask.class, iFilterList);
        List<ClockInTaskDetailVO> rows = buildVo(tasks, null);
        pageRows.setRows(rows);
        pageRows.setCurrent(queryDTO.getPageNum());
        pageRows.setTotal(totalCount);
        return pageRows;
    }

    @Override
    public IFilter getRelationPermissionFilter(String userId) {
        // 1. 查询指定人员相关的变更任务单号
        String subQuerySql =
                String.format("select cit.id from clock_in_task cit"
                                + " where cit.change_order_id in"
                                + " (select ass.entity_id from assignment_person_relevance apr"
                                + " inner join assignment ass on apr.assignment_id = ass.id"
                                + " inner join assignment_network_change_ex ance on ass.id = ance.id"
                                + " where apr.relevant = '%s'"
                                + " and ance.create_clock_in_task = 1"
                                + " and apr.is_deleted = 0"
                                + " and ass.is_deleted = 0"
                                + " and json_overlaps(ass.assignment_type_ext, '[\"%s\", \"%s\"]'))",
                        userId, AssignmentTypeEnum.NETWORK_CHANGE.getValue(),
                        AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue());

        // 2.组装子查询
        return new SubQueryFilter(new Filter(ID, Comparator.IN, Arrays.asList(INTEGER_ONE)), subQuerySql);
    }

    private static void addBatchTaskIdFilter(List<String> batchTaskIdList, List<IFilter> iFilterList) {
        if (CollectionUtils.isEmpty(batchTaskIdList)) {
            return;
        }
        IFilter batchTaskIdFilter = new Filter(BATCH_TASK_ID, Comparator.IN, batchTaskIdList);
        iFilterList.add(batchTaskIdFilter);
    }

    private static void addClockInPersonFilter(String clockInPerson, List<IFilter> iFilterList) {
        if (StringUtils.isEmpty(clockInPerson)) {
            return;
        }

        IFilter filter = new Filter(OPERATOR, Comparator.IN, Lists.newArrayList(clockInPerson));

        iFilterList.add(filter);
    }

    private static void addResponsiblePersonFilter(List<String> personInChargeOfOperationList, List<IFilter> iFilterList) {
        if (CollectionUtils.isEmpty(personInChargeOfOperationList)) {
            return;
        }

        IFilter filter = new Filter(OPERATOR_ROLES, Comparator.CONTAINS, Lists.newArrayList(OPERATING_SUPERVISOR.getValue()));
        filter.and(new Filter(OPERATOR, Comparator.IN, personInChargeOfOperationList));
        iFilterList.add(filter);
    }

    private static void addOperationSubjectFilter(String operationSubject, List<IFilter> iFilterList) {
        if (StringUtils.isEmpty(operationSubject)) {
            return;
        }
        String nameCondition = PERCENT + operationSubject + PERCENT;
        IFilter operationSubjectFilter = new Filter(OPERATION_SUBJECT, Comparator.LIKE, nameCondition);
        iFilterList.add(operationSubjectFilter);
    }

    private void addNetworkIdFilter(List<String> customerNetworkIdList, List<IFilter> iFilterList) {
        if (CollectionUtils.isEmpty(customerNetworkIdList)) {
            return;
        }
        String networkIdStr = list2String(customerNetworkIdList);
        String subQuerySql = String.format(
                "select cit.id from clock_in_task cit"
                        + " where cit.change_order_id in"
                        + " (select ass.entity_id"
                        + " from assignment ass"
                        + " where json_overlaps(ass.network_ext, '[%s]')"
                        + " and json_overlaps(ass.assignment_type_ext, '[\"%s\", \"%s\"]'))",
                networkIdStr,
                AssignmentTypeEnum.NETWORK_CHANGE.getValue(),
                AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue());
        IFilter networkIdFilter = new SubQueryFilter(new Filter(
                                            ID,
                                            Comparator.IN,
                                            Arrays.asList(INTEGER_ONE)
                                    ), subQuerySql);
        iFilterList.add(networkIdFilter);
    }

    private void addOfficeFilter(List<SelectedOffice> representativeOfficeList, List<IFilter> iFilterList) {
        if (CollectionUtils.isEmpty(representativeOfficeList)) {
            return;
        }
        List<String> offices = new TreeNodeHelper().getOfficeBottomPaths(representativeOfficeList, NisClient.queryOrganizationTree());
        // 2.包装过滤条件
        IFilter representativeOfficeFilter = new Filter(RESPONSIBLE_DEPT, Comparator.IN, offices);
        iFilterList.add(representativeOfficeFilter);
    }

    private static void addOperationLevelFilter(String operationLevel, List<IFilter> iFilterList) {
        if (StringUtils.isEmpty(operationLevel)) {
            return;
        }
        IFilter operationLevelFilter = new Filter(OPERATION_LEVEL, Comparator.EQ, Lists.newArrayList(operationLevel));
        iFilterList.add(operationLevelFilter);
    }

    @SneakyThrows
    private static void addOperationTimeFilter(String operationStartTime, String operationEndTime, List<IFilter> iFilterList) {
        if (StringUtils.isEmpty(operationStartTime) || StringUtils.isEmpty(operationEndTime)) {
            return;
        }
        // 调整为当两个时间段存在交集时返回，[A, B]，[C, D] 两个时间段存在交集的条件：A <= D && B >= C
        Date startDate = DateUtils.stringToDate(operationStartTime, SIMPLE_DATE_FORM);
        Date endDate = DateUtils.stringToDate(operationEndTime, SIMPLE_DATE_FORM);
        String startDateStr = DateUtils.dateToString(startDate, DATE_FORM);
        String endDateStr = DateUtils.dateToString(endDate, DATE_FORM);
        String formatSql = String.format("select cit.id from clock_in_task cit "
                        + "where cit.batch_task_id in "
                        + "(select id from batch_network_assignment "
                        + "where is_deleted = 0 "
                        + "and plan_operation_start_time < '%s' "
                        + "and plan_operation_end_time >= '%s' "
                        + "union select id from subcontractor_batch_task "
                        + "where is_deleted = 0 "
                        + "and plan_operation_start_time < '%s' "
                        + "and plan_operation_end_time >= '%s')",
                endDateStr, startDateStr,
                endDateStr, startDateStr);
        SubQueryFilter childFilter=new SubQueryFilter(new Filter(
                ID,
                Comparator.IN,
                Arrays.asList(INTEGER_ONE)
        ), formatSql);

        iFilterList.add(childFilter);
    }

    private static void addStatusFilter(List<String> checkInStatusList, List<IFilter> iFilterList) {
        if (CollectionUtils.isEmpty(checkInStatusList)) {
            return;
        }
        IFilter statusFilter = new Filter(TASK_STATUS, Comparator.IN, checkInStatusList);
        if (!checkInStatusList.contains(OPERATION_EXECUTION.name())) {
            iFilterList.add(statusFilter);
            return;
        }
        // 若查询待操作执行状态的打卡任务
        iFilterList.add(statusFilter.or(new Filter(TASK_STATUS, Comparator.IS_NULL, null)));
    }

    private static void addProductsFilters(List<String> productList, List<IFilter> iFilterList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        // 根据二层fullIdPath得到其叶子节点的fullIdPath；
        List<String> products = findBottomPaths(NisClient.queryProductTree(ProdCateLvlConsts.SUB_CATEGORY), productList);
        IFilter productsFilter = new Filter(PRODUCT_CLASSIFICATION, Comparator.IN, products);
        iFilterList.add(productsFilter);
    }

    public static PageRows<ClockInTaskDetailVO> queryClockInTaskList(ClockInTaskQueryDTO clockInTaskQueryDTO) {
        PageRows<ClockInTaskDetailVO> pageRows = new PageRows<>();
        // 若taskType字段为空，返回值守打卡任务与操作打卡任务
        List<IFilter> filters = Lists.newArrayList(new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(N)));
        if (null != clockInTaskQueryDTO.getTaskType()) {
            filters.add(new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(clockInTaskQueryDTO.getTaskType())));
        }
        filters.add(new Filter(OPERATOR, Comparator.CONTAINS, Lists.newArrayList(ContextHelper.getEmpNo())));
        // 移除已结束状态的任务
        List<String> statusFileterList = Arrays.asList(
                ClockInOptionEnum.TEST_SUCCESS_OPERATION_END.getLookupCode(),
                ClockInOptionEnum.TEST_FAILED_ROLLBACK_END.getLookupCode(),
                ClockInOptionEnum.TEST_FAILED_NO_ROLLBACK.getLookupCode(),
                ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_END.getLookupCode(),
                ClockInOptionEnum.CANCEL.getLookupCode());
        Filter statusFilter = new Filter(TASK_STATUS, Comparator.NOT_IN, statusFileterList);
        statusFilter.or(new Filter(TASK_STATUS, Comparator.IS_NULL, null));
        filters.add(statusFilter);
        Range range = new Range(clockInTaskQueryDTO.getPageNum(), clockInTaskQueryDTO.getPageSize());
        List<ClockInTask> queryList = QueryDataHelper.query(ClockInTask.class, CLOCK_IN_DETAIL_FIELDS, filters, range, new OrderBy(LAST_MODIFIED_TIME, OrderEnum.DESC));
        List<ClockInTaskDetailVO> rows = buildVo(queryList, null);
        int totalCount = QueryDataHelper.queryCount(ClockInTask.class, filters);
        pageRows.setRows(rows);
        pageRows.setCurrent(rows.size());
        pageRows.setTotal(totalCount);
        return pageRows;
    }

    public static PageRows<BatchInfoVO> getBatchInfoByBatchNo(String keyword, int pageNo, int pageSize) {
        // 1.操作单号过滤条件
        String codeCondition = PERCENT + keyword + PERCENT;
        IFilter operationCodeFilter = new Filter(ASSIGNMENT_CODE, Comparator.LIKE, codeCondition);
        // 2.仅查询批次任务维度单号
        List<String> batchTaskTypes = Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getValue(),
                AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.getValue());
        IFilter assignmentTypeFilter = new Filter(ASSIGNMENT_TYPE, Comparator.IN, batchTaskTypes);
        // 3.查询数据
        List<Assignment> assignments = QueryDataHelper.query(
                Assignment.class,
                Arrays.asList(ASSIGNMENT_CODE, ASSIGNMENT_NAME, ENTITY_ID),
                Lists.newArrayList(assignmentTypeFilter, operationCodeFilter));
        // 4.构造返回vo
        List<BatchInfoVO> batchTaskList = assignments
                .stream()
                .map(item -> new BatchInfoVO(){{
                    setBatchCode(item.getAssignmentCode());
                    setOperationSubject(item.getAssignmentName());
                    setBatchTaskId(item.getEntityId());
                }}).collect(Collectors.toList());
        return PageRowsUtils.list2PageRows(batchTaskList, pageNo, pageSize);
    }

    public static List<ClockInStatusVO> getClockInStatus() {
        // 查询快码
        List<LookupValue> lookupValues = LookupValueHelper.getLookupValues(
                LookupValueConstant.CLOCK_IN_OPTION_ENUM, new ArrayList<>());
        List<LookupValue> lookupValuesSort = lookupValues.stream()
                .sorted(java.util.Comparator.comparing(LookupValue::getSortId))
                .collect(Collectors.toList());
        // 添加待操作执行状态
        LookupValue lookupValue = new LookupValue();
        lookupValue.setLookupCode(OPERATION_EXECUTION.name());
        lookupValue.setMeaningCn(OPERATION_EXECUTION.getZhCn());
        lookupValue.setMeaningEn(OPERATION_EXECUTION.getEnUs());
        lookupValuesSort.add(INTEGER_ZERO, lookupValue);

        return lookupValuesSort.stream().map(item -> new ClockInStatusVO(){{
            setLookupCode(item.getLookupCode());
            setMeaningEn(item.getMeaningEn());
            setMeaningCn(item.getMeaningCn());
        }}).collect(Collectors.toList());
    }

    public static PageRows<UserVO> getUsers(ClockInUserQueryDTO queryDTO, boolean transferFlag) {
        // 1.判断当前为内部还是外部用户。根据机构前缀
        UcsUserInfo userInfo = UcsClient.getUserInfo(Lists.newArrayList(ContextHelper.getEmpNo()))
                .stream()
                .findFirst()
                .orElse(null);
        if (userInfo == null) {
            return new PageRows<UserVO>() {{
                setRows(Lists.newArrayList());
                setCurrent(queryDTO.getPageNo());
                setTotal(0);
            }};
        }

        String orgId = userInfo.getOrgId();
        // 2.内部用户
        if (StringUtils.isNotEmpty(orgId) && orgId.startsWith(ORG_PREFIX)) {
            return internalUserQueryWithSubstitution(queryDTO, transferFlag);
        }

        // 3.外部用户,查所属公司的账号
        String companyId = getCompanyIdByOrgId(orgId);
        PageRows<CompanyUserResp> pageRows = HolClient.queryPersonOutsourcingInfo(new GetOutSourcingReq() {{
                setQueryKey(queryDTO.getKeyword());
                setPage(queryDTO.getPageNo());
                setPageSize(queryDTO.getPageSize());
                setQueryVer("v1");
                setCompanyId(companyId);
            }});

        return companyUserResp2VO(pageRows);
    }

    /** 根据orgId查询对应的companyId */
    private static String getCompanyIdByOrgId(String orgId) {
        if (StringUtils.isEmpty(orgId)) {
            return orgId;
        }
        GetOrgInfoReq req = new GetOrgInfoReq();
        req.setIds(Lists.newArrayList(orgId));
        Map<String, OrgInfo> orgInfoMap = HolClient.getOrgInfo(req);
        OrgInfo orgInfo = orgInfoMap.get(orgId);
        if (orgInfo == null) {
            return orgId;
        }
        return orgInfo.getCompanyId();
    }

    public static PageRows<UserVO> internalUserQueryWithSubstitution(ClockInUserQueryDTO queryDTO, boolean transferFlag) {
        // 获取打卡转交平替接口配置，若为true表明使用uac新接口查询，否则使用uac老接口
        boolean substituteFlag = Boolean.parseBoolean(ConfigHelper
                .get("clockInTask.queryTransfer.switch", "false"));
        if (substituteFlag) {
            return internalUserQuery4Substitution(queryDTO);
        }
        return internalUserQuery(queryDTO, transferFlag);
    }

    private static PageRows<UserVO> internalUserQuery4Substitution(ClockInUserQueryDTO queryDTO) {
        List<FuzzyQueryUserResp> users = Uac30Helper.fuzzyUserQuery(queryDTO.getKeyword());
        PageRows<FuzzyQueryUserResp> resp = PageRowsUtils.list2PageRows(users, queryDTO.getPageNo(), queryDTO.getPageSize());
        return new PageRows<UserVO>() {{
            setCurrent(resp.getCurrent());
            setTotal(resp.getTotal());
            setRows(resp.getRows().stream()
                    .map(r -> new UserVO() {{
                        setUserId(r.getAccountId());
                        setUserName(r.getName());
                        setOrgId(r.getOrgId());
                        setOrgName(r.getOrgNamePath());
                    }}).collect(Collectors.toList()));
        }};
    }

    private static PageRows<UserVO> internalUserQuery(ClockInUserQueryDTO queryDTO, boolean transferFlag) {

        if (!transferFlag) {
            List<UserResp> zteUsers = Uac30Helper.zteUserQuery(queryDTO.getKeyword());
            List<UserResp> subconUsers = Uac30Helper.subconUserQuery(queryDTO.getKeyword());
            // 内部用户查询操作负责人，需同时查询内部员工与分包商员工；
            zteUsers.addAll(subconUsers);
            return userResp2VO(PageRowsUtils.list2PageRows(zteUsers, queryDTO.getPageNo(), queryDTO.getPageSize()));
        }

        List<UserResp> userList = (queryDTO.getUserType() == UserTypeEnum.ZTE) ?
                                        Uac30Helper.zteUserQuery(queryDTO.getKeyword()) :
                                        Uac30Helper.subconUserQuery(queryDTO.getKeyword());
        return userResp2VO(PageRowsUtils.list2PageRows(userList, queryDTO.getPageNo(), queryDTO.getPageSize()));
    }

    /**
     * 检索打卡任务表，检索计划呼叫的UTC时间小于当前时间，任务状态不为取消的数据
     *
     * @return List<ClockInTask>
     */
    public static List<ClockInTask> queryAllTimeOutTask(Long time) {
        Filter filter = new Filter(PLAN_CALL_UTC_TIME, Comparator.LT, time)
                // 任务状态不等于操作取消
                .and(new Filter(TASK_STATUS, Comparator.NOT_IN, Lists.newArrayList(ClockInOptionEnum.CANCEL.getLookupCode())))
                // 是否废止为否
                .and(new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(BoolEnum.N)));

        return QueryDataHelper.query(ClockInTask.class, Lists.newArrayList(), Lists.newArrayList(filter));
    }

    private static List<ClockInTaskDetailVO> buildVo(List<ClockInTask> clockInTaskList, String clockInTaskId) {
        if (CollectionUtils.isEmpty(clockInTaskList)) {
            return Lists.newArrayList();
        }

        List<ClockInTaskDetailVO> vos = new ArrayList<>();

        // NIS查询产品名称
        Map<String, String> productLineNameMap = getProductLine(clockInTaskList);
        // HR查询代表处名称
        Map<String, String> responsibleDeptMap = getResponsibleDeptMap(clockInTaskList);
        // NIS查询网络信息
        Map<String, List<NisNetwork>> networkMap = getNetWorks(clockInTaskList);
        // 查询实际值守开始时间
        Map<String, OnDutyInfo> onDutyInfoMap = getOnDutyInfos(
                clockInTaskList.stream()
                        .filter(clockInTask -> clockInTask.getTaskType() == ClockInTaskTypeEnum.ON_DUTY)
                        .collect(Collectors.toList()),
                clockInTaskId != null
        );

        for (ClockInTask clockInTask : clockInTaskList) {
            vos.add(buildTaskDetailVO(clockInTask, productLineNameMap, responsibleDeptMap, networkMap, onDutyInfoMap, clockInTaskId));
        }

        return vos;
    }

    private static ClockInTaskDetailVO buildTaskDetailVO(
            ClockInTask clockInTask,
            Map<String, String> productLineNameMap,
            Map<String, String> responsibleDeptMap,
            Map<String, List<NisNetwork>> networkMap,
            Map<String, OnDutyInfo> onDutyInfoMap,
            String clockInTaskId) {

        ClockInTaskDetailVO detailVO = new ClockInTaskDetailVO();
        detailVO.setId(clockInTask.getId());
        detailVO.setTaskType(clockInTask.getTaskType());
        detailVO.setOperationLevel(clockInTask.getOperationLevel());
        detailVO.setTaskStatus(clockInTask.getTaskStatus());

        // 产品线
        detailVO.setProductLineId(splitProductLine(clockInTask.getProductClassification()));
        detailVO.setProductLineName(
                splitProductLine(
                        productLineNameMap.get(
                                subProductLast(clockInTask.getProductClassification()))));

        detailVO.setOperationSubject(clockInTask.getOperationSubject());
        detailVO.setBatchCode(clockInTask.getBatchCode());
        if (clockInTask.getTimeZone() != null) {
            LookupValue lookupValue = LookupValueHelper.getLookupValue(
                    LookupValueConstant.TIME_ZONE_ENUM,
                    clockInTask.getTimeZone().getLookupCode());
            if (Objects.nonNull(lookupValue) && StringUtils.isNotBlank(lookupValue.getMeaningEn())) {
                detailVO.setTimeZone(
                        lookupValue.getMeaningEn().replaceFirst(TIME_ZONE_PREFIX_UTC, TIME_ZONE_PREFIX));
            }
        }
        detailVO.setOperatorId(clockInTask.getOperator().getId());
        detailVO.setOperatorName(ContextHelper.getLangId().equals(ZH_CN) ?
                clockInTask.getOperator().getEmpName() : clockInTask.getOperator().getEmpNameEn());

        // 代表处名称
        String responsibleDeptId = splitResponsibleDeptId(clockInTask.getResponsibleDept());
        detailVO.setResponsibleDeptId(responsibleDeptId);
        detailVO.setResponsibleDeptName(responsibleDeptMap.get(responsibleDeptId));

        // 批次任务表中查询操作对象结果 networks
        detailVO.setNetworks(networkMap.get(clockInTask.getChangeOrderId()));

        if (clockInTaskId != null) {
            getDetailProp(detailVO, clockInTask, clockInTaskId);
        }

        setPlanTimes(detailVO, clockInTask);

        if (clockInTask.getTaskType() == ClockInTaskTypeEnum.ON_DUTY) {
            setOnDutyInfo(detailVO, clockInTask, onDutyInfoMap);
        }

        detailVO.setInternalFlag(STR_ZERO);
        if (DeptTypeEnum.INNER == getDeptType(clockInTask)) {
            detailVO.setInternalFlag(STR_ONE);
        }

        setStartFlags(detailVO, clockInTask);

        return detailVO;
    }

    /** 判断是否可操作、可值守 */
    private static void setStartFlags(ClockInTaskDetailVO detailVO, ClockInTask clockInTask) {
        Date now = new Date();
        if (detailVO.getPlanPrepareStartTime() != null) {
            Date planPrepareStartTime = clockInTask.getTimeZone().fix(detailVO.getPlanPrepareStartTime());
            if (now.getTime() >= planPrepareStartTime.getTime()) {
                detailVO.setOperationStartFlag(true);
            }
        }
        if (detailVO.getActualOnDutyStartTime() == null) {
            return;
        }
        Date actualOnDutyStartTime = clockInTask.getTimeZone().fix(detailVO.getActualOnDutyStartTime());
        if (now.getTime() >= actualOnDutyStartTime.getTime()) {
            detailVO.setOnDutyStartFlag(true);
        }
    }

    public static DeptTypeEnum getDeptType(ClockInTask clockInTask) {
        if (clockInTask.getResponsibleDept() == null) {
            return null;
        }

        if (Arrays.stream(INNER_OFFICE_ORG_CODE_PATH)
                .anyMatch(target -> clockInTask.getResponsibleDept().startsWith(target))) {
            return DeptTypeEnum.INNER;
        }

        return DeptTypeEnum.INTER;
    }

    private static void setPlanTimes(ClockInTaskDetailVO detailVO, ClockInTask clockInTask) {
        detailVO.setPlanPrepareStartTime(clockInTask.getPlanPrepareStartTime());
        detailVO.setPlanOnDutyStartTime(clockInTask.getPlanOnDutyStartTime());
        detailVO.setPlanPrepareEndTime(clockInTask.getPlanPrepareEndTime());
        detailVO.setPlanExecuteStartTime(clockInTask.getPlanExecuteStartTime());
        detailVO.setPlanExecuteEndTime(clockInTask.getPlanExecuteEndTime());
        detailVO.setPlanTestStartTime(clockInTask.getPlanTestStartTime());
        detailVO.setPlanTestEndTime(clockInTask.getPlanTestEndTime());
        detailVO.setPlanOnDutyEndTime(clockInTask.getPlanOnDutyEndTime());
    }

    private static void setOnDutyInfo(ClockInTaskDetailVO detailVO, ClockInTask clockInTask, Map<String, OnDutyInfo> onDutyInfoMap) {
        OnDutyInfo onDutyInfo = onDutyInfoMap.get(clockInTask.getId());
        if (onDutyInfo != null) {
            detailVO.setActualOnDutyStartTime(onDutyInfo.operationEndTime);
            if (CollectionUtils.isNotEmpty(onDutyInfo.onDutyStageTimes)) {
                detailVO.setActualOnDutyEndTime(onDutyInfo.onDutyStageTimes.get(onDutyInfo.onDutyStageTimes.size() - 1).getEndTimeDate());
                detailVO.setOnDutyStageTime(stageTime2Vo(onDutyInfo.getOnDutyStageTimes()));
            }
        }
    }

    private static List<OnDutyStageTimeVO> stageTime2Vo(List<OnDutyStageTime> onDutyStageTime) {
        List<OnDutyStageTimeVO> onDutyStageTimes = new ArrayList<>();
        for (OnDutyStageTime dutyStageTime : onDutyStageTime) {
            OnDutyStageTimeVO stageTimeVO = new OnDutyStageTimeVO();
            stageTimeVO.setStageNumber(dutyStageTime.getStageNumber());
            stageTimeVO.setStartTime(DateUtils.dateToString(dutyStageTime.getStartTimeDate(), DATE_FORM));
            stageTimeVO.setEndTime(DateUtils.dateToString(dutyStageTime.getEndTimeDate(), DATE_FORM));
            onDutyStageTimes.add(stageTimeVO);
        }
        return onDutyStageTimes;
    }

    private static void getDetailProp(ClockInTaskDetailVO detailVO, ClockInTask clockInTask, String clockInTaskId) {
        detailVO.setRevocable(new ClockInRevokeAbility(clockInTaskId).revocable());
        // 批次任务表中查询操作人员列表  operators
        Class<? extends BaseSubEntity> operatorType = clockInTask.getEntityType() == BATCH_TASK
                ? BatchTaskOperator.class : SubcontractorBatchOperator.class;
        List<BatchTaskOperator> operatorList = OperatorAbility.getBatchOperators(clockInTask.getBatchTaskId(), operatorType);
        // 从快码中查操作类型对应的值
        LookupValue lookupValue = Objects.requireNonNull(LookupValueHelper.getLookupValue(
                LookupValueConstant.OPERATE_TYPE_ENUM, clockInTask.getOperationType()));
        detailVO.setOperationType(ZH_CN.equals(RequestHeaderUtils.getLangId()) ? lookupValue.getMeaningCn() : lookupValue.getMeaningEn());
        Map<String, ClockInOperatorVO> operators = new HashMap<>();
        for (BatchTaskOperator orig : operatorList) {
            ClockInOperatorVO vo = operators.getOrDefault(orig.getOperatePerson().getId(), new ClockInOperatorVO());
            vo.setUserId(orig.getOperatePerson().getId());
            vo.setUserName(ZH_CN.equals(RequestHeaderUtils.getLangId()) ? orig.getOperatePerson().getEmpNameCn() : orig.getOperatePerson().getEmpNameEn());
            vo.setPhoneNum(orig.getOperatorPhone());
            List<String> role = vo.getRole() == null ? new ArrayList<>() : vo.getRole();
            role.add(ZH_CN.equals(RequestHeaderUtils.getLangId()) ? orig.getOperatorRole().getZhCn() : orig.getOperatorRole().getEnUs());
            vo.setRole(role);
            operators.put(orig.getOperatePerson().getId(), vo);
        }
        detailVO.setOperators(new ArrayList<>(operators.values()));

        // records 打卡记录列表
        String[] clockInRecordFields = {CLOCK_IN_OPTION, CLOCK_IN_TIME,
                DESCRIPTION, PHOTOS, CREATE_BY, LONGITUDE, LATITUDE};
        List<ClockInRecord> clockInRecords = getClockInRecords(clockInTaskId, clockInRecordFields);
        // 如果是值守任务，查询此批次任务对应的操作负责人的操作打卡任务
        if (clockInTask.getTaskType() == ClockInTaskTypeEnum.ON_DUTY) {
            ClockInTask operationClockInTask = QueryDataHelper.queryOne(ClockInTask.class, Lists.newArrayList(),
                    Lists.newArrayList(
                            new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(N)),
                            new Filter(BATCH_TASK_ID, Comparator.EQ, clockInTask.getBatchTaskId()),
                            new Filter(OPERATOR_ROLES, Comparator.CONTAINS, Lists.newArrayList(OPERATING_SUPERVISOR.getValue())),
                            new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(ClockInTaskTypeEnum.OPERATION))));
            Objects.requireNonNull(operationClockInTask);
            List<ClockInRecord> operationClockInRecords = getClockInRecords(operationClockInTask.getId(),
                    clockInRecordFields);
            clockInRecords.addAll(operationClockInRecords);
        }
        Map<String, String> userIdPhoneNumMap = detailVO.getOperators().stream()
                .collect(Collectors.toMap(
                        ClockInOperatorVO::getUserId,
                        o -> o.getPhoneNum() == null
                                ? EMPTY_STRING : o.getPhoneNum()));
        List<String> userIds = clockInRecords.stream().map(ClockInRecord::getCreateBy).collect(Collectors.toList());
        // UCS - 检索用户信息
        List<UcsUserInfo> userInfoList = UcsClient.getUserInfo(userIds);
        Map<String, UcsUserInfo> userInfoMap = userInfoList.stream().collect(Collectors.toMap(UcsUserInfo::getAccountId, p -> p));
        List<ClockInRecordVO> records = new ArrayList<>();
        for (ClockInRecord clockInRecord : clockInRecords) {
            ClockInRecordVO clockInRecordVO = new ClockInRecordVO();
            clockInRecordVO.setOption(clockInRecord.getClockInOption());
            clockInRecordVO.setClockInTime(clockInRecord.getClockInTime());
            clockInRecordVO.setLongitude(clockInRecord.getLongitude());
            clockInRecordVO.setLatitude(clockInRecord.getLatitude());
            UcsUserInfo ucsUserInfo = userInfoMap.get(clockInRecord.getCreateBy());
            clockInRecordVO.setOperatorId(ucsUserInfo.getAccountId());
            clockInRecordVO.setOperatorName(ZH_CN.equals(RequestHeaderUtils.getLangId()) ? ucsUserInfo.getPersonName() : ucsUserInfo.getPersonNameEn());
            // 电话字段取操作人员列表（打卡记录里面的人都是单据的操作人员列表里面来的）
            clockInRecordVO.setOperatorPhoneNum(userIdPhoneNumMap.get(ucsUserInfo.getAccountId()));
            clockInRecordVO.setDescription(clockInRecord.getDescription());
            clockInRecordVO.setPhotos(clockInRecord.getPhotos());
            records.add(clockInRecordVO);
        }
        detailVO.setRecords(records);

        detailVO.setOnDutyFrequency(clockInTask.getOnDutyFrequency());
        detailVO.setOnDutyDurationHours(clockInTask.getTotalOnDutyDuration());
    }

    private static Map<String, List<NisNetwork>> getNetWorks(List<ClockInTask> clockInTaskList) {
        List<String> changeOrderIds = clockInTaskList.stream()
                .filter(item -> item.getEntityType() == BATCH_TASK)
                .map(ClockInTask::getChangeOrderId)
                .distinct()
                .collect(Collectors.toList());
        List<String> subChangeOrderIds = clockInTaskList.stream()
                .filter(item -> item.getEntityType() == SUBCONTRACT_BATCH_TASK)
                .map(ClockInTask::getChangeOrderId)
                .distinct()
                .collect(Collectors.toList());
        return queryNetworkByChangeOrderIds(changeOrderIds, subChangeOrderIds);
    }

    public static Map<String, List<NisNetwork>> queryNetworkByChangeOrderIds(List<String> changeOrderIds, List<String> subChangeOrderIds) {
        Map<String, List<NisNetwork>> nisNetworkMap = new HashMap<>();
        List<OperationObject> operationObjects = new ArrayList<>();
        List<SubconOperationObject> subconOperationObjects = new ArrayList<>();
        // 通过 changeOrderIds 查询操作对象结果列表
        if (CollectionUtils.isNotEmpty(changeOrderIds)) {
            operationObjects = QueryDataHelper.query(OperationObject.class, Lists.newArrayList(), changeOrderIds, Lists.newArrayList());
        }
        if (CollectionUtils.isNotEmpty(subChangeOrderIds)) {
            subconOperationObjects = QueryDataHelper.query(SubconOperationObject.class, Lists.newArrayList(), subChangeOrderIds, Lists.newArrayList());
        }
        operationObjects.addAll(subconOperationObjects);
        List<String> networkIds = operationObjects.stream()
                .map(OperationObject::getNetworkId)
                .distinct()
                .collect(Collectors.toList());
        // 再去NIS查询网络信息详情
        List<NisNetwork> networks;
        try {
            networks = NisClient.queryNetwork(networkIds);
        }catch (Exception e) {
            return nisNetworkMap;
        }
        // 组装返回
        Map<String, NisNetwork> networkMap = networks.stream().collect(Collectors.toMap(NisNetwork::getNetworkId, p -> p));
        Map<String, List<OperationObject>> operationMap = operationObjects.stream().collect(Collectors.groupingBy(OperationObject::getPid));
        for (String changeOrderId : operationMap.keySet()) {
            List<NisNetwork> nisNetworkList = new ArrayList<>();
            for (OperationObject operationObject : operationMap.get(changeOrderId)) {
                NisNetwork nisNetwork = networkMap.get(operationObject.getNetworkId());
                if (nisNetwork != null) {
                    nisNetworkList.add(nisNetwork);
                }
            }
            nisNetworkMap.put(changeOrderId, nisNetworkList);
        }
        return nisNetworkMap;
    }

    private static Map<String, String> getResponsibleDeptMap(List<ClockInTask> clockInTaskList) {
        List<String> responsibleDeptIdList = clockInTaskList
                .stream()
                .map(clockInTask -> splitResponsibleDeptId(clockInTask.getResponsibleDept()))
                .distinct()
                .collect(Collectors.toList());
        return HrClient.queryOrganizationNameInfo(responsibleDeptIdList);
    }

    private static String splitResponsibleDeptId(String responsibleDept) {
        String[] responsibleDeptAll = responsibleDept.split(FORWARD_SLASH);
        return responsibleDeptAll[responsibleDeptAll.length - INTEGER_ONE];
    }

    private static Map<String, String> getProductLine(List<ClockInTask> clockInTaskList) {
        // PRODUCT_CLASSIFICATION  切割查找产品线
        List<String> productNoList = clockInTaskList
                .stream()
                .map(clockInTask -> subProductLast(clockInTask.getProductClassification()))
                .distinct()
                .collect(Collectors.toList());
        return NisClient.queryProductPathName(productNoList);
    }

    private static String subProductLast(String productClassification) {
        return productClassification.substring(0, productClassification.length() - 1);
    }

    private static String splitProductLine(String productClassification) {
        if(StringUtils.isEmpty(productClassification)) {
            return EMPTY_STRING;
        }
        String[] prodList = productClassification.split(FORWARD_SLASH);
        return prodList[INTEGER_ZERO] + FORWARD_SLASH + prodList[INTEGER_ONE];
    }

    private static PageRows<UserVO> userResp2VO(PageRows<UserResp> resp) {
        return new PageRows<UserVO>() {{
            setCurrent(resp.getCurrent());
            setTotal(resp.getTotal());
            setRows(resp.getRows().stream()
                    .map(r -> new UserVO() {{
                        setUserId(r.getAccountId());
                        setOrgId(r.getOrgId());
                        if (ContextHelper.getLangId().equals(ZH_CN)) {
                            setUserName(r.getPersonNameCn());
                            setOrgName(r.getOrgNamePathCn());
                        } else {
                            setUserName(r.getPersonNameEn());
                            setOrgName(r.getOrgNamePathEn());
                        }
                    }}).collect(Collectors.toList()));
        }};
    }

    private static PageRows<UserVO> companyUserResp2VO(
            PageRows<CompanyUserResp> resp) {
        return new PageRows<UserVO>() {{
            setCurrent(resp.getCurrent());
            setTotal(resp.getTotal());
            setRows(resp.getRows().stream()
                    .map(r -> new UserVO() {{
                        setUserId(r.getAccountId());
                        setUserName((ContextHelper.getLangId().equals(ZH_CN) ? r.getEmpName() : r.getEmpNameEn()));
                        setOrgId(r.getCompanyId());
                        setOrgName(r.getCompanyName());
                    }}).collect(Collectors.toList()));
        }};
    }

    private static void dealErrorHandling(OnDutyInfo onDutyInfo, List<ClockInRecord> clockInRecords, ClockInTask onDutyTask) {
        // 计算每个值守阶段时间
        calculateStageTime(onDutyInfo, clockInRecords, onDutyTask);
    }

    private static void calculateStageTime(OnDutyInfo onDutyInfo, List<ClockInRecord> clockInRecords, ClockInTask onDutyTask) {
        List<ClockInRecord> sortClockInRecords = clockInRecords.stream()
                .sorted(java.util.Comparator.comparing(ClockInRecord::getClockInTime))
                .collect(Collectors.toList());
        List<OnDutyStageTime> stageTimes = new ArrayList<>();
        // 计算正常值守阶段时间（秒）
        double totalDurationSecond = onDutyTask.getTotalOnDutyDuration() * 60 * 60;
        double intervalSecond = totalDurationSecond / onDutyTask.getOnDutyFrequency();
        // 值守一开始的时间（第一次打卡使用此时间，后面打卡仅复用 calendar 对象）
        Calendar calendarTemp = Calendar.getInstance();
        calendarTemp.setTime(onDutyInfo.getOperationEndTime());
        // 恢复卡标识（下一个卡是不是恢复卡）
        boolean restoreFlag = false;
        // 阶段结束标识（当前值守阶段是否已值守结束）
        boolean stageEndFlag = false;
        for (ClockInRecord record : sortClockInRecords) {
            if (stageTimes.size() < onDutyTask.getOnDutyFrequency()) {
                // 根据不同打卡项增加阶段记录
                stageEndFlag = addStageTimes(stageTimes, calendarTemp, intervalSecond, record, restoreFlag);
            }
            // 如果是恢复卡（只有恢复卡需要重置值守时间）
            if (restoreFlag) {
                OnDutyStageTime prevDutyStageTime = stageTimes.get(stageTimes.size() - 1);
                calendarTemp.setTime(record.getClockInTime());
                calendarTemp.add(Calendar.SECOND, (int) intervalSecond);
                prevDutyStageTime.setEndTimeDate(calendarTemp.getTime());
                restoreFlag = false;
                stageEndFlag = false;
            } else {
                restoreFlag = record.getClockInOption() == ClockInOptionEnum.INDICATOR_ERROR_HANDLING;
            }
        }
        if (CollectionUtils.isNotEmpty(stageTimes) && !stageEndFlag) {
            onDutyInfo.lastOnDutyStage = stageTimes.get(stageTimes.size() - 1);
        }
        // 补充未打卡的阶段记录
        fillStageTimes(stageTimes, onDutyTask, intervalSecond, onDutyInfo, calendarTemp);
        onDutyInfo.onDutyStageTimes.addAll(stageTimes);
    }

    private static void fillStageTimes(List<OnDutyStageTime> stageTimes, ClockInTask onDutyTask, double intervalSecond, OnDutyInfo onDutyInfo, Calendar calendar) {
        int alreadyOnDutyCount = stageTimes.size();
        if (alreadyOnDutyCount >= onDutyTask.getOnDutyFrequency()) {
            onDutyInfo.lastOnDutyStage = stageTimes.get(stageTimes.size() - 1);
            return;
        }
        for (int i = 0; i < onDutyTask.getOnDutyFrequency() - alreadyOnDutyCount; i++) {
            if (CollectionUtils.isNotEmpty(stageTimes)) {
                OnDutyStageTime prevDutyStageTime = stageTimes.get(stageTimes.size() - 1);
                stageTimes.add(handleOtherClock(calendar, intervalSecond, null, prevDutyStageTime));
                // 如果已打卡的最后一个阶段已经结束的话，最近一次需要值守的阶段打卡计划取这里未打卡阶段的第一个
                if (onDutyInfo.lastOnDutyStage == null && i == 0) {
                    onDutyInfo.lastOnDutyStage = stageTimes.get(stageTimes.size() - 1);
                }
            } else {
                // 还未开始打值守卡的情况
                stageTimes.add(handleFirstClock(calendar, intervalSecond, null));
                // 第一个阶段就是目前需要值守的阶段
                onDutyInfo.lastOnDutyStage = stageTimes.get(0);
            }
        }
    }

    private static Boolean addStageTimes(List<OnDutyStageTime> stageTimes, Calendar calendar, double intervalSecond, ClockInRecord record, Boolean restoreFlag) {
        boolean stageEndFlag;
        if (CollectionUtils.isEmpty(stageTimes)) {
            // 第一次打卡
            OnDutyStageTime stageTime = handleFirstClock(calendar, intervalSecond, record);
            stageTimes.add(stageTime);
            // 正常的继续值守打卡，需要在阶段结束时间后打卡才算阶段结束
            stageEndFlag = record.getClockInOption() == ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE
                    && record.getClockInTime().compareTo(stageTime.getEndTimeDate()) > 0;
        } else {
            // 不是第一次打卡
            OnDutyStageTime prevDutyStageTime = stageTimes.get(stageTimes.size() - 1);
            // 正常的继续值守打卡（非恢复卡），需要在阶段结束时间后打卡才算阶段结束
            stageEndFlag = !restoreFlag && record.getClockInOption() == ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE
                    && record.getClockInTime().compareTo(prevDutyStageTime.getEndTimeDate()) > 0;
            if (stageEndFlag) {
                // 上一个阶段已结束
                stageTimes.add(handleOtherClock(calendar, intervalSecond, record, prevDutyStageTime));
            } else {
                /*
                 * 上一个阶段未结束并且是正常卡而不是恢复卡
                 * （上一个阶段未结束说明，上一个卡不是正常卡，要么是异常卡、要么是恢复卡，那当前的卡要么是恢复卡、要么是恢复之后的正常卡，此处处理恢复卡之后正常卡的逻辑,
                 *  恢复卡在上面最后处理，只有恢复卡需要重置值守时间）
                 */
                prevDutyStageTime.getStageRecords().add(record);
            }
        }
        return stageEndFlag;
    }

    private static OnDutyStageTime handleOtherClock(Calendar calendar, double intervalSecond, ClockInRecord record, OnDutyStageTime prevDutyStageTime) {
        OnDutyStageTime stageTime = new OnDutyStageTime();
        stageTime.setStageNumber(prevDutyStageTime.getStageNumber() + 1);
        if (record != null) {
            stageTime.getStageRecords().add(record);
        }
        stageTime.setStartTimeDate(prevDutyStageTime.getEndTimeDate());
        calendar.setTime(stageTime.getStartTimeDate());
        calendar.add(Calendar.SECOND, (int) intervalSecond);
        stageTime.setEndTimeDate(calendar.getTime());
        return stageTime;
    }

    private static OnDutyStageTime handleFirstClock(Calendar calendar, double intervalSecond, ClockInRecord record) {
        OnDutyStageTime stageTime = new OnDutyStageTime();
        stageTime.setStageNumber(1);
        stageTime.setStartTimeDate(calendar.getTime());
        calendar.add(Calendar.SECOND, (int) intervalSecond);
        stageTime.setEndTimeDate(calendar.getTime());
        stageTime.getStageRecords().add(record);
        return stageTime;
    }

    /**
     * inet打卡查询
     *
     * @param queryDTO inetClockInQueryDTO
     * @return PageRows<InetClockInVO>
     */
    public static PageRows<InetClockInVO> queryInetClockIn(InetClockInQueryDTO queryDTO) {
        // 1.第一次查询ids
        Pair<List<String>, Long> firstIdsQuery = IcosClient.firstIdsQuery(queryDTO);
        if (firstIdsQuery == null) {
            return new PageRows<InetClockInVO>() {{
                setRows(null);
                setCurrent(queryDTO.getPageNum());
                setTotal(0);
            }};
        }
        // 2.第二次查询具体的数据
        List<InetClockInResultDTO> inetClockInResultDTOList = IcosClient.queryClockInTask(firstIdsQuery.getLeft());

        return transferInetClockInVO(inetClockInResultDTOList,queryDTO.getPageNum(),firstIdsQuery.getRight());
    }

    private static PageRows<InetClockInVO> transferInetClockInVO(
            List<InetClockInResultDTO> inetClockInResults,
            int page,
            Long total) {
        PageRows<InetClockInVO> pageRows = new PageRows<>();
        List<InetClockInVO> resultList = new ArrayList<>();

        if (CollectionUtils.isEmpty(inetClockInResults)) {
            return new PageRows<InetClockInVO>() {{
                setRows(null);
                setCurrent(page);
                setTotal(0);
            }};
        }
        // 操作项查快码改成批次查
        List<String> clockInOptionList = inetClockInResults.stream()
                .map(InetClockInResultDTO::getClockInOption)
                .filter(StringUtils::isNotEmpty)
                .distinct().collect(Collectors.toList());

        Map<String, String> lookupValueMap = MapUtils.newHashMap();
        if (CollectionUtils.isNotEmpty(clockInOptionList)) {
            List<LookupValue> lookupValues = LookupValueHelper.getLookupValues(ZH_CN, LookupValueConstant.CLOCK_IN_OPTION_ENUM, clockInOptionList);
            lookupValueMap = lookupValues.stream().collect(Collectors.toMap(LookupValue::getLookupCode, LookupValue::getMeaningCn));
        }

        // 1.根据打卡任务id进行分组（默认一个批次一个打卡任务）
        Map<String, List<InetClockInResultDTO>> groupedByChangeOrder = inetClockInResults.stream()
                .collect(Collectors.groupingBy(InetClockInResultDTO::getClockInId));

        // 2.组装数据
        for (Map.Entry<String, List<InetClockInResultDTO>> entry : groupedByChangeOrder.entrySet()) {
            InetClockInVO inetClockInVO = new InetClockInVO();
            List<InetClockInResultDTO> clockInResults = entry.getValue();
            InetClockInResultDTO inetClockInResultDTO = clockInResults.get(0);
            // 2.1 打卡任务基础信息
            BeanUtils.copyProperties(inetClockInResultDTO, inetClockInVO);
            List<String> networkNames = clockInResults.stream().map(InetClockInResultDTO::getNetworkName).distinct().collect(Collectors.toList());
            inetClockInVO.setNetworkNames(networkNames);

            // 状态（如有打卡记录 取最后一次打卡记录的打卡项）
            InetClockInResultDTO endInetClockInResultDTO = clockInResults.stream()
                    .filter(item -> item.getClockInTime() != null)
                    .min(java.util.Comparator.comparing(InetClockInResultDTO::getClockInTime)).orElse(null);
            if (endInetClockInResultDTO != null) {
                inetClockInVO.setLastClockOptionStatus(lookupValueMap.get(endInetClockInResultDTO.getClockInOption()));
            }

            // 打卡任务和打卡记录是一对多的关系，判断其中一个打卡记录id不为空，则解析角色，转为数组，组装打卡记录信息(角色字段在打卡任务表中，且不会为空)
            if (StringUtils.isNotEmpty(inetClockInResultDTO.getClockInRecordId())) {
                List<String> operatorRoles = parseStringToList(inetClockInResultDTO.getOperatorRoles());
                // 2.2 打卡记录信息
                Map<String, String> finalLookupValueMap = lookupValueMap;
                List<ClockInRecordDTO> clockInRecordDTOList = clockInResults.stream()
                        .map(item -> {
                            ClockInRecordDTO clockInRecordDTO = new ClockInRecordDTO();
                            BeanUtils.copyProperties(item, clockInRecordDTO);
                            clockInRecordDTO.setClockInOption(finalLookupValueMap.get(item.getClockInOption()));
                            // 打卡角色获取的值为""操作人员A""或["操作人员A","操作人员B"]，将他处理成集合角色
                            clockInRecordDTO.setClockInRoles(operatorRoles);
                            clockInRecordDTO.setIsRevoked(item.getIsRevoked() != null && item.getIsRevoked().equals(BoolEnum.Y.name()));
                            return clockInRecordDTO;
                        }).collect(Collectors.toList());
                inetClockInVO.setClockInRecordDTOS(clockInRecordDTOList);
            }
            resultList.add(inetClockInVO);
        }
        pageRows.setRows(resultList);
        pageRows.setCurrent(page);
        pageRows.setTotal(total);
        return pageRows;
    }

    /**
     * 解析打卡任务中的打卡角色
     * @param input 打卡角色
     * @return 数组
     */
    private static List<String> parseStringToList(String input) {
        if (input == null || input.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // 去除首尾的空白字符
        input = input.trim();

        // 检查是否是 JSON 数组格式  --左中括号和右中括号
        if (input.startsWith(MEDIUM_PARENTHESES_START) && input.endsWith(RIGHT_CORE_BRACKET)) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(input);
                List<String> list = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    list.add(jsonArray.getString(i));
                }
                return list;
            } catch (JSONException e) {
                // 如果解析失败，按普通字符串处理
                return Collections.singletonList(input);
            }
        } else {
            // 普通字符串
            return Collections.singletonList(input.replace(QUOTATION_MARKS_STRING, EMPTY_STRING));
        }
    }

    @Getter
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class OnDutyInfo {

        private Date operationEndTime;

        /**
         * 值守阶段时间（未值守的阶段是计划时间，已值守的阶段按照打卡情况有恢复卡就重置后的时间）
         */
        private final List<OnDutyStageTime> onDutyStageTimes = Lists.newArrayList();

        /**
         * 最近一次需要值守的阶段打卡计划
         */
        private OnDutyStageTime lastOnDutyStage;

    }
}
