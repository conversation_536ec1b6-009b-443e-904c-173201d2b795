package com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("移动审批 - 审批提交对象")
@Getter
@Setter
public class ApprovalSubmitDto {

    @ApiModelProperty("审批单据编号")
    private String orderNo;

    @ApiModelProperty("审批节点")
    private String approvalNode;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批意见")
    private String approvalOpinion;

    @ApiModelProperty("需要办事处产品经理审批")
    private Boolean needProductManagerApprove;

    @ApiModelProperty("办事处产品经理")
    private List<String> productManager;

    @ApiModelProperty("办事处产品经理审核组")
    private List<String> productManagerApproveTeam;

    @ApiModelProperty("需要升级至网络处")
    private Boolean needNetworkDepartmentApprove;

    @ApiModelProperty("网络处审核人")
    private List<String> networkDepartmentReviewer;

    @ApiModelProperty("网络处审核组")
    private List<String> networkDepartmentApproveTeam;
}
