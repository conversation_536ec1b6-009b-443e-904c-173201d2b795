package com.zte.iccp.itech.extension.ability.changeorder;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.*;
import com.zte.iccp.itech.extension.ability.authtask.BatchTask4AuthAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInConfigAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInTaskUpdateAbility;
import com.zte.iccp.itech.extension.ability.configuration.EmailGroupAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.*;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.authtask.CancelSceneEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchApprNodeMappStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.FlowVariantEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiProductLinkageGuarantee;
import com.zte.iccp.itech.extension.handler.approver.admin.InnerAdminHandlerImpl;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.changeorder.enums.NodeWithInlinePageEnum;
import com.zte.iccp.itech.extension.plugin.form.assignment.HyperlinkPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.idatashare.client.util.Strings;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.MainEntityType;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.core.event.PropertyChangedEvent;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.control.container.AbstractTable;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TableEditNew;
import com.zte.paas.lcap.ddm.domain.enums.OpenTypeEnum;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecordsDTO;
import com.zte.paas.lcap.ddm.domain.flow.dto.SaveBizAndStartFlowDTO;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import com.zte.paas.lcap.metadata.engine.common.constant.NumberConst;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.INNER_OFFICE_ORG_CODE_PATH;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.NET_SERVICE_SALES;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.SYSTEM_USER;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.LookupValueConstant.OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.ClockIn.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.MobileApprove.*;
import static com.zte.iccp.itech.extension.domain.constant.ProdCategoryConsts.CCN_PROD_IDPATH_KEY;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.BATCH_NO;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.OPERATION_TYPE_GROUP;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum.DIRECTOR_OF_TELECOM_SERVICES;
import static com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum.BATCH_TASK_FLOW;
import static com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW;
import static com.zte.paas.lcap.ddm.common.api.constant.ColumnConstant.P_ID;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class BatchTaskAbility {

    /* 发布通告页面 pageID*/
    private static final String NOTICE_PAGE_ID = "PAGE1011296103953911809";

    /* 分包商 发布通告页面 pageID*/
    private static final String SUBCONTRACTOR_NOTICE_PAGE_ID = "PAGE1014466079750959105";

    private static final String OPERATION_STAGE_CLOCK_IN_PROPERTY = "operation_stage_clock_in";

    private static final int INT_99999 = 99999;

    /**
     * 兼容任务中心自定义页面过滤问题
     */
    private static final String CLICK_FIELD = "textfield_tz852y9c";

    /**
     * 检索特定类型任务 - 主键
     * @param id 主键
     * @param batchTaskClass 批次任务
     * @return T BaseEntity
     * @param <T> BaseEntity
     */
    public static <T extends BaseEntity> T queryBatchAssignment(String id, Class<T> batchTaskClass) {
        if (!StringUtils.hasText(id)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList();

        Filter idFilter = new Filter(ID, Comparator.EQ, id);
        List<IFilter> conditionFilterList = Lists.newArrayList(idFilter);

        return QueryDataHelper.queryOne(batchTaskClass, fieldList, conditionFilterList);
    }

    /**
     * 查询批次任务
     * @param id
     * @param fields
     * @return BatchTask
     */
    public static BatchTask get(String id, List<String> fields) {
        return QueryDataHelper.get(BatchTask.class, fields, id);
    }

    /**
     * 查询分包商批次任务
     * @param id
     * @param fields
     * @return BatchTask
     */
    public static SubcontractorBatchTask getSub(String id, List<String> fields) {
        return QueryDataHelper.get(SubcontractorBatchTask.class, fields, id);
    }

    /**
     * 批量查询批次任务
     * @param idList
     * @param fieldList
     * @return List<BatchTask>
     */
    public static <T extends BaseEntity>  List<T> batchGet(List<String> idList, List<String> fieldList, Class<T> entityEnum) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        if (!BatchTask.class.equals(entityEnum) && !SubcontractorBatchTask.class.equals(entityEnum)) {
            return Lists.newArrayList();
        }

        return QueryDataHelper.get(entityEnum, fieldList, idList);
    }

    /**
     * 查询单据对应批次任务
     * @param changeOrderId
     * @param entityEnum
     * @param fieldList
     * @return List<BatchTask>
     */
    public static <T extends BaseEntity> List<T> batchGetByChangeOrderId(
            String changeOrderId,
            Class<T> entityEnum,
            List<String> fieldList) {
        if (!StringUtils.hasText(changeOrderId)) {
            return Lists.newArrayList();
        }

        IFilter changeOrderIdFilter = new Filter(CHANGE_ORDER_ID, Comparator.EQ, changeOrderId);
        List<IFilter> filterList = Lists.newArrayList(changeOrderIdFilter);

        return QueryDataHelper.query(entityEnum, fieldList, filterList);
    }

    /**
     * 批量新增批次任务
     * @param batchTaskList
     * @return List<String>
     */
    public static List<String> batchInsert(List<BatchTask> batchTaskList) {
        return SaveDataHelper.batchCreate(batchTaskList);
    }

    /**
     * 批量新增关联实体
     * @param entityList
     * @return List<String>
     */
    public static <T extends BaseSubEntity> List<String> batchInsertSub(List<T> entityList) {
        return SaveDataHelper.batchCreate(entityList);
    }

    /*
     * 更新批次任务的当前状态
     * */
    public static void updateCurrentStatus(String btachTaskId, AssignmentStatusEnum currentStatus) {
        Map<String, Object> values = Maps.newHashMap();
        values.put(CURRENT_STATUS, currentStatus.getValue());
        SaveDataHelper.update(BatchTask.class, btachTaskId, values);
    }

    /**
     * 批量更新批次任务
     * @param batchTaskList
     */
    public static void batchUpdate(List<BatchTask> batchTaskList) {
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }

        SaveDataHelper.batchUpdate(batchTaskList);
    }

    public static void updateHasCancelOperation(String batchTaskId,
                                                String hasCancelOperation) {
        if (!StringUtils.hasText(batchTaskId)) {
            return;
        }
        Map<String, Object> values = Maps.newHashMap();
        values.put(HAS_CANCEL_OPERATION, hasCancelOperation);
        SaveDataHelper.update(BatchTask.class, batchTaskId, values);
    }

    public static void updateHasSaveOperation(String batchTaskId, String hasSaveOperation) {
        if (!StringUtils.hasText(batchTaskId)) {
            return;
        }
        Map<String, Object> values = Maps.newHashMap();
        values.put(HAS_SAVE_OPERATION, hasSaveOperation);

        SaveDataHelper.update(BatchTask.class, batchTaskId, values);
    }

    public static void updateApplyAuthorization(String batchTaskId,
                                                Object planOperationStartTimeApplyObj,
                                                Object planOperationEndTimeApplyObj,
                                                Object batchOperationAccountApplyObj) {
        if (!StringUtils.hasText(batchTaskId)) {
            return;
        }
        Map<String, Object> values = Maps.newHashMap();
        values.put(BATCH_OPERATION_ACCOUNT_APPLY, batchOperationAccountApplyObj);
        values.put(OPERATION_START_TIME_APPLY, planOperationStartTimeApplyObj);
        values.put(OPERATION_END_TIME_APPLY, planOperationEndTimeApplyObj);
        SaveDataHelper.update(BatchTask.class, batchTaskId, values);

    }


    /**
     * 批量更新合作方批次任务
     * @param subcontractorBatchTaskList
     */
    public static void batchUpdateSub(List<SubcontractorBatchTask> subcontractorBatchTaskList) {
        if (CollectionUtils.isEmpty(subcontractorBatchTaskList)) {
            return;
        }

        SaveDataHelper.batchUpdate(subcontractorBatchTaskList);
    }

    /**
     * 根据网络变跟单ID查询批次任务
     * @param fieldList
     * @return List<BatchTask>
     */
    public static List<BatchTask> listBatchTaskByChangeOrderId(String changeOrderId, List<String> fieldList) {
        return QueryDataHelper.query(BatchTask.class, fieldList, Arrays.asList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, changeOrderId)));
    }

    /**
     * 根据网络变跟单ID列表查询批次任务
     * @param fieldList
     * @return List<BatchTask>
     */
    public static List<BatchTask> listBatchTaskByChangeOrderIdList(List<String> changeOrderIdList, List<String> fieldList) {
        return QueryDataHelper.query(BatchTask.class, fieldList, Arrays.asList(new Filter(CHANGE_ORDER_ID, Comparator.IN, changeOrderIdList)));
    }

    /**
     * 分包商根据网络变跟单ID查询批次任务
     * @param fieldList
     * @return List<BatchTask>
     */
    public static List<SubcontractorBatchTask> listSubBatchTaskByChangeOrderId(String changeOrderId, List<String> fieldList) {
        return QueryDataHelper.query(SubcontractorBatchTask.class, fieldList, Arrays.asList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, changeOrderId)));
    }

    /**
     * 根据网络变跟单ID,查询主保障操作时间最大批次任务
     */
    public static BatchTask queryByChangeOrderIdOperationTime(String changeOrderId) {
        if (!StringUtils.hasText(changeOrderId)) {
            return null;
        }
        Assignment assignment = AssignmentAbility
                .querySpecificTypeAssignment(changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, Assignment.class);
        if (assignment == null) {
            return null;
        }

        List<MultiProductLinkageGuarantee> multiProducts
                = MultiProdGuaranteeAbility.queryByAssignmentIds(Arrays.asList(assignment.getId()));
        if (CollectionUtils.isEmpty(multiProducts)) {
            return null;
        }

        List<BatchTask> batchTasks = QueryDataHelper.query(BatchTask.class,
                Lists.newArrayList(),
                Lists.newArrayList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, multiProducts.get(0).getPid())));

        if (CollectionUtils.isEmpty(batchTasks)) {
            return null;
        }

        return batchTasks.stream()
                .filter(task -> task.getOperationUpdateTime() != null)
                .max(java.util.Comparator.comparing(BatchTask::getOperationUpdateTime))
                .orElse(null);
    }

    /**
     * 创建 网络变更批次任务 + 审批流
     * @param changeOrder
     * @param batchTasks
     * @return List<String>
     */
    public static List<String> createFlowAndSave(ChangeOrder changeOrder, List<BatchTask> batchTasks) {
        // 网络处审核
        String networkApproval = BoolEnum.N.getValue();
        //网服审核
        String netServiceApproval = BoolEnum.N.getValue();
        // 是否需要电信服务总监审批
        String isDirectorApproval = DISABLED_FLAG;
        int levelNum = 0;
        int minNetworkAttribute = 0;
        int govNeedAdmin = 0;
        Integer changeOrderType = getChangeDeptType(changeOrder);
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(changeOrder.getId(), AssignmentTypeEnum.NETWORK_CHANGE, Assignment.class);
        List<Employee> responsible = assignment.getResponsibleEmployee();
        String executePerson = assignment.getCreateBy();
        // 是否算力及核心网
        BoolEnum isCCn = changeOrder.getProductCategory().startsWith(ConfigHelper.get(CCN_PROD_IDPATH_KEY)) ? BoolEnum.Y : BoolEnum.N;
        // 保障批次不需要维护审批流程变量 审核人= system
        if (changeOrder.isSecondaryGuarantyOrder()) {
            executePerson = SYSTEM_USER;
        } else {
            if (!CollectionUtils.isEmpty(responsible)) {
                executePerson = responsible.get(0).getEmpUIID();
            }
            // 查询网络变更单的审核节点，是否有过 网络和网服审核过？
            Map<ApproveNodeEnum, String> networkApprovalMap = isNetworkApproval(changeOrder.getId(), networkApproval, netServiceApproval);
            // 通过使用 name() 方法，将枚举值转换为字符串，从而确保与 networkApprovalMap 的键类型兼容
            networkApproval = networkApprovalMap.get(ApproveNodeEnum.TD_NET_DEPT_APPROVE);
            netServiceApproval = networkApprovalMap.get(ApproveNodeEnum.NET_SERVICE_DEPT_APP);
            // 是否需要电信服务总监审批
            if (OperatorEnum.isChinaTelecom(changeOrder.getCustomerTypeFlag())) {
                InnerAdminHandlerImpl innerAdminHandler = new InnerAdminHandlerImpl(DIRECTOR_OF_TELECOM_SERVICES);
                List<String> apporvals = innerAdminHandler.rewardApproverByNodeExtend(changeOrder, null);
                if (!CollectionUtils.isEmpty(apporvals)) {
                    isDirectorApproval = ENABLED_FLAG;
                }
            }
            // 推荐层级具体层级
            NodeWithInlinePageEnum nodeEnum = ApprovalAbility.getRecommendAdminApprovalLvl(changeOrder);
            levelNum = nodeEnum == null ? 0 : nodeEnum.getLevel();
            // 服务对象网络属性，NetworkAttributeEnum
            minNetworkAttribute = ApprovalAbility.getNetworkAttribute(changeOrder.getId());
            // 重要程度+风险评估的星级
            govNeedAdmin = changeOrder.getImportance().getIntegerValue() + changeOrder.getRiskEvaluation().getIntValue();
        }
        // 部门是否网络服务处
        BoolEnum isNetService = BoolEnum.valueOf(NET_SERVICE_SALES.equals(changeOrder.getResponsibleDept()));
        List<String> batchIds = new ArrayList<>();
        // 保存数据并启动流程
        for (BatchTask task : batchTasks) {
            Map<String, Object> map = JsonUtils.parseObject(task, Map.class);
            map.put(VARIABLE_NETWORK_APPROVAL, networkApproval);
            map.put(VARIABLE_NET_SERVICE_APPROVAL, netServiceApproval);
            map.put(VARIABLE_CHANGE_ORDER_TYPE, changeOrderType);
            map.put(VARIABLE_EXECUTE_PERSON, executePerson);
            map.put(FlowVariantEnum.IS_MATCH_DIR_TELE_SERVICE_ROLE.getKey(), isDirectorApproval);
            map.put(FlowVariantEnum.ADMIN_APPROVAL_RECOMMEND_LEVEL_NUM.getKey(), levelNum);
            map.put(FlowVariantEnum.IS_CCN.getKey(), isCCn.getValue());
            map.put(FlowVariantEnum.MIN_NETWORK_ATTRIBUTE.getKey(), minNetworkAttribute);
            map.put(FlowVariantEnum.GOV_ENT_NEED_ADMIN_APPROVAL_STAR_NUM.getKey(), govNeedAdmin);
            map.put(FlowVariantEnum.IS_DEPT_NET_SERVICE.getKey(), isNetService.name());

            // 流程参数
            SaveBizAndStartFlowDTO flow = new SaveBizAndStartFlowDTO();
            flow.setTenantId(ContextHelper.getTenantId());
            flow.setAppId(ContextHelper.getAppId());
            flow.setPageId(NOTICE_PAGE_ID);
            flow.setBizObjCode(EntityHelper.getEntityId(BatchTask.class));
            flow.setParams(map);
            batchIds.add(FlowServiceHelper.saveBizAndStartFlow(flow).get(BASIC_FIELD_BUSINESS_ID).toString());
        }

        return batchIds;
    }

    private static Map<ApproveNodeEnum, String> isNetworkApproval(String changeOrderId, String networkApproval, String netServiceApproval) {
        // 查询网络变更单的审核节点，是否有过 网络和网服审核过？
        ApproveRecordsDTO approveRecordsDTO = new ApproveRecordsDTO();
        approveRecordsDTO.setBizInstanceId(changeOrderId);
        approveRecordsDTO.setTenantId(ContextHelper.getTenantId());
        List<ApproveRecord> approveRecords = FlowServiceHelper.getFlowApproveRecords(approveRecordsDTO);
        for (ApproveRecord approveRecord : approveRecords) {
            String extendedCode = approveRecord.getExtendedCode();
            if (ApproveNodeEnum.TD_NET_DEPT_APPROVE.name().equals(extendedCode)) {
                networkApproval = BoolEnum.Y.getValue();
            }else if (ApproveNodeEnum.NET_SERVICE_DEPT_APP.name().equals(extendedCode)) {
                netServiceApproval = BoolEnum.Y.getValue();
            }
        }
        return MapUtils.newHashMap(
                ApproveNodeEnum.TD_NET_DEPT_APPROVE, networkApproval,
                ApproveNodeEnum.NET_SERVICE_DEPT_APP, netServiceApproval
        );
    }

    /*
     * 获取网络变更单审核类型
     */
    private static Integer getChangeDeptType(ChangeOrder changeOrder) {
        Integer changeOrderType = 0;
        DeptTypeEnum deptType = ChangeOrderAbility.getDeptType(changeOrder);
        if (DeptTypeEnum.INNER == deptType && BoolEnum.N.getValue().equals(changeOrder.getIsGovEnt().getValue())) {
            changeOrderType = 1;
        }
        if (DeptTypeEnum.INTER == deptType) {
            changeOrderType = 2;
        }
        return changeOrderType;
    }

    /*
     * 更新批次任务的审批状态
     * */
    public static void updateApprovalStatus(String btachTaskId, BoolEnum boolEnum) {
        Map<String, Object> values = Maps.newHashMap();
        values.put(APPROVAL_STATUS, boolEnum.getPropValue());
        SaveDataHelper.update(BatchTask.class, btachTaskId, values);
    }

    /**
     * 创建 分包商网络变更批次任务 + 审批流
     * @param changeOrderId
     * @param batchTaskList
     * @return List<String>
     */
    public static List<String> createSubcontractFlowAndSave(String changeOrderId, List<SubcontractorBatchTask> batchTaskList) {
        // 1.检索分包商网络变更单
        SubcontractorChangeOrder changeOrder = QueryDataHelper.get(SubcontractorChangeOrder.class, Lists.newArrayList(), changeOrderId);
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(changeOrder.getId(), AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, Assignment.class);
        List<Employee> responsible = assignment.getResponsibleEmployee();
        String executePerson = assignment.getCreateBy();
        if (!CollectionUtils.isEmpty(responsible)) {
            executePerson = responsible.get(0).getEmpUIID();
        }

        // 2.创建审批流 + 保存数据
        List<String> batchIdList = new ArrayList<>();
        for (SubcontractorBatchTask task : batchTaskList) {
            // (1) 类型转换 + 补充参数(执行人)
            Map<String, Object> taskInfo = JsonUtils.parseObject(task, Map.class);
            taskInfo.put(VARIABLE_EXECUTE_PERSON, executePerson);

            // (2) 包装审批流参数
            SaveBizAndStartFlowDTO flow = new SaveBizAndStartFlowDTO();
            flow.setTenantId(ContextHelper.getTenantId());
            flow.setAppId(ContextHelper.getAppId());
            flow.setPageId(SUBCONTRACTOR_NOTICE_PAGE_ID);
            flow.setBizObjCode(EntityHelper.getEntityId(SubcontractorBatchTask.class));
            flow.setParams(taskInfo);

            // (3) 创建审批流 + 保存实体
            JSONObject saveInfo = FlowServiceHelper.saveBizAndStartFlow(flow);
            batchIdList.add(saveInfo.getString(BASIC_FIELD_BUSINESS_ID));
        }

        return batchIdList;
    }

    /*
     * 批次 判断是否是最后一个流程，更新变跟单状态，刷新批次和变跟单当前处理人和当前节点
     * */
    public static void flowEndUpadteAllStatus(String flowCode, String changeOrderId , String batchId) {
        List<String> endStatus = Arrays.asList(AssignmentStatusEnum.CLOSE.getValue(), AssignmentStatusEnum.ABOLISH.getValue());
        int batchCount;
        AssignmentTypeEnum type = AssignmentTypeEnum.NETWORK_CHANGE;
        // 邮件使用任务类型
        AssignmentTypeEnum assignmentTypeEnum;
        List<String> status = new ArrayList<>();
        ApproveFlowCodeEnum approveFlowCodeEnum = null;
        String currentStatus = AssignmentStatusEnum.ABOLISH.getValue();
        List<BatchTask> batchTasks = new ArrayList<>();
        List<SubcontractorBatchTask> subBatchTasks = new ArrayList<>();
        List<String> changeOrderIds = new ArrayList<>();
        changeOrderIds.add(changeOrderId);
        //更新批次任务进度条step为已关闭进度3
        Map<String, BaseEntity> idMap = new LinkedHashMap<>();
        if (ApprovalConstants.BATCH_TASK_FLOW.equals(flowCode)) {
            assignmentTypeEnum = AssignmentTypeEnum.NETWORK_CHANGE_BATCH;
            batchTasks = BatchTaskAbility.listBatchTaskByChangeOrderId(changeOrderId, Arrays.asList(ID, CURRENT_STATUS));
            batchCount = batchTasks.size();
            status = batchTasks.stream()
                    .filter(item -> endStatus.contains(item.getCurrentStatus()))
                    .map(BatchTask::getCurrentStatus)
                    .collect(Collectors.toList());
            approveFlowCodeEnum = ApproveFlowCodeEnum.BATCH_TASK_FLOW;
            BatchTask batchTask = batchTasks.stream()
                    .filter(item -> batchId.equals(item.getId()))
                    .findFirst()
                    .orElse(new BatchTask());
            currentStatus = batchTask.getCurrentStatus();
            // 同步保障单任务状态
            List<String> guaranteeChangeOrderIds = ChangeOrderAbility.getGuaranteeIdsByChangeOrderId(changeOrderId);
            if (!CollectionUtils.isEmpty(guaranteeChangeOrderIds)) {
                changeOrderIds.addAll(guaranteeChangeOrderIds);
                List<BatchTask> batchTaskList = QueryDataHelper.query(BatchTask.class, Arrays.asList(ID),
                        Arrays.asList(new Filter(CHANGE_ORDER_ID, Comparator.IN, changeOrderIds)));
                if (!CollectionUtils.isEmpty(batchTaskList)) {
                    batchTasks.addAll(batchTaskList);
                }
            }
            // 处理主批次
            processTasks(batchTasks, idMap, NumberConst.FOUR);
        } else {
            //  分包商
            subBatchTasks = QueryDataHelper.query(SubcontractorBatchTask.class,
                    new ArrayList<>(), Arrays.asList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, changeOrderId)));
            batchCount = subBatchTasks.size();
            status = subBatchTasks.stream()
                    .filter(item -> endStatus.contains(item.getCurrentStatus()))
                    .map(SubcontractorBatchTask::getCurrentStatus)
                    .collect(Collectors.toList());
            type = AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE;
            assignmentTypeEnum = AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH;
            approveFlowCodeEnum = ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW;
            SubcontractorBatchTask batchTask = subBatchTasks.stream()
                    .filter(item -> batchId.equals(item.getId()))
                    .findFirst()
                    .orElse(new SubcontractorBatchTask());
            currentStatus = batchTask.getCurrentStatus();
            // 处理合作方批次
            processSubTasks(subBatchTasks, idMap, NumberConst.FOUR);
        }
        // 批次 当前进展，处理人
        if (AssignmentStatusEnum.ABOLISH.getValue().equals(currentStatus)) {
            AssignmentAbility.updateProgressByType(ApproveNodeEnum.CANCEL.name(), null, approveFlowCodeEnum, batchId);
        } else {
            AssignmentAbility.updateProgressByType(ApproveNodeEnum.CLOSE.name(), null, approveFlowCodeEnum, batchId);
        }

        // 批次流程没有全关闭 结束
        if (batchCount != status.size()) {
            return;
        }

        // 如果状态都是废止 状态为废止，否则完成
        String changeStasus = AssignmentStatusEnum.CLOSE.getValue();
        OperationSchemeStatusEnum operationStatus = OperationSchemeStatusEnum.RELEASED;
        if (status.stream().allMatch(item -> item.equals(AssignmentStatusEnum.ABOLISH.getValue()))) {
            changeStasus = AssignmentStatusEnum.ABOLISH.getValue();
            operationStatus = OperationSchemeStatusEnum.ABOLISHED;
            IdopAbility.taskCancelSendIdop(changeOrderId, ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW, IdopCancelEnum.ABOLISH);
        }
        // 更新网络变更单主状态 处理人和当前节点
        AssignmentAbility.updateStatusByChangeId(changeOrderIds, changeStasus, type);

        SaveDataHelper.batchUpdate(new ArrayList<>(idMap.values()));
        // 更新操作方案状态
        OperationSchemeAbility.update(changeOrderId, operationStatus);

        // 主任务关闭发送邮件
        AsyncExecuteUtils.execute(() -> EmailAbility.sendMainTaskCloseEmail(batchId, assignmentTypeEnum));
    }

    /*
     * 更新所有保障单批次数据
     * */
    public static void updateGuarantyBatch(BatchTask batchTask) {
        List<String> guaranteeChangeOrderIds = ChangeOrderAbility.getGuaranteeIdsByChangeOrderId(batchTask.getChangeOrderId());
        if (CollectionUtils.isEmpty(guaranteeChangeOrderIds)) {
            return;
        }

        List<BatchTask> batchTasks = QueryDataHelper.query(BatchTask.class, new ArrayList<>(),
                Arrays.asList(new Filter(CHANGE_ORDER_ID, Comparator.IN, guaranteeChangeOrderIds),
                        new Filter(BATCH_NO, Comparator.EQ, batchTask.getBatchNo())));
        if (CollectionUtils.isEmpty(batchTasks)) {
            return;
        }

        List<BatchSummary> batchSummaries = BatchSummaryAbility.listBatchSummary(guaranteeChangeOrderIds,
                Arrays.asList(ID, P_ID, BATCH_NO, NETWORK_ELEMENT_COUNT, OPERATION_ACCOUNT));
        Map<String, BatchSummary> batchSummaryMap = batchSummaries.stream()
                .collect(Collectors.toMap(BatchSummary::getPrimaryKey, Function.identity()));
        batchTasks.forEach(item -> {
            BatchSummary batchSummary = batchSummaryMap.get(String.join(UNDER_SCORE, item.getChangeOrderId(), item.getBatchNo()));
            if (batchSummary != null) {
                item.setBatchOperationAccount(batchSummary.getOperationAccount());
            }
            item.setPlanOperationStartTime(batchTask.getPlanOperationStartTime());
            item.setPlanOperationEndTime(batchTask.getPlanOperationEndTime());
            item.setOperationDescription(batchTask.getOperationDescription());
            item.setNotificationDesc(batchTask.getNotificationDesc());
            item.setCustomerVoucherFile(batchTask.getCustomerVoucherFile());
            item.setUrgentFlag(batchTask.getUrgentFlag());
            item.setUrgentResaon(batchTask.getUrgentResaon());
            item.setControlPeriodFlag(batchTask.getControlPeriodFlag());
            item.setControlPeriodReason(batchTask.getControlPeriodReason());
            item.setPlanOperationDay(batchTask.getPlanOperationDay());
            item.setOperationPlanDesc(batchTask.getOperationPlanDesc());
            item.setAttachment(batchTask.getAttachment());
            item.setRemarks(batchTask.getRemarks());
            item.clearEntityValue();
        });
        SaveDataHelper.batchUpdate(batchTasks);

        List<BatchTaskOperator> operators = QueryDataHelper.query(BatchTaskOperator.class, Arrays.asList(ID), batchTasks.get(0).getId());
        //  创建保障单批次操作人员，已经创建过就不需要再创建
        if (!CollectionUtils.isEmpty(operators)) {
            return;
        }
        guaranteeChangeOrderIds.forEach(item -> {
            OperatorAbility.createBatchTaskOperator(item, true);
        });
    }

    /*
     * 通过批次ID,查询所有保障单批次号一致的批次任务
     * */
    public static List<BatchTask> getGuarantyBatchTask(String batchId) {
        BatchTask batchTask = BatchTaskAbility.get(batchId, Arrays.asList(ID, BATCH_NO, CHANGE_ORDER_ID, SOURCE));
        // 自动生成保障批次不需要再查
        if (batchTask == null || DataSourceEnum.GUARANTEE.name().equals(batchTask.getSource())) {
            return new ArrayList<>();
        }
        List<String> guaranteeChangeOrderIds = ChangeOrderAbility.getGuaranteeIdsByChangeOrderId(batchTask.getChangeOrderId());
        if (CollectionUtils.isEmpty(guaranteeChangeOrderIds)) {
            return new ArrayList<>();
        }

        return QueryDataHelper.query(BatchTask.class, Arrays.asList(ID, CHANGE_ORDER_ID, CURRENT_STATUS, APPROVAL_NUM),
                Arrays.asList(new Filter(CHANGE_ORDER_ID, Comparator.IN, guaranteeChangeOrderIds),
                        new Filter(BATCH_NO, Comparator.EQ, batchTask.getBatchNo())));
    }

    /**
     * 批量变更单查询批次任务详细
     * @param changeOrderIds
     * @return List<BatchSummary>
     */
    public static List<BatchTask> listBatchTasks (List<String> changeOrderIds, List<String> fields ) {
        if (CollectionUtils.isEmpty(changeOrderIds)) {
            return com.google.common.collect.Lists.newArrayList();
        }

        List<IFilter> filters = Arrays.asList(new Filter(CHANGE_ORDER_ID,Comparator.IN ,changeOrderIds));

        return QueryDataHelper.query(BatchTask.class, fields, filters);
    }


    /**
     * 根据批次编码查询批次任务
     *
     * @param batchCode 批次编码
     * @param batchTaskClass 批次任务实体类
     * @return 批次任务
     * @param <T> T
     */
    public static <T extends BaseEntity> T queryBatchTask(String batchCode, Class<T> batchTaskClass) {
        if (BatchTask.class != batchTaskClass && SubcontractorBatchTask.class != batchTaskClass) {
            return null;
        }
        Filter conditionFilter = new Filter(BATCH_CODE, Comparator.EQ, batchCode);
        List<IFilter> conditionFilterList = com.google.common.collect.Lists.newArrayList(conditionFilter);
        return QueryDataHelper.queryOne(batchTaskClass, new ArrayList<>(), conditionFilterList);
    }

    /**
     * 根据批次编码批量查询批次任务
     *
     * @param batchCodes 批次任务编码列表
     * @param batchTaskClass 批次任务实体类
     * @return 批次任务列表
     */
    public static <T extends BaseEntity> List<T> queryBatchTask(List<String> batchCodes, Class<T> batchTaskClass) {
        Filter conditionFilter = new Filter(BATCH_CODE, Comparator.IN, batchCodes);
        List<IFilter> conditionFilters = Lists.newArrayList(conditionFilter);
        return QueryDataHelper.query(batchTaskClass, new ArrayList<>(), conditionFilters);
    }

    /*
     * 批次操作人员校验
     * */
    public static boolean checkOperators(IDataModel model, IFormView view, Class<? extends BaseSubEntity> subEntityEnum) {
        // 获取表单填写 操作人员数据
        IDataEntityCollection dataEntityCollection = model.getEntryRowEntities(EntityHelper.getEntityId(subEntityEnum));
        if (dataEntityCollection == null) {
            log.error("getEntryRowEntities returned null");
            //操作负责人 不能为空
            view.showMessage(PART_OPERATOR_NOT_NULL, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, PART_OPERATOR_NOT_NULL);
            return false;
        }

        Map<OperatorRoleEnum, List<String>> rolePersonMap = getOperatorRoleEnumListMap(dataEntityCollection);
        List<String> supervisor = rolePersonMap.get(OperatorRoleEnum.OPERATING_SUPERVISOR);
        //操作人员
        List<String> operators = rolePersonMap.get(OperatorRoleEnum.OPERATOR);
        //交叉检查人员
        List<String> crossCheckers = rolePersonMap.get(OperatorRoleEnum.CROSS_CHECKER);

        //  内部单 高风险操作，操作负责人、操作人员、交叉检查人员，每个角色均至少保留一行人员信息
        if (subEntityEnum == BatchTaskOperator.class) {
            Object changeOderObj = model.getValue(CHANGE_ORDER_ID);
            ChangeOrder changeOrder = ChangeOrderAbility.get(changeOderObj != null ? changeOderObj.toString() : "",
                    Arrays.asList(ID, RESPONSIBLE_DEPT, IS_GOV_ENT, IMPORTANCE, OPERATION_LEVEL, RISK_EVALUATION));
            String highRiskOperationsPrompt = ChangeOrderAbility.getHighRiskOperationsPrompt(changeOrder);
            // 高风险操作时，需要保证双人操作，即操作负责人、操作人员、交叉检查人至少各一条
            if (StringUtils.hasText(highRiskOperationsPrompt)
                    && (CollectionUtils.isEmpty(supervisor) || CollectionUtils.isEmpty(operators) || CollectionUtils.isEmpty(crossCheckers))) {
                String message = MsgUtils.getMessage(HIGH_RISK_OPERATION_SUPERVISOR_OPERATOR_CROSSCHECKER_NOT_NULL, highRiskOperationsPrompt);
                view.showMessage(message, MsgType.ERROR);
                ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, message);
                return false;
            }
        }
        // 本地值守人员只能有一个
        if (checkRepeatedLocalWatchMan(dataEntityCollection)) {
            view.showMessage(MessageConsts.LOCAL_ON_DUTY_ROLE_ERROR, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, LOCAL_ON_DUTY_ROLE_ERROR);
            return false;
        }

        //同一个批次仅能有一个操作负责人
        if (CollectionUtils.isEmpty(supervisor)) {
            //操作负责人 不能为空
            view.showMessage(PART_OPERATOR_NOT_NULL, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, PART_OPERATOR_NOT_NULL);
            return false;
        }
        if (supervisor.size() > 1) {
            //只能有一个操作负责人
            view.showMessage(OPERATING_SUPERVISOR_ONLY_ONE, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, OPERATING_SUPERVISOR_ONLY_ONE);
            return false;
        }

        if (CollectionUtils.isEmpty(operators)) {
            //操作人员 不能为空
            view.showMessage(PART_OPERATOR_NOT_NULL, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, PART_OPERATOR_NOT_NULL);
            return false;
        }

        List<String> compareList = new ArrayList<>(operators);
        //同一个批次内，交叉检查人员与操作人员不能是同一个人
        if (!CollectionUtils.isEmpty(crossCheckers)) {
            compareList.retainAll(crossCheckers);
            if (!CollectionUtils.isEmpty(compareList)) {
                view.showMessage(OPERATOR_NAME_ERROR, MsgType.ERROR);
                ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, OPERATOR_NAME_ERROR);
                return false;
            }
        }
        //同一批次下和同一角色下，人员不可重复
        return checkRepeatedOperators(rolePersonMap, view);
    }

    /**
     * 提交时校验是否存在多个本地值守人员
     */
    private static boolean checkRepeatedLocalWatchMan(IDataEntityCollection dataEntityCollection) {
        int localWatchManCount = INTEGER_ZERO;
        for (int i = CommonConstants.INTEGER_ZERO; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            OperatorRoleEnum operatorRole = OperatorRoleEnum.fromValue(TextValuePairHelper.getValue(dynamicDataEntity.get(OperatorFieldConsts.OPERATOR_ROLE)));
            List<TextValuePair> isRemote = JsonUtils.parseArray(dynamicDataEntity.get(OperatorFieldConsts.REMOTE_FLAG), TextValuePair.class);
            boolean flag = !CollectionUtils.isEmpty(isRemote)
                    && RemoteEnum.LOCAL.name().equals(isRemote.get(INTEGER_ZERO).getValue())
                    && operatorRole == OperatorRoleEnum.WATCHMAN;
            localWatchManCount += flag ? INTEGER_ONE : INTEGER_ZERO;
        }
        return localWatchManCount > 1;
    }

    /*
     * 监听事件  开始时间 结束时间
     * */
    public static void listenerTime(PropertyChangedEvent event, IDataModel model, IFormView view ) {
        ChangeData changeData = event.getChangeSet()[CommonConstants.INTEGER_ZERO];
        String propertyKey = changeData.getProperty().getKey();
        // 修改紧急操作
        Object startTime = model.getValue(PLAN_OPERATION_START_TIME);
        Object endTime = model.getValue(PLAN_OPERATION_END_TIME);
        Class entity = BatchTask.class;
        if (EntityHelper.getEntityId(SubcontractorBatchTask.class).equals(model.getMainEntityType().getKey())) {
            entity = SubcontractorBatchTask.class;
        }
        TimeZoneEnum timeZone = getTimeZoneByBatchId(model.getRootDataEntity().getPkValue().toString(), entity);
        if (timeZone == null ) {
            view.showMessage(TIME_ZONE_EMPTY_OR_ERROR, MsgType.ERROR);
            return;
        }

        String urgentFlag = TextValuePairHelper.getValue(model.getValue(URGENT_FLAG));
        if ((PLAN_OPERATION_START_TIME.equals(propertyKey) || URGENT_FLAG.equals(propertyKey)) && startTime != null) {
            long startDate = timeZone.fix((Date) startTime).getTime();
            if (System.currentTimeMillis() > startDate) {
                view.showMessage(STARTTIME_MUST_LATER_THAN_CURRENTTIME, MsgType.ERROR);
                view.getClientViewProxy().showValidateMessage(
                        COMPONENT_BATCH_RELEASE_TASK_START_TIME_CID,
                        MsgUtils.getMessage(STARTTIME_MUST_LATER_THAN_CURRENTTIME));
                return;
            }
            checkStartTime(view, urgentFlag, startDate, model, timeZone);
            if (endTime != null && ((Date) startTime).getTime() > ((Date) endTime).getTime()) {
                view.showMessage(ENDTIME_MUST_LATER_THAN_STARTTIME, MsgType.ERROR);
                view.getClientViewProxy().showValidateMessage(
                        COMPONENT_BATCH_RELEASE_TASK_END_TIME_CID,
                        MsgUtils.getMessage(ENDTIME_MUST_LATER_THAN_STARTTIME));
                return;
            }
        }

        if (PLAN_OPERATION_END_TIME.equals(propertyKey) && endTime != null) {
            if (startTime == null || ((Date) startTime).getTime() > ((Date) endTime).getTime()) {
                view.showMessage(ENDTIME_MUST_LATER_THAN_STARTTIME, MsgType.ERROR);
                view.getClientViewProxy().showValidateMessage(
                        COMPONENT_BATCH_RELEASE_TASK_END_TIME_CID,
                        MsgUtils.getMessage(ENDTIME_MUST_LATER_THAN_STARTTIME));
                return;
            }
        }

        // 人员高负荷拦截监听（放在原时间校验之后）
        BatchHighLoadInterceptAbility.listenerHighLoadInterception(model, view, changeData);
    }

    /*
     * 提交  开始时间 结束时间 校验
     * */
    public static boolean checkTime(IDataModel model, IFormView view ) {
        Object startTime = model.getValue(PLAN_OPERATION_START_TIME);
        Object endTime = model.getValue(PLAN_OPERATION_END_TIME);
        String urgentFlag = TextValuePairHelper.getValue(model.getValue(URGENT_FLAG));
        Class entity = BatchTask.class;
        if (EntityHelper.getEntityId(SubcontractorBatchTask.class).equals(model.getMainEntityType().getKey())) {
            entity = SubcontractorBatchTask.class;
        }
        TimeZoneEnum timeZone = getTimeZoneByBatchId(model.getRootDataEntity().getPkValue().toString(), entity);
        if (timeZone == null) {
            view.showMessage(TIME_ZONE_EMPTY_OR_ERROR, MsgType.ERROR);
            return false;
        }
        if (startTime != null) {
            // 将开始时间转为系统时区
            long startDate = timeZone.fix((Date) startTime).getTime();
            if (System.currentTimeMillis() > startDate) {
                view.showMessage(STARTTIME_MUST_LATER_THAN_CURRENTTIME, MsgType.ERROR);
                view.getClientViewProxy().showValidateMessage(
                        COMPONENT_BATCH_RELEASE_TASK_START_TIME_CID,
                        MsgUtils.getMessage(STARTTIME_MUST_LATER_THAN_CURRENTTIME));
                return false;
            }

            if (checkStartTime(view, urgentFlag, startDate, view.getDataModel(), timeZone)) {
                return false;
            }
        }

        if (endTime != null) {
            if (startTime == null ||((Date) startTime).getTime() > ((Date) endTime).getTime()) {
                view.showMessage(ENDTIME_MUST_LATER_THAN_STARTTIME, MsgType.ERROR);
                view.getClientViewProxy().showValidateMessage(
                        COMPONENT_BATCH_RELEASE_TASK_END_TIME_CID,
                        MsgUtils.getMessage(ENDTIME_MUST_LATER_THAN_STARTTIME));
                return false;
            }
        }
        return true;
    }

    /**
     * 紧急操作\配合保障 不做校验，其他提交校验72h
     */
    private static boolean checkStartTime(IFormView view, String urgentFlag,
                                          long startDate , IDataModel model,
                                          TimeZoneEnum timeZone) {
        String typeGroup = getTypeGroup(model);
        // 紧急操作\配合保障 不做校验
        if (!CommonConstants.DISABLED_FLAG.equals(urgentFlag) ||
                LookupValueConstant.OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE.equals(typeGroup)) {
            return false;
        }

        String orgId = (String) model.getValue(ORGANIZATION_ID);
        // 代表处不属于国内，不校验
        if (null != orgId && Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).noneMatch(orgId::startsWith)) {
            return false;
        }

        long firstSubTime = System.currentTimeMillis();
        Date flowCreateDate = getChangeOrderCommitDate(model);
        if (flowCreateDate != null){
            firstSubTime = flowCreateDate.getTime();
        }
        // 其他提交校验72h
        if(CommonConstants.HOURS_72 > (startDate - firstSubTime)) {
            String message = MsgUtils.getMessage(NOT_URGENT_FORBIDDEN_72_HOURS,
                    DateUtils.dateToString(timeZone.pollute(flowCreateDate), DATE_FORM));
            view.showMessage(message, MsgType.ERROR);
            view.getClientViewProxy().showValidateMessage(COMPONENT_BATCH_RELEASE_TASK_START_TIME_CID, message);
            return true;
        }
        return false;
    }

    /*
     * 监听事件 操作人员
     * */
    public static void listenerOperator(PropertyChangedEvent event, IDataModel model) {
        ChangeData changeData = event.getChangeSet()[CommonConstants.INTEGER_ZERO];
        String propertyKey = changeData.getProperty().getKey();
        Object opertePersonObj = changeData.getNewValue();
        if (!OPERATE_PERSON.equals(propertyKey) || opertePersonObj == null) {
            return;
        }
        int row = event.getChangeSet()[CommonConstants.INTEGER_ZERO].getRowIndex();
        if(opertePersonObj instanceof List && CollectionUtils.isEmpty((List) opertePersonObj)){
            model.setValue(OperatorFieldConsts.OPERATOR_PHONE, null, row);
            return;
        }
        List<SingleEmployee> person = JsonUtils.parseArray(opertePersonObj, SingleEmployee.class);
        String telphone = HrHelper.queryPhoneNum(person.get(0).getEmpUIID());
        model.setValue(OperatorFieldConsts.OPERATOR_PHONE, telphone, row);
    }

    /*
     * 监听事件 操作账号
     * */
    public static void listenerAccount(PropertyChangedEvent event, IDataModel model, IFormView view) {
        ChangeData changeData = event.getChangeSet()[CommonConstants.INTEGER_ZERO];
        String propertyKey = changeData.getProperty().getKey();
        if (!BATCH_OPERATION_ACCOUNT.equals(propertyKey)) {
            return;
        }
        setOperatorAccountProp(model, view);
        Object oldValue = changeData.getOldValue();
        Object newValue = changeData.getNewValue();
        if (oldValue == null || newValue == null) {
            return;
        }
        List<String> oldList = new ArrayList<>(Arrays.asList(oldValue.toString().split(SEMICOLON)));
        List<String> newList = new ArrayList<>(Arrays.asList(newValue.toString().split(SEMICOLON)));
        List<String> delList = oldList.stream().filter(StringUtils::hasText)
                .filter(i -> !newList.contains(i)).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newList)) {
            return;
        }
        MainEntityType mainEntityType = model.getMainEntityType();
        String subFromCid = "network_batch_operator";
        if (EntityHelper.getEntityId(SubcontractorBatchTask.class).equals(mainEntityType.getKey())) {
            subFromCid = "subcontractor_network_batch_operator";
        }
        IDataEntityCollection operators = model.getEntryRowEntities(subFromCid);
        operators.forEach(d -> {
            Object batchNoObj = ((DynamicDataEntity) d).get(SELECT_ACCOUNT);
            if (batchNoObj != null) {
                int rowIndex = ((DynamicDataEntity) d).getRowIndex();
                List<TextValuePair> pairs = JsonUtils.parseArray(batchNoObj, TextValuePair.class);
                // 新操作不存在，清空
                pairs = pairs.stream().filter(i -> delList.contains(i.getValue())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pairs)) {
                    model.setValue(SELECT_ACCOUNT, null, rowIndex);
                }
            }
        });
    }

    public static void setOperatorAccountProp(IDataModel model, IFormView formView) {
        Object accountObj = model.getValue(BATCH_OPERATION_ACCOUNT);
        if (accountObj == null) {
            return;
        }
        String accountStr = accountObj.toString().trim();
        if (!StringUtils.hasText(accountStr)) {
            return;
        }
        Set<String> operationAccounts = new HashSet<>(Arrays.asList(accountStr.split(SEMICOLON)));
        List<Map<String, Object>> options = new ArrayList<>();
        for (String operationAccount : operationAccounts) {
            if (!StringUtils.hasText(operationAccount)) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put(TEXT, operationAccount);
            map.put(VALUE, operationAccount);
            options.add(map);
        }
        // 设置子表单组件单元格属性
        TableEditNew table = (TableEditNew) formView.getControl("operationObjectPersonList");
        AbstractTable.TableState tableState = table.getTableState();
        tableState.setHeaderAttribute(SELECT_ACCOUNT, OPTIONS, options);
    }

    private static Map<OperatorRoleEnum, List<String>> getOperatorRoleEnumListMap(IDataEntityCollection dataEntityCollection) {
        Map<OperatorRoleEnum, List<String>> rolePersonMap = Maps.newHashMap();
        for (int i = CommonConstants.INTEGER_ZERO; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            OperatorRoleEnum operatorRole = OperatorRoleEnum.fromValue(TextValuePairHelper.getValue(dynamicDataEntity.get(OPERATOR_ROLE)));
            List<SingleEmployee> employees = JsonUtils.parseArray(dynamicDataEntity.get(OPERATE_PERSON), SingleEmployee.class);
            if (!CollectionUtils.isEmpty(employees)) {
                List<String> operators = rolePersonMap.get(operatorRole);
                if (CollectionUtils.isEmpty(operators)) {
                    operators = Lists.newArrayList(employees.get(0).getEmpUIID());
                } else {
                    operators.add(employees.get(0).getEmpUIID());
                }
                rolePersonMap.put(operatorRole, operators);
            }
        }
        return rolePersonMap;
    }

    private static boolean checkRepeatedOperators(Map<OperatorRoleEnum, List<String>> rolePersonMap, IFormView view) {
        //操作人员
        List<String> operators = rolePersonMap.get(OperatorRoleEnum.OPERATOR);
        if (CollectionUtilsEx.isRepeated(operators)) {
            view.showMessage(OPERATOR_REPEATED, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, OPERATOR_REPEATED);
            return false;
        }

        // 交叉检查人员
        List<String> crossCheckers = rolePersonMap.get(OperatorRoleEnum.CROSS_CHECKER);
        if (CollectionUtilsEx.isRepeated(crossCheckers)) {
            view.showMessage(CROSS_CHECKER_REPEATED, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, CROSS_CHECKER_REPEATED);
            return false;
        }

        //值守人员
        List<String> watchmans = rolePersonMap.get(OperatorRoleEnum.WATCHMAN);
        if (CollectionUtilsEx.isRepeated(watchmans)) {
            view.showMessage(WATCHMAN_REPEATED, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, WATCHMAN_REPEATED);
            return false;
        }

        //测试人员
        List<String> testers = rolePersonMap.get(OperatorRoleEnum.TESTER);
        if (CollectionUtilsEx.isRepeated(testers)) {
            view.showMessage(TESTER_REPEATED, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, TESTER_REPEATED);
            return false;
        }

        //辅助操作人员
        List<String> auxiliaryOperators = rolePersonMap.get(OperatorRoleEnum.AUXILIARY_OPERATOR);
        if (CollectionUtilsEx.isRepeated(auxiliaryOperators)) {
            view.showMessage(AUXILIARY_OPERATOR_REPEATED, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, AUXILIARY_OPERATOR_REPEATED);
            return false;
        }

        //外包人员
        List<String> outsourcings = rolePersonMap.get(OperatorRoleEnum.OUTSOURCING_PERSONNEL);
        if (CollectionUtilsEx.isRepeated(outsourcings)) {
            view.showMessage(OUTSOURCING_PERSONNEL_REPEATED, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, OUTSOURCING_PERSONNEL_REPEATED);
            return false;
        }

        //支持人员
        List<String> supports = rolePersonMap.get(OperatorRoleEnum.SUPPORT_PERSONNEL);
        if (CollectionUtilsEx.isRepeated(supports)) {
            view.showMessage(SUPPORT_PERSONNEL_REPEATED, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(view, SHOW_ERROR_MSG_TIPS_CID, SUPPORT_PERSONNEL_REPEATED);
            return false;
        }
        return true;
    }


    /*
     * 监听变更会触发审批流程变更提示
     * */
    public static void changeFlowPrompt(PropertyChangedEvent event, IDataModel model, IFormView view) {
        Object batchIdObj = model.getRootDataEntity().getPkValue();
        if (batchIdObj == null) {
            return;
        }

        String batchId = batchIdObj.toString();
        ChangeData changeData = event.getChangeSet()[CommonConstants.INTEGER_ZERO];
        String propertyKey = changeData.getProperty().getKey();
        // 分包商
        if (EntityHelper.getEntityId(SubcontractorBatchTask.class).equals(model.getMainEntityType().getKey())) {
            subChangeAdminApproval(batchId, model, view, propertyKey);
            return;
        }
        // 内部单
        BatchTask batchTask = BatchTaskAbility.get(batchId, Arrays.asList(ID, URGENT_FLAG, CONTROL_PERIOD_FLAG,
                APPROVAL_NUM, PLAN_OPERATION_START_TIME, PLAN_OPERATION_END_TIME, IS_CHANGE_ADMIN_APPROVAL,
                IS_CHANGE_REMOTE_APPROVAL, OPERATION_TYPE_GROUP, ORGANIZATION_ID, CHANGE_ORDER_ID));
        // 计算是否需要行政领导审批
        changeAdminApproval(URGENT_FLAG, batchTask, model, view, propertyKey);
        changeAdminApproval(CONTROL_PERIOD_FLAG, batchTask, model, view, propertyKey);
        // 计算是否需要远程中心审批
        if (BoolEnum.Y == batchTask.getIsChangeRemoteApproval()) {
            if (PLAN_OPERATION_START_TIME.equals(propertyKey) || PLAN_OPERATION_END_TIME.equals(propertyKey)) {
                Object startTime = model.getValue(PLAN_OPERATION_START_TIME);
                Object endTime = model.getValue(PLAN_OPERATION_END_TIME);
                if ((startTime != null && ((Date) startTime).getTime() != batchTask.getPlanOperationStartTime().getTime())
                        || (endTime != null && ((Date) endTime).getTime() != batchTask.getPlanOperationEndTime().getTime())) {
                    view.showMessage(CHANGE_CONFIRMATION_TIME_APPROVAL, MsgType.WARNING);
                }
            }
        }
    }

    /* 变更是否是否审批*/
    private static void changeAdminApproval(String cid, BatchTask batchTask, IDataModel model, IFormView view, String propertyKey) {
        if (!cid.equals(propertyKey)) {
            return;
        }
        //  属于保障单不会触发行政审核提示
        if(OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE.equals(batchTask.getOperationTypeGroup())){
            return;
        }

        String value = TextValuePairHelper.getValue(model.getValue(cid));
        BoolEnum oldFlag = BoolEnum.N;
        long num = batchTask.getApprovalNum() == null ? 0 : batchTask.getApprovalNum();
        if (1 > num) {
            oldFlag = batchTask.getIsChangeAdminApproval();
        } else {
            if (batchTask.getUrgentFlag() == BoolEnum.Y
                    || batchTask.getControlPeriodFlag() == BoolEnum.Y) {
                oldFlag = BoolEnum.Y;
            }
        }
        if (BoolEnum.N == oldFlag && ENABLED_FLAG.equals(value)) {
            // 国际会签判断
            if (DeptTypeEnum.INTER == ResponsibleUtils.getDeptType(batchTask.getOrganizationId())) {
                if (InterAdminApproverAbility.isBatchSignApprover(batchTask.getChangeOrderId(), getPageBatchTask(model))) {
                    view.showMessage(CHANGE_URGENT_OR_CONTROL_APPROVAL, MsgType.WARNING);
                    return;
                }
            }
            view.showMessage(CHANGE_URGENT_OR_CONTROL_APPROVAL, MsgType.WARNING);
        }
    }

    /* 分包商：变更是否是否审批*/
    private static void subChangeAdminApproval(String batchId, IDataModel model, IFormView view, String propertyKey) {
        if (!URGENT_FLAG.equals(propertyKey)) {
            return;
        }
        SubcontractorBatchTask batchTask = BatchTaskAbility.getSub(batchId, Arrays.asList(ID, URGENT_FLAG, URGENT_FLAG_TOO));
        String isUrgent = TextValuePairHelper.getValue(model.getValue(URGENT_FLAG));
        // 老单据是否，新单据是走PD审核
        if (BoolEnum.N == batchTask.getUrgentFlagToo() && ENABLED_FLAG.equals(isUrgent)) {
            view.showMessage(CHANGE_URGENT_APPROVAL, MsgType.WARNING);
        }
    }

    /* 推荐层级具体层级 */
    public static int getApprovalLevel(ChangeOrder changeOrder, String emergency, String control) {
        changeOrder.setIsEmergencyOperation(BoolEnum.valueOf(emergency));
        changeOrder.setIsNetCloseOrControlOperation(BoolEnum.valueOf(control));
        return ApprovalAbility.getRecommendAdminApprovalLvl(changeOrder) == null ?
                0 : ApprovalAbility.getRecommendAdminApprovalLvl(changeOrder).getLevel();
    }

    /* 查询批次网络变更主单据创建时间*/
    public static Date getChangeOrderCommitDate(IDataModel model) {
        String id = model.getRootDataEntity().getPkValue().toString();
        String changeOderId = "";
        if (EntityHelper.getEntityId(BatchTask.class).equals(model.getMainEntityType().getKey())) {
            BatchTask batchTask = BatchTaskAbility.get(id, Arrays.asList(ID, CHANGE_ORDER_ID));
            if (batchTask == null) {
                return null;
            }
            changeOderId = batchTask.getChangeOrderId();
        } else {
            SubcontractorBatchTask batchTask = BatchTaskAbility.getSub(id, Arrays.asList(ID, CHANGE_ORDER_ID));
            if (batchTask == null) {
                return null;
            }
            changeOderId = batchTask.getChangeOrderId();
        }

        return FlowHelper.getFlowCreateDate(changeOderId);
    }


    /**
     * 处理主批次
     *
     * @param batchTaskList batchTaskList
     * @param idMap idMap
     * @param step step
     */
    private static void processTasks(List<BatchTask> batchTaskList, Map<String, BaseEntity> idMap, Integer step) {
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }

        for (BatchTask batch : batchTaskList) {
            if (idMap.putIfAbsent(batch.getId(), batch) == null) {
                batch.clearEntityValue();
                batch.setChangeOrderId(null);
                batch.setStep(step);
            }
        }
    }


    /**
     * 处理合作方批次
     * @param batchTaskList batchTaskList
     * @param idMap idMap
     * @param step step
     */
    private static void processSubTasks(List<SubcontractorBatchTask> batchTaskList, Map<String, BaseEntity> idMap, Integer step) {
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return;
        }

        for (SubcontractorBatchTask batch : batchTaskList) {
            if (idMap.putIfAbsent(batch.getId(), batch) == null) {
                batch.clearEntityValue();
                batch.setChangeOrderId(null);
                batch.setStep(step);
            }
        }
    }

    /* 查询批操作类型分组*/
    public static String getTypeGroup(IDataModel model) {
        String id = model.getRootDataEntity().getPkValue().toString();
        String typeGroup = "";
        if (EntityHelper.getEntityId(BatchTask.class).equals(model.getMainEntityType().getKey())) {
            BatchTask batchTask = BatchTaskAbility.get(id, Arrays.asList(ID, ChangeOrderFieldConsts.OPERATION_TYPE_GROUP));
            if (batchTask == null) {
                return null;
            }
            typeGroup = batchTask.getOperationTypeGroup();
        } else {
            SubcontractorBatchTask batchTask = BatchTaskAbility.getSub(id, Arrays.asList(ID, ChangeOrderFieldConsts.OPERATION_TYPE_GROUP));
            if (batchTask == null) {
                return null;
            }
            typeGroup = batchTask.getOperationTypeGroup();
        }

        return typeGroup;
    }

    public static boolean isInner(IDataModel dataModel) {
        String changeOrderId = PropertyValueConvertUtil.getString(dataModel.getValue(BatchTaskFieldConsts.CHANGE_ORDER_ID));
        IChangeOrder changeOrder = getChangeOrder(dataModel.getMainEntityType(), changeOrderId);
        // 国际办事处不创建打卡任务，不展示操作阶段打卡
        return DeptTypeEnum.INNER == ChangeOrderAbility.getDeptType(changeOrder);

    }

    /*
     * 操作阶段打卡load基础逻辑
     * */
    public static void operationStageClockInLoad(IDataModel dataModel, IFormView formView) {
        int count = dataModel.getEntrySize(OPERATION_STAGE_CLOCK_IN_PROPERTY);
        if (INTEGER_FOUR != count) {
            List<Integer> rowIndices = IntStream.range(0, count)
                    .boxed()
                    .collect(Collectors.toList());
            dataModel.deleteEntryRows(OPERATION_STAGE_CLOCK_IN_PROPERTY, rowIndices);
            List<DynamicDataEntity> entities = dataModel.createEntryRows(
                    OPERATION_STAGE_CLOCK_IN_PROPERTY, INTEGER_FOUR);
            dataModel.appendEntryRows(OPERATION_STAGE_CLOCK_IN_PROPERTY, entities);
        }

        //3、4行操作时长列只读
        IEntryTableSupport table = (IEntryTableSupport) formView.getControl(COMPONENT_OPERATION_PHASE_CHECK_CID);
        table.getTableState()
                .setCellAttribute(INTEGER_TWO, COMPONENT_OPERATION_PHASE_OPERATION_DURATION_CID, BEHAVIOR, READONLY);
        table.getTableState()
                .setCellAttribute(INTEGER_THREE, COMPONENT_OPERATION_PHASE_OPERATION_DURATION_CID, BEHAVIOR, READONLY);

        //操作阶段固定值 赋值
        dataModel.setValue(CidConstants.OPERATION_PHASE_KEY, MsgUtils.getMessage(OPERATION_PREPARATION), 0);
        dataModel.setValue(CidConstants.OPERATION_PHASE_KEY, MsgUtils.getMessage(OPERATION_IMPLEMENTATION), 1);
        dataModel.setValue(CidConstants.OPERATION_PHASE_KEY, MsgUtils.getMessage(TEST_VERIFICATION), INTEGER_TWO);
        dataModel.setValue(CidConstants.OPERATION_PHASE_KEY, MsgUtils.getMessage(SERVICE_OBSERVATION), INTEGER_THREE);
    }

    /** 发布通告页面是否展示操作阶段打卡表格*/
    public static boolean showOperationStageClockInTable(IDataModel dataModel) {
        // 国内与指定国际工服处单据开放操作阶段打卡表格
        String orgPathId = (String) dataModel.getValue(FIELD_ORGANIZATION_CID);
        List<String> interOrgIds = getInterClockInOrgIds();
        return BatchTaskAbility.isInner(dataModel)
                || interOrgIds.contains(orgPathId);
    }

    public static List<String> getInterClockInOrgIds() {
        String interClockInOrgIds = ConfigHelper.get(INTER_CLOCK_IN_ORG_PATH_IDS);
        if (Strings.isEmpty(interClockInOrgIds)) {
            return Lists.newArrayList();
        }
        return Arrays.asList(interClockInOrgIds.split(COMMA));
    }

    /** 操作结果反馈页面是否展示打卡记录表格 */
    public static boolean showClockInRecordTable(IChangeOrder changeOrder) {
        String orgPathId = changeOrder.getResponsibleDept();
        List<String> interOrgIds = BatchTaskAbility.getInterClockInOrgIds();
        return DeptTypeEnum.INNER == ChangeOrderAbility.getDeptType(changeOrder)
                || interOrgIds.contains(orgPathId);
    }

    /*
     * 操作阶段打卡时间相关自动计算逻辑
     * */
    public static void setOperationStageClockInTime(IDataModel dataModel) {

        LocalDateTime planStartTime = PropertyValueConvertUtil.getDate(dataModel.getValue(FIELD_PLAN_OPERATION_START_TIME_CID));
        LocalDateTime planEndTime = PropertyValueConvertUtil.getDate(dataModel.getValue(FIELD_PLAN_OPERATION_END_TIME_CID));

        //操作准备结束时间、操作实施开始时间、测试验证结束时间、业务值守开始时间 赋值
        dataModel.setValue(FIELD_OPERATION_PHASE_END_TIME_CID, planStartTime, 0);
        dataModel.setValue(FIELD_OPERATION_PHASE_START_TIME_CID, planStartTime, 1);
        dataModel.setValue(FIELD_OPERATION_PHASE_END_TIME_CID, planEndTime, INTEGER_TWO);
        dataModel.setValue(FIELD_OPERATION_PHASE_START_TIME_CID, planEndTime, INTEGER_THREE);

        //初上述单元格内容，及用户填写的单元格，其他内容先赋值为空
        dataModel.setValue(FIELD_OPERATION_PHASE_START_TIME_CID, null, 0);
        dataModel.setValue(FIELD_OPERATION_PHASE_END_TIME_CID, null, 1);
        dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, null, INTEGER_TWO);
        dataModel.setValue(FIELD_OPERATION_PHASE_START_TIME_CID, null, INTEGER_TWO);
        dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, null, INTEGER_THREE);
        dataModel.setValue(FIELD_OPERATION_PHASE_END_TIME_CID, null, INTEGER_THREE);

        String changeOrderId = PropertyValueConvertUtil.getString(dataModel.getValue(BatchTaskFieldConsts.CHANGE_ORDER_ID));
        IChangeOrder changeOrder = getChangeOrder(dataModel.getMainEntityType(), changeOrderId);
        ClockInConfigAbility.OnDutyParams onDutyParams = ClockInConfigAbility.calcOnDutyParams(changeOrder);
        int onDutyMinutes = (int) (onDutyParams.getTotalDurationHours() * 60);
        //业务值守阶段 操作时长，结束时间赋值
        if (onDutyMinutes >= 0) {
            dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, onDutyMinutes, INTEGER_THREE);
            if (null != planEndTime) {
                dataModel.setValue(FIELD_OPERATION_PHASE_END_TIME_CID, planEndTime.plusMinutes(onDutyMinutes), INTEGER_THREE);
            }
        }

        BigDecimal operationPrepTime = PropertyValueConvertUtil.getBigDecimal(
                dataModel.getValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, 0));
        BigDecimal operationImplTime = PropertyValueConvertUtil.getBigDecimal(
                dataModel.getValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, 1));

        //操作准备开始时间、操作实段结束时间、测试验证开始时间
        if (null != planStartTime) {
            if (null != operationPrepTime) {
                dataModel.setValue(FIELD_OPERATION_PHASE_START_TIME_CID,
                        planStartTime.minusMinutes(operationPrepTime.longValue()), 0);
            }
            if (null != operationImplTime) {
                dataModel.setValue(FIELD_OPERATION_PHASE_END_TIME_CID,
                        planStartTime.plusMinutes(operationImplTime.longValue()), 1);
                dataModel.setValue(FIELD_OPERATION_PHASE_START_TIME_CID,
                        planStartTime.plusMinutes(operationImplTime.longValue()), INTEGER_TWO);
            }
        }

        //测试验证操作时长 计算赋值
        if (null != planStartTime && null != planEndTime && null != operationImplTime) {
            int duration  = (int) (Duration.between(planStartTime, planEndTime).toMinutes() - operationImplTime.longValue());
            if (duration <= 0) {
                dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, null, 1);
            } else {
                dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, duration, INTEGER_TWO);
            }
        }
    }

    public static boolean checkOperationTime(IDataModel dataModel, IFormView formView, ChangeData changeData) {
        if (!FIELD_OPERATION_PHASE_OPERATION_DURATION_CID.equals(changeData.getProperty().getKey())) {
            return true;
        }

        LocalDateTime planStartTime = PropertyValueConvertUtil.getDate(dataModel.getValue(FIELD_PLAN_OPERATION_START_TIME_CID));
        LocalDateTime planEndTime = PropertyValueConvertUtil.getDate(dataModel.getValue(FIELD_PLAN_OPERATION_END_TIME_CID));
        if (planStartTime == null || planEndTime == null) {
            return false;
        }

        BigDecimal operationPrepTime = PropertyValueConvertUtil.getBigDecimal(
                dataModel.getValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, 0));
        BigDecimal operationImplTime = PropertyValueConvertUtil.getBigDecimal(
                dataModel.getValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, 1));

        if (null != operationPrepTime
                && operationPrepTime.compareTo(BigDecimal.valueOf(INT_99999)) > 0) {
            dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, null, 0);
            return false;
        }

        if (null != operationImplTime
                && operationImplTime.compareTo(BigDecimal.valueOf(INT_99999)) > 0) {
            dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, null, 1);
            return false;
        }

        if (0 == changeData.getRowIndex() && null != operationPrepTime && operationPrepTime.compareTo(BigDecimal.ZERO) <= 0) {
            formView.showMessage(MessageConsts.DURATION_LESS_THAN_0_ERROR, MsgType.ERROR);
            formView.getClientViewProxy().showTableValidateMessage(
                    COMPONENT_OPERATION_PHASE_CHECK_CID,
                    COMPONENT_OPERATION_PHASE_OPERATION_DURATION_CID,
                    0,
                    MsgUtils.getMessage(MessageConsts.DURATION_LESS_THAN_0_ERROR));
            dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, null, 0);
            return false;
        }
        if (1 == changeData.getRowIndex() && null != operationImplTime) {
            if (operationImplTime.compareTo(BigDecimal.ZERO) <= 0) {
                formView.showMessage(MessageConsts.DURATION_LESS_THAN_0_ERROR, MsgType.ERROR);
                formView.getClientViewProxy().showTableValidateMessage(
                        COMPONENT_OPERATION_PHASE_CHECK_CID,
                        COMPONENT_OPERATION_PHASE_OPERATION_DURATION_CID,
                        1,
                        MsgUtils.getMessage(MessageConsts.DURATION_LESS_THAN_0_ERROR));
                dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, null, 1);
                return false;
            }
            int duration  = (int) (Duration.between(planStartTime, planEndTime).toMinutes() - operationImplTime.longValue());
            if (duration <= 0) {
                formView.showMessage(MessageConsts.CURRENT_OPERATION_DURATION_ERROR, MsgType.ERROR);
                formView.getClientViewProxy().showTableValidateMessage(
                        COMPONENT_OPERATION_PHASE_CHECK_CID,
                        COMPONENT_OPERATION_PHASE_OPERATION_DURATION_CID,
                        1,
                        MsgUtils.getMessage(MessageConsts.CURRENT_OPERATION_DURATION_ERROR));
                dataModel.setValue(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID, null, 1);
                return false;
            }
        }
        return true;
    }

    /*
     * 操作阶段打卡责任人相关自动计算逻辑
     * */
    public static void setOperationStageClockInPerson(IDataModel dataModel, List<BatchTaskOperator> batchTaskOperators) {


        //先全部清空
        dataModel.setValue(STAGE_CHECK_IN_PERSON_KEY, null, 0);
        dataModel.setValue(STAGE_CHECK_IN_PERSON_KEY, null, 1);
        dataModel.setValue(STAGE_CHECK_IN_PERSON_KEY, null, INTEGER_TWO);
        dataModel.setValue(STAGE_CHECK_IN_PERSON_KEY, null, INTEGER_THREE);

        MainEntityType mainEntityType = dataModel.getMainEntityType();
        String changeOrderId = PropertyValueConvertUtil.getString(dataModel.getValue(BatchTaskFieldConsts.CHANGE_ORDER_ID));
        IChangeOrder changeOrder = getChangeOrder(mainEntityType, changeOrderId);



        List<SingleEmployee> employees = batchTaskOperators.stream()
                .filter(item -> Lists.newArrayList(OperatorRoleEnum.OPERATOR,
                        OperatorRoleEnum.OPERATING_SUPERVISOR).contains(item.getOperatorRole())
                        && null != item.getOperatePerson())
                .map(BatchTaskOperator::getOperatePerson).collect(Collectors.toList());

        if (OperationLevelEnum.CRITICAL.equals(changeOrder.getOperationLevel())) {
            List<BatchTaskOperator> crossCheckerOperators = batchTaskOperators.stream()
                    .filter(item -> OperatorRoleEnum.CROSS_CHECKER.equals(item.getOperatorRole())
                            && null != item.getOperatePerson())
                    .collect(Collectors.toList());
            employees.addAll(getEmployee(crossCheckerOperators));
        }

        if (!CollectionUtils.isEmpty(employees)) {
            JSONArray employeeArray = EmployeeHelper.getEmployeeJSONArray(employees);
            dataModel.setValue(STAGE_CHECK_IN_PERSON_KEY, employeeArray, 0);
            dataModel.setValue(STAGE_CHECK_IN_PERSON_KEY, employeeArray, 1);
            dataModel.setValue(STAGE_CHECK_IN_PERSON_KEY, employeeArray, INTEGER_TWO);
        }

        //操作类型属于配合保障，不用生成值守打卡任务，所以不需要计算值守的打卡责任人
        if (OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE.equals(changeOrder.getOperationTypeGroup())) {
            return;
        }
        List<BatchTaskOperator> watchmanOperators = batchTaskOperators.stream()
                .filter(item -> OperatorRoleEnum.WATCHMAN.equals(item.getOperatorRole())
                        && null != item.getOperatePerson())
                .collect(Collectors.toList());
        List<SingleEmployee> watchmans = getEmployee(watchmanOperators);
        if (!CollectionUtils.isEmpty(watchmans)) {
            dataModel.setValue(STAGE_CHECK_IN_PERSON_KEY, EmployeeHelper.getEmployeeJSONArray(watchmans), INTEGER_THREE);
        }
    }

    public static List<BatchTaskOperator> getBatchTaskOperators(IDataModel dataModel) {
        Object pkIdObj = dataModel.getRootDataEntity().getPkValue();
        if (Objects.isNull(pkIdObj)) {
            return new ArrayList<>();
        }
        MainEntityType mainEntityType = dataModel.getMainEntityType();
        Class<? extends BaseSubEntity> clazz =  EntityHelper.getEntityId(BatchTask.class).equals(mainEntityType.getKey())
                ? BatchTaskOperator.class
                : SubcontractorBatchOperator.class;

        return CollectionUtilsEx.copyListProperties(
                QueryDataHelper.query(clazz, new ArrayList<>(), pkIdObj.toString()), BatchTaskOperator.class);
    }

    public static List<BatchTaskOperator> getFormBatchTaskOperators(IDataModel dataModel) {

        MainEntityType mainEntityType = dataModel.getMainEntityType();
        String subCid =  EntityHelper.getEntityId(BatchTask.class).equals(mainEntityType.getKey())
                ? "network_batch_operator"
                : "subcontractor_network_batch_operator";

        return JsonUtils.parseArray(dataModel.getEntryRowEntities(subCid), BatchTaskOperator.class);
    }

    private static List<SingleEmployee> getEmployee(List<BatchTaskOperator> batchTaskOperators) {
        List<SingleEmployee> operators;
        if (batchTaskOperators.stream().anyMatch(item -> RemoteEnum.REMOTE.equals(item.getRemoteFlag()))
                && batchTaskOperators.stream().anyMatch(item -> RemoteEnum.LOCAL.equals(item.getRemoteFlag()))) {
            operators = batchTaskOperators.stream()
                    .filter(item -> RemoteEnum.LOCAL.equals(item.getRemoteFlag()))
                    .map(BatchTaskOperator::getOperatePerson).collect(Collectors.toList());
        } else {
            operators = batchTaskOperators.stream().map(BatchTaskOperator::getOperatePerson).collect(Collectors.toList());
        }
        return operators;
    }

    private static IChangeOrder getChangeOrder(MainEntityType mainEntityType, String changeOrderId) {
        IChangeOrder changeOrder;
        if (EntityHelper.getEntityId(BatchTask.class).equals(mainEntityType.getKey())) {
            changeOrder = QueryDataHelper.get(ChangeOrder.class, Lists.newArrayList(
                    ChangeOrderFieldConsts.OPERATION_LEVEL,
                    ChangeOrderFieldConsts.OPERATION_TYPE_GROUP,
                    ChangeOrderFieldConsts.RESPONSIBLE_DEPT,
                    ChangeOrderFieldConsts.PRODUCT_CATEGORY,
                    ChangeOrderFieldConsts.OPERATION_SUBJECT,
                    ChangeOrderFieldConsts.OPERATION_TYPE,
                    ChangeOrderFieldConsts.TIME_ZONE,
                    ChangeOrderFieldConsts.IS_BUSINESS_OPERATION,
                    ChangeOrderFieldConsts.IS_FIRST_APPLICATION,
                    ChangeOrderFieldConsts.CUSTOMER_ID,
                    ChangeOrderFieldConsts.TRIGGER_TYPE), changeOrderId);
        } else {
            changeOrder = QueryDataHelper.get(SubcontractorChangeOrder.class, Lists.newArrayList(
                    SubcontractorChangeOrderFieldConsts.OPERATION_LEVEL,
                    SubcontractorChangeOrderFieldConsts.OPERATION_TYPE_GROUP,
                    SubcontractorChangeOrderFieldConsts.ORGANIZATION_ID,
                    SubcontractorChangeOrderFieldConsts.PRODUCT_ID,
                    SubcontractorChangeOrderFieldConsts.OPERATION_SUBJECT,
                    SubcontractorChangeOrderFieldConsts.OPERATION_TYPE,
                    SubcontractorChangeOrderFieldConsts.TIME_ZONE,
                    SubcontractorChangeOrderFieldConsts.IS_BUSINESS_OPERATION,
                    SubcontractorChangeOrderFieldConsts.CUSTOMER_ID,
                    SubcontractorChangeOrderFieldConsts.TRIGGER_TYPE), changeOrderId);
        }
        return changeOrder;
    }

    /**
     * 获取网络变更批次 超链接批次对象
     * （1）作为主单据或保障单的主单据来说，changeOrderId是啥都不重要，因为不使用
     * （2）对于批次来说。batchId必传，进行批次比对
     * （3）对于保障子单据来说，changeOrderId必须为保障单据的id
     *
     * @param batchEntityList 批次实体id
     * @param assignmentType 任务类型
     * @param batchId 批次id
     * @param changeOrderId 保障单据id（网络变更单id）
     * @return IBatchTask
     */
    public static IBatchTask getHyperLinkBatchTask(List<? extends BaseEntity> batchEntityList,
                                                   List<TextValuePair> assignmentType,
                                                   String batchId,
                                                   String changeOrderId) {
        List<IBatchTask> batchTaskList = batchEntityList.stream()
                .filter(IBatchTask.class::isInstance)
                .map(IBatchTask.class::cast)
                .sorted(java.util.Comparator.comparingInt(task -> Integer.parseInt(task.getBatchNo())))
                .collect(Collectors.toList());

        // 保障任务流程走完了，主单据流程还没走完，batchTaskList会为空
        if (CollectionUtils.isEmpty(batchTaskList)){
            return null;
        }
        // 判断是否保障来源，如果为是，则entityId为当前保障任务的id
        String source = batchTaskList.get(0).getSource();
        if (!StringUtils.isEmpty(source)
                && DataSourceEnum.GUARANTEE.name().equals(source)) {
            // 根据保障单据id获取主单据最后操作的批次信息
            BatchTask batchTask = MultiProdGuaranteeAbility.getMainLatestProcessedBatchInfo(changeOrderId);
            // 如果未获取到数据，跳转变更单页面
            if (batchTask == null) {
                return null;
            }
            // 将保障单批次id覆盖主单据批次id传递下去（主单据批次号和保障单批次号一致）
            IBatchTask guaranteeBatchTask = batchTaskList.stream()
                    .filter(item -> (item.getBatchNo().equals(batchTask.getBatchNo())))
                    .findFirst()
                    .orElse(null);
            batchTask.setId(guaranteeBatchTask.getId());
            return batchTask;
        }

        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignmentType);
        // 1.任务类型为批次则返回对应批次对象
        if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH == assignmentTypeEnum
                || AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH == assignmentTypeEnum) {
            Map<String, IBatchTask> batchTaskMap = batchTaskList.stream()
                    .collect(Collectors.toMap(IBatchTask::getId, item -> item, (o1, o2) -> o1));
            return batchTaskMap.get(batchId);
        }

        IBatchTask batchTask = getMatchBatchTask(batchTaskList);
        if (batchTask == null) {
            return batchTaskList.get(INTEGER_ZERO);
        }

        return batchTask;
    }

    /**
     * 获取批次批次任务
     *
     * @param batchTaskList batchTaskList
     * @return IBatchTask
     */
    public static IBatchTask getMatchBatchTask(List<IBatchTask> batchTaskList) {
        // 1.过滤出未完成状态的数据
        List<IBatchTask> executeBatchTasks = batchTaskList.stream()
                .filter(item -> {
                    AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(item.getCurrentStatus());
                    return assignmentStatusEnum != null && assignmentStatusEnum.checkIsExecuteStatus();
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(executeBatchTasks)) {
            return null;
        }

        // 2.查询余下批次集当前处理人
        List<String> batchIds = executeBatchTasks.stream().map(IBatchTask::getId).collect(Collectors.toList());
        List<FlowHandler> flowHandlerList = FlowHelper.getFlowHandlerByFlowEntityIds(batchIds);

        // 3.获取当前用户命中处理批次（排序）
        String empNo = ContextHelper.getEmpNo();
        Map<String, List<String>> batchApproveMap = flowHandlerList.stream()
                // 3.1 过滤处理人为空数据集
                .filter(item -> item.getApproveTaskList() != null && !item.getApproveTaskList().isEmpty())
                // 3.2 获取批次id和处理人映射
                .collect(Collectors.toMap(
                        FlowHandler::getBusinessId,
                        item -> item.getApproveTaskList().stream()
                                .map(ApproveTask::getApprover)
                                .collect(Collectors.toList()),
                        (existing, replacement) -> existing
                ));

        return executeBatchTasks.stream()
                .filter(entry -> batchApproveMap.containsKey(entry.getId())
                        && batchApproveMap.get(entry.getId()).contains(empNo))
                .findFirst()
                .orElse(null);
    }

    /**
     * 变更单执行状态批次处理
     * 1.匹配当前能处理的第一个批次
     * 2.命中失败则给提示
     * 3.命中成功跳转批次详情
     */
    public static void changeOrderExecuteProcess(
            IFormView formView,
            Assignment mainAssignment) {

        String changeOrderId = mainAssignment.getEntityId();
        List<TextValuePair> assignmentType = mainAssignment.getAssignmentType();
        AssignmentTypeEnum assignmentTypeEnum
                = AssignmentTypeEnum.fromTextValuePair(assignmentType);

        Class<? extends BaseEntity> batchEntityClass = BatchTask.class;
        AssignmentTypeEnum batchAssignmentType = AssignmentTypeEnum.NETWORK_CHANGE_BATCH;
        if (assignmentTypeEnum == AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE) {
            batchEntityClass = SubcontractorBatchTask.class;
            batchAssignmentType = AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH;
        }

        // 1.检索批次列表
        List<? extends BaseEntity> batchEntityList = QueryDataHelper.query(batchEntityClass,
                Lists.newArrayList(ID, BATCH_NO, BATCH_CODE, BATCH_NAME, SOURCE, CURRENT_STATUS),
                Lists.newArrayList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, changeOrderId)));

        IBatchTask iBatchTask = getHyperLinkBatchTask(batchEntityList, assignmentType, null, changeOrderId);
        // 说明：如果未找到批次信息，说明为保障任务且没有批次任务被操作，跳转保障单详情
        if (iBatchTask == null) {
            HyperlinkPlugin plugin = new HyperlinkPlugin();
            plugin.setView(formView);
            Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(changeOrderId, assignmentTypeEnum, Assignment.class);
            plugin.showForm(formView, assignment.getId(), CLICK_FIELD);
            return;
        }

        batchShowForm(formView,
                ApprovePageEnum.getPageIdByType(batchAssignmentType),
                iBatchTask.getId(),
                mainAssignment.getAssignmentCode());
    }


    /**
     * 批次跳转
     *
     * @param formView formView
     * @param approvePageEnum approvePageEnum
     * @param batchId 批次id
     */
    public static void batchShowForm(IFormView formView, ApprovePageEnum approvePageEnum, String batchId, String batchCode) {
        // 2.组装详情或审批详情
        FormShowParameter formShowParameter = formView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setPageId(approvePageEnum.getPageId());
        formShowParameter.setBizObjectCode(approvePageEnum.getBizObjectCode());
        formShowParameter.setPkId(batchId);
        Map<String, Object> customParameters = MapUtils.newHashMap(
                PAGE_TITLE, batchCode,
                OPEN_TYPE, OpenTypeEnum.NEW_TAB.getValue());

        // 3.批次/合作方批次进的批次汇总_v2详情页面，没有审批页面
        formShowParameter.setPageStatus(PageStatusEnum.VIEW);
        customParameters.put(URL,
                String.format(ConfigHelper.get(HREF_IFRAME_URL),
                        ZH_CN.equals(ContextHelper.getLangId()) ? "zh" : "en", batchId,
                        PageStatusEnum.VIEW.name(), ContextHelper.getAppId(), approvePageEnum.getPageId()));
        formShowParameter.setCustomParameters(customParameters);
        formView.showForm(formShowParameter);
    }

    public static IChangeOrder getIChangeOrderByBatchId(String batchId, Class<? extends BaseEntity> entity) {
        ArrayList<String> batchFields = Lists.newArrayList(ID, CHANGE_ORDER_ID);
        ArrayList<String> changeFields = Lists.newArrayList(ID, TIME_ZONE);
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(entity, batchFields, batchId);
        if (batchTask == null) {
            return null;
        }

        Class changeClass = ChangeOrder.class;
        if (SubcontractorBatchTask.class.equals(entity)) {
            changeClass = SubcontractorChangeOrder.class;
        }
        return (IChangeOrder) QueryDataHelper.get(changeClass, changeFields, batchTask.getChangeOrderId());
    }

    public static TimeZoneEnum getTimeZoneByBatchId(String batchId, Class<BaseEntity> entity) {
        IChangeOrder iChangeOrder = getIChangeOrderByBatchId(batchId, entity);
        if (iChangeOrder == null) {
            return null;
        }
        return iChangeOrder.getTimeZone();
    }

    /*
     * 将流程推到待反馈操作结果节点
     * 当前状态为代操作执行，且当前时间大于操作开始时间任务
     * */
    public static void pushResultTobeBack() {
        List<IFilter> conditionObjFilters = Lists.newArrayList();
        conditionObjFilters.add(new Filter(CURRENT_STATUS, Comparator.EQ, AssignmentStatusEnum.OPERATION_EXECUTION.getValue()));
        LocalDateTime now = LocalDateTime.now();
        conditionObjFilters.add(new Filter(PLAN_OPERATION_START_TIME, Comparator.LT, now.plusHours(MAXIMUM_TIME_DIFFERENCE_HOUR)));
        Map<String, Object> variables = MapUtils.newHashMap(BATCH_CHANGE, DISABLED_FLAG);
        conditionObjFilters.add(new Filter(SOURCE, Comparator.NE, DataSourceEnum.GUARANTEE.name())
                .or(new Filter(SOURCE, Comparator.IS_NULL, null)));
        List<String> fields = Arrays.asList(ID, CHANGE_ORDER_ID, PLAN_OPERATION_START_TIME, CREATE_BY);
        List<String> changeOrderfields = Arrays.asList(ID, TIME_ZONE);
        //内部单
        List<BatchTask> batchTasks = QueryDataHelper.query(BatchTask.class, fields, conditionObjFilters);
        List<String> changeOrderIds = batchTasks.stream()
                .map(BatchTask::getChangeOrderId)
                .distinct()
                .collect(Collectors.toList());
        List<ChangeOrder> changeOrders = QueryDataHelper.get(ChangeOrder.class, changeOrderfields, changeOrderIds);
        Map<String, TimeZoneEnum> changeOrderIdTimeZoneMap = changeOrders.stream()
                .collect(Collectors.toMap(ChangeOrder::getId, ChangeOrder::getTimeZone));
        List<IBatchTask> iBatchTasks = batchTasks.stream()
                .map(BatchTask.class::cast)
                .collect(Collectors.toList());
        pushBatchTaskResultFlow(variables, iBatchTasks, changeOrderIdTimeZoneMap, BatchTask.class);

        // 分包商
        List<SubcontractorBatchTask> subBatchTasks = QueryDataHelper
                .query(SubcontractorBatchTask.class, fields, conditionObjFilters);
        List<String> subIds = subBatchTasks.stream().
                map(SubcontractorBatchTask::getChangeOrderId)
                .distinct()
                .collect(Collectors.toList());
        List<SubcontractorChangeOrder> subChangeOrders = QueryDataHelper
                .get(SubcontractorChangeOrder.class, changeOrderfields, subIds);
        Map<String, TimeZoneEnum> subOrderIdTimeZoneMap = subChangeOrders.stream()
                .collect(Collectors.toMap(SubcontractorChangeOrder::getId, SubcontractorChangeOrder::getTimeZone));
        List<IBatchTask> iSubBatchTasks = subBatchTasks.stream()
                .map(SubcontractorBatchTask.class::cast)
                .collect(Collectors.toList());
        pushBatchTaskResultFlow(variables, iSubBatchTasks, subOrderIdTimeZoneMap, SubcontractorBatchTask.class);
    }

    /**
     * 将批次流程推到反馈操作结果
     */
    private static void pushBatchTaskResultFlow(Map<String, Object> variables,
                                                List<IBatchTask> batchTasks,
                                                Map<String, TimeZoneEnum> changeOrderIdTimeZoneMap,
                                                Class<? extends BaseEntity> entity) {
        String closeNode = BatchTask.class == entity ? ApprovalConstants.BATCH_SYSTEM_NODE : ApprovalConstants.SUB_BATCH_SYSTEM_NODE;
        ApproveFlowCodeEnum flowCodeEnum =  BatchTask.class == entity  ? BATCH_TASK_FLOW : SUBCONTRACTOR_TASK_FLOW;
        Date now = new Date();
        for (IBatchTask item : batchTasks) {
            try {
                TimeZoneEnum timeZoneEnum = changeOrderIdTimeZoneMap.get(item.getChangeOrderId());
                // 转开始时间转为当前时间,在当前时间时候不处理
                if (timeZoneEnum == null
                        || now.before(timeZoneEnum.fix(item.getPlanOperationStartTime()))) {
                    continue;
                }
                BatchTaskAbility.updateCurrentStatus(item.getId(), AssignmentStatusEnum.RESULT_TOBE_BACK);
                // 改变流程变量
                FlowHelper.changeFlowParams(item.getId(), variables ,flowCodeEnum);
                // 当前进展，处理人
                AssignmentAbility.updateProgressByType(ApproveNodeEnum.RESULT_TOBE_BACK.name(),
                        getCurrentProcessor(item.getId(), item.getCreateBy()), flowCodeEnum, item.getId());
                // 将流程推到下一个节点
                FlowHelper.pushSystemNode(item.getId(), closeNode);
            } catch (Exception e) {
                log.error("scheduler pushResultTobeBack batch_task exception! id:{}， e:{}", item.getId(), e);
                AlarmUtils.major("scheduler pushResultTobeBack batch_task exception", e);
            }
        }
    }

    /* 获取当前进展处理人*/
    public static List<String> getCurrentProcessor(String batchId, String createBy) {
        NetworkChangeAssignment networkChangeAssignment = AssignmentAbility.queryBatchAssignment(batchId);
        if (networkChangeAssignment == null
                || CollectionUtils.isEmpty(networkChangeAssignment.getCurrentProcessorEmployee())) {
            return Lists.newArrayList(createBy);
        }
        return Lists.newArrayList(networkChangeAssignment.getCurrentProcessorEmployee().get(0).getEmpUIID());
    }


    /**
     * 操作取消审核高级容器标题展示
     *
     * @param batchEntityClass 批次类型
     * @param approveRecord 审批记录
     * @param batchApprNodeMappStatusEnum 批次审批节点映射状态枚举
     * @param batchId 批次id
     * @return 组装后的标题
     */
    public static String getOperationCancelTitle(Class<? extends BaseEntity> batchEntityClass,
                                                 ApproveRecord approveRecord,
                                                 BatchApprNodeMappStatusEnum batchApprNodeMappStatusEnum,
                                                 String batchId) {
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(batchEntityClass, Lists.newArrayList(ID, OC_APPROVE_RESULT), batchId);
        // 标题
        return String.format("%s(【%s】%s %s%s)", batchApprNodeMappStatusEnum.getTitleMsg(ContextHelper.getLangId()),
                // 审核时间
                DateUtils.dateToString(approveRecord.getApprovalDate(), DATE_FORM),
                // 审核人
                HrClient.queryEmployeeNameInfo(approveRecord.getApprover()),
                // 审核结果（中英文前缀）
                MsgUtils.getMessage(MessageConsts.REVIEW_RESULT),
                // 实际审核结果
                TextValuePairHelper.getLanguageText(batchTask.getOcApproveResult())
        );
    }

    /**
     * 待发通告场景：高级容器标题展示
     *
     * @param batchEntityClass 批次类型
     * @param approveRecord 审批记录
     * @param batchApprNodeMappStatusEnum 批次审批节点映射状态枚举
     * @param batchId 批次id
     * @return 组装后的标题
     */
    public static String getPendingNotifyTitle(Class<? extends BaseEntity> batchEntityClass,
                                               ApproveRecord approveRecord,
                                               BatchApprNodeMappStatusEnum batchApprNodeMappStatusEnum,
                                               String batchId) {
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(
                batchEntityClass, Lists.newArrayList(ID, OC_OPERATION_CHANGE_DESC, CURRENT_STATUS), batchId);

        AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(batchTask.getCurrentStatus());
        // 操作变更说明不为空，判断当前任务状态是否为操作取消中，如为则在标题后拼接上【操作取消申请】，反正则默认普通高级容器标题展示
        if (StringUtils.hasText(batchTask.getOcOperationChangeDesc())
                && AssignmentStatusEnum.OPERATION_CANCEL_REVIEW == assignmentStatusEnum) {
            return String.format("%s(【%s】%s %s)", batchApprNodeMappStatusEnum.getTitleMsg(ContextHelper.getLangId()),
                    // 审核时间
                    DateUtils.dateToString(approveRecord.getApprovalDate(), DATE_FORM),
                    // 审核人
                    HrClient.queryEmployeeNameInfo(approveRecord.getApprover()),
                    // 【操作取消申请】
                    MsgUtils.getMessage(MessageConsts.BATCH_CANCEL_REVIEW_OPERATION_CHANGE_DESC_PREFIX));
        }

        return getNormalAdvancedContainerTitle(approveRecord, batchApprNodeMappStatusEnum);
    }


    /**
     * 普通高级容器标题展示
     *
     * @param approveRecord approveRecord
     * @param batchApprNodeMappStatusEnum batchApprNodeMappStatusEnum
     * @return 组装后的标题
     */
    public static String getNormalAdvancedContainerTitle(ApproveRecord approveRecord,
                                                         BatchApprNodeMappStatusEnum batchApprNodeMappStatusEnum) {
        // 标题
        return String.format("%s(【%s】%s) %s", batchApprNodeMappStatusEnum.getTitleMsg(ContextHelper.getLangId()),
                // 提交时间
                DateUtils.dateToString(approveRecord.getApprovalDate(), DATE_FORM),
                // 审核人
                HrClient.queryEmployeeNameInfo(approveRecord.getApprover()),
                // 审核意见
                !StringUtils.isEmpty(approveRecord.getOpinion()) ? MsgUtils.getMessage(approveRecord.getOpinion()) : "");
    }

    /**
     * 更新邮件推送人 - 批次任务
     */
    public static List<Employee> updateBatchTaskEmailCcs(IBatchTask batchTask) {
        // 1.检索网络变更单
        ChangeOrder changeOrder = ChangeOrderAbility.get(
                batchTask.getChangeOrderId(),
                Lists.newArrayList(
                        ChangeOrderFieldConsts.PRODUCT_CATEGORY, RESPONSIBLE_DEPT, IS_GOV_ENT,
                        IS_SPECIAL_SCENARIO, IS_AFFECT_TO_B));

        // 2.获取批次任务默认邮件推送人
        List<String> defaultMailToIds = EmailGroupAbility.getBatchCcPerson(
                changeOrder, batchTask, ApproveNodeEnum.PENDING_NOTIFICATION, null);
        List<Employee> defaultMailTos = HrClient.queryEmployeeInfo(defaultMailToIds);

        // 3.更新批次任务邮件推送人
        if (!CollectionUtils.isEmpty(batchTask.getEmailCc())) {
            List<Employee> exMailTos = batchTask.getEmailCc().stream()
                    .filter(item -> !defaultMailToIds.contains(item.getEmpUIID()))
                    .collect(Collectors.toList());
            defaultMailTos.addAll(exMailTos);
        }

        BatchTask updateBatchTask = new BatchTask();
        updateBatchTask.setId(batchTask.getId());
        updateBatchTask.setEmailCc(defaultMailTos);
        batchUpdate(Lists.newArrayList(updateBatchTask));

        return defaultMailTos;
    }

    /**
     * 更新邮件推送人 - 合作方批次任务
     */
    public static List<Employee> updatePartnerBatchTaskEmailCcs(IBatchTask batchTask) {
        // 1.检索网络变更单
        SubcontractorChangeOrder changeOrder = PartnerChangeOrderAbility.get(
                batchTask.getChangeOrderId(),
                Lists.newArrayList(
                        SubcontractorChangeOrderFieldConsts.PRODUCT_ID,
                        SubcontractorChangeOrderFieldConsts.ORGANIZATION_ID,
                        SubcontractorChangeOrderFieldConsts.IS_GOV_ENT));

        // 2.获取批次任务默认邮件推送人
        List<String> defaultMailToIds = EmailGroupAbility.getBatchCcPerson(
                changeOrder, batchTask, null, PartnerApproveNodeEnum.PENDING_NOTIFICATION);
        List<Employee> defaultMailTos = HrClient.queryEmployeeInfo(defaultMailToIds);

        // 3.更新批次任务邮件推送人
        if (!CollectionUtils.isEmpty(batchTask.getEmailCc())) {
            List<Employee> exMailTos = batchTask.getEmailCc().stream()
                    .filter(item -> !defaultMailToIds.contains(item.getEmpUIID()))
                    .collect(Collectors.toList());
            defaultMailTos.addAll(exMailTos);
        }

        SubcontractorBatchTask updateBatchTask = new SubcontractorBatchTask();
        updateBatchTask.setId(batchTask.getId());
        updateBatchTask.setEmailCc(defaultMailTos);
        batchUpdateSub(Lists.newArrayList(updateBatchTask));

        return defaultMailTos;
    }

    /**
     * 更新对应节点邮件抄送人 - 批次任务
     */
    public static List<String> updateNodeEmailCcs(
            IBatchTask updateBatchTask,
            List<String> configCcs,
            List<Employee> exEmailCcs,
            Consumer<List<Employee>> propertySetFunction) {

        log.info("The exEmails contains" + JsonUtils.toJsonString(exEmailCcs));

        // 1.检索抄送人信息
        Set<String> groupEmails = new HashSet<>(configCcs);
        groupEmails.addAll(EmployeeHelper.getEpmUIID(exEmailCcs));
        List<Employee> groupEmailInfo = HrClient.queryEmployeeInfo(new ArrayList<>(groupEmails));

        log.info("The groupEmailInfo contains" + JsonUtils.toJsonString(groupEmailInfo));

        // 2.更新节点配置抄送人
        propertySetFunction.accept(groupEmailInfo);
        if (updateBatchTask instanceof BatchTask) {
            BatchTaskAbility.batchUpdate(Lists.newArrayList((BatchTask) updateBatchTask));
        } else {
            BatchTaskAbility.batchUpdateSub(
                    Lists.newArrayList((SubcontractorBatchTask) updateBatchTask));
        }

        return new ArrayList<>(groupEmails);
    }

    public static IBatchTask getBatchTaskById(String batchId, Assignment assignment, List<String> fields) {
        if (assignment == null || !StringUtils.hasText(TextValuePairHelper.getValue(assignment.getAssignmentType()))) {
            return null;
        }

        String type = TextValuePairHelper.getValue(assignment.getAssignmentType());
        if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getValue().equals(type)) {
            return get(batchId, fields);
        }else if (AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.getValue().equals(type)) {
            return getSub(batchId, fields);
        }

        return null;
    }

    public static List<IBatchTask> getBatchTaskByChangeOrderId(String changeOrderId, Assignment assignment, List<String> fields) {
        List<IBatchTask> batchTasks = new ArrayList<>();
        if (assignment == null || !StringUtils.hasText(TextValuePairHelper.getValue(assignment.getAssignmentType()))) {
            return batchTasks;
        }

        List<IFilter> filters = Lists.newArrayList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, changeOrderId));
        String type = TextValuePairHelper.getValue(assignment.getAssignmentType());
        if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getValue().equals(type)) {
            batchTasks.addAll(QueryDataHelper.query(BatchTask.class, fields, filters));
        } else if (AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.getValue().equals(type)) {
            batchTasks.addAll(QueryDataHelper.query(SubcontractorBatchTask.class, fields, filters));
        }
        return batchTasks;
    }

    /**
     * 是否需要行政领导审核
     * 不是配合保障 or是紧急操作 or是封网管控期操作 不需要行政审核
     */
    public static boolean isAdministrationApproval(String typeGroup ,String isUrgent, String isControlPeriod) {
        return !OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE.equals(typeGroup)
                && (CommonConstants.ENABLED_FLAG.equals(isUrgent)
                   || CommonConstants.ENABLED_FLAG.equals(isControlPeriod));
    }
    /**
     * 批次取消操作
     * 1.更新状态为废止,判断是否所有批次已关闭，更新变跟单状态，发送邮件
     * 2.取消inet鉴权任务
     * 3.取消打卡任务
     *
     * @param batchClassEntity 批次类型
     * @param batchId 批次id
     * @param approveFlowEnum 审批流程
     */
    public static void processBatchCancel(Class<? extends BaseEntity> batchClassEntity,
                                          String batchId,
                                          ApproveFlowCodeEnum approveFlowEnum) {
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(batchClassEntity, Lists.newArrayList(ID, CHANGE_ORDER_ID), batchId);
        // 1.原取消废止功能（从TaskCancelPlugin抽取）
        // 1.1 更新状态为废止
        Map<String, Object> values = MapUtils.newHashMap(CURRENT_STATUS, AssignmentStatusEnum.ABOLISH.getValue());
        if (BATCH_TASK_FLOW == approveFlowEnum) {
            values.put(OPERATION_UPDATE_TIME, new Date());
        }
        SaveDataHelper.update(batchClassEntity, batchId, values);
        // 1.2 是否所有批次已关闭，更新变跟单状态
        flowEndUpadteAllStatus(approveFlowEnum.name(), batchTask.getChangeOrderId(), batchId);
        // 1.3 发送邮件
        AsyncExecuteUtils.execute(() -> EmailAbility.sendBatchOperationEmail(batchId, NotifyTypeEnum.CANCEL, batchClassEntity));


        List<String> batchTaskIds = Stream.concat(
                        Stream.of(batchId),
                        getGuarantyBatchTask(batchId)
                                .stream()
                                .filter(Objects::nonNull)
                                .map(BatchTask::getId)
                                .filter(Objects::nonNull)
                )
                .collect(Collectors.toList());

        // 2.CancelAuthTaskPlugin操作插件内容
        if (Boolean.parseBoolean(ConfigHelper.get("authTask.enabled", "false"))) {
            AsyncExecuteUtils.execute(() ->
                    batchTaskIds.forEach(item -> {
                        BatchTask4AuthAbility.cancelAuthTask(CancelSceneEnum.ABOLISH, batchClassEntity, item);
                    })
            );
        }


        // 3.CancelClockInTaskPlugin操作插件内容
        if (Boolean.parseBoolean(ConfigHelper.get("clockInTask.enabled", "false"))) {
            ClockInTaskUpdateAbility.batchCancelClockInTasks(batchTaskIds);
        }
    }

    public static BatchTask getPageBatchTask(IDataModel model){
        BatchTask batchTask = new BatchTask();
        batchTask.setId(model.getRootDataEntity().getPkValue().toString());
        batchTask.setPlanOperationStartTime((Date) model.getValue(PLAN_OPERATION_START_TIME));
        batchTask.setPlanOperationEndTime((Date)model.getValue(PLAN_OPERATION_END_TIME));
        batchTask.setUrgentFlag(BoolEnum.valueOf(TextValuePairHelper.getValue(model.getValue(URGENT_FLAG))));
        return batchTask;
    }

    public static boolean isRevokeAccessible(ApproveRecord resultReviewRecord, AssignmentStatusEnum assignmentStatusEnum,
                                             String resultReviewFlag) {
        if (null == resultReviewRecord
                || null == resultReviewRecord.getApprovalDate()) {
            return false;
        }

        long checkTime = resultReviewRecord.getApprovalDate().toInstant().atZone(ZoneId.systemDefault())
                .toLocalDate().plusDays(INTEGER_SEVEN).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        return assignmentStatusEnum == AssignmentStatusEnum.CLOSE
                && ResultReviewFlagEnum.RESULT_REVIEW_PASSED.name().equals(resultReviewFlag)
                && ContextHelper.getEmpNo().equals(resultReviewRecord.getApprover())
                && System.currentTimeMillis() <= checkTime;
    }
}