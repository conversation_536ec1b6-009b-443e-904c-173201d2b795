package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AssignmentFieldConsts {
    // ========================== 任务基础字段 ===========================
    /** 实体ID */
    public static final String ENTITY_ID = "entity_id";

    /** 单据ID */
    public static final String BILL_ID = "bill_id";

    /** 任务名称 */
    public static final String ASSIGNMENT_NAME = "assignment_name";

    /** 任务编码 */
    public static final String ASSIGNMENT_CODE = "assignment_code";

    /** 任务状态 */
    public static final String ASSIGNMENT_STATUS = "assignment_status";

    /** 任务类型 */
    public static final String ASSIGNMENT_TYPE = "assignment_type";

    /** 单据类型 */
    public static final String BILL_TYPE = "bill_type";

    /** 公司 */
    public static final String COMPANY = "company";

    /** 客户标识 */
    public static final String CUSTOMER_CLASSIFICATION = "customer_classification";

    /** 责任人 */
    public static final String RESPONSIBLE = "responsible";

    /** 责任人（员工组件） */
    public static final String RESPONSIBLE_EMPLOYEE_FIELD = "responsible_employee_field";

    /** 营销 */
    public static final String MARKETING = "marketing";

    /** 代表处 */
    public static final String REPRESENTATIVE_OFFICE = "representative_office";

    /** 产品经营团队 */
    public static final String PRODUCT_MANAGEMENT_TEAM = "product_management_team";

    /** 产品分类 */
    public static final String PRODUCT_CLASSIFICATION = "product_classification";

    /** 网络 */
    public static final String NETWORK = "network";

    /** 当前处理人 */
    public static final String CURRENT_PROCESSOR_EMPLOYEE_FIELD = "current_processor_employee_field";

    /** 当前进展 */
    public static final String CURRENT_PROGRESS = "current_progress";

    /** 当前进展是否审批流程 */
    public static final String APPROVAL_TASK_FLAG = "approval_task_flag";

    // ======================= 网络变更任务扩展字段 =======================
    public static class NetworkChangeFieldConsts {
        /** 操作类型  */
        public static final String OPERATION_TYPE = "operation_type";

        /** 计划开始时间 */
        public static final String PLAN_START_TIME = "plan_start_time";

        /** SLA 要求完成时间 */
        public static final String SLA_FINISH_TIME = "sla_finish_time";

        /** 批次任务审批实体ID */
        public static final String APPROVE_BATCH_TASK_ID = "approve_batch_task_id";

        /** 用于标识批次任务是否创建打卡任务 */
        public static final String CREATE_CLOCK_IN_TASK = "create_clock_in_task";

        /** 国家/地区 */
        public static final String COUNTRY = "country";

        /** 内部网络变更单类型 */
        public static final String TYPE = "type";

        /** 查询条件 操作开始时间(UTC+8) */
        public static final String OPERATION_START_TIME_UTC_8 = "operation_start_time_utc_8";

        /** 操作原因 */
        public static final String OPERATION_REASON = "operation_reason";

        /** 操作结果 */
        public static final String OPERATION_RESULT = "operation_result";

        /** 重要程度 */
        public static final String IMPORTANCE = "importance";

        /** 风险评估 */
        public static final String RISK_EVALUATION = "risk_evaluation";

        /** 操作等级 */
        public static final String OPERATION_LEVEL = "operation_level";

        /** 局点名称 */
        public static final String OFFICE_NAME = "office_name";
    }

    // ======================= 技术管理任务扩展字段 =======================
    public static class TechnologyManagementFieldConsts {
        /** 任务分类 */
        public static final String TASK_CATEGORY = "task_category";

        /** 要求完成日期 */
        public static final String REQUIRED_COMPLETION_TIME = "required_completion_time";

        /** 子任务审批实体ID */
        public static final String APPROVE_SUB_TASK_ID = "approve_sub_task_id";
    }

    // ======================= 故障管理任务扩展字段 =======================
    public static class FaultManagementFieldConsts {
        /** WarRoom ID */
        public static final String WAR_ROOM = "warroom";

        /** WarRoom 主题 */
        public static final String WAR_ROOM_TITLE = "warroom_title";

        /** 故障程度 */
        public static final String FAULT_LEVEL = "fault_level";

        /** 客户ID */
        public static final String CUSTOMER = "customer";

        /** PDM产品ID */
        public static final String PDM_PRODUCT = "pdm_product";

        /** 区域 */
        public static final String REGION = "region";

        /** 故障发生时间 */
        public static final String OCCURRENCE_TIME = "occurrence_time";

        /** 备注 */
        public static final String REMARK = "remark";

        /** 故障响应时长（分钟） */
        public static final String FAULT_RESPONSE_TIME = "fault_response_time";

        /** 人员到位时长（分钟） */
        public static final String PERSON_IN_PLACE_TIME = "person_in_place_time";

        /** 故障定位时长（分钟） */
        public static final String FAULT_LOCATION_TIME = "fault_location_time";

        /** 故障恢复时长（分钟） */
        public static final String FAULT_RECOVERY_TIME = "fault_recovery_time";

        /** 历史进展（针对故障降级关闭场景） */
        public static final String HISTORY_PROGRESS = "history_progress";
    }

    // ======================= 权限申请任务扩展字段 =======================
    public static class PermissionApplicationFieldConsts {
        /** 模块 */
        public static final String MODULE = "module";

        /** 角色 */
        public static final String ROLE = "role";

        /** 有效日期 */
        public static final String EXPIRATION_TIME = "expiration_time";
    }

    // ======================= 打卡复盘任务扩展字段 =======================
    public static class ClockReviewsFieldConsts {
        /** 批次号 */
        public static final String BATCH_NO = "batch_no";

        /** 复盘操作等级 */
        public static final String OPERATION_LEVEL = "review_operation_level";

        /** 复盘操作类型 */
        public static final String OPERATION_TYPE = "review_operation_type";

        /** 计划操作开始时间 */
        public static final String PLAN_OPERATION_START_TIME = "plan_operation_start_time";

        /** 值守时长 */
        public static final String ON_DUTY_DURATION_HOURS = "on_duty_duration_hours";
    }

    // ======================= 操作计划管理扩展字段 =======================
    public static class OperationPlanFieldConsts {
        /** 操作类型 */
        public static final String OPERATION_TYPE = "plan_operation_type";

        /** 计划开始时间 */
        public static final String PLAN_ORDER_START_TIME = "plan_order_start_time";

        /** 操作原因 */
        public static final String OPERATION_REASON = "plan_operation_reason";

        /** 国家/地区 */
        public static final String COUNTRY = "plan_country";

        /** 重要程度 */
        public static final String IMPORTANCE = "plan_importance";

        /**  风险评估*/
        public static final String RISK_EVALUATION = "plan_risk_evaluation";

        /**  操作等级*/
        public static final String OPERATION_LEVEL = "plan_operation_level";

        /**  局点名称*/
        public static final String OFFICE_NAME = "plan_office_name";
    }

    // ========================= 流程任务集合字段 ========================
    /** 任务集合 */
    public static final String ASSIGNMENT_SET = "assignment_set";
}
