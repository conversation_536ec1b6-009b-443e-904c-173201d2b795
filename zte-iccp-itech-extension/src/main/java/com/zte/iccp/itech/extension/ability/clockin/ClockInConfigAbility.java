package com.zte.iccp.itech.extension.ability.clockin;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.OperationLvlOdDurationFreqConfigFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.ProdOdcConfigFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.ProdOperationTypeOdcConfigFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.TriggerTypeBaselineCoefficientConfigFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.TriggerTypeEnum;
import com.zte.iccp.itech.extension.domain.model.clockin.config.OperationLvlOdDurationFreqConfig;
import com.zte.iccp.itech.extension.domain.model.clockin.config.ProdOdcConfig;
import com.zte.iccp.itech.extension.domain.model.clockin.config.ProdOperationTypeOdcConfig;
import com.zte.iccp.itech.extension.domain.model.clockin.config.TriggerTypeBaselineCoefficientConfig;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.spi.client.CrmClient;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @since 2024/09/14
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ClockInConfigAbility {

    public static OnDutyParams calcOnDutyParams(IChangeOrder changeOrder) {
        String customerFlag;
        String langId = ContextHelper.getLangId();
        try {
            ContextHelper.setLangId(ZH_CN);
            customerFlag = CrmClient.getAccntType(changeOrder.getCustomerId());
        } finally {
            ContextHelper.setLangId(langId);
        }
        //TODO bug待跟进
        if(null == changeOrder.getOperationLevel()){
            log.error("OperationLevel=null: {}, {}", changeOrder.getId(), changeOrder.getOrderNo());
            changeOrder.setOperationLevel(OperationLevelEnum.NORMAL);
        }

        OperationLvlOdDurationFreqConfig opLvlOdConfig
                = queryOperationLvlOdDurationFreqConfig(changeOrder.getOperationLevel());
        // 匹配不到时取默认值，时长2小时，频次为1
        if (opLvlOdConfig == null) {
            opLvlOdConfig = new OperationLvlOdDurationFreqConfig();
            opLvlOdConfig.setFrequencyInner(INTEGER_ONE);
            opLvlOdConfig.setFrequencyInter(INTEGER_ONE);
            opLvlOdConfig.setTotalDurationInner(DOUBLE_TWO);
            opLvlOdConfig.setTotalDurationInter(DOUBLE_TWO);
        }

        TriggerTypeBaselineCoefficientConfig triggerConfig = queryTriggerTypeBaselineCoefficientConfig(
                changeOrder.getTriggerType(),
                changeOrder.getIsFirstTimeApply(),
                changeOrder.getIsBusinessOperation() == null
                        ? BoolEnum.N : changeOrder.getIsBusinessOperation());
        // 匹配不到时默认取 1
        if (triggerConfig == null) {
            triggerConfig = new TriggerTypeBaselineCoefficientConfig();
            triggerConfig.setBaselineCoefficientInner(DOUBLE_ONE);
            triggerConfig.setBaselineCoefficientInter(DOUBLE_ONE);
        }

        ProdOdcConfig prodOdcConfig = queryProdOdcConfig(customerFlag, changeOrder.getProductCategory());
        ProdOperationTypeOdcConfig opTypeOdcConfig = queryProdOperationTypeOdcConfig(
                changeOrder.getOperationType(), customerFlag, changeOrder.getProductCategory());

        double prodOnDutyCoefficient = 0;
        double opTypeOnDutyCoefficient = 0;
        double opLvlDuration = 0;
        double triggerCoefficient = 0;
        int onDutyFreq = 0;

        DeptTypeEnum deptType = ChangeOrderAbility.getDeptType(changeOrder);
        if (deptType == DeptTypeEnum.INNER) {
            prodOnDutyCoefficient = prodOdcConfig == null
                    ? Double.MAX_VALUE : prodOdcConfig.getOnDutyCoefficientInner();
            opTypeOnDutyCoefficient = opTypeOdcConfig == null
                    ? Double.MAX_VALUE : opTypeOdcConfig.getOnDutyCoefficientInner();
            opLvlDuration = opLvlOdConfig.getTotalDurationInner();
            triggerCoefficient = triggerConfig.getBaselineCoefficientInner();
            onDutyFreq = opLvlOdConfig.getFrequencyInner();
        } else if (deptType == DeptTypeEnum.INTER) {
            prodOnDutyCoefficient = prodOdcConfig == null
                    ? Double.MAX_VALUE : prodOdcConfig.getOnDutyCoefficientInter();
            opTypeOnDutyCoefficient = opTypeOdcConfig == null
                    ? Double.MAX_VALUE : opTypeOdcConfig.getOnDutyCoefficientInter();
            opLvlDuration = opLvlOdConfig.getTotalDurationInter();
            triggerCoefficient = triggerConfig.getBaselineCoefficientInter();
            onDutyFreq = opLvlOdConfig.getFrequencyInter();
        }

        double totalDuration = Math.min(prodOnDutyCoefficient, opTypeOnDutyCoefficient)
                * opLvlDuration * triggerCoefficient;

        return new OnDutyParams(totalDuration, onDutyFreq);
    }

    public static ProdOdcConfig queryProdOdcConfig(
            String customerFlag,
            String prodClassification) {
        // 匹配规则：按照【产品】（优先级：产品小类->产品大类->产品线）->【客户标识（运营商）】（优先级：有->无） ，均匹配不到时取1
        // 1、小类去找  多个选运营商匹配上的第一个，没有则选运营商为空的第一个
        List<ProdOdcConfig> prodSubCategoryPOCList = QueryDataHelper.query(
                ProdOdcConfig.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(ProdOdcConfigFieldConsts.PROD_TYPE,
                                Comparator.EQ, Lists.newArrayList(prodClassification))));
        ProdOdcConfig prodSubCategoryPOC = getProdOdcConfig(prodSubCategoryPOCList, customerFlag);
        if (prodSubCategoryPOC != null) {
            return prodSubCategoryPOC;
        }
        // 2、大类去找  多个选运营商匹配上的第一个，没有则选运营商为空的第一个
        List<ProdOdcConfig> prodCategoryPOCList = QueryDataHelper.query(
                ProdOdcConfig.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(ProdOdcConfigFieldConsts.PROD_TYPE,
                                Comparator.EQ, Lists.newArrayList(ProductUtils.getMain(prodClassification)))));
        ProdOdcConfig prodCategoryPOC = getProdOdcConfig(prodCategoryPOCList, customerFlag);
        if (prodCategoryPOC != null) {
            return prodCategoryPOC;
        }
        // 3、产品线去找  多个选运营商匹配上的第一个，没有则选运营商为空的第一个
        List<ProdOdcConfig> prodLinePOCList = QueryDataHelper.query(
                ProdOdcConfig.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(ProdOdcConfigFieldConsts.PROD_TYPE,
                                Comparator.EQ, Lists.newArrayList(ProductUtils.getLine(prodClassification)))));
        ProdOdcConfig prodLinePOC = getProdOdcConfig(prodLinePOCList, customerFlag);
        if (prodLinePOC != null) {
            return prodLinePOC;
        }
        ProdOdcConfig prodOdcConfig = new ProdOdcConfig();
        prodOdcConfig.setOnDutyCoefficientInner(DOUBLE_ONE);
        prodOdcConfig.setOnDutyCoefficientInter(DOUBLE_ONE);
        return prodOdcConfig;
    }

    private static ProdOdcConfig getProdOdcConfig(List<ProdOdcConfig> prodOdcConfigList, String customerFlag) {
        // 客户标识目前只匹配中文（打卡现在只有国内用）
        List<ProdOdcConfig> customerFlagConfigs = prodOdcConfigList.stream()
                .filter(p -> p.getCustomerFlag() != null && p.getCustomerFlag().getZhCn().equals(customerFlag))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(customerFlagConfigs)) {
            return customerFlagConfigs.get(0);
        }
        List<ProdOdcConfig> odcConfigs = prodOdcConfigList.stream()
                .filter(p -> p.getCustomerFlag() == null)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(odcConfigs)) {
            return odcConfigs.get(0);
        }
        return null;
    }

    public static ProdOperationTypeOdcConfig queryProdOperationTypeOdcConfig(
            String operationType,
            String customerFlag,
            String prodClassification) {
        // 匹配规则：按照【操作类型】【产品】（优先级：产品线->产品经营团队）->【运营商】（优先级：有->无），均匹配不到时取1
        // 1、操作类型+产品线去找  多个选运营商匹配上的第一个，没有则选运营商为空的第一个
        List<ProdOperationTypeOdcConfig> prodOperationTypeOdcConfigListLine = QueryDataHelper.query(
                ProdOperationTypeOdcConfig.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(ProdOperationTypeOdcConfigFieldConsts.OPERATION_TYPE,
                                Comparator.EQ, Lists.newArrayList(operationType)),
                        new Filter(ProdOperationTypeOdcConfigFieldConsts.PROD_TYPE,
                                Comparator.EQ, Lists.newArrayList(ProductUtils.getLine(prodClassification)))));
        ProdOperationTypeOdcConfig prodOperationTypeOdcConfigLine = getProdOperationTypeOdcConfig(prodOperationTypeOdcConfigListLine, customerFlag);
        if (prodOperationTypeOdcConfigLine != null) {
            return prodOperationTypeOdcConfigLine;
        }
        // 2、操作类型+产品经营团队去找  多个选运营商匹配上的第一个，没有则选运营商为空的第一个
        List<ProdOperationTypeOdcConfig> prodOperationTypeOdcConfigListTeam = QueryDataHelper.query(
                ProdOperationTypeOdcConfig.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(ProdOperationTypeOdcConfigFieldConsts.OPERATION_TYPE,
                                Comparator.EQ, Lists.newArrayList(operationType)),
                        new Filter(ProdOperationTypeOdcConfigFieldConsts.PROD_TYPE,
                                Comparator.EQ, Lists.newArrayList(ProductUtils.getTeam(prodClassification)))));
        ProdOperationTypeOdcConfig prodOperationTypeOdcConfigTeam = getProdOperationTypeOdcConfig(prodOperationTypeOdcConfigListTeam, customerFlag);
        if (prodOperationTypeOdcConfigTeam != null) {
            return prodOperationTypeOdcConfigTeam;
        }
        ProdOperationTypeOdcConfig prodOperationTypeOdcConfig = new ProdOperationTypeOdcConfig();
        prodOperationTypeOdcConfig.setOnDutyCoefficientInner(DOUBLE_ONE);
        prodOperationTypeOdcConfig.setOnDutyCoefficientInter(DOUBLE_ONE);
        return prodOperationTypeOdcConfig;
    }

    private static ProdOperationTypeOdcConfig getProdOperationTypeOdcConfig(List<ProdOperationTypeOdcConfig> prodOperationTypeOdcConfigList, String customerFlag) {
        // 客户标识目前只匹配中文（打卡现在只有国内用）
        List<ProdOperationTypeOdcConfig> customerFlagConfigs = prodOperationTypeOdcConfigList.stream()
                .filter(p -> p.getCustomerFlag() != null && p.getCustomerFlag().getZhCn().equals(customerFlag))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(customerFlagConfigs)) {
            return customerFlagConfigs.get(0);
        }
        List<ProdOperationTypeOdcConfig> odcConfigs = prodOperationTypeOdcConfigList.stream()
                .filter(p -> p.getCustomerFlag() == null)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(odcConfigs)) {
            return odcConfigs.get(0);
        }
        return null;
    }

    public static OperationLvlOdDurationFreqConfig queryOperationLvlOdDurationFreqConfig(OperationLevelEnum operationLevel) {
        return QueryDataHelper.queryOne(
                OperationLvlOdDurationFreqConfig.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(OperationLvlOdDurationFreqConfigFieldConsts.OPERATION_LEVEL,
                                Comparator.EQ, Lists.newArrayList(operationLevel.getValue()))));
    }

    public static TriggerTypeBaselineCoefficientConfig queryTriggerTypeBaselineCoefficientConfig(
            TriggerTypeEnum triggerType,
            BoolEnum isFirstTimeApply,
            BoolEnum isOperateWithinBusi) {
        return QueryDataHelper.queryOne(
                TriggerTypeBaselineCoefficientConfig.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(TriggerTypeBaselineCoefficientConfigFieldConsts.TRIGGER_TYPE,
                                Comparator.EQ, Lists.newArrayList(triggerType.getValue())),
                        new Filter(TriggerTypeBaselineCoefficientConfigFieldConsts.FIRST_TIME_APPLY,
                                Comparator.EQ, Lists.newArrayList(isFirstTimeApply)),
                        new Filter(TriggerTypeBaselineCoefficientConfigFieldConsts.OPERATION_WITHIN_BUSI,
                                Comparator.EQ, Lists.newArrayList(isOperateWithinBusi))));
    }

    @Getter
    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    public static class OnDutyParams {
        private final double totalDurationHours;

        private final int frequency;
    }
}
