package com.zte.iccp.itech.extension.infrastructure.aop;

import com.zte.iccp.itech.extension.common.dynamicbean.DynamicAspect;
import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.common.utils.StackTraceUtils;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.infrastructure.i18n.I18nEntry;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * 捕获I18nEntry#getContent方法的入参和返回值
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@DynamicAspect(I18nEntry.class)
public class I18nEntryAspect {
    @Around("execution(* com.zte.paas.lcap.infrastructure.i18n.I18nEntry.getContent(..))")
    public Object aroundGetContent(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object result = joinPoint.proceed();

        if (args.length > 1
                && args[1] instanceof String
                && result instanceof String) {
            checkMsgLang((String) args[1], (String) result);
        }

        return result;
    }

    private static void checkMsgLang(String msgId, String message) {
        try {
            if (msgId.equals(message)) {
                return;
            }

            // 此处不使用ContextHelper的默认langId，与平台内部保持一致
            String langId = RequestContextHolder.getLangId();
            boolean containsZhChar = LangUtils.containsZhChar(message);
            if (ZH_CN.equals(langId) && containsZhChar) {
                return;
            }

            if (EN_US.equals(langId) && !containsZhChar) {
                return;
            }

            log.error("MsgLangError:   langId: {}, msgId: {}, message: {}\n{}",
                    langId, msgId, message, StackTraceUtils.getStackTrace());
        } catch (Throwable ignore) {
        }
    }
}
