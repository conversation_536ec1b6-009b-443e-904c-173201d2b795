package com.zte.iccp.itech.extension.handler.approver.admin;

import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.vo.SubIntlAdminApproval;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.CustomizedNetworkSetConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.IntlAdminApprovalFieldConsts.APPROVED_BY;

/**
 * 批次会签节点
 *
 * <AUTHOR> 10284287
 * @since 2024/07/17
 */
public class InterBatchAdminHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        List<SubIntlAdminApproval> approvals = QueryDataHelper
                .query(SubIntlAdminApproval.class, Arrays.asList(ID, APPROVED_BY), bod.getBusinessId());
        if (CollectionUtils.isEmpty(approvals)) {
            return null;
        }
        return approvals.stream()
                .map(SubIntlAdminApproval::getApprover)
                .map(SingleEmployee::getEmpUIID)
                .collect(Collectors.toList());
    }
}
