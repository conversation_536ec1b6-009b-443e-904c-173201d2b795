package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.PermissionApplicationFieldConsts.*;

@ApiModel("权限申请任务")
@Setter
@Getter
@BaseEntity.Info("assignment")
public class PermissionApplicationAssignment extends Assignment {

    @ApiModelProperty("模块")
    @JsonProperty(value = MODULE)
    private List<TextValuePair> module;

    @ApiModelProperty("角色")
    @JsonProperty(value = ROLE)
    private List<TextValuePair> role;

    @ApiModelProperty("有效日期")
    @JsonProperty(value = EXPIRATION_TIME)
    private Date expirationTime;
}
