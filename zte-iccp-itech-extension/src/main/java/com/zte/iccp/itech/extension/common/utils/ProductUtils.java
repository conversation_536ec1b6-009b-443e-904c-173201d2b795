package com.zte.iccp.itech.extension.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.ProdCateLvlConsts;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.client.PdmClient;
import com.zte.iccp.itech.extension.spi.model.nis.ProductClassificationTree;
import com.zte.iccp.itech.extension.spi.model.pdm.dto.vo.ProductInfoByLevelVo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.ProdCategoryConsts.CCN_PROD_IDPATH_KEY;
import static com.zte.paas.lcap.metadata.engine.common.constant.NumberConst.THREE;
import static com.zte.paas.lcap.metadata.engine.common.constant.NumberConst.TWO;

/**
 * 产品型号截取
 *
 * <AUTHOR>
 * @create 2024/7/17 上午10:06
 */
public class ProductUtils {

    /**
     * 格式化产品ID
     */
    public static String trimEndSlash(String productId) {
        if (!StringUtils.hasText(productId)) {
            return "";
        }

        return productId.replaceAll("/$", "");
    }

    /*
     *根据产品IDpth获取经营团队
     */
    public static String getTeam(String productId) {
        if (!StringUtils.hasText(productId)) {
            return "";
        }
        String[] pro = productId.split(CommonConstants.FORWARD_SLASH);
        return pro[0] + CommonConstants.FORWARD_SLASH;
    }

    /*
     *根据产品IDpth获取产品线
     */
    public static String getLine(String productId) {
        if (!StringUtils.hasText(productId)) {
            return "";
        }
        String[] pro = productId.split(CommonConstants.FORWARD_SLASH);
        return String.join(CommonConstants.FORWARD_SLASH, pro[0], pro[1]) + CommonConstants.FORWARD_SLASH;
    }

    /*
     *根据产品IDpth获取产品大类
     */
    public static String getMain(String productId) {
        if (!StringUtils.hasText(productId)) {
            return "";
        }
        String[] pro = productId.split(CommonConstants.FORWARD_SLASH);
        return String.join(CommonConstants.FORWARD_SLASH, pro[0], pro[1], pro[TWO]) + CommonConstants.FORWARD_SLASH;
    }

    /**
     * 是否核心网
     */
    public static boolean isCcn(String productPath) {
        if (StringUtils.hasText(productPath)) {
            return productPath.startsWith(ConfigHelper.get(CCN_PROD_IDPATH_KEY));
        }
        return false;
    }

    /**
     * 获取单个产品index
     */
    public static String getPathIndex(String productPath, int index) {
        if (!StringUtils.hasText(productPath)) {
            return "";
        }

        String[] res = productPath.split(FORWARD_SLASH);
        if (res.length < index) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < index; i++) {
            result.append(res[i]).append(FORWARD_SLASH);
        }
        return result.toString();
    }

    public static String getValueIndex(String responsible, int index) {
        if (!StringUtils.hasText(responsible)) {
            return "";
        }

        String[] res = responsible.split(CommonConstants.FORWARD_SLASH);
        if (res.length < index) {
            return "";
        }

        return res[index - 1];
    }
    /*
     * 根据产品IDpth获取产品大类类
     */
    public static String getSub(String productId) {
        if (!StringUtils.hasText(productId)) {
            return "";
        }
        String[] pro = productId.split(CommonConstants.FORWARD_SLASH);
        return String.join(CommonConstants.FORWARD_SLASH, pro[0], pro[1], pro[TWO], pro[THREE]) + CommonConstants.FORWARD_SLASH;
    }

    /*
     * 是否属于IDOP产品
     * 云及智算/CCN_CLOUD_AI_PROD_ID_PATH  核心网/CCN_CCN_PROD_ID_PATH
     * 集成第三方与组网设备下的 第三方云平台  其他第三方设备  第三方APP  第三方安全设备
     * */
    public static boolean isBelongIdopProduct(String productIdPath) {
        return Arrays.asList(ConfigHelper.get(CCN_CCN_PROD_ID_PATH), ConfigHelper.get(CCN_CLOUD_AI_PROD_ID_PATH),
                        ConfigHelper.get(CCN_INTEGR_CLOUD_PROD_ID_PATH), ConfigHelper.get(CCN_INTEGR_DEVICE_PROD_ID_PATH),
                        ConfigHelper.get(CCN_INTEGR_APP_PROD_ID_PATH), ConfigHelper.get(CCN_INTEGR_SECURITY_PROD_ID_PATH))
                .stream().anyMatch(productIdPath::startsWith);
    }

    /**
     *  产品高级查询条件
     */
    public static IFilter productFilter(
            IDataModel dataModel,
            String productIdCid,
            String productIdField,
            Integer treeLevel,
            boolean onlyLeaf) {

        // 1.获取 产品分类 组件填写数据
        List<TextValuePair> productClassificationInfo
                = ComponentUtils.getChooseComponentInfo(dataModel, productIdCid);
        if (CollectionUtils.isEmpty(productClassificationInfo)) {
            return null;
        }

        // 2.检索产品分类树
        List<ProductClassificationTree> treeData = NisClient.queryProductTree(treeLevel);

        // 3.获取产品分类填写数据对应子节点路径
        Set<String> leafPathSet = new HashSet<>();
        productClassificationInfo
                .forEach(item -> leafPathSet.addAll(
                        getProductClassificationLeafNodePath(treeData, item.getValue(), onlyLeaf)));

        // 4.包装过滤条件
        return new Filter(productIdField, Comparator.IN, new ArrayList<>(leafPathSet));
    }

    /**
     * 获取产品分类叶子节点全路径 - 定位当前节点
     * @param treeData
     * @param productPath
     * @return List<String>
     */
    public static List<String> getProductClassificationLeafNodePath(
            List<ProductClassificationTree> treeData,
            String productPath,
            boolean onlyLeaf) {

        for (ProductClassificationTree treeNode : treeData) {
            String nodeFullPath = treeNode.getFullIdPath();

            if (productPath.equals(nodeFullPath)) {
                // 1.找到当前节点，向下获取子节点
                return getProductClassificationLeafNodePath(treeNode, onlyLeaf);
            } else if (productPath.startsWith(nodeFullPath)) {
                // 2.找到节点的父节点，继续查找当前节点
                return getProductClassificationLeafNodePath(
                        treeNode.getChildProdClass(), productPath, onlyLeaf);
            }
        }

        return Lists.newArrayList();
    }

    /**
     * 获取产品分类叶子节点全路径 - 检索路径
     */
    private static List<String> getProductClassificationLeafNodePath(
            ProductClassificationTree treeNode,
            boolean onlyLeaf) {

        List<ProductClassificationTree> childrenNodeList = treeNode.getChildProdClass();

        List<String> leafPath = Lists.newArrayList();
        if (CollectionUtils.isEmpty(childrenNodeList)) {
            // 1.叶子节点返回全路径
            leafPath.add(treeNode.getFullIdPath());
        } else {
            // 2.非叶子节点, 查叶子节点全路径
            if (!onlyLeaf) {
                leafPath.add(treeNode.getFullIdPath());
            }
            childrenNodeList.forEach(item ->
                    leafPath.addAll(getProductClassificationLeafNodePath(item, onlyLeaf)));
        }

        return leafPath;
    }

    /**
     * 获取 PDM 产品型号
     * 懒加载下拉数据
     */
    public static List<ProductInfoByLevelVo> getPdmProductModelLazyDropDown(
            String productModelName,
            Integer pageNumber,
            Integer pageSize) {

        // 1.检索产品型号数据
        List<ProductInfoByLevelVo> productModels = CacheUtils.get(
                CidConstants.PDM_PRODUCT_MODEL, new TypeReference<List<ProductInfoByLevelVo>>() {});
        if (CollectionUtils.isEmpty(productModels)) {
            productModels = getPdmProductModels();
            CacheUtils.set(CidConstants.PDM_PRODUCT_MODEL, productModels, 30 * 24 * 60 * 60);
        }

        // 2.根据分页 / 查询条件进行过滤
        List<ProductInfoByLevelVo> validProductModels = productModels.stream()
                .filter(item -> LangUtils.get(item.getCnName(), item.getEnName()).contains(productModelName))
                .collect(Collectors.toList());
        List<List<ProductInfoByLevelVo>> partitions = Lists.partition(validProductModels, pageSize);
        return partitions.size() >= pageNumber
                ? partitions.get(pageNumber - 1)
                : Lists.newArrayList();
    }

    /**
     * 检索 PDM 产品型号
     * 该方法因 PDM 系统限制比较慢，请配合异步线程使用
     */
    public static List<ProductInfoByLevelVo> getPdmProductModels() {
        // 产品经营团队
        // 限制范围：NIS 启用
        List<ProductClassificationTree> nisProductTeams
                = NisClient.queryProductTree(ProdCateLvlConsts.TEAM);
        List<String> productTeamCodes = nisProductTeams.stream()
                .map(ProductClassificationTree::getCode)
                .collect(Collectors.toList());
        List<ProductInfoByLevelVo> productTeams =
                PdmClient.queryProductInfo(productTeamCodes);
        Map<String, ProductInfoByLevelVo> productTeamMap = productTeams.stream()
                .collect(Collectors.toMap(
                        ProductInfoByLevelVo::getItemNo, Function.identity(), (v1, v2) -> v1));

        // 产品线
        List<ProductInfoByLevelVo> productLines
                = PdmClient.queryChildProductListByLinkage(productTeamCodes);
        Map<String, ProductInfoByLevelVo> productLineMap = productLines.stream()
                .collect(Collectors.toMap(
                        ProductInfoByLevelVo::getItemNo, Function.identity(), (v1, v2) -> v1));

        // 产品大类
        List<ProductInfoByLevelVo> productClasses
                = PdmClient.queryChildProductListByLinkage(new ArrayList<>(productLineMap.keySet()));
        Map<String, ProductInfoByLevelVo> productClassMap = productClasses.stream()
                .collect(Collectors.toMap(
                        ProductInfoByLevelVo::getItemNo, Function.identity(), (v1, v2) -> v1));

        // 产品小类
        List<ProductInfoByLevelVo> productSubclasses
                = PdmClient.queryChildProductListByLinkage(new ArrayList<>(productClassMap.keySet()));
        Map<String, ProductInfoByLevelVo> productSubclassMap = productSubclasses.stream()
                .collect(Collectors.toMap(
                        ProductInfoByLevelVo::getItemNo, Function.identity(), (v1, v2) -> v1));

        // 产品型号
        List<ProductInfoByLevelVo> productModels = PdmClient.queryChildProductListByLinkage(
                new ArrayList<>(productSubclassMap.keySet()));
        convertPdmProductModelFullPath(
                productTeamMap, productLineMap, productClassMap, productSubclassMap, productModels);

        return productModels;
    }

    /**
     * 包装产品型号全路径
     */
    private static void convertPdmProductModelFullPath(
            Map<String, ProductInfoByLevelVo> productTeamMap,
            Map<String, ProductInfoByLevelVo> productLineMap,
            Map<String, ProductInfoByLevelVo> productClassMap,
            Map<String, ProductInfoByLevelVo> productSubclassMap,
            List<ProductInfoByLevelVo> productModels) {

        String format = "%s/%s/%s/%s/%s";

        for (ProductInfoByLevelVo productModel : productModels) {
            ProductInfoByLevelVo productSubclass
                    = productSubclassMap.getOrDefault(productModel.getParentNo(), new ProductInfoByLevelVo());
            ProductInfoByLevelVo productClass
                    = productClassMap.getOrDefault(productSubclass.getParentNo(), new ProductInfoByLevelVo());
            ProductInfoByLevelVo productLine
                    = productLineMap.getOrDefault(productClass.getParentNo(), new ProductInfoByLevelVo());
            ProductInfoByLevelVo productTeam
                    = productTeamMap.getOrDefault(productLine.getParentNo(), new ProductInfoByLevelVo());

            productModel.setCnName(String.format(format,
                    productTeam.getCnName(),
                    productLine.getCnName(),
                    productClass.getCnName(),
                    productSubclass.getCnName(),
                    productModel.getProductModel()));
            productModel.setEnName(String.format(format,
                    getPdmProductEnName(productTeam),
                    getPdmProductEnName(productLine),
                    getPdmProductEnName(productClass),
                    getPdmProductEnName(productSubclass),
                    productModel.getProductModel()));
            productModel.setFullPath(String.format(format,
                    productTeam.getItemNo(),
                    productLine.getItemNo(),
                    productClass.getItemNo(),
                    productSubclass.getItemNo(),
                    productModel.getItemNo()));
        }
    }

    /**
     * 获取 PDM 产品英语名称
     */
    private static String getPdmProductEnName(ProductInfoByLevelVo product) {
        return StringUtils.hasText(product.getEnName()) ? product.getEnName() : product.getEnNameAb();
    }

    public static String getMainName(String prodName) {
        if (!StringUtils.hasText(prodName)) {
            return "";
        }
        String[] pro = prodName.split(CommonConstants.FORWARD_SLASH);
        return pro.length < THREE ? "" : String.join(CommonConstants.FORWARD_SLASH, pro[0], pro[1], pro[TWO]);
    }

    /**
     * 获取产品ID信息
     * key - PDM 产品编码   value - NIS 产品ID
     */
    public static Map<String, String> getPdmProductTeamAndLineIdMap() {
        List<ProductClassificationTree> productTree = NisClient.queryProductTree(ProdCateLvlConsts.LINE);

        Map<String, String> productIdMap = new HashMap<>();
        productTree.forEach(productTeam -> {
            productIdMap.putIfAbsent(productTeam.getCode(), productTeam.getId());
            List<ProductClassificationTree> productLines = productTeam.getChildProdClass();
            if (!CollectionUtils.isEmpty(productLines)) {
                productLines.forEach(item -> productIdMap.put(item.getCode(), item.getId()));
            }
        });

        return productIdMap;
    }

    /**
     * 根据层级从产品字符串中提取指定层级的产品ID
     *
     * @param productIdPath 产品IDPath，格式为 "6872952355824349360/7188264679487025229/9133838559736665043/2981922221503775339/"
     * @param level     要提取的层级（从1开始计数）
     * @return 对应层级的产品ID，如果层级无效或不存在则返回空字符串
     */
    public static String getProductIdByLevel(String productIdPath, int level) {
        // 1. 处理空值或无效输入
        if (productIdPath == null || productIdPath.trim().isEmpty() || level < 1) {
            return EMPTY_STRING;
        }

        // 2. 按分隔符拆分组织字符串
        String[] orgParts = productIdPath.split(FORWARD_SLASH);

        // 3. 检查层级是否在有效范围内
        if (level > orgParts.length) {
            return EMPTY_STRING;
        }

        // 4. 返回对应层级的组织ID（去除首尾空格）
        String orgId = orgParts[level - 1].trim();
        return orgId.isEmpty() ? EMPTY_STRING : orgId;
    }
}
