package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;

import java.util.ArrayList;
import java.util.Arrays;

/*
代表处属于国内代表处（属于国内营销或工程服务经营部-国内部）：优先级如下
*1【产品】（优先级：产品小类>产品大类>产品线）+【营销】（申请单=国内营销）（如果代表处属于工程服务国内部，则匹配【营销】=工程服务经营部 +片区=工程服务国内部）
    +【运营商】+【操作类型】+【是否政企】=否，
*2【产品小类】+【组织】（优先级：代表处>片区>营销）+【运营商】+【是否政企】=否，其他条件为空，
*3【产品线】+操作类型，其他条件为空，
*4【产品小类】+【营销】=国内营销（如果代表处属于工程服务国内部，则匹配【营销】=工程服务经营部 +片区=工程服务国内部）+【是否政企】，其他条件为空

*代表处属于国际（属于工程服务经营部但不属于工程服务国内部）
*5【产品线】+操作类型，其他条件为空，
*6【产品小类】+【组织】（优先级：代表处>片区>营销）（【是否政企】取值不考虑），其他条件为空，
*/
public class NetworkDeptHandlerImpl {

    public ApproverConfiguration getApprover(String changeOrderId) {
        ChangeOrder changeOrder = QueryDataHelper.get(ChangeOrder.class, new ArrayList<>(), changeOrderId);
        return getApprover(changeOrder);
    }

    public ApproverConfiguration getApprover(ChangeOrder changeOrder) {
        // 代表处idPATH
        String responsibleDept = changeOrder.getResponsibleDept();
        // 产品小类idPATH
        String productCategory = changeOrder.getProductCategory();
        ApproverConfiguration queryParam = new ApproverConfiguration();
        queryParam.setApprovalNode(ApprovalTypeEnum.NETWORK_DEPT);
        queryParam.setProdLine(ProductUtils.getLine(productCategory));

        ApproverConfiguration result;
        // 当代表处属于国内
        if (DeptTypeEnum.INNER == ResponsibleUtils.getDeptType(responsibleDept)) {
            result = getInner(changeOrder, responsibleDept, productCategory, queryParam);
        } else {
            // 5【产品线】+操作类型，其他条件为空，获取对应记录
            queryParam.setOperationType(Arrays.asList(changeOrder.getOperationType()));
            result = ApproverConfigAbility.getApproverConfiguration(queryParam);

            //6【产品小类】+【组织】（优先级：代表处>片区>营销）（【是否政企】取值不考虑），其他条件为空，
            if (result == null) {
                queryParam.setOperationType(null);
                queryParam.setProdMainCategory(ProductUtils.getMain(productCategory));
                queryParam.setProdSubCategory(productCategory);
                queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
                queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));
                queryParam.setResponsibleDeptId(responsibleDept);
                result = ApproverConfigAbility.getApproverConfiguration(queryParam);
            }
            if (result == null) {
                queryParam.setResponsibleDeptId(null);
                result = ApproverConfigAbility.getApproverConfiguration(queryParam);
            }
            if (result == null) {
                queryParam.setOrganizationRegion(null);
                result = ApproverConfigAbility.getApproverConfiguration(queryParam);
            }
        }
        return result;
    }

    private ApproverConfiguration getInner(ChangeOrder changeOrder, String responsibleDept, String productCategory, ApproverConfiguration queryParam) {
        ApproverConfiguration result;
        queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));
        queryParam.setProdMainCategory(ProductUtils.getMain(productCategory));
        queryParam.setProdSubCategory(productCategory);
        if (ConfigHelper.get(CommonConstants.ENGINEERING_SERVICE_DEPT_ORG_CODE_PATH).equals(ResponsibleUtils.getSales(responsibleDept))) {
            queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
        }
        //1【运营商】+【操作类型】+【是否政企】=否
        queryParam.setOperator(OperatorEnum.getCnnValue(changeOrder.getCustomerTypeFlag()));
        queryParam.setOperationType(Arrays.asList(changeOrder.getOperationType()));
        queryParam.setIsGov(BoolEnum.N);
        result = ApproverConfigAbility.getApprovalConfiguration(queryParam, productCategory, 0);
        //2[产品小类】+【组织】（优先级：代表处>片区>营销）+【运营商】+【是否政企】=否，其他条件为空
        if (result == null) {
            queryParam.setOperationType(null);
            queryParam.setProdMainCategory(ProductUtils.getMain(productCategory));
            queryParam.setProdSubCategory(productCategory);
            queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
            queryParam.setResponsibleDeptId(responsibleDept);
            result = ApproverConfigAbility.getApproverConfiguration(queryParam);
        }
        if (result == null) {
            queryParam.setResponsibleDeptId(null);
            result = ApproverConfigAbility.getApproverConfiguration(queryParam);
        }
        if (result == null) {
            queryParam.setOrganizationRegion(null);
            result = ApproverConfigAbility.getApproverConfiguration(queryParam);
        }

        // 3【产品线】+操作类型，其他条件为空，
        if (result == null) {
            queryParam.setOperator(null);
            queryParam.setSales(null);
            queryParam.setProdMainCategory(null);
            queryParam.setProdSubCategory(null);
            queryParam.setIsGov(null);
            queryParam.setOperationType(Arrays.asList(changeOrder.getOperationType()));
            result = ApproverConfigAbility.getApproverConfiguration(queryParam);
        }

        //4【产品小类】+【营销】=国内营销（如果代表处属于工程服务国内部，则匹配【营销】=工程服务经营部 +片区=工程服务国内部）+【是否政企】，其他条件为空，
        if (result == null) {
            queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));
            queryParam.setOperationType(null);
            queryParam.setProdMainCategory(ProductUtils.getMain(productCategory));
            queryParam.setProdSubCategory(productCategory);
            if (ConfigHelper.get(CommonConstants.ENGINEERING_SERVICE_DEPT_ORG_CODE_PATH).equals(ResponsibleUtils.getSales(responsibleDept))) {
                queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
            }
            queryParam.setIsGov(changeOrder.getIsGovEnt());
            result = ApproverConfigAbility.getApproverConfiguration(queryParam);
        }
        return result;
    }
}
