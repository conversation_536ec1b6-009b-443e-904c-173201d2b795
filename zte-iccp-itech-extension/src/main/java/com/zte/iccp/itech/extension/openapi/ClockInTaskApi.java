package com.zte.iccp.itech.extension.openapi;

import com.zte.iccp.itech.extension.ability.clockin.*;
import com.zte.iccp.itech.extension.common.utils.SysAuthUtils;
import com.zte.iccp.itech.extension.domain.constant.ProdCateLvlConsts;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInOptionVO;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTaskDetailVO;
import com.zte.iccp.itech.extension.openapi.model.UserVO;
import com.zte.iccp.itech.extension.openapi.model.clockin.*;
import com.zte.iccp.itech.extension.openapi.model.clockin.vo.BatchInfoVO;
import com.zte.iccp.itech.extension.openapi.model.clockin.vo.ClockInStatusVO;
import com.zte.iccp.itech.extension.openapi.model.clockin.vo.InetClockInVO;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.iccp.itech.extension.spi.model.nis.ProductClassificationTree;
import com.zte.iccp.itech.extension.spi.model.query.NisNetworkQuery;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/09/02
 * app打卡接口
 */
public class ClockInTaskApi extends AbstractOpenApi {

    /**
     * 获取打卡选项
     * @param clockInTaskId
     * @return
     */
    public ServiceData<List<ClockInOptionVO>> getClockInOptions(
            @PathVariable("id") String clockInTaskId) {
        return new ServiceData<List<ClockInOptionVO>>() {{
            setBo(new ClockInOptionAbility(clockInTaskId).getClockInOptions());
        }};
    }

    public ServiceData<Boolean> clockIn(
            @PathVariable("id") String clockInTaskId,
            @RequestBody ClockInDTO clockInDTO) {
        validate(clockInTaskId, clockInDTO);

        return new ServiceData<Boolean>() {{
            setBo(new ClockInAbility(clockInTaskId, clockInDTO).clockIn());
        }};
    }

    public ServiceData<Boolean> changeOwner(
            @PathVariable("id") String clockInTaskId,
            @PathVariable("userId") String targetUserId) {
        validate(clockInTaskId, targetUserId);

        return new ServiceData<Boolean>() {{
            setBo(ClockInTaskUpdateAbility.changeOwner(clockInTaskId, targetUserId));
        }};
    }

    /**
     * 撤销打卡接口
     * @param clockInTaskId
     * @return
     */
    public ServiceData<Boolean> revoke(@PathVariable("id") String clockInTaskId) {
        validate(clockInTaskId);

        return new ServiceData<Boolean>() {{
            setBo(new ClockInRevokeAbility(clockInTaskId).revoke(null));
        }};
    }

    /**
     * 打卡运维接口（补充两个值守字段数据）
     * @return
     */
    public ServiceData<Boolean> fillOnDutyData() {

        return new ServiceData<Boolean>() {{
            setBo(ClockInRevokeAbility.updateOnDutyData());
        }};
    }

    public ServiceData<PageRows<ClockInTaskDetailVO>> queryClockInTaskList(
            @RequestBody ClockInTaskQueryDTO clockInTaskQueryDTO) {
        return new ServiceData<PageRows<ClockInTaskDetailVO>>() {{
            setBo(ClockInQueryAbility.queryClockInTaskList(clockInTaskQueryDTO));
        }};
    }

    public ServiceData<ClockInTaskDetailVO> getClockInDetail(
            @PathVariable("id") String clockInTaskId) {
        return new ServiceData<ClockInTaskDetailVO>() {{
            setBo(ClockInQueryAbility.getClockInTaskDetail(clockInTaskId));
        }};
    }

    /**
     * 查询与我相关的打卡任务列表
     * @param queryDTO
     * @Return: com.zte.itp.msa.core.model.ServiceData<com.zte.itp.msa.core.model.PageRows<com.zte.iccp.itech.extension.domain.model.clockin.ClockInTaskDetailVO>>
     */
    public ServiceData<PageRows<ClockInTaskDetailVO>> queryRelatedTasks(@RequestBody RelatedClockInTaskQueryDTO queryDTO) {
        return new ServiceData<PageRows<ClockInTaskDetailVO>>() {{
            setBo(new ClockInQueryAbility().getRelatedTasks(queryDTO));
        }};
    }

    /**
     * 查询转交目标人员
     * @param queryDTO
     * @return
     */
    public ServiceData<PageRows<UserVO>> queryUser(@RequestBody ClockInUserQueryDTO queryDTO) {
        return new ServiceData<PageRows<UserVO>>() {{
            setBo(ClockInQueryAbility.getUsers(queryDTO, true));
        }};
    }

    /**
     * 根据操作批次号模糊查询操作批次信息
     * @param queryKey
     * @Return: com.zte.itp.msa.core.model.ServiceData<com.zte.itp.msa.core.model.PageRows<com.zte.iccp.itech.extension.openapi.model.clockin.vo.BatchInfoVO>>
     */
    public ServiceData<PageRows<BatchInfoVO>> queryBatchInfo(
            @RequestParam String queryKey,
            @RequestParam int currentPage,
            @RequestParam int pageSize) {
        return new ServiceData<PageRows<BatchInfoVO>>() {{
            setBo(ClockInQueryAbility.getBatchInfoByBatchNo(queryKey, currentPage, pageSize));
        }};
    }

    /**
     * 根据客户网络名称模糊查询NIS网络信息
     * @param keyword
     * @Return: com.zte.itp.msa.core.model.ServiceData<java.util.List<com.zte.iccp.itech.extension.spi.model.nis.NisNetwork>>
     */
    public ServiceData<PageRows<NisNetwork>> queryNetworkInfo(
            @RequestParam String keyword,
            @RequestParam int currentPage,
            @RequestParam int pageSize) {
        return new ServiceData<PageRows<NisNetwork>>() {{
            NisNetworkQuery query = new NisNetworkQuery();
            query.setCustomerNetworkName(keyword);
            query.setPageNum(currentPage);
            query.setPageSize(pageSize);
            setBo(NisClient.queryNisNetworkList(query));
        }};
    }

    /**
     * 查询NIS代表处
     * @param
     * @Return: com.zte.itp.msa.core.model.ServiceData<java.util.List<com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo>>
     */
    public ServiceData<List<OrganizationTreeVo>> queryRepresentativeOffice() {
        return new ServiceData<List<OrganizationTreeVo>>() {{
            setBo(NisClient.queryOrganizationTree());
        }};
    }

    /**
     * 查询NIS产品分类
     * @param
     * @Return: com.zte.itp.msa.core.model.ServiceData<java.util.List<com.zte.iccp.itech.extension.spi.model.nis.ProductClassificationTree>>
     */
    public ServiceData<List<ProductClassificationTree>> queryProductLine() {
        return new ServiceData<List<ProductClassificationTree>>() {{
            setBo(NisClient.queryProductTree(ProdCateLvlConsts.LINE));
        }};
    }

    /**
     * 查询操作负责人员/打卡人
     * @param queryKey 工号或姓名
     * @Return:
     */
    public ServiceData<PageRows<UserVO>> queryResponsibleUser(
            @RequestParam String queryKey,
            @RequestParam int currentPage,
            @RequestParam int pageSize) {
        ClockInUserQueryDTO queryDTO = new ClockInUserQueryDTO();
        queryDTO.setKeyword(queryKey);
        queryDTO.setPageNo(currentPage);
        queryDTO.setPageSize(pageSize);
        return new ServiceData<PageRows<UserVO>>() {{
            setBo(ClockInQueryAbility.internalUserQueryWithSubstitution(queryDTO, false));
        }};
    }

    /**
     * 查询快码获取打卡状态
     * @param
     * @Return: com.zte.itp.msa.core.model.ServiceData<java.util.List<com.zte.iccp.itech.extension.openapi.model.clockin.vo.ClockInStatusVO>>
     */
    public ServiceData<List<ClockInStatusVO>> queryClockInStatus() {
        return new ServiceData<List<ClockInStatusVO>>() {{
            setBo(ClockInQueryAbility.getClockInStatus());
        }};
    }

    private static void validate(String id) {
        if (StringUtils.isBlank(id)) {
            throw new IllegalArgumentException("id");
        }
    }

    private static void validate(String id, ClockInDTO clockInDTO) {
        validate(id);

        if (clockInDTO == null) {
            throw new IllegalArgumentException("clockInDTO");
        }

        if (clockInDTO.getOption() == null) {
            throw new IllegalArgumentException("clockInDTO.option");
        }
    }

    private static void validate(String id, String userId) {
        validate(id);

        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException("userId");
        }
    }


    /**
     * Inet打卡信息查询
     *
     * @param request request
     * @param inetClockInQueryDTO inetClockInQueryDTO
     * @return PageRows<InetClockInVO>
     */
    public ServiceData<PageRows<InetClockInVO>> queryINetClockIn(HttpServletRequest request,
                                                                   @RequestBody InetClockInQueryDTO inetClockInQueryDTO) {
        // 鉴权
        SysAuthUtils.auth(request);
        // 参数校验
        inetClockInQueryDTO.validateAndSetDateCondition();
        return new ServiceData<PageRows<InetClockInVO>>() {{
            setBo(ClockInQueryAbility.queryInetClockIn(inetClockInQueryDTO));
        }};
    }

}
