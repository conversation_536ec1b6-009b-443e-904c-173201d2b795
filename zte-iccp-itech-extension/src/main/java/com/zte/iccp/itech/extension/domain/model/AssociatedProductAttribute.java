package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssociatedProductConsts.*;

/**
 * 关联产品
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/12
 */
@ApiModel("关联产品(associated_product)")
@Setter
@Getter
@BaseEntity.Info("associated_product")
public class AssociatedProductAttribute extends BaseEntity {

    /**
     * AvailabilityEnum.name
     */
    @JsonProperty(BILLSTATUSFIELD_ASSOCIATED_PRODUCT)
    @ApiModelProperty("单据状态")
    private String status;

    @JsonProperty(TEXTFIELD_ASSOCIATED_PRODUCT_ZH)
    @ApiModelProperty("关联产品中文名")
    private String assocProdZh;

    @JsonProperty(TEXTFIELD_ASSOCIATED_PRODUCT_EN)
    @ApiModelProperty("关联产品英文名")
    private String assocProdEn;
}
