package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
/**
 * <AUTHOR>
 * @create 2025/2/24 下午3:46
 */
@Setter
@Getter
@BaseEntity.Info("createTechnicalManagementTask")
public class TechnicalManagementTask extends BaseEntity{

    @JsonProperty(value = "organization_id")
    @ApiModelProperty("代表处")
    private List<TextValuePair> organizationId;
}
