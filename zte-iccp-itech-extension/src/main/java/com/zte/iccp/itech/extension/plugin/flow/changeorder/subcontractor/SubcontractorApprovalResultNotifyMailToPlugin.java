package com.zte.iccp.itech.extension.plugin.flow.changeorder.subcontractor;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.InformEmailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;


/**
 * 审核通过/驳回 - 知会通知 分包商网络变更单
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/29
 */
public class SubcontractorApprovalResultNotifyMailToPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        InformEmailPlugin plugin = new InformEmailPlugin();
        return plugin.anyTrigger(body, out);
    }
}
