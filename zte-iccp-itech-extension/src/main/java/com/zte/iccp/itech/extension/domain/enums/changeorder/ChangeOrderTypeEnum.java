package com.zte.iccp.itech.extension.domain.enums.changeorder;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/29 上午11:11
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ChangeOrderTypeEnum {

    /**
     * 普通
     */
    NORMAL,

    /**
     * 多产品联动保障主任务
     */
    MULTI_PRODUCT_GUARANTEE_MAIN_TASK,

    /**
     * 多产品联动保障子任务
     */
    MULTI_PRODUCT_GUARANTEE_SUB_TASK,

    ;
}
