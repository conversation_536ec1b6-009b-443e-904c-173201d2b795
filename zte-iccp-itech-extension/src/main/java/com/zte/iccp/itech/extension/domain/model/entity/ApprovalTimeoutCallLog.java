package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.ApprovalLogTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
/**
 * 技术审批超时通知日志
 *
 * <AUTHOR>
 * @create 2024/10/17 上午11:06
 */
@ApiModel("技术审批超时通知日志")
@Setter
@Getter
@BaseEntity.Info("approval_timeout_call_log")
public class ApprovalTimeoutCallLog extends BaseEntity {

    @ApiModelProperty("呼叫时间")
    @JsonProperty(value = "call_time")
    private Date callTime;

    @ApiModelProperty("呼叫对象")
    @JsonProperty(value = "person")
    private String person;

    @ApiModelProperty("呼叫结果")
    @JsonProperty(value = "result")
    private String result;

    @ApiModelProperty("呼叫内容")
    @JsonProperty(value = "content")
    private String content;

    @ApiModelProperty("呼叫类型")
    @JsonProperty(value = "type")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApprovalLogTypeEnum type;

    @ApiModelProperty("错误日志")
    @JsonProperty(value = "error_info")
    private String errorInfo;

    @ApiModelProperty("电话号码")
    @JsonProperty(value = "telephone")
    private String telephone;

    public ApprovalTimeoutCallLog() {}

    public ApprovalTimeoutCallLog(String person, String result, String content, ApprovalLogTypeEnum type, String telephone) {
        this.callTime = new Date();
        this.person = person;
        this.result = result;
        this.content = content;
        this.type = type;
        this.telephone = telephone;
    }
}
