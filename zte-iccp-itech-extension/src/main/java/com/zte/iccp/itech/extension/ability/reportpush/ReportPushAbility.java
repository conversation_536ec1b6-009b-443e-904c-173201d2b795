package com.zte.iccp.itech.extension.ability.reportpush;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.utils.CommonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.common.utils.StringBuilderUtils;
import com.zte.iccp.itech.extension.domain.constant.EmailConsts;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.infrastructure.conditions.ApproverCfgQueryCondition;
import com.zte.iccp.itech.extension.infrastructure.repositories.ApproverConfigRepo;
import com.zte.iccp.itech.extension.openapi.model.reportpush.NetChangeReportOrgVO;
import com.zte.iccp.itech.extension.openapi.model.reportpush.NetChangeReportVO;
import com.zte.iccp.itech.extension.spi.client.EmailClient;
import com.zte.iccp.itech.extension.spi.client.ICenterClient;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum.PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE;
import static com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum.NETWORK_CHANGE_REPORT_DATA_AUTHORIZATION;
import static com.zte.iccp.itech.extension.domain.enums.ReportPushTitleEnum.*;
import static com.zte.iccp.itech.extension.domain.enums.ReportPushTitleEnum.ONE_STAR;

/**
 * <AUTHOR>
 * @date 2025/4/17 下午8:08
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class ReportPushAbility {

    private static final String TITLE_ONE = "netchange.report.push.title.one";

    private static final String TITLE_TWO = "netchange.report.push.title.two";

    private static final String TITLE_THREE = "netchange.report.push.title.three";

    private static final String TITLE_FOUR = "netchange.report.push.title.four";

    private static final String TITLE_FIVE = "netchange.report.push.title.five";

    private static final String TITLE_SIX = "netchange.report.push.title.six";

    private static final String TITLE_SEVEN = "netchange.report.push.title.seven";

    private static final String TITLE_EIGHT = "netchange.report.push.title.eight";

    private static final String INTERNAL = "INTERNAL";

    private static final String COPERATOR = "COPERATOR";

    private static final String FORMAT = "%s%s" + ORDER.getName();

    private static final String COMMA = "、";

    private static final String EMAIL_TITLE = "title";
    private static final String EMAIL_INFO = "info";
    private static final String URL = "url";
    private static final String TITLE_LOCATION = "emailtemplate/tabletitlehtml.txt";
    private static final String INFO = "${INFO}";

    public static String getTitle(List<NetChangeReportOrgVO> reportVos, String titleDate, String responsibleDept) {

        if (CollectionUtils.isEmpty(reportVos)) {
            return null;
        }

        List<String> interProdTitles = reportVos.stream()
                .filter(vo -> INTERNAL.equals(vo.getType()) && vo.getOperatorOrderCount() > 0)
                .map(vo -> String.format(FORMAT, vo.getProdTeam(), vo.getOperatorOrderCount()))
                .collect(Collectors.toList());

        List<String> hzfProdTitles = reportVos.stream()
                .filter(vo -> COPERATOR.equals(vo.getType()) && vo.getOperatorOrderCount() > 0)
                .map(vo -> String.format(FORMAT, vo.getProdTeam(), vo.getOperatorOrderCount()))
                .collect(Collectors.toList());

        List<String> govProdTitles = reportVos.stream()
                .collect(Collectors.groupingBy(NetChangeReportVO::getProdTeam))
                .entrySet().stream()
                .filter(entry -> entry.getValue().stream().mapToInt(NetChangeReportVO::getXjOrderCount).sum() > 0)
                .map(entry -> String.format(FORMAT, entry.getKey(),
                        entry.getValue().stream().mapToInt(NetChangeReportVO::getXjOrderCount).sum()))
                .collect(Collectors.toList());

        List<String> interOperatorTitles = getOperatorTitles(reportVos, INTERNAL);

        List<String> interOpLevelTitles = getOpLevelTitles(reportVos, INTERNAL);

        List<String> hzfOperatorTitles = getOperatorTitles(reportVos, COPERATOR);

        List<String> hzfOpLevelTitles = getOpLevelTitles(reportVos, COPERATOR);

        List<String> starTitles = getStarTitles(reportVos);


        int interEmployeeOpTotal = reportVos.stream()
                .filter(vo -> INTERNAL.equals(vo.getType()))
                .mapToInt(NetChangeReportVO::getOperatorOrderCount)
                .sum();
        int hzfEmployeeOpTotal = reportVos.stream()
                .filter(vo -> COPERATOR.equals(vo.getType()))
                .mapToInt(NetChangeReportVO::getOperatorOrderCount)
                .sum();

        int operatorTotal = reportVos.stream().mapToInt(NetChangeReportVO::getOperatorOrderCount).sum();
        int govTotal = reportVos.stream().mapToInt(NetChangeReportVO::getXjOrderCount).sum();
        int total = operatorTotal + govTotal;

        //消息内容
        StringBuilder title = new StringBuilder();

        title.append(MsgUtils.getLangMessage(ZH_CN, TITLE_ONE, titleDate, responsibleDept, total));

        if (operatorTotal > 0) {
            title.append(MsgUtils.getLangMessage(ZH_CN, TITLE_TWO, operatorTotal));
        }

        if (interEmployeeOpTotal > 0) {
            String interDimensionTitle = MsgUtils.getLangMessage(ZH_CN, TITLE_SIX, String.join(COMMA, interProdTitles)) +
                    MsgUtils.getLangMessage(
                            ZH_CN, TITLE_SEVEN, String.join(COMMA, interOperatorTitles), String.join(COMMA, interOpLevelTitles));

            title.append(MsgUtils.getLangMessage(ZH_CN, TITLE_THREE, interEmployeeOpTotal, interDimensionTitle));
        }

        if (hzfEmployeeOpTotal > 0) {
            String hzfDimensionTitle = MsgUtils.getLangMessage(ZH_CN, TITLE_SIX, String.join(COMMA, hzfProdTitles)) +
                    MsgUtils.getLangMessage(
                            ZH_CN, TITLE_SEVEN, String.join(COMMA, hzfOperatorTitles), String.join(COMMA, hzfOpLevelTitles));

            title.append(MsgUtils.getLangMessage(ZH_CN, TITLE_FOUR, hzfEmployeeOpTotal, hzfDimensionTitle));
        }

        if (govTotal > 0) {
            String govDimensionTitle = MsgUtils.getLangMessage(ZH_CN, TITLE_SIX, String.join(COMMA, govProdTitles)) +
                    MsgUtils.getLangMessage(ZH_CN, TITLE_EIGHT, String.join(COMMA, starTitles));
            title.append(MsgUtils.getLangMessage(ZH_CN, TITLE_FIVE, govTotal, govDimensionTitle));
        }
        return title.toString();
    }

    private static List<String> getOpLevelTitles(List<NetChangeReportOrgVO> reportVos, String type) {
        int criticalTotal = reportVos.stream().filter(vo -> type.equals(vo.getType())).mapToInt(NetChangeReportVO::getCritical).sum();
        int importantTotal = reportVos.stream().filter(vo -> type.equals(vo.getType())).mapToInt(NetChangeReportVO::getImportant).sum();
        int normalTotal = reportVos.stream().filter(vo -> type.equals(vo.getType())).mapToInt(NetChangeReportVO::getNormal).sum();
        List<String> interOpLevelTitles = Lists.newArrayList();
        if (criticalTotal > 0) {
            interOpLevelTitles.add(String.format(FORMAT, CRITICAL.getName(), criticalTotal));
        }
        if (importantTotal > 0) {
            interOpLevelTitles.add(String.format(FORMAT, IMPORTANT.getName(), importantTotal));
        }
        if (normalTotal > 0) {
            interOpLevelTitles.add(String.format(FORMAT, NORMAL.getName(), normalTotal));
        }
        return interOpLevelTitles;
    }

    private static List<String> getStarTitles(List<NetChangeReportOrgVO> reportVos) {
        List<String> starTitles = Lists.newArrayList();
        int sixStarTotal = reportVos.stream().mapToInt(NetChangeReportVO::getSixStar).sum();
        int fiveStarTotal = reportVos.stream().mapToInt(NetChangeReportVO::getFiveStar).sum();
        int fourStarTotal = reportVos.stream().mapToInt(NetChangeReportVO::getFourStar).sum();
        int threeStarTotal = reportVos.stream().mapToInt(NetChangeReportVO::getThreeStar).sum();
        int twoStarTotal = reportVos.stream().mapToInt(NetChangeReportVO::getTwoStar).sum();
        int oneStarTotal = reportVos.stream().mapToInt(NetChangeReportVO::getOneStar).sum();
        if (sixStarTotal > 0) {
            starTitles.add(String.format(FORMAT, SIX_STAR.getName(), sixStarTotal));
        }
        if (fiveStarTotal > 0) {
            starTitles.add(String.format(FORMAT, FIVE_STAR.getName(), fiveStarTotal));
        }
        if (fourStarTotal > 0) {
            starTitles.add(String.format(FORMAT, FOUR_STAR.getName(), fourStarTotal));
        }
        if (threeStarTotal > 0) {
            starTitles.add(String.format(FORMAT, THREE_STAR.getName(), threeStarTotal));
        }
        if (twoStarTotal > 0) {
            starTitles.add(String.format(FORMAT, TWO_STAR.getName(), twoStarTotal));
        }
        if (oneStarTotal > 0) {
            starTitles.add(String.format(FORMAT, ONE_STAR.getName(), oneStarTotal));
        }
        return starTitles;
    }

    private static List<String> getOperatorTitles(List<NetChangeReportOrgVO> reportVos, String type) {
        List<String> interOperatorTitles = Lists.newArrayList();
        int zgydTotal = reportVos.stream().filter(vo -> type.equals(vo.getType())).mapToInt(NetChangeReportVO::getZgyd).sum();
        int zgdxTotal = reportVos.stream().filter(vo -> type.equals(vo.getType())).mapToInt(NetChangeReportVO::getZgdx).sum();
        int zgltTotal = reportVos.stream().filter(vo -> type.equals(vo.getType())).mapToInt(NetChangeReportVO::getZglt).sum();
        int zggdTotal = reportVos.stream().filter(vo -> type.equals(vo.getType())).mapToInt(NetChangeReportVO::getZggd).sum();
        int otherTotal = reportVos.stream().filter(vo -> type.equals(vo.getType())).mapToInt(NetChangeReportVO::getOther).sum();
        if (zgydTotal > 0) {
            interOperatorTitles.add(String.format(FORMAT, CM.getName(), zgydTotal));
        }
        if (zgdxTotal > 0) {
            interOperatorTitles.add(String.format(FORMAT, CT.getName(), zgdxTotal));
        }
        if (zgltTotal > 0) {
            interOperatorTitles.add(String.format(FORMAT, CU.getName(), zgltTotal));
        }
        if (zggdTotal > 0) {
            interOperatorTitles.add(String.format(FORMAT, CBN.getName(), zggdTotal));
        }
        if (otherTotal > 0) {
            interOperatorTitles.add(String.format(FORMAT, OTHER.getName(), otherTotal));
        }
        return interOperatorTitles;
    }

    public static void sendMessage(String title, List<String> empNos) {
        Map<String, Object> data = MapUtils.newHashMap(EmailConsts.ICENTER_MESSAGE_TITLE_ZH, title,
                EmailConsts.ICENTER_MESSAGE_TITLE_EN, title,
                EmailConsts.MESSAGE_INFO_ZH, EMPTY_STRING,
                EmailConsts.MESSAGE_INFO_EN, EMPTY_STRING);
        ICenterClient.sendICenter(TemplateIdEnum.NETWORK_CHANGE_REPORT_PUSH, empNos, data);
    }

    public static void sendMail(String title, String mailInfo, String url, List<String> empNos) {
        Map<String, Object> mailData = MapUtils.newHashMap(EMAIL_TITLE, title,
                EMAIL_INFO, mailInfo,
                URL, url);
        EmailClient.sendMail(TemplateIdEnum.REPORT_MAIL_PUSH, empNos, mailData);
    }

    public static List<String> getEmpNos(String responsibleDept, List<String> pordIds) {
        ApproverCfgQueryCondition condition = new ApproverCfgQueryCondition(PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE);
        condition.setResponsibleDept(responsibleDept);
        condition.setRoles(Lists.newArrayList(NETWORK_CHANGE_REPORT_DATA_AUTHORIZATION));
        if (CollectionUtils.isEmpty(pordIds)) {
            condition.setProdOperationTeamIsEmpty(true);
        } else {
            condition.setProdOperationTeams(pordIds);
        }
        List<ApproverConfiguration> configurations = ApproverConfigRepo.query(condition);

        List<String> empNos = Lists.newArrayList();
        configurations.forEach(item -> {
            empNos.addAll(ApproverConfigAbility.getPersonUuidList(item.getApproverPersons()));
            empNos.addAll(ApproverConfigAbility.getPersonUuidList(item.getApproverGroups()));
        });
        return empNos;
    }

    public static String getTableTitle(String replacement) {
        Map<String, String> replacements = MapUtils.newHashMap(INFO, replacement);
        return StringBuilderUtils.replaceAll(CommonUtils.getTemplateString(TITLE_LOCATION), replacements);
    }

    public static List<String> getEmpNosByPordLine(String responsibleDept, List<String> pordLineIds) {
        ApproverCfgQueryCondition condition = new ApproverCfgQueryCondition(PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE);
        if (StringUtils.isBlank(responsibleDept)) {
            condition.setResponsibleDeptIsEmpty(true);
        } else {
            condition.setResponsibleDept(responsibleDept);
        }
        condition.setRoles(Lists.newArrayList(NETWORK_CHANGE_REPORT_DATA_AUTHORIZATION));
        condition.setProdLines(pordLineIds);
        List<ApproverConfiguration> configurations = ApproverConfigRepo.query(condition);

        List<String> empNos = Lists.newArrayList();
        configurations.forEach(item -> {
            empNos.addAll(ApproverConfigAbility.getPersonUuidList(item.getApproverPersons()));
            empNos.addAll(ApproverConfigAbility.getPersonUuidList(item.getApproverGroups()));
        });
        return empNos;
    }
}
