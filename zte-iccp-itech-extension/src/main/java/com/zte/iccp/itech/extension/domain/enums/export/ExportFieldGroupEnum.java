package com.zte.iccp.itech.extension.domain.enums.export;

import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 导出字段分组枚举
 * 用于管理导出字段的分组
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ExportFieldGroupEnum {
    /**
     * 网络变更操作申请
     */
    NETWORK_CHANGE_APPLICATION("network.change.operation.ticket"),

    /**
     * 网络责任人审核
     */
    NET_OWNER_REVIEW("network.owner"),

    /**
     * 办事处产品经理审核
     */
    OFFICE_MANAGER("office.manager"),

    /**
     * 核心网大区TD审核
     */
    CN_REGIONAL_TD_REVIEW("ccn.reviewed.regional.TD"),

    /**
     * 代表处TD审核
     */
    TD_REVIEW("td.review"),

    /**
     * 技术交付部/网络处审核
     */
    TECH_DELIVERY_DEPT_REVIEW("technical.delivery.department_network.department"),

    /**
     * 网络服务部审核
     */
    NETWORK_SERVICE_DEPT_REVIEW("net.service.dept"),

    /**
     * 研发经理审核
     */
    RD_MANAGER_REVIEW("dev.manager"),

    /**
     * 研发领导审核
     */
    RD_LEADER_REVIEW("dev.leader"),

    /**
     * 操作计划确认与通告
     */
    OPERATION_PLAN_CONFIRMATION("operation.confirm.and.announce"),

    /**
     * 反馈操作结果
     */
    OPERATION_RESULT_FEEDBACK("provide.operation.result"),

    /**
     * 审核确认
     */
    REVIEW_CONFIRMATION("fp.review.confirmed"),

    /**
     * 操作及支持人员列表
     */
    OPERATION_PERSONNEL("operation.support.personnel.list"),

    /**
     * 局点信息
     */
    OFFICE_INFO("office.info"),

    /**
     * CCN Checklist
     */
    CCN_CHECKLIST("ccn.checklist"),

    /**
     * 其他信息
     */
    OTHER_INFO("additional.information");

    /**
     * 国际化消息键
     */
    private final String messageKey;

    /**
     * 获取国际化显示名称
     *
     * @return 国际化显示名称
     */
    public String getDisplayName() {
        return MsgUtils.getMessage(messageKey);
    }
}