package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 网络属性枚举
 *
 * <AUTHOR>
 * @date 2024-04-30 下午2:25
 **/
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum NetworkAttributeEnum {
    /**
     * 风险网络
     */
    RISK(1, "风险网络", "Risk Network"),

    /**
     * 重要网络
     */
    IMPORTANT(2, "重要网络", "Important Network"),

    /**
     * 普通网络
     */
    NORMAL(3, "普通网络", "Common Network"),
    ;

    private final Integer value;

    private final String nameZh;

    private final String nameEn;

    public static NetworkAttributeEnum fromValue(String value) {
        for (NetworkAttributeEnum networkAttribute : NetworkAttributeEnum.values()) {
            if (String.valueOf(networkAttribute.getValue()).equals(value)) {
                return networkAttribute;
            }
        }

        return null;
    }
}
