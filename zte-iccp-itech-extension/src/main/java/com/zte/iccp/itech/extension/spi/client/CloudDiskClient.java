package com.zte.iccp.itech.extension.spi.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.EncryptorUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.spi.model.clouddisk.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.ddm.domain.helper.util.ApiClient;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.*;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;
import static org.apache.http.HttpHeaders.ACCEPT;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/15
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CloudDiskClient {

    private static final String RAW_APP_CODE = "cloudDiskSDK.xOriginServiceName";

    private static final String RAW_ORG_ID = "cloudDiskSDK.xOrgId";

    private static final String RAW_SECRET_KEY = "cloudDiskSDK.xSecretKey";

    private static final String GET_FILE_DOWNLOAD_TOKEN = "clouddisk.url.getDownloadToken";

    private static final String DOWNLOAD_BY_TOKEN = "clouddisk.url.downloadByToken";

    private static final String QUERY_FILE_STATUS = "clouddisk.url.queryFileStatus";

    private static final String SEND_PART_DATA = "clouddisk.url.sendPartData";

    private static final String FINISH_PART_OBJECT = "clouddisk.url.finishPartObject";

    private static final String QUERY_FILE_INFO = "clouddisk.url.queryFileInfo";

    @SneakyThrows
    public static ServiceData<String> getFileDownloadUrl(
            String empNo,
            String fileKey,
            String filename,
            Integer limit,
            Integer expiresHour,
            Boolean encryptEnable,
            Object encryptEnum,
            Boolean fixedRead) {

        /* Started by AICoder, pid:640d7n98dfg4b33149c8088c3023c82a24762f46 */
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String url = getUrl(
                    GET_FILE_DOWNLOAD_TOKEN,
                    "key", fileKey,
                    "downloadName", filename,
                    "limitCount", limit,
                    "expireTime", expiresHour);

            HttpPost httpPost = new HttpPost(url);
            setHeaders(httpPost, empNo);

            String respJson;
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                /* Ended by AICoder, pid:640d7n98dfg4b33149c8088c3023c82a24762f46 */
                respJson = EntityUtils.toString(response.getEntity());
            }
            ServiceData<String> token = JsonUtils.parseObject(respJson, ServiceData.class);

            if (!RetCode.SUCCESS_CODE.equals(token.getCode().getCode())) {
                throw new LcapBusiException(token.getCode().getMsgId());
            }

            String downloadUrl = ConfigHelper.get(DOWNLOAD_BY_TOKEN)
                    .replace("{appCode}", ConfigHelper.getRaw(RAW_APP_CODE))
                    .replace("{token}", token.getBo());
            token.setBo(downloadUrl);
            return token;
        }
    }

    @SneakyThrows
    public static ServiceData<ObjectTransStatus> queryFileStatusByBytes(
            QueryFileStatusInput queryFileStatusInput, byte[] bytes) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String url = getUrl(
                    QUERY_FILE_STATUS,
                    "fileMd5", queryFileStatusInput.getFileMd5(),
                    "fileSize", queryFileStatusInput.getFileSize());
            HttpPost httpPost = new HttpPost(url);
            setHeaders(httpPost, queryFileStatusInput.getEmpNo());
            httpPost.setEntity(new ByteArrayEntity(bytes, 0, 1 << 8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                bytes = IOUtils.toByteArray(response.getEntity().getContent());
                String respJson = new String(bytes, StandardCharsets.UTF_8);
                return JsonUtils.parseObject(respJson, new TypeReference<ServiceData<ObjectTransStatus>>() {});
            }
        }
    }

    @SneakyThrows
    public static ServiceData<Boolean> sendPartData(
            byte[] bytes,
            SendPartDataInput sendPartDataInput) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String url = getUrl(SEND_PART_DATA);
            HttpPost httpPost = new HttpPost(url);
            setHeaders(httpPost, sendPartDataInput.getEmpNo());
            setMultipartFormBody(httpPost, sendPartDataInput.getFileName(), bytes,
                    "fileMd5", sendPartDataInput.getFileMd5(),
                    "chunk", sendPartDataInput.getChunks());

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                bytes = IOUtils.toByteArray(response.getEntity().getContent());
                String respJson = new String(bytes, StandardCharsets.UTF_8);
                return JsonUtils.parseObject(respJson, ServiceData.class);
            }
        }
    }

    @SneakyThrows
    public static ServiceData<FileSaveResult> finishPartObject(FinishPartObjectInput finishPartObjectInput) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String url = getUrl(FINISH_PART_OBJECT);
            HttpPost httpPost = new HttpPost(url);
            setHeaders(httpPost, finishPartObjectInput.getEmpNo());

            setFormBody(httpPost,
                    "fileMd5", finishPartObjectInput.getFileMd5(),
                    "fileName", finishPartObjectInput.getFileName(),
                    "fileSize", finishPartObjectInput.getFileSize(),
                    "md5DirectTransfer", true);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                byte[] bytes = IOUtils.toByteArray(response.getEntity().getContent());
                String respJson = new String(bytes, StandardCharsets.UTF_8);
                return JsonUtils.parseObject(respJson, new TypeReference<ServiceData<FileSaveResult>>() {});
            }
        }
    }

    private static void setHeaders(HttpRequestBase requestBase, String empNo) {
        String appCode = ConfigHelper.getRaw(RAW_APP_CODE);
        String orgId = ConfigHelper.getRaw(RAW_ORG_ID);
        String secretKey = ConfigHelper.getRaw(RAW_SECRET_KEY);

        // 验证和清理敏感信息
        appCode = sanitize(appCode);
        orgId = sanitize(orgId);
        secretKey = sanitize(secretKey);

        String timeStamp = String.valueOf(System.currentTimeMillis());
        String authValue = EncryptorUtils.hmacSha256(appCode + empNo + secretKey + orgId + timeStamp, secretKey);

        requestBase.addHeader(ACCEPT, APPLICATION_JSON);
        requestBase.addHeader(X_EMP_NO, empNo);
        requestBase.addHeader(X_ORIGIN_SERVICENAME, appCode);
        requestBase.addHeader(X_ORG_ID, orgId);
        requestBase.addHeader(X_AUTH_VALUE, authValue);
        requestBase.addHeader(X_TIMESTAMP, timeStamp);
    }

    // 敏感信息清理方法
    private static String sanitize(String input) {
        if (input == null) {
            return StringUtils.EMPTY;
        }
        return input.replaceAll("[^-\\w]", StringUtils.EMPTY);
    }

    @SneakyThrows
    private static String getUrl(String baseUrlKey, Object... params) {
        String baseUrl = ConfigHelper.get(baseUrlKey);
        if (params.length == 0) {
            return baseUrl;
        }

        StringBuilder query = new StringBuilder();
        for (int i = 0; i < params.length - 1; i += 2) {
            String key = params[i].toString();
            Object value = params[i + 1];
            if (value == null || StringUtils.isBlank(value.toString())) {
                continue;
            }

            query.append(key)
                    .append("=")
                    .append(URLEncoder.encode(value.toString(), StandardCharsets.UTF_8.displayName()))
                    .append("&");
        }

        if (query.length() > 0) {
            query.insert(0, baseUrl.contains("?") ? "&" : "?");
            query.deleteCharAt(query.length() - 1);
        }

        return baseUrl + query;
    }

    @SneakyThrows
    private static void setFormBody(HttpEntityEnclosingRequestBase requestBase, Object... params) {
        List<NameValuePair> form = new ArrayList<>();
        for (int i = 0; i < params.length - 1; i += 2) {
            final int j = i;
            form.add(new NameValuePair() {
                @Override
                public String getName() {
                    return params[j].toString();
                }

                @Override
                public String getValue() {
                    return params[j + 1] == null
                            ? null : params[j + 1].toString();
                }
            });
        }

        requestBase.setEntity(new UrlEncodedFormEntity(form, StandardCharsets.UTF_8));
    }

    private static void setMultipartFormBody(
            HttpEntityEnclosingRequestBase requestBase,
            String filename,
            byte[] bytes,
            Object... params) {
        filename = StringUtils.isBlank(filename)
                ? String.valueOf(System.currentTimeMillis()) : filename;

        /* Started by AICoder, pid:v0d192bb1ae5139146c60898c0adf40e024571ad */
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addBinaryBody("file", bytes, ContentType.DEFAULT_BINARY, filename);
        for (int i = 0; i < params.length - 1; i += 2) {
            builder.addTextBody(params[i].toString(), params[i + 1] == null ? null : params[i + 1].toString());
        }

        requestBase.setEntity(builder.build());
        /* Ended by AICoder, pid:v0d192bb1ae5139146c60898c0adf40e024571ad */
    }

    @SneakyThrows
    public static List<FileInfoResp> queryFileInfoByKeys(List<String> fileKeys) {

        // 1.请求头
        String appCode = sanitize(ConfigHelper.getRaw(RAW_APP_CODE));
        String orgId = sanitize(ConfigHelper.getRaw(RAW_ORG_ID));
        String secretKey = sanitize(ConfigHelper.getRaw(RAW_SECRET_KEY));
        String empNo = ContextHelper.getEmpNo();
        String timeStamp = String.valueOf(System.currentTimeMillis());
        String authValue = EncryptorUtils.hmacSha256(appCode + empNo + secretKey + orgId + timeStamp, secretKey);

        Map<String, Object> headers = MapUtils.newHashMap(
                ACCEPT, APPLICATION_JSON,
                X_EMP_NO, empNo,
                X_ORIGIN_SERVICENAME, appCode,
                X_ORG_ID, orgId,
                X_AUTH_VALUE, authValue,
                X_TIMESTAMP, timeStamp);

        // 2.请求体
        QueryFileInfoReq queryFileInfoReq = new QueryFileInfoReq();
        queryFileInfoReq.setFileKey(fileKeys);
        queryFileInfoReq.setAppCode(ConfigHelper.getRaw(RAW_APP_CODE));

        // 3.对外查询
        Object fileInfo = ApiClient.invokeServiceByUrl(
                ConfigHelper.get(QUERY_FILE_INFO), POST, null, queryFileInfoReq, headers);
        return JsonUtils.parseArray(fileInfo, FileInfoResp.class);
    }
}