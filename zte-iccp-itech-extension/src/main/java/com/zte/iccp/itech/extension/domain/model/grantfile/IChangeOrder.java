package com.zte.iccp.itech.extension.domain.model.grantfile;

import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.TriggerTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/23
 */
public interface IChangeOrder {
    String getId();

    String getCreateBy();
    Date getCreateTime();

    Date getLastModifiedTime();

    String getOrderNo();

    String getOperationSubject();

    String getCustomerId();

    String getCustomerTypeFlag();

    MultiLangText getCountry();

    MultiLangText getProvince();

    MultiLangText getCity();

    String getResponsibleDept();

    String getProductCategory();

    String getOperationTypeGroup();

    String getOperationType();

    String getOperationReason();

    OperationLevelEnum getOperationLevel();

    void setOperationLevel(OperationLevelEnum value);

    TimeZoneEnum getTimeZone();

    AttachmentFile getNeListFile();

    List<String> getSaGrantFileReceiver();

    BoolEnum getIsBusinessOperation();

    TriggerTypeEnum getTriggerType();

    BoolEnum getIsFirstTimeApply();

    BoolEnum getIsNeedAuthorizationFile();

    // BEGIN 非属性getter
    Integer getBusiInterruptDuration();

    String getOperationReasonZh();

    String getOperationReasonEn();

    Date getOperationStartTime();

    Date getOperationEndTime();
    // END 非属性getter

    /** 是否紧急操作映射 */
    String isEmergencyOperationExt(String langId);

    List<Employee> getMailCopyExt();

    List<Employee> getMailCopy1Ext();

    List<Employee> getMailCopy2Ext();

    List<Employee> getMailCopy3Ext();

    List<Employee> getMailCopy4Ext();

    List<Employee> getMailCopy5Ext();

    List<Employee> getMailCopy6Ext();

    BoolEnum getIsGovEnt();

    String getOperationTypeName(String langId);

    String getLogicalNe();

    List<TextValuePair> getGuaranteeMode();

    /** 操作说明 */
    String getOperationDesc();

    /** 数据来源 */
    String getSource();
}
