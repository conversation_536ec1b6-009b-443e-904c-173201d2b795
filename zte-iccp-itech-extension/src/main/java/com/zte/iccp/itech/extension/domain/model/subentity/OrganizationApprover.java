package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.PermissionApplication;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.subentity.OrganizationApproverConsts.APPROVER;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OrganizationApproverConsts.ORGANIZATION;


/**
 * <AUTHOR>
 * @date 2024/10/9 下午6:49
 */
@Setter
@Getter
@BaseSubEntity.Info(value = "organization_approver", parent = PermissionApplication.class)
public class OrganizationApprover extends BaseSubEntity {

    @JsonProperty(value = ORGANIZATION)
    private String organization;

    @JsonProperty(value = APPROVER)
    private List<TextValuePair> approver;
}
