package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.PersonnelAbilty;
import com.zte.iccp.itech.extension.ability.changeorder.*;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ChangeOrderTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.FlowVariantEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiProductLinkageGuarantee;
import com.zte.iccp.itech.extension.domain.model.vo.ConfirmCopy;
import com.zte.iccp.itech.extension.domain.model.vo.OperatorCopy;
import com.zte.iccp.itech.extension.plugin.operation.changeorder.batchtask.ChangeCommitOperationPlugin;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import  com.zte.iccp.itech.extension.plugin.form.approvaldetails.changeorder.enums.NodeWithInlinePageEnum;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.SHOW_ERROR_MSG_TIPS_CID;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.SatisfactionResponsiblePersonConsts.ORGANIZATION_ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.FlowVariantEnum.IS_ADMINISTRATION_LEADER_APPROVAL;

/**
 * 流程节点，批次任务发布通告，计算是否流程变更，是否远程中心审核
 *
 * <AUTHOR>
 * @create 2024/6/24 上午11:12
 */
public class CalculateApprovalNodePlugin extends BaseFlowOperationPlugin {

    private static final List<String> VALID_ROLES = Lists.newArrayList(OperatorRoleEnum.OPERATOR.getValue(),
            OperatorRoleEnum.OPERATING_SUPERVISOR.getValue());

    @Override
    protected boolean beforeValidate(ExecuteEvent executeEvent) {
        IDataModel dataModel = getModel();
        getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.NOTIFICATION_UNDER_REVIEW.getValue());
        IFormView formView = getView();

        // 1.检索批次任务
        String batchId = dataModel.getRootDataEntity().getPkValue().toString();
        BatchTask batchTask = BatchTaskAbility.get(
                batchId, Arrays.asList(ID, CONFIRM_COPY, CHANGE_ORDER_ID, ORGANIZATION_ID));

        // 2.我已知晓 弹窗校验
        boolean noticeConfirmFlag = checkNoticeConfirm(dataModel, batchTask);
        if (!noticeConfirmFlag) {
            // 未确认我已知晓, 弹窗展示案例
            dataModel.setValue(ChangeOrderFieldConsts.AWARE_SUBMISSION, BoolEnum.Y);
            return false;
        }

        // 增加前置校验，如果当前批次的变更单为保障主任务变更单，校验是否符合提交的要求，不符合抛出异常
        if (!executeBeforeCheck(batchTask.getChangeOrderId())) {
            formView.showMessage(MULTI_PROD_NOT_COMMIT_ERROR, MsgType.WARNING);
            return false;
        }

        // 时间校验
        if(!BatchTaskAbility.checkTime(dataModel, formView)){
            return false;
        }

        //人员积分校验
        String orgId = PropertyValueConvertUtil.getString(getModel().getValue(ORGANIZATION_ID));
        IDataEntityCollection operatorDataEntity = getModel().getEntryRowEntities(EntityHelper.getEntityId(BatchTaskOperator.class));
        String msg = PersonnelAbilty.commitPersonCheck(orgId, operatorDataEntity, OPERATOR_ROLE, OPERATE_PERSON);
        if (StringUtils.hasText(msg)) {
            getView().showMessage(msg, MsgType.ERROR);
            ComponentUtils.showTipComponentMsg(formView, SHOW_ERROR_MSG_TIPS_CID, msg);
            return false;
        }

        // 操作人员校验
        boolean operatorFlag = BatchTaskAbility.checkOperators(getModel(),getView(), BatchTaskOperator.class);
        if (operatorFlag) {
            // 重新初始为 N, 支持后续驳回后重新弹窗
            dataModel.setValue(ChangeOrderFieldConsts.AWARE_SUBMISSION_HIDDEN, BoolEnum.N.getValue());
            dataModel.setValue(ChangeOrderFieldConsts.AWARE_SUBMISSION, new OptionsBuilder().build());
        }

        return operatorFlag;
    }

    /**
     * 弹窗提示确认
     */
    private boolean checkNoticeConfirm(IDataModel dataModel, BatchTask batchTask) {
        // 1.校验国内代表处
        String organizationId = batchTask.getOrganizationId();
        if (!StringUtils.hasText(organizationId)) {
            return false;
        }

        // 国际代表处 不弹框
        DeptTypeEnum deptType = ResponsibleUtils.getDeptType(organizationId);
        if (DeptTypeEnum.INTER.equals(deptType)) {
            return true;
        }

        // 2.获取 我已知晓 组件结果
        String confirmInfo = PropertyValueConvertUtil.getString(
                dataModel.getValue(ChangeOrderFieldConsts.AWARE_SUBMISSION_HIDDEN));
        return BoolEnum.Y.getValue().equals(confirmInfo);
    }

    @Override
    public void beforeOperate(ExecuteEvent executeEvent) {
        String batchId = getModel().getRootDataEntity().getPkValue().toString();

        BatchTask batchTask = BatchTaskAbility.get(batchId, Arrays.asList(ID, CONFIRM_COPY, CHANGE_ORDER_ID,
                APPROVAL_NUM, IS_CHANGE_ADMIN_APPROVAL, OPERATION_TYPE_GROUP));
        getModel().setValue(APPROVAL_STATUS, BoolEnum.Y.getPropValue());
        ConfirmCopy confirmCopy = JsonUtils.parseObject(batchTask.getConfirmCopy(), ConfirmCopy.class);
        if (confirmCopy == null) {
            return;
        }

        String changeRemoteApproval = TextValuePairHelper.getValue(getModel().getValue(IS_CHANGE_REMOTE_APPROVAL));
        // 1判断是否需要远程中心审批
        //是否远程中心审核规则：网络变更单是否网络变更单审核 && 计划开始时间结束时间是否改变
        boolean isNeedRemote = false;
        if (CommonConstants.ENABLED_FLAG.equals(changeRemoteApproval)) {
            long pageStartTime = ((Date) getModel().getValue(PLAN_OPERATION_START_TIME)).getTime();
            long pageEndTime = ((Date) getModel().getValue(PLAN_OPERATION_END_TIME)).getTime();

            long changeSatrtTime = ((Date) getModel().getValue(CHANGE_OPERATION_TIME_START)).getTime();
            long changeEndTime = ((Date) getModel().getValue(CHANGE_OPERATION_TIME_END)).getTime();
            boolean isNotNeedRemote = pageStartTime == changeSatrtTime && pageEndTime == changeEndTime;
            List<TextValuePair> result = (isNotNeedRemote ? BoolEnum.N.getPropValue() : BoolEnum.Y.getPropValue());
            getModel().setValue(IS_NEED_REMOTE_APPROVAL, result);
            isNeedRemote = true;
        }

        // 2计算是否需要行政领导审批
        BoolEnum oldAdminapprove = batchTask.getIsChangeAdminApproval();
        if (batchTask.getApprovalNum() > 0) {
            // 后续根据历史记录判断
            if (BoolEnum.Y == confirmCopy.getUrgentFlag()
                    || BoolEnum.Y == confirmCopy.getControlPeriodFlag()) {
                oldAdminapprove = BoolEnum.Y;
            }else{
                oldAdminapprove = BoolEnum.N;
            }
        }

        String isUrgent = TextValuePairHelper.getValue(getModel().getValue(URGENT_FLAG));
        String isControlPeriod = TextValuePairHelper.getValue(getModel().getValue(CONTROL_PERIOD_FLAG));
        BoolEnum newAdminapprove = BoolEnum.N;
        //  不是配合保障 and (是紧急操作 or 是封网、管控期操作) == 需要行政审核
        if (BatchTaskAbility.isAdministrationApproval(batchTask.getOperationTypeGroup(),isUrgent,isControlPeriod)) {
            newAdminapprove = BoolEnum.Y;
        }

        ChangeOrder changeOrder = ChangeOrderAbility.get(batchTask.getChangeOrderId(), new ArrayList<>());
        Map<String, Object> objectObjectMap = getApprovalFlowValueMap(getModel(), changeOrder, confirmCopy, isNeedRemote, oldAdminapprove, newAdminapprove);
        objectObjectMap.put(FlowVariantEnum.ADMIN_APPROVAL_RECOMMEND_LEVEL_NUM.getKey(),
                BatchTaskAbility.getApprovalLevel(changeOrder, isUrgent, isControlPeriod));
        // 更新推荐等级
        changeOrder.setIsEmergencyOperation(BoolEnum.valueOf(isUrgent));
        changeOrder.setIsNetCloseOrControlOperation(BoolEnum.valueOf(isControlPeriod));
        NodeWithInlinePageEnum nodeEnum = ApprovalAbility.getRecommendAdminApprovalLvl(changeOrder);
        int levelNum = nodeEnum == null ? 0 : nodeEnum.getLevel();
        objectObjectMap.put(FlowVariantEnum.ADMIN_APPROVAL_RECOMMEND_LEVEL_NUM.getKey(), levelNum);

        // 兼容历史版本，当前历史版本为1 大于1才进行高负荷检测，历史单据缺少流程线路值会有问题
        if (FlowHelper.getCustomizeFlowVersion(batchId) > 1) {
            handleHighLoadInterception(getModel());
            highLoadIntercepFlowVariantSet(getModel(), batchId, objectObjectMap, false);
        }

        FlowHelper.changeFlowParams(batchId, objectObjectMap, ApproveFlowCodeEnum.BATCH_TASK_FLOW);

        ChangeCommitOperationPlugin plugin = new ChangeCommitOperationPlugin();
        plugin.defineFlagData(getModel());
    }

    /**
     * 高负荷拦截流程变量设置
     *
     * @param batchId 批次id
     * @param flowVariantMap 流程变量map
     */
    public void highLoadIntercepFlowVariantSet(IDataModel dataModel,String batchId,
                                                Map<String, Object> flowVariantMap,
                                                boolean isHzfBatch) {
        // 默认为N
        flowVariantMap.put(FlowVariantEnum.IS_HIGH_LOAD.getKey(), BoolEnum.N.name());
        // 高负荷场景
        Object highLoadInterceptionValue = dataModel.getValue(OPERATION_CHANGE_DESC);
        if (highLoadInterceptionValue != null) {
            // 是否远程中心审批，默认为N
            BoolEnum isNeedRemoteApproval = BoolEnum.N;
            // 操作计划审核自定义编码
            String extendedCode;
            // 是否行政领导审批
            BoolEnum isAdministrationLeaderApproval;
            if (isHzfBatch) {
                extendedCode = PartnerApproveNodeEnum.PARTNER_OPERATION_PLAN_APPROVAL.name();
                // 合作方是否行政领导审批从页面取字段
                isAdministrationLeaderApproval = BoolEnum.convertStringToBoolean(
                        TextValuePairHelper.getValue(dataModel.getValue(BatchTaskFieldConsts.URGENT_FLAG_TOO)));
            }else {
                extendedCode = ApproveNodeEnum.CHANGED_BY_REP_PROD_CHIEF.name();
                // 内部从flowVariantMap流程变量中获取，默认为N
                isAdministrationLeaderApproval = BoolEnum.convertStringToBoolean(
                        (String) flowVariantMap.get(IS_ADMINISTRATION_LEADER_APPROVAL.getKey()));
                // 内部才有远程中心审批，从页面取值
                isNeedRemoteApproval = BoolEnum.convertStringToBoolean(
                        TextValuePairHelper.getValue(dataModel.getValue(IS_NEED_REMOTE_APPROVAL)));
            }

            // 1、是否远程中心审批 = N and 是否行政领导审批 = N and 当天审批过操作计划审核 = N
            if (BoolEnum.N == isNeedRemoteApproval
                    && BoolEnum.N == isAdministrationLeaderApproval
                    && !FlowHelper.hasExtendedCodeApprovalToday(batchId, extendedCode)) {

                // 1.1 is_change_person 需要修改为Y，否则可能同时触发两条分支
                dataModel.setValue(PERSON_CHANGE_FLAG, BoolEnum.Y.getPropValue());
                // 1.2 走高负荷到操作计划审核
                flowVariantMap.put(FlowVariantEnum.IS_HIGH_LOAD.getKey(), BoolEnum.Y.name());
            }
            // 2、如果远程中心审批 = Y 则正常走远程中心，高负荷逻辑在远程中心节点 - 页面同意前判断是否修改流程变量控制走向
        }
    }


    /**
     * 待发通告审批页面beforeOperate提交前 - 人员高负荷拦截计算逻辑
     * 新增的字段 - 操作变更说明默认清空，然后根据满足高负荷场景再set真实的值（保证不满足高负荷为空，满足则set值）
     *
     * @param model model
     */
    public void handleHighLoadInterception(IDataModel model) {
        // 默认是清空值的指令,当满足条件默认会将值塞进去，保证在流程其他节点能根据这个值来的结果来判断是否高负荷
        model.setValue(OPERATION_CHANGE_DESC, new OptionsBuilder().build());

        String batchId = (String) model.getRootDataEntity().getPkValue();
        // 网络变更单id
        String changeOrderId = PropertyValueConvertUtil.getString(model.getValue(BatchTaskFieldConsts.CHANGE_ORDER_ID));
        // 确认操作开始日期
        Date planOperationStartDate = (Date) model.getValue(PLAN_OPERATION_START_TIME);
        // 确认操作结束日期
        Date planOperationEndDate = (Date) model.getValue(PLAN_OPERATION_END_TIME);

        Class<? extends BaseEntity> changeOrderEntityClass = ChangeOrder.class;
        String subTabCid = CidConstants.FIELD_INTERNAL_NETWORK_BATCH_OPERATOR_CID;
        if (!EntityHelper.getEntityId(BatchTask.class).equals(model.getMainEntityType().getKey())) {
            changeOrderEntityClass = SubcontractorChangeOrder.class;
            subTabCid = CidConstants.FIELD_SUBCONTRACTOR_NETWORK_BATCH_OPERATOR_CID;
        }

        List<String> operators = new ArrayList<>();
        if (BatchHighLoadInterceptAbility.isTriggerHighLoadInterception(
                changeOrderId, changeOrderEntityClass, planOperationStartDate, planOperationEndDate)) {

            // 构建需要进行高负荷计算的人员集
            IDataEntityCollection dataEntityCollection = model.getEntryRowEntities(subTabCid);
            for (int i = 0; i < dataEntityCollection.size(); i++) {
                DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
                String operatorRoleValue = TextValuePairHelper.getValue(dynamicDataEntity.get(OperatorFieldConsts.OPERATOR_ROLE));
                List<SingleEmployee> employees = JsonUtils.parseArray(dynamicDataEntity.get(OperatorFieldConsts.OPERATE_PERSON), SingleEmployee.class);

                if (!StringUtils.hasText(operatorRoleValue)
                        || employees.isEmpty()
                        || !VALID_ROLES.contains(operatorRoleValue)) {
                    continue;
                }
                operators.add(employees.get(0).getEmpUIID());
            }

            if (CollectionUtils.isEmpty(operators)) {
                return;
            }

            Map<String, Map<LocalDate, Boolean>> employeeIdToHighLoadDateResults =
                    BatchHighLoadInterceptAbility.executeHighLoadComputation(batchId, changeOrderId, changeOrderEntityClass, planOperationEndDate, operators);

            if (CollectionUtils.isEmpty(employeeIdToHighLoadDateResults)) {
                return;
            }


            // 根据employeeIdToHighLoadDateResults的结果组装多行文本【操作变更说明】
            List<MultiLangText> multiLangTextList = BatchHighLoadInterceptAbility.getOperationChangeDesc(employeeIdToHighLoadDateResults);
            // 满足高负荷
            if (!CollectionUtils.isEmpty(multiLangTextList)) {
                model.setValue(OPERATION_CHANGE_DESC, JsonUtils.toJsonString(multiLangTextList));
            }
        }
    }


    @NotNull
    private Map<String, Object> getApprovalFlowValueMap(IDataModel model, ChangeOrder changeOrder,
                                                        ConfirmCopy confirmCopy, boolean isNeedRemote,
                                                        BoolEnum oldAdminapprove, BoolEnum newAdminapprove) {
        Map<String, Object> objectObjectMap = MapUtils.newHashMap();
        // 老单据是不行政审批，新单据是新增审批 需要重新走新增审批
        if (oldAdminapprove == BoolEnum.N && newAdminapprove == BoolEnum.Y) {
            objectObjectMap.put(IS_ADMINISTRATION_LEADER_APPROVAL.getKey(), BoolEnum.Y.getValue());
            // 如果是国际需要创建国家审批
            if (DeptTypeEnum.INTER == ChangeOrderAbility.getDeptType(changeOrder)) {
                // 未找到国际会签审核人 不需要国际会签审核
                if (!InterAdminApproverAbility.saveBatchInterCountersignApprover(changeOrder, BatchTaskAbility.getPageBatchTask(model))) {
                    objectObjectMap.put(IS_ADMINISTRATION_LEADER_APPROVAL.getKey(), BoolEnum.N.getValue());
                }
            }
        } else {
            objectObjectMap.put(IS_ADMINISTRATION_LEADER_APPROVAL.getKey(), BoolEnum.N.getValue());
            // 3计算是否有人员变更,如果第二次发布，操作人员变更已为是，不需要再计算
            operationChangeExt(changeOrder,confirmCopy, isNeedRemote);
        }
        return objectObjectMap;
    }

    /**
     * 国际场景拓展（国际人员变更默认为N）
     * @param changeOrder 网络变更单
     * @param confirmCopy confirmCopy
     * @param isNeedRemote isNeedRemote
     */
    private void operationChangeExt(ChangeOrder changeOrder, ConfirmCopy confirmCopy, boolean isNeedRemote) {
        // 如为国际场景，不计算人员变更（默认为N即可）
        if (DeptTypeEnum.INTER == ChangeOrderAbility.getDeptType(changeOrder)) {
            getModel().setValue(PERSON_CHANGE_FLAG, BoolEnum.N.getPropValue());
            return;
        }

        operationChange(confirmCopy, isNeedRemote);
    }

    /*
     * 判断操作人员是否变更，并给页面赋值
     * */
    private void operationChange(ConfirmCopy confirmCopy, boolean isNeedRemote) {
        // 获取表单填写 操作人员数据
        IDataEntityCollection dataEntityCollection = getModel().getEntryRowEntities(EntityHelper.getEntityId(BatchTaskOperator.class));
        if (dataEntityCollection == null || dataEntityCollection.size() == CommonConstants.INTEGER_ONE) {
            return;
        }

        List<String> batchOperatorList = new ArrayList<>();
        for (int i = CommonConstants.INTEGER_ZERO; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            String operatorRole = TextValuePairHelper.getValue(dynamicDataEntity.get(OperatorFieldConsts.OPERATOR_ROLE));
            List<SingleEmployee> employees = JsonUtils.parseArray(dynamicDataEntity.get(OperatorFieldConsts.OPERATE_PERSON), SingleEmployee.class);
            batchOperatorList.add(String.join(CommonConstants.UNDER_SCORE, operatorRole, employees.get(0).getEmpUIID()));
        }

        // 查询操作操作人员
        List<OperatorCopy> changeOrderOperators = confirmCopy.getOperators();
        boolean isOperationChange;
        if (changeOrderOperators.size() != batchOperatorList.size()) {
            isOperationChange = true;
        } else {
            List<String> changeOperatorList = Lists.newArrayList();
            changeOrderOperators.forEach(item ->
                    changeOperatorList.add(String.join(CommonConstants.UNDER_SCORE,
                            item.getOperatorRole() == null ? CommonConstants.EMPTY_STRING : item.getOperatorRole().getValue(),
                            item.getOperatePerson() == null ? CommonConstants.EMPTY_STRING : item.getOperatePerson()))
            );

            //对比 表单填写的操作人员数据和网络变更单人员数据是否一致
            isOperationChange = !batchOperatorList.stream().allMatch(changeOperatorList::contains);
        }

        // 给表单人员变更 字段赋值
        getModel().setValue(PERSON_CHANGE_FLAG,
                isOperationChange ? BoolEnum.Y.getPropValue() : BoolEnum.N.getPropValue());
    }

    /**
     * 前置校验，如果当前批次的变更单为保障主任务变更单，校验是否符合提交的要求
     *
     * @param changeOrderId 变更单id
     * @return 是否符合放行规则
     */
    private boolean executeBeforeCheck(String changeOrderId){
        // 1.批次任务关联的变更单为普通变更单或为子保障任务均放行。 而主保障任务需要判断余下的全部子保障任务状态
        NetworkChangeAssignment networkChangeAssignment = AssignmentAbility.querySpecificTypeAssignment(
                changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);

        if (networkChangeAssignment == null || ChangeOrderTypeEnum.NORMAL.name().equals(networkChangeAssignment.getType()) ||
                ChangeOrderTypeEnum.MULTI_PRODUCT_GUARANTEE_SUB_TASK.name().equals(networkChangeAssignment.getType())) {
            return true;
        }

        List<MultiProductLinkageGuarantee> multiProductLinkageGuarantees = MultiProdGuaranteeAbility.query(Lists.newArrayList(changeOrderId));
        // 2.1 过滤保障关联中的主保障任务
        multiProductLinkageGuarantees = multiProductLinkageGuarantees.stream().filter(item -> !item.getIsMainTask()).collect(Collectors.toList());

        // 2.2 存在只有保障主任务，子任务全部被删除的情况
        if (CollectionUtils.isEmpty(multiProductLinkageGuarantees)) {
            return true;
        }

        // 2.3 批次任务关联的变更单为保障主任务，检测其余保障子任务状态是否存在待启动、审核中状态的任务
        List<String> assignmentIds = multiProductLinkageGuarantees.stream().map(MultiProductLinkageGuarantee::getAssignmentId).collect(Collectors.toList());
        List<Assignment> assignments = AssignmentAbility.querySpecificTypeAssignment(assignmentIds, Assignment.class);

        assignments = assignments.stream().filter(item ->
                        Lists.newArrayList(AssignmentStatusEnum.START.getValue(),
                                AssignmentStatusEnum.APPROVE.getValue(),
                                AssignmentStatusEnum.APPROVE_START.getValue()).contains(item.getAssignmentStatus()))
                .collect(Collectors.toList());

        // 2.3 不存在待启动、审核中状态的任务，将流程推到下一个节点
        return CollectionUtils.isEmpty(assignments);
    }
}
