package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.TIME_ZONE;

@ApiModel("网络变更任务实体")
@Setter
@Getter
@BaseEntity.Info("assignment")
public class NetworkChangeAssignment extends Assignment {

    @ApiModelProperty("操作类型")
    @JsonProperty(value = NetworkChangeFieldConsts.OPERATION_TYPE)
    private List<TextValuePair> operationType;

    @ApiModelProperty("计划开始时间")
    @JsonProperty(value = NetworkChangeFieldConsts.PLAN_START_TIME)
    private Date planStartTime;

    @ApiModelProperty("操作开始时间（UTC+8）")
    @JsonProperty(value = NetworkChangeFieldConsts.OPERATION_START_TIME_UTC_8)
    private Date operationStartTimeUtc8;

    @ApiModelProperty("时区")
    @JsonProperty(value = TIME_ZONE)
    private String timeZone;

    @ApiModelProperty("批次任务审核实体ID")
    @JsonProperty(value = NetworkChangeFieldConsts.APPROVE_BATCH_TASK_ID)
    private String approveBatchTaskId;

    @ApiModelProperty("SLA 要求完成时间")
    @JsonProperty(value = NetworkChangeFieldConsts.SLA_FINISH_TIME)
    private Date slaFinishTime;

    @ApiModelProperty("国家 / 地区")
    @JsonProperty(value = NetworkChangeFieldConsts.COUNTRY)
    private List<MultiLangText> country;

    @ApiModelProperty("内部网络变更单类型")
    @JsonProperty(value = NetworkChangeFieldConsts.TYPE)
    private String type;

    @ApiModelProperty("操作原因")
    @JsonProperty(value = NetworkChangeFieldConsts.OPERATION_REASON)
    private List<TextValuePair> operationReason;

    @ApiModelProperty("操作结果")
    @JsonProperty(value = NetworkChangeFieldConsts.OPERATION_RESULT)
    private List<TextValuePair> operationResult;

    @ApiModelProperty("重要程度")
    @JsonProperty(value = NetworkChangeFieldConsts.IMPORTANCE)
    private List<TextValuePair> importance;

    @ApiModelProperty("风险评估")
    @JsonProperty(value = NetworkChangeFieldConsts.RISK_EVALUATION)
    private List<TextValuePair> riskEvaluation;

    @ApiModelProperty("操作等级")
    @JsonProperty(value = NetworkChangeFieldConsts.OPERATION_LEVEL)
    private List<TextValuePair> operationLevel;

    @ApiModelProperty("局点名称")
    @JsonProperty(value = NetworkChangeFieldConsts.OFFICE_NAME)
    private List<TextValuePair> officeName;

    @ApiModelProperty("是否创建打卡任务 1：是；null：否")
    @JsonProperty(value = NetworkChangeFieldConsts.CREATE_CLOCK_IN_TASK)
    private Integer createClockInTaskOrNot;

    @ApiModelProperty("批次号")
    private String batchNo;

    /**
     * 类型匹配
     *
     * @param type type
     * @return 匹配结果
     */
    public boolean isMatchType(String type) {
        if (this.type == null) {
            return false;
        }

        return StringUtils.equals(type, this.type);
    }
}
