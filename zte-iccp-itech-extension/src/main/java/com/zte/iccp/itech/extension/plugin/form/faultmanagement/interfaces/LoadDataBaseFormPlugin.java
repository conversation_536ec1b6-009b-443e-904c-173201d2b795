package com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces;

import com.zte.iccp.itech.extension.domain.model.FaultManagementDetails;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.LoadDataEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

public interface LoadDataBaseFormPlugin {

    default void loadData(LoadDataEventArgs args, FaultManagementDetails faultManagementDetails) {}

    @RequiredArgsConstructor
    class LoadDataEventArgs {

        private final BaseFormPlugin formPlugin;

        @Getter
        private final LoadDataEvent event;

        @Getter
        private final IDataModel model;

        public IFormView getFormView() {
            return formPlugin.getView();
        }

        public IFormView getParentView() {
            return formPlugin.getView().getParentView();
        }
    }
}
