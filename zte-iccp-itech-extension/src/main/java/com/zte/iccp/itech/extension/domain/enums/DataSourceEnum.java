package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 数据来源字段枚举
 *
 * <AUTHOR>
 * @create 2024/12/19 上午10:07
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum DataSourceEnum {
    /*
     * iDop系统创建
     * */
    IDOP,

    /*
     * 关联保障单创建
     * */
    GUARANTEE,

    /*
     * CNOP系统创建
     * */
    CNOP,

    /*
     * 人员积分管理
     * */
    PERSONNEL,

    /*
     * 操作计划单
     * */
    PLAN,
}
