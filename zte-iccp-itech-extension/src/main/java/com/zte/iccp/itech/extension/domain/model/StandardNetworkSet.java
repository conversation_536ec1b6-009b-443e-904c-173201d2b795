package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.StandardNetworkSetConsts.*;

/**
 * 标准网络集
 * <AUTHOR> 10335201
 * @date 2024-07-03 上午9:14
 **/
@Getter
@Setter
@BaseEntity.Info("standard_network_set")
public class StandardNetworkSet extends BaseEntity {
    @JsonProperty(value = NETWORK_SET_NAME)
    @ApiModelProperty("网络集名称")
    private String networkSetName;

    @JsonProperty(value = NETWORK)
    @ApiModelProperty("网络")
    private List<TextValuePair> network;

    @JsonProperty(value = DESCRIPTION)
    @ApiModelProperty("说明")
    private String description;

    @JsonProperty(value = COUNTRY_PROVINCE)
    @ApiModelProperty("国家地区/省州")
    private List<MultiLangText> countryProvince;

    @JsonProperty(value = PRODUCT_TEAM)
    @ApiModelProperty("产品经营团队")
    private List<TextValuePair> productTeam;

    @JsonProperty(value = ORGANIZATION)
    @ApiModelProperty("代表处")
    private List<TextValuePair> organization;

    @JsonProperty(value = CUSTOMER_IDENTIFICATION)
    @ApiModelProperty("客户标识")
    private List<TextValuePair> customerIdentification;
}
