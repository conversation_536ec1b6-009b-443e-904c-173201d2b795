package com.zte.iccp.itech.extension.kafka.consumer;

import com.alibaba.druid.util.StringUtils;
import com.zte.iccp.itech.extension.kafka.consumer.csc.ServiceRequestConsumer;
import com.zte.iccp.itech.zlic.util.Lists;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.List;

public class MessageDispatcher {

    // @DynamicComponent + @Autowired 托管该类并自动注入，会导致启动异常
    // 由于是单例模式，直接实例化来注册
    private static final List<ConsumerListener> CONSUMER_LISTENERS
            = Lists.newArrayList(new ServiceRequestConsumer());

    public static ConsumerListener dispatch (ConsumerRecord<String, String> record) {
        for (ConsumerListener listener : CONSUMER_LISTENERS) {
            if (StringUtils.equals(listener.getMessageTopic(), record.topic())
                    && StringUtils.equals(listener.getMessageKey(), record.key())) {
                return listener;
            }
        }
        return null;
    }
}
