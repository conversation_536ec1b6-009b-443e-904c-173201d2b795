package com.zte.iccp.itech.extension.common.helper;

import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.ddm.common.api.dto.UacParamDTO;
import com.zte.paas.lcap.ddm.domain.control.buildin.util.EmployeeUtil;
import com.zte.paas.lcap.ddm.domain.repository.IParameterRepository;
import com.zte.paas.lcap.platform.context.RequestHeader;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/29
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ContextHelper {

    private static final String DEFAULT_APP_ID = ConfigHelper.get("default.appId", "APP0984032412495249408");

    private static final String DEFAULT_TENANT_ID = ConfigHelper.get("default.tenantId", "10001");

    private static final String DEFAULT_LANG_ID = ConfigHelper.get("default.langId", "en_US");

    private static final String DEFAULT_EMP_NO = ConfigHelper.get("default.empNo", "iTechCloud");

    public static String getAppId() {
        String appId = RequestContextHolder.getAppId();
        if (StringUtils.isNotBlank(appId)) {
            return appId;
        }

        RequestHeader requestHeader = RequestHeaderUtils.getRequestHeader();
        if (requestHeader != null) {
            appId = requestHeader.getAppId();

            if (StringUtils.isNotBlank(appId)) {
                return appId;
            }
        }

        return DEFAULT_APP_ID;
    }

    public static void setAppId(String appId) {
        RequestContextHolder.setAppId(appId);
        RequestHeader requestHeader = RequestHeaderUtils.getRequestHeader();
        if (requestHeader != null) {
            requestHeader.setAppId(appId);
        }
    }

    public static void setDefaultAppId() {
        setAppId(DEFAULT_APP_ID);
    }

    public static String getUacAppId() {
        IParameterRepository parameterRepository = SpringContextUtil
                .getBeanFactory().getBean(IParameterRepository.class);
        UacParamDTO uacParams = parameterRepository.getUacParams(getTenantId(), getAppId());
        return uacParams.getAppId();
    }

    /**
     * 获取4A accessKey
     *
     * @return accessKey
     */
    public static String getAccessKey() {
        IParameterRepository parameterRepository = SpringContextUtil
                .getBeanFactory().getBean(IParameterRepository.class);
        UacParamDTO uacParams = parameterRepository.getUacParams(getTenantId(), getAppId());
        return uacParams.getAccessKey();
    }

    /**
     * 获取4A accessSecret
     *
     * @return accessSecret
     */
    public static String getAccessSecret() {
        IParameterRepository parameterRepository = SpringContextUtil
                .getBeanFactory().getBean(IParameterRepository.class);
        UacParamDTO uacParams = parameterRepository.getUacParams(getTenantId(), getAppId());
        return uacParams.getAccessSecret();
    }

    public static void resetHeaders() {
        RequestContextHolder.resetRequestContext();
        LocaleContextHolder.resetLocaleContext();
    }

    public static String getTenantId() {
        return ifBlank(RequestContextHolder.getTenantId(), DEFAULT_TENANT_ID);
    }

    public static void setTenantId(String tenantId) {
        RequestContextHolder.setTenantId(tenantId);
    }

    public static void setDefaultTenantId() {
        setTenantId(DEFAULT_TENANT_ID);
    }

    public static String getEmpNo() {
        return ifBlank(RequestContextHolder.getEmpNo(), DEFAULT_EMP_NO);
    }

    public static void setEmpNo(String empNo) {
        RequestContextHolder.setEmpNo(empNo);
        RequestHeader requestHeader = RequestHeaderUtils.getRequestHeader();
        if (requestHeader != null) {
            requestHeader.setxEmpNo(empNo);
        }
    }

    public static String getLangId() {
        return ifBlank(RequestContextHolder.getLangId(), DEFAULT_LANG_ID);
    }

    public static void setLangId(String langId) {
        Locale locale = ZH_CN.equals(langId)
                ? Locale.SIMPLIFIED_CHINESE : Locale.US;
        RequestContextHolder.setLocale(locale);
        LocaleContextHolder.setLocale(locale);
    }

    public static String getToken() {
        return RequestContextHolder.getToken();
    }

    private static String ifBlank(String value, String defaultVal) {
        return StringUtils.isNotBlank(value) ? value : defaultVal;
    }

    /**
     * 获取上下文登录用户信息
     *
     * @return 名字+工号
     */
    public static String getEmpInfo(){
        String empNo = getEmpNo();
        String empName = EmployeeUtil.getName(empNo, false);
        return empName + empNo;
    }
}
