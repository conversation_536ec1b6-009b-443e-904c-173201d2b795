package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("操作对象")
@Setter
@Getter
public class OperationObjecReadOnlytVO extends BaseSubEntity {

    @JsonProperty(value = "custom_7gtvjtpf")
    @ApiModelProperty("网络ID")
    private String networkId;

    @JsonProperty(value = "custom_r1nc6mtw")
    @ApiModelProperty("网络名称")
    private String networkName;

    @JsonProperty(value = "custom_g9d28lc7")
    @ApiModelProperty("局点名称")
    private String officeName;

    @ApiModelProperty("产品型号ID")
    private String productModel;

    @JsonProperty(value = "custom_svmt85qi")
    @ApiModelProperty("产品型号")
    private String productModelName;

    @ApiModelProperty("当前版本ID")
    private String currentVersion;

    @JsonProperty(value = "custom_60ed71q2")
    @ApiModelProperty("当前版本名称")
    private String currentVersionName;

    @ApiModelProperty("目标版本ID")
    private String targetVersion;

    @JsonProperty(value = "custom_ejws0sms")
    @ApiModelProperty("目标版本名称")
    private String targetVersionName;
}
