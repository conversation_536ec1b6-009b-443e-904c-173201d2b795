package com.zte.iccp.itech.extension.ability.permissionapplication;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.UppAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.helper.entity.OrderBy;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.CommonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.permissionapplication.PermissionApplicationStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.enums.UserTypeEnum;
import com.zte.iccp.itech.extension.domain.model.PermissionApplication;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.subentity.OrganizationApprover;
import com.zte.iccp.itech.extension.spi.client.EmailClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iss.approval.sdk.bean.ApprovalTaskRecord;
import com.zte.iss.approval.sdk.bean.FlowApprovalRecordDTO;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.itp.authorityclient.entity.output.UserRoleVO;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.dto.orm.Range;
import com.zte.paas.lcap.flow.dto.FlowInfo;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.EmailConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.ROLE_NAME_EN_US;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.ROLE_NAME_ZH_CN;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.PermissionApplicationConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum.PERMISSION_APPLICATION_AGREE;

/**
 * <AUTHOR>
 * @date 2024/10/12 上午11:05
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PermissionApplicationAbility {

    private static final String PAGE_ID = "PAGE1050778320536952833";

    public static PermissionApplication get(String id) {
        return QueryDataHelper.get(PermissionApplication.class, Lists.newArrayList(), id);
    }

    public static List<PermissionApplication> get(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return QueryDataHelper.get(PermissionApplication.class, Lists.newArrayList(), ids);
    }

    public static PermissionApplication queryByBillNo(String billNo) {
        if(!StringUtils.hasText(billNo)){
            return null;
        }
        Filter  filter = new Filter(BILL_NO, Comparator.EQ, billNo);
        List<IFilter> conditionFilterList = Lists.newArrayList(filter);

        return QueryDataHelper.queryOne(PermissionApplication.class, Lists.newArrayList(), conditionFilterList);
    }

    public static void update(PermissionApplication permissionApplication) {
        if (null == permissionApplication) {
            return;
        }
        SaveDataHelper.update(permissionApplication);
    }

    public static void delete(String id) {
        if (!StringUtils.hasText(id)) {
            return;
        }

        SaveDataHelper.batchDelete(PermissionApplication.class, Lists.newArrayList(id));
    }


    @SneakyThrows
    public static void asyncSendMail(FlowClient body, TemplateIdEnum templateIdEnum) {

        JSONObject variables = body.getVariables();
        String url = String.format(ConfigHelper.get(HREF_IFRAME_URL),
                ZH_CN.equals(RequestHeaderUtils.getLangId()) ? "zh" : "en", body.getBusinessId(),
                PageStatusEnum.VIEW.name(), ContextHelper.getAppId(), PAGE_ID);

        Map<String, Object> data = MapUtils.newHashMap(
                ROLE_NAME_ZH, variables.getString(ROLE_NAME_ZH_CN),
                ROLE_NAME_EN, variables.getString(ROLE_NAME_EN_US),
                LINK_URL, url
        );
        EmailClient.sendMail(templateIdEnum, variables.getString(CREATE_BY), data);
    }

    /**
     * 查询权限申请 - 用户申请的 中智&高租 数据权限
     */
    @SneakyThrows
    public static List<PermissionApplication> queryZhongZhiHLPermission(String userId) {
        List<String> fieldList = Lists.newArrayList();

        IFilter createUserFilter = new Filter(CREATE_BY, Comparator.EQ, userId);
        IFilter statusFilter = new Filter(STATUS, Comparator.EQ, PermissionApplicationStatusEnum.VALID.getStatus());
        IFilter crossFilter = new Filter(ZHONG_ZHI_HL, Comparator.EQ, true);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);
        IFilter dateFilter = new Filter(EXPIRATION_TIME, Comparator.GE, dateFormat.parse(dateFormat.format(new Date())));
        List<IFilter> conditionFilterList = Lists.newArrayList(createUserFilter, statusFilter, crossFilter, dateFilter);

        return QueryDataHelper.query(PermissionApplication.class, fieldList, conditionFilterList);
    }

    /**
     * 自定义检索
     */
    public static List<PermissionApplication> query(
            List<String> fields,
            List<IFilter> filters,
            Range range,
            OrderBy... orderBy) {

        return QueryDataHelper.query(PermissionApplication.class, fields, filters, range, orderBy);
    }

    public static void agree(PermissionApplication permissionApplication) {
        if (null == permissionApplication) {
            return;
        }

        String roleCode = TextValuePairHelper.getValue(permissionApplication.getRoleName());
        if (null == roleCode) {
            return;
        }
        List<UserRoleVO> roleList = UppAbility.getAllRoles();
        Long roleId = null;
        for (UserRoleVO vo : roleList) {
            if (org.apache.commons.lang3.StringUtils.equals(roleCode, vo.getRoleCode())) {
                roleId = vo.getId();
                break;
            }
        }

        if (null == roleId) {
            return;
        }

        String langId = ContextHelper.getLangId();

        Map<String, String> prodConstraintMap = permissionApplication.getProduct()
                .stream().collect(Collectors.toMap(TextValuePair::getValue, item -> item.getTextByLanguage(langId)));

        Set<String> threeLevelOrgIds = new HashSet<>();
        Map<String, String> orgConstraintMap = new HashMap<>();
        List<TextValuePair> agreeOrgs = getOrgs(permissionApplication);
        for (TextValuePair textValuePair : agreeOrgs) {
            String[] productId = textValuePair.getValue().split(FORWARD_SLASH);
            if (productId.length == INTEGER_THREE) {
                threeLevelOrgIds.add(productId[INTEGER_THREE - 1]);
            } else if (productId.length == INTEGER_FOUR) {
                orgConstraintMap.put(textValuePair.getValue(), textValuePair.getTextByLanguage(langId));
            }
        }

        List<BasicOrganizationInfo> orgInfos = HrClient.getByPids(threeLevelOrgIds);
        orgConstraintMap.putAll(orgInfos.stream()
                .collect(Collectors.toMap(item -> org.apache.commons.lang3.StringUtils.replace(item.getOrgIDPath(), SPECIAL_HYPHEN, FORWARD_SLASH),
                        BasicOrganizationInfo::getHrOrgName)));

        UppAbility.addRoleDataAuthori(String.valueOf(roleId), permissionApplication.getCreateBy(),
                permissionApplication.getExpirationTime(), prodConstraintMap, orgConstraintMap);

        // 发邮件
        AsyncExecuteUtils.execute(() -> sendEmail(permissionApplication, PERMISSION_APPLICATION_AGREE));
    }

    private static List<TextValuePair> getOrgs(PermissionApplication permissionApplication) {
        if (UserTypeEnum.EXTERNAL.name().equals(permissionApplication.getType())) {
            return permissionApplication.getOrganizations();
        }
        FlowInfo flowInfo = FlowHelper.getFlowInfo(permissionApplication.getId());
        if (null == flowInfo) {
            return new ArrayList<>();
        }
        List<ApprovalTaskRecord> approvalRecordList = getLatestApprovalRecords(flowInfo.getFlowInstanceId());
        List<String> agreeApprovarList = approvalRecordList.stream()
                .filter(item -> APPROVAL_RESULT_Y.equals(item.getResult())).map(ApprovalTaskRecord::getHandler)
                .collect(Collectors.toList());

        List<OrganizationApprover> approvers = OrganizationApproverAbility.query(Lists.newArrayList(permissionApplication.getId()));
        List<String> agreeOrgIds = approvers.stream()
                .filter(item -> agreeApprovarList.contains(CommonUtils.extractNumbers(item.getApprover().get(0).getValue())))
                .map(OrganizationApprover::getOrganization).collect(Collectors.toList());
        List<TextValuePair> agreelist = Lists.newArrayList();
        for (TextValuePair textValuePair : permissionApplication.getOrganizations()) {
            String[] productId = textValuePair.getValue().split(FORWARD_SLASH);
            if (agreeOrgIds.contains(productId[productId.length -1])) {
                agreelist.add(textValuePair);
            }
        }
        return agreelist;
    }

    public static void sendEmail(PermissionApplication permissionApplication, TemplateIdEnum templateIdEnum) {
        String url = String.format(ConfigHelper.get(HREF_IFRAME_URL),
                ZH_CN.equals(RequestHeaderUtils.getLangId()) ? "zh" : "en", permissionApplication.getId(),
                PageStatusEnum.VIEW.name(), ContextHelper.getAppId(), PAGE_ID);

        Map<String, Object> data = MapUtils.newHashMap(
                ROLE_NAME_ZH, permissionApplication.getRoleName().get(0).getTextByLanguage(ZH_CN),
                ROLE_NAME_EN, permissionApplication.getRoleName().get(0).getTextByLanguage(EN_US),
                LINK_URL, url
        );
        EmailClient.sendMail(templateIdEnum, permissionApplication.getCreateBy(), data);
    }

    public static List<ApprovalTaskRecord> getLatestApprovalRecords(String flowInstanceId) {
        FlowApprovalRecordDTO approvalRecordDto = FlowHelper.getFlowRecord(flowInstanceId);
        List<ApprovalTaskRecord> approvalRecordList = approvalRecordDto.getApprovalTaskRecordList();
        return approvalRecordList.stream()
                .filter(task -> PA_REPRESENTATIVE_OFFICE_APPROVAL_NODE_ID.equals(task.getNodeId())
                        && COMPLETED_TASK_STATUS.equals(task.getTaskStatus()))
                .collect(Collectors.groupingBy(ApprovalTaskRecord::getHandler))
                .values().stream()
                .map(tasks -> tasks.stream()
                        .max(java.util.Comparator.comparing(ApprovalTaskRecord::getApprovalDate))
                        .orElseThrow(() -> new IllegalArgumentException("IllegalArgument")))
                .collect(Collectors.toList());
    }

    public static void revoke(String id) {
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                id, AssignmentTypeEnum.PERMISSION_APPLICATION, Assignment.class);

        if (Objects.isNull(assignment)) {
            return;
        }

        String userId = ContextHelper.getEmpNo();

        // 撤销后，更新当前处理人及状态
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setAssignmentStatus(AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.getValue());
        updateAssignment.setCurrentProcessorEmployee(
                HrClient.queryEmployeeInfo(Lists.newArrayList(userId)));
        AssignmentAbility.update(updateAssignment);

        // 撤销后，流程驳回到提交人
        FlowHelper.rollbackToStarter(
                id, PERMISSION_APPLICATION, MsgUtils.getMessage(MessageConsts.APPLICANT_RETURN_APPROVAL));
    }
}
