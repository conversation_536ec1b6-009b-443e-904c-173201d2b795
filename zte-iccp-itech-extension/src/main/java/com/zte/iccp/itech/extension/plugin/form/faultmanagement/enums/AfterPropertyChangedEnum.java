package com.zte.iccp.itech.extension.plugin.form.faultmanagement.enums;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.PropertyChangedBaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.propertychanged.CheckNumPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.propertychanged.FaultReviewTaskButtonPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.propertychanged.SatisfactionResponsiblePersonPlugin;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids.*;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum AfterPropertyChangedEnum {

    // 故障复盘任务按钮插件
    FAULT_REVIEW_TASK_BUTTON_PLUGIN(Lists.newArrayList(FIELD_FAULT_REVIEW_CID), new FaultReviewTaskButtonPlugin()),

    // 故障管理页面_字段选择的最大个数场景
    // 故障复盘_负责人最大支持10个
    // 客户满意度_知会人最大支持100个
    CHECK_NUM_PLUGIN(Lists.newArrayList(FIELD_RECTIFICATION_PERSON_CID, FIELD_INFORMED_PERSON_CID), new CheckNumPlugin()),

    // 故障复盘，【客户满意度责任人】员工组件插件
    SATISFACTION_RESPONSIBLE_PERSON_PLUGIN(Lists.newArrayList(FIELD_FAULT_REVIEW_CID), new SatisfactionResponsiblePersonPlugin()),
    ;

    // valueChange插件的cid要配置实体字段CID
    private final List<String> propIds;

    private final PropertyChangedBaseFormPlugin valueChangeBaseFormPlugin;

    public static List<PropertyChangedBaseFormPlugin> getValueChangedEventPlugins(String propId) {
        List<PropertyChangedBaseFormPlugin> valueChangeBaseFormPlugins = new ArrayList<>();
        for (AfterPropertyChangedEnum pluginEnum : AfterPropertyChangedEnum.values()) {
            if (pluginEnum.getPropIds().contains(propId)) {
                valueChangeBaseFormPlugins.add(pluginEnum.getValueChangeBaseFormPlugin());
            }
        }
        return valueChangeBaseFormPlugins;
    }
}
