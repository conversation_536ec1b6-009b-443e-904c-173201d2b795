package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.enums.AvailabilityEnum;
import com.zte.iccp.itech.extension.domain.model.V4OperationHardware;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.V4OperationHardwareConsts.BILL_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.V4OperationHardwareConsts.PROD_SUB_CATEGORY_ID_PATH;
import static com.zte.iccp.itech.extension.domain.constant.entity.V4OperationHardwareConsts.LOGICAL_NE;

/**
 * <AUTHOR>
 * @date 2024/10/28 下午5:35
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class V4OperationHardwareAbility {

    public static List<V4OperationHardware> query(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return QueryDataHelper.get(V4OperationHardware.class, Lists.newArrayList(), ids);
    }

    public static V4OperationHardware get(String id) {
        if (null == id) {
            return null;
        }
        return QueryDataHelper.get(V4OperationHardware.class, Lists.newArrayList(), id);
    }

    public static void update(V4OperationHardware hardware) {
        if (null == hardware) {
            return;
        }
        SaveDataHelper.update(hardware);
    }

    public static void batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        SaveDataHelper.batchDelete(V4OperationHardware.class, ids);
    }

    public static List<V4OperationHardware> getByProdSub(String prodSubIdPath) {
        if (null == prodSubIdPath) {
            return new ArrayList<>();
        }
        List<IFilter> filters = new ArrayList<>();
        filters.add(new Filter(PROD_SUB_CATEGORY_ID_PATH, Comparator.IN, Arrays.asList(prodSubIdPath)));
        filters.add(new Filter(BILL_STATUS, Comparator.IN, Arrays.asList(AvailabilityEnum.ENABLED.name())));
        return QueryDataHelper.query(V4OperationHardware.class, new ArrayList<>(), filters);
    }

    public static V4OperationHardware getByLogicalIdAndProdId(String logicalId, String prodSubIdPath) {
        List<IFilter> filters = new ArrayList<>();
        filters.add(new Filter(PROD_SUB_CATEGORY_ID_PATH, Comparator.IN, Arrays.asList(prodSubIdPath)));
        filters.add(new Filter(LOGICAL_NE, Comparator.IN, Arrays.asList(logicalId)));
        List<V4OperationHardware> configurations = QueryDataHelper.query(V4OperationHardware.class, new ArrayList<>(), filters);
        return CollectionUtils.isEmpty(configurations) ? null : configurations.get(0);
    }
}
