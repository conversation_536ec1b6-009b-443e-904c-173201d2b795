package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.HIGH_RISK_INSTRUCTION;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.HIGH_RISK_INSTRUCTION_DESC;

/**
 * <AUTHOR>
 * @create 2024/7/4 下午3:38
 */
@ApiModel("分包商 - 高危指令")
@Setter
@Getter
@BaseSubEntity.Info(value = "hzs_high_risk_instruction_table", parent = SubcontractorChangeOrder.class)
public class SubHighInstruction extends BaseSubEntity {

    @ApiModelProperty("高危指令表")
    @JsonProperty(value = HIGH_RISK_INSTRUCTION)
    private String instructions;


    @ApiModelProperty("支持方式")
    @JsonProperty(value = HIGH_RISK_INSTRUCTION_DESC)
    private String instructionsDesc;
}
