package com.zte.iccp.itech.extension.domain.model.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.ReflectUtils;
import com.zte.iccp.itech.extension.domain.model.base.annotation.ConditionalAllow;
import com.zte.paas.lcap.common.classloader.CustomClassLoaderHelper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/02/11
 */
@Slf4j
public final class BaseEntityEnhancer extends BaseEntity {

    private static final String ALL = "**";

    private static final String ITECH_PKG_PREFIX = "com.zte.iccp.itech";

    private static final String FIELD_PATH_FORMAT = "%s#%s";

    private static final boolean IS_ENHANCE_NEEDED = ConfigHelper.getBoolean("baseEntity.enhance");

    private static final BaseEntityMethodInterceptor INTERCEPTOR = new BaseEntityMethodInterceptor();

    private static final ClassFieldsMap CLASS2FIELDS = new ClassFieldsMap();

    /** key: 字段名称(首字母大小); value: 字段唯一标识 */
    private static final Map<String, String> FIELD2UID = MapUtils.newHashMap();

    private final Enhancer enhancer = new Enhancer();

    private final Set<String> fieldUids;

    public BaseEntityEnhancer(Collection<String> fieldUids) {
        this.fieldUids = new HashSet<>(fieldUids);
    }

    public <T extends BaseEntity> T enhance(T origin) {
        if (!IS_ENHANCE_NEEDED) {
            return origin;
        }

        enhancer.setSuperclass(origin.getClass());
        enhancer.setCallback(INTERCEPTOR);

        //noinspection unchecked
        T enhance = (T) enhancer.create();
        if (CollectionUtils.isEmpty(fieldUids)) {
            enhance.getPreparedFieldUIds().add(ALL);
        } else {
            enhance.getPreparedFieldUIds().addAll(fieldUids);
        }

        initClassInformation(origin.getClass());
        copyTo(origin, enhance);

        return enhance;
    }

    public <T extends BaseEntity> List<T> enhance(List<T> originList) {
        if (!IS_ENHANCE_NEEDED || CollectionUtils.isEmpty(originList)) {
            return originList;
        }

        return originList.stream()
                .map(this::enhance)
                .collect(Collectors.toList());
    }

    @SneakyThrows
    private static <T extends BaseEntity> void initClassInformation(Class<T> clazz) {
        if (CLASS2FIELDS.containsKey(clazz)) {
            return;
        }

        List<Field> fields = ReflectUtils.getAllFields(clazz);

        for (Field field : fields) {
            field.setAccessible(true);

            JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
            if (Modifier.isStatic(field.getModifiers()) || jsonProperty == null) {
                continue;
            }

            FIELD2UID.put(
                    fieldKey(clazz, getUpperCaseName(field)),
                    jsonProperty.value());
        }

        CLASS2FIELDS.put(clazz, fields);
    }

    @SneakyThrows
    private static <T extends BaseEntity> void copyTo(T origin, T enhance) {
        for (Field field : ReflectUtils.getAllFields(origin.getClass())) {
            field.setAccessible(true);
            if (Modifier.isStatic(field.getModifiers())) {
                continue;
            }

            if (field.getAnnotation(JsonProperty.class) == null) {
                continue;
            }

            field.set(enhance, field.get(origin));
        }
    }

    private static String fieldKey(Class<?> entityClass, String fieldUpperCaseName) {
        return String.format(FIELD_PATH_FORMAT, entityClass.getName(), fieldUpperCaseName);
    }

    private static String getUpperCaseName(Field field) {
        return String.format("%s%s",
                field.getName().substring(0, 1).toUpperCase(),
                field.getName().substring(1));
    }

    private static String getFieldName(Method method) {
        return method.getName().replaceFirst("^get", "");
    }

    private static final class BaseEntityMethodInterceptor implements MethodInterceptor {

        private static final Set<String> ALLOWED_METHODS = Sets.newHashSet(
                JsonUtils.class.getName() + "#toJsonString",
                BeanUtils.class.getName() + "#copyProperties");

        @Override
        @SneakyThrows
        public Object intercept(Object obj, Method method, Object[] args, MethodProxy methodProxy) {
            if (!(obj instanceof BaseEntity)) {
                throw new IllegalArgumentException();
            }

            String fieldUid = FIELD2UID.get(fieldKey(obj.getClass(), getFieldName(method)));
            if (StringUtils.isBlank(fieldUid)) {
                return methodProxy.invokeSuper(obj, args);
            }

            BaseEntity entity = (BaseEntity) obj;

            if (method.getName().startsWith("get")) {
                return enhanceGet(entity, fieldUid, methodProxy, args);
            }

            if (method.getName().startsWith("set")) {
                return enhanceSet(entity, fieldUid, methodProxy, args);
            }

            return methodProxy.invokeSuper(obj, args);
        }

        @SneakyThrows
        private static Object enhanceGet(
                BaseEntity entity,
                String fieldUid,
                MethodProxy methodProxy,
                Object[] args) {
            Object value = methodProxy.invokeSuper(entity, args);

            if (value == null
                    && !entity.getPreparedFieldUIds().contains(ALL)
                    && !entity.getPreparedFieldUIds().contains(fieldUid)) {
                LcapBusiException notPreparedException = new LcapBusiException("Field is not prepared: {0}", fieldUid);

                // 部分场景允许获取所有字段值
                if (!allowed(fieldUid, entity.getPreparedFieldUIds(), notPreparedException.getStackTrace())) {
                    log.error(notPreparedException.getMessage(), notPreparedException);
                    throw notPreparedException;
                }
            }

            return value;
        }

        @SneakyThrows
        private static boolean allowed(
                String fieldUid,
                Set<String> preparedFieldUids,
                StackTraceElement[] stackTrace) {
            for (StackTraceElement element : stackTrace) {
                if (BaseEntityMethodInterceptor.class.getName().equals(element.getClassName())
                        || !element.getClassName().startsWith(ITECH_PKG_PREFIX)) {
                    continue;
                }

                if (ALLOWED_METHODS.contains(
                        String.format("%s#%s", element.getClassName(), element.getMethodName()))) {
                    return true;
                }

                Class<?> contextClass = CustomClassLoaderHelper
                        .getAppCustomClassLoader(ContextHelper.getTenantId(), ContextHelper.getAppId())
                        .loadClass(element.getClassName());
                Method[] methods = ReflectUtils.getAllMethods(contextClass).stream()
                        .filter(m -> m.getName().equals(element.getMethodName()))
                        .toArray(Method[]::new);
                if (methods.length != 1) {
                    continue;
                }

                List<ConditionalAllow> allows = ReflectUtils.findAnnotations(methods[0], ConditionalAllow.class);
                if (allows.isEmpty()) {
                    continue;
                }

                boolean condAllow = allows.stream()
                        .anyMatch(cond -> allow(cond, fieldUid, preparedFieldUids));
                if (condAllow) {
                    return true;
                }
            }

            return false;
        }

        @SneakyThrows
        private static Object enhanceSet(
                BaseEntity entity,
                String fieldUid,
                MethodProxy methodProxy,
                Object[] args) {
            entity.getPreparedFieldUIds().add(fieldUid);
            return methodProxy.invokeSuper(entity, args);
        }

        private static boolean allow(
                ConditionalAllow cond,
                String fieldUid,
                Set<String> preparedFieldUids) {
            if (!Sets.newHashSet(cond.missingFields()).contains(fieldUid)) {
                return false;
            }

            return Arrays.stream(cond.whileExistsAnyOf())
                    .anyMatch(preparedFieldUids::contains);
        }
    }

    /**
     * key: 实体类Class;
     * value: Field清单;
     */
    private static final class ClassFieldsMap extends HashMap<Class<? extends BaseEntity>, List<Field>> {
    }
}
