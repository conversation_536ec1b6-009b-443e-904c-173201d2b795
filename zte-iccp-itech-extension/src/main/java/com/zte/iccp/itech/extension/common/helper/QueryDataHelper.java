package com.zte.iccp.itech.extension.common.helper;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.entity.OrderBy;
import com.zte.iccp.itech.extension.common.helper.entity.OrderEnum;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.ReflectUtils;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntityEnhancer;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.paas.lcap.common.api.metadata.engine.driver.IBizData;
import com.zte.paas.lcap.core.entity.MainEntityType;
import com.zte.paas.lcap.core.orm.QueryBuilder;
import com.zte.paas.lcap.core.orm.SelectField;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.core.orm.query.OrderBy.OrderField;
import com.zte.paas.lcap.ddm.common.api.dto.orm.Range;
import com.zte.paas.lcap.ddm.domain.helper.orm.BaseQueryDataHelper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.zte.iccp.itech.extension.common.helper.FilterHelper.handleFilters;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.SQL_MAX_PAGE_SIZE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class QueryDataHelper {

    private static final int MAX_PAGE_SIZE = 5000;

    private static final Range DEFAULT_RANGE = new Range(MAX_PAGE_SIZE);

    public static JSONArray queryRaw(
            Class<? extends BaseEntity> clazz,
            List<String> fields,
            List<IFilter> filters) {
        String entityId = EntityHelper.getEntityId(clazz);
        fields = handleFields(entityId, fields);
        filters = handleFilters(clazz, filters);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        JSONArray jsonArray = BaseQueryDataHelper.query(
                mainEntityType,
                entityId,
                fields,
                filters,
                DEFAULT_RANGE,
                Lists.newArrayList());
        return handleJsonArray(jsonArray);
    }

    public static JSONArray queryRaw(
            Class<? extends BaseEntity> clazz,
            List<String> fields,
            List<IFilter> filters,
            Range range,
            OrderBy... orderBy) {
        String entityId = EntityHelper.getEntityId(clazz);
        fields = handleFields(entityId, fields, orderBy);
        filters = handleFilters(clazz, filters);
        List<OrderField> orderFields = handleOrderBy(clazz, orderBy);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        JSONArray jsonArray = BaseQueryDataHelper.query(
                mainEntityType,
                entityId,
                fields,
                filters,
                range,
                orderFields);
        return handleJsonArray(jsonArray);
    }

    public static int queryCount(
            Class<? extends BaseEntity> clazz,
            List<IFilter> filters) {
        filters = handleFilters(clazz, filters);

        IBizData bizData = SpringContextUtil.getBean(IBizData.class);
        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        QueryBuilder queryBuilder = new QueryBuilder(mainEntityType);
        queryBuilder.addFilter(filters);
        queryBuilder.add(new SelectField(ID));
        return bizData.queryCount(queryBuilder);
    }

    public static int querySubCount(
            Class<? extends BaseSubEntity> clazz,
            List<IFilter> filters) {
        filters = handleFilters(clazz, filters);

        IBizData bizData = SpringContextUtil.getBean(IBizData.class);
        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        QueryBuilder queryBuilder = new QueryBuilder(mainEntityType);
        queryBuilder.addFilter(filters);
        queryBuilder.add(new SelectField(ID));
        return bizData.queryCount(queryBuilder);
    }

    @SneakyThrows
    public static <T extends BaseEntity> List<T> queryDistinct(
            Class<? extends BaseEntity> clazz,
            List<String> fields,
            List<IFilter> filters) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        filters = handleFilters(clazz, filters);

        IBizData bizData = SpringContextUtil.getBean(IBizData.class);
        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        QueryBuilder queryBuilder = new QueryBuilder(mainEntityType);
        queryBuilder.addFilter(filters);

        String entityId = EntityHelper.getEntityId(clazz);
        if (CollectionUtils.isEmpty(fields)) {
            fields = ReflectUtils.getAllFieldNames(clazz);
        }
        fields = handleFields(entityId, fields);
        fields.forEach(item -> queryBuilder.add(new SelectField(item)));

        queryBuilder.distinctResult();

        List<JSONObject> jsonObjects = bizData.queryData(queryBuilder);
        return enhancer.enhance(JsonUtils.parseArray(jsonObjects, clazz));
    }

    public static JSONObject getRaw(
            Class<? extends BaseEntity> clazz,
            List<String> fields,
            String id) {
        JSONArray jsonArray = getRaw(clazz, fields, Lists.newArrayList(id));
        return CollectionUtils.isEmpty(jsonArray) ? null : jsonArray.getJSONObject(0);
    }

    public static JSONArray getRaw(
            Class<? extends BaseEntity> clazz,
            List<String> fields,
            List<String> ids) {
        return queryRaw(
                clazz, fields,
                Lists.newArrayList(new Filter(ID, Comparator.IN, ids)));
    }

    public static <T extends BaseEntity> T get(
            Class<T> clazz,
            List<String> fields,
            String id) {
        return queryOne(
                clazz,
                fields,
                Lists.newArrayList(new Filter(ID, Comparator.EQ, id)));
    }

    public static <T extends BaseEntity> List<T> get(
            Class<T> clazz,
            List<String> fields,
            List<String> ids) {
        List<T> result = new ArrayList<>();
        for (List<String> pageIds : Lists.partition(ids, SQL_MAX_PAGE_SIZE)) {
            result.addAll(query(
                    clazz,
                    fields,
                    Lists.newArrayList(new Filter(ID, Comparator.IN, pageIds))));
        }
        return result;
    }

    public static <T extends BaseEntity> T queryOne(
            Class<T> clazz,
            List<String> fields,
            List<IFilter> filters) {
        List<T> list = query(clazz, fields, filters);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @SneakyThrows
    public static <T extends BaseEntity> List<T> query(
            Class<T> clazz,
            List<String> fields,
            List<IFilter> filters) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        JSONArray jsonArray = queryRaw(clazz, fields, filters);
        return enhancer.enhance(JsonUtils.parseArray(jsonArray, clazz));
    }

    @SneakyThrows
    public static <T extends BaseEntity> List<T> queryAll(
            Class<T> clazz,
            List<String> fields,
            List<IFilter> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            return Collections.emptyList();
        }

        List<T> allResults = new ArrayList<>();
        int offset = 0;
        while (true) {
            JSONArray jsonArray = queryRaw(clazz, fields, filters, new Range(offset, MAX_PAGE_SIZE));
            List<T> batchResults = JsonUtils.parseArray(jsonArray, clazz);
            if (CollectionUtils.isEmpty(batchResults)) {
                break;
            }
            allResults.addAll(batchResults);
            if (batchResults.size() < MAX_PAGE_SIZE) {
                break;
            }
            offset += MAX_PAGE_SIZE;
        }

        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        return enhancer.enhance(allResults);
    }

    public static <T extends BaseSubEntity> List<T> queryByPid(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            List<String> pids) {
        return query(clazz, fields, pids, Lists.newArrayList());
    }

    @SneakyThrows
    public static <T extends BaseSubEntity> List<T> queryAllByPid(
            Class<T> clazz,
            List<String> fields,
            List<String> pids) {
        if (CollectionUtils.isEmpty(pids)) {
            return Collections.emptyList();
        }

        List<T> allResults = new ArrayList<>();
        String lastId = null;
        while (true) {
            List<IFilter> filters = new ArrayList<>();
            filters.add(new Filter(PID, Comparator.IN, pids));
            if (lastId != null) {
                filters.add(new Filter(ID, Comparator.GT, lastId));
            }
            List<T> batchResults = query(clazz, fields, filters, new Range(MAX_PAGE_SIZE));
            if (CollectionUtils.isEmpty(batchResults)) {
                break;
            }
            allResults.addAll(batchResults);
            lastId = batchResults.get(batchResults.size() - 1).getId();
            if (batchResults.size() < MAX_PAGE_SIZE) {
                break;
            }
        }
        return allResults;
    }

    @SneakyThrows
    public static <T extends BaseSubEntity> List<T> querySub(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            List<IFilter> filters) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        JSONArray jsonArray = queryRaw(clazz, fields, filters);
        return enhancer.enhance(JsonUtils.parseArray(jsonArray, clazz));
    }

    public static <T extends BaseEntity> List<T> query(
            Class<? extends BaseEntity> clazz,
            List<String> fields,
            List<IFilter> filters,
            Range range,
            OrderBy... orderBy) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        JSONArray jsonArray = queryRaw(clazz, fields, filters, range, orderBy);
        return enhancer.enhance(JsonUtils.parseArray(jsonArray, clazz));
    }

    public static <T extends BaseEntity> List<T> query(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            String pid,
            Range range,
            OrderBy... orderBy) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        JSONArray jsonArray = queryRaw(
                clazz, fields, Lists.newArrayList(new Filter(PID, Comparator.EQ, pid)), range, orderBy);
        return enhancer.enhance(JsonUtils.parseArray(jsonArray, clazz));
    }

    public static <T extends BaseSubEntity> List<T> query(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            String mainEntityId) {
        return query(clazz, fields, mainEntityId, Lists.newArrayList());
    }

    public static <T extends BaseSubEntity> List<T> query(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            String pid,
            List<IFilter> filters) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        String entityId = EntityHelper.getEntityId(clazz);
        fields = handleFields(entityId, fields);
        filters = handleFilters(clazz, filters);

        filters.add(new Filter(entityId + "." + PID, Comparator.EQ, pid));

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        JSONArray jsonArray = BaseQueryDataHelper.query(
                mainEntityType,
                entityId,
                fields,
                filters,
                DEFAULT_RANGE,
                Lists.newArrayList());
        return enhancer.enhance(JsonUtils.parseArray(handleJsonArray(jsonArray), clazz));
    }

    public static <T extends BaseEntity> List<T> query(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            List<String> pids,
            List<IFilter> filters) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        String entityId = EntityHelper.getEntityId(clazz);
        fields = handleFields(entityId, fields);
        filters = handleFilters(clazz, filters);

        String parentEntityId = EntityHelper.getParentEntityId(clazz);
        String mainEntityId = EntityHelper.getMainEntityId(clazz);
        if (parentEntityId.equals(mainEntityId)) {
            filters.add(new Filter(entityId + "." + PID, Comparator.IN, pids));
        } else {
            filters.add(new Filter(parentEntityId + "." + entityId + "." + PID, Comparator.IN, pids));
        }

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        JSONArray jsonArray = BaseQueryDataHelper.query(
                mainEntityType,
                entityId,
                fields,
                filters,
                DEFAULT_RANGE,
                Lists.newArrayList());
        return enhancer.enhance(JsonUtils.parseArray(handleJsonArray(jsonArray), clazz));
    }

    public static <T extends BaseEntity> List<T> query(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            List<String> pids,
            List<IFilter> filters,
            Range range,
            OrderBy... orderBy) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        String entityId = EntityHelper.getEntityId(clazz);
        fields = handleFields(entityId, fields);
        filters = handleFilters(clazz, filters);

        String parentEntityId = EntityHelper.getParentEntityId(clazz);
        String mainEntityId = EntityHelper.getMainEntityId(clazz);
        if (parentEntityId.equals(mainEntityId)) {
            filters.add(new Filter(entityId + "." + PID, Comparator.IN, pids));
        } else {
            filters.add(new Filter(parentEntityId + "." + entityId + "." + PID, Comparator.IN, pids));
        }

        List<OrderField> orderFields = handleOrderBy(clazz, orderBy);
        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        JSONArray jsonArray = BaseQueryDataHelper.query(
                mainEntityType,
                entityId,
                fields,
                filters,
                range,
                orderFields);
        return enhancer.enhance(JsonUtils.parseArray(handleJsonArray(jsonArray), clazz));
    }

    public static <T extends BaseEntity> List<T> querySub(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            List<IFilter> filters,
            Range range,
            OrderBy... orderBy) {
        BaseEntityEnhancer enhancer = new BaseEntityEnhancer(fields);
        String entityId = EntityHelper.getEntityId(clazz);
        fields = handleFields(entityId, fields);
        filters = handleFilters(clazz, filters);

        List<OrderField> orderFields = handleOrderBy(clazz, orderBy);
        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        JSONArray jsonArray = BaseQueryDataHelper.query(
                mainEntityType,
                entityId,
                fields,
                filters,
                range,
                orderFields);
        return enhancer.enhance(JsonUtils.parseArray(handleJsonArray(jsonArray), clazz));
    }

    public static <T extends BaseSubEntity> T queryOne(
            Class<? extends BaseSubEntity> clazz,
            List<String> fields,
            String mainEntityId,
            List<IFilter> filters) {
        List<T> list = query(clazz, fields, mainEntityId, filters);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    private static String handleField(String entityId, String field) {
        if (field.equals(CREATE_BY)
                || field.equals(CREATE_TIME)
                || field.equals(LAST_MODIFIED_BY)
                || field.equals(LAST_MODIFIED_TIME)) {
            field = String.format("%s_%s", entityId, field);
        }

        return field;
    }

    private static List<String> handleFields(String entityId, List<String> fields, OrderBy... orderBy) {
        List<String> clone = new ArrayList<>(fields);
        clone.replaceAll(field -> handleField(entityId, field));

        for (OrderBy order : orderBy) {
            String field = handleField(entityId, order.getField());
            if (!clone.contains(field)) {
                clone.add(field);
            }
        }

        if (!clone.isEmpty() && !clone.contains(ID)) {
            clone.add(ID);
        }

        return clone;
    }

    private static List<OrderField> handleOrderBy(Class<? extends BaseEntity> clazz, OrderBy... orderBy) {
        String entityId = EntityHelper.getEntityId(clazz);
        return BaseSubEntity.class.isAssignableFrom(clazz)
                ? handleOrderBy4Sub(entityId, orderBy)
                : handleOrderBy4Main(entityId, orderBy);
    }

    private static List<OrderField> handleOrderBy4Main(String entityId, OrderBy... orderBy) {
        List<OrderField> orderFields = new ArrayList<>(orderBy.length);

        for (OrderBy order : orderBy) {
            OrderField orderField = new OrderField();
            orderField.setDefaultSort(order.getOrder() == OrderEnum.ASC);
            orderField.setField(handleField(entityId, order.getField()));
            orderFields.add(orderField);
        }

        return orderFields;
    }

    private static List<OrderField> handleOrderBy4Sub(String entityId, OrderBy... orderBy) {
        List<OrderField> orderFields = new ArrayList<>(orderBy.length);

        for (OrderBy order : orderBy) {
            OrderField orderField = new OrderField();
            orderField.setDefaultSort(order.getOrder() == OrderEnum.ASC);
            orderField.setField(String.format("%s.%s",
                    entityId, handleField(entityId, order.getField())));
            orderFields.add(orderField);
        }

        return orderFields;
    }

    private static JSONArray handleJsonArray(JSONArray jsonArray) {
        final String regexp = "\"\\w+_("
                + CREATE_BY + "|"
                + CREATE_TIME + "|"
                + LAST_MODIFIED_BY + "|"
                + LAST_MODIFIED_TIME + ")\"";

        String json = jsonArray.toJSONString();
        json = json.replaceAll(regexp, "\"$1\"");
        return JSONArray.parseArray(json);
    }
}