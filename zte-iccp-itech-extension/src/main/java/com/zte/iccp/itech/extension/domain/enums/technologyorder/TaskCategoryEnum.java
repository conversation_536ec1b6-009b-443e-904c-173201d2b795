package com.zte.iccp.itech.extension.domain.enums.technologyorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 任务分类枚举，和快码类型task_category_enum对应(通用任务略)
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum TaskCategoryEnum implements SingletonTextValuePairsProvider {
    /**
     * 自定义任务
     */
    CUSTOM("1"),

    /**
     * 故障复盘
     */
    FAULT_REVIEW("3"),

    /**
     * 故障整改
     */
    FAULT_RECTIFY("4"),

    /**
     * 横推任务
     */
    HORIZONTAL_PUSHING_TASK("2"),

    /**
     * 人员资质管理任务
     */
    PERSONNEL_QUALIFICATION_MANAGEMENT_TASK("14"),

    //其他任务属通用任务，不进行处理
    ;

    /**
     * 编码
     */
    private final String value;


    /**
     * 是否为特殊任务分类
     * 任务分类=复盘任务、整改任务、横推任务其中一种时，进行额外拓展
     *
     * @param value value
     * @return true/false
     */
    public static boolean isSpecialTaskCategory(String value) {
        TaskCategoryEnum taskCategoryEnum = Arrays.stream(TaskCategoryEnum.values()).filter(item -> item.value.equals(value)).findFirst().orElse(null);
        if (taskCategoryEnum == null) {
            return false;
        }
        switch (taskCategoryEnum) {
            case FAULT_REVIEW:
            case FAULT_RECTIFY:
            case HORIZONTAL_PUSHING_TASK:
                return true;
            default:
                return false;
        }
    }
}
