package com.zte.iccp.itech.extension.common.utils;

import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmInfo;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/09/05
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AlarmUtils {

    public static final String EXCEPTION = "Exception";

    public static boolean critical(String title, Exception e) {
        return critical(title, MapUtils.newHashMap(EXCEPTION, e.getMessage()));
    }

    public static boolean critical(String title, Map<String, String> additionalText) {
        return alarm(AlarmSeverityEnum.CRITICAL, title, additionalText);
    }

    public static boolean major(String title, Exception e) {
        return major(title, MapUtils.newHashMap(EXCEPTION, e.getMessage()));
    }

    public static boolean major(String title, Map<String, String> additionalText) {
        return alarm(AlarmSeverityEnum.MAJOR, title, additionalText);
    }

    public static boolean minor(String title, Exception e) {
        return major(title, MapUtils.newHashMap(EXCEPTION, e.getMessage()));
    }

    public static boolean minor(String title, Map<String, String> additionalText) {
        return alarm(AlarmSeverityEnum.MINOR, title, additionalText);
    }

    public static boolean warning(String title, Exception e) {
        return major(title, MapUtils.newHashMap(EXCEPTION, e.getMessage()));
    }

    public static boolean warning(String title, Map<String, String> additionalText) {
        return alarm(AlarmSeverityEnum.WARNING, title, additionalText);
    }

    private static boolean alarm(
            AlarmSeverityEnum severity,
            String title,
            Map<String, String> additionalText) {
        Properties props = new Properties();

        AlarmInfo alarmInfo = new AlarmInfo();
        alarmInfo.setMoc(props.moc);
        alarmInfo.setAlarmCode(props.codesDefault);
        alarmInfo.setAlarmSeverity(severity.getLevel());
        alarmInfo.setAlarmTitle(MapUtils.newHashMap("en", title, "zh", title));
        alarmInfo.setAdditionalText(additionalText);
        alarmInfo.setObjKey(getCaller());

        return AlarmHelper.alarmWithDefaultAppInsId(alarmInfo);
    }

    private static String getCaller() {
        return Arrays.stream(Thread.currentThread().getStackTrace())
                .skip(1)
                .filter(e -> !e.getClassName().equals(AlarmUtils.class.getName()))
                .findFirst()
                .map(e -> String.format("%s#%s", e.getClassName(), e.getMethodName()))
                .orElse(null);
    }

    private static class Properties {
        private static final String PREFIX = "dtems.agent.";

        private final String moc;

        private final String codesDefault;

        private Properties() {
            moc = ConfigHelper.getRaw(PREFIX + "moc");
            codesDefault = ConfigHelper.getRaw(PREFIX + "codes.default");
        }
    }
}
