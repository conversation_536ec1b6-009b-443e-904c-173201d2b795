package com.zte.iccp.itech.extension.domain.enums.accredit;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum CcnAuthorizationApplicationStatusEnum {

    /** 待生成 */
    GENERATE("1", "待生成", "Generate Authorization File"),

    /** 待反馈 */
    FEEDBACK("2", "待反馈", "Feedback Evidence"),

    /** 已关闭 */
    CLOSED("3", "已关闭", "Closed");

    /**
     * 编码
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;

    /**
     * 获取保存格式数据
     * @return List<TextValuePair>
     */
    public List<TextValuePair> convertSavingData() {
        return TextValuePairHelper.buildList(value, zhCn, enUs);
    }

    /**
     * 根据枚举编码获取对应枚举
     * @param value
     * @return applicationStatusEnum
     */
    public static CcnAuthorizationApplicationStatusEnum fromValue(String value) {
        for (CcnAuthorizationApplicationStatusEnum applicationStatusEnum : CcnAuthorizationApplicationStatusEnum.values()) {
            if (applicationStatusEnum.getValue().equals(value)) {
                return applicationStatusEnum;
            }
        }

        return null;
    }
}
