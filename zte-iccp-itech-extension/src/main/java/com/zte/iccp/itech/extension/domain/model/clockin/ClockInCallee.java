package com.zte.iccp.itech.extension.domain.model.clockin;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.StringDecryptDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.StringEncryptSerializer;
import lombok.Getter;
import lombok.Setter;

/**
 * 打卡呼叫对象
 * <AUTHOR>
 * @since 2024/10/10
 */
@Getter
@Setter
public class ClockInCallee {
    private String userId;

    @JsonSerialize(using = StringEncryptSerializer.class)
    @JsonDeserialize(using = StringDecryptDeserializer.class)
    private String phoneNum;
}
