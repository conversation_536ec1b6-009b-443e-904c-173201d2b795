package com.zte.iccp.itech.extension.common.helper;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/27
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TextValuePairHelper {

    public static String getValue(Object obj) {
        if(obj == null ){
            return null;
        }
        List<TextValuePair> pairs = JsonUtils.parseArray(obj, TextValuePair.class);
        return CollectionUtils.isEmpty(pairs)
                ? null : pairs.get(0).getValue();
    }

    public static MultiLangText getText(Object obj) {
        if(obj == null ){
            return null;
        }
        List<TextValuePair> pairs = JsonUtils.parseArray(obj, TextValuePair.class);
        return CollectionUtils.isEmpty(pairs)
                ? null : pairs.get(0).getText();
    }

    /**
     * 根据语言环境获取对应文本
     */
    public static String getLanguageText(Object object) {
        if (Objects.isNull(object)){
            return null;
        }

        List<TextValuePair> textValuePairList = JsonUtils.parseArray(object, TextValuePair.class);
        return CollectionUtils.isEmpty(textValuePairList)
                ? null
                : textValuePairList.get(0).getTextByLanguage(ContextHelper.getLangId());
    }

    /**
     * 根据语言环境获取对应文本
     */
    public static String getLanguageText(
            List<TextValuePair> textValuePairs,
            Map<String, String> names) {
        return CollectionUtils.isEmpty(textValuePairs)
                ? null
                : names.get(textValuePairs.get(0).getValue());
    }

    /**
     * 根据语言环境获取对应文本
     */
    public static String getLanguageText(List<MultiLangText> multiLangTexts) {
        if (CollectionUtils.isEmpty(multiLangTexts) || ObjectUtils.isNull(multiLangTexts.get(0))) {
            return null;
        }
        return multiLangTexts.get(0).getTextByLanguage(ContextHelper.getLangId());
    }

    public static List<TextValuePair> buildList(String value, String zhCn, String enUs) {
        return Lists.newArrayList(new TextValuePair() {{
            setValue(value);
            setText(new MultiLangText(zhCn, enUs, value));
        }});
    }

    public static List<String> getValueList(List<TextValuePair> textValuePairList) {
        if (CollectionUtils.isEmpty(textValuePairList)) {
            return Lists.newArrayList();
        }

        return textValuePairList.stream().map(TextValuePair::getValue).distinct().collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @Description 根据传入的文本对象获取文本列表
     * @Date 下午4:29 2025/6/27
     * @Param obj
     * @return java.util.List<java.lang.String>
     **/
    public static List<String> getValueList(Object obj) {
        if (obj == null) {
            return new ArrayList<>(CommonConstants.INTEGER_ZERO);
        }
        try {
            List<TextValuePair> textValuePairs = JsonUtils.parseArray(obj, TextValuePair.class);
            return textValuePairs.stream().filter(Objects::nonNull).map(TextValuePair::getValue).distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return new ArrayList<>(CommonConstants.INTEGER_ZERO);
        }
    }

    /* object转List*/
    public static List<TextValuePair> objectTransferList(Object o) {
        if (o == null) {
            return new ArrayList<>();
        }
        return JsonUtils.parseArray(o, TextValuePair.class);
    }

    public static List<TextValuePair> buildList(List<String> values) {
        List<TextValuePair> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(values)) {
            return list;
        }

        values = values.stream().filter(StringUtils::hasText)
                .distinct().collect(Collectors.toList());
        values.forEach(item -> {
            list.add(new TextValuePair() {{
                setValue(item);
                setText(new MultiLangText(item, item, item));
            }});
        });

        return list;
    }

    public static <T extends Enum<T>> T get(Class<T> clazz, Object obj) {
        return Enum.valueOf(clazz, getValue(obj));
    }

    public static OptionsBuilder textValuePairs2optionsBuilder(List<TextValuePair> textValuePairs) {
        OptionsBuilder optionsBuilder = new OptionsBuilder();
        for (TextValuePair textValuePair : textValuePairs) {
            optionsBuilder.addOption(new Option(textValuePair.getValue(),
                    new Text(textValuePair.getText().getZhCN(), textValuePair.getText().getEnUS())));
        }
        return optionsBuilder;
    }

    /**
     * 下拉选择类型字段在表单页面展示需要 setOptions
     *
     * @param dataModel
     * @param view
     * @param entityFieldKey 表单元素映射的实体字段
     * @param entityFieldCid 表单元素的唯一标识
     */
    public static void setOptions(IDataModel dataModel, IFormView view, String entityFieldKey, String entityFieldCid) {
        OptionsBuilder optionsBuilder = textValuePairs2optionsBuilder(ComponentUtils.getChooseComponentInfo(dataModel, entityFieldKey));
        view.getClientViewProxy().setOptions(entityFieldCid, optionsBuilder.build());
    }

    /**
     * 删除字符串最末尾“/”
     *
     * @param value
     */
    public static String removeTrailingSlash(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }

        if (value.endsWith(CommonConstants.FORWARD_SLASH)) {
            return value.substring(0, value.length() - 1);
        }

        return value;
    }
}