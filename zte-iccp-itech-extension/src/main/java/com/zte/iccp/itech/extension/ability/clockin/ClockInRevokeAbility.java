package com.zte.iccp.itech.extension.ability.clockin;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.ability.clockin.callplan.ClockInCallPlanAbolishAbility;
import com.zte.iccp.itech.extension.ability.clockin.callplan.ClockInCallPlanDeleteAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.OperationCache;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.*;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInCallPlan;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility.getLastClockInRecords;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.N;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.ClockIn.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CustomizedNetworkSetConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInCallPlanFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInRecordFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum.OPERATING_SUPERVISOR;
import static com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum.WATCHMAN;
import static com.zte.iccp.itech.extension.domain.enums.clockin.ClockInStateEnum.ON_DUTY_GOING;

/**
 * <AUTHOR>
 * @since 2024/09/20
 */
public class ClockInRevokeAbility {

    private static final int REVOCABLE_MILLISECONDS = 10 * 60 * 1000;

    private static final Set<ClockInOptionEnum> TEST_END_OPTIONS = Collections.unmodifiableSet(Sets.newHashSet(
            ClockInOptionEnum.TEST_SUCCESS_OPERATION_END,
            ClockInOptionEnum.TEST_FAILED_ROLLBACK_END,
            ClockInOptionEnum.TEST_FAILED_NO_ROLLBACK));

    private final ClockInTask clockInTask;

    private final ClockInRecord last1Record;

    private final ClockInRecord last2Record;

    private final boolean isOperationOwner;

    private final boolean isOnDutyOwner;

    private final Date lastModifiedTime;

    private final OperationCache operationCache = new OperationCache();

    private final ClockInCallPlanAbolishAbility clockInCallPlanAbolishAbility
            = new ClockInCallPlanAbolishAbility(operationCache);

    private final ClockInCallPlanDeleteAbility clockInCallPlanDeleteAbility
            = new ClockInCallPlanDeleteAbility(operationCache);

    private final RelatedTasksProvider relatedTasksProvider = new RelatedTasksProvider();

    private IBatchTask batchTask;

    public ClockInRevokeAbility(String clockInTaskId) {
        clockInTask = QueryDataHelper.get(
                ClockInTask.class,
                Lists.newArrayList(
                        ENTITY_TYPE,
                        BATCH_TASK_ID,
                        TASK_TYPE,
                        TIME_ZONE,
                        OPERATOR_ROLES,
                        TOTAL_ON_DUTY_DURATION,
                        ON_DUTY_FREQUENCY,
                        STAGE_ON_DUTY_END_TIME,
                        ALREADY_ON_DUTY_FREQUENCY,
                        LAST_MODIFIED_TIME),
                clockInTaskId);

        List<ClockInRecord> lastRecords = getLastClockInRecords(
                clockInTaskId,1, null,
                CREATE_BY, CREATE_TIME, CLOCK_IN_OPTION, CLOCK_IN_TIME, REVOKED);
        List<ClockInRecord> lastEnabledRecords = getLastClockInRecords(
                clockInTaskId,2, BoolEnum.N,
                CREATE_BY, CREATE_TIME, CLOCK_IN_OPTION, CLOCK_IN_TIME, REVOKED);
        lastRecords.addAll(
                lastEnabledRecords.stream()
                        .filter(ler -> lastRecords.stream().noneMatch(lr -> lr.getId().equals(ler.getId())))
                        .collect(Collectors.toList()));

        switch (lastRecords.size()) {
            case 0:
                last1Record = last2Record = null;
                break;
            case 1:
                last1Record = lastRecords.get(0);
                last2Record = null;
                break;
            case 2:
            default:
                last1Record = lastRecords.get(0);
                last2Record = lastRecords.get(lastRecords.size() - 1);
                break;
        }

        isOperationOwner = clockInTask.getTaskType() == ClockInTaskTypeEnum.OPERATION
                && clockInTask.getOperatorRoles().contains(OPERATING_SUPERVISOR);
        isOnDutyOwner = clockInTask.getTaskType() == ClockInTaskTypeEnum.ON_DUTY
                && clockInTask.getOperatorRoles().contains(WATCHMAN);
        lastModifiedTime = clockInTask.getLastModifiedTime();
    }

    public boolean revocable() {
        return revocable(null);
    }

    public boolean revocable(Out<String> reason) {
        if (reason == null) {
            reason = new Out<>();
        }

        if (last1Record == null) {
            reason.setValue(NOT_EXISTS_ANY_CLOCK_IN_RECORD);
            return false;
        }

        if (last1Record.getRevoked() == BoolEnum.Y) {
            reason.setValue(CANNOT_REVOKE_CONSECUTIVELY);
            return false;
        }

        if (!last1Record.getCreateBy().equals(ContextHelper.getEmpNo())) {
            reason.setValue(CANNOT_REVOKE_OTHERS_RECORD);
            return false;
        }

        // 数据库中打卡时间为当地时区时间，转换为服务器时间后进行比较；
        Date now = new Date();
        Date clockInTime = clockInTask.getTimeZone().fix(last1Record.getClockInTime());
        if (now.getTime() - clockInTime.getTime() > REVOCABLE_MILLISECONDS) {
            reason.setValue(CANNOT_REVOKE_RECORD_OVER_10_MINUTES);
            return false;
        }

        // 已有值守打卡记录，不能撤销测试阶段终止卡
        if (TEST_END_OPTIONS.contains(last1Record.getClockInOption())) {
            ClockInTask onDutyTask = ClockInQueryAbility.getKeyTask(
                    clockInTask.getBatchTaskId(), ClockInTaskTypeEnum.ON_DUTY, ID);
            if (onDutyTask != null) {
                List<ClockInRecord> onDutyRecords = getLastClockInRecords(onDutyTask.getId(), 1, null, ID);
                if (CollectionUtils.isNotEmpty(onDutyRecords)) {
                    reason.setValue(CANNOT_REVOKE_ON_DUTY_RECORD_EXISTS);
                    return false;
                }
            }
        }

        return true;
    }

    public boolean revoke(String recordId) {
        Out<String> reason = new Out<>();
        // 用户指定撤销某一条打卡记录的情况，校验必须是最后一条打卡记录
        if (recordId != null && last1Record != null && !last1Record.getId().equals(recordId)) {
            throw new LcapBusiException(NOT_LAST_CLOCK_IN_RECORD);
        }
        if (!revocable(reason)) {
            throw new LcapBusiException(reason.getValue());
        }

        revokeLastRecord();
        rollbackClockInState();
        rollbackClockInTaskStatus();
        rollbackOnDutyClockInTask();
        rollbackOperationResult();

        rollbackCallPlans();

        operationCache.transFlush();
        return true;
    }

    /**
     * 更新最后一个打卡记录的撤销状态
     */
    private void revokeLastRecord() {
        operationCache.update(new ClockInRecord() {{
            setId(last1Record.getId());
            setRevoked(BoolEnum.Y);
        }});
    }

    /**
     * 回滚批次任务的打卡状态
     */
    @SneakyThrows
    private void rollbackClockInState() {
        if (last1Record.getClockInOption().getOptionType() == ClockInOptionTypeEnum.PROCESSING
                || (!isOperationOwner && !isOnDutyOwner)) {
            return;
        }

        batchTask = (IBatchTask) QueryDataHelper.get(
                clockInTask.getEntityType().getEntityClass(),
                Lists.newArrayList(CLOCK_IN_STATE, CURRENT_STATUS),
                clockInTask.getBatchTaskId());
        ClockInStateEnum clockInState;
        switch (batchTask.getClockInState()) {
            case PREPARING:
                clockInState = null;
                break;
            case ABOLISHED:
                clockInState = last2Record == null
                        ? null : ClockInStateEnum.PREPARING;
                break;
            case CLOSED:
                clockInState = clockInTask.getTaskType() == ClockInTaskTypeEnum.OPERATION
                        ? ClockInStateEnum.TESTING : ON_DUTY_GOING;
                break;
            default:
                clockInState = batchTask.getClockInState().prev();
                break;
        }

        operationCache.update(
                clockInTask.getEntityType().getEntityClass(),
                MapUtils.newHashMap(
                        ID, batchTask.getId(),
                        CLOCK_IN_STATE, clockInState));
    }

    /**
     * 回滚打卡任务的状态
     */
    private void rollbackClockInTaskStatus() {
        if (last2Record == null) {
            operationCache.update(ClockInTask.class, MapUtils.newHashMap(
                    ID, clockInTask.getId(),
                    TASK_STATUS, null));
        } else {
            clockInTask.setTaskStatus(last2Record.getClockInOption());
            operationCache.update(clockInTask);
        }

        // 撤销操作负责人的 操作取消卡，所有相关任务都要回退
        if (isOperationOwner && last1Record.getClockInOption() == ClockInOptionEnum.CANCEL) {
            rollbackRelatedTaskStatus();
        }
    }

    /**
     * 如果是撤销值守继续卡的话，回滚已值守次数和阶段值守结束时间
     */
    private void rollbackOnDutyClockInTask() {
        // 如果是操作负责人撤销测试结束卡的话，清空已值守次数和阶段值守结束时间
        if (TEST_END_OPTIONS.contains(last1Record.getClockInOption()) && isOperationOwner) {
            ClockInTask onDutyTask = ClockInQueryAbility.getKeyTask(
                    clockInTask.getBatchTaskId(), ClockInTaskTypeEnum.ON_DUTY);
            operationCache.update(
                    ClockInTask.class,
                    MapUtils.newHashMap(
                            ID, onDutyTask.getId(),
                            STAGE_ON_DUTY_END_TIME, null,
                            ALREADY_ON_DUTY_FREQUENCY, null));
        }
        //如果是撤销值守继续卡的话，回滚已值守次数和阶段值守结束时间
       if (last1Record.getClockInOption() == ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE
               && lastModifiedTime.after(last1Record.getCreateTime())) {
           // 撤销恢复卡时，这里不变
           if (last2Record != null && last2Record.getClockInOption() == ClockInOptionEnum.INDICATOR_ERROR_HANDLING){
               return;
           }
           //【阶段值守结束时间】 = 【阶段值守结束时间】 - 值守时长/值守频次
           double totalDurationSecond = clockInTask.getTotalOnDutyDuration() * 60 * 60;
           double intervalSecond = totalDurationSecond / clockInTask.getOnDutyFrequency();
           Calendar stageOnDutyEndTime = Calendar.getInstance();
           stageOnDutyEndTime.setTime(clockInTask.getStageOnDutyEndTime());
           stageOnDutyEndTime.add(Calendar.SECOND, - (int) intervalSecond);
           clockInTask.setStageOnDutyEndTime(stageOnDutyEndTime.getTime());
           // 已值守次数-1
           clockInTask.setAlreadyOnDutyFrequency(clockInTask.getAlreadyOnDutyFrequency() - 1);
           operationCache.update(clockInTask);
       }
    }

    private void rollbackRelatedTaskStatus() {
        List<ClockInTask> relatedTasks = relatedTasksProvider.get();
        List<String> relatedTaskIds = relatedTasks.stream()
                .map(ClockInTask::getId)
                .collect(Collectors.toList());
        List<ClockInRecord> clockInRecords = QueryDataHelper.query(
                ClockInRecord.class,
                Lists.newArrayList(PID, CLOCK_IN_OPTION),
                relatedTaskIds,
                Lists.newArrayList(
                        new Filter(REVOKED, Comparator.EQ, Lists.newArrayList(BoolEnum.N))));

        // 打过卡的任务，回退到上一步状态
        List<ClockInTask> clockedRelatedTasks = clockInRecords.stream()
                        .collect(Collectors.groupingBy(ClockInRecord::getPid))
                        .entrySet().stream()
                        .map(e -> {
                            ClockInTask relatedTask = new ClockInTask();
                            clockInTask.setId(e.getKey());

                            e.getValue().sort((a, b) -> b.getClockInTime().compareTo(a.getClockInTime()));
                            ClockInRecord lastRecord = e.getValue().get(0);
                            clockInTask.setTaskStatus(lastRecord.getClockInOption());

                            return relatedTask;
                        }).collect(Collectors.toList());
        operationCache.update(clockedRelatedTasks);

        // 没有打过卡的其他任务，回退到空状态
        List<Map<String, Object>> relatedStatus = relatedTaskIds.stream()
                .filter(id -> clockInRecords.stream().noneMatch(r -> r.getPid().equals(id)))
                .map(id -> MapUtils.<String, Object>newHashMap(ID, id, TASK_STATUS, null))
                .collect(Collectors.toList());
        operationCache.update(ClockInTask.class, relatedStatus);
    }

    /**
     * 回滚结果反馈
     */
    private void rollbackOperationResult() {
        if (batchTask == null
                || (batchTask.getClockInState() != ClockInStateEnum.CLOSED
                        && batchTask.getClockInState() != ClockInStateEnum.ABOLISHED)
                || AssignmentStatusEnum.RESULT_UNDER_REVIEW.getValue()
                        .equals(batchTask.getCurrentStatus())) {
            return;
        }

        Map<String, Object> forUpdate = MapUtils.newHashMap(
                ID, batchTask.getId(),
                IS_CANCELED, null,
                IS_ROLLBACK_DONE, null,
                IS_ONE_TIME_SUCCEED, null,
                ACTUAL_OPERATION_START_TIME, null,
                ACTUAL_OPERATION_END_TIME, null,
                TEST_FINISH_TIME, null);
        operationCache.update(clockInTask.getEntityType().getEntityClass(), forUpdate);
    }

    private void rollbackCallPlans() {
        if (last1Record.getClockInOption() == ClockInOptionEnum.CANCEL) {
            // 需要恢复呼叫计划的任务ID列表
            List<String> taskIds = Lists.newArrayList(clockInTask.getId());

            if (isOperationOwner) {
                taskIds.addAll(relatedTasksProvider.getIds());
            }

            clockInCallPlanAbolishAbility.recover(last1Record.getCreateTime(), taskIds);
            return;
        }

        CallReasonEnum revokedState = CallReasonEnum.fromOption(last1Record.getClockInOption());
        if (revokedState == null) {
            return;
        }

        if (revokedState == CallReasonEnum.EXCEPTION) {
            clockInCallPlanDeleteAbility.delete(
                    last1Record.getCreateTime(), clockInTask.getId(), CallReasonEnum.EXCEPTION);
            return;
        }

        rollbackStateCallPlans(revokedState);
    }

    /**
     * 阶段打卡撤回
     */
    private void rollbackStateCallPlans(CallReasonEnum revokedState) {
        String taskIdForDeletePlan = clockInTask.getId();
        if (revokedState == CallReasonEnum.TEST_END) {
            ClockInTask onDutyTask = ClockInQueryAbility.getKeyTask(
                    clockInTask.getBatchTaskId(), ClockInTaskTypeEnum.ON_DUTY);
            if (onDutyTask != null) {
                taskIdForDeletePlan = onDutyTask.getId();

                // 清空值守任务的呼叫时间
                operationCache.update(ClockInTask.class, MapUtils.newHashMap(
                        ID, onDutyTask.getId(),
                        PLAN_CALL_UTC_TIME, null));
            }
        }
        // 撤销打卡时，删除掉最后一条打卡记录创建的呼叫计划
        clockInCallPlanDeleteAbility.delete(last1Record.getCreateTime(), taskIdForDeletePlan);

        // 撤销打卡时，恢复了由最后一条打卡记录更新为废止状态的呼叫计划
        List<ClockInCallPlan> callPlans = QueryDataHelper.query(
                ClockInCallPlan.class,
                Lists.newArrayList(ID, CALL_UTC_TIME),
                Lists.newArrayList(
                        new Filter(OBJECT_ID, Comparator.EQ, clockInTask.getId()),
                        new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.ABOLISHED)),
                        new Filter(LAST_MODIFIED_TIME, Comparator.GE, last1Record.getCreateTime())));
        if (!CollectionUtils.isNotEmpty(callPlans)) {
            return;
        }

        clockInCallPlanAbolishAbility.recover(
                callPlans.stream()
                        .map(ClockInCallPlan::getId)
                        .collect(Collectors.toList()));

        // 恢复打卡任务的呼叫时间
        long callUtcTime = callPlans.stream()
                .map(ClockInCallPlan::getCallUtcTime)
                .min(Long::compare)
                .orElseThrow(RuntimeException::new);
        operationCache.update(new ClockInTask() {{
            setId(clockInTask.getId());
            setPlanCallUtcTime(callUtcTime);
        }});
    }

    private class RelatedTasksProvider {
        private List<ClockInTask> value;

        public List<ClockInTask> get() {
            if (value != null) {
                return value;
            }

            return value = QueryDataHelper.query(
                    ClockInTask.class,
                    com.google.common.collect.Lists.newArrayList(ID),
                    com.google.common.collect.Lists.newArrayList(
                            new Filter(BATCH_TASK_ID, Comparator.EQ, clockInTask.getBatchTaskId()),
                            new Filter(ID, Comparator.NE, clockInTask.getId())));
        }

        public List<String> getIds() {
            return get().stream()
                    .map(ClockInTask::getId)
                    .collect(Collectors.toList());
        }
    }

    public static Boolean updateOnDutyData () {
        List<IFilter> batchFilters = new ArrayList<>();
        batchFilters.add(new Filter(CLOCK_IN_STATE, Comparator.IN, Arrays.asList(ON_DUTY_GOING)));
        List<String> batchIds = QueryDataHelper.query(BatchTask.class, Arrays.asList(ID), batchFilters)
                .stream()
                .map(BatchTask::getId)
                .collect(Collectors.toList());
        batchIds.addAll(QueryDataHelper.query(SubcontractorBatchTask.class, Arrays.asList(ID), batchFilters)
                .stream()
                .map(SubcontractorBatchTask::getId)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(batchIds)) {
            return false;
        }
        List<ClockInTask> clockInTasks = QueryDataHelper.query(
                ClockInTask.class,
                com.google.common.collect.Lists.newArrayList(ID,
                        ENTITY_TYPE,
                        BATCH_TASK_ID,
                        TOTAL_ON_DUTY_DURATION,
                        ON_DUTY_FREQUENCY,
                        STAGE_ON_DUTY_END_TIME,
                        ALREADY_ON_DUTY_FREQUENCY),
                com.google.common.collect.Lists.newArrayList(
                        new Filter(BATCH_TASK_ID, Comparator.IN, batchIds),
                        new Filter(ABOLISHED, Comparator.EQ, Lists.newArrayList(N)),
                        new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(ClockInTaskTypeEnum.ON_DUTY))));
        Map<String, ClockInQueryAbility.OnDutyInfo> onDutyInfoMap = ClockInQueryAbility.getOnDutyInfos(clockInTasks, true);
        for (ClockInTask clockInTask : clockInTasks) {
            ClockInQueryAbility.OnDutyInfo onDutyInfo = onDutyInfoMap.get(clockInTask.getId());
            if (onDutyInfo != null) {
                clockInTask.setAlreadyOnDutyFrequency(onDutyInfo.getLastOnDutyStage().getStageNumber() - 1);
                clockInTask.setStageOnDutyEndTime(onDutyInfo.getLastOnDutyStage().getEndTimeDate());
            }
        }
        return SaveDataHelper.batchUpdate(clockInTasks);
    }
}
