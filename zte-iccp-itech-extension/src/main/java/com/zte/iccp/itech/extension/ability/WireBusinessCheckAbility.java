package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.CommonUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.ProdCateLvlConsts;
import com.zte.iccp.itech.extension.domain.model.WireBusinessCheck;
import com.zte.iccp.itech.extension.domain.model.base.TextValueTypePair;
import com.zte.iccp.itech.extension.domain.model.vo.TableDisplayRows;
import com.zte.iccp.itech.extension.domain.model.vo.WireBusinessCheckVO;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.iccp.itech.extension.spi.model.nis.ProductClassificationTree;
import com.zte.paas.lcap.core.orm.query.AbstractFilter;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DEFAULT_EMPTY_TOTAL;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DEFAULT_PAGE_NO;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.WireBusinessCheckFieldConsts.*;

/**
 * 有线业务检查能力类
 * 提供有线业务检查相关的查询、初始化、数据处理等功能
 */
public class WireBusinessCheckAbility {

    /**
     * 批量检索有线业务检查表信息
     * 根据产品ID和文档名称进行检索
     * 
     * @param productIds 产品ID列表
     * @param documentName 文档名称
     * @return 有线业务检查信息列表
     */
    public static List<WireBusinessCheck> queryByProductAndName(List<String> productIds, String documentName) {
        List<IFilter> conditionFilterList = new ArrayList<>();
        List<String> fieldList = Lists.newArrayList(ID, PRODUCT_MODEL, DOCUMENT_NAME, LINK, REMARK);

        // 构建产品ID过滤条件
        if (!CollectionUtils.isEmpty(productIds)) {
            IFilter modelFilter = productIds.stream()
                    .map(productId -> new Filter(PRODUCT_MODEL, Comparator.LIKE, CommonConstants.PERCENT + productId + CommonConstants.PERCENT))
                    .reduce(AbstractFilter::or)
                    .orElseThrow(() -> new IllegalArgumentException("productIds cannot be empty"));
            conditionFilterList.add(modelFilter);
        }

        // 构建文档名称过滤条件
        if (StringUtils.hasText(documentName)) {
            Filter documentNameFilter = new Filter(DOCUMENT_NAME, Comparator.LIKE, CommonConstants.PERCENT + documentName + CommonConstants.PERCENT);
            conditionFilterList.add(documentNameFilter);
        }
        
        // 执行查询并返回结果
        return QueryDataHelper.query(WireBusinessCheck.class, fieldList, conditionFilterList);
    }

    /**
     * 初始化产品信息
     * 根据路径和级别获取产品分类树
     * 
     * @param paths 路径列表
     * @param level 级别
     * @return 产品分类树列表
     */
    public static List<ProductClassificationTree> initProduct(List<String> paths, String level) {
        // 参数校验
        if (CollectionUtils.isEmpty(paths) || !StringUtils.hasText(level)) {
            return new ArrayList<>();
        }
        
        // 获取并扁平化产品树
        List<ProductClassificationTree> products = flattenTree(NisClient.queryProductTree(ProdCateLvlConsts.SUB_CATEGORY));
        
        // 过滤符合条件的产品
        return products.stream()
                .filter(i -> level.equals(i.getProdClassLevel()))
                .filter(i -> StringUtils.hasText(i.getFullIdPath()) && CommonUtils.startsWiths(i.getFullIdPath(), paths))
                .collect(Collectors.toList());
    }

    /**
     * 包装展示数据
     * 将业务数据转换为前端展示所需的格式
     * 
     * @param wireBusinessCheckList 有线业务检查列表
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 表格展示数据
     */
    public static TableDisplayRows<WireBusinessCheckVO> displayWireBusinessCheckList(List<WireBusinessCheck> wireBusinessCheckList,
                                                                                     Integer pageNum, Integer pageSize) {
        TableDisplayRows<WireBusinessCheckVO> tableDisplayRows = new TableDisplayRows<>();
        
        // 处理空数据情况
        if (CollectionUtils.isEmpty(wireBusinessCheckList)) {
            tableDisplayRows.setCurrent(Long.valueOf(DEFAULT_PAGE_NO));
            tableDisplayRows.setTotal(Long.valueOf(DEFAULT_EMPTY_TOTAL));
            tableDisplayRows.setRecords(Lists.newArrayList());
            return tableDisplayRows;
        }

        // 获取用户信息
        List<String> userIdList = wireBusinessCheckList.stream()
                .map(WireBusinessCheck::getLastModifiedBy)
                .distinct().collect(Collectors.toList());
        Map<String, String> userNameMap = HrClient.queryEmployeeNameInfo(userIdList);

        // 获取产品信息
        List<String> productIds = wireBusinessCheckList.stream()
                .map(WireBusinessCheck::getProductModel)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(t -> CommonUtils.removeTrailingSlashes(t.getValue()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, String> productNameMap = NisClient.queryProductPathName(productIds);

        // 数据包装转换
        List<WireBusinessCheckVO> wireBusinessCheckListVOS = Lists.newArrayList();
        for (WireBusinessCheck wire : wireBusinessCheckList) {
            // 跳过无产品模型的数据
            if (wire.getProductModel() == null) {
                continue;
            }
            
            WireBusinessCheckVO wireBusinessCheckListVO = new WireBusinessCheckVO();
            String product = productNameMap.get(CommonUtils.removeTrailingSlashes(wire.getProductModel().get(CommonConstants.INTEGER_ZERO).getValue()));
            String[] products = CommonUtils.parseLevels(product);

            // 设置产品层级信息
            wireBusinessCheckListVO.setProductOperationTeam(products[CommonConstants.INTEGER_ZERO]);
            wireBusinessCheckListVO.setProductLine(products[CommonConstants.INTEGER_ONE]);
            wireBusinessCheckListVO.setProductMainCategory(products[CommonConstants.INTEGER_TWO]);
            wireBusinessCheckListVO.setProductSubCategory(products[CommonConstants.INTEGER_THREE]);
            
            // 设置其他基本信息
            wireBusinessCheckListVO.setDocumentName(wire.getDocumentName());
            wireBusinessCheckListVO.setLink(wire.getLink());
            wireBusinessCheckListVO.setRemark(wire.getRemark());
            wireBusinessCheckListVO.setUpdatedUser(userNameMap.get(wire.getLastModifiedBy()));
            wireBusinessCheckListVO.setUpdatedDate(wire.getLastModifiedTime());
            wireBusinessCheckListVO.setEntityId(wire.getId());

            wireBusinessCheckListVOS.add(wireBusinessCheckListVO);
        }

        // 设置分页信息
        tableDisplayRows.setCurrent(Long.valueOf(pageNum));
        tableDisplayRows.setTotal(Long.valueOf(pageSize));
        tableDisplayRows.setRecords(wireBusinessCheckListVOS);

        return tableDisplayRows;
    }

    /**
     * 保存有线业务检查数据
     * 更新缺少产品层级信息的记录
     */
    public static void saveWireBusinessCheckData(String id) {
        // 初始化查询条件 - 查找有产品模型但缺少产品层级信息的记录
        final List<IFilter> conditionFilterList = Collections.singletonList(new Filter(ID, Comparator.EQ, id));

        // 查询需要更新的记录
        List<WireBusinessCheck> wireBusinessCheckList = Optional.ofNullable(
                QueryDataHelper.query(WireBusinessCheck.class, Collections.emptyList(), conditionFilterList)
        ).orElse(Collections.emptyList());

        if (CollectionUtils.isEmpty(wireBusinessCheckList)) {
            return;
        }

        // 提取产品ID列表
        List<String> productIdList = wireBusinessCheckList.stream()
                .map(wire -> Optional.ofNullable(wire.getProductModel())
                        .filter(list -> !list.isEmpty())
                        .map(list -> list.get(CommonConstants.INTEGER_ZERO))
                        .map(TextValueTypePair::getValue)
                        .map(CommonUtils::removeTrailingSlashes)
                        .orElse(null))
                .filter(com.zte.km.udm.common.util.StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        // 查询产品信息并构建映射
        Map<String, BasicProductInfo> productMap = Optional.of(NisClient.queryProductInfo(productIdList))
                .orElse(Collections.emptyList()).stream()
                .filter(Objects::nonNull)
                .filter(info -> com.zte.km.udm.common.util.StringUtils.isNotEmpty(info.getIdFullPath()))
                .collect(Collectors.toMap(
                        BasicProductInfo::getIdFullPath,
                        Function.identity(),
                        (existing, replacement) -> existing));

        // 构建更新列表
        List<WireBusinessCheck> updates = wireBusinessCheckList.stream()
                .map(wire -> {
                    WireBusinessCheck update = new WireBusinessCheck();
                    update.setId(wire.getId());

                    Optional.ofNullable(wire.getProductModel())
                            .filter(list -> !list.isEmpty())
                            .map(list -> list.get(CommonConstants.INTEGER_ZERO).getValue())
                            .ifPresent(modelValue -> {
                                BasicProductInfo productInfo = productMap.get(modelValue);
                                if (productInfo == null) {
                                    return;
                                }

                                // 解析产品层级信息
                                String[] productLevels = CommonUtils.safeParseLevels(CommonUtils.parseLevels(modelValue));
                                String[] cnNames = CommonUtils.safeParseLevels(CommonUtils.parseLevels(productInfo.getNamePathZh()));
                                String[] enNames = CommonUtils.safeParseLevels(CommonUtils.parseLevels(productInfo.getNamePathEn()));

                                // 构建产品路径
                                buildProductPath(update, productLevels, cnNames, enNames);
                            });
                    return update;
                })
                .collect(Collectors.toList());

        // 批量更新数据
        if (!CollectionUtils.isEmpty(updates)) {
            SaveDataHelper.batchUpdate(updates);
        }
    }

    /**
     * 构建产品路径
     * 设置产品的四个层级信息
     * 
     * @param update 待更新的对象
     * @param levels 层级ID数组
     * @param cnNames 中文名称数组
     * @param enNames 英文名称数组
     */
    private static void buildProductPath(WireBusinessCheck update, String[] levels, String[] cnNames, String[] enNames) {
        StringBuilder pathBuilder = new StringBuilder();

        // 遍历四个层级
        IntStream.range(0, 4).forEach(i -> {
            if (com.zte.km.udm.common.util.StringUtils.isNotEmpty(levels[i])) {
                // 构建累积路径
                pathBuilder.append(levels[i]).append(CommonConstants.FORWARD_SLASH);
                String currentPath = pathBuilder.toString();
                
                // 根据层级设置对应字段
                switch (i) {
                    case 0: // 产品运营团队
                        safeSetField(update::setProductOperationTeam, currentPath, cnNames[i], enNames[i]);
                        break;
                    case 1: // 产品线
                        safeSetField(update::setProductLine, currentPath, cnNames[i], enNames[i]);
                        break;
                    case 2: // 产品大类
                        safeSetField(update::setProductMainCategory, currentPath, cnNames[i], enNames[i]);
                        break;
                    case 3: // 产品子类
                        safeSetField(update::setProductSubCategory, currentPath, cnNames[i], enNames[i]);
                        break;
                }
            }
        });
    }

    /**
     * 设置字段值
     * 构建TextValuePair列表并设置到目标字段
     * 
     * @param setter 字段设置函数
     * @param path 路径
     * @param nameZh 中文名称
     * @param nameEn 英文名称
     */
    private static void safeSetField(Consumer<List<TextValueTypePair>> setter, String path, String nameZh, String nameEn) {
        List<TextValueTypePair> list = TextValueTypePair.buildList(
                path,
                Optional.ofNullable(nameZh).orElse(CommonConstants.EMPTY_STRING),
                Optional.ofNullable(nameEn).orElse(CommonConstants.EMPTY_STRING),
                CommonConstants.I18N
        );
        setter.accept(list);
    }

    /**
     * 扁平化产品分类树
     * 将树形结构转换为列表
     * 
     * @param trees 产品分类树列表
     * @return 扁平化后的产品分类列表
     */
    private static List<ProductClassificationTree> flattenTree(List<ProductClassificationTree> trees) {
        List<ProductClassificationTree> result = new ArrayList<>();
        if (trees == null || trees.isEmpty()) {
            return result;
        }
        
        // 遍历每棵树并扁平化
        for (ProductClassificationTree tree : trees) {
            flattenTreeHelper(tree, result);
        }
        return result;
    }

    /**
     * 扁平化树的辅助方法
     * 递归处理树节点
     * 
     * @param tree 当前树节点
     * @param result 结果列表
     */
    private static void flattenTreeHelper(ProductClassificationTree tree, List<ProductClassificationTree> result) {
        if (tree == null) {
            return;
        }
        
        // 添加当前节点
        result.add(tree);
        
        // 递归处理子节点
        if (tree.getChildProdClass() != null && !tree.getChildProdClass().isEmpty()) {
            for (ProductClassificationTree child : tree.getChildProdClass()) {
                flattenTreeHelper(child, result);
            }
        }
    }
}
