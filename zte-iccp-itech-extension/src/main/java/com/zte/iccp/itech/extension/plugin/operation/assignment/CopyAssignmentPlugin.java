package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.domain.enums.UserTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ASSIGNMENT_TYPE;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ENTITY_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.paas.lcap.ddm.domain.enums.OpenTypeEnum.NEW_TAB;

public class CopyAssignmentPlugin extends BaseOperationPlugin {

    /**
     * 任务主键ID - dataKey
     */
    private static final String ASSIGNMENT_ID_KEY = "pk_25049603";

    /**
     * 自定义页面操作项，复制按钮绑定的编码
     */
    private static final String SOURCE_OPERATION_KEY = "copy";

    /**
     * 低代码复制能力固定的key：copyFromPkId
     */
    private static final String COPY_FROM_PK_ID = "copyFromPkId";

    /**
     * 复制任务
     *
     * @param executeEvent executeEvent
     */
    @Override
    public void afterExecute(ExecuteEvent executeEvent) {
        IFormView formView = getView();

        // 1.获取待赋值任务主键
        JSONObject assignmentInfo = (JSONObject) executeEvent.getArgs().get(CommonConstants.VALUE);
        String assignmentId = assignmentInfo.getString(ASSIGNMENT_ID_KEY);

        // 2.任务校验
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                assignmentId,
                Lists.newArrayList(ENTITY_ID, ASSIGNMENT_TYPE),
                Assignment.class);
        if (Objects.isNull(assignment)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_MANAGE_NON_EXISTENT_WARNING, MsgType.WARNING);
            return;
        }

        // 3.任务数据复制
        AssignmentTypeEnum assignmentType
                = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        if (Objects.isNull(assignmentType)) {
            return;
        }

        String entityId = assignment.getEntityId();
        switch (assignmentType) {
            case NETWORK_CHANGE:
                String msg = beforeValidate(entityId);
                if (StringUtils.hasText(msg)) {
                    formView.showMessage(msg, MsgType.WARNING);
                } else {
                    copy(formView, entityId, PageConstants.PAGE_NETWORK_CHANGE_BILL,
                            ChangeOrder.class, BILL_NAME_NETWORK_CHANGE_BILL_COPY);
                }
                break;

            case TECHNOLOGY_MANAGEMENT:
                copy(formView, entityId, PageConstants.PAGE_BILL_TECHNOLOGY_MANAGEMENT_BILL,
                        TechnologyManagementOrder.class, BILL_NAME_TECHNOLOGY_MANAGEMENT_BILL_COPY);
                break;

            case SUBCONTRACTOR_NETWORK_CHANGE:
                copy(formView, entityId, PageConstants.PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_BILL,
                        SubcontractorChangeOrder.class, BILL_NAME_SUBCONTRACTOR_NETWORK_CHANGE_BILL_COPY);
                break;

            case PERMISSION_APPLICATION:
                PermissionApplication application = PermissionApplicationAbility.get(assignment.getEntityId());
                copy(formView,
                        application.getId(),
                        UserTypeEnum.EXTERNAL.name().equals(application.getType())
                                ? PageConstants.PAGE_BILL_EXTERNAL_PERMISSION_APPLICATION
                                : PageConstants.PAGE_BILL_INTERNAL_PERMISSION_APPLICATION,
                        PermissionApplication.class,
                        TITLE_PERMISSION_APPLICATION);
                break;

            case OPERATION_PLAN_TASK:
                copy(formView, entityId, PageConstants.PAGE_PLAN_OPERATION_BILL,
                        PlanOperationOrder.class, BILL_NAME_OPERATION_PLAN_COPY);
                break;

            default:
                formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_COPY_ERROR_TYPE, MsgType.WARNING);
                break;
        }
    }

    /**
     * 复制
     */
    private void copy(
            IFormView formView,
            String pkId,
            String pageId,
            Class<? extends BaseEntity> entityClass,
            String pageTitle) {

        FormShowParameter formShowParameter = formView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setCopy(true);
        //这里取了创建/编辑页面的pageId
        formShowParameter.setPageId(pageId);
        //B实体表的唯一标识
        formShowParameter.setBizObjectCode(EntityHelper.getEntityId(entityClass));
        //自定义页面的复制按钮绑定的编码
        formShowParameter.setSourceOperationKey(SOURCE_OPERATION_KEY);

        Map<String, Object> customParameters = new HashMap<>();
        customParameters.put(COPY_FROM_PK_ID, pkId);
        customParameters.put(HIDDEN_OPERATION, true);
        customParameters.put(FULL_SCREEN, true);
        customParameters.put(OPEN_TYPE, NEW_TAB.getValue());
        customParameters.put(PAGE_TITLE, MsgUtils.getMessage(pageTitle));
        customParameters.put(OPERATION_KEY, OPERATION_CREATE);
        customParameters.put(IS_COPY, true);
        formShowParameter.setCustomParameters(customParameters);
        formShowParameter.setPageStatus(PageStatusEnum.NEW);

        formView.showForm(formShowParameter);
    }

    /**
     * 前置校验
     */
    private String beforeValidate(String changeOrderId) {
        ChangeOrder changeOrder = ChangeOrderAbility.get(changeOrderId, Lists.newArrayList(ID, SOURCE));
        String source = changeOrder.getSource();
        if (DataSourceEnum.IDOP.name().equals(source)) {
            return MessageConsts.ASSIGNMENT_OPERATION_WARNING_IDOP_COPY_ERROR_TYPE;
        } else if (DataSourceEnum.GUARANTEE.name().equals(source)) {
            return MessageConsts.GUARANTEE_CHANGE_ORDER_NOT_COPY_ERROR;
        } else {
            return "";
        }
    }
}
