package com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo;

import com.zte.iccp.itech.extension.spi.model.hol.PersonGeneralInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 审批进展
 */
@Getter
@Setter
@NoArgsConstructor
public class ApprovalProgressVO {
    // 审批人
    private List<PersonGeneralInfo> approvers;

    // 审批节点状态（已完成/激活）
    private String approvalStatus;

    // 审批节点
    private String approvalNode;

    // 审核结果
    private String approvalResult;

    // 审核意见
    private String approvalOpinion;

    // 审批时间
    private Date approvalDate;
}
