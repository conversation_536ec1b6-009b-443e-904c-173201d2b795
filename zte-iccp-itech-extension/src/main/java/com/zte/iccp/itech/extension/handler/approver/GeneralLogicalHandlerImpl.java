package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.ability.ApproverConfigAbility.getApprovalPersonsByType;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.IS_MAIN_PRODUCT;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.PRODUCT_MODEL;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.THREE;
import static com.zte.paas.lcap.metadata.engine.common.constant.NumberConst.FOUR;

/**
 * <AUTHOR>
 * @create 2025/3/10 下午3:04
 * 通用逻辑网元 审核人查询：【远程中心负责人审核】【远程中心方案提交】【研发经理审核】【研发领导审核】【远程中心操作实施人指派】
 * 一、申请单代表处属于国内代表处，且产品类型不属于算力及核心网，优先级规则如下：
 * 按照产品类型（产品型号>产品小类>产品大类>产品线）+ 片区 + 是否政企 精确匹配，读取【审核人】【审核组】作为当前处理人
 * 按照产品类型（产品型号>产品小类>产品大类>产品线）+ 营销 + 是否政企 精确匹配，读取【审核人】【审核组】作为当前处理人
 * <p>
 * 二、申请单【代表处】属于国内代表处，且产品类型属于算力及核心网：则在第一点的基础上，将产品型号替换成逻辑网元（逻辑网元来源也在申请单）；
 * <p>
 * 三、申请单【代表处】属于国际代表处，且产品类型属于不算力及核心网，优先级规则如下：
 * 按照产品类型（产品型号>产品小类>产品大类>产品线）+ 片区 （不区分是否政企，出现政企不同其他相同的数据时默认取其中一条） 精确匹配，读取【审核人】【审核组】作为当前处理人
 * 按照产品类型（产品型号>产品小类>产品大类>产品线）+ 营销 （不区分是否政企，出现政企不同其他相同的数据时默认取其中一条）  精确匹配，读取【审核人】【审核组】作为当前处理人
 * <p>
 * 四、申请单【代表处】属于国际代表处，且产品类型属于算力及核心网：在第三点的基础上，将产品型号替换成逻辑网元；
 */
@RequiredArgsConstructor
public class GeneralLogicalHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    private final ApprovalTypeEnum role;

    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        ApproverConfiguration approverConfiguration = getApprovers(changeOrder);
        return getApprovalPersonsByType(approverConfiguration, null);
    }

    public ApproverConfiguration getApprovers(ChangeOrder changeOrder) {
        ApproverConfiguration approverConfiguration;
        String responsibleDept = changeOrder.getResponsibleDept();
        String productCategory = changeOrder.getProductCategory();
        ApproverConfiguration queryParam = new ApproverConfiguration();
        queryParam.setApprovalNode(role);
        queryParam.setProdTeam(ProductUtils.getTeam(productCategory));
        queryParam.setProdLine(ProductUtils.getLine(productCategory));
        queryParam.setProdMainCategory(ProductUtils.getMain(productCategory));
        queryParam.setProdSubCategory(productCategory);
        queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));
        // 优先级 片区 > 营销
        queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
        if (DeptTypeEnum.INNER == ResponsibleUtils.getDeptType(responsibleDept)) {
            queryParam.setIsGov(changeOrder.getIsGovEnt());
        }
        int level = 0;
        if (ProductUtils.isCcn(productCategory)) {
            // 逻辑网元>产品小类>产品大类>产品线）+ 片区 + 是否政企 精确匹配
            queryParam.setLogicalNe(changeOrder.getLogicalNe());
            level = FOUR;
            approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, productCategory, level);
            if (approverConfiguration == null) {
                queryParam.setOrganizationRegion(null);
                approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, productCategory, level);
            }
            return approverConfiguration;
        }

        // 非核心网 查询变跟单操作对象的产品型号idpath
        List<String> idPaths = new ArrayList<>();
        List<OperationObject> operationObjects = QueryDataHelper.
                query(OperationObject.class, Arrays.asList(PRODUCT_MODEL, IS_MAIN_PRODUCT), changeOrder.getId());
        if (!CollectionUtils.isEmpty(operationObjects)) {
            // 取【主产品】操作对象的产品型号
            List<String> productIds = operationObjects
                    .stream()
                    .filter(item -> !CollectionUtils.isEmpty(item.getIsMainProduct())
                            && CommonConstants.Y.equals(item.getIsMainProduct().get(0).getValue()))
                    .map(OperationObject::getProductModel)
                    .collect(Collectors.toList());
            List<String> firstProductIds = operationObjects.stream().map(OperationObject::getProductModel)
                    .collect(Collectors.toList());
            // 历史数据兼容：如果主产品为空，默认走原来的逻辑
            idPaths = NisAbility.queryProductModelIdPath(CollectionUtils.isEmpty(productIds) ? firstProductIds : productIds);
        }
        // （产品型号>产品小类>产品大类>产品线）+ 片区 + 是否政企 精确匹配
        if (!CollectionUtils.isEmpty(idPaths)) {
            queryParam.setProductModelId(idPaths.get(0));
            level = THREE;
        }
        approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, CollectionUtils.isEmpty(idPaths) ?
                productCategory : idPaths.get(0), level);
        // 按照产品类型（产品型号>产品小类>产品大类>产品线）+ 营销 + 是否政企 精确匹配，
        if (approverConfiguration == null) {
            queryParam.setOrganizationRegion(null);
            approverConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, CollectionUtils.isEmpty(idPaths) ?
                    productCategory : idPaths.get(0), level);
        }

        return approverConfiguration;
    }
}
