package com.zte.iccp.itech.extension.plugin.operation.changeorder;

import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.event.AfterAddRowsEvent;
import com.zte.paas.lcap.core.event.AfterDeleteRowsEvent;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.core.event.PropertyChangedEvent;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteTransactionalEvent;
import com.zte.paas.lcap.ddm.common.api.operation.OperationContext;
import org.springframework.util.ObjectUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.Y;
import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.PRODUCT_MODEL_ID;


public class MainProductModelPlugin extends BaseFormPlugin {


        @Override
    public void afterDeleteRows(AfterDeleteRowsEvent e) {
        String entryKey = e.getEntryKey();
        if (!OPERATION_OBJECT_TABLE_PROPERTY_KEY.equals(entryKey)) {
            return;
        }
        List<DynamicDataEntity> rowEntities = e.getRowEntities();
        System.out.println(rowEntities);
    }
//
//    @Override
//    public void afterAddRows(AfterAddRowsEvent e) {
//        String entryKey = e.getEntryKey();
//        if (!OPERATION_OBJECT_TABLE_PROPERTY_KEY.equals(entryKey)) {
//            return;
//        }
//        int rowIndex = e.getRowIndex();
//        System.out.println(rowIndex);
//        // TODO: 2025/7/23 如何获取服务对象的字段值？需要测试下
//        IDataEntityCollection dataEntityCollection = getModel().getEntryRowEntities("operation_object");
//        DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(0);
//        Object o = dynamicDataEntity.get("network_id");
//        Object o1 = dynamicDataEntity.get("product_model");
//        Object o2 = dynamicDataEntity.get("batch_info_no");
//        System.out.println(o2);
//
//    }

    @Override
    public void propertyChanged(PropertyChangedEvent e) {
        ChangeData changeData = e.getChangeSet()[0];
        if (!changeData.getProperty().getKey().equals(FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY)) {
            return;
        }
        IDataEntityCollection dataEntityCollection = getModel().getEntryRowEntities(CidConstants.OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        if (!dataEntityCollection.isEmpty()) {
            // 取操作对象【主产品】数据行对应的产品型号数据
            for (int i = 0; i < dataEntityCollection.size(); i++) {
                DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
                String isMainProduct = TextValuePairHelper.getValue(dynamicDataEntity.get(OperationObjectFieldConsts.IS_MAIN_PRODUCT));
                if (Y.equals(isMainProduct)) {
                    Object productModel = dynamicDataEntity.get(FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY);
                    System.out.println(productModel);
                }
            }
        }
    }
}
