package com.zte.iccp.itech.extension.domain.enums.partnerchangeorder;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/6/18 下午6:22
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PartnerChangeOrderAttachmentEnum {

    /**
     * 紧急操作附件
     */
    EMERGENCY_OPERATION_ATTACHMENT("emergency_operation_attachment", "emergency_operation_attachment", null),
    /**
     * 网元清单
     */
    NE_LIST_FILE("ne_list_file", "AttachmentField_r3ytalhz", null),
    /**
     * 模型包
     */
    MODEL_PACKAGE("model_package", "model_package", null),
    /**
     * 其他附件
     */
    OTHER_ATTACHMENT("other_attachment", "other_attachment", null),

    ;

    //实体唯一标识
    private final String propertyKey;

    //布局唯一标识
    private final String cid;

    //子表单实体唯一标识
    private final String tablePropertyKey;
}
