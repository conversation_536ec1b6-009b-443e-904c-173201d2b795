package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

// 所以为什么这个支持人员不合成一张表，加个审批节点字段区分一下
// 重复的人快麻了。。。
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SupportStaffFieldConsts {
    // ======================== 数据库字段ID ============================
    /** 支持人员 */
    public static final String PERSON_ID = "person_id";
    /** 支持方式 */
    public static final String SUPPORT_WAY = "support_way";
    /** 任务说明 */
    public static final String TASK_DESC = "task_desc";
    /** 所属部门 */
    public static final String DEPT_ID = "dept_id";
    /** 联系方式 */
    public static final String TEL_NO = "tel_no";


    // =================== 支持人员（代表处产品 TD 审核）===================
    /** 支持人员 */
    public static final String PERSON_ID_REP_PROD_TD_APP = "person_id_rep_prod_td_app";
    /** 支持方式 */
    public static final String SUPPORT_WAY_REP_PROD_TD_APP = "support_way_rep_prod_td_app";
    /** 任务说明 */
    public static final String TASK_DESC_REP_PROD_TD_APP = "task_desc_rep_prod_td_app";
    /** 所属部门 */
    public static final String DEPT_ID_REP_PROD_TD_APP = "dept_id_rep_prod_td_app";
    /** 联系方式 */
    public static final String TEL_NO_REP_PROD_TD_APP = "tel_no_rep_prod_td_app";


    // ====================== 支持人员（研发经理审批）======================
    /** 支持人员 */
    public static final String PERSON_ID_RD_MANAGER = "person_id_rd_manager";
    /** 支持方式 */
    public static final String SUPPORT_WAY_RD_MANAGER = "support_way_rd_manager";
    /** 任务说明 */
    public static final String TASK_DESC_RD_MANAGER = "task_desc_rd_manager";
    /** 所属部门 */
    public static final String DEPT_ID_RD_MANAGER = "dept_id_rd_manager";
    /** 联系方式 */
    public static final String TEL_NO_RD_MANAGER = "tel_no_rd_manager";


    // =================== 支持人员（SSP 产品支持团队审批）==================
    /** 支持人员 */
    public static final String PERSON_ID_SSP_PROD_SUPPORT = "person_id_ssp_prod_support";
    /** 支持方式 */
    public static final String SUPPORT_WAY_SSP_PROD_SUPPORT = "support_way_ssp_prod_support";
    /** 任务说明 */
    public static final String TASK_DESC_SSP_PROD_SUPPORT = "task_desc_ssp_prod_support";
    /** 所属部门 */
    public static final String DEPT_ID_SSP_PROD_SUPPORT = "dept_id_ssp_prod_support";
    /** 联系方式 */
    public static final String TEL_NO_SSP_PROD_SUPPORT = "tel_no_ssp_prod_support";


    // ==================== 支持人员（服务产品支持部审批）===================
    /** 支持人员 */
    public static final String PERSON_ID_SERVICE_PROD_SUPPORT = "person_id_service_prod_support";
    /** 支持方式 */
    public static final String SUPPORT_WAY_SERVICE_PROD_SUPPORT = "support_way_service_prod_support";
    /** 任务说明 */
    public static final String TASK_DESC_SERVICE_PROD_SUPPORT = "task_desc_service_prod_support";
    /** 所属部门 */
    public static final String DEPT_ID_SERVICE_PROD_SUPPORT = "dept_id_service_prod_support";
    /** 联系方式 */
    public static final String TEL_NO_SERVICE_PROD_SUPPORT = "tel_no_service_prod_support";


    // =============== 支持人员（技术交付部/网络处审核远程方案）===============
    /** 支持人员 */
    public static final String PERSON_ID_TD_NET_DEPT_APP_SOLUTION = "person_id_td_net_dept_app_solution";
    /** 支持方式 */
    public static final String SUPPORT_WAY_TD_NET_DEPT_APP_SOLUTION = "support_way_td_net_dept_app_solution";
    /** 任务说明 */
    public static final String TASK_DESC_TD_NET_DEPT_APP_SOLUTION = "task_desc_td_net_dept_app_solution";
    /** 所属部门 */
    public static final String DEPT_ID_TD_NET_DEPT_APP_SOLUTION = "dept_id_td_net_dept_app_solution";
    /** 联系方式 */
    public static final String TEL_NO_TD_NET_DEPT_APP_SOLUTION = "tel_no_td_net_dept_app_solution";


    // ==================== 支持人员（远程中心方案提交）=====================
    /** 支持人员 */
    public static final String PERSON_ID_REMOTE_CENTER_SCHEME = "person_id_remote_center_scheme";
    /** 支持方式 */
    public static final String SUPPORT_WAY_REMOTE_CENTER_SCHEME = "support_way_remote_center_scheme";
    /** 任务说明 */
    public static final String TASK_DESC_REMOTE_CENTER_SCHEME = "task_desc_remote_center_scheme";
    /** 所属部门 */
    public static final String DEPT_ID_REMOTE_CENTER_SCHEME = "dept_id_remote_center_scheme";
    /** 联系方式 */
    public static final String TEL_NO_REMOTE_CENTER_SCHEME = "tel_no_remote_center_scheme";


    // =================== 支持人员（远程中心操作实施指派）===================
    /** 支持人员 */
    public static final String PERSON_ID_REMOTE_CENTER_OPER_ASSIGN = "person_id_remote_center_oper_assign";
    /** 支持方式 */
    public static final String SUPPORT_WAY_REMOTE_CENTER_OPER_ASSIGN = "support_way_remote_center_oper_assign";
    /** 任务说明 */
    public static final String TASK_DESC_REMOTE_CENTER_OPER_ASSIGN = "task_desc_remote_center_oper_assign";
    /** 所属部门 */
    public static final String DEPT_ID_REMOTE_CENTER_OPER_ASSIGN = "dept_id_remote_center_oper_assign";
    /** 联系方式 */
    public static final String TEL_NO_REMOTE_CENTER_OPER_ASSIGN = "tel_no_remote_center_oper_assign";


    // ================== 支持人员（技术交付部/网络处审核） ==================
    /** 支持人员 */
    public static final String PERSON_ID_TD_NET_DEPT_APPROVE = "person_id_td_net_dept_approve";
    /** 支持方式 */
    public static final String SUPPORT_WAY_TD_NET_DEPT_APPROVE = "support_way_td_net_dept_approve";
    /** 任务说明 */
    public static final String TASK_DESC_TD_NET_DEPT_APPROVE = "task_desc_td_net_dept_approve";
    /** 所属部门 */
    public static final String DEPT_ID_TD_NET_DEPT_APPROVE = "dept_id_td_net_dept_approve";
    /** 联系方式 */
    public static final String TEL_NO_TD_NET_DEPT_APPROVE = "tel_no_td_net_dept_approve";


    // ===================== 支持人员（网络服务部审批） =====================
    /** 支持人员 */
    public static final String PERSON_ID_NET_SERVICE_DEPT_APPROVE = "person_id_net_service_dept_approve";
    /** 支持方式 */
    public static final String SUPPORT_WAY_NET_SERVICE_DEPT_APPROVE = "support_way_net_service_dept_approve";
    /** 任务说明 */
    public static final String TASK_DESC_NET_SERVICE_DEPT_APPROVE = "task_desc_net_service_dept_approve";
    /** 所属部门 */
    public static final String DEPT_ID_NET_SERVICE_DEPT_APPROVE = "dept_id_net_service_dept_approve";
    /** 联系方式 */
    public static final String TEL_NO_NET_SERVICE_DEPT_APPROVE = "tel_no_net_service_dept_approve";


    // ======================= 支持人员（网服一体化） ======================
    /** 支持人员 */
    public static final String PERSON_ID_NET_SERVICE_INTEGRATION = "person_id_net_service_integration";
    /** 支持方式 */
    public static final String SUPPORT_WAY_NET_SERVICE_INTEGRATION = "support_way_net_service_integration";
    /** 任务说明 */
    public static final String TASK_DESC_NET_SERVICE_INTEGRATION = "task_desc_net_service_integration";
    /** 所属部门 */
    public static final String DEPT_ID_NET_SERVICE_INTEGRATION = "dept_id_net_service_integration";
    /** 联系方式 */
    public static final String TEL_NO_NET_SERVICE_INTEGRATION = "tel_no_net_service_integration";


    // ======================= 支持人员（研发一体化） ======================
    /** 支持人员 */
    public static final String PERSON_ID_RD_INTEGRATION = "person_id_rd_integration";
    /** 支持方式 */
    public static final String SUPPORT_WAY_RD_INTEGRATION = "support_way_rd_integration";
    /** 任务说明 */
    public static final String TASK_DESC_RD_INTEGRATION = "task_desc_rd_integration";
    /** 所属部门 */
    public static final String DEPT_ID_RD_INTEGRATION = "dept_id_rd_integration";
    /** 联系方式 */
    public static final String TEL_NO_RD_INTEGRATION = "tel_no_rd_integration";

    // ==================分包商 支持人员（网络处审核） ==================
    /** 支持人员 */
    public static final String PERSON_ID_NET_DEPT = "person_id_net_dept";
    /** 支持方式 */
    public static final String SUPPORT_WAY_NET_DEPT = "support_way_net_dept";
    /** 任务说明 */
    public static final String TASK_DESC_NET_DEPT = "task_desc_net_dept";
    /** 所属部门 */
    public static final String DEPT_ID_NET_DEPT = "dept_id_net_dept";
    /** 联系方式 */
    public static final String TEL_NO_NET_DEPT = "tel_no_net_dept";

    // ==================分包商 支持人员（办事处产品经理） ==================
    /** 支持人员 */
    public static final String PERSON_ID_OFFICE_PROD_MANAGER = "person_id_office_prod_manager";
    /** 支持方式 */
    public static final String SUPPORT_WAY_OFFICE_PROD_MANAGER = "support_way_office_prod_manager";
    /** 任务说明 */
    public static final String TASK_DESC_OFFICE_PROD_MANAGER = "task_desc_office_prod_manager";
    /** 所属部门 */
    public static final String DEPT_ID_OFFICE_PROD_MANAGER = "dept_id_office_prod_manager";
    /** 联系方式 */
    public static final String TEL_NO_OFFICE_PROD_MANAGER = "tel_no_office_prod_manager";

    // ==================分包商 支持人员（网络责任人） ==================
    /** 支持人员 */
    public static final String PERSON_ID_NET_OWNER = "person_id_net_owner";
    /** 支持方式 */
    public static final String SUPPORT_WAY_NET_OWNER = "support_way_net_owner";
    /** 任务说明 */
    public static final String TASK_DESC_NET_OWNER = "task_desc_net_owner";
    /** 所属部门 */
    public static final String DEPT_ID_NET_OWNER = "dept_id_net_owner";
    /** 联系方式 */
    public static final String TEL_NO_NET_OWNER = "tel_no_net_owner";
}
