package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;

@ApiModel("技术管理任务")
@Setter
@Getter
@BaseEntity.Info("assignment")
public class TechnologyManagementAssignment extends Assignment {

    @ApiModelProperty("任务分类")
    @JsonProperty(value = TechnologyManagementFieldConsts.TASK_CATEGORY)
    private String taskCategory;

    @ApiModelProperty("要求完成日期")
    @JsonProperty(value = TechnologyManagementFieldConsts.REQUIRED_COMPLETION_TIME)
    private Date requiredCompletionTime;

    @ApiModelProperty("子任务审核实体ID")
    @JsonProperty(value = TechnologyManagementFieldConsts.APPROVE_SUB_TASK_ID)
    private String approveSubTaskId;
}
