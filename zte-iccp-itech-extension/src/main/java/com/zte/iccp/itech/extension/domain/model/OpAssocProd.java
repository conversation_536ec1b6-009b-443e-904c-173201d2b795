package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.ProdTreeNodeIdDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.subentity.BaseOpAssocProdSubEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.OpAssocProdFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/03
 */
@Getter
@Setter
@BaseSubEntity.Info(value = "operation_association_product_table", parent = ChangeOrder.class)
public class OpAssocProd extends BaseOpAssocProdSubEntity {
    @JsonProperty(PRODUCT_TYPE)
    @JsonDeserialize(using = ProdTreeNodeIdDeserializer.class)
    private String productType;

    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    @JsonProperty(RESPONSIBLE_PERSON)
    private SingleEmployee responsiblePerson;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(IS_RELATED_PROD_APPROVAL_NET_SRV_INTEG)
    private BoolEnum isRelatedProdApprovalNetSrvInteg;

    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    @JsonProperty(APPROVER_NET_SERVICE_INTEGRATION)
    private SingleEmployee approverNetServiceIntegration;

    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @JsonProperty(APPROVE_GROUP_NET_SERVICE_INTEGRATION)
    private List<Employee> approveGroupNetServiceIntegration;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(APPROVE_RESULT_NET_SERVICE_INTEGRATION)
    private ApproveResultEnum approveResultNetServiceIntegration;

    @JsonProperty(APPROVE_OPINION_NET_SERVICE_INTEGRATION)
    private String approveOpinionNetServiceIntegration;

    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @JsonProperty(EMAIL_CC_NET_SERVICE_INTEGRATION)
    private List<Employee> emailCcNetServiceIntegration;

    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    @JsonProperty(APPROVED_BY_NET_SERVICE_INTEGRATION)
    private SingleEmployee approvedByNetServiceIntegration;

    @JsonProperty(APPROVED_TIME_NET_SERVICE_INTEGRATION)
    private Date approvedDate;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(IS_RELATED_PROD_APPROVAL_RD_INTEGRATION)
    private BoolEnum isRelatedProdApprovalRdIntegration;

    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    @JsonProperty(APPROVER_RD_INTEGRATION)
    private SingleEmployee approverRdIntegration;

    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @JsonProperty(APPROVE_GROUP_RD_INTEGRATION)
    private List<Employee> approveGroupRdIntegration;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(APPROVE_RESULT_RD_INTEGRATION)
    private ApproveResultEnum result;

    @JsonProperty(APPROVE_OPINION_RD_INTEGRATION)
    private String opinion;

    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @JsonProperty(EMAIL_CC_RD_INTEGRATION)
    private List<Employee> emailCcRdIntegration;

    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    @JsonProperty(APPROVED_BY_RD_INTEGRATION)
    private SingleEmployee approver;

    @JsonProperty(APPROVED_TIME_RD_INTEGRATION)
    private Date approvedTimeRdIntegration;

    @JsonProperty(INTEGRATED_ASSOCIATED_PRODUCT_ZH)
    private String assocProdNameZh;

    @JsonProperty(INTEGRATED_ASSOCIATED_PRODUCT_EN)
    private String assocProdNameEn;
}
