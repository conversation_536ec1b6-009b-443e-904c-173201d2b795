package com.zte.iccp.itech.extension.ability.clockin.callplan;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.HrHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.RemoteEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInCustomerFlagEnum;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInCallee;
import com.zte.iccp.itech.extension.domain.model.clockin.config.BaseWeeklyDutyPerson;
import com.zte.iccp.itech.extension.domain.model.clockin.config.NetSvrDutyChiefEngineer;
import com.zte.iccp.itech.extension.domain.model.clockin.config.OfficeDutyPersonnel;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import org.apache.commons.lang3.StringUtils;

import java.time.temporal.ChronoField;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.OfficeDutyPersonnelConsts.*;

/**
 * 查询呼叫对象
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/15
 */
public class CalleeQueryAbility {

    /**
     * 根据代表处、2层产品、客户标识查询当前日期对应的值班人员
     *
     * @param responsibleDept 代表处
     * @param productCategory 2层产品
     * @param customerTypeFlag 客户标识
     * @return 当前日期值班人员
     */
    public static OfficeDutyPersonnel queryOnDutyPerson(
            String responsibleDept,
            String productCategory,
            String customerTypeFlag) {
        return QueryDataHelper.queryOne(
                OfficeDutyPersonnel.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(OFFICE, Comparator.EQ, Lists.newArrayList(responsibleDept)),
                        new Filter(PROD_TYPE, Comparator.EQ, Lists.newArrayList(ProductUtils.getLine(productCategory))),
                        new Filter(CUSTOMER_FLAG, Comparator.REGEXP, ClockInCustomerFlagEnum.fromZhCn(customerTypeFlag))));
    }

    public static ClockInCallee getOfficeDutyPerson(
            ZonedTime time,
            String responsibleDeptId,
            String prodCategory,
            String customerFlag) {
        OfficeDutyPersonnel config = queryOnDutyPerson(responsibleDeptId, prodCategory, customerFlag);
        if (config == null) {
            return null;
        }
        SingleEmployee dutyPerson = getWeeklyDutyPerson(time, config);
        return employee2Callee(dutyPerson);
    }

    public static ClockInCallee getNetSvrDutyChiefEngineer(
            ZonedTime time,
            String prodCategory,
            String customerFlag) {
        NetSvrDutyChiefEngineer config = QueryDataHelper.queryOne(
                NetSvrDutyChiefEngineer.class,
                Lists.newArrayList(),
                Lists.newArrayList(
                        new Filter(PROD_TYPE, Comparator.EQ, Lists.newArrayList(ProductUtils.getLine(prodCategory))),
                        new Filter(CUSTOMER_FLAG, Comparator.REGEXP, ClockInCustomerFlagEnum.fromZhCn(customerFlag))));
        SingleEmployee chiefEngineer = getWeeklyDutyPerson(time, config);
        return employee2Callee(chiefEngineer);
    }

    public static List<ClockInCallee> getRemoteSupporters(Class<? extends BaseEntity> batchTaskType, String batchTaskId) {
        Class<? extends BaseSubEntity> operatorType = batchTaskType == BatchTask.class
                ? BatchTaskOperator.class : SubcontractorBatchOperator.class;
        List<BatchTaskOperator> operators = QueryDataHelper.query(
                operatorType,
                Lists.newArrayList(OPERATE_PERSON, OPERATOR_PHONE),
                batchTaskId,
                Lists.newArrayList(
                        new Filter(OPERATOR_ROLE, Comparator.EQ, Lists.newArrayList(OperatorRoleEnum.SUPPORT_PERSONNEL.getValue())),
                        new Filter(REMOTE_FLAG, Comparator.EQ, Lists.newArrayList(RemoteEnum.REMOTE))));
        return operators.stream()
                .map(o -> new ClockInCallee() {{
                    setUserId(o.getOperatePerson().getId());
                    setPhoneNum(StringUtils.getIfEmpty(
                            o.getOperatorPhone(),
                            () -> HrHelper.queryPhoneNum(o.getOperatePerson().getId())));
                }}).collect(Collectors.toList());
    }

    public static ClockInCallee employee2Callee(Employee employee) {
        if (employee == null) {
            return null;
        }

        return new ClockInCallee() {{
            setUserId(employee.getId());
            setPhoneNum(HrHelper.queryPhoneNum(employee.getId()));
        }};
    }

    public static List<ClockInCallee> employee2Callee(List<Employee> employee) {
        return employee.stream()
                .map(Employee::getId)
                .distinct()
                .map(CalleeQueryAbility::userId2Callee)
                .collect(Collectors.toList());
    }

    public static ClockInCallee userId2Callee(String userId) {
        return new ClockInCallee() {{
            setUserId(userId);
            setPhoneNum(HrHelper.queryPhoneNum(userId));
        }};
    }

    public static List<ClockInCallee> userId2Callee(List<String> userIds) {
        return userIds.stream()
                .distinct()
                .map(CalleeQueryAbility::userId2Callee)
                .collect(Collectors.toList());
    }

    private static SingleEmployee getWeeklyDutyPerson(ZonedTime time, BaseWeeklyDutyPerson config) {
        if (config == null) {
            return null;
        }

        // 22点后的呼叫，取后一天的值班人
        final int minHourAsNextDay = 22;
        if (time.get(ChronoField.HOUR_OF_DAY) >= minHourAsNextDay) {
            time.add(Calendar.DATE, 1);
        }

        int dayOfWeek = time.get(ChronoField.DAY_OF_WEEK);
        switch (dayOfWeek) {
            case Calendar.SUNDAY:
                return config.getSunday();
            case Calendar.MONDAY:
                return config.getMonday();
            case Calendar.TUESDAY:
                return config.getTuesday();
            case Calendar.WEDNESDAY:
                return config.getWednesday();
            case Calendar.THURSDAY:
                return config.getThursday();
            case Calendar.FRIDAY:
                return config.getFriday();
            case Calendar.SATURDAY:
                return config.getSaturday();
            default:
                return null;
        }
    }
}
