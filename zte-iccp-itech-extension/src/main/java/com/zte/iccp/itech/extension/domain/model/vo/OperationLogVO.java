package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 操作日志VO
 *
 * <AUTHOR> jiang<PERSON>awen
 * @date 2025/5/14
 */

@Setter
@Getter
public class OperationLogVO {

    @JsonProperty("textfield_cans6u5z")
    private String relationId;

    @JsonProperty("textfield_259j0frf")
    private String parentRelationId;

    @JsonProperty("textfield_b7tv1wmk")
    private String operationNamePrefix;

    @JsonProperty("textfield_9zu9uxo8")
    private String operationType;

    /**
     * 日志内容
     */
    @JsonProperty("textfield_7xbw4e61")
    private String operationDesc;

    /**
     * 操作人员
     */
    @JsonProperty("textfield_axppoogt")
    private String operator;

    /**
     * 操作时间
     */
    @JsonProperty("node_95415044")
    private String operationTime;

    /**
     * 操作节点
     */
    @JsonProperty("textfield_3aygg3x1")
    private String action;

}
