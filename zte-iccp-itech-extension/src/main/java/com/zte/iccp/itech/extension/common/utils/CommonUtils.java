package com.zte.iccp.itech.extension.common.utils;

import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.paas.lcap.common.classloader.CustomClassLoaderHelper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @date 2024/7/23 上午9:44
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class CommonUtils {

    private static final String NUMBER_PATTERN = "\\d+";

    public static String getUUID() {
        return String.valueOf(KeyGeneratorUtils.generateUUID());
    }

    public static String extractNumbers(String input) {
        if (null == input) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        Pattern pattern = Pattern.compile(NUMBER_PATTERN);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            sb.append(matcher.group());
        }

        return sb.toString();
    }

    /**
     * 取 / 连接字符串的最后一个
     * @param path
     * @return
     */
    public static String last(String path) {
        if (path == null) {
            return null;
        }

        String[] array = path.split(FORWARD_SLASH);
        return array[array.length - 1];
    }

    /**
     * 取 / 连接字符串的前n个
     * @param path
     * @return
     */
    public static String getFirstIndexChars(String path, int index) {
        if (path == null) {
            return null;
        }
        if (index < 1) {
            return null;
        }
        String[] array = path.split(FORWARD_SLASH);
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < array.length; i++) {
            if (i < index) {
                result.append(array[i]);
            }
            if (i < index - 1) {
                result.append(FORWARD_SLASH);
            }
        }
        return result.toString();
    }

    /**
     * 取 / 连接字符串的第 startIndex 个到第 endIndex 个
     * @param path
     * @return
     */
    public static String subIndex(String path, int startIndex, int endIndex) {
        if (path == null) {
            return null;
        }

        String[] array = path.split(FORWARD_SLASH);
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < array.length; i++) {
            if (startIndex <= i && i <= endIndex) {
                result.append(array[i]);
                // 最后一个不加/
                if (i != endIndex) {
                    result.append(FORWARD_SLASH);
                }
            }
        }
        return result.toString();
    }

    /**
     * 从字符串中提取员工ID
     */
    public static Set<String> extractEmployeeIds(String input) {
        if (!StringUtils.hasText(input)) {
            return Sets.newHashSet();
        }

        Set<String> ids = new HashSet<>();
        Pattern pattern = Pattern.compile(NUMBER_PATTERN);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            ids.add(matcher.group());
        }
        return ids;
    }

    /**
     * 从字符串中提取操作类型名称
     */
    public static Set<String> extractOperationTypes(String input) {
        if (!StringUtils.hasText(input)) {
            return Sets.newHashSet();
        }
        return Arrays.stream(input.split("、"))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }

    /**
     * 移除字符串末尾"/"
     */
    public static String removeTrailingSlashes(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("/+$", "");
    }

    /**
     * 以"/"分割字符串，最多4层
     */
    public static String[] parseLevels(String input) {
        String[] parts = input.split(FORWARD_SLASH, 4);
        String[] result = new String[4];
        for (int i = 0; i < 4; i++) {
            if (i < parts.length) {
                result[i] = parts[i].replaceAll(REGEX_TRAILING_SLASH, EMPTY_STRING);
            } else {
                result[i] = EMPTY_STRING;
            }
        }
        return result;
    }

    public static boolean startsWiths(String input, List<String> prefixes) {
        for (String prefix : prefixes) {
            if (input.startsWith(prefix)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 解析层级信息
     * 确保返回的数组长度为4
     *
     * @param levels 原始层级数组
     * @return 标准化后的层级数组
     */
    public static String[] safeParseLevels(String[] levels) {
        if (levels == null || levels.length < 4) {
            return new String[4];
        }
        return Arrays.copyOf(levels, 4);
    }

    public static String getTemplateString(String location) {
        ClassLoader classLoader = CustomClassLoaderHelper.getAppCustomClassLoader(ContextHelper.getTenantId(), ContextHelper.getAppId());
        if (classLoader == null) {
            return EMPTY_STRING;
        }

        try (InputStream input = classLoader.getResourceAsStream(location)) {
            return IOUtils.toString(input, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("Failed to load email template from location: " + location, e);
            return EMPTY_STRING;
        }
    }

    /**
     * 安全获取数组元素
     * @param array 目标数组
     * @param index 索引位置
     * @param defaultValue 默认值（当数组为null、索引越界时返回）
     * @return 数组元素或默认值
     */
    public static String getArrayElement(String[] array, int index, String defaultValue) {
        if (array != null && index >= 0 && index < array.length) {
            return array[index];
        }
        return defaultValue;
    }
}
