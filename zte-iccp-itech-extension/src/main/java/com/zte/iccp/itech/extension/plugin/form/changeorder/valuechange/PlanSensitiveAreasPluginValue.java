package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.FormModelProxyHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.BatchHiddenUtils;
import com.zte.iccp.itech.extension.domain.enums.AiProductTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.BuilderViewEnum;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.AREA_BEIJING_COUNT_CODE;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.AREA_SHANGHAI_COUNT_CODE;

/**
 * 触发时机：【产品分类、省/州】字段值变化时
 * 插件功能：1、仅当计划单的“产品分类”属于 承载网、固网及多媒体  这两个产品经营团队时才显示
 * 2、当 省/州 = 北京市、上海市 时，默认为“是”，其它情况默认“否”，允许用户修改
 *
 * <AUTHOR> 10335201
 * @date 2025-07-17 下午4:18
 **/
public class PlanSensitiveAreasPluginValue implements ValueChangeBaseFormPlugin {

    /**
     * loadDataEvent统一封装方法
     *
     * @param args 默认入参，args
     * @return: void
     * @author: heqian
     * @date: 2025/7/21 上午9:21
     */
    @Override
    public void loadData(LoadDataEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = formView.getDataModel();
        showSensitiveAreas(dataModel, formView, true);
    }


    /**
     * propertyChanged统一封装方法
     *
     * @param args 默认入参，args
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        IDataModel dataModel = args.getModel();
        showSensitiveAreas(dataModel, args.getFormView(), false);
    }

    public void showSensitiveAreas(IDataModel dataModel, IFormView view, boolean isLoadData) {
        String product = FormModelProxyHelper.getTextFirstValue(FIELD_PRODUCT_CID, dataModel);
        if (StringUtils.isEmpty(product)) {
            return;
        }
        //仅当计划单的“产品分类”属于 承载网、固网及多媒体  这两个产品经营团队时才显示
        List<String> prodcts = ConfigHelper.get(AiProductTypeEnum.BN.getProdIdPaths());
        prodcts.addAll(ConfigHelper.get(AiProductTypeEnum.FM.getProdIdPaths()));
        if (prodcts.stream().anyMatch(product::startsWith)) {
            BatchHiddenUtils.hiddenTool(view, BuilderViewEnum.NORMAL, Lists.newArrayList(SENSITIVE_AREAS));
        } else {
            BatchHiddenUtils.hiddenTool(view, BuilderViewEnum.HIDDEN, Lists.newArrayList(SENSITIVE_AREAS));
            dataModel.setValue(SENSITIVE_AREAS, null);
            return;
        }
        // 如果有值不再赋值
        if (isLoadData && StringUtils.isNotEmpty(TextValuePairHelper.getValue(dataModel.getValue(SENSITIVE_AREAS)))) {
            return;
        }

        // 操作对象列表-地市
        String province = FormModelProxyHelper.getValueLangFirstValue(FIELD_PROVINCE_CID, OPERATION_OBJECT_TABLE_PROPERTY_KEY, dataModel);
        //  北京上海 默认为敏感区域
        if (AREA_BEIJING_COUNT_CODE.equals(province) || AREA_SHANGHAI_COUNT_CODE.equals(province)) {
            dataModel.setValue(SENSITIVE_AREAS, BoolEnum.Y.getPropValue());
        } else {
            dataModel.setValue(SENSITIVE_AREAS, BoolEnum.N.getPropValue());
        }
    }
}
