package com.zte.iccp.itech.extension.openapi.model.networksecurity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 批次信息
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/2
 */
@Getter
@Setter
public class BatchInfoDTO {
    /** 批次id */
    private String batchId;

    /** 批次任务code */
    private String batchCode;

    /** 批次号 */
    private String batchNo;

    /** 批次名称 */
    private String batchOperationSubject;

    /** 计划操作开始时间 */
    private String changeOperationTimeStart;

    /** 计划操作结束时间 */
    private String changeOperationTimeEnd;

    /** 确认操作开始时间 */
    private String planOperationStartTime;

    /** 确认操作结束时间 */
    private String planOperationEndTime;

    /** 实际操作开始时间 */
    private String factOperationDateStart;

    /** 实际操作结束时间 */
    private String factOperationDateEnd;

    /** 操作对象列表 */
    private List<OperationObjectDTO> operationObjectInfoDTOS;

    /** 操作及支持人员列表 */
    private List<NetworkBatchOperatorDTO> batchOperatorDTOS;

    /** 操作阶段打卡信息 */
    private List<BatchOperationStageClockDTO> batchOperationStageClockList;

    /** 通告发布时间 */
    private String releaseNotifyTime;

    /** 操作通告确认人 */
    private String releaseNotifier;

    /** 操作结果反馈人 */
    private String feedbackOperator;

    /** 操作结果反馈时间 */
    private String feedbackOperationTime;

    /** 操作结果 */
    private String operationResult;

    /** 批次失败原因 */
    private String batchFailureReason;

    /** 批次状态 */
    private String batchStatus;

    /** 网元数量 */
    private String neCount;

    /** 批次最后更新人 */
    private String batchLastModifiedBy;

    /** 批次最后更新时间 */
    private String batchLastModifiedTime;

}
