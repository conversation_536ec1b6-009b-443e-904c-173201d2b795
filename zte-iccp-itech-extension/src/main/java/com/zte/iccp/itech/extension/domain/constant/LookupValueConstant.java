package com.zte.iccp.itech.extension.domain.constant;

public class LookupValueConstant {

    /** 快码类型 - 任务中心网络变更任务当前进展 */
    public static final String NETWORK_CHANGE_CURRENT_PROCESS_ENUM = "network_change_current_process_enum";

    /** 快码类型 - 待办中心内部网络变更任务当前进展 */
    public static final String CHANGE_ORDER_CURRENT_PROCESS_ENUM = "changeorder_current_process_enum";

    /** 快码类型——待办中心合作方网络变更任务当前进展 */
    public static final String PARTNER_CHANGE_ORDER_CURRENT_PROCESS_ENUM = "partner_change_order_current_process_enum";

    /**
     * 快码类型——操作类型
     */
    public static final String OPERATE_TYPE_GROUP_ENUM = "operate_type_group_enum";

    /**
     * 快码类型——操作类型分组
     */
    public static final String OPERATE_TYPE_ENUM = "operate_type_enum";

    /**
     * 快码类型——时区
     */
    public static final String TIME_ZONE_ENUM = "time_zone_enum";

    /**
     * 快码类型——操作原因
     */
    public static final String OPERATE_REASON_ENUM = "operate_reason_enum";

    /**
     * 快码属性——快码值
     */
    public static final String LOOKUP_CODE = "lookupCode";

    /**
     * 快码属性——快码值名称
     */
    public static final String MEANING = "meaning";

    /**
     * 快码类型——操作类型分组 配合保障
     */
    public static final String OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE ="COOPERATION_GUARANTEE";

    /**
     * 快码类型——任务当前状态枚举
     */
    public static final String ASSIGNMENT_STATUS_ENUM = "assignment_status_enum";

    /**
     * 快码类型  - 客户标识
     */
    public static final String CUSTOMER_IDENTIFICATION_ENUM = "permission_customer_identification";

    /**
     * 快码类型 技术管理任务 - 任务分类
     */
    public static final String TASK_CATEGORY_ENUM = "task_category_enum";

    /**
     * 快码类型——打卡选项
     */
    public static final String CLOCK_IN_OPTION_ENUM = "CLOCK_IN_OPTION";

    /**
     * 操作类型 -配合保障code
     */
    public static final String OPERATE_TYPE_COOPERATING_TO_PROVIDE_SUPPORT = "COOPERATING_TO_PROVIDE_SUPPORT";
}
