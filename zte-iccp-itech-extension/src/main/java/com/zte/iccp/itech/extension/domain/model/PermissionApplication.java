package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.PermissionApplicationConsts.*;

/**
 * <AUTHOR>
 * @date 2024/10/9 下午6:28
 */
@Getter
@Setter
@BaseEntity.Info("permission_application")
public class PermissionApplication extends BaseEntity {

    @JsonProperty(value = MODULE_NAME)
    private List<TextValuePair> moduleName;

    @JsonProperty(value = ROLE_NAME)
    private List<TextValuePair> roleName;

    @JsonProperty(value = PRODUCT)
    private List<TextValuePair> product;

    @JsonProperty(value = CROSS_PARTNER_PERMISSION)
    private Boolean crossPartnerPermission;

    @JsonProperty(value = EXPIRATION_TIME)
    private Date expirationTime;

    @JsonProperty(value = DIRECT_MANAGEMENT_LEADER)
    private List<TextValuePair> directManagementLeader;

    @JsonProperty(value = REPRESENTATIVE_OFFICE_GROUP_MANAGER)
    private List<TextValuePair> representativeOfficeGroupManager;

    @JsonProperty(value = PRODUCT_TECHNOLOGY_SECTION_CHIEF)
    private List<TextValuePair> productTechnologySectionChief;

    @JsonProperty(value = APPLICATION_REASON)
    private String applicationReason;

    @JsonProperty(value = STATUS)
    private String status;

    @JsonProperty(value = BILL_NO)
    private String billNo;

    @JsonProperty(value = BILL_NAME)
    private String billName;

    @JsonProperty(value = ORGANIZATION_HZS)
    private String organizationHzs;

    @JsonProperty(value = TYPE)
    private String type;

    @JsonProperty(value = FILE)
    private Object file;

    @JsonProperty(value = ORGANIZATIONS)
    private List<TextValuePair> organizations;

    @JsonProperty(value = APPLICANT)
    private List<Employee> applicant;

    @JsonProperty(value = APPLICATION_TIME)
    private Date applicationTime;

    @JsonProperty(value = ZHONG_ZHI_HL)
    private Boolean zhongZhiHighLease;

}
