package com.zte.iccp.itech.extension.domain.model;

import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 故障管理任务任务详情
 *
 * 用于页面初始化/加载
 */
@Setter
@Getter
public class FaultManagementDetails {

    /**
     * 故障管理任务单据信息
     */
    List<FaultManagementOrder> faultManagementOrders;

    /**
     * 故障管理任务中心详情
     */
    List<FaultManagementAssignment> faultManagementAssignments;
}
