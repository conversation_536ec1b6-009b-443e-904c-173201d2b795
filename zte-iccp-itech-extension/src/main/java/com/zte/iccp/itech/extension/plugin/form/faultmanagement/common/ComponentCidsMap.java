package com.zte.iccp.itech.extension.plugin.form.faultmanagement.common;

import com.google.common.collect.Sets;

import java.util.Set;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.COMPONENT_SATISFACTION_RESPONSIBLE_PERSON_CID;
import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.ComponentCids.*;

/**
 * 布局cids的map集合
 */
public class ComponentCidsMap {


    // 故障复盘第一部分 - Y
    public static final Set<String> COMPONENT_FAULT_REVIEW_PART1_Y_CIDS = Sets.newHashSet(
            COMPONENT_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_TASK_TABLE_CID
    );

    // 故障复盘第一部分 - N
    public static final Set<String> COMPONENT_FAULT_REVIEW_PART1_N_CIDS = Sets.newHashSet(
            COMPONENT_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_REASON_CID,
            COMPONENT_SATISFACTION_RESPONSIBLE_PERSON_CID
    );

    // 故障复盘第二部分 - Y（是否需要横推整改）
    public static final Set<String> COMPONENT_FAULT_REVIEW_PART2_Y_CIDS = Sets.newHashSet(
            COMPONENT_REVIEW_REPORT_CID, COMPONENT_FAULT_RECTIFICATION_CID,
            COMPONENT_FAULT_RECTIFICATION_PERSON_CID
    );

    // 故障复盘第二部分 - N（是否需要横推整改）
    public static final Set<String> COMPONENT_FAULT_REVIEW_PART2_N_CIDS = Sets.newHashSet(
            COMPONENT_REVIEW_REPORT_CID, COMPONENT_FAULT_RECTIFICATION_CID, COMPONENT_FAULT_RECTIFICATION_REASON_CID
    );

    // 故障整改横推
    public static final Set<String> COMPONENT_FAULT_RECTIFICATION_CIDS = Sets.newHashSet(
            COMPONENT_FAULT_RECTIFICATION_REASON_CID
    );

    // 客户满意度（在线反馈）
    public static final Set<String> COMPONENT_SATISFACTION_ONLINE_CIDS = Sets.newHashSet(
            COMPONENT_SATISFACTION_CONTENT_TYPE_CID,
            COMPONENT_SATISFACTION_CID,
            COMPONENT_SATISFACTION_CONTENT_CID,
            COMPONENT_INFORMED_PERSON_CID
    );

    // 客户满意度（邮件反馈）
    public static final Set<String> COMPONENT_SATISFACTION_EMAIL_CIDS = Sets.newHashSet(
            COMPONENT_SATISFACTION_CONTENT_TYPE_CID,
            COMPONENT_CUSTOMER_MAIN_TO_CID,
            COMPONENT_CUSTOMER_MAIN_RECEIVE_CID
    );
}
