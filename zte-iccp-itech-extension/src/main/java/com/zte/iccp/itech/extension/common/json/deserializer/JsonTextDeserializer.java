package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import lombok.SneakyThrows;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2024/09/30
 */
public class JsonTextDeserializer<T> extends JsonDeserializer<T> {
    @SneakyThrows
    @Override
    public T deserialize(JsonParser p, DeserializationContext ctxt) {
        // 获取当前解析的字段
        Field field = JsonUtils.findField(p.getCurrentValue().getClass(), p.getCurrentName());
        if (field == null) {
            return null;
        }

        String json = p.getCodec().readValue(p, String.class);
        return JsonUtils.parseObject(json, field.getType());
    }
}
