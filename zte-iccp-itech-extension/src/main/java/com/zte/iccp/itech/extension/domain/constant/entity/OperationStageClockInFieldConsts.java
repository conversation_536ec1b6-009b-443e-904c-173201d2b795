package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/1/9 下午2:56
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OperationStageClockInFieldConsts {

    /**
     * 操作阶段
     */
    public static final String OPERATION_PHASE = "operation_phase";

    /**
     * 操作时长实体
     */
    public static final String OPERATION_DURATION = "operation_duration";

    /**
     * 操作阶段
     */
    public static final String STAGE_START_TIME = "stage_start_time";

    /**
     * 操作阶段
     */
    public static final String STAGE_END_TIME = "stage_end_time";

    /**
     * 操作阶段
     */
    public static final String STAGE_CHECK_IN_PERSON = "stage_check_in_person";
}
