package com.zte.iccp.itech.extension.plugin.dev.operation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.zlic.util.StringUtils;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.platform.api.PluginManagementServiceI;
import com.zte.paas.lcap.platform.domain.attach.AttachMultipartFile;
import com.zte.paas.lcap.platform.dto.plugin.PluginManagementCmd;
import com.zte.paas.lcap.platform.dto.plugin.PluginPageSelectCmd;
import com.zte.paas.lcap.platform.dto.plugin.PluginPageSelectVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import sun.misc.IOUtils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DEFAULT_PAGE_SIZE;
import static javax.ws.rs.core.MediaType.APPLICATION_OCTET_STREAM;

/**
 * <AUTHOR>
 * @since 2025/06/12
 */
@Slf4j
public class PluginUpdateFilePlugin extends BaseOperationPlugin {

    private static final String PLUGIN_NAME_PREFIX = "zte-iccp-itech-extension";

    private static final String PLUGIN_NAME_FORMAT = PLUGIN_NAME_PREFIX + "-%s.jar";

    private static final String REPO_URL = ConfigHelper.getRaw("artifactory.url");

    private static final String BASIC = new String(
            Base64.getDecoder().decode("QmFzaWMgYVhRdGNIVmliR2xqTFdOcE9tbDBMWEIxWW14cFl5MWphU294TWpNPQ=="),
            StandardCharsets.UTF_8);

    private static final Pattern HREF_JAR = Pattern.compile("href=\"([^\"]+\\.jar)\"");

    private final PluginManagementServiceI pluginMgmtSvc
            = SpringContextUtil.getBean(PluginManagementServiceI.class);

    @Override
    public void afterExecute(ExecuteEvent event) {
        try {
            String jarName = getModel().getValue("artifactFileName").toString();
            update(jarName);
            showMessage(MsgType.SUCCESS, "SUCCESS");
            getView().getClientViewProxy().openWindow(
                    "/zte-iccp-itech-frontendcli/index.html#/platform/pluginManager/APP0984032412495249408",
                    "_self");
        } catch (Exception e) {
            log.error("PluginUpdateFilePlugin error", e);
            showMessage(MsgType.ERROR, e.getMessage());
        }
    }

    private void update(String jarName) {
        // 如果jarName为空，先获取目录下所有jar，选最新的
        if (StringUtils.isWhitespace(jarName)) {
            jarName = getLatestJar();
        }

        byte[] bytes = downloadArtifact(jarName);
        String newJarId = upload(jarName, bytes);

        queryJarIds().stream()
                .filter(id -> !id.equals(newJarId))
                .forEach(id -> pluginMgmtSvc.delete(ContextHelper.getAppId(), id));
    }

    @SneakyThrows
    private static byte[] downloadArtifact(String jarName) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String jarUrl = REPO_URL + jarName;
            HttpGet httpGet = new HttpGet(jarUrl);
            httpGet.setHeader("Authorization", BASIC);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                return IOUtils.readAllBytes(response.getEntity().getContent());
            }
        }
    }

    @SneakyThrows
    private static String getLatestJar() {
        String html = getRepositoryHtml();

        // 简单正则匹配所有jar文件名
        Matcher matcher = HREF_JAR.matcher(html);
        String latestJar = null;
        while (matcher.find()) {
            String found = matcher.group(1);
            if (latestJar == null || found.compareTo(latestJar) > 0) {
                latestJar = found;
            }
        }

        return latestJar;
    }

    @SneakyThrows
    private static String getRepositoryHtml() {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(REPO_URL);
            httpGet.setHeader("Authorization", BASIC);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                return new String(
                        IOUtils.readAllBytes(response.getEntity().getContent()),
                        StandardCharsets.UTF_8);
            }
        }
    }

    private List<String> queryJarIds() {
        Page<PluginPageSelectVO> plugins = pluginMgmtSvc.pageSelect(
                ContextHelper.getAppId(),
                new PluginPageSelectCmd() {{
                    setPageNo(1);
                    setPageSize(DEFAULT_PAGE_SIZE);
                    setPluginName(PLUGIN_NAME_PREFIX);
                }});
        return plugins.getRecords().stream()
                .map(PluginPageSelectVO::getId)
                .collect(Collectors.toList());
    }

    @SneakyThrows
    private String upload(String name, byte[] bytes) {
        return pluginMgmtSvc.save(
                ContextHelper.getAppId(),
                new PluginManagementCmd() {{
                    setFileMd5(DigestUtils.md5Hex(bytes));
                    setPluginName(String.format(PLUGIN_NAME_FORMAT, System.currentTimeMillis()));
                    setPluginDesc(name);
                    setPluginFile(new AttachMultipartFile(
                            name, name, APPLICATION_OCTET_STREAM, bytes));
                }});
    }
}
