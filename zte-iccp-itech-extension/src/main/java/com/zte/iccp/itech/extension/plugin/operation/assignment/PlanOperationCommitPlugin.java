package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.Operator;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.PlanOperationAssignment;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.extension.plugin.operation.assignment.helper.AssignmentHelper;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteTransactionalEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.COMMA;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.PLAN_REJECT_PENDING_SUBMISSION_NODE_KEY;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.MobileApprove.CHANGE_STARTTIME_MUST_LATER_THAN_CURRENTTIME;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.BATCH_INFO_NO;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.PRODUCT_MODEL;
import static com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin.BasePluginVariants.SUBMIT_TIME;

@Slf4j
public class PlanOperationCommitPlugin extends BaseOperationPlugin {

    /* 提交按钮key */
    private static final List<String> COMMIT_BUTTON_KEYS = Arrays.asList("submit_approval_newest", "resubmit");

    @Override
    public boolean beforeExecuteValidate(ExecuteEvent executeEvent) {
        // 只有提交和重新提交按钮 需要校验
        if (!COMMIT_BUTTON_KEYS.contains(executeEvent.getOperationKey())) {
            return true;
        }
        // 必填校验
        if (!beforeExecuteValidateExt(getModel(), getView())) {
            return false;
        }
        //时间校验
        if (!beforeCheckOperationTime()) {
            return false;
        }
        // 主操作对象校验
        if (!isExistMainObject()) {
            getView().showMessage(PRODUCT_MODEL_BELONG_PRODUCT_CATEGORY, MsgType.ERROR);
            return false;
        }

        return true;
    }

    /**
     * 1、如果存在产品型号等于空，默认第一个为主对象；
     * 2、如果产品信号都不为空，需要有一个产品型号，属于产品类型中
     */
    private boolean isExistMainObject() {
        String product = PropertyValueConvertUtil.getString(getModel().getValue(ChangeOrderFieldConsts.PRODUCT_CATEGORY));
        List<String> proModels = FormModelProxyHelper.getSubFormCloumValueList(
                getModel(), OPERATION_OBJECT_TABLE_PROPERTY_KEY, PRODUCT_MODEL);

        return !StringUtils.hasText(product)
                || CollectionUtils.isEmpty(proModels)
                || proModels.stream().anyMatch(itech -> !StringUtils.hasText(itech) || itech.startsWith(product));
    }

    /**
     * 计划开始时间和结束时间取 批次最大时间和最小时间，区间不能大于7天
     *
     * @return boolean
     */
    private boolean beforeCheckOperationTime() {
        // 操作对象必须有一行校验
        List<Date> startTimes = DateUtils.dateListConverter(getModel().getEntryColumnObject(FIELD_BATCH_SUMMARY_CID,
                FIELD_PLAN_OPERATION_START_TIME_CID));
        List<Date> endTimes = DateUtils.dateListConverter(getModel().getEntryColumnObject(FIELD_BATCH_SUMMARY_CID,
                FIELD_PLAN_OPERATION_END_TIME_CID));
        Date startDate = startTimes.stream().min(Date::compareTo).orElse(null);
        Date endDate = endTimes.stream().max(Date::compareTo).orElse(null);
        if (startDate == null || endDate == null) {
            getView().showMessage(OPERATION_START_AND_END_TIME_CANNOT_NULL, MsgType.ERROR);
            return false;
        }
        // 开始时间小于当前时间
        if (startDate.getTime() < System.currentTimeMillis()) {
            getView().showMessage(CHANGE_STARTTIME_MUST_LATER_THAN_CURRENTTIME, MsgType.ERROR);
            return false;
        }
        long timeDifference = Math.abs(endDate.getTime() - startDate.getTime());

        if (timeDifference > TimeUnit.DAYS.toMillis(7)) {
            getView().showMessage(OPERATION_START_AND_END_TIME_MAX_7_DAY, MsgType.ERROR);
            return false;
        }
        getModel().setValue(OPERATION_START_TIME, startDate);
        getModel().setValue(OPERATION_END_TIME, endDate);
        return true;
    }

    /**
     * 1.检查操作对象必须有一行校验
     *
     * @param model model
     * @param view  view
     * @return boolean
     */
    private boolean beforeExecuteValidateExt(IDataModel model, IFormView view) {
        // 操作对象必须有一行校验
        IDataEntityCollection dataEntityCollection = model.getEntryRowEntities(OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        if (dataEntityCollection.isEmpty()) {
            view.showMessage(MessageConsts.OPERATION_OBJECT_NOT_NULL_ERROR, MsgType.ERROR);
            return false;
        }
        // 在这个基础上增加一个校验，那就是每一个批次概要的(操作批次)必须对应操作对象一行
        // 获取批次概要的操作批次集
        IDataEntityCollection batchSummaryEntryRowEntities = model.getEntryRowEntities(FIELD_BATCH_SUMMARY_CID);
        if (batchSummaryEntryRowEntities.isEmpty()) {
            getView().showMessage(MessageConsts.CHECK_BATCH_SUMMARY_EMPTY_ERROR, MsgType.ERROR);
            return false;
        }

        List<String> batchNoList = ChangeOrderAbility.getEntryAllColumnData(
                batchSummaryEntryRowEntities, BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY);

        // 批次概要的操作批次是唯一的，不能重复：增加校验（批次概要不能存在相同的操作批次）
        if (batchNoList.size() != batchNoList.stream().distinct().count()) {
            view.showMessage(MessageConsts.CHECK_BATCH_SUMMARY_REPEAT_ERROR, MsgType.ERROR);
            return false;
        }
        // 获取操作对象的操作批次集
        List<String> batchNos = ChangeOrderAbility.getEntryAllColumnData(
                model.getEntryRowEntities(OPERATION_OBJECT_TABLE_PROPERTY_KEY), BATCH_INFO_NO);
        // 如果批次概要的【操作批次】所有元素有一个不在操作对象【操作批次】中，则给一个提示“每一个批次概要操作批次必须对应一条操作对象操作批次”
        batchNoList.removeAll(batchNos);
        if (!CollectionUtils.isEmpty(batchNoList)) {
            view.showMessage(MsgUtils.getMessage(
                    MessageConsts.OPERATION_OBJECT_CHECK_BATCH_SUMMARY_ERROR, String.join(COMMA, batchNoList)), MsgType.ERROR);
            return false;
        }
        return true;
    }

    @Override
    public void afterExecuteTransactional(ExecuteTransactionalEvent executeTransactionalEvent) {
        // 1.获取提单主键, 检索已生成主任务数据
        IDataModel dataModel = getModel();
        String billId = getPkId();
        PlanOperationAssignment existAssignment =
                AssignmentAbility.querySpecificTypeAssignment(billId, AssignmentTypeEnum.OPERATION_PLAN_TASK, PlanOperationAssignment.class);

        // 2.生成主任务
        String buttonId = executeTransactionalEvent.getCid();
        PlanOperationAssignment newAssignment = createOrUpdateMainAssignment(dataModel, billId, buttonId, existAssignment);

        //如果是重新提交 手动维护流程变量 推流程
        if ("resubmit".equals(executeTransactionalEvent.getOperationKey())) {
            Map<String, Object> values = Maps.newHashMap();
            values.put(TIME_CONFLICT, LookupValueHelper.getModelLookupCode(dataModel.getValue(TIME_CONFLICT)));
            values.put(OPERATION_LEVEL, TextValuePairHelper.getValue(dataModel.getValue(OPERATION_LEVEL)));
            values.put(IS_UPGRADE_TECHNOLOGY, TextValuePairHelper.getValue(dataModel.getValue(IS_UPGRADE_TECHNOLOGY)));
            values.put(FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID, TextValuePairHelper.getValue(dataModel.getValue(FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID)));
            values.put(SUBMIT_TIME.getKey(), String.valueOf(System.currentTimeMillis()));
            FlowHelper.changeFlowParams(getPkId(), values, ApproveFlowCodeEnum.OPERATION_PLAN_FLOW);
            FlowHelper.pushSystemNode(getPkId(), PLAN_REJECT_PENDING_SUBMISSION_NODE_KEY);
        }

        if (COMMIT_BUTTON_KEYS.contains(executeTransactionalEvent.getOperationKey())) {
            // 3.对应按钮特殊操作 提交按钮发邮件
            submitAssignmentOperation(dataModel, newAssignment, billId);
            // 2000L为提示停留时候，非等待时间
            getView().showMessage(MessageConsts.MESSAGE_SUBMIT_SUCCESS_REFRESH, MsgType.SUCCESS, 2000L, true);
        }
    }

    /**
     * 创建 / 更新主任务
     */
    private PlanOperationAssignment createOrUpdateMainAssignment(IDataModel dataModel,
                                                                 String billId,
                                                                 String buttonId,
                                                                 PlanOperationAssignment existAssignment) {
        PlanOperationAssignment mainAssignment = new PlanOperationAssignment();

        // 1.获取用户信息
        String userId = ContextHelper.getEmpNo();
        List<Employee> userInfo = HrClient.queryEmployeeInfo(Lists.newArrayList(userId));

        // 2.获取系统自动生成数据
        // (1) 任务名称(操作主题) + 任务编码(单据编号)
        mainAssignment.setAssignmentName(
                PropertyValueConvertUtil.getString(dataModel.getValue(ChangeOrderFieldConsts.OPERATION_SUBJECT)));
        mainAssignment.setAssignmentCode(
                PropertyValueConvertUtil.getString(dataModel.getValue("pl_no")));

        // (2) 任务状态 + 当前进展是否审批任务
        switch (buttonId) {
            case BUTTON_SAVE_DRAFT_CID:
                mainAssignment.setAssignmentStatus(AssignmentStatusEnum.START.getValue());
                mainAssignment.setCurrentProgress(ApproveNodeEnum.DRAFT.name());
                mainAssignment.setApprovalTaskFlag(BoolEnum.N);
                break;

            case BUTTON_SAVE_APPROVE_DRAFT_CID:
                mainAssignment.setAssignmentStatus(AssignmentStatusEnum.APPROVE_START.getValue());
                mainAssignment.setCurrentProgress(ApproveNodeEnum.DRAFT.name());
                mainAssignment.setApprovalTaskFlag(BoolEnum.N);
                break;

            default:
                mainAssignment.setAssignmentStatus(AssignmentStatusEnum.APPROVE.getValue());
                mainAssignment.setApprovalTaskFlag(BoolEnum.Y);
                break;
        }

        // (3) 当前处理人
        mainAssignment.setCurrentProcessorEmployee(userInfo);

        // (4) 任务类型 + 单据类型
        mainAssignment.setAssignmentType(AssignmentTypeEnum.OPERATION_PLAN_TASK.getPropValue());
        mainAssignment.setBillType(BillTypeEnum.PLAN_OPERATION.getPropValue());

        SaveNetworkMainTaskPlugin saveNetworkMainTaskPlugin = new SaveNetworkMainTaskPlugin();
        // 3.获取用户填报数据
        // (1) 责任单位
        // 网络变更操作单会选择到第四层组织架构，返回全路径
        // 营销获取第二层组织id, 代表处获取第四层组织id
        Pair<List<TextValuePair>, List<TextValuePair>> organizationInfo = saveNetworkMainTaskPlugin.getOrganizationInfo(dataModel);
        mainAssignment.setMarketing(organizationInfo.getLeft());
        mainAssignment.setRepresentativeOffice(organizationInfo.getRight());

        // (2) 产品分类
        // 网络变更操作单会选择到第四层产品分类，返回全路径
        // 产品经营团队选择第一层产品id，产品分类按全路径存储
        Pair<List<TextValuePair>, List<TextValuePair>> productInfo = saveNetworkMainTaskPlugin.getProductInfo(dataModel);
        mainAssignment.setProductManagementTeam(productInfo.getLeft());
        mainAssignment.setProductClassification(productInfo.getRight());

        // (3) 客户标识
        String accnType = FormModelProxyHelper.getValueLangFirstValue(ACCN_TYPE_CID, dataModel);
        if (StringUtils.hasText(accnType)) {
            mainAssignment.setCustomerClassification(TextValuePairHelper.buildList(accnType, accnType, accnType));
        }

        // (4) 操作类型
        mainAssignment.setOperationType(AssignmentHelper.getLookupValueDropDownResult(
                dataModel, ChangeOrderFieldConsts.OPERATION_TYPE, LookupValueConstant.OPERATE_TYPE_ENUM));

        // (5) 操作对象相关属性
        // 网络信息 / 局点名称
        mainAssignment.setNetwork(saveNetworkMainTaskPlugin.getNetworkInfo(dataModel));
        mainAssignment.setOfficeName(saveNetworkMainTaskPlugin.getOfficeNameInfo(dataModel));

        // (6) 责任人 + 公司
        mainAssignment.setResponsibleEmployee(Objects.isNull(existAssignment)
                ? userInfo
                : existAssignment.getResponsibleEmployee());
        mainAssignment.setCompany(AssignmentAbility.getAssignmentCompany(Lists.newArrayList(userId)));

        // (7) 计划开始时间
        mainAssignment.setPlanStartTime(
                ComponentUtils.getDateComponentInfo(dataModel, ChangeOrderFieldConsts.OPERATION_START_TIME));

        // (8) 国家/地区
        List<Object> countrys = getModel().getEntryColumnObject(FIELD_COMPONENT_PARTNER_OPERATION_OBJECT_CID, COUNTRY);
        if (!CollectionUtils.isEmpty(countrys)) {
            mainAssignment.setCountry(JsonUtils.parseArray(countrys.get(0), MultiLangText.class));
        }

        // (9) 操作原因
        mainAssignment.setOperationReason(
                AssignmentHelper.getLookupValueDropDownResult(
                        dataModel, ChangeOrderFieldConsts.OPERATION_REASON, LookupValueConstant.OPERATE_REASON_ENUM));

        // (10) 重要程度
        mainAssignment.setImportance(
                ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.IMPORTANCE));

        // (11) 风险评估
        mainAssignment.setRiskEvaluation(
                ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.RISK_EVALUATION));

        // (12) 操作等级
        mainAssignment.setOperationLevel(
                ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.OPERATION_LEVEL));

        // (13) 时区
        Object timeZoneObj = dataModel.getValue(TIME_ZONE_KEY);
        String timeZone = null;
        if (!ObjectUtils.isEmpty(timeZoneObj)) {
            Map<String, String> timeZoneMap = JsonUtils.parseObject(timeZoneObj, Map.class);
            timeZone = timeZoneMap.get(LookupValueConstant.LOOKUP_CODE);
            mainAssignment.setTimeZone(timeZone);
        }

        // (14) 操作开始时间（UTC+8）
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneEnum(timeZone);
        Date originTime = mainAssignment.getPlanStartTime();
        // 如果不是北京时间，需要根据时区转换成北京时间;否则，直接取用户输入的计划操作开始时间即可
        if (timeZoneEnum != null && TimeZoneEnum.BEIJING != timeZoneEnum) {
            mainAssignment.setOperationStartTimeUtc8(TimeZoneEnum.BEIJING.pollute(timeZoneEnum.fix(originTime)));
        } else {
            mainAssignment.setOperationStartTimeUtc8(originTime);
        }

        // 冲突
        mainAssignment.setConflict(LookupValueHelper.getModelLookupCode(dataModel.getValue(TIME_CONFLICT)));

        // 4.生成主任务
        if (Objects.isNull(existAssignment)) {
            // (1) 任务不存在，新增网络任务数据
            mainAssignment.setBillId(billId);
            mainAssignment.setEntityId(billId);
            String id = AssignmentAbility.insert(mainAssignment);
            mainAssignment.setId(id);
        } else {
            // (2) 任务存在，更新网络任务数据
            mainAssignment.setId(existAssignment.getId());
            AssignmentAbility.update(mainAssignment);
        }

        return mainAssignment;
    }

    /**
     * 额外操作 - 提交审批
     *
     * @param assignment
     * @param billId
     */
    private void submitAssignmentOperation(IDataModel dataModel, PlanOperationAssignment assignment, String billId) {
        // 1.相关人授权
        // 授权范围：任务责任人 + 邮件知会人 + 操作人员
        // 审批人员在对应审批节点授权
        // (1) 任务责任人
        List<String> responsible = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());

        // (2) 邮件知会人
        List<Employee> emailNotifier = ComponentUtils.getEmployeeComponentInfo(dataModel, ChangeOrderFieldConsts.MAIL_COPY);
        List<String> emailNotifierId = emailNotifier.stream().map(Employee::getEmpUIID).collect(Collectors.toList());

        // (3) 操作人员
        List<Operator> operator = ChangeOrderAbility.listSpecificChangeOrderSubEntity(billId, Operator.class);
        List<String> operatorId = operator.stream().map(Operator::getOperatorEpmNo).collect(Collectors.toList());

        Set<String> relevanceSet = new HashSet<>(responsible);
        relevanceSet.addAll(emailNotifierId);
        relevanceSet.addAll(operatorId);
        AssignmentAbility.createAssignmentPersonRelevance(assignment.getId(), new ArrayList<>(relevanceSet));
    }

    @Override
    public final void beforeExecute(ExecuteEvent event) {
        // 只有提交和重新提交按钮 初始化流程变量
        if (!COMMIT_BUTTON_KEYS.contains(event.getOperationKey())) {
            return;
        }
        getModel().setValue(CidConstants.SUBMIT_TIME, new Date());
    }

}
