package com.zte.iccp.itech.extension.openapi.model.reportpush;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/16 下午3:17
 */
@Setter
@Getter
public class NetChangeReportVO {

    /**
     * 产品经营团队
     */
    private String prodTeam;

    /**
     * 运营商总单数
     */
    private int operatorOrderCount;

    /**
     * 中国移动单数
     */
    private int zgyd;

    /**
     * 中国电信单数
     */
    private int zgdx;

    /**
     * 中国联通单数
     */
    private int zglt;

    /**
     * 中国广电单数
     */
    private int zggd;

    /**
     * 其它运营商单数
     */
    private int other;

    /**
     * 操作等级总单数
     */
    private int opLevelOrderCount;

    /**
     * 普通单数
     */
    private int normal;

    /**
     * 重要单数
     */
    private int important;

    /**
     * 关键单数
     */
    private int critical;

    /**
     * 星级总单数
     */
    private int xjOrderCount;

    /**
     * 六星单数
     */
    private int sixStar;

    /**
     * 五星单数
     */
    private int fiveStar;

    /**
     * 四星单数
     */
    private int fourStar;

    /**
     * 三星单数
     */
    private int threeStar;

    /**
     * 二星单数
     */
    private int twoStar;

    /**
     * 一星单数
     */
    private int oneStar;

    /**
     * 合作方或者内部单据
     */
    private String type;

    /**
     * 经营团队Id
     */
    private String prodTeamId;


}
