package com.zte.iccp.itech.extension.openapi.model.assignment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2024/8/27 下午1:46
 */
@ApiModel("修改时间冲突")
@Getter
@Setter
public class TimeConflictDTO extends ConflictDTO {

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationStartTime;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationEndTime;

    /* 是否确认时间 */
    private boolean confirmTime;
}
