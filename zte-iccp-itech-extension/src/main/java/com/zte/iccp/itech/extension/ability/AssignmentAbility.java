package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.BusinessConsts;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.*;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.LinkTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInReviewsNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.permissionapplication.PermissionApplicationStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.TaskCategoryEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.entity.*;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.client.UcsClient;
import com.zte.iccp.itech.extension.spi.enums.gtdcenter.ConstraintTypeEnum;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicEmployeeInfo;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.iccp.itech.extension.spi.model.ucs.vo.UcsUserInfo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.ProcessRecordVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomNodeVo;
import com.zte.paas.lcap.common.api.metadata.engine.manage.model.ObjectInstanceLinkDO;
import com.zte.paas.lcap.core.entity.MainEntityType;
import com.zte.paas.lcap.core.orm.QueryBuilder;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.core.orm.query.SubQueryFilter;
import com.zte.paas.lcap.ddm.domain.helper.orm.BaseQueryDataHelper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.DEFAULT_APPROVER_KEY;
import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.LookupValueConstant.ASSIGNMENT_STATUS_ENUM;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CURRENT_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.TIME_ZONE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.PersonRelevanceFieldConsts.ASSIGNMENT_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.PersonRelevanceFieldConsts.RELEVANT;

public class AssignmentAbility {

    // ============== 检索常量 ===============
    /** 查询线程数 */
    private static final String THREAD_TOTAL = "assignment.threadTotal";


    public static final String SUB_QUERY_SQL = "select l.bill_id_in from lcap_schema_object_link_instance l where l.link_type_id = ";

    // ========================= 公共数据库方法 =========================

    /**
     * 基于entityIds和任务类型，批量查询任务中心
     * @param entityIds 任务Id
     * @param assignmentType 任务类型
     * @return 任务中心
     */
    public static List<Assignment> queryAssignment(List<String> entityIds, AssignmentTypeEnum assignmentType) {
        if (CollectionUtils.isEmpty(entityIds) || null == assignmentType) {
            return Lists.newArrayList();
        }
        List<String> fields = Lists.newArrayList();

        Filter entityIdsFilter = new Filter(ENTITY_ID, Comparator.IN, entityIds);
        Filter assignmentTypeFilter = new Filter(ASSIGNMENT_TYPE, Comparator.REGEXP, assignmentType.getValue());
        List<IFilter> conditionFilters = Lists.newArrayList(entityIdsFilter, assignmentTypeFilter);
        return QueryDataHelper.query(Assignment.class, fields, conditionFilters);
    }

    /**
     * 检索特定类型任务 - 主键
     */
    public static <T extends Assignment> T querySpecificTypeAssignment(String id, Class<T> assignmentClass) {
        if (!StringUtils.hasText(id)) {
            return null;
        }

        return QueryDataHelper.get(assignmentClass, Lists.newArrayList(), id);
    }

    /**
     * 检索特定类型任务 - 主键
     */
    public static <T extends Assignment> T querySpecificTypeAssignment(
            String id,
            List<String> fields,
            Class<T> assignmentClass) {

        if (!StringUtils.hasText(id)) {
            return null;
        }

        return QueryDataHelper.get(assignmentClass, fields, id);
    }

    /**
     * 检索特定类型任务 - 主键
     */
    public static <T extends Assignment> List<T> querySpecificTypeAssignment(List<String> idList, Class<T> assignmentClass) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        return QueryDataHelper.get(assignmentClass, Lists.newArrayList(), idList);
    }

    /**
     * 检索特定类型任务 - 主键
     * todo querySpecificTypeAssignment(List<String> idList, Class<T> assignmentClass) 建议逐渐替换成该方法
     * 指定字段进行查询可减少平台联表，提高性能速率
     */
    public static <T extends Assignment> List<T> get(
            List<String> ids,
            List<String> fields,
            Class<T> assignmentClass) {

        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        return QueryDataHelper.get(assignmentClass, fields, ids);
    }

    /**
     * 检索特定类型任务 - 单据ID + 任务类型 - 主任务
     * @param billId
     * @param assignmentType
     * @param assignmentClass
     * @return T
     * @param <T>
     */
    public static <T extends Assignment> T querySpecificTypeAssignment(String billId,
                                                                       AssignmentTypeEnum assignmentType,
                                                                       Class<T> assignmentClass) {
        if (!StringUtils.hasText(billId)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList();

        Filter billIdFilter = new Filter(BILL_ID, Comparator.EQ, billId);
        Filter assignmentTypeFilter = new Filter(
                ASSIGNMENT_TYPE, Comparator.IN, Lists.newArrayList(assignmentType.getValue()));
        List<IFilter> conditionFilterList = Lists.newArrayList(billIdFilter, assignmentTypeFilter);

        return QueryDataHelper.queryOne(assignmentClass, fieldList, conditionFilterList);
    }

    /**
     * 检索特定类型任务 - 单据ID + 任务类型 - 主任务
     *
     * @param billIds
     * @param assignmentType
     * @param assignmentClass
     * @return T
     * @param <T>
     */
    public static <T extends Assignment> List<T> querySpecificTypeAssignment(
            List<String> billIds,
            AssignmentTypeEnum assignmentType,
            Class<T> assignmentClass) {

        if (CollectionUtils.isEmpty(billIds)) {
            return Lists.newArrayList();
        }

        List<String> fieldList = Lists.newArrayList();

        Filter billIdFilter = new Filter(BILL_ID, Comparator.IN, billIds);
        Filter assignmentTypeFilter = new Filter(ASSIGNMENT_TYPE, Comparator.REGEXP, assignmentType.getValue());
        List<IFilter> conditionFilterList = Lists.newArrayList(billIdFilter, assignmentTypeFilter);

        return QueryDataHelper.query(assignmentClass, fieldList, conditionFilterList);
    }

    /**
     * 检索特定类型任务 - 任务编码
     * @param assignmentCode
     * @param assignmentClass
     * @return T
     * @param <T>
     */
    public static <T extends Assignment> T querySpecificTypeAssignmentByCode(String assignmentCode,
                                                                             Class<T> assignmentClass) {
        if (!StringUtils.hasText(assignmentCode)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList();

        Filter assignmentCodeFilter = new Filter(ASSIGNMENT_CODE, Comparator.EQ, assignmentCode);
        List<IFilter> conditionFilterList = Lists.newArrayList(assignmentCodeFilter);

        return QueryDataHelper.queryOne(assignmentClass, fieldList, conditionFilterList);
    }

    /**
     * 检索特定类型任务 - 任务编码
     */
    public static <T extends Assignment> T queryByAssignmentCode(
            String assignmentCode,
            List<String> fields,
            Class<T> assignmentClass) {

        if (!StringUtils.hasText(assignmentCode)) {
            return null;
        }

        return QueryDataHelper.queryOne(
                assignmentClass,
                fields,
                Lists.newArrayList(new Filter(ASSIGNMENT_CODE, Comparator.EQ, assignmentCode)));
    }

    /**
     * 检索特定类型任务 - EntityId
     */
    public static <T extends Assignment> T queryAssignment(
            String entityId,
            List<String> fields,
            Class<T> assignmentClass) {

        if (!StringUtils.hasText(entityId)) {
            return null;
        }

        Filter entityIdFilter = new Filter(ENTITY_ID, Comparator.EQ, entityId);
        return QueryDataHelper.queryOne(assignmentClass, fields, Lists.newArrayList(entityIdFilter));
    }

    public static boolean updateStatusByBillId(String billId, AssignmentStatusEnum status) {
        Assignment assignment = QueryDataHelper.queryOne(
                Assignment.class,
                Lists.newArrayList(ID),
                Lists.newArrayList(new Filter(BILL_ID, Comparator.EQ, billId)));
        if (assignment == null) {
            return false;
        }

        assignment.setAssignmentStatus(status.getValue());
        return SaveDataHelper.update(assignment);
    }


    /**
     * 更新技术管理任务
     *
     * @param pkId 主/子任务主键id
     * @param assignmentStatusEnum 待更新任务状态
     * @param assignmentTypeEnum 任务类型
     */
    public static void updateTechMgmtTaskStatus(String pkId,
                                                AssignmentStatusEnum assignmentStatusEnum,
                                                AssignmentTypeEnum assignmentTypeEnum) {
        TechnologyManagementAssignment assignment = null;
        switch (assignmentTypeEnum) {
            case TECHNOLOGY_MANAGEMENT:
                assignment = querySpecificTypeAssignment(
                        pkId, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT, TechnologyManagementAssignment.class);
                break;
            case TECHNOLOGY_MANAGEMENT_SUB:
                List<TechnologyManagementAssignment> assignmentList
                        = AssignmentAbility.queryExistSubAssignment(Lists.newArrayList(pkId));
                if (!CollectionUtils.isEmpty(assignmentList)) {
                    assignment = assignmentList.get(0);
                }
                break;
            default:
                break;
        }

        if (assignment == null) {
            return;
        }

        assignment.clearEntityValue();
        assignment.setAssignmentStatus(assignmentStatusEnum.getValue());

        if (AssignmentStatusEnum.ABOLISH.equals(assignmentStatusEnum)
                || AssignmentStatusEnum.CLOSE.equals(assignmentStatusEnum)) {
            assignment.setCurrentProcessorEmployee(Lists.newArrayList());
        } else if (AssignmentStatusEnum.APPROVE.equals(assignmentStatusEnum)) {
            // 获取主任务验收人
            TechnologyManagementOrder manageTask = ManageTaskAbility.queryById(assignment.getBillId());
            assignment.setCurrentProcessorEmployee(
                    EmployeeHelper.uniqueEmployees(manageTask.getAcceptorPerson()));
        } else if (AssignmentStatusEnum.EXECUTE == assignmentStatusEnum) {
            assignment.setCurrentProcessorEmployee(
                    EmployeeHelper.uniqueEmployees(assignment.getResponsibleEmployee()));
        }


        update(assignment);

        // 更新我已处理关系
        AssignmentAbility.updateHandledByMeRelevance(assignment.getId(), ContextHelper.getEmpNo(), BoolEnum.N);
    }

    /**
     * 新增任务
     * @param specificAssignment
     * @return String
     */
    public static <T extends Assignment> String insert(T specificAssignment) {
        if (Objects.isNull(specificAssignment)) {
            return EMPTY_STRING;
        }

        return SaveDataHelper.create(specificAssignment);
    }

    /**
     * 新增任务
     * @param specificAssignmentList
     * @return List<String>
     */
    public static <T extends Assignment> List<String> batchInsert(List<T> specificAssignmentList) {
        if (CollectionUtils.isEmpty(specificAssignmentList)) {
            return Lists.newArrayList();
        }

        return SaveDataHelper.batchCreate(specificAssignmentList);
    }

    /**
     * 更新任务
     * @param specificAssignment
     * @param <T>
     */
    public static <T extends Assignment> void update(T specificAssignment) {
        if (Objects.isNull(specificAssignment)) {
            return;
        }

        SaveDataHelper.update(specificAssignment);
    }

    /**
     * 更新任务
     * @param specificAssignmentList
     * @param <T>
     */
    public static <T extends Assignment> void batchUpdate(List<T> specificAssignmentList) {
        if (CollectionUtils.isEmpty(specificAssignmentList)) {
            return;
        }

        SaveDataHelper.batchUpdate(specificAssignmentList);
    }

    /**
     * 删除任务
     * @param id
     */
    public static void delete(String id) {
        if (!StringUtils.hasText(id)) {
            return;
        }

        SaveDataHelper.batchDelete(Assignment.class, Lists.newArrayList(id));
    }

    public static void batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        SaveDataHelper.batchDelete(Assignment.class, ids);
    }

    /**
     * 校验任务是否有子任务
     */
    public static Map<String, Integer> checkHasChildren(List<String> assignmentIds) {
        List<ObjectInstanceLinkDO> instances
                = ObjectLinkInstanceAbility.queryLinkInstance(LinkTypeEnum.PARENT, assignmentIds);
        List<String> existInstanceIds = instances.stream()
                .map(ObjectInstanceLinkDO::getBillIdIn)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Integer> hasChildren = new HashMap<>();
        for (String assignmentId : assignmentIds) {
            hasChildren.put(assignmentId, existInstanceIds.contains(assignmentId) ? 1 : 0);
        }

        return hasChildren;
    }


    // ====================== 网络变更任务数据库方法 ======================
    /**
     * 检索批次任务
     * @param batchTaskId
     * @return NetworkChangeAssignment
     */
    public static NetworkChangeAssignment queryBatchAssignment(String batchTaskId) {
        if (!StringUtils.hasText(batchTaskId)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList();

        Filter batchTaskIdFilter = new Filter(NetworkChangeFieldConsts.APPROVE_BATCH_TASK_ID, Comparator.EQ, batchTaskId);
        List<IFilter> conditionFilterList = Lists.newArrayList(batchTaskIdFilter);

        return QueryDataHelper.queryOne(NetworkChangeAssignment.class, fieldList, conditionFilterList);
    }

    /**
     * 检索批次任务
     */
    public static List<NetworkChangeAssignment> queryBatchAssignment(String billId, AssignmentTypeEnum assignmentType) {
        if (!StringUtils.hasText(billId) || Objects.isNull(assignmentType)) {
            return null;
        }

        List<String> fields = Lists.newArrayList();

        Filter billIdFilter = new Filter(BILL_ID, Comparator.EQ, billId);
        Filter assignmentTypeFilter
                = new Filter(ASSIGNMENT_TYPE, Comparator.EQ, Lists.newArrayList(assignmentType.getValue()));
        List<IFilter> conditionFilterList = Lists.newArrayList(billIdFilter, assignmentTypeFilter);

        return QueryDataHelper.query(NetworkChangeAssignment.class, fields, conditionFilterList);
    }

    /**
     * 检索已存在子任务
     * @param orderNo 多产品联动保障任务主单单号
     * @return List<NetworkChangeAssignment>
     */
    public static List<NetworkChangeAssignment> queryByOrderNo(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            return new ArrayList<>();
        }

        Filter noFilter = new Filter(ASSIGNMENT_CODE, Comparator.LIKE, orderNo + HYPHEN + MULTI_PROD_SUFFIX + PARAM_LIKE_SYMBOL);
        Filter typeFilter = new Filter(ASSIGNMENT_TYPE, Comparator.REGEXP, AssignmentTypeEnum.NETWORK_CHANGE.getValue());

        return QueryDataHelper.query(NetworkChangeAssignment.class,
                Lists.newArrayList(), Lists.newArrayList(noFilter, typeFilter));
    }



    // ====================== 技术管理任务数据库方法 ======================
    /**
     * 检索已存在子任务
     * @param subTaskIdList
     * @return List<TechnologyManagementAssignment>
     */
    public static List<TechnologyManagementAssignment> queryExistSubAssignment(List<String> subTaskIdList) {
        if (CollectionUtils.isEmpty(subTaskIdList)) {
            return Lists.newArrayList();
        }

        List<String> fieldList = Lists.newArrayList();

        Filter approveTaskIdFilter = new Filter(TechnologyManagementFieldConsts.APPROVE_SUB_TASK_ID, Comparator.IN, subTaskIdList);
        Filter assignmentTypeFilter = new Filter(ASSIGNMENT_TYPE, Comparator.REGEXP, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB.getValue());
        List<IFilter> conditionFilterList = Lists.newArrayList(approveTaskIdFilter, assignmentTypeFilter);

        return QueryDataHelper.query(TechnologyManagementAssignment.class, fieldList, conditionFilterList);
    }

    /**
     * 检索 故障复盘 / 故障整改 任务
     * @param faultAssignmentId
     * @return List<TechnologyManagementAssignment>
     */
    public static List<TechnologyManagementAssignment> queryFaultReviewAssignment(String faultAssignmentId) {
        if (!StringUtils.hasText(faultAssignmentId)) {
            return Lists.newArrayList();
        }

        // 1.检索绑定技术管理任务
        List<ObjectInstanceLinkDO> instanceList =
                ObjectLinkInstanceAbility.queryLinkInstance(LinkTypeEnum.BOUND, Lists.newArrayList(faultAssignmentId));
        List<String> assignmentIdList = instanceList.stream().map(ObjectInstanceLinkDO::getBillIdOut).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assignmentIdList)) {
            return Lists.newArrayList();
        }
        // 2.获取对应任务分类技术管理任务
        List<String> fieldList = Lists.newArrayList();

        Filter idFilter =new Filter(ID, Comparator.IN, new ArrayList<>(assignmentIdList));
        Filter categoryFilter =
                new Filter(TechnologyManagementFieldConsts.TASK_CATEGORY, Comparator.EQ, TaskCategoryEnum.FAULT_REVIEW.getValue());
        List<IFilter> conditionFilterList = Lists.newArrayList(idFilter, categoryFilter);

        return QueryDataHelper.query(TechnologyManagementAssignment.class, fieldList, conditionFilterList);
    }

    /**
     * 检索 故障复盘 / 故障整改 任务
     */
    public static List<TechnologyManagementAssignment> queryFaultTechAssignments(
            String faultAssignmentId,
            List<String> taskCategories) {

        if (!StringUtils.hasText(faultAssignmentId)) {
            return Lists.newArrayList();
        }

        // 1.检索绑定技术管理任务
        List<ObjectInstanceLinkDO> instances = ObjectLinkInstanceAbility.queryLinkInstance(
                LinkTypeEnum.BOUND, Lists.newArrayList(faultAssignmentId));
        List<String> techAssignmentIds = instances.stream()
                .map(ObjectInstanceLinkDO::getBillIdOut)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(techAssignmentIds)) {
            return Lists.newArrayList();
        }

        // 2.获取对应任务分类技术管理任务
        // 无条件默认检索整改横推任务任务
        Filter idFilter = new Filter(ID, Comparator.IN, new ArrayList<>(techAssignmentIds));

        if (CollectionUtils.isEmpty(taskCategories)) {
            taskCategories = Lists.newArrayList(
                    EMPTY_STRING,
                    TaskCategoryEnum.FAULT_RECTIFY.getValue(),
                    TaskCategoryEnum.HORIZONTAL_PUSHING_TASK.getValue());
        }
        IFilter categoryFilter =
                new Filter(TechnologyManagementFieldConsts.TASK_CATEGORY, Comparator.IN, taskCategories)
                        .or(new Filter(TechnologyManagementFieldConsts.TASK_CATEGORY, Comparator.IS_NULL, null));

        return QueryDataHelper.query(
                TechnologyManagementAssignment.class,
                Lists.newArrayList(),
                Lists.newArrayList(idFilter, categoryFilter));
    }



    // ====================== 故障管理任务数据库方法 ======================
    /**
     * 检索故障管理任务 - CSC 单据编号
     * @param cscCodeList
     * @return List<TechnologyManagementAssignment>
     */
    public static List<FaultManagementAssignment> queryFaultManagementAssignment(List<String> cscCodeList) {
        if (CollectionUtils.isEmpty(cscCodeList)) {
            return Lists.newArrayList();
        }

        List<String> fieldList = Lists.newArrayList();

        Filter codeFilter = new Filter(ASSIGNMENT_CODE, Comparator.IN, cscCodeList);
        Filter assignmentTypeFilter = new Filter(ASSIGNMENT_TYPE, Comparator.REGEXP, AssignmentTypeEnum.FAULT_MANAGEMENT.getValue());
        List<IFilter> conditionFilterList = Lists.newArrayList(codeFilter, assignmentTypeFilter);

        return QueryDataHelper.query(FaultManagementAssignment.class, fieldList, conditionFilterList);
    }

    /**
     * 检索故障管理任务 - 未绑定 WarRoom
     * @return List<FaultManagementAssignment>
     */
    public static List<FaultManagementAssignment> queryUnbindingFaultManagementAssignment() {
        List<String> fieldList = Lists.newArrayList();

        List<IFilter> conditionFilterList = new ArrayList<>();
        conditionFilterList.add(new Filter(ASSIGNMENT_TYPE, Comparator.REGEXP, AssignmentTypeEnum.FAULT_MANAGEMENT.getValue()));
        conditionFilterList.add(FilterHelper.newMultiFilter(
                new Filter(FaultManagementFieldConsts.WAR_ROOM, Comparator.IS_NULL, null)
                        .or(new Filter(FaultManagementFieldConsts.WAR_ROOM, Comparator.EQ, CommonConstants.EMPTY_STRING))));

        return QueryDataHelper.query(FaultManagementAssignment.class, fieldList, conditionFilterList);
    }

    // ======================= 流程任务集数据库方法 ======================
    /**
     * 检索流程任务集
     * @param taskIdList
     * @return AssignmentProcessSet
     */
    public static AssignmentProcessSet queryAssignmentProcessSet(List<String> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList();

        Filter idFilter = new Filter(ASSIGNMENT_SET, Comparator.IN, taskIdList);
        List<IFilter> conditionFilterList = Lists.newArrayList(idFilter);

        return QueryDataHelper.queryOne(AssignmentProcessSet.class, fieldList, conditionFilterList);
    }

    /**
     * 检索流程任务集
     * @param assignmentProcessSet
     * @return AssignmentProcessSet
     */
    public static void insertAssignmentProcessSet(AssignmentProcessSet assignmentProcessSet) {
        if (Objects.isNull(assignmentProcessSet)) {
            return;
        }

        SaveDataHelper.create(assignmentProcessSet);
    }

    /**
     * 检索流程任务集
     * @param assignmentProcessSet
     * @return AssignmentProcessSet
     */
    public static void updateAssignmentProcessSet(AssignmentProcessSet assignmentProcessSet) {
        if (Objects.isNull(assignmentProcessSet)) {
            return;
        }

        SaveDataHelper.update(assignmentProcessSet);
    }



    // =================== 子实体 - 人员关联表数据库方法 ===================
    /**
     * 校验已存在任务 - 人员关联关系
     * left - 已存在关系的用户id   right - 不存在关系的用户id
     * @param assignmentId
     * @param relevantList
     * @return Pair<List<String>, List<String>>
     */
    private static Pair<List<String>, List<String>> checkExistAssignmentRelevant(
            String assignmentId,
            List<String> relevantList) {

        if (!StringUtils.hasText(assignmentId) || CollectionUtils.isEmpty(relevantList)) {
            return Pair.of(Lists.newArrayList(), Lists.newArrayList());
        }

        // 1.检索相关关系
        Filter assignmentIdFilter = new Filter(PersonRelevanceFieldConsts.ASSIGNMENT_ID, Comparator.EQ, assignmentId);
        Filter relevantFilter = new Filter(PersonRelevanceFieldConsts.RELEVANT, Comparator.IN, relevantList);
        List<IFilter> conditionFilterList = Lists.newArrayList(assignmentIdFilter, relevantFilter);

        List<PersonRelevance> personRelevanceList =
                QueryDataHelper.query(PersonRelevance.class, Lists.newArrayList(), conditionFilterList);
        List<String> existRelevantList = personRelevanceList.stream()
                .map(PersonRelevance::getRelevant)
                .distinct().collect(Collectors.toList());

        // 2.校验用户与任务存在关联关系
        Set<String> existUserIdSet = new HashSet<>();
        Set<String> missUserIdSet = new HashSet<>();
        for (String relevant : relevantList) {
            if (existRelevantList.contains(relevant)) {
                existUserIdSet.add(relevant);
            } else {
                missUserIdSet.add(relevant);
            }
        }
        return Pair.of(new ArrayList<>(existUserIdSet), new ArrayList<>(missUserIdSet));
    }

    /**
     * 检索任务 - 人员关联关系
     */
    public static PersonRelevance getPersonRelevance(String assignmentId, String userId) {
        if (!StringUtils.hasText(assignmentId) || !StringUtils.hasText(userId)) {
            return null;
        }

        Filter assignmentIdFilter
                = new Filter(PersonRelevanceFieldConsts.ASSIGNMENT_ID, Comparator.EQ, assignmentId);
        Filter relevantFilter
                = new Filter(PersonRelevanceFieldConsts.RELEVANT, Comparator.EQ, userId);

        return QueryDataHelper.queryOne(
                PersonRelevance.class,
                Lists.newArrayList(),
                Lists.newArrayList(assignmentIdFilter, relevantFilter));
    }

    /**
     * 检索任务 - 人员关联关系
     */
    private static List<PersonRelevance> getPersonRelevance(List<String> assignmentIds, String userId) {
        if (CollectionUtils.isEmpty(assignmentIds) || !StringUtils.hasText(userId)) {
            return null;
        }

        Filter assignmentIdFilter
                = new Filter(PersonRelevanceFieldConsts.ASSIGNMENT_ID, Comparator.IN, assignmentIds);
        Filter relevantFilter
                = new Filter(PersonRelevanceFieldConsts.RELEVANT, Comparator.EQ, userId);

        return QueryDataHelper.query(
                PersonRelevance.class,
                Lists.newArrayList(),
                Lists.newArrayList(assignmentIdFilter, relevantFilter));
    }

    /**
     * 获取用户处理任务时间
     */
    public static Map<String, String> getUserDealingTime(List<String> assignmentIds, String userId) {
        // 1.检索任务 - 人员关联信息
        List<PersonRelevance> relevance = getPersonRelevance(assignmentIds, userId);
        if (CollectionUtils.isEmpty(relevance)) {
            return new HashMap<>();
        }

        // 2.包装 处理时间 信息
        return relevance.stream()
                .filter(item -> BoolEnum.Y.name().equals(item.getSelfHandlerFlag()))
                .collect(Collectors.toMap(
                        PersonRelevance::getAssignmentId,
                        item -> DateUtils.dateToString(item.getLastModifiedTime(), DATE_FORM),
                        (v1, v2) -> v1));
    }

    public static void getUserDealingTaskInfo(
            List<String> assignmentIds,
            String userId,
            Map<String, String> approvalTaskFlag,
            Map<String, String> dealingTime) {

        // 1.检索任务 - 人员关联信息
        List<PersonRelevance> personRelevance = getPersonRelevance(assignmentIds, userId);
        if (CollectionUtils.isEmpty(personRelevance)) {
            return;
        }

        // 2.包装 处理时间 信息
        for (PersonRelevance relevance : personRelevance) {
            if (BoolEnum.N.name().equals(relevance.getSelfHandlerFlag())) {
                continue;
            }

            approvalTaskFlag.put(relevance.getAssignmentId(), relevance.getApprovalTaskFlag());
            dealingTime.put(relevance.getAssignmentId(), DateUtils.dateToString(relevance.getLastModifiedTime(), DATE_FORM));
        }
    }

    /**
     * 新增任务相关人员关系
     * @param relevanceList
     */
    private static void batchInsertRelevance(List<PersonRelevance> relevanceList) {
        if (CollectionUtils.isEmpty(relevanceList)) {
            return;
        }

        SaveDataHelper.batchCreate(relevanceList);
    }

    /**
     * 更新任务相关人员关系
     * @param relevances 人员关系集合
     */
    public static void batchUpdateRelevance(List<PersonRelevance> relevances) {
        if (CollectionUtils.isEmpty(relevances)) {
            return;
        }
        SaveDataHelper.batchUpdate(relevances);
    }

    /**
     * 更新我已处理关系
     */
    public static void updateHandledByMeRelevance(
            String assignmentId,
            String userId,
            BoolEnum approvalTaskFlag) {

        if (!StringUtils.hasText(assignmentId) || !StringUtils.hasText(userId)) {
            return;
        }

        // 1.检索已存在关系
        PersonRelevance existRelevance = getPersonRelevance(assignmentId, userId);

        // 2.关系不存在，新增关联关系
        if (Objects.isNull(existRelevance)) {
            PersonRelevance insertRelevance = new PersonRelevance();
            insertRelevance.setAssignmentId(assignmentId);
            insertRelevance.setRelevant(userId);
            insertRelevance.setApprovalTaskFlag(approvalTaskFlag.name());
            insertRelevance.setSelfHandlerFlag(BoolEnum.Y.name());

            batchInsertRelevance(Lists.newArrayList(insertRelevance));

        } else {
            PersonRelevance updateRelevance = new PersonRelevance();
            updateRelevance.setId(existRelevance.getId());
            updateRelevance.setSelfHandlerFlag(BoolEnum.Y.name());
            updateRelevance.setApprovalTaskFlag(approvalTaskFlag.name());

            batchUpdateRelevance(Lists.newArrayList(updateRelevance));
        }
    }

    /**
     * 批量创建任务 - 人员关系
     */
    public static void createAssignmentPersonRelevance(String assignmentId, List<String> relevantIds) {
        // 1.检索已存在关联关系
        Pair<List<String>, List<String>> existRelevantPair =
                checkExistAssignmentRelevant(assignmentId, relevantIds);
        if (CollectionUtils.isEmpty(existRelevantPair.getRight())) {
            return;
        }

        // 2.未存在关联关系，新增关联关系
        List<PersonRelevance> insertRelevance = Lists.newArrayList();
        for (String insertRelevant : existRelevantPair.getRight()) {
            PersonRelevance personRelevance = new PersonRelevance();
            personRelevance.setAssignmentId(assignmentId);
            personRelevance.setRelevant(insertRelevant);
            personRelevance.setSelfHandlerFlag(BoolEnum.N.name());
            personRelevance.setApprovalTaskFlag(BoolEnum.N.name());

            insertRelevance.add(personRelevance);
        }

        batchInsertRelevance(insertRelevance);
    }

    /**
     * 批量创建任务 - 人员关系
     */
    public static boolean createAssignmentPersonRelevance(List<PersonRelevance> personRelevance) {
        if (CollectionUtils.isEmpty(personRelevance)) {
            return false;
        }

        // 1.根据 任务ID 汇聚对应人员关系
        Map<String, List<PersonRelevance>> assignmentRelevantInfo = personRelevance.stream()
                .collect(Collectors.groupingBy(PersonRelevance::getAssignmentId));

        for (Map.Entry<String, List<PersonRelevance>> entry : assignmentRelevantInfo.entrySet()) {
            String assignmentId = entry.getKey();
            List<String> relevantIdList = entry.getValue().stream()
                    .map(PersonRelevance::getRelevant)
                    .distinct()
                    .collect(Collectors.toList());

            // 2.检索已存在关联关系
            Pair<List<String>, List<String>> existRelevantPair =
                    checkExistAssignmentRelevant(assignmentId, relevantIdList);
            if (CollectionUtils.isEmpty(existRelevantPair.getRight())) {
                continue;
            }

            // 3.未存在关联关系，新增关联关系
            List<PersonRelevance> insertRelevanceList = entry.getValue().stream()
                    .filter(item -> existRelevantPair.getRight().contains(item.getRelevant()))
                    .collect(Collectors.toList());
            batchInsertRelevance(Lists.newArrayList(insertRelevanceList));
        }
        return true;
    }

    /**
     * 删除任务 - 人员关联关系
     * @param assignmentId
     * @param relevant
     */
    public static void deleteAssignmentPersonRelevance(String assignmentId, List<String> relevant) {
        // 1.检索已存在关联关系
        Filter assignmentIdFilter = new Filter(ASSIGNMENT_ID, Comparator.EQ, assignmentId);
        Filter relevantFilter = new Filter(RELEVANT, Comparator.IN, relevant);
        List<IFilter> conditionFilterList = Lists.newArrayList(assignmentIdFilter, relevantFilter);

        List<PersonRelevance> personRelevanceList =
                QueryDataHelper.query(PersonRelevance.class, Lists.newArrayList(), conditionFilterList);
        if (CollectionUtils.isEmpty(personRelevanceList)) {
            return;
        }

        // 2.删除关联关系
        List<String> personRelevanceIdList = personRelevanceList.stream().map(PersonRelevance::getId).collect(Collectors.toList());
        SaveDataHelper.batchDelete(PersonRelevance.class, personRelevanceIdList);
    }

    /**
     * 检索处理时间
     * @param assignmentIdList
     * @param userId
     * @return Map<String, Date>
     */
    public static List<PersonRelevance> getAssignmentHandledTime(List<String> assignmentIdList, String userId) {
        if (CollectionUtils.isEmpty(assignmentIdList)) {
            return new ArrayList<>();
        }

        Filter assignmentIdFilter = new Filter(PersonRelevanceFieldConsts.ASSIGNMENT_ID, Comparator.IN, assignmentIdList);
        Filter relevantFilter = new Filter(PersonRelevanceFieldConsts.RELEVANT, Comparator.EQ, userId);
        Filter selfHandleFilter = new Filter(PersonRelevanceFieldConsts.SELF_HANDLER_FLAG, Comparator.EQ, BoolEnum.Y.getValue());
        List<IFilter> conditionFilterList = Lists.newArrayList(assignmentIdFilter, relevantFilter, selfHandleFilter);
        return QueryDataHelper.query(PersonRelevance.class, Lists.newArrayList(), conditionFilterList);
    }

    /**
     * 根据网络变跟单 更新状态为完成或废止终止
     *
     * @param changeorderIds
     * @param status
     */
    public static void updateStatusByChangeId(List<String> changeorderIds, String status, AssignmentTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(changeorderIds) || !StringUtils.hasText(status)) {
            return;
        }
        List<Assignment> upadteAssignments = new ArrayList<>();
        changeorderIds.forEach(changeorderid -> {
            ApproveNodeEnum approveNodeEnum = ApproveNodeEnum.CLOSE;
            if (status.equals(AssignmentStatusEnum.ABOLISH.getValue())) {
                approveNodeEnum = ApproveNodeEnum.CANCEL;
            }

            List<String> fieldList = Lists.newArrayList(ID);
            List<IFilter> conditionFilterList = new ArrayList<>();
            conditionFilterList.add(new Filter(ASSIGNMENT_TYPE, Comparator.REGEXP, typeEnum.getValue()));
            conditionFilterList.add(new Filter(BILL_ID, Comparator.EQ, changeorderid));
            List<Assignment> assignments = QueryDataHelper.query(Assignment.class, fieldList, conditionFilterList);
            for (Assignment item : assignments) {
                item.setAssignmentStatus(status);
                item.setCurrentProgress(approveNodeEnum.name());
                item.setCurrentProcessorEmployee(new ArrayList<>());
            }
            upadteAssignments.addAll(assignments);
        });
        batchUpdate(upadteAssignments);
    }

    // ========================= 任务 - 工具方法 ========================
    /**
     * 获取任务所属公司
     * @param userIdList  任务所属员工
     * @return List<TextValuePair>
     */
    public static List<TextValuePair> getAssignmentCompany(List<String> userIdList) {
        // 1.UCS - 检索用户信息
        List<UcsUserInfo> userInfoList = UcsClient.getUserInfo(userIdList);
        if (CollectionUtils.isEmpty(userInfoList)) {
            return Lists.newArrayList();
        }

        // 2.包装返回数据
        List<TextValuePair> companyList = Lists.newArrayList();
        for (UcsUserInfo userInfo : userInfoList) {
            companyList.addAll(TextValuePairHelper.buildList(userInfo.getOrgId(), EMPTY_STRING, EMPTY_STRING));
        }
        return companyList;
    }

    /**
     * 设置当前进展和审批人到单据中心
     */
    public static void updateProgressByType(
            String nodeKey,
            List<String> approverIds,
            ApproveFlowCodeEnum approveFlowEnum,
            String businessId) {

        // 1.基础参数校验
        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromFlowCode(approveFlowEnum);
        if (!StringUtils.hasText(businessId) || Objects.isNull(assignmentType)) {
            return;
        }

        // 1.兼容团队节点会签格式数据
        List<String> compatibleIds = teamNodesCompatible(approverIds);

        // 兼容国际会签节点
        compatibleIds = intelTeamNodeCompatible(nodeKey, businessId, compatibleIds);
        // 2.更新审批进展
        if (AssignmentTypeEnum.NETWORK_CHANGE.equals(assignmentType)
                || AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(assignmentType)
                || AssignmentTypeEnum.CLOCK_REVIEW.equals(assignmentType)) {
            updateMainApproveProcess(businessId, assignmentType, nodeKey, compatibleIds);

        } else if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)
                || AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.equals(assignmentType)) {
            updateBatchApproveProcess(businessId, assignmentType, nodeKey, compatibleIds);
        }
    }

    private static List<String> intelTeamNodeCompatible(String nodeKey,
                                                        String businessId,
                                                        List<String> compatibleIds) {
        if (ApproveNodeEnum.INTL_ADMIN_APPROVAL.name().equals(nodeKey)
                || ApproveNodeEnum.BATCH_INTL_ADMIN_APPROVAL.name().equals(nodeKey)) {
            ActiveApproverNode activeApproverNode = FlowHelper.getApprovalNodeCurrentHandlers(businessId);
            List<String> approvers = activeApproverNode.getApprover();
            // 第一次进入国际会签不更新当前处理人
            if (!CollectionUtils.isEmpty(approvers)) {
                compatibleIds = approvers;
            }
        }
        return compatibleIds;
    }

    /**
     * 兼容会签节点处理人
     */
    private static List<String> teamNodesCompatible(List<String> approverIds) {
        List<String> compatibleIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(approverIds)) {
            return compatibleIds;
        }

        for (String approverId : approverIds) {
            if (!StringUtils.hasText(approverId)) {
                continue;
            }

            // 1.正常格式审批人
            if (!approverId.startsWith(CommonConstants.LEFT_CURLY_BRACE)) {
                compatibleIds.add(approverId);
                continue;
            }

            // 2.会签节点格式审批人
            Collection<Object> values = JSON.parseObject(approverId).values();
            for (Object value : values) {
                if (Objects.nonNull(value)) {
                    compatibleIds.addAll(Arrays.asList(value.toString().split(CommonConstants.COMMA)));
                }
            }
        }

        return compatibleIds;
    }

    /**
     * 更新审批进展 - 主任务
     */
    public static void updateMainApproveProcess(
            String businessId,
            AssignmentTypeEnum assignmentType,
            String approveNodeExCode,
            List<String> approverIds) {

        // 1.检索任务
        Assignment assignment = querySpecificTypeAssignment(businessId, assignmentType, Assignment.class);
        if (Objects.isNull(assignment)) {
            return;
        }

        // 2.包装审批信息
        BoolEnum approvalTaskFlag = calculateApprovalTaskFlag(assignmentType, approveNodeExCode);
        Assignment updateAssignment
                = convertApproveProcessInfo(assignment, approveNodeExCode, approverIds, approvalTaskFlag);

        // 特殊处理 - 打卡复盘任务状态
        if (AssignmentTypeEnum.CLOCK_REVIEW.equals(assignmentType)) {
            ClockInReviewsNodeEnum clockInReviewsNodeEnum = ClockInReviewsNodeEnum.fromName(approveNodeExCode);
            updateAssignment.setAssignmentStatus(clockInReviewsNodeEnum.getAssignmentStatusEnum().getValue());
        }

        // 3.更新任务
        update(updateAssignment);

        // 4.创建任务 - 人员关联关系
        String assignmentId = updateAssignment.getId();
        List<PersonRelevance> personRelevance = Lists.newArrayList();
        for (String approver : approverIds) {
            PersonRelevance relevance = new PersonRelevance();
            relevance.setAssignmentId(assignmentId);
            relevance.setRelevant(approver);
            relevance.setApprovalTaskFlag(approvalTaskFlag.name());
            relevance.setSelfHandlerFlag(BoolEnum.N.name());

            personRelevance.add(relevance);
        }
        createAssignmentPersonRelevance(personRelevance);
    }

    /**
     * 更新审批进展 - 批次任务
     */
    private static void updateBatchApproveProcess(
            String businessId,
            AssignmentTypeEnum assignmentType,
            String approveNodeExCode,
            List<String> approverIds) {

        // 1.检索任务
        Assignment assignment = queryBatchAssignment(businessId);
        if (Objects.isNull(assignment)) {
            return;
        }

        // 2.检索对应主任务
        AssignmentTypeEnum mainAssignmentType
                = AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)
                ? AssignmentTypeEnum.NETWORK_CHANGE
                : AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE;
        Assignment mainAssignment = querySpecificTypeAssignment(assignment.getBillId(), mainAssignmentType, Assignment.class);
        if (Objects.isNull(mainAssignment)) {
            return;
        }

        // 3.更新任务进展信息
        BoolEnum approvalTaskFlag = calculateApprovalTaskFlag(assignmentType, approveNodeExCode);
        Assignment updateAssignment
                = convertApproveProcessInfo(assignment, approveNodeExCode, approverIds, approvalTaskFlag);
        update(updateAssignment);

        // 4.创建任务 - 人员关联关系
        // 主任务创建默认联系
        createAssignmentPersonRelevance(mainAssignment.getId(), approverIds);

        // 批次任务创建审批任务联系
        List<PersonRelevance> personRelevance = Lists.newArrayList();
        for (String approver : approverIds) {
            PersonRelevance relevance = new PersonRelevance();
            relevance.setAssignmentId(assignment.getId());
            relevance.setRelevant(approver);
            relevance.setApprovalTaskFlag(approvalTaskFlag.name());
            relevance.setSelfHandlerFlag(BoolEnum.N.name());

            personRelevance.add(relevance);
        }
        createAssignmentPersonRelevance(personRelevance);

        // 5.主任务更新当前处理人
        updateCorrespondingMainAssignmentInfo(mainAssignment, assignmentType);
    }

    /**
     * 计算审批任务标识
     */
    private static BoolEnum calculateApprovalTaskFlag(
            AssignmentTypeEnum assignmentType,
            String approvingNode) {

        // 1.获取审批节点范围
        List<String> approvingNodes;
        if (AssignmentTypeEnum.NETWORK_CHANGE.equals(assignmentType)
                || AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)) {
            approvingNodes = ApproveNodeEnum.wholeApproveNodeEnum();
        } else if (AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(assignmentType)
                || AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.equals(assignmentType)) {
            approvingNodes = PartnerApproveNodeEnum.approvingNode();
        } else {
            approvingNodes = ClockInReviewsNodeEnum.approvingNode();
        }

        // 2.计算审批任务标识
        return approvingNodes.contains(approvingNode) ? BoolEnum.Y : BoolEnum.N;
    }

    /**
     * 包装任务进展信息
     */
    private static Assignment convertApproveProcessInfo(
            Assignment assignment,
            String approveNodeExCode,
            List<String> approverIds,
            BoolEnum approvalTaskFlag) {

        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignment.getId());

        // (1) 当前进展
        updateAssignment.setCurrentProgress(approveNodeExCode);

        // (2) 当前处理人
        updateAssignment.setCurrentProcessorEmployee(HrClient.queryEmployeeInfo(approverIds));

        // (3) 是否审批任务
        updateAssignment.setApprovalTaskFlag(approvalTaskFlag);

        return updateAssignment;
    }

    /**
     * 更新进展信息 - 批次对应主任务
     */
    private static void updateCorrespondingMainAssignmentInfo(
            Assignment assignment,
            AssignmentTypeEnum batchAssignmentType) {

        // 1.检索任务下所有批次任务
        List<NetworkChangeAssignment> batchAssignments
                = queryBatchAssignment(assignment.getBillId(), batchAssignmentType);
        if (CollectionUtils.isEmpty(batchAssignments)) {
            return;
        }

        // 2.汇总批次任务当前处理人
        List<Employee> currentProcessorEmployee = Lists.newArrayList();
        batchAssignments.forEach(batchTask ->
                currentProcessorEmployee.addAll(batchTask.getCurrentProcessorEmployee()));

        // 3.更新主任务当前处理人
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setCurrentProcessorEmployee(EmployeeHelper.uniqueEmployees(currentProcessorEmployee));

        update(updateAssignment);
    }

    /**
     * 重计算主任务当前处理人
     */
    public static void recalculateMainAssignmentCurrentProcessor(List<Assignment> assignments) {
        // 1.检索对应主任务
        List<String> billIds = assignments.stream()
                .map(Assignment::getBillId)
                .distinct()
                .collect(Collectors.toList());
        List<Assignment> wholeAssignments = QueryDataHelper.query(
                Assignment.class,
                Lists.newArrayList(ID, BILL_ID, ASSIGNMENT_TYPE, CURRENT_PROCESSOR_EMPLOYEE_FIELD),
                Lists.newArrayList(new Filter(BILL_ID, Comparator.IN, billIds)));

        // 2.区分主任务 + 批次任务
        Map<String, Assignment> mainAssignmentMap = new HashMap<>();
        Map<String, List<Assignment>> batchAssignmentMap = new HashMap<>();
        for (Assignment assignment : wholeAssignments) {
            AssignmentTypeEnum assignmentType
                    = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
            String billId = assignment.getBillId();

            if (AssignmentTypeEnum.NETWORK_CHANGE.equals(assignmentType)
                    || AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(assignmentType)) {
                mainAssignmentMap.put(billId, assignment);
            } else {
                List<Assignment> batchAssignments
                        = batchAssignmentMap.getOrDefault(billId, Lists.newArrayList());
                batchAssignments.add(assignment);
                batchAssignmentMap.put(billId, batchAssignments);
            }
        }

        // 3.汇总计算主任务当前处理人
        List<Assignment> updateAssignments = Lists.newArrayList();
        mainAssignmentMap.forEach((billId, mainAssignment) -> {
            List<Assignment> batchAssignments
                    = batchAssignmentMap.getOrDefault(billId, Lists.newArrayList());
            Set<Employee> newCurrentProcessors = batchAssignments.stream()
                    .map(Assignment::getCurrentProcessorEmployee)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toSet());

            Assignment updateAssignment = new Assignment();
            updateAssignment.setId(mainAssignment.getId());
            updateAssignment.setCurrentProcessorEmployee(new ArrayList<>(newCurrentProcessors));

            updateAssignments.add(updateAssignment);
        });
        batchUpdate(updateAssignments);
    }

    /**
     * 网络变更 - 汇总外部接口数据查询条件
     */
    public static Pair<ClientData, Map<String, FormatFieldData>> getNetworkChangeClientCondition(
            List<NetworkChangeAssignment> assignmentList) {
        if (CollectionUtils.isEmpty(assignmentList)) {
            return Pair.of(new ClientData(), new HashMap<>());
        }

        Set<String> organizationIdSet = new HashSet<>();
        Set<String> productIdSet = new HashSet<>();
        Set<String> networkIdSet = new HashSet<>();

        // 1.汇总外部接口查询条件 + 任务下数据ID信息
        Map<String, FormatFieldData> formatFieldDataMap = new HashMap<>();
        for (NetworkChangeAssignment assignment : assignmentList) {
            FormatFieldData formatFieldData = new FormatFieldData();

            // 1.组织ID - 代表处ID
            List<String> representativeOfficeIdList
                    = TextValuePairHelper.getValueList(assignment.getRepresentativeOffice());
            organizationIdSet.addAll(representativeOfficeIdList);
            formatFieldData.setRepresentativeOffice(representativeOfficeIdList);

            // 2.产品分类ID
            List<String> productClassificationIdList
                    = TextValuePairHelper.getValueList(assignment.getProductClassification());
            List<String> formatIdList = Lists.newArrayList();
            for (String productId : productClassificationIdList) {
                formatIdList.add(productId.substring(INTEGER_ZERO, productId.length() - INTEGER_ONE));
            }
            productIdSet.addAll(formatIdList);
            formatFieldData.setProduct(formatIdList);

            // 3.网络ID
            List<String> networkIdList = TextValuePairHelper.getValueList(assignment.getNetwork());
            networkIdSet.addAll(networkIdList);
            formatFieldData.setNetwork(networkIdList);

            formatFieldDataMap.put(assignment.getId(), formatFieldData);
        }

        ClientData clientData = new ClientData();
        clientData.setOrganizationId(organizationIdSet);
        clientData.setProductId(productIdSet);
        clientData.setNetworkId(networkIdSet);

        return Pair.of(clientData, formatFieldDataMap);
    }

    /**
     * 网络变更 - 查询外部接口数据
     */
    public static void queryNetworkChangeClientData(ClientData clientData) {
        // 1.HR - 组织数据
        clientData.setFullOrganization(
                getFullOrganizationName(Lists.newArrayList(clientData.getOrganizationId())));

        // 2.NIS - 产品分类
        clientData.setProduct(
                NisClient.queryProductPathName(Lists.newArrayList(clientData.getProductId())));

        // 3.NIS - 网络
        clientData.setNetwork(
                NisClient.queryNetworkName(Lists.newArrayList(clientData.getNetworkId())));
    }

    /**
     * 获取全量组织名称
     * 营销 / 片区 / 代表处 拆分成对应数据
     * key - 代表处 id   value - 组织全量名称（按顺序为 营销 / 片区 / 代表处）
     */
    private static Map<String, List<String>> getFullOrganizationName(List<String> representativeOfficeIds) {
        // 1.检索组织信息
        Map<String, BasicOrganizationInfo> organizations
                = HrClient.queryOrganizationInfo(representativeOfficeIds);

        // 2.拆分组织信息
        // 全路径拆分为 营销 - 片区 - 代表处
        // 名称顺序：代表处 / 片区 / 营销 / 公司
        Map<String, List<String>> organizationNames = new HashMap<>();
        for (Map.Entry<String, BasicOrganizationInfo> entry : organizations.entrySet()) {
            BasicOrganizationInfo organization = entry.getValue();

            String pathName = organization.getHrOrgNamePath();
            String[] names = pathName.split(FORWARD_SLASH);
            organizationNames.put(entry.getKey(), Lists.newArrayList(names[2], names[1], names[0]));
        }

        return organizationNames;
    }

    /**
     * 网络变更 - 汇总内部数据查询条件
     */
    public static InteriorData getNetworkChangeInteriorCondition(List<NetworkChangeAssignment> assignmentList) {
        if (CollectionUtils.isEmpty(assignmentList)) {
            return new InteriorData();
        }

        Set<String> batchApproveId = new HashSet<>();
        Set<String> subcontractBatchApproveId = new HashSet<>();
        for (NetworkChangeAssignment assignment : assignmentList) {
            // 1.批次任务 / 分包商批次任务状态
            AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
            if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)) {
                batchApproveId.add(assignment.getEntityId());
            }

            if (AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.equals(assignmentType)) {
                subcontractBatchApproveId.add(assignment.getEntityId());
            }
        }

        InteriorData interiorData = new InteriorData();
        interiorData.setBatchApproveId(batchApproveId);
        interiorData.setSubcontractBatchApproveId(subcontractBatchApproveId);

        return interiorData;
    }

    /**
     * 网络变更 - 查询内部数据
     */
    public static void queryNetworkChangeInteriorData(InteriorData interiorData) {
        String language = ContextHelper.getLangId();

        // 1.批次任务 - 查询状态
        interiorData.setBatchStatus(
                getBatchAssignmentStatus(new ArrayList<>(interiorData.getBatchApproveId())));

        // 2.分包商批次任务 - 查询状态
        interiorData.setSubcontractBatchStatus(
                getSubcontractBatchAssignmentStatus(new ArrayList<>(interiorData.getSubcontractBatchApproveId())));

        // 3.网络变更任务当前进展
        interiorData.setMainCurrentProgress(ApproveNodeEnum.getNodeNames(language));

        // 4.合作方网络变更任务当前进展
        interiorData.setSubCurrentProgress(PartnerApproveNodeEnum.getNodeNames(language));
    }

    /**
     * 查询批次任务状态
     */
    public static  Map<String, String> getBatchAssignmentStatus(List<String> batchTaskIdList) {
        if (CollectionUtils.isEmpty(batchTaskIdList)) {
            return new HashMap<>();
        }

        // 1.检索批次任务数据
        List<String> fieldList = Lists.newArrayList(ID, CURRENT_STATUS);
        List<BatchTask> batchTaskList = BatchTaskAbility.batchGet(batchTaskIdList, fieldList, BatchTask.class);
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return new HashMap<>();
        }

        // 2.检索快码
        List<LookupValue> lookupValueList = LookupValueHelper.getLookupValues(ASSIGNMENT_STATUS_ENUM);
        Map<String, String> lookupValueMap = lookupValueList.stream()
                .collect(Collectors.toMap(LookupValue::getLookupCode, LookupValue::getMeaning, (v1, v2) -> v1));

        // 3.包装任务状态数据
        Map<String, String> assignmentStatusMap = new HashMap<>();
        for (BatchTask batchTask : batchTaskList) {
            assignmentStatusMap.put(batchTask.getId(), lookupValueMap.get(batchTask.getCurrentStatus()));
        }

        return assignmentStatusMap;
    }

    /**
     * 查询分包商批次任务状态
     */
    public static  Map<String, String> getSubcontractBatchAssignmentStatus(List<String> batchTaskIdList) {
        if (CollectionUtils.isEmpty(batchTaskIdList)) {
            return new HashMap<>();
        }

        // 1.检索批次任务数据
        List<String> fieldList = Lists.newArrayList(ID, CURRENT_STATUS);
        List<SubcontractorBatchTask> batchTaskList = BatchTaskAbility.batchGet(batchTaskIdList, fieldList, SubcontractorBatchTask.class);
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return new HashMap<>();
        }

        // 2.检索快码
        List<LookupValue> lookupValueList = LookupValueHelper.getLookupValues(ASSIGNMENT_STATUS_ENUM);
        Map<String, String> lookupValueMap = lookupValueList.stream()
                .collect(Collectors.toMap(LookupValue::getLookupCode, LookupValue::getMeaning, (v1, v2) -> v1));

        // 3.包装任务状态数据
        Map<String, String> assignmentStatusMap = new HashMap<>();
        for (SubcontractorBatchTask batchTask : batchTaskList) {
            assignmentStatusMap.put(batchTask.getId(), lookupValueMap.get(batchTask.getCurrentStatus()));
        }

        return assignmentStatusMap;
    }

    /**
     * 获取变更单当前进展节点
     * todo 这个方法用在列表里会有性能问题
     * 平均执行 1 次是 30 ms, 列表数据量通常 20 条起步，100 条 就需要 3s
     */
    public static String getNetWorkCurrentProcess(AssignmentTypeEnum assignmentType,
                                                  String approvalNodeExCode) {
        String currentProcess = "";
        if(AssignmentTypeEnum.NETWORK_CHANGE == assignmentType ||
                AssignmentTypeEnum.NETWORK_CHANGE_BATCH == assignmentType ){
            ApproveNodeEnum approveNodeEnum = ApproveNodeEnum.getApproveNodeEnum(approvalNodeExCode);
            if(null != approveNodeEnum){
                return approveNodeEnum.getName(ContextHelper.getLangId());
            }
        }
        if(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE == assignmentType ||
                AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH == assignmentType ){
            PartnerApproveNodeEnum approveNodeEnum = PartnerApproveNodeEnum.getApproveNodeEnum(approvalNodeExCode);
            if(null != approveNodeEnum){
                return approveNodeEnum.getName(ContextHelper.getLangId());
            }
        }
        return currentProcess;
    }

    /**
     * 参数 - 用户基础权限
     */
    public static IFilter userPermissionFilter(String userId) {

        // 1.责任人权限
        IFilter relationFilter = getRelationPermissionFilter(userId);

        // 2.UPP 权限
        IFilter uppFilter = getUppPermissionFilter(userId);

        // 3.包装查询对象
        return Objects.isNull(uppFilter)
                ? relationFilter
                : FilterHelper.newMultiFilter(uppFilter.or(relationFilter));
    }

    /**
     * 用户权限 - 相关人权限
     */
    private static IFilter getRelationPermissionFilter(String userId) {
        // 1.包装子查询 SQL
        String subQuerySql =
                String.format("select apr.assignment_id from assignment_person_relevance apr"
                        + " where apr.relevant = '%s'"
                        + " and apr.is_deleted = 0",
                userId);

        // 2.包装查询条件
        return new SubQueryFilter(new Filter(ID, Comparator.IN, Arrays.asList(INTEGER_ONE)), subQuerySql);
    }

    /**
     * 用户权限 - UPP 授权
     */
    public static IFilter getUppPermissionFilter(String userId) {
        // 1.获取用户 UPP 授权数据
        Pair<List<String>, List<String>> constraintPair = getUppDataConstraint(userId);
        List<String> organizationConstraint = constraintPair.getLeft();
        List<String> productConstraint = constraintPair.getRight();
        if (CollectionUtils.isEmpty(organizationConstraint) || CollectionUtils.isEmpty(productConstraint)) {
            return null;
        }

        // 2.校验 合作方 用户
        BasicEmployeeInfo basicEmployeeInfo = HrClient.getEmployeeInfo(userId);

        // 3.用户授权
        return HR_PERSON_COMPANY_TYPE_PARTNER_ZH.equals(basicEmployeeInfo.getCompanyType())
                    || HR_PERSON_COMPANY_TYPE_PARTNER_EN.equals(basicEmployeeInfo.getCompanyType())
                ? createPartnerUserPermissionFilter(userId, organizationConstraint, productConstraint, basicEmployeeInfo.getOrgID())
                : createInternalUserPermissionFilter(organizationConstraint, productConstraint);
    }

    /**
     * 获取用户授权数据
     * left - 组织授权   right - 产品授权
     */
    public static Pair<List<String>, List<String>> getUppDataConstraint(String userId) {
        // 1.查询用户 代表处 / 产品经营团队 授权数据
        List<ConstraintTypeEnum> constraintTypeList
                = Lists.newArrayList(ConstraintTypeEnum.ORGANIZATION, ConstraintTypeEnum.PRODUCT);
        Map<ConstraintTypeEnum, Set<String>> userConstraint
                = NisClient.getUserConstraint(userId, constraintTypeList);
        Set<String> organizationConstraint
                = userConstraint.getOrDefault(ConstraintTypeEnum.ORGANIZATION, new HashSet<>());
        Set<String> productConstraint
                = userConstraint.getOrDefault(ConstraintTypeEnum.PRODUCT, new HashSet<>());

        // 2.特殊权限补充
        addSpecialOrganizationConstraint(organizationConstraint);
        List<String> sortOrganizations
                = organizationConstraint.stream().sorted().collect(Collectors.toList());

        // 3.产品经营团队权限格式化
        List<String> formatProductConstraint = productConstraint.stream()
                .map(ProductUtils::trimEndSlash)
                .sorted()
                .collect(Collectors.toList());

        return Pair.of(sortOrganizations, formatProductConstraint);
    }

    /**
     * 特殊 代表处 权限补充范围
     */
    private static void addSpecialOrganizationConstraint(Set<String> organizationConstraint) {
        // 1.检索 NIS 组织树
        List<OrganizationTreeVo> tiledTreeData = NisClient.queryTiledOrganizationTree();

        // 2.计算待扩展范围
        // (1) 国内营销 代表处
        List<String> domesticExtendOffices = tiledTreeData.stream()
                .map(OrganizationTreeVo::getOrgIdPath)
                .filter(item -> item.startsWith(BusinessConsts.DOMESTIC_SALES))
                .collect(Collectors.toList());

        // (2) 工服一部 & 工服二部 & 工服五部 & MTO 所有代表处
        List<String> internationalExtendOffices = tiledTreeData.stream()
                .map(OrganizationTreeVo::getOrgIdPath)
                .filter(item -> item.startsWith(BusinessConsts.ENGINEERING_SERVICE_DEPARTMENT_ONE)
                        || item.startsWith(BusinessConsts.ENGINEERING_SERVICE_DEPARTMENT_TWO)
                        || item.startsWith(BusinessConsts.ENGINEERING_SERVICE_DEPARTMENT_FIVE)
                        || item.startsWith(BusinessConsts.ENGINEERING_SERVICE_DEPARTMENT_MTO) )
                .collect(Collectors.toList());

        // 1.国内权限扩展
        // 工程服务处 & 网络服务处 需扩展 国内营销 所有代表处
        if (organizationConstraint.contains(BusinessConsts.ENGINEERING_SERVICE_SALES)
                || organizationConstraint.contains(BusinessConsts.NET_SERVICE_SALES)) {
            organizationConstraint.addAll(domesticExtendOffices);
        }

        // 2.国际权限扩展
        // 综合技术交付部 需扩展 工服一部 & 工服二部 & 工服五部 & MTO 所有代表处
        if (organizationConstraint.contains(BusinessConsts.INTEGRATED_TECHNOLOGY_DEPARTMENT)) {
            organizationConstraint.addAll(internationalExtendOffices);
        }
    }

    /**
     * 用户授权条件 - 内部用户
     */
    private static IFilter createInternalUserPermissionFilter(
            List<String> organizationConstraint,
            List<String> productConstraint) {

        // 1.条件格式化 - 代表处
        List<String> organizationIds = organizationConstraint.stream()
                .map(ResponsibleUtils::getResponsible)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        // 2.包装数据授权条件
        if (CollectionUtils.isEmpty(organizationIds)) {
            return null;
        }

        IFilter organizationFilter = FilterHelper.multiMemberOfFilter(REPRESENTATIVE_OFFICE, organizationIds);
        IFilter productFilter = FilterHelper.multiMemberOfFilter(PRODUCT_MANAGEMENT_TEAM, productConstraint);
        return FilterHelper.newMultiFilter(organizationFilter, productFilter);
    }

    /**
     * 用户授权条件 - 合作方用户
     */
    private static IFilter createPartnerUserPermissionFilter(
            String userId,
            List<String> organizationConstraint,
            List<String> productConstraint,
            String companyId) {

        // 1.条件格式化 - 代表处
        Pair<List<String>, List<String>> organizationPair
                = distinguishInternationalAndDomesticOrganization(userId, organizationConstraint);
        List<String> domestic = organizationPair.getLeft();
        List<String> international = organizationPair.getRight();

        // 2.包装授权条件 - 无有效代表处数据
        if (CollectionUtils.isEmpty(domestic) && CollectionUtils.isEmpty(international)) {
            return null;
        }

        // 3.包装授权条件 - 全量国际代表处，无国内代表处
        if (CollectionUtils.isEmpty(domestic)) {
            return FilterHelper.newMultiFilter(
                    new Filter(REPRESENTATIVE_OFFICE, Comparator.IN, international)
                            .and(new Filter(PRODUCT_MANAGEMENT_TEAM, Comparator.IN, productConstraint)));
        }

        // 4.包装授权条件 - 全量国内代表处，无国际代表处
        if (CollectionUtils.isEmpty(international)) {
            return FilterHelper.newMultiFilter(
                    new Filter(REPRESENTATIVE_OFFICE, Comparator.IN, domestic)
                            .and(new Filter(PRODUCT_MANAGEMENT_TEAM, Comparator.IN, productConstraint))
                            .and(new Filter(COMPANY, Comparator.IN, Lists.newArrayList(companyId))));
        }

        // 5.包装授权条件 - 同时有国内代表处 + 国际代表处
        IFilter domesticFilter =
                FilterHelper.newMultiFilter(
                        new Filter(REPRESENTATIVE_OFFICE, Comparator.IN, domestic)
                                .and(new Filter(PRODUCT_MANAGEMENT_TEAM, Comparator.IN, productConstraint))
                                .and(new Filter(COMPANY, Comparator.IN, Lists.newArrayList(companyId))));
        IFilter internationalFilter = FilterHelper.newMultiFilter(
                new Filter(REPRESENTATIVE_OFFICE, Comparator.IN, international)
                        .and(new Filter(PRODUCT_MANAGEMENT_TEAM, Comparator.IN, productConstraint)));
        return FilterHelper.newMultiFilter(domesticFilter.or(internationalFilter));
    }

    /**
     * 区分 国际 / 国内 代表处
     * left - 国内   right - 国际
     */
    public static Pair<List<String>, List<String>> distinguishInternationalAndDomesticOrganization(
            String userId,
            List<String> organizationConstraint) {

        Set<String> domesticIds = new HashSet<>();
        Set<String> internationalIds = new HashSet<>();

        // 1.检索权限申请数据 - 中智&高租
        List<PermissionApplication> zhongZhiHLPermission
                = PermissionApplicationAbility.queryZhongZhiHLPermission(userId);
        Set<String> zhongZhiHLOrganizationIds = new HashSet<>();
        zhongZhiHLPermission.forEach(item ->
                zhongZhiHLOrganizationIds.addAll(TextValuePairHelper.getValueList(item.getOrganizations())));
        //是否申请特殊代表处 中智&高租权限
        boolean isSpecialOrg = zhongZhiHLOrganizationIds.stream().anyMatch(
                item -> BusinessConsts.ENGINEERING_SERVICE_SALES.startsWith(item) || BusinessConsts.NET_SERVICE_SALES.startsWith(item));

        // 2.区分 国内 / 国际 代表处
        // domesticMarketing - 国内营销
        // domesticDepartment - 工程服务三部
        String[] innerOrgPath = BusinessConsts.INNER_OFFICE_ORG_CODE_PATH;
        String domesticMarketing = innerOrgPath[0];
        String domesticDepartment = innerOrgPath[1];

        for (String organization : organizationConstraint) {
            // (1) 拆分全路径
            String[] organizationIdArray = organization.split(CommonConstants.FORWARD_SLASH);
            if (organizationIdArray.length < 4) {
                continue;
            }
            String organizationId = organizationIdArray[3];

            // (2) 校验 国际 代表处
            if (!organization.startsWith(domesticMarketing) && !organization.startsWith(domesticDepartment)) {
                internationalIds.add(organizationId);
                continue;
            }

            // (3) 国内代表处 - 校验中智&高租
            boolean zhongZhiHLFlag = (organization.startsWith(domesticMarketing) && isSpecialOrg)
                    || zhongZhiHLOrganizationIds.stream().anyMatch(organization::startsWith);

            // (4) 国内代表处 中智&高租 在权限数据应用时 等同于国际代表处
            if (zhongZhiHLFlag) {
                internationalIds.add(organizationId);
            } else {
                domesticIds.add(organizationId);
            }
        }

        return Pair.of(new ArrayList<>(domesticIds), new ArrayList<>(internationalIds));
    }

    /**
     * 包装基础 权限申请 任务
     */
    public static PermissionApplicationAssignment convertBasicPermissionAssignment(
            PermissionApplication application) {

        PermissionApplicationAssignment assignment = new PermissionApplicationAssignment();

        assignment.setAssignmentType(AssignmentTypeEnum.PERMISSION_APPLICATION.getPropValue());
        assignment.setBillType(BillTypeEnum.PERMISSION_APPLICATION.getPropValue());
        assignment.setApprovalTaskFlag(BoolEnum.Y);

        assignment.setBillId(application.getId());
        assignment.setEntityId(application.getId());
        assignment.setAssignmentName(application.getBillName());
        assignment.setAssignmentCode(application.getBillNo());
        assignment.setModule(application.getModuleName());
        assignment.setRole(application.getRoleName());
        assignment.setRepresentativeOffice(getMinLevelOrganizationIds(application.getOrganizations()));
        assignment.setProductClassification(application.getProduct());
        assignment.setExpirationTime(application.getExpirationTime());
        assignment.setAssignmentStatus(
                PermissionApplicationStatusEnum.convertAssignmentStatus(application.getStatus()));

        return assignment;
    }

    /**
     * 获取最小层级组织ID
     */
    private static List<TextValuePair> getMinLevelOrganizationIds(List<TextValuePair> organizations) {
        if (CollectionUtils.isEmpty(organizations)) {
            return com.zte.iccp.itech.zlic.util.Lists.newArrayList();
        }

        List<TextValuePair> minLevels = Lists.newArrayList();
        for (TextValuePair organization : organizations) {
            String minLevelId = ResponsibleUtils.minLevelId(organization.getValue());
            minLevels.addAll(
                    TextValuePairHelper.buildList(minLevelId, CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING));
        }

        return minLevels;
    }

    /**
     * 包装 打卡复盘 任务
     */
    public static ClockInReviewsAssignment convertClockInReviewAssignment(
            ClockInReviews clockInReviews,
            List<Employee> currentProcessors) {

        ClockInReviewsAssignment assignment = new ClockInReviewsAssignment();

        assignment.setEntityId(clockInReviews.getId());
        assignment.setBillId(clockInReviews.getId());
        assignment.setAssignmentName(clockInReviews.getOperationSubject());
        assignment.setAssignmentCode(clockInReviews.getReviewsNo());
        assignment.setMarketing(clockInReviews.getMarketing());
        assignment.setRepresentativeOffice(clockInReviews.getResponsibleDept());
        assignment.setProductClassification(clockInReviews.getProductClassification());
        assignment.setNetwork(clockInReviews.getNetworkName());
        assignment.setBatchNo(clockInReviews.getBatchNo());
        assignment.setOperationType(clockInReviews.getOperationType());
        assignment.setOperationLevel(clockInReviews.getOperationLevel());
        assignment.setPlanOperationStartTime(clockInReviews.getPlanOperationStartTime());
        assignment.setOnDutyDurationHours(clockInReviews.getOnDutyDurationHours());

        assignment.setAssignmentStatus(clockInReviews.getReplayStatus().getValue());

        List<Employee> operationOwners = EmployeeHelper.uniqueEmployees(clockInReviews.getOperationOwners());
        assignment.setResponsibleEmployee(operationOwners);
        assignment.setCurrentProcessorEmployee(!CollectionUtils.isEmpty(currentProcessors)
                ? currentProcessors
                : HrClient.queryEmployeeInfo(ConfigHelper.getList(DEFAULT_APPROVER_KEY)));

        assignment.setAssignmentType(AssignmentTypeEnum.CLOCK_REVIEW.getPropValue());
        assignment.setBillType(BillTypeEnum.CLOCK_REVIEW.getPropValue());

        return assignment;
    }

    /**
     * 异步分时间段计算总量
     */
    public static Integer asyncStepCount(
            List<IFilter> tableFilters,
            MainEntityType mainEntityType,
            Date startDate) {

        // 1.计算时间区间长度
        int threadTotal = Integer.parseInt(ConfigHelper.get(THREAD_TOTAL));
        int days = DateUtils.dayDiff(new Date(), startDate);
        Integer partDays = days / threadTotal + 1;

        // 2.包装异步检索执行方法
        List<Supplier<Integer>> suppliers = Lists.newArrayList();
        for (int index = 0; index < threadTotal; index ++) {
            Date endDate = DateUtils.addDay(startDate, partDays);

            List<IFilter> iFilters = Lists.newArrayList();
            iFilters.add(FilterHelper.newMultiFilter(
                    new Filter(CommonFieldConsts.CREATE_TIME, Comparator.GE, startDate)
                            .and(new Filter(CommonFieldConsts.CREATE_TIME, Comparator.LT, endDate))));
            iFilters.addAll(tableFilters);
            iFilters = FilterHelper.handleFilters(Assignment.class, iFilters);

            QueryBuilder queryBuilder = new QueryBuilder(mainEntityType);
            queryBuilder.addFilter(Lists.newArrayList(iFilters));
            List<IFilter> finalIFilters = iFilters;
            suppliers.add(() -> BaseQueryDataHelper.count(mainEntityType, CidConstants.TABLE_ASSIGNMENT_CID, finalIFilters));

            startDate = endDate;
        }

        // 3.异步执行检索总量
        List<Integer> partTotal = AsyncExecuteUtils.asyncQuery(suppliers);
        return partTotal.stream()
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
    }

    /**
     * 异步分时间段计算总量
     */
    public static Integer asyncStepCount(String countSql, Date startDate) {

        // 1.计算时间区间长度
        int threadTotal = Integer.parseInt(ConfigHelper.get(THREAD_TOTAL));
        int days = DateUtils.dayDiff(new Date(), startDate);
        Integer partDays = days / threadTotal + 1;

        // 2.包装异步检索执行方法
        List<Supplier<Integer>> suppliers = Lists.newArrayList();
        for (int index = 0; index < threadTotal; index ++) {
            Date endDate = DateUtils.addDay(startDate, partDays);
            String partCountSql = String.format("%s AND (create_time >= '%s' AND create_time < '%s')",
                    countSql,
                    DateUtils.dateToString(startDate, CommonConstants.DATE_FORM),
                    DateUtils.dateToString(endDate, CommonConstants.DATE_FORM));
            suppliers.add(() -> BaseQueryDataHelper.count(partCountSql, new HashMap<>()));

            startDate = endDate;
        }

        // 3.异步执行检索总量
        List<Integer> partTotal = AsyncExecuteUtils.asyncQuery(suppliers);
        return partTotal.stream()
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
    }

    /**
     * 获取任务表当前进展（不为空，且不为草稿、驳回待启动状态均可）
     *
     * @param pkId id
     * @return 当前进展
     */
    public static String getAssignmentCurrentProgress(String pkId, String extendedCode) {
        if (isValid(extendedCode)) {
            return extendedCode;
        }

        return RetryUtils.get(() -> {
            Assignment assignment = AssignmentAbility.queryAssignment(pkId, Lists.newArrayList(ID, CURRENT_PROGRESS), Assignment.class);
            if (assignment != null && isValid(assignment.getCurrentProgress())) {
                return assignment.getCurrentProgress();
            }
            return null;
        });
    }

    /**
     * 校验当前进展是否不为空，且不为草稿、驳回待启动状态
     *
     * @param extendedCode 当前进展
     * @return true：通告，false：不通过
     */
    private static boolean isValid(String extendedCode) {
        return extendedCode != null && !ApproveNodeEnum.DRAFT.name().equals(extendedCode)
                && !ApproveNodeEnum.REJECTION.name().equals(extendedCode);
    }

    /**
     * 根据entityID修改计划开始时间
     */
    public static void updatePlanStartTimeByEntityIds(List<String> entityIds, Date planStartTime) {
        if (CollectionUtils.isEmpty(entityIds) || planStartTime == null) {
            return;
        }

        Filter entityIdsFilter = new Filter(ENTITY_ID, Comparator.IN, entityIds);
        Filter assignmentTypeFilter = new Filter(BILL_TYPE, Comparator.EQ,
                Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE.name()));
        List<IFilter> conditionFilters = Lists.newArrayList(entityIdsFilter, assignmentTypeFilter);
        List<NetworkChangeAssignment> assignments = QueryDataHelper.query(NetworkChangeAssignment.class,
                Lists.newArrayList(ID, TIME_ZONE), conditionFilters);
        assignments.forEach(i -> {
            // 操作开始时间
            i.setPlanStartTime(planStartTime);
            // 操作开始时间（UTC+8）
            TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneEnum(i.getTimeZone());
            // 如果不是北京时间，需要根据时区转换成北京时间;否则，直接取用户输入的计划操作开始时间即可
            if (timeZoneEnum != null && TimeZoneEnum.BEIJING != timeZoneEnum) {
                i.setOperationStartTimeUtc8(TimeZoneEnum.BEIJING.pollute(timeZoneEnum.fix(planStartTime)));
            } else {
                i.setOperationStartTimeUtc8(planStartTime);
            }

        });
        SaveDataHelper.batchUpdate(assignments);
    }

    /**
     * 获取额外参数 - 转交当前处理人
     * key - 跳转参数 key   value - 跳转属性
     */
    public static Map<String, Object> getTransferCurrentProcessorExtraParameter(List<String> assignmentIds) {
        // 1.检索任务数据
        List<Assignment> assignments = get(
                assignmentIds,
                Lists.newArrayList(
                        AssignmentFieldConsts.ASSIGNMENT_CODE,
                        AssignmentFieldConsts.ASSIGNMENT_TYPE,
                        AssignmentFieldConsts.ASSIGNMENT_STATUS),
                Assignment.class);

        // 2.任务类型校验
        // 由于 全部 页签批量转交按钮已隐藏（网络变更 / 合作方网络变更 转交模式与其他任务不同，批量转交无法处理）
        // 仅根据首任务任务类型进行校验处理即可
        AssignmentTypeEnum assignmentType
                = AssignmentTypeEnum.fromTextValuePair(assignments.get(0).getAssignmentType());
        if (!AssignmentTypeEnum.networkChangeTypes().contains(assignmentType)) {
            return MapUtils.newHashMap( CidConstants.NETWORK_CHANGE_FLAG, false);
        }

        // 主任务执行中校验
        String executeCodes = ChangeOrderAbility.todoTransferBeforeCheck(assignments);
        if (StringUtils.hasText(executeCodes)) {
            throw new LcapBusiException(
                    MsgUtils.getMessage(MessageConsts.TODO_TRANSFER_PROMPT, executeCodes));
        }

        return MapUtils.newHashMap( CidConstants.NETWORK_CHANGE_FLAG, true);
    }
}
