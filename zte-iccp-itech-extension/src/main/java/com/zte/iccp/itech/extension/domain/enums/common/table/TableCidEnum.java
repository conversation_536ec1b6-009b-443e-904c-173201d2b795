package com.zte.iccp.itech.extension.domain.enums.common.table;

import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum TableCidEnum {

    /** 网络查询 */
    NETWORK_QUERY(
            PageConstants.PAGE_WEB_OPERAND_QUERY_NETWORK,
            CidConstants.BUTTON_QUERY_CID,
            CidConstants.TABLE_OPERAND_QUERY_NETWORK_CID),

    /** 版本列表 */
    VERSION_QUERY(
            PageConstants.PAGE_WEB_QUERY_VERSION,
            CidConstants.SELF_PAGE_VERSION_BUTTON_QUERY_ID,
            CidConstants.SELF_PAGE_VERSION_TABLE_ID),

    /** 当前版本列表 - IVersion */
    I_VERSION_CURRENT_QUERY(
            PageConstants.PAGE_WEB_QUERY_CURRENT_VERSION,
            CidConstants.SELF_PAGE_VERSION_BUTTON_QUERY_ID,
            CidConstants.SELF_PAGE_VERSION_TABLE_ID),

    /** 当前版本列表 - CSC */
    CSC_CURRENT_QUERY(
            PageConstants.PAGE_WEB_QUERY_CURRENT_VERSION,
            CidConstants.BUTTON_QUERY_CID,
            CidConstants.TABLE_CSC_VERSION),

    /** 配置中心 - 邮件推送群组配置 */
    EMAIL_SEND_GROUP(
            PageConstants.PAGE_WEB_EMAIL_SEND_GROUP,
            CidConstants.BUTTON_QUERY_CID,
            CidConstants.TABLE_EMAIL_SEND_GROUP)
    ;

    /** 页面ID */
    private final String pageCid;

    /** 按钮ID */
    private final String buttonCid;

    /** 表格ID */
    private final String tableCid;

    /**
     * 获取对应表格 CID
     */
    public static String getTableCid(String pageCid, String buttonCid) {
        for (TableCidEnum tableCidEnum : TableCidEnum.values()) {
            if (tableCidEnum.getPageCid().equals(pageCid)
                    && tableCidEnum.getButtonCid().equals(buttonCid)) {
                return tableCidEnum.getTableCid();
            }
        }

        return "";
    }
}
