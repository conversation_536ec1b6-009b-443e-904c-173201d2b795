package com.zte.iccp.itech.extension.domain.enums.flow;

import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.NoSuchElementException;

import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.APPROVAL_FLOW_NOT_FOUND;

/**
 * 流程编码对应流程信息
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ApproveFlowCodeEnum {
    /** 技术管理-流程编 码对应流程信息 */
    TECH_MANAGE_FLOW("技术管理-主流程", TechnologyManagementOrder.class, TechnologyManagementOrder.class),
    /** 技术管理-子任务 流程编码对应流程信息 */
    TECH_MANAGE_SUBTASK("技术管理-子任务", ManageSubTaskFlow.class, ManageSubTaskFlow.class),
    /** 网络变更单-主流程，流程编码对应流程信息 */
    CHANGE_ORDER_COMP_FLOW("网络变更单-主流程", ChangeOrder.class, ChangeOrder.class),
    /** 流网络变更单-批次任务,程编码对应流程信息 */
    BATCH_TASK_FLOW("网络变更单-批次任务", BatchTask.class, ChangeOrder.class),
    /** 分包商网络变更单-主流程，流程编码对应流程信息 */
    SUBCONTRACTOR_OC_FLOW("分包商网络变更单-主流程", SubcontractorChangeOrder.class, SubcontractorChangeOrder.class),
    /** 分包商网络变更单-批次任务,流程编码对应流程信息 */
    SUBCONTRACTOR_TASK_FLOW("分包商网络变更单-批次任务", SubcontractorBatchTask.class, SubcontractorChangeOrder.class),
    /** 故障管理任务,流程编码对应流程信息 */
    FAULT_MANAGE_FLOW("故障管理任务", FaultManagementOrder.class, FaultManagementOrder.class),
    /** 权限申请任务,流程编码对应流程信息 */
    PERMISSION_APPLICATION("权限申请任务",PermissionApplication.class, PermissionApplication.class),
    /** 打卡复盘 */
    CLOCK_IN_REVIEW_FLOW("打卡复盘", ClockInReviews.class, ClockInReviews.class),
    /** 操作计划任务 */
    OPERATION_PLAN_FLOW("操作计划", PlanOperationOrder.class, PlanOperationOrder.class),;

    /**
     * 流程名称
     */
    private final String name;
    /**
     * 当前流程对应的单据实体
     */
    private final Class<? extends BaseEntity> flowEntity;
    /**
     * 用于获取审批人的单据（批次任务和变更单任务用的一个）
     */
    private final Class<? extends BaseEntity> approverEntity;

    /**
     * 通过流程编码获取对应流程枚举
     * @param enumName enumName
     * @return ApproveFlowCodeEnum
     */
    public static ApproveFlowCodeEnum getApproveFlowEnum(String enumName) {
        return Arrays.stream(values())
                .filter(item -> StringUtils.equals(enumName, item.name()))
                .findFirst()
                .orElseThrow(() -> new NoSuchElementException(MsgUtils.getMessage(APPROVAL_FLOW_NOT_FOUND)));
    }
}
