package com.zte.iccp.itech.extension.common.helper;

import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.paas.lcap.component.util.BillNumberGenerateUtil;
import com.zte.paas.lcap.ddm.common.api.dto.BillNumberGeneratorDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 获取任务单标号
 *
 * <AUTHOR>
 * @create 2025/6/16 下午3:06
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class TaskNoHelper {

    /**
     * 技术管理任务编码code
     */
    public final static String TECHNOLOGY_MANAGEMENT_TASK_CODE = "iTechCloud_MT_ticket_number";

    /**
     * 网络变更单任务编码code
     */
    public final static String NETWORK_CHANGE_TASK_CODE = "iTechCloud_OC_ticket_number";

    /**
     * 创建任务单号编码
     */
    public static String createTaskNo(String taskCode) {
        BillNumberGeneratorDTO billNumberGeneratorDTO = new BillNumberGeneratorDTO();
        billNumberGeneratorDTO.setRuleCode(taskCode);
        List<BillNumberGeneratorDTO.Param> params = new ArrayList<>();
        BillNumberGeneratorDTO.Param param = new BillNumberGeneratorDTO.Param();
        param.setCount(1);
        params.add(param);
        billNumberGeneratorDTO.setParams(params);
        List<List<String>> coNos = BillNumberGenerateUtil.generateBatch(billNumberGeneratorDTO);
        if (CollectionUtils.isEmpty(coNos) || CollectionUtils.isEmpty(coNos.get(0))) {
            throw new LcapBusiException("GetTaskNo ERROR!");
        }
        return coNos.get(0).get(0);
    }
}
