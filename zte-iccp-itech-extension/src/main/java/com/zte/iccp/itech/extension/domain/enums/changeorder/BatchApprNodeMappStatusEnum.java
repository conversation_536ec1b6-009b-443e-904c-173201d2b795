package com.zte.iccp.itech.extension.domain.enums.changeorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 批次审批节点映射状态枚举
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/2/7
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum BatchApprNodeMappStatusEnum {

    /**
     *  申请人确认操作计划   -- 待发通告
     */
    PENDING_NOTIFICATION(Lists.newArrayList(ApproveNodeEnum.PENDING_NOTIFICATION), AssignmentStatusEnum.PENDING_NOTIFICATION,
            "Pending_Notify_Advanced_Container_IP", "Pending_Notify_Iframe",
            "PAGE1093829194329612288","title.batch.pending.notify"),

    /**
     *  操作取消审核
     */
    OPERATION_CANCEL_REVIEW(Lists.newArrayList(ApproveNodeEnum.OPERATION_CANCEL_REVIEW), AssignmentStatusEnum.OPERATION_CANCEL_REVIEW,
            "Cancel_Review_Advanced_Container_IP", "Cancel_Review_Iframe",
            "PAGE1109043122941095937","title.batch.cancel.review"),

    /**
     *  通告审核  -- 通告审核
     */
    NOTIFICATION_UNDER_REVIEW(Lists.newArrayList(ApproveNodeEnum.REMOTE_CENTER_OWNER,
            ApproveNodeEnum.CHANGED_BY_REP_PROD_CHIEF,
            ApproveNodeEnum.ADMIN_REP_PROD_CHIEF,
            ApproveNodeEnum.ADMIN_NET_PROD_DIR,
            ApproveNodeEnum.ADMIN_REP_DEPUTY_MNG,
            ApproveNodeEnum.ADMIN_NET_DEPT_MNG,
            ApproveNodeEnum.ADMIN_NETSERVICE_LV4,
            ApproveNodeEnum.ADMIN_NETSERVICE_LV3,
            ApproveNodeEnum.ADMIN_RD_DEPT_LV3,
            ApproveNodeEnum.ADMIN_ENG_SERVICE3,
            ApproveNodeEnum.BATCH_INTL_ADMIN_APPROVAL,
            ApproveNodeEnum.ADMIN_DIR_TELE_SERVICE), AssignmentStatusEnum.NOTIFICATION_UNDER_REVIEW,
            "Columnslayout_Notify_Review", "Notify_Under_Review_Iframe","PAGE1089571632839122945",""),

    /**
     *  反馈操作结果  -- 反馈操作结果
     */
    RESULT_TOBE_BACK(Lists.newArrayList(ApproveNodeEnum.RESULT_TOBE_BACK),
            AssignmentStatusEnum.RESULT_TOBE_BACK,
            "Result_Tobe_Back_Advanced_Container_IP", "Result_Tobe_Back_Iframe",
            "PAGE1094641683987279873","title.batch.result.tobe.back"),

    /**
     *  操作结果审核  -- 操作结果审核
     */
    RESULT_UNDER_REVIEW(Lists.newArrayList(ApproveNodeEnum.TD_NET_DEPT_APPROVE, ApproveNodeEnum.NET_SERVICE_DEPT_APP),
            AssignmentStatusEnum.RESULT_UNDER_REVIEW,
            "Result_Under_Review_Advanced_Container_IP", "Result_Under_Review_Iframe",
            "PAGE1089570554726834177","title.batch.result.under.review"),

    /**
     *  合作方 - 申请人确认操作计划   -- 待发通告
     */
    PARTNER_PENDING_NOTIFICATION(Lists.newArrayList(PartnerApproveNodeEnum.PENDING_NOTIFICATION), AssignmentStatusEnum.PENDING_NOTIFICATION,
            "Pending_Notify_Advanced_Container_IP", "Pending_Notify_Iframe",
            "PAGE1101083910815916032","title.batch.pending.notify"),

    /**
     *  合作方 - 操作取消审核
     */
    PARTNER_OPERATION_CANCEL_REVIEW(Lists.newArrayList(PartnerApproveNodeEnum.OPERATION_CANCEL_REVIEW), AssignmentStatusEnum.OPERATION_CANCEL_REVIEW,
            "Cancel_Review_Advanced_Container_IP", "Cancel_Review_Iframe",
            "PAGE1109854632070934529","title.batch.cancel.review"),
    /**
     *  合作方 - 通告审核  -- 通告审核
     */
    PARTNER_NOTIFICATION_UNDER_REVIEW(Lists.newArrayList(PartnerApproveNodeEnum.PARTNER_OFFICE_PD_APPROVAL,
            PartnerApproveNodeEnum.PARTNER_OFFICE_PROD_CHIEF_APPROVAL,
            PartnerApproveNodeEnum.PARTNER_OPERATION_PLAN_APPROVAL), AssignmentStatusEnum.NOTIFICATION_UNDER_REVIEW,
            "Columnslayout_Notify_Review", "Notify_Under_Review_Iframe","PAGE1101106079142813696",""),

    /**
     *  合作方 - 反馈操作结果  -- 反馈操作结果
     */
    PARTNER_RESULT_TOBE_BACK(Lists.newArrayList(PartnerApproveNodeEnum.RESULT_TOBE_BACK),
            AssignmentStatusEnum.RESULT_TOBE_BACK,
            "Result_Tobe_Back_Advanced_Container_IP", "Result_Tobe_Back_Iframe",
            "PAGE1101143959609319424","title.batch.result.tobe.back"),

    /**
     *  合作方 - 操作结果审核  -- 操作结果审核
     */
    PARTNER_RESULT_UNDER_REVIEW(Lists.newArrayList(PartnerApproveNodeEnum.PARTNER_NET_OWNER_APPROVAL),
            AssignmentStatusEnum.RESULT_UNDER_REVIEW,
            "Result_Under_Review_Advanced_Container_IP", "Result_Under_Review_Iframe",
            "PAGE1101149642249760768","title.batch.result.under.review"),
    ;

    /**
     * 审批节点
     */
    private final List<SingletonTextValuePairsProvider> pairsProviders;

    /**
     * 任务状态
     */
    private final AssignmentStatusEnum assignmentStatusEnum;

    /**
     * 高级容器CID
     */
    private final String AdvContainerCid;

    /**
     * iframeCID
     */
    private final String iframeCid;

    /**
     * 跳转页面id
     */
    public final String pageId;

    /**
     * 高级容器标题
     */
    private final String titleMsg;

    /**
     * 根据审批节点获取对应的高级容器CID、IframeCID和页面ID
     *
     * @param pairsProvider 审批节点
     * @return BatchApprNodeMappStatusEnum
     */
    public static BatchApprNodeMappStatusEnum fromValue(SingletonTextValuePairsProvider pairsProvider) {
        return Arrays.stream(BatchApprNodeMappStatusEnum.values())
                .filter(item -> item.getPairsProviders().contains(pairsProvider))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取状态对应的高级容器原始标题
     *
     * @param lang 语言
     * @return 高级容器标题
     */
    public String getTitleMsg(String lang) {
        return MsgUtils.getLangMessage(lang, this.titleMsg);
    }

}
