package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Getter
@Setter
@BaseEntity.Info("standard_network_set")
public class StandardNetworkSetNetworkFieldEntity extends BaseEntity {

    @JsonProperty(value = "network")
    @ApiModelProperty("网络")
    private List<TextValueTypePair> network;

    @Getter
    @Setter
    public static class TextValueTypePair {
        private MultiLangTextType text;
        private String value;

        public TextValueTypePair(MultiLangTextType text, String value) {
            this.text = text;
            this.value = value;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class MultiLangTextType {

        @JsonProperty(value = "zh_CN")
        private String zhCN;

        @JsonProperty(value = "en_US")
        private String enUS;

        private String type;
    }

    public static List<TextValueTypePair> buildList(String value, String zhCn, String enUs, String type) {
        return new ArrayList<>(Collections.singletonList(new TextValueTypePair(
                new MultiLangTextType(zhCn, enUs, type),
                value
        )));
    }
}

