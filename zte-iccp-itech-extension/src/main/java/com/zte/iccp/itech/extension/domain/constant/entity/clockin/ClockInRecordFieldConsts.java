package com.zte.iccp.itech.extension.domain.constant.entity.clockin;

import lombok.AccessLevel;
import lombok.NoArgsConstructor; /**
 * <AUTHOR>
 * @since 2024/09/06
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ClockInRecordFieldConsts {
    public static final String CLOCK_IN_TIME = "clock_in_time";

    public static final String CLOCK_IN_OPTION = "clock_in_option";

    public static final String DESCRIPTION = "description";

    public static final String PHOTOS = "photos";

    public static final String REVOKED = "revoked";

    public static final String LONGITUDE = "longitude";

    public static final String LATITUDE = "latitude";
}
