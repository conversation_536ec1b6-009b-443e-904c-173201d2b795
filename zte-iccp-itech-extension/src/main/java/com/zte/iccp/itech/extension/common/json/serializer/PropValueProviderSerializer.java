package com.zte.iccp.itech.extension.common.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zte.iccp.itech.extension.common.json.provider.PropValueProvider;
import lombok.SneakyThrows;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/07
 */
@SuppressWarnings("rawtypes")
public class PropValueProviderSerializer extends JsonSerializer<PropValueProvider> {

    @SneakyThrows
    @Override
    public void serialize(PropValueProvider provider, JsonGenerator gen, SerializerProvider serializers) {
        gen.writeObject(provider.getPropValue());
    }
}
