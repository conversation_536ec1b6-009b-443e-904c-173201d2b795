package com.zte.iccp.itech.extension.domain.enums;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 上午10:03
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum AiProductTypeEnum {
    /**
     * RAN
     */
    RAN("RAN", Lists.newArrayList("ran.prodIdPath")),
    /**
     * 算力及核心网
     */
    CCN("CCN", Lists.newArrayList("ccn.prodIdPath")),
    /**
     * 承载网
     */
    BN("BN", Lists.newArrayList("bn.prodIdPath")),
    /**
     * 固网及多媒体
     */
    FM("FM", Lists.newArrayList("fm.prodIdPath")),
    /**
     * 通信能源
     */
    TE("TE", Lists.newArrayList("te.prodIdPath")),
    /**
     * 视频
     */
    VD("VD", Lists.newArrayList("vd.prodIdPath")),
    ;

    private final String code;

    private final List<String> prodIdPaths;

    public static String getCodeByProductId(String productId) {
        if (StringUtils.isBlank(productId)) {
            return null;
        }

        for (AiProductTypeEnum aiProductTypeEnum : AiProductTypeEnum.values()) {
            if (ConfigHelper.get(aiProductTypeEnum.getProdIdPaths()).stream().anyMatch(productId::startsWith)) {
                return aiProductTypeEnum.getCode();
            }
        }

        return null;
    }

}
