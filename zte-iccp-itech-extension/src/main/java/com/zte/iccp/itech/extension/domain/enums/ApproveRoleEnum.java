package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/11
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ApproveRoleEnum implements SingletonTextValuePairsProvider {
    /** 代表处方案审核人 */
    REPRESENTATIVE_OFFICE_SOLUTION_REVIEWER("代表处方案审核人", "Representative Office Solution Reviewer"),

    /** 办事处产品科长/项目TD */
    PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE_PROJECT_TD("办事处产品科长/项目TD", "Product Section Chief of The Representative Office Project Td"),

    /** 办事处副经理/工服处经理 */
    DEPUTY_MANAGER_OF_THE_REPRESENTATIVE_OFFICE_MANAGER_OF_THE_ENGINEERING_SERVICE_OFFICE("办事处副经理/工服处经理", "Deputy Manager of The Representative Office Manager of The Engineering Service Office"),

    /** 代表处数据维护授权 */
    REPRESENTATIVE_OFFICE_DATA_MAINTENANCE_AUTHORIZATION("代表处数据维护授权", "Representative Office Data Maintenance Authorization"),

    /** 网络部 */
    NETWORK_DEPT("网络部", "Network Department"),

    /** 研发部 */
    R_AND_D_DEP("研发部", "R&D Department"),

    /** TD/科长 */
    TD_SECTION_CHIEF("TD/科长", "Td Section Chief"),

    /** PD */
    PD("PD", "PD"),

    /** 工服处经理 */
    MANAGER_OF_THE_ENGINEERING_SERVICE_OFFICE("工服处经理", "Manager of The Engineering Service Office"),

    /** 战区部长 */
    DIRECTOR_OF_THE_WAR_ZONE("战区部长", "Director of The War Zone"),

    /** 国代 */
    MANAGING_DIRECTOR("国代", "Managing Director"),

    /** 工服分部部长 */
    DIRECTOR_OF_THE_ENGINEERING_SERVICE_BRANCH("工服分部部长", "Director of The Engineering Service Branch"),

    /** 产品总工 */
    CHIEF_PRODUCT_ENGINEER("产品总工", "Chief Product Engineer"),

    /** 产品总监 */
    PRODUCT_DIRECTOR("产品总监", "Product Director"),

    /** 产品技术交付部部长 */
    DIRECTOR_OF_THE_PRODUCT_TECHNOLOGY_DELIVERY_DEPT("产品技术交付部部长", "Director of The Product Technology Delivery Dept"),

    /** 网服部部长/网研院院长 */
    DIRECTOR_OF_THE_NETWORK_SERVICE_DEPT_DEAN_OF_THE_NETWORK_R_AND_D_INSTITUTE("网服部部长/网研院院长", "Director of The Network Service Dept Dean of The Network R&D Institute"),

    /** 网研院国际产品总监 */
    INTERNATIONAL_PRODUCT_DIRECTOR_OF_THE_NETWORK_R_AND_D_INSTITUTE("网研院国际产品总监", "International Product Director of The Network R&D Institute"),

    /** 研发中心主任 */
    DIRECTOR_OF_THE_R_AND_D_CENTER("研发中心主任", "Director of The R&D Center"),

    /** 办事处产品科长 */
    PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE("办事处产品科长", "Product Section Chief of The Representative Office"),

    /** 网络服务处总监 */
    NETWORK_SERVICE_OFFICE_DIRECTOR("网络服务处总监", "Network Service Office Director"),

    /** 网络服务处移动总监 */
    NETWORK_SERVICE_OFFICE_MOBILE_DIRECTOR("网络服务处移动总监", "Network Service Office Mobile Director"),

    /** 网络服务处联通总监 */
    NETWORK_SERVICE_OFFICE_CHINA_UNICOM_DIRECTOR("网络服务处联通总监", "Network Service Office China Unicom Director"),

    /** 网络服务处电信总监 */
    NETWORK_SERVICE_OFFICE_DIRECTOR_OF_CHINA_TELECOM("网络服务处电信总监", "Network Service Office Director of China Telecom"),

    /** 电信服务总监 */
    DIRECTOR_OF_TELECOM_SERVICES("电信服务总监", "Director of Telecom Services"),

    /** 办事处副经理 */
    DEPUTY_MANAGER_OF_THE_REPRESENTATIVE_OFFICE("办事处副经理", "Deputy Manager of The Representative Office"),

    /** 电信集团责任经理 */
    RESPONSIBLE_MANAGER_OF_CHINA_TELECOM("电信集团责任经理", "Responsible Manager of China Telecom"),

    /** 网络服务处经理 */
    NETWORK_SERVICE_OFFICE_MANAGER("网络服务处经理", "Network Service Office Manager"),

    /** 网络服务部部长 */
    DIRECTOR_OF_THE_NETWORK_SERVICE_DEPT("网络服务部部长", "Director of The Network Service Dept"),

    /** 产品经理 */
    PRODUCT_MANAGER("产品经理", "Product Manager"),

    /** 网络服务处总经理（即工服三部部长） */
    NETWORK_SERVICE_DEPT_GENERAL_MANAGER("网络服务处总经理", "Network Services Department General Manager"),

    /** 综合交付部部长 */
    SYNTH_DELIV_DEPT_MANAGER("综合交付部部长", "Synthesis Delivery Department Manager", "ORG2223815", "J000026250"),

    /** 技术交付部主任 */
    TECH_DELIV_DEPT_DIRECTOR("技术交付部主任", "Technical Delivery Department Director", "ORG2224922", "J000026245"),

    /** 办事处群经理 */
    GROUP_MANAGER_OF_THE_REPRESENTATIVE_OFFICE("办事处群经理", "Group Manager of The Representative Office"),

    /** 网研院院长 */
    DIRECTOR_OF_THE_INTERNET_RESEARCH_INSTITUTE("网研院院长", "Director of The Internet Research Institute"),

    /** 网络部总工 */
    NETWORK_CHIEF_ENGINEER("网络部总工", "Network Chief Engineer"),

    /** 网络变更报表数据授权 */
    NETWORK_CHANGE_REPORT_DATA_AUTHORIZATION("网络变更报表数据授权", "Network Change Report Data Authorization"),

    /** 国内故障管理经理 */
    DOMESTIC_FAULT_MANAGEMENT_MANAGER("国内故障管理经理", "Domestic Fault Management Manager"),

    /** 国际故障管理经理 */
    INTERNATIONAL_FAULT_MANAGEMENT_MANAGER("国际故障管理经理", "International Fault Management Manager"),

    /**
     * 安全总监
     */
    SECURITY_DIRECTOR("安全总监", "Security Director"),
    ;

    private final String zhCn;

    private final String enUs;

    private final String orgId;

    private final String roleId;

    ApproveRoleEnum(String zhCn, String enUs) {
        this(zhCn, enUs, null, null);
    }

    @Override
    public String getValue() {
        return name();
    }

    public static ApproveRoleEnum fromValue(String name) {
        return Arrays.stream(values()).filter(approveRole -> name.equals(approveRole.name())).findFirst().orElse(null);
    }

    public static ApproveRoleEnum fromNameCn(String nameCn) {
        return Arrays.stream(values()).filter(approveRole -> nameCn.equals(approveRole.getZhCn())).findFirst().orElse(null);
    }
}
