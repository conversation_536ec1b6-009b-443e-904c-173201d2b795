package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/03
 */
public class CustomerIdDeserializer extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        Object o = p.getCodec().readValue(p, Object.class);
        if (o instanceof String) {
            return (String) o;
        }

        if (o instanceof Map) {
            //noinspection unchecked
            Map<String, Object> map = (Map<String, Object>) o;
            return (String) map.get("accountNum");
        }

        return null;
    }
}
