package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import rx.functions.Func1;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
/**
 * <AUTHOR> 10284287
 * @since 2024/05/28
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CollectionUtilsEx {

    public static <T> T single(Collection<T> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            return null;
        }

        if (collection.size() > 1) {
            throw new IllegalArgumentException("collection not single");
        }

        return collection.iterator().next();
    }

    /* 有重复的字符串*/
    public static boolean isRepeated(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        return list.size() != list.stream()
                .distinct()
                .collect(Collectors.toList())
                .size();
    }

    public static <T, R> R single(Collection<T> collection, Func1<T, R> func) {
        T t = single(collection);
        return t == null ? null : func.call(t);
    }

    /*
     * 拷贝集合
     * */
    public static <S, T> List<T> copyListProperties(List<S> sourceList, Class<T> targetClass) {
        List<T> targetList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return targetList;
        }
        try {
            for (S sourceItem : sourceList) {
                T targetItem = targetClass.newInstance();
                BeanUtils.copyProperties(sourceItem, targetItem);
                targetList.add(targetItem);
            }
        } catch (Exception e) {
        }
        return targetList;
    }
}