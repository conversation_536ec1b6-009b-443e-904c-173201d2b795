package com.zte.iccp.itech.extension.domain.constant;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;

/**
 * 公共常量
 */
public class CommonConstants {

    public static final String TYPE = "type";

    public static final String I18N = "i18n";

    public static final String TIPS = "tips";

    public static final String TIME_ZONE_PREFIX = "GMT";

    public static final String TIME_ZONE_PREFIX_UTC = "UTC";

    /**
     * 弹出页面底部确认按钮，对应在该页面自定义操作的编码
     */
    public static final String OK = "ok";

    public static final String STRING = "String";

    // ============= 基本数量 ==============
    /**
     * 默认分页数
     */
    public static final Integer DEFAULT_PAGE_NO = 1;

    /**
     * 默认分页大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 100;

    /**
     * 默认空白页总量
     */
    public static final Integer DEFAULT_EMPTY_TOTAL = 0;

    /**
     * Integer 8
     */
    public static final Integer INTEGER_EIGHT = 8;

    /** 最大循环次数 */
    public static final Integer MAX_CYCLE_TIMES = 1000;

    /**
     * Integer 0
     */
    public static final Integer INTEGER_NEGATIVE_1 = -1;

    /**
     * Integer 0
     */
    public static final Integer INTEGER_ZERO = 0;

    /**
     * Integer 1
     */
    public static final Integer INTEGER_ONE = 1;

    /** Integer 5000 */
    public static final Integer INTEGER_FIVE_THOUSAND = 5000;

    /** Integer 10000 */
    public static final Integer INTEGER_TEN_THOUSAND = 10000;

    /**
     * Integer 60000
     */
    public static final Integer INTEGER_SIXTY_THOUSAND = 60000;

    /** Integer 100000 */
    public static final Integer INTEGER_ONE_HUNDRED_THOUSAND = 100000;

    /**
     * Double 0.0
     */
    public static final Double DOUBLE_ZERO =0.0;

    /**
     * Double 1.0
     */
    public static final Double DOUBLE_ONE =1.0;

    /**
     * Double 1.5
     */
    public static final Double DOUBLE_THREE_QUARTERS =1.5;

    /**
     * Double 2.0
     */
    public static final Double DOUBLE_TWO =2.0;

    /**
     * Double 3
     */
    public static final Double DOUBLE_THREE =3.0;

    /**
     * Integer 2
     */
    public static final Integer INTEGER_TWO = 2;

    /**
     * Integer 3
     */
    public static final Integer INTEGER_THREE = 3;

    /**
     * Integer 4
     */
    public static final Integer INTEGER_FOUR = 4;

    /**
     * Integer 5
     */
    public static final Integer INTEGER_FIVE = 5;

    /**
     * Integer 6
     */
    public static final Integer INTEGER_SIX = 6;

    /**
     * Integer 7
     */
    public static final Integer INTEGER_SEVEN = 7;

    /**
     * Integer 10
     */
    public static final Integer INTEGER_TEN = 10;

    /**
     * Integer 15
     */
    public static final Integer INTEGER_FIFTEEN = 15;

    /**
     * Integer 50
     */
    public static final Integer INTEGER_FIFTY = 50;

    /**
     * Integer 20
     */
    public static final Integer INTEGER_TWENTY = 20;

    /** Integer 90 */
    public static final Integer INTEGER_NINETY = 90;

    /**
     * Integer 999
     */
    public static final Integer INTEGER_999 = 999;

    /**
     * Integer 100
     */
    public static final Integer INTEGER_ONE_HUNDRED = 100;

    /**
     * Integer 200
     */
    public static final Integer INTEGER_200 = 200;

    /**
     * Integer 500
     */
    public static final Integer INTEGER_500 = 500;

    /**
     * Integer 2000
     */
    public static final Integer INTEGER_2000 = 2000;

    /**
     * BigDecimal 1024
     */
    public static final BigDecimal BIG_DECIMAL_1024 = new BigDecimal(1024 * 1024);

    /** 组织树层级 - 营销 */
    public static final Integer LEVEL_ORGANIZATION_MARKETING = 2;

    /** 组织树层级 - 代表处 */
    public static final Integer LEVEL_ORGANIZATION_REPRESENTATIVE_OFFICE = 4;

    /** 产品树层级 - 产品经营团队 */
    public static final Integer LEVEL_PRODUCT_MANAGEMENT_TEAM = 1;

    /** 产品树层级 - 产品小类 */
    public static final Integer LEVEL_PRODUCT_SUBCLASS = 4;

    /** 产品树层级 - 产品型号 */
    public static final Integer LEVEL_PRODUCT_MODEL = 5;

    // ============= 基本命名 ==============
    /**
     * 页码
     */
    public static final String PAGE_NO = "pageNo";

    /**
     * 页码2,低码固定的名字
     */
    public static final String PAGE_NO_2 = "pageNumber";

    /**
     * 页容
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 主键
     */
    public static final String PK_ID = "pkId";

    /**
     * 键
     */
    public static final String KEY = "key";

    /**
     * 值
     */
    public static final String VALUE = "value";

    /**
     * 内容（提示语等）
     */
    public static final String CONTENT = "content";

    /**
     * 记录
     */
    public static final String RECORDS = "records";

    /**
     * 数据
     */
    public static final String DATA = "data";

    /**
     * 已选行
     */
    public static final String SELECTION_ROWS = "selectionRows";

    /**
     * list
     */
    public static final String LIST = "list";

    /**
     * totalRecord
     */
    public static final String TOTAL_RECORD = "totalRecord";

    /**
     * OPTIONS
     */
    public static final String OPTIONS = "options";

    /**
     * 目标cid
     */
    public static final String TARGET = "target";

    /**
     * 过滤查询条件参数
     */
    public static final String FILTER_KEY = "filterKey";

    /**
     * 操作key：search和appendOptions
     */
    public static final String OPERATION_KEY = "operationKey";

    /**
     * DATA_SOURCE
     */
    public static final String DATA_SOURCE = "dataSource" ;

    /**
     * 是否必填属性标志位
     */
    public static final String REQUIRED = "required";
    /**
     * 快码值
     */
    public static final String LOOKUPCODE = "lookupCode";

    /**
     * 操作 - 点击
     */
    public static final String OPERATION_CLICK = "click";

    /**
     * 操作 - 查询
     */
    public static final String OPERATION_SEARCH = "search";

    /**
     * 操作 - 新建
     */
    public static final String OPERATION_CREATE = "create";

    /**
     * 操作 - 浏览
     */
    public static final String OPERATION_VIEW = "view";

    /** 操作 - 转交 */
    public static final String OPERATION_TRANSFER = "transfer";

    /** 操作 - 确认 */
    public static final String OPERATION_CONFIRM = "confirm";

    /** 批次-操作取消绑定动作cid */
    public static final String TASK_CANCEL = "task_cancel";

    /** 操作 - 编辑 */
    public static final String OPERATION_EDIT = "edit";

    /** 跳转参数 - 行ID */
    public static final String ROW_ID = "rowId";

    /** 跳转参数 - 父页面标识 */
    public static final String PARENT_PAGE = "parentPage";

    /** 跳转参数 - 页面ID */
    public static final String PAGE_ID = "pageId";

    /** 跳转参数 - 页面标题 */
    public static final String PAGE_TITLE = "pageTitle";

    /** 跳转参数 - 数据来源 */
    public static final String LINK_SOURCE = "source";

    /** 跳转参数 - 主产品 */
    public static final String IS_MAIN_PRODUCT = "isMainProduct";

    /** url */
    public static final String URL = "url";

    /** 跳转参数 - 实体对象编码 */
    public static final String BIZ_OBJ_CODE = "bizObjectCode";

    /** 跳转参数 - 页面状态 */
    public static final String PAGE_STATUS = "pageStatus";

    /** 跳转参数 - 是否隐藏操作项 */
    public static final String HIDDEN_OPERATION = "hiddenOperation";

    /** 跳转参数 - 是否全屏 */
    public static final String FULL_SCREEN = "fullScreen";

    /** 跳转参数 - 是否复制 */
    public static final String IS_COPY = "is_copy";

    /** 跳转参数 - 页面展示类型 */
    public static final String OPEN_TYPE = "openType";

    /** 跳转参数 - 实例ID */
    public static final String INSTANCE_ID = "instanceId";

    /** 指令 - 额外操作 */
    public static final String EMIT_ACTION = "emitAction";

    /** 指令 - 数据来源 */
    public static final String RESP = "resp";

    /** 指令 - 来源类型 */
    public static final String RESP_TYPE = "respType";

    /** 指令 - 初始化 */
    public static final String INIT = "init";

    // ============= 特殊符号 ==============
    /**
     * 逗号
     */
    public static final String COMMA = ",";

    /** 正斜杠 */
    public static final String FORWARD_SLASH = "/";

    /** 正斜杠（转义符） */
    public static final String FORWARD_SLASH_TRANSFER = "%2F";

    /** 与 */
    public static final String AND = "&";

    /** 与（转义符） */
    public static final String AND_TRANSFER = "%26";

    /** 井号 */
    public static final String WELL_NUMBER = "#";

    /** 井号（转义符） */
    public static final String WELL_NUMBER_TRANSFER = "%23";

    /** 问号（通配符） */
    public static final String QUESTION_MARK_REGEX = "\\?";

    /** 问号（通配符） */
    public static final String QUESTION_MARK_TRANSFER = "%3F";

    /**
     * 日期格式yyyy-MM-dd
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /** 百分号 */
    public static final String PERCENT = "%";

    public static final String PERCENT_LIKE = "%%%s%%";

    /** 单引号 */
    public static final String SINGLE_QUOTES = "'";

    /** 短横线 */
    public static final String HYPHEN = "-";

    /** 特殊短横线 */
    public static final String SPECIAL_HYPHEN = "－";

    /**
     * 下划线
     */
    public static final String UNDER_SCORE = "_";

    /**
     * 空格
     */
    public static final String SPACE = " ";

    /**
     * 末尾"/"匹配
     */
    public static final String REGEX_TRAILING_SLASH = "/$";

    /** 等于符号 */
    public static final String EQUAL_SYMBOL = "=";

    /** 等于（转义符） */
    public static final String EQUAL_TRANSFER = "%3D";

    /**
     * 英文分号
     */
    public static final String SEMICOLON = ";";

    /**
     * 左括号
     */
    public static final String LEFT_BRACKET = "(";

    /**
     * 右括号
     */
    public static final String RIGHT_BRACKET = ")";

    /** 左中括号 */
    public static final String LEFT_SQUARE_BRACKET = "【";

    /** 右中括号 */
    public static final String RIGHT_SQUARE_BRACKET = "】";

    /** 左中括号 */
    public static final String LEFT_CURLY_BRACE = "{";

    /** 退服-中文 */
    public static final String OUT_SERVICE_CN ="（退服）";

    /** 退服-英文 */
    public static final String OUT_SERVICE_EN ="(Out of Service)";

    /**
     * 额外的引号
     */
    public static final String QUOTATION_MARKS_STRING = "\"";

    /**
     * 空字符串
     */
    public static final String EMPTY_STRING = "";

    /**
     * 是
     */
    public static final String Y = "Y";

    /**
     * 否
     */
    public static final String N = "N";

    /**
     * 有效标识
     */
    public static final String ENABLED_FLAG = "Y";

    /**
     * 无效标识
     */
    public static final String DISABLED_FLAG = "N";

    /**
     * 待操作执行 - 取消标识
     */
    public static final String CANCEL_FLAG = "Q";

    // ============= 特殊命名 ==============
    /**
     * 有效
     */
    public static final String ENABLED = "ENABLED";

    /**
     * 无效
     */
    public static final String DISABLED = "DISABLED";

    /**
     * 普通
     */
    public static final String NORMAL = "NORMAL";

    /**
     * 只读
     */
    public static final String READONLY = "READONLY";

    /**
     * defaultChecked
     */
    public static final String DEFAULT_CHECKED = "defaultChecked";

    /**
     * 政企-需要行政审批的星级数量（重要程度+风险评估的星级）
     */
    public static final Integer GOV_ENT_NEED_ADMIN_APPROVAL_STAR_NUM = 5;

    /**
     * 字符串-0
     */
    public static final String STR_ZERO = "0";

    /**
     * 字符串1
     */
    public static final String STR_ONE = "1";

    /**
     * 字符串2
     */
    public static final String STR_TWO = "2";

    /**
     * 字符串3
     */
    public static final String STR_THREE = "3";

    /**
     * 字符串4
     */
    public static final String STR_FOUR = "4";

    /**
     * 字符串5
     */
    public static final String STR_FIVE = "5";

    /**
     * 字符串6
     */
    public static final String STR_SIX = "6";

    /**
     * 字符串12
     */
    public static final String STR_TWELVE = "12";

    /**
     * 快码属性meaning
     */
    public static final String FAST_CODE_MEANING = "meaning";

    /**
     * 自定义组织树/产品树组件属性key--text
     */
    public static final String TEXT = "text";

    /**
     * 中文标识
     */
    public static final String ZH_CN = "zh_CN";

    /**
     * 英文标识
     */
    public static final String EN_US = "en_US";


    /**
     * 流程中心 - 流程变量中文标识
     */
    public static final String FLOW_ZH_CN = ".zh_cn";

    /**
     * 流程中心 - 流程变量中文标识
     */
    public static final String FLOW_EN_US = ".en_us";


    /**
     * 子表单Object对象，其中列属性对应 cid
     */
    public static final String SUB_FORM_COLUMN_MAP_KEY = "columnMap";

    /**
     * 设置隐藏操作key
     */
    public static final String BEHAVIOR = "behavior";

    public static final String PAGINGINFO = "pagingInfo";

    /**
     * 隐藏
     */
    public static final String HIDDEN = "HIDDEN";

    /**
     * 空集合
     */
    public static final String EMPTY_SET = "[]";

    /**
     * 责任单位-【国内营销】idPath
     */
    public static final String DOMESTIC_SALES_ORG_CODE_PATH = "rule.data.key.domestic.sales.orgCodePath";
    /**
     * 责任单位-【工程服务经营部】idPath
     */
    public static final String ENGINEERING_SERVICE_DEPT_ORG_CODE_PATH = "rule.data.key.engineering.service.dept.orgCodePath";
    /**
     * 责任单位-【工程服务经营部-工程服务国内部（测试环境是：工程服务经营部-工程服务三部）】idPath
     */
    public static final String ENGINEERING_SERVICE_DEPT_DOMESTIC_ORG_CODE_PATH = "rule.data.key.engineering.service.dept.domestic.orgCodePath";

    /**
     * 责任单位-【工程服务经营部-工程服务国内部（测试环境是：工程服务经营部-工程服务三部）-网络服务处】idPath
     */
    public static final String ENGINEERING_SERVICE_DEPT_DOMESTIC_NETWORK_ORG_CODE_PATH = "rule.data.key.engineering.service.dept.domestic.network.orgCodePath";
    /**
     * 产品分类-【RAN】idPath
     */
    public static final String RAN_PROD_ID_PATH = "rule.data.key.ran.prodIdPath";

    /**
     * 产品分类-【数据智能及服务】idPath
     */
    public static final String SDI_PROD_ID_PATH = "rule.data.key.sdi.prodIdPath";
    /**
     * 产品分类-【算力及核心网】idPath
     */
    public static final String CCN_PROD_ID_PATH = "rule.data.key.ccn.prodIdPath";

    /**
     * 产品分类-【算力及核心网/核心网】idPath
     */
    public static final String CCN_CCN_PROD_ID_PATH = "rule.data.key.ccn.ccn.prodIdPath";
    /**
     * 产品分类-【算力及核心网/服务器及存储】idPath
     */
    public static final String CCN_SSP_PROD_ID_PATH = "rule.data.key.ccn.ssp.prodIdPath";
    /**
     * 产品分类-【算力及核心网/云及智算】idPath
     */
    public static final String CCN_CLOUD_AI_PROD_ID_PATH = "rule.data.key.ccn.cloud.ai.prodIdPath";

    /**
     * 产品分类-【算力及核心网/集成第三方与组网设备】idPath
     */
    public static final String CCN_INTEGRATION_DEVICES_PROD_ID_PATH = "rule.data.key.ccn.integration.devices.prodIdPath";

    /**
     * 产品分类-【算力及核心网/集成第三方与组网设备/第三方云平台】idPath
     */
    public static final String CCN_INTEGR_CLOUD_PROD_ID_PATH = "rule.data.key.ccn.integr.cloud.prodIdPath";

    /**
     * 产品分类-【算力及核心网/集成第三方与组网设备/其他第三方设备】idPath
     */
    public static final String CCN_INTEGR_DEVICE_PROD_ID_PATH = "rule.data.key.ccn.integr.device.prodIdPath";

    /**
     * 产品分类-【算力及核心网/集成第三方与组网设备/第三方APP】idPath
     */
    public static final String CCN_INTEGR_APP_PROD_ID_PATH = "rule.data.key.ccn.integr.app.prodIdPath";

    /**
     * 产品分类-【算力及核心网/集成第三方与组网设备/第三方安全设备】idPath
     */
    public static final String CCN_INTEGR_SECURITY_PROD_ID_PATH = "rule.data.key.ccn.integr.security.prodIdPath";

    /**
     * 产品分类-【算力及核心网/核心网/IMS-新】idPath
     */
    public static final String CCN_CCN_IMS_PROD_ID_PATH = "rule.data.key.ccn.ccn.ims.data.prodIdPath";
    /**
     * 产品分类-【承载网】idPath
     */
    public static final String BN_PROD_ID_PATH = "rule.data.key.bn.prodIdPath";
    /**
     * 产品分类-【视频-多媒体视讯系统】idPath
     */
    public static final String MMVS_PROD_ID_PATH = "rule.data.key.mmvs.prodIdPath";
    /**
     * 产品分类-【固网及多媒体】idPath
     */
    public static final String FM_PROD_ID_PATH = "rule.data.key.fm.prodIdPath";

    /**
     * itechCloud 服务url
     */
    public static final String SERVICE_HOST = "service.host";

    /**
     * itechCloud 服务name
     */
    public static final String SERVICE_NAME = "service.name";

    /**
     * itechCloud 我的代办后缀
     */
    public static final String SERVICE_MYCHARGE_SUFFIX_PATH = "service.myCharge.suffix.path";

    /**
     * 操作类型分类-配合保障-快码值
     */
    public static final String COOPERATION_GUARANTEE = "COOPERATION_GUARANTEE";

    /**
     * 员工组件_id key
     */
    public static final String ID_KEY = "id";

    /**
     * 员工组件_orgId key
     */
    public static final String ORG_ID_KEY = "orgID";

    /**
     * 员工组件_userName key
     */
    public static final String USER_NAME = "userName";

    /**
     * 技术方案检查checklist所属节点--操作申请
     */
    public static final String NODE_OF_OPERATION_APPLY = "操作申请";

    /**
     * "株式会社"
     */
    public static final String FOREIGN_COMPANY_CORPORATION = "株式会社";

    /**
     * "恒辉设计有限公司"
     */
    public static final String HENGHUI_DESIGN_CO_LTD = "恒辉设计有限公司";

    /**
     * "积奇国际有限公司"
     */
    public static final String JACKIE_INTERNATIONAL_LTD = "积奇国际有限公司";

    /**
     * 工程服务经营部
     */
    public static final String ENGINEERING_SERVICE_DEPT = "工程服务经营部";

    /**
     * 技术交付部
     */
    public static final String TECH_DELIVERY_DEPT = "技术交付部";

    /**
     * 网络服务处
     */
    public static final String NETWORK_SERVICE_OFFICE = "网络服务处";

    /**
     * 股份公司
     */
    public static final String JOINT_STOCK_COMPANY = "股份公司";

    /**
     * 本地
     */
    public static final String LOCAL = "本地";

    /**
     * 第三国
     */
    public static final String THIRD_COMPANY = "第三国";

    /**
     * 中方
     */
    public static final String CHINESE = "中方";

    /**
     * 核心网产品外场质量保证规范动作checklist所属节点--操作申请ccn
     */
    public static final String NODE_OF_OPERATION_APPLY_CCN = "操作申请CCN";

    /**
     * 服务对象唯一标识 colKey
     */
    public static final String COL_KEY = "colKey";
    /**
     * 员工组件_姓名key
     */
    public static final String EMP_NAME_KEY = "empName";

    /**
     * 员工组件_姓名key
     */
    public static final String EMP_NAME_CN_KEY = "empNameCn";

    /**
     * 员工组件_姓名key
     */
    public static final String EMP_NAME_EN_KEY = "empNameEn";

    /**
     * 员工组件_工号key
     */
    public static final String EMP_UIID_KEY = "empUIID";

    /**
     * 员工组件_组织key
     */
    public static final String ORG_NAME_PATH_KEY = "orgNamePath";

    /**
     * 员工组件_组织（中文）key
     */
    public static final String ORG_NAME_PATH_CN_KEY = "orgNamePathCn";

    /**
     * 员工组件_组织（英文）key
     */
    public static final String ORG_NAME_PATH_EN_KEY = "orgNamePathEn";

    /**
     * 自定义组件树分类key
     */
    public static final String TREE_TYPE = "treeType";

    /**
     * 自定义组件树分类value-产品树
     */
    public static final String PRODUCT = "product";

    /**
     * 自定义组件树分类value-组织树
     */
    public static final String ORGANIZATION = "organization";

    /**
     * 自定义组件树分类value-原因归类树
     */
    public static final String REASON = "reason";

    /**
     * 自定义组件树层级
     */
    public static final String TREE_LEVEL = "treeLevel";

    /**
     * 自定义组件树数据
     */
    public static final String TREE_DATA = "treeData";

    /**
     * 自定义组件-进度条 进度属性
     */
    public static final String PROGRESS_BAR_PERCENTAGE = "percentage";

    /**
     * 自定义组件-进度条 状态属性
     */
    public static final String PROGRESS_BAR_STATUS = "status";


    // ============= 技术管理任务【反馈类型】==============
    /**
     * 反馈进展
     */
    public static final String FEEDBACK_PROGRESS = "SAVE";

    /**
     * 申请关闭
     */
    public static final String APPLICATION_DISABLE = "SUBMIT";

    /**
     * 申请废止
     */
    public static final String APPLICATION_REVOKED = "TERMINATE";

    // ============= 技术管理任务【审核结果】==============
    /**
     * 同意
     */
    public static final String PASS = "PASS";

    /**
     * 同意_中文
     */
    public static final String PASS_CN = "同意";

    /**
     * 审核通过_中文
     */
    public static final String PASS_CN_INTEGRATION = "审核通过";

    /**
     * 驳回
     */
    public static final String REJECT = "REJECT";

    /**
     * 驳回_中文
     */
    public static final String REJECT_CN = "不同意";

    /**
     * 审核驳回修改_中文
     */
    public static final String REJECT_CN_INTEGRATION = "审核驳回修改";



    /**
     * 子任务流程编码
     */
    public static final String TECH_MANAGE_SUBTASK = "TECH_MANAGE_SUBTASK";

    /**
     * 技术管理任务-主任务流程编码
     */
    public static final String TECH_MANAGE_FLOW = "TECH_MANAGE_FLOW";

    /**
     * 自定义组件 查看进展的key
     */
    public static final String CHECK_PROGRESS_DATA = "checkProgressData";

    /**
     * 标准方案-版本号前缀
     */
    public static final String STR_V = "V";

    /**
     * 日期区间-开始时间
     */
    public static final String DATERANGE_START = "start";

    /**
     * 日期区间-结束时间
     */
    public static final String DATERANGE_END = "end";

    public static final String BEIJING_TIME_ZONE_PREFIX = "(UTC+08:00)";

    /**
     * 懒加载search重新初始化属性
     */
    public static final String INIT_OPTION_FLAG = "initOptionFlag";

    /**
     * iVersion查询接口-默认参数（iTechCloud作为调用方的token和服务编码，test/user/生产环境通用）
     */
    public static final String I_VERSION_AUTH_VALUE = "33877863B60C67F3A05F21CD4BAC248C";
    public static final String I_VERSION_SYSTEM_NAME = "iTechCloud";
    public static final String I_VERSION_SYSTEM_CODE = "100000455584";
    public static final String I_VERSION_DETAIL_INFO = "releaseVersionDetail";
    public static final String I_VERSION_NAME_KEY = "projectVersionName";
    public static final String I_VERSION_ID_KEY = "versionId";
    public static final String I_VERSION_DEFAULT_CORP_CODE = "0000";

    /**
     * iSupport查询接口-warroomId
     */
    public static final String I_WAR_ROOM_ID = "warroomId";

    /**
     * PDM查询接口-默认参数（AUTH_VALUE，test环境c0e5cab68dbfe3c812d2add62869225d可用，生产环境ddc4c9ce526c30e43ac08a8775dfe654暂未开启）
     */
    public static final String PDM_SYSTEM_CODE = "100000455584";
    public static final String PDM_QUERY_TYPE = "P0001";
    public static final String PDM_QUERY_STATUS = "启用";
    public static final String PDM_QUERY_PARAM_NOS = "NOs";
    public static final String PDM_QUERY_PARAM_INFO_BLOCKS = "infoBlocks";
    public static final String PDM_QUERY_RESPONSE_CHILD_LIST = "ITEM_CHILD_LIST";
    public static final String PDM_QUERY_RESPONSE_OTHER = "other";
    public static final String PDM_QUERY_RESPONSE_TOTAL = "total";
    public static final String PDM_QUERY_RESPONSE_CURRENT = "current";


    public static final String DATE_FORM = "yyyy-MM-dd HH:mm:ss";

    public static final String SIMPLE_DATE_FORM = "yyyy-MM-dd";

    public static final String NO_SEPARATOR_DATE_FORM = "yyyyMMddHHmmss";

    public static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat(DATE_FORM);

    public static final String DATE_SIMPLE_FORM = "yyyyMMdd";

    public static final SimpleDateFormat SIMPLE_DATE = new SimpleDateFormat(SIMPLE_DATE_FORM);

    public static final String FIXED_FIELD_OPERATION_SOLUTION = "operation.scheme";

    public static final String INTERNAL = "internal";

    public static final String CUSTOMER = "customer";

    public static final String FILE_DOWNLOAD_URL_PREFIX = "/v1/app/file/download?fileKey=";

    public static final String DOC_SUFFIX = ".docx";

    public static final String COLON = "：";

    /** 英文冒号 */
    public static final String EN_COLON = ":";

    /** 英文冒号（转义符） */
    public static final String EN_COLON_TRANSFER = "%3A";

    public static final String LINE_FEED = "\\n";

    public static final String ESCAPE_VERTICAL = "\\|";

    public static final String VERTICAL = "|";

    public static final String DASH = "------";

    public static final String NO = "No.";

    public static final String WAVE = "~";

    // ------------------ 提示语等组件插入数据 -------------------//
    public static final String DIV = "<div>";

    public static final String R_DIV = "</div>";

    public static final String A_HREF = "<a href = ";

    public static final String A = "<a>";

    public static final String R_A = "</a>";

    public static final String BR = "<br>";

    public static final String LEFT_ANGLE_BRACKET = "<";

    public static final String RIGHT_ANGLE_BRACKET = ">";

    public static final String SPAN = "<span>";

    public static final String HTML_P ="<p>";

    public static final String HTML_R_P ="</p>";

    public static final String R_SPAN = "</span>";

    public static final String TARGET_BLANK = "target = ";

    public static final String BLANK = "_blank";



    // ------------------ 提示语等组件插入数据 -------------------//
    /**
     * ZXRDC查询接口-默认参数（AUTH_VALUE，SYSTEM_CODE需要单独申请）
     */
    public static final String ZXRDC_AUTH_VALUE = "2668fcc0-2af5-11ef-858c-00163e00a69f";
    public static final String ZXRDC_SYSTEM_CODE = "iTech";
    public static final String ZXRDC_RESPONSE_ROWS = "rows";
    public static final Integer ZXRDC_DEFAULT_QUERY_STID = 12;
    public static final String ZXRDC_DEFAULT_QUERY_TYPE = "Q0000";


    /**
     * 变更单查询标准方案列表的业务入参
     */
    public static final String PARAM_SOLUTION_TYPE = "solutionType";
    public static final String PARAM_AI_FLAG = "aiFlag";
    public static final String PARAM_COUNTRY_CODE = "countryCode";
    public static final String PARAM_OPERATION_TYPE = "operationType";
    public static final String PARAM_PRODUCT_TEAM = "productTeamList";
    public static final String PARAM_PRODUCT_LINE = "productLineList";
    public static final String PARAM_PRODUCT_CATEGORY = "productCategoryList";
    public static final String PARAM_PRODUCT_SUBCATEGORY = "productSubcategoryList";
    public static final String PARAM_LIKE_SYMBOL = "%";

    /*审批流程状态：完成*/
    public static final String COMPLETED_TASK_STATUS = "COMPLETED";

    /*审批流程状态：活动中
    * */
    public static final String ACTIVE_TASK_STATUS = "ACTIVE";

    /*审批流程状态：重新指派
     * */
    public static final String REASSIGN_TASK_STATUS = "REASSIGN";

    public static final String IN = "in";

    public static final String FONT_BOLD = "<span style=\"font-weight:bold;font-size:20px;\">%s</span>";

    //*********** 审批-任务转交 ***********
    /**
     * 原任务处理人
     */
    public static final String ORIGINAL_TASK_HANDLER ="originalTaskHandler";

    /**
     * 转交的新处理人
     */
    public static final String TASK_RECEIVER ="taskReceiver";

    //*********** 改变页面名称 ***********
    public static final String PAGE = "page";

    public static final String TITLE = "title";

    /**
     * 内部变更单前缀：CO
     */
    public static final String CHANGE_ORDER_PREFIX_CO = "CO";

    /**
     * 合作方变更单前缀：HZF
     */
    public static final String PARTNER_CHANGE_ORDER_PREFIX_HZF = "HZF";

    /**
     * 中括号
     */
    public static final String MEDIUM_PARENTHESES_START = "[";

    /**
     * 技术方案检查checkList--产品安全检查--数据编码
     */
    public static final String CHECK_CODE_013 = "check013";

    /**
     *  核心网产品外场质量保证规范动作checkList--IMS License申请--数据编码
     */
    public static final String CHECK_CODE_025 = "check025";

    /**
     *  操作类型分组--升级类--快码值
     */
    public static final String OPERATION_TYPE_UPGRADE_CODE = "UPGRADE";
    /**
     * 成功
     */
    public static final String SUCCESS  = "SUCCESS";
    /**
     * 失败
     */
    public static final String FAIL  = "FAIL";

    /**
     * 员工编号
     */
    public static final String EMP_NO  = "empNo";

    /**
     * 操作传入的参数
     */
    public static final String OPERATION_ARGS  = "operationArgs";

    /**
     * 母版物料传入的类型参数
     */
    public static final String V4_OPERATION_HARDWARE  = "v4OperationHardware";

    /**
     * 是否紧急操作拓展常量（中文）
     */
    public static final String IS_EMERGENCY_OPERATION_EXT_ZH = "（紧急）";

    /**
     * 是否紧急操作拓展常量（英文）
     */
    public static final String IS_EMERGENCY_OPERATION_EXT_EN = "（Emergency）";

    /**
     * 左中括号
     */
    public static final String LEFT_CORE_BRACKET  = "[";

    /**
     * 右中括号
     */
    public static final String RIGHT_CORE_BRACKET = "]";

    /**
     * 变更单审批页面--支持人员子表单--清除行还是默认加一行的标志位--清空子表单
     */
    public static final String SUPPORT_PERSON_TABLE_FLAG_CLEAR = "clear";

    /**
     * 变更单审批页面--支持人员子表单--清除行还是默认加一行的标志位--默认加一行
     */
    public static final String SUPPORT_PERSON_TABLE_FLAG_ONE_LINE = "oneLine";

    /**
     * 故障管理任务详情页面--地铁图组件--对接节点数据的属性名
     */
    public static final String SUBWAY_NODE_DATA_ATTRIBUTE_NAME = "subwayNodeInfo";

    /**
     * 升级操作类型分组
     */
    public static final String UPGRADE_OPERATE_TYPE_GROUP = "UPGRADE";

    /**
     * 网优操作类型分组
     */
    public static final String NETWORK_OPTIMIZATION_OPERATE_TYPE_GROUP = "NETWORK_OPTIMIZATION";

    /**
     * 非升级类操作类型分组
     */
    public static final String NON_UPGRADE_OPERATE_TYPE_GROUP = "NON_UPGRADE";

    /**
     * 升级类操作原因分组
     */
    public static final String UPGRADE_OPERATE_REASON_GROUP = "UPGRADE";

    /**
     * 网优操作原因分组
     */
    public static final String NETWORK_OPTIMIZATION_OPERATE_REASON_GROUP = "NETWORK_OPTIMIZATION";

    /**
     * 非升级类操作类型分组
     */
    public static final String NON_UPGRADE_OPERATE_REASON_GROUP = "NON_UPGRADE";
    /**
     * 多产品联动保障，保障单后缀
     */
    public static final String MULTI_PROD_SUFFIX = "P";

    /**
     * 删除标识
     */
    public static final String DELETE_FLAG = "删除";

    /**
     * 废止标识
     */
    public static final String ABOLISH_FLAG = "废止";

    /**
     * 解除标识
     */
    public static final String REMOVE_FLAG = "解除";

    /**
     * 72小时
     */
    public static final long HOURS_72 = 259200000;

    /**
     * 24小时毫秒
     */
    public static final long HOURS_24_MS = 86400000;

    /**
     * 24*60*60
     */
    public static final long HOURS_24 = 86400;

    /**
     * 5s
     */
    public static final long SECOND_5 = 5;

    /**
     * XLSX
     */
    public static final String XLSX = ".xlsx";

    /**
     * idop的创建页面地址
     */
    public static final String IDOP_CREATE_PAGE_ADDRESS = "idop.create.page.address";

    /**
     * idop的创建页面提示页面
     */
    public static final String IDOP_CREATE_TIPS_PAGE = "idop.create.tips.page";

    /**
     * 紧急操作标记中文Value
     */
    public static final String EMERGENCY_OPERATION_FLAG_ZH = "（紧急）";

    /**
     * 紧急操作标记英文Value
     */
    public static final String EMERGENCY_OPERATION_FLAG_EN = "(Emergency)";

    /**
     * 封网管控期操作标记中文Value
     */
    public static final String CLOSE_OR_CONTROL_OPERATION_FLAG_ZH = "（封网、管控期操作）";

    /**
     * 封网管控期操作标记英文Value
     */
    public static final String CLOSE_OR_CONTROL_OPERATION_FLAG_EN = "(Network Sealing, Control Period)";

    // ====== 国内（中国）国家标识 =========
    public static final String CHINA_COUNTRY_ID = "0001";

    /**
     * SQL分页查询个数1000
     */
    public static final int SQL_MAX_PAGE_SIZE = 1000;

    /*
     * 审批中心查询最大查询400个
     * */
    public static final int APPROVAL_QUERY_MAX_SIZE = 400;

    /**
     * idop 提示校验开关  默认关
     */
    public static final String IDOP_CHECK_ENABLE = "idop.check.enable";

    /**
     *  技术交付云平台
     */
    public static final String ITECHCLOUD_NAME_CN = "技术交付云平台";
    /**
     * 技术交付云平台
     */
    public static final String ITECHCLOUD_NAME_EN = "iTechCloud";
    /**
     * 技术交付云平台
     */
    public static final String ALL_CN = "全部";
    /**
     * 技术交付云平台
     */
    public static final String ALL_EN = "All";

    /**
     * 任务主键ID（父子页面自定义参数）
     */
    public static final String CUSTOM_PARAME_ASSIGNMENT_ID  = "assignmentId";

    /**
     * 打卡单据数据Row（父子页面自定义参数）
     */
    public static final String CLOCK_ROW  = "clockRow";

    /**
     * refresh
     */
    public static final String REFRESH = "refresh";

    /**
     * 复盘单单号前缀
     */
    public static final String CLOCK_IN_REVIEWS_FP  = "FP";

    // ==================   关键词标识（应用于 DataKey）   ==================
    /** 任务状态 */
    public static final String KEY_ASSIGNMENT_STATUS = "assignmentStatus";

    /** 营销 */
    public static final String KEY_MARKETING = "marketing";

    /** 片区 */
    public static final String KEY_REGION = "region";

    /** 代表处（责任单位） */
    public static final String KEY_REPRESENTATIVE_OFFICE = "representativeOffice";

    /** 产品分类 */
    public static final String KEY_PRODUCT_CLASSIFICATION = "productClassification";

    /** 算力及核心网-集成第三方与组网设备 产品线的rule data key */
    public static final String CCN_THIRD_PARTY_RULE_DATA_KEY = "rule.data.key.ccn.integration.devices.prodIdPath";

    /** 乌兹别克工服处orgPathId */
    public static final String UZBEKISTAN_ORG_PATH_ID = "rule.data.key.uzbekistan.orgCodePath";

    /** 国际工服处要开放打卡的orgPathId */
    public static final String INTER_CLOCK_IN_ORG_PATH_IDS = "rule.data.key.internation.clockin.orgCodePath";

    /** 网络 */
    public static final String KEY_NETWORK = "network";

    /** 操作结果 */
    public static final String KEY_OPERATION_RESULT = "operationResult";

    /** 责任人 */
    public static final String KEY_RESPONSIBLE = "responsible";

    /** 当前进展 */
    public static final String KEY_CURRENT_PROGRESS = "currentProgress";

    /** 是否超期 */
    public static final String KEY_OVER_FLAG = "overFlag";

    /** 超期时间 */
    public static final String KEY_OVERDUE_TIME = "overdueTime";

    /** 是否审批任务 */
    public static final String KEY_APPROVAL_TASK_FLAG = "approvalFlag";

    /** 处理时间 */
    public static final String KEY_DEALING_TIME = "dealingTime";

    /** 产品经营团队 */
    public static final String KEY_PRODUCT_OPERATION_TEAM = "productOperationTeam";

    /** 产品线 */
    public static final String KEY_PRODUCT_LINE = "productLine";

    /** 产品大类 */
    public static final String KEY_PRODUCT_CATEGORY = "productCategory";

    /** 产品小类 */
    public static final String KEY_PRODUCT_SUBCATEGORY = "productSubcategory";

    /** 是否有子级节点 */
    public static final String KEY_HAS_CHILDREN = "hasChildren";

    /** 故障管理任务ID */
    public static final String FAULT_ASSIGNMENT_ID = "fault_assignment_id";

    /** 故障管理单ID */
    public static final String FAULT_ORDER_ID = "fault_order_id";

    /**
     * 更变符号
     */
    public static final String CHANGE  = " -> ";

    public static final String PHOTO_JPG_TYPE = "%s.jpg";

    /**
     * 当前步骤
     */
    public static final String CURRENT_STEP = "currentStep";

    /**
     * 配置中心 - 任务审批页面路径
     */
    public static final String APPROVE_HREF_URL = "assignment.approve.pageUrl";

    /**
     * 配置中心 - 任务Iframe页面路径
     */
    public static final String HREF_IFRAME_URL = "assignment.href.iframeUrl";

    public static final String NODE_KEY = "nodeKey";


    /**
     * 路径url拼接使用
     */
    public static final String ZH = "zh";

    /**
     * 路径url拼接使用
     */
    public static final String EN = "en";

    /**
     * 任务类型
     */
    public static final String ASSIGNMENT_TYPE_EXT = "assignmentType";

    /**
     * 时区最大差12小时
     */
    public static final int MAXIMUM_TIME_DIFFERENCE_HOUR = 12;

    /**
     * 超链接
     */
    public static final String HYPERLINK = "hyperlink";

    /**
     * 多选
     */
    public static final String MULTIPLE = "multiple";

    /**
     * 多联动保障单类型
     */
    public static final String GUARANTEE = "GUARANTEE";

    /**
     * 是否打开网络变更单提交校验 默认关
     */
    public static final String CHANGE_NETWORK_CHECK_ENABLE = "change.network.check.enable";

    /**
     * 人员积分校验开关
     */
    public static final String CHECK_PERSONNEL_ENABLE = "check.personnel.enable";

    /**
     * 人员积分校验白名单账号
     */
    public static final String CHECK_PERSONNEL_WHITE = "check.personnel.white";

    /**
     * 工程服务经营部/工程服务政企部/网络服务处
     */
    public static final String ENGINEERING_SERVICE_OFFICE_ORG_CODE_PATH = "rule.data.key.engineering.service.office.orgCodePath";


    /**
     * 蓝色字体
     */
    public static final String BLUE_FONT = "<p style=\"color:#00ABFF;\">%s</p>";

    /**
     * 橙色字体
     */
    public static final String ORANGE_FONT = "<p style=\"color:#FF8A14;\">%s</p>";

    /**
     * 默认字体
     */
    public static final String NORMAL_FONT = "<p>%s</p>";

    /** 查询员工信息  */
    public static final String QUERY_PERSON_GENERAL_INFO = "hr.service.url.queryPersonGeneralInfo";

    /** itech服务 */
    public static final String NETCHANGE_SERVICE_NAME = "zte-iccp-itech-netchange";

    public static final String QUERY_CUSTOMER_INFO_LIST = "crm.service.url.queryCustomerInfoList";
    public static final List<String> CRM_INFO_REQ_CONFIG = Arrays.asList("market","base");

    /**
     * 组织编码 -公司
     */
    public static final String ZTE_ORG_CODE = "ORG0000000";

    /**
     * 网络变更任务同步导出条数上限
     */
    public static final String CHANGE_ORDER_SYNC_EXPORT_LIMIT = "change.order.sync.export.limit";

    /**
     * 国家地区编码-北京
     */
    public static final String AREA_BEIJING_COUNT_CODE ="0001/00010002";

    /**
     * 国家地区编码-上海
     */
    public static final String AREA_SHANGHAI_COUNT_CODE ="0001/00010024";


    /**
     * 产品分类-【载网/IP网络产品/数通产品/高端路由器及BRAS】idPath
     */
    public static final String BN_IP_DATA_BARS_PROD_ID_PATH = "rule.data.key.bn.ip.data.bars.prodIdPath";

    /**
     * 产品分类-【承载网/IP网络产品/数通产品/高端交换机】idPath
     */
    public static final String BN_IP_DATA_SWITCH_PROD_ID_PATH = "rule.data.key.bn.ip.data.switch.prodIdPath";
}
