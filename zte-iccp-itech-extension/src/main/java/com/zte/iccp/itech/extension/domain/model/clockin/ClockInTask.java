package com.zte.iccp.itech.extension.domain.model.clockin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.LookupValueDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.MultiListPropValueProviderSerializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.*;

/**
 * <AUTHOR>
 * @since 2024/09/05
 */
@Getter
@Setter
@BaseEntity.Info("clock_in_task")
public class ClockInTask extends BaseEntity {
    @JsonProperty(value = ENTITY_TYPE)
    private BatchTaskTypeEnum entityType;

    @JsonProperty(value = CHANGE_ORDER_ID)
    private String changeOrderId;

    @JsonProperty(value = BATCH_TASK_ID)
    private String batchTaskId;

    @JsonProperty(value = TASK_TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private ClockInTaskTypeEnum taskType;

    @JsonProperty(value = TASK_STATUS)
    @JsonDeserialize(using = LookupValueDeserializer.class)
    private ClockInOptionEnum taskStatus;

    @JsonProperty(value = ABOLISHED)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private BoolEnum abolished;

    @JsonProperty(value = BATCH_CODE)
    private String batchCode;

    @JsonProperty(value = RESPONSIBLE_DEPT)
    private String responsibleDept;

    @JsonProperty(value = PRODUCT_CLASSIFICATION)
    private String productClassification;

    @JsonProperty(value = OPERATION_SUBJECT)
    private String operationSubject;

    @JsonProperty(value = OPERATION_TYPE)
    private String operationType;

    @JsonProperty(value = OPERATION_LEVEL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private OperationLevelEnum operationLevel;

    @JsonProperty(value = TIME_ZONE)
    @JsonDeserialize(using = LookupValueDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private TimeZoneEnum timeZone;

    @JsonProperty(value = OPERATOR)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee operator;

    @JsonProperty(value = OPERATOR_ROLES)
    @MultiTextValuePairsDeserializer.ItemClass(OperatorRoleEnum.class)
    @JsonDeserialize(using = MultiTextValuePairsDeserializer.class)
    @JsonSerialize(using = MultiListPropValueProviderSerializer.class)
    private List<OperatorRoleEnum> operatorRoles;

    @JsonProperty(value = PLAN_CALL_UTC_TIME)
    private Long planCallUtcTime;

    @ApiModelProperty("批次任务维度最新打卡时间")
    @JsonProperty(value = LAST_CHECK_IN_UTC_TIME_BATCH_TASK)
    private Long lastCheckInUtcTimeBatchTask;

    // BEGIN 操作打卡任务字段
    @JsonProperty(value = PLAN_PREPARE_START_TIME)
    private Date planPrepareStartTime;

    @JsonProperty(value = PLAN_PREPARE_END_TIME)
    private Date planPrepareEndTime;

    @JsonProperty(value = PLAN_EXECUTE_START_TIME)
    private Date planExecuteStartTime;

    @JsonProperty(value = PLAN_EXECUTE_END_TIME)
    private Date planExecuteEndTime;

    @JsonProperty(value = PLAN_TEST_START_TIME)
    private Date planTestStartTime;

    @JsonProperty(value = PLAN_TEST_END_TIME)
    private Date planTestEndTime;
    // END 操作打卡任务字段

    // BEGIN 值守打卡任务字段
    @JsonProperty(value = PLAN_ON_DUTY_START_TIME)
    private Date planOnDutyStartTime;

    @JsonProperty(value = PLAN_ON_DUTY_END_TIME)
    private Date planOnDutyEndTime;


    @JsonProperty(value = TOTAL_ON_DUTY_DURATION)
    private Double totalOnDutyDuration;

    @JsonProperty(value = ON_DUTY_FREQUENCY)
    private Integer onDutyFrequency;

    @JsonProperty(value = STAGE_ON_DUTY_END_TIME)
    private Date stageOnDutyEndTime;

    @JsonProperty(value = ALREADY_ON_DUTY_FREQUENCY)
    private Integer alreadyOnDutyFrequency;

    /**
     * 转交次数
     */
    @JsonProperty(value = TRANSFER_FREQUENCY)
    private Integer transferFrequency;
    // END 值守打卡任务字段

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
