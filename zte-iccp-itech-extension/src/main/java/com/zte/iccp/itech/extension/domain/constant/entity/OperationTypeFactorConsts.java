package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 操作类型系数
 *
 * <AUTHOR> 10335201
 * @date 2024-05-13 下午2:33
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OperationTypeFactorConsts {
    /**
     * 分值
     */
    public static final String SCORE = "score";
    /**
     * 产品经营团队
     */
    public static final String PRODUCT_OPERATION_TEAM = "product_operation_team";
    /**
     * 产品线
     */
    public static final String PRODUCT_LINE = "product_line";
    /**
     * 产品大类
     */
    public static final String PRODUCT_CATEGORY = "product_category";
    /**
     * 产品小类
     */
    public static final String PRODUCT_SUBCATEGORY = "product_subcategory";
    /**
     * 操作类型
     */
    public static final String OPERATION_TYPE = "operation_type";
}
