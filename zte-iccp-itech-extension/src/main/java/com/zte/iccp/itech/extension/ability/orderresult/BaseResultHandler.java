package com.zte.iccp.itech.extension.ability.orderresult;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.common.classloader.CustomClassLoaderHelper;
import lombok.SneakyThrows;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public abstract class BaseResultHandler {

    protected abstract JSONObject doHandle(FlowClient body);

    /**
     * 该方法预留用于通用的流程结束处理逻辑
     */
    public final JSONObject handle(FlowClient body) {
        return doHandle(body);
    }

    @SneakyThrows
    public static BaseResultHandler getInstance(String key) {
        String className = BaseResultHandler.class.getName().replace(
                BaseResultHandler.class.getSimpleName(),
                key + "Handler");
        Class<?> handlerClass = CustomClassLoaderHelper
                .getAppCustomClassLoader(ContextHelper.getTenantId(), ContextHelper.getAppId())
                .loadClass(className);
        return (BaseResultHandler) handlerClass.newInstance();
    }
}
