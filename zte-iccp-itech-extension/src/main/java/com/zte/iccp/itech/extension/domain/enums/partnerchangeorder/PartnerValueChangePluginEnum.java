package com.zte.iccp.itech.extension.domain.enums.partnerchangeorder;


import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange.*;
import com.zte.iccp.itech.extension.plugin.form.partnerchangeorder.valuechange.*;
import com.zte.iccp.itech.extension.plugin.operation.partnerchangeorder.operand.PartnerNetworkNamePluginValue;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;

/**
 * <AUTHOR>
 * @date 2024/5/10 下午5:40
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PartnerValueChangePluginEnum {
    /**
     * 操作类型-联动插件
     */
    OPERATION_TYPE(Lists.newArrayList(FIELD_PRODUCT_CID), new PartnerOperationTypePluginValue()),

    /**
     * 操作类型分组-赋值插件
     */
    OPERATION_TYPE_GROUP(Lists.newArrayList(FIELD_OPERATION_TYPE_CID), new PartnerOperationTypeGroupPluginValue()),

    /**
     * 更新操作阶段打卡子表单里【操作准备-开始时间】、【测试验证-结束时间】两个数据
     */
    OPERATION_PHASE_TIME_UPDATE(Lists.newArrayList(FIELD_OPERATION_START_TIME_CID,FIELD_OPERATION_END_TIME_CID), new PartnerOperationPhaseCheckTimeChangePluginValue()),

    /**
     * 操作阶段打卡表中的操作时长变化时，自动计算开始时间和结束时间
     */
    OPERATION_PHASE_TIME_UPDATE_BY_DURATION(Lists.newArrayList(FIELD_OPERATION_DURATION_CID), new PartnerOperationPhaseCheckTimeChangeByDurationPlugin()),

    /**
     * 操作阶段打卡子表单-打卡责任人自动计算插件
     */
    OPERATION_PHASE_PERSON_UPDATE(Lists.newArrayList(FIELD_OPERATION_LEVEL_CID,OPERATOR_NAME_KEY), new PartnerOperationPhaseCheckPersonPluginValue()),

    /**
     * 重要程度-自动计算插件
     */
    IMPORTANCE(Lists.newArrayList(FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID,
            FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, FIELD_CUSTOMER_ID_CID,
            FIELD_COUNTRY_CID, FIELD_OPERATION_TYPE_CID), new PartnerImportanceFormPluginValue()),

    /**
     * 操作主题-拼接赋值插件
     */
    OPERATION_SUBJECT(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_PRODUCT_CID, FIELD_IS_GOV_ENT_CID,
            FIELD_OPERATION_TYPE_CID, FIELD_AREA_CID, FIELD_PROVINCE_CID, FIELD_COUNTRY_CID,
            FIELD_CUSTOMER_ID_CID, ACCN_TYPE_CID, ChangeOrderFieldConsts.LOGICAL_NE),
            new PartnerOperationSubjectPluginValue()),

    /**
     * 操作等级-自动计算插件
     */
    OPERATION_LEVEL(Lists.newArrayList(FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID,
            FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, FIELD_CUSTOMER_ID_CID,
            FIELD_COUNTRY_CID, FIELD_OPERATION_TYPE_CID,FIELD_SERVICE_DISCONNECT_DURATION_CID), new PartnerOperationLevelPluginValue()),

    /**
     * 风险评估-自动计算插件
     */
    RISK_EVALUATION(
            Lists.newArrayList(FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID, FIELD_OPERATION_TYPE_CID,FIELD_SERVICE_DISCONNECT_DURATION_CID), new PartnerRiskEvaluationPluginValue()),

    /**
     * 网络负责人插件
     */
    NETWORK_RESPONSIBLE_PERSON(Lists.newArrayList(FIELD_NETWORK_NAME), new PartnerNetworkResponsiblePluginValue()),

    /**
     * 操作等级-禁止提交单据提示语插件（操作等级为关键时，禁止分包商提交网络变更单据，保存和提交按钮禁用，提示语提醒）
     */
    OPERATION_LEVEL_MESSAGE(Lists.newArrayList(FIELD_OPERATION_LEVEL_CID), new PartnerForbiddenSubmitPluginValue()),

    /**
     * 当子表单被规则隐藏时，需要清空子表单上的数据
     */
    CLEAR_SUB_FORM_DATA(Lists.newArrayList(ZHS_IS_CHECK_IN_KEY,ZHS_IS_HIGH_RISK_INSTRUCTION), new PartnerClearSubFormPluginValue()),

    /**
     * 变更单主单【计划操作开始时间】、【计划操作结束时间】时区提示语赋值插件
     */
    TIME_ZONE(Lists.newArrayList(TIME_ZONE_KEY, COMPONENT_OPERATION_START_TIME_CID, COMPONENT_OPERATION_END_TIME_CID), new TimeZonePluginValue()),

    /**
     * 变更单批次概要子表单【计划操作开始时间】、【计划操作结束时间】时区提示语赋值插件
     */
    BATCH_SUMMARY_TIME_ZONE(Lists.newArrayList(TIME_ZONE_KEY, FIELD_PLAN_OPERATION_START_TIME_CID, FIELD_PLAN_OPERATION_END_TIME_CID, FIELD_ORGANIZATION_CID), new BatchSummaryTimeZonePlugin()),


    /**
     * 操作人员列表数据检查
     */
    OPERATOR_CHECK_PEOPLE(Lists.newArrayList(OPERATOR_ROLE_KEY, OPERATOR_NAME_KEY, FIELD_OPERATOR_BATCH_NO_KEY,
            OPERATOR_IS_REMOTE, FIELD_ORGANIZATION_CID), new PartnerOperatorCheckPluginValue()),


    /**
     * 网元清单解析生成批次概要插件
     */
    NE_LIST_TO_BATCH_SUMMARY(Lists.newArrayList(NE_LIST_FILE_PROPERTY_KEY), new NeListParseToBatchSummaryPlugin()),

    /**
     * 批次概要批次号值变化插件
     */
    BATCH_SUMMARY_BATCH_NO_NEW_OPERATION_OBJ(Lists.newArrayList(BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY),
            new BatchNoChangePluginValue()),

    /**
     * 操作步骤打卡表中的开始时间和结束时间检查
     */
    OPERATION_STEP_TIME_CHECK(Lists.newArrayList(STEP_START_TIME_CID,STEP_END_TIME_CID), new PartnerOperationStepCheckTimePluginValue()),

    /**
     * 操作对象【客户网络名称】字段值变化插件
     */
    NETWORK_NAME(Lists.newArrayList(FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY), new PartnerNetworkNamePluginValue()),

    /**
     * 客户标识插件
     */
    ACCNT_TYPE(Lists.newArrayList(FIELD_CUSTOMER_NAME_CID), new AccntTypePluginValue()),

    /**
     * 客户标识插件
     */
    EMPLOYEE_DEPARTMENT(Lists.newArrayList(OPERATOR_NAME_KEY), new PartnerEmployeeDepartmentPluginValue()),

    /**
     * 操作对象清空插件
     */
    OPERATION_OBJECT_CLEAR(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_PRODUCT_CID), new OperationObjectClearPlugin()),

    /**
     * 国省市数据更新插件
     */
    COUNTRY_PROVINCE_AREA(Lists.newArrayList(FIELD_NETWORK_NAME), new PartnerCountryChangeByNetworkPlugin()),

    /**
     * 紧急操作/封网、管控操作标记-赋值插件
     */
    EMERGENCY_AND_CLOSE_OR_CONTROL_OPERATION(Lists.newArrayList(FIELD_IS_EMERGENCY_OPERATION_CID), new PartnerEmergencyAndClosedOperationFlagPluginValue()),

    /**
     * 产品型号字段插件
     */
    PRODUCT_MODEL(Lists.newArrayList(FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY), new OperationObjectProductModelPlugin()),

    /**
     * 主产品字段插件
     */
    MAIN_PRODUCT(Lists.newArrayList(OperationObjectFieldConsts.IS_MAIN_PRODUCT), new PartnerOperationObjectMainProductPlugin()),

    ;

    // valueChange插件的cid要配置实体字段CID
    private final List<String> propIds;

    private final PartnerValueChangeBaseFormPlugin partnerValueChangeBaseFormPlugin;

    public static List<PartnerValueChangeBaseFormPlugin> getValueChangedEventPlugins(String propId) {
        List<PartnerValueChangeBaseFormPlugin> partnerValueChangeBaseFormPlugins = new ArrayList<>();
        for (PartnerValueChangePluginEnum pluginEnum : PartnerValueChangePluginEnum.values()) {
            if (pluginEnum.getPropIds().contains(propId)) {
                partnerValueChangeBaseFormPlugins.add(pluginEnum.getPartnerValueChangeBaseFormPlugin());
            }
        }
        return partnerValueChangeBaseFormPlugins;
    }
}
