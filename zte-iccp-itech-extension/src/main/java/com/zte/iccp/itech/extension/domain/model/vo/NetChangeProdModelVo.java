package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/5/29 上午9:25
 */
@ApiModel("产品型号(dataKey)")
@Setter
@Getter
public class NetChangeProdModelVo {

    @JsonProperty("custom_at7dd19r")
    @ApiModelProperty("产品型号id")
    private String prodModelId;

    @JsonProperty("custom_cawztv3n")
    @ApiModelProperty("产品型号fullIdPath")
    private String prodModelFullIdPath;

    @JsonProperty("custom_2rjv107s")
    @ApiModelProperty("局点名称")
    private String customerNeName;

    @JsonProperty("custom_q34jp1l5")
    @ApiModelProperty("局点别名")
    private String neAlias;

    @JsonProperty("custom_7rum8ac3")
    @ApiModelProperty("产品型号")
    private String prodModel;
}
