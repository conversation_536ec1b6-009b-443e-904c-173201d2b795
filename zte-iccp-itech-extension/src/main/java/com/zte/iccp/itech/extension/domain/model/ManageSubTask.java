package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFieldConsts.*;


/**
 * 技术管理子任务
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/3
 */
@ApiModel("技术管理子任务模型")
@Setter
@Getter
@BaseSubEntity.Info(value = "technical_management_subtask", parent = TechnologyManagementOrder.class)
public class ManageSubTask extends BaseSubEntity {

    /**
     * 子任务名称
     */
    @JsonProperty(value = SUBTASK_NAME)
    private String subtaskName;
    /**
     * 任务详情
     */
    @JsonProperty(value = SUBTASK_DETAIL)
    private String subtaskDetail;
    /**
     * 子任务责任人
     */
    @JsonProperty(value = SUBTASK_RESPONSIBLE_PERSON)
    private List<Employee> subtaskResponsiblePerson;

    /**
     * 要求完成日期
     */
    @JsonProperty(value = SUBTASK_COMPLETION_DATE)
    private Date subtaskCompletionDate;
    /**
     * 子任务名称
     */
    @JsonProperty(value = NETWORK_ID)
    private String networkId;
    /**
     * 状态：0-草稿，1-进行中，2-完成，3-审批中，9-已废止
     */
    @JsonProperty(value = SUBTASK_STATUS)
    private List<TextValuePair> subtaskStatus;

    /**
     * 当前进展
     */
    @JsonProperty(value = CURRENT_PROGRESS)
    private Object currentProgress;

    /**
     * 子任务单据编号
     */
    @JsonProperty(value = SUBTASK_CN_NO)
    private String subtaskCnNo;

    /**
     * 责任单位id
     */
    @JsonProperty(value = RESPONSIBLE_ORG_ID)
    private String responsibleOrgId;

    /**
     * 责任单位名称
     */
    @JsonProperty(value = RESPONSIBLE_DEPT_NAME)
    private String responsibleDeptName;

}
