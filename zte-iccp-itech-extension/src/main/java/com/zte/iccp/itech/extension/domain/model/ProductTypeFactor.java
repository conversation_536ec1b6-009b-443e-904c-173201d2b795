package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ProductTypeFactorConsts.*;


/**
 * <AUTHOR>
 * @date 2024/5/14 下午3:16
 */
@Setter
@Getter
@BaseEntity.Info("product_type_factor")
public class ProductTypeFactor extends BaseEntity {

    @JsonProperty(value = STATUS)
    private Integer status;

    @JsonProperty(value = SCORE)
    private Double score;

    @JsonProperty(value = PRODUCT_OPERATION_TEAM)
    private List<TextValuePair> productOperationTeam;

    @JsonProperty(value = PRODUCT_LINE)
    private List<TextValuePair> productLine;

    @JsonProperty(value = PRODUCT_CATEGORY)
    private List<TextValuePair> productCategory;

    @JsonProperty(value = PRODUCT_SUBCATEGORY)
    private List<TextValuePair> productSubcategory;

    @JsonProperty(value = PRODUCT_MODEL)
    private List<TextValuePair> productModel;

    @JsonProperty(value = PRODUCT_MODEL_EXT)
    private List<TextValuePair> productModelExt;
}
