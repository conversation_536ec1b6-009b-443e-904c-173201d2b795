package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ChangeOrderSubFormEnum;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.spi.model.icrm.FileDto;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.COLON;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.VERTICAL;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts.PRODUCT_ID;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.COMMA;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.EMPTY_STRING;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @date 2024/6/17 下午4:36
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AiParamUtils {
    private static final List<String> VERSION_LIST = Arrays.asList("current_version", "target_version");

    private static final List<String> VERSION_LIST_TEXT_FIELD = Arrays.asList("current_version_text_field", "target_version_text_field");

    public static <T extends Enum<T>> String getWorkOrderText(IFormView formView, Class<T> enumClass,
                                                              Function<T, String> propertyKeyFunction,
                                                              Function<T, String> cidFunction,
                                                              Function<T, Function<Object, String>> functionFunction) {


        StringBuilder workOrderTextBuilder = new StringBuilder();
        for (T enumConstant : enumClass.getEnumConstants()) {
            try {
                String propertyKey = propertyKeyFunction.apply(enumConstant);
                String cid = cidFunction.apply(enumConstant);

                Object valueObject = formView.getDataModel().getValue(propertyKey);
                if (ObjectUtils.isEmpty(valueObject)) {
                    continue;
                }
                Function<Object, String> function = functionFunction.apply(enumConstant);
                Object labelObject = formView.getControl(cid).getAttribute(LABEL);
                workOrderTextBuilder.append(getLabel(labelObject)).append(COLON).append(function.apply(valueObject)).append(LINE_FEED);
            } catch (Exception e) {
                log.error("AI generateDoc error AiField enum:" + enumConstant.name() + e);
            }
        }
        return workOrderTextBuilder.toString();
    }

    private static String getLabel(Object labelObject) {
        if (labelObject instanceof String) {
            return (String) labelObject;
        }
        MultiLangText multiLangText = JsonUtils.parseObject(labelObject, MultiLangText.class);
        return multiLangText.getTextByLanguage(RequestContextHolder.getLangId());
    }

    public static String getAllTextNameString(Object textObject) {
        List<TextValuePair> textValuePairList = JsonUtils.parseArray(textObject, TextValuePair.class);
        return textValuePairList.stream()
                .map(t -> t.getTextByLanguage(RequestContextHolder.getLangId()))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(COMMA));
    }

    public static String getAllValueLangNameSring(Object textObject) {
        List<MultiLangText> valueMultiLangListList = JsonUtils.parseArray(textObject, MultiLangText.class);
        return valueMultiLangListList.stream()
                .map(t -> t.getTextByLanguage(RequestContextHolder.getLangId()))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(COMMA));
    }

    public static String getEmployeeNameString(Object operatorNameObj) {
        List<SingleEmployee> operatorNameList = JsonUtils.parseArray(operatorNameObj, SingleEmployee.class);
        return operatorNameList.stream()
                .map(t -> t.getTextByLanguage(RequestContextHolder.getLangId()))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(COMMA));

    }

    public static String getTimeRangeString(Object object) {
        return PropertyValueConvertUtil.getMap(object).get(DATERANGE_START) + WAVE + PropertyValueConvertUtil.getMap(object).get(DATERANGE_END);
    }

    public static <T extends Enum<T>> String getSubFormString(
            IFormView formView, Class<T> enumClass, Function<T, String> propertyKeyFunction, Function<T, String> cidFunction,
            Function<T, List<Triple<String, String, Function<Object, String>>>> lsitFunction) {
        StringBuilder workOrderTextBuilder = new StringBuilder();
        for (T enumConstant : enumClass.getEnumConstants()) {
            try {
                String propertyKey = propertyKeyFunction.apply(enumConstant);
                String cid = cidFunction.apply(enumConstant);
                List<Triple<String, String, Function<Object, String>>> triples = lsitFunction.apply(enumConstant);

                Object labelObject = formView.getControl(cid).getAttribute(LABEL);
                workOrderTextBuilder.append(getLabel(labelObject)).append(COLON).append(LINE_FEED).append(VERTICAL)
                        .append(NO).append(VERTICAL);

                List<String> prodIds = TextValuePairHelper.getValueList(
                        JsonUtils.parseArray(formView.getDataModel().getValue(PRODUCT_ID), TextValuePair.class));
                // 若是 算力及核心网-集成第三方与组网设备 产品线时，对于操作对象子表单，需要去掉操作对象子表单中的VERSION_LIST中的值
                boolean flag = !CollectionUtils.isEmpty(prodIds)
                        && prodIds.get(0).startsWith(ConfigHelper.get(CCN_THIRD_PARTY_RULE_DATA_KEY))
                        && enumClass == ChangeOrderSubFormEnum.class
                        && enumConstant == ChangeOrderSubFormEnum.OPERATION_OBJECT;
                if (flag) {
                    triples = triples.stream().filter(t -> !VERSION_LIST.contains(t.getLeft())).collect(Collectors.toList());
                } else {
                    triples = triples.stream().filter(t -> !VERSION_LIST_TEXT_FIELD.contains(t.getLeft())).collect(Collectors.toList());
                }

                triples.forEach(t -> {
                    Object fieldlabelObject = formView.getControl(t.getMiddle()).getAttribute(LABEL);
                    workOrderTextBuilder.append(getLabel(fieldlabelObject)).append(VERTICAL);
                });
                workOrderTextBuilder.append(LINE_FEED).append(VERTICAL);
                for (int i = 0; i < triples.size() + 1; i++) {
                    workOrderTextBuilder.append(DASH).append(VERTICAL);
                }
                workOrderTextBuilder.append(LINE_FEED);

                IDataEntityCollection collection = formView.getDataModel().getEntryRowEntities(propertyKey);
                List<Triple<String, String, Function<Object, String>>> finalTriples = triples;
                collection.forEach(d -> {
                    workOrderTextBuilder.append(VERTICAL).append(((DynamicDataEntity) d).getRowIndex() + 1);
                    finalTriples.forEach(t -> {
                        Object object = ((DynamicDataEntity) d).get(t.getLeft());
                        if (!ObjectUtils.isEmpty(object)) {
                            workOrderTextBuilder.append(VERTICAL).append(t.getRight().apply(object));
                        } else {
                            workOrderTextBuilder.append(VERTICAL).append(EMPTY_STRING);
                        }
                    });
                    workOrderTextBuilder.append(VERTICAL).append(LINE_FEED);
                });
            } catch (Exception e) {
                log.error("AI generateDoc error subForm enum:" + enumConstant.name() + e);
            }
        }
        return workOrderTextBuilder.toString();
    }


    public static <T extends Enum<T>> List<FileDto> getFileDtos(IFormView formView, Class<T> enumClass,
                                                                Function<T, String> propertyKeyFunction,
                                                                Function<T, String> tableKeyFunction) {
        List<FileDto> fileDtos = new ArrayList<>();
        for (T enumConstant : enumClass.getEnumConstants()) {
            try {
                String propertyKey = propertyKeyFunction.apply(enumConstant);
                String tablePropertyKey = tableKeyFunction.apply(enumConstant);

                if (null == tablePropertyKey) {
                    fileDtos.addAll(assemblyFielDtos(formView.getDataModel().getValue(propertyKey)));
                } else {
                    List<Object> objectList = formView.getDataModel().getEntryColumnObject(tablePropertyKey, propertyKey);
                    objectList.forEach(object -> {
                        if (ObjectUtils.isEmpty(object)) {
                            return;
                        }
                        fileDtos.addAll(assemblyFielDtos(object));
                    });
                }
            } catch (Exception e) {
                log.error("AI generateDoc error attachment enum:" + enumConstant.name() + e);
            }
        }
        return fileDtos;
    }

    private static List<FileDto> assemblyFielDtos(Object object) {
        List<AttachmentFile> attachmentFiles = JsonUtils.parseArray(object, AttachmentFile.class);
        List<FileDto> fileDtos = new ArrayList<>();
        attachmentFiles.forEach(a -> {
            FileDto dto = new FileDto();
            dto.setName(a.getName());
            dto.setUrl(a.getId());
            dto.setFileType(a.getName());
            fileDtos.add(dto);
        });
        return fileDtos;
    }


}
