package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FaultManageOrderFieldConsts {

    /**
     * 任务单号
     */
    public static final String TASK_CODE = "task_code";

    /**
     * 任务主题(Call Log 主题)
     */
    public static final String TASK_SUBJECT = "task_subject";

    /**
     * WarRoom ID
     */
    public static final String WAR_ROOM_ID = "warroom_id";

    /**
     * 客户ID
     */
    public static final String CUSTOMER_ID = "customer_id";

    /**
     * 办事处 / 代表处
     */
    public static final String ORGANIZATION = "organization";

    /**
     * PDM 产品ID
     */
    public static final String PDM_PRODUCT_ID = "pdm_product_id";

    /**
     * 产品经营团队 idPath
     */
    public static final String PRODUCT_TEAM = "product_team";

    /**
     * 产品线 id
     */
    public static final String PRODUCT_LINE = "product_line";

    /**
     * 区域ID
     */
    public static final String AREA_CODE = "area_code";

    /**
     * CSC 单据状态
     */
    public static final String CSC_TASK_STATUS = "csc_task_status";

    /**
     * 故障复盘 - 是否需要故障复盘
     */
    public static final String FAULT_REVIEW = "fault_review";

    /**
     * 故障复盘 - 不进行故障复盘原因
     */
    public static final String FAULT_REVIEW_REASON = "fault_review_reason";

    /**
     * 故障复盘 - 故障复盘报告
     */
    public static final String REVIEW_REPORT = "review_report";

    /**
     * 故障复盘 - 是否故障整改
     */
    public static final String FAULT_RECTIFICATION = "fault_rectification";

    /**
     * 故障复盘 - 不进行故障整改原因
     */
    public static final String FAULT_RECTIFICATION_REASON = "fault_rectification_reason";

    /**
     * 故障复盘 - 节点提交时间
     */
    public static final String REVIEW_SUBMIT_TIME = "review_submit_time";

    /**
     * 故障整改 - 节点提交时间
     */
    public static final String RECTIFICATION_SUBMIT_TIME = "rectification_submit_time";

    /**
     * 客户满意度 - 反馈方式
     */
    public static final String SATISFACTION_CONTENT_TYPE = "satisfaction_content_type";

    /**
     * 客户满意度 - 满意度
     */
    public static final String SATISFACTION = "satisfaction";

    /**
     * 客户满意度 - 客户支持经理
     */
    public static final String CUSTOMER_SUPPORT_MANAGER = "customer_support_manager";

    /**
     * 客户满意度 - 在线反馈内容
     */
    public static final String SATISFACTION_CONTENT = "satisfaction_content";

    /**
     * 客户满意度 - 知会人
     */
    public static final String INFORMED_PERSON = "informed_person";

    /**
     * 客户满意度 - 给客户发的邮件截图
     */
    public static final String CUSTOMER_MAIL_TO = "customer_mail_to";

    /**
     * 客户满意度 - 客户反馈的邮件截图
     */
    public static final String CUSTOMER_MAIL_RECEIVE = "customer_mail_receive";

    /**
     * 客户满意度 - 节点提交时间
     */
    public static final String SATISFACTION_SUBMIT_TIME = "satisfaction_submit_time";

    /**
     * 故障分析 - 提交人
     */
    public static final String ANALYSIS_SUBMITTER = "analysis_submitter";

    /** 故障复盘 - 提交人 */
    public static final String REVIEW_SUBMITTER = "review_submitter";

    /** 故障整改 - 提交人 */
    public static final String RECTIFICATION_SUBMITTER = "rectification_submitter";

    /** 客户满意度 - 提交人 */
    public static final String SATISFACTION_SUBMITTER = "satisfaction_submitter";

    /** 客户满意度责任人 */
    public static final String SATISFACTION_RESPONSIBLE = "satisfaction_responsible_person";

    /** 客户满意度责任人（故障横推整改） */
    public static final String SATISFACTION_RESPONSIBLE_FR = "satisfaction_responsible_person_fr";
}
