package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.SneakyThrows;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORM;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/11
 */
public class DateDeserializer extends JsonDeserializer<Date> {

    private final SimpleDateFormat fmt = new SimpleDateFormat(DATE_FORM);

    @Override
    @SneakyThrows
    public Date deserialize(JsonParser p, DeserializationContext ctxt) {
        String s = p.getCodec().readValue(p, String.class);
        return s.matches("^\\d+$")
                ? new Date(Long.parseLong(s)) : fmt.parse(s);
    }
}
