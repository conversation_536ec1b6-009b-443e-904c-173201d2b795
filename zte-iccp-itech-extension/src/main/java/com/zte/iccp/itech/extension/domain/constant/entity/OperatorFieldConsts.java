package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OperatorFieldConsts {

    /**
     * 人员角色
     */
    public static final String OPERATOR_ROLE = "operator_role";

    /**
     * 是否远程
     */
    public static final String REMOTE_FLAG = "is_remote";

    /**
     * 员工
     */
    public static final String OPERATOR_NAME = "operator_name";

    /**
     * 电话
     */
    public static final String OPERATOR_PHONE = "operator_phone";

    /**
     * 归属部门
     */
    public static final String OPERATOR_DEPARTMENT = "operator_department";

    /**
     * 操作账号
     */
    public static final String OPERATOR_ACCOUNT = "operator_account";

    /**
     * 子表单操作人员操作账号
     */
    public static final String SELECT_ACCOUNT = "select_account";

    /**
     * 人员属性
     */
    public static final String OPERATOR_ATTRIBUTE = "operator_attribute";

    /**
     * 任务说明
     */
    public static final String TASK_DESC = "task_desc";

    /**
     * 人员
     */
    public static final String OPERATE_PERSON = "operate_person";

    /**
     * 电话
     */
    public static final String TELEPHONE = "telephone";

    /**
     * 操作批次
     */
    public static final String OPERATOR_BATCH_NO = "operator_batch_no";
}
