package com.zte.iccp.itech.extension.openapi.model.clockin;

import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * 打卡记录列表
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/11/29
 */
@Getter
@Setter
public class ClockInRecordDTO {

    /** 打卡记录id */
    private String clockInRecordId;

    /** 打卡项 */
    private String clockInOption;

    /** 打卡角色 */
    private List<String> clockInRoles;

    /** 打卡时间 */
    private String clockInTime;

    /** 打卡人 */
    private String clockInBy;

    /** 打卡记录状态 - 是否已撤销 */
    private Boolean isRevoked;
}
