package com.zte.iccp.itech.extension.common.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 开始时间距离当前时间过了多少分钟
 *
 * <AUTHOR>
 * @create 2024/10/14 下午1:45
 */
public class TimeRangeCalculatorUtils {

    public static long calculateDuration(Date startDate) {
        Instant instant = startDate.toInstant();
        LocalDateTime startTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());

        LocalDateTime endTime = LocalDateTime.now();
        // 计算开始时间和结束时间所在天的有效时长

        // 计算当天有效时长
        long startDayDuration = startTime.toLocalTime().getHour() * 60 + startTime.toLocalTime().getMinute();
        long endDayDuration = endTime.toLocalTime().getHour() * 60 + endTime.toLocalTime().getMinute();

        long validDuration = 0;
        if (startTime.toLocalDate().equals(endTime.toLocalDate())) {
            // 同一天内
            if (startTime.toLocalTime().isAfter(LocalTime.of(22, 0))) {
                startDayDuration = 0;
            }
            if (endTime.toLocalTime().isBefore(LocalTime.of(8, 0))) {
                endDayDuration = 0;
            }
            validDuration = endDayDuration - startDayDuration;
        } else {
            // TODO
            // 跨天计算
            if (startTime.toLocalTime().isAfter(LocalTime.of(22, 0))) {
                startDayDuration = 0;
            } else {
                validDuration += (LocalTime.of(22, 0).toSecondOfDay() - startTime.toLocalTime().toSecondOfDay()) / 60;
            }
            if (endTime.toLocalTime().isBefore(LocalTime.of(8, 0))) {
                endDayDuration = 0;
            } else {
                validDuration += (endTime.toLocalTime().toSecondOfDay() - LocalTime.of(8, 0).toSecondOfDay()) / 60;
            }
        }
        return validDuration;
    }


}
