package com.zte.iccp.itech.extension.ability.grantfile;

import com.zte.iccp.itech.extension.ability.OperationLogAbility;
import com.zte.iccp.itech.extension.ability.accredit.CcnAuthorizationApplicationAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.CloudDiskHelper;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.OperationLogTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.CcnAuthorizationApplication;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.helper.util.FileStorageUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> 10284287
 * @since 2024/08/08
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DownloadGrantFileAbility extends BaseGrantFileAbility {

    private static final String SUFFIX_SELF = "_self";

    public static void downloadGrantFile(
            IClientViewProxy clientViewProxy,
            boolean sa,
            Class<? extends BaseEntity> batchTaskEntity,
            String batchTaskId) {
        try {
            String url = getDownloadUrl(sa, batchTaskEntity, batchTaskId);
            clientViewProxy.openWindow(url, SUFFIX_SELF);

            if (sa) {
                OperationLogAbility.addLog(OperationLogTypeEnum.DOWNLOAD_SA_GRANT_FILE, batchTaskId, batchTaskEntity.getSimpleName());
            }
        } catch (LcapBusiException e) {
            log.error(e.getMessage(), e);
            clientViewProxy.showMessage(e.getMessage(), MsgType.WARNING);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            clientViewProxy.showMessage(e.getMessage(), MsgType.ERROR);
        }
    }

    /**
     * 下载授权文件 - CCN 授权文件
     * @param clientViewProxy
     * @param applicationId
     */
    public static void downloadGrantFile(IClientViewProxy clientViewProxy, String applicationId) {
        try {
            // 1.检索申请单
            CcnAuthorizationApplication application = CcnAuthorizationApplicationAbility.queryApplicationById(applicationId);
            if (Objects.isNull(application)) {
                throw new LcapBusiException(MessageConsts.ApplyCcnAuthorization.APPLICATION_NOT_EXISTS);
            }

            // 2.获取下载链接
            String fileKey = application.getDefaultAuthorizationFileKey();
            String fileName = CloudDiskHelper.getFileName(fileKey);
            String url = FileStorageUtils.buildDownLoadUrl(fileKey, fileName);

            // 3.下载文件
            clientViewProxy.openWindow(url, SUFFIX_SELF);
        } catch (LcapBusiException e) {
            log.error(e.getMessage(), e);
            clientViewProxy.showMessage(e.getMessage(), MsgType.WARNING);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            clientViewProxy.showMessage(e.getMessage(), MsgType.ERROR);
        }
    }

    @SneakyThrows
    public static void downloadGrantFile(
            HttpServletResponse response,
            boolean sa,
            Class<? extends BaseEntity> batchTaskEntity,
            String batchTaskId) {
        try {
            String url = getDownloadUrl(sa, batchTaskEntity, batchTaskId);
            response.sendRedirect(url);

            if (sa) {
                OperationLogAbility.addLog(OperationLogTypeEnum.DOWNLOAD_SA_GRANT_FILE, batchTaskId, batchTaskEntity.getSimpleName());
            }
        } catch (LcapBusiException e) {
            log.error(e.getMessage(), e);
            response.getWriter().println(MsgUtils.getMessage(e.getMessage()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.getWriter().println(e.getMessage());
        }
    }

    private static String getDownloadUrl(
            boolean sa,
            Class<? extends BaseEntity> batchTaskEntity,
            String batchTaskId) {
        ApproveFlowCodeEnum flow = findFlowCodeEnum(batchTaskEntity);
        IBatchTask batchTask = getBatchTask(flow, batchTaskId);
        String fileKey = sa ? batchTask.getSaGrantFile() : batchTask.getGrantFile();
        String fileName = CloudDiskHelper.getFileName(fileKey);

        if (StringUtils.isBlank(fileKey)) {
            throw new LcapBusiException(MessageConsts.BATCH_TASK_NOT_FIND_GRANT_FILE);
        }

        return FileStorageUtils.buildDownLoadUrl(fileKey, fileName);
    }

    private static ApproveFlowCodeEnum findFlowCodeEnum(Class<? extends BaseEntity> batchTaskEntity) {
        for (Map.Entry<ApproveFlowCodeEnum, GrantFileConfig.Config> entry : GrantFileConfig.ALL.entrySet()) {
            GrantFileConfig.Config v = entry.getValue();
            if (v.getBatchTaskEntity() == batchTaskEntity) {
                return entry.getKey();
            }
        }
        return null;
    }
}
