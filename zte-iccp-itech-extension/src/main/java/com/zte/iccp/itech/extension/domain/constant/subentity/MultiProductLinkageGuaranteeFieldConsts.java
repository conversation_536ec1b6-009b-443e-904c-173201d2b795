package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/29 下午4:02
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MultiProductLinkageGuaranteeFieldConsts {
    /**
     * 产品分类
     */
    public static final String PRODUCT_CLASS = "product_class";
    /**
     * 实施责任人
     */
    public static final String IMPLEMENTATION_RESPONSIBLE_PERSON = "implementation_responsible_person";

    /**
     * 任务id
     */
    public static final String ASSIGNMENT_ID = "assignment_id";

    /**
     * 当前进展
     */
    public static final String CURRENT_PROGRESS = "current_progress";

    /**
     * 当前处理人
     */
    public static final String CURRENT_HANDLER = "current_handler";

    /**
     * 是否多产品联动保障任务主任务
     */
    public static final String IS_MAIN_TASK = "is_main_task";
}
