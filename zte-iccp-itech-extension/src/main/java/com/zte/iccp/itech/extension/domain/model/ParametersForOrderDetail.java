package com.zte.iccp.itech.extension.domain.model;

import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 变更单审批信息
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/4/29
 */
@Setter
@Getter
public class ParametersForOrderDetail {

    /**
     * 特殊审批信息
     */
    private List<ApproveRecord> allSpecialDealApprovedRecords;

    /**
     * 正常审批信息
     */
    private List<ApproveRecord> approvedRecords;

    /**
     * 当前进展
     */
    private String extendedCode;

}
