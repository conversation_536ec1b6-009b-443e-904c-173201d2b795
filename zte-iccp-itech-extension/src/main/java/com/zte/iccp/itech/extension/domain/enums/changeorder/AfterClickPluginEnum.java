package com.zte.iccp.itech.extension.domain.enums.changeorder;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.plugin.form.changeorder.afterclick.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum AfterClickPluginEnum {


    /**
     * 操作对象-产品型号，跳转自定义页面
     */
    OPERATION_OBJECT_PRODUCT_MODEL_QUERY(
            Lists.newArrayList(COMPONENT_OPERATION_OBJECT_PRODUCT_MODEL_ID_CID, COMPONENT_APP_OFFICE_PRODUCT_MODEL_CID),
            new ProductModelAfterClickPlugin()),

    /**
     * 按钮-查询客户/内部操作方案列表，跳转内部页面（标准方案-已发布-列表）
     */
    BUTTON_QUERY_SOLUTION_LIST(Lists.newArrayList(INNER_SOLUTION_BUTTON,CUSTOMER_SOLUTION_BUTTON), new SolutionSearchPlugin()),

    /**
     * 操作对象--当前版本、目标版本，跳转自定义页面（版本列表）
     */
    QUERY_VERSION_LIST(Lists.newArrayList(COMPONENT_OPERATION_OBJECT_CURRENT_VERSION,
            COMPONENT_OPERATION_OBJECT_TARGET_VERSION, COMPONENT_NETWORK_UNIT_VERSION_CID),
            new VersionSearchPluginClick()),

    /**
     * ai回调刷新按钮
     */
    AI_CALLBACK_REFRESH(Lists.newArrayList(AI_CALLBACK_REFRESH_BUTTON, CUSTOMER_AI_CALLBACK_REFRESH_BUTTON), new AiRefreshPlugin()),

    /**
     * 无线产品升级单，跳转自定义页面（版本列表）
     */
    UPGRADE_FORM_QUERY_LIST(Lists.newArrayList(FIELD_WIRELESS_UPGRADE_TICKET_CID), new UpgradeFormSearchPluginClick()),

    ;


    // afterClick插件的cid要配置布局组件CID
    private final List<String> cids;

    private final AfterClickBaseFormPlugin afterClickBaseFormPlugin;

    public static List<AfterClickBaseFormPlugin> getAfterClickEventPlugins(String cid) {
        List<AfterClickBaseFormPlugin> afterClickBaseFormPlugins = new ArrayList<>();
        for (AfterClickPluginEnum pluginEnum : AfterClickPluginEnum.values()) {
            if (pluginEnum.getCids().contains(cid)) {
                afterClickBaseFormPlugins.add(pluginEnum.getAfterClickBaseFormPlugin());
            }
        }
        return afterClickBaseFormPlugins;
    }
}
