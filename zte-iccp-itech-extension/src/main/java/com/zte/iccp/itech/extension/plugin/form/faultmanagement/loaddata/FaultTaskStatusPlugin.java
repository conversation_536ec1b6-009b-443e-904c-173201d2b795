package com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultProcessEnum;
import com.zte.iccp.itech.extension.domain.model.FaultManagementDetails;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iss.approval.sdk.bean.TaskDetailInfo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.LoadDataEvent;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.flow.dto.FlowTaskDetailQuery;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ZERO;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.REQUIRED;
import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.ComponentCids.*;
import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.ComponentCidsMap.*;
import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids.*;
import static com.zte.paas.lcap.platform.ruleengine.constant.CommonConstant.URL_PARAMS;

/**
 * 故障管理任务状态插件
 *
 * 包括故障分析，故障复盘，故障整改横推，客户满意度四个状态
 */
public class FaultTaskStatusPlugin implements LoadDataBaseFormPlugin {

    private static final String ONLINE_FILL = "ONLINE_FILL";

    private static final String EMAIL_FILL = "EMAIL_FILL";

    private static final String TASK_ID = "taskId";

    private static final String TASK_TYPE = "taskType";

    private static final String APPROVAL_TYPE = "APPROVAL";

    private static final String COMPLETED = "COMPLETED";

    @Override
    public void loadData(LoadDataEventArgs args, FaultManagementDetails faultManagementDetails) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();
        if (formView == null || dataModel == null) {
            return;
        }

        // 1.校验跳转来源
        PageStatusEnum pageStatusEnum = formView.getFormShowParameter().getPageStatus();
        // 审批态，说明不是从任务中心跳转
        boolean isFromAssignment = pageStatusEnum != PageStatusEnum.APPROVE;

        // 2.获取故障管理任务
        List<FaultManagementAssignment> assignments = faultManagementDetails.getFaultManagementAssignments();
        if (CollectionUtils.isEmpty(assignments)) {
            return;
        }
        FaultManagementAssignment assignment = assignments.get(CommonConstants.INTEGER_ZERO);

        // 3.检索故障管理单
        List<FaultManagementOrder> faultOrderList = faultManagementDetails.getFaultManagementOrders();
        if (CollectionUtils.isEmpty(faultOrderList)) {
            return;
        }
        FaultManagementOrder faultOrder = faultOrderList.get(CommonConstants.INTEGER_ZERO);

        // 4.详情信息展示
        // 故障复盘，故障整改横推，客户满意度默认隐藏
        String currentProgress = assignment.getCurrentProgress();
        if (FaultProcessEnum.DEMOTION_CLOSED.name().equals(currentProgress)) {
            currentProgress = assignment.getHistoryProgress();
        }
        switch (FaultProcessEnum.valueOf(currentProgress)) {
            // (1) 故障复盘确认
            case FAULT_REVIEW_CONFIRMING:
                setFaultReviewProps(args, faultOrder, isFromAssignment);
                break;

            // (2) 故障复盘任务执行中 (中间状态, 描述同故障复盘确认)
            case FAULT_REVIEWING:
                setExecuteFaultReviewAssignment(args, faultOrder, isFromAssignment);
                break;

            // (3) 待提交故障复盘报告
            case TO_SUBMIT_FAULT_REVIEW_REPORT:
                setFaultReviewReportProps(args, faultOrder,isFromAssignment);
                break;

            // (4) 待故障整改横推
            case FAULT_RECTIFICATION_PROMOTION_CONFIRMING:
                setFaultRectificationProps(args, faultOrder, isFromAssignment);
                break;

            // (5) 待反馈客户满意度
            case PENDING_FEEDBACK_CUSTOMER_SATISFACTION:
                setSatisfactionProps(args, faultOrder, isFromAssignment);
                break;

            // (6) 故障整改中/已关闭
            case FAULT_BEING_RECTIFIED:
            case CLOSED:
                setCloseProps(args, faultOrder);
                break;

            default:
                break;
        }
    }

    /**
     * 容器 / 字段信息展示 - 故障复盘确认
     * 展示 故障复盘（部分字段）
     * @param args
     * @param faultOrder
     * @param isFromAssignment
     */
    private static void setFaultReviewProps(LoadDataEventArgs args,
                                            FaultManagementOrder faultOrder,
                                            boolean isFromAssignment) {
        // 1.设置审批状态属性
        boolean res = setApproveStatus(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);

        // 2.设置组件只读

        // 3.隐藏故障复盘任务按钮
        if (res) {
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                    new PageStatusAttributeBuilder().hidden().build());
            return;
        }

        // 4.容器展示
        // 故障复盘确认（我的待办跳转展示）
        if (!isFromAssignment) {
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_REVIEW_ADC_CID,
                    new PageStatusAttributeBuilder().normal().build());
        }
    }

    // 待提交故障复盘报告，展示故障复盘（全部字段）和故障分析，其中故障复盘（部分字段）和故障分析为只读
    private static void setFaultReviewReportProps(LoadDataEventArgs args,
                                                  FaultManagementOrder faultOrder,
                                                  boolean isFromAssignment) {
        boolean res1 = setApproveStatus(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);
        // 隐藏创建故障复盘任务按钮
        args.getFormView().getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                new PageStatusAttributeBuilder().hidden().build());
        if (res1) {
            return;
        }
        // 展示故障复盘确认高级容器
        args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_REVIEW_ADC_CID,
                new PageStatusAttributeBuilder().normal().build());
        // 隐藏创建故障复盘任务按钮
        args.getFormView().getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                new PageStatusAttributeBuilder().hidden().build());

        if (!isFromAssignment) {
            // 展示故障复盘报告
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_REVIEW_REPORT_CID,
                    new PageStatusAttributeBuilder().normal().build());
            // 展示故障复盘报告模版布局容器
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_COLUMNSLAYOUT_TEMPLATE_REPORT_CID,
                    new PageStatusAttributeBuilder().normal().build());
            // 故障复盘报告必填
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_REVIEW_REPORT_CID, new BasicAttributeBuilder()
                    .attribute(REQUIRED, true)
                    .build());

            // 展示是否需要横推整改
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_RECTIFICATION_CID,
                    new PageStatusAttributeBuilder().normal().build());

            // 展示是否故障复盘带出来的字段
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_RECTIFICATION_CID,
                    new PageStatusAttributeBuilder().normal().build());
        }

        setComponentProps(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);
    }

    /**
     * 容器展示 - 故障复盘任务执行中
     * @param args
     * @param faultOrder
     * @param isFromAssignment
     */
    private static void setExecuteFaultReviewAssignment(LoadDataEventArgs args,
                                                        FaultManagementOrder faultOrder,
                                                        boolean isFromAssignment) {
        boolean res = setApproveStatus(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);
        if (res) {
            return;
        }
        // 从任务中心跳转
        if (isFromAssignment) {
            // 展示故障复盘确认高级容器
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_REVIEW_ADC_CID,
                    new PageStatusAttributeBuilder().normal().build());
            // 隐藏创建故障复盘任务按钮
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                    new PageStatusAttributeBuilder().hidden().build());
            // 故障复盘提交人/提交时间只读
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_REVIEW_SUBMIT_TIME_CID,
                    new PageStatusAttributeBuilder().readOnly().build());
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_ANALYSIS_REVIEW_SUBMITTER_CID,
                    new PageStatusAttributeBuilder().readOnly().build());
            // 是否需要故障复盘
            setComponentProps(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                    COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);

        }
    }

    /**
     * 容器 / 字段信息展示 - 故障整改横推
     * 展示 故障整改横推（部分字段） + 故障复盘，其中 故障复盘 为只读
     * @param args
     * @param faultOrder
     * @param isFromAssignment
     */
    private static void setFaultRectificationProps(LoadDataEventArgs args,
                                                   FaultManagementOrder faultOrder,
                                                   boolean isFromAssignment) {
        boolean res1 = setApproveStatus(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);
        boolean res2 = setApproveStatus(args, FIELD_FAULT_RECTIFICATION_CID, COMPONENT_FAULT_RECTIFICATION_CID,
                COMPONENT_FAULT_RECTIFICATION_Y_CIDS, COMPONENT_FAULT_RECTIFICATION_N_CIDS, CommonConstants.Y, CommonConstants.N);

        // 隐藏创建故障复盘任务按钮
        args.getFormView().getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                new PageStatusAttributeBuilder().hidden().build());
        // 隐藏故障横推整改任务表格
        args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_RECTIFICATION_TASK_TABLE_CID,
                new PageStatusAttributeBuilder().hidden().build());
        // “故障复盘提交人”和“故障复盘提交时间”为只读
        Object faultRectificationObj = args.getModel().getValue(FIELD_FAULT_RECTIFICATION_CID);
        if (!ObjectUtils.isEmpty(faultRectificationObj)) {
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_ANALYSIS_REVIEW_SUBMITTER_CID,
                    new PageStatusAttributeBuilder().readOnly().build());
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_REVIEW_SUBMIT_TIME_CID,
                    new PageStatusAttributeBuilder().readOnly().build());
        }

        if (res1 || res2) {
            return;
        }
        // 展示故障复盘确认高级容器
        args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_REVIEW_ADC_CID,
                new PageStatusAttributeBuilder().normal().build());

        if (!isFromAssignment) {
            // 展示故障横推整改高级容器
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_RECTIFICATION_ADC_CID,
                    new PageStatusAttributeBuilder().normal().build());
            // 展示需要故障复盘
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_RECTIFICATION_CID,
                    new PageStatusAttributeBuilder().normal().build());
        }

        setComponentProps(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);

        // 故障复盘报告_只读
        args.getFormView().getClientViewProxy().setControlState(COMPONENT_REVIEW_REPORT_CID,
                new PageStatusAttributeBuilder().readOnly().build());
    }

    /**
     * 容器 / 字段信息展示 - 客户满意度
     * 展示 客户满意度（部分字段） + 故障复盘 + 故障整改横推，其中 故障复盘 + 故障整改横推 为只读
     * @param args
     * @param faultOrder
     * @param isFromAssignment
     */
    private static void setSatisfactionProps(LoadDataEventArgs args,
                                             FaultManagementOrder faultOrder,
                                             boolean isFromAssignment) {
        IDataModel dataModel = args.getModel();
        IClientViewProxy clientViewProxy = args.getFormView().getClientViewProxy();

        boolean res1 = setApproveStatus(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);
        boolean res2 = setApproveStatus(args, FIELD_FAULT_RECTIFICATION_CID, COMPONENT_FAULT_RECTIFICATION_CID,
                COMPONENT_FAULT_RECTIFICATION_Y_CIDS, COMPONENT_FAULT_RECTIFICATION_N_CIDS, CommonConstants.Y, CommonConstants.N);

        // 隐藏创建故障复盘任务按钮
        clientViewProxy.setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                new PageStatusAttributeBuilder().hidden().build());
        // 隐藏创建横推整改任务按钮
        clientViewProxy.setControlState(COMPONENT_FAULT_RECTIFICATION_BUTTON_CID,
                new PageStatusAttributeBuilder().hidden().build());
        shownFaultRectification(args);
        // “故障复盘提交人”和“故障复盘节点提交时间”为只读
        Object faultRectificationObj = args.getModel().getValue(FIELD_FAULT_RECTIFICATION_CID);
        if (!ObjectUtils.isEmpty(faultRectificationObj)) {
            clientViewProxy.setControlState(COMPONENT_ANALYSIS_REVIEW_SUBMITTER_CID,
                    new PageStatusAttributeBuilder().readOnly().build());
            clientViewProxy.setControlState(COMPONENT_REVIEW_SUBMIT_TIME_CID,
                    new PageStatusAttributeBuilder().readOnly().build());
        }
        // “故障整改提交人”和“故障整改提交时间”为只读
        clientViewProxy.setControlState(COMPONENT_RECTIFICATION_SUBMITTER_CID,
                new PageStatusAttributeBuilder().readOnly().build());
        clientViewProxy.setControlState(COMPONENT_RECTIFICATION_SUBMIT_TIME_CID,
                new PageStatusAttributeBuilder().readOnly().build());

        if (res1 || res2) {
            return;
        }
        // 展示故障复盘确认高级容器
        clientViewProxy.setControlState(COMPONENT_FAULT_REVIEW_ADC_CID,
                new PageStatusAttributeBuilder().normal().build());

        // 判断故障整改横推是否需要展示
        setFaultRectificationComponentProps(dataModel, clientViewProxy, faultOrder);

        if (!isFromAssignment) {
            // 展示客户满意度高级容器
            clientViewProxy.setControlState(COMPONENT_SATISFACTION_ADC_CID,
                    new PageStatusAttributeBuilder().normal().build());
        }

        setComponentProps(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);
    }

    /**
     * 容器 / 字段信息展示 - 关闭状态
     * 展示所有节点字段
     * @param args
     * @param faultOrder
     */
    private static void setCloseProps(LoadDataEventArgs args, FaultManagementOrder faultOrder) {
        IDataModel dataModel = args.getModel();
        IClientViewProxy clientViewProxy = args.getFormView().getClientViewProxy();
        boolean res1 = setApproveStatus(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);
        boolean res2 = setApproveStatus(args, FIELD_FAULT_RECTIFICATION_CID, COMPONENT_FAULT_RECTIFICATION_CID,
                COMPONENT_FAULT_RECTIFICATION_Y_CIDS, COMPONENT_FAULT_RECTIFICATION_N_CIDS, CommonConstants.Y, CommonConstants.N);
        boolean res3 = setApproveStatus(args, FIELD_SATISFACTION_CONTENT_TYPE_CID, COMPONENT_SATISFACTION_CONTENT_TYPE_CID,
                COMPONENT_SATISFACTION_ONLINE_CIDS, COMPONENT_SATISFACTION_EMAIL_CIDS, ONLINE_FILL, EMAIL_FILL);

        // 隐藏创建故障复盘任务按钮
        clientViewProxy.setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                new PageStatusAttributeBuilder().hidden().build());
        // 隐藏创建横推整改任务按钮
        clientViewProxy.setControlState(COMPONENT_FAULT_RECTIFICATION_BUTTON_CID,
                new PageStatusAttributeBuilder().hidden().build());
        shownFaultRectification(args);
        // “故障复盘提交人”和“故障复盘提交时间”为只读
        Object faultRectificationObj = dataModel.getValue(FIELD_FAULT_RECTIFICATION_CID);
        if (!ObjectUtils.isEmpty(faultRectificationObj)) {
            clientViewProxy.setControlState(COMPONENT_ANALYSIS_REVIEW_SUBMITTER_CID,
                    new PageStatusAttributeBuilder().readOnly().build());
            clientViewProxy.setControlState(COMPONENT_REVIEW_SUBMIT_TIME_CID,
                    new PageStatusAttributeBuilder().readOnly().build());
        }
        // “故障整改提交人”和“故障整改提交时间”为只读
        clientViewProxy.setControlState(COMPONENT_RECTIFICATION_SUBMITTER_CID,
                new PageStatusAttributeBuilder().readOnly().build());
        clientViewProxy.setControlState(COMPONENT_RECTIFICATION_SUBMIT_TIME_CID,
                new PageStatusAttributeBuilder().readOnly().build());
        // “客户满意度提交人”和“客户满意度提交时间”为只读
        clientViewProxy.setControlState(COMPONENT_SATISFACTION_SUBMITTER_CID,
                new PageStatusAttributeBuilder().readOnly().build());
        clientViewProxy.setControlState(COMPONENT_SATISFACTION_SUBMIT_TIME_CID,
                new PageStatusAttributeBuilder().readOnly().build());

        if (res1 || res2 || res3) {
            return;
        }

        // 展示故障复盘确认高级容器
        clientViewProxy.setControlState(COMPONENT_FAULT_REVIEW_ADC_CID,
                new PageStatusAttributeBuilder().normal().build());

        // 展示客户满意度高级容器
        clientViewProxy.setControlState(COMPONENT_SATISFACTION_ADC_CID,
                new PageStatusAttributeBuilder().normal().build());

        // 是否需要横推整改
        setComponentProps(args, FIELD_FAULT_RECTIFICATION_CID, COMPONENT_FAULT_RECTIFICATION_CID,
                COMPONENT_FAULT_RECTIFICATION_Y_CIDS, COMPONENT_FAULT_RECTIFICATION_N_CIDS, CommonConstants.Y, CommonConstants.N);

        // 是否需要故障复盘
        setComponentProps(args, FIELD_FAULT_REVIEW_CID, COMPONENT_FAULT_REVIEW_CID,
                COMPONENT_FAULT_REVIEW_Y_CIDS, COMPONENT_FAULT_REVIEW_N_CIDS, CommonConstants.Y, CommonConstants.N);

        // 客户满意度_反馈方式
        setComponentProps(args, FIELD_SATISFACTION_CONTENT_TYPE_CID, COMPONENT_SATISFACTION_CONTENT_TYPE_CID,
                COMPONENT_SATISFACTION_ONLINE_CIDS, COMPONENT_SATISFACTION_EMAIL_CIDS, ONLINE_FILL, EMAIL_FILL);

        // 判断展示故障整改横推是否需要展示
        setFaultRectificationComponentProps(dataModel, clientViewProxy, faultOrder);
    }

    // 故障横推整改是否展示
    private static void shownFaultRectification(LoadDataEventArgs args) {
        Object fieldCidObj = args.getModel().getValue(FIELD_FAULT_RECTIFICATION_CID);
        if (Objects.isNull(fieldCidObj)) {
            // 隐藏故障横推高级容器（默认是展示）
            args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_RECTIFICATION_ADC_CID,
                    new PageStatusAttributeBuilder().hidden().build());
        } else {
            List<TextValuePair> isFields = JsonUtils.parseArray(fieldCidObj, TextValuePair.class);
            if (CollectionUtils.isEmpty(isFields)) {
                return;
            }
            String isField = isFields.get(INTEGER_ZERO).getValue();
            if (CommonConstants.N.equals(isField)) {
                // 隐藏故障横推高级容器（默认是展示）
                args.getFormView().getClientViewProxy().setControlState(COMPONENT_FAULT_RECTIFICATION_ADC_CID,
                        new PageStatusAttributeBuilder().hidden().build());
            }
        }
    }

    /**
     * 设置故障整改横推组件属性
     * @param dataModel
     * @param clientViewProxy
     * @param faultOrder
     */
    private static void setFaultRectificationComponentProps(IDataModel dataModel,
                                                            IClientViewProxy clientViewProxy,
                                                            FaultManagementOrder faultOrder) {
        // 1.判断展示故障整改横推是否需要展示
        List<TextValuePair> needFaultRectificationInfo = ComponentUtils.getChooseComponentInfo(dataModel, FIELD_FAULT_RECTIFICATION_CID);
        if (CollectionUtils.isEmpty(needFaultRectificationInfo)) {
            clientViewProxy.setControlState(COMPONENT_FAULT_RECTIFICATION_ADC_CID, new PageStatusAttributeBuilder().hidden().build());
            return;
        }

        // 2.故障整改横推展示内容
        String needFaultRectification = TextValuePairHelper.getValue(needFaultRectificationInfo);
        // 展示故障横推整改高级容器
        clientViewProxy.setControlState(COMPONENT_FAULT_RECTIFICATION_ADC_CID, new PageStatusAttributeBuilder().normal().build());
        // (1) 无需故障整改横推 - 设置 故障复盘报告 + 是否整改横推 + 不进行整改横推，客户满意度责任人 只读
        if (CommonConstants.N.equals(needFaultRectification)) {
            COMPONENT_FAULT_RECTIFICATION_N_CIDS.forEach(item ->
                    clientViewProxy.setControlState(item, new PageStatusAttributeBuilder().readOnly().build()));
            return;
        }

        // (2) 需故障整改横推
        clientViewProxy.setControlState(COMPONENT_FAULT_RECTIFICATION_TASK_TABLE_CID, new PageStatusAttributeBuilder().normal().build());
        // 创建横推整改任务（按钮）隐藏
        clientViewProxy.setControlState(COMPONENT_FAULT_RECTIFICATION_BUTTON_CID, new PageStatusAttributeBuilder().hidden().build());

        // 设置 故障复盘报告 + 是否整改横推 + 客户满意度责任人 只读
        COMPONENT_FAULT_RECTIFICATION_Y_CIDS.forEach(item ->
                clientViewProxy.setControlState(item, new PageStatusAttributeBuilder().readOnly().build()));
    }

    /**
     * 设置 组件 相关属性
     * @param args
     * @param fieldCids
     * @param componentCids
     * @param componentYCids
     * @param componentNCids
     * @param s1
     * @param s2
     */
    private static void setComponentProps(LoadDataEventArgs args,
                                          String fieldCids,
                                          String componentCids,
                                          Set<String> componentYCids,
                                          Set<String> componentNCids,
                                          String s1, String s2) {
        Object fieldCidObj = args.getModel().getValue(fieldCids);
        if (Objects.isNull(fieldCidObj)) {
            return;
        }
        List<TextValuePair> isFields = JsonUtils.parseArray(fieldCidObj, TextValuePair.class);
        if (CollectionUtils.isEmpty(isFields)) {
            return;
        }
        String isField = isFields.get(INTEGER_ZERO).getValue();
        if (s1.equals(isField)) {
            for(String componentYCid : componentYCids) {
                // 对应的字段设置为只读
                args.getFormView().getClientViewProxy().setControlState(componentYCid,
                        new PageStatusAttributeBuilder().readOnly().build());
            }
        } else if (s2.equals(isField)) {
            for (String componentNCid : componentNCids) {
                // 对应的字段设置为只读
                args.getFormView().getClientViewProxy().setControlState(componentNCid,
                        new PageStatusAttributeBuilder().readOnly().build());
            }
        }
    }

    /**
     * 设置 审批状态 相关属性
     * @param args
     * @param fieldCids
     * @param componentCids
     * @param componentYCids
     * @param componentNCids
     * @param str3
     * @param str4
     * @return boolean
     */
    private static boolean setApproveStatus(LoadDataEventArgs args,
                                            String fieldCids,
                                            String componentCids,
                                            Set<String> componentYCids,
                                            Set<String> componentNCids,
                                            String str3,
                                            String str4
    ) {
        LoadDataEvent event = args.getEvent();
        if (event == null) {
            return false;
        }
        Map<String, Object> urlParamsObj = (Map<String, Object>) event.getArgs().get(URL_PARAMS);
        Map<String, String> urlParams = urlParamsObj.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey, entry -> (String) entry.getValue()));
        Object taskIdObj = urlParams.get(TASK_ID);
        Object taskTypeObj = urlParams.get(TASK_TYPE);
        if (Objects.isNull(taskIdObj) || Objects.isNull(taskTypeObj)) {
            return false;
        }
        String taskId = taskIdObj.toString();
        String taskType = taskTypeObj.toString();
        if (APPROVAL_TYPE.equals(taskType)) {
            FlowTaskDetailQuery flowTaskDetailQuery = new FlowTaskDetailQuery();
            flowTaskDetailQuery.setTenantId(ContextHelper.getTenantId());
            flowTaskDetailQuery.setTaskId(taskId);
            TaskDetailInfo taskDetailInfo = FlowServiceHelper.queryTaskInfo(flowTaskDetailQuery);
            String statusCode = taskDetailInfo.getStatusCode();
            if (COMPLETED.equals(statusCode)) {
                // 已完成的状态，设置为只读/不显示
                setComponentProps(args, fieldCids, componentCids,  componentYCids, componentNCids, str3, str4);
                return true;
            }
        }
        return false;
    }

    /**
     * 补充 用户 信息
     * @param dataModel
     * @param fieldIdMap
     */
    private static void supplementaryUserInfoData(IDataModel dataModel, Map<String, String> fieldIdMap) {
        // 1.获取 用户Id 集合
        List<String> userIdList = new ArrayList<>(fieldIdMap.values());

        // 2.检索 HR 系统
        Map<String, String> userNameMap = HrClient.queryEmployeeNameInfo(userIdList);

        // 3.展示名称数据
        for (Map.Entry<String, String> entry : fieldIdMap.entrySet()) {
            String fieldId = entry.getKey();
            String userId = entry.getValue();
            dataModel.setValue(fieldId, userNameMap.getOrDefault(userId, userId));
        }
    }
}
