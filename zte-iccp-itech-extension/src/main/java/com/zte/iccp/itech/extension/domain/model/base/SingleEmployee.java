package com.zte.iccp.itech.extension.domain.model.base;

import com.zte.iccp.itech.extension.common.json.provider.SingletonListProvider;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

@Getter
@Setter
public class SingleEmployee extends Employee implements SingletonListProvider {

    public static SingleEmployee from(Employee src) {
        SingleEmployee target = new SingleEmployee();
        BeanUtils.copyProperties(src, target);
        return target;
    }

    public String getTextByLanguage(String language) {
        return ZH_CN.equals(language) ? getEmpNameCn() + getEmpUIID() : getEmpNameEn() + getEmpUIID();
    }
}
