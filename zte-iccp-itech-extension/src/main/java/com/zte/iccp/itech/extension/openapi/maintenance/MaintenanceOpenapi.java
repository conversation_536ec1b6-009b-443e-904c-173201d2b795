package com.zte.iccp.itech.extension.openapi.maintenance;

import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.model.ApproverConfigurationSave;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.SubQueryFilter;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CustomizedNetworkSetConsts.ID;

/**
 * 运维接口类
 *
 * <AUTHOR>
 * @create 2025/1/22 下午2:12
 */
public class MaintenanceOpenapi extends AbstractOpenApi {

    /**
     * 刷新审核人配置 片区字段赛错成代表处接口
     */
    public void updateApproverConfigRegion() {
        SubQueryFilter subQueryFilter = new SubQueryFilter(new Filter(CommonFieldConsts.ID, Comparator.IN, Arrays.asList(1)),
                "SELECT id FROM approver_config WHERE LENGTH(organization_region_id_path_ext) - " +
                        "LENGTH(REPLACE(organization_region_id_path_ext, '/', '')) = 3 AND is_deleted =0");
        List<ApproverConfigurationSave> configurations = QueryDataHelper.query(ApproverConfigurationSave.class,
                Arrays.asList(ID, APPROVAL_NODE, ORGANIZATION_REGION), Arrays.asList(subQueryFilter));
        configurations.forEach(item -> {
            List<TextValuePair> region = item.getOrganizationRegion();
            if (!CollectionUtils.isEmpty(region)) {
                String value = region.get(0).getValue();
                String zh = region.get(0).getTextByLanguage(ZH_CN);
                String en = region.get(0).getTextByLanguage(EN_US);
                String[] zhSplit = zh.split(FORWARD_SLASH);
                if (zhSplit.length <= 1) {
                    region = TextValuePairHelper.buildList(ResponsibleUtils.getRegion(value), zh, en);
                } else {
                    region = TextValuePairHelper.buildList(ResponsibleUtils.getRegion(value), zhSplit[1], zhSplit[1]);
                }
                item.setOrganizationRegion(region);
                item.setApprovalNode(null);
            }
            item.setOrganizationRegion(region);
        });

        SaveDataHelper.batchUpdate(configurations);
    }

}
