package com.zte.iccp.itech.extension.openapi.mobile.approval.permission;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.PermissionApplication;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.openapi.mobile.approval.permission.dto.PermissionApprovalDto;
import com.zte.iss.approval.sdk.bean.OpinionDTO;
import com.zte.iss.approval.sdk.client.ApprovalTaskClient;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.helper.FlowHelper.getSystemNodeTask;
import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.*;
import static com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum.PERMISSION_APPLICATION_RETURN;

/**
 * <AUTHOR>
 * @date 2024/11/28 下午5:13
 */
public class ApprovalOpenApi extends AbstractOpenApi {

    private static final List<String> ADD_AUTH_NODE_CODES = Lists.newArrayList(
            DIRECT_MANAGEMENT_LEADER_APPROVAL_NODE,
            PRODUCT_TECHNOLOGY_SECTION_CHIEF_APPROVAL_NODE);


    public ServiceData<Void> agree(@RequestBody @NonNull PermissionApprovalDto dto) {
        ServiceData<Void> serviceData = new ServiceData<>();

        // 1.检索权限申请单
        PermissionApplication permissionApplication = PermissionApplicationAbility.queryByBillNo(dto.getBillNo());
        if (null == permissionApplication) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.DATA_NOT_FOUND));
        }

        // 2.检索任务数据
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                permissionApplication.getId(), AssignmentTypeEnum.PERMISSION_APPLICATION, Assignment.class);
        if (null == assignment) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.DATA_NOT_FOUND));
        }

        // 3.检索审批任务
        ApproveTask approveTask = getApproveTask(permissionApplication.getId());

        // 4.包装提交审批信息
        OpinionDTO opinionDTO = new OpinionDTO();
        opinionDTO.setHandler(ContextHelper.getEmpNo());
        opinionDTO.setAppId(ContextHelper.getUacAppId());
        opinionDTO.setAppCode(ContextHelper.getUacAppId());
        opinionDTO.setSecretKey(ContextHelper.getAccessSecret());
        opinionDTO.setResult(APPROVAL_RESULT_Y);
        opinionDTO.setOpinion(dto.getApprovalOpinion());

        opinionDTO.setTaskId(approveTask.getTaskId());

        // 5.提交审批信息
        // 最终节点审批通过，需要 UPP 创建对应权限
        ApprovalTaskClient.submit(opinionDTO);
        if (ADD_AUTH_NODE_CODES.contains(approveTask.getNodeCode())) {
            // (1) UPP 创建权限
            PermissionApplicationAbility.agree(permissionApplication);

            // (2) 更新任务信息
            Assignment updateAssignment = new Assignment();
            updateAssignment.setId(assignment.getId());
            updateAssignment.setCurrentProcessorEmployee(Lists.newArrayList());
            updateAssignment.setAssignmentStatus(AssignmentStatusEnum.VALID.getValue());
            AssignmentAbility.update(updateAssignment);
        }

        // 6.更新人员关联关系
        AssignmentAbility.updateHandledByMeRelevance(assignment.getId(), ContextHelper.getEmpNo(), BoolEnum.Y);

        return serviceData;
    }

    private static ApproveTask getApproveTask(String id) {
        List<ApproveTask> taskList = getSystemNodeTask(id);
        if (CollectionUtils.isEmpty(taskList)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.APPROVAL_FLOW_NOT_FOUND));
        }
        Map<String, ApproveTask> taskApproverMap = taskList.stream()
                .collect(Collectors.toMap(ApproveTask::getApprover, Function.identity()));

        ApproveTask approveTask = taskApproverMap.get(ContextHelper.getEmpNo());
        if (null == approveTask || StringUtils.isBlank(approveTask.getTaskId())) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.APPROVAL_FLOW_NOT_FOUND));
        }
        return approveTask;
    }

    public ServiceData<Void> rollback(@RequestBody @NonNull PermissionApprovalDto dto) {
        ServiceData<Void> serviceData = new ServiceData<>();
        String userId = ContextHelper.getEmpNo();

        // 1.检索权限申请单
        PermissionApplication permissionApplication = PermissionApplicationAbility.queryByBillNo(dto.getBillNo());
        if (null == permissionApplication) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.DATA_NOT_FOUND));
        }

        // 2.检索任务数据
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                permissionApplication.getId(), AssignmentTypeEnum.PERMISSION_APPLICATION, Assignment.class);
        if (null == assignment) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.DATA_NOT_FOUND));
        }

        // 3.检索审批任务
        ApproveTask approveTask = getApproveTask(permissionApplication.getId());

        // 4.会签节点提交对应信息
        if (PA_REPRESENTATIVE_OFFICE_APPROVAL_NODE_ID.equals(approveTask.getNodeCode())) {
            // (1) 包装审批信息
            OpinionDTO opinionDTO = new OpinionDTO();
            opinionDTO.setHandler(ContextHelper.getEmpNo());
            opinionDTO.setAppId(ContextHelper.getUacAppId());
            opinionDTO.setAppCode(ContextHelper.getUacAppId());
            opinionDTO.setSecretKey(ContextHelper.getAccessSecret());
            opinionDTO.setResult(APPROVAL_RESULT_N);
            opinionDTO.setOpinion(dto.getApprovalOpinion());
            opinionDTO.setTaskId(approveTask.getTaskId());

            // (2) 提交审批信息
            ApprovalTaskClient.submit(opinionDTO);

            // (2) 更新任务信息
            Assignment updateAssignment = new Assignment();
            updateAssignment.setId(assignment.getId());

            // 若无剩余当前处理人，说明驳回到起始
            // 有剩余当前处理人，去除自己
            List<Employee> currentProcessors = assignment.getCurrentProcessorEmployee();
            currentProcessors = currentProcessors.stream()
                    .filter(item -> !userId.equals(item.getEmpUIID()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currentProcessors)) {
                updateAssignment.setCurrentProcessorEmployee(permissionApplication.getApplicant());
                updateAssignment.setAssignmentStatus(
                        AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.getValue());
            } else {
                updateAssignment.setCurrentProcessorEmployee(currentProcessors);
            }
            AssignmentAbility.update(updateAssignment);

            // (3) 更新关联人信息
            AssignmentAbility.updateHandledByMeRelevance(assignment.getId(), ContextHelper.getEmpNo(), BoolEnum.Y);

            return serviceData;
        }

        FlowHelper.rollbackToStarter(permissionApplication.getId(), PERMISSION_APPLICATION, dto.getApprovalOpinion());
        AsyncExecuteUtils.execute(() -> PermissionApplicationAbility.sendEmail(permissionApplication, PERMISSION_APPLICATION_RETURN));

        // (2) 更新任务信息
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setCurrentProcessorEmployee(permissionApplication.getApplicant());
        updateAssignment.setAssignmentStatus(AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.getValue());
        AssignmentAbility.update(updateAssignment);

        // (3) 更新关联人信息
        AssignmentAbility.updateHandledByMeRelevance(assignment.getId(), ContextHelper.getEmpNo(), BoolEnum.Y);

        return serviceData;
    }
}
