package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.OpAssocProdAbility;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdDevIntApproval;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdDevMgrConfirm;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;

/**
 * <AUTHOR> jiangjiawen
 * @date 2024/10/15
 */
public class RdIntegrationAfterPlugin extends BaseFlowOperationPlugin {

    @Override
    public void afterOperate(ExecuteEvent executeEvent) {
        JSONObject args = executeEvent.getArgs();
        String taskId = (String) args.get(ApprovalConstants.TASK_ID);
        OpAssocProdAbility.integrationAfterOperate(
                getPkId(), taskId, OpAssocProdDevIntApproval.class, OpAssocProdDevMgrConfirm.class);
    }
}
