package com.zte.iccp.itech.extension.openapi.model.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@ApiModel("版本信息查询条件(用于兼容服务对象)")
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class VersionQuery {

    @ApiModelProperty("版本编码")
    private String versionCode;
}
