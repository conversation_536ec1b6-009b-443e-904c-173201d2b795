package com.zte.iccp.itech.extension.domain.enums.permissionapplication;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum RoleEnum implements SingletonTextValuePairsProvider {

    /** 执行项目经理 */
    EXECUTIVE_PROJECT_MANAGER(
            "iTech0001",
            "执行项目经理",
            "Executive Project Manager",
            "permissionApplication.roleDescription.executiveProjectManager"),

    /** 技术总监 */
    TECHNICAL_DIRECTOR(
            "iTech0002",
            "技术总监",
            "Technical Director",
            "permissionApplication.roleDescription.technicalDirector"),

    /** 产品工程师 */
    PRODUCT_ENGINEER(
            "iTech0003",
            "产品工程师",
            "Product Engineer",
            "permissionApplication.roleDescription.productEngineer"),

    /** 项目管理员 */
    PROJECT_ADMINISTRATOR(
            "iTech0004",
            "项目管理员",
            "Project Administrator",
            "permissionApplication.roleDescription.projectAdministrator"),

    /** 产品总工 */
    CHIEF_PRODUCT_ENGINEER(
            "iTech0005",
            "产品总工",
            "Chief Product Engineer",
            "permissionApplication.roleDescription.chiefProductEngineer"),

    /** 产品总监 */
    PRODUCT_DIRECTOR(
            "iTech0006",
            "产品总监",
            "Product Director",
            "permissionApplication.roleDescription.productDirector"),

    /** 研发总工 */
    DEVELOPMENT_CHIEF_ENGINEER(
            "iTech0007",
            "研发总工",
            "R&D Chief Engineer",
            "permissionApplication.roleDescription.rdChiefEngineer"),

    /** 研发管理员 */
    DEVELOPMENT_ADMINISTRATOR(
            "iTech0008",
            "研发管理员",
            "R&d Administrator",
            "permissionApplication.roleDescription.rdAdministrator"),

    /** 国内合作方工程师 */
    DOMESTIC_PARTNER_ENGINEER(
            "iTech0009",
            "国内合作方工程师",
            "Domestic Partner Engineer",
            "permissionApplication.roleDescription.domesticPartnerEngineer"),

    /** 国际合作方工程师 */
    INTERNATIONAL_PARTNER_ENGINEER(
            "iTech0010",
            "国际合作方工程师",
            "International Partner Engineer",
            "permissionApplication.roleDescription.internationalPartnerEngineer"),

    /** 数据核查员 */
    DATA_CHECKER(
            "iTech0011",
            "数据核查员",
            "Data Checker",
            "permissionApplication.roleDescription.dataChecker"),

    /** 组织级管理层 */
    ZTE_LEADERS(
            "iTech0012",
            "公司领导",
            "Leaders of ZTE",
            "permissionApplication.roleDescription.zteLeader"),

    /** 组织级管理层 */
    ORGANIZATION_LEVEL_ADMINISTRATOR(
            "iTech0013",
            "组织级管理层",
            "Organization-level Administrator",
            "permissionApplication.roleDescription.organizationLevelAdministrator"),

    /** 系统运维人员 */
    SYSTEM_MAINTENANCE_PERSONNEL(
            "iTech0014",
            "系统运维人员",
            "System Operation and Maintenance Personnel",
            "permissionApplication.roleDescription.systemOperationAndMaintenancePersonnel"),

    /** 系统管理员 */
    SYSTEM_ADMINISTRATOR(
            "iTech0015",
            "系统管理员",
            "System administrator",
            "permissionApplication.roleDescription.systemAdministrator");



    /** 角色编码 */
    private final String roleCode;

    /** 中文名称 */
    private final String nameZh;

    /** 英文名称 */
    private final String nameEn;

    /** 角色描述 */
    private final String roleDescription;


    @Override
    public String getValue() {
        return this.roleCode;
    }
}
