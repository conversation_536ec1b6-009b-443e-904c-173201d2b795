package com.zte.iccp.itech.extension.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * aes加解密工具类
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/17
 */
@Slf4j
public class AesUtils {

    /**
     * ENCRYPTION_ALGORITHM_AES
     */
    private static final String ENCRYPTION_ALGORITHM_AES = "AES";

    /**
     * CIPHER_MODE_WITH_AES_GCM_PKCS5PADDING
     */
    private static final String CIPHER_MODE_WITH_AES_GCM_PKCS5PADDING = "AES/GCM/PKCS5Padding";

    /**
     * aes加密
     *
     * @param content 需要加密的内容
     * @param pkey 密钥
     * @return 加密数据
     */
    public static String aesGcmEncrypt(String content, String pkey) {
        try {
            SecretKey secretKey = new SecretKeySpec(pkey.getBytes(), ENCRYPTION_ALGORITHM_AES);
            Cipher cipher = Cipher.getInstance(CIPHER_MODE_WITH_AES_GCM_PKCS5PADDING);
            cipher.init(1, secretKey);
            byte[] iv = cipher.getIV();

            assert iv != null && iv.length == 12;

            byte[] encryptData = cipher.doFinal(content.getBytes());

            assert encryptData.length == content.getBytes().length + 16;

            byte[] message = new byte[12 + content.getBytes().length + 16];
            System.arraycopy(iv, 0, message, 0, 12);
            System.arraycopy(encryptData, 0, message, 12, encryptData.length);
            return Base64.encodeBase64String(message);
        } catch (Exception var7) {
            log.error("AES/GCM/PKCS5Padding/256 encrypt failed,error:", var7);
            return null;
        }
    }


    /**
     * aes解密
     *
     * @param content 解密的内容
     * @param pkey 密钥key
     * @return 解密数据
     */
    public static String aesGcmDecrypt(String content, String pkey) {
        try {
            SecretKey secretKey = new SecretKeySpec(pkey.getBytes(), ENCRYPTION_ALGORITHM_AES);
            Cipher cipher = Cipher.getInstance(CIPHER_MODE_WITH_AES_GCM_PKCS5PADDING);
            cipher.init(1, secretKey);
            byte[] message = Base64.decodeBase64(content);
            if (message.length < 28) {
                throw new IllegalArgumentException();
            } else {
                GCMParameterSpec params = new GCMParameterSpec(128, message, 0, 12);
                cipher.init(2, secretKey, params);
                byte[] decryptData = cipher.doFinal(message, 12, message.length - 12);
                String decrypt = new String(decryptData);
                return decrypt;
            }
        } catch (Exception var8) {
            log.error("AES/GCM/PKCS5Padding/256 decrypt failed,error:", var8);
            return null;
        }
    }
}