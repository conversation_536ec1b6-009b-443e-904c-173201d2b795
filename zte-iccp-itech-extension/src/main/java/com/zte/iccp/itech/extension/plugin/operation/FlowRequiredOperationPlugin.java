package com.zte.iccp.itech.extension.plugin.operation;

import com.google.common.collect.Lists;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.OperationLog.SUBMISSION_FAILED_PLEASE_SAVE_REFRESH_AGAIN;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.NetServiceDeptAppConsts.RD_REQ;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.TdNetDeptApproveConsts.IS_NET_DEPT_APPROVAL;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum.NET_SERVICE_DEPT_APP;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum.TD_NET_DEPT_APPROVE;

/**
 * 流程提交按钮必填字段后台校验
 *
 * <AUTHOR>
 * @create 2025/5/23 下午1:55
 */
public class FlowRequiredOperationPlugin extends BaseOperationPlugin {

    // 审批节点对应必填字段
    private final Map<String, FlowRequiredField> NODE_CHECK_MAP = MapUtils.newHashMap(
            /*技术交付部/网络处审核*/
            TD_NET_DEPT_APPROVE.name(), new FlowRequiredField(
                    TdNetDeptApproveConsts.APPROVE_RESULT,
                    TdNetDeptApproveConsts.APPROVE_OPINION,
                    // 初始必填字段： 审核意见 、需要升级至研发、重要程度、风险等级、操作等级、是否紧急操作、是否封网操作、需要升级至网络服务部
                    Lists.newArrayList(IMPORTANCE, RISK_EVALUATION, OPERATION_LEVEL, IS_EMERGENCY_OPERATION,
                            IS_NET_CLOSE_OR_CONTROL_OPERATION, IS_NET_DEPT_APPROVAL),
                    new LinkedList<HiddenCheckField>() {{
                            // 需要升级至网络服务部 = 是
                        add(new HiddenCheckField(IS_NET_DEPT_APPROVAL, ENABLED_FLAG,
                                    // 网络服务部审核人 必填
                                    Lists.newArrayList(NETWORK_SERVICE_DEPT_APPROVER)));
            }}),

            /*网络服务部审核*/
            NET_SERVICE_DEPT_APP.name(), new FlowRequiredField(
                    NetServiceDeptAppConsts.APPROVE_RESULT,
                    NetServiceDeptAppConsts.APPROVE_OPINION_NET_SERVICE_DEPT,
                    // 初始必填字段：需要升级至研发
                    Lists.newArrayList(IS_DEV_DEPT_APPROVAL),
                    new LinkedList<HiddenCheckField>() {{
                            // 需要升级至研发 = 是
                        add(new HiddenCheckField(IS_DEV_DEPT_APPROVAL, ENABLED_FLAG,
                                    // 研发经理 、对研发的要求 必填
                                    Lists.newArrayList(ApproverFieldConsts.RD_MANAGER, RD_REQ)));
            }})
    );


    @Override
    public boolean beforeExecuteValidate(ExecuteEvent executeEvent) {
        // 校验开关
        if (!ConfigHelper.getBoolean(CHANGE_NETWORK_CHECK_ENABLE)) {
            return true;
        }

        String currentNodeCode = FlowHelper.getCurrentNodeCode(getPkId());
        FlowRequiredField flowRequiredField = NODE_CHECK_MAP.get(currentNodeCode);

        if (flowRequiredField == null) {
            return true;
        }

        Object opinionValue = getModel().getValue(flowRequiredField.getOpinion());

        String result = TextValuePairHelper.getValue(getModel().getValue(flowRequiredField.getResult()));
        String opinion = opinionValue == null ? null : opinionValue.toString();
        //  操作结果 or 审核意见 不能为空
        if (!StringUtils.hasText(result) || !StringUtils.hasText(opinion)) {
            getView().showMessage(SUBMISSION_FAILED_PLEASE_SAVE_REFRESH_AGAIN, MsgType.ERROR);
            return false;
        }
        //  审核不通过  不需要校验其他必填字段
        if (!PASS.equals(result)) {
            return true;
        }

        // 必填字段校验
        if (!checkRequiredFields(flowRequiredField)) {
            return false;
        }

        return true;
    }

    private boolean checkRequiredFields(FlowRequiredField flowRequiredField) {
        // 第一层页面初始化必填字段校验
        boolean hasInvalidField = flowRequiredField.getRequiredFields().stream()
                .map(i -> getModel().getValue(i))
                .anyMatch(value -> value == null
                        || !StringUtils.hasText(value.toString())
                        || EMPTY_SET.equals(value.toString()));

        if (hasInvalidField) {
            getView().showMessage(SUBMISSION_FAILED_PLEASE_SAVE_REFRESH_AGAIN, MsgType.ERROR);
            return false;
        }

        // 第二层动态必填字段校验
        boolean hasInvalidDynamicField = flowRequiredField.getCheckFields().stream()
                // 填写值和对应值相等
                .filter(checkField -> checkField.getValue().equals(TextValuePairHelper.getValue(getModel().getValue(checkField.getDynamics()))))
                // 检验新展示的字段是否必填
                .flatMap(checkField -> checkField.getRequiredFields().stream())
                .map(field -> getModel().getValue(field))
                .anyMatch(value -> value == null
                        || !StringUtils.hasText(value.toString())
                        || EMPTY_SET.equals(value.toString()));

        if (hasInvalidDynamicField) {
            getView().showMessage(SUBMISSION_FAILED_PLEASE_SAVE_REFRESH_AGAIN, MsgType.ERROR);
            return false;
        }
        return true;
    }

    /**
     * 初始化页面必填字段校验类
     */
    @Getter
    @RequiredArgsConstructor
    class FlowRequiredField {
        // 操作结果
        private final String result;
        // 审核意见
        private final String opinion;
        // 必填字段
        private final List<String> requiredFields;
        // 隐藏校验字段
        private final LinkedList<HiddenCheckField> checkFields;
    }

    /**
     * 隐藏字段展示后的必填字段校验
     */
    @Getter
    @RequiredArgsConstructor
    class HiddenCheckField {
        // 判断字段
        private final String dynamics;
        //  判断字段满足展示的值
        private final String value;
        // 必填字段
        private final List<String> requiredFields;
    }
}
