package com.zte.iccp.itech.extension.openapi.iversion;

import com.zte.iccp.itech.extension.ability.VersionAbility;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.vo.VersionServiceVo;
import com.zte.iccp.itech.extension.openapi.model.version.VersionQuery;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Objects;

public class IVersionInfoOpenapi extends AbstractOpenApi {
    /**
     * 检索版本信息
     * 可同时检索 CSC / iVersion
     */
    public ServiceData<PageRows<VersionServiceVo>> queryVersionInfo(@RequestBody VersionQuery versionQuery) {
        ServiceData<PageRows<VersionServiceVo>> serviceData = new ServiceData<>();
        String versionCode = versionQuery.getVersionCode();
        if (!StringUtils.hasText(versionCode)) {
            return serviceData;
        }

        String[] versionCodeArray = versionCode.split(CommonConstants.UNDER_SCORE);
        VersionServiceVo versionInfo;
        if (CidConstants.SystemName.CSC.equals(versionCodeArray[0])) {
            versionInfo = VersionAbility.queryCscVersionInfo(versionCodeArray[1]);
        } else if (versionCodeArray.length > 1) {
            versionInfo = VersionAbility.queryIVersionInfo(versionCodeArray[1], true);
        } else {
            versionInfo = VersionAbility.queryIVersionInfo(versionCode, false);
        }

        PageRows<VersionServiceVo> pageRows = new PageRows<>();
        pageRows.setTotal(1);
        pageRows.setCurrent(1);
        pageRows.setRows(Objects.isNull(versionInfo)
                ? Lists.newArrayList()
                : Lists.newArrayList(versionInfo));
        serviceData.setBo(pageRows);
        return serviceData;
    }
}