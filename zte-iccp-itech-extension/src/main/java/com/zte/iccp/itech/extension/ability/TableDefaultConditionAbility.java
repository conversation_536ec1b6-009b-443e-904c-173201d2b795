package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FilterHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.backlog.BacklogTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.*;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.sql.PreparedStatement;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.PERCENT;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.CHECK_DATE_MONTHS_BETWEEN_ERROR;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.PRODUCT_MANAGEMENT_TEAM;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;

public class TableDefaultConditionAbility {

    /** 默认时间区间 - 半年（6 个月） */
    private static final int DEFAULT_MONTH_SECTION = 6;

    /**
     * 高级查询 - 默认下拉查询条件
     * @return IFilter
     */
    public static IFilter defaultDropDownFilter(
            IDataModel dataModel,
            String componentCid,
            String fieldId) {

        // 1.获取 单选 / 下拉单选 / 复选 / 下拉复选 组件填报内容
        List<TextValuePair> componentInfo = ComponentUtils.getChooseComponentInfo(dataModel, componentCid);
        if (CollectionUtils.isEmpty(componentInfo)) {
            return null;
        }

        // 2.包装过滤条件
        List<String> componentValues = TextValuePairHelper.getValueList(componentInfo);
        return new Filter(fieldId, Comparator.IN, componentValues);
    }

    /**
     * 高级查询条件 - 默认时间区间条件
     */
    public static IFilter defaultDateTimeSectionFilter(
            IDataModel dataModel,
            String componentCid,
            String fieldId) {

        // 1.获取 创建时间 组件填写数据
        Pair<Date, Date> dateSection = ComponentUtils.getDateSectionComponentInfo(dataModel, componentCid);
        Date startDate = dateSection.getLeft();
        Date endDate = DateUtils.addDay(dateSection.getRight(), 1);
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return null;
        }

        // 2.包装过滤条件
        return FilterHelper.newMultiFilter(
                new Filter(fieldId, Comparator.GE, startDate)
                        .and(new Filter(fieldId, Comparator.LT, endDate)));
    }

    /**
     * 高级查询 - 默认员工条件
     */
    public static IFilter defaultEmployeeFilter(
            IDataModel dataModel,
            String componentCid,
            String fieldId,
            boolean multipleChoice) {

        // 1.获取 员工 组件填写数据
        List<Employee> employeeInfo = ComponentUtils.getEmployeeComponentInfo(dataModel, componentCid);
        if (CollectionUtils.isEmpty(employeeInfo)) {
            return null;
        }

        // 2.包装过滤条件
        Comparator comparator = multipleChoice ? Comparator.IN : Comparator.EQ;
        List<String> employeeIds = employeeInfo.stream().map(Employee::getEmpUIID).distinct().collect(Collectors.toList());
        return new Filter(fieldId, comparator, employeeIds);
    }

    /**
     * 默认查询 - 我已处理
     */
    public static IFilter assignmentHandledByMeFilter() {
        String subQuerySql =
                String.format("SELECT apr.assignment_id FROM assignment_person_relevance apr"
                                + " WHERE apr.relevant = '%s'"
                                + " AND apr.self_handler_flag = 'Y'"
                                + " AND apr.is_deleted = 0",
                        ContextHelper.getEmpNo());
        return new SubQueryFilter(
                new Filter(ID, Comparator.IN, Lists.newArrayList(CommonConstants.INTEGER_ONE)), subQuerySql);
    }

    /**
     * 高级查询条件 是否审批任务
     * 注意：不同场景查询的表不同：①待我处理:assignment；②我已处理：assignment_person_relevance，我已处理走子查询方式；③我发起的：不涉及这个查询条件
     * @param dataModel
     * @param backlogTypeEnum
     * @param componentCid
     */
    public static IFilter approveTaskFilter(IDataModel dataModel, BacklogTypeEnum backlogTypeEnum, String componentCid) {
        // 我发起的不涉及此查询条件
        if(BacklogTypeEnum.INITIATED_BY_ME.equals(backlogTypeEnum)){
            return null;
        }

        // 1.获取 是否审批任务 组件填写数据
        Object approveTaskInfo = dataModel.getValue(componentCid);
        if (ObjectUtils.isEmpty(approveTaskInfo)) {
            return null;
        }
        List<TextValuePair> approveTask = JsonUtils.parseArray(approveTaskInfo,TextValuePair.class);
        if (CollectionUtils.isEmpty(approveTask)) {
            return null;
        }

        // 2.包装过滤条件
        String approvalTaskFlag = approveTask.get(0).getValue();

        // 3.不同场景查询的表不同：①待我处理:assignment；②我已处理：assignment_person_relevance
        if(BacklogTypeEnum.TO_BE_HANDLED_BY_ME.equals(backlogTypeEnum)){
            return new Filter(AssignmentFieldConsts.APPROVAL_TASK_FLAG, Comparator.EQ, Lists.newArrayList(approvalTaskFlag));
        }

        // 我已处理：【是否审批任务】、【关联人员】拼接入参
        String subQuerySql = String.format("select apr.assignment_id from assignment_person_relevance apr where apr.approval_task_flag = '%s' and apr.relevant = '%s' and apr.is_deleted = 0"
                ,approvalTaskFlag
                ,ContextHelper.getEmpNo());

        return new SubQueryFilter(new Filter(ID, Comparator.IN, Lists.newArrayList(CommonConstants.INTEGER_ONE)), subQuerySql);
    }

    /**
     * 高级查询条件 - 创建时间（时间区间，校验条件：不能超过3个月）
     * @param dataModel
     * @param componentCid
     */
    public static IFilter backlogCreatedTimeFilter(IDataModel dataModel, String componentCid) {
        // 1.获取 创建时间 组件填写数据
        Pair<Date, Date> createDateSection = ComponentUtils.getDateSectionComponentInfo(dataModel, componentCid);
        Date startDate = createDateSection.getLeft();
        Date endDate = createDateSection.getRight();

        // 2.组件填写数据非空，校验时间区间
        // 组件数据为空，查询最近半年
        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            boolean validInterval
                    = DateUtils.checkMonthInterval(startDate, endDate, DEFAULT_MONTH_SECTION);
            if (!validInterval){
                throw new LcapBusiException(
                        MsgUtils.getMessage(CHECK_DATE_MONTHS_BETWEEN_ERROR), DEFAULT_MONTH_SECTION);
            }
        } else {
            endDate = new Date();
            startDate = DateUtils.addMonths(endDate, -DEFAULT_MONTH_SECTION);
        }

        // 2.包装过滤条件
        return FilterHelper.newMultiFilter(
                new Filter(CREATE_TIME, Comparator.GE, startDate)
                        .and(new Filter(CREATE_TIME, Comparator.LT, DateUtils.addDay(endDate, 1))));
    }

    /**
     * 高级查询条件 - 任务中心 / 待办中心 - 任务名称 / 编码
     * @param dataModel
     * @param componentCid
     * @return IFilter
     */
    public static IFilter assignmentNameAndCodeFilter(IDataModel dataModel, String componentCid) {
        // 1.获取 任务名称 组件填写数据
        String nameCodeInfo = PropertyValueConvertUtil.getString(dataModel.getValue(componentCid));
        if (!StringUtils.hasText(nameCodeInfo)) {
            return null;
        }

        // 2.包装过滤条件
        String nameCodeCondition = CommonConstants.PERCENT + nameCodeInfo + CommonConstants.PERCENT;
        return FilterHelper.newMultiFilter(
                new Filter(AssignmentFieldConsts.ASSIGNMENT_NAME, Comparator.LIKE, nameCodeCondition)
                        .or(new Filter(AssignmentFieldConsts.ASSIGNMENT_CODE, Comparator.LIKE, nameCodeCondition)));
    }

    /**
     * 高级查询条件 - 任务中心 / 待办中心 - 任务名称 / 编码（兼容子级任务）
     */
    public static IFilter assignmentNameAndCodeWithSubFilter(
            IDataModel dataModel,
            String componentCid) {

        // 1.获取组件填写数据
        String nameCodeInfo = PropertyValueConvertUtil.getString(dataModel.getValue(componentCid));
        if (!StringUtils.hasText(nameCodeInfo)) {
            return null;
        }

        // 输入清洗
        nameCodeInfo = sanitizeInput(nameCodeInfo);

        // 2.封装查询条件
        String subQuerySql = String.format(
                "select distinct bill_id from assignment"
                        + " where (assignment_name like '%%%s%%' or assignment_code like '%%%s%%')"
                        + " and is_deleted = 0",
                nameCodeInfo, nameCodeInfo);
        return new SubQueryFilter(
                new Filter(
                        AssignmentFieldConsts.BILL_ID,
                        Comparator.IN,
                        Lists.newArrayList(CommonConstants.INTEGER_ONE)),
                subQuerySql);
    }

    /**
     * 清洗输入中的危险字符
     * @param input 用户输入
     * @return 安全的字符串
     */
    private static String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }

        // 转义单引号（SQL标准转义方式）
        String sanitized = input.replace("'", "''");

        // 移除分号（防止多语句执行）
        sanitized = sanitized.replace(";", "");

        return sanitized;
    }

    /**
     * 高级查询条件 - 任务中心 / 代办中心 - 任务名称
     * @param dataModel
     * @param componentCid
     */
    public static IFilter assignmentNameFilter(IDataModel dataModel, String componentCid) {
        // 1.获取 任务名称 组件填写数据
        String nameInfo = PropertyValueConvertUtil.getString(dataModel.getValue(componentCid));
        if (!StringUtils.hasText(nameInfo)) {
            return null;
        }

        // 2.包装过滤条件
        return new Filter(AssignmentFieldConsts.ASSIGNMENT_NAME, Comparator.LIKE, CommonConstants.PERCENT + nameInfo + CommonConstants.PERCENT);
    }

    /**
     * 高级查询条件 - 任务中心 / 代办中心 - 任务编码
     * @param dataModel
     * @param componentCid
     */
    public static IFilter assignmentCodeFilter(IDataModel dataModel, String componentCid) {
        // 1.获取 任务编码 组件填写数据
        String codeInfo = PropertyValueConvertUtil.getString(dataModel.getValue(componentCid));
        if (!StringUtils.hasText(codeInfo)) {
            return null;
        }

        // 2.包装过滤条件
        return new Filter(AssignmentFieldConsts.ASSIGNMENT_CODE, Comparator.LIKE, CommonConstants.PERCENT + codeInfo + CommonConstants.PERCENT);
    }

    /**
     * 高级查询条件 - 任务中心 / 代办中心 - 任务类型
     * @param dataModel
     * @param componentCid
     */
    public static IFilter assignmentTypeFilter(IDataModel dataModel, String componentCid) {
        // 1.获取 任务状态 组件填写数据
        List<TextValuePair> assignmentTypeInfo = ComponentUtils.getChooseComponentInfo(dataModel, componentCid);
        if (CollectionUtils.isEmpty(assignmentTypeInfo)) {
            return null;
        }

        // 2.枚举校验
        // 全部 不在枚举范围内，不增加 IFilter 即可
        List<String> assignmentTypeValueList = TextValuePairHelper.getValueList(assignmentTypeInfo);
        for (String assignmentTypeValue : assignmentTypeValueList) {
            AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromValue(assignmentTypeValue);
            if (Objects.isNull(assignmentType)) {
                return null;
            }
        }

        // 3.包装过滤条件
        return new Filter(AssignmentFieldConsts.ASSIGNMENT_TYPE, Comparator.IN, assignmentTypeValueList);
    }

    /**
     * 高级查询条件 - 任务中心 / 代办中心 - 任务状态
     */
    public static IFilter assignmentStatusFilter(
            IDataModel dataModel,
            AssignmentTypeEnum assignmentType,
            String componentCid) {

        // 1.获取 任务状态 组件填写数据
        List<TextValuePair> assignmentStatusInfo = ComponentUtils.getChooseComponentInfo(dataModel, componentCid);
        if (CollectionUtils.isEmpty(assignmentStatusInfo)) {
            return null;
        }

        // 2.枚举校验
        // 全部 不在枚举范围内，不增加 IFilter 即可
        List<String> assignmentStatusValues = TextValuePairHelper.getValueList(assignmentStatusInfo);
        for (String assignmentStatusValue : assignmentStatusValues) {
            AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignmentStatusValue);
            if (Objects.isNull(assignmentStatus)) {
                return null;
            }
        }

        // 3.内部网络变更单、合作方网络变更单针对用户选择了【待开始】状态，要默认加上【审批驳回-待启动】状态
        if (AssignmentTypeEnum.NETWORK_CHANGE.equals(assignmentType)
                || AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(assignmentType)) {
            if (assignmentStatusValues.contains(AssignmentStatusEnum.START.getValue())) {
                assignmentStatusValues.add(AssignmentStatusEnum.APPROVE_START.getValue());
            }
        }

        // 4.包装过滤条件
        return new Filter(AssignmentFieldConsts.ASSIGNMENT_STATUS, Comparator.IN, assignmentStatusValues);
    }

    /**
     * 高级查询条件 - 任务中心 / 待办中心 - 组织
     */
    public static IFilter assignmentOrganizationFilter(
            IDataModel dataModel,
            String componentCid,
            String fieldId,
            Integer treeLevel) {

        // 1.获取 组织 组件填写数据
        List<TextValuePair> organizationInfo = ComponentUtils.getChooseComponentInfo(dataModel, componentCid);
        if (CollectionUtils.isEmpty(organizationInfo)) {
            return null;
        }

        // 2.拆分组织路径
        // 本地数据未保存组织全路径，拆分代表处层级路径用于检索
        List<String> conditions = Lists.newArrayList();
        for (TextValuePair organization : organizationInfo) {
            conditions.addAll(Lists.newArrayList(
                    ResponsibleUtils.specificLevelId(organization.getValue(), treeLevel)));
        }

        // 3.包装过滤条件
        return new Filter(fieldId, Comparator.IN, conditions);
    }

    /**
     * 高级查询 - 待办中心-汇总任务 - 产品树（适用于：单选，仅匹配第一层级）
     */
    public static IFilter assignmentProductTeamFilter(IDataModel dataModel, String componentCid) {
        // 1.获取 产品树 组件填写数据
        List<TextValuePair> productTreeInfo = ComponentUtils.getChooseComponentInfo(dataModel, componentCid);
        if (CollectionUtils.isEmpty(productTreeInfo)) {
            return null;
        }

        // 2.获取产品分类填写数据对应产品经营团队路径
        List<String> productIds = productTreeInfo.stream().map(TextValuePair::getValue).collect(Collectors.toList());
        String productTeamId = productIds.get(0).split(CommonConstants.FORWARD_SLASH)[0];

        // 3.检索 NIS，获取对应 PDM 信息
        List<BasicProductInfo> productTeamInfo = NisClient.queryProductInfo(Lists.newArrayList(productTeamId));
        String productTeamPdmNo = CollectionUtils.isEmpty(productTeamInfo)
                ? CommonConstants.EMPTY_STRING
                : productTeamInfo.get(0).getCode();

        // 3.包装过滤条件
        return FilterHelper.newMultiFilter(
                new JsonFilter(
                        AssignmentFieldConsts.PRODUCT_CLASSIFICATION, Comparator.LIKE, productTeamId + PERCENT)
                        .extractIndex(0)
                        .or(new Filter(
                                AssignmentFieldConsts.FaultManagementFieldConsts.PDM_PRODUCT,
                                Comparator.LIKE,
                                productTeamPdmNo + PERCENT)));
    }

    /**
     * 高级查询 - 任务中心 / 待办中心 - 操作开始时间/操作开始时间(UTC+8)
     */
    public static IFilter operationStartTimeFilter(IDataModel dataModel, String componentId) {

        // 1.获取 计划开始时间 组件填写数据
        Pair<Date, Date> confirmDateSection = ComponentUtils.getDateSectionComponentInfo(
                dataModel, componentId);
        Date startDate = confirmDateSection.getLeft();
        Date endDate = DateUtils.addDay(confirmDateSection.getRight(), 1);
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return null;
        }

        // 2.封装查询条件
        String startDateStr = DateUtils.dateToString(startDate, CommonConstants.DATE_FORM);
        String endDateStr = DateUtils.dateToString(endDate, CommonConstants.DATE_FORM);

        String subQuerySql = String.format(
                "select distinct a.bill_id "
                        + " from assignment_network_change_ex ance"
                        + " inner join assignment a on ance.id = a.id"
                        + " where ance.%s >= '%s' and ance.%s < '%s'"
                        + " and a.is_deleted = 0",
                componentId, startDateStr, componentId, endDateStr);

        return new SubQueryFilter(
                new Filter(
                        AssignmentFieldConsts.BILL_ID,
                        Comparator.IN,
                        Lists.newArrayList(CommonConstants.INTEGER_ONE)),
                subQuerySql);
    }

    /**
     * 高级查询 - 任务中心 / 待办中心 - 当前进展
     */
    public static IFilter currentProgressFilter(IDataModel dataModel) {
        // 1.获取 当前进展 组件填写数据
        List<String> currentProgresses = TextValuePairHelper.getValueList(
                ComponentUtils.getChooseComponentInfo(
                        dataModel, AssignmentFieldConsts.CURRENT_PROGRESS));
        if (CollectionUtils.isEmpty(currentProgresses)) {
            return null;
        }

        // 2.包装查询条件
        StringBuilder stringBuilder = new StringBuilder();
        currentProgresses.forEach(item -> stringBuilder
                .append(CommonConstants.SINGLE_QUOTES)
                .append(item)
                .append(CommonConstants.SINGLE_QUOTES)
                .append(CommonConstants.COMMA));
        String subQuerySql = String.format(
                "select distinct bill_id from assignment "
                        + "where current_progress in (%s) "
                        + "and is_deleted = 0",
                stringBuilder.deleteCharAt(stringBuilder.lastIndexOf(CommonConstants.COMMA)));

        return new SubQueryFilter(
                new Filter(
                        AssignmentFieldConsts.BILL_ID,
                        Comparator.IN,
                        Lists.newArrayList(CommonConstants.INTEGER_ONE)),
                subQuerySql);
    }

    /**
     * 高级查询条件 - 任务中心 / 代办中心 - 技术管理任务 - 是否超期
     */
    public static IFilter technologyAssignmentOverdueFlagFilter(IDataModel dataModel, String componentCid) {
        // 1.获取 是否超期 组件填写数据
        List<TextValuePair> overdueFlagInfo = ComponentUtils.getChooseComponentInfo(dataModel, componentCid);
        if (CollectionUtils.isEmpty(overdueFlagInfo)) {
            return null;
        }
        boolean overdueFlag = Boolean.parseBoolean(TextValuePairHelper.getValue(overdueFlagInfo));

        // 2.包装过滤条件
        // (1) 执行中 / 审批中 是否超期
        Date formatDate = DateUtils.addDay(new Date(), -1);
        Comparator comparator = overdueFlag ? Comparator.LT : Comparator.GT;
        List<String> assignmentStatusList = Lists.newArrayList(AssignmentStatusEnum.EXECUTE.getValue(), AssignmentStatusEnum.APPROVE.getValue());

        IFilter executeOrApproveOverdueFilter = FilterHelper.newMultiFilter(
                new Filter(AssignmentFieldConsts.TechnologyManagementFieldConsts.REQUIRED_COMPLETION_TIME, comparator, formatDate)
                        .and(new Filter(AssignmentFieldConsts.ASSIGNMENT_STATUS, Comparator.IN, assignmentStatusList)));

        // (2) 已完成超期
        String subQuerySql = "select a.id from assignment a inner join assignment_technology_manage_ex atme on a.id = atme.id"
                + " where a.assignment_status = 4 and a.last_modified_time "
                + (overdueFlag ? ">" : "<")
                + " date_add(atme.required_completion_time, interval 1 day) and a.is_deleted = 0";
        return FilterHelper.newMultiFilter(
                new SubQueryFilter(new Filter(ID, Comparator.IN, Arrays.asList(1)), subQuerySql)
                        .or(executeOrApproveOverdueFilter));
    }

    /**
     * 高级查询条件 - 任务中心 / 待办中心 - 故障管理任务 - 产品经营团队
     */
    public static IFilter faultAssignmentProductManageTeamFilter(IDataModel dataModel, String componentCid) {
        // 1.获取 产品经营团队 组件填写数据
        List<TextValuePair> productManageTeamInfo =
                ComponentUtils.getChooseComponentInfo(dataModel, componentCid);
        if (CollectionUtils.isEmpty(productManageTeamInfo)) {
            return null;
        }

        // 2.包装过滤条件
        List<String> productManageTeamPathList = TextValuePairHelper.getValueList(productManageTeamInfo);
        List<String> formatPathList = Lists.newArrayList();
        productManageTeamPathList.forEach(item ->
                formatPathList.add(item.replace(CommonConstants.FORWARD_SLASH, CommonConstants.EMPTY_STRING)));
        return new Filter(PRODUCT_MANAGEMENT_TEAM, Comparator.IN, formatPathList);
    }
}
