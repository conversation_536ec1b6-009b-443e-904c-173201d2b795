package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 技术管理子任务流程字段常量
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/12
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ManageSubTaskFlowFieldConsts {

    /**
     * 父任务id
     */
    public static final String APPROVER_PARENT_TASK_ID = "parent_task_id";

    /**
     * 子任务id
     */
    public static final String APPROVER_SUB_TASK_ID = "sub_task_id";

    /**
     * 状态
     */
    public static final String APPROVER_STATUS = "status";

    /**
     * 子任务名称
     */
    public static final String APPROVER_SUBTASK_NAME = "subtask_name";

    /**
     * 子任务流程表反馈类型
     */
    public static final String APPROVER_FEEDBACK_TYPE = "feedback_type";

    /**
     * 子任务流程表反馈描述
     */
    public static final String APPROVER_PROGRESS_DESCRIPTION = "progress_description";
}
