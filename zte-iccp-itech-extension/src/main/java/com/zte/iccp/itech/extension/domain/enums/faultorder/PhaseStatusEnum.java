package com.zte.iccp.itech.extension.domain.enums.faultorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 服务请求阶段状态枚举
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PhaseStatusEnum implements SingletonTextValuePairsProvider {
    /**
     * 建单中
     */
    ORDER_MAKING("1", "建单中", "Under Creation"),

    /**
     * 待响应
     */
    RESPONSE_WAITING("2", "待响应", "Waiting For Response"),

    /**
     * 处理中
     */
    PROCESSING("3", "处理中", "Working In Progress"),

    /**
     * 关闭审核中
     */
    REVIEWING("4", "关闭审核中", "Closure Validating"),

    /**
     * 已关闭
     */
    CLOSED("5", "已关闭", "Closed"),

    /**
     * 受理中
     */
    UNDER_CONSIDERATION("6", "受理中", "Being Accepted");

    /** 编码 */
    private final String value;

    /** 中文描述 */
    private final String zhCn;

    /** 英文描述 */
    private final String enUs;

    /**
     * 根据枚举编码获取对应枚举
     */
    public static PhaseStatusEnum fromValue(String value) {
        for (PhaseStatusEnum phaseStatusEnum : PhaseStatusEnum.values()) {
            if (phaseStatusEnum.getValue().equals(value)) {
                return phaseStatusEnum;
            }
        }

        return null;
    }

    public static List<TextValuePair> getPropValue(String value) {
        PhaseStatusEnum phaseStatus = fromValue(value);
        return Objects.isNull(phaseStatus) ? Lists.newArrayList() : phaseStatus.getPropValue();
    }
}
