package com.zte.iccp.itech.extension.plugin.operation.plan;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.paas.lcap.common.api.dto.PopUpSizeDTO;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import com.zte.paas.lcap.ddm.domain.enums.OpenTypeEnum;
import com.zte.paas.lcap.platform.constant.SchemaConstant;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.ROW;
import static com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum.NEW;

/**
 * 操作计划单 - 打开添加操作对象弹框插件
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/7/25
 */
public class PlanCommonPageJumpPlugin extends BaseOperationPlugin {

    /** 操作计划单 - 操作对象 - 添加操作对象操作key */
    private static final String ADD_OPERATION_OBJECT = "add_operation_object";

    /** 添加操作对象页面 - 添加客户网络名称操作key */
    private static final String ADD_CUSTOMER_NETWORK_NAME = "add_customer_network_name";

    @Override
    public void afterExecute(ExecuteEvent executeEvent) {
        String operationKey = executeEvent.getOperationKey();
        IDataModel dataModel = getModel();
        if (ADD_OPERATION_OBJECT.equals(operationKey)) {
            FormShowParameter formShowParameter = getView().getFormShowParameter().createSubFormShowParameter();
            formShowParameter.setPageId(PageConstants.OPERATION_OBJ_SELECT_PRODUCT_MODEL_CID);
            formShowParameter.setPageStatus(NEW);
            formShowParameter.setBizObjectCode(PageConstants.OPERATION_OBJ_SELECT_PRODUCT_MODEL_CID);

            Map<String, Object> customParameters = MapUtils.newHashMap();
            customParameters.put(HIDDEN_OPERATION, true);
            customParameters.put(FULL_SCREEN, false);

            //将子表单的行末总量带到后面
            int entrySize = getModel().getEntrySize(CidConstants.OPERATION_OBJECT_TABLE_PROPERTY_KEY);
            customParameters.put(ROW, entrySize);
            //批次概要数据
            IDataEntityCollection batchSummaryEntryRowEntities = dataModel.getEntryRowEntities(CidConstants.BATCH_SUMMARY_TABLE_PROPERTY_KEY);
            List<BatchSummary> batchSummaryList = new ArrayList<>(batchSummaryEntryRowEntities.size());
            for (int i = 0; i < batchSummaryEntryRowEntities.size(); i++) {
                BatchSummary batchSummary = new BatchSummary();
                DynamicDataEntity batchSummaryEntry = (DynamicDataEntity) batchSummaryEntryRowEntities.get(i);
                batchSummary.setBatchNo(JsonUtils.parseArray(
                        batchSummaryEntry.get(CidConstants.BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY), TextValuePair.class));
                batchSummary.setPlanOperationStartTime(batchSummaryEntry.getDate(CidConstants.FIELD_PLAN_OPERATION_START_TIME_CID));
                batchSummary.setPlanOperationEndTime(batchSummaryEntry.getDate(CidConstants.FIELD_PLAN_OPERATION_END_TIME_CID));
                batchSummaryList.add(batchSummary);
            }
            customParameters.put(CidConstants.COMPONENT_OPERATION_OBJECT_BATCH_SUMMARY_CID, batchSummaryList);
            formShowParameter.setCustomParameters(customParameters);

            getView().showForm(formShowParameter);

            // 添加操作对象 - 打开客户网络名称弹框
        } else if (ADD_CUSTOMER_NETWORK_NAME.equals(operationKey)) {
            FormShowParameter formShowParameter = getView().getFormShowParameter().createSubFormShowParameter();
            formShowParameter.setPageId(PageConstants.PAGE_WEB_OPERAND_QUERY_NETWORK);
            formShowParameter.setPageStatus(NEW);
            formShowParameter.setBizObjectCode(PageConstants.PAGE_WEB_OPERAND_QUERY_NETWORK);

            Map<String, Object> customParameters = MapUtils.newHashMap();
            customParameters.put(HIDDEN_OPERATION, true);
            customParameters.put(FULL_SCREEN, false);
            customParameters.put(OPEN_TYPE, OpenTypeEnum.POPUP.getValue());

            PopUpSizeDTO.PopUpSizeDTOBuilder popupSize= PopUpSizeDTO.builder();
            popupSize.width(70);
            popupSize.unit(CommonConstants.PERCENT);
            customParameters.put(SchemaConstant.POPUP_SIZE, popupSize.build());
            customParameters.put(PARENT_PAGE, PageConstants.OPERATION_OBJ_SELECT_PRODUCT_MODEL_CID);

            formShowParameter.setCustomParameters(customParameters);

            getView().showForm(formShowParameter);
        }

    }


    @Override
    public boolean beforeExecuteValidate(ExecuteEvent executeEvent) {
        String operationKey = executeEvent.getOperationKey();
        // 校验批次概要操作批次、计划操作开始时间、计划操作结束时间不能为空
        if (ADD_OPERATION_OBJECT.equals(operationKey)) {
            IDataEntityCollection dataEntityCollection = getModel().getEntryRowEntities(CidConstants.FIELD_BATCH_SUMMARY_CID);
            if (dataEntityCollection.isEmpty()) {
                getView().showMessage(MessageConsts.CHECK_BATCH_SUMMARY_EMPTY_ERROR, MsgType.ERROR);
                return false;
            }

            for (int i = 0; i < dataEntityCollection.size(); i++) {
                DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
                String batchNo = TextValuePairHelper.getValue(dynamicDataEntity.get(CidConstants.BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY));
                Object planOperationStartTime = dynamicDataEntity.get(CidConstants.FIELD_PLAN_OPERATION_START_TIME_CID);
                Object planOperationEndTime = dynamicDataEntity.get(CidConstants.FIELD_PLAN_OPERATION_END_TIME_CID);
                if (StringUtils.isEmpty(batchNo)
                        || ObjectUtils.isEmpty(planOperationStartTime)
                        || ObjectUtils.isEmpty(planOperationEndTime)) {
                    getView().showMessage(MsgUtils.getMessage(MessageConsts.BATCH_SUMMARY_TIMING_FIELDS_MISSING, i + 1), MsgType.ERROR);
                    return false;
                }
            }

        }

        return true;
    }
}
