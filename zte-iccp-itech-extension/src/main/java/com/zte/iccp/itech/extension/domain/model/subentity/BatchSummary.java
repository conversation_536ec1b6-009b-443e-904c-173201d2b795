package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchSummary;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.*;

@ApiModel("批次概要信息")
@Setter
@Getter
@BaseSubEntity.Info(value = "batch_summary", parent = ChangeOrder.class)
public class BatchSummary extends BaseSubEntity implements IBatchSummary {

    @JsonProperty(value = BATCH_NO)
    @ApiModelProperty("批次号")
    private List<TextValuePair> batchNo;

    @JsonProperty(value = NETWORK_ELEMENT_COUNT)
    @ApiModelProperty("网元数量")
    private Integer networkElementCount;

    @JsonProperty(value = PLAN_OPERATION_START_TIME)
    @ApiModelProperty("计划操作开始时间")
    private Date planOperationStartTime;

    @JsonProperty(value = PLAN_OPERATION_END_TIME)
    @ApiModelProperty("计划操作结束时间")
    private Date planOperationEndTime;

    @JsonProperty(value = OPERATION_ACCOUNT)
    @ApiModelProperty("操作账号")
    private String operationAccount;

    @JsonProperty(value = OPERATION_DESCRIPTION)
    @ApiModelProperty("操作描述")
    private String operationDescription;

    @JsonProperty(value = TIME_CONFLICT)
    @ApiModelProperty("时间冲突")
    private String timeConflict;

    @JsonIgnore
    public String getPrimaryKey() {
        return String.join(CommonConstants.UNDER_SCORE, getPid(), TextValuePairHelper.getValue(getBatchNo()));
    }
}
