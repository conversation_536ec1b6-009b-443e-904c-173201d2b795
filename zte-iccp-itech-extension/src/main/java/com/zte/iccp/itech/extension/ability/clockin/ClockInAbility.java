package com.zte.iccp.itech.extension.ability.clockin;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.ability.clockin.callplan.ClockInCallPlanAbolishAbility;
import com.zte.iccp.itech.extension.ability.clockin.callplan.ClockInCallPlanCreateAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.OperationCache;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInOptionVO;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.openapi.model.clockin.ClockInDTO;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility.getKeyTask;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.ClockIn.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInRecordFieldConsts.CLOCK_IN_OPTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInRecordFieldConsts.CLOCK_IN_TIME;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum.OPERATING_SUPERVISOR;
import static com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum.WATCHMAN;

/**
 * 打卡
 *
 * <AUTHOR>
 * @since 2024/09/11
 */
public class ClockInAbility {

    private final ClockInDTO clockInDTO;

    private final ClockInTask clockInTask;

    private final boolean isOperationOwner;

    private final boolean isOnDutyOwner;

    private final OperationCache operationCache = new OperationCache();

    private final ClockInCallPlanCreateAbility clockInCallPlanCreateAbility
            = new ClockInCallPlanCreateAbility(operationCache);

    private final ClockInCallPlanAbolishAbility clockInCallPlanAbolishAbility
            = new ClockInCallPlanAbolishAbility(operationCache);

    private final ClockInRecord clockInRecord = new ClockInRecord();

    private final RelatedTasksProvider relatedTasksProvider = new RelatedTasksProvider();

    // 测试阶段终止卡
    private static final Set<ClockInOptionEnum> TEST_END_OPTIONS = Collections.unmodifiableSet(Sets.newHashSet(
            ClockInOptionEnum.TEST_SUCCESS_OPERATION_END,
            ClockInOptionEnum.TEST_FAILED_ROLLBACK_END,
            ClockInOptionEnum.TEST_FAILED_NO_ROLLBACK));

    private IBatchTask batchTask;

    public ClockInAbility(String clockInTaskId, ClockInDTO clockInDTO) {
        this.clockInDTO = clockInDTO;

        clockInTask = QueryDataHelper.get(
                ClockInTask.class,
                Lists.newArrayList(
                        ENTITY_TYPE,
                        CHANGE_ORDER_ID,
                        BATCH_TASK_ID,
                        TASK_TYPE,
                        OPERATOR,
                        OPERATOR_ROLES,
                        RESPONSIBLE_DEPT,
                        TIME_ZONE,
                        ABOLISHED,
                        TOTAL_ON_DUTY_DURATION,
                        ON_DUTY_FREQUENCY,
                        STAGE_ON_DUTY_END_TIME,
                        ALREADY_ON_DUTY_FREQUENCY,
                        TASK_STATUS),
                clockInTaskId);
        if (clockInTask == null) {
            throw new LcapBusiException(TASK_NOT_EXISTS);
        }

        if (BoolEnum.Y == clockInTask.getAbolished()) {
            throw new LcapBusiException(TASK_IS_ABOLISH);
        }

        if (!StringUtils.equals(
                ContextHelper.getEmpNo(), clockInTask.getOperator().getId())) {
            throw new LcapBusiException(TASK_OPERATOR_NOT_MATCH);
        }

        isOperationOwner = clockInTask.getTaskType() == ClockInTaskTypeEnum.OPERATION
                && clockInTask.getOperatorRoles().contains(OPERATING_SUPERVISOR);
        isOnDutyOwner = clockInTask.getTaskType() == ClockInTaskTypeEnum.ON_DUTY
                && clockInTask.getOperatorRoles().contains(WATCHMAN);
    }

    public boolean clockIn() {
        checkClockInOption();
        updateLastCheckInUtcTime();
        updateClockInState();
        updateClockInTaskStatus();
        updateOnDutyClockInTask();
        createClockInRecord();
        writeBackOperationResult();

        writeCallPlans();

        operationCache.transFlush();
        return true;
    }

    /**
     * 校验打卡选项是否可用
     */
    private void checkClockInOption() {
        List<ClockInOptionVO> clockInOptions = new ClockInOptionAbility(clockInTask.getId()).getClockInOptions();
        if (clockInOptions.stream().noneMatch(o ->
                o.isEnabledNow() && o.getOption() == clockInDTO.getOption())) {
            throw new LcapBusiException(OPTION_DISABLED);
        }
    }

    /**
     * 打任意卡后，更新批次任务的最新打卡时间
     */
    private void updateLastCheckInUtcTime() {
        batchTask = (IBatchTask) QueryDataHelper.get(
                clockInTask.getEntityType().getEntityClass(),
                Lists.newArrayList(
                        BatchTaskFieldConsts.CURRENT_STATUS,
                        BatchTaskFieldConsts.CLOCK_IN_STATE),
                clockInTask.getBatchTaskId());

        // 根据批次任务id查询对应的打卡任务列表
        List<IFilter> filters = Lists.newArrayList(new Filter(BATCH_TASK_ID, Comparator.EQ, batchTask.getId()));
        List<ClockInTask> updateTasks = QueryDataHelper.query(ClockInTask.class, Lists.newArrayList(ID), filters);
        // 设置last_check_in_utc_time_batch_task字段
        Long checkInTime = System.currentTimeMillis();
        for (ClockInTask updateTask : updateTasks) {
            updateTask.setLastCheckInUtcTimeBatchTask(checkInTime);
        }
        // 更新数据
        operationCache.update(updateTasks);
    }

    /**
     * 操作负责人或值守人打非过程卡，则更新打卡阶段
     */
    private void updateClockInState() {
        if (clockInDTO.getOption().getOptionType() == ClockInOptionTypeEnum.PROCESSING
                || (!isOperationOwner && !isOnDutyOwner)) {
            return;
        }

        if (clockInDTO.getOption().getOptionType() == ClockInOptionTypeEnum.STATE_END) {
            batchTask.setClockInState(batchTask.getClockInState() == null
                    ? ClockInStateEnum.PREPARING
                    : batchTask.getClockInState().next());
        } else if (clockInDTO.getOption().getOptionType() == ClockInOptionTypeEnum.MANUAL_CANCEL) {
            batchTask.setClockInState(ClockInStateEnum.ABOLISHED);
        }

        // 走到值守阶段，没有值守任务，则跳到已关闭
        if (batchTask.getClockInState() == ClockInStateEnum.ON_DUTY_GOING) {
            int onDutyTaskCount = QueryDataHelper.queryCount(ClockInTask.class, Lists.newArrayList(
                    new Filter(BATCH_TASK_ID, Comparator.EQ, clockInTask.getBatchTaskId()),
                    new Filter(TASK_TYPE, Comparator.EQ, Lists.newArrayList(ClockInTaskTypeEnum.ON_DUTY))));
            if (onDutyTaskCount == 0) {
                batchTask.setClockInState(ClockInStateEnum.CLOSED);
            }
        }

        operationCache.update((BaseEntity) batchTask);
    }

    /**
     * 更新任务状态；
     * 操作负责人打取消卡，则取消所有相关任务；
     */
    private void updateClockInTaskStatus() {
        if (clockInTask.getTaskStatus() != clockInDTO.getOption()) {
            clockInTask.setTaskStatus(clockInDTO.getOption());
            operationCache.update(clockInTask);
        }

        // 操作负责人打 操作取消卡，置所有相关任务为取消状态
        if (isOperationOwner && clockInDTO.getOption() == ClockInOptionEnum.CANCEL) {
            List<ClockInTask> relatedTasks = relatedTasksProvider.get();
            relatedTasks.forEach(t -> t.setTaskStatus(ClockInOptionEnum.CANCEL));
            operationCache.update(relatedTasks);
        }
    }

    /**
     * 更新值守任务已值守次数和值守阶段结束时间
     */
    private void updateOnDutyClockInTask() {
        // 除非是操作负责人打测试结束卡，其他操作阶段打卡不用处理值守这两个字段
        if (clockInTask.getTaskType() == ClockInTaskTypeEnum.OPERATION && !(isOperationOwner && TEST_END_OPTIONS.contains(clockInDTO.getOption()))) {
            return;
        }
        ClockInTask onDutyTask = getKeyTask(
                clockInTask.getBatchTaskId(), ClockInTaskTypeEnum.ON_DUTY, ENTITY_TYPE,
                ID,
                TOTAL_ON_DUTY_DURATION,
                ON_DUTY_FREQUENCY,
                TIME_ZONE,
                STAGE_ON_DUTY_END_TIME,
                ALREADY_ON_DUTY_FREQUENCY);
        if (onDutyTask == null) {
            // 没有值守任务
            return;
        }
        double totalDurationSecond = onDutyTask.getTotalOnDutyDuration() * 60 * 60;
        double intervalSecond = totalDurationSecond / onDutyTask.getOnDutyFrequency();
        Calendar stageOnDutyEndTime = Calendar.getInstance();
        Date zonedNow = clockInTask.getTimeZone().pollute(new Date());
        // 操作负责人打 测试阶段结束卡，初始化已值守次数和值守阶段结束时间
        if (isOperationOwner && TEST_END_OPTIONS.contains(clockInDTO.getOption())) {
            //【阶段值守结束时间】 = 当前时间 + 值守时长/值守频次
            stageOnDutyEndTime.setTime(zonedNow);
            stageOnDutyEndTime.add(Calendar.SECOND, (int) intervalSecond);
            onDutyTask.setStageOnDutyEndTime(stageOnDutyEndTime.getTime());
            // 已值守次数为 0
            onDutyTask.setAlreadyOnDutyFrequency(0);
            operationCache.update(onDutyTask);
            return;
        }

        // 打恢复卡时
        List<ClockInRecord> lastRecord = ClockInQueryAbility.getLastClockInRecords(
                onDutyTask.getId(), 1, BoolEnum.N, CLOCK_IN_OPTION);
        if (CollectionUtils.isNotEmpty(lastRecord)
                && lastRecord.get(0).getClockInOption() == ClockInOptionEnum.INDICATOR_ERROR_HANDLING) {
            //【阶段值守结束时间】=  当前时间 + 值守时长/值守频次
            stageOnDutyEndTime.setTime(zonedNow);
            stageOnDutyEndTime.add(Calendar.SECOND, (int) intervalSecond);
            onDutyTask.setStageOnDutyEndTime(stageOnDutyEndTime.getTime());
        } else if (clockInDTO.getOption() == ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE
                && zonedNow.after(clockInTask.getStageOnDutyEndTime())) {
            // 打值守正常卡时，如果当前时间 >= 【阶段值守结束时间】
            //【阶段值守结束时间】 = 【阶段值守结束时间】 + 值守时长/值守频次
            stageOnDutyEndTime.setTime(onDutyTask.getStageOnDutyEndTime());
            stageOnDutyEndTime.add(Calendar.SECOND, (int) intervalSecond);
            onDutyTask.setStageOnDutyEndTime(stageOnDutyEndTime.getTime());
            // 已值守次数+1
            onDutyTask.setAlreadyOnDutyFrequency(onDutyTask.getAlreadyOnDutyFrequency() + 1);
        } else {
            return;
        }
        operationCache.update(onDutyTask);
    }

    /**
     * 保存打卡记录
     */
    private void createClockInRecord() {
        clockInRecord.setPid(clockInTask.getId());
        clockInRecord.setClockInOption(clockInDTO.getOption());
        clockInRecord.setClockInTime(clockInTask.getTimeZone().pollute(new Date()));
        clockInRecord.setDescription(clockInDTO.getDescription());
        clockInRecord.setPhotos(clockInDTO.getPhotos());
        clockInRecord.setRevoked(BoolEnum.N);
        clockInRecord.setLongitude(clockInDTO.getLongitude());
        clockInRecord.setLatitude(clockInDTO.getLatitude());
        operationCache.create(clockInRecord);
    }

    /**
     * 判断是否正常结束，回写数据到“结果反馈”
     */
    private void writeBackOperationResult() {
        if (batchTask == null
                || (batchTask.getClockInState() != ClockInStateEnum.CLOSED
                        && batchTask.getClockInState() != ClockInStateEnum.ABOLISHED)
                || AssignmentStatusEnum.RESULT_UNDER_REVIEW.getValue()
                        .equals(batchTask.getCurrentStatus())) {
            return;
        }

        List<ClockInRecord> clockInRecords = ClockInQueryAbility
                .getClockInRecords(clockInTask.getId(), CLOCK_IN_OPTION, CLOCK_IN_TIME);
        // 由于打卡记录还在操作缓存里面没有提交到数据库，所以要手动添加到记录列表
        clockInRecords.add(clockInRecord);

        ClockInTask othKeyTask = getOthKeyTask();
        if (othKeyTask != null) {
            List<ClockInRecord> othRecords = ClockInQueryAbility
                    .getClockInRecords(othKeyTask.getId(), CLOCK_IN_OPTION, CLOCK_IN_TIME);
            clockInRecords.addAll(othRecords);
        }

        batchTask.setIsCanceled(BoolEnum.N);
        batchTask.setIsRollbackDone(BoolEnum.N);
        batchTask.setIsOneTimeSucceed(BoolEnum.Y);

        for (ClockInRecord rec : clockInRecords) {
            switch (rec.getClockInOption()) {
                case CANCEL:
                    batchTask.setIsCanceled(BoolEnum.Y);
                    break;
                case PREPARE_END_OPERATION_START:
                    batchTask.setActualOperationStartTime(rec.getClockInTime());
                    break;
                case OPERATION_COMPLETED_TEST_START:
                case OPERATION_PARTIALLY_TEST_START:
                case OPERATION_FAILED_ROLLBACK_END:
                case OPERATION_FAILED_NO_ROLLBACK:
                    batchTask.setActualOperationEndTime(rec.getClockInTime());
                    break;
                case TEST_SUCCESS_OPERATION_END:
                case TEST_FAILED_ROLLBACK_END:
                case TEST_FAILED_NO_ROLLBACK:
                    batchTask.setTestFinishTime(rec.getClockInTime());
                    break;
                default:
                    break;
            }

            if (rec.getClockInOption().isRollback()) {
                batchTask.setIsRollbackDone(BoolEnum.Y);
            }
            if (rec.getClockInOption().isException()) {
                batchTask.setIsOneTimeSucceed(BoolEnum.N);
            }
        }

        operationCache.update((BaseEntity) batchTask);
    }

    private ClockInTask getOthKeyTask() {
        if (isOperationOwner) {
            return ClockInQueryAbility.getKeyTask(
                    batchTask.getId(), ClockInTaskTypeEnum.ON_DUTY, ID);
        } else if (isOnDutyOwner) {
            return ClockInQueryAbility.getKeyTask(
                    batchTask.getId(), ClockInTaskTypeEnum.OPERATION, ID);
        }
        return null;
    }

    private void handleCancelCallPlan() {
        List<String> taskIds = Lists.newArrayList(clockInTask.getId());

        if (isOperationOwner) {
            taskIds.addAll(relatedTasksProvider.getIds());
        }

        clockInCallPlanAbolishAbility.abolish(taskIds);
    }

    private void writeCallPlans() {
        // 仅国内工服处创建ai呼叫任务
        if (DeptTypeEnum.INNER != ResponsibleUtils.getDeptType(clockInTask.getResponsibleDept())) {
            return;
        }
        if (clockInDTO.getOption() == ClockInOptionEnum.CANCEL) {
            handleCancelCallPlan();
            return;
        }
        // 如果是无效的继续值守卡，只废止转交呼叫，不影响其他呼叫
        if (isUnEffectiveOnDutyContinue()) {
            clockInCallPlanAbolishAbility.abolish(clockInTask.getId(), CallReasonEnum.TRANSFER);
            return;
        }

        CallReasonEnum clockedState = CallReasonEnum.fromOption(clockInDTO.getOption());
        if (clockedState != null) {
            if (clockedState == CallReasonEnum.EXCEPTION) {
                clockInCallPlanCreateAbility.create4ExceptionClock(clockInDTO.getOption(), clockInTask.getId());
                return;
            }
            if (clockInDTO.getOption() == ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE
                    || clockInDTO.getOption() == ClockInOptionEnum.INDICATOR_ERROR_ROLLBACK_END
                    || clockInDTO.getOption() == ClockInOptionEnum.INDICATOR_ERROR_NO_ROLLBACK) {
                List<ClockInRecord> lastRecord = ClockInQueryAbility.getLastClockInRecords(
                        clockInTask.getId(), 1, BoolEnum.N, CLOCK_IN_OPTION);
                // 如果是恢复卡
                if (CollectionUtils.isNotEmpty(lastRecord)
                        && lastRecord.get(0).getClockInOption() == ClockInOptionEnum.INDICATOR_ERROR_HANDLING) {
                    // 值守清零，重新创建呼叫计划
                    clockInCallPlanAbolishAbility.abolish(clockInTask.getId(), CallReasonEnum.ON_DUTY_CONTINUE);
                    clockInCallPlanAbolishAbility.abolish(clockInTask.getId(), CallReasonEnum.ON_DUTY_END);
                    // 后面会根据时间来判断下一个呼叫是值守继续还是值守结束
                    clockInCallPlanCreateAbility.create4NextState(clockInTask.getId(), CallReasonEnum.ON_DUTY_CONTINUE);
                    return;
                }
            }

            // 阶段打卡呼叫计划
            clockInCallPlanAbolishAbility.abolish(clockInTask.getId(), clockedState);
            clockInCallPlanCreateAbility.create4NextState(clockInTask.getId(), clockedState);
        }

        if (clockInTask.getTaskType() == ClockInTaskTypeEnum.ON_DUTY) {
            clockInCallPlanAbolishAbility.abolish(clockInTask.getId(), CallReasonEnum.TRANSFER);
        }
    }

    private boolean isUnEffectiveOnDutyContinue() {
        Date now = new Date();
        List<ClockInRecord> lastRecord = ClockInQueryAbility.getLastClockInRecords(
                clockInTask.getId(), 1, BoolEnum.N, CLOCK_IN_OPTION);
        // 恢复卡
        if (CollectionUtils.isNotEmpty(lastRecord)
                && lastRecord.get(0).getClockInOption() == ClockInOptionEnum.INDICATOR_ERROR_HANDLING) {
            return false;
        }
        // 无效的值守继续卡
        return clockInDTO.getOption() == ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE
                && now.before(clockInTask.getStageOnDutyEndTime());
    }

    private class RelatedTasksProvider {
        private List<ClockInTask> value;

        public List<ClockInTask> get() {
            if (value != null) {
                return value;
            }

            return value = QueryDataHelper.query(
                    ClockInTask.class,
                    Lists.newArrayList(ID),
                    Lists.newArrayList(
                            new Filter(BATCH_TASK_ID, Comparator.EQ, clockInTask.getBatchTaskId()),
                            new Filter(ID, Comparator.NE, clockInTask.getId())));
        }

        public List<String> getIds() {
            return get().stream()
                    .map(ClockInTask::getId)
                    .collect(Collectors.toList());
        }
    }
}
