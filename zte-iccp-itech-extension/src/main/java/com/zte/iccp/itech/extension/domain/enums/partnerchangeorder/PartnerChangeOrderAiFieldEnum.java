package com.zte.iccp.itech.extension.domain.enums.partnerchangeorder;

import com.zte.iccp.itech.extension.common.utils.AiParamUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.spi.model.crm.SvcCustomerInfo;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.function.Function;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORM;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.FAST_CODE_MEANING;

/**
 * <AUTHOR>
 * @date 2024/6/13 下午8:10
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PartnerChangeOrderAiFieldEnum {

    /**
     * 单据编号
     */
    HZS_NO("hzs_no", "hzs_no", Object::toString),
    /**
     * 操作主题
     */
    OPERATION_SUBJECT("operation_subject", "operation_subject", Object::toString),
    /**
     * 产品分类
     */
    PRODUCT_ID("product_id", "iTechCloudCustomTreeSelect_usitd200", AiParamUtils::getAllTextNameString),
    /**
     * 代表处
     */
    ORGANIZATION_ID("organization_id", "iTechCloudCustomTreeSelect_4ltniree", AiParamUtils::getAllTextNameString),
    /**
     * 是否政企
     */
    IS_GOVERNMENT_ENTERPRISE("is_government_enterprise", "is_government_enterprise", AiParamUtils::getAllTextNameString),
    /**
     * 操作类型
     */
    OPERATION_TYPE("operation_type", "operation_type", AiParamUtils::getAllTextNameString),
    /**
     * 操作原因
     */
    OPERATION_REASON("operation_reason", "operation_reason", AiParamUtils::getAllTextNameString),
    /**
     *触发类型
     */
    TRIGGER_TYPE("trigger_type", "trigger_type", AiParamUtils::getAllTextNameString),
    /**
     *重要程度
     */
    IMPORTANCE("importance", "importance", AiParamUtils::getAllTextNameString),
    /**
     *风险评估
     */
    RISK_EVALUATION("risk_evaluation", "risk_evaluation", AiParamUtils::getAllTextNameString),
    /**
     *操作等级
     */
    OPERATION_LEVEL("operation_level", "operation_level", AiParamUtils::getAllTextNameString),
    /**
     * 客户名称
     */
    CUSTOMER_ID("customer_id", "customer_id", object -> {
        SvcCustomerInfo svcCustomerInfo = JsonUtils.parseObject(object, SvcCustomerInfo.class);
        return svcCustomerInfo.getCustomerName();
    }),
    /**
     * 客户标识
     */
    CUSTOMER_IDENTIFICATION("accn_type", "accn_type", Object::toString),
    /**
     *客户联系人
     */
    CUSTOMER_CONTACT_PERSON("customer_contact_person", "customer_contact_person", Object::toString),
    /**
     *操作说明
     */
    OPERATION_DESC("operation_desc", "operation_desc", Object::toString),
    /**
     * 国家/地区
     */
    COUNTRY("country", "country", AiParamUtils::getAllValueLangNameSring),
    /**
     * 省/州
     */
    PROVINCE("province", "province", AiParamUtils::getAllValueLangNameSring),
    /**
     * 地区
     */
    AREA("area", "area", AiParamUtils::getAllValueLangNameSring),
    /**
     * 是否紧急操作
     */
    IS_EMERGENCY_OPERATION("is_emergency_operation", "is_emergency_operation", AiParamUtils::getAllTextNameString),
    /**
     * 预计业务中断时长
     */
    SERVICE_DISCONNECT_DURATION("service_disconnect_duration", "service_disconnect_duration", Object::toString),
    /**
     * 紧急操作原因简述
     */
    EMERGENCY_OPERATION_REASON("emergency_operation_reason", "emergency_operation_reason", Object::toString),
    /**
     * 时区
     */
    TIME_ZONE("time_zone", "time_zone", r -> (String) PropertyValueConvertUtil.getMap(r).get(FAST_CODE_MEANING)),
    /**
     * 计划操作开始时间
     */
    OPERATION_START_TIME("operation_start_time", "operation_start_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
    /**
     * 计划操作结束时间
     */
    OPERATION_END_TIME("operation_end_time", "operation_end_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
    /**
     *是否合作方独立完成
     */
    IS_PARTNER_INDEPENDENTLY("is_partner_independently", "is_partner_independently", AiParamUtils::getAllTextNameString),
    /**
     *网络名称
     */
    NETWORK_NAME("network_name", "network_name", object -> ((DynamicDataEntity) object).getString("NetworkNameZh")),
    /**
     *网络负责人
     */
    NETWORK_RESPONSIBLE_PERSON("network_responsible_person", "network_responsible_person", AiParamUtils::getEmployeeNameString),
    /**
     *网络负责组
     */
    NETWORK_RESPONSIBLE_TEAM("network_responsible_team", "network_responsible_team", AiParamUtils::getEmployeeNameString),
    /**
     * 是否需提供详细保障方案
     */
    IS_GUARANTEE_SOLUTION("is_guarantee_solution", "is_guarantee_solution", AiParamUtils::getAllTextNameString),
    /**
     * 保障方式
     */
    GUARANTEE_MODE("guarantee_mode", "guarantee_mode", AiParamUtils::getAllTextNameString),
    /**
     * 是否需要授权文件
     */
    IS_AUTHORIZATION_FILE("is_authorization_file", "is_authorization_file", AiParamUtils::getAllTextNameString),
    /**
     * 操作类型分组
     */
    OPERATION_TYPE_GROUP("operation_type_group", "operation_type_group", Object::toString),
    /**
     *操作方案描述
     */
    OPERATION_SOLUTION_DESC("operation_solution_desc", "operation_solution_desc", Object::toString),
    /**
     *附件描述
     */
    OTHER_ATTACHMENT_DESC("other_attachment_desc", "other_attachment_desc", Object::toString),
    /**
     * 是否涉及高危指令
     */
    IS_HIGH_RISK_INSTRUCTION("hzs_is_high_risk_instruction", "is_high_risk_instruction", AiParamUtils::getAllTextNameString),


    ;


    //实体唯一标识
    private final String propertyKey;

    //布局唯一标识
    private final String cid;

    //方法
    private final Function<Object, String> function;


}
