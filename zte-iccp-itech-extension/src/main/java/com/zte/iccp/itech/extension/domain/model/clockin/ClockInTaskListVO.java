package com.zte.iccp.itech.extension.domain.model.clockin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2024/09/05
 */
@Getter
@Setter
public class ClockInTaskListVO extends BaseEntity {

    /**
     * 主键ID
     */
    @JsonProperty(value = "pk_25049603")
    private String assignmentId;

    /**
     * 操作单号
     */
    @JsonProperty(value = "custom_rzfkmeji")
    private String orderNo;

    /**
     * 操作主题
     */
    @JsonProperty(value = "custom_8gy0e2s4")
    private String operationSubject;

    /**
     * 批次号
     */
    @JsonProperty(value = "custom_jo2aqax3")
    private String batchNo;

    /**
     * 客户网络名称
     */
    @JsonProperty(value = "custom_bugwsnx3")
    private String customerNetworkNameList;

    /**
     * 产品经营团队
     */
    @JsonProperty(value = "multiselectfield_64cx4qss")
    @JsonDeserialize(using = MultiTextValuePairsDeserializer.class)
    private List<TextValuePair> productTeam;

    /**
     * 产品线
     */
    @JsonProperty(value = "custom_suat42wj")
    private String productLine;

    /**
     * 产品大类
     */
    @JsonProperty(value = "custom_kbv27yxn")
    private String prodMainCategory;

    /**
     * 产品小类
     */
    @JsonProperty(value = "custom_pewt9x3m")
    private String prodSubCategory;

    /**
     * 风险等级
     */
    @JsonProperty(value = "custom_bo311n7o")
    private String riskEvaluation;

    /**
     * 操作等级
     */
    @JsonProperty(value = "custom_hubvfcps")
    private String operationLevel;

    /**
     * 营销
     */
    @JsonProperty(value = "multiselectfield_cj8fsbyb")
    @JsonDeserialize(using = MultiTextValuePairsDeserializer.class)
    private List<TextValuePair> marketing;

    /**
     * 代表处
     */
    @JsonProperty(value = "multiselectfield_iw640uo3")
    @JsonDeserialize(using = MultiTextValuePairsDeserializer.class)
    private List<TextValuePair> responsibleDept;

    /**
     * 操作类型
     */
    @JsonProperty(value = "selectfield_aabarr58")
    @JsonDeserialize(using = MultiTextValuePairsDeserializer.class)
    private List<TextValuePair> operationType;

    /**
     * 操作开始时间
     */
    @JsonProperty(value = "custom_v6ycsfco")
    private Date operationStartTime;

    /**
     * 操作结束时间
     */
    @JsonProperty(value = "custom_a16zn0b2")
    private Date operationEndTime;

    /**
     * 操作负责人
     */
    @JsonProperty(value = "custom_iarlz1cz")
    private String operatingSupervisor;

    /**
     * 打卡状态
     */
    @JsonProperty(value = "custom_r30nvyfo")
    private String clockInStatus;


    /**
     * 任务类型
     */
    @JsonProperty(value = "selectfield_iqoe8iy9")
    @JsonDeserialize(using = MultiTextValuePairsDeserializer.class)
    private List<TextValuePair> assignmentType;

    /**
     * 产品分类
     */
    private String productClassification;

//    下面导出独有字段

    /**
     * 转交处理人
     */
    private String taskReceiver;

    /**
     * 值守时长
     */
    private Integer onDutyDurationHours;

    /**
     * 是否需要复盘 BoolEnum
     */
    private String clockInReviewsFlag;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 打卡项
     */
    private Map<String, Date> clockInOptionMap;

//    multiselectfield_0xeob7ch
}
