package com.zte.iccp.itech.extension.domain.enums.clockin;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/11/11
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ClockInCustomerFlagEnum implements SingletonTextValuePairsProvider {
    /** 中国移动 */
    CHINA_MOBILE("1", "中国移动", "China Mobile"),

    /** 中国联通 */
    CHINA_UNICOM("2", "中国联通", "China Unicom"),

    /** 中国电信 */
    CHINA_TELECOM("3", "中国电信", "China Telecom"),

    /** 其他 */
    OTHER("4", "其他", " Other"),
    ;

    private final String value;

    private final String zhCn;

    private final String enUS;

    public static ClockInCustomerFlagEnum fromValue(String value) {
        return Arrays.stream(values())
                .filter(v -> v.value.equals(value))
                .findAny()
                .orElse(null);
    }

    public static String fromZhCn(String zhCn) {
        return Arrays.stream(values())
                .filter(v -> v.zhCn.equals(zhCn))
                .map(ClockInCustomerFlagEnum::getValue)
                .findFirst()
                .orElse(null);
    }
}
