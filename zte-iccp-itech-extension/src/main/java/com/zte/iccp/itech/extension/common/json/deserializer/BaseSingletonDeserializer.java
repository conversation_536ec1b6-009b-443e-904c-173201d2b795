package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.common.utils.ReflectUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.common.utils.CollectionUtilsEx.single;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/26
 */
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class BaseSingletonDeserializer<T> extends JsonDeserializer<T> {

    private final Class<T> clazz;

    @SuppressWarnings("unchecked")
    @SneakyThrows
    @Override
    public T deserialize(JsonParser p, DeserializationContext ctxt) {
        Object obj = p.getCodec().readValue(p, Object.class);
        if (obj == null) {
            return null;
        }

        if (obj instanceof Map) {
            //noinspection rawtypes
            return (T) ReflectUtils.map2Obj((Map) obj, clazz);
        }

        List<Map<String, Object>> rawObjs = (List<Map<String, Object>>) obj;
        return CollectionUtils.isEmpty(rawObjs)
                ? null : ReflectUtils.map2Obj(single(rawObjs), clazz);
    }
}
