package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.model.OperationTypeAttribute;
import com.zte.iccp.itech.extension.domain.model.ProductToOption;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.helper.FormModelProxyHelper.setFormModelProxyProps;
import static com.zte.iccp.itech.extension.common.utils.ConvertUtil.getListObjectValue;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFieldConsts.OPERATE_TYPE;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFieldConsts.PRODUCT_LINE;

/**
 * @author: 李江斌 10318434
 * @date: 2024/9/6
 */
public class ProductToOptionAbility {
    public static void setOperationType(IFormView formView, IDataModel dataModel, boolean isLoadData, ProductToOption productToOption) {
        // 获取界面产品线，来自自定义产品分类树第二层id
        Object productLineObject = dataModel.getValue(productToOption.getProductTypeCid());
        // 产品线为空时，直接返回
        if (ObjectUtils.isEmpty(productLineObject)) {
            // 操作类型下拉单选置空
            setFormModelProxyProps(productToOption.getOptionType(), formView, new ArrayList<>());
            // 清空操作类型已选值
            dataModel.setValue(productToOption.getOptionTypeCid(), new OptionsBuilder().build());
            return;
        }
        List<String> values = getListObjectValue(productLineObject);
        if (CollectionUtils.isEmpty(values)) {
            // 清空操作类型已选值
            dataModel.setValue(productToOption.getOptionTypeCid(), new OptionsBuilder().build());
            return;
        }
        String value = values.get(INTEGER_ZERO);
        String productLine = value.split(FORWARD_SLASH)[INTEGER_ZERO] + FORWARD_SLASH;
        if (value.split(FORWARD_SLASH).length > INTEGER_ONE) {
            productLine = productLine + value.split(FORWARD_SLASH)[INTEGER_ONE] + FORWARD_SLASH;
        }
        List<String> productLines = Lists.newArrayList(productLine);
        List<String> operationTypeLookupCode = getOperationType(productLines);
        // 加载页面的时候不需要清空数据
        if (!isLoadData) {
            // 清空已选操作类型的值
            dataModel.setValue(productToOption.getOptionTypeCid(), new OptionsBuilder().build());
        }
        // 从快码中查操作类型对应的值
        List<LookupValue> lookupValues = LookupValueHelper.getLookupValues(
                LookupValueConstant.OPERATE_TYPE_ENUM, operationTypeLookupCode);
        // 快码值回写下拉单选组件
        setFormModelProxyProps(productToOption.getOptionType(), formView, lookupValues);
    }

    // 基于产品线，从产品操作类型维护基础资料获取操作类型
    private static List<String> getOperationType(List<String> productLines) {
        // 基于产品线和操作类型，从产品操作类型维护基础资料先获取字段“是否定制操作原因”
        List<String> fieldList = Lists.newArrayList(OPERATE_TYPE);
        List<IFilter> conditionFilters = Lists.newArrayList();
        conditionFilters.add(new Filter(PRODUCT_LINE, Comparator.IN, productLines));
        // 基于产品线，从产品操作类型维护基础资料获取操作类型
        List<OperationTypeAttribute> operationTypeAttributes = OperationTypeAbility.query(fieldList, conditionFilters);
        // 从数据库中没有查到，直接返回空列表
        if (CollectionUtils.isEmpty(operationTypeAttributes)) {
            return Lists.newArrayList();
        }
        // 获取操作类型快码值
        return operationTypeAttributes.stream()
                .map(OperationTypeAttribute::getOperateType)
                .collect(Collectors.toList());
    }
}
