package com.zte.iccp.itech.extension.domain.enums;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.domain.model.base.LookupValueEnum;
import com.zte.iccp.itech.extension.domain.model.base.ZonedTime;
import com.zte.iccp.itech.extension.domain.model.base.tzdb.TzdbZoneRulesProvider;
import lombok.Getter;
import lombok.SneakyThrows;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.zone.ZoneRulesProvider;
import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORM;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/22
 */
@Getter
public enum TimeZoneEnum implements LookupValueEnum, IValueEnum<String> {

    /** (UTC+14:00)圣诞岛 */
    KIRTIMATI_ISLAND("Pacific/Kiritimati",
            "customize/Pacific/Kiritimati",
            Lists.newArrayList()),

    /** (UTC+13:00)协调世界时 +13 */
    UTC_PLUS_1300("Etc/GMT-13",
            "Etc/GMT-13",
            Lists.newArrayList()),

    /** (UTC+13:00)萨摩亚群岛 */
    SAMOA("Pacific/Apia",
            "customize/Pacific/Apia",
            Lists.newArrayList("new_time_zone_code_2")),

    /** (UTC+13:00)努库阿洛法 */
    NUKU_ALOFA("Pacific/Tongatapu",
            "customize/Pacific/Tongatapu",
            Lists.newArrayList("new_time_zone_code_75")),

    /** (UTC+12:45)查塔姆群岛 */
    CHATHAM_ISLAND("Pacific/Chatham",
            "customize/Pacific/Chatham",
            Lists.newArrayList()),

    /** (UTC+12:00)协调世界时 +12 */
    UTC_PLUS_1200("Etc/GMT-12",
            "Etc/GMT-12",
            Lists.newArrayList()),

    /** (UTC+12:00)斐济 */
    FIJI("Pacific/Fiji",
            "customize/Pacific/Fiji",
            Lists.newArrayList("new_time_zone_code_74")),

    /** (UTC+12:00)奥克兰,惠灵顿 */
    AUCKLAND("Pacific/Auckland",
            "customize/Pacific/Auckland",
            Lists.newArrayList("new_time_zone_code_73")),

    /** (UTC+12:00)阿纳德尔,堪察加彼得罗巴甫洛夫斯克 */
    ANADYR("Asia/Anadyr",
            "customize/Asia/Anadyr",
            Lists.newArrayList()),

    /** (UTC+11:00)所罗门群岛,新喀里多尼亚 */
    SOLOMON_ISLAND("Pacific/Guadalcanal",
            "customize/Pacific/Guadalcanal",
            Lists.newArrayList("new_time_zone_code_72")),

    /** (UTC+11:00)萨哈林 */
    SAKHALIN("Asia/Sakhalin",
            "customize/Asia/Sakhalin",
            Lists.newArrayList()),

    /** (UTC+11:00)乔库尔达赫 */
    CHOKURDAKH("Asia/Srednekolymsk",
            "customize/Asia/Srednekolymsk",
            Lists.newArrayList()),

    /** (UTC+11:00)诺福克岛 */
    NORFOLK_ISLAND("Pacific/Norfolk",
            "customize/Pacific/Norfolk",
            Lists.newArrayList()),

    /** (UTC+11:00)马加丹 */
    MAGADAN("Asia/Magadan",
            "customize/Asia/Magadan",
            Lists.newArrayList()),

    /** (UTC+11:00)布干维尔岛 */
    BOUGAINVILLE_ISLAND("Pacific/Bougainville",
            "customize/Pacific/Bougainville",
            Lists.newArrayList()),

    /** (UTC+10:30)豪勋爵岛*/
    LORD_HOWE_ISLAND("Australia/Lord_Howe",
            "customize/Australia/Lord_Howe",
            Lists.newArrayList()),

    /** (UTC+10:00)堪培拉,墨尔本,悉尼 */
    SYDNEY("Australia/Sydney",
            "customize/Australia/Sydney",
            Lists.newArrayList("new_time_zone_code_71")),

    /** (UTC+10:00)霍巴特 */
    HOBART("Australia/Hobart",
            "customize/Australia/Hobart",
            Lists.newArrayList("new_time_zone_code_70")),

    /** (UTC+10:00)关岛,莫尔兹比港 */
    GUAM("Pacific/Guam",
            "customize/Pacific/Guam",
            Lists.newArrayList("new_time_zone_code_69")),

    /** (UTC+10:00)符拉迪沃斯托克 */
    VLADIVOSTOK("Asia/Vladivostok",
            "customize/Asia/Vladivostok",
            Lists.newArrayList("new_time_zone_code_68")),

    /** (UTC+10:00)布里斯班 */
    BRISBANE("Australia/Brisbane",
            "customize/Australia/Brisbane",
            Lists.newArrayList("new_time_zone_code_67")),

    /** (UTC+09:30)达尔文 */
    DARWIN("Australia/Darwin",
            "customize/Australia/Darwin",
            Lists.newArrayList("new_time_zone_code_66")),

    /** (UTC+09:30)阿德莱德 */
    ADELAIDE("Australia/Adelaide",
            "customize/Australia/Adelaide",
            Lists.newArrayList("new_time_zone_code_65")),

    /** (UTC+09:00)雅库茨克 */
    YAKUTSK("Asia/Yakutsk",
            "customize/Asia/Yakutsk",
            Lists.newArrayList("new_time_zone_code_64")),

    /** (UTC+09:00)首尔 */
    SEOUL("Asia/Seoul",
            "customize/Asia/Seoul",
            Lists.newArrayList("new_time_zone_code_63")),

    /** (UTC+09:00)平壤 */
    PYONGYANG("Asia/Pyongyang",
            "customize/Asia/Pyongyang",
            Lists.newArrayList()),

    /** (UTC+09:00)大阪,札幌,东京 */
    TOKYO("Asia/Tokyo",
            "customize/Asia/Tokyo",
            Lists.newArrayList("new_time_zone_code_62")),

    /** (UTC+09:00)赤塔市*/
    CHITA("Asia/Chita",
            "customize/Asia/Chita",
            Lists.newArrayList()),

    /** (UTC+08:45)尤克拉 */
    EUCLA("Australia/Eucla",
            "customize/Australia/Eucla",
            Lists.newArrayList()),

    /** (UTC+08:00)伊尔库茨克 */
    IRKUTSK("Asia/Irkutsk",
            "customize/Asia/Irkutsk",
            Lists.newArrayList()),

    /** (UTC+08:00)乌兰巴托 */
    ULAANBAATAR("Asia/Ulaanbaatar",
            "customize/Asia/Ulaanbaatar",
            Lists.newArrayList("new_time_zone_code_61")),

    /** (UTC+08:00)珀斯 */
    PERTH("Australia/Perth",
            "customize/Australia/Perth",
            Lists.newArrayList("new_time_zone_code_59")),

    /** (UTC+08:00)吉隆坡,新加坡 */
    SINGAPORE("Asia/Singapore",
            "customize/Asia/Singapore",
            Lists.newArrayList("new_time_zone_code_58")),

    /** (UTC+08:00)台北 */
    TAIPEI("Asia/Taipei",
            "customize/Asia/Taipei",
            Lists.newArrayList("new_time_zone_code_60")),

    /** (UTC+08:00)北京,重庆,香港特别行政区,乌鲁木齐 */
    BEIJING("Asia/Shanghai",
            "customize/Asia/Shanghai",
            Lists.newArrayList("new_time_zone_code_27")),

    /** (UTC+07:00)科布多 */
    HOVD("Asia/Hovd",
            "customize/Asia/Hovd",
            Lists.newArrayList()),

    /** (UTC+07:00)托木斯克 */
    TOMSK("Asia/Tomsk",
            "customize/Asia/Tomsk",
            Lists.newArrayList()),

    /** (UTC+07:00)新西伯利亚 */
    NOVOSIBIRSK("Asia/Novosibirsk",
            "customize/Asia/Novosibirsk",
            Lists.newArrayList("new_time_zone_code_51")),

    /** (UTC+07:00)巴尔瑙尔,戈尔诺-阿尔泰斯克 */
    BARNAUL("Asia/Barnaul",
            "customize/Asia/Barnaul",
            Lists.newArrayList()),

    /** (UTC+07:00)克拉斯诺亚尔斯克 */
    KRASNOYARSK("Asia/Krasnoyarsk",
            "customize/Asia/Krasnoyarsk",
            Lists.newArrayList("new_time_zone_code_55")),

    /** (UTC+07:00)曼谷,河内,雅加达 */
    BANGKOK("Asia/Bangkok",
            "customize/Asia/Bangkok",
            Lists.newArrayList("new_time_zone_code_56")),

    /** (UTC+06:30)仰光 */
    YANGON("Asia/Rangoon",
            "customize/Asia/Rangoon",
            Lists.newArrayList("new_time_zone_code_54")),

    /** (UTC+06:00)鄂木斯克 */
    OMSK("Asia/Omsk",
            "customize/Asia/Omsk",
            Lists.newArrayList()),

    /** (UTC+06:00)达卡 */
    DHAKA("Asia/Dhaka",
            "customize/Asia/Dhaka",
            Lists.newArrayList()),

    /** (UTC+06:00)阿斯塔纳 */
    ASTANA("Asia/Almaty",
            "customize/Asia/Almaty",
            Lists.newArrayList("new_time_zone_code_52")),

    /** (UTC+05:45)加德满都 */
    KATHMANDU("Asia/Katmandu",
            "customize/Asia/Katmandu",
            Lists.newArrayList("new_time_zone_code_50")),

    /** (UTC+05:30)钦奈,加尔各答,孟买,新德里 */
    KOLKATA("Asia/Calcutta",
            "customize/Asia/Calcutta",
            Lists.newArrayList("new_time_zone_code_49")),

    /** (UTC+05:30)斯里加亚渥登普拉 */
    SRI_JAYAWARDENAPURA("Asia/Colombo",
            "customize/Asia/Colombo",
            Lists.newArrayList("new_time_zone_code_53")),

    /** (UTC+05:00)伊斯兰堡,卡拉奇 */
    KARACHI("Asia/Karachi",
            "customize/Asia/Karachi",
            Lists.newArrayList("new_time_zone_code_48")),

    /** (UTC+05:00)叶卡捷琳堡 */
    YEKATERINBURG("Asia/Yekaterinburg",
            "customize/Asia/Yekaterinburg",
            Lists.newArrayList("new_time_zone_code_47")),

    /** (UTC+05:00)克孜洛尔达 */
    QYZYLORDA("Asia/Qyzylorda",
            "customize/Asia/Qyzylorda",
            Lists.newArrayList()),

    /** (UTC+05:00)阿什哈巴德,塔什干 */
    TASHKENT("Asia/Tashkent",
            "customize/Asia/Tashkent",
            Lists.newArrayList()),

    /** (UTC+04:30)喀布尔 */
    KABUL("Asia/Kabul",
            "customize/Asia/Kabul",
            Lists.newArrayList("new_time_zone_code_46")),

    /** (UTC+04:00)伊热夫斯克,萨马拉 */
    SAMARA("Europe/Samara",
            "customize/Europe/Samara",
            Lists.newArrayList()),

    /** (UTC+04:00)萨拉托夫 */
    SARATOV("Europe/Saratov",
            "customize/Europe/Saratov",
            Lists.newArrayList()),

    /** (UTC+04:00)路易港 */
    PORT_LOUIS("Indian/Mauritius",
            "customize/Indian/Mauritius",
            Lists.newArrayList()),

    /** (UTC+04:00)第比利斯 */
    TBILISI(
            "Asia/Tbilisi",
            "customize/Asia/Tbilisi",
            Lists.newArrayList()),

    /** (UTC+04:00)阿布扎比,马斯喀特 */
    MUSCAT("Asia/Muscat",
            "customize/Asia/Muscat",
            Lists.newArrayList("new_time_zone_code_44")),

    /** (UTC+04:00)阿斯特拉罕,乌里扬诺夫斯克 */
    ULYANOVSK("Europe/Ulyanovsk",
            "customize/Europe/Ulyanovsk",
            Lists.newArrayList()),

    /** (UTC+04:00)巴库 */
    BAKU("Asia/Baku",
            "customize/Asia/Baku",
            Lists.newArrayList("new_time_zone_code_45")),

    /** (UTC+04:00)埃里温 */
    YEREVAN("Asia/Yerevan",
            "customize/Asia/Yerevan",
            Lists.newArrayList()),

    /** (UTC+03:30)德黑兰 */
    TEHRAN("Asia/Tehran",
            "customize/Asia/Tehran",
            Lists.newArrayList("new_time_zone_code_43")),

    /** (UTC+03:00)莫斯科,圣彼得堡 */
    MOSCOW("Europe/Moscow",
            "customize/Europe/Moscow",
            Lists.newArrayList("new_time_zone_code_41")),

    /** (UTC+03:00)伊斯坦布尔 */
    ISTANBUL("Asia/Istanbul",
            "customize/Asia/Istanbul",
            Lists.newArrayList()),

    /** (UTC+03:00)内罗毕 */
    NAIROBI("Africa/Nairobi",
            "customize/Africa/Nairobi",
            Lists.newArrayList("new_time_zone_code_42")),

    /** (UTC+03:00)明斯克 */
    MINSK("Europe/Minsk",
            "customize/Europe/Minsk",
            Lists.newArrayList()),

    /** (UTC+03:00)伏尔加格勒 */
    VOLGOGRAD("Europe/Volgograd",
            "customize/Europe/Volgograd",
            Lists.newArrayList()),

    /** (UTC+03:00)巴格达 */
    BAGHDAD("Asia/Baghdad",
            "customize/Asia/Baghdad",
            Lists.newArrayList("new_time_zone_code_39")),

    /** (UTC+03:00)科威特,利雅得 */
    RIYADH("Asia/Riyadh",
            "customize/Asia/Riyadh",
            Lists.newArrayList("new_time_zone_code_40")),

    /** (UTC+03:00)安曼 */
    AMMAN("Asia/Amman",
            "customize/Asia/Amman",
            Lists.newArrayList()),

    /** (UTC+02:00)耶路撒冷 */
    JERUSALEM("Asia/Jerusalem",
            "customize/Asia/Jerusalem",
            Lists.newArrayList("new_time_zone_code_38")),

    /** (UTC+02:00)大马士革 */
    DAMASCUS("Asia/Damascus",
            "customize/Asia/Damascus",
            Lists.newArrayList()),

    /** (UTC+02:00)朱巴 */
    JUBA("Africa/Juba",
            "customize/Africa/Juba",
            Lists.newArrayList()),

    /** (UTC+02:00)温得和克 */
    WINDHOEK("Africa/Windhoek",
            "customize/Africa/Windhoek",
            Lists.newArrayList()),

    /** (UTC+02:00)喀土穆 */
    KHARTOUM("Africa/Khartoum",
            "customize/Africa/Khartoum",
            Lists.newArrayList()),

    /** (UTC+02:00)雅典,布加勒斯特 */
    ATHENS("Europe/Athens",
            "customize/Europe/Athens",
            Lists.newArrayList("new_time_zone_code_33", "new_time_zone_code_37")),

    /** (UTC+02:00)开罗 */
    CAIRO("Africa/Cairo",
            "customize/Africa/Cairo",
            Lists.newArrayList("new_time_zone_code_36")),

    /** (UTC+02:00)加沙,希伯伦 */
    GAZA("Asia/Gaza",
            "customize/Asia/Gaza",
            Lists.newArrayList()),

    /** (UTC+02:00)加里宁格勒 */
    KALININGRAD("Europe/Kaliningrad",
            "customize/Europe/Kaliningrad",
            Lists.newArrayList()),

    /** (UTC+02:00)赫尔辛基,基辅,里加,索非亚,塔林,维尔纽斯 */
    HELSINKI("Europe/Helsinki",
            "customize/Europe/Helsinki",
            Lists.newArrayList("new_time_zone_code_35")),

    /** (UTC+02:00)哈拉雷,比勒陀利亚 */
    HARARE("Africa/Harare",
            "customize/Africa/Harare",
            Lists.newArrayList("new_time_zone_code_34")),

    /** (UTC+02:00)的黎波里 */
    TRIPOLI("Africa/Tripoli",
            "customize/Africa/Tripoli",
            Lists.newArrayList()),

    /** (UTC+02:00)贝鲁特 */
    BEIRUT("Asia/Beirut",
            "customize/Asia/Beirut",
            Lists.newArrayList()),

    /** (UTC+02:00)基希讷乌 */
    CHISINAU("Europe/Chisinau",
            "customize/Europe/Chisinau",
            Lists.newArrayList()),

    /** (UTC+01:00)萨拉热窝,斯科普里,华沙,萨格勒布 */
    SARAJEVO("Europe/Belgrade",
            "customize/Europe/Belgrade",
            Lists.newArrayList("new_time_zone_code_31")),

    /** (UTC+01:00)卡萨布兰卡 */
    CASABLANCA("Africa/Casablanca",
            "customize/Africa/Casablanca",
            Lists.newArrayList()),

    /** (UTC+01:00)中非西部 */
    WEST_CENTRAL_AFRICA("Africa/Luanda",
            "customize/Africa/Luanda",
            Lists.newArrayList("new_time_zone_code_32")),

    /** (UTC+01:00)阿姆斯特丹,柏林,伯尔尼,罗马,斯德哥尔摩,维也纳 */
    BERLIN("Europe/Berlin",
            "customize/Europe/Berlin",
            Lists.newArrayList("new_time_zone_code_28")),

    /** (UTC+01:00)贝尔格莱德,布拉迪斯拉发,布达佩斯,卢布尔雅那,布拉格 */
    PRAGUE("Europe/Prague",
            "customize/Europe/Prague",
            Lists.newArrayList("new_time_zone_code_29")),

    /** (UTC+01:00)布鲁塞尔,哥本哈根,马德里,巴黎 */
    PARIS("Europe/Paris",
            "customize/Europe/Paris",
            Lists.newArrayList("new_time_zone_code_30")),

    /** (UTC+00:00)都柏林,爱丁堡,里斯本,伦敦 */
    LONDON("Europe/London",
            "customize/Europe/London",
            Lists.newArrayList("new_time_zone_code_26")),

    /** (UTC+00:00)圣多美 */
    SAO_TOME("Africa/Sao_Tome",
            "customize/Africa/Sao_Tome",
            Lists.newArrayList()),

    /** (UTC+00:00)蒙罗维亚,雷克雅未克 */
    MONROVIA("Africa/Monrovia",
            "customize/Africa/Monrovia",
            Lists.newArrayList("new_time_zone_code_57")),

    /** (UTC)协调世界时 */
    UTC("Etc/UTC",
            "Etc/UTC",
            Lists.newArrayList()),

    /** (UTC-01:00)亚速尔群岛 */
    AZORES("Atlantic/Azores",
            "customize/Atlantic/Azores",
            Lists.newArrayList("new_time_zone_code_25")),

    /** (UTC-01:00)佛得角群岛 */
    CAPE_VERDE_ISLAND("Atlantic/Cape_Verde",
            "customize/Atlantic/Cape_Verde",
            Lists.newArrayList("new_time_zone_code_24")),

    /** (UTC-02:00)协调世界时-02 */
    UTC_MINUS_0200("Etc/GMT+2",
            "Etc/GMT+2",
            Lists.newArrayList()),

    /** (UTC-02:00)格陵兰 */
    GREENLAND("America/Godthab",
            "customize/America/Godthab",
            Lists.newArrayList("new_time_zone_code_22")),

    /** (UTC-03:00)圣皮埃尔和密克隆群岛 */
    SAINT_PIERRE_MIQUELON("America/Miquelon",
            "customize/America/Miquelon",
            Lists.newArrayList()),

    /** (UTC-03:00)萨尔瓦多 */
    SALVADOR("America/El_Salvador",
            "customize/America/El_Salvador",
            Lists.newArrayList()),

    /** (UTC-03:00)蓬塔阿雷纳斯 */
    PUNTA_ARENAS("America/Punta_Arenas",
            "customize/America/Punta_Arenas",
            Lists.newArrayList()),

    /** (UTC-03:00)蒙得维的亚 */
    MONTEVIDEO("America/Montevideo",
            "customize/America/Montevideo",
            Lists.newArrayList()),

    /** (UTC-03:00)卡宴,福塔雷萨 */
    CAYENNE("America/Cayenne",
            "customize/America/Cayenne",
            Lists.newArrayList()),

    /** (UTC-03:00)布宜诺斯艾利斯 */
    BUENOS_AIRES("America/Buenos_Aires",
            "customize/America/Buenos_Aires",
            Lists.newArrayList("new_time_zone_code_21")),

    /** (UTC-03:00)巴西利亚 */
    BRASILIA("America/Sao_Paulo",
            "customize/America/Sao_Paulo",
            Lists.newArrayList("new_time_zone_code_20")),

    /** (UTC-03:00)阿拉瓜伊纳 */
    ARAGUAINA("America/Araguaina",
            "customize/America/Araguaina",
            Lists.newArrayList()),

    /** (UTC-03:30)纽芬兰 */
    NEWFOUNDLAND("America/St_Johns",
            "customize/America/St_Johns",
            Lists.newArrayList("new_time_zone_code_19")),

    /** (UTC-04:00)乔治敦,拉巴斯,马瑙斯,圣胡安 */
    LA_PAZ("America/La_Paz",
            "customize/America/La_Paz",
            Lists.newArrayList()),

    /** (UTC-04:00)库亚巴 */
    CUIABA("America/Cuiaba",
            "customize/America/Cuiaba",
            Lists.newArrayList()),

    /** (UTC-04:00)亚松森 */
    ASUNCION("America/Asuncion",
            "customize/America/Asuncion",
            Lists.newArrayList()),

    /** (UTC-04:00)加拉加斯 */
    CARACAS("America/Caracas",
            "customize/America/Caracas",
            Lists.newArrayList("new_time_zone_code_17")),

    /** (UTC-04:00)圣地亚哥 */
    SANTIAGO("America/Santiago",
            "customize/America/Santiago",
            Lists.newArrayList("new_time_zone_code_18")),

    /** (UTC-04:00)大西洋时间(加拿大) */
    ATLANTIC_TIME("America/Halifax",
            "customize/America/Halifax",
            Lists.newArrayList("new_time_zone_code_16", "new_time_zone_code_23")),

    /** (UTC-05:00)印地安那州(东部) */
    INDIANA("America/Indianapolis",
            "customize/America/Indianapolis",
            Lists.newArrayList("new_time_zone_code_15")),

    /** (UTC-05:00)特克斯和凯科斯群岛 */
    TURKS_CAICOS("America/Grand_Turk",
            "customize/America/Grand_Turk",
            Lists.newArrayList()),

    /** (UTC-05:00)切图马尔 */
    CHETUMAL("America/Cancun",
            "customize/America/Cancun",
            Lists.newArrayList()),

    /** (UTC-05:00)海地 */
    HAITI("America/Port-au-Prince",
            "customize/America/Port-au-Prince",
            Lists.newArrayList()),

    /** (UTC-05:00)波哥大,利马,基多,里奥布朗库 */
    BOGOTA("America/Bogota",
            "customize/America/Bogota",
            Lists.newArrayList("new_time_zone_code_13")),

    /** (UTC-05:00)哈瓦那 */
    HAVANA("America/Havana",
            "customize/America/Havana",
            Lists.newArrayList()),

    /** (UTC-05:00)东部时间(美国和加拿大) */
    EASTERN_TIME("America/New_York",
            "customize/America/New_York",
            Lists.newArrayList("new_time_zone_code_14")),

    /** (UTC-06:00)中美洲 */
    CENTRAL_AMERICA("America/Guatemala",
            "customize/America/New_York",
            Lists.newArrayList("new_time_zone_code_11")),

    /** (UTC-06:00)中部时间（美国和加拿大）*/
    CENTRAL_TIME("America/Chicago",
            "customize/America/Chicago",
            Lists.newArrayList("new_time_zone_code_10")),

    /** (UTC-06:00)萨斯喀彻温 */
    SASKATCHEWAN("America/Regina",
            "customize/America/Regina",
            Lists.newArrayList("new_time_zone_code_9")),

    /** (UTC-06:00)复活节岛 */
    EASTER_ISLAND("Pacific/Easter",
            "customize/Pacific/Easter",
            Lists.newArrayList()),

    /** (UTC-06:00)瓜搭拉哈拉,墨西哥城,蒙特雷 */
    MEXICO_CITY("America/Mexico_City",
            "customize/America/Mexico_City",
            Lists.newArrayList("new_time_zone_code_12")),

    /** (UTC-07:00)育空 */
    YUKON("America/Whitehorse",
            "customize/America/Whitehorse",
            Lists.newArrayList()),

    /** (UTC-07:00)亚利桑那 */
    ARIZONA("America/Phoenix",
            "customize/America/Phoenix",
            Lists.newArrayList("new_time_zone_code_7")),

    /** (UTC-07:00)山地时间(美国和加拿大) */
    MOUNTAIN_TIME("America/Denver",
            "customize/America/Denver",
            Lists.newArrayList("new_time_zone_code_6")),

    /** (UTC-07:00)拉巴斯,马扎特兰 */
    MAZATLAN("America/Chihuahua",
            "customize/America/Chihuahua",
            Lists.newArrayList("new_time_zone_code_8")),

    /** (UTC-08:00)协调世界时-08 */
    UTC_MINUS_0800("Etc/GMT+8",
            "Etc/GMT+8",
            Lists.newArrayList()),

    /** (UTC-08:00)下加利福尼亚州 */
    BAJA_CALIFORNIA("America/Tijuana",
            "customize/America/Tijuana",
            Lists.newArrayList()),

    /** (UTC-08:00)太平洋时间(美国和加拿大) */
    PACIFIC_TIME("America/Los_Angeles",
            "customize/America/Los_Angeles",
            Lists.newArrayList("new_time_zone_code_5")),

    /** (UTC-09:00)协调世界时-09 */
    UTC_MINUS_0900("Etc/GMT+9",
            "Etc/GMT+9",
            Lists.newArrayList()),

    /** (UTC-09:00)阿拉斯加 */
    ALASKA("America/Anchorage",
            "customize/America/Anchorage",
            Lists.newArrayList("new_time_zone_code_4")),

    /** (UTC-09:30)马克萨斯群岛 */
    MARQUESAS_ISLANDS("Pacific/Marquesas",
            "customize/Pacific/Marquesas",
            Lists.newArrayList()),

    /** (UTC-10:00)夏威夷 */
    HAWAII("Pacific/Honolulu",
            "customize/Pacific/Honolulu",
            Lists.newArrayList("new_time_zone_code_3")),

    /** (UTC-10:00)阿留申群岛 */
    ALEUTIAN_ISLANDS("America/Adak",
            "customize/America/Adak",
            Lists.newArrayList()),

    /** (UTC-11:00)协调世界时-11 */
    UTC_MINUS_1100("Etc/GMT+11",
            "Etc/GMT+11",
            Lists.newArrayList()),

    /** (UTC-12:00)国际日期变更线西 */
    INTERNATIONAL_DATE_LINE_WEST("Etc/GMT+12",
            "Etc/GMT+12",
            Lists.newArrayList("new_time_zone_code_1"));



    /** 默认 zoneId */
    private final String defaultZoneId;

    /** 自定义 zoneId */
    private final String customizeZoneId;

    /** 历史快码(临时兼容 idop 创建单据，后续外部系统参数改造完成可删除) */
    private final List<String> historyLookupCodes;

    /** 时区 */
    private final ZoneId zoneId;



    TimeZoneEnum(String defaultZoneId, String customizeZoneId, List<String> historyLookupCodes) {
        // 1.校验时区规则是否注册
        // 由于底层 register 在序列化 2025 数字时判断有异常，自定义标识选择不带数字
        Set<String> zoneIds;
        try {
            zoneIds = ZoneRulesProvider.getAvailableZoneIds();
            if (!zoneIds.contains("customize/Asia/Shanghai")) {
                ZoneRulesProvider.registerProvider(new TzdbZoneRulesProvider());
            }
        } catch (Exception ignored) {}

        // 2.初始化
        this.defaultZoneId = defaultZoneId;
        this.customizeZoneId = customizeZoneId;
        this.historyLookupCodes = historyLookupCodes;

        // 兜底机制:
        // 若注册后仍无法找到自定义规则，沿用 JDK 自带规则
        // 当前仅 UT 会出现该情况
        zoneIds = ZoneRulesProvider.getAvailableZoneIds();
        this.zoneId = ZoneId.of(
                zoneIds.contains(customizeZoneId) ? customizeZoneId : defaultZoneId);
    }

    @Override
    public String getValue() {
        return defaultZoneId;
    }

    @Override
    public String getLookupCode() {
        return this.defaultZoneId;
    }

    public ZonedTime toZonedTime(Date value) {
        return new ZonedTime(this, value);
    }

    /**
     * 修正数据库存储的时区错位时间
     */
    public Date fix(Date zonedTimeValue) {
        if (Objects.isNull(zonedTimeValue)) {
            return null;
        }
        DateTimeFormatter format = DateTimeFormatter.ofPattern(DATE_FORM);

        LocalDateTime localDateTime
                = LocalDateTime.parse(DateUtils.dateToString(zonedTimeValue, DATE_FORM), format);
        ZonedDateTime zonedDateTime = localDateTime.atZone(this.getZoneId());

        ZonedDateTime fixTime = zonedDateTime.withZoneSameInstant(ZoneId.systemDefault());
        return DateUtils.stringToDate(fixTime.format(format), DATE_FORM);
    }

    /**
     * 将服务器时间污染为时区错位时间用于入库
     */
    @SneakyThrows
    public Date pollute(Date value) {
        if (Objects.isNull(value)) {
            return null;
        }
        ZonedTime zonedTime = new ZonedTime(this, value);
        String zonedTimeString = zonedTime.zonedTimeString();

        SimpleDateFormat format = new SimpleDateFormat(DATE_FORM);
        return format.parse(zonedTimeString);
    }

    /**
     * 根据lookupCode取时间枚举
     */
    public static TimeZoneEnum getTimeZoneEnum(String lookupCode) {
        for (TimeZoneEnum timeZoneEnum : TimeZoneEnum.values()) {
            if (timeZoneEnum.getLookupCode().equals(lookupCode)) {
                return timeZoneEnum;
            }
        }

        return null;
    }

    /**
     * 历史快码转新快码
     * 兼容 idop / cnop 创建网络变更任务，后续外部系统改造完成可删除
     */
    public static String historyLookupCode2New(String historyCode) {
        return Arrays.stream(TimeZoneEnum.values())
                .filter(item -> item.historyLookupCodes.contains(historyCode))
                .findFirst()
                .map(TimeZoneEnum::getDefaultZoneId)
                .orElse(historyCode);
    }
}
