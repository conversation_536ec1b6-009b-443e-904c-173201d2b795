package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;

import java.util.List;
import java.util.Objects;

public class UpdateImportancePlugin extends BaseFlowOperationPlugin {

    @Override
    public void afterOperate(ExecuteEvent executeEvent) {
        IDataModel dataModel = getModel();

        // 1.获取页面填报信息
        // 重要程度 / 风险评估 / 操作等级
        List<TextValuePair> importance
                = ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.IMPORTANCE);
        List<TextValuePair> riskEvaluation
                = ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.RISK_EVALUATION);
        List<TextValuePair> operationLevel
                = ComponentUtils.getChooseComponentInfo(dataModel, ChangeOrderFieldConsts.OPERATION_LEVEL);

        // 2.检索任务中心对应数据
        String changeOrderId = dataModel.getRootDataEntity().getPkValue().toString();
        NetworkChangeAssignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);
        if (Objects.isNull(assignment)) {
            return;
        }

        // 3.更新填报信息
        NetworkChangeAssignment updateAssignment = new NetworkChangeAssignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setImportance(importance);
        updateAssignment.setRiskEvaluation(riskEvaluation);
        updateAssignment.setOperationLevel(operationLevel);

        AssignmentAbility.update(updateAssignment);
    }
}
