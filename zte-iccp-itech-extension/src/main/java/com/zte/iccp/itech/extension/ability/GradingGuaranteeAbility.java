package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.entity.OrderBy;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFactorConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ProductTypeFactorConsts;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.dto.orm.Range;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.BusinessInterruptionDurationFactorConsts.PRODUCT_OPERATION_TEAM;
import static com.zte.iccp.itech.extension.domain.constant.entity.BusinessInterruptionDurationFactorConsts.SCORE;
import static com.zte.iccp.itech.extension.domain.constant.entity.BusinessInterruptionDurationFactorConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.LocalFactorFieldConsts.COUNTRY;
import static com.zte.iccp.itech.extension.domain.constant.entity.LocalFactorFieldConsts.CUSTOMER_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.ProductTypeFactorConsts.*;

/**
 * 分级保障ability
 * <AUTHOR> 10335201
 * @date 2024-05-13 上午10:09
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class GradingGuaranteeAbility {
    /**
     * 查询业务中断时长系数，未配置时默认返回1
     * @param productOperationTeamIdPath 产品经营团队idPath
     * @param serviceDisconnectDuration 业务中断时长
     * @return: int
     */
    public static double queryBusinessInterruptionDurationFactor(String serviceDisconnectDuration,List<String> productOperationTeamIdPath) {
        if(CollectionUtils.isEmpty(productOperationTeamIdPath)||StringUtils.isBlank(serviceDisconnectDuration)){
            return CommonConstants.DOUBLE_ONE;
        }
        List<String> fields = Lists.newArrayList(SCORE);
        List<IFilter> filters = Lists.newArrayList();
        filters.add(new Filter(PRODUCT_OPERATION_TEAM, Comparator.IN, productOperationTeamIdPath));
        filters.add(new Filter(MAXIMUM_SERVICE_INTERRUPTION_DURATION, Comparator.GE, PropertyValueConvertUtil.getBigDecimal(serviceDisconnectDuration)));
        filters.add(new Filter(MINIMUM_SERVICE_INTERRUPTION_DURATION, Comparator.LE, PropertyValueConvertUtil.getBigDecimal(serviceDisconnectDuration)));
        List<BusinessInterruptionDurationFactor> businessInterruptionDurationFactors = QueryDataHelper.query(BusinessInterruptionDurationFactor.class, fields, filters);
        return !CollectionUtils.isEmpty(businessInterruptionDurationFactors)?businessInterruptionDurationFactors.get(CommonConstants.INTEGER_ZERO).getScore():CommonConstants.DOUBLE_ONE;
    }

    /**
     * 查询业务中断时长系数列表
     * @param productOperationTeamIdPath 产品经营团队idPath
     * @return: int
     */
    public static List<BusinessInterruptionDurationFactor> queryBusinessInterruptionDurationFactorList(List<String> productOperationTeamIdPath) {
        List<String> fields = Lists.newArrayList(ID,PRODUCT_OPERATION_TEAM,MINIMUM_SERVICE_INTERRUPTION_DURATION,MAXIMUM_SERVICE_INTERRUPTION_DURATION,SCORE);
        List<IFilter> filters = Lists.newArrayList();
        filters.add(new Filter(PRODUCT_OPERATION_TEAM, Comparator.IN, productOperationTeamIdPath));
        return QueryDataHelper.query(BusinessInterruptionDurationFactor.class, fields, filters);
    }

    /**
     * 查询操作类型系数，未配置时默认返回1
     * @param productIdPath 产品小类idPath
     * @param operationType 操作类型快码值
     * @return: int
     */
    public static double queryOperationTypeFactor(List<String> productIdPath,String operationType) {
        if(CollectionUtils.isEmpty(productIdPath)||StringUtils.isBlank(operationType)){
            return CommonConstants.DOUBLE_ONE;
        }
        List<String> fields = Lists.newArrayList(OperationTypeFactorConsts.SCORE);
        List<IFilter> filters = Lists.newArrayList();
        filters.add(new Filter(OperationTypeFactorConsts.PRODUCT_SUBCATEGORY, Comparator.IN, productIdPath));
        filters.add(new Filter(OperationTypeFactorConsts.OPERATION_TYPE, Comparator.EQ, operationType));
        List<OperationTypeFactor> operationTypeFactors = QueryDataHelper.query(OperationTypeFactor.class, fields, filters);
        return !CollectionUtils.isEmpty(operationTypeFactors)?operationTypeFactors.get(CommonConstants.INTEGER_ZERO).getScore():CommonConstants.DOUBLE_ONE;
    }

    public static List<LocalFactor> getLocalFactorList(String countryCode, String customerId, List<String> fieldList) {
        Filter countryFilter = new Filter(COUNTRY, Comparator.REGEXP, countryCode);
        Filter customerIdFilter = new Filter(CUSTOMER_ID, Comparator.EQ, customerId);
        return QueryDataHelper.query(LocalFactor.class, fieldList, Lists.newArrayList(customerIdFilter, countryFilter));
    }

    // 局点系数查询-罗鹏
    public static double getLocalFactor(String countryCode, String customerId, List<String> fieldList) {
        if(StringUtils.isBlank(countryCode)||StringUtils.isBlank(customerId)){
            return CommonConstants.DOUBLE_ONE;
        }
        Filter countryFilter = new Filter(COUNTRY, Comparator.REGEXP, countryCode);
        Filter customerIdFilter = new Filter(CUSTOMER_ID, Comparator.EQ, customerId);
        List<LocalFactor> localFactors = QueryDataHelper.query(LocalFactor.class, fieldList, Lists.newArrayList(customerIdFilter, countryFilter));
        return !CollectionUtils.isEmpty(localFactors)?localFactors.get(CommonConstants.INTEGER_ZERO).getScore():CommonConstants.DOUBLE_ONE;
    }


    // 产品分类系数查询-罗鹏
    /**
     * 朱小安 10335201   2024/9/13 上午11:15
     * 查询逻辑改造
     * 1、查询条件来自【主单据-产品分类】和【操作对象列表-产品型号】
     * 2、优先匹配【操作对象列表-产品型号】条件，查到数据即可返回（多条数据时取最大值）
     * 3、其次匹配【【主单据-产品分类】条件，且此时要限制产品型号为空，查到数据即可返回（多条数据时取最大值）
     * 4、以上都查不到数据时，默认返回：1
     */
    public static double getProductTypeFactor(List<String> productModels,
                                              List<String> productSubcategoryList,
                                              List<String> fieldList) {
        if(CollectionUtils.isEmpty(productModels)&&CollectionUtils.isEmpty(productSubcategoryList)){
            return CommonConstants.DOUBLE_ONE;
        }

        List<IFilter> filters = Lists.newArrayList();
        filters.add(new Filter(STATUS, Comparator.EQ, CommonConstants.INTEGER_ONE));

        // 先根据【操作对象列表-产品型号】查询，查到数据即可返回（多条数据时取最大值）
        filters.add(new Filter(PRODUCT_MODEL, Comparator.R_CONTAINS, productModels));
        List<ProductTypeFactor> productTypeFactors =  QueryDataHelper.query(ProductTypeFactor.class, fieldList, filters);
        if(!CollectionUtils.isEmpty(productTypeFactors)){
            return productTypeFactors.stream().filter(a -> null != a.getScore()).mapToDouble(ProductTypeFactor::getScore).max().orElse(CommonConstants.DOUBLE_ONE);
        }

        // 上一步查询为空，则根据【主单据-产品分类】查询，且此时要限制产品型号为空，查到数据即可返回（多条数据时取最大值）
        filters.remove(filters.size() - CommonConstants.INTEGER_ONE);
        filters.add(new Filter(PRODUCT_SUBCATEGORY, Comparator.R_CONTAINS, productSubcategoryList));
        filters.add(new Filter(PRODUCT_MODEL, Comparator.IS_NULL, null));
        List<ProductTypeFactor> productTypeFactorList =  QueryDataHelper.query(ProductTypeFactor.class, fieldList, filters);
        if(!CollectionUtils.isEmpty(productTypeFactorList)){
            return productTypeFactorList.stream().filter(a -> null != a.getScore()).mapToDouble(ProductTypeFactor::getScore).max().orElse(CommonConstants.DOUBLE_ONE);
        }

        // 都查不到数据，默认返回：1
        return CommonConstants.DOUBLE_ONE;
    }

    public static double getProdTypeFactorByProSubIds(List<String> productSubIds, List<String> fieldList) {
        Filter productModelFilter = new Filter(ProductTypeFactorConsts.PRODUCT_SUBCATEGORY, Comparator.R_CONTAINS, productSubIds);
        Filter statusFilter = new Filter(STATUS, Comparator.EQ, 1);
        List<ProductTypeFactor> productTypeFactors =  QueryDataHelper.query(ProductTypeFactor.class, fieldList, Lists.newArrayList(productModelFilter, statusFilter));
        return productTypeFactors.stream().filter(a -> null != a.getScore()).mapToDouble(ProductTypeFactor::getScore).max().orElse(CommonConstants.DOUBLE_ONE);
    }

    /**
     * 检索指定类型的所有实体数据，主要用于处理历史数据
     *
     * @param clazz  要检索的实体类类型
     * @param fields 检索所需的字段列表
     * @param filters 过滤条件列表
     * @param range   检索范围，指定分页信息
     * @param orderBy 排序规则，可变参数，指定排序字段及顺序
     * @param <T>    实体类的泛型
     * @return 返回符合条件的实体数据列表
     */
    public static <T extends BaseEntity> List<T> query(
            Class<T> clazz,
            List<String> fields,
            List<IFilter> filters,
            Range range,
            OrderBy... orderBy) {

        return QueryDataHelper.query(clazz, fields, filters, range, orderBy);
    }

    /**
     * 批量更新指定的实体数据
     *
     * @param specificProductTypeFactors 需要更新的实体数据列表
     * @param <T> 实体类的泛型
     */
    public static <T extends BaseEntity> void batchUpdate(List<T> specificProductTypeFactors) {
        if (CollectionUtils.isEmpty(specificProductTypeFactors)) {
            return;
        }

        SaveDataHelper.batchUpdate(specificProductTypeFactors);
    }

}
