package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel("三营网络配置(dataKey)")
@Setter
@Getter
public class DomesticNetworkConfigurationVO {

    @JsonProperty("custom_hu9fbwpc")
    @ApiModelProperty()
    private String networkId;

    @JsonProperty("custom_iz5a3lmu")
    @ApiModelProperty("网络ID(网络编码)")
    private String networkCode;

    @JsonProperty("custom_2wfhk0ej")
    @ApiModelProperty("网络名称")
    private String networkName;

    @JsonProperty("custom_v4o4f8hk")
    @ApiModelProperty("责任单位")
    private String responsibleDept;

    @JsonProperty("custom_u19kxds1")
    @ApiModelProperty("产品类型")
    private String prodClass;

    @JsonProperty("custom_46lqek83")
    @ApiModelProperty("客户网络名称")
    private String customerNetworkName;

    @JsonProperty("custom_lv64g6lk")
    @ApiModelProperty("客户名称")
    private String customerName;

    @JsonProperty("custom_xvrhi7wi")
    @ApiModelProperty("客户标识")
    private String customerClassification;

    @JsonProperty("custom_9shzo5m9")
    @ApiModelProperty("是否政企")
    private String isGovernment;

    @JsonProperty("custom_v8400poc")
    @ApiModelProperty("是否受限制主体")
    private String isLimit;

    @JsonProperty("custom_y5mai2fv")
    @ApiModelProperty("网络责任人")
    private String networkResponsiblePerson;

    @JsonProperty("custom_rdxirt98")
    @ApiModelProperty("网络属性")
    private String networkAttribute;

    @JsonProperty("custom_htpj0skt")
    @ApiModelProperty("等级分值")
    private String gradeScore;

    @JsonProperty("custom_pmu83trd")
    @ApiModelProperty("状态")
    private String networkStatus;

    @JsonProperty("custom_dnt9vk9u")
    @ApiModelProperty("更新人")
    private String updatedUser;

    @JsonProperty("custom_krub2uuw")
    @ApiModelProperty("更新时间")
    private Date updatedDate;
}
