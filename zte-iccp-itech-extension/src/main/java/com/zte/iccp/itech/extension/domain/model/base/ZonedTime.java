package com.zte.iccp.itech.extension.domain.model.base;

import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import lombok.Getter;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.Calendar;
import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORM;

/**
 * 特定时区时间
 *
 * <AUTHOR>
 * @since 2025/02/22
 */
public class ZonedTime {

    /** 指定时区格式化器 */
    private final DateTimeFormatter format = DateTimeFormatter.ofPattern(DATE_FORM);

    /** 时区 */
    @Getter
    private final TimeZoneEnum timezone;

    /** 时区时间 */
    @Getter
    private ZonedDateTime zonedDateTime;

    public ZonedTime(TimeZoneEnum timezone, Date date) {
        this.timezone = timezone;
        this.zonedDateTime = ZonedDateTime.ofInstant(date.toInstant(), timezone.getZoneId());
    }

    public Date getValue() {
        return DateUtils.stringToDate(zonedTimeString(), DATE_FORM);
    }

    public String zonedTimeString() {
        return zonedDateTime.format(format);
    }

    public int get(ChronoField field) {
        return zonedDateTime.get(field);
    }

    public ZonedTime add(int calendarField, int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getValue());
        calendar.add(calendarField, amount);

        LocalDateTime localDateTime
                = LocalDateTime.parse(DateUtils.dateToString(calendar.getTime(), DATE_FORM), format);
        zonedDateTime = localDateTime.atZone(this.getTimezone().getZoneId());
        return this;
    }
}
