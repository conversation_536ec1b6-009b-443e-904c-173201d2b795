package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 关联类型枚举
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum LinkTypeEnum {
    /**
     * 父级
     */
    PARENT("3"),

    /**
     * 子级
     */
    CHILD("4"),

    /**
     * 关联
     */
    ASSOCIATING("5"),

    /**
     * 被关联
     */
    ASSOCIATED("6"),

    /**
     * 绑定
     */
    BINDING("7"),

    /**
     * 被绑定
     */
    BOUND("8")
    ;

    /**
     * 关联类型ID
     */
    private final String id;
}
