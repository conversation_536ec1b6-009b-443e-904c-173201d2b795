package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.INSTRUCTIONS;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.INSTRUCTIONS_DESC;

/**
 * <AUTHOR>
 * @create 2024/7/4 下午3:38
 */
@ApiModel("批次 -高危指令")
@Setter
@Getter
@BaseSubEntity.Info(value = "network_batch_instructions", parent = BatchTask.class)
public class BatchHighInstruction extends BaseSubEntity {

    @ApiModelProperty("高危指令表")
    @JsonProperty(value = INSTRUCTIONS)
    private String instructions;


    @ApiModelProperty("支持方式")
    @JsonProperty(value = INSTRUCTIONS_DESC)
    private String instructionsDesc;
}
