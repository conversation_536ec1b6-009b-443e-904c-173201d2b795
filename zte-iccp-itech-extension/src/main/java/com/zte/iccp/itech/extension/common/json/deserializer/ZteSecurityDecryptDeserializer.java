package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.itp.msa.util.security.ZteSecurity;
import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
public class ZteSecurityDecryptDeserializer extends JsonDeserializer<String> {
    @Override
    @SneakyThrows
    public String deserialize(JsonParser p, DeserializationContext ctxt) {
        String encrypted = p.getCodec().readValue(p, String.class);
        return ZteSecurity.getInstance().decrypt(encrypted);
    }
}
