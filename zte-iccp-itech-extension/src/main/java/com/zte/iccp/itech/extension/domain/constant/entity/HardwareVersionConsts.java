package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 基础配置 - 规则型号配置常量
 *
 * <AUTHOR> jiang<PERSON><PERSON>en
 * @date 2024/12/23
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HardwareVersionConsts {

    /**
     * 物料名称
     */
    public static final String MATERIAL_NAME = "material_name";

    /**
     * 硬件版本
     */
    public static final String MATERIAL_VERSION = "material_version";
}
