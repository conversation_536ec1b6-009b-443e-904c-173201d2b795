package com.zte.iccp.itech.extension.common.utils;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.util.CollectionUtils;

import java.lang.annotation.Annotation;
import java.lang.annotation.Repeatable;
import java.lang.reflect.AnnotatedElement;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 反射工具类
 *
 * <AUTHOR> 10284287
 * @since 2024/05/09
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ReflectUtils {

    @SneakyThrows
    public static Map<String, Object> obj2Map(Object obj) {
        if (obj == null) {
            return Maps.newHashMap();
        }

        Map<String, Object> map = Maps.newHashMap();
        for (Field field : getAllFields(obj.getClass())) {
            field.setAccessible(true);
            if (checkModifiers(field.getModifiers(), Modifier.STATIC, Modifier.FINAL)) {
                continue;
            }

            map.put(field.getName(), field.get(obj));
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    @SneakyThrows
    public static <T> T map2Obj(Map<String, Object> map, Class<T> clazz) {
        if (map == null) {
            return null;
        }

        if (clazz == Map.class) {
            return (T) map;
        }

        T t = clazz.newInstance();
        if (map.isEmpty()) {
            return t;
        }

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String k = entry.getKey();
            Object v = entry.getValue();

            Field field = JsonUtils.findField(clazz, k);
            if (field == null || Modifier.isStatic(field.getModifiers())) {
                continue;
            }

            field.setAccessible(true);
            if (v instanceof Map) {
                //noinspection rawtypes
                v = map2Obj((Map) v, field.getType());
            }

            field.set(t, v);
        }

        return t;
    }

    /**
     * 检查目标修饰符是否满足任一检查值
     */
    public static boolean checkModifiers(int value, int... checkPoints) {
        for (int p : checkPoints) {
            if ((value | p) == p) {
                return true;
            }
        }
        return false;
    }

    public static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = Arrays.stream(clazz.getDeclaredFields())
                .collect(Collectors.toList());

        if (clazz.getSuperclass() != Object.class) {
            fields.addAll(getAllFields(clazz.getSuperclass()));
        }

        return fields;
    }

    public static List<Method> getAllMethods(Class<?> clazz) {
        List<Method> methods = Arrays.stream(clazz.getDeclaredMethods())
                .collect(Collectors.toList());
        methods.addAll(Lists.newArrayList(clazz.getMethods()));
        return methods;
    }

    /**
     * 查找指定的注解及在Repeatable注解内的元素
     */
    @SneakyThrows
    public static <T extends Annotation> List<T> findAnnotations(
            AnnotatedElement element,
            Class<T> annotationClass) {
        T annotation = element.getAnnotation(annotationClass);
        if (annotation != null) {
            return Lists.newArrayList(annotation);
        }

        Repeatable repeatable = annotationClass.getAnnotation(Repeatable.class);
        if (repeatable == null) {
            return Lists.newArrayList();
        }

        Class<? extends Annotation> repeatableAnnotationClass = repeatable.value();
        Annotation repeatableAnnotation = element.getAnnotation(repeatableAnnotationClass);
        if (repeatableAnnotation == null) {
            return Lists.newArrayList();
        }

        Method value = repeatableAnnotation.annotationType().getMethod("value");
        //noinspection unchecked
        return Lists.newArrayList((T[]) value.invoke(repeatableAnnotation));
    }

    /**
     * 获取当前类字段，转换为数据库的映射对象,
     */
    @SneakyThrows
    public static List<String> getAllFieldNames(Class<?> clazz) {
        List<Field> allFields = getAllFields(clazz);
        if (CollectionUtils.isEmpty(allFields)) {
            return new ArrayList<>();
        }

        List<String> properties = Lists.newArrayList();
        for (Field field : allFields) {
            JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
            if (Objects.nonNull(jsonProperty)) {
                properties.add(jsonProperty.value());
            }
        }

        return properties;
    }
}