package com.zte.iccp.itech.extension.domain.model.clockin;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.LookupValueDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/09/21
 */
@Getter
@Setter
public class ClockInTaskDetailVO{

    private String id;

    /** 是否可以撤销打卡 */
    private Boolean revocable;

    /** 打卡任务类型 */
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private ClockInTaskTypeEnum taskType;

    /** 操作等级 */
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private OperationLevelEnum operationLevel;

    /** 任务状态 */
    @JsonDeserialize(using = LookupValueDeserializer.class)
    private ClockInOptionEnum taskStatus;

    /** 产品线ID */
    private String productLineId;

    /** 产品线名称 */
    private String productLineName;

    /** 操作主题 */
    private String operationSubject;

    /** 批次任务编号 */
    private String batchCode;

    /** 时区（如：GMT+8:00） */
    private String timeZone;

    /** 打卡人工号 */
    private String operatorId;

    /** 打卡人名称 */
    private String operatorName;

    /** 办事处/代表处ID */
    private String responsibleDeptId;

    /** 办事处/代表处名称 */
    private String responsibleDeptName;

    /** 网络清单 BATCH_TASK_ID 查批次任务 batch_network_assignment 中操作对象结果*/
    private List<NisNetwork> networks;


    /** 计划准备开始时间，操作任务独有 */
    private Date planPrepareStartTime;

    /** 操作类型 */
    private String operationType;

    /** 人员清单 BATCH_TASK_ID 查批次任务 batch_network_assignment 中操作人员列表*/
    private List<ClockInOperatorVO> operators;

    /** 打卡记录列表 */
    private List<ClockInRecordVO> records;


    /** 计划准备结束时间，操作任务独有 */
    private Date planPrepareEndTime;

    /** 计划执行开始时间，操作任务独有 */
    private Date planExecuteStartTime;

    /** 计划执行结束时间，操作任务独有 */
    private Date planExecuteEndTime;

    /** 计划测试开始时间，操作任务独有 */
    private Date planTestStartTime;

    /** 计划测试结束时间，操作任务独有 */
    private Date planTestEndTime;

    /** 计划值守开始时间，值守任务独有 */
    private Date planOnDutyStartTime;

    /** 计划值守结束时间，值守任务独有 */
    private Date planOnDutyEndTime;

    /** 实际值守开始时间，值守任务独有 */
    private Date actualOnDutyStartTime;

    /** 实际值守结束时间，值守任务独有 */
    private Date actualOnDutyEndTime;

    /** 值守频次，值守任务独有 */
    private Integer onDutyFrequency;

    /** 值守时长，值守任务独有 */
    private Double onDutyDurationHours;

    private List<OnDutyStageTimeVO> onDutyStageTime;

    /** 是否国际单据 0：国际，1：国内 */
    private String internalFlag;

    /** 是否待操作 true：待操作 */
    private boolean operationStartFlag;

    /** 是否待值守 true：待值守 */
    private boolean onDutyStartFlag;
}
