package com.zte.iccp.itech.extension.plugin.form.approvaldetails.partnerchangeorder.propertychanged;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.changeorder.OperationObjectAbility;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.domain.model.subentity.SubconOperationObject;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.helper.EmployeeAttributeProps;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.helper.EmployeeCidProps;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.interfaces.PropertyChangedBaseFormPlugin;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.plugin.form.approvaldetails.helper.ReviewerHepler.*;

/**
 * 合作商_办事处经理审批中，网络处审核人（组）插件
 *
 * 网络处审核人的获取规则和内部网络变更单一致
 */
public class PartnerNetDeptApprovalPlugin implements PropertyChangedBaseFormPlugin, LoadDataBaseFormPlugin {
    @Override
    public void loadData(LoadDataBaseFormPlugin.LoadDataEventArgs args) {
        IDataModel dataModel = args.getModel();
        IFormView formView = args.getFormView();
        // 驳回场景，办事处产品经理审核结果非通过，直接返回
        Object resObj = dataModel.getValue(FIELD_APPROVE_RESULT_OFFICE_PROD_MANAGER_CID);
        String res = TextValuePairHelper.getValue(resObj);
        if (!PASS.equals(res)) {
            return;
        }

        boolean res1 = setReviewerAttrAfterLoadData(formView, dataModel,
                COMPONENT_NET_DEPT_APPROVAL_CID,
                COMPONENT_NET_DEPT_APPROVAL_SELECTED_CID,
                FIELD_NET_DEPT_APPROVAL_CID,
                FIELD_NET_DEPT_APPROVAL_SELECTED_CID);

        EmployeeCidProps personCidProps = new EmployeeCidProps(
                COMPONENT_NET_DEPT_APPROVE_TEAM_CID,
                COMPONENT_NET_DEPT_APPROVE_TEAM_MULTI_CID,
                BUTTON_HZF_SELECT_NET_APPROVE_TEAM_CID,
                SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.NET_DEPT_APPROVE_TEAM,
                SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.NET_DEPT_APPROVE_TEAM_MULTI);
        boolean res2 = setReviewerAttrAfterLoadData(formView, dataModel, personCidProps);
        if (res1 && res2) {
            return;
        }
        setNetDeptApprovalPlugin(dataModel, formView);
    }


    @Override
    public void operate(PropertyChangedBaseFormPlugin.ValueChangedEventArgs args) {
        IDataModel dataModel = args.getModel();
        IFormView formView = args.getFormView();
        setNetDeptApprovalPlugin(dataModel, formView);
    }

    private void setNetDeptApprovalPlugin(IDataModel dataModel, IFormView formView) {
        // 是否需要办事处产品经理审核
        String isUpgradeNetDept = TextValuePairHelper.getValue(
                dataModel.getValue(FIELD_IS_UPGRADE_NET_DEPT_CID));

        // 获取审核结果
        String approveResult = TextValuePairHelper.getValue(
                dataModel.getValue(FIELD_APPROVE_RESULT_OFFICE_PROD_MANAGER_CID));

        // 是
        if (Y.equals(isUpgradeNetDept) && PASS.equals(approveResult)) {
            displayOfficeProdManagerComponent(formView, dataModel);
        } else {
            hiddenOfficeProdManagerComponent(formView, dataModel);
        }
    }

    /**
     * 展示 办事处产品经理审核人 / 组 组件
     */
    private void displayOfficeProdManagerComponent(IFormView formView, IDataModel dataModel) {
        // 检索合作方网络变更单
        String billId = (String) dataModel.getRootDataEntity().getPkValue();
        IChangeOrder changeOrder = QueryDataHelper.get(
                SubcontractorChangeOrder.class,
                Lists.newArrayList(ID, IS_GOV_ENT, OPERATION_TYPE, PRODUCT_CATEGORY, RESPONSIBLE_DEPT, ACCN_TYPE),
                billId);
        if (null == changeOrder) {
            return;
        }

        // 检索产品型号
        String productModelId = null;
        List<OperationObject> operationObjects
                = OperationObjectAbility.listOperationObject(billId, SubconOperationObject.class);
        if (!CollectionUtils.isEmpty(operationObjects)) {
            // 取【主产品】操作对象的产品型号
            List<String> productIds = operationObjects
                    .stream()
                    .filter(item -> !CollectionUtils.isEmpty(item.getIsMainProduct())
                            && CommonConstants.Y.equals(item.getIsMainProduct().get(0).getValue()))
                    .map(OperationObject::getProductModel)
                    .collect(Collectors.toList());
            productModelId = CollectionUtils.isEmpty(productIds)
                    ? operationObjects.get(INTEGER_ZERO).getProductModel()
                    : productIds.get(0);
        }

        // 获取网络处审核人（组）
        // 产品分类、代表处、是否政企、操作类型、客户标识 + 产品型号（从操作对象取【主产品】操作对象产品型号数据）
        ApproverConfiguration approver = ApproverConfigAbility.getApprover(
                changeOrder, productModelId, ApprovalTypeEnum.TECHNOLOGY_DELIVERY_DEPT_NETWORK_OFFICE);
        List<Employee> persons = Lists.newArrayList();
        List<Employee> groups = Lists.newArrayList();
        if (approver != null) {
            persons = approver.getApproverPersons();
            groups = approver.getApproverGroups();
        }

        EmployeeCidProps personCidProps = new EmployeeCidProps(
                COMPONENT_NET_DEPT_APPROVAL_CID,
                COMPONENT_NET_DEPT_APPROVAL_SELECTED_CID,
                "",
                SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.NET_DEPT_APPROVAL,
                SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.NET_DEPT_APPROVAL_SELECTED);
        EmployeeCidProps groupCidProps = new EmployeeCidProps(
                COMPONENT_NET_DEPT_APPROVE_TEAM_CID,
                COMPONENT_NET_DEPT_APPROVE_TEAM_MULTI_CID,
                BUTTON_HZF_SELECT_NET_APPROVE_TEAM_CID,
                SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.NET_DEPT_APPROVE_TEAM,
                SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.NET_DEPT_APPROVE_TEAM_MULTI);
        EmployeeAttributeProps personAttrProps = new EmployeeAttributeProps(
                new PageStatusAttributeBuilder().normal().build(),
                new BasicAttributeBuilder().attribute(REQUIRED, true).build(),
                new PageStatusAttributeBuilder().normal().build(),
                new PageStatusAttributeBuilder().hidden().build(),
                new BasicAttributeBuilder().attribute(REQUIRED, false).build());
        EmployeeAttributeProps groupAttrProps = new EmployeeAttributeProps(
                new PageStatusAttributeBuilder().normal().build(),
                new BasicAttributeBuilder().attribute(REQUIRED, false).build(),
                new PageStatusAttributeBuilder().normal().build(),
                new PageStatusAttributeBuilder().hidden().build(),
                new BasicAttributeBuilder().attribute(REQUIRED, false).build());
        setEmployeeComponentAttribute(formView, dataModel, persons, personCidProps, personAttrProps);
        setEmployeeComponentAttribute(formView, dataModel, groups, groupCidProps, groupAttrProps);
    }

    /**
     * 隐藏 办事处产品经理审核人 / 组 组件
     */
    private void hiddenOfficeProdManagerComponent(IFormView formView, IDataModel dataModel) {
        // 获网络处审核人（组）（员工组件/下拉组件）均设置为隐藏，非必填并清空
        setComponentProps(formView, dataModel, true,
                COMPONENT_NET_DEPT_APPROVAL_CID, FIELD_NET_DEPT_APPROVAL_CID,
                new PageStatusAttributeBuilder().hidden().build(),
                new BasicAttributeBuilder().attribute(REQUIRED, false).build());
        setComponentProps(formView, dataModel, true,
                COMPONENT_NET_DEPT_APPROVAL_SELECTED_CID, FIELD_NET_DEPT_APPROVAL_SELECTED_CID,
                new PageStatusAttributeBuilder().hidden().build(),
                new BasicAttributeBuilder().attribute(REQUIRED, false).build());
        setComponentProps(formView, dataModel, true,
                COMPONENT_NET_DEPT_APPROVE_TEAM_CID, FIELD_NET_DEPT_APPROVE_TEAM_CID,
                new PageStatusAttributeBuilder().hidden().build(),
                new BasicAttributeBuilder().attribute(REQUIRED, false).build());
        setComponentProps(formView, dataModel, true,
                COMPONENT_NET_DEPT_APPROVE_TEAM_MULTI_CID, FIELD_NET_DEPT_APPROVE_TEAM_MULTI_CID,
                new PageStatusAttributeBuilder().hidden().build(),
                new BasicAttributeBuilder().attribute(REQUIRED, false).build());
        formView.getClientViewProxy().setControlState(
                BUTTON_HZF_SELECT_NET_APPROVE_TEAM_CID,
                new PageStatusAttributeBuilder().hidden().build());
    }
}
