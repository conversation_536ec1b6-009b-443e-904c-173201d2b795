package com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo;

import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.spi.model.hol.PersonGeneralInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 合作商网络变更单审批详情
 */
@Getter
@Setter
@NoArgsConstructor
public class PartnerReviewDetailVO {
    // 合作方网络变更操作审批（固定字段）
    private String pratnerOperationsApproval;

    /**
     * 批次任务取消操作审批（固定字段）
     */
    private String cancelOperationApproval;

    /**
     * 批次任务取消原因
     */
    private String reasonOfCancellation;

    // 是否紧急操作
    private String isEmergencyOperation;

    // 操作主题
    private String operationSubject;

    // 单据编号
    private String coNo;

    // 操作说明
    private String operationDesc;

    // 紧急原因操作简述
    private String emergencyOperationReason;

    // 客户名称
    private String customerName;

    // 客户联系人
    private String customerContacts;

    // 操作方案描述
    private String operationPlanDesc;

    // 客户网络名称
    private String networkName;

    // 计划操作时间
    private List<PlanOperateVO> planOperateDate;

    // 网络负责人
    private List<Employee> netOwners;

    // 操作负责人
    private List<Employee> operationOwners;

    // 附件
    private List<AttachFilesVO> attachFiles;

    // 审批进展
    private List<ApprovalProgressVO> approvalProgress;

    // 是否政企
    private Boolean isGovEnt;

    // 申请时间
    private Date submitDate;

    // 申请人
    private PersonGeneralInfo submitter;
}
