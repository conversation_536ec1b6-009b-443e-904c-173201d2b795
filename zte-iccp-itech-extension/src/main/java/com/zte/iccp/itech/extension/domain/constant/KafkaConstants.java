package com.zte.iccp.itech.extension.domain.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class KafkaConstants {

    /**
     * 消息主题
     */
    public static final class Topic {

        /** CSC 南京 */
        public static final String CSC_NANJING = "zte-crm-csc-nj";
    }

    /**
     * 消息 Key
     */
    public static final class Key {

        /** 全量 */
        public static final String WHOLE = "*";

        /** CSC - 服务请求变更消息 */
        public static final String CSC_SERVICE_REQUEST_CHANGED = "zte_crm_csc_servicerequest";
    }
}
