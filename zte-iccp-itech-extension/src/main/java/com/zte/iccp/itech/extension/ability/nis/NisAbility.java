package com.zte.iccp.itech.extension.ability.nis;

import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.ProductClassificationDto;
import com.zte.iccp.itech.extension.spi.model.TreeServiceObjectVo;
import com.zte.iccp.itech.extension.spi.model.nis.*;
import com.zte.iccp.itech.extension.spi.model.query.*;
import com.zte.iccp.itech.extension.spi.model.zxrdc.TreeServiceObjectDto;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.metadata.engine.common.constant.NumberConst;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.FORWARD_SLASH;

/**
 * <AUTHOR>
 * @create 2024/6/11 上午9:31
 */
public class NisAbility {

    /**
     * 根据网络ID查询网络名称Map<id,name>
     *
     * @return List<NisNetwork>
     */
    public static Map<String, String> queryNisNetworkMap(List<String> networkIds) {
        if (CollectionUtils.isEmpty(networkIds)) {
            return Maps.newHashMap();
        }
        networkIds = networkIds.stream().distinct().collect(Collectors.toList());
        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();
        nisNetworkQuery.setNetworkIds(networkIds);
        PageRows<NisNetwork> networkPageRows = NisClient.queryNisNetworkList(nisNetworkQuery);
        List<NisNetwork> networkList = networkPageRows.getRows();
        if (CollectionUtils.isEmpty(networkList)) {
            return Maps.newHashMap();
        }

        String langId = ContextHelper.getLangId();
        return networkList.stream()
                .collect(Collectors.toMap(NisNetwork::getNetworkId,
                        (CommonConstants.ZH_CN.equals(langId) ? NisNetwork::getNameZh : NisNetwork::getNameEn)));
    }

    /**
     * 根据网络ID查询客户网络名称Map<id,CustomerNetworkName>
     *
     * @return List<NisNetwork>
     */
    public static Map<String, String> queryCustomerNetworkNameMap(List<String> networkIds) {
        if (CollectionUtils.isEmpty(networkIds)) {
            return Maps.newHashMap();
        }

        networkIds = networkIds.stream().distinct().collect(Collectors.toList());
        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();
        nisNetworkQuery.setNetworkIds(networkIds);
        PageRows<NisNetwork> networkPageRows = NisClient.queryNisNetworkList(nisNetworkQuery);
        List<NisNetwork> networkList = networkPageRows.getRows();
        if (CollectionUtils.isEmpty(networkList)) {
            return Maps.newHashMap();
        }

        return networkList.stream()
                .collect(Collectors.toMap(NisNetwork::getNetworkId, NisNetwork::getCustomerNetworkName));
    }

    /**
     * 根据产品型号ID查询产品型号名称Map<id,name>
     *
     * @return List<NisNetwork>
     */
    public static Map<String, String> queryProductModelMap(List<String> productModelIds) {
        if (CollectionUtils.isEmpty(productModelIds)) {
            return Maps.newHashMap();
        }
        List<ProductModel> networkList = queryProductModelList(productModelIds);
        if (CollectionUtils.isEmpty(networkList)) {
            return Maps.newHashMap();
        }

        return networkList.stream().collect(Collectors.toMap(ProductModel::getProdModelId, ProductModel::getProdModel));
    }

    /**
     * 根据产品型号ID查询产品型号信息
     * @return List<NisNetwork>
     */
    public static List<ProductModel> queryProductModelList(List<String> productModelIds) {
        if (CollectionUtils.isEmpty(productModelIds)) {
            return new ArrayList<>();
        }

        productModelIds = productModelIds.stream().distinct().collect(Collectors.toList());
        ProductModelQuery query = new ProductModelQuery();
        query.setProdModelIds(productModelIds);
        PageRows<ProductModel> networkPageRows = NisClient.queryProductModels(query);
        return networkPageRows.getRows();
    }

    /**
     * 根据产品型号ID查询idpath
     * @return List<NisNetwork>
     */
    public static List<String> queryProductModelIdPath(List<String> productModelIds) {
        List<ProductModel> productModels = queryProductModelList(productModelIds);
        List<String> idPaths = productModels.stream()
                .map(ProductModel::getProdModelFullIdPath).collect(Collectors.toList());
        return idPaths;
    }

    public static List<ProductClassificationDto> queryProductModelNames(List<String> names, List<String> productLineCodes) {
        ProductQuery params = new ProductQuery(names, productLineCodes);
        return NisClient.queryProductModelNames(params);
    }

    /**
     * 查询map<productModelName,info>
     */
    public static Map<String, ProductClassificationDto> getProductModelMap(List<String> names, List<String> productLineCodes) {
        List<ProductClassificationDto> list = queryProductModelNames(names, productLineCodes);
        Map<String, ProductClassificationDto> productNameDtoMap = Maps.newHashMap();
        list.forEach(item -> {
            // 只需要查询 启用的产品
            if (CommonConstants.ENABLED.equals(item.getPstatus()) && CommonConstants.ENABLED.equals(item.getStatus())) {
                productNameDtoMap.put(LangUtils.get(ContextHelper.getLangId(), item.getProductModelNameZh(), item.getProductModelNameEn()), item);
            }
        });
        return productNameDtoMap;
    }

    /**
     * 产品类型详情
     */
    public static ProductClassification getProductClassification(String keyword) {
        ProductClassificationQuery params = new ProductClassificationQuery(NumberConst.TWO, keyword);
        List<ProductClassification> productClassifications = NisClient.queryProductClassification(params);
        if (CollectionUtils.isEmpty(productClassifications)) {
            return null;
        }
        return productClassifications.get(0);
    }

    /**
     * 根据名称 查询组织详情
     * @param name
     * @return
     */
    public static OrganizationTreeVo getOrganization(String name) {
        List<OrganizationTreeVo> list = NisClient.queryOrganization(name, "4");
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    /**
     * 根据产品型号ID查询产品型号信息
     *
     * @return List<NisNetwork>
     */
    public static Map<String, NetworkElementVo> queryNisElmentMap(List<String> elementIds) {
        List<NetworkElementVo> networkElementVos = NisClient.getNisElementVoByIds(elementIds);
        return networkElementVos.stream()
                .collect(Collectors.toMap(NetworkElementVo::getId, Function.identity()));
    }

    /**
     * 实体id和产品分类映射（去掉后缀"/"）
     *
     * 1.低代码保存的数据集产品分类json中value值会带后缀“/”
     * 2.查询nis接口时，不能带
     * @param entities 数据集
     * @param textValuePairList 入参为实体对象，value为产品分类变量List<TextValuePair>
     * @return 实体id，产品分类（不带后缀“/”）
     * @param <T> 实体
     */
    public static <T extends BaseEntity> Map<String, String> getProductMap(List<T> entities,
                                                                            Function<T, List<TextValuePair>> textValuePairList) {
        return entities.stream()
                .filter(item -> !CollectionUtils.isEmpty(textValuePairList.apply(item)))
                .collect(Collectors.toMap(BaseEntity::getId, item -> {
                    String category = textValuePairList.apply(item).get(0).getValue();
                    return category.endsWith(FORWARD_SLASH) ? category.substring(0, category.length() - 1) : category;
                }));
    }

    /**
     * 获取产品分类id(不带后缀"/")和产品对象map  和上述方法getProductMap一起使用
     *
     * @param entities 数据集
     * @param fieldExtractor 入参为实体对象，出差为List<TextValuePair>（这里指代产品变量属性）
     * @return 产品分类id，产品对象
     * @param <T> 实体
     */
    public static <T extends BaseEntity> Map<String, BasicProductInfo> getProductObjMap(List<T> entities,
                                                                                        Function<T, List<TextValuePair>> fieldExtractor) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyMap();
        }

        List<BasicProductInfo> basicProductInfoList = NisClient.queryProductInfo(
                extractUniqueProductCategories(entities, fieldExtractor));

        if (CollectionUtils.isEmpty(basicProductInfoList)) {
            return Collections.emptyMap();
        }

        return basicProductInfoList.stream()
                .collect(Collectors.toMap(
                        item -> item.getPidPath() + item.getId(),
                        item -> item, (v1, v2) -> v1));
    }


    /**
     * 获取产品分类，产品名称（根据语言自动转换中英文）map
     *
     * @param entities 数据集
     * @param fieldExtractor 入参为实体对象，value为产品分类变量List<TextValuePair>
     * @return 产品路径id（不带后缀"/"）,产品名称
     * @param <T> 实体
     */
    public static <T extends BaseEntity> Map<String, String> getProductNameMap(List<T> entities,
                                                                               Function<T, List<TextValuePair>> fieldExtractor) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyMap();
        }

        return NisClient.queryProductPathName(
                extractUniqueProductCategories(entities, fieldExtractor));
    }


    /* Started by AICoder, pid:a2180qc0a54bfef147a7096b20934c271a19f2f2 */
    public static <T> List<String> extractUniqueProductCategories(List<T> entities,
                                                                  Function<T, List<TextValuePair>> fieldExtractor) {
        return entities.stream()
                .flatMap(entity -> {
                    List<TextValuePair> textValuePairs = fieldExtractor.apply(entity);
                    if (CollectionUtils.isEmpty(textValuePairs)) {
                        return Stream.empty();
                    }
                    String category = textValuePairs.get(0).getValue();
                    if (StringUtils.isNotEmpty(category)) {
                        if (category.endsWith(FORWARD_SLASH)) {
                            category = category.substring(0, category.length() - 1);
                        }
                        return Stream.of(category);
                    }
                    return Stream.empty();
                }).distinct().collect(Collectors.toList());
    }

    public static List<TreeServiceObjectDto> queryOrgnizaList(List<String> codes) {
        TreeServiceObjectQuery query = new TreeServiceObjectQuery();
        query.setCodeList(codes);
        String langId = ContextHelper.getLangId();
        List<TreeServiceObjectDto> reuslt = new ArrayList<>();
        try {
            ContextHelper.setLangId(CommonConstants.EN_US);
            List<TreeServiceObjectVo> organizationClassUs = NisClient.queryOrganizationClass(query);
            if (CollectionUtils.isEmpty(organizationClassUs)) {
                return reuslt;
            }

            ContextHelper.setLangId(CommonConstants.ZH_CN);
            List<TreeServiceObjectVo> organizationClassZh = NisClient.queryOrganizationClass(query);
            Map<String, TreeServiceObjectVo> codeCnMap = organizationClassZh.stream()
                    .collect(Collectors.toMap(TreeServiceObjectVo::getCode, Function.identity()));
            organizationClassUs.forEach(item -> {
                TreeServiceObjectDto dto = TreeServiceObjectDto.builder().code(item.getCode())
                        .level(item.getLevel()).parentCode(item.getParentCode()).codeFullPath(item.getCodeFullPath())
                        .nameEn(item.getName()).nameFullPathEn(item.getNameFullPath()).build();
                TreeServiceObjectVo treeServiceObjectVo = codeCnMap.get(item.getCode());
                if (treeServiceObjectVo != null) {
                    dto.setNameZh(treeServiceObjectVo.getName());
                    dto.setNameFullPathZh(treeServiceObjectVo.getNameFullPath());
                }
                reuslt.add(dto);
            });
        } finally {
            ContextHelper.setLangId(langId);
        }
        return reuslt;

    }

    /**
     * 根据条件查询产品型号中英文
     */
    public static List<ProductModel> queryProductModels(ProductModelQuery productModelQuery) {
        String langId = ContextHelper.getLangId();
        List<ProductModel> reuslt;
        try {
            ContextHelper.setLangId(CommonConstants.ZH_CN);
            PageRows<ProductModel> zhRows = NisClient.queryProductModels(productModelQuery);
            reuslt = zhRows.getRows();
            if (CollectionUtils.isEmpty(reuslt)) {
                return reuslt;
            }
            ContextHelper.setLangId(CommonConstants.EN_US);
            PageRows<ProductModel> enRows = NisClient.queryProductModels(productModelQuery);
            Map<String, ProductModel> enMap = enRows.getRows().stream()
                    .collect(Collectors.toMap(ProductModel::getProdModelId, Function.identity()));
            reuslt.forEach(i -> {
                i.setProdModelZh(i.getProdModel());
                ProductModel productModel = enMap.get(i.getProdModelId());
                if (productModel != null) {
                    i.setProdModelEn(productModel.getProdModel());
                }
            });
        } finally {
            ContextHelper.setLangId(langId);
        }
        return reuslt;
    }

    /**
     * 获取下拉树组件的名称（不获取全路径名称）
     * 1.如获取产品经营团队，则传入1层id，获取产品经营团队的名称
     * 2.如获取产品小类则传入4层id，获取产品小类对应的名称
     * ps：如格式非下拉树能解析的List<TextValuePair> 则方法不适应
     *
     * @param productId 6872952355824349361/6872952355824349365/8098010551841013787/8098010551841013857/
     * @return 对应的产品名称
     */
    public static String getProductName(Object productId) {
        if (ObjectUtils.isEmpty(productId)) {
            return null;
        }

        List<TextValuePair> jsonObjects = JsonUtils.parseArray(productId, TextValuePair.class);
        // 获取到产品分类id
        String prodClassIdPath = jsonObjects.get(CommonConstants.INTEGER_ZERO).getValue();
        // 有后缀则去掉后缀/
        prodClassIdPath = prodClassIdPath.endsWith(FORWARD_SLASH)
                ? prodClassIdPath.substring(0, prodClassIdPath.length() - 1)
                : prodClassIdPath;

        Map<String, String> prodSubCategoryNames = NisClient.queryProductName(Lists.newArrayList(prodClassIdPath));

        return !CollectionUtils.isEmpty(prodSubCategoryNames) ? prodSubCategoryNames.get(prodClassIdPath) : null;
    }

    /**
     * 根据产品分类查询产品信息
     *
     * @param productIds productIds
     * @return 产品分类id，产品信息
     */
    public static Map<String, BasicProductInfo> queryProductInfoMap(List<String> productIds) {
        List<BasicProductInfo> basicProductInfoList = NisClient.queryProductInfo(productIds);
        if (CollectionUtils.isEmpty(basicProductInfoList)) {
            return Collections.emptyMap();
        }

        return basicProductInfoList.stream()
                .collect(Collectors.toMap(
                        item -> item.getPidPath() + item.getId(),
                        item -> item, (v1, v2) -> v1));
    }
}
