package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 研发经理审核
 *
 * <AUTHOR> 10284287
 * @since 2024/06/26
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OpAssocProdDevMgrConfirmFieldConsts {
    public static final String PRODUCT_TYPE = "product_type_rd";

    public static final String IS_RELATED_PROD_APPROVAL = "is_related_prod_approval_net_rd";

    public static final String APPROVAL = "approval_rd";

    public static final String APPROVAL_TEAM = "approval_team_rd";

    /**
     * 操作关联产品中文名（研发经理）
     */
    public static final String INTEGRATED_ASSOCIATED_PRODUCT_RD_ZH = "integrated_associated_product_rd_zh";

    /**
     * 操作关联产品英文名（研发经理）
     */
    public static final String INTEGRATED_ASSOCIATED_PRODUCT_RD_EN = "integrated_associated_product_rd_en";
}
