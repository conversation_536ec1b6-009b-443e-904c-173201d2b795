package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 操作人员角色枚举
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OperatorRoleEnum implements SingletonTextValuePairsProvider {
    /**
     * 操作负责人
     */
    OPERATING_SUPERVISOR("1", "操作负责人", "Operating Supervisor"),

    /**
     * 操作人员
     */
    OPERATOR("2", "操作人员", "Operator"),

    /**
     * 交叉检查人员
     */
    CROSS_CHECKER("3", "交叉检查人员", "Cross Checker"),

    /**
     * 值守人员
     */
    WATCHMAN("4", "值守人员", "Watchman"),

    /**
     * 测试人员
     */
    TESTER("5", "测试人员", "Tester"),

    /**
     * 辅助操作人员
     */
    AUXILIARY_OPERATOR("6", "辅助操作人员", "Auxiliary Operator"),

    /**
     * 外包人员
     */
    OUTSOURCING_PERSONNEL("7", "外包人员", "Outsourcing Personnel"),

    /**
     * 支持人员
     */
    SUPPORT_PERSONNEL("8", "支持人员", "Support Personnel")
    ;

    /**
     * 操作人员角色值
     */
    private final String value;

    /**
     * 操作人员角色中文描述
     */
    private final String zhCn;

    /**
     * 操作人员角色英文描述
     */
    private final String enUs;

    public static OperatorRoleEnum fromValue(String str){
        return Arrays.stream(OperatorRoleEnum.values())
                .filter(item -> item.value.equals(str))
                .findFirst()
                .orElse(null);
    }
}
