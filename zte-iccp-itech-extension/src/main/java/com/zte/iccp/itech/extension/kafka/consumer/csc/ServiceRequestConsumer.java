package com.zte.iccp.itech.extension.kafka.consumer.csc;

import com.zte.iccp.itech.extension.ability.assignment.FaultAssignmentAbility;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.KafkaConstants;
import com.zte.iccp.itech.extension.domain.model.kafka.ServiceRequestMessage;
import com.zte.iccp.itech.extension.kafka.consumer.ConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;

@Slf4j
public class ServiceRequestConsumer implements ConsumerListener {

    @Override
    public String getMessageKey() {
        return KafkaConstants.Key.CSC_SERVICE_REQUEST_CHANGED;
    }

    @Override
    public String getMessageTopic() {
        return KafkaConstants.Topic.CSC_NANJING;
    }

    @Override
    public void consumeMessage(ConsumerRecord<String, String> record) {
        try {
            ServiceRequestMessage message
                    = JsonUtils.parseObject(record.value(), ServiceRequestMessage.class);
            FaultAssignmentAbility.consumeServiceRequestMsg(message);
        } catch (Exception e) {
            log.error("ServiceRequest message consume Failed: " + e.getMessage());
        }
    }
}
