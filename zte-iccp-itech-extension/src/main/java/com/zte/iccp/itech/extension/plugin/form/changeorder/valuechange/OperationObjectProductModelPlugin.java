package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.plugin.form.partnerchangeorder.valuechange.PartnerValueChangeBaseFormPlugin;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * 网络变更操作单（内部+合作方）操作对象子表单【产品型号】字段值变更插件
 *
 * <AUTHOR>
 * @since 2025/07/31
 */
public class OperationObjectProductModelPlugin implements ValueChangeBaseFormPlugin, PartnerValueChangeBaseFormPlugin {

    /**
     * 内部变更单propertyChanged统一封装方法
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        invoke(args.getModel(), args.getFormView(), args.getChangeData());
    }

    /**
     * 合作方变更单propertyChanged统一封装方法
     */
    @Override
    public void operate(PartnerValueChangedEventArgs args) {
        invoke(args.getModel(), args.getFormView(), args.getChangeData());
    }

    public void invoke(IDataModel dataModel, IFormView formView, ChangeData changeData) {
        Object productModelObj = changeData.getNewValue();
        int rowIndex = changeData.getRowIndex();

        // 提前返回空值情况
        if (ObjectUtils.isEmpty(productModelObj)) {
            handleNonMainProduct(formView, rowIndex, true);
            return;
        }

        DynamicDataEntity entity = (DynamicDataEntity) productModelObj;
        String productModelIdFullPath = entity.get(SERVICE_PRODUCT_MODEL_FULL_ID_PATH_CID).toString();
        String isMainProduct = TextValuePairHelper.getValue(dataModel.getValue(OperationObjectFieldConsts.IS_MAIN_PRODUCT, rowIndex));
        String productId = TextValuePairHelper.getValue(dataModel.getValue(FIELD_PRODUCT_CID));

        boolean isValidModel = checkProductModel(productModelIdFullPath, productId);

        // 主产品处理逻辑（提前返回）
        // 若该数据行为【主产品】，则校验该产品型号是否是主单据产品分类下属产品型号
        // 若是，则正常更新；若不是，则弹窗报错，且清空【产品型号】的值
        if (CommonConstants.Y.equals(isMainProduct)) {
            handleMainProduct(formView, dataModel, rowIndex, isValidModel);
            return;
        }

        // 非主产品处理逻辑
        // 若数据行不为【主产品】，【产品型号】字段正常更新，但是也需要校验该产品型号是否是主单据产品分类下属型号
        // 若是，则设置【主产品】组件状态为normal；若不是，则设置【主产品】组件状态为禁用，不允许选择
        handleNonMainProduct(formView, rowIndex, isValidModel);
    }

    private void handleMainProduct(IFormView formView, IDataModel dataModel, int rowIndex, boolean isValidModel) {
        if (!isValidModel) {
            formView.showMessage(MessageConsts.OPERATION_OBJECT_MAIN_PRODUCT_MODEL_ERROR, MsgType.ERROR);
            dataModel.setValue(FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, null, rowIndex);
        }
    }

    private void handleNonMainProduct(IFormView formView, int rowIndex, boolean isValidModel) {
        IEntryTableSupport table = (IEntryTableSupport) formView.getControl(COMPONENT_OPERATION_OBJECT_ORDER_CID);
        String behavior = isValidModel ? NORMAL : DISABLED;
        table.getTableState().setCellAttribute(rowIndex, OperationObjectFieldConsts.IS_MAIN_PRODUCT, BEHAVIOR, behavior);
    }

    private Boolean checkProductModel(String productModelIdFullPath, String productId) {
        int level = CommonConstants.INTEGER_FOUR;
        String product4LevelId = ProductUtils.getProductIdByLevel(productId, level);
        String productModel4LevelId = ProductUtils.getProductIdByLevel(productModelIdFullPath, level);
        return StringUtils.equals(productModel4LevelId, product4LevelId);
    }

}
