package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BatchSummaryFieldConsts {

    /**
     * 批次号
     */
    public static final String BATCH_NO = "batch_no";

    /**
     * 网元数量
     */
    public static final String NETWORK_ELEMENT_COUNT = "ne_count";

    /**
     * 计划操作开始时间
     */
    public static final String PLAN_OPERATION_START_TIME = "plan_operation_start_time";

    /**
     * 计划操作结束时间
     */
    public static final String PLAN_OPERATION_END_TIME = "plan_operation_end_time";

    /**
     * 操作账号
     */
    public static final String OPERATION_ACCOUNT = "operation_account";

    /**
     * 操作描述
     */
    public static final String OPERATION_DESCRIPTION = "operation_description";

    /**
     * 高危指令
     */
    public static final String INSTRUCTIONS = "instructions";

    /**
     * 指令描述
     */
    public static final String INSTRUCTIONS_DESC = "instructions_desc";

    /**
     * 指令描述
     */
    public static final String HIGH_RISK_INSTRUCTION_DESC = "high_risk_instruction_desc";

    /**
     * 时间冲突
     */
    public static final String TIME_CONFLICT = "time_conflict";

    /**
     * 网元数量
     */
    public static final String NE_COUNT = "ne_count";

    /**
     * 操作已回退
     */
    public static final String IS_RETURNED = "is_returned";

    /**
     * 计划操作/升级对象（局点/站点、OMC）全部完成
     */
    public static final String IS_ALL_COMPLETED = "is_all_completed";

    /**
     * 计划实现的需求全部达成（对已完成操作/升级的局点/站点等对象）
     */
    public static final String IS_ALL_REACH = "is_all_reach";

    /**
     * 有无重大故障遗留
     */
    public static final String MAJOR_FAULTS_BEHIND = "major_faults_behind";

    /**
     * 操作后（7天内）重要网络指标正常（无显著下降）
     */
    public static final String NETWORK_INDICATORS = "network_indicators";

    /**
     * 是第一次操作/升级完成
     */
    public static final String IS_FIRST_OPERATION = "is_first_operation";

    /**
     * 多次操作原因
     */
    public static final String MULTIPLE_OPERATION_REASON = "multiple_operation_reason";

    /**
     * 失败原因分类
     */
    public static final String FAILURE_REASON = "failure_reason_select";

    /**
     * 操作手册齐套性
     */
    public static final String IS_OPERATION_MANUAL = "is_operation_manual";

    /**
     * 操作文档指导性得分
     */
    public static final String OPERATION_DOC_SCORE = "operation_doc_score";

    /**
     * 操作文档可获取时间
     */
    public static final String CAN_OPERATION_TIME = "can_operation_time";

    /**
     * 版本及配套文档可获取时间
     */
    public static final String VERSION_TIME = "version_time";

    /**
     * 操作实际投入人天
     */
    public static final String FACT_INPUT_DAY = "fact_input_day";

    /**
     * 操作总结
     */
    public static final String OPERATION_SUMMARY = "operation_summary";

    /**
     * 操作人
     */
    public static final String OPERATION_PERSON = "operation_person";

    /**
     * 操作时间
     */
    public static final String RESULT_OPERATION_DATE = "result_operation_date";

    /**
     * 多次操作原因
     */
    public static final String APP_MULTIPLE_OPERATION_REASON = "app_multiple_operation_reason";

    /**
     * 审核确认备注
     */
    public static final String REVIEW_CONFIRMATION = "approval_description";

    /**
     * 工具落地状态
     */
    public static final String TOOL_LANDING_STATUS = "tool_landing_status";

    /**
     * 未使用工具原因
     */
    public static final String NO_TOOL_REASON = "no_tool_reason";

    /**
     * 工具名称
     */
    public static final String TOOL_NAME = "tool_name";

    /**
     * 工具产品支持状态
     */
    public static final String TOOL_PRODUCT_STATUS = "tool_product_status";

    /**
     * 操作复杂度及效率评价
     */
    public static final String EFFICIENCY_EVALUATION = "efficiency_evaluation";

    /**
     * 工具使用满意度
     */
    public static final String TOOL_SATISFACTION = "tool_satisfaction";

    /**
     * 远程支持情况
     */
    public static final String REMOTE_SUPPORT = "remote_support";

    /**
     * 操作人员操作能力评估
     */
    public static final String OPERATOR_EVALUATION = "operator_evaluation";

    /**
     * 网络变更操作是否成熟
     */
    public static final String OPERATION_MATURE = "operation_mature";
}
