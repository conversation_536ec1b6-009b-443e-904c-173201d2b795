package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;


/**
 * 技术管理任务操作记录枚举
 *
 * <AUTHOR> jiang<PERSON><PERSON>en
 * @date 2024/6/4
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ManageTaskOperationRecordEnum {

    /**
     * 创建任务
     */
    MANAGE_TASK_CREDIT("创建任务","Create Task",
            "manage.task.operation.record.credit"),

    /**
     * 进展反馈
     */
    FEEDBACK_PROGRESS("进展反馈","Progress Feedback",
            "manage.task.operation.record.feedback.progress"),

    /**
     * 完成任务，申请关闭
     */
    APPLY_CLOSE("完成任务，申请关闭","Complete the Task",
            "manage.task.operation.record.apply.close"),

    /**
     * 申请废止
     */
    APPLICATION_REVOKED("申请废止","Application for Abolition",
            "manage.task.operation.record.application.revoked"),

    /**
     * 责任转交
     */
    RESPONSIBILITY_TRANSFERENCE("责任转交","Transfer of Responsibility",
            "manage.task.operation.record.responsibility.transfer"),

    /**
     * 关闭任务
     */
    DISABLE_TASK("关闭任务","Close Task",
            "manage.task.operation.record.close.task"),

    /**
     * 驳回任务
     */
    REJECT_TASK("驳回任务","Reject Task",
            "manage.task.operation.record.reject.task"),
    ;

    /**
     * 操作说明中文
     */
    private final String operationDescZhCn;

    /**
     * 操作说明英文
     */
    private final String operationDescEnUs;

    /**
     * 文案管理key
     */
    private final String msgKey;

    /**
     * 根据操作描述（中文或英文）获取对应的msgKey
     *
     * @param operationDesc 操作描述（中文或英文）
     * @return 对应的msgKey，如果未找到匹配项则返回null
     */
    public static String getMsgKeyByOperationDesc(String operationDesc) {
        for (ManageTaskOperationRecordEnum record : ManageTaskOperationRecordEnum.values()) {
            if (record.getOperationDescZhCn().equals(operationDesc) || record.getOperationDescEnUs().equals(operationDesc)) {
                return record.getMsgKey();
            }
        }
        return null;
    }
}
