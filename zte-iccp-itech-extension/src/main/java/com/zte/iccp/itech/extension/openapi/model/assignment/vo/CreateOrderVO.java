package com.zte.iccp.itech.extension.openapi.model.assignment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
/**
 * <AUTHOR>
 * @create 2024/8/29 上午9:45
 */
@ApiModel("idop和cnop创建草稿单VO")
@Setter
@Getter
public class CreateOrderVO {

    @ApiModelProperty("单据ID")
    private String changeOrderId;

    @ApiModelProperty("单据预览url")
    private String url;

    @ApiModelProperty("单据状态")
    private String changeStatus;

    @ApiModelProperty("单据状态中文名")
    private String changeStatusCn;

    @ApiModelProperty("单据状态英文名")
    private String changeStatusEn;

    @ApiModelProperty("单据编码")
    private String changeOrderIdDisplay;
}
