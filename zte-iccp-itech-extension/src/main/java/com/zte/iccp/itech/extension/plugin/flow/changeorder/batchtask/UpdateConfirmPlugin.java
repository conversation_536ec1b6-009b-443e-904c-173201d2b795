package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.OperatorAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.vo.ConfirmCopy;
import com.zte.iccp.itech.extension.domain.model.vo.OperatorCopy;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CONFIRM_COPY;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.NOTICE_COPY;

/**
 * 批次任务，审批通过修改副本数据
 *
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class UpdateConfirmPlugin extends BaseFlowPlugin {

    private static final String URGENT_FLAG = "URGENT_FLAG";

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String batchId = body.getBusinessId();
        String flowCode = body.getFlowCode();
        Map<String, Object> values = Maps.newHashMap();
        ConfirmCopy confirmCopy;
        Class<? extends BaseEntity> entityEnumType;
        if (ApprovalConstants.BATCH_TASK_FLOW.equals(flowCode)) {
            entityEnumType = BatchTask.class;
            BatchTask batchTask = BatchTaskAbility.get(batchId, new ArrayList<>());
            String confirmCopyStr = batchTask.getConfirmCopy();
            if (!StringUtils.hasText(confirmCopyStr)) {
                return false;
            }
            confirmCopy = JsonUtils.parseObject(confirmCopyStr, ConfirmCopy.class);
            confirmCopy.setUrgentFlag(batchTask.getUrgentFlag());
            confirmCopy.setControlPeriodFlag(batchTask.getControlPeriodFlag());
            confirmCopy.setPlanOperationStartTime(batchTask.getPlanOperationStartTime());
            confirmCopy.setPlanOperationEndTime(batchTask.getPlanOperationEndTime());
            List<BatchTaskOperator> operators = OperatorAbility.getBatchOperators(batchId, BatchTaskOperator.class);
            if (!CollectionUtils.isEmpty(operators)) {
                List<OperatorCopy> operatorCopies = new ArrayList<>();
                operators.forEach(item -> {
                    OperatorCopy op = new OperatorCopy();
                    op.setOperatorRole(item.getOperatorRole());
                    op.setOperatePerson(item.getOperatePersonEmpNo());
                    operatorCopies.add(op);
                });
                confirmCopy.setOperators(operatorCopies);
            }
        } else {
            entityEnumType = SubcontractorBatchTask.class;
            String type = (String) body.getVariables().get("update_type");
            SubcontractorBatchTask batchTask = BatchTaskAbility.getSub(batchId, new ArrayList<>());
            String confirmCopyStr = batchTask.getConfirmCopy();
            if (!StringUtils.hasText(confirmCopyStr)) {
                return false;
            }
            confirmCopy = JsonUtils.parseObject(confirmCopyStr, ConfirmCopy.class);
            confirmCopy.setUrgentFlag(batchTask.getUrgentFlag());
            confirmCopy.setPlanOperationStartTime(batchTask.getPlanOperationStartTime());
            confirmCopy.setPlanOperationEndTime(batchTask.getPlanOperationEndTime());
            if (!URGENT_FLAG.equals(type)) {
                List<SubcontractorBatchOperator> operators = OperatorAbility.getBatchOperators(batchId, SubcontractorBatchOperator.class);
                if (!CollectionUtils.isEmpty(operators)) {
                    List<OperatorCopy> operatorCopies = new ArrayList<>();
                    operators.forEach(item -> {
                        OperatorCopy op = new OperatorCopy();
                        op.setOperatorRole(item.getOperatorRole());
                        op.setOperatePerson(item.getOperatePersonEmpNo());
                        operatorCopies.add(op);
                    });
                    confirmCopy.setOperators(operatorCopies);
                }
            }
        }

        Object type = body.getVariables().get("is_notice");
        if(type != null ) {
            values.put(NOTICE_COPY, JSON.toJSONString(confirmCopy));
        }
        values.put(CONFIRM_COPY, JSON.toJSONString(confirmCopy));
        SaveDataHelper.update(entityEnumType, batchId, values);

        return false;
    }
}
