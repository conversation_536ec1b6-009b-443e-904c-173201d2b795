package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 操作日志记录字段常量
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/12
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OperationLogRecordConsts {
    /**
     * 操作日志记录关联id
     */
    public static final String OPERATION_RECORD_RELATION_ID = "relation_id";

    /**
     * 操作日志记录关联父id
     */
    public static final String OPERATION_RECORD_PARENT_RELATION_ID = "parent_relation_id";

    /**
     * 操作名称
     */
    public static final String OPERATION_NAME = "operation_name";

    /**
     * 操作类型
     */
    public static final String OPERATION_LOG_OPERATION_TYPE = "operation_type";

    /**
     * 操作名称前缀
     */
    public static final String OPERATION_LOG_OPERATION_NAME_PREFIX = "operation_name_prefix";

    /**
     * 子名称
     */
    public static final String OPERATION_LOG_CHILD_NAME = "child_name";

    /**
     * 操作人员
     */
    public static final String OPERATION_LOG_RESPONSIBLE_PERSON = "responsible_person";

    /**
     * 附件
     */
    public static final String OPERATION_LOG_ATTACHMENT = "attachment";

    /**
     * 操作描述
     */
    public static final String OPERATION_LOG_OPERATION_DESC = "operation_description";
}
