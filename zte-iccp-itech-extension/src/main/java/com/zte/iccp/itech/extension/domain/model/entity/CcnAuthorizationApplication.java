package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.CcnAuthorizationApplicationFieldConsts.*;

@ApiModel("CCN 默认授权文件申请单")
@Setter
@Getter
@BaseEntity.Info("default_authorization_file_application")
public class CcnAuthorizationApplication extends BaseEntity {

    @ApiModelProperty("单据编号")
    @JsonProperty(value = AD_NO)
    private String orderNo;

    @ApiModelProperty("单据状态")
    @JsonProperty(value = AD_STATUS)
    private List<TextValuePair> status;

    @ApiModelProperty("责任单位")
    @JsonProperty(value = ORGANIZATION_ID)
    private List<TextValuePair> organization;

    @ApiModelProperty("产品分类")
    @JsonProperty(value = PRODUCT_ID)
    private List<TextValuePair> productClassification;

    @ApiModelProperty("网络ID")
    @JsonProperty(value = NETWORK_ID)
    private String networkId;

    @ApiModelProperty("申请人")
    @JsonProperty(value = APPLY_BY)
    private List<Employee> applyBy;

    @ApiModelProperty("申请时间")
    @JsonProperty(value = APPLY_TIME)
    private Date applyTime;

    @ApiModelProperty("国家 / 地区")
    @JsonProperty(value = COUNTRY)
    private List<MultiLangText> country;

    @ApiModelProperty("省 / 州")
    @JsonProperty(value = PROVINCE)
    private List<MultiLangText> province;

    @ApiModelProperty("客户标识")
    @JsonProperty(value = ACCN_TYPE)
    private String accnType;

    @ApiModelProperty("局点名称（客户网络名称）")
    @JsonProperty(value = OFFICE_NAME)
    private String officeName;

    @ApiModelProperty("默认授权文件key")
    @JsonProperty(value = DEFAULT_AUTHORIZATION_FILE_KEY)
    private String defaultAuthorizationFileKey;

    @ApiModelProperty("默认授权文件")
    @JsonProperty(value = DEFAULT_AUTHORIZATION_FILE)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile defaultAuthorizationFile;

    @ApiModelProperty("已上传默认授权文件举证")
    @JsonProperty(value = UPLOAD_DEFAULT_AUTHORIZATION_FILE)
    private List<MultiAttachmentFile> evidenceFile;

    @ApiModelProperty("知会人")
    @JsonProperty(value = NOTIFIER)
    private List<Employee> notifier;

    @ApiModelProperty("抄送人")
    @JsonProperty(value = EMAIL_CC)
    private List<Employee> emailCCs;

    @ApiModelProperty("操作账号")
    @JsonProperty(value = OPERATION_ACCOUNT)
    private String operationAccount;
}
