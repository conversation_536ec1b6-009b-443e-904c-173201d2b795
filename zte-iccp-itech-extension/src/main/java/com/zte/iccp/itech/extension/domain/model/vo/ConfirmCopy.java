package com.zte.iccp.itech.extension.domain.model.vo;

import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/8/31 上午9:27
 */
@Getter
@Setter
public class ConfirmCopy {

    @ApiModelProperty("紧急操作")
    private BoolEnum urgentFlag;

    @ApiModelProperty("封网管控期操作")
    private BoolEnum controlPeriodFlag;

    @ApiModelProperty("计划操作开始时间")
    private Date planOperationStartTime;

    @ApiModelProperty("计划操作结束时间")
    private Date planOperationEndTime;

    @ApiModelProperty("操作人员")
    private List<OperatorCopy> operators;

}