package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/7 下午7:22
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TechSolutionCheckFieldConsts {
    /**
     * 检查内容
     */
    public static final String TECH_SOLUTION_CHECK_CONTENT = "tech_solution_check_content";

    /**
     * 检查内容 EN
     */
    public static final String TECH_SOLUTION_CHECK_CONTENT_EN = "tech_solution_check_content_en";

}
