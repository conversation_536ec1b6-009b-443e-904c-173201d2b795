package com.zte.iccp.itech.extension.plugin.flow.changeorder.subcontractor;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchOperationResultEnum;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_RESULT;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CURRENT_STATUS;

/**
 * 分包商批次任务，操作结果反馈
 *
 * <AUTHOR>
 * @since 2024/05/08
 */
public class SubcontractorResultPlugin extends BaseFlowOperationPlugin {

    @Override
    public void beforeOperate(ExecuteEvent executeEvent) {
        // 分包商只要操作结果
        String cancel = TextValuePairHelper.getValue(getModel().getValue(OPERATION_RESULT));
        if (BatchOperationResultEnum.OPERATION_CANCELLED.getValue().equals(cancel)) {
            getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.ABOLISH.getValue());
        } else {
            getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.RESULT_UNDER_REVIEW.getValue());
        }
    }
}
