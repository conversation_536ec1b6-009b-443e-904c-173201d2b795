package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 技术管理子任务字段常量
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/12
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ManageSubTaskFieldConsts {

    /**
     * 父任务id
     */
    public static final String TASK_PID = "p_id";
    /**
     * 子任务名称
     */
    public static final String SUBTASK_NAME = "subtask_name";

    /**
     * 子任务详情
     */
    public static final String SUBTASK_DETAIL = "subtask_detail";

    /**
     * 子任务责任人
     */
    public static final String SUBTASK_RESPONSIBLE_PERSON = "subtask_responsible_person";

    /**
     * 子任务要求完成日期
     */
    public static final String SUBTASK_COMPLETION_DATE = "subtask_completion_date";

    /**
     * 网络服务对象：网络id
     */
    public static final String NETWORK_ID = "network_id";

    /**
     * 网络名称，根据网络id查询出来的展示值
     */
    public static final String NETWORK_NAME = "network_name";

    /**
     * 子任务状态
     */
    public static final String SUBTASK_STATUS = "subtask_status";

    /**
     * 当前进展
     */
    public static final String CURRENT_PROGRESS = "current_progress";

    /**
     * 子任务单据编号
     */
    public static final String SUBTASK_CN_NO = "subtask_cn_no";

    /**
     * 责任单位id
     */
    public static final String RESPONSIBLE_ORG_ID = "responsible_org_id";

    /**
     * 责任单位名称
     */
    public static final String RESPONSIBLE_DEPT_NAME = "responsible_dept_name";

}
