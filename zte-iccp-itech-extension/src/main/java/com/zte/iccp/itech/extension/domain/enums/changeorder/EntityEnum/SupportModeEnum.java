package com.zte.iccp.itech.extension.domain.enums.changeorder.EntityEnum;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 支持方式
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum SupportModeEnum implements SingletonTextValuePairsProvider {
    /**
     * 现场支持
     */
    ONSITE_SUPPORT("ONSITE_SUPPORT", "现场支持", "On Site Support"),

    /**
     * 远程电话支持
     */
    REMOTE_PHONE_SUPPORT("REMOTE_PHONE_SUPPORT", "远程电话支持", "Remote Phone Support"),

    /**
     * 远程中心支持
     */
    REMOTE_CENTER_SUPPORT("REMOTE_CENTER_SUPPORT", "远程中心支持", "Remote Center Support");

    /**
     * 编码
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;

    public static SupportModeEnum fromValue(String value) {
        for (SupportModeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new IllegalArgumentException();
    }
}
