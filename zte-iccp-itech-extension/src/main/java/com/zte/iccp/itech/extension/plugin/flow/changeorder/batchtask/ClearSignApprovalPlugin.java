package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.model.vo.SubIntlAdminApproval;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.IntlAdminApprovalFieldConsts.*;

/**
 * 批次任务，进入会签节点情空审批记录
 *
 * <AUTHOR>
 * @since 2024/11/25
 */
public class ClearSignApprovalPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String batchId = body.getBusinessId();
        List<SubIntlAdminApproval> subIntlAdminApprovals = QueryDataHelper.queryByPid(SubIntlAdminApproval.class,
                Arrays.asList(ID, APPROVE_RESULT, APPROVE_OPINION, APPROVED_TIME), Collections.singletonList(batchId));

        for (SubIntlAdminApproval item : subIntlAdminApprovals) {
            Map<String, Object> values = Maps.newHashMap();
            values.put(APPROVE_RESULT, null);
            values.put(APPROVE_OPINION, null);
            values.put(APPROVED_TIME, null);
            if (StringUtils.hasText(item.getId()) && (item.getResult() != null || item.getApprovedDate() != null)) {
                SaveDataHelper.update(SubIntlAdminApproval.class, batchId, item.getId(), values);
            }
        }
        return false;
    }
}
