package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 操作类型系数
 *
 * <AUTHOR> 10335201
 * @date 2024-05-13 下午2:33
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductTypeFactorConsts {

    /**
     * 状态
     */
    public static final String STATUS = "status";
    /**
     * 分值
     */
    public static final String SCORE = "score";
    /**
     * 产品经营团队
     */
    public static final String PRODUCT_OPERATION_TEAM = "product_operation_team";
    /**
     * 产品线
     */
    public static final String PRODUCT_LINE = "product_line";
    /**
     * 产品大类
     */
    public static final String PRODUCT_CATEGORY = "product_category";
    /**
     * 产品小类
     */
    public static final String PRODUCT_SUBCATEGORY = "product_subcategory";
    /**
     * 产品型号
     */
    public static final String PRODUCT_MODEL = "product_model";

    public static final String PRODUCT_MODEL_EXT = "product_model_ext";
}
