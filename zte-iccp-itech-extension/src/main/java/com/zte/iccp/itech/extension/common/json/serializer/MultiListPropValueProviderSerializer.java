package com.zte.iccp.itech.extension.common.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zte.iccp.itech.extension.common.json.provider.PropValueProvider;
import lombok.SneakyThrows;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/09/07
 */
public class MultiListPropValueProviderSerializer<T> extends JsonSerializer<List<PropValueProvider<List<T>>>> {
    @Override
    @SneakyThrows
    public void serialize(List<PropValueProvider<List<T>>> value, JsonGenerator gen, SerializerProvider serializers) {
        List<T> textValuePairs = value.stream()
                .map(PropValueProvider::getPropValue)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        gen.writeObject(textValuePairs);
    }
}
