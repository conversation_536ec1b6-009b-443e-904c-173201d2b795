package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.*;

@ApiModel("审核人数据更新类")
@Setter
@Getter
@BaseEntity.Info("Approver_Config")
public class ApproverConfigurationUpdate extends BaseEntity {

    @JsonProperty(value = APPROVAL_NODE)
    @ApiModelProperty("审核节点")
    private List<TextValuePair> approvalNode;

    @JsonProperty(value = APPROVER_PERSON)
    @ApiModelProperty("审核人")
    private List<Employee> approvalPerson;

    @JsonProperty(value = APPROVER_GROUP)
    @ApiModelProperty("审核组")
    private List<Employee> approvalGroup;

    @JsonProperty(value = OPERATOR)
    @ApiModelProperty("运营商")
    private List<TextValueTypePair> operator;

    @JsonProperty(value = SALES)
    @ApiModelProperty("营销单位")
    private List<TextValuePair> salesDept;

    @JsonProperty(value = ORGANIZATION_REGION)
    @ApiModelProperty("片区")
    private List<TextValuePair> organizationRegion;

    @JsonProperty(value = RESPONSIBLE_DEPT)
    @ApiModelProperty("代表处")
    private List<TextValuePair> responsibleDept;

    @JsonProperty(value = PROD_OPERATION_TEAM)
    @ApiModelProperty("产品经营团队")
    private List<TextValuePair> prodTeam;

    @JsonProperty(value = PRODUCT_LINE)
    @ApiModelProperty("产品线")
    private List<TextValuePair> prodLine;

    @JsonProperty(value = PROD_MAIN_CATEGORY)
    @ApiModelProperty("产品大类")
    private List<TextValuePair> prodMainCategory;

    @JsonProperty(value = PRODUCT_SUB_CATEGORY)
    @ApiModelProperty("产品小类")
    private List<TextValuePair> prodSubCategory;

    @JsonProperty(value = PRODUCT_MODEL_ID)
    @ApiModelProperty("产品型号")
    private List<TextValuePair> productModelId;

    @JsonProperty(value = OPERATE_TYPE_MULTI)
    @ApiModelProperty("操作类型")
    private List<String> operationTypeMulti;

    @JsonProperty(value = ROLE)
    @ApiModelProperty("角色")
    private List<TextValuePair> role;

    @JsonProperty(value = IS_GOV)
    @ApiModelProperty("是否政企")
    private List<TextValueTypePair> isGovEnt;

    @JsonProperty(value = LOGICAL_NE)
    @ApiModelProperty("逻辑网元")
    private List<TextValueTypePair> selectfieldLogicalNe;

    @JsonProperty(value = REDUNDAN)
    @ApiModelProperty("冗余选择下拉处理-无业务逻辑字段")
    private List<TextValuePair> redundan;

    @JsonProperty(value = ENTITY_ID)
    @ApiModelProperty("实体ID")
    private String entityId;

    @Getter
    @Setter
    @NoArgsConstructor
    public static class TextValueTypePair {
        private MultiLangTextType text;
        private String value;

        public TextValueTypePair(MultiLangTextType text, String value) {
            this.text = text;
            this.value = value;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MultiLangTextType {

        @JsonProperty(value = "zh_CN")
        private String zhCN;

        @JsonProperty(value = "en_US")
        private String enUS;

        private String type;
    }

    public static List<TextValueTypePair> buildList(String value, String zhCn, String enUs, String type) {
        return new ArrayList<>(Collections.singletonList(new TextValueTypePair(
                new MultiLangTextType(zhCn, enUs, type),
                value
        )));
    }
}
