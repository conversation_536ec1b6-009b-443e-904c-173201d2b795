package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.StandardSchemeFieldConsts.*;

@ApiModel("标准方案")
@Setter
@Getter
@BaseEntity.Info("standard_scheme_copy")
public class StandardScheme extends BaseEntity {

    @JsonProperty(value = SCHEME_FILE)
    @ApiModelProperty("方案附件")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile schemeFile;

    @JsonProperty(value = VERSION_NO)
    @ApiModelProperty("版本号")
    private String versionNo;

    @JsonProperty(value = SCHEME_NAME)
    @ApiModelProperty("方案名称")
    private String schemeName;

    @JsonProperty(value = FILE_SIZE)
    @ApiModelProperty("文件大小")
    private BigDecimal fileSize;

    @JsonProperty(value = OPERATE_TYPE)
    @ApiModelProperty("操作类型")
    private List<TextValuePair> operateType;

    @JsonProperty(value = PROD_TYPE)
    @ApiModelProperty("产品类型")
    private List<TextValuePair> productType;
}
