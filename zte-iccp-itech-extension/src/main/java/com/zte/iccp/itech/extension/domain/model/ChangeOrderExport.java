package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.InfraInfoPkIdDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.LookupValueDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonMultiLangTextDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.model.base.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.OVDC_NFVI_PROPERTY_KEY;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.OFFICE_SOLUTION_REVIEWER;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.*;

/**
 * <AUTHOR>
 * @date 2025/4/5
 */
@Getter
@Setter
@BaseEntity.Info("oc_apply")
public class ChangeOrderExport extends BaseEntity {

    // ====================== 网络变更操作申请 =======================//
    @JsonProperty(value = ORDER_NO)
    @ApiModelProperty("任务单号")
    private String orderNo;

    @JsonProperty(value = DELIVERY_MODE)
    @ApiModelProperty("交付方式")
    private List<TextValuePair> deliveryMode;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = IS_GDPR)
    @ApiModelProperty("属于GDPR管控项目")
    private BoolEnum isGdpr;

    @JsonProperty(value = NETWORK_ELEMENT_COUNT)
    @ApiModelProperty("网元数量")
    private Integer neCount;

    @JsonProperty(value = OPERATION_ENCAPSULATION)
    @ApiModelProperty("操作封装")
    private List<TextValuePair> operationEncapsulation;

    @JsonProperty(value = OPERATION_START_TIME)
    @ApiModelProperty("计划操作开始时间")
    private Date operationStartTime;

    @JsonProperty(value = OPERATION_END_TIME)
    @ApiModelProperty("计划操作结束时间")
    private Date operationEndTime;

    @JsonProperty(value = IS_COMMERCIAL_OFFICE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("商用局")
    private BoolEnum isCommercialOffice;

    @JsonProperty(value = IS_COMMERCIAL_CHARGE_CONTRACT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("有商务收费合同")
    private BoolEnum isCommercialChargeContract;

    @JsonProperty(value = CSC_NO)
    @ApiModelProperty("CSC请求单号")
    private String cscNo;

    @JsonProperty(value = IS_TECHNICAL_NOTICE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否是技术通知单实施")
    private BoolEnum isTechnicalNotice;

    @JsonProperty(value = IS_TASK_STATEMENT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否有任务书")
    private BoolEnum isTaskStatement;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = TASK_STATEMENT_OVER_MONTH)
    @ApiModelProperty("任务书是否超过一个月")
    private BoolEnum taskStatementOverMonth;

    @JsonProperty(value = IS_MULTI_MODE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否多模")
    private BoolEnum isMultiMode;

    @JsonProperty(value = OPERATION_DESC)
    @ApiModelProperty("操作说明")
    private String operationDesc;

    @JsonProperty(value = PROVINCE)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    @ApiModelProperty("省/州")
    private MultiLangText province;

    @JsonProperty(value = CITY)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    @ApiModelProperty("地市")
    private MultiLangText area;

    @JsonProperty(value = CUSTOMER_ID)
    @ApiModelProperty("客户Id")
    private String customerId;

    @JsonProperty(value = ACCN_TYPE)
    @ApiModelProperty("客户标识")
    private String accnType;

    @JsonProperty(value = OFFICE_SOLUTION_REVIEWER)
    @ApiModelProperty("代表处方案审核人")
    private List<SingleEmployee> officeSolutionReviewer;

    @JsonProperty(value = ATTACHMENT_DESC)
    @ApiModelProperty("附件说明")
    private String attachmentDesc;

    @JsonProperty(value = IS_UPGRADE_TECHNOLOGY)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("需要升级至技术交付部/网络处")
    private BoolEnum isUpgradeTechnology;

    @JsonProperty(value = UPGRADE_TECHNOLOGY_REQUIRE)
    @ApiModelProperty("对技术交付部/网络处要求")
    private List<TextValuePair> upgradeTechnologyRequire;

    @ApiModelProperty("申请人")
    private String applyBy;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @JsonProperty(value = IS_EMERGENCY_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否紧急操作")
    private BoolEnum isEmergencyOperation;

    @JsonProperty(value = IS_NET_CLOSE_OR_CONTROL_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否封网、管控期操作")
    private BoolEnum isNetCloseOrControlOperation;

    @JsonProperty(value = IS_REGIONAL_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否大区操作")
    private BoolEnum isRegionalOperation;

    @JsonProperty(value = MULTI_PROD_GUARANTEE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("多产品联动保障")
    private BoolEnum isProductLinkageGuarantee;

    @JsonProperty(value = TIME_ZONE)
    @JsonDeserialize(using = LookupValueDeserializer.class)
    @ApiModelProperty("时区")
    private TimeZoneEnum timeZone;

    @JsonProperty(value = OPERATION_SCENARIO)
    @JsonDeserialize(using = InfraInfoPkIdDeserializer.class)
    @ApiModelProperty("操作场景")
    private String operationScenario;

    @JsonProperty(value = IS_CUSTOMER_SCAN_PERMISSION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("获得客户扫描许可")
    private BoolEnum isCustomerScanPermission;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = IS_ASSOCIATE_WIRELESS_UPGRADE)
    @ApiModelProperty("关联无线产品升级单")
    private BoolEnum isAssociateWirelessUpgrade;

    @JsonProperty(value = CidConstants.FIELD_WIRELESS_UPGRADE_TICKET_CID)
    @ApiModelProperty("无线产品升级单")
    private String wirelessUpgradeTicket;

    @JsonProperty(value = NOT_ASSOCIATE_REASON)
    @ApiModelProperty("不关联原因")
    private String notAssociateReason;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = IS_UPGRADING_OPTICAL)
    @ApiModelProperty("升级光模块")
    private BoolEnum isUpgradingOptical;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = IS_THIRD_PARTY_TERMINAL)
    @ApiModelProperty("有第三方终端")
    private BoolEnum isThirdPartyTerminal;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = IS_AFFECT_TO_B)
    @ApiModelProperty("影响ToB业务")
    private BoolEnum isAffectToB;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = OVDC_NFVI_PROPERTY_KEY)
    @ApiModelProperty("oVDC/NDVI项目")
    private BoolEnum radiofieldOvdcNfviRdVerify;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = IS_ADMINISTRATION_LEADER_APPROVAL)
    @ApiModelProperty("行政领导审核")
    private BoolEnum isAdministrationLeaderApproval;

    // ====================== 网络责任人审核 =======================//
    @JsonProperty(value = SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.APPROVE_RESULT)
    @ApiModelProperty("审核结果")
    private List<TextValuePair> approveResultNetOwner;

    @JsonProperty(value = SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.APPROVE_OPINION)
    @ApiModelProperty("审核意见")
    private String reviewCommentNetOwner;

    @JsonProperty(value = SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.IS_REVIEW_OFFICE_PROD_MANAGER)
    @ApiModelProperty("是否需要办事处产品经理审核")
    private BoolEnum isReviewOfficeProdManager;

    // ====================== 办事处产品经理审核 =======================//
    @JsonProperty(value = SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.APPROVE_RESULT)
    @ApiModelProperty("审核结果")
    private List<TextValuePair> approveResultOfficeProdManager;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.APPROVE_OPINION)
    private String reviewCommentOfficeProdManager;

    @ApiModelProperty("需要升级至网络处")
    @JsonProperty(value = SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.IS_UPGRADE_NET_DEPARTMENT)
    private BoolEnum isUpgradeNetDept;

    // ====================== 核心网大区TD审核 =======================//
    @ApiModelProperty("审核意见")
    @JsonProperty(value = ReginalTdConfirmConsts.APPROVE_OPINION)
    private String approveOpinionRegionalTdConfirm;

    // ====================== 代表处产品TD审核 =======================//
    @JsonProperty(value = RepProdTdApproveConsts.APPROVE_RESULT)
    @ApiModelProperty("审核结果")
    private List<TextValuePair> approveResultTdApp;

    @JsonProperty(value = RepProdTdApproveConsts.APPROVE_OPINION)
    @ApiModelProperty("审核意见")
    private String approveOpinionTdApp;

    // ====================== 技术交付部/网络处审核 =======================//
    @JsonProperty(value = TdNetDeptApproveConsts.APPROVE_RESULT)
    @ApiModelProperty("审核结果")
    private List<TextValuePair> approveResultTdNetDeptApp;

    @JsonProperty(value = TdNetDeptApproveConsts.APPROVE_OPINION)
    @ApiModelProperty("审核意见")
    private String approveOpinionTdNetDeptApp;

    @JsonProperty(value = TdNetDeptApproveConsts.IS_TD_NET_OFFICE_LEADER_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("需要领导审核")
    private BoolEnum isTdNetOfficeLeaderApproval;

    @JsonProperty(value = TdNetDeptApproveConsts.TD_NET_OFFICE_LEADER_APPROVAL_TEAM)
    @ApiModelProperty("技术交付部/网络处领导")
    private List<SingleEmployee> tdNetOfficeLeaderApprovalTeam;

    @JsonProperty(value = TdNetDeptApproveConsts.IS_NET_DEPT_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("需要升级至网络服务部")
    private BoolEnum isNetDeptApproval;

    @JsonProperty(value = TdNetDeptApproveConsts.REQ_FOR_NET_SERVICE_DEPT)
    @ApiModelProperty("对网络服务部的要求")
    private List<TextValuePair> reqForNetServiceDept;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = TdNetDeptApproveConsts.IS_SERV_PROD_SUPPORT_DEPT_APPROVAL)
    @ApiModelProperty("需要升级到服务产品支持部（IT外购件支持）")
    private BoolEnum isServProdSupportDeptApproval;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = TdNetDeptApproveConsts.IS_SSP_SUPPORT_TEAM_APPROVAL)
    @ApiModelProperty("需要升级到SSP支持组")
    private BoolEnum isSspSupportTeamApproval;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = TdNetDeptApproveConsts.IS_CAPACITY_PERFORMANCE_EVALUATION)
    @ApiModelProperty("需要容量性能评估")
    private BoolEnum isCapacityPerformanceEvaluation;

    // ====================== 网络服务部审核 =======================//
    @JsonProperty(value = NetServiceDeptAppConsts.APPROVE_RESULT)
    @ApiModelProperty("审核结果")
    private List<TextValuePair> approveResultNetServiceDeptApp;

    @JsonProperty(value = NetServiceDeptAppConsts.APPROVE_OPINION_NET_SERVICE_DEPT)
    @ApiModelProperty("审核意见")
    private String approveOpinionNetServiceDept;

    @JsonProperty(value = NetServiceDeptAppConsts.IS_DEV_DEPT_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("需要升级至研发")
    private BoolEnum isDevDeptApproval;

    @JsonProperty(value = NetServiceDeptAppConsts.RD_REQ)
    @ApiModelProperty("对研发的要求")
    private List<TextValuePair> rdReq;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = NetServiceDeptAppConsts.IS_EARLY_SITE)
    @ApiModelProperty("早期局")
    private BoolEnum isEarlySite;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = NetServiceDeptAppConsts.IS_HIGH_RISK_VERSION)
    @ApiModelProperty("高风险版本")
    private BoolEnum isHighRiskVersion;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = NetServiceDeptAppConsts.IS_MAJOR_VERSION)
    @ApiModelProperty("重大版本")
    private BoolEnum isMajorVersion;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = NetServiceDeptAppFieldConsts.IS_DEDICATED_BEAR_GUARANTEE)
    @ApiModelProperty("承载专项保障")
    private BoolEnum isDedicatedBearerGuarantee;

    @JsonProperty(value = NetServiceDeptAppConsts.SOLUTION_REVIEW_INPUT_MANDAY)
    @ApiModelProperty("方案审核投入人天")
    private String solutionReviewInputManday;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = NetServiceDeptAppConsts.IS_PROJECT_LINKED_CENTER_MANAG_CADRE)
    @ApiModelProperty("中心管理干部挂钩项目")
    private BoolEnum isProjectLinkedCenterManagCadre;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = CidConstants.FIELD_IS_SSP_SUPPORT_TEAM_NET_CID)
    @ApiModelProperty("需要升级至SSP支持组")
    private BoolEnum isSspSupportTeamNet;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = IS_OPER_PLAN_FROM_TEST_DEPT)
    @ApiModelProperty("操作方案由测试部提供")
    private BoolEnum isOperPlanFromTestDept;

    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonProperty(value = IS_OPER_PLAN_NEED_TEST_VERIFY)
    @ApiModelProperty("操作方案需要验证测试")
    private BoolEnum isOperPlanNeedTestVerify;

    // ====================== 研发经理审核 =======================//
    @JsonProperty(value = RDManagerConsts.APPROVE_RESULT)
    @ApiModelProperty("审核结果")
    private List<TextValuePair> approveResultRdManager;

    @JsonProperty(value = RDManagerConsts.APPROVE_OPINION)
    @ApiModelProperty("审核意见")
    private String approveOpinionRdManager;

    @JsonProperty(value = RDManagerConsts.OPER_TEST_VERIFY_RD_MANAGER)
    @ApiModelProperty("操作验证测试")
    private String operTestVerifyRdManager;

    @JsonProperty(value = RDManagerConsts.JOINT_VERIFY_TEST_RD_MANAGER)
    @ApiModelProperty("联合测试验证")
    private String jointVerifyTestRdManager;

    @JsonProperty(value = RDManagerConsts.IS_DEV_LEADER_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("需要研发领导审核")
    private BoolEnum isDevLeaderApproval;

    @JsonProperty(value = RDManagerConsts.CAP_PERF_EVAL_RESULT_RD_MANAGER)
    @ApiModelProperty("需要容量性能评估")
    private String capPerfEvalResultRdManager;

    @JsonProperty(value = RDManagerConsts.TEST_VERIFY_CONCLUSION_RD_MANAGER)
    @ApiModelProperty("测试验证结论")
    private List<TextValuePair> testVerifyConclusionRdManager;

    // ====================== 研发领导审核 =======================//
    @JsonProperty(value = RdLeaderAppConsts.APPROVE_OPINION)
    @ApiModelProperty("审核意见")
    private String approveOpinionRdLeader;

    @JsonProperty(value = RdLeaderAppConsts.APPROVE_RESULT)
    @ApiModelProperty("审核结果")
    private List<TextValuePair> approveResultRdLeader;

    // ====================== （补充给批次字段） =======================//
    @JsonProperty(value = OPERATION_REASON)
    @ApiModelProperty("操作原因")
    private List<TextValuePair> operationReason;

    @JsonProperty(value = OPERATION_LEVEL)
    @ApiModelProperty("操作等级")
    private List<TextValuePair> operationLevel;

    @JsonProperty(value = COUNTRY)
    @ApiModelProperty("国家/地区")
    private List<MultiLangText> country;

    @ApiModelProperty("重要程度")
    @JsonProperty(value = AssignmentFieldConsts.NetworkChangeFieldConsts.IMPORTANCE)
    private List<TextValuePair> importance;

    @ApiModelProperty("风险评估")
    @JsonProperty(value = AssignmentFieldConsts.NetworkChangeFieldConsts.RISK_EVALUATION)
    private List<TextValuePair> riskEvaluation;

    @JsonProperty(value = GUARANTEE_MODE)
    @ApiModelProperty("保障方式")
    private List<TextValuePair> guaranteeMode;

    @JsonProperty(value = IS_GUARANTEE_SOLUTION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否需提供详细保障方案")
    private BoolEnum isGuaranteeSolution;

    @JsonProperty(value = SERVICE_DISCONNECT_DURATION)
    @ApiModelProperty("预计业务中断时长（分钟）")
    private String serviceDisconnectDuration;

    @JsonProperty(value = TRIGGER_TYPE)
    @ApiModelProperty("触发类型")
    private List<TextValuePair> triggerType;

    @JsonProperty(value = IS_BUSINESS_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否带业务操作")
    private BoolEnum isBusinessOperation;

    @JsonProperty(value = IS_SPECIAL_SCENARIO)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否特殊场景")
    private BoolEnum isSpecialScenario;

    @JsonProperty(value = CUSTOMER_SPECIAL_SERVICE)
    @ApiModelProperty("客户特殊业务")
    private List<TextValuePair> customerSpecialService;

    @JsonProperty(value = IS_GOV_ENT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否政企")
    private BoolEnum isGovernmentEnterprise;

    @JsonProperty(value = CHANGE_OPERATION_SOURCE)
    @ApiModelProperty("变更操作来源")
    private List<TextValuePair> changeOperationSource;

    @JsonProperty(value = LICENSE_LOAD)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否涉及license文件加载")
    private BoolEnum licenseLoad;

    @JsonProperty(value = TOOL_USE)
    @ApiModelProperty("工具落地状态")
    private List<TextValuePair> toolUse;

    @JsonProperty(value = TOOL_NAME_SELECTED)
    @ApiModelProperty("工具名称")
    private List<TextValuePair> toolName;

    @JsonProperty(value = NOT_USE_TOOL_REASON)
    @ApiModelProperty("未使用工具原因")
    private List<TextValuePair> notUseToolReason;

    @JsonProperty(value = IS_FIRST_APPLICATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否首次应用")
    private BoolEnum isFirstApplication;

    @JsonProperty(value = IS_NEED_AUTHORIZATION_FILE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("需要网管授权文件")
    private BoolEnum isAuthorizationFile;

    @JsonProperty(value = IS_REMOTE_CENTER_SUPPORT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否远程中心执行")
    private BoolEnum isRemoteCenterSupport;

    @JsonProperty(value = IS_NETWORK_CHANGE_OPER_MATURE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("网络变更操作是否成熟")
    private BoolEnum isNetworkChangeOperMature;

    @JsonProperty(value = NetServiceDeptAppFieldConsts.IS_COMMERCIAL_USE_FIRST_TIME)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("此版本是否首次商用")
    private BoolEnum isCommercialUseFirstTime;

    @JsonProperty(value = NetServiceDeptAppFieldConsts.IS_FIRST_APPLICATION_NET)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("首次应用")
    private BoolEnum isFirstApplicationNet;

}
