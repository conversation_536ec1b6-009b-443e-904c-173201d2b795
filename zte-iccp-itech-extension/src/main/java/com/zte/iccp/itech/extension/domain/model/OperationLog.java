package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.OperationLogTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperationLogFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/08/06
 */
@Getter
@Setter
@BaseEntity.Info("operation_log")
public class OperationLog extends BaseEntity {
    @JsonProperty(value = TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private OperationLogTypeEnum type;

    @JsonProperty(value = TARGET_ID)
    private String targetId;

    @JsonProperty(value = DETAILS)
    private String details;
}