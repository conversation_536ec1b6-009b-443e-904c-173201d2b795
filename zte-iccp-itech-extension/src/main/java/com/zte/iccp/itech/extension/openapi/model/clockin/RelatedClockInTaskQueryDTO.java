/*
 * 版权所有：版权所有(C) 2020，中兴通讯
 * 系统名称：IEPMS
 */
package com.zte.iccp.itech.extension.openapi.model.clockin;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * app查询 与我相关 打卡任务列表查询条件
 * <AUTHOR> 10344315
 * @date 2025-03-17 上午11:30
 **/
@Getter
@Setter
public class RelatedClockInTaskQueryDTO{
    /** 产品分类列表 */
    private List<String> productList;

    /** 打卡状态列表 */
    private List<String> checkInStatusList;

    /** 操作开始时间 */
    private String operationStartTime;

    /** 操作结束时间 */
    private String operationEndTime;

    /** 操作等级 */
    private String operationLevel;

    /** 代表处列表 */
    private List<SelectedOffice> representativeOfficeList;

    /** Nis网络ID列表 */
    private List<String> customerNetworkIdList;

    /** 操作主题 */
    private String operationSubject;

    /** 操作负责人工号 */
    private List<String> personInChargeOfOperationList;

    /** 批次任务id列表 */
    private List<String> batchTaskIdList;

    /** 打卡人工号 */
    private String clockInPerson;

    /** 页码 */
    private int pageNum;

    /** 页容 */
    private int pageSize;
}
