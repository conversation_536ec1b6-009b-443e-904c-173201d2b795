package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.OPERATOR_ROLE_ERROR;

/**
 * 触发时机：变更单中操作人员列表中的角色和员工字段数据变化时
 * 插件功能：操作人员列表数据检查：1、操作负责人角色只能有一行
 *
 * <AUTHOR> 10335201
 * @date 2024-06-05 上午10:52
 **/
public class PlanOperatorCheckPluginValue implements ValueChangeBaseFormPlugin {

    /**
     * 1、操作负责人角色只能有一行
     */
    public void checkOperatorAndRole(IDataModel dataModel, IFormView formView) {
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(CidConstants.FIELD_PARTNER_OPERATION_PERSON_LIST_CID);
        Map<String, String> batchPersonMap = Maps.newHashMap();
        for (int i = CommonConstants.INTEGER_ZERO; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            Object operatorNameObj = dynamicDataEntity.get(CidConstants.OPERATOR_NAME_KEY);
            Object batchNameObj = dynamicDataEntity.get(CidConstants.FIELD_OPERATOR_BATCH_NO_KEY);
            if (ObjectUtils.isEmpty(operatorNameObj) || ObjectUtils.isEmpty(batchNameObj)) {
                continue;
            }
            List<Employee> operatorNameList = JsonUtils.parseArray(operatorNameObj, Employee.class);
            String empUiId = operatorNameList.get(CommonConstants.INTEGER_ZERO).getEmpUIID();
            String batch = TextValuePairHelper.getValue(JsonUtils.parseArray(batchNameObj, TextValuePair.class));
            // 已经存在数据  错误提示
            if (StringUtils.hasText(batchPersonMap.get(batch))) {
                formView.showMessage(OPERATOR_ROLE_ERROR, MsgType.ERROR);
                dataModel.setValue(CidConstants.OPERATOR_NAME_KEY, null, i);
                return;
            }
            batchPersonMap.put(batch, empUiId);
        }
    }
}
