package com.zte.iccp.itech.extension.domain.enums.configuration;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum EmailCcTypeEnum {

    /** 仅通告及操作结果抄送 */
    NOTIFICATION_AND_FEEDBACK("1"),

    /** 仅操作反馈失败时抄送 */
    OPERATION_FAILED("2"),

    /** 审核为高风险时抄送 */
    HIGH_RISK("3"),

    /** 所有处理过程均抄送 */
    ALL_PROCESSES("4"),

    /** 有线产品专项保障抄送 */
    WIRED_SPECIAL_GUARANTEE("5"),

    /** 客户特殊业务默认抄送 */
    SPECIAL_SERVICES("6"),

    /** ToB业务默认抄送 */
    TO_B_SERVICE("7");

    /** 编码 */
    private final String code;
}
