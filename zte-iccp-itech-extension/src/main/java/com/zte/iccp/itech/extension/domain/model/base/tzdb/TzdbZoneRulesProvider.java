package com.zte.iccp.itech.extension.domain.model.base.tzdb;

import com.zte.iccp.itech.extension.common.utils.CommonUtils;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.zone.*;
import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;

import static com.zte.iccp.itech.extension.domain.model.base.tzdb.TzdbConstant.*;

/**
 * 该方法为 tzdb 读取时区文件源码，这里仅提供 时区读取解析 + 系统注册 应用相关逻辑
 * 完整源码逻辑请参看 build.tools.tzdb.TzdbZoneRulesProvider
 */
@Slf4j
public class TzdbZoneRulesProvider extends ZoneRulesProvider {

    /** 时区ID */
    private List<String> regionIds = new ArrayList<>(600);

    /** 时区规则 */
    private final Map<String, Object> zones = new ConcurrentSkipListMap<>();

    /** 兼容性时区 */
    private static Set<String> excludedZones;
    static {
        // (1) exclude EST, HST and MST. They are supported
        //     via the short-id mapping
        // (2) remove UTC and GMT
        // (3) remove ROC, which is not supported in j.u.tz
        excludedZones = new TreeSet<>();
        excludedZones.add("EST");
        excludedZones.add("HST");
        excludedZones.add("MST");
        excludedZones.add("GMT+0");
        excludedZones.add("GMT-0");
        excludedZones.add("ROC");
    }

    private Map<String, String> links = new TreeMap<>();

    private Map<String, List<RuleLine>> rules = new TreeMap<>();

    /** 自定义时区规则前缀 */
    private static final String ZONE_PREFIX = "customize/";

    /** 时区文件地址 */
    private static final List<String> ZONE_FILES;
    static {
        ZONE_FILES = Lists.newArrayList(
                CommonUtils.getTemplateString("tzdbzone/africa.txt"),
                CommonUtils.getTemplateString("tzdbzone/antarctica.txt"),
                CommonUtils.getTemplateString("tzdbzone/asia.txt"),
                CommonUtils.getTemplateString("tzdbzone/australasia.txt"),
                CommonUtils.getTemplateString("tzdbzone/backward.txt"),
                CommonUtils.getTemplateString("tzdbzone/etcetera.txt"),
                CommonUtils.getTemplateString("tzdbzone/europe.txt"),
                CommonUtils.getTemplateString("tzdbzone/northamerica.txt"),
                CommonUtils.getTemplateString("tzdbzone/southamerica.txt"));
    }

    public TzdbZoneRulesProvider() {
        try {
            ZONE_FILES.forEach(this::load);
        } catch (Exception ex) {
            log.error("Unable to load TZDB time-zone rules: " + ex.getMessage(), ex);
        }
    }

    @Override
    protected Set<String> provideZoneIds() {
        return new TreeSet<>(regionIds);
    }

    @Override
    protected ZoneRules provideRules(String zoneId, boolean flag) {
        Object object = zones.get(zoneId);
        if (object == null) {
            zoneId = links.get(zoneId);
            object = zones.get(zoneId);

            // 部分时区会通过 backward 文件关联到其它时区上
            // 下面兼容该逻辑
            if (object == null) {
                object = zones.get(ZONE_PREFIX + zoneId);
            }
        }

        if (object instanceof ZoneRules) {
            return (ZoneRules) object;
        }

        try {
            @SuppressWarnings("unchecked")
            ZoneRules zoneRules = buildRules((List<ZoneLine>) object);
            zones.put(zoneId, zoneRules);
            return zoneRules;
        } catch (Exception ex) {
            throw new ZoneRulesException(
                    "Invalid binary time-zone data: TZDB:" + zoneId, ex);
        }
    }

    @Override
    protected NavigableMap<String, ZoneRules> provideVersions(String s) {
        return null;
    }

    /**
     * 时区信息加载
     */
    @SneakyThrows
    private void load(String fileText) {
        List<ZoneLine> openZone = null;

        String[] lines = fileText.split("\n");
        for (String line : lines) {
            if (StringUtils.isBlank(line) || line.charAt(0) == CHAR_WELL_NUMBER) {
                continue;
            }
            String[] tokens = split(line);

            // continuing zone line
            if (openZone != null && Character.isWhitespace(line.charAt(0)) && tokens.length > 0) {
                ZoneLine zoneLine = new ZoneLine();
                openZone.add(zoneLine);
                if (zoneLine.parseZoneLine(tokens, 0)) {
                    openZone = null;
                }
                continue;
            }

            int token0len = tokens.length > 0 ? tokens[0].length() : line.length();
            // parse Zone line
            openZone = parseZoneLine(line, tokens, token0len, openZone);

            // parse Rule line
            parseRuleLine(line, tokens, token0len);

            // parse link line
            parseLinkLine(line, tokens, token0len);
        }
    }

    /**
     * 文件行解析 - 时区
     */
    private List<ZoneLine> parseZoneLine(
            String line,
            String[] tokens,
            int token0len,
            List<ZoneLine> openZone) {

        if (!line.regionMatches(true, 0, ZONE, 0, token0len)) {
            return openZone;
        }

        String name = tokens[1];
        openZone = new ArrayList<>(10);

        String systemName = ZONE_PREFIX + name;
        zones.put(systemName, openZone);
        regionIds.add(systemName);
        ZoneLine zLine = new ZoneLine();
        openZone.add(zLine);
        if (zLine.parseZoneLine(tokens, 2)) {
            return null;
        }

        return openZone;
    }

    /**
     * 文件行解析 - 时区规则
     */
    private void parseRuleLine(String line, String[] tokens, int token0len) {
        if (!line.regionMatches(true, 0, RULE, 0, token0len)) {
            return;
        }

        String name = tokens[1];
        if (!rules.containsKey(name)) {
            rules.put(name, new ArrayList<>(10));
        }
        rules.get(name).add(new RuleLine().parse(tokens));
    }

    /**
     * 文件行解析 - 时区链接
     */
    private void parseLinkLine(String line, String[] tokens, int token0len) {
        if (!line.regionMatches(true, 0, LINK, 0, token0len)
                || tokens.length < 3) {
            return;
        }

        String realId = tokens[1];
        String aliasId = ZONE_PREFIX + tokens[2];
        links.put(aliasId, realId);
        regionIds.add(aliasId);
    }

    private String[] split(String str) {
        int off = 0;
        int end = str.length();
        ArrayList<String> list = new ArrayList<>(10);
        while (off < end) {
            char c = str.charAt(off);
            if (c == '\t' || c == ' ') {
                off++;
                continue;
            }

            // comment
            if (c == CHAR_WELL_NUMBER) {
                break;
            }

            // split
            off = getSplitResult(off, end, str, list);
        }
        return list.stream()
                .map(String::trim)
                .toArray(String[]::new);
    }


    private int getSplitResult(int off, int end, String str, List<String> splitResult) {

        int start = off;
        while (off < end) {
            char c = str.charAt(off);
            if (c == ' ' || c == '\t') {
                break;
            }
            off++;
        }

        if (start != off) {
            splitResult.add(str.substring(start, off));
        }

        return off;
    }

    /**
     * Class representing a rule line in the TZDB file.
     */
    private static class RuleLine extends AbstractMonthDayTime {
        /** The start year. */
        int startYear;

        /** The end year. */
        int endYear;

        /** The amount of savings, in seconds. */
        int savingsAmount;

        /**
         * Converts this to a transition rule.
         */
        ZoneOffsetTransitionRule toTransitionRule(ZoneOffset stdOffset, int savingsBefore, int negativeSavings) {
            // rule shared by different zones, so don't change it
            Month month = this.month;
            int dayOfMonth = this.dayOfMonth;
            DayOfWeek dayOfWeek = this.dayOfWeek;
            boolean endOfDay = this.endOfDay;

            // optimize stored format
            if (dayOfMonth < 0) {
                if (month != Month.FEBRUARY) {
                    dayOfMonth = month.maxLength() - 6;
                }
            }
            if (endOfDay && dayOfMonth > 0
                    && !(dayOfMonth == 28 && month == Month.FEBRUARY)) {
                // leap-year
                LocalDate date = LocalDate.of(2004, month, dayOfMonth).plusDays(1);
                month = date.getMonth();
                dayOfMonth = date.getDayOfMonth();
                if (dayOfWeek != null) {
                    dayOfWeek = dayOfWeek.plus(1);
                }
                endOfDay = false;
            }

            // build rule
            return ZoneOffsetTransitionRule.of(
                    //month, dayOfMonth, dayOfWeek, time, endOfDay, timeDefinition,
                    month, dayOfMonth, dayOfWeek,
                    LocalTime.ofSecondOfDay(secsOfDay), endOfDay, timeDefinition,
                    stdOffset,
                    ZoneOffset.ofTotalSeconds(stdOffset.getTotalSeconds() + savingsBefore),
                    ZoneOffset.ofTotalSeconds(stdOffset.getTotalSeconds() + savingsAmount - negativeSavings));
        }

        RuleLine parse(String[] tokens) {
            startYear = parseYear(tokens[2], 0);
            endYear = parseYear(tokens[3], startYear);
            if (startYear > endYear) {
                throw new IllegalArgumentException(
                        "Invalid <Rule> line/Year order invalid:" + startYear + " > " + endYear);
            }
            // monthdaytime parsing
            super.parse(tokens, 5);
            savingsAmount = parseSecs(tokens[8]);
            return this;
        }
    }

    /**
     * Class representing a linked set of zone lines in the TZDB file.
     */
    private static class ZoneLine extends AbstractMonthDayTime {
        /** The standard offset. */
        int stdOffsetSecs;

        /** The fixed savings amount. */
        int fixedSavingsSecs = 0;

        /** The savings rule. */
        String savingsRule;

        /** The text name of the zone. */
        String text;

        /** The cutover year */
        int year = Year.MAX_VALUE;

        /** The cutover date time */
        LocalDateTime ldt;

        /** The cutover date/time in epoch seconds/UTC */
        long ldtSecs = Long.MIN_VALUE;

        LocalDateTime toDateTime() {
            if (ldt == null) {
                ldt = toDateTime(year);
            }
            return ldt;
        }

        /**
         * Creates the date-time epoch second in the wall offset for the local
         * date-time at the end of the window.
         *
         * @param savingsSecs  the amount of savings in use in seconds
         * @return the created date-time epoch second in the wall offset, not null
         */
        long toDateTimeEpochSecond(int savingsSecs) {
            if (ldtSecs == Long.MIN_VALUE) {
                ldtSecs = toDateTime().toEpochSecond(ZoneOffset.UTC);
            }
            switch(timeDefinition) {
                case UTC:      return ldtSecs;
                case STANDARD: return ldtSecs - stdOffsetSecs;
                default:       return ldtSecs - (stdOffsetSecs + savingsSecs);
            }
        }

        boolean parseZoneLine(String[] tokens, int off) {
            stdOffsetSecs = parseSecs(tokens[off++]);
            savingsRule = parseOptional(tokens[off++]);
            if (savingsRule != null && savingsRule.length() > 0 &&
                    (savingsRule.charAt(0) == '-' || isDigit(savingsRule.charAt(0)))) {
                try {
                    fixedSavingsSecs = parseSecs(savingsRule);
                    savingsRule = null;
                } catch (Exception ex) {
                    fixedSavingsSecs = 0;
                }
            }
            text = tokens[off++];
            if (off < tokens.length) {
                year = Integer.parseInt(tokens[off++]);
                if (off < tokens.length) {
                    super.parse(tokens, off);
                }
                return false;
            } else {
                return true;
            }
        }
    }

    /**
     * Class representing a rule line in the TZDB file for a particular year.
     */
    private static class TransRule implements Comparable<TransRule> {
        private int year;
        private final RuleLine rule;

        /** The trans date/time */
        private LocalDateTime ldt;

        /** The trans date/time in epoch seconds (assume UTC) */
        long ldtSecs;

        TransRule(int year, RuleLine rule) {
            this.year = year;
            this.rule = rule;
            this.ldt = rule.toDateTime(year);
            this.ldtSecs = ldt.toEpochSecond(ZoneOffset.UTC);
        }

        ZoneOffsetTransition toTransition(ZoneOffset standardOffset, int savingsBeforeSecs, int negativeSavings) {
            // copy of code in ZoneOffsetTransitionRule to avoid infinite loop
            ZoneOffset wallOffset = ZoneOffset.ofTotalSeconds(
                    standardOffset.getTotalSeconds() + savingsBeforeSecs);
            ZoneOffset offsetAfter = ZoneOffset.ofTotalSeconds(
                    standardOffset.getTotalSeconds() + rule.savingsAmount - negativeSavings);
            LocalDateTime dt = rule.timeDefinition
                    .createDateTime(ldt, standardOffset, wallOffset);
            return ZoneOffsetTransition.of(dt, wallOffset, offsetAfter);
        }

        long toEpochSecond(ZoneOffset stdOffset, int savingsBeforeSecs) {
            switch(rule.timeDefinition) {
                case UTC:      return ldtSecs;
                case STANDARD: return ldtSecs - stdOffset.getTotalSeconds();
                default:       return ldtSecs - (stdOffset.getTotalSeconds() + savingsBeforeSecs); // WALL
            }
        }

        /**
         * Tests if this a real transition with the active savings in seconds
         *
         * @param savingsBefore the active savings in seconds
         * @param negativeSavings minimum savings in the rule, usually zero, but negative if negative DST is
         *                   in effect.
         * @return true, if savings changes
         */
        boolean isTransition(int savingsBefore, int negativeSavings) {
            return rule.savingsAmount - negativeSavings != savingsBefore;
        }

        @Override
        public int compareTo(TransRule other) {
            return Long.compare(ldtSecs, other.ldtSecs);
        }
    }

    private ZoneRules buildRules(List<ZoneLine> zones) {
        final List<ZoneOffsetTransition> standardTransitions = new ArrayList<>(4);
        final List<ZoneOffsetTransition> transitions = new ArrayList<>(256);
        final List<ZoneOffsetTransitionRule> lastTransitionRules = new ArrayList<>(2);

        final ZoneLine zone0 = zones.get(0);

        // initialize the standard offset, wallOffset and savings for loop
        int savings = zone0.fixedSavingsSecs;
        ZoneOffset stdOffset = ZoneOffset.ofTotalSeconds(zone0.stdOffsetSecs);
        ZoneOffset wallOffset = ZoneOffset.ofTotalSeconds(stdOffset.getTotalSeconds() + savings);

        // start ldt of each zone window
        LocalDateTime zoneStart = LocalDateTime.MIN;

        // first standard offset
        ZoneOffset firstStdOffset = stdOffset;

        // first wall offset
        ZoneOffset firstWallOffset = wallOffset;

        for (ZoneLine zone : zones) {
            // Adjust stdOffset, if negative DST is observed. It should be either
            // fixed amount, or expressed in the named Rules.
            int negativeSavings = Math.min(zone.fixedSavingsSecs, findNegativeSavings(zoneStart, zone));
            fixAmount(zone, negativeSavings);

            // check if standard offset changed, update it if yes
            ZoneOffset stdOffsetPrev = stdOffset;

            // for effectiveSavings check
            stdOffset = addStandardTransitions(zone, zoneStart, stdOffset, wallOffset, standardTransitions);

            LocalDateTime zoneEnd = zone.year == Year.MAX_VALUE
                    ? LocalDateTime.MAX
                    : zone.toDateTime();
            // calculate effective savings at the start of the window
            List<TransRule> trules = null;
            List<TransRule> lastRules = null;

            int effectiveSavings = zone.fixedSavingsSecs;
            if (zone.savingsRule != null) {
                List<RuleLine> tzdbRules = rules.get(zone.savingsRule);
                trules = new ArrayList<>(256);
                lastRules = new ArrayList<>(2);

                // merge the rules to transitions
                int lastRulesStartYear = mergeRules2Transitions(tzdbRules, zoneEnd, trules, lastRules, zone);

                // last rules, fill the gap years between different last rules
                fillGapYears(lastRules, zoneStart, zoneEnd, trules, lastRulesStartYear);

                // sort the merged rules
                Collections.sort(trules);

                effectiveSavings = calculateEffectiveSavings(
                        trules, stdOffsetPrev, negativeSavings, savings, zoneStart, wallOffset);
            }
            // check if the start of the window represents a transition
            ZoneOffset effectiveWallOffset =
                    ZoneOffset.ofTotalSeconds(stdOffset.getTotalSeconds() + effectiveSavings);

            if (!wallOffset.equals(effectiveWallOffset)) {
                transitions.add(ZoneOffsetTransition.of(zoneStart,
                        wallOffset,
                        effectiveWallOffset));
            }

            // apply rules within the window
            savings = applyRulesWithinWindow(effectiveSavings, negativeSavings, zone, trules,
                    zoneStart, stdOffset, wallOffset, transitions);
            savings = addLastTransitionRule(lastRules, stdOffset, savings, negativeSavings, lastTransitionRules);

            // finally we can calculate the true end of the window, passing it to the next window
            wallOffset = ZoneOffset.ofTotalSeconds(stdOffset.getTotalSeconds() + savings);
            zoneStart = LocalDateTime.ofEpochSecond(zone.toDateTimeEpochSecond(savings),
                    0,
                    wallOffset);
        }
        return ZoneRules.of(firstStdOffset,
                firstWallOffset,
                standardTransitions,
                transitions,
                lastTransitionRules);
    }

    private int applyRulesWithinWindow(
            int effectiveSavings,
            int negativeSavings,
            ZoneLine zone,
            List<TransRule> trules,
            LocalDateTime zoneStart,
            ZoneOffset stdOffset,
            ZoneOffset wallOffset,
            List<ZoneOffsetTransition> transitions) {

        int savings = effectiveSavings;
        if (trules == null) {
            return savings;
        }

        long zoneStartEpochSecs = zoneStart.toEpochSecond(wallOffset);
        for (TransRule trule : trules) {
            if (!trule.isTransition(savings, negativeSavings)) {
                continue;
            }

            long epochSecs = trule.toEpochSecond(stdOffset, savings);
            if (epochSecs < zoneStartEpochSecs
                    || epochSecs >= zone.toDateTimeEpochSecond(savings)) {
                continue;
            }
            transitions.add(trule.toTransition(stdOffset, savings, negativeSavings));
            savings = trule.rule.savingsAmount - negativeSavings;
        }

        return savings;
    }

    private int calculateEffectiveSavings(
            List<TransRule> trules,
            ZoneOffset stdOffsetPrev,
            int negativeSavings,
            int savings,
            LocalDateTime zoneStart,
            ZoneOffset wallOffset) {

        int effectiveSavings = -negativeSavings;
        for (TransRule rule : trules) {
            if (rule.toEpochSecond(stdOffsetPrev, savings) > zoneStart.toEpochSecond(wallOffset)) {
                // previous savings amount found, which could be the
                // savings amount at the instant that the window starts
                // (hence isAfter)
                break;
            }
            effectiveSavings = rule.rule.savingsAmount - negativeSavings;
        }

        return effectiveSavings;
    }

    private void fixAmount(ZoneLine zone, int negativeSavings) {
        if (negativeSavings > 0) {
            return;
        }

        zone.stdOffsetSecs += negativeSavings;
        if (zone.fixedSavingsSecs < 0) {
            zone.fixedSavingsSecs = 0;
        }
    }

    private ZoneOffset addStandardTransitions(
            ZoneLine zone,
            LocalDateTime zoneStart,
            ZoneOffset stdOffset,
            ZoneOffset wallOffset,
            List<ZoneOffsetTransition> standardTransitions) {

        if (zone.stdOffsetSecs == stdOffset.getTotalSeconds()) {
            return stdOffset;
        }

        // for effectiveSavings check
        ZoneOffset stdOffsetNew = ZoneOffset.ofTotalSeconds(zone.stdOffsetSecs);
        standardTransitions.add(ZoneOffsetTransition.of(
                LocalDateTime.ofEpochSecond(zoneStart.toEpochSecond(wallOffset), 0, stdOffset),
                stdOffset,
                stdOffsetNew));
        return stdOffsetNew;
    }

    private int mergeRules2Transitions(
            List<RuleLine> tzdbRules,
            LocalDateTime zoneEnd,
            List<TransRule> trules,
            List<TransRule> lastRules,
            ZoneLine zone) {

        int lastRulesStartYear = Year.MIN_VALUE;
        for (RuleLine rule : tzdbRules) {
            if (rule.startYear > zoneEnd.getYear()) {
                // rules will not be used for this zone entry
                continue;
            }

            // irrelevant, treat as leap year
            rule.adjustToForwards(2004);
            int startYear = rule.startYear;
            int endYear = rule.endYear;

            if (zoneEnd.equals(LocalDateTime.MAX)) {
                if (endYear == Year.MAX_VALUE) {
                    endYear = startYear;
                    lastRules.add(new TransRule(endYear, rule));
                }
                lastRulesStartYear = Math.max(startYear, lastRulesStartYear);
            } else if (endYear == Year.MAX_VALUE) {
                endYear = zone.year;
            }

            int year = startYear;
            while (year <= endYear) {
                trules.add(new TransRule(year, rule));
                year++;
            }
        }

        return lastRulesStartYear;
    }

    private void fillGapYears(
            List<TransRule> lastRules,
            LocalDateTime zoneStart,
            LocalDateTime zoneEnd,
            List<TransRule> trules,
            int lastRulesStartYear) {

        if (!zoneEnd.equals(LocalDateTime.MAX)) {
            return;
        }

        int startYear = Math.max(lastRulesStartYear, zoneStart.getYear()) + 1;
        for (TransRule rule : lastRules) {
            if (rule.year > startYear) {
                continue;
            }

            int year = rule.year;
            while (year <= startYear) {
                trules.add(new TransRule(year, rule.rule));
                year++;
            }
            rule.year = startYear;
            rule.ldt = rule.rule.toDateTime(year);
            rule.ldtSecs = rule.ldt.toEpochSecond(ZoneOffset.UTC);
        }

        Collections.sort(lastRules);
    }

    private int addLastTransitionRule(
            List<TransRule> lastRules,
            ZoneOffset stdOffset,
            int savings,
            int negativeSavings,
            List<ZoneOffsetTransitionRule> lastTransitionRules) {

        if (lastRules == null) {
            return savings;
        }

        for (TransRule trule : lastRules) {
            lastTransitionRules.add(trule.rule.toTransitionRule(stdOffset, savings, negativeSavings));
            savings = trule.rule.savingsAmount - negativeSavings;
        }
        return savings;
    }

    /**
     * Find the minimum negative savings in named Rules for a Zone. Savings are only
     * looked at for the period of the subject Zone.
     *
     * @param zoneStart start LDT of the zone
     * @param zl ZoneLine to look at
     */
    private int findNegativeSavings(LocalDateTime zoneStart, ZoneLine zl) {
        int negativeSavings = 0;
        LocalDateTime zoneEnd = zl.toDateTime();

        if (zl.savingsRule != null) {
            List<RuleLine> rlines = rules.get(zl.savingsRule);
            if (rlines == null) {
                throw new IllegalArgumentException("<Rule> not found: " +
                        zl.savingsRule);
            }

            negativeSavings = Math.min(0, rlines.stream()
                    .filter(l -> windowOverlap(l, zoneStart.getYear(), zoneEnd.getYear()))
                    .map(l -> l.savingsAmount)
                    .min(Comparator.naturalOrder())
                    .orElse(0));
        }

        return negativeSavings;
    }

    private boolean windowOverlap(RuleLine ruleLine, int zoneStartYear, int zoneEndYear) {
        return zoneStartYear <= ruleLine.startYear && zoneEndYear >= ruleLine.startYear ||
                zoneStartYear <= ruleLine.endYear && zoneEndYear >= ruleLine.endYear;
    }
}