package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME;

/**
 * 更新确认操作开始时间
 */
public class UpdateConfirmStartTimePlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {

        String batchId = body.getBusinessId();
        String flowCode = body.getFlowCode();

        // 1.检索批次任务审批实体
        List<String> fields
                = Lists.newArrayList(CommonFieldConsts.ID, PLAN_OPERATION_START_TIME);
        IBatchTask batchTask = ApprovalConstants.BATCH_TASK_FLOW.equals(flowCode)
                ? BatchTaskAbility.get(batchId, fields)
                : BatchTaskAbility.getSub(batchId, fields);
        if (Objects.isNull(batchTask)) {
            return false;
        }
        Date operationStartTime = batchTask.getPlanOperationStartTime();

        // 2.检索任务中心批次任务实体
        NetworkChangeAssignment batchAssignment = AssignmentAbility.queryAssignment(
                batchId,
                Lists.newArrayList(CommonFieldConsts.ID, ChangeOrderFieldConsts.TIME_ZONE),
                NetworkChangeAssignment.class);
        if (Objects.isNull(batchAssignment)) {
            return false;
        }

        // 3.更新操作开始时间 + 操作开始时间（UTC+8）
        NetworkChangeAssignment updateAssignment = new NetworkChangeAssignment();
        updateAssignment.setId(batchAssignment.getId());
        updateAssignment.setPlanStartTime(operationStartTime);
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneEnum(batchAssignment.getTimeZone());
        updateAssignment.setOperationStartTimeUtc8(
                TimeZoneEnum.BEIJING == timeZoneEnum || null == timeZoneEnum
                ? operationStartTime
                : TimeZoneEnum.BEIJING.pollute(timeZoneEnum.fix(operationStartTime)));

        AssignmentAbility.update(updateAssignment);

        return false;
    }
}
