package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 更新流程的当前处理人和当前进展
 * <AUTHOR>
 * @since 2024/07/31
 */
public class UpdateCurrentInfoPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        // 1.获取当前处理人
        List<String> approverList = Lists.newArrayList();
        Object approvers = body.getVariables().get("approvers");
        if (approvers != null) {
            approverList.add((String) body.getVariables().get(approvers.toString()));
        }
        approverList = approverList.stream().distinct().collect(Collectors.toList());

        // 2.获取当前进展
        // 取不到需要特殊设置为空字符串，为 null 不会更新数据库表
        String nodeKey = (String) body.getVariables().get("nodeKey");
        if (!StringUtils.hasText(nodeKey)) {
            nodeKey = CommonConstants.EMPTY_STRING;
        }

        // 3.获取流程编码
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(body.getFlowCode());

        // 4.更新当前进展，处理人
        AssignmentAbility.updateProgressByType(nodeKey, approverList, approveFlowEnum, body.getBusinessId());
        return false;
    }
}
