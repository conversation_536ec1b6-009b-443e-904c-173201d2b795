package com.zte.iccp.itech.extension.plugin.flow.changeorder.subcontractor;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;

/**
 *  分包商批次任务，办事处PD是否审批通过
 *
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class SubcontractorPdApprovalPlugin extends BaseFlowOperationPlugin {

    @Override
    public  void beforeOperate(ExecuteEvent executeEvent) {
        // 操作结果
        String result = TextValuePairHelper.getValue(getModel().getValue(APPROVE_RESULT_OFFICE_PD));
        if(CommonConstants.PASS.equals(result) ){
            getModel().setValue(CURRENT_STATUS,AssignmentStatusEnum.NOTIFICATION_UNDER_REVIEW.getValue());
            getModel().setValue(APPROVAL_STATUS, BoolEnum.Y.getPropValue());
        }else {
            getModel().setValue(CURRENT_STATUS,AssignmentStatusEnum.PENDING_NOTIFICATION.getValue());
            getModel().setValue(APPROVAL_STATUS, BoolEnum.N.getPropValue());
        }
    }
}
