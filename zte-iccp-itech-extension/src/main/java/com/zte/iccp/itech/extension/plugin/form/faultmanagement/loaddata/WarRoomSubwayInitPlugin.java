package com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.domain.enums.subway.WarRoomSubwayEnum;
import com.zte.iccp.itech.extension.domain.model.FaultManagementDetails;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.ComponentCids;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.CscClient;
import com.zte.iccp.itech.extension.spi.client.WarRoomClient;
import com.zte.iccp.itech.extension.spi.model.csc.dto.CscDetailQuery;
import com.zte.iccp.itech.extension.spi.model.csc.vo.CscDetailInfoVo;
import com.zte.iccp.itech.extension.spi.model.warroom.subway.SubwayNodeInfo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.HeaderVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.ProcessRecordVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomNodeVo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;


/**
 * <AUTHOR> 10335201
 * @date 2024-08-13 下午2:52
 **/
public class WarRoomSubwayInitPlugin implements LoadDataBaseFormPlugin {

    @Override
    public void loadData(LoadDataEventArgs args, FaultManagementDetails faultManagementDetails) {
        IDataModel dataModel = args.getModel();
        IFormView formView = args.getFormView();
        String warRoomId = PropertyValueConvertUtil.getString(dataModel.getValue(FieldCids.FIELD_WARROOM_ID_CID));
        String cscId = PropertyValueConvertUtil.getString(dataModel.getValue(FieldCids.FIELD_TASK_CODE_CID));
        WarRoomNodeVo warRoomNodeVo = null;
        try {
            warRoomNodeVo = WarRoomClient.queryWarRoomNode(warRoomId);
            initWarRoomSubway(cscId, warRoomNodeVo, formView);
        } catch (Exception e) {
            initWarRoomSubway(cscId, warRoomNodeVo, formView);
        }
    }

    private void initWarRoomSubway(
            String cscId,
            WarRoomNodeVo warRoomNodeVo,
            IFormView formView) {

        String language = ContextHelper.getLangId();

        // 初始化默认的地铁图节点，全部不点亮，没有点亮时间
        List<SubwayNodeInfo> subwayNodeInfoList = new LinkedList<>();
        for(WarRoomSubwayEnum warRoomSubwayEnum : WarRoomSubwayEnum.values()){
            SubwayNodeInfo info = new SubwayNodeInfo();
            info.setNodeCode(warRoomSubwayEnum.getCode());
            info.setNodeName(warRoomSubwayEnum.getName(language));
            info.setNodeLightingStatus(false);
            subwayNodeInfoList.add(info);
        }

        // warRoom地铁图接口没有查到数据，返回默认的数据给自定义组件
        if(ObjectUtils.isEmpty(warRoomNodeVo)){
            formView.getClientViewProxy().setControlState(
                    ComponentCids.COMPONENT_WARROOM_SUBWAY_CID,
                    new BasicAttributeBuilder().attribute(SUBWAY_NODE_DATA_ATTRIBUTE_NAME, JSONArray.parseArray(JSON.toJSONString(
                            subwayNodeInfoList))).build());
            return;
        }

        // warRoom地铁图接口查到数据，封装数据后返回给自定义组件
        initWarRoomSubwayData(cscId, warRoomNodeVo, subwayNodeInfoList);
        formView.getClientViewProxy().setControlState(
                ComponentCids.COMPONENT_WARROOM_SUBWAY_CID,
                new BasicAttributeBuilder().attribute(SUBWAY_NODE_DATA_ATTRIBUTE_NAME, JSONArray.parseArray(JSON.toJSONString(
                        subwayNodeInfoList))).build());
    }

    private static void initWarRoomSubwayData(
            String cscId,
            WarRoomNodeVo warRoomNodeVo,
            List<SubwayNodeInfo> subwayNodeInfoList) {

        CscDetailQuery cscDetailQuery = new CscDetailQuery();
        cscDetailQuery.setRequestNo(cscId);
        CscDetailInfoVo cscDetailInfoVo =  CscClient.getCscDetail(cscDetailQuery);

        // 故障发起（从CSC单据详情获取）
        if(!ObjectUtils.isEmpty(cscDetailInfoVo) && !ObjectUtils.isEmpty(cscDetailInfoVo.getBasicInfo().getFaultInfo())){
            subwayNodeInfoList.get(INTEGER_ZERO).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_ZERO).setNodeLightingTime(cscDetailInfoVo.getBasicInfo().getFaultInfo().getBreakdownDate());
        }

        // 故障申告（从CSC单据详情获取）
        if(!ObjectUtils.isEmpty(cscDetailInfoVo) && StringUtils.isNotBlank(cscDetailInfoVo.getBasicInfo().getDeclareDate())){
            subwayNodeInfoList.get(INTEGER_ONE).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_ONE).setNodeLightingTime(cscDetailInfoVo.getBasicInfo().getDeclareDate());
        }

        // warRoom创建（从warRoom地铁图详情获取）
        HeaderVo createWarRoom = warRoomNodeVo.getCreateWarRoom();
        if(!ObjectUtils.isEmpty(createWarRoom)&& StringUtils.isNotBlank(createWarRoom.getCreatedDate())){
            subwayNodeInfoList.get(INTEGER_TWO).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_TWO).setNodeLightingTime(createWarRoom.getCreatedDate());
        }

        // 避免圈复杂度超标，这里抽一个方法出来
        initDate(warRoomNodeVo, subwayNodeInfoList);
    }

    private static void initDate(WarRoomNodeVo warRoomNodeVo, List<SubwayNodeInfo> subwayNodeInfoList) {
        // 远程接入（从warRoom地铁图详情获取）
        processRecordLightNode(
                warRoomNodeVo.getAccess(),
                subwayNodeInfoList.get(INTEGER_THREE),
                ProcessRecordVo::getAccessTime);

        // 人员到位（从warRoom地铁图详情获取）
        processRecordLightNode(
                warRoomNodeVo.getInPlace(),
                subwayNodeInfoList.get(INTEGER_FOUR),
                ProcessRecordVo::getInPlaceTime);

        // 故障定位（从warRoom地铁图详情获取）
        processRecordLightNode(
                warRoomNodeVo.getFaultLocating(),
                subwayNodeInfoList.get(INTEGER_FIVE),
                ProcessRecordVo::getFaultLocatingResultFilledTime);

        // 业务恢复（从warRoom地铁图详情获取）
        processRecordLightNode(
                warRoomNodeVo.getBusinessRecover(),
                subwayNodeInfoList.get(INTEGER_SIX),
                ProcessRecordVo::getBusinessRecoverTime);
    }

    /**
     * 节点点亮 - 节点记录
     */
    private static void processRecordLightNode(
            List<ProcessRecordVo> processRecords,
            SubwayNodeInfo subwayNode,
            Function<ProcessRecordVo, Date> getDateFunction) {

        if (CollectionUtils.isEmpty(processRecords)) {
            return;
        }

        List<Date> dates = processRecords.stream()
                .map(getDateFunction)
                .collect(Collectors.toList());
        subwayNode.setNodeLightingStatus(true);
        subwayNode.setNodeLightingTime(DateUtils.dateToString(Collections.min(dates), DATE_FORM));
    }
}
