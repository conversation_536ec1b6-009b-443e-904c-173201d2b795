package com.zte.iccp.itech.extension.common.utils;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.paas.lcap.common.context.RequestContext;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/13
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AsyncExecuteUtils {

    private static final ThreadPoolExecutor DEFAULT_EXECUTOR
            = new ThreadPoolExecutor(16, 32,
                    10, TimeUnit.MINUTES,
                    new LinkedBlockingQueue<>(100),
                    new ThreadFactoryBuilder().setNameFormat("pool-%d").build());

    public static void execute(Runnable runnable) {
        RequestContext requestContext = RequestContextHolder.getRequestContext();
        String langId = ContextHelper.getLangId();
        DEFAULT_EXECUTOR.execute(() -> {
            RequestContextHolder.setRequestContext(requestContext);
            ContextHelper.setLangId(langId);
            try {
                runnable.run();
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
                throw e;
            } finally {
                RequestContextHolder.resetRequestContext();
                ContextHelper.resetHeaders();
            }
        });
    }

    private static <T> CompletableFuture<T> supplyAsyncWithHeaders(Supplier<T> supplier) {
        RequestContext requestContext = RequestContextHolder.getRequestContext();
        String langId = ContextHelper.getLangId();
        return CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestContext(requestContext);
            ContextHelper.setLangId(langId);
            try {
                return supplier.get();
            } finally {
                RequestContextHolder.resetRequestContext();
                ContextHelper.resetHeaders();
            }
        }, DEFAULT_EXECUTOR);
    }

    public static <T> List<T> asyncQuery(List<Supplier<T>> suppliers) {
        List<T> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(suppliers)) {
            return results;
        }

        List<CompletableFuture<T>> completableFutures = suppliers.stream()
                .map(AsyncExecuteUtils::supplyAsyncWithHeaders)
                .collect(Collectors.toList());

        CompletableFuture<Void> allFuture = CompletableFuture
                .allOf(completableFutures.toArray(new CompletableFuture[0]));

        try {
            allFuture.get();
            for (CompletableFuture<T> completableFuture : completableFutures) {
                results.add(completableFuture.get());
            }
        } catch (Exception e){
            log.error("asyncQuery Error", e);
        }

        return results;
    }

    public static Map<String, Object> asyncQuery(Map<String, Supplier<?>> tasks, long timeout) {
        // 创建CompletableFuture集合
        Map<String, CompletableFuture<Object>> futures = tasks.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> CompletableFuture.supplyAsync(
                                () -> entry.getValue().get(),
                                DEFAULT_EXECUTOR
                        )
                ));

        try {
            // 等待所有任务完成（带超时）
            Map<String, Object> results = futures.entrySet().parallelStream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                try {
                                    return entry.getValue().get(timeout, TimeUnit.MILLISECONDS);
                                } catch (TimeoutException e) {
                                    throw new RuntimeException("Task '" + entry.getKey() + "' timed out", e);
                                } catch (InterruptedException | ExecutionException e) {
                                    throw new RuntimeException("Task '" + entry.getKey() + "' failed", e);
                                }
                            }
                    ));

            // 验证所有任务是否成功
            validateAllTasksCompleted(futures);

            return results;
        } catch (RuntimeException e) {
            // 取消所有未完成的任务
            cancelPendingTasks(futures);
            throw e;
        }
    }

    private static void validateAllTasksCompleted(Map<String, CompletableFuture<Object>> futures) {
        for (CompletableFuture<?> future : futures.values()) {
            if (!future.isDone()) {
                throw new RuntimeException("Not all tasks completed");
            }
        }
    }

    private static void cancelPendingTasks(Map<String, CompletableFuture<Object>> futures) {
        futures.forEach((key, future) -> {
            if (!future.isDone()) {
                future.cancel(true);
            }
        });
    }
}
