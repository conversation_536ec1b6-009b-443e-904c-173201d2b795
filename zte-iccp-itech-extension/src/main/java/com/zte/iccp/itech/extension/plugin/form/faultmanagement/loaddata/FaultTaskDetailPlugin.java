package com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.assignment.FaultAssignmentAbility;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.FaultManagementDetails;
import com.zte.iccp.itech.extension.domain.model.base.MultiAttachmentFile;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.CrmClient;
import com.zte.iccp.itech.extension.spi.client.CscClient;
import com.zte.iccp.itech.extension.spi.client.PdmClient;
import com.zte.iccp.itech.extension.spi.client.WarRoomClient;
import com.zte.iccp.itech.extension.spi.model.csc.dto.CscDetailQuery;
import com.zte.iccp.itech.extension.spi.model.csc.vo.*;
import com.zte.iccp.itech.extension.spi.model.pdm.dto.vo.ProductInfoByLevelVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.ProcessRecordVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomNodeVo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids.*;

/**
 * 故障任务详情插件
 */
public class FaultTaskDetailPlugin implements LoadDataBaseFormPlugin {

    @Override
    public void loadData(LoadDataEventArgs args, FaultManagementDetails faultManagementDetails) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();
        if (formView == null || dataModel == null) {
            return;
        }

        // 1.检索 CSC 详情
        String cscCode = PropertyValueConvertUtil.getString(dataModel.getValue(FieldCids.FIELD_TASK_CODE_CID));
        CscDetailQuery cscDetailQuery = new CscDetailQuery();
        cscDetailQuery.setRequestNo(cscCode);
        CscDetailInfoVo cscDetailInfoVo = CscClient.getCscDetail(cscDetailQuery);

        // 2.检索 WarRoom 详情
        String warRoomId = PropertyValueConvertUtil.getString(dataModel.getValue(FieldCids.FIELD_WARROOM_ID_CID));
        WarRoomNodeVo warRoomNodeVo = WarRoomClient.queryWarRoomNode(warRoomId);

        // 3.检索 故障管理单 详情
        List<FaultManagementOrder> faultOrders = faultManagementDetails.getFaultManagementOrders();

        // 4.展示详情数据
        // 故障管理单 + CSC 详情 + WarRoom 详情
        if (Objects.isNull(cscDetailInfoVo)
                || CollectionUtils.isEmpty(faultOrders)
                || Objects.isNull(faultOrders.get(CommonConstants.INTEGER_ZERO))) {
            return;
        }

        FaultManagementOrder faultOrder = faultOrders.get(0);
        setFaultOrderDataDetail(dataModel, faultOrder);
        setCscDataDetail(dataModel, cscDetailInfoVo);
        setWarRoomDataDetail(dataModel, faultOrder.getId(), cscDetailInfoVo, warRoomNodeVo);
    }

    /**
     * 展示 故障管理单 数据
     * @param dataModel
     */
    private void setFaultOrderDataDetail(IDataModel dataModel, FaultManagementOrder faultOrder) {
        // 1.单据编号
        dataModel.setValue(FIELD_CSC_TASK_CODE_CID, faultOrder.getTaskCode());

        // 2.产品经营团队 + PDM产品
        Pair<String, String> pdmProductInfoPair = getPdmProductDetail(faultOrder.getPdmProductId());
        dataModel.setValue(FIELD_PROD_TEAM_CID, pdmProductInfoPair.getLeft());
        dataModel.setValue(FIELD_PRODUCT_CID, pdmProductInfoPair.getRight());
    }

    /**
     * 展示 CSC 详情数据
     * @param dataModel
     * @param cscDetailInfoVo
     */
    private void setCscDataDetail(IDataModel dataModel, CscDetailInfoVo cscDetailInfoVo) {
        // 1.请求单基础信息数据
        BasicInfoVo basicInfo = cscDetailInfoVo.getBasicInfo();
        if (Objects.nonNull(basicInfo)) {
            // (1) 直接展示数据 - Call log 申告时间 + 单据状态
            dataModel.setValue(FIELD_CALL_LOG_TIME_CID, basicInfo.getDeclareDate());
            dataModel.setValue(FIELD_TASK_STATUS_CID, basicInfo.getPhaseStatus());

            // (2) 故障详情相关数据 - 故障描述 + 故障发生时间
            FaultInfoVo faultInfo = basicInfo.getFaultInfo();
            if (Objects.nonNull(faultInfo)) {
                dataModel.setValue(FIELD_FAULT_DESCRIPTION_CID, faultInfo.getRequestInfo());
                dataModel.setValue(FIELD_FAULT_OCCURRENCE_TIME_CID, faultInfo.getBreakdownDate());
            }

            // (3) 客户数据
            String clientNo = basicInfo.getClientNo();
            if (StringUtils.hasText(clientNo)) {
                String clientName = CrmClient.getCustomerName(clientNo);
                dataModel.setValue(FIELD_CUSTOMER_CID, clientName);
            }
        }

        // 2.请求方信息数据
        RequesterInfoVo requestInfo = cscDetailInfoVo.getRequesterInfo();
        if (Objects.nonNull(requestInfo)) {
            // 请求方类型 + 客户所在区域
            dataModel.setValue(FIELD_REQUESTER_TYPE_CID, requestInfo.getDeclareTypeName());
            dataModel.setValue(FIELD_CUSTOMER_AREA_CID, requestInfo.getCustomerArea());
        }

        // 3.分类定级数据
        ClassificationInfoVo classificationInfo = cscDetailInfoVo.getClassificationInfo();
        if (Objects.nonNull(classificationInfo)) {
            // 故障级别 + 客户关注层级 + 原因分类(服务类型)
            dataModel.setValue(FIELD_FAULT_LEVEL_CID, classificationInfo.getServiceAttributeName());
            dataModel.setValue(FIELD_CUSTOMER_CONCERN_LEVEL_CID, classificationInfo.getCustomerAttentionName());
            dataModel.setValue(FIELD_REASON_TYPE_CID, classificationInfo.getServiceTypeName());
        }

        // 4.附件信息数据
        List<AttachInfoVo> attachInfoList = cscDetailInfoVo.getAttachInfo();
        if (!CollectionUtils.isEmpty(attachInfoList)) {
            dataModel.setValue(FIELD_ATTACHMENT_CID, convertAttachmentFile(attachInfoList));
        }
    }

    /**
     * 展示 WarRoom 详情数据
     */
    private void setWarRoomDataDetail(
            IDataModel dataModel,
            String faultOrderId,
            CscDetailInfoVo requestDetail,
            WarRoomNodeVo warRoomNodeInfo) {

        if (warRoomNodeInfo == null) {
            return;
        }

        // 1.展示 WarRoom 地铁图相关信息
        List<ProcessRecordVo> processRecordVos = warRoomNodeInfo.getResponse();
        if (!CollectionUtils.isEmpty(processRecordVos)) {
            ProcessRecordVo processRecordVo = processRecordVos.get(CommonConstants.INTEGER_ZERO);
            if (processRecordVo != null) {
                // todo 故障解决周期/故障挂起时长暂无接口，暂不展示
                // 故障恢复时间
                dataModel.setValue(FIELD_RECOVERY_TIME_CID,
                        DateUtils.dateToString(processRecordVo.getBusinessRecoverTime(), CommonConstants.DATE_FORM));
            }
        }

        // 2.部分 WarRoom 地铁图信息需在加载时更新到任务中心
        // 涉及信息：故障响应时长 / 人员到位时长 / 故障定位时长 / 故障恢复时长
        AsyncExecuteUtils.execute(() ->
                FaultAssignmentAbility.updateFaultWarRoomTimeInfo(
                        faultOrderId, requestDetail, warRoomNodeInfo));
    }

    /**
     * 获取 PDM 产品信息
     * left - 产品经营团队   right - 产品
     * @param pdmProductNo
     * @return Pair<String, String>
     */
    private Pair<String, String> getPdmProductDetail(String pdmProductNo) {
        if (!StringUtils.hasText(pdmProductNo)) {
            return Pair.of(CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING);
        }

        // 1.检索 产品型号 + 产品小类 + 产品大类 + 产品经营团队 + 产品线 信息
        ProductInfoByLevelVo productModel = getProductInfo(pdmProductNo);
        ProductInfoByLevelVo productSubclass = getProductInfo(productModel.getParentNo());
        ProductInfoByLevelVo productClass = getProductInfo(productSubclass.getParentNo());
        ProductInfoByLevelVo productLine = getProductInfo(productClass.getParentNo());
        ProductInfoByLevelVo productManageTeam = getProductInfo(productLine.getParentNo());

        // 2.数据转换
        String productManageTeamName = LangUtils.get(productManageTeam.getCnName(), productManageTeam.getEnName());
        String productName = productManageTeamName + CommonConstants.FORWARD_SLASH
                + LangUtils.get(productLine.getCnName(), productLine.getEnName()) + CommonConstants.FORWARD_SLASH
                + LangUtils.get(productClass.getCnName(), productClass.getEnName()) + CommonConstants.FORWARD_SLASH
                + LangUtils.get(productSubclass.getCnName(), productSubclass.getEnName()) + CommonConstants.FORWARD_SLASH
                + productModel.getProductModel();
        return Pair.of(productManageTeamName, productName);
    }

    /**
     * 获取产品信息
     * @param productNo
     * @return ProductInfoByLevelVo
     */
    private ProductInfoByLevelVo getProductInfo(String productNo) {
        if (!StringUtils.hasText(productNo)) {
            return new ProductInfoByLevelVo();
        }

        // 1.检索 PDM 系统
        List<ProductInfoByLevelVo> productInfo = PdmClient.queryProductInfo(Lists.newArrayList(productNo));
        if (CollectionUtils.isEmpty(productInfo)) {
            return new ProductInfoByLevelVo();
        }

        // 2.获取父级编码
        return productInfo.get(CommonConstants.INTEGER_ZERO);
    }

    /**
     * 包装附件对象
     * @param attachInfoList
     * @return List<MultiAttachmentFile>
     */
    private List<MultiAttachmentFile> convertAttachmentFile(List<AttachInfoVo> attachInfoList) {
        List<MultiAttachmentFile> attachmentFileList = new ArrayList<>();
        for (AttachInfoVo attachInfo : attachInfoList) {
            MultiAttachmentFile attachmentFile = new MultiAttachmentFile();

            attachmentFile.setId(attachInfo.getAttachmentId());
            attachmentFile.setName(attachInfo.getAttachment());
            attachmentFile.setDownloadUrl(attachInfo.getAttachLink());
            attachmentFile.setUploadTime(attachInfo.getCreatedDate());

            attachmentFileList.add(attachmentFile);
        }

        return attachmentFileList;
    }
}
