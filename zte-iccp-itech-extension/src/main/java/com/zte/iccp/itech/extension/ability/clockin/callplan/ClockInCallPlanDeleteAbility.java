package com.zte.iccp.itech.extension.ability.clockin.callplan;

import com.zte.iccp.itech.extension.ability.BaseOperationCacheAbility;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.OperationCache;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallReasonEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallStatusEnum;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInCallPlan;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_TIME;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInCallPlanFieldConsts.*;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
public class ClockInCallPlanDeleteAbility extends BaseOperationCacheAbility {

    public ClockInCallPlanDeleteAbility() {
        super(null);
    }

    public ClockInCallPlanDeleteAbility(OperationCache operationCache) {
        super(operationCache);
    }

    public void delete(Date createTime, String clockInTaskId) {
        delete(createTime, clockInTaskId, null);
    }

    public void delete(Date createTime, String clockInTaskId, CallReasonEnum reason) {
        List<IFilter> filters = Lists.newArrayList(
                new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.WAITING)),
                new Filter(CREATE_TIME, Comparator.GE, createTime),
                new Filter(OBJECT_ID, Comparator.EQ, clockInTaskId));

        if (reason != null) {
            filters.add(new Filter(REASON, Comparator.EQ, Lists.newArrayList(reason)));
        }

        List<ClockInCallPlan> callPlans = QueryDataHelper.query(
                ClockInCallPlan.class, Lists.newArrayList(ID), filters);

        List<String> ids = callPlans.stream()
                .map(ClockInCallPlan::getId)
                .collect(Collectors.toList());

        delete(ClockInCallPlan.class, ids);
    }
}
