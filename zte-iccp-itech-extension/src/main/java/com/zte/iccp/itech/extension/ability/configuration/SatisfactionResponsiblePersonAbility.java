package com.zte.iccp.itech.extension.ability.configuration;

import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.entity.SatisfactionResponsiblePerson;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.SatisfactionResponsiblePersonConsts.*;

/**
 * 故障管理任务--客户满意度责任人查询
 *
 * <AUTHOR> 10335201
 * @date 2024-08-27 下午2:34
 **/
public final class SatisfactionResponsiblePersonAbility {

    /**
     * 获取客户满意度责任人
     */
    public static List<SatisfactionResponsiblePerson> getSatisfactionResponsible(
            String country,
            String organization,
            String productTeam,
            String productLine) {

        if (StringUtils.isBlank(country)
                || StringUtils.isBlank(organization)
                || StringUtils.isBlank(productTeam)
                || StringUtils.isBlank(productLine)) {
            return Lists.newArrayList();
        }

        IFilter dataStatusFilter = new Filter(DATA_STATUS, Comparator.EQ, Lists.newArrayList(CommonConstants.Y));
        IFilter countryFilter = new Filter(COUNTRY, Comparator.EQ, Lists.newArrayList(country));
        IFilter organizationFilter = new Filter(ORGANIZATION_ID, Comparator.EQ, organization);
        IFilter productTeamFilter = new Filter(PRODUCT_ID, Comparator.EQ, productTeam);
        IFilter productLineFilter = new Filter(PRODUCT_ID, Comparator.EQ, productLine);

        // 1.层级匹配规则，匹配到则返回，匹配不到则按照下一层级进行查询
        // (1) 代表处 + 国家 + 产品线
        List<SatisfactionResponsiblePerson> responsible = querySatisfactionResponsible(
                Lists.newArrayList(dataStatusFilter, organizationFilter, countryFilter, productLineFilter));
        if (!CollectionUtils.isEmpty(responsible)) {
            return responsible;
        }

        // (2) 代表处 + 国家 + 产品经营团队
        responsible = querySatisfactionResponsible(
                Lists.newArrayList(dataStatusFilter, organizationFilter, countryFilter, productTeamFilter));
        if (!CollectionUtils.isEmpty(responsible)) {
            return responsible;
        }

        // (3) 代表处 + 产品线
        responsible = querySatisfactionResponsible(
                Lists.newArrayList(dataStatusFilter, organizationFilter, productLineFilter));
        if (!CollectionUtils.isEmpty(responsible)) {
            return responsible;
        }

        // (4) 代表处 + 产品经营团队
        responsible = querySatisfactionResponsible(
                Lists.newArrayList(dataStatusFilter, organizationFilter, productTeamFilter));
        if (!CollectionUtils.isEmpty(responsible)) {
            return responsible;
        }

        // 5、如果三个条件查不到数据，换成代表处+产品也查不到数据，则换成代表处+国家查一次（remove掉productTeamId，add上countIdList）
        // 代表处 + 国家
        responsible = querySatisfactionResponsible(
                Lists.newArrayList(dataStatusFilter, organizationFilter, countryFilter));
        if (!CollectionUtils.isEmpty(responsible)) {
            return responsible;
        }

        // (6) 代表处
        responsible = querySatisfactionResponsible(
                Lists.newArrayList(dataStatusFilter, organizationFilter));
        return responsible;
    }

    /**
     * 检索客户满意度责任人
     */
    private static List<SatisfactionResponsiblePerson> querySatisfactionResponsible(
            List<IFilter> iFilters) {
        return QueryDataHelper.query(
                SatisfactionResponsiblePerson.class,
                Lists.newArrayList(
                        MANAGER, PRODUCT_SECTION_CHIEF, DATA_STATUS,
                        ORGANIZATION_ID, COUNTRY, PRODUCT_ID),
                iFilters);
    }
}