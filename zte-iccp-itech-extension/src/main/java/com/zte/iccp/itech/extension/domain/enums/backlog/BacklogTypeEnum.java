package com.zte.iccp.itech.extension.domain.enums.backlog;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 代办任务类型枚举
 *
 * <AUTHOR> 10335201
 * @date 2024-12-10 上午10:18
 **/
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum BacklogTypeEnum {
    /** 待我处理 */
    TO_BE_HANDLED_BY_ME("TO_BE_HANDLED_BY_ME", "待我处理", "To be handled by me"),

    /** 我已处理 */
    HANDLED_BY_ME("HANDLED_BY_ME", "我已处理", "Handled by me"),

    /** 我发起的 */
    INITIATED_BY_ME("INITIATED_BY_ME", "我发起的", "Initiated by me");


    /**
     * 编码
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;
}
