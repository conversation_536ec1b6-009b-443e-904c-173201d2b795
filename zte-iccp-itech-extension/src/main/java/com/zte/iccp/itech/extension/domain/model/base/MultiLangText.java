package com.zte.iccp.itech.extension.domain.model.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.zlic.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MultiLangText {
    @JsonProperty(value = "zh_CN")
    private String zhCN;

    @JsonProperty(value = "en_US")
    private String enUS;

    private String value;

    public String getTextByLanguage(String language) {
        return ZH_CN.equals(language) ? zhCN : enUS;
    }

    public String getZhCN() {
        return StringUtils.isWhitespace(zhCN) ? value : zhCN;
    }

    public String getEnUS() {
        return StringUtils.isWhitespace(enUS) ? value : enUS;
    }
}