package com.zte.iccp.itech.extension.domain.model.export;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NetworkChangeTaskInfo {

    /** 任务单号 */
    private String assignmentCode;

    /** 任务名称 - 需设置超链接 */
    private String assignmentName;

    /** 任务类型 */
    private String assignmentType;

    /** 营销 */
    private String marketing;

    /** 代表处 */
    private String representativeOffice;

    /** 产品分类 */
    private String productSubclass;

    /** 国家 / 地区 */
    private String country;

    /** 操作类型 */
    private String operationType;

    /** 操作原因 */
    private String operationReason;

    /** 操作结果 */
    private String operationResult;

    /** 操作等级 */
    private String operationLevel;

    /** 重要程度 */
    private String importance;

    /** 风险评估 */
    private String riskEvaluation;

    /** 客户网络名称 */
    private String customerNetworkName;

    /** 局点名称 */
    private String officeName;

    /** 客户标识 */
    private String customerClassification;

    /** 计划开始时间 */
    private String planStartTime;

    /** 任务状态 */
    private String assignmentStatus;

    /** 当前进展 */
    private String currentProgress;

    /** 当前处理人 */
    private String currentProcessor;

    /** 责任人 */
    private String responsible;

    /** 是否超期 */
    private String overdueFlag;

    /** 超期时间 */
    private String overdueTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private String createTime;

    /** 更新人 */
    private String lastUpdateBy;

    /** 更新时间 */
    private String lastUpdateTime;
}
