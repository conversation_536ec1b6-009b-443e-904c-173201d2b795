package com.zte.iccp.itech.extension.handler.approver.partner;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ONE;

/**
 * 办事处产品经理
 * 从配置中心-代表处人员配置，根据产品线>经营团队 +【代表处】+【是否政企】+【角色=产品经理】读取代表处产品经理和代表处产品经理审核组
 * <AUTHOR>
 * @create 2024/7/15 上午10:36
 */
public class OfficeManagerHandlerImpl extends AbstractRewardApproverHandler<SubcontractorChangeOrder> {

    @Override
    public List<String> rewardApproverByNodeExtend(SubcontractorChangeOrder changeOrder, FlowClient bod) {
        String productId = changeOrder.getProductCategory();
        ApproverConfiguration queryParam = getQueryParam(changeOrder, productId);
        return ApproverConfigAbility.getApprovalPriorityPersons(queryParam, productId, INTEGER_ONE, null);
    }

    public ApproverConfiguration getApprover(SubcontractorChangeOrder changeOrder) {
        String productId = changeOrder.getProductCategory();
        ApproverConfiguration queryParam = getQueryParam(changeOrder, productId);
        return ApproverConfigAbility.getApprovalConfiguration(queryParam, productId, INTEGER_ONE);
    }

    private ApproverConfiguration getQueryParam(SubcontractorChangeOrder changeOrder, String productId) {
        ApproverConfiguration queryParam = new ApproverConfiguration();
        queryParam.setApprovalNode(ApprovalTypeEnum.PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE);
        queryParam.setProdTeam(ProductUtils.getTeam(productId));
        queryParam.setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
        queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
        queryParam.setResponsibleDeptId(changeOrder.getResponsibleDept());
        queryParam.setIsGov(changeOrder.getIsGovEnt());
        queryParam.setRole(ApproveRoleEnum.PRODUCT_MANAGER);
        return queryParam;
    }
}
