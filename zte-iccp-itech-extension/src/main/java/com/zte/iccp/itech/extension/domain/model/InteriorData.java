package com.zte.iccp.itech.extension.domain.model;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> 10335201
 * @date 2024-12-20 下午5:39
 **/
@ApiModel("内部接口数据结果集合")
@Setter
@Getter
public class InteriorData {
    /** 批次任务审核实体ID */
    private Set<String> batchApproveId;

    /** 批次任务状态 */
    private Map<String, String> batchStatus;

    /** 分包商批次任务审核实体ID */
    private Set<String> subcontractBatchApproveId;

    /** 分包商批次任务状态 */
    private Map<String, String> subcontractBatchStatus;

    /** 主任务实体Id */
    private List<String> mainId;

    /** 子任务实体Id */
    private List<String> subId;

    /** 主任务当前进展 */
    private Map<String, String> mainCurrentProgress;

    /** 子任务当前进展 */
    private Map<String, String> subCurrentProgress;

    /** 主任务验收人信息 */
    private Map<String, String> acceptPerson;

    /** 是否审批任务 */
    private Map<String, String> approvalTaskFlag;

    /** 处理时间 */
    private Map<String, String> dealingTime;
}
