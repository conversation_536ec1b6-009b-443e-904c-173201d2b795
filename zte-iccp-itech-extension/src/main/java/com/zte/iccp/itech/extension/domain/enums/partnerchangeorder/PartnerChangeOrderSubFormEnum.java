package com.zte.iccp.itech.extension.domain.enums.partnerchangeorder;


import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.utils.AiParamUtils;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Triple;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.function.Function;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORM;

/**
 * <AUTHOR>
 * @date 2024/6/18 下午7:43
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PartnerChangeOrderSubFormEnum {
    /**
     * 批次概要
     */
    BATCH_SUMMARY("batch_summary", "operation_object_batch_summary",
            Lists.newArrayList(
                    Triple.of("batch_no","batch_summary_batch_no", AiParamUtils::getAllTextNameString),
                    Triple.of("plan_operation_start_time","cid_plan_operation_start_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
                    Triple.of("plan_operation_end_time","cid_plan_operation_end_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
                    Triple.of("operation_account","operation_account", Object::toString),
                    Triple.of("ne_count","batch_summary_ne_count", Object::toString),
                    Triple.of("operation_description", "TextareaField_jy1uk29d", Object::toString))),

    /**
     * 操作对象
     */
    OPERATION_OBJECT("operation_object", "operation_object_order",
            Lists.newArrayList(
                    Triple.of("batch_info_no","operation_object_batch_no", AiParamUtils::getAllTextNameString),
                    Triple.of("network_id", "operation_object_network_id", object -> ((DynamicDataEntity) object).getString("CustomerNetworkName")),
                    Triple.of("office_name", "operation_object_office_name", Object::toString),
                    Triple.of("product_model", "operation_object_product_model", object -> ((DynamicDataEntity) object).getString("product_model")))),

    /**
     * 操作人员
     */
    OPERATOR_TABLE("operator_table", "operator",
            Lists.newArrayList(
                    Triple.of("operator_role","operation_person_role", AiParamUtils::getAllTextNameString),
                    Triple.of("operator_name", "cid_operator_name", AiParamUtils::getEmployeeNameString),
                    Triple.of("is_remote", "is_remote", AiParamUtils::getAllTextNameString),
                    Triple.of("operator_batch_no", "operator_object_batch_no", AiParamUtils::getAllTextNameString),
                    Triple.of("task_desc", "task_desc", Object::toString),
                    Triple.of("operator_department", "operator_department", Object::toString),
                    Triple.of("operator_account", "operator_account", AiParamUtils::getAllTextNameString),
                    Triple.of("telephone", "telephone", Object::toString))),

    /**
     * 操作阶段打卡
     */
    OPERATION_STAGE_CHECK_IN_TABLE("operation_stage_check_in_table", "operation_phase_check",
            Lists.newArrayList(
                    Triple.of("operation_phase","operation_phase", Object::toString),
                    Triple.of("operation_duration", "operation_duration", Object::toString),
                    Triple.of("stage_start_time", "operation_phase_start_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
                    Triple.of("stage_end_time", "operation_phase_end_time",r -> new SimpleDateFormat(DATE_FORM).format(r)),
                    Triple.of("stage_check_in_person", "stage_check_in_person", Object::toString))),

    /**
     * 高危指令表
     */
    HIGH_RISK_INSTRUCTION_TABLE("hzs_high_risk_instruction_table", "TableFieldNew_azyzu9w9",
            Lists.newArrayList(
                    Triple.of("high_risk_instruction", "high_risk_instruction", Object::toString))),

    /**
     * 技术方案检查
     */
    TECH_SOLUTION_CHECK("hzs_tech_solution_check", "hzs_tech_solution_check",
            Lists.newArrayList(
                    Triple.of("check_content","check_content", Object::toString),
                    Triple.of("preparation", "preparation", AiParamUtils::getAllTextNameString),
                    Triple.of("preparation_desc", "preparation_desc", Object::toString))),



    ;


    //实体表唯一标识
    private final String propertyKey;

    //布局唯一标识
    private final String cid;

    //子表单所有字段集合
    private final List<Triple<String, String, Function<Object, String>>> fieldCidKeys;
}
