package com.zte.iccp.itech.extension.openapi;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * KMS解密对比
 *
 * <AUTHOR>
 * @create 2025/4/21 下午8:02
 */
public class KmsDecryptApi extends AbstractOpenApi {

    public Map<String, String> kmsDecrypt(@RequestBody List<String> systems) {
        Map<String, String> result = new HashMap<>();
        systems.forEach(i -> {
            result.put(i, ConfigHelper.getRaw(i));
        });
        return result;
    }
}
