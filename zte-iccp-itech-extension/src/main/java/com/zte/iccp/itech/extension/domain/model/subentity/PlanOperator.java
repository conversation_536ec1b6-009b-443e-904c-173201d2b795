package com.zte.iccp.itech.extension.domain.model.subentity;


import com.zte.iccp.itech.extension.domain.model.Operator;
import com.zte.iccp.itech.extension.domain.model.PlanOperationOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

@ApiModel("操作人员")
@Setter
@Getter
@BaseSubEntity.Info(value = "operator_table", parent = PlanOperationOrder.class)
public class PlanOperator extends Operator {
}
