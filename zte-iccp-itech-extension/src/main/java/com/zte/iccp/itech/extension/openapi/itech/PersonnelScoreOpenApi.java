package com.zte.iccp.itech.extension.openapi.itech;

import com.zte.iccp.itech.extension.ability.ManageTaskAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.domain.model.TechnologyManagementOrder;
import com.zte.iccp.itech.extension.openapi.model.itech.PersonnelScoreTechManagementTaskDto;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;

/**
 * 人员积分openapi
 *
 * <AUTHOR>
 * @create 2025/6/16 下午1:48
 */
public class PersonnelScoreOpenApi extends AbstractOpenApi {

    /*
     * 创建人员积分技术管理管理任务
     * */
    public ServiceData<String> createPersonnelScoreTask(@RequestBody PersonnelScoreTechManagementTaskDto dto) {
        checkRequired(dto);
        String id = ManageTaskAbility.createTaskFlow(dto);
        ServiceData<String> result = new ServiceData<>();
        result.setCode(success());
        result.setBo(id);
        return result;
    }

    /*
     * 废止人员积分技术管理管理任务
     * */
    public ServiceData<String> abolishTask(@RequestParam String id) {
        if (!StringUtils.hasText(id)) {
            throw new LcapBusiException("id is null");
        }
        ManageTaskAbility.abolishTask(id);
        ServiceData<String> result = new ServiceData<>();
        result.setCode(success());
        result.setBo(id);
        return result;
    }

    /**
     * 基于sourceId判断是否创建技术管理任务，用于重试判断幂等操作
     */
    public ServiceData<String> isCreatedTask(@RequestParam String sourceId) {
        if (!StringUtils.hasText(sourceId)) {
            throw new LcapBusiException("sourceId is null");
        }
        TechnologyManagementOrder order = ManageTaskAbility.queryBySourceId(sourceId);
        ServiceData<String> result = new ServiceData<>();
        result.setCode(success());
        result.setBo(null == order ? EMPTY_STRING : order.getId());
        return result;
    }

    /*
     * 校验必填字段
     * */
    private void checkRequired(PersonnelScoreTechManagementTaskDto dto) {
        if (dto.getDeductionEpmNo() == null) {
            throw new LcapBusiException("deductionEpmNo is null");
        }

        if (dto.getTaskLevel() == null) {
            throw new LcapBusiException("taskLevel is null");
        }

        if (dto.getTaskName() == null) {
            throw new LcapBusiException("taskName is null");
        }

        if (dto.getTaskDetail() == null) {
            throw new LcapBusiException("taskDetail is null");
        }

        if (dto.getOrgId() == null) {
            throw new LcapBusiException("orgId is null");
        }
    }

    private static RetCode success() {
        return new RetCode() {{
            setCode(RetCode.SUCCESS_CODE);
            setMsgId(RetCode.SUCCESS_MSGID);
            setMsg(RetCode.SUCCESS_MSG);
        }};
    }
}
