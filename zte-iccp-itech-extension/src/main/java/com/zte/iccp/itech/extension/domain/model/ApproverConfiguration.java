package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.AvailabilityEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.*;

@ApiModel("审核人")
@Setter
@Getter
@BaseEntity.Info("Approver_Config")
public class ApproverConfiguration extends BaseEntity {
    @JsonProperty(value = APPROVAL_NODE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApprovalTypeEnum approvalNode;

    @JsonProperty(value = SALES)
    @ApiModelProperty("营销单位")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String sales;

    @JsonProperty(value = ORGANIZATION_REGION)
    @ApiModelProperty("片区")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String organizationRegion;

    @JsonProperty(value = RESPONSIBLE_DEPT)
    @ApiModelProperty("代表处")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String responsibleDeptId;

    @JsonProperty(value = PROD_OPERATION_TEAM)
    @ApiModelProperty("产品经营团队")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String prodTeam;

    @JsonProperty(value = PRODUCT_LINE)
    @ApiModelProperty("产品线")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String prodLine;

    @JsonProperty(value = PROD_MAIN_CATEGORY)
    @ApiModelProperty("产品大类")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String prodMainCategory;

    @JsonProperty(value = PRODUCT_SUB_CATEGORY)
    @ApiModelProperty("产品小类")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String prodSubCategory;

    @JsonProperty(value = PRODUCT_MODEL_ID)
    @ApiModelProperty("产品型号")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String productModelId;

    @JsonProperty(value = OPERATOR)
    @ApiModelProperty("运营商")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String operator;

    @JsonProperty(value = ROLE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveRoleEnum role;

    @JsonProperty(value = OPERATION_START_TIME)
    @ApiModelProperty("操作开始时间")
    private Integer operationStartTime;

    @JsonProperty(value = OPERATION_END_TIME)
    @ApiModelProperty("操作结束时间")
    private Integer operationEndTime;

    @JsonProperty(value = APPROVER_PERSON)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> approverPersons;

    @JsonProperty(value = APPROVER_GROUP)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> approverGroups;

    @JsonProperty(value = STATUS)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private AvailabilityEnum status;

    @JsonProperty(value = IS_GOV)
    @ApiModelProperty("是否政企")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGov;

    @JsonProperty(value = OPERATE_TYPE_MULTI)
    @ApiModelProperty("操作类型多选")
    private List<String> operationType;

    @JsonProperty(value = INTEGRATED_ASSOCIATED_PRODUCT_ZH)
    @ApiModelProperty("关联操作产品中文名")
    private String assocProdZh;

    @JsonProperty(value = INTEGRATED_ASSOCIATED_PRODUCT_EN)
    @ApiModelProperty("关联操作产品英文名")
    private String assocProdEn;

    @JsonProperty(value = BILLSTATUSFIELD_STATUS)
    @ApiModelProperty("单据状态")
    private String billStatus;

    @JsonProperty(value = LOGICAL_NE)
    @ApiModelProperty("逻辑网元")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String logicalNe;

    @JsonProperty(value = MULTI_ASSOCIATED_PROD_ZH)
    @ApiModelProperty("多选关联产品")
    private List<String> multiAssociatedProdZh;

    @JsonProperty(value = MULTI_ASSOCIATED_PROD_EN)
    @ApiModelProperty("多选关联产品")
    private List<String> multiAssociatedProdEn;

    @JsonProperty(value = ENTITY_ID)
    @ApiModelProperty("实体ID（导入场景使用）")
    private String entityId;
}
