package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.FlowVariantEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME;
import static com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum.PASS;
import static com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin.BasePluginVariants.FLOW_VERSION;

/**
 * 流程插件：远程中心审核通过后更新,更新上次操作时间
 *
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class BatchUpdateChangeTimePlugin extends BaseFlowOperationPlugin {

    @Override
    public  void beforeOperate(ExecuteEvent executeEvent) {
        if (!PASS.name().equals(TextValuePairHelper.getValue(getModel().getValue(APPROVE_RESULT_TD_NET_DEPT_APP_SOLU)))) {
            getModel().setValue(APPROVAL_STATUS, BoolEnum.N.getPropValue());
            return;
        }

        long pageStartTime = ((Date) getModel().getValue(PLAN_OPERATION_START_TIME)).getTime();
        long pageEndTime = ((Date) getModel().getValue(PLAN_OPERATION_END_TIME)).getTime();

        long changeSatrtTime = ((Date) getModel().getValue(CHANGE_OPERATION_TIME_START)).getTime();
        long changeEndTime = ((Date) getModel().getValue(CHANGE_OPERATION_TIME_END)).getTime();

        if (pageStartTime != changeSatrtTime || pageEndTime != changeEndTime) {
            getModel().setValue(CHANGE_OPERATION_TIME_START, new Date(pageStartTime));
            getModel().setValue(CHANGE_OPERATION_TIME_END, new Date(pageEndTime));
        }
        getModel().setValue(APPROVAL_STATUS, BoolEnum.Y.getPropValue());
        getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.NOTIFICATION_UNDER_REVIEW.getValue());

        String batchId = getPkId();
        BatchTask batchTask = QueryDataHelper.get(BatchTask.class, Lists.newArrayList(ID, OPERATION_CHANGE_DESC), batchId);
        // 1、高负荷场景追加
        if (StringUtils.isEmpty(batchTask.getOperationChangeDesc())) {
            return;
        }
        Map<String, Object> flowVariables = FlowHelper.getFlowVariables(batchId);
        BoolEnum isAdministrationLeaderApproval = BoolEnum.convertStringToBoolean(
                (String) flowVariables.get(FlowVariantEnum.IS_ADMINISTRATION_LEADER_APPROVAL.getKey()));
        // 是否行政领导审批 = N and 当天审批过操作计划审核 = N 走高负荷操作计划审核
        if (((int) flowVariables.getOrDefault(FLOW_VERSION.getKey(), 0)) > 1
                && BoolEnum.N == isAdministrationLeaderApproval
                && !FlowHelper.hasExtendedCodeApprovalToday(batchId, ApproveNodeEnum.CHANGED_BY_REP_PROD_CHIEF.name())) {

            getModel().setValue(PERSON_CHANGE_FLAG, BoolEnum.Y.getPropValue());
            Map<String, Object> variables = MapUtils.newHashMap(FlowVariantEnum.IS_HIGH_LOAD.getKey(), BoolEnum.Y.name(),
                    PERSON_CHANGE_FLAG, BoolEnum.Y.name());
            FlowHelper.changeFlowParams(batchId, variables, ApproveFlowCodeEnum.BATCH_TASK_FLOW);
        }

    }
}
