package com.zte.iccp.itech.extension.openapi.forward;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.openapi.forward.handler.IForwardHandler;
import com.zte.iccp.itech.extension.plugin.ServiceEntryPlugin;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.common.classloader.CustomClassLoaderHelper;
import com.zte.paas.lcap.ddm.domain.helper.util.ApiClient;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

import static com.zte.paas.lcap.platform.constant.HttpHeaderConst.*;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

/**
 * <AUTHOR> ********
 * @since 2024/05/23
 */
public class ForwardApi extends AbstractOpenApi {

    static {
        ServiceEntryPlugin.init();
    }

    private static final String X_TARGET_URL = "X-Target-Url";

    private static final String X_TARGET_HANDLER = "X-Target-Handler";

    private static final List<String> TRANSMISSIBLE_HEADERS = Lists.newArrayList(
            CONTENT_TYPE, X_EMP_NO, X_ACCOUNT_ID,
            X_AUTH_VALUE, X_TENANT_ID, X_LANG_ID,
            X_UUID, X_FORWARDED_FOR, X_DEVICE_TYPE);

    @SneakyThrows
    public ServiceData<Object> forward(
            @Autowired HttpServletRequest request,
            @RequestHeader(X_TARGET_URL) String targetUrl,
            @RequestHeader(value = X_TARGET_HANDLER, required = false) String targetHandler,
            @RequestBody(required = false) Object body) {

        Map<String, Object> headers = getHeaders(request);
        Map<String, String[]> params = request.getParameterMap();

        IForwardHandler forwardHandler = getForwardHandler(targetHandler);
        headers = forwardHandler.headers(headers);
        params = forwardHandler.params(params);
        body = forwardHandler.body(body);
        targetUrl = forwardHandler.url(targetUrl, params);

        //noinspection unchecked,rawtypes
        Object result = ApiClient.invokeServiceByUrl(
                targetUrl, request.getMethod(), (Map) params, body, headers);
        return success(forwardHandler.bo(result));
    }

    private static IForwardHandler getForwardHandler(String targetHandler)
            throws ClassNotFoundException, InstantiationException, IllegalAccessException {
        IForwardHandler forwardHandler;
        if (StringUtils.isNotBlank(targetHandler)) {
            //noinspection unchecked
            Class<? extends IForwardHandler> handler = (Class<? extends IForwardHandler>) CustomClassLoaderHelper
                    .getAppCustomClassLoader(ContextHelper.getTenantId(), ContextHelper.getAppId())
                    .loadClass(targetHandler);
            forwardHandler = handler.newInstance();
        } else {
            forwardHandler = new IForwardHandler() {};
        }
        return forwardHandler;
    }

    private static Map<String, Object> getHeaders(HttpServletRequest request) {
        Map<String, Object> headers = Maps.newHashMap();
        Enumeration<String> headerNames = request.getHeaderNames();

        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if (TRANSMISSIBLE_HEADERS.stream()
                            .noneMatch(h -> h.equalsIgnoreCase(headerName))
                    || request.getHeader(headerName) == null) {
                continue;
            }

            headers.put(headerName, request.getHeader(headerName));
        }

        return headers;
    }

    private static ServiceData<Object> success(Object result) {
        return new ServiceData<Object>() {{
            setBo(result);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
                setMsgId(RetCode.SUCCESS_MSGID);
                setMsg(RetCode.SUCCESS_MSG);
            }});
        }};
    }
}
