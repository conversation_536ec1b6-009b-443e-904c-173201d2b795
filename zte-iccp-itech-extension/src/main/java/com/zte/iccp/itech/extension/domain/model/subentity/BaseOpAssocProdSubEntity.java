package com.zte.iccp.itech.extension.domain.model.subentity;

import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/26
 */
public abstract class BaseOpAssocProdSubEntity extends BaseSubEntity {
    public abstract String getProductType();

    public abstract void setProductType(String productType);

    public abstract String getAssocProdNameZh();

    public abstract void setAssocProdNameZh(String assocProdName);

    public abstract String getAssocProdNameEn();

    public abstract void setAssocProdNameEn(String assocProdName);

    public abstract SingleEmployee getApprover();

    public abstract void setApprover(SingleEmployee approver);

}