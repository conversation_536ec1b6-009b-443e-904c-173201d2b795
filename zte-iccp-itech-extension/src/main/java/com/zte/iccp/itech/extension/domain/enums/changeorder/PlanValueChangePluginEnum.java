package com.zte.iccp.itech.extension.domain.enums.changeorder;


import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts;
import com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;

/**
 * <AUTHOR>
 * @date 2024/5/10 下午5:40
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PlanValueChangePluginEnum {

    /**
     * 操作类型-联动插件  + 逻辑网元展示
     */
    OPERATION_TYPE(Lists.newArrayList(FIELD_PRODUCT_CID), new OperationTypePluginValue()),

    /**
     * 逻辑网元展示
     */
    LOGICAL_NE(Lists.newArrayList(FIELD_PRODUCT_CID), new OperationTypePluginValue()),

    /**
     * 操作原因-联动插件
     */
    OPERATION_REASON(Lists.newArrayList(FIELD_OPERATION_TYPE_CID), new OperationReasonPluginValue()),

    /**
     * 操作类型分组-赋值插件
     */
    OPERATION_TYPE_GROUP(Lists.newArrayList(FIELD_OPERATION_TYPE_CID), new OperationTypeGroupPluginValue()),

    /**
     * 敏感区域
     */
    SENSITIVE_AREAS(Lists.newArrayList(FIELD_PRODUCT_CID, FIELD_PROVINCE_CID), new PlanSensitiveAreasPluginValue()),

    /**
     * 敏感操作
     */
    SENSITIVE_OPERATIONS(Lists.newArrayList(FIELD_PRODUCT_CID, CidConstants.SENSITIVE_AREAS, FIELD_IS_EMERGENCY_OPERATION_CID,
            COMPONENT_OPERATION_LEVEL_COMPONENT_CID, FIELD_IS_NET_CLOSE_OR_CONTROL_OPERATION_CID, GRP_DRCT_MNG,
            FIELD_IS_NET_CLOSE_OR_CONTROL_OPERATION_CID), new PlanSensitiveOperationPluginValue()),

    /**
     * 重要程度-自动计算插件
     */
    IMPORTANCE(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID,
            FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, FIELD_CUSTOMER_ID_CID,
            FIELD_COUNTRY_CID, FIELD_OPERATION_TYPE_CID, FIELD_IS_FIRST_APPLICATION_CID), new ImportanceFormPluginValue(true)),

    /**
     * 操作等级-自动计算插件
     */
    OPERATION_LEVEL(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID,
            FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, FIELD_CUSTOMER_ID_CID,
            FIELD_COUNTRY_CID, FIELD_OPERATION_TYPE_CID, FIELD_IS_FIRST_APPLICATION_CID, FIELD_SERVICE_DISCONNECT_DURATION_CID),
            new OperationLevelPluginValue(true)),

    /**
     * 风险评估-自动计算插件
     */
    RISK_EVALUATION(
            Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID, FIELD_IS_SPECIAL_SCENARIO_CID,
                    FIELD_OPERATION_TYPE_CID, FIELD_SERVICE_DISCONNECT_DURATION_CID, FIELD_IS_FIRST_APPLICATION_CID), new RiskEvaluationPluginValue()),

    /**
     * 操作主题-拼接赋值插件
     */
    OPERATION_SUBJECT(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_PRODUCT_CID, FIELD_IS_GOV_ENT_CID,
            FIELD_OPERATION_TYPE_CID, FIELD_AREA_CID, FIELD_PROVINCE_CID, FIELD_COUNTRY_CID,
            FIELD_CUSTOMER_ID_CID, ACCN_TYPE_CID, ChangeOrderFieldConsts.LOGICAL_NE), new OperationSubjectPluginValue(true)),

    /**
     * 代表处方案审核人插件
     */
    OFFICE_SOLUTION_REVIEWER(Lists.newArrayList(FIELD_PRODUCT_CID, FIELD_ORGANIZATION_CID,
            FIELD_IS_GOV_ENT_CID), new PlanOfficeSolutionReviewerPluginValue()),

    /**
     * 是否需要网络总工审核 + 需要技术交付部/网络处审核
     */
    NEED_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL(Lists.newArrayList(TIME_CONFLICT, COMPONENT_OPERATION_LEVEL_COMPONENT_CID),
            new PlanNeedNetworkChiefApprovalPluginValue()),

    /**
     * 网络处总工审核人获取
     */
    NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL(Lists.newArrayList(FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID, FIELD_PRODUCT_CID,
            ACCN_TYPE_CID), new PlanNetworkChiefApprovalPluginValue()),

    /**
     * 网元清单解析生成批次概要插件
     */
     NE_LIST_TO_BATCH_SUMMARY(Lists.newArrayList(NE_LIST_FILE_PROPERTY_KEY), new NeListParseToBatchSummaryPlugin()),

    /**
     * 批次概要批次号值变化，新增操作对象插件
     */
    BATCH_SUMMARY_BATCH_NO_NEW_OPERATION_OBJ(Lists.newArrayList(BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY),
            new BatchNoChangePluginValue()),

    /**
     * 操作人员列表数据检查
     */
    OPERATOR_CHECK_PEOPLE(Lists.newArrayList(OPERATOR_ROLE_KEY, OPERATOR_NAME_KEY, FIELD_OPERATOR_BATCH_NO_KEY,
            OPERATOR_IS_REMOTE, FIELD_ORGANIZATION_CID), new PlanOperatorCheckPluginValue()),

    /**
     * 员工-归属部门插件
     */
    EMPLOYEE_DEPARTMENT(Lists.newArrayList(OPERATOR_NAME_KEY), new EmployeeDepartmentPluginValue()),

    /**
     * 人员属性列是否展示，人员属性单元格，是否可编辑
     */
    RESPONSIBLE_DEPT_CHANGE(Lists.newArrayList(FIELD_ORGANIZATION_CID), new ResponsibleDeptChangePlugin()),

    /**
     * 操作对象清空插件
     */
    OPERATION_OBJECT_CLEAR(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_PRODUCT_CID), new OperationObjectClearPlugin()),

    /**
     * 批次概要时间给批次对象时间赋值
     */
    batch_start_date(Lists.newArrayList(BatchSummaryFieldConsts.BATCH_NO,BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME,
            BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME), new PlanBatchSummaryDatePluginValue()),

    /**
     * 技术交付部/网络处审核人插件
     */
    TECHNICAL_REVIEWER(Lists.newArrayList(FIELD_IS_UPGRADE_TECHNOLOGY_CID, FIELD_PRODUCT_CID, FIELD_ORGANIZATION_CID,
            FIELD_IS_GOV_ENT_CID, ACCN_TYPE_CID, FIELD_OPERATION_TYPE_CID, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY,
            LOGICAL_NE_PROPERTY_KEY), new PlanUpgradeTechnologyPluginValue());

    // valueChange插件的cid要配置实体字段CID
    private final List<String> propIds;

    private final ValueChangeBaseFormPlugin valueChangeBaseFormPlugin;

    public static List<ValueChangeBaseFormPlugin> getValueChangedEventPlugins(String propId) {
        List<ValueChangeBaseFormPlugin> valueChangeBaseFormPlugins = new ArrayList<>();
        for (PlanValueChangePluginEnum pluginEnum : PlanValueChangePluginEnum.values()) {
            if (pluginEnum.getPropIds().contains(propId)) {
                valueChangeBaseFormPlugins.add(pluginEnum.getValueChangeBaseFormPlugin());
            }
        }
        return valueChangeBaseFormPlugins;
    }
}
