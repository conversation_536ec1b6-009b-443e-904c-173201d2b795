package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/30 上午10:33
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum UserTypeEnum {

    /**
     * 外部
     */
    EXTERNAL("0"),

    /**
     * 内部
     */
    INTERNAL("1"),

    ;

    private final String type;

    public static UserTypeEnum getUserType(String userType) {
        for (UserTypeEnum userTypeEnum : UserTypeEnum.values()) {
            if (userTypeEnum.getType().equals(userType)) {
                return userTypeEnum;
            }
        }
        return null;
    }
}
