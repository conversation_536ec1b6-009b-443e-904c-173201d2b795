package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.DEFAULT_APPROVER_KEY;

/**
 * 2.4.7 多模从属产品负责人审核
 *
 * <AUTHOR>
 * @create 2024/7/17 下午3:36
 * 根据产品+组织精确查询审批配置记录，则从网服部配置获取该产品对应记录【网络部审核人】【网络部审核组】作为整体审核组（以下代表处、片区、营销单位基于申请单获取；其他条件不匹配，即运营商和操作类型不能有值）
 * 第一优先：匹配【产品小类】+【代表处】+【是否政企】；当代表处属于国际（即属于工程服务经营部且不属于工程服务国内部），则【是否政企】取值不考虑
 * 第二优先：匹配【产品小类】+【片区】+【是否政企】；当代表处属于国际（即属于工程服务经营部且不属于工程服务国内部），则【是否政企】取值不考虑
 * 第三优先：匹配【产品小类】+【营销】+【是否政企】；当代表处属于国际（即属于工程服务经营部且不属于工程服务国内部），则【是否政企】取值不考虑
 * 如果第1点为空，则读取2.4.3【技术交付部/网络处审核】中的网服部审核人和审核组作为整体审核组（若需要上升至网服部审核）
 */
public class MultimodeProductHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        Map<String, String> ret = getMultiModeProductApprovers(changeOrder);
        return Lists.newArrayList(JsonUtils.toJsonString(ret));
    }

    public Map<String, String> getMultiModeProductApprovers(ChangeOrder changeOrder) {
        List<String> persons = new ArrayList<>();
        Map<String, String> ret = MapUtils.newHashMap();
        String responsibleDept = changeOrder.getResponsibleDept();

        List<TextValuePair> multiModeProductsPairs = changeOrder.getMultiProductMode();
        if (CollectionUtils.isEmpty(multiModeProductsPairs)) {
            return null;
        }

        for (TextValuePair multi : multiModeProductsPairs) {
            //  产品小类ID path
            String prodectId = multi.getValue();
            String productName = multi.getTextByLanguage(RequestHeaderUtils.getLangId());

            ApproverConfiguration queryParam = new ApproverConfiguration();
            queryParam.setApprovalNode(ApprovalTypeEnum.NETWORK_DEPT);
            queryParam.setProdTeam(ProductUtils.getTeam(prodectId));
            queryParam.setProdLine(ProductUtils.getLine(prodectId));
            queryParam.setProdMainCategory(ProductUtils.getMain(prodectId));
            queryParam.setProdSubCategory(prodectId);
            queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
            queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));
            queryParam.setResponsibleDeptId(changeOrder.getResponsibleDept());
            // 当代表处属于国内 考虑【是否政企】
            if (DeptTypeEnum.INNER == ResponsibleUtils.getDeptType(responsibleDept)) {
                queryParam.setIsGov(changeOrder.getIsGovEnt());
            }
            persons = ApproverConfigAbility.getApprovalPersons(queryParam, null);
            if (CollectionUtils.isEmpty(persons)) {
                queryParam.setResponsibleDeptId(null);
                persons = ApproverConfigAbility.getApprovalPersons(queryParam, null);
            }
            if (CollectionUtils.isEmpty(persons)) {
                queryParam.setOrganizationRegion(null);
                persons = ApproverConfigAbility.getApprovalPersons(queryParam, null);
            }
            if (CollectionUtils.isEmpty(persons)) {
                persons = new ArrayList<>();
                persons.addAll(ApproverConfigAbility.getPersonUuidList(changeOrder.getNetWorkServiceDeptApprover()));
                persons.addAll(ApproverConfigAbility.getPersonUuidList(changeOrder.getNetWorkServiceDeptApproverTeam()));
            }

            if (CollectionUtils.isEmpty(persons)) {
                persons.add(ConfigHelper.get(DEFAULT_APPROVER_KEY));
            }

            ret.put(productName, persons.stream().distinct().collect(Collectors.joining(",")));
        }
        return ret;
    }
}
