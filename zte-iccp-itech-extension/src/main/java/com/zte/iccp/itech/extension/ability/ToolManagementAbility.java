package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.model.ToolManagementAttribute;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> 10347404
 * @since 2024/06/03
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ToolManagementAbility {
    public static List<ToolManagementAttribute> query(List<String> fieldList,
                                                      List<IFilter> conditionFilters) {
        if (CollectionUtils.isEmpty(conditionFilters)) {
            return Lists.newArrayList();
        }
        return QueryDataHelper.query(ToolManagementAttribute.class, fieldList, conditionFilters);
    }
}
