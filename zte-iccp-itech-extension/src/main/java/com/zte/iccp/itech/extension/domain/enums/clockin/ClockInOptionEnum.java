package com.zte.iccp.itech.extension.domain.enums.clockin;

import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.model.base.LookupValueEnum;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionTypeEnum.*;
import static com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum.ON_DUTY;
import static com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum.OPERATION;

/**
 * <AUTHOR>
 * @since 2024/09/05
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ClockInOptionEnum implements LookupValueEnum {
    /** 操作取消 */
    CANCEL(MANUAL_CANCEL, true),
    /** 操作准备开始 */
    PREPARE_START(STATE_END),
    /** 准备完成，开始操作 */
    PREPARE_END_OPERATION_START(STATE_END),
    /** 全部完成，开始业务验证 */
    OPERATION_COMPLETED_TEST_START(STATE_END),
    /** 部分完成，开始业务验证 */
    OPERATION_PARTIALLY_TEST_START(STATE_END),
    /** 操作异常，正在处理 */
    OPERATION_ERROR_HANDLING(PROCESSING, true),
    /** 操作未成功，二次操作 */
    OPERATION_FAILED_REPEAT(PROCESSING, true),
    /** 操作未成功，回退完成 */
    OPERATION_FAILED_ROLLBACK_END(STATE_END, true, true),
    /** 操作未成功，未回退 */
    OPERATION_FAILED_NO_ROLLBACK(STATE_END, true),
    /** 测试正常，操作结束 */
    TEST_SUCCESS_OPERATION_END(STATE_END),
    /** 测试异常，正在处理 */
    TEST_FAILED_HANDLING(PROCESSING, true),
    /** 测试异常，未回退 */
    TEST_FAILED_NO_ROLLBACK(STATE_END, true),
    /** 测试异常，完成回退 */
    TEST_FAILED_ROLLBACK_END(STATE_END, true, true),
    /** 指标正常，继续值守 */
    INDICATOR_NORMAL_ON_DUTY_CONTINUE(PROCESSING),
    /** 指标异常，正在处理 */
    INDICATOR_ERROR_HANDLING(PROCESSING, true),
    /** 指标异常，回退完成 */
    INDICATOR_ERROR_ROLLBACK_END(PROCESSING, true, true),
    /** 指标异常，未回退 */
    INDICATOR_ERROR_NO_ROLLBACK(PROCESSING, true),
    /** 指标正常，值守结束 */
    INDICATOR_NORMAL_ON_DUTY_END(STATE_END),
    ;

    private static final Map<ClockInTaskTypeEnum, Set<ClockInOptionEnum>> INIT_OPTIONS = MapUtils.newHashMap(
            // 操作打卡
            OPERATION, Collections.unmodifiableSet(Sets.newHashSet(
                    CANCEL,
                    PREPARE_START)),
            // 值守打卡
            ON_DUTY, Collections.unmodifiableSet(Sets.newHashSet(
                    INDICATOR_NORMAL_ON_DUTY_END,
                    INDICATOR_NORMAL_ON_DUTY_CONTINUE,
                    INDICATOR_ERROR_HANDLING)));

    private static final Map<ClockInOptionEnum, Set<ClockInOptionEnum>> NEXT_STEP = create(
            // 准备中
            PREPARE_START, Collections.unmodifiableSet(Sets.newHashSet(
                    CANCEL,
                    PREPARE_END_OPERATION_START)),
            // 执行中
            PREPARE_END_OPERATION_START, Collections.unmodifiableSet(Sets.newHashSet(
                    CANCEL,
                    OPERATION_COMPLETED_TEST_START,
                    OPERATION_PARTIALLY_TEST_START,
                    OPERATION_ERROR_HANDLING,
                    OPERATION_FAILED_REPEAT)),
            OPERATION_ERROR_HANDLING, Collections.unmodifiableSet(Sets.newHashSet(
                    OPERATION_COMPLETED_TEST_START,
                    OPERATION_PARTIALLY_TEST_START,
                    OPERATION_FAILED_REPEAT,
                    OPERATION_FAILED_NO_ROLLBACK,
                    OPERATION_FAILED_ROLLBACK_END)),
            OPERATION_FAILED_REPEAT, Collections.unmodifiableSet(Sets.newHashSet(
                    OPERATION_COMPLETED_TEST_START,
                    OPERATION_PARTIALLY_TEST_START,
                    OPERATION_ERROR_HANDLING,
                    OPERATION_FAILED_NO_ROLLBACK,
                    OPERATION_FAILED_ROLLBACK_END)),
            // 测试中
            OPERATION_COMPLETED_TEST_START,
            OPERATION_PARTIALLY_TEST_START,
            OPERATION_FAILED_NO_ROLLBACK,
            OPERATION_FAILED_ROLLBACK_END, Collections.unmodifiableSet(Sets.newHashSet(
                    TEST_SUCCESS_OPERATION_END,
                    TEST_FAILED_HANDLING)),
            TEST_FAILED_HANDLING, Collections.unmodifiableSet(Sets.newHashSet(
                    TEST_SUCCESS_OPERATION_END,
                    TEST_FAILED_NO_ROLLBACK,
                    TEST_FAILED_ROLLBACK_END)),
            // 值守中
            INDICATOR_NORMAL_ON_DUTY_CONTINUE, Collections.unmodifiableSet(Sets.newHashSet(
                    INDICATOR_NORMAL_ON_DUTY_END,
                    INDICATOR_NORMAL_ON_DUTY_CONTINUE,
                    INDICATOR_ERROR_HANDLING)),
            INDICATOR_ERROR_HANDLING, Collections.unmodifiableSet(Sets.newHashSet(
                    INDICATOR_NORMAL_ON_DUTY_CONTINUE,
                    INDICATOR_ERROR_NO_ROLLBACK,
                    INDICATOR_ERROR_ROLLBACK_END)),
            INDICATOR_ERROR_NO_ROLLBACK,
            INDICATOR_ERROR_ROLLBACK_END, Collections.unmodifiableSet(Sets.newHashSet(
                    INDICATOR_ERROR_HANDLING,
                    INDICATOR_NORMAL_ON_DUTY_END,
                    INDICATOR_NORMAL_ON_DUTY_CONTINUE)));

    private final ClockInOptionTypeEnum optionType;

    private final boolean isException;

    private final boolean isRollback;

    ClockInOptionEnum(ClockInOptionTypeEnum optionType) {
        this.optionType = optionType;
        isException = false;
        isRollback = false;
    }

    ClockInOptionEnum(ClockInOptionTypeEnum optionType, boolean isException) {
        this.optionType = optionType;
        this.isException = isException;
        isRollback = false;
    }

    public static Set<ClockInOptionEnum> initOptions(ClockInTaskTypeEnum taskType) {
        return new HashSet<>(INIT_OPTIONS.getOrDefault(taskType, Collections.emptySet()));
    }

    public static Set<ClockInOptionEnum> nextStep(ClockInOptionEnum option) {
        return new HashSet<>(NEXT_STEP.getOrDefault(option, Collections.emptySet()));
    }

    @Override
    public String getLookupCode() {
        return name();
    }

    private static Map<ClockInOptionEnum, Set<ClockInOptionEnum>> create(Object... args) {
        Map<ClockInOptionEnum, Set<ClockInOptionEnum>> result = MapUtils.newHashMap();

        Set<ClockInOptionEnum> prevOptions = null;
        for (int i = args.length - 1; i >= 0; --i) {
            if (args[i] instanceof Set) {
                //noinspection unchecked
                prevOptions = (Set<ClockInOptionEnum>) args[i];
                continue;
            }

            ClockInOptionEnum option = (ClockInOptionEnum) args[i];
            result.put(option, prevOptions);
        }

        return result;
    }
}
