package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.AiGenerateDocCallbackFieldConsts.*;

/**
 * <AUTHOR>
 * @date 2024/6/13 下午5:15
 */
@Setter
@Getter
@BaseEntity.Info("ai_generate_doc_callback")
public class AiGenerateDocCallback extends BaseEntity {

    @JsonProperty(REQUEST_ID)
    private String requestId;

    @JsonProperty(PAGE_ID)
    private String pageId;

    @JsonProperty(PAGE_SESSION_ID)
    private String pageSessionId;

    @JsonProperty(TYPE)
    private String type;

    @JsonProperty(TAG_DOC_ID)
    private String tagDocId;

    @JsonProperty(DOC_ID)
    private String docId;

    @JsonProperty(FILE_NAME_SUFFIX)
    private String fileNameSuffix;

    @JsonProperty(DOC_NAME)
    private String docName;

    @JsonProperty(STATUS)
    private String status;

    @JsonProperty(LOG)
    private String log;
}
