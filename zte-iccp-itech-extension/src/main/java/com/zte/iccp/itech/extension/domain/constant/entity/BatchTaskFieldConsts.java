package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.RANGE_END_SUFFIX;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.RANGE_START_SUFFIX;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BatchTaskFieldConsts {

    /**
     * 批次任务名称
     */
    public static final String BATCH_NAME = "batch_name";

    /**
     * 批次号
     */
    public static final String BATCH_NO = "batch_no";

    /**
     * 批次编码
     */
    public static final String BATCH_CODE = "batch_code";

    /**
     * 是否紧急操作
     */
    public static final String URGENT_FLAG = "is_urgent";

    /**
     * 紧急操作标识
     */
    public static final String EMERGENCY_OPERATION_FLAG = "emergency_operation_flag";

    /**
     * 紧急操作附件
     */
    public static final String IS_URGENT_FILE = "is_urgent_file";

    /**
     * 是否变更紧急操作
     */
    public static final String URGENT_FLAG_TOO = "is_urgent_too";

    /**
     * 客户授权凭证
     */
    public static final String CUSTOMER_VOUCHER_FILE = "customer_voucher_file";

    /**
     * 通告说明
     */
    public static final String NOTIFICATION_DESC = "notification_desc";

    /**
     * 是否封网管控期操作
     */
    public static final String CONTROL_PERIOD_FLAG = "is_control_period";

    /**
     * 封网管控期操作标识
     */
    public static final String NETWORK_SEALING_OPERATION_FLAG = "network_sealing_operation_flag";


    /**
     * 封网管控期操作原因
     */
    public static final String CONTROL_PERIOD_REASON = "control_period_reason";

    /**
     * 封网管控期操作原因附件
     */
    public static final String CONTROL_PERIOD_FILE = "control_period_file";

    /**
     * 操作预计投入人天
     */
    public static final String PLAN_OPERATION_DAY = "plan_operation_day";

    /**
     * 涉及人员变更标识
     */
    public static final String PERSON_CHANGE_FLAG = "is_change_person";

    /**
     * 单据状态
     */
    public static final String STATUS = "status";

    /**
     * 当前处理人
     */
    public static final String CURRENT_PROCESSOR = "current_processor";

    /** 授权文件 */
    public static final String GRANT_FILE = "grant_file";

    /** 超级授权文件 */
    public static final String SA_GRANT_FILE = "sa_grant_file";

    /**
     * 是否生成超级授权文件
     */
    public static final String NEED_SP_GRANT_FILE =  "need_sp_grant_file";

    /**
     * 操作类型
     */
    public static final String OPERATION_TYPE = "operation_type";

    /**
     * 操作类型分组
     */
    public static final String OPERATION_TYPE_GROUP = "operation_type_group";

    /**
     * 紧急操作原因
     */
    public static final String URGENT_RESAON =  "urgent_resaon";

    /**
     * 内部操作方案
     */
    public static final String OPERATION_PLAN_FILE =  "operation_plan_file";

    /**
     * 客户操作方案
     */
    public static final String CUSTOMER_OPERATION_SOLUTION =  "customer_operation_solution";

    /**
     * 附件
     */
    public static final String ATTACHMENT =  "attachment";

    /**
     * 是否需要操作步骤打卡
     */
    public static final String IS_CHECK =  "is_check";

    /**
     * 模型包
     */
    public static final String MODEL_PACKAGE =  "model_package";

    /**
     * 备注说明
     */
    public static final String REMARKS =  "remarks";

    /**
     * 网络变更单ID
     */
    public static final String CHANGE_ORDER_ID =  "change_order_id";

    /** 网络变更单 - 责任单位 */
    public static final String ORGANIZATION_ID = "organization_id";

    /**
     * 批次操作账号
     */
    public static final String BATCH_OPERATION_ACCOUNT =  "batch_operation_account";

    /**
     * 网络变更单是否走行政领导审批
     */
    public static final String IS_CHANGE_ADMIN_APPROVAL =  "is_change_admin_approval";

    /**
     * 网络变更单是否走远程中心审批
     */
    public static final String IS_CHANGE_REMOTE_APPROVAL =  "is_change_remote_approval";

    /**
     * 网络变更单设置计划操作开始时间--审核通过后刷新
     */
    public static final String CHANGE_OPERATION_TIME_START = "change_operation_time_start";

    /**
     * 网络变更单设置计划操作结束时间--审核通过后刷新
     */
    public static final String CHANGE_OPERATION_TIME_END = "change_operation_time_end";

    /**
     * 审批是否需要远程中心
     */
    public static final String IS_NEED_REMOTE_APPROVAL = "is_need_remote_approval";

    /**
     * 网络变更单-是否上传网元清单
     */
    public static final String IS_CHANGE_UPLOAD_ELEMENT = "is_change_upload_element";

    /**
     * 当前状态
     */
    public static final String CURRENT_STATUS = "current_status";

    /**
     * 挂起时间
     */
    public static final String SUSPENDED_TIME = "suspended_time";

    /**
     * 操作对象列表
     */
    public static final String OPERATION_OBJECT_TABLE = "operationObjectTable";

    /**
     * 审批状态
     */
    public static final String APPROVAL_STATUS = "approval_status";

    /**
     * 当前状态名称
     */
    public static final String CURRENT_STATUS_NAME = "current_status_name";

    /**
     * 远程中心页签
     */
    public static final String REMOTE_CENTER_PAGE = "remote_center_page";

    /**
     * 审核结果（远程中心负责人）
     */
    public static final String APPROVE_RESULT_TD_NET_DEPT_APP_SOLU = "approve_result_td_net_dept_app_solu";

    /**
     * 审核意见（远程中心负责人）
     */
    public static final String APPROVE_OPINION_TD_NET_DEPT_APP_SOLU = "approve_opinion_td_net_dept_app_solu";

    /**
     * 审核人（远程中心负责人）
     */
    public static final String APPROVED_BY_TD_NET_DEPT_APP_SOLU = "approved_by_td_net_dept_app_solu";

    /**
     * 审核时间（远程中心负责人）
     */
    public static final String APPROVED_TIME_TD_NET_DEPT_APP_SOLU = "approved_time_td_net_dept_app_solu";

    /**
     * 变更人员-代表处产品科长
     */
    public static final String BATCH_RES_SECTION_PAGE = "batch_res_app";

    /**
     * 审核结果（变更人员-代表处产品科长）
     */
    public static final String APPROVE_RESULT_REP_PROD_CHIEF = "approve_result_rep_prod_chief";

    /**
     * 审核意见（变更人员-代表处产品科长）
     */
    public static final String APPROVE_OPINION_REP_PROD_CHIEF = "approve_opinion_rep_prod_chief";

    /**
     * 审核人（变更人员-代表处产品科长）
     */
    public static final String APPROVED_BY_REP_PROD_CHIEF = "approved_by_rep_prod_chief";

    /**
     * 审核时间（变更人员-代表处产品科长）
     */
    public static final String APPROVED_TIME_REP_PROD_CHIEF = "approved_time_rep_prod_chief";

    /**
     * 行政审批_代表处产品科长
     */
    public static final String BATCH_SECTION_PAGE = "batch_section_page";

    /**
     * 审核结果（行政-代表处产品科长）
     */
    public static final String APPROVE_RESULT_ADMIN_REP_PROD_CHIEF = "approve_result_admin_rep_prod_chief";

    /**
     * 审核意见（行政-代表处产品科长）
     */
    public static final String APPROVE_OPINION_ADMIN_REP_PROD_CHIEF = "approve_opinion_admin_rep_prod_chief";

    /**
     * 审核人（行政-代表处产品科长）
     */
    public static final String APPROVED_BY_ADMIN_REP_PROD_CHIEF = "approved_by_admin_rep_prod_chief";

    /**
     * 审核时间（行政-代表处产品科长）
     */
    public static final String APPROVED_TIME_ADMIN_REP_PROD_CHIEF = "approved_time_admin_rep_prod_chief";

    /**
     * 行政审批_网络处产品总监
     */
    public static final String BATCH_DIRECTOR_PAGE = "batch_director_page";

    /**
     * 审核结果（行政-网络处产品总监）
     */
    public static final String APPROVE_RESULT_ADMIN_NET_PROD_DIRECTOR = "approve_result_admin_net_prod_director";

    /**
     * 审核意见（行政-网络处产品总监）
     */
    public static final String APPROVE_OPINION_ADMIN_NET_PROD_DIRECTOR = "approve_opinion_admin_net_prod_director";

    /**
     * 审核人（行政-网络处产品总监）
     */
    public static final String APPROVED_BY_ADMIN_NET_PROD_DIRECTOR = "approved_by_admin_net_prod_director";

    /**
     * 审核时间（行政-网络处产品总监）
     */
    public static final String APPROVED_TIME_ADMIN_NET_PROD_DIRECTOR = "approved_time_admin_net_prod_director";

    /**
     * 行政审批_办事处副经理
     */
    public static final String BATCH_DEPUTY_PAGE = "batch_deputy_page";

    /**
     * 审核结果（行政-办事处副经理）
     */
    public static final String APPROVE_RESULT_ADMIN_REP_DEPUTY_MANAGER = "approve_result_admin_rep_deputy_manager";

    /**
     * 审核意见（行政-办事处副经理）
     */
    public static final String APPROVE_OPINION_ADMIN_REP_DEPUTY_MANAGER = "approve_opinion_admin_rep_deputy_manager";

    /**
     * 审核人（行政-办事处副经理）
     */
    public static final String APPROVED_BY_ADMIN_REP_DEPUTY_MANAGER = "approved_by_admin_rep_deputy_manager";

    /**
     * 审核时间（行政-办事处副经理）
     */
    public static final String APPROVED_TIME_ADMIN_REP_DEPUTY_MANAGER = "approved_time_rep_deputy_manager";

    /**
     * 行政审批_网络处主管经理
     */
    public static final String BATCH_MANAGER_PAGE = "batch_manager_page";

    /**
     * 审核结果（行政-网络处主管经理）
     */
    public static final String APPROVE_RESULT_ADMIN_NET_DEPT_MNG = "approve_result_admin_net_dept_mng";

    /**
     * 审核意见（行政-网络处主管经理）
     */
    public static final String APPROVE_OPINION_ADMIN_NET_DEPT_MNG = "approve_opinion_admin_net_dept_mng";

    /**
     * 审核人（行政-网络处主管经理）
     */
    public static final String APPROVED_BY_ADMIN_NET_DEPT_MNG = "approved_by_admin_net_dept_mng";

    /**
     * 审核时间（行政-网络处主管经理）
     */
    public static final String APPROVED_TIME_ADMIN_NET_DEPT_MNG = "approved_time_admin_net_dept_mng";

    /**
     * 行政审批_网服部四层
     */
    public static final String BATCH_NETLV4_PAGE = "batch_netlv4_page";

    /**
     * 审核结果（行政-网服部四层）
     */
    public static final String APPROVE_RESULT_ADMIN_NET_SERVCIE_LV4 = "approve_result_admin_net_servcie_lv4";

    /**
     * 审核意见（行政-网服部四层）
     */
    public static final String APPROVE_OPINION_ADMIN_NET_SERVCIE_LV4 = "approve_opinion_admin_net_servcie_lv4";

    /**
     * 审核人（行政-网服部四层）
     */
    public static final String APPROVED_BY_ADMIN_NET_SERVCIE_LV4 = "approved_by_admin_net_servcie_lv4";

    /**
     * 审核时间（行政-网服部四层）
     */
    public static final String APPROVED_TIME_ADMIN_NET_SERVCIE_LV4 = "approved_time_admin_net_servcie_lv4";

    /**
     * 行政审批_网服部三层
     */
    public static final String BATCH_NETLV3_PAGE = "batch_netlv3_page";

    /**
     * 审核结果（行政-网服部三层）
     */
    public static final String APPROVE_RESULT_ADMIN_NET_SERVCIE_LV3 = "approve_result_admin_net_servcie_lv3";

    /**
     * 审核意见（行政-网服部三层）
     */
    public static final String APPROVE_OPINION_ADMIN_NET_SERVCIE_LV3 = "approve_opinion_admin_net_servcie_lv3";

    /**
     * 审核人（行政-网服部三层）
     */
    public static final String APPROVED_BY_ADMIN_NET_SERVCIE_LV3 = "approved_by_admin_net_servcie_lv3";

    /**
     * 审核时间（行政-网服部三层）
     */
    public static final String APPROVED_TIME_ADMIN_NET_SERVCIE_LV3 = "approved_time_admin_net_servcie_lv3";

    /**
     * 行政审批_研发三层
     */
    public static final String BATCH_DEP_LV3_PAGE = "batch_dep_lv3_page";

    /**
     * 审核结果（行政-研发三层）
     */
    public static final String APPROVE_RESULT_ADMIN_RD_DEPT_LV3 = "approve_result_admin_rd_dept_lv3";

    /**
     * 审核意见（行政-研发三层）
     */
    public static final String APPROVE_OPINION_ADMIN_RD_DEPT_LV3 = "approve_opinion_admin_rd_dept_lv3";

    /**
     * 审核人（行政-研发三层）
     */
    public static final String APPROVED_BY_ADMIN_RD_DEPT_LV3 = "approved_by_admin_rd_dept_lv3";

    /**
     * 审核时间（行政-研发三层）
     */
    public static final String APPROVED_TIME_ADMIN_RD_DEPT_LV3 = "approved_time_admin_rd_dept_lv3";

    /**
     * 行政审批_工服三部部长
     */
    public static final String BATCH_ENG_SERVICE3_PAGE = "batch_eng_service3_page";

    /**
     * 审核结果（行政-工服三部部长）
     */
    public static final String APPROVE_RESULT_ADMIN_ENG_SERVICE3_LEADER = "approve_result_admin_eng_service3_leader";

    /**
     * 审核意见 实体唯一标识（行政-工服三部部长）
     */
    public static final String APPROVE_OPINION_ADMIN_ENG_SERVICE3_LEADER = "approve_opinion_admin_eng_service3_leader";

    /**
     * 审核意见 实体字段名（行政-工服三部部长）
     */
    public static final String APPROVE_OPINION_ADMIN_ENG_SERVICE3_LEADER_FIELD_NAME = "approve_opinion_admin_eng_service3_leade";

    /**
     * 审核人（行政-工服三部部长）
     */
    public static final String APPROVED_BY_ADMIN_ENG_SERVICE3_LEADER = "approved_by_admin_eng_service3_leader";

    /**
     * 审核时间（行政-工服三部部长）
     */
    public static final String APPROVED_TIME_ADMIN_ENG_SERVICE3_LEADER = "approved_time_admin_eng_service3_leader";

    /**
     * 行政审批_电信服务总监
     */
    public static final String ADMIN_DIR_TELE_SER_PAGE = "admin_dir_tele_ser";

    /**
     * 审核结果 实体唯一标识（行政-电信服务总监）
     */
    public static final String APPROVE_RESULT_ADMIN_DIR_TELE_SER_DIRECTOR = "approve_result_admin_dir_tele_ser_director";

    /**
     * 审核意见 实体唯一标识（行政-电信服务总监）
     */
    public static final String APPROVE_OPINION_ADMIN_DIR_TELE_SER_DIRECTOR = "approve_opinion_admin_dir_tele_ser_director";

    /**
     * 审核人 实体唯一标识和字段名一致（行政-电信服务总监）
     */
    public static final String APPROVED_BY_ADMIN_DIR_TELE_SER_DIRECTOR = "approved_by_admin_dir_tele_ser_director";

    /**
     * 审核时间 实体唯一标识（行政-电信服务总监）
     */
    public static final String APPROVED_TIME_ADMIN_DIR_TELE_SER_DIRECTOR = "approved_time_admin_dir_tele_ser_director";


    /**
     * 审核结果 实体字段名（行政-电信服务总监）
     */
    public static final String APPROVE_RESULT_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME = "approve_result_admin_dir_tele_ser_direct";

    /**
     * 审核意见 实体字段名（行政-电信服务总监）
     */
    public static final String APPROVE_OPINION_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME = "approve_opinion_admin_dir_tele_ser_direc";

    /**
     * 审核时间 实体字段名（行政-电信服务总监）
     */
    public static final String APPROVED_TIME_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME = "approved_time_admin_dir_tele_ser_directo";

    /**
     * 国际审批
     */
    public static final String INTERNATION_APPROVAL = "internation_approval";

    /**
     * 国际审批page
     */
    public static final String INTERNATION_APPROVAL_PAGE = "internation_approval_page";


    /**
     * 办事处PD审核节点
     */
    public static final String OFFICE_PD_PAGE = "office_pd_page";

    /**
     * 审核结果（办事处PD审核）
     */
    public static final String APPROVE_RESULT_OFFICE_PD = "approve_result_office_pd";

    /**
     * 审核意见（办事处PD审核）
     */
    public static final String APPROVE_OPINION_OFFICE_PD = "approve_opinion_office_pd";

    /**
     * 审核人（办事处PD审核）
     */
    public static final String APPROVED_BY_OFFICE_PD = "approved_by_office_pd";

    /**
     * 审核时间（办事处PD审核）
     */
    public static final String APPROVED_TIME_OFFICE_PD = "approved_time_office_pd";

    /**
     * 操作计划审核审核节点
     */
    public static final String BATCH_PLAN_PAGE = "batch_plan_page";

    /**
     * 审核结果（操作计划审核）
     */
    public static final String APPROVE_RESULT_OPERATION_PLAN = "approve_result_operation_plan";

    /**
     * 审核意见（操作计划审核）
     */
    public static final String APPROVE_OPINION_OPERATION_PLAN = "approve_opinion_operation_plan";

    /**
     * 审核人（操作计划审核）
     */
    public static final String APPROVED_BY_OPERATION_PLAN = "approved_by_operation_plan";

    /**
     * 操作结束时间（操作计划审核）
     */
    public static final String APPROVED_TIME_OPERATION_PLAN = "approved_time_operation_plan";

    /**
     * 审核意见（网络责任人确认）
     */
    public static final String APPROVAL_DESCRIPTION = "approval_description";

    /**
     * 审核结果（网络责任人确认）
     */
    public static final String APPROVAL_RESULT = "approve_result";



    /**
     * 步骤条
     */
    public static final String STEP = "step";

    /**
     * 通告信息 tab
     */
    public static final String TAB_INFO = "tab_info";

    /**
     * 通告审核TAB
     */
    public static final String TAB_APPROVAL = "tab_approval";

    /**
     * 反馈操作结果TAB
     */
    public static final String TAB_RESULT = "tab_result";

    /**
     * 审核确认TAB
     */
    public static final String TAB_CONFIRM = "tab_confirm";

    /**
     * 是否操作取消
     */
    public static final String IS_CANCELED = "is_canceled";

    /** 是否已回滚 */
    public static final String IS_ROLLBACK_DONE = "is_returned";

    /** 一次操作/升级完成 */
    public static final String IS_ONE_TIME_SUCCEED = "is_first_operation";

    /** 实际操作开始时间 */
    public static final String ACTUAL_OPERATION_START_TIME = "fact_operation_date" + RANGE_START_SUFFIX;

    /** 实际操作结束时间 */
    public static final String ACTUAL_OPERATION_END_TIME = "fact_operation_date" + RANGE_END_SUFFIX;

    /** 测试完成时间 */
    public static final String TEST_FINISH_TIME = "test_finish_date";

    /** 实际业务中断时长 */
    public static final String FACT_INTERRUPT_TIME = "fact_interrupt_time";

    /**
     * 邮件抄送(操作结果确认)   批次任务拓展实体字段
     */
    public static final String APPROVAL_EMAIL = "approval_email";

    /**
     * 邮件抄送(反馈操作结果拓展表字段)
     */
    public static final String RESULT_MAIL = "result_mail";

    /**
     * 邮件抄送(发布通告)
     */
    public static final String EMAIL_CC = "email_cc";

    /**
     * 操作方案描述
     */
    public static final String OPERATION_PLAN_DESC = "operation_desc";

    /**
     * 时间冲突
     */
    public static final String TIME_CONFLICT = "time_conflict";

    /**
     * 确认副本
     */
    public static final String CONFIRM_COPY = "confirm_copy";

    /**
     * 发布通告副本
     */
    public static final String NOTICE_COPY = "notice_copy";

    /**
     * 操作计划变更说明-中文
     */
    public static final String CHANGE_PLAN_DESC_ZH = "change_plan_desc_zh";

    /**
     * 操作计划变更说明-英文
     */
    public static final String CHANGE_PLAN_DESC_US = "change_plan_desc_us";

    /** 打卡阶段 */
    public static final String CLOCK_IN_STATE = "clock_in_state";

    /**
     * 审批次数
     */
    public static final String APPROVAL_NUM = "approval_num";

    /**
     * 申请客户授权凭证是否点击取消标志位
     */
    public static final String HAS_CANCEL_OPERATION = "has_cancel_operation";

    /**
     * 申请客户授权凭证是否点击取消标志位
     */
    public static final String HAS_SAVE_OPERATION = "has_save_operation";

    /**
     * 操作账号（申请客户授权凭证）
     */
    public static final String BATCH_OPERATION_ACCOUNT_APPLY = "batch_operation_account_apply";

    /**
     * 操作开始时间（申请客户授权凭证）
     */
    public static final String OPERATION_START_TIME_APPLY = "operation_start_time_apply";

    /**
     * 操作结束时间（申请客户授权凭证）
     */
    public static final String OPERATION_END_TIME_APPLY = "operation_end_time_apply";

    /** 操作结果 */
    public static final String OPERATION_RESULT = "operation_result";

    /** 操作总结 */
    public static final String BATCH_OPERATION_SUMMARY = "operation_summary";

    /** 备注说明 */
    public static final String BATCH_RESULT_DESC = "result_desc";

    /** 操作时间(记录最后一次挂起、取消、发布通告) */
    public static final String OPERATION_UPDATE_TIME = "operation_update_time";

    /** 驳回节点CODE */
    public static final String REJECT_NODE = "reject_node";

    /** 驳回时审核结果 */
    public static final String RESULT_REJECTION = "result_rejection";

    /** 驳回时审核意见 */
    public static final String AUDIT_REJECTION = "audit_rejection";

    /** 驳回时审核人 */
    public static final String PERSON_REJECTION = "person_rejection";

    /** 驳回时审核意见附件 */
    public static final String ATTACHMENT_REJECTION = "attachment_rejection";

    /**
     * 每次提交时间
     */
    public static final String SUBMIT_TIME = "submit_time";

    /*
     *变更日志提示
     */
    public static final String CHANGE_PLAN_DESC = "change_plan_desc";

    /** 操作变更说明（操作取消审核） */
    public static final String OC_OPERATION_CHANGE_DESC = "oc_operation_change_desc";

    /** 审核结果（操作取消审核） */
    public static final String OC_APPROVE_RESULT = "oc_approve_result";

    /** 审核意见（操作取消审核）*/
    public static final String OC_REVIEW_OPINION = "oc_review_opinion";

    /** 邮件抄送（操作取消审核） */
    public static final String OC_EMAIL_CC = "oc_email_cc";

    /** 操作变更说明 */
    public static final String OPERATION_CHANGE_DESC = "operation_change_desc";

    /** 远程中心负责人审核 - 操作变更说明 */
    public static final String BATCH_TD_OPERATION_CHANGE_DESC = "td_operation_change_desc";

    /** 操作计划审核 - 操作变更说明 */
    public static final String BATCH_RPC_OPERATION_CHANGE_DESC = "rpc_operation_change_desc";

    /** 行政审核_办事处产品科长 - 操作变更说明 */
    public static final String BATCH_ARPC_OPERATION_CHANGE_DESC = "arpc_operation_change_desc";

    /** 合作方批次 - 办事处产品PD - 操作变更说明 */
    public static final String HZF_BATCH_PD_OPERATION_CHANGE_DESC = "pd_operation_change_desc";

    /** 批次- 审核说明附件 */
    public static final String BATCH_APPROVAL_DESCRIPTION_FILE = "approval_description_file";

    /** 合作方批次 - 审核说明附件 */
    public static final String HZF_BATCH_APPROVAL_CONFIRMATION_FILE = "approval_confirmation_file";

    /** 操作结果审核标识 */
    public static final String RESULT_REVIEW_FLAG = "result_review_flag";
}
