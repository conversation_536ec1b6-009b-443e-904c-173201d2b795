package com.zte.iccp.itech.extension.handler.approver.admin;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum.ADMINISTRATIVE_LEADER_DIVISION_THREE;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/17
 */
@RequiredArgsConstructor
@Getter
public class InnerAdminHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {

    private final ApproveRoleEnum role;

    @Override
    public final List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        List<String> approver;
        // 国内且非政企（三营）且为行政审核_网络服务处总监节点角色
        if (DeptTypeEnum.INNER == ChangeOrderAbility.getDeptType(changeOrder)
                && BoolEnum.N == changeOrder.getIsGovEnt()
                && ApproveRoleEnum.NETWORK_SERVICE_OFFICE_DIRECTOR == role) {
            OperatorEnum operatorEnum = OperatorEnum.getOperatorEnum(changeOrder.getCustomerTypeFlag());
            if (operatorEnum == null) {
                // 申请单客户标识字段不属于上述时，读取【网络服务处总监】的人员作为审批人
                return getApprover(changeOrder, role);
            }
            ApproveRoleEnum roleEnum = ApproveRoleEnum.NETWORK_SERVICE_OFFICE_DIRECTOR;
            switch (operatorEnum) {
                // 1.当申请单客户标识字段等于中国电信,读取【网络服务处电信总监】的人员
                case CT:
                    roleEnum = ApproveRoleEnum.NETWORK_SERVICE_OFFICE_DIRECTOR_OF_CHINA_TELECOM;
                    break;
                // 2.当申请单客户标识字段等于中国移动，读取【网络服务处移动总监】
                case CM:
                    roleEnum = ApproveRoleEnum.NETWORK_SERVICE_OFFICE_MOBILE_DIRECTOR;
                    break;
                // 3.当申请单客户标识字段等于中国联通,读取【网络服务处联通总监】
                case CU:
                    roleEnum = ApproveRoleEnum.NETWORK_SERVICE_OFFICE_CHINA_UNICOM_DIRECTOR;
                    break;
                default:
                    break;
            }
            // 或者客户标识属于上述单上述条件下人员读取为空，读取【网络服务处总监】
            approver = getApprover(changeOrder, roleEnum);
            if (!CollectionUtils.isEmpty(approver)) {
                return approver;
            }
        }
        return getApprover(changeOrder, role);
    }

    /**
     * 获取行政审批人
     *
     * @param changeOrder changeOrder
     * @param approveRoleEnum approveRoleEnum
     * @return 审批人
     */
    private List<String> getApprover(ChangeOrder changeOrder, ApproveRoleEnum approveRoleEnum) {
        ApproverConfiguration condition = new ApproverConfiguration() {{
            setApprovalNode(ADMINISTRATIVE_LEADER_DIVISION_THREE);
            setProdLine(ProductUtils.getLine(changeOrder.getProductCategory()));
            setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
            setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
            setResponsibleDeptId(changeOrder.getResponsibleDept());
            setIsGov(changeOrder.getIsGovEnt());
            setRole(approveRoleEnum);
        }};
        return ApproverConfigAbility.getApprovalPriorityPersons(
                condition, changeOrder.getProductCategory(), 0, null);
    }
}
