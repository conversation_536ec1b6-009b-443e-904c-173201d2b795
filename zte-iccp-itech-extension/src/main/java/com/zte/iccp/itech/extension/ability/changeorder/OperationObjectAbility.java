package com.zte.iccp.itech.extension.ability.changeorder;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.VersionAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.domain.model.subentity.SubconOperationObject;
import com.zte.iccp.itech.extension.domain.model.vo.OperationObjectVO;
import com.zte.iccp.itech.extension.spi.model.query.NisNetworkQuery;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.*;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.FIELD_OPTIONS;

public class OperationObjectAbility {

    // ====================== 操作对象基本数据库方法 =======================
    /**
     * 检索操作对象数据 - PID
     * @param changeOrderId
     * @return List<OperationObject>
     */
    public static List<OperationObject> listOperationObject(String changeOrderId,
                                                            Class<? extends BaseSubEntity> clazz) {
        if (!StringUtils.hasText(changeOrderId)) {
            return Lists.newArrayList();
        }

        if (OperationObject.class != clazz && SubconOperationObject.class != clazz) {
            return Lists.newArrayList();
        }

        return QueryDataHelper.query(clazz, Lists.newArrayList(), changeOrderId);
    }

    /**
     * 根据变更单ID和操作批次NO，查询变更单的操作对象数据
     * @param changeOrderId
     * @return List<OperationObject>
     */
    public static List<OperationObject> listOperationObject(String changeOrderId,
                                                            String batchNo,
                                                            Class<? extends BaseSubEntity> clazz) {
        List<String> fieldObjList = Lists.newArrayList(
                NETWORK_ID,
                OFFICE_NAME,
                PRODUCT_MODEL,
                CURRENT_VERSION,
                TARGET_VERSION);
        if (clazz == OperationObject.class) {
            fieldObjList.add(CURRENT_VERSION_THIRD_PARTY);
            fieldObjList.add(TARGET_VERSION_THIRD_PARTY);
        }
        List<IFilter> conditionObjFilters = Lists.newArrayList();
        conditionObjFilters.add(new Filter(BATCH_INFO_NO, Comparator.CONTAINS, Arrays.asList(batchNo)));
        // 通过批次ID，查询操作对象数据 塞入发布通告操作对象表格中
        return QueryDataHelper.query(clazz, fieldObjList, changeOrderId, conditionObjFilters);
    }

    /**
     * 根据变更单ID和操作批次NO，查询变更单的操作对象数据详情
     * @param changeOrderId
     * @return List<OperationObject>
     */
    public static List<OperationObjectVO> listByChangeOrderIdAndBatch(String changeOrderId, String batchNo, Class<? extends BaseSubEntity> clazz) {
        List<OperationObject> objects = listOperationObject(changeOrderId, batchNo, clazz);
        if (CollectionUtils.isEmpty(objects)) {
            return new ArrayList<>();
        }

        List<OperationObjectVO> objectVos = new ArrayList<>();
        // 查询 NIS 网络信息
        List<String> networkIds = new ArrayList<>();
        List<String> productModelIds = new ArrayList<>();
        List<String> iversions = new ArrayList<>();
        objects.forEach(item -> {
            networkIds.add(item.getNetworkId());
            productModelIds.add(item.getProductModel());
            iversions.add(item.getCurrentVersion());
            iversions.add(item.getTargetVersion());
        });

        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();
        nisNetworkQuery.setNetworkIds(networkIds);
        Map<String, String> networkIdNameMap = NisAbility.queryCustomerNetworkNameMap(networkIds);
        Map<String, String> productModeMap = NisAbility.queryProductModelMap(productModelIds);
        Map<String, String> versionMap = VersionAbility.queryVersionMap(iversions);

        // 查询批次任务product_id
        Assignment task = QueryDataHelper.queryOne(Assignment.class,
                Arrays.asList(AssignmentFieldConsts.PRODUCT_CLASSIFICATION),
                Arrays.asList(new Filter(AssignmentFieldConsts.ENTITY_ID, Comparator.EQ, changeOrderId)));

        List<String> productIds = TextValuePairHelper.getValueList(task.getProductClassification());
        boolean isCcnThirdPartyFlag = !CollectionUtils.isEmpty(productIds)
                && productIds.get(0).startsWith(ConfigHelper.get(CCN_THIRD_PARTY_RULE_DATA_KEY));

        if (!CollectionUtils.isEmpty(objects)) {
            for (OperationObject item : objects) {
                OperationObjectVO vo = new OperationObjectVO();
                BeanUtils.copyProperties(item, vo);
                vo.setNetworkName(networkIdNameMap.get(item.getNetworkId()));
                vo.setProductModelName(productModeMap.get(item.getProductModel()));
                setVersionNames(vo, versionMap, item, isCcnThirdPartyFlag);
                objectVos.add(vo);
            }
        }
        return objectVos;
    }

    public static void setVersionNames(OperationObjectVO vo,
                                       Map<String, String> versionMap,
                                       OperationObject item,
                                       boolean isCcnThirdPartyFlag) {
        if (isCcnThirdPartyFlag) {
            vo.setCurrentVersionName(item.getCurrentVersionThirdParty());
            vo.setTargetVersionName(item.getTargetVersionThirdParty());
        } else {
            vo.setCurrentVersionName(versionMap.getOrDefault(item.getCurrentVersion(), EMPTY_STRING));
            vo.setTargetVersionName(versionMap.getOrDefault(item.getTargetVersion(), EMPTY_STRING));
        }
    }


    // ====================== 操作对象基本扩展方法 ========================
    /**
     * 获取批次操作对象 - PID
     * @param changeOrderId
     */
    public static Map<String, List<OperationObject>> getBatchOperationObject(String changeOrderId,
                                                                             Class<? extends BaseSubEntity> clazz) {
        // 1.检索操作对象
        List<OperationObject> operationObjectList = listOperationObject(changeOrderId, clazz);
        if (CollectionUtils.isEmpty(operationObjectList)) {
            return new HashMap<>();
        }

        // 2.根据批次号汇聚操作对象
        Map<String, List<OperationObject>> batchOpeationObjectMap = new HashMap<>();
        for (OperationObject operationObject : operationObjectList) {
            String batchNo = operationObject.getBatchNo().get(INTEGER_ZERO).getValue();

            if (batchOpeationObjectMap.containsKey(batchNo)) {
                batchOpeationObjectMap.get(batchNo).add(operationObject);
            } else {
                batchOpeationObjectMap.put(batchNo, Lists.newArrayList(operationObject));
            }
        }

        return batchOpeationObjectMap;
    }


    // ===================== 操作对象字段数据检索方法 ======================
    public static void setOptionAndClear(IFormView formView, List<Object> selectOfficeObjects, List<Object> clearValues) {

        OptionsBuilder optionsBuilder = new OptionsBuilder();
        for (Object object : selectOfficeObjects) {
            String officeName = (String) object;
            if (org.apache.commons.lang3.StringUtils.isBlank(officeName)) {
                continue;
            }
            optionsBuilder.addOption(new Option(officeName, new Text(officeName, officeName)));
        }

        IEntryTableSupport table = (IEntryTableSupport) formView.getControl(SECURITY_REINFORCEMENT_CID);
        table.getTableState()
                .setHeaderAttribute(SECURITY_OFFICE_NAME_CID, FIELD_OPTIONS, optionsBuilder.build());


        List<String> stringValues = clearValues.stream()
                .map(String.class::cast).filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .collect(Collectors.toList());
        List<Object> officeObjects = formView.getDataModel().getEntryColumnObject(
                SECURITY_REINFORCEMENT_PROPERTY_KEY, SECURITY_OFFICE_NAME_PROPERTY_KEY);
        for (int i = 0; i < officeObjects.size(); i++) {
            if (stringValues.contains(TextValuePairHelper.getValue(officeObjects.get(i)))) {
                formView.getDataModel().setValue(SECURITY_OFFICE_NAME_PROPERTY_KEY, null, i);
            }
        }
    }

    /*
     * 初始化保障变更单 操作对象
     * */
    public static void initCreate(List<String> changeOrderIds) {
        List<OperationObject> objects = new ArrayList<>();
        changeOrderIds.forEach(id -> {
            OperationObject obj = new OperationObject();
            obj.setPid(id);
            obj.setBatchNo(TextValuePairHelper.buildList("1", "1", "1"));
            objects.add(obj);
        });
        SaveDataHelper.batchCreate(objects);
    }
}
