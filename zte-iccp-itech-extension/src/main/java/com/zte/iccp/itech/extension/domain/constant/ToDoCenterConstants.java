package com.zte.iccp.itech.extension.domain.constant;

/**
 * @version V1.0
 * @author: 待办中心页面常量
 * @date: 2025/7/2 下午4:21
 * @Description:
 */
public class ToDoCenterConstants {
    /**
     * 我已处理-内部网络变更单页签的tabName
     */
    public static final String NETWORK_CHANGE_HANDLED_BY_ME = "network_change_handled_by_me";
    /**
     * 我已处理-合作方网络变更单页签的tabName
     */
    public static final String PARTNER_NETWORK_CHANGE_HANDLED_BY_ME = "partner_network_change_handled_by_me";
    /**
     * 我已处理-技术管理任务页签的tabName
     */
    public static final String TECHNOLOGY_HANDLED_BY_ME = "technology_handled_by_me";
    /**
     * 我已处理-故障管理任务页签的tabName
     */
    public static final String FAULT_HANDLED_BY_ME = "fault_handled_by_me";
    /**
     * 我已处理-权限申请页签的tabName
     */
    public static final String PERMISSION_HANDLED_BY_ME = "permission_handled_by_me";
    /**
     * 我已处理-异常复盘任务页签的tabName
     */
    public static final String CLOCK_REVIEW_HANDLED_BY_ME = "clock_review_handled_by_me";
    /**
     * 我已处理-全部页签的tabName
     */
    public static final String WHOLE_HANDLED_BY_ME = "whole_handled_by_me";
    /**
     * 待我处理-内部网络变更单页签的tabName
     */
    public static final String NETWORK_CHANGE_TO_BE_HANDLED = "network_change_to_be_handled";
    /**
     * 待我处理-合作方网络变更单页签的tabName
     */
    public static final String PARTNER_NETWORK_CHANGE_TO_BE_HANDLED = "partner_network_change_to_be_handled";
    /**
     * 待我处理-技术管理任务页签的tabName
     */
    public static final String TECHNOLOGY_TO_BE_HANDLED = "technology_to_be_handled";
    /**
     * 待我处理-故障管理任务页签的tabName
     */
    public static final String FAULT_TO_BE_HANDLED = "fault_to_be_handled";
    /**
     * 待我处理-权限申请页签的tabName
     */
    public static final String PERMISSION_TO_BE_HANDLED = "permission_to_be_handled";
    /**
     * 待我处理-异常复盘任务页签的tabName
     */
    public static final String CLOCK_REVIEW_TO_BE_HANDLED = "clock_review_to_be_handled";
    /**
     * 待我处理-全部页签的tabName
     */
    public static final String WHOLE_TO_BE_HANDLED = "whole_to_be_handled";

    /**
     * 我发起的-内部网络变更单页签的tabName
     */
    public static final String NETWORK_CHANGE_INITIATED_BY_ME  = "network_change_initiated_by_me";
    /**
     * 我发起的-合作方网络变更单页签的tabName
     */
    public static final String PARTNER_NETWORK_CHANGE_INITIATED_BY_ME = "partner_network_change_initiated_by_me";
    /**
     * 我发起的-技术管理任务页签的tabName
     */
    public static final String TECHNOLOGY_INITIATED_BY_ME = "technology_initiated_by_me";
    /**
     * 我发起的-故障管理任务页签的tabName
     */
    public static final String FAULT_INITIATED_BY_ME = "fault_initiated_by_me";
    /**
     * 我发起的-权限申请页签的tabName
     */
    public static final String PERMISSION_INITIATED_BY_ME = "permission_initiated_by_me";
    /**
     * 我发起的-异常复盘任务页签的tabName
     */
    public static final String CLOCK_REVIEW_INITIATED_BY_ME = "clock_review_initiated_by_me";
    /**
     * 我发起的-全部页签的tabName
     */
    public static final String WHOLE_INITIATED_BY_ME = "whole_initiated_by_me";

    /**
     * 待我处理、我已处理、我发起的-网络变更单
     */
    public static final String TO_DO_CENTER_NETWORK_CHANGE_PAGE = "ToDoCenter_NetworkChange_Page";
    /**
     * 待我处理、我已处理、我发起的-合作方网络变更单
     */
    public static final String TO_DO_CENTER_PARTNER_NETWORK_CHANGE_PAGE = "ToDoCenter_PartnerNetworkChange_Page";
    /**
     * 待我处理、我已处理、我发起的-技术管理任务
     */
    public static final String TO_DO_CENTER_TECHNOLOGY_PAGE = "ToDoCenter_Technology_Page";
    /**
     * 待我处理、我已处理、我发起的-故障管理任务
     */
    public static final String TO_DO_CENTER_FAULT_PAGE = "ToDoCenter_Fault_Page";
    /**
     * 待我处理、我已处理、我发起的-权限申请
     */
    public static final String TO_DO_CENTER_PERMISSION_PAGE = "ToDoCenter_Permission_Page";
    /**
     * 待我处理、我已处理、我发起的-异常复盘任务
     */
    public static final String TO_DO_CENTER_CLOCK_REVIEW_PAGE = "ToDoCenter_ClockReview_Page";
    /**
     * 待我处理、我已处理、我发起的-全部
     */
    public static final String TO_DO_CENTER_WHOLE_PAGE = "ToDoCenter_Whole_Page";

    /**
     * 低代码click事件封装参数tabName）
     */
    public static final String TAB_NAME = "tabName";
}
