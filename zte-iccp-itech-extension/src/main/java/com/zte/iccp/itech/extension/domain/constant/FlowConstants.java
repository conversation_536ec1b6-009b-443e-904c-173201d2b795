package com.zte.iccp.itech.extension.domain.constant;

/**
 * 审批流常量
 */
public class FlowConstants {
    // ========== 审批流变量名称 ===========
    /**
     * 基础 - 业务ID
     */
    public static final String BASIC_FIELD_BUSINESS_ID = "business_id";

    /**
     * 网络变更 - 网络处审核
     */
    public static final String VARIABLE_NETWORK_APPROVAL = "network_approval";

    /**
     * 网络变更 - 网服部审核
     */
    public static final String VARIABLE_NET_SERVICE_APPROVAL = "netService_approval";

    /**
     * 网络变更 - 变更单类型
     */
    public static final String VARIABLE_CHANGE_ORDER_TYPE = "change_order_type";

    /**
     * 网络变更 / 分包商网络变更 - 执行人
     */
    public static final String VARIABLE_EXECUTE_PERSON = "execute_person";

    /**
     * 故障管理 - CSC 单据编码
     */
    public static final String VARIABLE_CSC_CODE = "csc_code";

    /**
     * 区域ID
     */
    public static final String AREA_CODE = "area_code";

    /**
     * 办事处/代表处编码
     */
    public static final String ORGANIZATION = "organization";

    // ========== 权限申请 流程变量名 ===========

    /**
     * 创建人
     */
    public static final String CREATE_BY = "create_by";

    /**
     * 创建人中文
     */
    public static final String CREATE_BY_ZH_CN = "create_by.zh_cn";

    /**
     * 创建人英文
     */
    public static final String CREATE_BY_EN_US = "create_by.en_us";

    /**
     * 修改时间
     */
    public static final String LAST_MODIFIED_TIME = "last_modified_time";

    /**
     * 角色中文
     */
    public static final String ROLE_NAME_ZH_CN = "role_name.zh_cn";

    /**
     * 角色英文
     */
    public static final String ROLE_NAME_EN_US = "role_name.en_us";

    /*
     * 批次系统节点 流程变量
     * */
    public static final String BATCH_CHANGE = "batch_change";

    /*
     * 批次系统节点 流程变量 -G 保障单结束
     * */
    public static final String BATCH_GUARANTEE_END = "G";

    /*
     * 批次系统节点 流程变量 -Z 变更通告
     * */
    public static final String BATCH_CHANGE_COMMIT = "Z";

    /*
     * 批次反馈操作结果节点 -R 变更通告
     * */
    public static final String BATCH_RESULT_CHANGET = "R";

    /**
     * 批次 - 待发通告流程变量
     */
    public static final String PENDING_NOTIFICATION_CANCEL = "pending_notification_cancel";


    /**
     * 确认故障复盘_节点标识
     */
    public static final String FAULT_REVIEW_CONFIRMING_NODE_KEY = "Activity_1kwh29g";

    /**
     * 复盘任务执行中_节点标识
     */
    public static final String FAULT_REVIEWING_NODE_KEY = "Activity_0gi38fo";

    /**
     * 确认整改横推_节点标识
     */
    public static final String FAULT_RECTIFICATION_PROMOTION_CONFIRMING_NODE_KEY = "Activity_1kyvd0r";

    /**
     * 客户满意度节点标识
     */
    public static final String PENDING_FEEDBACK_CUSTOMER_SATISFACTION_NODE_KEY = "Activity_14j5yv9";

    /**
     * 内部变更单批次任务会签节点
     */
    public static final String CHANGE_ORDERBATCH_INTL_ADMIN_NODE_KEY = "Activity_1kd5qxn";

    /**
     * 操作计划驳回待提交节点
     */
    public static final String PLAN_REJECT_PENDING_SUBMISSION_NODE_KEY = "Activity_15b0cl2";

}
