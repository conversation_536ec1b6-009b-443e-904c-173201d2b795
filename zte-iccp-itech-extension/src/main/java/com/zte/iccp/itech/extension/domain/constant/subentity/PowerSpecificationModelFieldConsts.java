package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 电源规格型号子单据体常量类
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/25
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PowerSpecificationModelFieldConsts {
    /**
     * 物料名称
     */
    public static final String MATERIAL_NAME = "material_name";

    /**
     * 物料名称其他
     */
    public static final String MATERIAL_NAME_OTHER = "material_name_other";

    /**
     * 硬件版本
     */
    public static final String HARDWARE_VERSION = "hardware_version";

    /**
     * 硬件版本其他
     */
    public static final String HARDWARE_VERSION_OTHER = "hardware_version_other";

    /**
     * 数量
     */
    public static final String POWER_SPECIFICATION_MODEL_COUNT = "power_specification_model_count";

    /**
     * 序列号
     */
    public static final String SERIAL_NUMBER = "serial_number";

    /**
     * 软件版本
     */
    public static final String SOFTWARE_VERSION = "software_version";

    /**
     * 生产批次
     */
    public static final String PRODUCTION_BATCH = "production_batch";

    /**
     * 备注说明
     */
    public static final String POWER_SPECIFICATION_MODEL_DESC = "power_specification_model_desc";
}
