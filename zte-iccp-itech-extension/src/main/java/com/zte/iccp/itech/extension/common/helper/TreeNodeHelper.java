package com.zte.iccp.itech.extension.common.helper;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.openapi.model.clockin.SelectedOffice;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.iccp.itech.extension.spi.model.nis.ProductClassificationTree;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10344315
 * @date 2025-03-25 下午6:20
 **/
public class TreeNodeHelper {

    /**
     * 第二层产品类别
     */
    private static final String SECOND_LEVEL_PRODUCT_CLASSIFICATION = "2";

    public static List<String> collectLeafPaths(ProductClassificationTree node) {
        List<String> paths = Lists.newArrayList();
        if (node.getChildProdClass() == null || node.getChildProdClass().isEmpty()) {
            paths.add(node.getFullIdPath());
        } else {
            for (ProductClassificationTree child : node.getChildProdClass()) {
                paths.addAll(collectLeafPaths(child));
            }
        }
        return paths;
    }

    /**
     * @param rootNodes 根节点
     * @param targetFullIdPaths 二层产品分类fullIdPath
     */
    public static List<String> findBottomPaths(List<ProductClassificationTree> rootNodes, List<String> targetFullIdPaths) {
        Set<String> targetFullIdPath = new HashSet<>(targetFullIdPaths);
        List<String> bottomPaths = Lists.newArrayList();
        for (ProductClassificationTree root : rootNodes) {
            if (root.getChildProdClass() == null) {
                continue;
            }
            // 仅遍历二层子节点
            for (ProductClassificationTree child : root.getChildProdClass()) {
                if (SECOND_LEVEL_PRODUCT_CLASSIFICATION.equals(child.getProdClassLevel()) &&
                        targetFullIdPath.contains(child.getFullIdPath())) {
                    bottomPaths.addAll(collectLeafPaths(child));
                }
            }
        }
        return bottomPaths;
    }

    public List<String> getOfficeBottomPaths(List<SelectedOffice> selectedOffices, List<OrganizationTreeVo> organizations) {
        if (CollectionUtils.isEmpty(selectedOffices)) {
            return Lists.newArrayList();
        }
        Set<String> targetOfficePaths = selectedOffices
                .stream()
                .map(SelectedOffice::getOrgIdPath)
                .collect(Collectors.toSet());

        List<String> bottomPaths = Lists.newArrayList();

        findBottomPathsById(organizations, targetOfficePaths, bottomPaths);
        return bottomPaths;
    }

    /**
     * 获取目标节点的所有底层叶子结点，若目标节点为叶子节点，则也要返回目标节点本身
     * @param roots 全部节点
     * @param targetIds 目标节点id
     * @param bottomPaths 返回数据
     */
    private static void findBottomPathsById(
            List<OrganizationTreeVo> roots,
            Set<String> targetIds,
            List<String> bottomPaths) {
        if (CollectionUtils.isEmpty(roots)) {
            return;
        }
        for (OrganizationTreeVo item : roots) {
            if (targetIds.contains(item.getOrgIdPath())) {
                bottomPaths.addAll(collectLeafPaths(item));
            }
            findBottomPathsById(item.getChild(), targetIds, bottomPaths);
        }
    }

    private static List<String> collectLeafPaths(OrganizationTreeVo node) {
        List<String> paths = Lists.newArrayList();
        if (node.getChild() == null || node.getChild().isEmpty()) {
            paths.add(node.getOrgIdPath());
        } else {
            for (OrganizationTreeVo child : node.getChild()) {
                paths.addAll(collectLeafPaths(child));
            }
        }
        return paths;
    }
}
