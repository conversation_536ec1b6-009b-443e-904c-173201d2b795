package com.zte.iccp.itech.extension.domain.enums.assignment;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 待办中心 - 单据类型枚举
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum BillTypeEnum implements SingletonTextValuePairsProvider {
    /** 网络变更任务 */
    NETWORK_CHANGE("网络变更任务", "Network Change Assignment"),

    /** 技术管理任务 */
    TECHNOLOGY_MANAGEMENT("技术管理任务", "Technology Management Assignment"),

    /** 故障管理任务 */
    FAULT_MANAGEMENT("故障管理任务", "Fault Management Assignment"),

    /** 权限申请 */
    PERMISSION_APPLICATION("iTechCloud权限申请", "iTechCloud Permission Application"),

    /** 复盘任务 */
    CLOCK_REVIEW("打卡复盘", "Clock in Review"),

    /** 操作计划任务 */
    PLAN_OPERATION("操作计划", "Plan Operation"),
    ;


    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;



    @Override
    public String getValue() {
        return name();
    }
}
