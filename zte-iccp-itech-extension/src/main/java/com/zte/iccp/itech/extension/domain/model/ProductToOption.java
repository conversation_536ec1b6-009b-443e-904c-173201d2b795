package com.zte.iccp.itech.extension.domain.model;


import lombok.Getter;
import lombok.Setter;

/**
 * @author: 李江斌 10318434
 * @date: 2024/9/6
 */
@Getter
@Setter
public class ProductToOption {

    // 产品类型（实体字段唯一标识）
    private String productTypeCid;

    // 操作类型（下拉框唯一标识）
    private String optionType;

    // 操作类型（实体字段唯一标识）
    private String optionTypeCid;

    public ProductToOption(String productTypeCid, String optionType, String optionTypeCid) {
        this.productTypeCid = productTypeCid;
        this.optionType = optionType;
        this.optionTypeCid = optionTypeCid;
    }

    public ProductToOption() {
    }
}
