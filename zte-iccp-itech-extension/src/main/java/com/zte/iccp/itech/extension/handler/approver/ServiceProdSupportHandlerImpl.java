package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum.PURCHASED_IT_COMPONENTS;

/**
 * 服务产品支持部审核
 * 优先级  片区 -》 片区为空
 * 国际代表处：根据【营销/片区】（不区分是否政企），读取审核人和审核组作为当前处理人；
 * 国内代表处：根据【营销/片区】+【是否政企】，读取审核人和审核组作为当前处理人；
 * <AUTHOR> 10284287
 * @since 2024/07/18
 */
public class ServiceProdSupportHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        String responsibleDept = changeOrder.getResponsibleDept();
        DeptTypeEnum deptType = ResponsibleUtils.getDeptType(responsibleDept);

        ApproverConfiguration condition = new ApproverConfiguration();
        condition.setApprovalNode(PURCHASED_IT_COMPONENTS);
        condition.setSales(ResponsibleUtils.getSales(responsibleDept));
        condition.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
        condition.setIsGov(deptType == DeptTypeEnum.INNER ? changeOrder.getIsGovEnt() : null);

        List<String> approvalPersons = ApproverConfigAbility.getApprovalPersons(condition, null);
        if (CollectionUtils.isEmpty(approvalPersons)) {
            condition.setOrganizationRegion(null);
            approvalPersons = ApproverConfigAbility.getApprovalPersons(condition, null);
        }

        return approvalPersons;
    }
}
