package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValueTypePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.WireBusinessCheckFieldConsts.*;

@ApiModel("有线业务检查表")
@Setter
@Getter
@BaseEntity.Info("wire_business_check")
public class WireBusinessCheck extends BaseEntity {

    @JsonProperty(value = PRODUCT_OPERATION_TEAM)
    @ApiModelProperty("产品经营团队")
    private List<TextValueTypePair> productOperationTeam;

    @JsonProperty(value = PRODUCT_LINE)
    @ApiModelProperty("产品线")
    private List<TextValueTypePair> productLine;

    @JsonProperty(value = PRODUCT_MAIN_CATEGORY)
    @ApiModelProperty("产品大类")
    private List<TextValueTypePair> productMainCategory;

    @JsonProperty(value = PRODUCT_SUB_CATEGORY)
    @ApiModelProperty("产品小类")
    private List<TextValueTypePair> productSubCategory;

    @JsonProperty(value = PRODUCT_MODEL)
    @ApiModelProperty("产品类型")
    private List<TextValueTypePair> productModel;

    @JsonProperty(value = DOCUMENT_NAME)
    @ApiModelProperty("文档名称")
    private String documentName;

    @JsonProperty(value = LINK)
    @ApiModelProperty("发布链接")
    private String link;

    @JsonProperty(value = REMARK)
    @ApiModelProperty("备注说明")
    private String remark;
}
