package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 技术方案检查/核心网产品外场质量保证规范动作实体
 *
 * <AUTHOR> 10335201
 * @date 2024-05-15 上午11:25
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TechnicalSolutionCheckConsts {
    /**
     * 检查项编码
     */
    public static final String CHECK_CODE = "check_code";
    /**
     * 检查内容中文
     */
    public static final String CHECK_CONTENT_ZH = "check_content_zh";
    /**
     * 检查内容英文
     */
    public static final String CHECK_CONTENT_EN = "check_content_en";
    /**
     * 检查节点
     */
    public static final String CHECK_NODE = "check_node";
    /**
     * 确认定制化选项中文
     */
    public static final String SPECIAL_OPTION_ZH = "special_option_zh";
    /**
     * 确认定制化选项英文
     */
    public static final String SPECIAL_OPTION_EN = "special_option_en";
    /**
     * 排序
     */
    public static final String SORT = "sort";
    /**
     * 是否作废
     */
    public static final String IS_INVALIDATED = "is_invalidated";
    /**
     * 升级单检查标志
     */
    public static final String UPGRADE_TASK_CHECK_FLAG = "upgrade_task_check_flag";
    /**
     * 一级分类中文
     */
    public static final String GROUP_ONE_ZH = "group_one_zh";
    /**
     * 一级分类英文
     */
    public static final String GROUP_ONE_EN = "group_one_en";
    /**
     * 二级分类中文
     */
    public static final String GROUP_TWO_ZH = "group_two_zh";
    /**
     * 二级分类英文
     */
    public static final String GROUP_TWO_EN = "group_two_en";
}
