package com.zte.iccp.itech.extension.domain.model.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024/6/22 下午1:54
 */
@Getter
@Setter
@AllArgsConstructor
public class ItemsVO {
    /*
    * 项标题
    * */
    private String title;

    /*
     * 标签项唯一标识
     * */
    private String primaryKey;

    /*
     * 标签项内字段是否校验通过
     * */
    private Boolean isValid;

    /*
     * 标签项关闭按钮
     * */
    private Boolean closable;

    /*
     * 禁用标签项
     * */
    private Boolean disabled;
    /*
     * 设置这个属性为true也可以实现上面想要激活的标签项的功能，但要注意所有的标签项只能有一个为true，其余均为false
     * */
    private Boolean defaultActivated;

    /*
     * 设置你想要隐
     * */
    private Boolean hidden;
}
