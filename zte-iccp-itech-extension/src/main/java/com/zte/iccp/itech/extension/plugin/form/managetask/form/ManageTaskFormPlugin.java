package com.zte.iccp.itech.extension.plugin.form.managetask.form;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.FaultManagementOrderAbility;
import com.zte.iccp.itech.extension.ability.PersonnelAbilty;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.BusinessConsts;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SatisfactionResponsiblePersonConsts;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.TaskCategoryEnum;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.enums.PersonnelTaskTypeEnum;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.core.event.LoadDataEvent;
import com.zte.paas.lcap.core.event.PropertyChangedEvent;
import com.zte.paas.lcap.core.metadata.IDataEntityProperty;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts.ORGANIZATION_ID;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.PROPS;

/**
 * 任务分类下拉快码值筛选
 */
public class ManageTaskFormPlugin extends BaseFormPlugin {

    /**
     * 故障管理任务相关任务属性设置
     */
    @Override
    public void afterLoadData(LoadDataEvent loadDataEvent) {
        IDataModel dataModel = getModel();
        IFormView formView = getView();
        IClientViewProxy clientViewProxy = formView.getClientViewProxy();

        // 1.获取跳转来源
        Map<String, Object> customerParameters
                = formView.getFormShowParameter().getCustomParameters();
        String operationKey = (String) customerParameters.get(CommonConstants.OPERATION_KEY);
        if (!CidConstants.OPERATION_CREATE_FAULT_REVIEW_ASSIGNMENT_CID.equals(operationKey)
                && !CidConstants.OPERATION_CREATE_FAULT_RECTIFY_ASSIGNMENT_CID.equals(operationKey)) {
            return;
        }

        // 2.隐藏保存草稿按钮
        formView.getClientViewProxy().setControlState(
                CidConstants.BUTTON_TECHNICAL_TASK_SAVE_DRAFT_CID,
                new PageStatusAttributeBuilder().hidden().build());

        // 3.故障复盘任务 需额外设置组件信息
        if (CidConstants.OPERATION_CREATE_FAULT_REVIEW_ASSIGNMENT_CID.equals(operationKey)) {
            String faultOrderId = (String) customerParameters.get(CommonConstants.FAULT_ORDER_ID);
            String assignmentId = (String) customerParameters.get(CommonConstants.FAULT_ASSIGNMENT_ID);

            // 2.设置组件信息 - 任务分类 / 组织 / 产品经营团队
            setReviewTaskCategory(clientViewProxy, dataModel);
            setReviewOrganization(dataModel, faultOrderId);
            setReviewProductTeam(dataModel, assignmentId);
        }
    }

    @Override
    public void propertyChanged(PropertyChangedEvent event) {
        // 监听代表处变更 判断人员是否有提交权限
        ChangeData changeData = event.getChangeSet()[CommonConstants.INTEGER_ZERO];
        String changeField = changeData.getProperty().getKey();
        List<String> orgIds = TextValuePairHelper.getValueList(getModel().getValue(SatisfactionResponsiblePersonConsts.ORGANIZATION_ID));
        if (CollectionUtils.isEmpty(orgIds) || !ORGANIZATION_ID.equals(changeField)) {
            return;
        }

        String msg = PersonnelAbilty.checkPersonnel(orgIds, ContextHelper.getEmpNo(),
                null, PersonnelTaskTypeEnum.TECHNICAL_MANAGEMENT_TASK);
        if (StringUtils.hasText(msg)) {
            getView().showMessage(msg, MsgType.ERROR);
        }
    }

    /**
     * 故障复盘设置任务分类
     */
    private void setReviewTaskCategory(IClientViewProxy clientViewProxy, IDataModel dataModel) {
        // 1.检索快码 - 故障复盘
        LookupValue lookupValue = LookupValueHelper.getLookupValue(
                LookupValueConstant.TASK_CATEGORY_ENUM,
                TaskCategoryEnum.FAULT_REVIEW.getValue());
        if (Objects.isNull(lookupValue)) {
            return;
        }

        // 2.封装快码组件选项
        JSONObject componentValue = new JSONObject();
        componentValue.put(CommonConstants.LOOKUPCODE, lookupValue.getLookupCode());
        componentValue.put(
                CommonConstants.FAST_CODE_MEANING,
                LangUtils.get(lookupValue.getMeaningCn(), lookupValue.getMeaningEn()));

        // 3.任务分类固定赋值 + 设置只读
        dataModel.setValue(ManageTaskFieldConsts.TASK_CATEGORY, componentValue);
        clientViewProxy.setControlState(
                CidConstants.COMPONENT_TASK_CATEGORY_CID,
                new PageStatusAttributeBuilder().readOnly().build());
    }

    /**
     * 故障复盘设置责任单位
     */
    private void setReviewOrganization(IDataModel dataModel, String faultOrderId) {

        // 1.检索故障管理任务 + 校验国家
        FaultManagementOrder faultOrder = FaultManagementOrderAbility.queryOrderById(faultOrderId);
        if (Objects.isNull(faultOrder) ||
                !CommonConstants.CHINA_COUNTRY_ID.equals(faultOrder.getAreaCode())) {
            return;
        }

        // 2.代表处初始值
        // 当国家 = 中国时，代表处初始为 网络服务处
        String netServiceSalesId = ResponsibleUtils.getResponsible(BusinessConsts.NET_SERVICE_SALES);
        Map<String, BasicOrganizationInfo> organizationInfo
                = HrClient.queryOrganizationInfo(Lists.newArrayList(netServiceSalesId));
        BasicOrganizationInfo netServiceSales = organizationInfo.get(netServiceSalesId);

        dataModel.setValue(
                ORGANIZATION_ID,
                TextValuePairHelper.buildList(
                        StringUtils.replace(
                                netServiceSales.getOrgIDPath(),
                                CommonConstants.SPECIAL_HYPHEN,
                                CommonConstants.FORWARD_SLASH),
                        netServiceSales.getHrOrgName(),
                        netServiceSales.getHrOrgName()));
    }

    /**
     * 故障复盘设置产品经营团队
     */
    private void setReviewProductTeam(IDataModel dataModel, String assignmentId) {

        // 1.检索故障管理任务
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                assignmentId,
                Lists.newArrayList(AssignmentFieldConsts.PRODUCT_MANAGEMENT_TEAM),
                Assignment.class);
        if (Objects.isNull(assignment)) {
            return;
        }

        // 2.产品经营团队初始值
        String productTeamId = TextValuePairHelper.getValue(assignment.getProductManagementTeam());
        List<BasicProductInfo> products
                = NisClient.queryProductInfo(Lists.newArrayList(productTeamId));
        if (CollectionUtils.isEmpty(products) || Objects.isNull(products.get(0))) {
            return;
        }
        BasicProductInfo productTeamInfo = products.get(0);

        dataModel.setValue(
                ManageTaskFieldConsts.PRODUCT_ID,
                TextValuePairHelper.buildList(
                        productTeamId + CommonConstants.FORWARD_SLASH,
                        productTeamInfo.getNameZh(),
                        productTeamInfo.getNameEn()));
    }

    /**
     * 整改 / 横推任务设置任务分类下拉选项
     */
    @Override
    public void afterBindData(EventObject eventObject) {
        IFormView formView = getView();
        IDataModel dataModel = getModel();
        IDataEntityProperty property = dataModel.getProperty(ManageTaskFieldConsts.TASK_CATEGORY);

        // 1.获取跳转来源
        Map<String, Object> customerParameters
                = formView.getFormShowParameter().getCustomParameters();
        String operationKey = (String) customerParameters.get(CommonConstants.OPERATION_KEY);
        if (CidConstants.OPERATION_CREATE_FAULT_RECTIFY_ASSIGNMENT_CID.equals(operationKey)
                || CidConstants.OPERATION_CREATE_CLOCK_REVIEWS_RECTIFY_ASSIGNMENT_CID.equals(operationKey)) {
            setRectifyTaskCategoryDropDown(formView, property);
        }
    }

    /**
     *  整改横推任务分类下拉范围控制
     */
    private void setRectifyTaskCategoryDropDown(
            IFormView formView,
            IDataEntityProperty property) {

        // 1.检索快码 - 整改横推
        List<LookupValue> lookupValues = LookupValueHelper.getLookupValues(
                LookupValueConstant.TASK_CATEGORY_ENUM,
                Lists.newArrayList(
                        TaskCategoryEnum.FAULT_RECTIFY.getValue(),
                        TaskCategoryEnum.HORIZONTAL_PUSHING_TASK.getValue()));

        // 2.封装快码组件选项
        PageRows<LookupValue> pageRows = new PageRows<>();
        pageRows.setRows(lookupValues);
        pageRows.setCurrent(1);
        pageRows.setTotal(lookupValues.size());

        JSONObject props = new JSONObject();
        Set<String> customAttributeKeys = property.getCustomAttributeKeys();
        customAttributeKeys.forEach(item -> props.put(item, property.getCustomAttribute(item)));

        // 3.任务分类限制下拉范围
        JSONObject command = new JSONObject();
        command.put(CommonConstants.RESP, pageRows);
        command.put(CommonConstants.RESP_TYPE, CommonConstants.INIT);
        command.put(PROPS, props);
        command.put(CommonConstants.PAGE_ID, formView.getPageId());
        command.put(CommonConstants.TARGET, CidConstants.COMPONENT_TASK_CATEGORY_CID);
        formView.getClientViewProxy().setOptions(command);
    }
}
