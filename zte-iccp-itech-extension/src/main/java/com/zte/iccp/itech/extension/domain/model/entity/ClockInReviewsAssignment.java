package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;

@ApiModel("打卡复盘任务")
@Setter
@Getter
@BaseEntity.Info("assignment")
public class ClockInReviewsAssignment extends Assignment {

    @ApiModelProperty("批次号")
    @JsonProperty(value = ClockReviewsFieldConsts.BATCH_NO)
    private String batchNo;

    @ApiModelProperty("复盘操作等级")
    @JsonProperty(value = ClockReviewsFieldConsts.OPERATION_LEVEL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private OperationLevelEnum operationLevel;

    @ApiModelProperty("复盘操作类型")
    @JsonProperty(value = ClockReviewsFieldConsts.OPERATION_TYPE)
    private String operationType;

    @ApiModelProperty("计划操作开始时间")
    @JsonProperty(value = ClockReviewsFieldConsts.PLAN_OPERATION_START_TIME)
    private Date planOperationStartTime;

    @ApiModelProperty("值守时长(单位：分钟)")
    @JsonProperty(value = ClockReviewsFieldConsts.ON_DUTY_DURATION_HOURS)
    private Integer onDutyDurationHours;
}
