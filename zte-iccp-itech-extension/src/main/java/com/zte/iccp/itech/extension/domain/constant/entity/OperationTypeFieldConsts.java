package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 操作类型基础资料字段标识
 *
 * <AUTHOR> 10347404
 * @since 2024/04/28
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OperationTypeFieldConsts {

    /** 产品经营团队 */
    public static final String PRODUCT_TEAM = "multiselectfield_prod_operation_team";

    /** 产品线 */
    public static final String PRODUCT_LINE = "multiselectfield_product_line";

    /** 操作类型 */
    public static final String OPERATE_TYPE = "fastcodefield_operate_type";

    /** 操作原因 */
    public static final String OPERATE_REASON = "multifastcodefield_operate_reason";

    /** 合作商网络变更可用 */
    public static final String PARTNER_NETWORK_CHANGES_AVAILABLE = "partner_network_changes_available";

    /** 是否定制操作原因 */
    public static final String IS_CUSTOMIZE_OPERATION_REASON = "radiofield_is_customize_operation_reason";

    /** 操作类型分组 */
    public static final String OPERATE_TYPE_GROUP = "fastcodefield_operate_type_group";

    /** 风险评估 */
    public static final String RISK_ASSESSMENT = "risk_assessment";

    /** 操作等级 */
    public static final String OPERATE_LEVEL = "operate_level";

    /** 重要程度 */
    public static final String IMPORTANCE_LEVEL = "importance_level";
}
