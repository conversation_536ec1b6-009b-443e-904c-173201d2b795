package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("操作对象")
@Setter
@Getter
public class OperationObjectVO extends BaseSubEntity {

    @JsonProperty(value = "custom_kfe6gvl7")
    @ApiModelProperty("网络ID")
    private String networkId;

    @JsonProperty(value = "custom_a3egetpm")
    @ApiModelProperty("网络名称")
    private String networkName;

    @JsonProperty(value = "custom_mlaeyjpz")
    @ApiModelProperty("局点名称")
    private String officeName;

    @ApiModelProperty("产品型号ID")
    private String productModel;

    @JsonProperty(value = "custom_kx8mucue")
    @ApiModelProperty("产品型号名称")
    private String productModelName;

    @ApiModelProperty("当前版本ID")
    private String currentVersion;

    @JsonProperty(value = "custom_vnr7gtps")
    @ApiModelProperty("当前版本名称")
    private String currentVersionName;

    @ApiModelProperty("目标版本ID")
    private String targetVersion;

    @JsonProperty(value = "custom_qyk5wg40")
    @ApiModelProperty("目标版本名称")
    private String targetVersionName;
}
