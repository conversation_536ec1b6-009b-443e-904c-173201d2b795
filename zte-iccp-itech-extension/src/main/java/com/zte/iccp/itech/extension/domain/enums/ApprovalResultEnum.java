package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
/* 单选项中的value取code字段*/
public enum ApprovalResultEnum implements SingletonTextValuePairsProvider {
    /**
     * 同意
     */
    PASS("PASS", "同意", "pass"),
    /**
     * 否
     */
    TERMINATE("TERMINATE", "不同意", "reject"),
    ;

    private final String code;
    private final String zhCn;
    private final String enUs;

    @Override
    public String getValue() {
        return getCode();
    }

    /**
     * 根据语言环境获取对应描述信息
     * @param language
     * @return String
     */
    public String getTextByLanguage(String language) {
        return ZH_CN.equals(language) ? zhCn : enUs;
    }

}
