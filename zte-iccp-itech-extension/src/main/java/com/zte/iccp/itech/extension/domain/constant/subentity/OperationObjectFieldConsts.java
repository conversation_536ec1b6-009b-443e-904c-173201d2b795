package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OperationObjectFieldConsts {

    /**
     * 批次号 - 数据库字段名
     */
    public static final String BATCH_NO = "batch_no";

    /**
     * 批次号 - 实体字段唯一标识
     */
    public static final String BATCH_INFO_NO = "batch_info_no";

    /**
     * 主产品 - 数据库字段名
     */
    public static final String IS_MAIN_PRODUCT = "is_main_product";

    /**
     * 网络责任人&组 - 数据库字段名
     */
    public static final String NET_RESPONSIBLE_PERSON_AND_TEAM = "network_responsible_person_and_team";

    /**
     * 网络ID
     */
    public static final String NETWORK_ID = "network_id";

    /**
     * 局点名称
     */
    public static final String OFFICE_NAME = "office_name";

    /**
     * 产品型号
     */
    public static final String PRODUCT_MODEL = "product_model";

    /**
     * 当前版本
     */
    public static final String CURRENT_VERSION = "current_version";

    /**
     * 目标版本
     */
    public static final String TARGET_VERSION = "target_version";

    /**
     * 当前版本-第三方组网
     */
    public static final String CURRENT_VERSION_THIRD_PARTY = "current_version_text_field";

    /**
     * 目标版本-第三方组网
     */
    public static final String TARGET_VERSION_THIRD_PARTY = "target_version_text_field";

    /**
     * 操作对象冲突
     */
    public static final String TIME_CONFLICT_OBJECT = "time_conflict_object";
}
