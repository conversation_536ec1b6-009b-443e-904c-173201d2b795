package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.extension.domain.enums.BuilderViewEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批量设置显示、隐藏、可编辑工具类
 * <AUTHOR>
 * @create 2024/5/31 下午4:43
 */
public class BatchHiddenUtils {

    public static void hiddenTool(IFormView formView, BuilderViewEnum viewEnum, List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }

        List<String> filteredKeys = keys.stream()
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Object> viewMap = getViewMap(viewEnum);

        for (String cid : filteredKeys) {
            formView.getClientViewProxy().setControlState(cid, viewMap);
        }
    }

    private static Map<String, Object> getViewMap(BuilderViewEnum viewEnum) {
        switch (viewEnum) {
            case HIDDEN:
                return new PageStatusAttributeBuilder().hidden().build();
            case READONLY:
                return new PageStatusAttributeBuilder().readOnly().build();
            case DISABLED:
                return new PageStatusAttributeBuilder().disable().build();
            default:
                return new PageStatusAttributeBuilder().normal().build();
        }
    }
}
