package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.FaultManagementOrderAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.FaultManageOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultProcessEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class SubmitFaultAssignment extends BaseFlowOperationPlugin {

    /** 流程变量 - 整改横推任务已完成 */
    private static final String RECTIFY_FINISH_FLAG = "rectification_promotion_finish";

    @Override
    public void afterOperate(ExecuteEvent executeEvent) {
        String faultOrderId = getPkId();

        // 1.检索故障复盘单 + 故障复盘任务
        Assignment assignment = AssignmentAbility.queryAssignment(
                faultOrderId,
                Lists.newArrayList(CommonFieldConsts.ID, AssignmentFieldConsts.CURRENT_PROGRESS),
                Assignment.class);
        FaultManagementOrder faultOrder = FaultManagementOrderAbility.queryOrderById(faultOrderId);
        if (Objects.isNull(assignment) || Objects.isNull(faultOrder)) {
            return;
        }

        // 2.更新提交信息
        FaultProcessEnum currentProcess
                = FaultProcessEnum.valueOf(assignment.getCurrentProgress());
        switch (currentProcess) {
            case FAULT_REVIEW_CONFIRMING:
                faultReviewConfirmingSubmit(assignment.getId(), faultOrderId, faultOrder);
                break;
            case FAULT_RECTIFICATION_PROMOTION_CONFIRMING:
                faultRectificationPromotionConfirmingSubmit(assignment.getId(), faultOrderId);
                break;
            case PENDING_FEEDBACK_CUSTOMER_SATISFACTION:
                feedbackSatisfactionSubmit(assignment.getId(), faultOrder);
                break;
            default:
                break;
        }
    }

    /**
     * 节点提交 - 确认故障复盘
     */
    private void faultReviewConfirmingSubmit(
            String assignmentId,
            String faultOrderId,
            FaultManagementOrder faultOrder) {

        // 仅 无需故障复盘 会使用提交按钮
        // 需要故障复盘 则通过故障复盘任务进行提交更新
        String userId = ContextHelper.getEmpNo();

        // 1.故障管理单 - 故障复盘提交人 / 提交时间
        FaultManagementOrder updateOrder = new FaultManagementOrder();
        updateOrder.setId(faultOrderId);
        updateOrder.setReviewSubmitter(
                EmployeeHelper.getEmployees(Lists.newArrayList(userId)));
        updateOrder.setReviewSubmitTime(
                DateUtils.dateToString(new Date(), CommonConstants.DATE_FORM));

        // 2.故障管理任务 - 当前进展（变更为：待反馈客户满意度）
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignmentId);
        if (BoolEnum.N == faultOrder.getFaultReview()) {
            updateOrder.setSatisfactionResponsible(faultOrder.getSatisfactionResponsible());
            updateOrder.setSatisfactionResponsibleFr(faultOrder.getSatisfactionResponsibleFr());
            updateAssignment.setCurrentProgress(
                    FaultProcessEnum.PENDING_FEEDBACK_CUSTOMER_SATISFACTION.name());
        } else {
            updateAssignment.setCurrentProgress(
                    FaultProcessEnum.FAULT_REVIEWING.name());
        }
        FaultManagementOrderAbility.batchUpdate(Lists.newArrayList(updateOrder));
        AssignmentAbility.update(updateAssignment);

        // 3.我已处理
        AssignmentAbility.updateHandledByMeRelevance(assignmentId, userId, BoolEnum.N);
    }

    /**
     * 节点提交 - 确认整改横推
     */
    private void faultRectificationPromotionConfirmingSubmit(
            String assignmentId,
            String faultOrderId) {

        String userId = ContextHelper.getEmpNo();

        // 1.故障管理单 - 确认整改横推提交人 / 提交时间
        FaultManagementOrder updateOrder = new FaultManagementOrder();
        updateOrder.setId(faultOrderId);
        updateOrder.setRectificationSubmitter(
                EmployeeHelper.getEmployees(Lists.newArrayList(userId)));
        updateOrder.setRectificationSubmitTime(
                DateUtils.dateToString(new Date(), CommonConstants.DATE_FORM));
        FaultManagementOrderAbility.batchUpdate(Lists.newArrayList(updateOrder));

        // 2.故障管理任务 - 当前进展（变更为：待反馈客户满意度）
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignmentId);
        updateAssignment.setCurrentProgress(
                FaultProcessEnum.PENDING_FEEDBACK_CUSTOMER_SATISFACTION.name());
        AssignmentAbility.update(updateAssignment);

        // 3.我已处理
        AssignmentAbility.updateHandledByMeRelevance(assignmentId, userId, BoolEnum.N);
    }

    /**
     * 节点提交 - 反馈客户满意度
     */
    private void feedbackSatisfactionSubmit(
            String assignmentId,
            FaultManagementOrder faultOrder) {

        String userId = ContextHelper.getEmpNo();

        // 1.故障管理单 - 反馈客户满意度提交人 / 提交时间
        FaultManagementOrder updateOrder = new FaultManagementOrder();
        updateOrder.setId(faultOrder.getId());
        updateOrder.setSatisfactionSubmitter(
                EmployeeHelper.getEmployees(Lists.newArrayList(userId)));
        updateOrder.setSatisfactionSubmitTime(
                DateUtils.dateToString(new Date(), CommonConstants.DATE_FORM));
        FaultManagementOrderAbility.batchUpdate(Lists.newArrayList(updateOrder));

        // 2.故障管理任务 - 当前进展
        feedbackSatisfactionUpdateFaultAssignment(
                assignmentId, faultOrder.getId(), faultOrder.getFaultRectification());

        // 3.我已处理
        AssignmentAbility.updateHandledByMeRelevance(assignmentId, userId, BoolEnum.N);

        // 4.客户满意度知会人相关处理
        createSatisfactionNoticeRelevance(assignmentId);
    }

    /**
     * 更新任务信息 - 待反馈客户满意度
     */
    private void feedbackSatisfactionUpdateFaultAssignment(
            String assignmentId,
            String faultOrderId,
            BoolEnum rectifyFlag) {

        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignmentId);
        updateAssignment.setCurrentProcessorEmployee(Lists.newArrayList());

        // 无需整改横推（选“否”或者为空） - 任务关闭
        if (BoolEnum.N == rectifyFlag || null == rectifyFlag) {
            updateAssignment.setCurrentProgress(FaultProcessEnum.CLOSED.name());
            AssignmentAbility.update(updateAssignment);
            return;
        }

        // 需整改横推
        List<TechnologyManagementAssignment> rectifyAssignments
                = AssignmentAbility.queryFaultTechAssignments(assignmentId, Lists.newArrayList());
        List<TechnologyManagementAssignment> unFinishAssignments = rectifyAssignments.stream()
                .filter(item -> !AssignmentStatusEnum.CLOSE.getValue().equals(item.getAssignmentStatus())
                        && !AssignmentStatusEnum.ABOLISH.getValue().equals(item.getAssignmentStatus()))
                .collect(Collectors.toList());

        // 整改横推任务未完成 - 整改横推任务执行中
        if (!CollectionUtils.isEmpty(unFinishAssignments)) {
            updateAssignment.setCurrentProgress(FaultProcessEnum.FAULT_BEING_RECTIFIED.name());
            AssignmentAbility.update(updateAssignment);
            return;
        }

        // 整改横推任务未完成 - 关闭任务
        // 需设置流程变量
        updateAssignment.setCurrentProgress(FaultProcessEnum.CLOSED.name());
        FlowHelper.changeVariables(
                faultOrderId,
                MapUtils.newHashMap(RECTIFY_FINISH_FLAG, BoolEnum.Y.name()));
    }

    /**
     * 创建关联关系 - 客户满意度知会人
     */
    private void createSatisfactionNoticeRelevance(String assignmentId) {
        IDataModel dataModel = getModel();

        List<Employee> infoPerson = ComponentUtils.getEmployeeComponentInfo(
                dataModel, FaultManageOrderFieldConsts.INFORMED_PERSON);
        List<String> personIdList = infoPerson.stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toList());
        AssignmentAbility.createAssignmentPersonRelevance(assignmentId, personIdList);
    }
}
