package com.zte.iccp.itech.extension.ability.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.EmailAbility;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.ability.UppAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.IntlAdminApprovalFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OpAssocProdDevIntApprovalFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OpAssocProdNetServIntApprovalFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.permissionapplication.RoleEnum;
import com.zte.iccp.itech.extension.domain.model.IntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.UppUserAuthInfo;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.PersonRelevance;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiModeProduct;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdDevIntApproval;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdNetServIntApproval;
import com.zte.iccp.itech.extension.domain.model.vo.SubIntlAdminApproval;
import com.zte.iss.approval.sdk.bean.*;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.paas.lcap.common.api.RequestHeaderUtils;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.ddm.domain.flow.dto.AddApproverDTO;
import com.zte.paas.lcap.ddm.domain.flow.dto.CreateApprovalTaskDTO;
import com.zte.paas.lcap.ddm.domain.flow.dto.DeleteApprovalTaskDTO;
import com.zte.paas.lcap.ddm.domain.flow.dto.TaskReassignDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.PERCENT;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.SPACE;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.BILL_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;

public class TransferAbility {

    /**
     * 待办中心 - 网络变更任务 - 转交当前处理人
     */
    public static Map<String, List<String>> networkChangeTransferCurrentProcessors(
            List<Assignment> assignments,
            String newCurrentProcessorId,
            boolean reserveFlag) {

        Map<String, List<String>> errorInfo = new HashMap<>();
        if (CollectionUtils.isEmpty(assignments)) {
            return errorInfo;
        }

        // 1.封装转交基础信息
        TransferInfo transferInfo
                = new TransferInfo(reserveFlag, ContextHelper.getEmpNo(), newCurrentProcessorId);

        // 2.待转交任务校验
        errorInfo = networkChangeTransferCurrentProcessorsCheck(assignments, transferInfo);

        // 3.区分 待启动 / 审批中 任务
        List<Assignment> approvingAssignments = assignments.stream()
                .filter(item -> AssignmentStatusEnum.APPROVE.getValue().equals(item.getAssignmentStatus()))
                .collect(Collectors.toList());
        assignments.removeAll(approvingAssignments);

        // 4.转交 草稿 当前处理人
        networkChangeDraftTransferCurrentProcessors(assignments, transferInfo);

        // 5.转交 审批中 当前处理人
        networkChangeApprovingTransferCurrentProcessors(approvingAssignments, transferInfo);

        return errorInfo;
    }

    /**
     * 转交任务校验（带过滤）- 网络变更任务
     */
    private static Map<String, List<String>> networkChangeTransferCurrentProcessorsCheck(
            List<Assignment> assignments,
            TransferInfo transferInfo) {

        List<String> executingCodes = Lists.newArrayList();

        // 1.转交任务数据校验
        for (Assignment assignment : assignments) {
            String assignmentCode = assignment.getAssignmentCode();

            // (1) 任务状态校验
            if (AssignmentStatusEnum.EXECUTE.getValue().equals(assignment.getAssignmentStatus())) {
                executingCodes.add(assignmentCode);
            }

            // (2) 已在当前处理人校验
            List<String> currentProcessorIds = assignment.getCurrentProcessorEmployee().stream()
                    .map(Employee::getEmpUIID)
                    .collect(Collectors.toList());
            transferInfo.currentUserExistFlag.put(
                    assignment.getId(),
                    currentProcessorIds.contains(transferInfo.targetUserId));
        }

        // 2.合法转交任务过滤 - 引用传递完成
        List<Assignment> invalidAssignments = assignments.stream()
                .filter(item -> executingCodes.contains(item.getAssignmentCode()))
                .collect(Collectors.toList());
        assignments.removeAll(invalidAssignments);

        // 3.包装错误信息
        return MapUtils.newHashMap(
                MessageConsts.Backlog.TRANSFER_EXECUTE_NETWORK_CHANGE, executingCodes);
    }

    /**
     * 待办中心 - 网络变更任务（待启动 / 驳回待启动） - 转交当前处理人
     */
    private static void networkChangeDraftTransferCurrentProcessors(
            List<Assignment> draftAssignments,
            TransferInfo transferInfo) {

        if (CollectionUtils.isEmpty(draftAssignments)) {
            return;
        }

        // 1.转交更新任务信息
        updateTransferAssignmentInfo(draftAssignments, transferInfo, BoolEnum.N);

        // 2.记录操作日志
        OperationLogRecordAbility.saveNetworkChangeTransferLog(
                draftAssignments,
                Lists.newArrayList(transferInfo.currentUser),
                Lists.newArrayList(transferInfo.targetUser),
                MessageConsts.Backlog.LOG_TRANSFER_CURRENT_PROCESSOR);
    }

    /**
     * 待办中心 - 网络变更任务（审批中） - 转交当前处理人
     */
    private static void networkChangeApprovingTransferCurrentProcessors(
            List<Assignment> approvingAssignments,
            TransferInfo transferInfo) {

        if (CollectionUtils.isEmpty(approvingAssignments)) {
            return;
        }

        // 1.转交审批流
        transferNetworkFlowApproveTask(approvingAssignments, transferInfo);

        // 2.更新任务中心 + 关联关系
        updateTransferAssignmentInfo(approvingAssignments, transferInfo, BoolEnum.Y);

        // 3.会签节点子表单数据更新
        updateCountingSignRecords(approvingAssignments, transferInfo);

        // 4.异步推送转交邮件
        asyncSendTransferEmail(approvingAssignments, transferInfo.targetUserId);

        // 5.记录操作日志
        OperationLogRecordAbility.saveNetworkChangeTransferLog(
                approvingAssignments,
                Lists.newArrayList(transferInfo.currentUser),
                Lists.newArrayList(transferInfo.targetUser),
                MessageConsts.Backlog.LOG_TRANSFER_CURRENT_PROCESSOR);
    }

    /**
     * 转交审批流任务 - 网络变更
     */
    private static void transferNetworkFlowApproveTask(
            List<Assignment> assignments,
            TransferInfo transferInfo) {

        // 1.检索审批流当前处理人信息
        List<String> entityIds = assignments.stream()
                .map(Assignment::getEntityId)
                .collect(Collectors.toList());
        List<FlowHandler> flowHandlers = FlowHelper.getFlowHandlerByFlowEntityIds(entityIds);

        // 2.审批流筛选无审批任务数据
        Map<Boolean, List<FlowHandler>> partitionedFlows = flowHandlers.stream()
                .collect(Collectors.partitioningBy(handler ->
                        handler.getApproveTaskList().stream()
                                .map(ApproveTask::getApprover)
                                .anyMatch(transferInfo.targetUserId::equals)));
        List<FlowHandler> hasApproveTaskFlows = partitionedFlows.get(true);
        List<FlowHandler> noneApproveTaskFlows = partitionedFlows.get(false);

        // 3.转交审批流任务
        // 保留用户 - 在节点新增审批人对应任务
        // 不保留用户 - 节点转交审批任务 + 删除用户原有审批任务
        if (transferInfo.reserveFlag) {
            addApproveTask(noneApproveTaskFlows, assignments, transferInfo);
        } else {
            transferApproveTask(noneApproveTaskFlows, transferInfo.currentUserId, transferInfo.targetUserId);
            deleteApproveTask(hasApproveTaskFlows, transferInfo.currentUserId);
        }
    }

    /**
     * 新增用户审批流任务
     */
    private static void addApproveTask(
            List<FlowHandler> flows,
            List<Assignment> assignments,
            TransferInfo transferInfo) {

        // todo 由于 FlowServiceHelper 中没有批量创建任务的方法，这里循环调用
        // 后续建议给 审批中心 / 平台 提批量需求处理
        // 转交 / 删除 为相同情况

        // 1.区分 会签节点 / 非会签节点
        Map<String, String> currentProgresses = assignments.stream()
                .collect(Collectors.toMap(Assignment::getEntityId, Assignment::getCurrentProgress));
        List<String> signNodes = ApproveNodeEnum.getSignApproveNodeEnums();

        List<String> signFlowIds = flows.stream()
                .filter(item -> signNodes.contains(currentProgresses.get(item.getBusinessId())))
                .map(FlowHandler::getFlowInstanceId)
                .collect(Collectors.toList());
        List<FlowHandler> approveFlows = flows.stream()
                .filter(item -> !signFlowIds.contains(item.getFlowInstanceId()))
                .collect(Collectors.toList());

        // 2.非会签节点，直接新增审批任务
        approveFlows.forEach(item -> {
            AddApproverDTO addInfo = new AddApproverDTO();
            addInfo.setBusinessId(item.getBusinessId());
            addInfo.setNodeKey(item.getApproveTaskList().get(0).getNodeCode());
            addInfo.setApprovers(Lists.newArrayList(transferInfo.targetUserId));
            FlowServiceHelper.addApprover(addInfo);
        });

        // 3.会签节点，需检索 teamCode，才可以新增审批任务
        BatchProcessparamDTO batchProcessParam = new BatchProcessparamDTO();
        batchProcessParam.setInsFlowIdList(signFlowIds);
        batchProcessParam.setHandler(ContextHelper.getEmpNo());
        batchProcessParam.setAppCode(ContextHelper.getUacAppId());
        batchProcessParam.setAppId(ContextHelper.getAppId());
        batchProcessParam.setTenantId(ContextHelper.getTenantId());
        Map<String, ApprovalProcessDTO> approvalProcesses
                = ApprovalFlowClient.queryBatchProcess(batchProcessParam);

        approvalProcesses.forEach((flowInstanceId, approvalProcess) -> {
            List<ApprovalTaskDTO> approvalTasks = approvalProcess.getApprovalTaskList();

            List<String> currentApprovalTypes = approvalTasks.stream()
                    .filter(item -> transferInfo.currentUserId.equals(item.getApprover()))
                    .map(ApprovalTaskDTO::getApproverTypeValue)
                    .collect(Collectors.toList());
            currentApprovalTypes.forEach(approvalTypeInfo -> {
                Map<String, Object> approvalType = JsonUtils.parseObject(approvalTypeInfo);

                AddApproverFlowInstIdDTO addInfo = new AddApproverFlowInstIdDTO();
                addInfo.setFlowInstId(approvalProcess.getFlowInstanceId());
                addInfo.setNodeCode(approvalTasks.get(0).getNodeId());
                addInfo.setTeamCode(approvalType.get("approvers").toString());
                addInfo.setApprovers(Lists.newArrayList(transferInfo.targetUserId));
                addInfo.setHandler(ContextHelper.getEmpNo());
                addInfo.setAppCode(ContextHelper.getUacAppId());
                addInfo.setAppId(ContextHelper.getAppId());
                addInfo.setTenantId(ContextHelper.getTenantId());
                ApprovalFlowClient.addApprovers(addInfo);
            });
        });
    }

    /**
     * 转交用户审批流任务
     */
    private static void transferApproveTask(
            List<FlowHandler> flows,
            String userId,
            String newCurrentProcessorId) {

        List<String> taskIds = flows.stream()
                .map(item -> item.getApproveTaskList().stream()
                        .filter(task -> userId.equals(task.getApprover()))
                        .findFirst()
                        .map(ApproveTask::getTaskId)
                        .orElse(null))
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
        taskIds.forEach(item -> {
            TaskReassignDTO reassignInfo = new TaskReassignDTO();
            reassignInfo.setTaskReceiver(newCurrentProcessorId);
            reassignInfo.setTaskId(item);
            FlowServiceHelper.reassign(reassignInfo);
        });
    }

    /**
     * 删除用户审批流任务
     */
    private static void deleteApproveTask(List<FlowHandler> flows, String userId) {
        List<String> taskIds = flows.stream()
                .map(item -> item.getApproveTaskList().stream()
                        .filter(task -> userId.equals(task.getApprover()))
                        .findFirst()
                        .map(ApproveTask::getTaskId)
                        .orElse(null))
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
        taskIds.forEach(item -> {
            DeleteApprovalTaskDTO deleteInfo = new DeleteApprovalTaskDTO();
            deleteInfo.setTaskId(item);
            FlowServiceHelper.deleteTask(deleteInfo);
        });
    }

    /**
     * 转交更新任务信息
     */
    private static void updateTransferAssignmentInfo(
            List<Assignment> assignments,
            TransferInfo transferInfo,
            BoolEnum approveTaskFlag) {

        // 1.检索主任务
        List<String> billIds = assignments.stream()
                .map(Assignment::getBillId)
                .distinct()
                .collect(Collectors.toList());
        List<Assignment> mainAssignments = QueryDataHelper.query(
                Assignment.class,
                Lists.newArrayList(ID, BILL_ID),
                Lists.newArrayList(
                        new Filter(BILL_ID, Comparator.IN, billIds),
                        new Filter(ASSIGNMENT_TYPE, Comparator.IN, Lists.newArrayList(
                                AssignmentTypeEnum.NETWORK_CHANGE.getValue(),
                                AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue()))));
        Map<String, String> mainAssignmentMap = mainAssignments.stream()
                .collect(Collectors.toMap(Assignment::getBillId, Assignment::getId));

        List<Assignment> updateAssignments = Lists.newArrayList();
        List<PersonRelevance> personRelevance = Lists.newArrayList();
        for (Assignment assignment : assignments) {
            String assignmentId = assignment.getId();
            List<Employee> currentProcessors = assignment.getCurrentProcessorEmployee();

            // 1.被转交用户去重
            boolean existFlag = transferInfo.currentUserExistFlag.get(assignmentId);
            if (existFlag) {
                List<String> currentProcessorIds = currentProcessors.stream()
                        .map(Employee::getEmpUIID)
                        .collect(Collectors.toList());
                currentProcessors.remove(
                        currentProcessorIds.indexOf(transferInfo.targetUserId));
            }

            // 2.定位用户
            List<String> currentProcessorIds = currentProcessors.stream()
                    .map(Employee::getEmpUIID)
                    .collect(Collectors.toList());
            int userIndex = currentProcessorIds.indexOf(transferInfo.currentUserId);

            // 3.转交替换
            currentProcessors.addAll(
                    userIndex,
                    EmployeeHelper.singletonEmployee2Employee(transferInfo.targetUser));
            if (!transferInfo.reserveFlag) {
                currentProcessors.remove(userIndex + 1);
            }

            // 4.待更新任务
            Assignment updateAssignment = new Assignment();
            updateAssignment.setId(assignmentId);
            updateAssignment.setCurrentProcessorEmployee(currentProcessors);
            updateAssignments.add(updateAssignment);

            // 4.待新建关联关系
            personRelevance.add(new PersonRelevance(
                    mainAssignmentMap.get(assignment.getBillId()),
                    transferInfo.targetUserId,
                    BoolEnum.N.name(),
                    approveTaskFlag.name()));
        }

        // 5.批量更新任务中心 + 新建关联关系
        AssignmentAbility.batchUpdate(updateAssignments);
        AssignmentAbility.createAssignmentPersonRelevance(personRelevance);
    }

    /**
     * 更新会签节点审批记录
     */
    private static void updateCountingSignRecords(
            List<Assignment> assignments,
            TransferInfo transferInfo) {

        // 1.任务分组
        Map<String, Set<String>> groupIds = assignments.stream()
                .collect(Collectors.groupingBy(
                        Assignment::getCurrentProgress,
                        Collectors.mapping(
                                Assignment::getBillId,
                                Collectors.toSet())));

        // 2.会签节点需更新审批记录
        // 网服一体化会签
        transferUpdateSignRecords(
                groupIds.get(ApproveNodeEnum.NET_INTEGRATION.name()),
                transferInfo,
                OpAssocProdNetServIntApproval.class,
                OpAssocProdNetServIntApprovalFieldConsts.APPROVER);

        // 研发一体化会签
        transferUpdateSignRecords(
                groupIds.get(ApproveNodeEnum.RD_INTEGRATION.name()),
                transferInfo,
                OpAssocProdDevIntApproval.class,
                OpAssocProdDevIntApprovalFieldConsts.APPROVER);

        // 多模产品会签
        transferUpdateMultiProductSignRecords(
                groupIds.get(ApproveNodeEnum.MULTIMODE_PRODUCT_OWNER.name()),
                transferInfo);

        // 主单 - 国际会签
        transferUpdateSignRecords(
                groupIds.get(ApproveNodeEnum.INTL_ADMIN_APPROVAL.name()),
                transferInfo,
                IntlAdminApproval.class,
                IntlAdminApprovalFieldConsts.APPROVED_BY);

        // 批次任务 - 国际会签
        transferUpdateSignRecords(
                groupIds.get(ApproveNodeEnum.BATCH_INTL_ADMIN_APPROVAL.name()),
                transferInfo,
                SubIntlAdminApproval.class,
                IntlAdminApprovalFieldConsts.APPROVED_BY);
    }

    /**
     * 转交会签记录 - 网络变更
     */
    private static <T extends BaseSubEntity> void transferUpdateSignRecords(
            Set<String> changeOrderIds,
            TransferInfo transferInfo,
            Class<T> recordClass,
            String approveByField) {

        if (CollectionUtils.isEmpty(changeOrderIds)) {
            return;
        }

        // 1.检索会签结果记录
        List<T> approveRecords = QueryDataHelper.query(
                recordClass,
                Lists.newArrayList(),
                new ArrayList<>(changeOrderIds),
                Lists.newArrayList(new Filter(
                        approveByField, Comparator.EQ, Lists.newArrayList(transferInfo.currentUserId))));
        Map<String, List<T>> pidInfo = approveRecords.stream()
                .collect(Collectors.groupingBy(BaseSubEntity::getPid));

        // 2.保留，新增审批记录
        if (transferInfo.reserveFlag) {
            pidInfo.forEach((pid, records) -> {
                List<Map<String, Object>> insertInfo = new ArrayList<>();
                records.forEach(record -> {
                    Map<String, Object> recordMap = JsonUtils.parseObject(record);

                    // 去除特殊字段
                    recordMap.remove(ID);
                    recordMap.remove(CREATE_BY);
                    recordMap.remove(LAST_MODIFIED_BY);
                    recordMap.remove(CREATE_TIME);
                    recordMap.remove(LAST_MODIFIED_TIME);

                    // 更新处理人
                    recordMap.put(approveByField, transferInfo.targetUser);
                    insertInfo.add(recordMap);
                });
                SaveDataHelper.batchCreate(recordClass, pid, insertInfo);
            });

            return;
        }

        // 3.不保留，更新审批记录
        pidInfo.forEach((pid, records) -> {
            List<Map<String, Object>> updateInfo = new ArrayList<>();
            records.forEach(record -> updateInfo.add(MapUtils.newHashMap(
                    ID, record.getId(),
                    approveByField, transferInfo.targetUser)));
            SaveDataHelper.batchUpdate(recordClass, pid, updateInfo);
        });
    }

    /**
     * 转交会签记录 - 网络变更
     */
    private static void transferUpdateMultiProductSignRecords(
            Set<String> changeOrderIds,
            TransferInfo transferInfo) {

        if (CollectionUtils.isEmpty(changeOrderIds)) {
            return;
        }

        // 1.检索会签结果记录
        List<MultiModeProduct> approveRecords = QueryDataHelper.query(
                MultiModeProduct.class,
                Lists.newArrayList(),
                new ArrayList<>(changeOrderIds),
                Lists.newArrayList(new Filter(
                        ChangeOrderFieldConsts.MultiModeProdConsts.MULTIMODE_NET_APPROVE_TEAM,
                        Comparator.LIKE,
                        PERCENT + transferInfo.currentUserId + PERCENT)));
        Map<String, List<MultiModeProduct>> pidInfo = approveRecords.stream()
                .collect(Collectors.groupingBy(BaseSubEntity::getPid));

        // 2.更新审批记录
        pidInfo.forEach((pid, records) -> {
            List<Map<String, Object>> updateInfo = new ArrayList<>();
            records.forEach(record -> {
                Map<String, Object> recordMap = JsonUtils.parseObject(record);

                // 去除特殊字段
                recordMap.remove(CREATE_BY);
                recordMap.remove(LAST_MODIFIED_BY);
                recordMap.remove(CREATE_TIME);
                recordMap.remove(LAST_MODIFIED_TIME);

                // 更新处理人
                String existApproveTeam = recordMap
                        .get(ChangeOrderFieldConsts.MultiModeProdConsts.MULTIMODE_NET_APPROVE_TEAM)
                        .toString();

                List<String> teamPeople = new ArrayList<>(
                        Arrays.asList(existApproveTeam.split(CommonConstants.COMMA)));
                int transferIndex = 0;
                for (int index = 0; index < teamPeople.size(); index++) {
                    if (teamPeople.get(index).contains(transferInfo.currentUserId)) {
                        transferIndex = index;
                        break;
                    }
                }

                if (!transferInfo.reserveFlag) {
                    teamPeople.remove(transferIndex);
                }
                String targetUserInfo = transferInfo.targetUser.getEmpName().replaceAll("\\d+$", "")
                        + SPACE
                        + transferInfo.targetUserId;
                teamPeople.add(transferIndex, targetUserInfo);

                recordMap.put(
                        ChangeOrderFieldConsts.MultiModeProdConsts.MULTIMODE_NET_APPROVE_TEAM,
                        String.join(CommonConstants.COMMA, teamPeople));
                updateInfo.add(recordMap);
            });
            SaveDataHelper.batchUpdate(MultiModeProduct.class, pid, updateInfo);
        });
    }

    /**
     * 异步发送转交邮件
     */
    private static void asyncSendTransferEmail(
            List<Assignment> assignments,
            String newCurrentProcessorId) {

        AsyncExecuteUtils.execute(() ->
                assignments.forEach(assignment -> EmailAbility.sendPressEmail(
                        assignment.getEntityId(),
                        CommonConstants.EMPTY_STRING,
                        true,
                        newCurrentProcessorId)));
    }

    /**
     * 待办中心 - 批次任务 - 转交当前处理人
     * key - 异常信息 Msg   value - 异常任务编码集
     */
    public static Map<String, List<String>> batchTransferCurrentProcessors(
            List<Assignment> assignments,
            String newCurrentProcessorId,
            boolean reserveFlag) {

        // 异常信息做预留，后续可直接使用
        Map<String, List<String>> errorInfo = new HashMap<>();
        if (CollectionUtils.isEmpty(assignments)) {
            return errorInfo;
        }

        // 1.封装转交基础信息
        TransferInfo transferInfo
                = new TransferInfo(reserveFlag, ContextHelper.getEmpNo(), newCurrentProcessorId);

        // 2.待转交任务校验
        assignments.forEach(assignment -> {
            boolean existFlag = assignment.getCurrentProcessorEmployee().stream()
                    .map(Employee::getEmpUIID)
                    .anyMatch(item -> item.equals(transferInfo.targetUserId));
            transferInfo.currentUserExistFlag.put(assignment.getId(), existFlag);
        });

        // 3.转交当前处理人
        batchTransferCurrentProcessors(assignments, transferInfo);

        return errorInfo;
    }

    /**
     * 待办中心 - 批次任务 - 转交当前处理人
     */
    private static void batchTransferCurrentProcessors(
            List<Assignment> assignments,
            TransferInfo transferInfo) {

        if (CollectionUtils.isEmpty(assignments)) {
            return;
        }

        // 1.转交审批流
        transferBatchFlowApproveTask(assignments, transferInfo);

        // 2.更新任务中心 + 关联关系
        updateTransferAssignmentInfo(assignments, transferInfo, BoolEnum.N);

        // 3.重新汇总对应主任务当前处理人
        AssignmentAbility.recalculateMainAssignmentCurrentProcessor(assignments);

        // 4.会签节点子表单数据更新
        updateCountingSignRecords(assignments, transferInfo);

        // 5.异步推送转交邮件
        asyncSendTransferEmail(assignments, transferInfo.targetUserId);

        // 6.记录操作日志
        OperationLogRecordAbility.saveNetworkChangeTransferLog(
                assignments,
                Lists.newArrayList(transferInfo.currentUser),
                Lists.newArrayList(transferInfo.targetUser),
                MessageConsts.Backlog.LOG_TRANSFER_CURRENT_PROCESSOR);
    }

    /**
     * 转交审批流任务 - 网络变更
     */
    private static void transferBatchFlowApproveTask(
            List<Assignment> assignments,
            TransferInfo transferInfo) {

        // 1.检索审批流当前处理人信息
        List<String> entityIds = assignments.stream()
                .map(Assignment::getEntityId)
                .collect(Collectors.toList());
        List<FlowHandler> flowHandlers = FlowHelper.getFlowHandlerByFlowEntityIds(entityIds);

        // 2.审批流筛选无审批任务数据
        // 部分节点为系统节点，展示当前处理人为责任人，但审批流审批人是 system，该情况不处理审批流
        List<FlowHandler> hasApproveTaskFlows = Lists.newArrayList();
        List<FlowHandler> noneApproveTaskFlows = Lists.newArrayList();
        for (FlowHandler flowHandler : flowHandlers) {
            List<String> approveUserIds = flowHandler.getApproveTaskList().stream()
                    .map(ApproveTask::getApprover)
                    .collect(Collectors.toList());
            if (approveUserIds.contains(ApprovalConstants.SYSTEM_USER)) {
                continue;
            }

            if (approveUserIds.contains(transferInfo.targetUserId)) {
                hasApproveTaskFlows.add(flowHandler);
            } else {
                noneApproveTaskFlows.add(flowHandler);
            }
        }

        // 3.转交审批流任务
        // 保留用户 - 在节点新增审批人对应任务
        // 不保留用户 - 节点转交审批任务 + 删除用户原有审批任务
        if (transferInfo.reserveFlag) {
            addApproveTask(noneApproveTaskFlows, assignments, transferInfo);
        } else {
            transferApproveTask(noneApproveTaskFlows, transferInfo.currentUserId, transferInfo.targetUserId);
            deleteApproveTask(hasApproveTaskFlows, transferInfo.currentUserId);
        }
    }

    /**
     * 转交任务校验 - 责任人 - 网络变更任务
     * key - 补充跳转参数 key   value - 补充跳转参数
     */
    public static Map<String, Object> networkChangeTransferResponsibleCheck(Assignment assignment) {
        // 1.任务状态校验
        // 除了已废止和已关闭状态，其他状态均支持转交任务中心责任人
        String assignmentStatus = assignment.getAssignmentStatus();
        if (AssignmentStatusEnum.CLOSE.getValue().equals(assignmentStatus)
                || AssignmentStatusEnum.ABOLISH.getValue().equals(assignmentStatus)) {
            throw new LcapBusiException(MsgUtils.getMessage(
                    MessageConsts.AssignmentCenter.TRANSFER_ERROR_STATUS_NETWORK_CHANGE));
        }

        // 2.系统管理员校验
        String userId = ContextHelper.getEmpNo();
        boolean systemAdminFlag = checkSystemAdmin(userId);

        // 3.任务责任人校验
        // 任务责任人支持转交
        // 系统管理员不考虑任务责任人
        String responsible = assignment.getResponsibleEmployee().get(0).getEmpUIID();
        if (!systemAdminFlag && !userId.equals(responsible)) {
            throw new LcapBusiException(MsgUtils.getMessage(
                    MessageConsts.AssignmentCenter.TRANSFER_ERROR_RESPONSIBLE));
        }

        // 4.包装跳转额外参数
        return networkChangeResponsibleExtraParameters(assignment, systemAdminFlag);
    }

    /**
     * 转交任务校验 - 责任人 - 批次任务
     * key - 补充跳转参数 key   value - 补充跳转参数
     */
    public static Map<String, Object> batchTransferResponsibleCheck(
            Assignment assignment,
            AssignmentTypeEnum assignmentType) {

        // 1.任务状态校验
        // 除 已关闭 / 已废止 状态，剩余状态均支持转交
        String assignmentStatus = assignment.getAssignmentStatus();
        if (AssignmentStatusEnum.CLOSE.getValue().equals(assignmentStatus)
                || AssignmentStatusEnum.ABOLISH.getValue().equals(assignmentStatus)) {
            throw new LcapBusiException(MsgUtils.getMessage(
                    MessageConsts.AssignmentCenter.TRANSFER_ERROR_STATUS_BATCH));
        }

        // 2.检索主任务
        AssignmentTypeEnum mainAssignmentType
                = AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)
                ? AssignmentTypeEnum.NETWORK_CHANGE
                : AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE;
        Assignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                assignment.getBillId(), mainAssignmentType, Assignment.class);

        // 3.系统管理员校验
        String userId = ContextHelper.getEmpNo();
        boolean systemAdminFlag = checkSystemAdmin(userId);

        // 4.任务责任人校验
        // 主任务责任人 / 批次任务责任人 支持转交
        // 系统管理员不考虑任务责任人
        String mainResponsible = mainAssignment.getResponsibleEmployee().get(0).getEmpUIID();
        String responsible = assignment.getResponsibleEmployee().get(0).getEmpUIID();
        if (!systemAdminFlag
                && !userId.equals(mainResponsible)
                && !userId.equals(responsible)) {
            throw new LcapBusiException(MsgUtils.getMessage(
                    MessageConsts.AssignmentCenter.TRANSFER_ERROR_RESPONSIBLE));
        }

        // 5.包装跳转额外参数
        return networkChangeResponsibleExtraParameters(assignment, systemAdminFlag);
    }

    /**
     * 校验是否系统管理员
     */
    private static boolean checkSystemAdmin(String userId) {
        List<UppUserAuthInfo> systemAdmins
                = UppAbility.queryUsersByRoleNum(RoleEnum.SYSTEM_ADMINISTRATOR.getRoleCode());
        List<String> systemAdminIds = systemAdmins.stream()
                .map(UppUserAuthInfo::getUserNo)
                .distinct()
                .collect(Collectors.toList());
        return systemAdminIds.contains(userId);
    }

    /**
     * 包装特殊跳转参数 - 责任人 - 网络变更类型
     * key - 补充跳转参数 key   value - 补充跳转参数
     */
    private static Map<String, Object> networkChangeResponsibleExtraParameters(
            Assignment assignment,
            boolean systemAdminFlag) {

        Map<String, Object> extraParameters
                = MapUtils.newHashMap(CidConstants.SYSTEM_ADMIN_FLAG, systemAdminFlag);
        if (systemAdminFlag) {
            extraParameters.putAll(MapUtils.newHashMap(
                    CidConstants.RESPONSIBLE_PERSON, assignment.getResponsibleEmployee(),
                    CidConstants.CURRENT_PROCESSOR, assignment.getCurrentProcessorEmployee(),
                    AssignmentFieldConsts.CURRENT_PROGRESS, assignment.getCurrentProgress()));
        }

        return extraParameters;
    }


    /**
     * 转交基础信息对象
     */
    private static final class TransferInfo {

        /** 继续保留用户标识 */
        private final boolean reserveFlag;

        /** 当前用户ID */
        private final String currentUserId;

        /** 当前用户信息 */
        private final SingleEmployee currentUser;

        /** 当前用户已存在标识 */
        private Map<String, Boolean> currentUserExistFlag = new HashMap<>();

        /** 转交用户ID */
        private final String targetUserId;

        /** 转交用户信息 */
        private final SingleEmployee targetUser;

        TransferInfo(boolean reserveFlag, String currentUserId, String targetUserId) {
            this.reserveFlag = reserveFlag;
            this.currentUserId = currentUserId;
            this.targetUserId = targetUserId;

            this.currentUser = EmployeeHelper.getSingle(currentUserId);
            this.targetUser = EmployeeHelper.getSingle(targetUserId);
        }
    }
}
