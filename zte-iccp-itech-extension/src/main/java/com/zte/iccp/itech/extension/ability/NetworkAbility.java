package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.common.table.DataAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FormModelProxyHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.vo.OperandNetworkVO;
import com.zte.iccp.itech.extension.domain.model.vo.TableDisplayRows;
import com.zte.iccp.itech.extension.spi.client.CrmClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.extension.spi.model.nis.ProdClass;
import com.zte.iccp.itech.extension.spi.model.query.NisNetworkQuery;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import com.zte.paas.lcap.ddm.domain.model.elementstatus.ElementStatusEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class NetworkAbility {

    /**
     * 网络查询
     * @param formView
     * @param dataModel
     */
    public static void queryNetwork(IFormView formView, IDataModel dataModel, boolean resetPage) {
        // 1.获取上层页面默认查询条件
        NisNetworkQuery nisNetworkQuery = getConvertNetworkQueryCondition(formView, dataModel, resetPage);

        // 2.查询 NIS 获取网络数据
        PageRows<NisNetwork> pageRows = NisClient.queryNisNetworkList(nisNetworkQuery);

        // 3.包装查询列表展示对象
        TableDisplayRows<OperandNetworkVO> displayRows = convertDisplayNetworkRows(pageRows);

        // 4.页面数据展示
        DataAbility.setNoneEntityTableData(formView, CidConstants.TABLE_OPERAND_QUERY_NETWORK_CID, displayRows);

        // 5.无数据时，展示页面提示语，有数据时，隐藏页面提示语
        Map<String, Object> props = new HashMap<>();
        props.put(CommonConstants.BEHAVIOR, displayRows.getTotal() == 0
                ? ElementStatusEnum.NORMAL.getValue()
                : ElementStatusEnum.HIDDEN.getValue());
        formView.getClientViewProxy().setControlState(CidConstants.CONDITION_PROMPT_CID, props);
    }

    /**
     * 包装网络查询条件（页面条件 + 后台默认条件）
     * @param formView
     * @param dataModel
     * @param resetPage
     * @return NisNetworkQuery
     */
    private static NisNetworkQuery getConvertNetworkQueryCondition(IFormView formView, IDataModel dataModel, boolean resetPage) {
        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();

        // 1.获取表格分页参数
        TablePc tableInfo = (TablePc) formView.getControl(CidConstants.TABLE_OPERAND_QUERY_NETWORK_CID);
        nisNetworkQuery.setPageNum(resetPage ? CommonConstants.DEFAULT_PAGE_NO : tableInfo.getCurrentPage());
        nisNetworkQuery.setPageSize(tableInfo.getPageSize());

        // 2.获取默认查询条件
        // （1）网络状态 - 在服
        nisNetworkQuery.setNetworkStatus(Lists.newArrayList(CommonConstants.ENABLED));

        // （2）是否受限制主体 - 非受限主体网络或受限主体网络在NIS受限主体管理备案过的客户
        nisNetworkQuery.setNonRestrictedOrRegistered(Boolean.TRUE);

        // 3.获取页面查询条件
        // (1) 网络名称 / ID
        String networkNameAndCode = PropertyValueConvertUtil.getString(dataModel.getValue(CidConstants.CONDITION_NETWORK_NAME_CID));
        nisNetworkQuery.setNameAndCodeKeyword(networkNameAndCode);

        // (2) 客户网络名称
        String customerNetworkName = PropertyValueConvertUtil.getString(dataModel.getValue(CidConstants.CONDITION_CUSTOMER_NETWORK_NAME_CID));
        nisNetworkQuery.setCustomerNetworkName(customerNetworkName);

        // (3) 是否政企 Y/N
        String governmentEnterpriseFlag = FormModelProxyHelper.getTextFirstValue(CidConstants.CONDITION_IS_GOVERNMENT_ENTERPRISE_CID, dataModel);
        nisNetworkQuery.setGovEntFlag(BoolEnum.nilValueOf(governmentEnterpriseFlag));

        // (4) 代表处
        List<TextValuePair> representativeOffice = ComponentUtils.getChooseComponentInfo(dataModel, CidConstants.CONDITION_ORGANIZATION_CID);
        nisNetworkQuery.setResponsibleDept(TextValuePairHelper.getValueList(representativeOffice));

        // (5) 网络设备类型
        List<TextValuePair> deviceInfo = ComponentUtils.getChooseComponentInfo(dataModel, CidConstants.CONDITION_DEVICE_CID);
        nisNetworkQuery.setAssetProdClassIdPath(TextValuePairHelper.getValueList(deviceInfo));

        // (6) 网络产品类型
        List<TextValuePair> prodClassInfo = ComponentUtils.getChooseComponentInfo(dataModel, CidConstants.COMPONENT_PRODUCT_TREE_CID);
        nisNetworkQuery.setProdClass(TextValuePairHelper.getValueList(prodClassInfo));

        return nisNetworkQuery;
    }

    /**
     * 包装列表展示数据
     */
    public static TableDisplayRows<OperandNetworkVO> convertDisplayNetworkRows(
            PageRows<NisNetwork> networkPageRows) {

        TableDisplayRows<OperandNetworkVO> tableDisplayRows = new TableDisplayRows<>();
        List<NisNetwork> networkList = networkPageRows.getRows();
        if (CollectionUtils.isEmpty(networkPageRows.getRows())) {
            return tableDisplayRows;
        }

        // 1.数据准备 - 查询客户信息
        List<String> customerIdList = networkList.stream()
                .map(NisNetwork::getCustomerId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> customerNameMap = CrmClient.queryCustomerNameInfo(customerIdList);

        // 2.包装列表数据
        String langId = ContextHelper.getLangId();
        List<OperandNetworkVO> displayNetworkList = Lists.newArrayList();
        for (NisNetwork network : networkList) {
            OperandNetworkVO displayNetwork = new OperandNetworkVO();

            // (1) 基础网络属性
            displayNetwork.setNetworkId(network.getNetworkId());
            displayNetwork.setNetworkCode(network.getNetworkCode());
            displayNetwork.setNetworkName(LangUtils.get(network.getNameZh(), network.getNameEn()));
            displayNetwork.setResponsibleDept(network.getResponsibleDeptName());
            displayNetwork.setCustomerNetworkName(network.getCustomerNetworkName());
            displayNetwork.setResponsiblePerson(network.getResponsiblePersonName());
            displayNetwork.setGovEntFlag(network.getGovEntFlag().getName(langId));
            displayNetwork.setRestrictedFlag(network.getRestrictedFlag().getName(langId));
            displayNetwork.setCustomerName(customerNameMap.get(network.getCustomerId()));
            displayNetwork.setOperatorGroupNetwork(network.getGrpDrctMngFlag().getName(langId));

            // (2) 产品分类属性
            List<ProdClass> prodClasses = network.getProdClasses();
            if (!CollectionUtils.isEmpty(prodClasses)) {
                List<String> prodClassNames = prodClasses.stream()
                        .map(item -> LangUtils.get(item.getProdClassNameZh(), item.getProdClassNameEn()))
                        .filter(StringUtils::hasText)
                        .distinct()
                        .collect(Collectors.toList());
                displayNetwork.setProdClass(String.join(CommonConstants.COMMA, prodClassNames));
            }

            // (3) 网络负责组
            List<String> responsibleGroup = network.getRespGroupMemberNames();
            if (!CollectionUtils.isEmpty(responsibleGroup)) {
                displayNetwork.setResponsibleTeam(String.join(CommonConstants.COMMA, responsibleGroup));
            }

            displayNetworkList.add(displayNetwork);
        }

        tableDisplayRows.setTotal(networkPageRows.getTotal());
        tableDisplayRows.setCurrent(networkPageRows.getCurrent());
        tableDisplayRows.setRecords(displayNetworkList);

        return tableDisplayRows;
    }
}
