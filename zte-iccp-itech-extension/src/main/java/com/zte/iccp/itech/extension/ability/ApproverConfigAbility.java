package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ApproverConfigurationUpdate;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.infrastructure.conditions.ApproverCfgQueryCondition;
import com.zte.iccp.itech.extension.infrastructure.repositories.ApproverConfigRepo;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.ENGINEERING_SERVICE_OPERATION;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.ENGINEERING_SERVICE_THREE_PARTS_OPERATION;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum.*;
import static com.zte.paas.lcap.metadata.engine.common.constant.NumberConst.FOUR;
import static com.zte.paas.lcap.metadata.engine.common.constant.NumberConst.THREE;

/**
 * <AUTHOR>
 * @create 2024/7/15 上午10:06
 */
public class ApproverConfigAbility {

    /*
     * 通用
     * 根据产品类型，角色，产品，是否政企，代表处,查询审核人 优先级：产品小类->产品大类->产品线依次匹配
     * @param configuration  过滤条件
     * @param type null 取两个字段 ，0 取人员 , 1 取审核组
     * */
    public static List<String> getApprovalPersons(ApproverConfiguration configuration, Integer personType) {
        ApproverConfiguration approverConfiguration = getApproverConf(configuration);
        return getApprovalPersonsByType(approverConfiguration, personType);
    }

    public static ApproverConfiguration getApproverConf(ApproverConfiguration configuration) {
        List<ApproverConfiguration> approvers = findApproverConfiguration(configuration);
        if (CollectionUtils.isEmpty(approvers)) {
            return null;
        }
        return approvers.get(0);
    }

    /**
     * 优先级  必传优先级最小的字段
     * 根据产品类型，角色，产品，是否政企，代表处,查询审核人 优先级：产品小类->产品大类->产品线依次匹配
     * @param configuration  过滤条件
     * @param  productId  变跟单产品ID;   优先级必填
     * @param priorityType 0->产品小类->产品大类->产品线;
     * 1->产品线>产品经营团队;
     * 3->产品型号->产品小类->产品大类->产品线依次匹配;
     * @param personType null 取两个字段 ，0 取人员 , 1 取审核组
     */
    public static List<String> getApprovalPriorityPersons(ApproverConfiguration configuration, String productId,
                                                          Integer priorityType, Integer personType) {
        ApproverConfiguration approverConfig = getApprovalConfiguration(configuration, productId, priorityType);
        return getApprovalPersonsByType(approverConfig, personType);
    }

    public static ApproverConfiguration getApprovalConfiguration(ApproverConfiguration configuration, String productId,
                                                                 Integer priorityType) {
        List<ApproverConfiguration> approvers = new ArrayList<>();
        if (!StringUtils.hasText(productId)) {
            return null;
        }

        if (priorityType == 0) {
            // 优先级：产品小类->产品大类->产品线依次匹配
            approvers = findPriorityApproverConfig(configuration, productId);
        } else if (priorityType == 1) {
            // 优先级：产品线>产品经营团队
            approvers = findLineApproverConfig(configuration, productId);
        } else if (priorityType == THREE) {
            // 3 优先级：产品型号->产品小类->产品大类->产品线依次匹配
            approvers = findPriorityProduct(configuration, productId);
        } else if (priorityType == FOUR) {
            // 4 优先级：逻辑逻辑网元->产品小类->产品大类->产品线依次匹配
            approvers = findPriorityLogicNe(configuration, productId);
        }

        if (CollectionUtils.isEmpty(approvers)) {
            return null;
        }

        ApproverConfiguration approverConfig = approvers.get(0);
        return approverConfig;
    }


    public static List<Map<String, Object>> getPermissionAppOptions(ApprovalTypeEnum approvalTypeEnum,
                                                                    List<ApproveRoleEnum> approveRoleEnums,
                                                                    String orgIdPath, List<String> prodOperationTeamIds) {
        String[] productId = orgIdPath.split(FORWARD_SLASH);

        ApproverCfgQueryCondition condition = new ApproverCfgQueryCondition(approvalTypeEnum);
        condition.setRoles(approveRoleEnums);
        if (productId.length == INTEGER_THREE) {
            condition.setOrganizationRegion(orgIdPath);
        } else if (productId.length == INTEGER_FOUR) {
            condition.setResponsibleDept(orgIdPath);
        } else {
            return Lists.newArrayList();
        }

        if (PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE.equals(approvalTypeEnum)) {
            if (!CollectionUtils.isEmpty(prodOperationTeamIds)) {
                condition.setProdOperationTeams(prodOperationTeamIds);
            } else {
                condition.setProdOperationTeamIsEmpty(true);
            }
        }

        List<ApproverConfiguration> configurations = ApproverConfigRepo.query(condition);
        return getOptions(approvalTypeEnum, configurations);

    }

    private static List<Map<String, Object>> getOptions(ApprovalTypeEnum approvalTypeEnum, List<ApproverConfiguration> configurations) {
        OptionsBuilder optionsBuilder = new OptionsBuilder();
        Set<String> userIds = new HashSet<>();
        for (ApproverConfiguration configuration : configurations) {
            List<Employee> approvers = Lists.newArrayList();
            switch (approvalTypeEnum) {
                case ADMINISTRATIVE_LEADER_DIVISION_THREE:
                    approvers.addAll(configuration.getApproverPersons());
                    break;
                case ADMINISTRATIVE_LEADER_RESPONSIBLE_DEPT_COUNTERSIGNING:
                    approvers.addAll(configuration.getApproverGroups());
                    break;
                case PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE:
                    approvers.addAll(configuration.getApproverGroups());
                    approvers.addAll(configuration.getApproverPersons());
                    break;
                default:
                    break;
            }
            if (CollectionUtils.isEmpty(approvers)) {
                continue;
            }
            approvers.forEach(item -> {
                if (null != item && userIds.add(item.getEmpUIID())) {
                    String textZh = item.getEmpNameCn() + item.getEmpUIID();
                    String textEn = item.getEmpNameEn() + item.getEmpUIID();
                    String value = EN_US.equals(ContextHelper.getLangId()) ? textEn : textZh;
                    optionsBuilder.addOption(new Option(value, new Text(textZh, textEn)));
                }
            });
        }

        return optionsBuilder.build();
    }


    public static List<ApproverConfiguration> getByProdSub(String prodSubIdPath) {
        if (null == prodSubIdPath) {
            return new ArrayList<>();
        }

        ApproverCfgQueryCondition condition = new ApproverCfgQueryCondition(ApprovalTypeEnum.INTEGRATE_ASSOCIATED_PRODUCT);
        condition.setProdSubCategories(Lists.newArrayList(prodSubIdPath));
        condition.setBillStatus(AvailabilityEnum.ENABLED.name());

        return ApproverConfigRepo.query(condition);
    }

    public static ApproverConfiguration getByLogicalIdAndProdId(String logicalId, String prodSubIdPath) {
        if (null == logicalId || null == prodSubIdPath) {
            return null;
        }

        ApproverCfgQueryCondition condition = new ApproverCfgQueryCondition(INTEGRATE_ASSOCIATED_PRODUCT);
        condition.setProdSubCategories(Lists.newArrayList(prodSubIdPath));
        condition.setLogicalNe(logicalId);
        condition.setBillStatus(AvailabilityEnum.ENABLED.name());

        List<ApproverConfiguration> configurations = ApproverConfigRepo.query(condition);
        return CollectionUtils.isEmpty(configurations) ? null : configurations.get(0);
    }

    /*
     * 通过条件查询审核人员表
     * @param configuration  过滤条件
     * */
    public static List<ApproverConfiguration> findApproverConfiguration(ApproverConfiguration configuration) {
        ApproverCfgQueryCondition condition = new ApproverCfgQueryCondition(configuration.getApprovalNode());

        // 角色
        if (configuration.getRole() != null) {
            condition.setRoles(Lists.newArrayList(configuration.getRole()));
        }
        // 是否政企
        condition.setIsGovEnt(configuration.getIsGov());
        // 状态
        condition.setBillStatus(configuration.getBillStatus());
        // 运营商
        if (StringUtils.hasText(configuration.getOperator())) {
            condition.setOperator(configuration.getOperator());
        } else {
            condition.setOperatorIsEmpty(true);
        }
        // 操作类型
        if (!CollectionUtils.isEmpty(configuration.getOperationType())) {
            condition.setOperationTypes(configuration.getOperationType());
        } else {
            condition.setOperationTypeIsEmpty(true);
        }
        // 营销单位
        if (StringUtils.hasText(configuration.getSales())) {
            condition.setSales(configuration.getSales());
        } else {
            condition.setSalesIsEmpty(true);
        }
        // 片区
        if (StringUtils.hasText(configuration.getOrganizationRegion())) {
            condition.setOrganizationRegion(configuration.getOrganizationRegion());
        } else {
            condition.setOrganizationRegionIsEmpty(true);
        }
        // 代表处
        if (StringUtils.hasText(configuration.getResponsibleDeptId())) {
            condition.setResponsibleDept(configuration.getResponsibleDeptId());
        } else {
            condition.setResponsibleDeptIsEmpty(true);
        }
        addFilters(configuration, condition);

        return ApproverConfigRepo.query(condition);
    }

    private static void addFilters(ApproverConfiguration configuration, ApproverCfgQueryCondition condition) {
        //  产品团队
        if (StringUtils.hasText(configuration.getProdTeam())) {
            condition.setProdOperationTeams(Lists.newArrayList(configuration.getProdTeam()));
        } else {
            condition.setProdOperationTeamIsEmpty(true);
        }
        //  产品线
        if (StringUtils.hasText(configuration.getProdLine())) {
            condition.setProdLines(Lists.newArrayList(configuration.getProdLine()));
        } else {
            condition.setProdLineIsEmpty(true);
        }
        //  产品大类
        if (StringUtils.hasText(configuration.getProdMainCategory())) {
            condition.setProdMainCategories(Lists.newArrayList(configuration.getProdMainCategory()));
        } else {
            condition.setProdMainCategoryIsEmpty(true);
        }
        //  产品小类
        if (StringUtils.hasText(configuration.getProdSubCategory())) {
            condition.setProdSubCategories(Lists.newArrayList(configuration.getProdSubCategory()));
        } else {
            condition.setProdSubCategoryIsEmpty(true);
        }
        //  产品型号
        if (StringUtils.hasText(configuration.getProductModelId())) {
            condition.setProductModels(Lists.newArrayList(configuration.getProductModelId()));
        } else {
            condition.setProductModelIsEmpty(true);
        }

        // 逻辑网元
        if (StringUtils.hasText(configuration.getLogicalNe())) {
            condition.setLogicalNe(configuration.getLogicalNe());
        } else {
            condition.setLogicalNeIsEmpty(true);
        }
    }

    /*
     * 通过条件查询审核人员表
     * @param configuration  过滤条件
     * */
    public static ApproverConfiguration getApproverConfiguration(ApproverConfiguration configuration) {
        List<ApproverConfiguration> approverConfiguration = findApproverConfiguration(configuration);
        return CollectionUtils.isEmpty(approverConfiguration) ? null : approverConfiguration.get(0);
    }

    public static List<String> getPersonUuidList(List<Employee> employees) {
        if (CollectionUtils.isEmpty(employees)) {
            return new ArrayList<>();
        }
        return employees.stream().map(Employee::getEmpUIID).collect(Collectors.toList());
    }

    /*
     * 优先级：产品小类->产品大类->产品线依次匹配
     * */
    private static List<ApproverConfiguration> findPriorityApproverConfig(ApproverConfiguration configuration, String productId) {
        // 产品小类
        configuration.setProdSubCategory(productId);
        configuration.setProdMainCategory(ProductUtils.getMain(productId));
        configuration.setProdLine(ProductUtils.getLine(productId));
        configuration.setProdTeam(ProductUtils.getTeam(productId));
        List<ApproverConfiguration> approverConfiguration = findApproverConfiguration(configuration);
        // 产品大类
        if (CollectionUtils.isEmpty(approverConfiguration)) {
            configuration.setProdSubCategory(null);
            approverConfiguration = findApproverConfiguration(configuration);
        }
        // 产品线
        if (CollectionUtils.isEmpty(approverConfiguration)) {
            configuration.setProdMainCategory(null);
            approverConfiguration = findApproverConfiguration(configuration);
        }

        return approverConfiguration;
    }

    /*
     * 优先级：产品线->产品团队依次匹配
     * */
    private static List<ApproverConfiguration> findLineApproverConfig(ApproverConfiguration configuration, String productId) {
        // 产品线
        configuration.setProdLine(ProductUtils.getLine(productId));
        configuration.setProdTeam(ProductUtils.getTeam(productId));
        List<ApproverConfiguration> approverConfiguration = findApproverConfiguration(configuration);
        // 产品团队
        if (CollectionUtils.isEmpty(approverConfiguration)) {
            configuration.setProdLine(null);
            configuration.setProdTeam(ProductUtils.getTeam(productId));
            approverConfiguration = findApproverConfiguration(configuration);
        }

        return approverConfiguration;
    }

    /*
     * 优先级：产品型号->产品小类->产品大类->产品线依次匹配
     * */
    private static List<ApproverConfiguration> findPriorityProduct(ApproverConfiguration configuration, String productId) {
        // 产品型号
        configuration.setProductModelId(productId);
        configuration.setProdSubCategory(ProductUtils.getSub(productId));
        configuration.setProdMainCategory(ProductUtils.getMain(productId));
        configuration.setProdLine(ProductUtils.getLine(productId));
        configuration.setProdTeam(ProductUtils.getTeam(productId));
        List<ApproverConfiguration> approverConfiguration = findApproverConfiguration(configuration);
        if (!CollectionUtils.isEmpty(approverConfiguration)) {
            return approverConfiguration;
        }
        configuration.setProductModelId(null);
        // 将5层产品型号切成4层产品小类作为参数传下去调小类->大类->产品线的方法
        return findPriorityApproverConfig(configuration, ProductUtils.getSub(productId));
    }

    /*
     * 优先级：逻辑网元->产品小类->产品大类->产品线依次匹配
     * */
    private static List<ApproverConfiguration> findPriorityLogicNe(ApproverConfiguration configuration, String productId) {
        // 逻辑网元
        configuration.setProdSubCategory(ProductUtils.getSub(productId));
        configuration.setProdMainCategory(ProductUtils.getMain(productId));
        configuration.setProdLine(ProductUtils.getLine(productId));
        configuration.setProdTeam(ProductUtils.getTeam(productId));
        if(StringUtils.hasText(configuration.getLogicalNe())){
            List<ApproverConfiguration> approverConfiguration = findApproverConfiguration(configuration);
            if (!CollectionUtils.isEmpty(approverConfiguration)) {
                return approverConfiguration;
            }
        }

        // 拷贝一个查询条件，置空逻辑网元，不修改原来查询条件
        ApproverConfiguration newQueryConifg = new ApproverConfiguration();
        BeanUtils.copyProperties(configuration, newQueryConifg);
        newQueryConifg.setLogicalNe(null);
        return findPriorityApproverConfig(newQueryConifg, productId);
    }

    /*
     * @param type null 取两个字段 ，0 取人员 , 1 取审核组
     * */
    public static List<String> getApprovalPersonsByType(ApproverConfiguration approverConfig, Integer personType) {
        List<String> approvalList = new ArrayList<>();
        if (approverConfig == null) {
            return approvalList;
        }

        if (personType == null) {
            approvalList.addAll(getPersonUuidList(approverConfig.getApproverPersons()));
            approvalList.addAll(getPersonUuidList(approverConfig.getApproverGroups()));
        } else if (0 == personType) {
            approvalList.addAll(getPersonUuidList(approverConfig.getApproverPersons()));
        } else {
            approvalList.addAll(getPersonUuidList(approverConfig.getApproverGroups()));
        }
        return approvalList.stream().distinct().collect(Collectors.toList());
    }

    public static <T extends ApproverConfigurationUpdate> boolean batchUpdate(List<T> configurationList) {
        if (CollectionUtils.isEmpty(configurationList)) {
            return true;
        }

        return SaveDataHelper.batchUpdate(configurationList);
    }

    public static <T extends ApproverConfigurationUpdate> List<String> batchCreate(List<T> configurationList) {
        if (CollectionUtils.isEmpty(configurationList)) {
            return Lists.newArrayList();
        }

        return SaveDataHelper.batchCreate(configurationList);
    }

    /*
     * 通过审核类型和关联基础配置表id查询审核人员表
     *
     * @param approvalTypes  审批类型
     * @param integratedAssociatedProds  关联id
     * */
    public static List<ApproverConfiguration> query(List<String> approvalTypes, List<String> integratedAssociatedProds) {
        if (CollectionUtils.isEmpty(integratedAssociatedProds)) {
            return Lists.newArrayList();
        }

        List<IFilter> filters = new ArrayList<>();
        if (!CollectionUtils.isEmpty(approvalTypes)) {
            filters.add(new Filter(APPROVAL_NODE, Comparator.CONTAINS, approvalTypes));
        }
        filters.add(new Filter(INTEGRATED_ASSOCIATED_PRODUCT_ZH, Comparator.IN, integratedAssociatedProds));
        return QueryDataHelper.query(ApproverConfiguration.class, Lists.newArrayList(), filters);
    }

    public static List<ApproverConfiguration> queryByAssociatedProdIds(List<String> associatedProdIds) {
        if (CollectionUtils.isEmpty(associatedProdIds)) {
            return Lists.newArrayList();
        }

        List<IFilter> filters = new ArrayList<>();
        filters.add(new Filter(APPROVAL_NODE, Comparator.IN, Arrays.asList(ApprovalTypeEnum.INTEGRATE_ASSOCIATED_PRODUCT.name())));
        filters.add(new Filter(MULTI_ASSOCIATED_PROD_ZH, Comparator.IN, associatedProdIds));
        return QueryDataHelper.query(ApproverConfiguration.class, Lists.newArrayList(), filters);
    }

    public static List<ApproverConfiguration> query(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return QueryDataHelper.get(ApproverConfiguration.class, Lists.newArrayList(), ids);
    }

    public static ApproverConfiguration get(String id) {
        if (null == id) {
            return null;
        }
        return QueryDataHelper.get(ApproverConfiguration.class, Lists.newArrayList(), id);
    }

    public static void update(ApproverConfiguration configuration) {
        if (null == configuration) {
            return;
        }
        SaveDataHelper.update(configuration);
    }

    public static void batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        SaveDataHelper.batchDelete(ApproverConfiguration.class, ids);
    }

    /**
     * 获取代表处审核处理人
     *
     * @param responsibleDeptId 代表处
     * @param productId 产品分类
     * @param role 角色
     * @param isGov 是否政企
     * @return ApproverConfiguration
     */
    public static ApproverConfiguration getRepProdTdApprover(String responsibleDeptId,
                                                             String productId,
                                                             ApproveRoleEnum role,
                                                             BoolEnum isGov) {
        // 代表处->片区->营销单位
        for (int i = 0; i < 3; i++) {
            // 根据【代表处】+【产品（产品线>产品经营团队）】+【是否政企】（如果代表处是国际，则不考虑【是否政企】取值）确定审批配置记录，查询【代表处方案审核人】字段数据-
            ApproverConfiguration queryParam = new ApproverConfiguration();
            // 代表处
            queryParam.setResponsibleDeptId(responsibleDeptId);
            // 审批节点 - 代表处人员配置
            queryParam.setApprovalNode(PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE);
            // 角色
            queryParam.setRole(role);
            if (DeptTypeEnum.INNER == ResponsibleUtils.getDeptType(responsibleDeptId)) {
                // 是否政企
                queryParam.setIsGov(isGov);
            }
            ApproverConfigAbility.buildResponsibleDept(queryParam, responsibleDeptId, i);
            ApproverConfiguration approvalConfiguration = ApproverConfigAbility.getApprovalConfiguration(
                    queryParam, productId, 1);
            if (approvalConfiguration != null) {
                return approvalConfiguration;
            }
        }
        return null;
    }

    /**
     * 技术交付部/网络处审核、网络服务部审核审批页获取处理人
     *
     * @param changeOrder 变更单
     * @param productModelId 产品型号：申请单的操作对象列表中取第一个产品型号
     * @param approvalTypeEnum 审批类型
     * @return ApproverConfiguration
     */
    public static ApproverConfiguration getApprover(IChangeOrder changeOrder,
                                                    String productModelId,
                                                    ApprovalTypeEnum approvalTypeEnum) {
        // 注：当前产品型号取值 - 申请单的操作对象列表中取第一个产品型号
        if (StringUtils.hasText(productModelId)) {
            productModelId = NisAbility.queryProductModelIdPath(Lists.newArrayList(productModelId)).get(INTEGER_ZERO);
        }

        ApproverConfiguration approverConfiguration = null;
        // 1.代表处属于国内营销或工程服务经营部-工程服务国内部
        if (DeptTypeEnum.INNER == ChangeOrderAbility.getDeptType(changeOrder)) {
            // 1.1 是否政企=否
            if (BoolEnum.N == changeOrder.getIsGovEnt()) {
                approverConfiguration = getInnerGovEntIsNo(productModelId, approvalTypeEnum, changeOrder);
            } else {
                // 1.2 是否政企=是
                approverConfiguration = getInnerGovEntIsYes(productModelId, approvalTypeEnum, changeOrder);
            }
            // 3.（国际）代表处属于工程服务经营部但不属于工程服务国内部时，不考虑是否政企：
        } else if (changeOrder.getResponsibleDept().startsWith(ENGINEERING_SERVICE_OPERATION)
                && !changeOrder.getResponsibleDept().startsWith(ENGINEERING_SERVICE_THREE_PARTS_OPERATION)) {
            approverConfiguration = getInter(productModelId, approvalTypeEnum, changeOrder);
        }

        return approverConfiguration;
    }


    /**
     * （国际）代表处属于工程服务经营部但不属于工程服务国内部时，不考虑是否政企：
     *
     * @param productModelId   产品型号
     * @param approvalTypeEnum 审批类型
     * @param changeOrder      代表处、产品分类
     * @return ApproverConfiguration
     */
    private static ApproverConfiguration getInter(String productModelId,
                                                  ApprovalTypeEnum approvalTypeEnum,
                                                  IChangeOrder changeOrder) {
        // 【产品型号>产品小类>产品大类>产品线】+【组织】+操作类型,其他条件为空的精确匹配
        ApproverConfiguration approvalConfiguration = getApproverConfiguration(productModelId, approvalTypeEnum, changeOrder, true);
        if (approvalConfiguration != null) {
            return approvalConfiguration;
        }
        // 【产品型号>产品小类>产品大类>产品线】+【组织】,其他条件为空的精确匹配
        return getApproverConfiguration(productModelId, approvalTypeEnum, changeOrder, false);
    }

    private static ApproverConfiguration getApproverConfiguration(String productModelId,
                                                                  ApprovalTypeEnum approvalTypeEnum,
                                                                  IChangeOrder changeOrder,
                                                                  boolean needOperationType) {
        for (int i = 0; i < INTEGER_THREE; i++) {
            ApproverConfiguration queryParam = new ApproverConfiguration();
            queryParam.setApprovalNode(approvalTypeEnum);
            queryParam.setLogicalNe(changeOrder.getLogicalNe());
            buildResponsibleDept(queryParam, changeOrder.getResponsibleDept(), i);
            if (needOperationType) {
                queryParam.setOperationType(Lists.newArrayList(changeOrder.getOperationType()));
            }

            ApproverConfiguration approvalConfiguration = getApprovalConfiguration(queryParam, productModelId,
                    changeOrder.getProductCategory(), changeOrder instanceof ChangeOrder);
            if (approvalConfiguration != null) {
                return approvalConfiguration;
            }
        }
        return null;
    }


    /**
     * 代表处属于国内营销或工程服务经营部-工程服务国内部，且是否政企=是
     *
     * @param productModelId   产品型号
     * @param approvalTypeEnum 审批类型
     * @param changeOrder      操作类型、代表处、产品分类
     * @return List<ApproverConfiguration>
     */
    private static ApproverConfiguration getInnerGovEntIsYes(String productModelId,
                                                             ApprovalTypeEnum approvalTypeEnum,
                                                             IChangeOrder changeOrder) {
        boolean isInternal = changeOrder instanceof ChangeOrder;
        for (int i = 0; i < INTEGER_THREE; i++) {
            ApproverConfiguration queryParam = new ApproverConfiguration();
            queryParam.setApprovalNode(approvalTypeEnum);
            queryParam.setLogicalNe(changeOrder.getLogicalNe());
            // a.根据【代表处】+【是否政企】=是（对应申请单的政企）+产品（产品型号>产品小类>产品大类>产品线）+操作类型精确匹配 ，其他条件为空的精确匹配：取人+组，并且作为当前处理人；
            queryParam.setIsGov(BoolEnum.Y);
            queryParam.setOperationType(Lists.newArrayList(changeOrder.getOperationType()));

            buildResponsibleDept(queryParam, changeOrder.getResponsibleDept(), i);
            ApproverConfiguration approvalConfiguration = getApprovalConfiguration(queryParam, productModelId,
                    changeOrder.getProductCategory(), isInternal);
            // b.根据【代表处】+【是否政企】=是（对应申请单的政企）+产品（产品型号>产品小类>产品大类>产品线） ，其他条件为空的精确匹配：取人+组，并且作为当前处理人；
            if (approvalConfiguration == null) {
                queryParam.setOperationType(null);
                approvalConfiguration = getApprovalConfiguration(queryParam, productModelId,
                        changeOrder.getProductCategory(), isInternal);
            }
            if (approvalConfiguration != null) {
                return approvalConfiguration;
            }
        }
        return null;
    }

    /**
     * 代表处属于国内营销或工程服务经营部-工程服务国内部，且是否政企=否
     *
     * @param productModelId   产品型号
     * @param approvalTypeEnum 审核类型
     * @param changeOrder      客户标识、产品分类、操作类型
     * @return ApproverConfiguration
     */
    private static ApproverConfiguration getInnerGovEntIsNo(String productModelId,
                                                            ApprovalTypeEnum approvalTypeEnum,
                                                            IChangeOrder changeOrder) {
        // 产品分类
        String productId = changeOrder.getProductCategory();
        boolean isInternal = changeOrder instanceof ChangeOrder;
        // 先从代表处、然后从片区、最后从营销进行匹配。任意命中其一则结束方法
        for (int i = 0; i < INTEGER_THREE; i++) {
            // a.代表处+申请单操作类型精确匹配+【运营商】精确匹配申请单的客户标识）+是否政企=否+产品（产品型号>产品小类>产品大类>产品线），其他条件为空
            ApproverConfiguration queryParam = new ApproverConfiguration();
            queryParam.setApprovalNode(approvalTypeEnum);
            queryParam.setLogicalNe(changeOrder.getLogicalNe());
            // 操作类型
            queryParam.setOperationType(Lists.newArrayList(changeOrder.getOperationType()));
            // 运营商：申请单的客户标识
            queryParam.setOperator(OperatorEnum.getCnnValue(changeOrder.getCustomerTypeFlag()));
            queryParam.setIsGov(BoolEnum.N);
            // 代表处、片区、营销单位处理
            buildResponsibleDept(queryParam, changeOrder.getResponsibleDept(), i);
            ApproverConfiguration approvalConfiguration = getApprovalConfiguration(queryParam, productModelId, productId, isInternal);

            // b.代表处+是否政企=否+产品（产品型号>产品小类>产品大类>产品线）+【运营商】（读取申请单的客户标识），其他条件为空
            if (approvalConfiguration == null) {
                queryParam.setOperationType(null);
                approvalConfiguration = getApprovalConfiguration(queryParam, productModelId, productId, isInternal);
                // c.代表处+申请单操作类型精确匹配申请单+是否政企=否+产品（产品型号>产品小类>产品大类>产品线），其他条件为空
                if (approvalConfiguration == null) {
                    queryParam.setOperator(null);
                    queryParam.setOperationType(Lists.newArrayList(changeOrder.getOperationType()));
                    approvalConfiguration = getApprovalConfiguration(queryParam, productModelId, productId, isInternal);
                    // d.d.代表处+是否政企=否+产品（产品小类>产品大类>产品线），其他条件为空
                    if (approvalConfiguration == null) {
                        queryParam.setOperationType(null);
                        approvalConfiguration = getApprovalConfiguration(queryParam, productModelId, productId, isInternal);
                    }
                }
            }

            // 每一次循环跳出条件
            if (approvalConfiguration != null) {
                return approvalConfiguration;
            }
        }
        return null;
    }

    /**
     * 根据构建代表处、片区和营销查询条件
     *
     * @param queryParam 构建查询条件
     * @param responsibleDept 代表处
     * @param i i
     */
    private static void buildResponsibleDept(ApproverConfiguration queryParam, String responsibleDept, int i) {
        if (StringUtils.isEmpty(responsibleDept)) {
            return;
        }
        // 片区
        queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
        // 营销单位
        queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));

        // i = 0为代表处查询：为代表处、片区、营销单位赋值
        if (i == 0) {
            queryParam.setResponsibleDeptId(responsibleDept);
            // i = 1为片区查询：为片区、营销单位赋值，代表处置空
        } else if (i == 1) {
            queryParam.setResponsibleDeptId(null);
            // i = 2为营销查询：为营销单位赋值，代表处置空
        } else {
            queryParam.setResponsibleDeptId(null);
            queryParam.setOrganizationRegion(null);
        }
    }

    /**
     * 检索审批配置信息，优先：产品型号(逻辑网元)->产品小类->产品大类->产品线依次匹配
     * 次：产品小类->产品大类->产品线依次匹配
     *
     * @param configuration  查询参数
     * @param productModelId 产品型号
     * @param isInternal 是否内部单
     * @return productId 产品分类
     */
    public static ApproverConfiguration getApprovalConfiguration(ApproverConfiguration configuration,
                                                                 String productModelId,
                                                                 String productId,
                                                                 boolean isInternal) {
        ApproverConfiguration approvalConfiguration;
        if (ProductUtils.isCcn(productId) && isInternal) {
            return getApprovalConfiguration(configuration, productId, FOUR);
        }

        if (StringUtils.hasText(productModelId)) {
            approvalConfiguration = getApprovalConfiguration(configuration, productModelId, 3);
            if (approvalConfiguration != null) {
                return approvalConfiguration;
            }
        }

        return getApprovalConfiguration(configuration, productId, 0);
    }

    /**
     * 人员积分验收人查询审核人
     */
    public static List<Employee> getPersonnelApprover(String orgId, List<ApproveRoleEnum> roleEnums) {
        // 构建查询条件
        List<IFilter> filters = Lists.newArrayList(
                new Filter(APPROVAL_NODE, Comparator.EQ, Lists.newArrayList(ADMINISTRATIVE_LEADER_RESPONSIBLE_DEPT_COUNTERSIGNING)),
                new Filter(ROLE, Comparator.IN, roleEnums),
                new Filter(RESPONSIBLE_DEPT, Comparator.EQ, Lists.newArrayList(orgId)));

        // 查询配置
        List<ApproverConfiguration> approverConfigurations = QueryDataHelper.query(ApproverConfiguration.class,
                Lists.newArrayList(ID, APPROVER_GROUP),
                filters);

        // 返回审批人列表，如果未找到配置则返回空列表
        return approverConfigurations.stream()
                .map(ApproverConfiguration::getApproverGroups)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }
}
