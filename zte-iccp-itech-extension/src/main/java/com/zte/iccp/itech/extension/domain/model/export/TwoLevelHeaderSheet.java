package com.zte.iccp.itech.extension.domain.model.export;

import com.zte.iccp.itech.extension.domain.enums.export.ExportFieldEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 支持二级表头的Sheet
 *
 * @param <T> 数据类型
 */
@Getter
@Setter
public class TwoLevelHeaderSheet<T> extends BaseSheet<T> {

    /**
     * 一级表头信息
     */
    private List<ExportFieldEnum.ExportHeaderInfo> headerInfos;

    /**
     * 二级表头字段列表
     */
    private List<ExportFieldEnum> exportFields;
} 