package com.zte.iccp.itech.extension.common.utils;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.control.Control;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.CONTENT;
@Slf4j
public class ComponentUtils {

    /**
     * 获取 单选 / 下拉单选 / 复选 / 下拉复选 组件填报内容
     * @param dataModel
     * @param fieldKey
     * @return List<TextValuePair>
     */
    public static List<TextValuePair> getChooseComponentInfo(IDataModel dataModel, String fieldKey) {
        List<Map<String, Object>> chooseComponentInfo = PropertyValueConvertUtil.getListMap(dataModel.getValue(fieldKey));
        if (CollectionUtils.isEmpty(chooseComponentInfo)) {
            return Lists.newArrayList();
        }

        // 下拉返回的值有空对象，需要先过滤
        List<Map<String, Object>> filterChooseComponentInfo = chooseComponentInfo.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterChooseComponentInfo)) {
            return Lists.newArrayList();
        }

        return JsonUtils.parseArray(JsonUtils.toJsonString(chooseComponentInfo), TextValuePair.class);
    }

    /**
     * 获取 员工 组件填报内容
     * @param dataModel
     * @param fieldKey
     * @return List<Employee>
     */
    public static List<Employee> getEmployeeComponentInfo(IDataModel dataModel, String fieldKey) {
        List<Map<String, Object>> employeeComponentInfo = PropertyValueConvertUtil.getListMap(dataModel.getValue(fieldKey));
        if (CollectionUtils.isEmpty(employeeComponentInfo)) {
            return Lists.newArrayList();
        }

        return JsonUtils.parseArray(JsonUtils.toJsonString(employeeComponentInfo), Employee.class);
    }

    /**
     * 获取 子表单 员工 组件填报内容
     * @param dataModel
     * @param fieldKey
     * @param rowIndex
     * @return List<Employee>
     */
    public static List<Employee> getEmployeeComponentInfo(IDataModel dataModel, String fieldKey, Integer rowIndex) {
        List<Map<String, Object>> employeeComponentInfo = PropertyValueConvertUtil.getListMap(dataModel.getValue(fieldKey, rowIndex));
        if (CollectionUtils.isEmpty(employeeComponentInfo)) {
            return Lists.newArrayList();
        }

        return JsonUtils.parseArray(JsonUtils.toJsonString(employeeComponentInfo), Employee.class);
    }

    /**
     * 获取 日期 组件填报内容
     * @param dataModel  布局数据模型
     * @param fieldKey   表单 - 实体唯一标识   自定义页面 - 组件cid
     * @return Date
     */
    public static Date getDateComponentInfo(IDataModel dataModel, String fieldKey) {
        LocalDateTime dateComponentInfo = PropertyValueConvertUtil.getDate(dataModel.getValue(fieldKey));
        if (Objects.isNull(dateComponentInfo)) {
            return null;
        }

        return Date.from(dateComponentInfo.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取 日期区间 组件填报内容
     * left - 起始时间   right - 结束时间
     * @param dataModel
     * @param fieldKey
     * @return Pair<Date, Date>
     */
    public static Pair<Date, Date> getDateSectionComponentInfo(IDataModel dataModel, String fieldKey) {
        Map<String, LocalDateTime> dateSectionComponentInfo = PropertyValueConvertUtil.getDateRange(dataModel.getValue(fieldKey));
        if (CollectionUtils.isEmpty(dateSectionComponentInfo)) {
            return Pair.of(null, null);
        }

        LocalDateTime startDate = dateSectionComponentInfo.get("start");
        LocalDateTime endDate = dateSectionComponentInfo.get("end");
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Pair.of(null, null);
        }

        return Pair.of(
                Date.from(startDate.atZone(ZoneId.systemDefault()).toInstant()),
                Date.from(endDate.atZone(ZoneId.systemDefault()).toInstant()));
    }

    /**
     * 提示语组件显示信息
     */
    public static void showTipComponentMsg(IFormView view, String componentCid, String msgCode) {
        try {
            view.getClientViewProxy().setControlState(
                    componentCid,
                    new PageStatusAttributeBuilder().normal().build());
            // 设置提示语
            Map<String, Object> args = new HashMap<>();
            args.put(CONTENT, MsgUtils.getMessage(msgCode));
            Control control = view.getControl(componentCid);
            control.setAttribute(args);
        } catch (Exception e) {
            log.error(e.toString());
        }
    }

    /**
     * 获取 子表单指定某行 单选 / 下拉单选 / 复选 / 下拉复选 组件填报内容
     * @param dataModel
     * @param fieldKey
     * @return List<TextValuePair>
     */
    public static List<TextValuePair> getChooseComponentInfo(IDataModel dataModel, String fieldKey, Integer rowIndex) {
        List<Map<String, Object>> chooseComponentInfo = PropertyValueConvertUtil.getListMap(dataModel.getValue(fieldKey, rowIndex));
        if (CollectionUtils.isEmpty(chooseComponentInfo)) {
            return Lists.newArrayList();
        }

        // 下拉返回的值有空对象，需要先过滤
        List<Map<String, Object>> filterChooseComponentInfo = chooseComponentInfo.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterChooseComponentInfo)) {
            return Lists.newArrayList();
        }

        return JsonUtils.parseArray(JsonUtils.toJsonString(chooseComponentInfo), TextValuePair.class);
    }
}
