package com.zte.iccp.itech.extension.domain.model.base;

import com.zte.iccp.itech.extension.common.json.provider.SingletonListProvider;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/16
 */
@Getter
@Setter
public class AttachmentFile extends MultiAttachmentFile implements SingletonListProvider {

    // 字段属性沿用 MultiAttachmentFile, 这里补充额外属性 / 子段
    // 建议后续统一命名规则, 类名改造成 SingleAttachmentFile
}
