package com.zte.iccp.itech.extension.kafka.consumer;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.domain.constant.ClientConstants;
import com.zte.itp.msa.message.annotation.AbstractMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.lang.reflect.Method;
import java.util.Objects;

@Slf4j
public class KafkaHandler extends AbstractMessageHandler {

    public KafkaHandler(Object bean, Method method, Method errorSaveMethod) {
        super(bean, method, errorSaveMethod);
    }

    @Override
    public void processMsg(ConsumerRecord<String, String> record) {
        ContextHelper.setDefaultTenantId();
        ContextHelper.setDefaultAppId();

        ConsumerListener listener = MessageDispatcher.dispatch(record);
        if (Objects.isNull(record) || Objects.isNull(listener)){
            return;
        }

        try {
            log.info("==== {} consume start: topic:{} | key:{}, msg:{}",
                    listener.getClass().getSimpleName(), record.topic(), record.key(), record.value());
            listener.consumeMessage(record);
            log.info("==== {} consume finish: topic:{} | key:{}, msg:{}",
                    listener.getClass().getSimpleName(), record.topic(), record.key(), record.value());
        } catch (Exception e) {
            log.error("==== {} consume error: topic:{} | key:{}, msg:{}, error info:",
                    listener.getClass().getSimpleName(), record.topic(), record.key(), record.value(), e);
            throw new RuntimeException(e);
        }
    }
}
