package com.zte.iccp.itech.extension.openapi.networksecurity;

import com.zte.iccp.itech.extension.ability.IcosAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.SysAuthUtils;
import com.zte.iccp.itech.extension.openapi.model.networksecurity.dto.ChangeOrderAggregationQueryDTO;
import com.zte.iccp.itech.extension.openapi.model.networksecurity.vo.ChangeOrderAggregationVO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 网络安全三营稽核_变更单聚合API
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/2
 */
public class ChangeOrderAggregationApi extends AbstractOpenApi {

    /**
     * 变更单聚合查询
     *
     * @param request 请求头
     * @param queryDTO 请求参数
     * @return 结果
     */
    public ServiceData<PageRows<ChangeOrderAggregationVO>> changeOrderAggregationQuery(HttpServletRequest request,
                                                                                              @RequestBody ChangeOrderAggregationQueryDTO queryDTO){
        // 鉴权
        SysAuthUtils.auth(request);
        // 参数校验和日期条件处理
        if (queryDTO == null) {
            queryDTO = new ChangeOrderAggregationQueryDTO();
        }
        queryDTO.validateAndSetDateCondition();
        // 语言
        queryDTO.setLangId(ContextHelper.getLangId());
        ChangeOrderAggregationQueryDTO finalQueryDTO = queryDTO;
        return new ServiceData<PageRows<ChangeOrderAggregationVO>>() {{
            setBo(IcosAbility.queryChangeOrderInfo(finalQueryDTO));
        }};
    }

}
