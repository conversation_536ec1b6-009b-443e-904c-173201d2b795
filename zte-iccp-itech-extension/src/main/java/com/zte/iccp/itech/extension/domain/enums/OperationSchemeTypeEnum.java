package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/1 下午5:44
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OperationSchemeTypeEnum {

    /**
     * 内部操作方案
     */
    INTERNAL_OPERATION_SCHEME("内部操作方案", "Internal Operation Scheme"),

    /**
     * 客户操作方案
     */
    CUSTOMER_OPERATION_SCHEME("客户操作方案", "Customer Operation Scheme"),
    ;

    private final String zhCn;

    private final String enUs;
}
