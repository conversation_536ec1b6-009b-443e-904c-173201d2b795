package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("CSC 版本信息")
@Setter
@Getter
public class CscVersionVo {

    @ApiModelProperty("版本发布号")
    @JsonProperty("custom_gg9r94bl")
    private String versionCode;

    @ApiModelProperty("工程版本名称")
    @JsonProperty("custom_03c8e9a8")
    private String versionName;

    @ApiModelProperty("产品类型")
    @JsonProperty("custom_5r0s3b9n")
    private String productName;

    @ApiModelProperty("发布人")
    @JsonProperty("custom_8j6d9xbx")
    private String releaseBy;

    @ApiModelProperty("发布时间")
    @JsonProperty("custom_dv42u30y")
    private String releaseTime;
}
