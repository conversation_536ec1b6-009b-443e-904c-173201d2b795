package com.zte.iccp.itech.extension.ability.orderresult.common;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.orderresult.BaseUpdateStatusHandler;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iss.approval.sdk.bean.FlowClient;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class ResubmitHandler extends BaseUpdateStatusHandler {
    public ResubmitHandler() {
        super(AssignmentStatusEnum.APPROVE_START, null);
    }

    @Override
    protected JSONObject doHandle(FlowClient body) {
        return super.doHandle(body);
    }
}
