package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.LocalFactorFieldConsts.*;


/**
 * <AUTHOR>
 * @date 2024/5/14 上午9:27
 */
@Setter
@Getter
@BaseEntity.Info("local_factor")
public class LocalFactor extends BaseEntity {

    @JsonProperty(value = COUNTRY)
    private List<MultiLangText> country;

    @JsonProperty(value = COUNTRY_EXT)
    private String countryExt;

    @JsonProperty(value = SCORE)
    private Double score;

    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;

}
