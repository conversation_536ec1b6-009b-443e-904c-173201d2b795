package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;


/**
 * 流程节点 - 流程同意后（操作日志记录追加）
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/5/8
 */
public class OperationLogAfterPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        OperationLogRecordAbility.saveFlowOperationLog(body);
        return false;
    }
}
