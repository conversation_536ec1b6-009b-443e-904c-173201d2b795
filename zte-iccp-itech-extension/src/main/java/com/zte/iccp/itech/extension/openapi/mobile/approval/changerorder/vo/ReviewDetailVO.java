package com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo;

import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.spi.model.hol.PersonGeneralInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 内部网络变更单审批详情
 */
@Getter
@Setter
@NoArgsConstructor
public class ReviewDetailVO {
    // 网络变更操作审批（固定字段）
    private String operationsApproval;

    /**
     * 批次任务取消操作审批（固定字段）
     */
    private String cancelOperationApproval;

    /**
     * 批次任务取消原因
     */
    private String reasonOfCancellation;

    // 单据编号
    private String coNo;

    // 是否紧急操作
    private String emergencyOperation;

    // 是否“封网、管控期操作”
    private String netCloseorControlOperation;

    // 操作主题
    private String operationSubject;

    // 产品分类
    private String prodClassification;

    // 操作类型
    private String operationType;

    // 计划操作时间
    private List<PlanOperateVO> planOperateDates;

    // 操作等级
    private String operateLevel;

    // 客户标识
    private String customerIdentification;

    // 重要程度
    private String importanceLevel;

    // 风险评估
    private String riskAssessment;

    // 操作说明
    private String operationDesc;

    // 是否大区操作
    private String isRegionalOperation;

    // 审批提醒
    private String approvalReminder;

    // 紧急原因操作简述
    private String emergencyOperationReason;

    // 封网、管控期操作原因
    private String netCloseorControlOperationReason;

    // 是否行政领导审批
    private String isAdministrationLeaderApproval;

    // 行政领导审批原因简述
    private String administrationLeaderApprovalReason;

    // 代表处
    private String department;

    // 客户网络名称
    private List<String> networkName;

    // 操作负责人
    private List<Employee> operationOwners;

    // 申请时间
    private Date submitDate;

    // 申请人
    private PersonGeneralInfo submitter;

    // 附件
    private List<AttachFilesVO> attachFiles;

    // 是否政企
    private Boolean isGovEnt;

    // 操作计划变更说明，批次任务中才有（todo）
    private String operationPlanChangeDesc;

    // 审批进展
    private List<ApprovalProgressVO> approvalProgress;

    /**
     * 变更单类型
     *
     * 0：国内政企
     * 1：三营
     * 2：国际代表处
     */
    private String changeOrderType;

    // 推荐层级
    private String recommendationLevel;

    // 是否满足三营网络服务处集团直管网络
    private boolean operatorGroupNetwork;

}
