package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.OrganizationalChangesFieldConsts.*;


/**
 * <AUTHOR>
 * @create 2025/2/18 下午2:29
 */
@ApiModel("组织变更")
@Setter
@Getter
@BaseEntity.Info(value = "organizational_changes")
public class OrganizationalChanges extends BaseEntity {

    @JsonProperty(value = BEFORE_MAINTENANCE)
    @ApiModelProperty("维护前")
    private String beforeMaintenance;

    @JsonProperty(value = AFTER_MAINTENANCE)
    @ApiModelProperty("维护后")
    private String afterMaintenance;

    @JsonProperty(value = FUNCTION)
    @ApiModelProperty("操作类型")
    private List<TextValuePair> function;

    @JsonProperty(value = MAINTENANCE_DESC)
    @ApiModelProperty("维护说明")
    private String maintenanceDesc;

    @JsonProperty(value = TASK_STATUS)
    @ApiModelProperty("状态")
    private List<TextValuePair> taskStatus;

    @JsonProperty(value = SUCCESS_PERCENT)
    @ApiModelProperty("成功数/总数")
    private String successPercent;

    @JsonProperty(value = TOTAL)
    @ApiModelProperty("总数")
    private Integer total;

    @JsonProperty(value = FAIL_COUNT)
    @ApiModelProperty("失败总数")
    private Integer failCount;
}
