package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;

import java.util.List;

public class DisplayQueryConditionPlugin extends BaseOperationPlugin {

    @Override
    public void afterExecute(ExecuteEvent executeEvent) {
        IDataModel dataModel = getModel();
        IFormView formView = getView();

        String pageCid = formView.getPageId();
        List<String> componentIds;
        switch (pageCid) {
            case PageConstants.PAGE_WEB_NETWORK_CHANGE_ASSIGNMENT:
            case PageConstants.PAGE_WEB_NETWORK_CHANGE_TO_BE_HANDLED_BY_ME:
            case PageConstants.PAGE_WEB_NETWORK_CHANGE_INITIATED_BY_ME:
            case PageConstants.PAGE_WEB_NETWORK_CHANGE_HANDLED_BY_ME:
            case PageConstants.PARTNER_CHANGE_ORDER_HANDLED_BY_ME:
            case PageConstants.PARTNER_CHANGE_ORDER_TO_BE_HANDLED:
                componentIds = Lists.newArrayList(
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_START_TIME_UTC_8,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_REASON,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_RESULT,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.COUNTRY,
                        AssignmentFieldConsts.CUSTOMER_CLASSIFICATION,
                        CidConstants.COMPONENT_CREATED_BY_CID,
                        AssignmentFieldConsts.ASSIGNMENT_STATUS,
                        CidConstants.COMPONENT_CREATED_DATE_CID,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.IMPORTANCE,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.RISK_EVALUATION,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_LEVEL,
                        AssignmentFieldConsts.NETWORK,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OFFICE_NAME);
                displayComponentCondition(
                        formView, dataModel, CidConstants.CONDITION_ADVANCED_QUERY_FLAG_CID, componentIds);
                break;

            case PageConstants.PARTNER_CHANGE_ORDER_INITIATED_BY_ME:
                componentIds = Lists.newArrayList(
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_START_TIME_UTC_8,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_REASON,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_RESULT,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.COUNTRY,
                        AssignmentFieldConsts.CUSTOMER_CLASSIFICATION,
                        AssignmentFieldConsts.ASSIGNMENT_STATUS,
                        CidConstants.COMPONENT_CREATED_DATE_CID,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.IMPORTANCE,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.RISK_EVALUATION,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_LEVEL,
                        AssignmentFieldConsts.NETWORK,
                        AssignmentFieldConsts.NetworkChangeFieldConsts.OFFICE_NAME);
                displayComponentCondition(
                        formView, dataModel, CidConstants.CONDITION_ADVANCED_QUERY_FLAG_CID, componentIds);
                break;

            case PageConstants.PAGE_WEB_HANDLED_BY_ME:
            case PageConstants.PAGE_WEB_TO_BE_HANDLED_BY_ME:
                componentIds = Lists.newArrayList(
                        CidConstants.TEXT_FIELD_TASK_SUBJECT_CID,
                        CidConstants.TEXT_FIELD_BILL_CODE_CID,
                        CidConstants.MULTI_SELECT_FIELD_TASK_TYPE_CID,
                        CidConstants.SELECT_FIELD_IS_APPROVE_TASK_CID,
                        CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_PRODUCT_CID,
                        CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                        CidConstants.COMPONENT_CREATED_DATE_CID,
                        CidConstants.COMPONENT_CREATED_BY_CID);
                displayContainerCondition(
                        formView, dataModel, CidConstants.COLUMNS_LAYOUT_ADVANCED_QUERY_CID, componentIds);
                break;

            case PageConstants.PAGE_WEB_INITIATED_BY_ME:
                componentIds = Lists.newArrayList(
                        CidConstants.TEXT_FIELD_TASK_SUBJECT_CID,
                        CidConstants.TEXT_FIELD_BILL_CODE_CID,
                        CidConstants.MULTI_SELECT_FIELD_TASK_TYPE_CID,
                        CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_PRODUCT_CID,
                        CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                        CidConstants.COMPONENT_CREATED_DATE_CID);
                displayContainerCondition(
                        formView, dataModel, CidConstants.COLUMNS_LAYOUT_ADVANCED_QUERY_CID, componentIds);
                break;

            case PageConstants.PAGE_WEB_APPLICATION_HANDLED_BY_ME:
            case PageConstants.PAGE_WEB_APPLICATION_TO_BE_HANDLED_BY_ME:
                componentIds = Lists.newArrayList(
                        AssignmentFieldConsts.ASSIGNMENT_NAME,
                        AssignmentFieldConsts.ASSIGNMENT_CODE,
                        AssignmentFieldConsts.ASSIGNMENT_TYPE,
                        AssignmentFieldConsts.PermissionApplicationFieldConsts.ROLE,
                        CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_PRODUCT_CID,
                        CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                        CidConstants.COMPONENT_CREATED_BY_CID,
                        CidConstants.COMPONENT_CREATED_DATE_CID);
                displayComponentCondition(
                        formView, dataModel, CidConstants.CONDITION_ADVANCED_QUERY_FLAG_CID, componentIds);
                break;
            case PageConstants.PAGE_WEB_OPERATION_PLAN_MANAGEMENT:
                componentIds = Lists.newArrayList(
                        AssignmentFieldConsts.OperationPlanFieldConsts.COUNTRY,
                        AssignmentFieldConsts.CUSTOMER_CLASSIFICATION,
                        CidConstants.COMPONENT_CREATED_BY_CID,
                        CidConstants.COMPONENT_CREATED_DATE_CID,
                        AssignmentFieldConsts.ASSIGNMENT_STATUS,
                        AssignmentFieldConsts.OperationPlanFieldConsts.IMPORTANCE,
                        AssignmentFieldConsts.OperationPlanFieldConsts.RISK_EVALUATION,
                        AssignmentFieldConsts.OperationPlanFieldConsts.OPERATION_LEVEL,
                        AssignmentFieldConsts.NETWORK,
                        AssignmentFieldConsts.OperationPlanFieldConsts.OFFICE_NAME);
                displayComponentCondition(
                        formView, dataModel, CidConstants.CONDITION_ADVANCED_QUERY_FLAG_CID, componentIds);
                break;
            default:
                break;
        }
    }

    /**
     * 条件展示 - 组件类型
     */
    private void displayComponentCondition(
            IFormView formView,
            IDataModel dataModel,
            String flagCid,
            List<String> componentIds) {
        IClientViewProxy clientViewProxy = formView.getClientViewProxy();

        // 1.确认 高级查询 条件状态
        String advancedQueryFlag = PropertyValueConvertUtil.getString(dataModel.getValue(flagCid));

        // 2.条件为 N - 需展示组件条件
        if (BoolEnum.N.getValue().equals(advancedQueryFlag)) {
            componentIds.forEach(item ->
                    clientViewProxy.setControlState(item, new PageStatusAttributeBuilder().normal().build()));
            dataModel.setValue(flagCid, BoolEnum.Y.getValue());
            return;
        }

        // 3.条件为 Y - 需隐藏组件条件，并置空
        componentIds.forEach(item -> {
            dataModel.setValue(item,null);
            clientViewProxy.setControlState(item, new PageStatusAttributeBuilder().hidden().build());
        });
        dataModel.setValue(flagCid, BoolEnum.N.getValue());
    }

    /**
     * 条件展示 - 容器类型
     */
    private void displayContainerCondition(
            IFormView formView,
            IDataModel dataModel,
            String containerCid,
            List<String> componentIds) {

        // 1.确认容器状态
        String containerStatus
                = formView.getControl(containerCid).getAttribute(CommonConstants.BEHAVIOR).toString();

        // 2.容器隐藏 - 展示容器
        IClientViewProxy clientViewProxy = formView.getClientViewProxy();
        if (CommonConstants.HIDDEN.equals(containerStatus)) {
            clientViewProxy.setControlState(containerCid, new PageStatusAttributeBuilder().normal().build());
            return;
        }

        // 3.容器展示 - 隐藏容器，并置空对应条件
        componentIds.forEach(item -> dataModel.setValue(item,null));
        clientViewProxy.setControlState(containerCid, new PageStatusAttributeBuilder().hidden().build());
    }
}
