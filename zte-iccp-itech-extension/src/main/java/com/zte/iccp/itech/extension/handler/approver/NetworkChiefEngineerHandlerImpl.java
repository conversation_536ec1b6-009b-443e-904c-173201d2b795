package com.zte.iccp.itech.extension.handler.approver;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.AvailabilityEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 网络处总工
 *
 * <AUTHOR>
 * @create 2024/12/5 下午7:37
 * 配置表【产品小类】：对应申请单的【产品分类】
 * 配置表【运营商】：对应申请单的【客户标识】
 * 匹配规则：精确匹配：
 * 按照产品（产品小类>产品大类>产品线）+运营商精确匹配审核人
 * 若读取不到人，则转给系统管理员处理；
 */
public class NetworkChiefEngineerHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    @Override
    protected List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient body) {
        // 直接读取固定字段“网络处总工审核组”
        if (null == changeOrder) {
            return Lists.newArrayList();
        }
        List<Employee> networkDeptChiefEngineer = changeOrder.getNetworkDeptChiefEngineerApprovalTeam();
        if(!CollectionUtils.isEmpty(networkDeptChiefEngineer)){
            return networkDeptChiefEngineer.stream().map(Employee::getEmpUIID).collect(Collectors.toList());
        }
        // 兼容历史版本
        ApproverConfiguration queryParam = new ApproverConfiguration();
        String productCategory = changeOrder.getProductCategory();
        queryParam.setApprovalNode(ApprovalTypeEnum.NETWORK_CHIEF_ENGINEER);
        queryParam.setBillStatus(AvailabilityEnum.ENABLED.name());
        queryParam.setOperator(OperatorEnum.getCnnValue(changeOrder.getCustomerTypeFlag()));
        return ApproverConfigAbility.getApprovalPriorityPersons(queryParam, productCategory, 0, 1);
    }
}
