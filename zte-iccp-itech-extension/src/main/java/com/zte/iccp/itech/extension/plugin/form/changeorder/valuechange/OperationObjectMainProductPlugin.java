package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.AfterDeleteRowsEvent;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.COMPONENT_OPERATION_OBJECT_PRODUCT_MODEL_ID_CID;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

public class OperationObjectMainProductPlugin implements ValueChangeBaseFormPlugin {

    @Override
    public void operate(ValueChangedEventArgs args) {
        invoke(args.getModel(), args.getChangeData());
    }

    @Override
    public void loadBillTableData(LoadBillTableDataEventArgs args) {
        loadBillTableInvoke(args.getModel(), args.getFormView(), args.getTableCid());
    }

    @Override
    public void afterAddRows(AfterAddRowsEventArgs args) {
        addRowsInvoke(args.getModel(), args.getFormView(), args.getEvent().getEntryKey());
    }

    @Override
    public void afterDeleteRows(AfterDeleteRowsEventArgs args) {
        deleteRowsInvoke(args.getModel(), args.getFormView(), args.getEvent());
    }

    public void invoke(IDataModel dataModel, ChangeData changeData) {
        String isMainProduct = TextValuePairHelper.getValue(changeData.getNewValue());
        if (!Y.equals(isMainProduct)) {
            return;
        }
        // 清空其他【主产品】的勾选
        mainProductSelectChange(dataModel, changeData);
        // 更新主单据国家/省/市等信息
        CountryChangeByNetworkPlugin.changeCountryProvinceCityData(
                dataModel,
                dataModel.getValue(FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, changeData.getRowIndex()));
    }

    public static void mainProductSelectChange(IDataModel dataModel, ChangeData changeData) {
        int updateRow = changeData.getRowIndex();
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(CidConstants.OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        for (int rowIndex = 0; rowIndex < dataEntityCollection.size(); rowIndex++) {
            String isMainProduct = TextValuePairHelper.getValue(dataModel.getValue(OperationObjectFieldConsts.IS_MAIN_PRODUCT, rowIndex));
            if (Y.equals(isMainProduct) && rowIndex != updateRow) {
                dataModel.setValue(OperationObjectFieldConsts.IS_MAIN_PRODUCT, null, rowIndex);
                break;
            }
        }
    }

    public static void loadBillTableInvoke(IDataModel dataModel, IFormView formView, String tableCid) {
        // 非操作对象子表单，直接返回
        if (!COMPONENT_OPERATION_OBJECT_ORDER_CID.equals(tableCid)) {
            return;
        }
        PageStatusEnum pageStatusEnum = formView.getFormShowParameter().getPageStatus();
        // 获取子表单所有数据
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(CidConstants.OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        if (CollectionUtils.isEmpty(dataEntityCollection)) {
            return;
        }
        // 如果是新建/编辑态，则按照规则设置操作对象【主产品】组件为普通/禁用：
        // 若【产品型号】为空，或者为主单据产品分类下属型号，则默认设置【主产品】组件为normal；否则，设置为禁用
        if (Lists.newArrayList(PageStatusEnum.NEW, PageStatusEnum.EDIT).contains(pageStatusEnum)) {
            batchSetMainProductComponentStatus(dataEntityCollection, formView, dataModel);
            return;
        }
        // 如果是其他状态（查看态）等，特殊处理
        // 设置【主产品】字段值为Y的【主产品】组件状态为normal；设置其他【主产品】组件状态为disabled
        for (int rowIndex = 0; rowIndex < dataEntityCollection.size(); rowIndex++) {
            String isMainProduct = TextValuePairHelper.getValue(dataModel.getValue(OperationObjectFieldConsts.IS_MAIN_PRODUCT, rowIndex));
            if (Y.equals(isMainProduct)) {
                setMainProductComponentStatus(formView, rowIndex, true);
                continue;
            }
            setMainProductComponentStatus(formView, rowIndex, false);
        }

    }

    public void deleteRowsInvoke(IDataModel dataModel, IFormView formView, AfterDeleteRowsEvent event) {
        // 非操作对象子表单删除数据行，直接返回
        if (!OPERATION_OBJECT_TABLE_PROPERTY_KEY.equals(event.getEntryKey())) {
            return;
        }
        // 判断删除的子表单数据是否包含【主产品】，若包含则清空国家/省/市等信息
        List<DynamicDataEntity> deleteRowEntities = event.getRowEntities();
        boolean isMainProductFlag = deleteRowEntities
                .stream()
                .map(d -> d.get(OperationObjectFieldConsts.IS_MAIN_PRODUCT))
                .anyMatch(item -> Y.equals(TextValuePairHelper.getValue(item)));
        if (isMainProductFlag) {
            CountryChangeByNetworkPlugin.clearCountryInfo(dataModel);
        }

    }

    public static void addRowsInvoke(IDataModel dataModel, IFormView formView, String entryKey) {
        // 非操作对象子表单添加数据行，直接返回
        if (!OPERATION_OBJECT_TABLE_PROPERTY_KEY.equals(entryKey)) {
            return;
        }
        // 获取子表单所有数据
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(CidConstants.OPERATION_OBJECT_TABLE_PROPERTY_KEY);
        if (CollectionUtils.isEmpty(dataEntityCollection)) {
            return;
        }
        // 如果添加的行为操作对象首行数据，则默认首行设置为【主产品】
        if (dataEntityCollection.size() == CommonConstants.INTEGER_ONE) {
            dataModel.setValue(
                    OperationObjectFieldConsts.IS_MAIN_PRODUCT,
                    TextValuePairHelper.buildList(Y, EMPTY_STRING, EMPTY_STRING),
                    INTEGER_ZERO);
        }
        // 添加操作对象后设置操作对象数据行的【主产品】组件属性（显示/禁用）
        batchSetMainProductComponentStatus(dataEntityCollection, formView, dataModel);

    }

    private static Boolean checkProductModel(String productModelIdFullPath, String productId) {
        int level = CommonConstants.INTEGER_FOUR;
        String product4LevelId = ProductUtils.getProductIdByLevel(productId, level);
        String productModel4LevelId = ProductUtils.getProductIdByLevel(productModelIdFullPath, level);
        return StringUtils.equals(productModel4LevelId, product4LevelId);
    }

    private static void batchSetMainProductComponentStatus(IDataEntityCollection dataEntityCollection, IFormView formView, IDataModel dataModel) {

        String productId = TextValuePairHelper.getValue(dataModel.getValue(FIELD_PRODUCT_CID));
        for (int rowIndex = 0; rowIndex < dataEntityCollection.size(); rowIndex++) {
            Object productModelObj = dataModel.getValue(FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, rowIndex);
            if (ObjectUtils.isEmpty(productModelObj)) {
                setMainProductComponentStatus(formView, rowIndex, true);
                continue;
            }
            String productModelIdFullPath = ((DynamicDataEntity) productModelObj).get(SERVICE_PRODUCT_MODEL_FULL_ID_PATH_CID).toString();
            setMainProductComponentStatus(formView, rowIndex, checkProductModel(productModelIdFullPath, productId));
        }
    }

    private static void setMainProductComponentStatus(IFormView formView, int rowIndex, boolean isValidModel) {
        IEntryTableSupport table = (IEntryTableSupport) formView.getControl(COMPONENT_OPERATION_OBJECT_ORDER_CID);
        String behavior = isValidModel ? NORMAL : DISABLED;
        table.getTableState().setCellAttribute(rowIndex, OperationObjectFieldConsts.IS_MAIN_PRODUCT, BEHAVIOR, behavior);
    }
}
