package com.zte.iccp.itech.extension.domain.enums.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务类型枚举
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum AssignmentTypeEnum implements SingletonTextValuePairsProvider {
    /** 网络变更 */
    NETWORK_CHANGE("1", "网络变更任务", "Network Change Task", ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW,
            PageConstants.AssignmentTitle.NETWORK_CHANGE),

    /** 网络变更 - 批次任务 */
    NETWORK_CHANGE_BATCH("2", "网络变更 - 批次任务", "Batch Task of Network Change", ApproveFlowCodeEnum.BATCH_TASK_FLOW,
            "Backlog_Network_Change_Batch_Assignment"),

    /** 技术管理 */
    TECHNOLOGY_MANAGEMENT("3", "技术管理任务", "Technical Management Task", ApproveFlowCodeEnum.TECH_MANAGE_FLOW,
            PageConstants.AssignmentTitle.TECHNOLOGY),

    /** 技术管理 - 子任务 */
    TECHNOLOGY_MANAGEMENT_SUB("4", "技术管理 - 子任务", "Technical Management Subtask", ApproveFlowCodeEnum.TECH_MANAGE_SUBTASK,
            PageConstants.AssignmentTitle.SUB_TECHNOLOGY),

    /** 故障管理 */
    FAULT_MANAGEMENT("5", "故障管理任务", "Fault Management Task", ApproveFlowCodeEnum.FAULT_MANAGE_FLOW,
            PageConstants.AssignmentTitle.FAULT),

    /** 合作方网络变更 */
    SUBCONTRACTOR_NETWORK_CHANGE("6", "合作方网络变更任务", "Partner Network Change Task", ApproveFlowCodeEnum.SUBCONTRACTOR_OC_FLOW,
            PageConstants.AssignmentTitle.PARTNER_NETWORK_CHANGE),

    /** 合作方网络变更 - 批次任务 */
    SUBCONTRACT_NETWORK_CHANGE_BATCH("7", "合作方网络变更 - 批次任务", "Batch Task of Partner Network Change", ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW,
            "Backlog_Subcontractor_Network_Change_Batch_Assignment"),

    /** 权限申请任务 */
    PERMISSION_APPLICATION("8", "iTechCloud权限申请", "Permission Application", ApproveFlowCodeEnum.PERMISSION_APPLICATION,
            PageConstants.AssignmentTitle.PERMISSION),

    /** 复盘任务 */
    CLOCK_REVIEW("9", "打卡复盘", "Review Task", ApproveFlowCodeEnum.CLOCK_IN_REVIEW_FLOW,
            PageConstants.AssignmentTitle.CLOCK_REVIEW),

    /** 操作计划任务 */
    OPERATION_PLAN_TASK("10", "操作计划任务", "Operation Plan Task", ApproveFlowCodeEnum.OPERATION_PLAN_FLOW,
            PageConstants.AssignmentTitle.OPERATION_PLAN)
            ;

    /**
     * 编码
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;

    /**
     * 审批流程枚举
     */
    private final ApproveFlowCodeEnum approveFlowCodeEnum;

    private final String msgKey;

    /**
     * 获取会签节点
     */
    public static List<AssignmentTypeEnum> networkChangeTypes() {
        return Lists.newArrayList(
                AssignmentTypeEnum.NETWORK_CHANGE, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE,
                AssignmentTypeEnum.NETWORK_CHANGE_BATCH, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH);
    }


    /**
     * 根据枚举保存内容获取对应枚举
     * @param textValuePairList
     * @return AssignmentTypeEnum
     */
    public static AssignmentTypeEnum fromTextValuePair(List<TextValuePair> textValuePairList) {
        if (CollectionUtils.isEmpty(textValuePairList)) {
            return null;
        }

        String value = textValuePairList.get(CommonConstants.INTEGER_ZERO).getValue();
        for (AssignmentTypeEnum networkAssignmentType : AssignmentTypeEnum.values()) {
            if (networkAssignmentType.getValue().equals(value)) {
                return networkAssignmentType;
            }
        }

        return null;
    }

    public static AssignmentTypeEnum fromValue(String value) {
        for (AssignmentTypeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }


    /**
     * 根据类型获取流程编码
     *
     * @param assignmentTypes 任务类型类型
     * @return 流程编码
     */
    public static List<String> getFlowCodes(List<String> assignmentTypes) {
        List<String> flowCodes = new ArrayList<>();
        if (CollectionUtils.isEmpty(assignmentTypes)) {
            return flowCodes;
        }
        return Arrays.stream(AssignmentTypeEnum.values())
                .filter(assignmentTypeEnum -> assignmentTypes.contains(assignmentTypeEnum.value))
                .map(assignmentTypeEnum -> assignmentTypeEnum.approveFlowCodeEnum.name())
                .collect(Collectors.toList());
    }

    /**
     * 根据流程编码 映射任务类型
     * @param approveFlowCodeEnum 流程编码
     * @return AssignmentTypeEnum
     */
    public static AssignmentTypeEnum fromFlowCode(ApproveFlowCodeEnum approveFlowCodeEnum) {
        if (approveFlowCodeEnum == null) {
            return null;
        }

        return Arrays.stream(AssignmentTypeEnum.values())
                .filter(item -> approveFlowCodeEnum.equals(item.getApproveFlowCodeEnum()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据名称匹配任务类型
     *
     * @param name 名称
     * @return 任务类型
     */
    public static AssignmentTypeEnum fromName(String name) {
        return Arrays.stream(values())
                .filter(assignmentTypeEnum -> assignmentTypeEnum.name().equals(name))
                .findFirst()
                .orElse(null);
    }
}
