package com.zte.iccp.itech.extension.domain.model.clockin.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInCustomerFlagEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.ProdOdcConfigFieldConsts.*;

/**
 * 产品值守系数配置
 * <AUTHOR>
 * @since 2024/09/14
 */
@Getter
@Setter
@BaseEntity.Info("prod_odc_config")
public class ProdOdcConfig extends BaseEntity {
    @JsonProperty(value = CUSTOMER_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ClockInCustomerFlagEnum customerFlag;

    @JsonProperty(value = PROD_TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String prodType;

    @JsonProperty(value = ON_DUTY_COEFFICIENT_INNER)
    private Double onDutyCoefficientInner;

    @JsonProperty(value = ON_DUTY_COEFFICIENT_INTER)
    private Double onDutyCoefficientInter;
}
