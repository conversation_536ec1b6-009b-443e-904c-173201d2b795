package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.HardwareVersionConsts.MATERIAL_NAME;
import static com.zte.iccp.itech.extension.domain.constant.entity.HardwareVersionConsts.MATERIAL_VERSION;


/**
 * <AUTHOR> jiangjiawen
 * @date 2024/12/23
 */
@ApiModel("硬件版本(hardware_version)")
@Setter
@Getter
@BaseEntity.Info("hardware_version")
public class HardwareVersionAttribute extends BaseEntity{
    @ApiModelProperty("物料名称")
    @JsonProperty(value = MATERIAL_NAME)
    private String materialName;

    @ApiModelProperty("硬件版本")
    @JsonProperty(value = MATERIAL_VERSION)
    private String materialVersion;
}
