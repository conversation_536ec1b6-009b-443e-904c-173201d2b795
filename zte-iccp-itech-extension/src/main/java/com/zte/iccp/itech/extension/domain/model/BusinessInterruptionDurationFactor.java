package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.BusinessInterruptionDurationFactorConsts.*;

/**
 * 业务中断时长系数实体
 * <AUTHOR> 10335201
 * @date 2024-05-13 上午10:47
 **/
@Setter
@Getter
@BaseEntity.Info("business_interruption_duration_factor")
public class BusinessInterruptionDurationFactor extends BaseEntity {
    @JsonProperty(value = MAXIMUM_SERVICE_INTERRUPTION_DURATION)
    private Double maximumServiceInterruptionDuration;

    @JsonProperty(value = MINIMUM_SERVICE_INTERRUPTION_DURATION)
    private Double minimumServiceInterruptionDuration;

    @JsonProperty(value = SCORE)
    private Double score;

    @JsonProperty(value = PRODUCT_OPERATION_TEAM)
    private List<TextValuePair> productOperationTeam;
}
