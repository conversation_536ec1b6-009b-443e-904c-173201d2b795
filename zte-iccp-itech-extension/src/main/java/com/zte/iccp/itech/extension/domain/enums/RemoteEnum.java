package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @create 2024/5/28 上午9:26
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum RemoteEnum implements SingletonTextValuePairsProvider {
    /** 远程 */
    REMOTE("远程", "Remote"),
    /** 本地 */
    LOCAL("本地", "Local"),
    ;

    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }
}
