package com.zte.iccp.itech.extension.handler.approver.partner;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.List;

/**
 * 办事产品科长  OR 代表处科长审核人
 * 代表处产品科长：从审核人配置-三营审核配置中，根据【产品（先产品小类->产品大类->产品线依次匹配）+代表处+是否政企=否】读取办事处产品科长；
 *
 * <AUTHOR>
 * @create 2024/7/15 下午1:59
 */
public class OfficeProdChiefHandlerImpl extends AbstractRewardApproverHandler<SubcontractorChangeOrder> {

    @Override
    public List<String> rewardApproverByNodeExtend(SubcontractorChangeOrder changeOrder, FlowClient bod) {
        ApproverConfiguration queryParam = new ApproverConfiguration();
        String productId = changeOrder.getProductCategory();
        // 三营
        queryParam.setApprovalNode(ApprovalTypeEnum.ADMINISTRATIVE_LEADER_DIVISION_THREE);
        queryParam.setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
        queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
        queryParam.setResponsibleDeptId(changeOrder.getResponsibleDept());
        // 是否政企
        queryParam.setIsGov(changeOrder.getIsGovEnt());
        // 办事处产品科长
        queryParam.setRole(ApproveRoleEnum.PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE);
        return ApproverConfigAbility.getApprovalPriorityPersons(queryParam, productId, 0, null);
    }
}
