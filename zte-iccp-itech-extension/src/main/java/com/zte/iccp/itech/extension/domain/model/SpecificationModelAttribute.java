package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.SpecificationModelConsts.SPECIFICATION_MODEL_BILL_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.SpecificationModelConsts.SPECIFICATION_MODEL_NAME;


/**
 * 规格型号 - 基础资料
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/23
 */
@ApiModel("规格型号(specification_model)")
@Setter
@Getter
@BaseEntity.Info("specification_model")
public class SpecificationModelAttribute extends BaseEntity {

    @ApiModelProperty("规格型号")
    @JsonProperty(value = SPECIFICATION_MODEL_NAME)
    private String specificationModelName;

    @ApiModelProperty("单据状态")
    @JsonProperty(value = SPECIFICATION_MODEL_BILL_STATUS)
    private String billStatus;
}
