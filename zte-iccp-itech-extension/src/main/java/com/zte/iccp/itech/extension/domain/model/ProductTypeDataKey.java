package com.zte.iccp.itech.extension.domain.model;

import lombok.Data;

/**
 * @author: 李江斌 10318434
 * @date: 2024/9/13
 */
@Data
public class ProductTypeDataKey {
    // 产品类型
    private String productTypeDataKey;

    // 产品经营团队
    private String productTeamDataKey;

    // 产品线
    private String productLineDataKey;

    // 产品大类
    private String prodMainCategoryDataKey;

    // 产品小类
    private String prodSubCategoryDataKey;

    // 规格型号
    private String productModelDataKey;

    // 任意选择层级的最后一层
    private String lastLevelDataKey;

    public ProductTypeDataKey(String productTypeDataKey, String productTeamDataKey, String productLineDataKey, String prodMainCategoryDataKey, String prodSubCategoryDataKey, String productModelDataKey) {
        this.productTypeDataKey = productTypeDataKey;
        this.productTeamDataKey = productTeamDataKey;
        this.productLineDataKey = productLineDataKey;
        this.prodMainCategoryDataKey = prodMainCategoryDataKey;
        this.prodSubCategoryDataKey = prodSubCategoryDataKey;
        this.productModelDataKey = productModelDataKey;
    }

    public ProductTypeDataKey(String productTypeDataKey, String productTeamDataKey, String lastLevelDataKey) {
        this.productTypeDataKey = productTypeDataKey;
        this.productTeamDataKey = productTeamDataKey;
        this.lastLevelDataKey = lastLevelDataKey;
    }
}
