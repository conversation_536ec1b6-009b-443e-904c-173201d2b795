package com.zte.iccp.itech.extension.domain.model.vo;

import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024/8/31 上午9:29
 */
@Getter
@Setter
public class OperatorCopy {

    @ApiModelProperty("人员角色")
    private OperatorRoleEnum operatorRole;

    @ApiModelProperty("人员账号")
    private String operatePerson;
}
