package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 审批人配置实体字段信息
 *
 * <AUTHOR> 10347404
 * @date 2024-05-13 上午10:53
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ApprovalConfFieldConsts {
    /**
     * 审批节点
     */
    public static final String APPROVAL_NODE = "approval_node";

    /**
     * 营销单位
     */
    public static final String SALES = "sales_dept_id_path";

    /**
     * 片区
     */
    public static final String ORGANIZATION_REGION = "organization_region_id_path";

    /**
     * 代表处
     */
    public static final String RESPONSIBLE_DEPT = "responsible_dept_id";

    /**
     * 运营商
     */
    public static final String OPERATOR = "operator";

    /**
     * 客户
     */
    public static final String CUSTOMER = "customer_id";

    /**
     * 审核人
     */
    public static final String APPROVER_PERSON = "approval_person";

    /**
     * 审核组
     */
    public static final String APPROVER_GROUP = "approval_group";

    /** 产品经营团队 */
    public static final String PROD_OPERATION_TEAM = "prod_operation_team_id_path";

    /**
     * 产品线
     */
    public static final String PRODUCT_LINE = "prod_line_id_path";

    /** 产品大类 */
    public static final String PROD_MAIN_CATEGORY = "prod_main_category_id_path";

    /**
     * 产品小类
     */
    public static final String PRODUCT_SUB_CATEGORY = "prod_sub_category_id_path";

    /**
     * 产品型号
     */
    public static final String PRODUCT_MODEL_ID = "product_model_id";

    /**
     * 是否政企
     */
    public static final String IS_GOV = "is_gov_ent";

    /** 是否紧急操作 */
    public static final String IS_EMERGENCY_OPERATION = "is_emergency_operation";

    /**
     * 角色
     */
    public static final String ROLE = "role";

    /**
     * 操作类型
     */
    public static final String OPERATE_TYPE_MULTI = "operation_type_multi";

    /** 操作开始时间 */
    public static final String OPERATION_START_TIME = "operation_start_time";

    /** 操作结束时间 */
    public static final String OPERATION_END_TIME = "operation_end_time";

    /** 风险评估 */
    public static final String RISK_ASSESSMENT = "risk_assessment";

    /** 状态 */
    public static final String STATUS = "status";

    /**
     * 操作类型
     */
    public static final String OPERATION_TYPE = "operation_type";

    /**
     * 关联操作产品中文名
     */
    public static final String INTEGRATED_ASSOCIATED_PRODUCT_ZH = "integrated_associated_product_zh";

    /**
     * 关联操作产品英文名
     */
    public static final String INTEGRATED_ASSOCIATED_PRODUCT_EN = "integrated_associated_product_en";

    /**
     * 单据状态
     */
    public static final String BILLSTATUSFIELD_STATUS = "billstatusfield_status";

    /**
     * 逻辑网元
     */
    public static final String LOGICAL_NE = "selectfield_logical_ne";

    /**
     * 多选关联产品中文
     */
    public static final String MULTI_ASSOCIATED_PROD_ZH = "mult_integrated_associated_product_zh";

    /**
     * 多选关联产品英文
     */
    public static final String MULTI_ASSOCIATED_PROD_EN = "mult_integrated_associated_product_en";

    /**
     * 冗余选择下拉处理-无业务逻辑字段
     */
    public static final String REDUNDAN = "redundan";

    /**
     * 实体ID
     */
    public static final String ENTITY_ID = "entity_id";
}
