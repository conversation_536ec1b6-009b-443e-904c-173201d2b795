package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.V4OperationHardwareConsts.*;

/**
 * <AUTHOR>
 * @date 2024/10/28 下午4:11
 */
@Setter
@Getter
@BaseEntity.Info("v4_operation_hardware")
public class V4OperationHardware extends BaseEntity {

    @JsonProperty(value = LOGICAL_NE)
    @ApiModelProperty("逻辑网元")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String logicalNe;

    @JsonProperty(value = PROD_SUB_CATEGORY_ID_PATH)
    @ApiModelProperty("产品小类")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String prodSubCategory;

    @JsonProperty(value = BILL_STATUS)
    @ApiModelProperty("单据状态")
    private String billStatus;
}
