package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.model.vo.CscVersionVo;
import com.zte.iccp.itech.extension.domain.model.vo.TableDisplayRows;
import com.zte.iccp.itech.extension.domain.model.vo.VersionServiceVo;
import com.zte.iccp.itech.extension.openapi.iversion.IVersionInfoOpenapi;
import com.zte.iccp.itech.extension.openapi.model.version.VersionQuery;
import com.zte.iccp.itech.extension.spi.client.CscClient;
import com.zte.iccp.itech.extension.spi.client.IVersionClient;
import com.zte.iccp.itech.extension.spi.model.csc.dto.CscVersionQuery;
import com.zte.iccp.itech.extension.spi.model.csc.vo.CscVersion;
import com.zte.iccp.itech.extension.spi.model.iversion.dto.IVersionInfoQuery;
import com.zte.iccp.itech.extension.spi.model.iversion.vo.IVersionInfoVo;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 版本中心查询
 *
 * <AUTHOR> 10335201
 * @date 2024-06-13 下午4:39
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)

public class VersionAbility {

    /**
     * 查询版本名称
     */
    public static Map<String, String> queryVersionMap(List<String> versionIds) {
        if (CollectionUtils.isEmpty(versionIds)) {
            return Maps.newHashMap();
        }

        versionIds = versionIds.stream()
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());
        IVersionInfoOpenapi versionOpenApi = new IVersionInfoOpenapi();
        List<VersionServiceVo> versionInfo = Lists.newArrayList();
        versionIds.forEach(item-> {
            ServiceData<PageRows<VersionServiceVo>> serviceData
                    = versionOpenApi.queryVersionInfo(new VersionQuery(item));
            if (!CollectionUtils.isEmpty(serviceData.getBo().getRows())) {
                versionInfo.add(serviceData.getBo().getRows().get(0));
            }
        });

        return versionInfo.stream()
                .filter(v -> v.getVersionCode() != null)
                .collect(Collectors.toMap(
                        VersionServiceVo::getVersionCode,
                        VersionServiceVo::getVersionName,
                        (v1, v2) -> v1));
    }

    /**
     * 检索 CSC 版本列表
     */
    public static TableDisplayRows<CscVersionVo> queryCscVersions(
            CscVersionQuery cscVersionQuery) {

        PageRows<CscVersion> pageRows = CscClient.getCscVersion(cscVersionQuery);
        return convertCscVersionTableRows(pageRows);
    }

    /**
     * 列表数据包装 - CSC 版本
     */
    private static TableDisplayRows<CscVersionVo> convertCscVersionTableRows(
            PageRows<CscVersion> pageRows) {

        TableDisplayRows<CscVersionVo> tableRows = new TableDisplayRows<>();
        tableRows.setCurrent(pageRows.getCurrent());
        tableRows.setTotal(pageRows.getTotal());

        if (CollectionUtils.isEmpty(pageRows.getRows())) {
            return tableRows;
        }

        List<CscVersionVo> convertVersions = Lists.newArrayList();
        for (CscVersion version : pageRows.getRows()) {
            CscVersionVo convertVersion = new CscVersionVo();

            convertVersion.setVersionCode(version.getVerLogId());
            convertVersion.setVersionName(version.getVerName());
            convertVersion.setProductName(version.getProductName());
            convertVersion.setReleaseTime(version.getReleaseTime());

            // 用户名称格式：姓名 / 四级部门 / 三级部门 / 二级部门
            // 产品工具相关方法可兼容
            convertVersion.setReleaseBy(
                    ProductUtils.getValueIndex(version.getReleasemanName(), 1));

            convertVersions.add(convertVersion);
        }

        tableRows.setRecords(convertVersions);
        return tableRows;
    }

    /**
     * 检索版本服务对象信息 - CSC
     */
    public static VersionServiceVo queryCscVersionInfo(String cscVersionCode) {
        // 1.检索 CSC 版本信息
        CscVersionQuery cscVersionQuery = new CscVersionQuery();
        cscVersionQuery.setVersionReleaseNo(cscVersionCode);
        PageRows<CscVersion> pageRows = CscClient.getCscVersion(cscVersionQuery);
        if (CollectionUtils.isEmpty(pageRows.getRows())) {
            return null;
        }

        // 2.包装版本服务对象信息
        CscVersion version = pageRows.getRows().get(0);
        VersionServiceVo versionServiceInfo = new VersionServiceVo();
        versionServiceInfo.setVersionCode(
                String.format("%s_%s", CidConstants.SystemName.CSC, version.getVerLogId()));
        versionServiceInfo.setVersionName(version.getVerName());

        return versionServiceInfo;
    }

    /**
     * 检索版本服务对象信息 - iVersion
     */
    public static VersionServiceVo queryIVersionInfo(
            String iVersionId,
            boolean prefixFlag) {

        // 1.检索 iVersion 版本信息
        IVersionInfoQuery iVersionInfoQuery = new IVersionInfoQuery();
        iVersionInfoQuery.setVersionId(Long.parseLong(iVersionId));
        IVersionInfoVo iVersionInfoVo = IVersionClient.getVersionInfo(iVersionInfoQuery);

        // 2.包装版本服务对象信息
        VersionServiceVo versionServiceVo = new VersionServiceVo();

        if (iVersionInfoVo != null && iVersionInfoVo.getVersionId() > 0) {
            versionServiceVo.setVersionCode(prefixFlag
                    ? String.format("%s_%s", CidConstants.SystemName.I_VERSION, iVersionInfoVo.getVersionId())
                    : String.valueOf(iVersionInfoVo.getVersionId()));
            versionServiceVo.setVersionName(iVersionInfoVo.getVersionName());
        } else {
            versionServiceVo.setVersionCode(null);
            versionServiceVo.setVersionName(null);
        }

        return versionServiceVo;
    }
}
