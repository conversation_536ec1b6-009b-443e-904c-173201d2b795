package com.zte.iccp.itech.extension.openapi.model.networksecurity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR> jiangjiawen
 * @date 2024/12/3
 */
@Getter
@Setter
public class BaseQueryDTO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeEnd;

    /** 页码 */
    private Integer pageNum = 1;

    /** 页容 */
    private Integer pageSize = 500;

    /** 语言 */
    private String langId = CommonConstants.ZH_CN;

    /**
     * 是否空对象校验
     *
     * @return true:是 false:否
     */
    public boolean isEmptyObject() {
        return this.createTimeStart == null
                && this.createTimeEnd == null
                && this.updateTimeStart == null
                && this.updateTimeEnd == null;
    }

    /**
     * 设置默认的更新时间作为查询条件，默认查询更新时间一个月的数据
     */
    public void setDefaultValueIfEmpty() {
        this.updateTimeStart = Date.from(LocalDate.now().minusMonths(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        this.updateTimeEnd = new Date();
    }


    /**
     * 参数合法性校验
     */
    public void validateInput() {
        if (this.pageSize > CommonConstants.INTEGER_2000) {
            throw new IllegalArgumentException("External page size exceeds the maximum allowed size of " + 2000);
        }

        handleCreateDate(this.createTimeStart, this.createTimeEnd);
        handleUpdateDate(this.updateTimeStart, this.updateTimeEnd);
    }

    /**
     * 处理创建开始时间
     *
     * @param createTimeStart 创建开始时间
     * @param createTimeEnd 创建结束时间
     */
    private void handleCreateDate(Date createTimeStart, Date createTimeEnd) {
        Pair<Date, Date> dateRangeValue = handleDateRange(createTimeStart, createTimeEnd);
        if (dateRangeValue == null) {
            return;
        }
        this.createTimeStart = dateRangeValue.getLeft();
        this.createTimeEnd = dateRangeValue.getRight();
    }

    /**
     * 处理更新开始时间
     *
     * @param updateTimeStart 更新开始时间
     * @param updateTimeEnd 更新结束时间
     */
    private void handleUpdateDate(Date updateTimeStart, Date updateTimeEnd) {
        Pair<Date, Date> dateRangeValue = handleDateRange(updateTimeStart, updateTimeEnd);
        if (dateRangeValue == null) {
            return;
        }
        this.updateTimeStart = dateRangeValue.getLeft();
        this.updateTimeEnd = dateRangeValue.getRight();
    }

    /**
     * 处理日期区间
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 处理后的开始和结束时间
     */
    private Pair<Date, Date> handleDateRange(Date startTime, Date endTime) {
        if (startTime == null && endTime == null) {
            return null;
        }

        LocalDateTime start = (startTime != null) ? startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
        LocalDateTime end = (endTime != null) ? endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
        LocalDateTime now = LocalDateTime.now();
        // 1.如果只给了开始日期，结束日期往后推一个月
        if (start != null && end == null) {
            // 1.1 开始日期大于当前日期
            if (start.isAfter(now)) {
                throw new IllegalArgumentException(MsgUtils.getLangMessage(CommonConstants.ZH_CN, MessageConsts.CHECK_DATE_IS_AFTER_ERROR));
            }
            end = start.plusMonths(1);

            // 2.如果只给了结束日期
        } else if (end != null && start == null) {
            // 2.1 如结束日期大于当前日期，默认开始日期为当前日期往前推一个月
            if (end.isAfter(now)) {
                start = now.minusMonths(1);
                // 2.2 如结束日期小于当前日期，默认开始日期为结束日期往前推一个月
            }else {
                start = end.minusMonths(1);
            }
        }

        // 3.结束日期不能小于开始日期
        if (end.isBefore(start)) {
            throw new IllegalArgumentException(MsgUtils.getLangMessage(CommonConstants.ZH_CN, MessageConsts.CHECK_DATE_IS_BEFORE_ERROR));
        }

        return Pair.of(Date.from(start.atZone(ZoneId.systemDefault()).toInstant()), Date.from(end.atZone(ZoneId.systemDefault()).toInstant()));
    }
}
