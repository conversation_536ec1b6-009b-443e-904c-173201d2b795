package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.AlarmUtils;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.BATCH_CHANGE;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.BATCH_CHANGE_COMMIT;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;

/**
 * 发布通告，保障单主批次，推生成批次任务流程
 *
 * <AUTHOR>
 * @since 2024/12/23
 */
@Slf4j
public class GuaranteePushPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        // 发布通告刷新 操作时间
        Map<String, Object> values = Maps.newHashMap();
        values.put(OPERATION_UPDATE_TIME, new Date());
        SaveDataHelper.update(BatchTask.class, body.getBusinessId(), values);
        // 系统自动节点 - 发布通告 - 保障任务主单据的批次同步保障任务数据
        AsyncExecuteUtils.execute(() -> OperationLogRecordAbility.systemNodeSyncBatchGuaranteeData(body));

        List<BatchTask> batchTasks = BatchTaskAbility.getGuarantyBatchTask(body.getBusinessId());
        try {
            batchTasks.forEach(item -> {
                String node = ApprovalConstants.BATCH_SYSTEM_NODE;
                if (AssignmentStatusEnum.PENDING_NOTIFICATION.getValue().equals(item.getCurrentStatus())
                        || 0 == item.getApprovalNum()) {
                    node = ApprovalConstants.BATCH_PENDING_NOTIFICATION_NODE;
                }
                Map<String, Object> variables = MapUtils.newHashMap(BATCH_CHANGE, BATCH_CHANGE_COMMIT);
                FlowHelper.changeFlowParams(item.getId(), variables, ApproveFlowCodeEnum.BATCH_TASK_FLOW);
                FlowHelper.pushSystemNode(item.getId(), node);
            });
        } catch (Exception e) {
            log.error("GuaranteePushPlugin Guarante batch push error ! :{}", body.getBusinessId());
            AlarmUtils.major("GuaranteePushPlugin Guarante batch push error", e);
        }

        return false;
    }
}
