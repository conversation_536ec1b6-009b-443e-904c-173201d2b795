package com.zte.iccp.itech.extension.common.helper;

import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.spi.client.CloudDiskClient;
import com.zte.iccp.itech.extension.spi.model.clouddisk.*;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.CLOUD_DISK_UPLOAD_FAILED;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/11
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CloudDiskHelper {

    private static final int CHUNK_SIZE = 2 * 1024 * 1024;

    private static final int SEND_CHUNKS_TRY_COUNT = 3;

    private static final Pattern ALL_CHUNKS_SENT = Pattern.compile("^1+$");

    @SneakyThrows
    public static byte[] download2Bytes(String fileKey) {
        String url = getDownloadUrl(fileKey);
        return IOUtils.toByteArray(new URI(url));
    }

    public static String getDownloadUrl(String fileKey) {
        return getDownloadUrl(fileKey, null);
    }

    @SneakyThrows
    public static String getDownloadUrl(String fileKey, String filename) {
        ServiceData<String> url = CloudDiskClient.getFileDownloadUrl(
                ContextHelper.getEmpNo(), fileKey, filename, 1, 1, false, null, null);
        if (!RetCode.SUCCESS_CODE.equals(url.getCode().getCode())) {
            throw new LcapBusiException(url.getCode().getMsg());
        }

        return url.getBo().replace("http://", "https://");
    }

    @SneakyThrows
    public static String getFileName(String fileKey) {
        List<FileInfoResp> fileInfo
                = CloudDiskClient.queryFileInfoByKeys(Lists.newArrayList(fileKey));
        return CollectionUtils.isEmpty(fileInfo)
                ? ""
                : fileInfo.get(0).getFileName();
    }

    @SneakyThrows
    public static String getDownloadUrlPermanent(String fileKey, String filename) {
        ServiceData<String> url = CloudDiskClient.getFileDownloadUrl(
                ContextHelper.getEmpNo(), fileKey, filename, -1, -1, false, null, null);
        if (!RetCode.SUCCESS_CODE.equals(url.getCode().getCode())) {
            throw new LcapBusiException(url.getCode().getMsg());
        }

        return url.getBo().replace("http://", "https://");
    }

    @SneakyThrows
    public static String upload(String filename, byte[] bytes) {
        String md5 = DigestUtils.md5Hex(bytes);

        ObjectTransStatus status = queryStatus(md5, filename, bytes);
        if (Boolean.TRUE.equals(status.isMd5DirectTransferEnable())) {
            return finish(md5, filename, bytes.length);
        }

        int chunkCount = (int) Math.ceil((double) bytes.length / CHUNK_SIZE);
        String chunks = status.getChunks();
        if (StringUtils.isBlank(chunks)) {
            char[] chars = new char[chunkCount];
            Arrays.fill(chars, '0');
            chunks = new String(chars);
        }

        for (int tryNo = 0 ;
                tryNo < SEND_CHUNKS_TRY_COUNT
                        && !ALL_CHUNKS_SENT.matcher(chunks).matches();
                ++tryNo) {
            for (int chunk = 0; chunk < chunkCount; ++chunk) {
                if (chunks.charAt(chunk) == '0') {
                    sendPartData(md5, chunk, bytes);
                }
            }

            status = queryStatus(md5, filename, bytes);
            chunks = status.getChunks();
        }

        if (Boolean.TRUE.equals(status.isMd5DirectTransferEnable())
                || ALL_CHUNKS_SENT.matcher(chunks).matches()) {
            return finish(md5, filename, bytes.length);
        }

        throw new LcapBusiException(CLOUD_DISK_UPLOAD_FAILED);
    }

    @SneakyThrows
    private static ObjectTransStatus queryStatus(String md5, String filename, byte[] bytes) {
        ServiceData<ObjectTransStatus> status = CloudDiskClient.queryFileStatusByBytes(
                new QueryFileStatusInput() {{
                    setChunkSize(CHUNK_SIZE);
                    setEmpNo(ContextHelper.getEmpNo());
                    setFileName(filename);
                    setFileMd5(md5);
                    setFileSize((long) bytes.length);
                    setMd5DirectTransfer(true);
                }}, bytes);
        if (!RetCode.SUCCESS_CODE.equals(status.getCode().getCode())) {
            throw new LcapBusiException(status.getCode().getMsg());
        }

        return status.getBo();
    }

    private static void sendPartData(String md5, int i, byte[] bytes) {
        int start = i * CHUNK_SIZE;
        int end = Math.min(start + CHUNK_SIZE, bytes.length);
        ServiceData<Boolean> result = CloudDiskClient.sendPartData(
                ArrayUtils.subarray(bytes, start, end), new SendPartDataInput() {{
                    setEmpNo(ContextHelper.getEmpNo());
                    setFileMd5(md5);
                    setChunks(i);
                    setChunkSize(CHUNK_SIZE);
                }});
        if (!RetCode.SUCCESS_CODE.equals(result.getCode().getCode())) {
            throw new LcapBusiException(result.getCode().getMsg());
        }
    }

    private static String finish(String md5, String filename, long fileSize) {
        ServiceData<FileSaveResult> result = CloudDiskClient.finishPartObject(
                new FinishPartObjectInput() {{
                    setEmpNo(ContextHelper.getEmpNo());
                    setFileName(filename);
                    setFileMd5(md5);
                    setFileSize(fileSize);
                    setMd5DirectTransfer(true);
                }});
        if (!RetCode.SUCCESS_CODE.equals(result.getCode().getCode())) {
            throw new LcapBusiException(result.getCode().getMsg());
        }

        return result.getBo().getFileKey();
    }
}
