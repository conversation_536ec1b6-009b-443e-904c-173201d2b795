package com.zte.iccp.itech.extension.domain.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version V1.0
 * @author:
 * @date: 2025/6/19 上午9:28
 * @Description:
 */
@Data
public class CustomerBaseInfoVO {

    @ApiModelProperty("客户ID")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户标识")
    private String accntType;

    @ApiModelProperty("受限制主体")
    private String sanctionedParty;

    @ApiModelProperty("客户标识ID")
    private String accntTypeCd;

    @ApiModelProperty("国家编码")
    private String countryCode;
}
