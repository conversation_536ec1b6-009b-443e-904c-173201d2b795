package com.zte.iccp.itech.extension.domain.enums;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作台映射类型枚举
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/9/14
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum WorkbenchMappingTypeEnum {
    /**
     * 变更单
     */
    NETWORK_CHANGE("network_change",
            "network_change_number",
            Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE,AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE)),
    /**
     * 技术管理任务
     */
    TECHNOLOGY_MANAGEMENT("technology_management",
            "technology_management_number",
            Lists.newArrayList(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT)),
    /**
     * 故障管理任务
     */
    FAULT_MANAGEMENT("fault_management",
            "fault_management_number",
            Lists.newArrayList(AssignmentTypeEnum.FAULT_MANAGEMENT)),
    ;
    /**
     * 复选cid
     */
    private final String cid;
    /**
     * 图文展示cid
     */
    private final String numberCid;

    /**
     * assignmentTypeEnumList
     */
    private final List<AssignmentTypeEnum> assignmentTypeEnumList;


    /**
     * 获取全部任务类型
     *
     * @return List<String>
     */
    public static List<String> getAllAssignmentTypes() {
        List<String> assignmentTypes = new ArrayList<>();

        Arrays.stream(WorkbenchMappingTypeEnum.values()).forEach(mappingTypeEnum -> {
            List<AssignmentTypeEnum> assignmentTypeEnumList = mappingTypeEnum.getAssignmentTypeEnumList();
            assignmentTypes.addAll(assignmentTypeEnumList.stream().map(AssignmentTypeEnum::getValue).collect(Collectors.toList()));
        });
        return assignmentTypes;
    }

    /**
     * 获取任务类型
     *
     * @return List<String>
     */
    public List<String> getAssignmentTypeList() {
        return this.assignmentTypeEnumList.stream().map(AssignmentTypeEnum::getValue).collect(Collectors.toList());
    }

    /**
     * 获取流程编码
     *
     * @return List<String>
     */
    public List<String> getAssignmentFlowCodes() {
        return this.assignmentTypeEnumList.stream()
                .map(item -> item.getApproveFlowCodeEnum().name())
                .collect(Collectors.toList());
    }
}
