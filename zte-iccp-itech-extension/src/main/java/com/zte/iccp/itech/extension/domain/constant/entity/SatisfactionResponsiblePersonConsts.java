package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 故障管理任务--客户满意度责任人配置
 * <AUTHOR> 10335201
 * @date 2024-08-22 下午5:04
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SatisfactionResponsiblePersonConsts {
    /**
     * 国家
     */
    public static final String COUNTRY = "country";

    /**
     * 代表处
     */
    public static final String ORGANIZATION_ID = "organization_id";

    /**
     * 产品分类
     */
    public static final String PRODUCT_ID = "product_id";

    /**
     * 客户支持经理
     */
    public static final String MANAGER = "manager";

    /**
     * 产品科长
     */
    public static final String PRODUCT_SECTION_CHIEF = "product_section_chief";

    /**
     * 是否启用
     */
    public static final String DATA_STATUS = "data_status";


}
