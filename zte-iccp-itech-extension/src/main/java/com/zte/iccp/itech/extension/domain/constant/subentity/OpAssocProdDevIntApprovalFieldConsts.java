package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/26
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OpAssocProdDevIntApprovalFieldConsts {

    public static final String PRODUCT_TYPE = "product_type_rd_integration";

    public static final String APPROVER = "approval_rd_integration";

    public static final String APPROVED_DATE = "approved_data_rd_integration";

    public static final String RESULT = "approve_result_rd_integration";

    public static final String OPINION = "approve_opinion_rd_intetration";

    public static final String EMAIL_CC = "email_rd_integration";

    public static final String INTEGRATED_ASSOCIATED_PRODUCT_RD_IN_ZH = "integrated_associated_product_rd_in_zh";

    public static final String INTEGRATED_ASSOCIATED_PRODUCT_RD_IN_EN = "integrated_associated_product_rd_in_en";
}