package com.zte.iccp.itech.extension.plugin.form.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.TableDefaultConditionAbility;
import com.zte.iccp.itech.extension.ability.common.table.DataAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FilterHelper;
import com.zte.iccp.itech.extension.common.utils.CacheUtils;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.constant.RedisKeys;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.backlog.BacklogTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.export.DataRangeEnum;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.paas.lcap.core.entity.MainEntityType;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.SearchEvent;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.list.IListModel;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.LEVEL_PRODUCT_SUBCLASS;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;

public class AdvancedQueryPlugin extends BaseFormPlugin {

    /** 最早创建时间 */
    private static final String START_DATE = "assignment.firstOrderCreateTime";

    @Override
    public void beforeSearch(SearchEvent searchEvent) {
        IFormView formView = getView();

        // 1.是否展开树
        // 展开树不走设置查询参数逻辑，展开树 iFilter 也不生效
        Map<String, Object> eventArgs = searchEvent.getJsonArgs();
        if (!CollectionUtils.isEmpty(eventArgs) && eventArgs.containsKey("parentId")) {
            return;
        }

        // 2.根据页面，补充指定查询参数
        List<IFilter> iFilters = getQueryConditions();

        // 3.构建查询指令
        DataAbility.setTableQueryCondition
                (formView, CidConstants.TABLE_ASSIGNMENT_CID, iFilters, Assignment.class);
    }

    @Override
    public Optional<Integer> queryCount(SearchEvent searchEvent) {
        IListModel listModel = (IListModel) searchEvent.getSource();

        // 1.获取页面查询条件
        List<IFilter> tableFilters = getQueryConditions();
        tableFilters = tableFilters.stream().filter(Objects::nonNull).collect(Collectors.toList());

        // 2.计算总量
        Integer total = queryCount(tableFilters, listModel.getMainEntityType());
        return Optional.of(total);
    }

    /**
     * 检索总数
     */
    public Integer queryCount(List<IFilter> tableFilters, MainEntityType mainEntityType) {
        // 1.查询条件 SHA256 加密，构建缓存 Key
        String cacheKey = RedisKeys.entityCount(mainEntityType, tableFilters);

        // 2. 获取起始时间
        // 当前测试环境最早创建时间为 2020-05-15, 生产环境最早创建时间为 2024-07-02
        Date startDate = DateUtils.stringToDate(ConfigHelper.get(START_DATE), CommonConstants.DATE_FORMAT);

        // 3.优先从缓存中检索总数结果
        // 缓存中不存在，则启用异步线程分时间段计算总量
        return CacheUtils.get(
                cacheKey,
                () -> AssignmentAbility.asyncStepCount(tableFilters, mainEntityType, startDate),
                5 * 60,
                Integer.class);
    }

    /**
     * 获取页面查询条件
     */
    public List<IFilter> getQueryConditions() {
        IFormView formView = getView();
        IDataModel dataModel = getModel();

        List<IFilter> iFilters;

        String pageCid = formView.getPageId();
        switch (pageCid) {
            case PageConstants.PAGE_WEB_NETWORK_CHANGE_ASSIGNMENT:
                iFilters = defaultNetworkChangeCondition(dataModel);
                addAssignmentCenterCondition(dataModel, iFilters, Lists.newArrayList(
                        AssignmentTypeEnum.NETWORK_CHANGE.getValue(),
                        AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue()));
                break;

            case PageConstants.PAGE_WEB_TECHNOLOGY_MANAGEMENT_ASSIGNMENT:
                iFilters = defaultTechnologyCondition(dataModel);
                addAssignmentCenterCondition(dataModel, iFilters, Lists.newArrayList(
                        AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT.getValue()));
                break;

            case PageConstants.PAGE_WEB_FAULT_MANAGEMENT_ASSIGNMENT:
                iFilters = defaultFaultCondition(dataModel);
                addAssignmentCenterCondition(dataModel, iFilters, Lists.newArrayList(
                        AssignmentTypeEnum.FAULT_MANAGEMENT.getValue()));
                break;

            case PageConstants.PAGE_WEB_HANDLED_BY_ME:
                iFilters = defaultBacklogCondition(dataModel, BacklogTypeEnum.HANDLED_BY_ME);
                addHandledByMeCondition(dataModel, iFilters, Lists.newArrayList());
                break;

            case PageConstants.PAGE_WEB_NETWORK_CHANGE_HANDLED_BY_ME:
                iFilters = defaultNetworkChangeCondition(dataModel);
                addHandledByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.NETWORK_CHANGE.getValue()));
                break;

            case PageConstants.PARTNER_CHANGE_ORDER_HANDLED_BY_ME:
                iFilters = defaultNetworkChangeCondition(dataModel);
                addHandledByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue()));
                break;

            case PageConstants.PAGE_WEB_TECHNOLOGY_HANDLED_BY_ME:
                iFilters = defaultTechnologyCondition(dataModel);
                addHandledByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT.getValue(),
                                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB.getValue()));
                break;

            case PageConstants.PAGE_WEB_APPLICATION_HANDLED_BY_ME:
                iFilters = defaultPermissionApplicationCondition(dataModel);
                addHandledByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.PERMISSION_APPLICATION.getValue()));
                break;

            case PageConstants.FAULT_MANAGE_HANDLED_BY_ME:
                iFilters = defaultFaultCondition(dataModel);
                addHandledByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.FAULT_MANAGEMENT.getValue()));
                break;

            case PageConstants.PAGE_WEB_ABNORMAL_REVIEW_HANDLED_BY_ME:
                iFilters = defaultReviewCondition(dataModel);
                addHandledByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.CLOCK_REVIEW.getValue()));
                break;

            case PageConstants.PAGE_WEB_TO_BE_HANDLED_BY_ME:
                iFilters = defaultBacklogCondition(dataModel, BacklogTypeEnum.TO_BE_HANDLED_BY_ME);
                addToBeHandledCondition(dataModel, iFilters, Lists.newArrayList());
                break;

            case PageConstants.PAGE_WEB_NETWORK_CHANGE_TO_BE_HANDLED_BY_ME:
                iFilters = defaultNetworkChangeCondition(dataModel);
                addToBeHandledCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.NETWORK_CHANGE.getValue()));
                break;

            case PageConstants.PARTNER_CHANGE_ORDER_TO_BE_HANDLED:
                iFilters = defaultNetworkChangeCondition(dataModel);
                addToBeHandledCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue()));
                break;

            case PageConstants.PAGE_WEB_TECHNOLOGY_TO_BE_HANDLED_BY_ME:
                iFilters = defaultTechnologyCondition(dataModel);
                addToBeHandledCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT.getValue(),
                                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB.getValue()));
                break;

            case PageConstants.PAGE_WEB_APPLICATION_TO_BE_HANDLED_BY_ME:
                iFilters = defaultPermissionApplicationCondition(dataModel);
                addToBeHandledCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.PERMISSION_APPLICATION.getValue()));
                break;

            case PageConstants.FAULT_MANAGE_TO_BE_HANDLED:
                iFilters = defaultFaultCondition(dataModel);
                addToBeHandledCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.FAULT_MANAGEMENT.getValue()));
                break;

            case PageConstants.PAGE_WEB_ABNORMAL_REVIEW_TO_BE_HANDLED_BY_ME:
                iFilters = defaultReviewCondition(dataModel);
                addToBeHandledCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.CLOCK_REVIEW.getValue()));
                break;

            case PageConstants.PAGE_WEB_INITIATED_BY_ME:
                iFilters = defaultBacklogCondition(dataModel, BacklogTypeEnum.INITIATED_BY_ME);
                addInitiatedByMeCondition(dataModel, iFilters, Lists.newArrayList());
                break;

            case PageConstants.PAGE_WEB_NETWORK_CHANGE_INITIATED_BY_ME:
                iFilters = defaultNetworkChangeCondition(dataModel);
                addInitiatedByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.NETWORK_CHANGE.getValue()));
                break;

            case PageConstants.PARTNER_CHANGE_ORDER_INITIATED_BY_ME:
                iFilters = defaultNetworkChangeCondition(dataModel);
                addInitiatedByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue()));
                break;

            case PageConstants.PAGE_WEB_TECHNOLOGY_INITIATED_BY_ME:
                iFilters = defaultTechnologyCondition(dataModel);
                addInitiatedByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT.getValue(),
                                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB.getValue()));
                break;

            case PageConstants.PAGE_WEB_APPLICATION_INITIATED_BY_ME:
                iFilters = Lists.newArrayList();
                addInitiatedByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.PERMISSION_APPLICATION.getValue()));
                break;

            case PageConstants.FAULT_MANAGE_INITIATED_BY_ME:
                iFilters = defaultFaultCondition(dataModel);
                addInitiatedByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.FAULT_MANAGEMENT.getValue()));
                break;

            case PageConstants.PAGE_WEB_ABNORMAL_REVIEW_INITIATED_BY_ME:
                iFilters = defaultReviewCondition(dataModel);
                addInitiatedByMeCondition(
                        dataModel, iFilters, Lists.newArrayList(
                                AssignmentTypeEnum.CLOCK_REVIEW.getValue()));
                break;

            case PageConstants.PAGE_WEB_OPERATION_PLAN_MANAGEMENT:
                iFilters = defaultOperationPlanCondition(dataModel);
                addAssignmentCenterCondition(dataModel, iFilters, Lists.newArrayList(
                        AssignmentTypeEnum.OPERATION_PLAN_TASK.getValue()));
                break;
            default:
                iFilters = Lists.newArrayList();
                break;
        }

        return iFilters;
    }

    /**
     * 导出时获取页面查询条件
     */
    public List<IFilter> getExportQueryConditions(DataRangeEnum dataRange) {
        IDataModel dataModel = getView().getParentView().getParentView().getDataModel();

        List<IFilter> iFilters = defaultNetworkChangeCondition(dataModel);
        if (DataRangeEnum.NETWORK_CHANGE.equals(dataRange)) {
            addAssignmentCenterCondition(dataModel, iFilters, Lists.newArrayList(
                    AssignmentTypeEnum.NETWORK_CHANGE.getValue()));
        } else {
            addAssignmentCenterCondition(dataModel, iFilters, Lists.newArrayList(
                    AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getValue()));
        }
        iFilters.add(TableDefaultConditionAbility.assignmentNameFilter(
                dataModel, CidConstants.COMPONENT_ASSIGNMENT_NAME_CID));
        iFilters.add(TableDefaultConditionAbility.assignmentCodeFilter(
                dataModel, CidConstants.COMPONENT_ASSIGNMENT_CODE_CID));
        return iFilters;
    }

    /**
     * 默认高级查询条件 - 待办
     */
    private List<IFilter> defaultBacklogCondition(
            IDataModel dataModel,
            BacklogTypeEnum backlogTypeEnum) {

        List<IFilter> iFilters = Lists.newArrayList();

        // 1.任务名称
        iFilters.add(TableDefaultConditionAbility.assignmentNameFilter(
                dataModel, CidConstants.TEXT_FIELD_TASK_SUBJECT_CID));

        // 2.任务单号
        iFilters.add(TableDefaultConditionAbility.assignmentCodeFilter(
                dataModel, CidConstants.TEXT_FIELD_BILL_CODE_CID));

        // 3.任务类型
        iFilters.add(TableDefaultConditionAbility.assignmentTypeFilter(
                dataModel, CidConstants.MULTI_SELECT_FIELD_TASK_TYPE_CID));

        // 4.代表处
        iFilters.add(ResponsibleUtils.organizationFilter(
                dataModel,
                CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                AssignmentFieldConsts.REPRESENTATIVE_OFFICE,
                true,
                OrganizationTreeVo::getHrOrgId));

        // 5.产品经营团队
        iFilters.add(TableDefaultConditionAbility.assignmentProductTeamFilter(
                dataModel, CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_PRODUCT_CID));

        // 我发起的 页面不涉及如下字段
        if (!BacklogTypeEnum.INITIATED_BY_ME.equals(backlogTypeEnum)) {
            // 6.是否审批任务
            iFilters.add(TableDefaultConditionAbility.approveTaskFilter(
                    dataModel, backlogTypeEnum, CidConstants.SELECT_FIELD_IS_APPROVE_TASK_CID));
        }

        return iFilters;
    }

    /**
     * 默认高级查询条件 - 网络变更
     */
    private List<IFilter> defaultNetworkChangeCondition(IDataModel dataModel) {
        List<IFilter> iFilters = Lists.newArrayList();

        // 1.任务名称 / 单号
        iFilters.add(TableDefaultConditionAbility.assignmentNameAndCodeWithSubFilter(
                dataModel, CidConstants.COMPONENT_ASSIGNMENT_CODE_AND_NAME_CID));

        // 2.任务状态
        iFilters.add(TableDefaultConditionAbility.assignmentStatusFilter(
                dataModel, AssignmentTypeEnum.NETWORK_CHANGE, AssignmentFieldConsts.ASSIGNMENT_STATUS));

        // 3.代表处
        iFilters.add(ResponsibleUtils.organizationFilter(
                dataModel,
                CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                AssignmentFieldConsts.REPRESENTATIVE_OFFICE,
                true,
                OrganizationTreeVo::getHrOrgId));

        // 4.产品分类
        iFilters.add(ProductUtils.productFilter(
                dataModel,
                CidConstants.COMPONENT_PRODUCT_TREE_CID,
                PRODUCT_CLASSIFICATION,
                LEVEL_PRODUCT_SUBCLASS,
                true));

        // 5.操作类型
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                NetworkChangeFieldConsts.OPERATION_TYPE,
                NetworkChangeFieldConsts.OPERATION_TYPE));

        // 6.操作开始时间
        // (1)操作开始时间
        iFilters.add(
                TableDefaultConditionAbility.operationStartTimeFilter(dataModel,
                        NetworkChangeFieldConsts.PLAN_START_TIME));

        // (2)操作开始时间（UTC+8）
        iFilters.add(
                TableDefaultConditionAbility.operationStartTimeFilter(dataModel,
                        NetworkChangeFieldConsts.OPERATION_START_TIME_UTC_8));

        // 7.国家 / 地区
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                NetworkChangeFieldConsts.COUNTRY,
                NetworkChangeFieldConsts.COUNTRY));

        // 8.客户标识
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel, CUSTOMER_CLASSIFICATION, CUSTOMER_CLASSIFICATION));

        // 9.操作原因
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                NetworkChangeFieldConsts.OPERATION_REASON,
                NetworkChangeFieldConsts.OPERATION_REASON));

        // 10.操作结果
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                NetworkChangeFieldConsts.OPERATION_RESULT,
                NetworkChangeFieldConsts.OPERATION_RESULT));

        // 11.当前进展
        iFilters.add(
                TableDefaultConditionAbility.currentProgressFilter(dataModel));

        // 12.重要程度
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                NetworkChangeFieldConsts.IMPORTANCE,
                NetworkChangeFieldConsts.IMPORTANCE));

        // 13.风险评估
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                NetworkChangeFieldConsts.RISK_EVALUATION,
                NetworkChangeFieldConsts.RISK_EVALUATION));

        // 14.操作等级
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                NetworkChangeFieldConsts.OPERATION_LEVEL,
                NetworkChangeFieldConsts.OPERATION_LEVEL));

        // 15.客户网络名称
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel, NETWORK, NETWORK));

        // 16.局点名称
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                NetworkChangeFieldConsts.OFFICE_NAME,
                NetworkChangeFieldConsts.OFFICE_NAME));

        // 扩展表必填字段非空，强制INNER JOIN
        iFilters.add(new Filter(
                NetworkChangeFieldConsts.OPERATION_TYPE,
                Comparator.IS_NOT_NULL, null));

        return iFilters;
    }

    /**
     * 默认高级查询条件 - 技术管理
     */
    private List<IFilter> defaultTechnologyCondition(IDataModel dataModel) {
        List<IFilter> iFilters = Lists.newArrayList();

        // 1.任务名称 / 单号
        iFilters.add(TableDefaultConditionAbility.assignmentNameAndCodeWithSubFilter(
                dataModel, CidConstants.COMPONENT_ASSIGNMENT_CODE_AND_NAME_CID));

        // 2.任务状态
        iFilters.add(TableDefaultConditionAbility.assignmentStatusFilter(
                dataModel, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT, AssignmentFieldConsts.ASSIGNMENT_STATUS));

        // 3.任务分类
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                TechnologyManagementFieldConsts.TASK_CATEGORY,
                TechnologyManagementFieldConsts.TASK_CATEGORY));

        // 4.代表处
        iFilters.add(ResponsibleUtils.organizationFilter(
                dataModel,
                CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                AssignmentFieldConsts.REPRESENTATIVE_OFFICE,
                true,
                OrganizationTreeVo::getHrOrgId));

        // 5.产品分类
        iFilters.add(ProductUtils.productFilter(
                dataModel,
                CidConstants.COMPONENT_PRODUCT_TREE_CID,
                AssignmentFieldConsts.PRODUCT_CLASSIFICATION,
                CommonConstants.LEVEL_PRODUCT_MODEL,
                false));

        // 6.责任人
        iFilters.add(TableDefaultConditionAbility.defaultEmployeeFilter(
                dataModel, RESPONSIBLE, RESPONSIBLE_EMPLOYEE_FIELD, true));

        // 7.是否超期
        iFilters.add(TableDefaultConditionAbility.technologyAssignmentOverdueFlagFilter(
                dataModel, CidConstants.COMPONENT_OVERDUE_FLAG_CID));

        // 扩展表必填字段非空，强制INNER JOIN
        iFilters.add(new Filter(
                TechnologyManagementFieldConsts.TASK_CATEGORY,
                Comparator.IS_NOT_NULL, null));

        return iFilters;
    }

    /**
     * 默认高级查询条件 - 故障管理
     */
    private List<IFilter> defaultFaultCondition(IDataModel dataModel) {
        List<IFilter> iFilters = Lists.newArrayList();

        // 1.任务名称 / 单号
        iFilters.add(TableDefaultConditionAbility.assignmentNameAndCodeFilter(
                dataModel, CidConstants.COMPONENT_ASSIGNMENT_CODE_AND_NAME_CID));

        // 2.任务状态
        iFilters.add(TableDefaultConditionAbility.assignmentStatusFilter(
                dataModel, AssignmentTypeEnum.FAULT_MANAGEMENT, AssignmentFieldConsts.ASSIGNMENT_STATUS));

        // 3.代表处
        iFilters.add(ResponsibleUtils.organizationFilter(
                dataModel,
                CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                AssignmentFieldConsts.REPRESENTATIVE_OFFICE,
                true,
                OrganizationTreeVo::getHrOrgId));


        // 4.产品经营团队
        iFilters.add(TableDefaultConditionAbility.faultAssignmentProductManageTeamFilter(
                dataModel, CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_PRODUCT_CID));

        // 5.国家
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                FaultManagementFieldConsts.REGION,
                FaultManagementFieldConsts.REGION));

        return iFilters;
    }

    /**
     * 默认高级查询条件 - 权限申请
     */
    private List<IFilter> defaultPermissionApplicationCondition(IDataModel dataModel) {
        List<IFilter> iFilters = Lists.newArrayList();

        // 1.任务名称
        iFilters.add(
                TableDefaultConditionAbility.assignmentNameFilter(dataModel, ASSIGNMENT_NAME));

        // 2.任务单号
        iFilters.add(
                TableDefaultConditionAbility.assignmentCodeFilter(dataModel, ASSIGNMENT_CODE));

        // 3.任务类型
        iFilters.add(
                TableDefaultConditionAbility.assignmentTypeFilter(dataModel, ASSIGNMENT_TYPE));

        // 4.角色
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel, PermissionApplicationFieldConsts.ROLE, PermissionApplicationFieldConsts.ROLE));

        // 5.代表处
        iFilters.add(ResponsibleUtils.organizationFilter(
                dataModel,
                CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                AssignmentFieldConsts.REPRESENTATIVE_OFFICE,
                true,
                OrganizationTreeVo::getHrOrgId));

        // 6.产品
        iFilters.add(ProductUtils.productFilter(
                dataModel,
                CidConstants.COMPONENT_PRODUCT_TREE_CID,
                AssignmentFieldConsts.PRODUCT_CLASSIFICATION,
                CommonConstants.LEVEL_PRODUCT_MANAGEMENT_TEAM,
                true));


        // 扩展表必填字段非空，强制INNER JOIN
        iFilters.add(new Filter(
                PermissionApplicationFieldConsts.MODULE,
                Comparator.IS_NOT_NULL, null));

        return iFilters;
    }

    /**
     * 默认高级查询条件 - 打卡复盘
     */
    private List<IFilter> defaultReviewCondition(IDataModel dataModel) {
        List<IFilter> iFilters = Lists.newArrayList();

        // 1.操作主题 / 单号
        iFilters.add(TableDefaultConditionAbility.assignmentNameAndCodeFilter(
                dataModel, CidConstants.COMPONENT_ASSIGNMENT_CODE_AND_NAME_CID));

        // 2.营销
        iFilters.add(TableDefaultConditionAbility.assignmentOrganizationFilter(
                dataModel,
                MARKETING,
                MARKETING,
                CommonConstants.LEVEL_ORGANIZATION_MARKETING));

        // 3.代表处
        iFilters.add(TableDefaultConditionAbility.assignmentOrganizationFilter(
                dataModel,
                REPRESENTATIVE_OFFICE,
                REPRESENTATIVE_OFFICE,
                CommonConstants.LEVEL_ORGANIZATION_REPRESENTATIVE_OFFICE));

        // 4.产品分类
        iFilters.add(ProductUtils.productFilter(
                dataModel,
                CidConstants.COMPONENT_PRODUCT_TREE_CID,
                PRODUCT_CLASSIFICATION,
                LEVEL_PRODUCT_SUBCLASS,
                true));

        // 5.操作类型
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                ClockReviewsFieldConsts.OPERATION_TYPE,
                ClockReviewsFieldConsts.OPERATION_TYPE));

        // 6.操作等级
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                ClockReviewsFieldConsts.OPERATION_LEVEL,
                ClockReviewsFieldConsts.OPERATION_LEVEL));

        // 7.操作负责人
        iFilters.add(TableDefaultConditionAbility.defaultEmployeeFilter(
                dataModel, RESPONSIBLE, RESPONSIBLE_EMPLOYEE_FIELD, true));

        // 8.操作时间
        iFilters.add(TableDefaultConditionAbility.defaultDateTimeSectionFilter(
                dataModel,
                ClockReviewsFieldConsts.PLAN_OPERATION_START_TIME,
                ClockReviewsFieldConsts.PLAN_OPERATION_START_TIME));

        // 9.复盘状态
        iFilters.add(TableDefaultConditionAbility.assignmentStatusFilter(
                dataModel, AssignmentTypeEnum.CLOCK_REVIEW, AssignmentFieldConsts.ASSIGNMENT_STATUS));

        // 扩展表必填字段非空，强制INNER JOIN
        iFilters.add(new Filter(
                ClockReviewsFieldConsts.BATCH_NO,
                Comparator.IS_NOT_NULL, null));

        return iFilters;
    }

    /**
     * 高级查询条件 - 任务中心相关条件
     */
    private void addAssignmentCenterCondition(
            IDataModel dataModel,
            List<IFilter> iFilters,
            List<String> assignmentTypes) {

        // 1.任务类型
        iFilters.add(
                FilterHelper.multiMemberOfFilter(ASSIGNMENT_TYPE, assignmentTypes));

        // 2.用户权限
        iFilters.add(AssignmentAbility.userPermissionFilter(ContextHelper.getEmpNo()));

        // 故障管理任务页签 不涉及如下条件
        if (assignmentTypes.size() != 1
                || !assignmentTypes.contains(AssignmentTypeEnum.FAULT_MANAGEMENT.getValue())) {
            // 3.创建人
            iFilters.add(TableDefaultConditionAbility.defaultEmployeeFilter(
                    dataModel, CidConstants.COMPONENT_CREATED_BY_CID, CommonFieldConsts.CREATE_BY, true));

            // 4.创建时间
            iFilters.add(TableDefaultConditionAbility.defaultDateTimeSectionFilter(
                    dataModel,
                    CidConstants.COMPONENT_CREATED_DATE_CID,
                    CommonFieldConsts.CREATE_TIME));
        }
    }

    /**
     * 高级查询条件 - 我已处理相关条件
     */
    private void addHandledByMeCondition(
            IDataModel dataModel,
            List<IFilter> iFilters,
            List<String> assignmentTypes) {

        // 1.任务类型
        iFilters.add(
                FilterHelper.multiMemberOfFilter(ASSIGNMENT_TYPE, assignmentTypes));

        // 2.我已处理
        iFilters.add(TableDefaultConditionAbility.assignmentHandledByMeFilter());

        // 故障管理任务 / 打卡复盘 页签 不涉及如下条件
        List<String> nonCreatedInfoTypes = Lists.newArrayList(
                AssignmentTypeEnum.FAULT_MANAGEMENT.getValue(),
                AssignmentTypeEnum.CLOCK_REVIEW.getValue());
        if (assignmentTypes.stream().noneMatch(nonCreatedInfoTypes::contains)) {
            // 3.创建人
            iFilters.add(TableDefaultConditionAbility.defaultEmployeeFilter(
                    dataModel, CidConstants.COMPONENT_CREATED_BY_CID, CommonFieldConsts.CREATE_BY, true));

            // 4.创建时间
            iFilters.add(TableDefaultConditionAbility.backlogCreatedTimeFilter(
                    dataModel, CidConstants.COMPONENT_CREATED_DATE_CID));
        }
    }

    /**
     * 高级查询条件 - 待我处理相关条件
     */
    private void addToBeHandledCondition(
            IDataModel dataModel,
            List<IFilter> iFilters,
            List<String> assignmentTypes) {

        // 1.任务类型
        iFilters.add(
                FilterHelper.multiMemberOfFilter(ASSIGNMENT_TYPE, assignmentTypes));

        // 2.待我处理
        iFilters.add(FilterHelper.multiMemberOfFilter(
                CURRENT_PROCESSOR_EMPLOYEE_FIELD, Lists.newArrayList(ContextHelper.getEmpNo())));

        // 故障管理任务 / 打卡复盘 页签 不涉及如下条件
        List<String> nonCreatedInfoTypes = Lists.newArrayList(
                AssignmentTypeEnum.FAULT_MANAGEMENT.getValue(),
                AssignmentTypeEnum.CLOCK_REVIEW.getValue());
        if (assignmentTypes.stream().noneMatch(nonCreatedInfoTypes::contains)) {
            // 3.创建人
            iFilters.add(TableDefaultConditionAbility.defaultEmployeeFilter(
                    dataModel, CidConstants.COMPONENT_CREATED_BY_CID, CommonFieldConsts.CREATE_BY, true));

            // 4.创建时间
            iFilters.add(TableDefaultConditionAbility.backlogCreatedTimeFilter(
                    dataModel, CidConstants.COMPONENT_CREATED_DATE_CID));
        }
    }

    /**
     * 高级查询条件 - 我发起的相关条件
     */
    private void addInitiatedByMeCondition(
            IDataModel dataModel,
            List<IFilter> iFilters,
            List<String> assignmentTypes) {

        // 1.任务类型
        iFilters.add(
                FilterHelper.multiMemberOfFilter(ASSIGNMENT_TYPE, assignmentTypes));

        // 2.我发起的
        iFilters.add(
                new Filter(CommonFieldConsts.CREATE_BY, Comparator.EQ, ContextHelper.getEmpNo()));

        // 故障管理任务页签 / 权限申请任务页签 / 打卡复盘任务页签 不涉及如下条件
        List<String> nonCreatedInfoTypes = Lists.newArrayList(
                AssignmentTypeEnum.FAULT_MANAGEMENT.getValue(),
                AssignmentTypeEnum.PERMISSION_APPLICATION.getValue(),
                AssignmentTypeEnum.CLOCK_REVIEW.getValue());
        if (assignmentTypes.stream().noneMatch(nonCreatedInfoTypes::contains)) {
            // 3.创建时间
            iFilters.add(TableDefaultConditionAbility.backlogCreatedTimeFilter(
                    dataModel, CidConstants.COMPONENT_CREATED_DATE_CID));
        }
    }

    /**
     * 默认高级查询条件 - 操作管理
     */
    private List<IFilter> defaultOperationPlanCondition(IDataModel dataModel) {
        List<IFilter> iFilters = Lists.newArrayList();

        // 1.任务名称 / 单号
        iFilters.add(TableDefaultConditionAbility.assignmentNameAndCodeWithSubFilter(
                dataModel, CidConstants.COMPONENT_ASSIGNMENT_CODE_AND_NAME_CID));

        // 2.产品分类
        iFilters.add(ProductUtils.productFilter(
                dataModel,
                CidConstants.COMPONENT_PRODUCT_TREE_CID,
                PRODUCT_CLASSIFICATION,
                LEVEL_PRODUCT_SUBCLASS,
                true));

        // 3.代表处
        iFilters.add(ResponsibleUtils.organizationFilter(
                dataModel,
                CidConstants.ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID,
                AssignmentFieldConsts.REPRESENTATIVE_OFFICE,
                true,
                OrganizationTreeVo::getHrOrgId));

        // 4.操作类型
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                OperationPlanFieldConsts.OPERATION_TYPE,
                OperationPlanFieldConsts.OPERATION_TYPE));

        // 5.计划操作开始时间
        iFilters.add(
                TableDefaultConditionAbility.operationStartTimeFilter(dataModel,
                        OperationPlanFieldConsts.PLAN_ORDER_START_TIME));

        // 6.操作原因
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                OperationPlanFieldConsts.OPERATION_REASON,
                OperationPlanFieldConsts.OPERATION_REASON));

        // 7.国家 / 地区
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                OperationPlanFieldConsts.COUNTRY,
                OperationPlanFieldConsts.COUNTRY));

        // 8.客户标识
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel, CUSTOMER_CLASSIFICATION, CUSTOMER_CLASSIFICATION));

        // 9.任务状态
        iFilters.add(TableDefaultConditionAbility.assignmentStatusFilter(
                dataModel, AssignmentTypeEnum.OPERATION_PLAN_TASK, AssignmentFieldConsts.ASSIGNMENT_STATUS));

        // 10.重要程度
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                OperationPlanFieldConsts.IMPORTANCE,
                OperationPlanFieldConsts.IMPORTANCE));

        // 11.风险评估
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                OperationPlanFieldConsts.RISK_EVALUATION,
                OperationPlanFieldConsts.RISK_EVALUATION));

        // 12.操作等级
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                OperationPlanFieldConsts.OPERATION_LEVEL,
                OperationPlanFieldConsts.OPERATION_LEVEL));

        // 13.客户网络名称
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel, NETWORK, NETWORK));

        // 14.局点名称
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel,
                OperationPlanFieldConsts.OFFICE_NAME,
                OperationPlanFieldConsts.OFFICE_NAME));

        // 扩展表必填字段非空，强制INNER JOIN
        iFilters.add(new Filter(
                OperationPlanFieldConsts.OPERATION_TYPE,
                Comparator.IS_NOT_NULL, null));

        return iFilters;
    }
}
