package com.zte.iccp.itech.extension.ability.clockin.callplan;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.BaseOperationCacheAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility;
import com.zte.iccp.itech.extension.common.helper.HrHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallReasonEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInCallPlan;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInCallee;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.platform.util.IdGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility.getKeyTask;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInCallPlanFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInRecordFieldConsts.CLOCK_IN_OPTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum.OPERATING_SUPERVISOR;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
public final class ClockInCallPlanCreateAbility extends BaseOperationCacheAbility {

    /** 您有一个操作【{0}】需要打{1}卡，请及时完成，谢谢! */
    private static final String CONTENT_CLOCK_IN_KEY = "callPlan.content.clockInNotify";

    private static final String CONTENT_EXCEPTION_DUTY_KEY = "callPlan.content.exception.prodLine";

    private static final String CONTENT_EXCEPTION_OFFICE_KEY = "callPlan.content.exception.office";

    /** 您有一个被转交的值守操作【操作主题】需要打卡，请及时完成，谢谢! */
    public static final String CALLPLAN_CONTENT_TRANSFER_CALL = "callPlan.content.transfer.call";

    /** 您处网络变更操作【{0}】偏离30分钟未执行打卡，值守人员【{1}】，请您及时介入了解情况 */
    public static final String CALLPLAN_CONTENT_ON_DUTY_THIRD_TIME_CALL = "callPlan.content.on.duty.third.time.call";

    /** 您处网络变更操作【{0}】偏离30分钟未执行打卡，现场操作负责人【{1}】，请您及时介入了解情况 */
    public static final String CALLPLAN_CONTENT_OPERATION_THIRD_TIME_CALL = "callPlan.content.operation.third.time.call";

    private static final String[] CLOCK_IN_TASK_FIELDS_4_CLOCK_NOTIFY = {
            ENTITY_TYPE,
            CHANGE_ORDER_ID,
            OPERATION_SUBJECT,
            BATCH_TASK_ID,
            OPERATOR,
            TIME_ZONE,
            PLAN_PREPARE_START_TIME,
            PLAN_PREPARE_END_TIME,
            PLAN_EXECUTE_START_TIME,
            PLAN_EXECUTE_END_TIME,
            PLAN_TEST_START_TIME,
            PLAN_TEST_END_TIME,
            TOTAL_ON_DUTY_DURATION,
            ON_DUTY_FREQUENCY,
            STAGE_ON_DUTY_END_TIME,
            ALREADY_ON_DUTY_FREQUENCY,
            LAST_MODIFIED_TIME,
            CREATE_TIME,
            TRANSFER_FREQUENCY
    };

    public ClockInCallPlanCreateAbility() {
        this(null);
    }

    public ClockInCallPlanCreateAbility(OperationCache operationCache) {
        super(operationCache);
    }

    /**
     * 创建异常卡呼叫计划
     */
    public void create4ExceptionClock(ClockInOptionEnum clockInOption, String clockInTaskId) {
        ClockInTask clockInTask = QueryDataHelper.get(
                ClockInTask.class,
                Lists.newArrayList(
                        ENTITY_TYPE,
                        CHANGE_ORDER_ID,
                        TIME_ZONE,
                        BATCH_TASK_ID,
                        BATCH_CODE,
                        OPERATION_SUBJECT,
                        OPERATOR),
                clockInTaskId);

        if (clockInOption == ClockInOptionEnum.OPERATION_FAILED_REPEAT) {
            List<ClockInCallee> callee = CalleeQueryAbility
                    .getRemoteSupporters(clockInTask.getEntityType().getEntityClass(), clockInTask.getBatchTaskId());
            String prodLineContent = getExceptionContent(CONTENT_EXCEPTION_DUTY_KEY, clockInTask);
            create(forBatchCreate(clockInTaskId, prodLineContent, callee));
            return;
        }

        if (clockInTask.getEntityType() == BatchTaskTypeEnum.BATCH_TASK) {
            create4ZteException(clockInTask);
            return;
        }

        if (clockInTask.getEntityType() == BatchTaskTypeEnum.SUBCONTRACT_BATCH_TASK) {
            create4SubconException(clockInTask);
        }
    }

    /**
     * 创建下一阶段呼叫计划
     */
    public void create4NextState(String clockInTaskId, CallReasonEnum clockedState) {
        NextCallPlanInfo nextInfo = getClockInfo(clockInTaskId, clockedState);
        if (nextInfo == null) {
            if (clockedState != CallReasonEnum.TEST_END
                    && clockedState != CallReasonEnum.ON_DUTY_END) {
                return;
            }

            update(ClockInTask.class, Lists.newArrayList(MapUtils.newHashMap(
                    ID, clockInTaskId,
                    PLAN_CALL_UTC_TIME, null)));
            return;
        }

        switch (nextInfo.state) {
            case OPERATION_START:
            case OPERATION_END:
            case TEST_END:
                create4ClockInNotify(
                        nextInfo.state,
                        nextInfo.state.getPlanClockInTime(),
                        MapUtils.newHashMap(nextInfo.task, nextInfo.phoneNum));
                break;
            case ON_DUTY_CONTINUE:
            case ON_DUTY_END:
                create4ClockInNotify(
                        nextInfo.state,
                        this::onDutyCallTime,
                        MapUtils.newHashMap(nextInfo.task, nextInfo.phoneNum));
                break;
            default:
                break;
        }
    }

    public void create4ClockInNotify(
            CallReasonEnum clockInState,
            Map<ClockInTask, String> taskPhoneNums) {
        create4ClockInNotify(
                clockInState,
                clockInState.getPlanClockInTime(),
                taskPhoneNums);
    }

    public void createCallChain(int count, ClockInCallPlan... plans) {
        if (plans.length == 0) {
            return;
        }

        List<ClockInCallPlan> chain = Lists.newArrayList();
        String prevPlanId = null;

        for (ClockInCallPlan clockInCallPlan : plans) {
            for (int j = 0; j < count; ++j) {
                ClockInCallPlan plan = clockInCallPlan.clone();
                plan.setPrevPlanId(prevPlanId);
                plan.setId(prevPlanId = IdGenerator.genInstanceDataId());
                chain.add(plan);
            }
        }

        create(chain);
    }

    public void transferAiCall(ClockInTask clockInTask, SingleEmployee sourceOperator) {
        // 仅国内办事处创建ai呼叫计划
        if (DeptTypeEnum.INNER != ResponsibleUtils.getDeptType(clockInTask.getResponsibleDept())) {
            return;
        }
        // 未到值守阶段时没有值守打卡任务的呼叫计划，不用转交及创建呼叫计划
        if (clockInTask.getOperator() == null || sourceOperator == null || clockInTask.getStageOnDutyEndTime() == null) {
            return;
        }
        // 查询被转交的操作人电话
        String phoneNum = getOperatorPhoneNum(clockInTask);
        // 操作人电话为空时（含转交值守人场景）：查询员工中心该员工的手机号（存在多个电话时取第一个），若员工中心也无手机号则不呼叫
        final String fPhoneNum = StringUtils.getIfEmpty(
                phoneNum, () -> HrHelper.queryPhoneNum(clockInTask.getOperator().getId()));
        if (StringUtils.isBlank(fPhoneNum)) {
            return;
        }
        // 呼叫对象
        ClockInCallee clockInCallee = new ClockInCallee() {{
            setUserId(clockInTask.getOperator().getId());
            setPhoneNum(fPhoneNum);
        }};
        // 转交相关呼叫计划
        changeCallee(clockInTask, clockInCallee, sourceOperator);
        // 创建呼叫计划（当前时间/计划值守时间 + 30min）
        transferCreate(clockInTask, clockInCallee);
    }

    /**
     * 转交相关呼叫计划
     */
    private void changeCallee(ClockInTask clockInTask, ClockInCallee clockInCallee, SingleEmployee sourceOperator) {
        // 查询呼叫计划
        List<ClockInCallPlan> callPlans = QueryDataHelper.query(
                ClockInCallPlan.class,
                Lists.newArrayList(ID, CALLEE),
                Lists.newArrayList(
                        new Filter(OBJECT_ID, Comparator.EQ, clockInTask.getId()),
                        new Filter(REASON, Comparator.IN, Lists.newArrayList(CallReasonEnum.ON_DUTY_CONTINUE, CallReasonEnum.ON_DUTY_END)),
                        new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.WAITING))));
        // 变更呼叫对象并更新呼叫计划
        List<ClockInCallPlan> updateCallPlans = callPlans.stream()
                // 过滤出当前呼叫对象是转交前的人的呼叫计划
                .filter(p -> p.getCallee().getUserId().equals(sourceOperator.getId()))
                .peek(p -> p.setCallee(clockInCallee))
                .collect(Collectors.toList());
        update(updateCallPlans);
    }

    /**
     * 创建呼叫计划（当前时间 + 30min）
     */
    private void transferCreate(ClockInTask clockInTask, ClockInCallee clockInCallee) {
        List<ClockInCallPlan> callPlans = Lists.newArrayList();
        /* Started by AICoder, pid:y35ce5ad112a1d014c830af930dbe304d348e476 */
        // 计算FirstTimeDelayMinutes后的时间
        Instant thirtyMinutesLater = Instant.now().plus(CallReasonEnum.TRANSFER.getFirstTimeDelayMinutes(), ChronoUnit.MINUTES);
        // 将FirstTimeDelayMinutes后的时间转换为秒级时间戳（计划呼叫时间）
        long callUtcTime = thirtyMinutesLater.getEpochSecond();
        /* Ended by AICoder, pid:y35ce5ad112a1d014c830af930dbe304d348e476 */
        // 呼叫内容
        String content = MsgUtils.getLangMessage(ZH_CN, CALLPLAN_CONTENT_TRANSFER_CALL, clockInTask.getOperationSubject());

        String thirdContent = MsgUtils.getLangMessage(
                ZH_CN, CALLPLAN_CONTENT_ON_DUTY_THIRD_TIME_CALL,
                clockInTask.getOperationSubject(),
                clockInTask.getOperator().getTextByLanguage(ZH_CN));

        callPlans.addAll(forCreateRepeatCallPlan(new ClockInCallPlan() {{
            setObjectId(clockInTask.getId());
            setReason(CallReasonEnum.TRANSFER);
            setCallUtcTime(callUtcTime);
            setCallee(clockInCallee);
            setContent(content);
            setCallStatus(CallStatusEnum.WAITING);
        }}, getThirdClockInCallee(clockInTask), thirdContent));

        create(callPlans);
    }

    /**
     * 创建打卡通知呼叫计划，并且更新打卡任务的呼叫时间字段
     */
    private void create4ClockInNotify(
            CallReasonEnum clockInState,
            Function<ClockInTask, Date> planClockInTimeGetter,
            Map<ClockInTask, String> taskPhoneNums) {
        List<ClockInCallPlan> callPlans = Lists.newArrayList();
        List<ClockInTask> task4Update = Lists.newArrayList();

        List<ClockInCallee> thirdClockInCallee = getThirdClockInCallee(
                taskPhoneNums.keySet().stream()
                        .filter(Objects::nonNull)
                        .findAny()
                        .orElseThrow(NullPointerException::new));

        taskPhoneNums.forEach((clockInTask, phoneNum) -> {
            final String fPhoneNum = StringUtils.getIfEmpty(
                    phoneNum, () -> HrHelper.queryPhoneNum(clockInTask.getOperator().getId()));
            long planClockInUtcTime = clockInTask.getTimeZone()
                    .fix(planClockInTimeGetter.apply(clockInTask))
                    .getTime() / 1000;

            // 目前只有国内需要打卡
            String content = MsgUtils.getLangMessage(
                    ZH_CN, CONTENT_CLOCK_IN_KEY, clockInTask.getOperationSubject(), clockInState.getClockInName());

            String thirdContent = MsgUtils.getLangMessage(
                    ZH_CN, CALLPLAN_CONTENT_OPERATION_THIRD_TIME_CALL,
                    clockInTask.getOperationSubject(),
                    clockInTask.getOperator().getTextByLanguage(ZH_CN));
            boolean onDutyFlag = clockInState == CallReasonEnum.ON_DUTY_CONTINUE
                    || clockInState == CallReasonEnum.ON_DUTY_END;
            if (onDutyFlag) {
                thirdContent = MsgUtils.getLangMessage(
                        ZH_CN, CALLPLAN_CONTENT_ON_DUTY_THIRD_TIME_CALL,
                        clockInTask.getOperationSubject(),
                        clockInTask.getOperator().getTextByLanguage(ZH_CN));
            }

            long firstUtcTime = planClockInUtcTime + 60L * clockInState.getFirstTimeDelayMinutes();
            callPlans.addAll(forCreateRepeatCallPlan(new ClockInCallPlan() {{
                setObjectId(clockInTask.getId());
                setReason(clockInState);
                setCallUtcTime(firstUtcTime);
                setCallee(new ClockInCallee() {{
                    setUserId(clockInTask.getOperator().getId());
                    setPhoneNum(fPhoneNum);
                }});
                setContent(content);
                setCallStatus(CallStatusEnum.WAITING);
            }}, thirdClockInCallee, thirdContent));

            task4Update.add(new ClockInTask() {{
                setId(clockInTask.getId());
                setPlanCallUtcTime(firstUtcTime);
            }});
        });

        create(callPlans);
        update(task4Update);
    }

    private List<ClockInCallee> getThirdClockInCallee(ClockInTask clockInTask) {
        List<String> userIds = Lists.newArrayList();

        //内部变更单 审核人配置--代表处人员配置--办事处产品科长/项目TD
        if (clockInTask.getEntityType() == BatchTaskTypeEnum.BATCH_TASK) {
            ChangeOrder changeOrder = ChangeOrderAbility.get(
                    clockInTask.getChangeOrderId(), Lists.newArrayList(
                            ChangeOrderFieldConsts.RESPONSIBLE_DEPT,
                            ChangeOrderFieldConsts.PRODUCT_CATEGORY,
                            ChangeOrderFieldConsts.ACCN_TYPE,
                            ChangeOrderFieldConsts.IS_GOV_ENT));

            userIds = getThirdClockInUserIds(changeOrder.getProductCategory(), changeOrder.getResponsibleDept(),
                    changeOrder.getIsGovEnt(), ApproveRoleEnum.PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE_PROJECT_TD);
        }

        //合作方变更单 单据中的网络责任人
        if (clockInTask.getEntityType() == BatchTaskTypeEnum.SUBCONTRACT_BATCH_TASK) {
            SubcontractorChangeOrder changeOrder = QueryDataHelper.get(
                    SubcontractorChangeOrder.class,
                    Lists.newArrayList(
                            SubcontractorChangeOrderFieldConsts.NETWORK_RESPONSIBLE_PERSON),
                    clockInTask.getChangeOrderId());

            userIds = Optional.ofNullable(changeOrder.getNetworkResponsiblePerson())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .map(Employee::getEmpUIID)
                    .collect(Collectors.toList());
        }

        return CalleeQueryAbility.userId2Callee(userIds);

    }

    private List<String> getThirdClockInUserIds(String productCategory, String responsibleDept,
                                                BoolEnum isGovEnt, ApproveRoleEnum approveRoleEnum) {
        return ApproverConfigAbility.getApprovalPriorityPersons(
                new ApproverConfiguration() {{
                    setApprovalNode(ApprovalTypeEnum.PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE);
                    setRole(approveRoleEnum);
                    setProdLine(ProductUtils.getLine(productCategory));
                    setSales(ResponsibleUtils.getSales(responsibleDept));
                    setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
                    setResponsibleDeptId(responsibleDept);
                    setIsGov(isGovEnt);
                }}, productCategory, 1, null);
    }

    private void create4ZteException(ClockInTask clockInTask) {
        ChangeOrder changeOrder = ChangeOrderAbility.get(
                clockInTask.getChangeOrderId(), Lists.newArrayList(
                        ChangeOrderFieldConsts.RESPONSIBLE_DEPT,
                        ChangeOrderFieldConsts.PRODUCT_CATEGORY,
                        ChangeOrderFieldConsts.ACCN_TYPE,
                        ChangeOrderFieldConsts.IS_GOV_ENT));
        List<ClockInCallPlan> callPlans = Lists.newArrayList();

        // 产品线人员
        {
            List<ClockInCallee> prodLineCallee = Lists.newArrayList();

            // 远程支持人员
            prodLineCallee.addAll(CalleeQueryAbility.getRemoteSupporters(
                    clockInTask.getEntityType().getEntityClass(), clockInTask.getBatchTaskId()));

            // 网络服务处值班总工
            ClockInCallee netSvrDutyChiefEngineer = CalleeQueryAbility.getNetSvrDutyChiefEngineer(
                    clockInTask.getTimeZone().toZonedTime(new Date()),
                    changeOrder.getProductCategory(),
                    changeOrder.getCustomerTypeFlag());
            prodLineCallee.add(netSvrDutyChiefEngineer);

            String prodLineContent = getExceptionContent(CONTENT_EXCEPTION_DUTY_KEY, clockInTask);
            callPlans.addAll(forBatchCreate(
                    clockInTask.getId(), prodLineContent, prodLineCallee));
        }

        // 办事处人员
        {
            List<String> userIds = Lists.newArrayList();

            // 项目TD（审核人配置--代表处人员配置--办事处产品科长/项目TD）
            userIds.addAll(ApproverConfigAbility.getApprovalPriorityPersons(
                    new ApproverConfiguration() {{
                        setApprovalNode(ApprovalTypeEnum.PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE);
                        setRole(ApproveRoleEnum.PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE_PROJECT_TD);
                        setProdLine(ProductUtils.getLine(changeOrder.getProductCategory()));
                        setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
                        setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
                        setResponsibleDeptId(changeOrder.getResponsibleDept());
                        setIsGov(changeOrder.getIsGovEnt());
                    }}, changeOrder.getProductCategory(), 1, null));
            // 项目PD（审核人配置--行政领导配置--三营审核配置--办事处群经理）
            userIds.addAll(ApproverConfigAbility.getApprovalPriorityPersons(
                    new ApproverConfiguration() {{
                        setApprovalNode(ApprovalTypeEnum.ADMINISTRATIVE_LEADER_DIVISION_THREE);
                        setRole(ApproveRoleEnum.GROUP_MANAGER_OF_THE_REPRESENTATIVE_OFFICE);
                        setProdSubCategory(changeOrder.getProductCategory());
                        setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
                        setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
                        setResponsibleDeptId(changeOrder.getResponsibleDept());
                        setIsGov(changeOrder.getIsGovEnt());
                    }}, changeOrder.getProductCategory(), 0, null));
            // 产品科长（审核人配置--行政领导配置--三营审核配置--办事处产品科长）
            userIds.addAll(ApproverConfigAbility.getApprovalPriorityPersons(
                    new ApproverConfiguration() {{
                        setApprovalNode(ApprovalTypeEnum.ADMINISTRATIVE_LEADER_DIVISION_THREE);
                        setRole(ApproveRoleEnum.PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE);
                        setProdSubCategory(changeOrder.getProductCategory());
                        setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
                        setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
                        setResponsibleDeptId(changeOrder.getResponsibleDept());
                        setIsGov(changeOrder.getIsGovEnt());
                    }}, changeOrder.getProductCategory(), 0, null));

            List<ClockInCallee> officeCallee = CalleeQueryAbility.userId2Callee(userIds);
            String officeContent = getExceptionContent(CONTENT_EXCEPTION_OFFICE_KEY, clockInTask);
            callPlans.addAll(forBatchCreate(
                    clockInTask.getId(), officeContent, officeCallee));
        }

        create(callPlans);
    }

    private void create4SubconException(ClockInTask clockInTask) {
        SubcontractorChangeOrder changeOrder = QueryDataHelper.get(
                SubcontractorChangeOrder.class,
                Lists.newArrayList(
                        SubcontractorChangeOrderFieldConsts.ORGANIZATION_ID,
                        SubcontractorChangeOrderFieldConsts.PRODUCT_ID,
                        SubcontractorChangeOrderFieldConsts.CUSTOMER_TYPE_FLAG,
                        SubcontractorChangeOrderFieldConsts.NETWORK_NAME),
                clockInTask.getChangeOrderId());

        // 办事处网络责任人
        NisNetwork network = NisClient.queryNetwork(
                        Lists.newArrayList(changeOrder.getNetworkName())).stream()
                .findAny()
                .orElseThrow(RuntimeException::new);
        ClockInCallee networkResponsiblePerson = CalleeQueryAbility.userId2Callee(network.getResponsiblePerson());
        ClockInCallPlan prevCallPlan = new ClockInCallPlan() {{
            setObjectId(clockInTask.getId());
            setReason(CallReasonEnum.EXCEPTION);
            setCallUtcTime(System.currentTimeMillis() / 1000);
            setCallee(networkResponsiblePerson);
            setContent(getExceptionContent(CONTENT_EXCEPTION_OFFICE_KEY, clockInTask));
            setCallStatus(CallStatusEnum.WAITING);
        }};

        // 办事处值班人员
        ClockInCallee officeDutyPerson = CalleeQueryAbility.getOfficeDutyPerson(
                clockInTask.getTimeZone().toZonedTime(new Date()),
                changeOrder.getResponsibleDept(),
                changeOrder.getProductCategory(),
                changeOrder.getCustomerTypeFlag());
        ClockInCallPlan nextCallPlan = new ClockInCallPlan() {{
            setObjectId(clockInTask.getId());
            setReason(CallReasonEnum.EXCEPTION);
            setCallUtcTime(System.currentTimeMillis() / 1000);
            setCallee(officeDutyPerson);
            setContent(getExceptionContent(CONTENT_EXCEPTION_DUTY_KEY, clockInTask));
            setCallStatus(CallStatusEnum.WAITING);
        }};

        createCallChain(3, prevCallPlan, nextCallPlan);
    }

    private static String getExceptionContent(String key, ClockInTask clockInTask) {
        return MsgUtils.getLangMessage(
                ZH_CN, key,
                clockInTask.getOperationSubject(),
                clockInTask.getOperator().getEmpNameCn() + clockInTask.getOperator().getId(),
                getOperatorPhoneNum(clockInTask));
    }

    private static List<ClockInCallPlan> forCreateRepeatCallPlan(
            ClockInCallPlan firstPlan,
            List<ClockInCallee> thirdClockInCallee,
            String thirdContent) {
        List<ClockInCallPlan> callPlans = Lists.newArrayList();

        callPlans.add(firstPlan);

        for (int i = 1; i < firstPlan.getReason().getMaxCallCount(); ++i) {
            if (i == CommonConstants.INTEGER_TWO) {
                for (ClockInCallee callee : thirdClockInCallee) {
                    ClockInCallPlan clone = firstPlan.clone();
                    clone.setCallee(callee);
                    clone.setContent(thirdContent);
                    clone.setCallUtcTime(firstPlan.getCallUtcTime() + i * 60L * firstPlan.getReason().getIntervalMinutes());
                    callPlans.add(clone);
                }
                continue;
            }
            ClockInCallPlan clone = firstPlan.clone();
            clone.setCallUtcTime(
                    firstPlan.getCallUtcTime() + i * 60L * firstPlan.getReason().getIntervalMinutes());
            callPlans.add(clone);
        }

        return callPlans;
    }

    private static List<ClockInCallPlan> forBatchCreate(
            String objectId,
            String content,
            List<ClockInCallee> callee) {
        return callee.stream()
                .filter(Objects::nonNull)
                .map(c -> new ClockInCallPlan() {{
                    setObjectId(objectId);
                    setReason(CallReasonEnum.EXCEPTION);
                    setCallUtcTime(System.currentTimeMillis() / 1000);
                    setCallee(c);
                    setContent(content);
                    setCallStatus(CallStatusEnum.WAITING);
                }}).collect(Collectors.toList());
    }

    private Date onDutyCallTime(ClockInTask onDutyTask) {
        Date now = new Date();
        double totalDurationSecond = onDutyTask.getTotalOnDutyDuration() * 60 * 60;
        double intervalSecond = totalDurationSecond / onDutyTask.getOnDutyFrequency();
        Calendar stageOnDutyEndTime = Calendar.getInstance();
        List<ClockInRecord> lastRecord = ClockInQueryAbility.getLastClockInRecords(
                onDutyTask.getId(), 1, BoolEnum.N, CLOCK_IN_OPTION);
        // 打测试结束卡（此时数据库还未更新 stageOnDutyEndTime 阶段值守结束时间字段，初始为null）
        // 如果是测试结束卡或者值守过程中的恢复卡
        if (onDutyTask.getStageOnDutyEndTime() == null || (CollectionUtils.isNotEmpty(lastRecord)
                && lastRecord.get(0).getClockInOption() == ClockInOptionEnum.INDICATOR_ERROR_HANDLING)) {
            //【阶段值守结束时间】（计划呼叫时间点） = 当前时间 + 值守时长/值守频次
            stageOnDutyEndTime.setTime(now);
            stageOnDutyEndTime.add(Calendar.SECOND, (int) intervalSecond);
            return stageOnDutyEndTime.getTime();
        }
        // 如果是有效的值守继续卡，那应该更新阶段结束时间
        if (now.after(onDutyTask.getStageOnDutyEndTime())) {
            //【阶段值守结束时间】 = 【阶段值守结束时间】 + 值守时长/值守频次
            stageOnDutyEndTime.setTime(onDutyTask.getStageOnDutyEndTime());
            stageOnDutyEndTime.add(Calendar.SECOND, (int) intervalSecond);
            return stageOnDutyEndTime.getTime();
        }
        return onDutyTask.getStageOnDutyEndTime();
    }

    private NextCallPlanInfo getClockInfo(String clockInTaskId, CallReasonEnum clockedState) {
        NextCallPlanInfo nextCallPlanInfo = new NextCallPlanInfo();
        ClockInTask onDutyTask = null;

        if (clockedState == CallReasonEnum.TEST_END) {
            // 打完测试结束卡，判断是否有值守任务
            ClockInTask operationTask = QueryDataHelper.get(
                    ClockInTask.class, Lists.newArrayList(), clockInTaskId);
            onDutyTask = getKeyTask(
                    operationTask.getBatchTaskId(), ClockInTaskTypeEnum.ON_DUTY, CLOCK_IN_TASK_FIELDS_4_CLOCK_NOTIFY);
            if (onDutyTask == null || !operationTask.getOperatorRoles().contains(OPERATING_SUPERVISOR)) {
                // 没有值守任务，或者测试结束卡不是操作负责人打的，不创建值守的呼叫计划
                return null;
            }

            nextCallPlanInfo.state = onDutyTask.getOnDutyFrequency() > 1
                    ? CallReasonEnum.ON_DUTY_CONTINUE : CallReasonEnum.ON_DUTY_END;
            // 如果是转交单的话触发转交呼叫
            if (onDutyTask.getTransferFrequency() != null && onDutyTask.getTransferFrequency() > 0) {
                String id = onDutyTask.getOperator().getId();
                String operatorPhoneNum = getOperatorPhoneNum(onDutyTask);
                ClockInCallee clockInCallee = new ClockInCallee() {{
                    setUserId(id);
                    setPhoneNum(operatorPhoneNum);
                }};
                // 创建转交呼叫计划（当前时间 + 30min）
                transferCreate(onDutyTask, clockInCallee);
            }

        } else if (clockedState == CallReasonEnum.ON_DUTY_CONTINUE) {
            // 打完值守继续卡，判断剩余值守次数
            onDutyTask = QueryDataHelper.get(
                    ClockInTask.class, Lists.newArrayList(CLOCK_IN_TASK_FIELDS_4_CLOCK_NOTIFY), clockInTaskId);
            int continueCount = getContinueCount(clockInTaskId, onDutyTask);

            nextCallPlanInfo.state = onDutyTask.getOnDutyFrequency() > continueCount + 1
                    ? CallReasonEnum.ON_DUTY_CONTINUE : CallReasonEnum.ON_DUTY_END;
        } else {
            nextCallPlanInfo.state = clockedState.next();
        }

        if (nextCallPlanInfo.state == null) {
            return null;
        }

        nextCallPlanInfo.task = onDutyTask == null
                ? QueryDataHelper.get(
                        ClockInTask.class, Lists.newArrayList(CLOCK_IN_TASK_FIELDS_4_CLOCK_NOTIFY), clockInTaskId)
                : onDutyTask;
        nextCallPlanInfo.phoneNum = getOperatorPhoneNum(nextCallPlanInfo.task);

        return nextCallPlanInfo;
    }

    private static String getOperatorPhoneNum(ClockInTask clockInTask) {
        String userId = clockInTask.getOperator().getId();
        Class<? extends BaseSubEntity> operatorType = clockInTask.getEntityType() == BatchTaskTypeEnum.BATCH_TASK
                ? BatchTaskOperator.class
                : SubcontractorBatchOperator.class;
        BatchTaskOperator operator = QueryDataHelper.queryOne(
                operatorType,
                Lists.newArrayList(OperatorFieldConsts.OPERATOR_PHONE),
                clockInTask.getBatchTaskId(),
                Lists.newArrayList(
                        new Filter(OperatorFieldConsts.OPERATE_PERSON, Comparator.EQ, Lists.newArrayList(userId)),
                        new Filter(OperatorFieldConsts.OPERATOR_PHONE, Comparator.GT, StringUtils.EMPTY)));
        return operator == null || StringUtils.isBlank(operator.getOperatorPhone())
                ? HrHelper.queryPhoneNum(userId)
                : operator.getOperatorPhone();
    }

    private int getContinueCount(String onDutyTaskId, ClockInTask onDutyTask) {
        Date now = new Date();
        List<ClockInRecord> newRecords = operationCache.queryInNew(
                ClockInRecord.class, r -> r.getPid().equals(onDutyTaskId)
                        && r.getClockInOption() == ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE);
        List<ClockInRecord> lastRecord = ClockInQueryAbility.getLastClockInRecords(
                onDutyTaskId, 1, BoolEnum.N, CLOCK_IN_OPTION);
        // 如果是恢复卡
        if (CollectionUtils.isNotEmpty(lastRecord)
                && lastRecord.get(0).getClockInOption() == ClockInOptionEnum.INDICATOR_ERROR_HANDLING) {
            return onDutyTask.getAlreadyOnDutyFrequency();
        }
        // 如果是有效的值守继续卡，应该给已值守次数+1
        if (now.after(onDutyTask.getStageOnDutyEndTime())) {
            return onDutyTask.getAlreadyOnDutyFrequency() + newRecords.size();
        }
        return onDutyTask.getAlreadyOnDutyFrequency();
    }

    private static class NextCallPlanInfo {
        private CallReasonEnum state;

        private ClockInTask task;

        private String phoneNum;
    }
}
