package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EmailGroupFieldConsts {

    /** 抄送类型 */
    public static final String CC_TYPE = "cc_type";

    /** 产品类型 */
    public static final String PRODUCT = "product";

    /** 组织 */
    public static final String ORGANIZATION = "organization";

    /** 是否政企 */
    public static final String GOVERNMENT_FLAG = "government_flag";

    /** 抄送人员 */
    public static final String CC_PERSON = "cc_person";

    /** 备注 */
    public static final String REMARK = "remark";

    /** 是否启用 */
    public static final String IN_SERVICE_FLAG = "in_service_flag";
}
