package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/11/20
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class StackTraceUtils {
    public static String getStackTrace(String... filters) {
        StringBuilder stackTrace = new StringBuilder();
        StackTraceElement[] trace = Thread.currentThread().getStackTrace();
        for (int i = 2; i < trace.length; i++) {
            StackTraceElement element = trace[i];
            if (element.getClassName().equals(StackTraceUtils.class.getName())) {
                continue;
            }

            String stackTraceLine = String.format(
                    "    at %s.%s(%s:%s)%n",
                    element.getClassName(),
                    element.getMethodName(),
                    element.getFileName(),
                    element.getLineNumber());
            if (filters.length == 0 || Arrays.stream(filters).anyMatch(stackTraceLine::contains)) {
                stackTrace.append(stackTraceLine);
            }
        }
        return stackTrace.toString();
    }
}
