package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/25/24 下午7:02
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ChangeStatusEnum implements SingletonTextValuePairsProvider {
    /**
     * 成功
     */
    SUCCESS("成功", "Success"),

    /**
     * 失败
     */
    FAIL("失败", "Fail"),

    ;
    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }

}