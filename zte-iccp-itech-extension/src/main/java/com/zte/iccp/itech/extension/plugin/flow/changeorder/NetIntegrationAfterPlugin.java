package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.OpAssocProdAbility;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdNetServDeptConfirm;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdNetServIntApproval;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;


/**
 * 网服部一体化关联产品审核/研发一体化关联产品审核人并行审核 流程提交后置赋值
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/14
 */
public class NetIntegrationAfterPlugin extends BaseFlowOperationPlugin {

    @Override
    public void afterOperate(ExecuteEvent executeEvent) {
        JSONObject args = executeEvent.getArgs();
        String taskId = (String) args.get(ApprovalConstants.TASK_ID);
        OpAssocProdAbility.integrationAfterOperate(
                getPkId(), taskId, OpAssocProdNetServIntApproval.class, OpAssocProdNetServDeptConfirm.class);
    }
}
