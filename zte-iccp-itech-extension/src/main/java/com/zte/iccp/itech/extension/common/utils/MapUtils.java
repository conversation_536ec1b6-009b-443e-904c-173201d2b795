package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MapUtils {
    public static <K, V> Map<K, V> unmodifiableMap(Object... params) {
        return Collections.unmodifiableMap(newHashMap(params));
    }

    public static <K, V> Map<K, V> newHashMap(Object... params) {
        return fill(new HashMap<>(params.length / 2), params);
    }

    public static <K, V> Map<K, V> newLinkedHashMap(Object... params) {
        return fill(new LinkedHashMap<>(params.length / 2), params);
    }

    private static <K, V> Map<K, V> fill(Map<K, V> map, Object... params) {
        if (params.length % 2 != 0) {
            throw new IllegalArgumentException();
        }

        for (int i = 0; i < params.length; i += 2) {
            //noinspection unchecked
            map.put((K) params[i], (V) params[i + 1]);
        }

        return map;
    }

    public static Map<String, String> subMap(Map<String, String> map, List<String> keys) {
        Map<String, String> subMap = new HashMap<>(map);
        subMap.keySet().retainAll(keys);
        return subMap;
    }
}