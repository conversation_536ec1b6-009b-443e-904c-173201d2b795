package com.zte.iccp.itech.extension.plugin.form.faultmanagement.propertychanged;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.model.FaultManagementDetails;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.PropertyChangedBaseFormPlugin;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ZERO;
import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.ComponentCids.COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID;
import static com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids.FIELD_FAULT_REVIEW_CID;

public class FaultReviewTaskButtonPlugin implements LoadDataBaseFormPlugin, PropertyChangedBaseFormPlugin {

    @Override
    public void operate(ValueChangedEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();
        setFaultReviewTaskButtonProps(formView, dataModel);
    }

    @Override
    public void loadData(LoadDataEventArgs args, FaultManagementDetails faultManagementDetails) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = args.getModel();
        setFaultReviewTaskButtonProps(formView, dataModel);
    }

    private void setFaultReviewTaskButtonProps(IFormView formView, IDataModel dataModel) {
        Object faultReviewObj = dataModel.getValue(FIELD_FAULT_REVIEW_CID);
        List<TextValuePair> isFaultReview = JsonUtils.parseArray(faultReviewObj, TextValuePair.class);
        if (CollectionUtils.isEmpty(isFaultReview)) {
            return;
        }
        String isField = isFaultReview.get(INTEGER_ZERO).getValue();
        if (CommonConstants.Y.equals(isField)) {
            String cscCode = PropertyValueConvertUtil.getString(dataModel.getValue(FieldCids.FIELD_TASK_CODE_CID));
            // 选了“是”
            List<FaultManagementAssignment> assignments =
                    AssignmentAbility.queryFaultManagementAssignment(Lists.newArrayList(cscCode));
            if (CollectionUtils.isEmpty(assignments)) {
                // 设置创建故障复盘任务按钮为显示
                formView.getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                        new PageStatusAttributeBuilder().normal().build());
                return;
            }

            FaultManagementAssignment assignment = assignments.get(CommonConstants.INTEGER_ZERO);

            List<TechnologyManagementAssignment> faultReviewAssignmentList =
                    AssignmentAbility.queryFaultReviewAssignment(assignment.getId());
            if (CollectionUtils.isEmpty(faultReviewAssignmentList)) {
                // 设置创建故障复盘任务按钮为显示
                formView.getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                        new PageStatusAttributeBuilder().normal().build());
                return;
            }
            // 审批中，执行中和已关闭中的任务
            List<TechnologyManagementAssignment> approveTask =
                    faultReviewAssignmentList.stream()
                            .filter(v -> AssignmentStatusEnum.APPROVE.getValue().equals(v.getAssignmentStatus()) ||
                                    AssignmentStatusEnum.EXECUTE.getValue().equals(v.getAssignmentStatus()) ||
                                    AssignmentStatusEnum.CLOSE.getValue().equals(v.getAssignmentStatus()) ||
                                    AssignmentStatusEnum.START.getValue().equals(v.getAssignmentStatus()))
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(approveTask)) {
                // 设置创建故障复盘任务按钮为显示
                formView.getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                        new PageStatusAttributeBuilder().normal().build());
            } else {
                // 设置创建故障复盘任务按钮为隐藏
                formView.getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                        new PageStatusAttributeBuilder().hidden().build());
            }
        } else {
            // 设置创建故障复盘任务按钮为隐藏
            formView.getClientViewProxy().setControlState(COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID,
                    new PageStatusAttributeBuilder().hidden().build());
        }
    }
}
