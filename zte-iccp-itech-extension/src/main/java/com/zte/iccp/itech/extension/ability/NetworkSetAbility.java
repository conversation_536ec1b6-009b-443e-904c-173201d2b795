package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.model.CustomizedNetworkSet;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.CustomizedNetworkSetConsts.*;

/**
 * 网络集ability
 *
 * <AUTHOR> 10335201
 * @date 2024-07-03 上午9:11
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NetworkSetAbility {

    /**
     * 查询当前用户创建的自定义网络集
     * @param
     * @return: java.util.List<com.zte.iccp.itech.extension.domain.model.CustomizedNetworkSet> 自定义网络集
     * @author: 朱小安 10335201
     * @date: 2024/7/3 上午10:01
     */
    public static List<CustomizedNetworkSet> get(){
        List<String> fields = Lists.newArrayList(ID,NETWORK_SET_NAME,NETWORK,DESCRIPTION,CREATE_BY,CREATE_TIME,LAST_MODIFIED_BY,LAST_MODIFIED_TIME);
        List<IFilter> filters = Lists.newArrayList();
        filters.add(new Filter(CREATE_BY, Comparator.EQ, RequestHeaderUtils.getEmpNo()));
        return QueryDataHelper.query(CustomizedNetworkSet.class, fields, filters);
    }

    /**
     * 根据自定义网络集id查询所有网络
     * @param id 自定义网络集id
     * @return: java.util.List<com.zte.iccp.itech.extension.domain.model.base.TextValuePair> 自定义网络集下的网络
     * @author: 朱小安 10335201
     * @date: 2024/7/3 上午10:34
     */
    public static List<TextValuePair> getNetworkById(String id){
        List<String> fields = Lists.newArrayList(NETWORK);
        List<IFilter> filters = Lists.newArrayList();
        filters.add(new Filter(ID, Comparator.EQ, id));
        CustomizedNetworkSet customizedNetworkSet = QueryDataHelper.queryOne(CustomizedNetworkSet.class, fields, filters);
        return customizedNetworkSet!=null?customizedNetworkSet.getNetwork():Lists.newArrayList();
    }
}
