package com.zte.iccp.itech.extension.common.helper;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.LookupValueQueryDto;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.LookupValueVO;
import com.zte.iccp.itech.extension.spi.client.LookupValueClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.query.LookupValueQuery;
import com.zte.itp.msa.core.model.PageRows;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/18
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class LookupValueHelper {

    private static final String ACCESS_SYSTEM = "fastcode.iss.system.code";

    private static final Map<String,String> LANG_ID_MAP = MapUtils.newHashMap(ZH_CN,"ZHS",EN_US,"US");

    public static List<LookupValue> getLookupValues(String lookupType) {
        return getLookupValues(lookupType, (List<String>) null);
    }

    public static List<LookupValue> getLookupValues(String langId, String lookupType) {
        return getLookupValues(langId, lookupType, null);
    }

    public static LookupValue getLookupValue(String lookupType, String lookupCode) {
        List<LookupValue> lookupValues = getLookupValues(lookupType, Lists.newArrayList(lookupCode));
        return CollectionUtils.isEmpty(lookupValues) ? null : lookupValues.get(0);
    }

    public static LookupValue getLookupValue(String langId, String lookupType, String lookupCode) {
        List<LookupValue> lookupValues = getLookupValues(langId, lookupType, Lists.newArrayList(lookupCode));
        return CollectionUtils.isEmpty(lookupValues) ? null : lookupValues.get(0);
    }

    public static List<LookupValue> getLookupValues(String lookupType, List<String> lookupCodes) {
        return getLookupValues(ContextHelper.getLangId(), lookupType, lookupCodes);
    }

    public static List<LookupValue> getLookupValues(String langId, String lookupType, List<String> lookupCodes) {
        if (StringUtils.isBlank(lookupType)) {
            return Collections.emptyList();
        }

        // 构造LookupValue查询条件
        LookupValueQuery lookupValueQueryCN = LookupValueQuery.builder()
                .enableFlag(ENABLED_FLAG)
                .accessSystem(ConfigHelper.getRaw(ACCESS_SYSTEM))
                .lookupTypeList(Lists.newArrayList(lookupType))
                .lookupCodeList(lookupCodes)
                // 查中文
                .lang(LANG_ID_MAP.get(ZH_CN))
                .tenantId(ContextHelper.getTenantId())
                .build();

        LookupValueQuery lookupValueQueryEN = LookupValueQuery.builder()
                .enableFlag(ENABLED_FLAG)
                .accessSystem(ConfigHelper.getRaw(ACCESS_SYSTEM))
                .lookupTypeList(Lists.newArrayList(lookupType))
                .lookupCodeList(lookupCodes)
                // 查英文
                .lang(LANG_ID_MAP.get(EN_US))
                .tenantId(ContextHelper.getTenantId())
                .build();

        List<LookupValue> lookupValueCNpageRows = LookupValueClient.queryLookupValue(lookupValueQueryCN).getRows();
        if (CollectionUtils.isEmpty(lookupValueCNpageRows)) {
            return Collections.emptyList();
        }
        Map<String, LookupValue> lookupValueCNMap = lookupValueCNpageRows.stream()
                .collect(Collectors.toMap(LookupValue::getLookupCode, Function.identity()));

        List<LookupValue> lookupValueENpageRows = LookupValueClient.queryLookupValue(lookupValueQueryEN).getRows();
        if (lookupValueENpageRows == null) {
            lookupValueENpageRows = lookupValueCNpageRows;
        }
        Map<String, LookupValue> lookupValueENMap = lookupValueENpageRows.stream()
                .collect(Collectors.toMap(LookupValue::getLookupCode, Function.identity()));

        Map<String, LookupValue> lookupValueMap = ZH_CN.equals(langId) ? lookupValueCNMap : lookupValueENMap;
        List<LookupValue> lookupValues = new ArrayList<>();
        for (String key : lookupValueMap.keySet()) {
            LookupValue lookupValue = lookupValueMap.get(key);
            if (lookupValueCNMap.containsKey(key)) {
                String meaningCn = lookupValueCNMap.get(key).getMeaning();
                lookupValue.setMeaningCn(meaningCn);
            }
            if (lookupValueENMap.containsKey(key)) {
                String meaningEn = lookupValueENMap.get(key).getMeaning();
                lookupValue.setMeaningEn(meaningEn);
            }
            lookupValues.add(lookupValue);
        }
        return lookupValues;
    }

    public static List<LookupValue> getLookupValuesZhAndUs(String lookupType, List<String> lookupCodes) {
        if (StringUtils.isBlank(lookupType)) {
            return Collections.emptyList();
        }

        // 构造LookupValue查询条件
        LookupValueQuery lookupValueQueryCN = LookupValueQuery.builder()
                .enableFlag(ENABLED_FLAG)
                .accessSystem(ConfigHelper.getRaw(ACCESS_SYSTEM))
                .lookupTypeList(Lists.newArrayList(lookupType))
                // 查中文
                .lang(LANG_ID_MAP.get(ZH_CN))
                .tenantId(ContextHelper.getTenantId())
                .build();

        LookupValueQuery lookupValueQueryEN = LookupValueQuery.builder()
                .enableFlag(ENABLED_FLAG)
                .accessSystem(ConfigHelper.getRaw(ACCESS_SYSTEM))
                .lookupTypeList(Lists.newArrayList(lookupType))
                // 查英文
                .lang(LANG_ID_MAP.get(EN_US))
                .tenantId(ContextHelper.getTenantId())
                .build();
        if(!CollectionUtils.isEmpty(lookupCodes)){
                    lookupValueQueryCN.setLookupCodeList(lookupCodes);
            lookupValueQueryEN.setLookupCodeList(lookupCodes);
        }

        List<LookupValue> lookupValueCNpageRows = LookupValueClient.queryLookupValue(lookupValueQueryCN).getRows();
        if (CollectionUtils.isEmpty(lookupValueCNpageRows)) {
            return Collections.emptyList();
        }
        Map<String, LookupValue> lookupValueCNMap = lookupValueCNpageRows.stream()
                .collect(Collectors.toMap(LookupValue::getLookupCode, Function.identity()));

        List<LookupValue> lookupValueENpageRows = LookupValueClient.queryLookupValue(lookupValueQueryEN).getRows();
        if (lookupValueENpageRows == null) {
            lookupValueENpageRows = lookupValueCNpageRows;
        }
        Map<String, LookupValue> lookupValueENMap = lookupValueENpageRows.stream()
                .collect(Collectors.toMap(LookupValue::getLookupCode, Function.identity()));

        List<LookupValue> lookupValues = new ArrayList<>();
        for (String key : lookupValueCNMap.keySet()) {
            LookupValue lookupValue = lookupValueCNMap.get(key);
            if (lookupValueCNMap.containsKey(key)) {
                String meaningCn = lookupValueCNMap.get(key).getMeaning();
                lookupValue.setMeaningCn(meaningCn);
                lookupValue.setMeaningEn(lookupValueENMap.get(key).getMeaning());
            }
            lookupValues.add(lookupValue);
        }
        return lookupValues;
    }

    public static List<LookupValueVO> getLookupValues(LookupValueQueryDto lookupValueQueryDto) {
        // 查询快码（包含中英文，不区分语言环境）
        List<LookupValue> lookupValues = getLookupValuesZhAndUs(lookupValueQueryDto.getLookupType(), lookupValueQueryDto.getLookupCodes());
        if (CollectionUtils.isEmpty(lookupValues)) {
            return new ArrayList<>();
        }

        // 组装返回结果
        List<LookupValueVO> lookupValueVOList = Lists.newArrayList();
        lookupValues.forEach(lookupValue -> {
            LookupValueVO lookupValueVO = new LookupValueVO();
            lookupValueVO.setCode(lookupValue.getLookupCode());
            lookupValueVO.setNameZh(lookupValue.getMeaningCn());
            lookupValueVO.setNameEn(lookupValue.getMeaningEn());

            lookupValueVOList.add(lookupValueVO);
        });

        return lookupValueVOList;
    }

    public static List<LookupValue> getLookupValuesByLangId(String langId, String lookupType, List<String> lookupCodes) {
        if (StringUtils.isBlank(lookupType)) {
            return Collections.emptyList();
        }
        // 构造LookupValue查询条件
        LookupValueQuery lookupValueQuery = LookupValueQuery.builder()
                .enableFlag(ENABLED_FLAG)
                .accessSystem(ConfigHelper.getRaw(ACCESS_SYSTEM))
                .lookupTypeList(Lists.newArrayList(lookupType))
                .lookupCodeList(lookupCodes)
                // 查中文
                .lang(LANG_ID_MAP.get(langId))
                .tenantId(ContextHelper.getTenantId())
                .build();

        PageRows<LookupValue> lookupValuePageRows = LookupValueClient.queryLookupValue(lookupValueQuery);
        if (Objects.isNull(lookupValuePageRows) || Objects.isNull(lookupValuePageRows.getRows())) {
            return Collections.emptyList();
        }

        return lookupValuePageRows.getRows();
    }

    public static String getModelLookupCode(Object object) {
        if (object == null) {
            return null;
        }

        return ((LookupValue)JsonUtils.parseObject(object, LookupValue.class)).getLookupCode();
    }
}