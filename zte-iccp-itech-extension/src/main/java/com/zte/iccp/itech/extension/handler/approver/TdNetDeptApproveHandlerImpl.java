package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @description: 2.4.3技术交付部/网络处审核人 or 2.4.6 技术交付部/网络处审核远程方案  or 2.4.8 网络服务部审核 读取页面字段
 * @date 2024/4/29 2:55
 * zte-iccp-itech-netchange
 */
public class TdNetDeptApproveHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    /**
     * todo 暂时未修改
     * 代表处属于国内代表处（属于国内营销或工程服务经营部-国内部）：优先级如下
     * 【产品】（优先级：产品小类>产品大类>产品线）+【营销】=国内政企+【运营商】+【操作类型】+【是否政企】=否，获取对应记录【审核人】【审核组】数据并且作为当前处理人；
     * 【产品小类】+【组织】（优先级：代表处>片区>营销）+【运营商】+【是否政企】=否，其他条件为空，获取对应记录【审核人】【审核组】数据并且作为当前处理人；
     * 【产品线】+操作类型，其他条件为空，获取对应记录【审核人】【审核组】数据并且作为当前处理人；
     * 【产品小类】+【营销】=国内营销+【是否政企】，其他条件为空，获取对应记录【审核人】【审核组】数据并且作为当前处理人；
     * 代表处属于国际（属于工程服务经营部但不属于工程服务国内部）
     * 【产品线】+操作类型，其他条件为空，获取对应记录【审核人】【审核组】数据并且作为当前处理人；
     * 【产品小类】+【组织】（优先级：代表处>片区>营销）（【是否政企】取值不考虑），其他条件为空，获取对应记录【审核人】【审核组】数据并且作为当前处理人；
     * 若上述点查表发现未配置，则支持用户手动选人+组：组最大支持50个人；
     *
     * @param changeOrder
     */
    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        // 代表处
        String responsibleDept = changeOrder.getResponsibleDept();
        ApproverConfiguration queryParam = new ApproverConfiguration();
        queryParam.setApprovalNode(ApprovalTypeEnum.TECHNOLOGY_DELIVERY_DEPT_NETWORK_OFFICE);
        queryParam.setProdTeam(ProductUtils.getTeam(changeOrder.getProductCategory()));
        queryParam.setProdLine(ProductUtils.getLine(changeOrder.getProductCategory()));
        List<String> persons = new ArrayList<>();
        // 1）代表处属于国内营销
        if (ConfigHelper.get(CommonConstants.DOMESTIC_SALES_ORG_CODE_PATH).equals(ResponsibleUtils.getSales(responsibleDept))) {
            // 产品】（优先级：产品小类>产品大类>产品线）+【营销单位】=国内营销+【运营商】（对应申请单的客户标识）+【操作类型】+【是否政企】=否的精确匹配：取人+组，并且作为当前处理人
            queryParam.setSales(ConfigHelper.get(CommonConstants.DOMESTIC_SALES_ORG_CODE_PATH));
            queryParam.setOperator(OperatorEnum.getCnnValue(changeOrder.getCustomerTypeFlag()));
            queryParam.setIsGov(BoolEnum.N);
            queryParam.setOperationType(Arrays.asList(changeOrder.getOperationType()));
            persons = ApproverConfigAbility.getApprovalPriorityPersons(queryParam, changeOrder.getProductCategory(), 0, null);
            if (CollectionUtils.isEmpty(persons)) {
                queryParam.setOperationType(null);
                persons = ApproverConfigAbility.getApprovalPriorityPersons(queryParam, changeOrder.getProductCategory(), 0, null);
            }
            if (CollectionUtils.isEmpty(persons)) {
                queryParam.setOperator(null);
                queryParam.setProdSubCategory(changeOrder.getProductCategory());
                persons = ApproverConfigAbility.getApprovalPersons(queryParam, null);
            }
        }
        //2）代表处属于工程服务经营部-工程服务国内部-网络服务部：根据【产品】（优先级：产品小类>产品大类>产品线）+【代表处】+【运营商】（
        // 申请单客户标识不等于中国电信、中国移动、中国联通，则匹配空值）（对应申请单的客户标识），其他条件为空的精确匹配：取人+组，并且作为当前处理人
        queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));
        queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
        if (ConfigHelper.get(ENGINEERING_SERVICE_DEPT_DOMESTIC_NETWORK_ORG_CODE_PATH).equals(responsibleDept)) {
            queryParam.setResponsibleDeptId(responsibleDept);
            queryParam.setOperator(OperatorEnum.getCnnValue(changeOrder.getCustomerTypeFlag()));
            persons = ApproverConfigAbility.getApprovalPriorityPersons(queryParam, changeOrder.getProductCategory(), 0, null);
        }
        //3）代表处属于工程服务经营部但不属于工程服务国内部：根据【产品小类】+【片区】，其他条件为空的精确匹配：取人+组，并且作为当前处理人
        if (ConfigHelper.get(ENGINEERING_SERVICE_DEPT_ORG_CODE_PATH).equals(ResponsibleUtils.getRegion(responsibleDept)) &&
                !ConfigHelper.get(ENGINEERING_SERVICE_DEPT_DOMESTIC_ORG_CODE_PATH).equals(ResponsibleUtils.getRegion(responsibleDept))) {
            queryParam.setProdMainCategory(ProductUtils.getMain(changeOrder.getProductCategory()));
            queryParam.setProdSubCategory(changeOrder.getProductCategory());
            persons = ApproverConfigAbility.getApprovalPersons(queryParam, null);
        }
        return persons;
    }
}
