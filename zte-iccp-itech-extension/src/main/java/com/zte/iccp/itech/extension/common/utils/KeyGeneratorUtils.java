package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/23 上午9:29
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class KeyGeneratorUtils {
    private static volatile IdWorkerUtils idWorker;

    public static long generateUUID() {
        if (idWorker == null) {
            synchronized(KeyGeneratorUtils.class) {
                if (idWorker == null) {
                    init(null);
                }
            }
        }

        return idWorker.nextId();
    }

    public static void init(Long serverNode) {
        idWorker = new IdWorkerUtils(serverNode);
    }
}
