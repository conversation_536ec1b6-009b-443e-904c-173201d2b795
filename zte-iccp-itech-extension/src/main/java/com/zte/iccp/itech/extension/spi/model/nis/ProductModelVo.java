package com.zte.iccp.itech.extension.spi.model.nis;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 添加操作对象 子表单
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/7/30
 */
@Setter
@Getter
public class ProductModelVo {

    /** 客户网元id */
    @JsonProperty("custom_omo1uevd")
    private String customerNeId;

    /** 客户网元名称 */
    @JsonProperty("custom_9qbclozy")
    private String customerNeName;

    /** 网元别名 */
    @JsonProperty("custom_7lknmgmi")
    private String neAlias;

    /** 产品经营团队 */
    @JsonProperty("custom_jkqgh0gw")
    private String prodOperationTeam;

    /** 产品线 */
    @JsonProperty("custom_g43l2bqn")
    private String prodLine;

    /** 产品大类 */
    @JsonProperty("custom_mv56ip9e")
    private String prodMainCategory;

    /** 产品小类 */
    @JsonProperty("custom_52s9sj5s")
    private String prodSubCategory;

    /** 产品型号id */
    @JsonProperty("custom_5rw5jh2o")
    private String prodModelId;

    /** 产品型号 */
    @JsonProperty("custom_5fwukbv1")
    private String prodModel;

    /** 数据中心 */
    @JsonProperty("custom_away8ekq")
    private String idcName;

    /** 数量 */
    @JsonProperty("custom_ccsspd88")
    private String triCount;
}
