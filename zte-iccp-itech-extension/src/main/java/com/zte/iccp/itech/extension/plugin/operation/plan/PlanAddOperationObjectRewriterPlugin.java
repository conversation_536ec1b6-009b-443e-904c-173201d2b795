package com.zte.iccp.itech.extension.plugin.operation.plan;

import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.iccp.itech.extension.spi.model.nis.ProductModelVo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.FIELD_OPTIONS;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.ROW;

/**
 * 添加操作对象 - 确认
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/7/31
 */
public class PlanAddOperationObjectRewriterPlugin extends BaseOperationPlugin {

    /** 添加操作对象 子表单已选中行 */
    private static final String SUB_FORM_SELECTED_ROWS = "networkProductList";

    @Override
    public void beforeExecute(ExecuteEvent executeEvent) {
        IFormView formView = getView();
        Map<String, Object> customerParameterMap = formView.getFormShowParameter().getCustomParameters();
        String productDeviceValue = TextValuePairHelper.getValue(getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_PRODUCT_DEVICE_CID));
        // 从网络已注册资产中选择
        if (STR_ONE.equals(productDeviceValue)) {
            // 获取添加操作对象子表单已选择数据
            Object networkProductList = customerParameterMap.get(SUB_FORM_SELECTED_ROWS);
            if (networkProductList != null) {
                List<ProductModelVo> productModelVoList = JsonUtils.parseArray(networkProductList, ProductModelVo.class);
                processProductModels(productModelVoList, customerParameterMap);
            }
        } else {
            // 获取产品型号
            List<TextValuePair> productModelList = JsonUtils.parseArray(
                    getModel().getValue(CidConstants.COMPONENT_PRODUCT_TREE_CID), TextValuePair.class);
            processProductModels(productModelList, customerParameterMap);
        }
    }

    /**
     * 子表单已选中行或产品型号选中数据处理
     *
     * @param productModels 子表单已选中行或产品型号多选
     * @param customerParameterMap customerParameterMap
     */
    private void processProductModels(List<?> productModels, Map<String, Object> customerParameterMap) {
        // 产品型号id和产品分类id的映射map
        Map<String, String> prodModelIdToCategoryIdMap = MapUtils.newHashMap();
        // 产品型号id和产品型号服务对象的映射map
        Map<String, String> prodModelIdToServiceModelMap = MapUtils.newHashMap();
        // 产品小类ids
        Set<String> productIds = new HashSet<>();
        for (Object model : productModels) {
            String prodModelId = extractProdModelId(model);
            String[] parts = prodModelId.split("/");
            List<String> filteredParts = Arrays.stream(parts).limit(4).collect(Collectors.toList());
            String productId = String.join("/", filteredParts);

            productIds.add(productId);
            prodModelIdToCategoryIdMap.putIfAbsent(prodModelId, productId);
            prodModelIdToServiceModelMap.putIfAbsent(prodModelId, parts[parts.length - 1]);
        }

        // 查询产品信息
        Map<String, BasicProductInfo> productIdToProductInfoMap = NisAbility.queryProductInfoMap(new ArrayList<>(productIds));

        IFormView parentView = getView().getParentView();
        IDataModel parentDataModel = parentView.getDataModel();
        // 操作计划单 - 操作对象子表单cid
        IEntryTableSupport table = (IEntryTableSupport) parentView.getControl(CidConstants.COMPONENT_OPERATION_OBJECT_ORDER_CID);

        // 操作计划单 - 操作对象尾部行号
        Integer rowStart = (Integer) customerParameterMap.getOrDefault(ROW, INTEGER_ZERO);
        parentDataModel.appendEntryRows(CidConstants.OPERATION_OBJECT_TABLE_PROPERTY_KEY, productModels.size());
        // 处理每个产品模型的数据填充
        for (int i = 0; i < productModels.size(); i++) {
            Object model = productModels.get(i);
            int rowIndex = i + rowStart;
            String prodModelId = extractProdModelId(model);

            String productId = prodModelIdToCategoryIdMap.get(prodModelId);
            BasicProductInfo basicProductInfo = productIdToProductInfoMap.get(productId);
            // 操作计划单 - 操作对象通用属性赋值
            setOperationObjectCommonProperties(parentDataModel, customerParameterMap, rowIndex);
            // 添加操作对象中子表单额外赋值
            processNetworkProductSubTab(model, parentDataModel, rowIndex);
            // 其他字段赋值
            parentDataModel.setValue(CidConstants.FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY,
                    prodModelIdToServiceModelMap.get(prodModelId), rowIndex);

            OptionsBuilder optionsBuilder = new OptionsBuilder().addOption(
                    new Option(productId + "/", new Text(basicProductInfo.getNamePathZh(), basicProductInfo.getNamePathEn()))
            );
            table.getTableState().setCellAttribute(
                    rowIndex, CidConstants.OPERATION_OBJECT_PROD_SUB_CATEGORY_CID, FIELD_OPTIONS, optionsBuilder.build());
            parentDataModel.setValue(CidConstants.FIELD_OPERATION_OBJECT_PRODUCT_CLASS_PROPERTY_KEY,
                    optionsBuilder.build(), rowIndex);
        }

        getView().getClientViewProxy().sendParentViewCmd(parentView.getClientViewProxy());
        //行数据加载到缓存
        parentDataModel.commit();
    }

    /**
     * 添加操作对象 子表单特殊逻辑
     *
     * @param model model
     * @param parentDataModel parentDataModel
     * @param rowIndex rowIndex
     */
    private void processNetworkProductSubTab(Object model, IDataModel parentDataModel, int rowIndex) {
        if (model instanceof ProductModelVo) {
            ProductModelVo vo = (ProductModelVo) model;
            parentDataModel.setValue(CidConstants.FIELD_OPERATION_OBJECT_OFFICE_NAME_PROPERTY_KEY, vo.getNeAlias(), rowIndex);
            parentDataModel.setValue(CidConstants.ENTITY_OPERATION_OBJECT_COUNT_CID, Integer.valueOf(vo.getTriCount()), rowIndex);
            parentDataModel.setValue(CidConstants.ENTITY_OPERATION_OBJECT_DATA_CENTER_CID, vo.getIdcName(), rowIndex);
        }
    }

    /**
     * 子表单数据中的产品型号或页面已选产品型号处理类
     *
     * @param model 不同数据模型
     * @return 产品型号
     */
    private String extractProdModelId(Object model) {
        if (model instanceof ProductModelVo) {
            // 已确认子表单捞到的数据中产品型号不会为空
            return ((ProductModelVo) model).getProdModelId();
        } else if (model instanceof TextValuePair) {
            return ((TextValuePair) model).getValue();
        }
        return null;
    }

    /**
     * 操作计划单 - 操作对象通用属性赋值
     *
     * @param parentDataModel parentDataModel
     * @param customerParameterMap customerParameterMap
     * @param rowIndex rowIndex
     */
    private void setOperationObjectCommonProperties(IDataModel parentDataModel,
                                                    Map<String, Object> customerParameterMap,
                                                    int rowIndex) {

        //（1）操作批次
        Object batchNo = getModel().getValue(CidConstants.BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY);
        String batchNoValue = TextValuePairHelper.getValue(batchNo);

        List<BatchSummary> batchSummaryList = JsonUtils.parseArray(
                customerParameterMap.get(CidConstants.COMPONENT_OPERATION_OBJECT_BATCH_SUMMARY_CID), BatchSummary.class);
        BatchSummary batchSummary = batchSummaryList.stream()
                .filter(item -> batchNoValue.equals(item.getBatchNo().get(0).getValue()))
                .findFirst().get();

        // 批次概要
        parentDataModel.setValue(CidConstants.FIELD_OPERATION_OBJECT_BATCH_NO_PROPERTY_KEY, batchNo, rowIndex);
        // 计划操作开始时间
        parentDataModel.setValue(CidConstants.PLAN_OPERATION_START_TIME_OBJ, batchSummary.getPlanOperationStartTime(), rowIndex);
        // 计划操作结束时间
        parentDataModel.setValue(CidConstants.PLAN_OPERATION_END_TIME_OBJ, batchSummary.getPlanOperationEndTime(), rowIndex);
        // 客户网络名称（服务对象）
        parentDataModel.setValue(CidConstants.FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY,
                getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_NETWORK_ID_CID), rowIndex);
        // 国家
        parentDataModel.setValue(CidConstants.FIELD_COUNTRY_CID, getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_COUNTRY_CID), rowIndex);
        // 省/州
        parentDataModel.setValue(CidConstants.FIELD_PROVINCE_CID, getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_PROVINCE_CID), rowIndex);
        // 地市
        parentDataModel.setValue(CidConstants.FIELD_AREA_CID, getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_AREA_CID), rowIndex);
        // 客户名称（服务对象）
        parentDataModel.setValue(CidConstants.FIELD_CUSTOMER_ID_CID, getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_CUSTOMER_ID_CID), rowIndex);
        // 客户标识
        parentDataModel.setValue(CidConstants.ACCN_TYPE_CID, getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_ACCN_TYPE_CID), rowIndex);
    }

    @Override
    public boolean beforeExecuteValidate(ExecuteEvent executeEvent) {
        // 1、操作批次，客户网络名称是否必填
        String batchNo = TextValuePairHelper.getValue(getModel().getValue(CidConstants.BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY));
        if (StringUtils.isEmpty(batchNo)) {
            getView().showMessage(MessageConsts.CHECK_BATCH_NO_EMPTY_ERROR, MsgType.ERROR);
            return false;
        }

        Object networkId = getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_NETWORK_ID_CID);
        if (ObjectUtils.isEmpty(networkId)) {
            getView().showMessage(MessageConsts.CHECK_CUSTOMER_NETWORK_NAME_EMPTY_ERROR, MsgType.ERROR);
            return false;
        }

        // 2、产品设备为网络资产中未找到我需要的设备时产品型号必填
        String productDeviceValue = TextValuePairHelper.getValue(getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_PRODUCT_DEVICE_CID));
        List<TextValuePair> productModelList = JsonUtils.parseArray(
                getModel().getValue(CidConstants.COMPONENT_PRODUCT_TREE_CID), TextValuePair.class);
        if (STR_TWO.equals(productDeviceValue)
                && CollectionUtils.isEmpty(productModelList)) {
            getView().showMessage(MessageConsts.CHECK_PRODUCT_MODEL_ID_EMPTY_ERROR, MsgType.ERROR);
            return false;
        }
        return true;
    }
}
