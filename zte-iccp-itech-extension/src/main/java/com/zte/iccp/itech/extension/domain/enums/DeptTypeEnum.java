package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.ENGINEERING_SERVICE_OPERATION;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.ENGINEERING_SERVICE_THREE_PARTS_OPERATION;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/28
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum DeptTypeEnum {
    /** 国内 */
    INNER,
    /** 国际 */
    INTER,
    /**国内，但除了网络服务处*/
    INNER_WITHOUT_NET_SERVICE,
    /**网络服务处*/
    NET_SERVICE,

    /**其他*/
    OTHER
    ;

    /**
     * 根据代表处编码获取代表处类型（国内/国际）
     * @param deptId 代表处IdPath
     * @return 代表处类型枚举
     */
    public static DeptTypeEnum getDeptTypeById(String deptId) {
        if (!StringUtils.hasText(deptId)) {
            return null;
        }

        if (deptId.startsWith(ENGINEERING_SERVICE_OPERATION)
                && !deptId.startsWith(ENGINEERING_SERVICE_THREE_PARTS_OPERATION)) {
            return INTER;
        }

        return INNER;
    }
}
