package com.zte.iccp.itech.extension.openapi.mobile.approval.permission.vo;

import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo.ApprovalProgressVO;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo.AttachFilesVO;
import com.zte.iccp.itech.extension.spi.model.hol.PersonGeneralInfo;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
* 
* <AUTHOR> 10335201
* @date 2024-11-28 上午11:24
**/
@ApiModel("移动审批_审批详情返回值")
@Getter
@Setter
public class PermissionApplyDetailVO {
    private String type;
    private String billNo;
    private String billName;
    private String moduleName;
    private String roleName;
    private String product;
    private String organization;
    private String expirationTime;
    private String applicationReason;
    private String crossPartnerPermission;
    private String zhongZhiHighLease;
    private List<AttachFilesVO> attachFiles;
    private List<ApprovalProgressVO> approvalProgress;
    private List<OrganizationApprovalVO> organizationApprovals;
    private Date submitDate;
    private PersonGeneralInfo submitter;
}
