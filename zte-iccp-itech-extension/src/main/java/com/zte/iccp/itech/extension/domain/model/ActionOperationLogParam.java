package com.zte.iccp.itech.extension.domain.model;

import com.zte.iccp.itech.extension.domain.enums.OperationLogActionEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 操作管理 - 执行动作 - 操作日志入参
 *
 * <AUTHOR> jiang<PERSON><PERSON>en
 * @date 2025/5/16
 */
@Setter
@Getter
public class ActionOperationLogParam {

    private String assignmentCode;

    private OperationLogActionEnum operationLogActionEnum;

    private String businessId;

}
