package com.zte.iccp.itech.extension.domain.enums.clockin;

import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.plugin.form.clockin.reviews.ComponentCidConsts;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 打卡复盘节点枚举
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/11
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ClockInReviewsNodeEnum {

    /** 复盘初审 */
    CLOCKIN_REVIEWS_INIT(AssignmentStatusEnum.REVIEWS_INIT, ComponentCidConsts.COMPONENT_CLOCKIN_REVIEWS_INIT_CID),

    /** 复盘提交 */
    CLOCKIN_REVIEWS_SUBMIT(AssignmentStatusEnum.REVIEWS_SUBMIT, ComponentCidConsts.COMPONENT_CLOCKIN_REVIEWS_SUBMIT_CID),

    /** 复盘审核 */
    CLOCKIN_REVIEWS_APPROVAL(AssignmentStatusEnum.REVIEWS_APPROVAL, ComponentCidConsts.COMPONENT_CLOCKIN_REVIEWS_APPROVAL_CID),

    /** 审核确认 */
    CLOCKIN_REVIEWS_RECTIFY(AssignmentStatusEnum.REVIEWS_RECTIFY, ComponentCidConsts.COMPONENT_CLOCKIN_REVIEWS_RECTIFY_CID),

    /** 系统审核节点 */
    CLOCKIN_REVIEWS_SYSTEM(AssignmentStatusEnum.REVIEWS_RECTIFICATION),

    /** 已关闭 节点 */
    CLOSE(AssignmentStatusEnum.CLOSE),
    ;

    private final AssignmentStatusEnum assignmentStatusEnum;

    private String advancedContainerCid;

    ClockInReviewsNodeEnum(AssignmentStatusEnum assignmentStatusEnum, String advancedContainerCid) {
        this.assignmentStatusEnum = assignmentStatusEnum;
        this.advancedContainerCid = advancedContainerCid;
    }

    /**
     * 根据name获取打卡复盘节点枚举
     *
     * @param name 名称
     * @return ClockInReviewsNodeEnum
     */
    public static ClockInReviewsNodeEnum fromName(String name) {
        return Arrays.stream(values()).filter(item -> item.name().equals(name)).findFirst().orElse(null);
    }

    /**
     * 打卡复盘审批节点
     */
    public static List<String> approvingNode() {
        return Lists.newArrayList(
                CLOCKIN_REVIEWS_INIT.name(),
                CLOCKIN_REVIEWS_APPROVAL.name(),
                CLOCKIN_REVIEWS_RECTIFY.name(),
                CLOCKIN_REVIEWS_SYSTEM.name());
    }
}
