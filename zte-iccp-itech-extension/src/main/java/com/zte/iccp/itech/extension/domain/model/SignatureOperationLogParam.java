package com.zte.iccp.itech.extension.domain.model;

import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 会签操作日志输入参数
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/5/14
 */

@Setter
@Getter
public class SignatureOperationLogParam {

    /**
     * IntlAdminApproval、MultiModeProduct、OpAssocProdNetServIntApproval、OpAssocProdDevIntApproval
     */
    private Class<? extends BaseSubEntity> subEntityClass;

    /**
     * 业务id，如网络变更单id、批次id（不能传子单据体id）
     */
    private String businessId;

    /**
     * 审核人
     */
    private String approver;
}
