package com.zte.iccp.itech.extension.domain.model.clockin.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInCustomerFlagEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.NetSvrDutyChiefEngineerFieldsConsts.*;

/**
 * 网络服务处值班总工
 * <AUTHOR>
 * @since 2024/10/16
 */
@Getter
@Setter
@BaseEntity.Info("network_service_department_duty_chief_engineer")
public class NetSvrDutyChiefEngineer extends BaseWeeklyDutyPerson {
    @JsonProperty(value = PROD_TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String prodType;

    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;

    @JsonProperty(value = CUSTOMER_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ClockInCustomerFlagEnum customerFlag;
}
