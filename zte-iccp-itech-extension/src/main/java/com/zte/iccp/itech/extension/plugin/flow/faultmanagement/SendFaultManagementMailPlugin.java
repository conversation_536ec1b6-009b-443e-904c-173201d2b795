package com.zte.iccp.itech.extension.plugin.flow.faultmanagement;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.EmailConsts;
import com.zte.iccp.itech.extension.domain.enums.ApprovePageEnum;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.TaskCategoryEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.spi.client.EmailClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecordsDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.FaultAssignment.*;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.FAULT_MANAGEMENT_TASK_APPROVAL;
import static com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum.FAULT_MANAGEMENT_NOTICE;
import static com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum.FAULT_MANAGEMENT_PRESS;
import static com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum.FAULT_MANAGEMENT;


/**
 * 发送故障管理邮件催办/知会插件
 */
@Slf4j
public class SendFaultManagementMailPlugin extends BaseFlowPlugin {

    // 确认故障复盘_自定义编码
    private static final String FAULT_REVIEW_CONFIRMING_EXTEND_CODE = "FAULT_REVIEW_CONFIRMING";

    // 复盘任务执行中_自定义编码
    private static final String FAULT_REVIEWING_EXTEND_CODE = "FAULT_REVIEWING";

    // 故障整改_自定义编码
    private static final String FAULT_RECTIFICATION_PROMOTION_CONFIRMING_EXTEND_CODE = "FAULT_RECTIFICATION_PROMOTION_CONFIRMING";

    // 反馈客户满意度_自定义编码
    private static final String PENDING_FEEDBACK_CUSTOMER_SATISFACTION_EXTEND_CODE = "PENDING_FEEDBACK_CUSTOMER_SATISFACTION";

    private static final Map<String, String> EXTEND_CODE_NAME = MapUtils.newHashMap(
            FAULT_REVIEW_CONFIRMING_EXTEND_CODE, FAULT_REVIEW_CONFIRM,
            FAULT_REVIEWING_EXTEND_CODE, FAULT_REVIEW_REPORT,
            FAULT_RECTIFICATION_PROMOTION_CONFIRMING_EXTEND_CODE, FAULT_RECTIFICATION,
            PENDING_FEEDBACK_CUSTOMER_SATISFACTION_EXTEND_CODE, CUSTOMER_SATISFACTION
    );

    private static final Map<String, String> EXTEND_CODE_LEVEL = MapUtils.newHashMap(
            FAULT_REVIEW_CONFIRMING_EXTEND_CODE, FAULT_MANAGEMENT_TASK_CURRENT_PROGRESS_2,
            FAULT_REVIEWING_EXTEND_CODE, FAULT_MANAGEMENT_TASK_CURRENT_PROGRESS_3,
            FAULT_RECTIFICATION_PROMOTION_CONFIRMING_EXTEND_CODE, FAULT_MANAGEMENT_TASK_CURRENT_PROGRESS_4,
            PENDING_FEEDBACK_CUSTOMER_SATISFACTION_EXTEND_CODE, FAULT_MANAGEMENT_TASK_CURRENT_PROGRESS_5
    );

    private static final Map<String, String> EXTEND_CODE_NODE_KEY = MapUtils.newHashMap(
            FAULT_REVIEW_CONFIRMING_EXTEND_CODE, FAULT_REVIEW_CONFIRMING_NODE_KEY,
            FAULT_REVIEWING_EXTEND_CODE, FAULT_REVIEWING_NODE_KEY,
            FAULT_RECTIFICATION_PROMOTION_CONFIRMING_EXTEND_CODE, FAULT_RECTIFICATION_PROMOTION_CONFIRMING_NODE_KEY,
            PENDING_FEEDBACK_CUSTOMER_SATISFACTION_EXTEND_CODE, PENDING_FEEDBACK_CUSTOMER_SATISFACTION_NODE_KEY
    );

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        // 异步发送
        AsyncExecuteUtils.execute(() -> {
            sendEmail(body);
        });
        return false;
    }

    @SneakyThrows
    private void sendEmail(FlowClient body) {
        /**
         * 解决配置在流程节点中，当前配置的插件提前发送未获取到知会人问题
         * 以及催办邮件taskId未获取问题
         */
        Thread.sleep(5000);
        FaultManagementAssignment assignment =
                AssignmentAbility.querySpecificTypeAssignment(body.getBusinessId(),
                        FAULT_MANAGEMENT, FaultManagementAssignment.class);
        // 查询审批记录
        List<ApproveRecord> allRecords = FlowServiceHelper
                .getFlowApproveRecords(new ApproveRecordsDTO() {{
                    setTenantId(ContextHelper.getTenantId());
                    setAppId(ContextHelper.getAppId());
                    setBizInstanceId(body.getBusinessId());
                }});
        List<ApproveRecord> activeRecords = allRecords.stream()
                .filter(v -> ACTIVE_TASK_STATUS.equals(v.getTaskStatus()))
                .collect(Collectors.toList());
        List<FlowHandler> flowHandlers = FlowHelper.getFlowHandlerByFlowEntityIds(Lists.newArrayList(body.getBusinessId()));
        sendFaultManagementPessMail(activeRecords, assignment, flowHandlers);
        sendFaultManagementNoticeMail(allRecords, activeRecords, assignment, body.getBusinessId());
    }

    /**
     * 发送催办邮件，故障分析/故障复盘确认/待提交复盘报告/待故障横推整改
     *
     */
    private void sendFaultManagementPessMail(List<ApproveRecord> activeRecords,
            FaultManagementAssignment assignment,
            List<FlowHandler> flowHandlers) {
        if (null == assignment
                || CollectionUtils.isEmpty(flowHandlers)
                || CollectionUtils.isEmpty(activeRecords)) {
            return;
        }
        // 当前进展
        ApproveRecord activeRecord = activeRecords.get(INTEGER_ZERO);
        String activeExtendCode = activeRecord.getExtendedCode();
        String assignmentName = assignment.getAssignmentName();
        String nextLevelZh = MsgUtils.getLangMessage(ZH_CN, EXTEND_CODE_LEVEL.getOrDefault(activeExtendCode, EMPTY_STRING));
        String nextLevelEn = MsgUtils.getLangMessage(EN_US, EXTEND_CODE_LEVEL.getOrDefault(activeExtendCode, EMPTY_STRING));

        // 收件人，节点的处理人
        // 当前处理人
        List<String> receivers = activeRecords.stream().map(ApproveRecord::getApprover).distinct().collect(Collectors.toList());

        FlowHandler flowHandler = flowHandlers.get(INTEGER_ZERO);
        Map<String, Set<ApproveTask>> approveMap = getApproveMap(flowHandler);

        // 数据data
        Map<String, Object> data = MapUtils.newHashMap(
                EmailConsts.NEXT_LEVEL_ZH, nextLevelZh,
                EmailConsts.NEXT_LEVEL_EN, nextLevelEn,
                EmailConsts.TASK_NAME_ZH, assignmentName,
                EmailConsts.TASK_NAME_EN, assignmentName,
                EmailConsts.EMAIL_SENDTIME, SIMPLE_DATE_FORMAT.format(new Date()));
        // 当前处理人的链接不同，需循环发送
        for (String receiver : receivers) {
            String taskId = getTaskId(activeExtendCode, approveMap, receiver);
            String tabName = MsgUtils.getLangMessage(ContextHelper.getLangId(), FAULT_MANAGEMENT_TASK_APPROVAL);
            String approvalZhUrl = SendEmailHelper.getApprovalUrl(
                    tabName, flowHandler.getFlowInstanceId(),
                    taskId, ApprovePageEnum.FAULT_MANAGEMENT.getPageId());
            // todo 英文跳转暂不支持
            String approvalEnUrl = SendEmailHelper.getApprovalUrl(tabName, flowHandler.getFlowInstanceId(),
                    taskId, ApprovePageEnum.FAULT_MANAGEMENT.getPageId());
            data.put(EmailConsts.EMAIL_SERVICE_ZH_URL, approvalZhUrl);
            data.put(EmailConsts.EMAIL_SERVICE_EN_URL, approvalEnUrl);
            EmailClient.sendMail(FAULT_MANAGEMENT_PRESS, Lists.newArrayList(receiver), null, data, null);
        }
    }

    /**
     * 发送知会邮件，故障复盘确认/故障复盘报告/故障整改横推
     *
     * 其中“客户满意度“的知会人邮件特殊，取的是页面的字段。
     * 其他的节点都是取之前节点处理人的总和，
     * 如“客户复盘确认”取的知会人是“故障分析”的处理人；
     */
    private void sendFaultManagementNoticeMail(List<ApproveRecord> allRecords,
            List<ApproveRecord> activeRecords,
            FaultManagementAssignment assignment, String businessId) {
        if (null == assignment
                || CollectionUtils.isEmpty(allRecords)
                || CollectionUtils.isEmpty(activeRecords)) {
            return;
        }
        // 当前进展
        ApproveRecord approveRecord = activeRecords.get(INTEGER_ZERO);
        String extendCode = approveRecord.getExtendedCode();
        String assignmentName = assignment.getAssignmentName();

        // 获取所有完成节点
        allRecords = allRecords.stream()
                .filter(v -> COMPLETED_TASK_STATUS.equals(v.getTaskStatus()))
                .collect(Collectors.toList());

        // 知会人
        List<String> receivers = getNoticeEmailReceivers(allRecords, assignment, extendCode);


        // 审批人中文
        String approverZh = EMPTY_STRING;
        // 审批人英文
        String approverEn = EMPTY_STRING;
        // 上一个节点的中文
        String lastLevelZh = EMPTY_STRING;
        // 上一个节点的英文
        String lastLevelEn = EMPTY_STRING;

        // 保留最近的一次审批记录
        Optional<ApproveRecord> latestRecords = allRecords.stream()
                .max(Comparator.comparing(ApproveRecord::getApprovalDate));

        if (latestRecords.isPresent()) {
            ApproveRecord latestRecord = latestRecords.get();
            lastLevelZh = MsgUtils.getLangMessage(ZH_CN, EXTEND_CODE_NAME.getOrDefault(latestRecord.getExtendedCode(), EMPTY_STRING));
            lastLevelEn = MsgUtils.getLangMessage(EN_US, EXTEND_CODE_NAME.getOrDefault(latestRecord.getExtendedCode(), EMPTY_STRING));
            String handlerId = latestRecord.getHandler();
            Map<String, Employee> employeeMap = HrClient.queryEmployeeInfos(Lists.newArrayList(handlerId));
            Employee employee = employeeMap.getOrDefault(handlerId, null);
            if (null != employee) {
                approverZh = employee.getEmpNameCn() + employee.getEmpUIID();
                approverEn = employee.getEmpNameEn() + employee.getEmpUIID();
            }
        }

        String zhUrl = SendEmailHelper.getViewUrl(ZH, businessId);
        String enUrl = SendEmailHelper.getViewUrl(EN, businessId);

        // 数据data
        Map<String, Object> data = MapUtils.newHashMap(
                EmailConsts.APPROVER_ZH, approverZh,
                EmailConsts.APPROVER_EN, approverEn,
                EmailConsts.LAST_LEVEL_ZH, lastLevelZh,
                EmailConsts.LAST_LEVEL_EN, lastLevelEn,
                EmailConsts.TASK_NAME_ZH, assignmentName,
                EmailConsts.TASK_NAME_EN, assignmentName,
                EmailConsts.EMAIL_SENDTIME, SIMPLE_DATE_FORMAT.format(new Date()),
                EmailConsts.EMAIL_SERVICE_ZH_URL, zhUrl,
                EmailConsts.EMAIL_SERVICE_EN_URL, enUrl);

        EmailClient.sendMail(FAULT_MANAGEMENT_NOTICE, receivers, null, data, null);
    }

    private List<String> getNoticeEmailReceivers(List<ApproveRecord> allRecords,
            FaultManagementAssignment assignment, String extendCode) {
        if (null == assignment || CollectionUtils.isEmpty(allRecords)) {
            return Lists.newArrayList();
        }
        // 待提交故障复盘报告和客户满意度的知会人，需要查询技术管理的责任人
        List<String> faultReviewResponsibles = Lists.newArrayList();
        List<String> techResponsibles = Lists.newArrayList();
        if (FAULT_REVIEWING_EXTEND_CODE.equals(extendCode)) {
            List<TechnologyManagementAssignment> technologyManagementAssignments =
                    AssignmentAbility.queryFaultReviewAssignment(assignment.getId());
            technologyManagementAssignments.forEach(item -> {
                List<String> faultReviewResponsible = EmployeeHelper.getEpmUIID(item.getResponsibleEmployee());
                faultReviewResponsibles.addAll(faultReviewResponsible);
            });
        }
        if (PENDING_FEEDBACK_CUSTOMER_SATISFACTION_EXTEND_CODE.equals(extendCode)) {
            List<TechnologyManagementAssignment> technologyManagementAssignments
                    = AssignmentAbility.queryFaultTechAssignments(
                            assignment.getId(), Lists.newArrayList(
                                    EMPTY_STRING,
                                    TaskCategoryEnum.FAULT_RECTIFY.getValue(),
                                    TaskCategoryEnum.HORIZONTAL_PUSHING_TASK.getValue(),
                                    TaskCategoryEnum.FAULT_REVIEW.getValue()));
            technologyManagementAssignments.forEach(item -> {
                List<String> faultReviewResponsible = EmployeeHelper.getEpmUIID(item.getResponsibleEmployee());
                techResponsibles.addAll(faultReviewResponsible);
            });
        }
        List<String> handlers = allRecords.stream().map(ApproveRecord::getHandler).distinct().collect(Collectors.toList());
        List<String> receivers = Stream.of(faultReviewResponsibles, techResponsibles, handlers)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        return receivers;
    }

    private String getTaskId(String activeExtendCode, Map<String, Set<ApproveTask>> approverMap, String receiver) {
        if (!StringUtils.hasText(receiver) || null == approverMap) {
            log.warn("Receivers is null in pressing email");
            return EMPTY_STRING;
        }
        String taskId = EMPTY_STRING;
        String nodeKey = EXTEND_CODE_NODE_KEY.getOrDefault(activeExtendCode, EMPTY_STRING);
        Set<ApproveTask> approveTaskTmp = approverMap.getOrDefault(nodeKey, null);
        if (null != approveTaskTmp) {
            Optional<ApproveTask> approveTask
                    = approveTaskTmp.stream().filter(v -> receiver.equals(v.getApprover())).findFirst();
            if (approveTask.isPresent()) {
                taskId = approveTask.get().getTaskId();
            } else {
                log.warn("TaskId is null in pressing email");
            }
        } else {
            log.warn("Approve Task is null in pressing email");
        }
        return taskId;
    }

    private Map<String, Set<ApproveTask>> getApproveMap(FlowHandler flowHandler) {
        if (null != flowHandler) {
            List<ApproveTask> approveTasks = flowHandler.getApproveTaskList();
            if (!CollectionUtils.isEmpty(approveTasks)) {
                return approveTasks.stream()
                        .collect(Collectors.groupingBy(ApproveTask::getNodeCode,
                                Collectors.mapping(approveTask -> approveTask, Collectors.toSet())));
            }
        }
        return Maps.newHashMap();
    }
}