package com.zte.iccp.itech.extension.domain.enums.clockin;

import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Date;
import java.util.Set;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum CallReasonEnum implements SingletonTextValuePairsProvider {
    /** 汇总呼叫 */
    SUMMARY(0, 5, 3),
    /** 转交提醒 */
    TRANSFER(10, 10, 3),
    /** 异常卡 */
    EXCEPTION(0, 0, -1,
            ClockInOptionEnum.OPERATION_ERROR_HANDLING,
            ClockInOptionEnum.OPERATION_FAILED_REPEAT,
            ClockInOptionEnum.TEST_FAILED_HANDLING,
            ClockInOptionEnum.INDICATOR_ERROR_HANDLING),
    /** 准备开始卡 */
    PREPARE_START(10, 10, 3,
            "操作准备开始", ClockInTask::getPlanPrepareStartTime,
            ClockInOptionEnum.PREPARE_START),
    /** 操作开始卡 */
    OPERATION_START(10, 10, 3,
            "操作执行开始", ClockInTask::getPlanExecuteStartTime,
            ClockInOptionEnum.PREPARE_END_OPERATION_START),
    /** 操作执行结束卡 */
    OPERATION_END(10, 10, 3,
            "操作执行结束", ClockInTask::getPlanExecuteEndTime,
            ClockInOptionEnum.OPERATION_COMPLETED_TEST_START,
            ClockInOptionEnum.OPERATION_PARTIALLY_TEST_START,
            ClockInOptionEnum.OPERATION_FAILED_ROLLBACK_END,
            ClockInOptionEnum.OPERATION_FAILED_NO_ROLLBACK),
    /** 测试结束卡 */
    TEST_END(10, 10, 3,
            "业务测试结束", ClockInTask::getPlanTestEndTime,
            ClockInOptionEnum.TEST_SUCCESS_OPERATION_END,
            ClockInOptionEnum.TEST_FAILED_ROLLBACK_END,
            ClockInOptionEnum.TEST_FAILED_NO_ROLLBACK),
    /** 值守过程卡 */
    ON_DUTY_CONTINUE(30, 0, 1,
            "值守", null,
            ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_CONTINUE,
            ClockInOptionEnum.INDICATOR_ERROR_ROLLBACK_END,
            ClockInOptionEnum.INDICATOR_ERROR_NO_ROLLBACK
            ),
    /** 值守结束卡 */
    ON_DUTY_END(10, 10, 3,
            "值守结束", null,
            ClockInOptionEnum.INDICATOR_NORMAL_ON_DUTY_END),
    ;

    @Getter
    private final int firstTimeDelayMinutes;

    @Getter
    private final int intervalMinutes;

    @Getter
    private final int maxCallCount;

    @Getter
    private final String clockInName;

    @Getter
    private final Function<ClockInTask, Date> planClockInTime;

    private final Set<ClockInOptionEnum> neededOptions;

    CallReasonEnum(int firstTimeDelayMinutes, int intervalMinutes, int maxCallCount) {
        this(firstTimeDelayMinutes, intervalMinutes, maxCallCount,
                null, null, new ClockInOptionEnum[0]);
    }

    CallReasonEnum(int firstTimeDelayMinutes, int intervalMinutes, int maxCallCount, ClockInOptionEnum... neededOptions) {
        this(firstTimeDelayMinutes, intervalMinutes, maxCallCount,
                null, null, neededOptions);
    }

    CallReasonEnum(
            int firstTimeDelayMinutes,
            int intervalMinutes,
            int maxCallCount,
            String clockInName,
            Function<ClockInTask, Date> planClockInTime,
            ClockInOptionEnum... neededOptions) {
        this.firstTimeDelayMinutes = firstTimeDelayMinutes;
        this.intervalMinutes = intervalMinutes;
        this.maxCallCount = maxCallCount;
        this.clockInName = clockInName;
        this.planClockInTime = planClockInTime;
        this.neededOptions = Sets.newHashSet(neededOptions);
    }

    @Override
    public String getMsgKey() {
        return name();
    }

    @Override
    public String getValue() {
        return name();
    }

    public static CallReasonEnum fromOption(ClockInOptionEnum option) {
        for (CallReasonEnum reason : values()) {
            if (reason.neededOptions.contains(option)) {
                return reason;
            }
        }

        return null;
    }

    public CallReasonEnum next() {
        if (clockInName == null) {
            return null;
        }

        return ordinal() == values().length - 1
                ? null : values()[ordinal() + 1];
    }
}
