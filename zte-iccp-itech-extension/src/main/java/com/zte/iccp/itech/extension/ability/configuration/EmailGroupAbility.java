package com.zte.iccp.itech.extension.ability.configuration;

import com.alibaba.fastjson2.JSONArray;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.TableDefaultConditionAbility;
import com.zte.iccp.itech.extension.ability.common.table.DataAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.ProdCateLvlConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.EmailGroupFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchOperationResultEnum;
import com.zte.iccp.itech.extension.domain.enums.configuration.EmailCcTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.configuration.EmailSendGroup;
import com.zte.iccp.itech.extension.domain.model.export.EmailSendGroupInfo;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.paas.lcap.common.constant.LangConst;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class EmailGroupAbility {

    /**
     * 检索邮件推送群组 - 主键
     */
    public static EmailSendGroup get(String id, List<String> fields) {
        if (!StringUtils.hasText(id)) {
            return null;
        }

        return QueryDataHelper.get(EmailSendGroup.class, fields, id);
    }

    /**
     * 检索邮件推送群组 - 主键
     */
    private static List<EmailSendGroup> get(List<String> ids, List<String> fields) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        Filter idFilter = new Filter(CommonFieldConsts.ID, Comparator.IN, ids);

        return QueryDataHelper.query(EmailSendGroup.class, fields, Lists.newArrayList(idFilter));
    }

    /**
     * 构建列表查询条件
     */
    public static List<IFilter> getTableQueryConditions(IDataModel dataModel) {
        // 抄送类型
        List<IFilter> iFilters = Lists.newArrayList();
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel, EmailGroupFieldConsts.CC_TYPE, EmailGroupFieldConsts.CC_TYPE));

        // 产品分类
        iFilters.add(ProductUtils.productFilter(
                dataModel,
                CidConstants.COMPONENT_PRODUCT_TREE_CID,
                EmailGroupFieldConsts.PRODUCT,
                ProdCateLvlConsts.SUB_CATEGORY,
                false));

        // 组织
        iFilters.add(ResponsibleUtils.organizationFilter(
                dataModel,
                CidConstants.COMPONENT_ORGANIZATION_TREE_CID,
                EmailGroupFieldConsts.ORGANIZATION,
                false,
                OrganizationTreeVo::getOrgIdPath));

        // 抄送人员
        iFilters.add(TableDefaultConditionAbility.defaultEmployeeFilter(
                dataModel, EmailGroupFieldConsts.CC_PERSON, EmailGroupFieldConsts.CC_PERSON, true));

        // 政企
        iFilters.add(TableDefaultConditionAbility.defaultDropDownFilter(
                dataModel, EmailGroupFieldConsts.GOVERNMENT_FLAG, EmailGroupFieldConsts.GOVERNMENT_FLAG));

        return iFilters.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 包装列表补充数据
     */
    public static Map<String, Map<String, String>> convertTableSupplementaryData(
            JSONArray groupArray,
            String idKey,
            Map<String, String> dataKeys) {

        // 1.查询群组信息
        List<String> groupIds = DataAbility.getSearchRecordsPkId(groupArray, idKey);
        List<String> fields = Lists.newArrayList(CommonFieldConsts.ID, EmailGroupFieldConsts.PRODUCT);
        List<EmailSendGroup> groups = get(groupIds, fields);
        if (CollectionUtils.isEmpty(groups)) {
            return new HashMap<>();
        }

        // 2.包装补充数据
        Map<String, Map<String, String>> groupSupplementaryData = new HashMap<>();
        for (EmailSendGroup group : groups) {
            String productNamePath = TextValuePairHelper.getLanguageText(group.getProduct());

            Map<String, String> supplementaryData = MapUtils.newHashMap(
                    dataKeys.get(CommonConstants.KEY_PRODUCT_OPERATION_TEAM),
                    ProductUtils.getValueIndex(productNamePath, ProdCateLvlConsts.TEAM),
                    dataKeys.get(CommonConstants.KEY_PRODUCT_LINE),
                    ProductUtils.getValueIndex(productNamePath, ProdCateLvlConsts.LINE),
                    dataKeys.get(CommonConstants.KEY_PRODUCT_CATEGORY),
                    ProductUtils.getValueIndex(productNamePath, ProdCateLvlConsts.MAIN_CATEGORY),
                    dataKeys.get(CommonConstants.KEY_PRODUCT_SUBCATEGORY),
                    ProductUtils.getValueIndex(productNamePath, ProdCateLvlConsts.SUB_CATEGORY));

            groupSupplementaryData.put(group.getId(), supplementaryData);
        }

        return groupSupplementaryData;
    }

    /**
     * 包装导出数据
     */
    public static List<EmailSendGroupInfo> convertExportData(List<EmailSendGroup> groups) {
        String language = ContextHelper.getLangId();

        // 1.检索外部接口的数据
        // HR - 更新人
        List<String> userIds = groups.stream()
                .map(EmailSendGroup::getLastModifiedBy)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> userNames = HrClient.queryEmployeeNameInfo(userIds);

        // 2.包装导出对象
        List<EmailSendGroupInfo> exportInfo = Lists.newArrayList();
        for (EmailSendGroup group : groups) {
            EmailSendGroupInfo groupInfo = new EmailSendGroupInfo();

            groupInfo.setCcType(TextValuePairHelper.getLanguageText(group.getCcType()));
            groupInfo.setOrganization(TextValuePairHelper.getLanguageText(group.getOrganization()));
            groupInfo.setGovernment(group.getGovernmentFlag().getName(language));
            groupInfo.setCcPerson(EmployeeHelper.getEpmNameUIID(group.getCcPerson()));
            groupInfo.setRemarks(group.getRemark());
            groupInfo.setStatus(TextValuePairHelper.getLanguageText(group.getInServiceFlag()));
            groupInfo.setUpdateBy(userNames.get(group.getLastModifiedBy()));
            groupInfo.setUpdateTime(
                    DateUtils.dateToString(group.getLastModifiedTime(), CommonConstants.DATE_FORM));

            String productNamePath = TextValuePairHelper.getLanguageText(group.getProduct());
            groupInfo.setProductTeam(
                    ProductUtils.getValueIndex(productNamePath, ProdCateLvlConsts.TEAM));
            groupInfo.setProductLine(
                    ProductUtils.getValueIndex(productNamePath, ProdCateLvlConsts.LINE));
            groupInfo.setProductCategory(
                    ProductUtils.getValueIndex(productNamePath, ProdCateLvlConsts.MAIN_CATEGORY));
            groupInfo.setProductSubcategory(
                    ProductUtils.getValueIndex(productNamePath, ProdCateLvlConsts.SUB_CATEGORY));

            exportInfo.add(groupInfo);
        }

        return exportInfo;
    }

    /**
     * 启用 / 停用 群组配置
     */
    public static boolean inServiceControl(List<String> groupIds, boolean inService) {
        // 1.检索群组数据
        List<String> fields
                = Lists.newArrayList(CommonFieldConsts.ID, EmailGroupFieldConsts.IN_SERVICE_FLAG);
        List<EmailSendGroup> groups = get(groupIds, fields);
        if (CollectionUtils.isEmpty(groups)) {
            return false;
        }

        // 2.根据操作标识，启用 / 停用群组
        List<TextValuePair> inServiceFlag = inService
                ? TextValuePairHelper.buildList(
                        BoolEnum.Y.name(),
                        MsgUtils.getLangMessage(LangConst.ZH_CN, MessageConsts.CommonKeyword.ENABLED),
                        MsgUtils.getLangMessage(LangConst.EN_US, MessageConsts.CommonKeyword.ENABLED))
                : TextValuePairHelper.buildList(
                        BoolEnum.N.name(),
                        MsgUtils.getLangMessage(LangConst.ZH_CN, MessageConsts.CommonKeyword.DISABLED),
                        MsgUtils.getLangMessage(LangConst.EN_US, MessageConsts.CommonKeyword.DISABLED));
        groups.forEach(item -> item.setInServiceFlag(inServiceFlag));
        return SaveDataHelper.batchUpdate(groups);
    }

    /**
     * 启用 / 停用 群组配置
     */
    public static boolean delete(List<String> groupIds) {
        // 1.检索群组数据
        List<String> fields
                = Lists.newArrayList(CommonFieldConsts.ID, EmailGroupFieldConsts.IN_SERVICE_FLAG);
        List<EmailSendGroup> groups = get(groupIds, fields);
        if (CollectionUtils.isEmpty(groups)) {
            return true;
        }

        // 2.存在启用状态数据，不允许删除
        // 需要提示信息
        List<String> disabledGroupIds = groups.stream()
                .filter(item -> BoolEnum.N.name().equals(
                        TextValuePairHelper.getValue(item.getInServiceFlag())))
                .map(EmailSendGroup::getId)
                .collect(Collectors.toList());
        if (disabledGroupIds.size() != groups.size()) {
            return false;
        }

        // 3.删除停用数据
        SaveDataHelper.batchDelete(EmailSendGroup.class, disabledGroupIds);
        return true;
    }

    /**
     * 获取抄送人员 - 网络变更
     */
    public static List<String> getNetworkChangeCcPerson(
            IChangeOrder changeOrder,
            ApproveNodeEnum approveNodeEnum) {

        // 1.产品分类
        IFilter productFilter = productFilter(changeOrder.getProductCategory());

        // 2.组织
        IFilter organizationFilter = organizationFilter(changeOrder.getResponsibleDept());

        // 3.政企
        IFilter governmentFilter = new Filter(
                EmailGroupFieldConsts.GOVERNMENT_FLAG,
                Comparator.EQ,
                Lists.newArrayList(changeOrder.getIsGovEnt().getValue()));

        // 4.启用
        IFilter enabledFilter = new Filter(EmailGroupFieldConsts.IN_SERVICE_FLAG,
                Comparator.EQ,
                Lists.newArrayList(BoolEnum.Y.getValue()));

        // 5.抄送类型
        IFilter ccTypeFilter = Objects.isNull(approveNodeEnum)
                ? new Filter(
                        EmailGroupFieldConsts.CC_TYPE,
                        Comparator.IN,
                        Lists.newArrayList(EmailCcTypeEnum.ALL_PROCESSES.getCode()))
                : networkChangeCcTypeFilter(changeOrder, approveNodeEnum);

        // 6.检索群组推送配置
        List<EmailSendGroup> groups =  QueryDataHelper.query(
                EmailSendGroup.class,
                Lists.newArrayList(EmailGroupFieldConsts.CC_PERSON),
                Lists.newArrayList(productFilter, organizationFilter, governmentFilter, enabledFilter, ccTypeFilter));
       return groups.stream()
               .map(EmailSendGroup::getCcPerson)
               .flatMap(List::stream)
               .map(Employee::getEmpUIID)
               .distinct()
               .collect(Collectors.toList());
    }

    /**
     * 获取抄送人员 - 批次任务
     */
    public static List<String> getBatchCcPerson(
            IChangeOrder changeOrder,
            IBatchTask batchTask,
            ApproveNodeEnum approveNodeEnum,
            PartnerApproveNodeEnum partnerApproveNodeEnum) {

        // 1.产品分类
        IFilter productFilter = productFilter(changeOrder.getProductCategory());

        // 2.组织
        IFilter organizationFilter = organizationFilter(changeOrder.getResponsibleDept());

        // 3.政企
        IFilter governmentFilter = new Filter(
                EmailGroupFieldConsts.GOVERNMENT_FLAG,
                Comparator.EQ,
                Lists.newArrayList(changeOrder.getIsGovEnt().getValue()));

        // 4.启用
        IFilter enabledFilter = new Filter(EmailGroupFieldConsts.IN_SERVICE_FLAG,
                Comparator.EQ,
                Lists.newArrayList(BoolEnum.Y.getValue()));

        // 5.抄送类型
        IFilter ccTypeFilter = Objects.isNull(partnerApproveNodeEnum)
                ? batchCcTypeFilter(approveNodeEnum, changeOrder, batchTask)
                : partnerBatchCcTypeFilter(partnerApproveNodeEnum, batchTask);

        // 6.检索群组推送配置
        List<EmailSendGroup> groups = QueryDataHelper.query(
                EmailSendGroup.class,
                Lists.newArrayList(EmailGroupFieldConsts.CC_PERSON),
                Lists.newArrayList(productFilter, organizationFilter, governmentFilter, enabledFilter, ccTypeFilter));
        return groups.stream()
                .map(EmailSendGroup::getCcPerson)
                .flatMap(List::stream)
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 查询条件 - 产品
     */
    private static IFilter productFilter(String product) {
        String productTeam = ProductUtils.getTeam(product);
        String productLine = ProductUtils.getLine(product);
        String productCategory = ProductUtils.getMain(product);
        String productSubcategory = ProductUtils.getSub(product);
        return new Filter(
                EmailGroupFieldConsts.PRODUCT,
                Comparator.IN,
                Lists.newArrayList(productTeam, productLine, productCategory, productSubcategory));
    }

    /**
     * 查询条件 - 组织
     */
    private static IFilter organizationFilter(String organization) {
        String marketing = ResponsibleUtils.getSales(organization);
        String region = ResponsibleUtils.getRegion(organization);
        return new Filter(
                EmailGroupFieldConsts.ORGANIZATION,
                Comparator.IN,
                Lists.newArrayList(marketing, region, organization));
    }

    /**
     * 推送类型 - 网络变更
     */
    private static IFilter networkChangeCcTypeFilter(
            IChangeOrder iChangeOrder,
            ApproveNodeEnum approveNodeEnum) {

        ChangeOrder changeOrder = (ChangeOrder) iChangeOrder;

        // 1.所有流程均抄送
        List<String> ccTypes = Lists.newArrayList(EmailCcTypeEnum.ALL_PROCESSES.getCode());

        // 2.有线产品专项保障抄送
        if (ApproveNodeEnum.NET_SERVICE_DEPT_APP.equals(approveNodeEnum)
                && BoolEnum.Y.equals(changeOrder.getIsDedicatedBearerGuarantee())) {
            ccTypes.add(EmailCcTypeEnum.WIRED_SPECIAL_GUARANTEE.getCode());
        }

        // 3.客户特殊业务默认抄送
        if (BoolEnum.Y.equals(changeOrder.getIsSpecialScenario())) {
            ccTypes.add(EmailCcTypeEnum.SPECIAL_SERVICES.getCode());
        }

        // 4.ToB业务默认抄送
        if (BoolEnum.Y.equals(changeOrder.getIsAffectToB())) {
            ccTypes.add(EmailCcTypeEnum.TO_B_SERVICE.getCode());
        }

        return new Filter(EmailGroupFieldConsts.CC_TYPE, Comparator.IN, ccTypes);
    }

    /**
     * 推送类型 - 批次任务
     */
    private static IFilter batchCcTypeFilter(
            ApproveNodeEnum approveNodeEnum,
            IChangeOrder iChangeOrder,
            IBatchTask batchTask) {

        // 1.所有流程均抄送
        List<String> ccTypes = Lists.newArrayList(EmailCcTypeEnum.ALL_PROCESSES.getCode());

        // 2.仅通告及反馈操作结果抄送
        if (ApproveNodeEnum.PENDING_NOTIFICATION.equals(approveNodeEnum)) {
            ccTypes.add(EmailCcTypeEnum.NOTIFICATION_AND_FEEDBACK.getCode());
        }

        // 3.仅操作反馈失败时抄送
        if (ApproveNodeEnum.RESULT_TOBE_BACK.equals(approveNodeEnum)) {
            ccTypes.add(EmailCcTypeEnum.NOTIFICATION_AND_FEEDBACK.getCode());

            String result = TextValuePairHelper.getValue(batchTask.getOperationResult());
            if (BatchOperationResultEnum.OPERATION_FAILED.getValue().equals(result)
                    || BatchOperationResultEnum.PARTIAL_FAILURE.getValue().equals(result)) {
                ccTypes.add(EmailCcTypeEnum.OPERATION_FAILED.getCode());
            }
        }

        // 4.客户特殊业务默认抄送
        ChangeOrder changeOrder = (ChangeOrder) iChangeOrder;
        if (BoolEnum.Y.equals(changeOrder.getIsSpecialScenario())) {
            ccTypes.add(EmailCcTypeEnum.SPECIAL_SERVICES.getCode());
        }

        // 5.ToB业务默认抄送
        if (BoolEnum.Y.equals(changeOrder.getIsAffectToB())) {
            ccTypes.add(EmailCcTypeEnum.TO_B_SERVICE.getCode());
        }

        return new Filter(EmailGroupFieldConsts.CC_TYPE, Comparator.IN, ccTypes);
    }

    /**
     * 推送类型 - 合作方批次任务
     */
    private static IFilter partnerBatchCcTypeFilter(
            PartnerApproveNodeEnum approveNodeEnum,
            IBatchTask batchTask) {

        // 1.所有流程均抄送
        List<String> ccTypes = Lists.newArrayList(EmailCcTypeEnum.ALL_PROCESSES.getCode());

        // 2.仅通告及反馈操作结果抄送
        if (PartnerApproveNodeEnum.PENDING_NOTIFICATION.equals(approveNodeEnum)) {
            ccTypes.add(EmailCcTypeEnum.NOTIFICATION_AND_FEEDBACK.getCode());
        }

        // 3.仅操作反馈失败时抄送
        if (PartnerApproveNodeEnum.RESULT_TOBE_BACK.equals(approveNodeEnum)) {
            ccTypes.add(EmailCcTypeEnum.NOTIFICATION_AND_FEEDBACK.getCode());

            String result = TextValuePairHelper.getValue(batchTask.getOperationResult());
            if (BatchOperationResultEnum.OPERATION_FAILED.getValue().equals(result)
                    || BatchOperationResultEnum.PARTIAL_FAILURE.getValue().equals(result)) {
                ccTypes.add(EmailCcTypeEnum.OPERATION_FAILED.getCode());
            }
        }

        return new Filter(EmailGroupFieldConsts.CC_TYPE, Comparator.IN, ccTypes);
    }
}
