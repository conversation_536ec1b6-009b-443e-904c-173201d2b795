package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInStateEnum;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.IS_HIGH_RISK_INSTRUCTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.*;

@ApiModel("网络批次任务")
@Setter
@Getter
@BaseEntity.Info("subcontractor_batch_task")
public class SubcontractorBatchTask extends BaseEntity implements IBatchTask {

    @JsonProperty(value = BATCH_NAME)
    @ApiModelProperty("批次任务名称")
    private String batchName;

    @JsonProperty(value = BatchTaskFieldConsts.BATCH_NO)
    @ApiModelProperty("批次号")
    private String batchNo;

    @JsonProperty(value = BATCH_CODE)
    @ApiModelProperty("批次编码")
    private String batchCode;

    @JsonProperty(value = URGENT_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("紧急操作")
    private BoolEnum urgentFlag;

    @JsonProperty(value = EMERGENCY_OPERATION_FLAG)
    @ApiModelProperty("紧急操作标记")
    private String emergencyOperationFlag;

    @JsonProperty(value = URGENT_FLAG_TOO)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否变更紧急操作")
    private BoolEnum urgentFlagToo;

    @JsonProperty(value = CUSTOMER_VOUCHER_FILE)
    @ApiModelProperty("客户授权凭证")
    private List<MultiAttachmentFile> customerVoucherFile;

    @JsonProperty(value = NOTIFICATION_DESC)
    @ApiModelProperty("通告说明")
    private String notificationDesc;

    @JsonProperty(value = PLAN_OPERATION_DAY)
    @ApiModelProperty("操作预计投入人天")
    private Long planOperationDay;

    @JsonProperty(value = PERSON_CHANGE_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("涉及人员变更")
    private BoolEnum personChangeFlag;

    @JsonProperty(value = STATUS)
    @ApiModelProperty("单据状态")
    private String status;

    @JsonProperty(value = CURRENT_PROCESSOR)
    @ApiModelProperty("当前处理人")
    private String currentProcessor;

    @JsonProperty(value = GRANT_FILE)
    @ApiModelProperty("授权文件")
    private String grantFile;

    @JsonProperty(value = SA_GRANT_FILE)
    @ApiModelProperty("超级授权文件")
    private String saGrantFile;

    @JsonProperty(value = NEED_SP_GRANT_FILE)
    @ApiModelProperty("是否生成超级授权文件")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum needSpGrantFile;

    @JsonProperty(value = URGENT_RESAON)
    @ApiModelProperty("紧急操作原因")
    private String urgentResaon;

    @JsonProperty(value = OPERATION_PLAN_FILE)
    @ApiModelProperty("内部操作方案")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile operationPlanFile;

    @JsonProperty(value = CUSTOMER_OPERATION_SOLUTION)
    @ApiModelProperty("客户操作方案")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile customerOperationSolution;

    @JsonProperty(value = ATTACHMENT)
    @ApiModelProperty("附件")
    private List<MultiAttachmentFile> attachment;

    @JsonProperty(value = IS_CHECK)
    @ApiModelProperty("是否需要操作方案打卡")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isCheck;

    @JsonProperty(value = MODEL_PACKAGE)
    @ApiModelProperty("模型包")
    private Object modelPackage;

    @JsonProperty(value = REMARKS)
    @ApiModelProperty("备注说明")
    private String remarks;

    @JsonProperty(value = CHANGE_ORDER_ID)
    @ApiModelProperty("网络变更单ID")
    private String changeOrderId;

    @JsonProperty(value = CURRENT_STATUS)
    @ApiModelProperty("当前状态")
    private String currentStatus;

    @JsonProperty(value = BATCH_OPERATION_ACCOUNT)
    @ApiModelProperty("批次操作账号")
    private String batchOperationAccount;

    @JsonProperty(value = OPERATION_DESCRIPTION)
    @ApiModelProperty("操作描述")
    private String operationDescription;

    @JsonProperty(value = OPERATION_PLAN_DESC)
    @ApiModelProperty("操作方案描述")
    private String operationPlanDesc;

    @JsonProperty(value = PLAN_OPERATION_START_TIME)
    @ApiModelProperty("计划操作开始时间")
    private Date planOperationStartTime;

    @JsonProperty(value = PLAN_OPERATION_END_TIME)
    @ApiModelProperty("计划操作结束时间")
    private Date planOperationEndTime;

    @JsonProperty(value = SUSPENDED_TIME)
    @ApiModelProperty("挂起时间")
    private Date suspendedTime;

    @JsonProperty(value = OPERATION_TYPE)
    @ApiModelProperty("网络变更单-操作类型")
    private String operationType;

    @JsonProperty(value = OPERATION_TYPE_GROUP)
    @ApiModelProperty("操作类型分组")
    private String operationTypeGroup;

    @JsonProperty(value = IS_NEED_REMOTE_APPROVAL)
    @ApiModelProperty("是否生成超级授权文件")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNeedRemoteApproval;

    @JsonProperty(value = IS_CHANGE_UPLOAD_ELEMENT)
    @ApiModelProperty("网络变更单-是否上传网元清单")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isChangeUploadElement;

    @JsonProperty(value = APPROVAL_STATUS)
    @ApiModelProperty("审核状态")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum approvalStatus;

    @JsonProperty(value = STEP)
    @ApiModelProperty("步骤条")
    private Integer step;

    @JsonProperty(value = IS_URGENT_FILE)
    @ApiModelProperty("紧急操作附件")
    private Object isUrgenFile;

    @JsonProperty(value = IS_HIGH_RISK_INSTRUCTION)
    @ApiModelProperty("是否涉及高危指令")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isHighRiskInstruction;

    @ApiModelProperty("邮件抄送(发布通告)")
    @JsonProperty(value = EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> emailCc;

    /** 审核结果(操作取消审核) */
    @JsonProperty(value = OC_APPROVE_RESULT)
    private List<TextValuePair> ocApproveResult;

    /** 审核意见（操作取消审核） */
    @JsonProperty(value = OC_REVIEW_OPINION)
    private String ocReviewOpinion;

    /** 操作变更说明（操作取消审核） */
    @JsonProperty(value = OC_OPERATION_CHANGE_DESC)
    private String ocOperationChangeDesc;

    @ApiModelProperty("邮件抄送(操作取消审核)")
    @JsonProperty(value = OC_EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> ocEmailCc;

    @ApiModelProperty("邮件抄送(操作结果反馈)")
    @JsonProperty(value = RESULT_MAIL)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> resultMail;

    @ApiModelProperty("邮件抄送(操作结果确认)")
    @JsonProperty(value = APPROVAL_EMAIL)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> approvalEmail;

    @JsonProperty(value = CONFIRM_COPY)
    @ApiModelProperty("确认副本")
    private String confirmCopy;

    @JsonProperty(value = CLOCK_IN_STATE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private ClockInStateEnum clockInState;

    // BEGIN 反馈操作结果
    @JsonProperty(value = IS_CANCELED)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private BoolEnum isCanceled;

    @JsonProperty(value = IS_ROLLBACK_DONE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private BoolEnum isRollbackDone;

    @JsonProperty(value = IS_ONE_TIME_SUCCEED)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private BoolEnum isOneTimeSucceed;

    @JsonProperty(value = OPERATION_RESULT)
    private List<TextValuePair> operationResult;

    @JsonProperty(value = ACTUAL_OPERATION_START_TIME)
    private Date actualOperationStartTime;

    @JsonProperty(value = ACTUAL_OPERATION_END_TIME)
    private Date actualOperationEndTime;

    @JsonProperty(value = TEST_FINISH_TIME)
    private Date testFinishTime;
    // END 反馈操作结果

    // ==================== 扩展实体 - 代表处产品科科长 =====================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_REP_PROD_CHIEF)
    private List<TextValuePair> approveResultAdminRepProdChief;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_REP_PROD_CHIEF)
    private String approveOpinionAdminRepProdChief;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_REP_PROD_CHIEF)
    private List<Employee> approvedByAdminRepProdChief;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_REP_PROD_CHIEF)
    private Date approvedTimeAdminRepProdChief;

    // ======================= 扩展实体 - 办事处PD ========================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_OFFICE_PD)
    private List<TextValuePair> approveResultOfficePd;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_OFFICE_PD)
    private String approveOpinionOfficePd;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_OFFICE_PD)
    private List<Employee> approvedByOfficePd;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_OFFICE_PD)
    private Date approvedTimeOfficePd;

    // ====================== 扩展实体 - 操作计划审核 ======================
    @JsonProperty(value = APPROVE_RESULT_OPERATION_PLAN)
    @ApiModelProperty("操作计划审核审核结果")
    private List<TextValuePair> approveResultOperationPlan;

    @JsonProperty(value = APPROVE_OPINION_OPERATION_PLAN)
    @ApiModelProperty("操作计划审核审核意见")
    private String approveOpinionOperationPlan;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_OPERATION_PLAN)
    private List<Employee> approvedByOperationPlan;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_OPERATION_PLAN)
    private Date approvedTimeOperationPlan;

    @ApiModelProperty("审核意见（网络责任人确认）")
    @JsonProperty(value = APPROVAL_DESCRIPTION)
    private String approvalDescription;

    @ApiModelProperty("审核结论（网络责任人确认）")
    @JsonProperty(value = APPROVAL_RESULT)
    private List<TextValuePair> approvalResult;

    @JsonProperty(value = APPROVAL_NUM)
    @ApiModelProperty("审批次数")
    private Long approvalNum;

    @JsonProperty(value = NOTICE_COPY)
    @ApiModelProperty("发布通告副本")
    private String noticeCopy;

    @JsonProperty(value = CHANGE_PLAN_DESC_ZH)
    @ApiModelProperty("操作计划变更说明-中文")
    private String changePlanDescZh;

    @JsonProperty(value = CHANGE_PLAN_DESC_US)
    @ApiModelProperty("操作计划变更说明-英文")
    private String changePlanDescUs;

    @JsonProperty(value = ORGANIZATION_ID)
    @ApiModelProperty("网络变更代表处ID")
    private String organizationId;

    @JsonProperty(value = SOURCE)
    @ApiModelProperty("数据来源")
    private String source;

    /**
     * 驳回节点CODE
     */
    @JsonProperty(value = REJECT_NODE)
    private String rejectNode;

    /**
     * 驳回时审核结果，PASS：通过；REJECT：驳回
     */
    @JsonProperty(value = RESULT_REJECTION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveResultEnum resultRejection;

    /**
     * 驳回时审核意见
     */
    @JsonProperty(value = AUDIT_REJECTION)
    private String opinionRejection;

    /**
     * 驳回时审核人
     */
    @JsonProperty(value = PERSON_REJECTION)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> personRejection;

    /**
     * 驳回时审核意见附件
     */
    @JsonProperty(ATTACHMENT_REJECTION)
    private Object attachmentRejection;

    @JsonProperty(value = IS_ALL_COMPLETED)
    @ApiModelProperty("计划操作/升级对象（局点/站点、OMC）全部完成")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isAllCompleted;

    @JsonProperty(value = IS_ALL_REACH)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("计划实现的需求全部达成（对已完成操作/升级的局点/站点等对象）")
    private BoolEnum isAllReach;

    @JsonProperty(value = MAJOR_FAULTS_BEHIND)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("有无重大故障遗留")
    private BoolEnum majorFaultsBehind;

    @JsonProperty(value = NETWORK_INDICATORS)
    @ApiModelProperty("操作后（7天内）重要网络指标正常（无显著下降）")
    private Object networkIndicators;

    @JsonProperty(value = MULTIPLE_OPERATION_REASON)
    @ApiModelProperty("多次操作原因")
    private Object multipleOperationReason;

    @JsonProperty(value = FAILURE_REASON)
    @ApiModelProperty("失败原因分类")
    private Object failureReason;

    @JsonProperty(value = OPERATION_DOC_SCORE)
    @ApiModelProperty("操作文档指导性得分")
    private Object operationDocScore;

    @JsonProperty(value = CAN_OPERATION_TIME)
    @ApiModelProperty("操作文档可获取时间")
    private Object canOperationTime;

    @JsonProperty(value = VERSION_TIME)
    @ApiModelProperty("版本及配套文档可获取时间")
    private Object versionTime;

    @JsonProperty(value = FACT_INPUT_DAY)
    @ApiModelProperty("操作实际投入人天")
    private String factInputDay;

    @JsonProperty(value = OPERATION_SUMMARY)
    @ApiModelProperty("操作总结")
    private String operationSummary;

    @JsonProperty(value = RESULT_OPERATION_DATE)
    @ApiModelProperty("操作时间")
    private Date resultOperationDate;

    @JsonProperty(value = APP_MULTIPLE_OPERATION_REASON)
    @ApiModelProperty("多次操作原因")
    private Object appMultipleOperationReason;

    @JsonProperty(value = REMOTE_SUPPORT)
    @ApiModelProperty("远程支持情况")
    private Object remoteSupport;

    @JsonProperty(value = OPERATOR_EVALUATION)
    @ApiModelProperty("操作人员操作能力评估")
    private Object operatorEvaluation;

    @JsonProperty(value = OPERATION_MATURE)
    @ApiModelProperty("网络变更操作是否成熟")
    private Object operationMature;

    /** 操作变更说明 */
    @JsonProperty(value = OPERATION_CHANGE_DESC)
    private String operationChangeDesc;

    /** 审核说明附件 */
    @JsonProperty(value = HZF_BATCH_APPROVAL_CONFIRMATION_FILE)
    private Object approvalConfirmationAttachment;

    /** 操作结果审核标识 */
    @JsonProperty(value = RESULT_REVIEW_FLAG)
    private String resultReviewFlag;
}
