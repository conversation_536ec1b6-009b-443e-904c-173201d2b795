/**
 * 版权所有：版权所有(C) 2022，中兴通讯
 * 系统名称：iTech Cloud
 */
package com.zte.iccp.itech.extension.common.utils;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.SUCCESS;

/**
 *
 * <AUTHOR> 10335201
 * @date 2024-04-29 下午2:26
 **/
public class ConvertUtil {

    /**
     * 两位小数格式
     */
    private static final String TWO_DECIMAL_PLACES_PATTERN = "#0.00";
    /**
     * 文件大小单位：KB
     */
    private static final String FILE_SIZE_KB = "KB";

    /**
     * 文件大小单位：KB
     */
    private static final String FILE_SIZE_MB = "MB";
    /**
     * 文件大小单位：KB
     */
    private static final String FILE_SIZE_GB = "GB";

    /**
     * 默认文件大小
     */
    private static final String DEFAULT_FILE_SIZE = "0.00KB";

    private static final Double SIZE = 1024.00;

    /**
     * 获取列表对象
     * @param object
     * @return List<String>
     */
    public static List<String> getListObjectValue(Object object) {
        if (ObjectUtils.isEmpty(object)) {
            return Lists.newArrayList();
        }

        List<Map<String, Object>> objectInfoList = JSONObject.parseObject(JSONObject.toJSONString(object), List.class);
        List<String> valueList = Lists.newArrayList();
        for (Map<String, Object> info : objectInfoList) {
            valueList.add(info.getOrDefault(VALUE, EMPTY_STRING).toString());
        }

        return valueList;
    }

    /**
     * 获取下拉列表值
     * @param dropDownList
     * @return List<String>
     */
    public static List<String> getDropDownValue(List<Map<String, Object>> dropDownList) {
        if (CollectionUtils.isEmpty(dropDownList)) {
            return Lists.newArrayList();
        }

        // 下拉返回的值有空对象，需要先过滤
        List<Map<String, Object>> filterList = dropDownList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<TextValuePair> dropDownPairList = JsonUtils.parseArray(filterList, TextValuePair.class);
        return dropDownPairList.stream().map(TextValuePair::getValue).collect(Collectors.toList());
    }

    /**
     * 获取下拉填报内容 - 逗号分隔
     * @param textValuePairList
     * @return String
     */
    public static String getDropDownInformation(List<TextValuePair> textValuePairList, String language) {
        if (CollectionUtils.isEmpty(textValuePairList)) {
            return EMPTY_STRING;
        }

        // 1.根据语言环境拼接填报结果
        StringBuilder resultBuilder = new StringBuilder();
        for (TextValuePair textValuePair : textValuePairList) {
            resultBuilder.append(textValuePair.getTextByLanguage(language)).append(COMMA);
        }

        // 2.去除末尾逗号
        resultBuilder.deleteCharAt(resultBuilder.lastIndexOf(COMMA));

        return resultBuilder.toString();
    }

    /**
     * Object转String
     * @param textObject
     * @return
     */
    public static String getTextFirstValue(Object textObject) {
        if (!(textObject instanceof List)) {
            return EMPTY_STRING;
        }

        List<TextValuePair> textValuePairList = JsonUtils.parseArray(textObject, TextValuePair.class);
        return CollectionUtils.isEmpty(textValuePairList) ? EMPTY_STRING : textValuePairList.get(INTEGER_ZERO).getValue();
    }

    /**
     * 包装展示名称信息
     * @param entityNameMap  实体ID - 名称 Map
     * @param entityIdList   实体ID
     * @param separator      分隔符
     * @param removalFlag    是否去重
     * @return String
     */
    public static String convertNameInfo(Map<String, String> entityNameMap,
                                         List<String> entityIdList,
                                         String separator,
                                         boolean removalFlag) {
        if (CollectionUtils.isEmpty(entityIdList) || CollectionUtils.isEmpty(entityNameMap)) {
            return EMPTY_STRING;
        }

        Set<String> nameSet = new HashSet<>();
        StringBuilder builder = new StringBuilder();
        for (String entityId : entityIdList) {
            String entityName = entityNameMap.get(entityId);
            if (!StringUtils.hasText(entityName)) {
                continue;
            }

            if (removalFlag && nameSet.contains(entityName)) {
                continue;
            }

            builder.append(entityName).append(separator);
            nameSet.add(entityName);
        }

        if (builder.length() > INTEGER_ZERO) {
            builder.deleteCharAt(builder.lastIndexOf(separator));
        }

        return builder.toString();
    }

    public static String getFileSize(Long fileSize) {
        DecimalFormat decimalFormat = new DecimalFormat(TWO_DECIMAL_PLACES_PATTERN);
        if (null != fileSize) {
            double size;
            size = fileSize.doubleValue() / SIZE;
            if (size < SIZE) {
                return decimalFormat.format(size) + FILE_SIZE_KB;
            }
            size = size / SIZE;
            if (size < SIZE) {
                return decimalFormat.format(size) + FILE_SIZE_MB;
            }
            size = size / SIZE;
            return decimalFormat.format(size) + FILE_SIZE_GB;
        }
        return DEFAULT_FILE_SIZE;
    }

    /**
     * 包装对象 - 附件
     * @param fileKey
     * @param filename
     * @return AttachmentFile
     */
    public static AttachmentFile convertAttachmentFile(String fileKey, String filename) {
        AttachmentFile attachmentFile = new AttachmentFile();

        attachmentFile.setDownloadUrl(CommonConstants.FILE_DOWNLOAD_URL_PREFIX + fileKey);
        attachmentFile.setFileKey(fileKey);
        attachmentFile.setId(fileKey);
        attachmentFile.setName(filename);
        attachmentFile.setPercentage(CommonConstants.INTEGER_ZERO);
        attachmentFile.setStatus(SUCCESS);

        return attachmentFile;
    }
}
