package com.zte.iccp.itech.extension.domain.model.excel;

import com.zte.iccp.itech.extension.domain.constant.ApproverConfigConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import lombok.Data;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 审批配置导入实体
 */
@Data
public class ApproverConfigImportDTO {
    //======= 业务字段 =======//
    private String entityId;
    private String approvalPerson;
    private String approvalGroup;
    private String prodOperationTeamIdPath;
    private String prodLineIdPath;
    private String prodMainCategoryIdPath;
    private String prodSubCategoryIdPath;
    private String productModelId;
    private String salesDeptIdPath;
    private String organizationRegionIdPath;
    private String responsibleDeptId;
    private String operationTypeMulti;
    private String role;
    private String operator;
    private String isGovEnt;
    private String selectfieldLogicalNe;

    //======= 元数据字段 =======//
    private int rowNum;
    private boolean hasError;
    private List<String> errorMessages = new ArrayList<>();

    //======= 字段白名单 =======//
    private static final Set<String> FIELD_WHITELIST = new HashSet<>(Arrays.asList(
            "entityId", "approvalPerson", "approvalGroup",
            "prodOperationTeamIdPath", "prodLineIdPath", "prodMainCategoryIdPath",
            "prodSubCategoryIdPath", "productModelId", "salesDeptIdPath",
            "organizationRegionIdPath", "responsibleDeptId", "operationType",
            "role", "operator", "isGovEnt", "selectfieldLogicalNe", "operationTypeMulti"
    ));

    /**
     * 从Excel行数据构建模型
     */
    public static ApproverConfigImportDTO buildFromExcel (
            Map<Integer, String> rowData,
            Map<Integer, String> headerMap,
            int excelRowNum) {
        ApproverConfigImportDTO dto = new ApproverConfigImportDTO();
        dto.setRowNum(excelRowNum + 1);

        // 1. 构建字段值映射表
        Map<String, String> fieldValues = new HashMap<>();
        for (Map.Entry<Integer, String> header : headerMap.entrySet()) {
            int colIdx = header.getKey();
            String colName = normalizeHeader(header.getValue());
            String cellValue = rowData.getOrDefault(colIdx, CommonConstants.EMPTY_STRING);

            if (FIELD_WHITELIST.contains(colName)) {
                fieldValues.put(colName, cellValue);
            }
        }

        // 3. 反射填充所有字段
        for (Map.Entry<String, String> entry : fieldValues.entrySet()) {
            String fieldName = entry.getKey();
            String value = entry.getValue();

            try {
                Field field = dto.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                field.set(dto, value);
            } catch (Exception e) {
                dto.addError(ApproverConfigConstants.ERROR_INVALID_FIELD_MAPPING + fieldName);
            }
        }
        return dto;
    }

    /**
     * 标准化列头命名
     * 示例：approval_person -> approvalPerson
     */
    private static String normalizeHeader(String rawHeader) {
        return Arrays.stream(rawHeader.split("[ _-]"))
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
                .reduce((a, b) -> a + b)
                .orElse("")
                .replaceFirst("^.", String.valueOf(rawHeader.charAt(0)).toLowerCase());
    }

    /**
     * 添加错误信息
     */
    private void addError(String message) {
        this.hasError = true;
        this.errorMessages.add(ApproverConfigConstants.ERROR_ROW_PREFIX + rowNum + ApproverConfigConstants.ERROR_ROW_SUFFIX + message);
    }
}