package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.subentity.SubconBatchSummary;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class BatchSummaryAbility {

    /**
     * 检索批次概要信息 - PID
     * @param changeOrderId
     * @param entityClass
     * @return List<BatchSummary>
     */
    public static List<BatchSummary> listBatchSummary(String changeOrderId, Class<? extends BaseSubEntity> entityClass) {
        if (!StringUtils.hasText(changeOrderId)) {
            return Lists.newArrayList();
        }

        if (!BatchSummary.class.equals(entityClass)
                && !SubconBatchSummary.class.equals(entityClass)) {
            return Lists.newArrayList();
        }

        return QueryDataHelper.query(entityClass, Lists.newArrayList(), changeOrderId);
    }

    /**
     * 检索批次概要信息 - PIDS
     * @param changeOrderIds
     * @return List<BatchSummary>
     */
    public static List<BatchSummary> listBatchSummary(List<String> changeOrderIds, List<String> fields ) {
        if (CollectionUtils.isEmpty(changeOrderIds)) {
            return Lists.newArrayList();
        }

        return QueryDataHelper.query(BatchSummary.class, fields, changeOrderIds,Lists.newArrayList());
    }

    public static void initCreate(List<String> changeOrderIds, String mainChangeOrderId) {
        List<BatchSummary> orgBatchSummaries = QueryDataHelper.query(BatchSummary.class, new ArrayList<>(), mainChangeOrderId);
        List<BatchSummary> batchSummaries = new ArrayList<>();
        BatchSummary org = orgBatchSummaries.stream()
                .filter(item -> !CollectionUtils.isEmpty(item.getBatchNo())
                        && TextValuePairHelper.getValueList(item.getBatchNo()).contains(CommonConstants.STR_ONE))
                .findFirst()
                .orElse(new BatchSummary());

        changeOrderIds.forEach(id -> {
            BatchSummary batchSummary = new BatchSummary();
            batchSummary.setPid(id);
            batchSummary.setBatchNo(org.getBatchNo());
            batchSummary.setPlanOperationStartTime(org.getPlanOperationStartTime());
            batchSummary.setPlanOperationEndTime(org.getPlanOperationEndTime());
            batchSummary.setNetworkElementCount(org.getNetworkElementCount());
            batchSummary.setOperationDescription(org.getOperationDescription());
            batchSummaries.add(batchSummary);
        });
        SaveDataHelper.batchCreate(batchSummaries);
    }
}
