package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/08/06
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OperationLogTypeEnum implements SingletonTextValuePairsProvider {
    /** 下载超级授权文件 */
    DOWNLOAD_SA_GRANT_FILE("下载超级授权文件", "Download super authorization file"),

    /** 打卡呼叫 */
    CLOCK_IN_CALL("打卡呼叫", "clock in call"),
    ;

    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }
}
