package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/11
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class LangUtils {

    private static final String ZH_CHAR_PATTERN_VALUE = "[\\u4e00-\\u9fa5]";

    private static final Pattern ZH_CHAR_PATTERN
            = Pattern.compile(ZH_CHAR_PATTERN_VALUE);

    private static final Pattern ALL_ZH_CHAR_PATTERN
            = Pattern.compile(String.format("^%s+$", ZH_CHAR_PATTERN_VALUE));

    /** 名称中是否没有包含中文字符 */
    public static boolean containsZhChar(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }

        return ZH_CHAR_PATTERN.matcher(text).find();
    }

    public static boolean allZhChar(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }

        return ALL_ZH_CHAR_PATTERN.matcher(text).find();
    }

    public static String get(String zhCn, String enUs) {
        return get(ContextHelper.getLangId(), zhCn, enUs);
    }

    public static String get(String langId, String zhCn, String enUs) {
        return ZH_CN.equals(langId) ? zhCn : enUs;
    }
}