package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.ability.changeorder.ApprovalAbility;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> jiangjiawen
 * @date 2025/7/1
 */
@Getter
public enum ApprovalRejectionEnum {

    /** 取消操作审核 */
    OPERATION_CANCEL_REVIEW(ApproveNodeEnum.OPERATION_CANCEL_REVIEW, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.OC_APPROVE_RESULT,BatchTaskFieldConsts.OC_REVIEW_OPINION,"REJECT"),


    /** 待发通告 - 申请人执行操作计划 */
    PENDING_NOTIFICATION(ApproveNodeEnum.PENDING_NOTIFICATION, AssignmentTypeEnum.NETWORK_CHANGE_BATCH),

    /**
     * 远程中心负责人审核
     */
    BATCH_REMOTE_CENTER_OWNER(ApproveNodeEnum.REMOTE_CENTER_OWNER, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_TD_NET_DEPT_APP_SOLU, BatchTaskFieldConsts.APPROVE_OPINION_TD_NET_DEPT_APP_SOLU, "TERMINATE"),

    /**
     * 操作计划审核
     */
    BATCH_CHANGED_BY_REP_PROD_CHIEF(ApproveNodeEnum.CHANGED_BY_REP_PROD_CHIEF, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_REP_PROD_CHIEF, BatchTaskFieldConsts.APPROVE_OPINION_REP_PROD_CHIEF,"TERMINATE"),

    /**
     * 行政审核_办事处产品科长
     */
    BATCH_ADMIN_REP_PROD_CHIEF(ApproveNodeEnum.ADMIN_REP_PROD_CHIEF, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_PROD_CHIEF, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_PROD_CHIEF,"TERMINATE"),

    /**
     * 行政审核_网络服务处总监
     */
    BATCH_ADMIN_NET_PROD_DIR(ApproveNodeEnum.ADMIN_NET_PROD_DIR, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_PROD_DIRECTOR, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_PROD_DIRECTOR,"TERMINATE"),

    /**
     * 行政审核_电信服务总监
     */
    BATCH_ADMIN_DIR_TELE_SERVICE(ApproveNodeEnum.ADMIN_DIR_TELE_SERVICE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME,"TERMINATE"),

    /**
     * 行政审核_办事处副经理
     */
    BATCH_ADMIN_REP_DEPUTY_MNG(ApproveNodeEnum.ADMIN_REP_DEPUTY_MNG, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_DEPUTY_MANAGER, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_DEPUTY_MANAGER,"TERMINATE"),

    /**
     * 行政审核_网络服务处经理
     */
    BATCH_ADMIN_NET_DEPT_MNG(ApproveNodeEnum.ADMIN_NET_DEPT_MNG, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_DEPT_MNG, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_DEPT_MNG,"TERMINATE"),

    /**
     * 行政审核_网络服务部部长
     */
    BATCH_ADMIN_NETSERVICE_LV4(ApproveNodeEnum.ADMIN_NETSERVICE_LV4, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_SERVCIE_LV4, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_SERVCIE_LV4,"TERMINATE"),

    /**
     * 行政审核_网研院院长
     */
    BATCH_ADMIN_NETSERVICE_LV3(ApproveNodeEnum.ADMIN_NETSERVICE_LV3, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_SERVCIE_LV3, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_SERVCIE_LV3, "TERMINATE"),

    /**
     * 行政审核_研发中心主任
     */
    BATCH_ADMIN_RD_DEPT_LV3(ApproveNodeEnum.ADMIN_RD_DEPT_LV3, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_RD_DEPT_LV3, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_RD_DEPT_LV3,"TERMINATE"),

    /**
     * 行政审核_网络服务处总经理
     */
    BATCH_ADMIN_ENG_SERVICE3(ApproveNodeEnum.ADMIN_ENG_SERVICE3, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_ENG_SERVICE3_LEADER, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_ENG_SERVICE3_LEADER_FIELD_NAME,"TERMINATE"),

    // 国际行政会签单独处理

    /**
     * 待反馈操作结果
     */
    RESULT_TOBE_BACK(ApproveNodeEnum.RESULT_TOBE_BACK, AssignmentTypeEnum.NETWORK_CHANGE_BATCH),

    /**
     * 技术交付部/网络处审核 - 操作结果审核
     */
    BATCH_TD_NET_DEPT_APPROVE(ApproveNodeEnum.TD_NET_DEPT_APPROVE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVAL_RESULT, BatchTaskFieldConsts.APPROVAL_DESCRIPTION, "TERMINATE","getApprovalDescAttachment"),

    /**
     * 网服部审核人 - 操作结果审核
     */
    BATCH_NET_SERVICE_DEPT_APP(ApproveNodeEnum.NET_SERVICE_DEPT_APP, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVAL_RESULT, BatchTaskFieldConsts.APPROVAL_DESCRIPTION, "TERMINATE", "getApprovalDescAttachment"),


    /********* 合作方批次  **********/
    /**
     * 取消操作审核
     */
    HZF_BATCH_OPERATION_CANCEL_REVIEW(PartnerApproveNodeEnum.OPERATION_CANCEL_REVIEW, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.OC_APPROVE_RESULT, BatchTaskFieldConsts.OC_REVIEW_OPINION,"REJECT"),

    /**
     * 待发通告 - 申请人执行操作计划
     */
    HZF_BATCH_PENDING_NOTIFICATION(PartnerApproveNodeEnum.PENDING_NOTIFICATION, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH),

    /**
     * 行政审批_办事处PD
     */
    HZF_BATCH_PARTNER_OFFICE_PD_APPROVAL(PartnerApproveNodeEnum.PARTNER_OFFICE_PD_APPROVAL, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_OFFICE_PD, BatchTaskFieldConsts.APPROVE_OPINION_OFFICE_PD, "TERMINATE"),

    /**
     * 操作计划审核
     */
    HZF_BATCH_PARTNER_OPERATION_PLAN_APPROVAL(PartnerApproveNodeEnum.PARTNER_OPERATION_PLAN_APPROVAL, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_OPERATION_PLAN, BatchTaskFieldConsts.APPROVE_OPINION_OPERATION_PLAN,"TERMINATE"),

    /**
     * 行政审批_办事处产品科长
     */
    HZF_BATCH_PARTNER_OFFICE_PROD_CHIEF_APPROVAL(PartnerApproveNodeEnum.PARTNER_OFFICE_PROD_CHIEF_APPROVAL, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_PROD_CHIEF, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_PROD_CHIEF,"TERMINATE"),

    /**
     * 待反馈操作结果
     */
    HZF_BATCH_RESULT_TOBE_BACK(PartnerApproveNodeEnum.RESULT_TOBE_BACK, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH),

    /**
     * 网服部审核人 - 操作结果审核
     */
    HZF_BATCH_PARTNER_NET_OWNER_APPROVAL(PartnerApproveNodeEnum.PARTNER_NET_OWNER_APPROVAL, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            BatchTaskFieldConsts.APPROVAL_RESULT, BatchTaskFieldConsts.APPROVAL_DESCRIPTION, "TERMINATE", "getApprovalConfirmationAttachment"),
    ;

    /**
     * 审批节点
     */
    private final SingletonTextValuePairsProvider approveNodeEnum;

    /**
     * 任务类型
     */
    private final AssignmentTypeEnum assignmentTypeEnum;

    /**
     * 审批结果字段
     */
    private String approveResultField;

    /**
     * 审批意见字段
     */
    private String approvalOpinionField;

    /**
     * 驳回结果真实的值
     */
    private String rejectValue;

    /**
     * 附件字段
     */
    private String attachmentField;

    ApprovalRejectionEnum(SingletonTextValuePairsProvider approveNodeEnum,
                          AssignmentTypeEnum assignmentTypeEnum,
                          String approveResultField,
                          String approvalOpinionField,
                          String rejectValue,
                          String attachmentField) {
        this.approveNodeEnum = approveNodeEnum;
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.approveResultField = approveResultField;
        this.approvalOpinionField = approvalOpinionField;
        this.rejectValue = rejectValue;
        this.attachmentField = attachmentField;
    }

    ApprovalRejectionEnum(SingletonTextValuePairsProvider approveNodeEnum,
                          AssignmentTypeEnum assignmentTypeEnum,
                          String approveResultField,
                          String approvalOpinionField,
                          String rejectValue) {
        this.approveNodeEnum = approveNodeEnum;
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.approveResultField = approveResultField;
        this.approvalOpinionField = approvalOpinionField;
        this.rejectValue = rejectValue;
    }

    ApprovalRejectionEnum(SingletonTextValuePairsProvider approveNodeEnum, AssignmentTypeEnum assignmentTypeEnum) {
        this.approveNodeEnum = approveNodeEnum;
        this.assignmentTypeEnum = assignmentTypeEnum;
    }

    public static ApprovalRejectionEnum getApprovalRejectionEnum(String flowCode, String extendedCode) {
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(flowCode);
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromFlowCode(approveFlowEnum);

        SingletonTextValuePairsProvider approveNodeName = ApprovalAbility.getApproveNodeName(approveFlowEnum.getFlowEntity(), extendedCode);

        return Arrays.stream(ApprovalRejectionEnum.values())
                .filter(approveNode -> approveNodeName.equals(approveNode.getApproveNodeEnum())
                        && assignmentTypeEnum == approveNode.getAssignmentTypeEnum())
                .findFirst()
                .orElse(null);
    }
}
