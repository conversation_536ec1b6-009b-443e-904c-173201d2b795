package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.ArrayList;
import java.util.Arrays;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.MULTI_PROD_GUARANTEE;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * <AUTHOR>
 * @create 2024/12/21 上午9:22
 */
public class UpdateGuarantyBatchPlgin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String businessId = body.getBusinessId();
        BatchTask batchTask = BatchTaskAbility.get(businessId, new ArrayList<>());
        ChangeOrder changeOrder = ChangeOrderAbility.get(batchTask.getChangeOrderId(), Arrays.asList(ID, SOURCE,MULTI_PROD_GUARANTEE));
        // 如果属于保障单主单，更新生成保障批次数据
        if (changeOrder == null || !changeOrder.isPrimaryGuarantyOrder()) {
            return false;
        }

        BatchTaskAbility.updateGuarantyBatch(batchTask);
        return false;
    }
}
