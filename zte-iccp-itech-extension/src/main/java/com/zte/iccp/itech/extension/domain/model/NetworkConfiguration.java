package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.NetworkConfigurationFieldConsts.*;

@ApiModel("网络配置属性")
@Setter
@Getter
@BaseEntity.Info("network_configuration")
public class NetworkConfiguration extends BaseEntity {
    @JsonProperty(value = NETWORK_ID)
    @ApiModelProperty("网络Id")
    private String networkId;

    @JsonProperty(value = NETWORK_ATTRIBUTE)
    @ApiModelProperty("网络属性")
    private List<TextValuePair> networkAttribute;

    @JsonProperty(value = GRADE_SCORE)
    @ApiModelProperty("等级分值")
    private List<TextValuePair> gradeScore;
}
