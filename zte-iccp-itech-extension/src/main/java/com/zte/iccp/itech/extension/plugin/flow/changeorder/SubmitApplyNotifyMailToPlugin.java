package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.SubmitApplyMailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;



/**
 * 提交申请-知会通知
 * 1.提交审批后置节点操作
 * 收件人：申请单中操作人员列表内所有人员和群组、申请单中选择的代表处方案审核人、主管经理/副经理、申请单中用户维护的抄送人员
 * 示例：【网络变更操作知会】王焱华10282740审核通过中国联通云南省分公司_4&5G_UME网管MR&CDT&MOS采集任务（紧急）
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/18
 */
public class SubmitApplyNotifyMailToPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        SubmitApplyMailPlugin plugin = new SubmitApplyMailPlugin();
        return plugin.anyTrigger(body,out);
    }
}
