package com.zte.iccp.itech.extension.plugin.flow.changeorder.subcontractor;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.PersonnelAbilty;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.vo.ConfirmCopy;
import com.zte.iccp.itech.extension.domain.model.vo.OperatorCopy;
import com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask.CalculateApprovalNodePlugin;
import com.zte.iccp.itech.extension.plugin.operation.changeorder.batchtask.ChangeCommitOperationPlugin;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATE_PERSON;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATOR_ROLE;
import static com.zte.iccp.itech.extension.domain.constant.entity.SatisfactionResponsiblePersonConsts.ORGANIZATION_ID;

/**
 * 分包商批次任务，
 *
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class SubcontractorFirstCommitPlugin extends BaseFlowOperationPlugin {

    @Override
    public boolean beforeValidate(ExecuteEvent executeEvent) {
        IDataModel dataModel = getModel();
        IFormView formView = getView();

        // 1.获取 我已知晓 组件结果
        String confirmInfo = PropertyValueConvertUtil.getString(dataModel.getValue(ChangeOrderFieldConsts.AWARE_SUBMISSION_HIDDEN));
        if (!BoolEnum.Y.getValue().equals(confirmInfo)) {
            // 未确认我已知晓, 弹窗展示案例
            dataModel.setValue(ChangeOrderFieldConsts.AWARE_SUBMISSION, BoolEnum.Y);
            return false;
        }

        //时间校验
        if (!BatchTaskAbility.checkTime(dataModel, formView)) {
            return false;
        }

        //人员积分校验
        String orgId = PropertyValueConvertUtil.getString(getModel().getValue(ORGANIZATION_ID));
        IDataEntityCollection operatorDataEntity = getModel().getEntryRowEntities(EntityHelper.getEntityId(SubcontractorBatchOperator.class));
        String msg = PersonnelAbilty.commitPersonCheck(orgId, operatorDataEntity, OPERATOR_ROLE, OPERATE_PERSON);
        if (StringUtils.hasText(msg)) {
            getView().showMessage(msg, MsgType.ERROR);
            return false;
        }

        // 操作人员校验
        boolean operatorFlag = BatchTaskAbility.checkOperators(dataModel, formView, SubcontractorBatchOperator.class);
        if (operatorFlag) {
            // 重新初始为 N, 支持后续驳回后重新弹窗
            dataModel.setValue(ChangeOrderFieldConsts.AWARE_SUBMISSION_HIDDEN, BoolEnum.N.getValue());
            dataModel.setValue(ChangeOrderFieldConsts.AWARE_SUBMISSION, new OptionsBuilder().build());
        }

        return operatorFlag;
    }

    @Override
    public void beforeOperate(ExecuteEvent executeEvent) {
        getModel().setValue(APPROVAL_STATUS, BoolEnum.Y.getPropValue());
        getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.NOTIFICATION_UNDER_REVIEW.getValue());
        // 2计算是否需要行政领导审批
        String batchId = getModel().getRootDataEntity().getPkValue().toString();
        SubcontractorBatchTask batchTask = BatchTaskAbility.getSub(batchId, Arrays.asList(ID, CONFIRM_COPY, CHANGE_ORDER_ID));
        ConfirmCopy confirmCopy = JsonUtils.parseObject(batchTask.getConfirmCopy(), ConfirmCopy.class);
        if (confirmCopy == null) {
            return;
        }

        // 是否紧急操作
        String isUrgent = TextValuePairHelper.getValue(getModel().getValue(URGENT_FLAG));
        BoolEnum urgentFlag = confirmCopy.getUrgentFlag();
        // 老单据是否，新单据是 需要重新走行政审核
        if (BoolEnum.N == urgentFlag && BoolEnum.Y.getValue().equals(isUrgent)) {
            getModel().setValue(URGENT_FLAG_TOO, BoolEnum.Y.getPropValue());
        } else {
            getModel().setValue(URGENT_FLAG_TOO, BoolEnum.N.getPropValue());
        }

        // 操作人员是否变更
        operationChangeExt(batchTask.getChangeOrderId(), confirmCopy);
        // 紧急操作标志
        ChangeCommitOperationPlugin plugin = new ChangeCommitOperationPlugin();
        plugin.defineFlagData(getModel());

        // 修改流程变量
        Map<String, Object> variables = MapUtils.newHashMap();
        // 获取高负荷值
        if (FlowHelper.getCustomizeFlowVersion(batchId) > 1) {
            CalculateApprovalNodePlugin calculateApprovalNodePlugin = new CalculateApprovalNodePlugin();
            calculateApprovalNodePlugin.handleHighLoadInterception(getModel());
            calculateApprovalNodePlugin.highLoadIntercepFlowVariantSet(getModel(), batchId, variables, true);
        }

        FlowHelper.changeFlowParams(batchId, variables, ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW);
    }

    /**
     * 人员变更（国际场景下不计算默认为N）
     *
     * @param changeOrderId 网络变更单id
     * @param confirmCopy confirmCopy
     */
    private void operationChangeExt(String changeOrderId, ConfirmCopy confirmCopy) {
        SubcontractorChangeOrder changeOrder = QueryDataHelper.get(
                SubcontractorChangeOrder.class, Lists.newArrayList(ID, ORGANIZATION_ID), changeOrderId);
        if (DeptTypeEnum.INTER == ChangeOrderAbility.getDeptType(changeOrder)) {
            getModel().setValue(PERSON_CHANGE_FLAG, BoolEnum.N.getPropValue());
            return;
        }

        operationChange(confirmCopy);
    }

    private void operationChange( ConfirmCopy confirmCopy) {
        // 获取表单填写 操作人员数据
        IDataEntityCollection dataEntityCollection = getModel().getEntryRowEntities(EntityHelper.getEntityId(SubcontractorBatchOperator.class));
        if (dataEntityCollection == null || dataEntityCollection.size() == CommonConstants.INTEGER_ONE) {
            getModel().setValue(CURRENT_STATUS, AssignmentStatusEnum.OPERATION_EXECUTION.getValue());
            return;
        }

        List<String> batchOperatorList = new ArrayList<>();
        for (int i = CommonConstants.INTEGER_ZERO; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            String operatorRole = TextValuePairHelper.getValue(dynamicDataEntity.get(OperatorFieldConsts.OPERATOR_ROLE));
            List<SingleEmployee> employees = JsonUtils.parseArray(dynamicDataEntity.get(OperatorFieldConsts.OPERATE_PERSON), SingleEmployee.class);
            batchOperatorList.add(String.join(CommonConstants.UNDER_SCORE, operatorRole, employees.get(0).getEmpUIID()));
        }
        // 查询历史操作操作人员
        if (confirmCopy == null || CollectionUtils.isEmpty(confirmCopy.getOperators())) {
            return;
        }
        List<OperatorCopy> hisOperators = confirmCopy.getOperators();

        boolean isOperationChange;
        if (hisOperators.size() != batchOperatorList.size()) {
            isOperationChange = true;
        } else {
            List<String> changeOperatorList = Lists.newArrayList();
            hisOperators.forEach(item ->
                    changeOperatorList.add(String.join(CommonConstants.UNDER_SCORE,
                            item.getOperatorRole() == null ? CommonConstants.EMPTY_STRING : item.getOperatorRole().getValue(),
                            item.getOperatePerson() == null ? CommonConstants.EMPTY_STRING : item.getOperatePerson()))
            );

            //对比 表单填写的操作人员数据和网络变更单人员数据是否一致
            isOperationChange = !batchOperatorList.stream().allMatch(changeOperatorList::contains);
        }
        if (isOperationChange) {
            // 给表单人员变更 字段赋值
            getModel().setValue(PERSON_CHANGE_FLAG, BoolEnum.Y.getPropValue());
        }
    }
}
