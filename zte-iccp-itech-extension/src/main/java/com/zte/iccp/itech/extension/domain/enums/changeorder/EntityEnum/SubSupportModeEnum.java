package com.zte.iccp.itech.extension.domain.enums.changeorder.EntityEnum;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 分包商 支持方式
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum SubSupportModeEnum implements SingletonTextValuePairsProvider {
    /**
     * 现场支持
     */
    ONSITE_SUPPORT("ONSITE_SUPPORT", "现场支持", "On Site Support"),

    /**
     * 远程支持
     */
    REMOTE_SUPPORT("REMOTE_SUPPORT", "远程支持", "Remote Support"),
    ;

    /**
     * 编码
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;

    public static SubSupportModeEnum fromValue(String value) {
        for (SubSupportModeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new IllegalArgumentException();
    }
}
