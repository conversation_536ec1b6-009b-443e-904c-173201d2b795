package com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 计划操作时间
 */

@Getter
@Setter
@NoArgsConstructor
public class PlanOperateVO {
    // 批次号
    private String batchNo;

    // 计划开始时间 2024-08-27 01:00
    private Date planStartDate;

    // 计划结束时间 2024-08-27 05:00
    private Date planEndDate;
}
