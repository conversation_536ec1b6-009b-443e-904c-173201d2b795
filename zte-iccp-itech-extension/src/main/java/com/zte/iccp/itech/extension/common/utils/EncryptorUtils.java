package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Hex;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/28
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class EncryptorUtils {

    private static final String HMACSHA256 = "HmacSHA256";

    private static final String MD5 = "MD5";

    private static final String SHA256 = "SHA-256";

    private static final String SHA256_FORMAT = "%02x";

    @SneakyThrows
    public static String hmacSha256(String content) {
        Mac mac = Mac.getInstance(HMACSHA256);
        mac.init(new SecretKeySpec(content.getBytes(StandardCharsets.UTF_8), HMACSHA256));
        return new String(Hex.encodeHex(mac.doFinal()));
    }

    @SneakyThrows
    public static String hmacSha256(String content, String key) {
        content = content == null ? "" : content;

        byte[] data = key.getBytes(StandardCharsets.UTF_8);

        SecretKey secretKey = new SecretKeySpec(data, HMACSHA256);
        Mac mac = Mac.getInstance(HMACSHA256);
        mac.init(secretKey);

        byte[] text = content.getBytes(StandardCharsets.UTF_8);
        return new String(Hex.encodeHex(mac.doFinal(text)));
    }

    /**
     * MD5 加盐
     * @param code
     * @return String
     */
    @SneakyThrows
    public static String getMD5Code(String code) {
        if (!StringUtils.hasText(code)) {
            return EMPTY_STRING;
        }

        MessageDigest messageDigest = MessageDigest.getInstance(MD5);
        byte[] md5ByteArray = messageDigest.digest(code.getBytes());

        StringBuilder stringBuilder = new StringBuilder();
        for (byte byteCode : md5ByteArray) {
            stringBuilder.append(byteToArrayString(byteCode));
        }

        return stringBuilder.toString();
    }

    @SneakyThrows
    public static String sha256(String content) {
        MessageDigest digest = MessageDigest.getInstance(SHA256);
        byte[] hash = digest.digest(content.getBytes(StandardCharsets.UTF_8));
        StringBuilder result = new StringBuilder();
        for (byte b : hash) {
            result.append(String.format(SHA256_FORMAT, b));
        }
        return result.toString();
    }

    private static String byteToArrayString(byte byteCode) {
        int iRet = byteCode;
        if (byteCode < 0) {
            iRet = byteCode + 256;
        }

        int iD1 = iRet / 16;
        int iD2 = iRet % 16;
        String[] strDigits = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};
        return strDigits[iD1] + strDigits[iD2];
    }
}
