package com.zte.iccp.itech.extension.domain.enums.partnerchangeorder;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.plugin.form.changeorder.afterclick.AiRefreshPlugin;
import com.zte.iccp.itech.extension.plugin.form.changeorder.afterclick.ProductModelAfterClickPlugin;
import com.zte.iccp.itech.extension.plugin.form.partnerchangeorder.afterclick.PartnerAfterClickBaseFormPlugin;
import com.zte.iccp.itech.extension.plugin.form.partnerchangeorder.afterclick.PartnerSolutionSearchPluginClick;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PartnerAfterClickPluginEnum {

    /**
     * 操作对象-产品型号，跳转自定义页面
     */
    OPERATION_OBJECT_PRODUCT_MODEL_QUERY(Lists.newArrayList(COMPONENT_OPERATION_OBJECT_PRODUCT_MODEL_ID_CID), new ProductModelAfterClickPlugin()),

    /**
     * 按钮-查询客户/内部操作方案列表，跳转内部页面（标准方案-已发布-列表）
     */
    BUTTON_QUERY_SOLUTION_LIST(Lists.newArrayList(INNER_SOLUTION_BUTTON,CUSTOMER_SOLUTION_BUTTON), new PartnerSolutionSearchPluginClick()),

    /**
     * ai回调刷新按钮
     */
    AI_CALLBACK_REFRESH(Lists.newArrayList(AI_CALLBACK_REFRESH_BUTTON, CUSTOMER_AI_CALLBACK_REFRESH_BUTTON), new AiRefreshPlugin()),

    ;


    // afterClick插件的cid要配置布局组件CID
    private final List<String> cids;

    private final PartnerAfterClickBaseFormPlugin partnerAfterClickBaseFormPlugin;

    public static List<PartnerAfterClickBaseFormPlugin> getAfterClickEventPlugins(String cid) {
        List<PartnerAfterClickBaseFormPlugin> afterClickBaseFormPlugins = new ArrayList<>();
        for (PartnerAfterClickPluginEnum pluginEnum : PartnerAfterClickPluginEnum.values()) {
            if (pluginEnum.getCids().contains(cid)) {
                afterClickBaseFormPlugins.add(pluginEnum.getPartnerAfterClickBaseFormPlugin());
            }
        }
        return afterClickBaseFormPlugins;
    }
}
