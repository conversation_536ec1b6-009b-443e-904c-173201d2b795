package com.zte.iccp.itech.extension.domain.constant;

/**
 * <AUTHOR>
 * @create 2024/7/4 下午9:37
 */
public class ApprovalConstants {

    /* 批次任务 系统审批节点*/
    public static final String BATCH_SYSTEM_NODE = "Activity_0tw2e7s";

    /* 分包商 批次任务 系统审批节点*/
    public static final String SUB_BATCH_SYSTEM_NODE = "Activity_0tnj0ce";

    /* 批次任务 待发通告节点*/
    public static final String BATCH_PENDING_NOTIFICATION_NODE = "Activity_1gf5nd5";

    /* 批次任务 反馈操作结果节点*/
    public static final String BATCH_RESULT_NODE = "Activity_18l0hem";

    /* 批次任务 反馈操作结果节点*/
    public static final String SUB_BATCH_RESULT_NODE = "Activity_1xxa7ev";

    /* 网络变更任务 流程编码*/
    public static final String CHANGE_ORDER_COMP_FLOW = "CHANGE_ORDER_COMP_FLOW";

    /* 批次任务 流程编码*/
    public static final String BATCH_TASK_FLOW = "BATCH_TASK_FLOW";

    /* 分包商网络变更任务 流程编码*/
    public static final String SUBCONTRACTOR_OC_FLOW = "SUBCONTRACTOR_OC_FLOW";

    /* 分包商 批次任务 流程编码*/
    public static final String SUBCONTRACTOR_TASK_FLOW = "SUBCONTRACTOR_TASK_FLOW";

    /* 故障管理任务-主流程 流程编码*/
    public static final String FAULT_MANAGE_FLOW = "FAULT_MANAGE_FLOW";

    /* 权限在线申请 流程编码*/
    public static final String PERMISSION_APPLICATION = "PERMISSION_APPLICATION";

    /**
     * 默认审批人
     */
    public static final String DEFAULT_APPROVER_KEY = "default.approver.key";

    /**
     * 系统默认管理管理员
     */
    public static final String DEFAULT_SYSTEM_APPROVER_KEY = "default.system.approver.key";

    /**
     * 节点名称-自定义参数
     */
    public static final String NODE_NAME = "NODE_NAME";

    /**
     * 任务类型-默认为变更单任务类型
     */
    public static final String TASK_TYPE = "TASK_TYPE";

    /**
     * 节点操作类型 PASS：审批通过，REJECT：审批驳回
     */
    public static final String NODE_OPERATION = "NODE_OPERATION";

    /**
     * 故障管理任务 【过渡节点】节点标识
     */
    public static final String TO_SUBMIT_FAULT_REVIEW_NODE = "Activity_1jkxvt0";


    /**
     * 批次任务 - 系统用户
     */
    public static final String SYSTEM_USER = "system";

    /**
     * 审批记录approvers
     */
    public static final String APPROVERS = "approvers";

    /**
     * taskId
     */
    public static final String TASK_ID = "taskId";

    public static final String APPROVAL_RESULT_Y = "Y";

    public static final String APPROVAL_RESULT_N = "N";

    /** 权限申请 - 代表处会签节点id */
    public static final String PA_REPRESENTATIVE_OFFICE_APPROVAL_NODE_ID = "Activity_1anrbz3";

    /** 权限申请 - 直管领导审批审批节点id */
    public static final String DIRECT_MANAGEMENT_LEADER_APPROVAL_NODE = "Activity_0o60jyl";

    /** 权限申请 - 产品技术科长节点id */
    public static final String PRODUCT_TECHNOLOGY_SECTION_CHIEF_APPROVAL_NODE = "Activity_01owoae";
}
