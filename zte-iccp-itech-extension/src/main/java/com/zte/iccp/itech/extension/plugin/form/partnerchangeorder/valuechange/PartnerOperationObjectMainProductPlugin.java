package com.zte.iccp.itech.extension.plugin.form.partnerchangeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange.CountryChangeByNetworkPlugin;
import com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange.OperationObjectMainProductPlugin;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.AfterDeleteRowsEvent;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import java.util.Collections;
import java.util.List;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.NETWORK_RESPONSIBLE_PERSON;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.NETWORK_RESPONSIBLE_TEAM;

public class PartnerOperationObjectMainProductPlugin implements PartnerValueChangeBaseFormPlugin {

    private static final String NIS_NETWORK_SERVICE_OBJECT_NETWORK_ID_PROPERTY_KEY = "NetworkId";

    @Override
    public void operate(PartnerValueChangedEventArgs args) {
        invoke(args.getModel(), args.getChangeData());
    }

    @Override
    public void loadBillTableData(PartnerLoadBillTableDataEventArgs args) {
        OperationObjectMainProductPlugin.loadBillTableInvoke(args.getModel(), args.getFormView(), args.getTableCid());
    }

    @Override
    public void afterAddRows(PartnerAfterAddRowsEventArgs args) {
        OperationObjectMainProductPlugin.addRowsInvoke(args.getModel(), args.getFormView(), args.getEvent().getEntryKey());
    }

    @Override
    public void afterDeleteRows(PartnerAfterDeleteRowsEventArgs args) {
        deleteRowsInvoke(args.getModel(), args.getFormView(), args.getEvent());
    }

    public void invoke(IDataModel dataModel, ChangeData changeData) {
        String isMainProduct = TextValuePairHelper.getValue(changeData.getNewValue());
        int rowIndex = changeData.getRowIndex();
        if (!Y.equals(isMainProduct)) {
            return;
        }
        // 清空其他【主产品】的勾选
        OperationObjectMainProductPlugin.mainProductSelectChange(dataModel, changeData);
        // 更新主单据【客户网络名称】字段信息
        Object networkObj = dataModel.getValue(FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, rowIndex);
        String networkId = null;
        if (ObjectUtils.isNotEmpty(networkObj)) {
            networkId = ((DynamicDataEntity) networkObj).get(NIS_NETWORK_SERVICE_OBJECT_NETWORK_ID_PROPERTY_KEY).toString();
        }
        dataModel.setValue(FIELD_NETWORK_NAME, networkId);

        // 更新主单据【网络责任人】和【网络责任组】字段信息
        List<Employee> employeeComponentInfo
                = ComponentUtils.getEmployeeComponentInfo(dataModel, OperationObjectFieldConsts.NET_RESPONSIBLE_PERSON_AND_TEAM, rowIndex);
        if (CollectionUtils.isEmpty(employeeComponentInfo)) {
            return;
        }
        List<Employee> responsiblePerson = Collections.singletonList(employeeComponentInfo.get(CommonConstants.INTEGER_ZERO));
        List<Employee> responsibleTeam = employeeComponentInfo.size() <= CommonConstants.INTEGER_ONE
                ? Collections.emptyList()
                : employeeComponentInfo.subList(CommonConstants.INTEGER_ONE, employeeComponentInfo.size());
        dataModel.setValue(
                NETWORK_RESPONSIBLE_PERSON,
                responsiblePerson);
        dataModel.setValue(
                NETWORK_RESPONSIBLE_TEAM,
                responsibleTeam);
    }

    public void deleteRowsInvoke(IDataModel dataModel, IFormView formView, AfterDeleteRowsEvent event) {
        // 非操作对象子表单删除数据行，直接返回
        if (!OPERATION_OBJECT_TABLE_PROPERTY_KEY.equals(event.getEntryKey())) {
            return;
        }
        // 判断删除的子表单数据是否包含【主产品】，若包含则清空主单据客户网络名称、网络责任人、网络责任组以及国家/省/市等信息
        List<DynamicDataEntity> deleteRowEntities = event.getRowEntities();
        boolean isMainProductFlag = deleteRowEntities
                .stream()
                .map(d -> d.get(OperationObjectFieldConsts.IS_MAIN_PRODUCT))
                .anyMatch(item -> Y.equals(TextValuePairHelper.getValue(item)));
        if (isMainProductFlag) {
            dataModel.setValue(FIELD_NETWORK_NAME, null);
            CountryChangeByNetworkPlugin.clearCountryInfo(dataModel);
        }

    }
}
