package com.zte.iccp.itech.extension.openapi.model.assignment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025/3/19 下午15：08
 */
@ApiModel("cnop网络变更单")
@Getter
@Setter
public class CnopChangeOrderDto {

    @ApiModelProperty("变更单ID")
    private String changeOrderId;

    @ApiModelProperty("变更单编码")
    private String orderNo;

    @ApiModelProperty("主题后缀")
    private String operationSubjectSuffix;

    @ApiModelProperty("是否GDPR管控项目")
    private Boolean isGdpr;

    @ApiModelProperty("是否政企")
    private Boolean isGovernmentEnterprise;

    @ApiModelProperty("GDPR要求")
    private Boolean gdprRequire;

    @ApiModelProperty("交付方式")
    private int deliveryMode;

    @ApiModelProperty("代表处code")
    private String departmentCode;

    @ApiModelProperty("产品小类idPath")
    private String productIdPath;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("操作原因")
    private String operationReason;

    @ApiModelProperty("所属时区")
    private String timeZone;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationEndTime;

    @ApiModelProperty("是否紧急操作")
    private Boolean isEmergencyOperation;

    @ApiModelProperty("是否首次应用（首次应用需发起方案会签）")
    private Boolean isFirstApplication;

    @ApiModelProperty("提单人")
    private String createBy;

    @ApiModelProperty("变更单名称")
    private String orderName;

}
