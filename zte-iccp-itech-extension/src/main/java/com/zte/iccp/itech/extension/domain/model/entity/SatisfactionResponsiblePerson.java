package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.SatisfactionResponsiblePersonConsts.*;

/**
 * 故障管理任务--客户满意度责任人配置
 * <AUTHOR> 10335201
 * @date 2024-08-22 下午5:03
 **/
@ApiModel("故障管理任务--客户满意度责任人配置")
@Setter
@Getter
@BaseEntity.Info("satisfaction_responsible_person")
public class SatisfactionResponsiblePerson extends BaseEntity {
    @ApiModelProperty("国家")
    @JsonProperty(value = COUNTRY)
    private List<MultiLangText> country;

    @ApiModelProperty("代表处")
    @JsonProperty(value = ORGANIZATION_ID)
    private String organizationId;

    @ApiModelProperty("产品分类")
    @JsonProperty(value = PRODUCT_ID)
    private String productId;


    @ApiModelProperty("客户支持经理")
    @JsonProperty(value = MANAGER)
    private List<Employee> manager;


    @ApiModelProperty("产品科长")
    @JsonProperty(value = PRODUCT_SECTION_CHIEF)
    private List<Employee> productSectionChief;

    @ApiModelProperty("是否启用")
    @JsonProperty(value = DATA_STATUS)
    private List<TextValuePair> dataStatus;
}
