package com.zte.iccp.itech.extension.domain.model.subentity;

import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024/7/4 下午3:38
 */
@ApiModel("分包商 批次 -高危指令")
@Setter
@Getter
@BaseSubEntity.Info(value = "network_batch_instructions", parent = SubcontractorBatchTask.class)
public class SubBatchHighInstruction extends BatchHighInstruction {
}
