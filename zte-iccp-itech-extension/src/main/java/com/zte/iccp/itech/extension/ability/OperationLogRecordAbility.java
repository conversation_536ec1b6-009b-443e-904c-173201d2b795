package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.MultiProdGuaranteeAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.helper.entity.OrderBy;
import com.zte.iccp.itech.extension.common.helper.entity.OrderEnum;
import com.zte.iccp.itech.extension.common.utils.CacheUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.common.utils.ReflectUtils;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.RedisKeys;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.OperationLogRecordConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ChangeOrderTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.grantfile.IApprovalRecord;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.common.constant.LangConst;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.dto.orm.Range;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.FLOW_EN_US;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_TIME;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationLogRecordConsts.OPERATION_RECORD_PARENT_RELATION_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationLogRecordConsts.OPERATION_RECORD_RELATION_ID;

/**
 * 操作日志记录表
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/4
 */
@Slf4j
public class OperationLogRecordAbility {
    /**
     * 系统用户
     */
    private static final String SYSTEM_USER = "System";

    /**
     * 锁值
     */
    private static final String LOCKED = "locked";

    /**
     * 新增技术管理任务操作记录
     *
     * @param operationRecord operationRecord
     */
    public static void insert(OperationLogRecord operationRecord) {
        if (Objects.isNull(operationRecord)) {
            return;
        }

        SaveDataHelper.create(operationRecord);
    }

    /**
     * 根据主键id更新操作日志记录信息
     *
     * @param record record
     */
    public static void update(OperationLogRecord record) {
        if (Objects.isNull(record) || record.getId() == null) {
            return;
        }

        SaveDataHelper.update(record);
    }

    /**
     * 批量新增技术管理任务操作记录
     *
     * @param operationRecordList operationRecordList
     * @return List<String>
     */
    public static List<String> batchInsert(List<OperationLogRecord> operationRecordList) {
        return SaveDataHelper.batchCreate(operationRecordList);
    }

    /**
     * 根据任务id查询操作日志记录数据集
     *
     * @param taskId 任务id
     * @return 操作日志记录数据集
     */
    public static List<OperationLogRecord> query(String taskId) {
        List<OperationLogRecord> resultList = new ArrayList<>();
        List<String> fields = ReflectUtils.getAllFieldNames(OperationLogRecord.class);
        if (CollectionUtils.isEmpty(fields)) {
            return resultList;
        }

        List<IFilter> filters = Lists.newArrayList();
        //查询关联id为taskId，或者父关联id也为taskId的数据
        IFilter filter = new Filter(OPERATION_RECORD_RELATION_ID, Comparator.EQ, taskId);
        filter.or(new Filter(OPERATION_RECORD_PARENT_RELATION_ID, Comparator.EQ, taskId));
        filters.add(filter);

        resultList = QueryDataHelper.query(OperationLogRecord.class, fields, filters);

        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        return resultList.stream()
                .sorted(java.util.Comparator.comparing(OperationLogRecord::getCreateTime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 保存会签操作记录信息
     *
     * @param operationDate 操作时间
     * @param businessId 业务id
     * @param taskId 审批taskId
     */
    public static void saveSignatureOperationLog(Date operationDate, String businessId, String taskId) {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            // do nothing.
        }
        ApproveRecord approveRecord = FlowHelper.findApproveRecordByTaskId(businessId, taskId);
        if (approveRecord == null) {
            return;
        }
        String empNo = ContextHelper.getEmpNo();

        Assignment assignment = AssignmentAbility.queryAssignment(
                businessId, Lists.newArrayList(ID, BILL_ID, ASSIGNMENT_TYPE, ENTITY_ID, ASSIGNMENT_CODE), Assignment.class);
        if (assignment == null) {
            return;
        }
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());

        OperationLogEnum operationLogEnum = OperationLogEnum.getOperationLogEnumByFlowCode(
                assignmentTypeEnum.getApproveFlowCodeEnum().name(), approveRecord.getExtendedCode());

        String operationDesc = operationLogEnum.getLogContent(operationLogEnum.getSubEntityClass(), assignment.getEntityId(), empNo);
        OperationLogRecord operationLogRecord = OperationLogRecord.builder()
                // 关联id为主单据id
                .relationId(businessId)
                // 操作人员
                .responsiblePerson(empNo)
                // 操作节点文案msgId
                .operationName(operationLogEnum.getProvider().getMsgKey())
                // 日志内容
                .operationDescription(operationDesc)
                // 操作类型
                .operationType(getOperationType(assignment).name())
                // 父关联id
                .parentRelationId(getParentRelationId(assignment))
                // 批次 - 国际行政会签
                .operationNamePrefix(getOperationNamePrefix(assignment))
                .build();
        operationLogRecord.setCreateTime(operationDate);
        SaveDataHelper.create(operationLogRecord);
    }

    /**
     * 流程同意后 - 添加操作日志记录信息
     *
     * @param body body
     */
    public static void saveFlowOperationLog(FlowClient body) {
        String businessId = body.getBusinessId();

        Assignment assignment = AssignmentAbility.queryAssignment(
                businessId, Lists.newArrayList(ID, ENTITY_ID, BILL_ID, ASSIGNMENT_TYPE, ASSIGNMENT_CODE), Assignment.class);
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(
                Objects.requireNonNull(assignment).getAssignmentType());
        // 1.前置校验：1.保障批次不进行日志处理
        if (!isGuaranteeBatch(businessId, assignmentTypeEnum)) {
            return;
        }

        // 2.根据流程节点自定义编码和任务类型获取对应的操作日志枚举
        OperationLogEnum operationLogEnum = OperationLogEnum.getOperationLogEnumByFlowCode(
                body.getFlowCode(), body.getNodeElement().getNodeExtendName());
        if (operationLogEnum == null) {
            return;
        }

        String operationNameMsg = getOperationNameMsg(operationLogEnum);
        String operationLogKey = RedisKeys.operationLogKey(businessId, operationLogEnum.name(), ContextHelper.getEmpNo());
        // 三分钟内重复的key值操作直接拒绝
        if (!CacheUtils.setIfAbsent(operationLogKey, LOCKED, 3)) {
            return;
        }

        try {
            // 增加幂等操作
            if (checkLogExistence(businessId, operationNameMsg)) {
                return;
            }
            String operationDescZh;
            String operationDescEn;
            // 2.组装操作日志
            // 2.1获取操作日志枚举模板，模板为空走默认模板逻辑，不为空则调用对应类型的方法获取
            if (StringUtils.isEmpty(operationLogEnum.getOperationDescMsg())) {
                // 2.2 特殊说明：单独返回中文和英文为方便保障单主批次进行数据同步组装 - 操作日志
                operationDescZh = convertOperationLogRadioAndTextContentByLanguage(body, ZH_CN);
                operationDescEn = convertOperationLogRadioAndTextContentByLanguage(body, EN_US);
            } else {
                operationDescZh = operationLogEnum.getLogContent(body, ZH_CN);
                operationDescEn = operationLogEnum.getLogContent(body, EN_US);
            }

            List<OperationLogRecord> operationLogRecordList = new ArrayList<>();
            OperationLogRecord operationLogRecord = buildNormalOperationLogRecord(assignment, operationNameMsg);
            operationLogRecord.setOperationDesc(operationDescZh, operationDescEn);
            operationLogRecordList.add(operationLogRecord);

            if (operationLogEnum.isSyncBatchGuaranteeApprovalData()) {
                operationLogRecordList.addAll(buildGuaranteeBatchOperationLog(
                        assignment, operationDescZh, operationDescEn, operationNameMsg, null));
            }

            SaveDataHelper.batchCreate(operationLogRecordList);
        } finally {
            CacheUtils.del(operationLogKey);
        }
    }

    public static void saveResultReviewRevokeLog(Assignment assignment) {
        OperationLogRecord operationLogRecord = buildNormalOperationLogRecord(assignment, "review.result.withdrawal");
        operationLogRecord.setOperationDesc(
                MsgUtils.getLangMessage(
                        LangConst.ZH_CN, "operation.result.review.log"),
                MsgUtils.getLangMessage(
                        LangConst.EN_US, "operation.result.review.log"));
        SaveDataHelper.create(operationLogRecord);
    }

    /**
     * 检查日志是否已经存在
     *
     * @param businessId 当前业务id
     * @param operationNameMsg 当前操作名称msg
     * @return true：存在 / false：不存在
     */
    public static boolean checkLogExistence(String businessId, String operationNameMsg) {
        // 1.查询操作日志记录表
        List<IFilter> conditionFilterList = new ArrayList<>();
        conditionFilterList.add(FilterHelper.newMultiFilter(
                // 1.1 如当前为主单据，查询关联id为当前id的数据。如当前为批次，查询关联id为当前批次id的即可
                new Filter(OperationLogRecordConsts.OPERATION_RECORD_RELATION_ID, Comparator.EQ, businessId)
                        // 1.2 操作类型非GUARANTEE
                        .and(new Filter(OperationLogRecordConsts.OPERATION_LOG_OPERATION_TYPE, Comparator.NE, OperationLogRecordTypeEnum.GUARANTEE.name()))));

        // 2.过滤出创建时间最近的数据，比对操作名称是否一致
        List<OperationLogRecord> recordList = QueryDataHelper.query(
                OperationLogRecord.class,
                Lists.newArrayList(ID, OperationLogRecordConsts.OPERATION_NAME),
                conditionFilterList,
                new Range(1),
                new OrderBy(CREATE_TIME, OrderEnum.DESC));
        if (CollectionUtils.isEmpty(recordList)) {
            return false;
        }
        return recordList.get(0).getOperationName().equals(operationNameMsg);
    }

    /**
     * 获取关联父id
     *
     * @param assignment 任务类型
     * @return 父关联id
     */
    private static String getParentRelationId(Assignment assignment) {
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH == assignmentTypeEnum
                || AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH == assignmentTypeEnum) {
            return assignment.getBillId();
        }
        return null;
    }

    /**
     * 获取操作名称
     *
     * @param operationLogEnum 操作日志枚举
     * @return 操作名称msg
     */
    private static String getOperationNameMsg(OperationLogEnum operationLogEnum) {
        return StringUtils.isEmpty(operationLogEnum.getCustomOperationNameMsg())
                ? operationLogEnum.getProvider().getMsgKey()
                : operationLogEnum.getCustomOperationNameMsg();
    }

    /**
     * 获取操作类型
     *
     * @param assignment 任务类型
     * @return 操作类型
     */
    private static OperationLogRecordTypeEnum getOperationType(Assignment assignment) {
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        OperationLogRecordTypeEnum operationLogRecordTypeEnum = null;
        // 1.类型为合作方变更单、合作方批次，类型均为SUBCONTRACTOR_NETWORK_CHANGE
        switch (assignmentTypeEnum) {
            case SUBCONTRACTOR_NETWORK_CHANGE:
            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                operationLogRecordTypeEnum = OperationLogRecordTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE;
                break;
            case NETWORK_CHANGE_BATCH:
                operationLogRecordTypeEnum = OperationLogRecordTypeEnum.NETWORK_CHANGE;
                break;
            case NETWORK_CHANGE:
                NetworkChangeAssignment networkChangeAssignment = AssignmentAbility.querySpecificTypeAssignment(
                        assignment.getBillId(), AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);
                operationLogRecordTypeEnum = networkChangeAssignment.isMatchType(ChangeOrderTypeEnum.MULTI_PRODUCT_GUARANTEE_SUB_TASK.name())
                        ? OperationLogRecordTypeEnum.GUARANTEE
                        : OperationLogRecordTypeEnum.NETWORK_CHANGE;
                break;
            default:
                break;
        }

        return operationLogRecordTypeEnum;
    }

    /**
     * 前置校验 - 是否保障批次
     *
     * @param businessId 业务id
     * @param assignmentTypeEnum 任务类型
     * @return true - 通过，false - 不通过
     */
    private static boolean isGuaranteeBatch(String businessId, AssignmentTypeEnum assignmentTypeEnum) {
        if (assignmentTypeEnum == AssignmentTypeEnum.NETWORK_CHANGE_BATCH) {
            BatchTask batchTask = QueryDataHelper.get(BatchTask.class, Lists.newArrayList(ID, SOURCE, CHANGE_ORDER_ID), businessId);
            String source = batchTask.getSource();
            // 1.保障子批次不添加日志
            if (StringUtils.isNotEmpty(source)
                    && DataSourceEnum.GUARANTEE.name().equals(batchTask.getSource())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 适配操作日志需要动态填充一位的方法，如【提交操作方案】【方案描述】{0}
     *
     * @param body body
     * @param language 语言
     * @return 操作记录日志（中英文）
     */
    public static String convertSingleFieldTextByLanguage(FlowClient body, String language) {
        OperationLogEnum operationLogEnum = OperationLogEnum.getOperationLogEnumByFlowCode(body.getFlowCode(),
                body.getNodeElement().getNodeExtendName());

        String operationLogTemplateMsg = operationLogEnum.getOperationDescMsg();
        List<String> fields = operationLogEnum.getFields();
        if (fields.size() != 1) {
            throw new IllegalArgumentException("The field list can only contain one element.");
        }

        // 动态字段只有1个
        String zeroField = fields.get(0);

        // 例：【提交操作方案】【方案描述】{0}
        return MsgUtils.getLangMessage(language, operationLogTemplateMsg, getRadioOrTextValue(body, zeroField, language));
    }

    /**
     * 适配操作日志需要动态填充两位的方法，如【审核结果】{0}; 【审核意见】{1}
     *
     * @param body 流程中心body对象
     * @param language 语言
     * @return 操作日志中心+英文map
     */
    public static String convertOperationLogRadioAndTextContentByLanguage(FlowClient body, String language) {
        OperationLogEnum operationLogEnum = OperationLogEnum.getOperationLogEnumByFlowCode(body.getFlowCode(),
                body.getNodeElement().getNodeExtendName());

        String operationLogTemplateMsg = processOperationDescMsg(body, operationLogEnum);
        // 操作日志通用模板：【审核结果】{0}; 【审核意见】{1} / 【Review Result】{0}; 【Review Comment】{1}
        if (StringUtils.isEmpty(operationLogTemplateMsg)) {
            operationLogTemplateMsg = MessageConsts.OperationLog.LOG_COMMON;
        }

        List<String> fields = processOperationFields(body, operationLogEnum);
        if (fields.size() < 2) {
            throw new IllegalArgumentException("Fields list must contain at least two elements.");
        }

        // 动态字段2个
        String zeroField = fields.get(0);
        String oneField = fields.get(1);
        // 例：审核结果+审核结果的值；+审核意见+审核意见的值   approve_result_admin_net_servcie_lv4.zh_cn
        return MsgUtils.getLangMessage(language, operationLogTemplateMsg,
                getRadioOrTextValue(body, zeroField, language), getRadioOrTextValue(body, oneField, language));
    }

    /**
     * 获取单选或文本的值
     * （1）先带语言获取，如果未获取到则获取不带语言的
     *
     * @param body body
     * @param field 字段
     * @param language 语言
     * @return 单选或文本值
     */
    private static Object getRadioOrTextValue(FlowClient body, String field, String language) {
        String languageSuffix = ZH_CN.equals(language) ? FLOW_ZH_CN : FLOW_EN_US;
        JSONObject jsonObject = body.getVariables();
        Object o = jsonObject.get(field + languageSuffix);
        if (ObjectUtils.isEmpty(o)) {
            return jsonObject.get(field);
        }
        return o;
    }

    /**
     * 操作日志说明msg处理
     *
     * @param body body
     * @param operationLogEnum operationLogEnum
     * @return String
     */
    private static String processOperationDescMsg(FlowClient body, OperationLogEnum operationLogEnum) {
        JSONObject jsonObject = body.getVariables();
        if (OperationLogEnum.RESULT_TOBE_BACK == operationLogEnum
                && isModifyBatchResultTobeBackValue(jsonObject.get(OPERATION_RESULT))) {
            return MessageConsts.OperationLog.LOG_BATCH_RESULT_TOBE_BACK_EXT;
        }
        return operationLogEnum.getOperationDescMsg();
    }

    private static List<String> processOperationFields(FlowClient body, OperationLogEnum operationLogEnum) {
        JSONObject jsonObject = body.getVariables();
        if (OperationLogEnum.RESULT_TOBE_BACK == operationLogEnum
                && isModifyBatchResultTobeBackValue(jsonObject.get(OPERATION_RESULT))) {
            return Lists.newArrayList(BatchTaskFieldConsts.OPERATION_RESULT, BatchTaskFieldConsts.BATCH_RESULT_DESC);
        }
        return operationLogEnum.getFields();
    }

    /**
     * 内部批次 - 待反馈操作结果 是否需要修改
     *
     * @param operationResultValue operationResultValue
     * @return boolean
     */
    private static boolean isModifyBatchResultTobeBackValue(Object operationResultValue) {
        return ObjectUtils.isNotEmpty(operationResultValue)
                && StringUtils.equals(STR_FIVE, (String) operationResultValue);
    }


    /**
     * 会签 映射方法（查询当前子单据体）
     *
     * @param signatureOperationLogParam 会签操作日志输入参数
     * @return 操作日志中英文Map
     */
    public static String convertSignatureOperationLogContent(SignatureOperationLogParam signatureOperationLogParam) {
        String approver = signatureOperationLogParam.getApprover();
        List<? extends BaseSubEntity> approvalList = QueryDataHelper.query(
                signatureOperationLogParam.getSubEntityClass(), Lists.newArrayList(), signatureOperationLogParam.getBusinessId());

        if (CollectionUtils.isEmpty(approvalList)) {
            return null;
        }
        IApprovalRecord approval = approvalList.stream()
                .filter(Objects::nonNull)
                .filter(a -> a instanceof IApprovalRecord)
                .map(a -> (IApprovalRecord) a)
                .filter(a -> a.getApprover() != null && approver.equals(a.getApprover().getEmpUIID()))
                .findFirst()
                .orElse(null);


        if (approval == null
                || approval.getResult() == null) {
            return null;
        }

        String templateMsg = MessageConsts.OperationLog.LOG_COMMON;
        // 审核结果
        ApproveResultEnum result = approval.getResult();
        String approveResultMsg = ApproveResultEnum.PASS == result
                ? MessageConsts.OperationLog.APPROVE_RESULT_AGREE
                : MessageConsts.OperationLog.APPROVE_RESULT_DISAGREE;

        // 审核意见
        String opinion = approval.getOpinion();
        String operationZh = MsgUtils.getLangMessage(ZH_CN, templateMsg, MsgUtils.getLangMessage(ZH_CN, approveResultMsg), opinion);
        String operationEn = MsgUtils.getLangMessage(EN_US, templateMsg, MsgUtils.getLangMessage(EN_US, approveResultMsg), opinion);
        return JSON.toJSONString(MapUtils.newHashMap(ZH_CN, operationZh, EN_US, operationEn));
    }

    /**
     * 获取操作名前缀（目前批次会拼接批次号在里面）
     *
     * @param assignment assignment
     * @return 操作名前缀
     */
    private static String getOperationNamePrefix(Assignment assignment) {
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH == assignmentTypeEnum
                || AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH == assignmentTypeEnum) {
            return assignment.getAssignmentCode();
        }
        return null;
    }

    /**
     * 系统自动节点 - 自动发布通告 -保障任务主单据的批次同步数据
     *
     * @param body body
     */
    public static void systemNodeSyncBatchGuaranteeData(FlowClient body) {
        String flowCode = body.getFlowCode();
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(flowCode);
        // 1.是内部批次
        if (ApproveFlowCodeEnum.BATCH_TASK_FLOW != approveFlowEnum) {
            return;
        }

        BatchTask batchTask = QueryDataHelper.get(
                BatchTask.class, Lists.newArrayList(ID, CHANGE_ORDER_ID, BATCH_CODE, SOURCE), body.getBusinessId());
        // 2.当前批次对应的网络变更单单据为保障主任务，获取除主任务以外的保障单据任务
        List<Assignment> subAssignmentList = MultiProdGuaranteeAbility.getSubTakByMainId(batchTask.getChangeOrderId());
        if (CollectionUtils.isEmpty(subAssignmentList)) {
            return;
        }

        String operationDescMsg = MessageConsts.OperationLog.LOG_RELEASE_NOTIFY;
        JSONObject variables = body.getVariables();
        String operationDesc = (String) variables.get(BatchTaskFieldConsts.NOTIFICATION_DESC);

        String operationDescZh = MsgUtils.getLangMessage(ZH_CN, operationDescMsg, operationDesc);
        String operationDescEn = MsgUtils.getLangMessage(EN_US, operationDescMsg, operationDesc);
        List<OperationLogRecord> operationLogRecordList = new ArrayList<>();
        for (Assignment subAssignment : subAssignmentList) {
            AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(subAssignment.getAssignmentStatus());
            if (AssignmentStatusEnum.ABOLISH == assignmentStatusEnum
                    || AssignmentStatusEnum.CLOSE == assignmentStatusEnum) {
                continue;
            }
            OperationLogRecord record = OperationLogRecord.builder()
                    .relationId(subAssignment.getEntityId())
                    .responsiblePerson(SYSTEM_USER)
                    .operationName(MessageConsts.OperationLog.LOG_SYSTEM_NODE_RELEASED_NOTICE)
                    .operationNamePrefix(batchTask.getBatchCode())
                    .operationType(OperationLogRecordTypeEnum.GUARANTEE.name())
                    .build();
            // 3.3 保障主单 - 批次进行的同步需要追加前缀：【由{主单单号}自动同步信息】+ 成功发布操作通告
            String operationDescZhExt = MsgUtils.getLangMessage(
                    ZH_CN, MessageConsts.OperationLog.LOG_GUARANTEE_APPROVER, batchTask.getBatchCode(), operationDescZh);
            String operationDescEnExt = MsgUtils.getLangMessage(
                    EN_US, MessageConsts.OperationLog.LOG_GUARANTEE_APPROVER, batchTask.getBatchCode(), operationDescEn);
            record.setOperationDesc(operationDescZhExt, operationDescEnExt);
            operationLogRecordList.add(record);

        }

        SaveDataHelper.batchCreate(operationLogRecordList);
    }

    /**
     * 操作管理 - 操作日志
     *
     * @param businessId 业务id
     * @param operationKey 操作管理key
     * @param operationTime 操作时间
     */
    public static void saveActionOperationLog(String businessId, String operationKey, Date operationTime) {
        Assignment assignment = AssignmentAbility.queryAssignment(businessId, Lists.newArrayList(), Assignment.class);
        if (assignment == null) {
            return;
        }

        OperationLogActionEnum operationLogActionEnum = OperationLogActionEnum.getOperationLogActionEnum(
                operationKey, AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType()));
        if (operationLogActionEnum == null) {
            return;
        }
        String operationDescZh = operationLogActionEnum.getLogContent(businessId, assignment.getAssignmentCode(), ZH_CN);
        String operationDescEn = operationLogActionEnum.getLogContent(businessId, assignment.getAssignmentCode(), EN_US);


        List<OperationLogRecord> operationLogRecordList = new ArrayList<>();
        OperationLogRecord operationLogRecord = buildNormalOperationLogRecord(
                assignment, operationLogActionEnum.getCustomOperationNameMsg());
        operationLogRecord.setOperationDesc(operationDescZh, operationDescEn);
        operationLogRecord.setCreateTime(operationTime);
        operationLogRecordList.add(operationLogRecord);

        // 3.内部批次特殊逻辑：当前批次为保障主单据的批次 - 额外给全部保障单据冗余日志，其关联id为保障单据的id，操作类型为GUARANTEE

        if (operationLogActionEnum.isSyncBatchGuaranteeActionData()) {
            operationLogRecordList.addAll(buildGuaranteeBatchOperationLog(
                    assignment, operationDescZh, operationDescEn, operationLogActionEnum.getCustomOperationNameMsg(), operationTime));
        }

        SaveDataHelper.batchCreate(operationLogRecordList);
    }


    /**
     * 操作日志 - 执行动作操作日志内容
     *
     * @param operationLogParam operationLogParam
     * @param language language
     * @return String
     */
    public static String convertActionOperationLogContentByLanguage(ActionOperationLogParam operationLogParam, String language) {
        OperationLogActionEnum actionEnum = operationLogParam.getOperationLogActionEnum();
        String operationDescTemplateMsg = actionEnum.getOperationDescMsg();
        // todo 废止、取消原因待后续开发
        if (StringUtils.isEmpty(operationDescTemplateMsg)
                || CollectionUtils.isEmpty(actionEnum.getMethodNames())) {
            return null;
        }

        String operationDesc = null;
        List<String> methodNames = actionEnum.getMethodNames();
        AssignmentTypeEnum assignmentTypeEnum = actionEnum.getAssignmentTypeEnum();
        Class<? extends BaseEntity> entityClass = assignmentTypeEnum.getApproveFlowCodeEnum().getFlowEntity();
        BaseEntity baseEntity = QueryDataHelper.get(entityClass, Lists.newArrayList(), operationLogParam.getBusinessId());
        try {
            // 获取方法对象
            Method method = entityClass.getMethod(methodNames.get(0));
            // 调用方法并获取结果
            Object result = method.invoke(baseEntity);
            operationDesc = (String) result;
        } catch (Exception e) {
            log.error("OperationLogRecordAbility#convertOperationLog1Content invoke error, assignmentCode:{},action:{}",
                    operationLogParam.getAssignmentCode(), actionEnum.getOperationCode(), e);
        }

        return MsgUtils.getLangMessage(language, actionEnum.getOperationDescMsg(), operationDesc);
    }

    /**
     * 主任务废止 - 连带保障单据也废纸 - 增加操作日志
     *
     * @param toBeDeprecatedTasks toBeDeprecatedTasks
     * @param changeOrderId changeOrderId
     */
    public static void saveGuaranteeAbolishOperationLog(List<NetworkChangeAssignment> toBeDeprecatedTasks,
                                                        String changeOrderId,
                                                        String operationKey,
                                                        Date operationTime) {
        // 仅针对任务中心废止操作 - 会带一个 abolish 流程中的流程关闭不同步
        if (StringUtils.isEmpty(operationKey)) {
            return;
        }
        Assignment assignment = AssignmentAbility.queryAssignment(
                changeOrderId, Lists.newArrayList(ID, ASSIGNMENT_CODE), Assignment.class);
        if (assignment == null) {
            return;
        }

        String empNo = ContextHelper.getEmpNo();
        // 保障单据废止入口有两个：一为任务中心废止按钮，能取到操作人。
        List<OperationLogRecord> operationLogRecordList = new ArrayList<>();
        for (Assignment toBeDeprecatedTask : toBeDeprecatedTasks) {
            OperationLogRecord record = OperationLogRecord.builder()
                    // 取保障单据自己的id即可
                    .relationId(toBeDeprecatedTask.getEntityId())
                    .responsiblePerson(empNo)
                    .operationName(OperationLogActionEnum.ABOLISH.getCustomOperationNameMsg())
                    .operationType(OperationLogRecordTypeEnum.GUARANTEE.name())
                    .build();
            record.setCreateTime(operationTime);
            // todo 废止原因待后续开发 废止原因字段暂为空，后续直接从主单据取中英文值
            record.setOperationDesc(MsgUtils.getLangMessage(ZH_CN, MessageConsts.OperationLog.LOG_GUARANTEE_APPROVER, assignment.getAssignmentCode(), null),
                    MsgUtils.getLangMessage(EN_US, MessageConsts.OperationLog.LOG_GUARANTEE_APPROVER, assignment.getAssignmentCode(), null));
            operationLogRecordList.add(record);
        }

        SaveDataHelper.batchCreate(operationLogRecordList);
    }

    /**
     * 记录转交操作日志
     */
    public static List<String> saveNetworkChangeTransferLog(
            List<Assignment> assignments,
            List<Employee> users,
            List<Employee> newUsers,
            String transferLog) {

        // 1.校验转交前后人是否一致
        // 一致则无需记录日志
        Set<String> userIds = users.stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toSet());
        Set<String> newUserIds = newUsers.stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toSet());
        if (userIds.containsAll(newUserIds) && newUserIds.containsAll(userIds)) {
            return Lists.newArrayList();
        }

        // 2.包装日志信息
        List<OperationLogRecord> operationLogRecords = new ArrayList<>();
        for (Assignment assignment : assignments) {
            AssignmentTypeEnum assignmentType
                    = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
            String operationNameMsg = AssignmentTypeEnum.NETWORK_CHANGE.equals(assignmentType)
                    || AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)
                    ? ApproveNodeEnum.fromValue(assignment.getCurrentProgress()).getValue()
                    : PartnerApproveNodeEnum.getApproveNodeEnum(assignment.getCurrentProgress()).getValue();
            OperationLogRecord operationLogRecord = buildNormalOperationLogRecord(assignment, operationNameMsg);

            MultiLangText userNames = EmployeeHelper.getEmployeeFormatNames(users);
            MultiLangText newUserNames = EmployeeHelper.getEmployeeFormatNames(newUsers);
            operationLogRecord.setOperationDesc(
                    MsgUtils.getLangMessage(
                            LangConst.ZH_CN, transferLog, userNames.getZhCN(), newUserNames.getZhCN()),
                    MsgUtils.getLangMessage(
                            LangConst.EN_US, transferLog, userNames.getEnUS(), newUserNames.getZhCN()));
            operationLogRecord.setCreateTime(new Date());
            operationLogRecords.add(operationLogRecord);
        }

        return SaveDataHelper.batchCreate(operationLogRecords);
    }

    /**
     * 构建基础操作日志对象
     *
     * @param assignment assignment
     * @param operationNameMsg 操作节点名称msg
     * @return OperationLogRecord
     */
    private static OperationLogRecord buildNormalOperationLogRecord(Assignment assignment,String operationNameMsg) {
        // 操作人员
        String empNo = ContextHelper.getEmpNo();

        // 挂起自动关闭时会取值为iTechCloud，需要把人改成System
        empNo = !StringUtils.equalsIgnoreCase(I_VERSION_SYSTEM_NAME, empNo) ? empNo : SYSTEM_USER;
        return OperationLogRecord.builder()
                .relationId(assignment.getEntityId())
                .responsiblePerson(empNo)
                .operationName(operationNameMsg)
                .operationNamePrefix(getOperationNamePrefix(assignment))
                .operationType(getOperationType(assignment).name())
                .parentRelationId(getParentRelationId(assignment))
                .build();
    }

    /**
     * 构建保障批次操作日志
     *
     * @param assignment assignment
     * @param operationDescZh 操作说明中文
     * @param operationDescEn 操作说明英文
     * @param operationNameMsg 操作节点msg
     */
    private static List<OperationLogRecord> buildGuaranteeBatchOperationLog(Assignment assignment,
                                                                            String operationDescZh,
                                                                            String operationDescEn,
                                                                            String operationNameMsg,
                                                                            Date operationTime) {
        List<OperationLogRecord> guaranteeOperationLogList = new ArrayList<>();
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        // 1.是内部批次任务
        if (assignmentTypeEnum != AssignmentTypeEnum.NETWORK_CHANGE_BATCH) {
            return guaranteeOperationLogList;
        }

        // 3.该内部批次对应的主单据是否为保障单主任务，获取除主任务外的全部子保障单据
        List<Assignment> assignmentList = MultiProdGuaranteeAbility.getSubTakByMainId(assignment.getBillId());
        if (CollectionUtils.isEmpty(assignmentList)) {
            return guaranteeOperationLogList;
        }

        // 4.根据主任务操作日志信息 - 增加保障单据的日志
        String empNo = ContextHelper.getEmpNo();
        empNo = !StringUtils.equalsIgnoreCase(I_VERSION_SYSTEM_NAME, empNo) ? empNo : SYSTEM_USER;
        for (Assignment subAssignment : assignmentList) {
            AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(subAssignment.getAssignmentStatus());
            // 保障单据为废止、关闭状态不需要再同步
            if (AssignmentStatusEnum.ABOLISH == assignmentStatusEnum
                    || AssignmentStatusEnum.CLOSE == assignmentStatusEnum) {
                continue;
            }
            OperationLogRecord record = OperationLogRecord.builder()
                    .relationId(subAssignment.getEntityId())
                    .responsiblePerson(empNo)
                    .operationName(operationNameMsg)
                    .operationNamePrefix(getOperationNamePrefix(assignment))
                    .operationType(OperationLogRecordTypeEnum.GUARANTEE.name())
                    .build();

            record.setCreateTime(operationTime);
            // 【由{主单单号}自动同步信息】+ 主任务的操作日志
            record.setOperationDesc(MsgUtils.getLangMessage(ZH_CN, MessageConsts.OperationLog.LOG_GUARANTEE_APPROVER, assignment.getAssignmentCode(), operationDescZh),
                    MsgUtils.getLangMessage(EN_US, MessageConsts.OperationLog.LOG_GUARANTEE_APPROVER, assignment.getAssignmentCode(), operationDescEn));
            guaranteeOperationLogList.add(record);
        }
        return guaranteeOperationLogList;
    }
}
