package com.zte.iccp.itech.extension.domain.enums.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 网络任务状态
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum AssignmentStatusEnum implements SingletonTextValuePairsProvider {
    /** 全部(非正式状态) */
    WHOLE("-1", "全部", "All"),

    /** 待开始 */
    START("1", "待启动", "To be Started"),

    /** 执行中 */
    EXECUTE("2", "执行中", "Executing"),

    /** 审批中 */
    APPROVE("3", "审核中", "In Reviewing"),

    /** 已关闭 */
    CLOSE("4", "已关闭", "Closed"),

    /** 已废止 */
    ABOLISH("5", "已废止", "Abolished"),

    /** 待发通告 */
    PENDING_NOTIFICATION("6", "待发通告", "To be Released"),

    /** 通告审核中 */
    NOTIFICATION_UNDER_REVIEW("7", "通告审核中", "Notification being Reviewed"),

    /** 待操作执行 */
    OPERATION_EXECUTION("8", "待操作执行", "To be Executed"),

    /** 待反馈结果 */
    RESULT_TOBE_BACK("9", "待反馈结果", "Result to be Feedback"),

    /** 结果审核中 */
    RESULT_UNDER_REVIEW("10", "结果审核中", "Result being Reviewed"),

    /** 已挂起 */
    SUSPENDED("11", "已挂起", "Suspended"),

    /** 审批驳回 - 待启动 */
    APPROVE_START("12", "待启动", "To be Started"),

    /** 复盘初审 */
    REVIEWS_INIT("13","复盘初审","Preliminary Review"),

    /** 复盘提交 */
    REVIEWS_SUBMIT("14","复盘提交","Review Submitted"),

    /** 复盘审核 */
    REVIEWS_APPROVAL("15","复盘审核","In Reviewing"),

    /** 审核确认 */
    REVIEWS_RECTIFY("16","审核确认","Review Confirmed"),

    /** 整改中 */
    REVIEWS_RECTIFICATION("17","整改中","Being Rectified"),

    /** 拟制 */
    DRAFT_APPLICATION("18", "拟制", "Drafting"),

    /** 有效 */
    VALID("19", "有效", "Valid"),

    /** 失效 */
    INVALID("20", "失效", "Invalid"),

    /** 拟制（申请驳回后）*/
    APPROVAL_RETURN_DRAFT_APPLICATION("21", "拟制", "Drafting"),

    /** 操作取消审核 */
    OPERATION_CANCEL_REVIEW("22", "操作取消中", "Operation is being canceled"),
    ;

    /**
     * 编码
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;



    /**
     * 根据枚举编码获取对应枚举
     * @param value
     * @return AssignmentTypeEnum
     */
    public static AssignmentStatusEnum fromValue(String value) {
        for (AssignmentStatusEnum assignmentStatusEnum : AssignmentStatusEnum.values()) {
            if (assignmentStatusEnum.getValue().equals(value)) {
                return assignmentStatusEnum;
            }
        }

        return null;
    }

    /**
     * 检查当前状态是否为执行状态
     *
     * @return true:是，false：否
     */
    public boolean checkIsApproveStatus() {
        // 审批状态
        List<AssignmentStatusEnum> notExecuteStatusEnumList = Arrays.asList(START, CLOSE, ABOLISH, OPERATION_EXECUTION, SUSPENDED, APPROVE_START);
        return !notExecuteStatusEnumList.contains(this);
    }

    /**
     * 检查当前状态是否为执行状态
     *
     * @return true:是，false：否
     */
    public boolean checkIsExecuteStatus() {
        // 非执行、审批状态
        List<AssignmentStatusEnum> notExecuteStatusEnumList = Arrays.asList(START, CLOSE, ABOLISH, OPERATION_EXECUTION, SUSPENDED, APPROVE_START);
        return !notExecuteStatusEnumList.contains(this);
    }

    /**
     * 技术管理任务-反馈类型映射
     *
     * @param feedbackTypeValues feedbackTypeValues
     * @return AssignmentStatusEnum
     */
    public static AssignmentStatusEnum getManageTaskStatus(List<MultiLangText> feedbackTypeValues) {
        if (CollectionUtils.isEmpty(feedbackTypeValues)) {
            return null;
        }
        MultiLangText multiLangText = feedbackTypeValues.get(CommonConstants.INTEGER_ZERO);
        String feedbackType = multiLangText.getValue();
        if (CommonConstants.APPLICATION_DISABLE.equals(feedbackType)) {
            return CLOSE;
        } else if (CommonConstants.APPLICATION_REVOKED.equals(feedbackType)) {
            return ABOLISH;
        }
        return EXECUTE;
    }

    public static List<String> getBatchTaskNotificationCodes() {
        return Lists.newArrayList(AssignmentStatusEnum.PENDING_NOTIFICATION.name(),
                AssignmentStatusEnum.OPERATION_EXECUTION.name());
    }
}
