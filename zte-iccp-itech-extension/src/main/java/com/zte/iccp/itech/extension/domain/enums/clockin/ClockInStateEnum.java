package com.zte.iccp.itech.extension.domain.enums.clockin;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/09/10
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ClockInStateEnum implements SingletonTextValuePairsProvider {
    /** 准备中 */
    PREPARING("准备中", "Preparing"),
    /** 操作中 */
    OPERATING("操作中", "Operating"),
    /** 测试中 */
    TESTING("测试中", "Testing"),
    /** 值守中 */
    ON_DUTY_GOING("值守中", "On-duty going"),
    /** 已关闭 */
    CLOSED("已关闭", "Closed"),
    /** 已废止 */
    ABOLISHED("已废止", "Abolished"),
    ;

    private final String zhCn;

    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }

    public ClockInStateEnum next() {
        ClockInStateEnum[] values = values();
        return this == CLOSED || this == ABOLISHED
                ? null : values[ordinal() + 1];
    }

    public ClockInStateEnum prev() {
        ClockInStateEnum[] values = values();
        return this == PREPARING || this == ABOLISHED
                ? null : values[ordinal() - 1];
    }
}
