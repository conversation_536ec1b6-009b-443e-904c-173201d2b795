package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 三营运营商枚举
 *
 * <AUTHOR>
 * @create 2024/7/16 上午10:21
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OperatorEnum {
    /**
     * 中国移动
     */
    CM("CM", "中国移动", "China Mobile"),
    /**
     * 中国联通
     */
    CU("CU", "中国联通", "China Unicom"),
    /**
     * 中国电信
     */
    CT("CT", "中国电信", "China Telecom"),

    /**
     * 中国广电
     */
    CBN("CBN", "中国广电", "China Broadcasting Network"),
    ;

    /**
     * 操作人员角色值
     */
    private final String value;

    private final String zhCn;

    private final String enUs;

    /*
     * 通过客户标识 获取运营商枚举
     * */
    public static OperatorEnum getOperatorEnum(String name) {
        return Arrays.stream(values())
                .filter(item -> StringUtils.equals(name, item.getZhCn())
                        || StringUtils.equals(name, item.getEnUs()))
                .findFirst().orElse(null);

    }

    /*
    * 通过客户标识 获取运营商
    * */
    public static String getCnnValue(String name) {
        for (OperatorEnum operatorEnum : OperatorEnum.values()) {
            if (operatorEnum.getZhCn().equals(name) || operatorEnum.getEnUs().equals(name)) {
                return operatorEnum.getValue();
            }
        }
        return "";
    }

    /*是否是移中国移动或中国联通，否则返回其他*/
    public static String getCmOrCu(String name) {

        if (OperatorEnum.CM.getZhCn().equals(name) || OperatorEnum.CM.getEnUs().equals(name)) {
            return OperatorEnum.CM.getValue();
        }
        if (OperatorEnum.CU.getZhCn().equals(name) || OperatorEnum.CU.getEnUs().equals(name)) {
            return OperatorEnum.CU.getValue();
        }
        return "";
    }

    /* 根据名称判断 是否是中国电信 */
    public static boolean isChinaTelecom(String name) {
        return OperatorEnum.CT.getZhCn().equals(name) || OperatorEnum.CT.getEnUs().equals(name);
    }

    public static OperatorEnum fromValue(String str){
        return Arrays.stream(OperatorEnum.values())
                .filter(item -> item.value.equals(str))
                .findFirst()
                .orElse(null);
    }
}
