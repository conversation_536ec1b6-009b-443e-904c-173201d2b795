package com.zte.iccp.itech.extension.common.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.enums.IValueEnum;
import lombok.SneakyThrows;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2024/08/21
 */
@SuppressWarnings("rawtypes")
public class IValueEnumSerializer extends JsonSerializer<IValueEnum> {
    @SneakyThrows
    @Override
    public void serialize(IValueEnum valueEnum, JsonGenerator gen, SerializerProvider serializers) {
        if (gen == null || gen.getCurrentValue() == null) {
            return;
        }
        Field field = JsonUtils.findField(gen.getCurrentValue().getClass(), gen.getOutputContext().getCurrentName());
        if (field == null) {
            return;
        }

        Class<?> fieldType = field.getType();

        Field valueField = null;
        IValueEnum.OthValueField othValueFieldName = field.getAnnotation(IValueEnum.OthValueField.class);
        if (othValueFieldName != null) {
            valueField = fieldType.getDeclaredField(othValueFieldName.value());
            valueField.setAccessible(true);
        }

        gen.writeObject(valueField == null
                ? valueEnum.getValue() : valueField.get(valueEnum));
    }
}
