package com.zte.iccp.itech.extension.openapi.idop;

import com.zte.iccp.itech.extension.ability.IdopAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.SysAuthUtils;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.ConflictDTO;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.IdopChangeOrderDto;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.NetworkOfficeDTO;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.TimeConflictDTO;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.CreateOrderVO;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.OperationTypeAttributeVO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_NETWORK_CHANGE_BILL;

/**
 * 对接IDOP接口api
 *
 * <AUTHOR>
 * @create 2024/8/22 下午4:26
 */
public class IdopOpenApi extends AbstractOpenApi {

    /*网络变更单地址后缀*/
    private static String URL = "&pageStatus=EDIT&openType=newTab#/app/APP0984032412495249408/page/";
    /*
     * idop查询操作类型原因下拉值
     * */
    public ServiceData<List<OperationTypeAttributeVO>> getOperationType(HttpServletRequest request,
                                                                        @RequestParam String productLine) {
        SysAuthUtils.auth(request);
        if (!StringUtils.hasText(productLine)) {
            throw new LcapBusiException("productLine is not null");
        }

        ServiceData<List<OperationTypeAttributeVO>> result = new ServiceData<>();
        result.setCode(success());
        result.setBo(IdopAbility.getOperationType(productLine));
        return result;
    }

    /*
     * 批量更新冲突状态
     * */
    public ServiceData<Void> batchUpdateConflict(HttpServletRequest request,
                                                 @RequestBody List<ConflictDTO> conflictDTOS) {
        SysAuthUtils.auth(request);
        if (CollectionUtils.isEmpty(conflictDTOS)) {
            throw new LcapBusiException("params is not null");
        }

        ServiceData<Void> result = new ServiceData<>();
        result.setCode(success());
        IdopAbility.batchUpdateConflict(conflictDTOS);
        return result;
    }

    /*
     * 更新时间
     * */
    public ServiceData<Void> updateTimeConflict(HttpServletRequest request,
                                                @RequestBody TimeConflictDTO timeConflictDTO) {
        SysAuthUtils.auth(request);
        if (!StringUtils.hasText(timeConflictDTO.getChangeOrderId())) {
            throw new LcapBusiException("changeOrderId is not null");
        }

        if (timeConflictDTO.getOperationStartTime() == null) {
            throw new LcapBusiException("operationStartTime is not null");
        }

        if (timeConflictDTO.getOperationEndTime() == null) {
            throw new LcapBusiException("operationEndTime is not null");
        }

        IdopAbility.batchUpdateConflict(timeConflictDTO);
        ServiceData<Void> result = new ServiceData<>();
        result.setCode(success());
        return result;
    }

    /*
     * 创建网络变更单
     * */
    public ServiceData<CreateOrderVO> createChangeOrder(HttpServletRequest request,
                                                        @RequestBody IdopChangeOrderDto idopChangeOrderDto) {
        SysAuthUtils.auth(request);
        checkRequired(idopChangeOrderDto);
        ServiceData<CreateOrderVO> result = new ServiceData<>();
        IdopChangeOrderDto dto = IdopAbility.createChangeOrder(idopChangeOrderDto);
        CreateOrderVO createOrderVO = new CreateOrderVO();
        createOrderVO.setChangeOrderId(dto.getChangeOrderId());
        createOrderVO.setChangeOrderIdDisplay(dto.getOrderNo());
        createOrderVO.setUrl(String.join("", "&type=app&instanceDataId=", dto.getChangeOrderId(),
                URL, PAGE_NETWORK_CHANGE_BILL));
        createOrderVO.setChangeStatus(AssignmentStatusEnum.APPROVE_START.getZhCn());
        result.setCode(success());
        result.setBo(createOrderVO);
        return result;
    }

    /*
     * 更新网络变更单
     * */
    public ServiceData<Void> updateChangeOrder(HttpServletRequest request,
                                               @RequestBody IdopChangeOrderDto idopChangeOrderDto) {
        SysAuthUtils.auth(request);
        if (!StringUtils.hasText(idopChangeOrderDto.getChangeOrderId())) {
            throw new LcapBusiException("changeOrderId is not null");
        }
        ServiceData<Void> result = new ServiceData<>();
        checkRequired(idopChangeOrderDto);
        IdopAbility.updateChangeOrder(idopChangeOrderDto);
        result.setCode(success());
        return result;
    }

    /*
    * 校验必填字段
    * */
    private void checkRequired(IdopChangeOrderDto dto) {
        if (dto.getIsGdpr() == null) {
            throw new LcapBusiException("isGdpr is not null");
        }
        if (!StringUtils.hasText(dto.getCountry())) {
            throw new LcapBusiException("country is not null");
        }
        if (!StringUtils.hasText(dto.getDepartment())) {
            throw new LcapBusiException("department is not null");
        }
        if (!StringUtils.hasText(dto.getProduct())) {
            throw new LcapBusiException("product is not null");
        }
        if (!StringUtils.hasText(dto.getOperationType())) {
            throw new LcapBusiException("operationType is not null");
        }
        if (!StringUtils.hasText(dto.getTimeZone())) {
            throw new LcapBusiException("timeZone is not null");
        }
        if (dto.getOperationStartTime() == null) {
            throw new LcapBusiException("operationStartTime is not null");
        }
        if (dto.getOperationEndTime() == null) {
            throw new LcapBusiException("operationEndTime is not null");
        }
        if (dto.getIsEmergencyOperation() == null) {
            throw new LcapBusiException("isEmergencyOperation is not null");
        }
        if (!StringUtils.hasText(dto.getCustomerName())) {
            throw new LcapBusiException("customerName is not null");
        }
        if (dto.getIsFirstApplication() == null) {
            throw new LcapBusiException("isFirstApplication is not null");
        }
        if (!StringUtils.hasText(dto.getCreateBy())) {
            throw new LcapBusiException("createBy is not null");
        }
        if (dto.getIsNeedAuthorizationFile() == null) {
            throw new LcapBusiException("isNeedAuthorizationFile is not null");
        }
        if (dto.getNeCount() == null) {
            throw new LcapBusiException("neCount is not null");
        }
        checkNetwork(dto);
    }


    private void checkNetwork(IdopChangeOrderDto dto) {
        List<NetworkOfficeDTO> network = dto.getNetwork();
        if (CollectionUtils.isEmpty(network)) {
            throw new LcapBusiException("network is not null");
        }
        if (!network.stream().allMatch(i -> StringUtils.hasText(i.getNetworkId()))) {
            throw new LcapBusiException("network.networkId is not null");
        }
        IdopAbility.checkNisNetwork(network.stream().map(NetworkOfficeDTO::getNetworkId).collect(Collectors.toList()));

        if (dto.getIsNeedAuthorizationFile() && dto.getNeListInfo() == null) {
            throw new LcapBusiException("neListInfo is not null");
        }
    }


    private static RetCode success() {
        return new RetCode() {{
            setCode(RetCode.SUCCESS_CODE);
            setMsgId(RetCode.SUCCESS_MSGID);
            setMsg(RetCode.SUCCESS_MSG);
        }};
    }
}
