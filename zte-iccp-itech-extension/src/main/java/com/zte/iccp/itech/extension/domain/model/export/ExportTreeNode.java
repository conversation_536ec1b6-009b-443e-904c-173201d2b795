package com.zte.iccp.itech.extension.domain.model.export;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * 导出字段树节点领域对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportTreeNode {
    private String id;
    private String code;
    private String value;
    private String level;
    private String status;
    private String nameCn;
    private String nameEn;
    private String namePathCn;
    private String namePathEn;
    private boolean isSelect;
    private List<ExportTreeNode> children;
    
    public void addChild(ExportTreeNode child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }
} 