package com.zte.iccp.itech.extension.openapi.model.assignment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("CSC 服务请求消息体")
@Getter
@Setter
public class CscDetailMessageDTO {

    @ApiModelProperty("服务请求ID")
    private String faultId;

    @ApiModelProperty("服务请求号")
    private String requestNo;

    @ApiModelProperty("故障等级(中文)")
    private String severityCn;

    @ApiModelProperty("故障等级(英文)")
    private String severityEn;

    @ApiModelProperty("区域编码")
    private String areaNo;

    @ApiModelProperty("pdm产品编号")
    private String pdmNo;
}
