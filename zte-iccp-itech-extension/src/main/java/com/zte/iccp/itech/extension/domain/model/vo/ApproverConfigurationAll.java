package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.*;

@ApiModel("审核人")
@Setter
@Getter
@BaseEntity.Info("Approver_Config")
public class ApproverConfigurationAll extends BaseEntity {

    @JsonProperty(value = ORGANIZATION_REGION)
    @ApiModelProperty("片区")
    private List<TextValuePair> organizationRegion;

    @JsonProperty(value = RESPONSIBLE_DEPT)
    @ApiModelProperty("代表处")
    private List<TextValuePair> responsibleDept;
}
