package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.plugin.form.changeorder.helper.ReviewerHelper;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.common.utils.ConvertUtil.getTextFirstValue;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;

/**
 * 操作计划-代表处方案审核人插件
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public class PlanOfficeSolutionReviewerPluginValue implements ValueChangeBaseFormPlugin {

    // 代表处方案审核人角色
    private static final String OFFICE_SOLUTION_REVIEWER_ROLE = "REPRESENTATIVE_OFFICE_SOLUTION_REVIEWER";

    /**
     * 重新加载页面参数
     *
     * @param args 加载页面参数
     * @return
     */
    @Override
    public void loadData(LoadDataEventArgs args) {
        IFormView iFormView = args.getFormView();
        IDataModel dataModel = iFormView.getDataModel();
        repProdTdApproverLister(dataModel,iFormView,true);
    }

    /**
     * 值变化参数，当满足触发代表处方案审核人场景时，触发
     *
     * @param args 值变化参数
     * @return
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        IFormView iFormView = args.getFormView();
        IDataModel dataModel = iFormView.getDataModel();
        repProdTdApproverLister(dataModel,iFormView,false);
    }

    /**
     * 代表处方案审核人员工组件
     *
     * @param dataModel dataModel
     * @param iFormView iFormView
     * @return 是否变动
     */
    private void repProdTdApproverLister(IDataModel dataModel, IFormView iFormView, boolean isLoadData) {
        List<Employee> employees = EmployeeHelper.getModelEmployees(dataModel.getValue(FIELD_OFFICE_SOLUTION_REVIEW_CID));
        if (isLoadData && !CollectionUtils.isEmpty(employees)) {
            return;
        }

        // 获取责任单位
        String organization = TextValuePairHelper.getValue(dataModel.getValue(FIELD_ORGANIZATION_CID));
        // 产品分类
        String product = TextValuePairHelper.getValue(dataModel.getValue(FIELD_PRODUCT_CID));
        // 是否政企
        String isGov = TextValuePairHelper.getValue(dataModel.getValue(FIELD_IS_GOV_ENT_CID));

        if (!StringUtils.hasText(organization) || !StringUtils.hasText(product) || !StringUtils.hasText(isGov)) {
            return;
        }

        ApproverConfiguration approverConfiguration = ApproverConfigAbility.getRepProdTdApprover(organization,
                getTextFirstValue(product),
                ApproveRoleEnum.fromValue(OFFICE_SOLUTION_REVIEWER_ROLE),
                BoolEnum.valueOf(isGov));
        if (approverConfiguration == null) {
            return;
        }

        employees = ReviewerHelper.getEmployees(approverConfiguration);
        ReviewerHelper.setEmployeeByCid(iFormView, employees, FIELD_OFFICE_SOLUTION_REVIEW_CID);
    }
}
