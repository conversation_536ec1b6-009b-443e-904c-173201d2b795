package com.zte.iccp.itech.extension.domain.model.clockin.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.BaseWeeklyDutyPersonFieldsConsts.*;

/**
 * <AUTHOR>
 * @since 2024/10/16
 */
@Getter
@Setter
public class BaseWeeklyDutyPerson extends BaseEntity {
    @ApiModelProperty("星期一")
    @JsonProperty(value = MONDAY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee monday;

    @ApiModelProperty("星期二")
    @JsonProperty(value = TUESDAY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee tuesday;

    @ApiModelProperty("星期三")
    @JsonProperty(value = WEDNESDAY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee wednesday;

    @ApiModelProperty("星期四")
    @JsonProperty(value = THURSDAY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee thursday;

    @ApiModelProperty("星期五")
    @JsonProperty(value = FRIDAY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee friday;

    @ApiModelProperty("星期六")
    @JsonProperty(value = SATURDAY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee saturday;

    @ApiModelProperty("星期日")
    @JsonProperty(value = SUNDAY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee sunday;
}
