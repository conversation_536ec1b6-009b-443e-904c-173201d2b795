package com.zte.iccp.itech.extension.openapi.model.networksecurity.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 批次 操作人员数据汇总
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/7/24
 */
@Getter
@Setter
public class BatchOperatorDTO {

    /** 操作人员表id */
    private String operatorId;

    /** 批次id */
    private String batchId;

    /** 角色中文 */
    private String operatorRoleZh;

    /** 角色英文 */
    private String operatorRoleEn;

    /** 员工 */
    private String empName;

    /** 归属组织（中文） */
    private String orgNamePathZh;

    /** 归属组织（英文） */
    private String orgNamePathEn;

    /** 是否远程 */
    private String isRemote;

    /** 人员属性中文 */
    private String operatorAttributeZh;

    /** 人员属性英文 */
    private String operatorAttributeEn;
}
