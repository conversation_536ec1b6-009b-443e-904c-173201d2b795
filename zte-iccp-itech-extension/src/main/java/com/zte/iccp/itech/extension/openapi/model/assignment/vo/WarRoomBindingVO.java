package com.zte.iccp.itech.extension.openapi.model.assignment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("WarRoom 绑定结果")
@Setter
@Getter
public class WarRoomBindingVO {

    @ApiModelProperty("绑定成功编码")
    private List<String> successCode;

    @ApiModelProperty("绑定失败编码")
    private List<String> failureCode;
}
