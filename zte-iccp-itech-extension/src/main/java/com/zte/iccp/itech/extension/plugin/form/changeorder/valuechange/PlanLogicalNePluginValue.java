package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.FormModelProxyHelper;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.apache.commons.lang3.StringUtils;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.LOGICAL_NE;

/**
 * 触发时机：【产品分类】字段值变化时 算力核心网展示逻辑网
 *
 * <AUTHOR>
 * @date 2025-07-28 下午4:18
 **/
public class PlanLogicalNePluginValue implements ValueChangeBaseFormPlugin {

    /**
     * loadDataEvent统一封装方法
     */
    @Override
    public void loadData(LoadDataEventArgs args) {
        showSensitiveAreas(args.getModel(), args.getFormView());
    }

    /**
     * propertyChanged统一封装方法
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        showSensitiveAreas(args.getModel(), args.getFormView());
    }

    public void showSensitiveAreas(IDataModel dataModel, IFormView view) {
        String product = FormModelProxyHelper.getTextFirstValue(FIELD_PRODUCT_CID, dataModel);
        if (StringUtils.isEmpty(product)) {
            return;
        }
        IEntryTableSupport objectTable = (IEntryTableSupport) view.getControl(COMPONENT_OPERATION_OBJECT_ORDER_CID);
        if (product.startsWith(ConfigHelper.get(CCN_PROD_ID_PATH))) {
            objectTable.getTableState().setHeaderAttribute(LOGICAL_NE, BEHAVIOR, READONLY);
        } else {
            objectTable.getTableState().setHeaderAttribute(LOGICAL_NE, BEHAVIOR, HIDDEN);
        }
    }
}
