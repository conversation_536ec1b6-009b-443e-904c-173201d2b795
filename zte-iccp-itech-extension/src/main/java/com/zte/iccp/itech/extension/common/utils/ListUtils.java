package com.zte.iccp.itech.extension.common.utils;

import com.google.common.base.Functions;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ListUtils {
    public static <T, R> List<T> distinct(List<T> list, Function<T, R> compareBy) {
        return new ArrayList<>(
                list.stream()
                        .collect(Collectors.toMap(
                                compareBy,
                                Functions.identity(),
                                (a, b) -> a))
                        .values());
    }

    @SafeVarargs
    public static <T> List<T> union(List<T>... lists) {
        List<T> union = new ArrayList<>();
        for (List<T> list : lists) {
            union.addAll(list);
        }
        return union;
    }
}