package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BatchOperatorAttrEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.RemoteEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.*;

@ApiModel("操作人员")
@Setter
@Getter
@BaseSubEntity.Info(value = "network_batch_operator", parent = BatchTask.class)
public class BatchTaskOperator extends BaseSubEntity {

    @JsonProperty(value = OPERATOR_ROLE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("人员角色")
    private OperatorRoleEnum operatorRole;

    @JsonProperty(value = REMOTE_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("远程操作")
    private RemoteEnum remoteFlag;

    @JsonProperty(value = OPERATE_PERSON)
    @ApiModelProperty("人员")
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee operatePerson;

    @JsonProperty(value = OPERATOR_PHONE)
    @ApiModelProperty("电话")
    private String operatorPhone;

    @JsonProperty(value = OPERATOR_DEPARTMENT)
    @ApiModelProperty("所属部门")
    private String operatorDepartment;

    @JsonProperty(value = OPERATOR_ACCOUNT)
    @ApiModelProperty("操作账号")
    private String operatorAccount;

    @JsonProperty(value = OPERATOR_ATTRIBUTE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("人员属性")
    private BatchOperatorAttrEnum operatorAttribute;

    @JsonProperty(value = TASK_DESC)
    @ApiModelProperty("任务说明")
    private String taskDesc;

    @JsonIgnore
    public String getOperatePersonEmpNo(){
        if ( getOperatePerson() == null || getOperatePerson().getEmpUIID() ==null){
            return "";
        }
        return getOperatePerson().getEmpUIID();
    }
}
