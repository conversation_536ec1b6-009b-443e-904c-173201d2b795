package com.zte.iccp.itech.extension.common.utils;

import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TransactionHelper;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.LAST_MODIFIED_TIME;

/**
 * <AUTHOR>
 * @since 2024/09/11
 */
public class OperationCache {
    private final Map<Class<? extends BaseEntity>, Set<? extends BaseEntity>> mainCreateMap = MapUtils.newHashMap();

    private final Map<Class<? extends BaseEntity>, UpdateEntities> mainUpdateMap = MapUtils.newHashMap();

    private final Map<Class<? extends BaseEntity>, Set<String>> mainDeleteMap = MapUtils.newHashMap();

    private final Map<Class<? extends BaseSubEntity>, Set<? extends BaseSubEntity>> subCreateMap = MapUtils.newHashMap();

    private final Map<Class<? extends BaseSubEntity>, UpdateEntities> subUpdateMap = MapUtils.newHashMap();

    private final Map<Class<? extends BaseSubEntity>, Set<String>> subDeleteMap = MapUtils.newHashMap();

    @SafeVarargs
    public final <T extends BaseEntity> void create(T... entities) {
        create(Arrays.asList(entities));
    }

    @SuppressWarnings("unchecked")
    public <T extends BaseEntity> void create(List<T> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        setCreateTime(entities);

        Class<? extends BaseEntity> entityClass = entities.get(0).getClass();
        if (BaseSubEntity.class.isAssignableFrom(entityClass)) {
            Class<? extends BaseSubEntity> subClass = (Class<? extends BaseSubEntity>) entityClass;
            Set<T> createSet = (Set<T>) subCreateMap
                    .computeIfAbsent(subClass, c -> Sets.newHashSet());
            createSet.addAll(entities);
            return;
        }

        Set<T> createSet = (Set<T>) mainCreateMap
                .computeIfAbsent(entityClass, c -> Sets.newHashSet());
        createSet.addAll(entities);
    }

    @SafeVarargs
    public final <T extends BaseEntity> void update(T... entities) {
        update(Arrays.asList(entities));
    }

    public final <T extends BaseEntity> void update(List<T> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        setLastModifiedTime(entities);

        Class<? extends BaseEntity> entityClass = entities.get(0).getClass();
        if (BaseSubEntity.class.isAssignableFrom(entityClass)) {
            //noinspection unchecked
            Class<? extends BaseSubEntity> subEntityEnum = (Class<? extends BaseSubEntity>) entityClass;
            update(subEntityEnum, entities.stream()
                    .map(OperationCache::toMap)
                    .collect(Collectors.toList()));
            return;
        }

        update(entityClass, entities.stream()
                .map(OperationCache::toMap)
                .collect(Collectors.toList()));
    }

    @SafeVarargs
    public final void update(Class<? extends BaseEntity> entityClass, Map<String, Object>... entities) {
        update(entityClass, Arrays.asList(entities));
    }

    public void update(Class<? extends BaseEntity> entityClass, List<Map<String, Object>> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        setLastModifiedTime(entities);

        //noinspection unchecked
        UpdateEntities updateEntities = BaseSubEntity.class.isAssignableFrom(entityClass)
                ? subUpdateMap.computeIfAbsent(
                        (Class<? extends BaseSubEntity>) entityClass, c -> new UpdateEntities())
                : mainUpdateMap.computeIfAbsent(
                        entityClass, c -> new UpdateEntities());
        addUpdate(updateEntities, entities);
    }

    public void delete(Class<? extends BaseEntity> entityClass, String... ids) {
        delete(entityClass, Arrays.asList(ids));
    }

    public void delete(Class<? extends BaseEntity> entityClass, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        //noinspection unchecked
        Set<String> deleteSet = BaseSubEntity.class.isAssignableFrom(entityClass)
                ? subDeleteMap.computeIfAbsent(
                        (Class<? extends BaseSubEntity>) entityClass, c -> Sets.newHashSet())
                : mainDeleteMap.computeIfAbsent(
                        entityClass, c -> Sets.newHashSet());
        deleteSet.addAll(ids);
    }

    public <T extends BaseEntity> List<T> queryInNew(Class<? extends BaseEntity> entityClass, Predicate<T> predicate) {
        if (BaseSubEntity.class.isAssignableFrom(entityClass)) {
            //noinspection unchecked
            return subCreateMap.get(entityClass).stream()
                    .map(t -> (T) t)
                    .filter(predicate)
                    .collect(Collectors.toList());
        }

        //noinspection unchecked
        return mainCreateMap.get(entityClass).stream()
                .map(t -> (T) t)
                .filter(predicate)
                .collect(Collectors.toList());
    }

    public <T extends BaseEntity> List<T> queryInUpdate(Class<? extends BaseEntity> entityClass, Predicate<T> predicate) {
        if (BaseSubEntity.class.isAssignableFrom(entityClass)) {
            return subUpdateMap.get(entityClass)
                    .entrySet().stream()
                    .map(entry -> {
                        T t = JsonUtils.parseObject(entry.getValue(), entityClass);
                        t.setId(entry.getKey());
                        return t;
                    }).filter(predicate)
                    .collect(Collectors.toList());
        }

        return mainUpdateMap.get(entityClass)
                .entrySet().stream()
                .map(entry -> {
                    T t = JsonUtils.parseObject(entry.getValue(), entityClass);
                    t.setId(entry.getKey());
                    return t;
                }).filter(predicate)
                .collect(Collectors.toList());
    }

    public Set<String> getDeleteIds(Class<? extends BaseEntity> entityClass) {
        Set<String> ids = BaseSubEntity.class.isAssignableFrom(entityClass)
                ? subDeleteMap.get(entityClass)
                : mainDeleteMap.get(entityClass);
        return ids == null ? Sets.newHashSet() : new HashSet<>(ids);
    }

    public void transFlush() {
        TransactionHelper.run(this::noTransFlush);
    }

    public void noTransFlush() {
        mainCreateMap.forEach((entityClass, createSet) -> {
            SaveDataHelper.batchCreate(new ArrayList<>(createSet));
            createSet.clear();
        });
        mainCreateMap.clear();

        subCreateMap.forEach((entityClass, createSet) -> {
            SaveDataHelper.batchCreate(new ArrayList<>(createSet));
            createSet.clear();
        });
        subCreateMap.clear();

        mainUpdateMap.forEach((entityClass, entityMap) -> {
            SaveDataHelper.batchUpdate(entityClass, new ArrayList<>(entityMap.values()));
            entityMap.clear();
        });
        mainUpdateMap.clear();

        subUpdateMap.forEach((entityClass, entityMap) -> {
            SaveDataHelper.batchUpdate(entityClass, new ArrayList<>(entityMap.values()));
            entityMap.clear();
        });
        subUpdateMap.clear();

        mainDeleteMap.forEach((entityClass, idSet) -> {
            SaveDataHelper.batchDelete(entityClass, new ArrayList<>(idSet));
            idSet.clear();
        });
        mainDeleteMap.clear();

        subDeleteMap.forEach((entityClass, idSet) -> {
            SaveDataHelper.batchDelete(entityClass, new ArrayList<>(idSet));
            idSet.clear();
        });
        subDeleteMap.clear();
    }

    public void clear() {
        mainCreateMap.clear();
        mainUpdateMap.clear();
        mainDeleteMap.clear();
        subCreateMap.clear();
        subUpdateMap.clear();
        subDeleteMap.clear();
    }

    private void addUpdate(
            UpdateEntities updateEntities,
            List<Map<String, Object>> newEntities) {
        for (Map<String, Object> newEntity : newEntities) {
            String id = newEntity.get(ID).toString();
            Map<String, Object> exists = updateEntities.get(id);

            if (exists == null) {
                updateEntities.put(id, newEntity);
            } else {
                exists.putAll(newEntity);
            }
        }
    }

    private static Map<String, Object> toMap(BaseEntity entity) {
        return JsonUtils.parseObject(JsonUtils.toJsonString(entity), Map.class);
    }

    private static <T extends BaseEntity> void setCreateTime(List<T> entities) {
        Date dbTimeNow = TimeZoneEnum.BEIJING
                .toZonedTime(new Date()).getValue();
        entities.forEach(e -> {
            e.setCreateTime(dbTimeNow);
            e.setLastModifiedTime(dbTimeNow);
        });
    }

    private static void setLastModifiedTime(List<?> entities) {
        Date dbTimeNow = TimeZoneEnum.BEIJING
                .toZonedTime(new Date()).getValue();

        Object o = entities.get(0);
        if (o instanceof Map) {
            entities.forEach(e -> {
                //noinspection unchecked
                Map<String, Object> map = (Map<String, Object>) e;
                map.put(LAST_MODIFIED_TIME, dbTimeNow);
            });
            return;
        }

        entities.forEach(e -> ((BaseEntity) e).setLastModifiedTime(dbTimeNow));
    }

    private static class UpdateEntities extends HashMap<String, Map<String, Object>> {
    }
}
