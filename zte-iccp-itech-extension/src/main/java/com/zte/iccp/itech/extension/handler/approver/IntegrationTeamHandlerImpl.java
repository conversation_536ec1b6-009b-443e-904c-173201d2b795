package com.zte.iccp.itech.extension.handler.approver;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/30 下午2:33
 */
public class IntegrationTeamHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    @Override
    protected List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        ApproverConfiguration configuration = ApproverConfigAbility.getByLogicalIdAndProdId(
                changeOrder.getLogicalNe(), changeOrder.getProductCategory());

        if (null == configuration) {
            return Lists.newArrayList();
        }

        return ApproverConfigAbility.getPersonUuidList(configuration.getApproverGroups());

    }

}
