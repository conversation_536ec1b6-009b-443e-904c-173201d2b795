package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel("有线业务检查表(dataKey)")
@Setter
@Getter
public class WireBusinessCheckVO {

    @JsonProperty(value = "custom_z2cy8we6")
    @ApiModelProperty("产品经营团队")
    private String productOperationTeam;

    @JsonProperty(value = "custom_whr13yjy")
    @ApiModelProperty("产品线")
    private String productLine;

    @JsonProperty(value = "custom_gn0dsg5c")
    @ApiModelProperty("产品大类")
    private String productMainCategory;

    @JsonProperty(value = "custom_vibw1svk")
    @ApiModelProperty("产品小类")
    private String productSubCategory;

    @JsonProperty(value = "custom_smco0xpu")
    @ApiModelProperty("文档名称")
    private String documentName;

    @JsonProperty(value = "custom_es5e1efa")
    @ApiModelProperty("发布链接")
    private String link;

    @JsonProperty(value = "custom_h94avib7")
    @ApiModelProperty("备注说明")
    private String remark;

    @JsonProperty(value = "custom_d1ipnm4p")
    @ApiModelProperty("更新人")
    private String updatedUser;

    @JsonProperty(value = "custom_yczzyksu")
    @ApiModelProperty("更新时间")
    private Date updatedDate;

    @JsonProperty(value = "custom_pons9sp0")
    @ApiModelProperty("实体ID")
    private String entityId;
}
