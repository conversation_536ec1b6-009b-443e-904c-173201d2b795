package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.GradingGuaranteeAbility;
import com.zte.iccp.itech.extension.ability.NetworkConfigurationAbility;
import com.zte.iccp.itech.extension.ability.OperationTypeAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.FormModelProxyHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.PageStatusEnumUtil;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.ImportanceEnum;
import com.zte.iccp.itech.extension.domain.model.NetworkConfiguration;
import com.zte.iccp.itech.extension.domain.model.OperationTypeAttribute;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.spi.model.crm.SvcCustomerInfo;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.INNER_OFFICE_ORG_CODE_PATH;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.ProdCategoryConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ProductTypeFactorConsts.SCORE;

/**
 * <AUTHOR>
 * @date 2024/5/13 上午10:33
 */
@AllArgsConstructor
public class ImportanceFormPluginValue implements ValueChangeBaseFormPlugin {

    private boolean isPlan;

    @Override
    public void operate(ValueChangedEventArgs args) {
        IDataModel dataModel = args.getFormView().getDataModel();
        IFormView formView = args.getFormView();
        this.invoke(dataModel, formView, args.getPropId());

    }

    @Override
    public void loadBillTableData(LoadBillTableDataEventArgs args) {
        if (!COMPONENT_OPERATION_OBJECT_ORDER_CID.equals(args.getTableCid())) {
            return;
        }

        IDataModel dataModel = args.getFormView().getDataModel();
        IFormView formView = args.getFormView();
        String pageStatus = formView.getFormShowParameter().getPageStatus().getValue();
        if (!PageStatusEnumUtil.isNew(pageStatus)) {
            return;
        }

        this.invoke(dataModel, formView, null);
    }

    private void invoke(IDataModel dataModel, IFormView formView, String propId) {
        PageStatusEnum pageStatusEnum = formView.getFormShowParameter().getPageStatus();
        if (!Lists.newArrayList(PageStatusEnum.NEW, PageStatusEnum.EDIT).contains(pageStatusEnum)) {
            return;
        }
        String responsibleDeptId = FormModelProxyHelper.getTextFirstValue(FIELD_ORGANIZATION_CID, dataModel);

        BoolEnum govEntFlag = BoolEnum.nilValueOf(FormModelProxyHelper.getTextFirstValue(COMPONENT_IS_GOV_ENT_CID, dataModel));
        String product = FormModelProxyHelper.getTextFirstValue(FIELD_PRODUCT_CID, dataModel);
        //责任单位、是否政企、产品为空 设置为normal直接返回
        if (null == responsibleDeptId
                || null == govEntFlag
                || StringUtils.isBlank(product)) {
            formView.getClientViewProxy().setControlState(
                    COMPONENT_IMPORTANCE_CID, new PageStatusAttributeBuilder().normal().build());
            return;
        }

        List<String> productJudeList = ConfigHelper.get(Lists.newArrayList(
                CCN_PROD_IDPATH_KEY, BN_PROD_IDPATH_KEY, FM_PROD_IDPATH_KEY, MMVS_PROD_IDPATH_KEY));
        String operationType = FormModelProxyHelper.getTextFirstValue(FIELD_OPERATION_TYPE_CID, dataModel);
        //产品分类不属于算力及核心网、承载网、固网及多媒体、视频-多媒体视讯系统，设置为normal
        if (productJudeList.stream().noneMatch(product::startsWith)) {
            //当是操作类型值变化时，且值不为空时,从选项配置表查询赋值
            if (StringUtils.isNotBlank(operationType) && FIELD_OPERATION_TYPE_CID.equals(propId)) {
                dataModel.setValue(COMPONENT_IMPORTANCE_CID, this.getValueByOperationTypeTable(product, operationType));
            }
            formView.getClientViewProxy().setControlState(
                    COMPONENT_IMPORTANCE_CID, new PageStatusAttributeBuilder().normal().build());
            return;
        }

        //当产品分类属于算力及核心网、承载网、固网及多媒体、视频-多媒体视讯系统时，进入计算逻辑不允许手填，设置为disable
        formView.getClientViewProxy().setControlState(
                COMPONENT_IMPORTANCE_CID, new PageStatusAttributeBuilder().disable().build());
        //责任单位不属于国内 或 责任单位属于国内且是否政企=是 做相应判断赋值
        if ((Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).anyMatch(responsibleDeptId::startsWith) && BoolEnum.Y == govEntFlag)
                || Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).noneMatch(responsibleDeptId::startsWith)) {
            this.judgeByLocalFactorAndProduct(dataModel);
            return;
        }

        //责任单位属于国内且是否政企为否 做相应判断赋值
        if (Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).anyMatch(responsibleDeptId::startsWith)
                && BoolEnum.N == govEntFlag) {
            this.notGovEntJudgLogic(dataModel, product);
        }

    }

    private String getValueByOperationTypeTable(String productIdPath, String operationType) {
        List<String> fieldList = Lists.newArrayList(RISK_ASSESSMENT, OPERATE_LEVEL, IMPORTANCE_LEVEL);
        List<IFilter> conditionFilters = Lists.newArrayList();
        List<String> productLines = Lists.newArrayList(productIdPath.split(FORWARD_SLASH)[INTEGER_ZERO] + FORWARD_SLASH + productIdPath.split(FORWARD_SLASH)[INTEGER_ONE] + FORWARD_SLASH);
        conditionFilters.add(new Filter(PRODUCT_LINE, Comparator.R_CONTAINS, productLines));
        conditionFilters.add(new Filter(OPERATE_TYPE, Comparator.EQ, operationType));
        // 基于产品线，从产品操作类型维护基础资料获取操作类型
        List<OperationTypeAttribute> operationTypeAttributes = OperationTypeAbility.query(fieldList, conditionFilters);
        if (CollectionUtils.isEmpty(operationTypeAttributes)
                || null == TextValuePairHelper.getValue(operationTypeAttributes.get(0).getImportanceLevel())) {
            return ImportanceEnum.STAR1.getValue();
        }
        return TextValuePairHelper.getValue(operationTypeAttributes.get(0).getImportanceLevel());
    }

    private void judgeByLocalFactorAndProduct(IDataModel dataModel) {
        String customerId;
        String countryCode = FormModelProxyHelper.getValueLangFirstValue(FIELD_COUNTRY_CID, dataModel);
        if (isPlan) {
            customerId = FormModelProxyHelper.getValueLangFirstValue(FIELD_CUSTOMER_NAME_CID, dataModel);
        } else {
            customerId = this.getCustomerId(dataModel);
        }

        //客户id 或国家，无法计算局点系数及产品类型系数，直接返回
        if (StringUtils.isBlank(customerId)
                || StringUtils.isBlank(countryCode)) {
            return;
        }
        //局点系数
        double localFactorScore = GradingGuaranteeAbility.getLocalFactor(countryCode, customerId, Lists.newArrayList(SCORE));

        //产品类型系数
        double productTypeFactorScore = this.getProductTypeFactorScore(dataModel);

        dataModel.setValue(COMPONENT_IMPORTANCE_CID, this.getRoundedDownStar(localFactorScore * productTypeFactorScore));
    }

    private double getProductTypeFactorScore(IDataModel dataModel) {
        // 操作对象列表-产品型号
        List<String> productModelIds = FormModelProxyHelper.getSubFormServiceColumnValueList(
                dataModel, OPERATION_OBJECT_TABLE_PROPERTY_KEY, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY,
                SERVICE_PRODUCT_MODEL_FULL_ID_PATH_CID);

        // 主表单--产品分类
        String productIdPath = getProductIdPath(dataModel);

        return GradingGuaranteeAbility.getProductTypeFactor(productModelIds,Lists.newArrayList(productIdPath), Lists.newArrayList(SCORE));
    }

    private String getCustomerId(IDataModel dataModel) {
        if (null == dataModel.getValue(FIELD_CUSTOMER_ID_CID)) {
            return null;
        }
        SvcCustomerInfo cust = JsonUtils.parseObject(dataModel.getValue(FIELD_CUSTOMER_ID_CID), SvcCustomerInfo.class);
        if (null == cust) {
            return null;
        }
        return cust.getCustomerId();
    }

    private void notGovEntJudgLogic(IDataModel dataModel, String product) {
        List<String> productJude1List = ConfigHelper.get(Lists.newArrayList(
                CCN_PROD_IDPATH_KEY, BN_PROD_IDPATH_KEY, FM_PROD_IDPATH_KEY));

        List<String> productJude2List = ConfigHelper.get(Lists.newArrayList(MMVS_PROD_IDPATH_KEY));

        String operationType = FormModelProxyHelper.getTextFirstValue(FIELD_OPERATION_TYPE_CID, dataModel);
        BoolEnum isFirstApplication = BoolEnum.nilValueOf(
                FormModelProxyHelper.getTextFirstValue(FIELD_IS_FIRST_APPLICATION_CID, dataModel));

        //产品分类属于算力及核心网、承载网、固网及多媒体
        //按照操作类型分组是否属于配置保障、是否首次应用，产品系数*网络系数,按顺序进行判断赋值
        if (productJude1List.stream().anyMatch(product::startsWith)) {
            this.judgeByProductOne(dataModel, operationType, isFirstApplication);
            return;
        }

        //产品分类属于视频-多媒体视讯系统
        //按照操作类型分组是否属于配置保障、是否首次应用，产品系数*局点系数，按顺序进行判断赋值
        if (productJude2List.stream().anyMatch(product::startsWith)) {
            this.judgeByProductTwo(dataModel, operationType, isFirstApplication);
        }


    }

    private void judgeByProductTwo(IDataModel dataModel, String operationType, BoolEnum isFirstApplication) {
        //操作类型或是否首次应用空，直接返回
        if (null == operationType
                || null == isFirstApplication) {
            return;
        }

        ImportanceEnum judgEnum = this.getByOperationTypeAndIsFirstApplication(operationType, isFirstApplication);
        //根据操作类型、是否首次应用判断出结果，赋值返回
        if (null != judgEnum) {
            dataModel.setValue(COMPONENT_IMPORTANCE_CID, judgEnum.getValue());
            return;
        }

        //根据操作类型、是否首次应用未判断出结果，则需要根据局点系数*产品系数赋值
        this.judgeByLocalFactorAndProduct(dataModel);
    }


    private void judgeByProductOne(IDataModel dataModel, String operationType, BoolEnum isFirstApplication) {
        //操作类型或是否首次应用空，直接返回
        if (null == operationType
                || null == isFirstApplication) {
            return;
        }

        ImportanceEnum judgEnum = this.getByOperationTypeAndIsFirstApplication(operationType, isFirstApplication);
        //根据操作类型、是否首次应用判断出结果，赋值返回
        if (null != judgEnum) {
            dataModel.setValue(COMPONENT_IMPORTANCE_CID, judgEnum.getValue());
            return;
        }

        //根据操作类型、是否首次应用未判断出结果，则需要根据网络系数*产品系数赋值
        this.judgeByNetworkFactorAndProductTypeFactor(dataModel);
    }

    private void judgeByNetworkFactorAndProductTypeFactor(IDataModel dataModel) {

        List<String> networkIds = FormModelProxyHelper.getSubFormServiceColumnValueList(
                dataModel, OPERATION_OBJECT_TABLE_PROPERTY_KEY, FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, SERVICE_NETWORK_ID_CID);

        if (CollectionUtils.isEmpty(networkIds)) {
            return;
        }

        //产品系数
        double productTypeFactorScore = this.getProductTypeFactorScore(dataModel);

        //网络系数
        List<NetworkConfiguration> networkConfigurationList =
                NetworkConfigurationAbility.queryByNetworkId(networkIds);
        double networkScore = networkConfigurationList.stream()
                .filter(a -> !CollectionUtils.isEmpty(a.getGradeScore()))
                .mapToDouble(attribute -> Double.parseDouble(attribute.getGradeScore().get(INTEGER_ZERO).getValue()))
                .max()
                .orElse(ImportanceEnum.STAR1.getIntegerValue());

        dataModel.setValue(COMPONENT_IMPORTANCE_CID, this.getRoundedDownStar(networkScore * productTypeFactorScore));
    }

    private ImportanceEnum getByOperationTypeAndIsFirstApplication(String operationType, BoolEnum isFirstApplication) {

        //首次应用=是 赋值3星
        if (BoolEnum.Y == isFirstApplication) {
            return ImportanceEnum.STAR3;
        }
        Boolean isOperationTypeOfGuarantee = FormModelProxyHelper.isOperationTypeOfGuaranteeType(
                operationType, LookupValueConstant.OPERATE_TYPE_GROUP_COOPERATION_GUARANTEE_CODE);
        //操作类型属于配置保障，赋值1星
        if (Boolean.TRUE.equals(isOperationTypeOfGuarantee)) {
            return ImportanceEnum.STAR1;
        }
        return null;
    }

    private String getRoundedDownStar(double i) {
        if (i < 2) {
            return STR_ONE;
        }
        if (2 <= i && i < 3) {
            return STR_TWO;
        }
        return STR_THREE;

    }

    @Nullable
    private static String getProductIdPath(IDataModel dataModel) {
        Object productIdObj = dataModel.getValue(FIELD_PRODUCT_CID);
        String productIdPath = null;
        if (!ObjectUtils.isEmpty(productIdObj)){
            List<TextValuePair> productJsonObjects = JsonUtils.parseArray(productIdObj, TextValuePair.class);
            productIdPath = productJsonObjects.get(INTEGER_ZERO).getValue();
        }
        return productIdPath;
    }

}
