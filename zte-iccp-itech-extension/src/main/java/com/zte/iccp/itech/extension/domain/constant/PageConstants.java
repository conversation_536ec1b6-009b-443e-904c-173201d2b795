package com.zte.iccp.itech.extension.domain.constant;

/**
 * 页面编码常量
 */
public class PageConstants {
    // =========== 实体表单编码 ============
    /**
     * 网络配置
     */
    public static final String PAGE_BILL_NETWORK_CONFIGURATION = "PAGE0995633470320648196";

    /**
     * 布局名称： 网络变更操作提单_审批_详情
     */
    public static final String PAGE_BILL_NETWORK_CHANGE_BILL = "PAGE1010486823108829185";

    /**
     * 布局名称： 审批驳回详情页面
     */
    public static final String PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL = "PAGE1126520872430673921";

    /**
     * 布局名称： 网络变更操作单_表单
     */
    public static final String PAGE_NETWORK_CHANGE_BILL = "PAGE0984501877314170885";

    /**
     * 布局名称： 操作计划_表单
     */
    public static final String PAGE_PLAN_OPERATION_BILL = "PAGE1148996033356578822";

    /**
     * 网络变更任务 - 保障 批次任务 - 详情
     */
    public static final String PAGE_BILL_BATCH_NETWORK_CHANGE_GURANTEE_BILL = "PAGE1078053343134027777";

    /**
     * 布局名称：分包商网络变更操作_表单
     */
    public static final String PAGE_PARTNER_NETWORK_CHANGE_BILL = "PAGE1001075285666070532";

    /**
     * 布局名称：内部网络变更操作_表单
     */
    public static final String PAGE_INTERNAL_NETWORK_CHANGE_BILL = "PAGE0984501877314170885";

    /**
     * 布局名称：合作方网络变更单批次任务_发布通告_进度条_分包商
     */
    public static final String PAGE_PARTNER_BATCH_NETWORK_CHANGE_NOTICE_RELEASE_PROGRESS_BILL = "PAGE1014466079771930650";

    /**
     * 布局名称：合作方网络变更单批次任务_发布通告_单页只读_分包商
     */
    public static final String PAGE_PARTNER_BATCH_NETWORK_CHANGE_NOTICE_RELEASE_BILL = "PAGE1014466079767736323";

    /**
     * 布局名称：内部网络变更单批次任务_发布通告_v2
     */
    public static final String PAGE_INTERNAL_BATCH_NETWORK_CHANGE_NOTICE_RELEASE_BILL = "PAGE0990260967728283653";

    /**
     * 布局名称：内部网络变更单批次任务_发布通告_单页只读
     */
    public static final String PAGE_INTERNAL_BATCH_NETWORK_CHANGE_NOTICE_RELEASE_READ_ONLY_BILL = "PAGE1005510209595056128";

    /**
     * 技术管理任务单
     */
    public static final String PAGE_BILL_TECHNOLOGY_MANAGEMENT_BILL = "PAGE0997791356245221380";

    /**
     * 技术管理任务 - 主任务 - 详情
     */
    public static final String PAGE_BILL_MAIN_TECHNOLOGY_DETAIL_BILL = "PAGE1004349069858136065";

    /**
     * 技术管理任务 - 子任务 - 详情
     */
    public static final String PAGE_BILL_SUB_TECHNOLOGY_DETAIL_BILL = "PAGE1010949797683027969";

    /**
     * 分包商网络变更操作单
     */
    public static final String PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_BILL = "PAGE1001075285666070532";

    /**
     * 分包商网络变更任务 - 审批详情
     */
    public static final String PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL = "PAGE1012012457422307329";

    /**
     * 网络变更任务 - 批次任务 - 详情
     */
    public static final String PAGE_BILL_BATCH_SUBCONTRACT_NETWORK_CHANGE_BILL = "PAGE1015270371719131136";

    /**
     * 故障管理任务
     */
    public static final String PAGE_BILL_FAULT_MANAGEMENT_BILL = "PAGE1022551814320730116";

    /**
     * 故障管理任务（审批中）
     */
    public static final String FAULT_MANAGEMENT_TASK_APPROVAL = "fault.management.task.approval";

    /**
     * WarRoom 详情
     */
    public static final String PAGE_BILL_WAR_ROOM_DETAIL = "PAGE1033747208179490817";

    /**
     * 批次任务发布通告_申请客户授权凭证
     */
    public static final String PAGE_BILL_APPLY_CUSTOMER_AUTHORIZATION = "PAGE1056246274900070400";

    /** CCN 默认授权文件申请 */
    public static final String PAGE_BILL_CCN_APPLY_AUTHORIZATION_FILE = "PAGE1060505210223013891";

    /** 发布通告页面 */
    public static final String NOTICE_PAGE_ID = "PAGE1011296103953911809";

    /** 权限申请 - 外部用户 */
    public static final String PAGE_BILL_EXTERNAL_PERMISSION_APPLICATION = "PAGE1050871693965373440";

    /** 权限申请 - 内部用户 */
    public static final String PAGE_BILL_INTERNAL_PERMISSION_APPLICATION = "PAGE1050815556909903873";

    /** 权限申请 - 审批详情 */
    public static final String PAGE_BILL_PERMISSION_APPLICATION_APPROVAL_DETAIL ="PAGE1050778320520175616";

    /** 打卡复盘 */
    public static final String PAGE_BILL_CLOCK_IN_REVIEW = "PAGE1061237587929899009";

    /** 邮件推送群组 */
    public static final String PAGE_BILL_EMAIL_GROUP_INFO = "PAGE1108418851671805956";

    /** 用户群组 */
    public static final String PAGE_BILL_USER_GROUP_INFO = "PAGE1108418671455145986";

    /** 有线业务检查表 */
    public static final String WIRE_BUSINESS_CHECK = "PAGE1111223503839858689";

    /** 导出模板列表 */
    public static final String PAGE_ASSIGNMENT_EXPORT_TEMPLATE = "PAGE1117205861242765313";


    // =========== 实体列表编码 ============

    /**
     * 标准方案 - 草稿
     */
    public static final String PAGE_LIST_STANDARD_SCHEME_DRAFT = "PAGE1005569789952983041";

    /**
     * 标准方案 - 已发布
     */
    public static final String PAGE_LIST_STANDARD_SCHEME_PUBLISHED = "PAGE1005569595958034434";

    /**
     * 标准方案 - 已废止
     */
    public static final String PAGE_LIST_STANDARD_SCHEME_REPEALED = "PAGE1005569939102433281";


    // =========== 页面设计编码 ============
    /** 页面 - 网络查询 */
    public static final String PAGE_WEB_OPERAND_QUERY_NETWORK = "network_query";

    /** 页面 - 当前版本查询 */
    public static final String PAGE_WEB_QUERY_CURRENT_VERSION = "current_version";

    /** 页面 - 版本查询 */
    public static final String PAGE_WEB_QUERY_VERSION = "version_list";

    /** 自定义标识 - 分包商网络变更 */
    public static final String PARTNER_CHANGE_ORDER_PAGE_CID = "partner_change_order_page_cid";

    /** 自定义标识 - 网络变更单 */
    public static final String CHANGE_ORDER_PAGE_CID = "change_order_page_cid";

    /** 自定义标识 - 技术管理子任务 */
    public static final String TECHNOLOGY_SUB_TASK_PAGE_CID = "technology_sub_task_page_cid";

    /**
     * 自定义标识 - CCN 默认授权文件申请
     */
    public static final String CCN_AUTHORIZATION_APPLICATION_PAGE_CID = "ccn_authorization_application_page_cid";

    /** 页面 - 任务中心 - 全部任务 */
    public static final String PAGE_WEB_WHOLE_ASSIGNMENT = "total_assignment";

    /** 页面 - 任务中心 - 网络变更任务 */
    public static final String PAGE_WEB_NETWORK_CHANGE_ASSIGNMENT = "network_change_assignment";

    /** 页面 - 任务中心 - 技术管理任务 */
    public static final String PAGE_WEB_TECHNOLOGY_MANAGEMENT_ASSIGNMENT = "technology_management_assignment";

    /** 页面 - 任务中心 - 故障管理任务 */
    public static final String PAGE_WEB_FAULT_MANAGEMENT_ASSIGNMENT = "fault_management_assignment";

    /** 页面 - 任务中心 - 可关联任务 */
    public static final String PAGE_WEB_ASSOCIATE_ASSIGNMENT_DETAIL = "associate_assignment_detail";

    /** 页面 - 任务中心 - 可关联任务 */
    public static final String PAGE_WEB_ASSOCIABLE_ASSIGNMENT = "associable_assignment";

    /** 页面 - 任务中心 - 确认转交人 */
    public static final String PAGE_WEB_CONFIRM_TRANSFER_RESPONSIBILITY = "confirm_transfer_responsibility";

    /** 页面 - 任务中心 - 确认转交人（故障管理） */
    public static final String PAGE_WEB_CONFIRM_TRANSFER_CURRENT_PROCESSOR = "confirm_transfer_current_processor";

    /** 页面 - 任务中心 - 操作计划管理 */
    public static final String PAGE_WEB_OPERATION_PLAN_MANAGEMENT = "operation_plan_management";

    /** 页面 - 授权中心 - CCN 默认授权文件创建失败弹窗 */
    public static final String PAGE_WEB_CREATE_AUTHORIZATION_FILE_FAILED_POP_UP = "create_authorization_file_failed_popup";

    /** 页面 - 帮助页面 - 网络变更案例提醒 */
    public static final String PAGE_WEB_NETWORK_CHANGE_ORDER_SUBMIT_DIALOG = "NetworkChangeOrdersSubmitDialog";

    /** 页面 - 帮助页面 - 批次发布通告案例提醒 */
    public static final String PAGE_WEB_BATCH_RELEASE_NOTICE_DIALOG = "BatchReleaseNoticeDialog";

    /** 页面 - 待办中心 - 确认转交人 */
    public static final String PAGE_WEB_TRANSFER_CONFIRM = "transfer_confirm";

    /** 页面 - 待办中心 - 我发起的 - 总页面 */
    public static final String PAGE_WEB_INITIATED_TASK = "initiated_task";

    /** 页面 - 待办中心 - 我发起的 - 汇总任务 */
    public static final String PAGE_WEB_INITIATED_BY_ME = "Initiated_by_me";

    /** 页面 - 待办中心 - 我发起的 - 网络变更任务 */
    public static final String PAGE_WEB_NETWORK_CHANGE_INITIATED_BY_ME = "changeorder_initiated_by_me";

    /** 页面 - 待办中心 - 我发起的 - 技术管理任务 */
    public static final String PAGE_WEB_TECHNOLOGY_INITIATED_BY_ME = "technology_initiated_by_me";

    /** 页面 - 待办中心 - 我发起的 - 权限申请任务 */
    public static final String PAGE_WEB_APPLICATION_INITIATED_BY_ME = "permission_application_initiated_by_me";

    /** 页面 - 待办中心 - 我发起的 - 异常复盘任务 */
    public static final String PAGE_WEB_ABNORMAL_REVIEW_INITIATED_BY_ME = "abnormal_review_initiated_by_me";

    /** 页面 - 待办中心 - 我发起的 - 操作计划任务 */
    public static final String PAGE_WEB_OPERATION_INITIATED_BY_ME =  "operation_plan_initiated_by_me";

    /** 页面 - 待办中心 - 待我处理 - 总页面 */
    public static final String PAGE_WEB_WAIT_TASK = "wait_task";

    /** 页面 - 待办中心 - 待我处理 - 汇总任务 */
    public static final String PAGE_WEB_TO_BE_HANDLED_BY_ME = "to_be_handled";

    /** 页面 - 待办中心 - 待我处理 - 网络变更任务 */
    public static final String PAGE_WEB_NETWORK_CHANGE_TO_BE_HANDLED_BY_ME = "changeorder_to_be_handled";

    /** 页面 - 待办中心 - 待我处理 - 技术管理任务 */
    public static final String PAGE_WEB_TECHNOLOGY_TO_BE_HANDLED_BY_ME = "technology_to_be_handled";

    /** 页面 - 待办中心 - 待我处理 - 权限申请任务 */
    public static final String PAGE_WEB_APPLICATION_TO_BE_HANDLED_BY_ME = "permission_application_to_be_handled";

    /** 页面 - 待办中心 - 待我处理 - 异常复盘任务 */
    public static final String PAGE_WEB_ABNORMAL_REVIEW_TO_BE_HANDLED_BY_ME = "abnormal_review_to_be_handled";

    /** 页面 - 待办中心 - 待我处理 - 操作计划单 */
    public static final String PAGE_WEB_OPERATION_PLAN_TO_BE_HANDLED_BY_ME = "operation_plan_to_be_handled";

    /** 页面 - 待办中心 - 我已处理 - 总页面 */
    public static final String PAGE_WEB_DEALT_TASK = "dealt_task";

    /** 页面 - 待办中心 - 我已处理 - 汇总任务 */
    public static final String PAGE_WEB_HANDLED_BY_ME = "handled_by_me";

    /** 页面 - 待办中心 - 我已处理 - 网络变更任务 */
    public static final String PAGE_WEB_NETWORK_CHANGE_HANDLED_BY_ME = "changeorder_handled_by_me";

    /** 页面 - 待办中心 - 我已处理 - 技术管理任务 */
    public static final String PAGE_WEB_TECHNOLOGY_HANDLED_BY_ME = "technology_handled_by_me";

    /** 页面 - 待办中心 - 我已处理 - 权限申请任务 */
    public static final String PAGE_WEB_APPLICATION_HANDLED_BY_ME = "permission_application_handled_by_me";

    /** 页面 - 待办中心 - 我已处理 - 异常复盘任务 */
    public static final String PAGE_WEB_ABNORMAL_REVIEW_HANDLED_BY_ME = "abnormal_review_handled_by_me";

    /** 页面 - 待办中心 - 我已处理 - 操作计划任务 */
    public static final String PAGE_WEB_OPERATION_PLAN_HANDLED_BY_ME = "operation_plan_handled_by_me";

    /** 页面 - 配置中心 - 邮件推送群组配置 */
    public static final String PAGE_WEB_EMAIL_SEND_GROUP = "email_sending_group_configuration";

    /** 页面 - 配置中心 - 用户群组配置 */
    public static final String PAGE_WEB_USER_GROUP = "user_group_configuration";

    /** 添加操作对象页面cid */
    public static final String OPERATION_OBJ_SELECT_PRODUCT_MODEL_CID = "add_operation_object";

    // =========== 实体表单标题 ============
    /**
     * 页面标题 - 任务中心 / 待办中心
     */
    public static class AssignmentTitle {
        /** 待我处理 */
        public final static String TO_BE_HANDLED = "backlog.title.toBeHandled";

        /** 我已处理 */
        public final static String HANDLED_BY_ME = "backlog.title.handledByMe";

        /** 我发起的 */
        public final static String INITIATED_BY_ME = "backlog.title.initiatedByMe";

        /** 网络变更 */
        public final static String NETWORK_CHANGE = "backlog.title.networkChange";

        /** 合作方网络变更 */
        public final static String PARTNER_NETWORK_CHANGE = "backlog.title.partnerNetworkChange";

        /** 技术管理 */
        public final static String TECHNOLOGY = "backlog.title.technology";

        /** 技术管理子任务 */
        public final static String SUB_TECHNOLOGY = "assignmentTitle.subTechnology";

        /** 故障管理 */
        public final static String FAULT = "backlog.title.fault";

        /** WarRoom 详情 */
        public final static String WAR_ROOM_DETAIL = "assignmentTitle.warRoomDetail";

        /** 权限申请 */
        public final static String PERMISSION = "backlog.title.permission";

        /** 打卡复盘 */
        public final static String CLOCK_REVIEW = "backlog.title.clockReview";

        /** 操作计划管理 */
        public final static String OPERATION_PLAN = "backlog.title.operationPlan";
    }

    /**
     * 页面标题 - 批次任务进展页签
     */
    public static class BatchTaskTitle {
        /** 通告信息 */
        public final static String NOTIFICATION_INFO = "notification.info";

        /** 通告审核 */
        public final static String NOTIFICATION_REVIEW = "notification.review";

        /** 反馈操作结果 */
        public final static String PROVIDE_OPERATION_RESULT = "provide.operation.result";

        /** 审核确认 */
        public final static String REVIEW_RECTIFY = "assignment.status.reviews.rectify.16";
    }

    /**
     * 网络变更任务单(复制)
     */
    public static final String BILL_NAME_NETWORK_CHANGE_BILL_COPY = "bill.name.network.change.copy";

    /**
     * 技术管理任务
     */
    public static final String BILL_NAME_TECHNOLOGY_MANAGEMENT_BILL = "技术管理任务单";

    /**
     * 技术管理任务单（复制）
     */
    public static final String BILL_NAME_TECHNOLOGY_MANAGEMENT_BILL_COPY = "bill.name.technology.management.copy";

    /**
     * 合作方网络变更任务
     */
    public static final String BILL_NAME_SUBCONTRACTOR_NETWORK_CHANGE_BILL = "合作方网络变更任务单";

    /**
     * 合作方网络变更任务单（复制）
     */
    public static final String BILL_NAME_SUBCONTRACTOR_NETWORK_CHANGE_BILL_COPY = "bill.name.subcontractor.network.change.copy";

    /**
     * 操作计划任务单(复制)
     */
    public static final String BILL_NAME_OPERATION_PLAN_COPY = "bill.name.operation.plan.copy";

    /**
     * 关联任务详情
     */
    public static final String TITLE_ASSOCIATE_ASSIGNMENT_DETAIL = "关联任务详情";

    /**
     * 申请客户授权凭证
     */
    public static final String APPLY_AUTHORIZATION = "apply.authorization";

    /**
     * 可关联任务
     */
    public static final String TITLE_ASSOCIABLE_ASSIGNMENT = "可关联任务";

    /**
     * CCN 默认授权文件申请
     */
    public static final String TITLE_CCN_APPLY_AUTHORIZATION_FILE = "default.authorization.file.apply";

    /**
     * CCN 查看授权文件申请
     */
    public static final String TITLE_CCN_SHOW_AUTHORIZATION_FILE = "default.authorization.file.view";

    /**
     * CCN 反馈授权结果
     */
    public static final String TITLE_CCN_FEED_BACK_THE_AUTHORIZATION_RESULT = "authorization.result.feedback";

    /** 案例确认 */
    public static final String TITLE_NOTIFICATION_CASE = "确认";

    /** 权限申请 */
    public static final String TITLE_PERMISSION_APPLICATION = "bill.name.permission.application.copy";

    /** 邮件推送群组 */
    public static final String TITLE_EMAIL_SEND_GROUP = "emailSendGroup.title";

    /** 用户群组 */
    public static final String TITLE_USER_GROUP = "userGroup.title";

    /**
     * CCN 申请单列表页-操作key-反馈
     */
    public static final String OPERATION_CCN_APPLY_PAGE_FEED_BACK = "uploadEvidence";

    /**
     * 页面 - 自动生成子任务-网络列表
     */
    public static final String PAGE_WEB_OPERAND_MANAGE_TASK_QUERY_NETWORK = "task_network_query";

    /**
     * 页面 - 子任务-网络名称-网络列表
     */
    public static final String PAGE_WEB_OPERAND_QUERY_NETWORK_NAME = "custom_network_query";

    /**
     * 页面 - 标准方案_已发布_仅变更单弹出页面-pageId
     */
    public static final String PAGE_STANDARD_SOLUTION_PUBLISHED_ID = "PAGE1005886504405807105";

    /**
     * 页面 - 标准方案_已发布_仅变更单弹出页面-BizObjectCode
     */
    public static final String PAGE_STANDARD_SOLUTION_PUBLISHED_BIZ_OBJECT_CODE = "standard_scheme_copy";

    /**
     * 技术管理任务-子任务流程审批页面pageId
     */
    public static final String PAGE_SUBTASK_FLOW_DETAIL_ID = "PAGE1010949797683027969";

    /**
     * 批次任务 - 取消发布页面
     */
    public static final String RELEASE_NOTICE_CANCEL = "release_notice_cancel";

    /**
     * V4单板信息-母版物料名点击弹窗布局
     */
    public static final String V4_BOARD_INFORMATION = "V4_board_information";
    public static final String V4_BOARD_INFORMATION_PAGEID = "PAGE1058484683032096769";

    public static final String TITLE_CLOCK_IN_TASK = "打卡单详情";

    /***************我的待办*****************/
    public static final String PARTNER_CHANGE_ORDER_TO_BE_HANDLED = "partner_change_order_to_be_handled";

    public static final String PARTNER_CHANGE_ORDER_INITIATED_BY_ME = "partner_change_order_initiated_by_me";

    public static final String PARTNER_CHANGE_ORDER_HANDLED_BY_ME = "partner_change_order_handled_by_me";

    public static final String FAULT_MANAGE_TO_BE_HANDLED = "fault_manager_to_be_handled";

    public static final String FAULT_MANAGE_HANDLED_BY_ME = "fault_manage_handled_by_me";

    public static final String FAULT_MANAGE_INITIATED_BY_ME = "fault_manage_initiated_by_me";

    /**************************************/

    /**
     * 打卡复盘-统一节点审批页面
     */
    public static final String PAGE_CLOCK_IN_REVIEWS = "PAGE1072905379946795008";

    /**
     * 页面标题 - 复盘任务
     */
    public static class ReviewOrderTitle {

        /** 复盘单详情 */
        public final static String REVIEW_ORDER_DETAIL = "reviewOrderTitle.reviewOrderDetail";

        /** 复盘单审批 */
        public final static String REVIEW_ORDER_APPROVE_DETAIL = "reviewOrderTitle.reviewOrderApproveDetail";
    }

    /** 页面 - 打卡复盘 - 确认转交人 */
    public static final String PAGE_CLOCK_IN_REVIEWS_CONFIRM_TRANSFER = "confirm_transfer";

    /** 页面 - 打卡复盘 - 关联任务 */
    public static final String PAGE_CLOCK_IN_REVIEWS_RELATION_ASSIGNMENT = "technology_assignment_options";

    /** 页面自定义参数名称 - 打卡复盘单ID */
    public static final String PARAM_CLOCK_IN_REVIEWS_ID = "clock_in_reviews_id";

    /** 操作场景新建页面pageID */
    public static final String OPERATION_SCENARIO_ADD_PAGE_ID = "PAGE0989830680972021765";

    /** 批次任务详情默认页面pageID */
    public static final String DEFAULT_BATCH_TASK_INFO_PAGE_ID ="PAGE1011221276299563009";

    /** 批次任务详情保障单页面pageID */
    public static final String GUARANTEE_BATCH_TASK_INFO_PAGE_ID ="PAGE1077979650525564929";

    /** 模板配置页面 */
    public static final String TEMPLATE_CONFIG ="template.config";
}
