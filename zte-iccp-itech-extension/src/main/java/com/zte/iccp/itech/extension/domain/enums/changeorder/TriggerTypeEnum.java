package com.zte.iccp.itech.extension.domain.enums.changeorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/09/14
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum TriggerTypeEnum implements SingletonTextValuePairsProvider {
    /** 工程交付 */
    ENGINEERING_DELIVERY("1", "工程交付", "Engineering Delivery"),
    /** 网络维护 */
    NETWORK_MAINTENANCE("2", "网络维护", "Network Maintenance"),
    ;

    private final String value;

    private final String zhCn;

    private final String enUs;

    public static TriggerTypeEnum fromValue(String value) {
        return Arrays.stream(values())
                .filter(v -> v.value.equals(value))
                .findAny()
                .orElse(null);
    }
}
