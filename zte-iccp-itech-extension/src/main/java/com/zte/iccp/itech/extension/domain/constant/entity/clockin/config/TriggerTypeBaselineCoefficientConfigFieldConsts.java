package com.zte.iccp.itech.extension.domain.constant.entity.clockin.config;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/09/14
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TriggerTypeBaselineCoefficientConfigFieldConsts {
    public static final String TRIGGER_TYPE = "trigger_type";

    public static final String FIRST_TIME_APPLY = "first_time_apply";

    public static final String OPERATION_WITHIN_BUSI = "operation_within_busi";

    public static final String BASELINE_COEFFICIENT_INNER = "baseline_coefficient_inner";

    public static final String BASELINE_COEFFICIENT_INTER = "baseline_coefficient_inter";
}
