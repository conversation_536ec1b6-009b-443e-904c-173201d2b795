package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.NetworkIdDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.ProdModelIdDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.VersionIdDeserializer;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.*;

@ApiModel("操作对象")
@Setter
@Getter
@BaseSubEntity.Info(value = "operation_object", parent = ChangeOrder.class)
public class OperationObject extends BaseSubEntity {

    @JsonProperty(value = BATCH_INFO_NO)
    @ApiModelProperty("批次号")
    private List<TextValuePair> batchNo;

    @JsonProperty(value = IS_MAIN_PRODUCT)
    @ApiModelProperty("主产品")
    private List<TextValuePair> isMainProduct;

    @JsonProperty(value = NETWORK_ID)
    @JsonDeserialize(using = NetworkIdDeserializer.class)
    @ApiModelProperty("网络ID")
    private String networkId;

    @JsonProperty(value = OFFICE_NAME)
    @ApiModelProperty("局点名称")
    private String officeName;

    @JsonProperty(value = PRODUCT_MODEL)
    @JsonDeserialize(using = ProdModelIdDeserializer.class)
    @ApiModelProperty("产品型号")
    private String productModel;

    @JsonProperty(value = CURRENT_VERSION)
    @JsonDeserialize(using = VersionIdDeserializer.class)
    @ApiModelProperty("当前版本")
    private String currentVersion;

    @JsonProperty(value = TARGET_VERSION)
    @JsonDeserialize(using = VersionIdDeserializer.class)
    @ApiModelProperty("目标版本")
    private String targetVersion;

    @JsonProperty(value = CURRENT_VERSION_THIRD_PARTY)
    @JsonDeserialize(using = VersionIdDeserializer.class)
    @ApiModelProperty("CCN第三方组网当前版本")
    private String currentVersionThirdParty;

    @JsonProperty(value = TARGET_VERSION_THIRD_PARTY)
    @JsonDeserialize(using = VersionIdDeserializer.class)
    @ApiModelProperty("CCN第三方组网目标版本")
    private String targetVersionThirdParty;

    @JsonProperty(value = TIME_CONFLICT_OBJECT)
    @ApiModelProperty("冲突")
    private String conflict;
}
