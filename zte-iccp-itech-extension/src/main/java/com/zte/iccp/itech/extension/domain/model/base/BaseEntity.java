package com.zte.iccp.itech.extension.domain.model.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;

import java.lang.annotation.*;
import java.util.Date;
import java.util.Set;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@Getter
@Setter
public abstract class BaseEntity {
    @JsonProperty(value = ID)
    private String id;

    @JsonProperty(value = CREATE_BY)
    private String createBy;

    @JsonProperty(value = CREATE_TIME)
    private Date createTime;

    @JsonProperty(value = LAST_MODIFIED_BY)
    private String lastModifiedBy;

    @JsonProperty(value = LAST_MODIFIED_TIME)
    private Date lastModifiedTime;

    @JsonIgnore
    private final Set<String> preparedFieldUIds = Sets.newHashSet();

    /**
     * 清理不需要提交字段值
     */
    @JsonIgnore
    public void clearEntityValue() {
        setCreateBy(null);
        setCreateTime(null);
        setLastModifiedBy(null);
        setLastModifiedTime(null);
    }

    @Inherited
    @Target(ElementType.TYPE)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface Info {
        /**
         * entityId
         */
        String value();
    }
}
