package com.zte.iccp.itech.extension.ability.changeorder;

import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.model.BatchHighLoadParam;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorBatchOperator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.core.orm.query.MultiFilter;
import com.zte.paas.lcap.ddm.common.api.control.container.IEntryTableSupport;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.FIELD_SUBCONTRACTOR_NETWORK_BATCH_OPERATOR_CID;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.HIGH_LOAD_NIGHT_SHIFT_OPERATIONS_REMINDER;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.HIGH_LOAD_OPERATOR_CONSECUTIVE_DAYS;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.CURRENT_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATE_PERSON;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.OPERATOR_ROLE;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInRecordFieldConsts.CLOCK_IN_TIME;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_END_TIME;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.PLAN_OPERATION_START_TIME;

/**
 * 批次人员高负荷拦截业务类
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/6/30
 */
public class BatchHighLoadInterceptAbility {

    /** 操作人员/操作负责人员 */
    private static final List<String> VALID_ROLES = Lists.newArrayList(OperatorRoleEnum.OPERATOR.getValue(),
            OperatorRoleEnum.OPERATING_SUPERVISOR.getValue());

    /** 配合保障类集：配合保障、配合保障（网优）、配合保障（产品）*/
    private static final List<String> CONFIG_GUARANTEE_SERVICE_NAMES = Lists.newArrayList("COOPERATING_TO_PROVIDE_SUPPORT",
            "COOPERATING_TO_PROVIDE_SUPPORT_NETWORK_OPTIMIZATION","COOPERATING_TO_PROVIDE_SUPPORT_PRODUCT");

    /** 待发通告后的状态集合 */
    private static final List<String> PENDING_ANNOUNCEMENT_STATUS_CODES = Lists.newArrayList(
            // 待操作执行
            AssignmentStatusEnum.OPERATION_EXECUTION.getValue(),
            // 待反馈操作结果
            AssignmentStatusEnum.RESULT_TOBE_BACK.getValue(),
            // 操作结果审核
            AssignmentStatusEnum.RESULT_UNDER_REVIEW.getValue(),
            // 已关闭
            AssignmentStatusEnum.CLOSE.getValue());

    /**
     * 人员高负荷拦截监听
     *
     * @param changeData changeData
     */
    public static void listenerHighLoadInterception(IDataModel dataModel, IFormView formView, ChangeData changeData) {
        String propertyKey = changeData.getProperty().getKey();

        List<String> subTabListenerList = Lists.newArrayList(OPERATOR_ROLE, OPERATE_PERSON);
        if (subTabListenerList.contains(propertyKey)) {
            int rowIndex = changeData.getRowIndex();
            handleHighLoadInterception(dataModel, formView, true, rowIndex);
            return;
        }

        List<String> operationTimeListenerList = Lists.newArrayList(PLAN_OPERATION_START_TIME, PLAN_OPERATION_END_TIME);
        // 高负荷场景监听（确认操作开始日期 - 确认操作结束日期）
        if (operationTimeListenerList.contains(propertyKey)) {
            Date startTime = (Date) dataModel.getValue(PLAN_OPERATION_START_TIME);
            Date endTime = (Date) dataModel.getValue(PLAN_OPERATION_END_TIME);
            if (startTime == null
                    || endTime == null) {
                handleNullTimeParameters(dataModel, formView);
                return;
            }

            Date oldValue = (Date) changeData.getOldValue();
            // 监听的结束日期，旧值为空不比对，不为空增加校验是否同一天（同一天不需要计算）
            if (PLAN_OPERATION_END_TIME.equals(propertyKey)
                    && oldValue != null) {
                LocalDate oldOperationEndDate = DateUtils.toLocalDate(oldValue);
                LocalDate newOperationEndDate = DateUtils.toLocalDate(endTime);
                // 结束日期旧值和新值要么都在0-6点，要么都在6点之后，否则还是要触发重新检测的
                if (oldOperationEndDate.equals(newOperationEndDate)
                        && ((DateUtils.checkTime(oldValue) && DateUtils.checkTime(endTime))
                        || (!DateUtils.checkTime(oldValue) && !DateUtils.checkTime(endTime)))) {
                    return;
                }
            }


            // 2、子表单行人员高负荷拦截计算
            handleHighLoadInterception(dataModel, formView, false, 0);
        }

    }

    /**
     * 空值逻辑问题
     *
     * @param dataModel dataModel
     * @param formView formView
     */
    private static void handleNullTimeParameters(IDataModel dataModel, IFormView formView) {
        // 日期中的任一一个为空，则清空
        IEntryTableSupport table = (IEntryTableSupport) formView.getControl(PARTNER_BATCH_NETWORK_COMPONENT_OPERATOR_TABLE_CID);
        String subTabCid = EntityHelper.getEntityId(BatchTask.class).equals(dataModel.getMainEntityType().getKey())
                ? FIELD_INTERNAL_NETWORK_BATCH_OPERATOR_CID
                : FIELD_SUBCONTRACTOR_NETWORK_BATCH_OPERATOR_CID;
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(subTabCid);
        for (int i = 0; i < dataEntityCollection.size(); i++) {
            table.getTableState().setCellAttribute(
                    i, BATCH_OPERATION_PERSON_SUB_TAB_OPERATE_PERSON_CID, CommonConstants.TIPS, new OptionsBuilder().build());
        }
    }

    /**
     * 检验是否触发高负荷拦截规则
     *
     * @param changeOrderId 网络变更单id
     * @return true：触发 false：不触发
     */
    public static boolean isTriggerHighLoadInterception(String changeOrderId,
                                                        Class<? extends BaseEntity> changeOrderEntityClass,
                                                        Date planOperationStartDate,
                                                        Date planOperationEndDate) {
        // 1、 确认操作开始时间 - 确认操作结束时间不能为空
        if (planOperationStartDate == null
                || planOperationEndDate == null) {
            return false;
        }

        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                changeOrderEntityClass,
                Lists.newArrayList(ID, OPERATION_TYPE, RESPONSIBLE_DEPT, TIME_ZONE),
                changeOrderId);
        // 2、校验当前单据是否保障单据，是否国内单据，确认操作开始时间 > 当前时间 && 确认操作结束时间不能为空
        if (CONFIG_GUARANTEE_SERVICE_NAMES.contains(changeOrder.getOperationType())
                || DeptTypeEnum.INTER == ChangeOrderAbility.getDeptType(changeOrder)) {
            return false;
        }

        TimeZoneEnum timeZoneEnum = changeOrder.getTimeZone();
        // 根据时区将系统时间转换为对应的时区的时间
        Date now = timeZoneEnum.pollute(new Date());

        // 确认操作开始时间在当前时间之前就不需要进行高负荷计算
        return planOperationStartDate.compareTo(now) >= 0
                // 确认操作结束时间是否和0-6点交叉
                && DateUtils.isNightTimeOverlap(planOperationStartDate,planOperationEndDate);
    }


    /**
     * 执行高负荷计算
     *
     * @param changeOrderId 网络变更单id
     * @param changeOrderEntityClass 网络变更单实体类型
     * @param planOperationEndDate 页面操作确认结束日期
     * @param operators 触发高负荷的人员工号集
     * @return 高负荷计算结果
     */
    public static Map<String, Map<LocalDate, Boolean>> executeHighLoadComputation(String batchId,
                                                                                  String changeOrderId,
                                                                                  Class<? extends BaseEntity> changeOrderEntityClass,
                                                                                  Date planOperationEndDate,
                                                                                  List<String> operators) {

        // 2、获取操作人员子表单全部行数据，获取到角色为操作人员/操作负责人员对应行的员工集，纪为S1
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                changeOrderEntityClass, Lists.newArrayList(ChangeOrderFieldConsts.TIME_ZONE), changeOrderId);
        TimeZoneEnum timeZoneEnum = changeOrder.getTimeZone();

        // 页面传递的确认操作结束日期 = 基准日期
        LocalDate baseDate = DateUtils.toLocalDate(planOperationEndDate);
        // 查询区间开始日期00:00:00
        Date start = DateUtils.toDate(baseDate.minusDays(6).atStartOfDay());
        // 查询区间结束日期23:59:59
        Date end = DateUtils.toDate(baseDate.minusDays(1).atTime(LocalTime.of(23, 59, 59)));
        // 3、批量查询打卡记录表，创建人 in S1的员工工号，打卡时间在查询时间区间范围之内的打卡记录数据，纪为S2
        List<ClockInRecord> clockInRecordList = getClockInRecordByRange(start, end, operators);

        // 3.2 打卡记录表的批次id+员工工号进行分组
        Map<String, ClockInRecord> groupByClockInRecord = groupByBatchIdAndEmployee(clockInRecordList);

        // 3.1 打卡记录高负荷计算（打卡记录为空属于正常）
        Map<String, Map<LocalDate, Boolean>> employeeIdToHighLoadDateResults = highLoadClockInRecordComputation(
                clockInRecordList, baseDate, timeZoneEnum);

        // 4、查询批次表，查询确认操作开始时间 - 确认操作结束时间区间和查询时间区间相交，批次状态为（待操作执行、待反馈操作结果、操作结果审核、已关闭）的批次数据集，纪为S4
        Map<String, IBatchTask> batchTaskIdToMap = findBatchTasksByTimeRangeAndGroupById(start, end, BatchTask.class);
        Map<String, IBatchTask> hzfBatchTaskIdToMap = findBatchTasksByTimeRangeAndGroupById(start, end, SubcontractorBatchTask.class);

        // 5、根据批次信息，查询批次操作人员列表，构建操作人员分组map
        Map<String, List<BatchTaskOperator>> groupedOperateByPerson = groupedOperateByPerson(
                batchId, new ArrayList<>(batchTaskIdToMap.keySet()), new ArrayList<>(hzfBatchTaskIdToMap.keySet()), operators);

        if (CollectionUtils.isEmpty(groupedOperateByPerson)) {
            return employeeIdToHighLoadDateResults;
        }

        batchTaskIdToMap.putAll(hzfBatchTaskIdToMap);
        BatchHighLoadParam batchHighLoadParam = new BatchHighLoadParam();
        batchHighLoadParam.setTimeZoneEnum(timeZoneEnum);
        batchHighLoadParam.setBatchIdAndEmployeeIdGrouped(groupByClockInRecord);
        batchHighLoadParam.setBaseDate(baseDate);
        batchHighLoadParam.setGroupedByOperatePerson(groupedOperateByPerson);
        batchHighLoadParam.setUserIdToRangeResult(employeeIdToHighLoadDateResults);
        batchHighLoadParam.setBatchTaskIdToMap(batchTaskIdToMap);
        return highLoadBatchOperatorComputation(batchHighLoadParam);
    }

    /**
     * 根据日期区间获取批次任务
     *
     * @param start 开始日期
     * @param end 结束日期
     * @return List<IBatchTask>
     */
    private static Map<String,IBatchTask> findBatchTasksByTimeRangeAndGroupById(Date start, Date end, Class<? extends BaseEntity> batchEntityClass) {
        // 状态查询发布通告之后的（不管是历史的还是未来的）
        List<IFilter> conditionFilters = Lists.newArrayList(new Filter(CURRENT_STATUS, Comparator.IN, PENDING_ANNOUNCEMENT_STATUS_CODES));
        // plan_operation_start_time <= end_date and plan_operation_end_time >= start_date
        conditionFilters.add(FilterHelper.newMultiFilter(
                new Filter(PLAN_OPERATION_START_TIME, Comparator.LE, end)
                        .and(new Filter(PLAN_OPERATION_END_TIME, Comparator.GE, start))));

        List<? extends BaseEntity> batchEntityList = QueryDataHelper.query(
                batchEntityClass,
                Lists.newArrayList(ID, CURRENT_STATUS, PLAN_OPERATION_START_TIME, PLAN_OPERATION_END_TIME),
                conditionFilters);

        if (CollectionUtils.isEmpty(batchEntityList)) {
            return MapUtils.newHashMap();
        }

        return batchEntityList.stream()
                .filter(IBatchTask.class::isInstance)
                .map(IBatchTask.class::cast)
                .collect(Collectors.toMap(IBatchTask::getId, iBatchTask -> iBatchTask));
    }

    /**
     * 根据日期区间和操作人员吗查询打卡记录
     *
     * @param start 开始日期
     * @param end 结束日期
     * @param operators 操作人员
     * @return 打卡记录
     */
    private static List<ClockInRecord> getClockInRecordByRange(Date start, Date end, List<String> operators) {
        MultiFilter multiFilter = FilterHelper.newMultiFilter(new Filter(CREATE_BY, Comparator.IN, operators)
                .and(new Filter(CLOCK_IN_TIME, Comparator.GE, start))
                .and(new Filter(CLOCK_IN_TIME, Comparator.LE, end)));

        return QueryDataHelper.query(
                ClockInRecord.class,
                Lists.newArrayList(ID, PID, CREATE_BY, CLOCK_IN_TIME),
                Lists.newArrayList(multiFilter));
    }

    /**
     * 获取操作人员列表数据，根据人员进行分组
     *
     * @param batchTaskIds batchTaskIds
     * @param operators operators
     * @return Map<String, List<BatchTaskOperator>>
     */
    private static Map<String, List<BatchTaskOperator>> groupedOperateByPerson(String batchId,
                                                                               List<String> batchTaskIds,
                                                                               List<String> hzfBatchTaskIds,
                                                                               List<String> operators) {
        // 4.1 查询操作人员列表，查询pid为S4的id，角色 = 操作人员/操作负责人员，人员 in S1的操作人员数据集（id、pid、角色、人员），纪为S5
        final MultiFilter operatorFilter = FilterHelper.newMultiFilter(
                new Filter(OPERATOR_ROLE, Comparator.IN, VALID_ROLES)
                        .and(new Filter(OPERATE_PERSON, Comparator.IN, operators))
        );

        // 2. 并行查询两个数据源
        List<BatchTaskOperator> mainOperators = queryBatchOperators(BatchTaskOperator.class,batchTaskIds, operatorFilter);
        List<BatchTaskOperator> subcontractorOperators = queryBatchOperators(SubcontractorBatchOperator.class, hzfBatchTaskIds, operatorFilter);

        // 3. 合并结果集
        List<BatchTaskOperator> allOperators = new ArrayList<>(mainOperators.size() + subcontractorOperators.size());
        allOperators.addAll(mainOperators);
        allOperators.addAll(subcontractorOperators);

        // 4. 空结果快速返回
        if (CollectionUtils.isEmpty(allOperators)) {
            return Collections.emptyMap();
        }

        // 4.2 S3打卡任务表的id和S2打卡记录表pid是关联的，意味着每一条S2的打卡记录对应者一个批次id。S5的数据去除属于S2的部分，剩余的数据纪为S6
        return allOperators.stream()
                .filter(operator -> operator.getOperatePerson() != null
                        // 排查当前在操作的批次
                        && !batchId.equals(operator.getPid()))
                .collect(Collectors.groupingBy(
                        operator -> operator.getOperatePerson().getEmpUIID(),
                        Collectors.mapping(Function.identity(), Collectors.toList())
                ));
    }


    /**
     * 查询批次操作人员列表
     *
     * @param batchOperatorEntityClass batchOperatorEntityClass
     * @param taskIds taskIds
     * @param filter filter
     * @return List<BatchTaskOperator>
     */
    private static List<BatchTaskOperator> queryBatchOperators(Class<? extends BaseSubEntity> batchOperatorEntityClass,
                                                               List<String> taskIds,
                                                               MultiFilter filter) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return new ArrayList<>();
        }

        return QueryDataHelper.query(
                batchOperatorEntityClass,
                Arrays.asList(ID, PID, OPERATOR_ROLE, OPERATE_PERSON),
                taskIds,
                Lists.newArrayList(filter)
        );
    }


    /**
     * 人员高负荷拦截计算逻辑
     *
     * @param enableRowListener 行监听
     * @param rowIndex 行监听的行号
     */
    public static void handleHighLoadInterception(IDataModel dataModel, IFormView formView, boolean enableRowListener, int rowIndex) {
        String batchId = (String) dataModel.getRootDataEntity().getPkValue();
        // 网络变更单id
        String changeOrderId = PropertyValueConvertUtil.getString(dataModel.getValue(BatchTaskFieldConsts.CHANGE_ORDER_ID));
        // 确认操作开始日期
        Date planOperationStartDate = (Date) dataModel.getValue(PLAN_OPERATION_START_TIME);
        // 确认操作结束日期
        Date planOperationEndDate = (Date) dataModel.getValue(PLAN_OPERATION_END_TIME);

        Class<? extends BaseEntity> changeOrderEntityClass = ChangeOrder.class;
        String subTabCid = CidConstants.FIELD_INTERNAL_NETWORK_BATCH_OPERATOR_CID;
        if (!EntityHelper.getEntityId(BatchTask.class).equals(dataModel.getMainEntityType().getKey())) {
            changeOrderEntityClass = SubcontractorChangeOrder.class;
            subTabCid = CidConstants.FIELD_SUBCONTRACTOR_NETWORK_BATCH_OPERATOR_CID;
        }

        IEntryTableSupport table = (IEntryTableSupport) formView.getControl(PARTNER_BATCH_NETWORK_COMPONENT_OPERATOR_TABLE_CID);
        // 1、是否满足触发高负荷拦截
        if (isTriggerHighLoadInterception(changeOrderId,
                changeOrderEntityClass, planOperationStartDate, planOperationEndDate)) {

            // 2、获取子表单操作人员，根据场景获取全量或单行的操作人员、操作责任人员
            List<String> operators = getSubFormOperationPersons(dataModel, table, enableRowListener, rowIndex, subTabCid);
            if (CollectionUtils.isEmpty(operators)) {
                return;
            }

            // 3、执行高负荷逻辑，获取人员 - 高负荷范围区间结果
            Map<String, Map<LocalDate, Boolean>> employeeIdToHighLoadDateResults =
                    executeHighLoadComputation(batchId, changeOrderId, changeOrderEntityClass, planOperationEndDate, operators);

            if (CollectionUtils.isEmpty(employeeIdToHighLoadDateResults)) {
                return;
            }

            // 加载时相同人员直接取值
            Map<String, MultiLangText> userIdToResultTipMap = MapUtils.newHashMap();

            // 4、监听单行或子表单全行人员提示赋值操作
            // 4.1监听单行人员提示赋值
            if (enableRowListener) {
                String empUserId = operators.get(0);
                Map<LocalDate, Boolean> dateToHighLoadTriggeredStatus = employeeIdToHighLoadDateResults.get(empUserId);
                setSubFormRowPersonTip(rowIndex, empUserId, dateToHighLoadTriggeredStatus, userIdToResultTipMap, table);
                return;
            }

            // 4.2 子表单全行人员提示赋值
            IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(subTabCid);
            for (int i = 0; i < dataEntityCollection.size(); i++) {
                DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
                String operatorRoleValue = TextValuePairHelper.getValue(dynamicDataEntity.get(OperatorFieldConsts.OPERATOR_ROLE));
                List<SingleEmployee> employees = JsonUtils.parseArray(dynamicDataEntity.get(OperatorFieldConsts.OPERATE_PERSON), SingleEmployee.class);
                if (!StringUtils.hasText(operatorRoleValue)
                        || employees.isEmpty()
                        || !VALID_ROLES.contains(operatorRoleValue)) {
                    return;
                }

                String empUserId = employees.get(0).getEmpUIID();
                Map<LocalDate, Boolean> dateToHighLoadTriggeredStatus = employeeIdToHighLoadDateResults.get(empUserId);
                setSubFormRowPersonTip(i, empUserId, dateToHighLoadTriggeredStatus, userIdToResultTipMap, table);
            }
            return;
        }

        // 不满足触发条件要清空提示
        clearSubformAndFetchOperators(dataModel, table);
    }

    /**
     * 设置子表单人员提示信息
     *
     * @param rowIndex 行索引
     * @param empUserId 员工工号
     * @param dateToHighLoadTriggeredStatus dateToHighLoadTriggeredStatus
     * @param userIdToResultTipMap userIdToResultTipMap
     * @param table table
     */
    private static void setSubFormRowPersonTip(int rowIndex,
                                               String empUserId,
                                               Map<LocalDate, Boolean> dateToHighLoadTriggeredStatus,
                                               Map<String, MultiLangText> userIdToResultTipMap,
                                               IEntryTableSupport table) {
        MultiLangText multiLangText = processHighLoadEmployeeEntry(
                empUserId,
                dateToHighLoadTriggeredStatus,
                userIdToResultTipMap);
        if (multiLangText != null) {
            updateControlState(multiLangText, table, rowIndex);
        }
    }

    /**
     * 获取子表单操作人员
     *
     * @param dataModel dataModel
     * @param table table
     * @param enableRowListener 是否监听行
     * @param rowIndex 行索引
     * @param subTabCid 子表单cid
     * @return 子表单操作人员
     */
    private static List<String> getSubFormOperationPersons(IDataModel dataModel,
                                                           IEntryTableSupport table,
                                                           boolean enableRowListener,
                                                           Integer rowIndex,
                                                           String subTabCid) {
        List<String> operators = new ArrayList<>();
        // 构建需要进行高负荷计算的人员集
        if (enableRowListener) {
            // 触发即默认清除
            table.getTableState().setCellAttribute(rowIndex, BATCH_OPERATION_PERSON_SUB_TAB_OPERATE_PERSON_CID, CommonConstants.TIPS, new OptionsBuilder().build());
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataModel.getEntryRowEntity(subTabCid, rowIndex);
            buildOperators(dynamicDataEntity, operators);
            return operators;
        }

        return clearSubformAndFetchOperators(dataModel, table);
    }

    /**
     * 清除子表单并返回操作人员/操作负责人
     *
     * @param dataModel dataModel
     * @param table table
     * @return List<String>
     */
    private static List<String> clearSubformAndFetchOperators(IDataModel dataModel, IEntryTableSupport table) {

        String subTabCid = CidConstants.FIELD_INTERNAL_NETWORK_BATCH_OPERATOR_CID;
        if (!EntityHelper.getEntityId(BatchTask.class).equals(dataModel.getMainEntityType().getKey())) {
            // 合作方子表单
            subTabCid = CidConstants.FIELD_SUBCONTRACTOR_NETWORK_BATCH_OPERATOR_CID;
        }

        List<String> operators = new ArrayList<>();
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(subTabCid);
        for (int i = 0; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            table.getTableState().setCellAttribute(i, BATCH_OPERATION_PERSON_SUB_TAB_OPERATE_PERSON_CID, CommonConstants.TIPS, new OptionsBuilder().build());
            buildOperators(dynamicDataEntity, operators);
        }

        return operators;
    }

    /**
     * 更新行人员的主要提示
     *
     * @param tipText 提示信息
     * @param table table
     * @param rowIndex 行索引
     */
    private static void updateControlState(MultiLangText tipText, IEntryTableSupport table, int rowIndex) {
        Map<String, Object> operatePersonMap = MapUtils.newHashMap(
                CommonConstants.TYPE, CommonConstants.I18N,
                CommonConstants.ZH_CN, String.format(ORANGE_FONT, tipText.getZhCN()),
                CommonConstants.EN_US, String.format(ORANGE_FONT, tipText.getEnUS()));

        table.getTableState().setCellAttribute(
                rowIndex, BATCH_OPERATION_PERSON_SUB_TAB_OPERATE_PERSON_CID, CommonConstants.TIPS, operatePersonMap);
    }

    /**
     * 获取操作人员/操作负责人员
     *
     * @param dynamicDataEntity dynamicDataEntity
     * @param operators operators
     */
    private static void buildOperators(DynamicDataEntity dynamicDataEntity, List<String> operators) {
        String operatorRoleValue = TextValuePairHelper.getValue(dynamicDataEntity.get(OperatorFieldConsts.OPERATOR_ROLE));
        List<SingleEmployee> employees = JsonUtils.parseArray(dynamicDataEntity.get(OperatorFieldConsts.OPERATE_PERSON), SingleEmployee.class);
        if (!StringUtils.hasText(operatorRoleValue)
                || employees.isEmpty()
                || !VALID_ROLES.contains(operatorRoleValue)) {
            return;
        }
        operators.add(employees.get(0).getEmpUIID());
    }

    /**
     * 打卡记录高负荷计算
     *
     * @param clockInRecordList 打卡记录集
     * @param pageBaseDate 页面基准日期 =确认操作结束日期
     * @param timeZoneEnum 时区
     * @return 员工id和高负荷日期范围区间是否夜间操作的map
     */
    private static Map<String, Map<LocalDate, Boolean>> highLoadClockInRecordComputation(List<ClockInRecord> clockInRecordList,
                                                                                         LocalDate pageBaseDate,
                                                                                         TimeZoneEnum timeZoneEnum) {
        Map<String, Map<LocalDate, Boolean>> resultMap = MapUtils.newHashMap();
        if (CollectionUtils.isEmpty(clockInRecordList)) {
            return resultMap;
        }
        // 3.1 根据S2的打卡记录，根据员工工号分组，for循环
        Map<String, List<ClockInRecord>> groupedByCreateBy = clockInRecordList.stream()
                .collect(Collectors.groupingBy(ClockInRecord::getCreateBy));
        // 打卡任务表id集
        for (Map.Entry<String, List<ClockInRecord>> entry : groupedByCreateBy.entrySet()) {
            Map<LocalDate, Boolean> dateToHighLoadTriggeredStatus = initializeDailyCheckMap(pageBaseDate);
            for (ClockInRecord clockInRecord : entry.getValue()) {
                // 获取打卡日期，如当前打卡时间为0-6点，当前日期在dailyCheckMap为false，则将对应日期的值修改为true
                Date clockInTime = timeZoneEnum.pollute(clockInRecord.getClockInTime());
                if (clockInTime == null) {
                    continue;
                }
                // 打卡记录日期存储的是东八区的时间，转成对应时区的实际打卡时间
                LocalDate localDate = DateUtils.toLocalDate(clockInTime);
                // 不在预期时间区间或已经为true或不是夜间操作 则跳过
                if (!dateToHighLoadTriggeredStatus.containsKey(localDate)
                        || dateToHighLoadTriggeredStatus.get(localDate)
                        || !DateUtils.checkTime(clockInTime)) {
                    continue;
                }

                dateToHighLoadTriggeredStatus.put(localDate, true);
            }
            resultMap.put(entry.getKey(), dateToHighLoadTriggeredStatus);
        }

        return resultMap;
    }

    /**
     * 最终高负荷拦截计算
     *
     * @param batchHighLoadParam batchHighLoadParam
     * @return 最终结果
     */
    private static Map<String, Map<LocalDate, Boolean>> highLoadBatchOperatorComputation(BatchHighLoadParam batchHighLoadParam) {
        // 根据用户id分组操作人员列表
        Map<String, List<BatchTaskOperator>> groupedByOperatePerson = batchHighLoadParam.getGroupedByOperatePerson();
        // 用户id和范围区间结果map
        Map<String, Map<LocalDate, Boolean>> userIdToRangeResult = batchHighLoadParam.getUserIdToRangeResult();
        // 基准日期
        LocalDate baseDate = batchHighLoadParam.getBaseDate();
        // 时区
        TimeZoneEnum timeZoneEnum = batchHighLoadParam.getTimeZoneEnum();
        // 批次id - 用户工号在操作记录表出现过的数据集（打卡记录会存在为空的场景，要判空）
        Map<String, ClockInRecord> batchIdAndEmployeeIdGrouped = batchHighLoadParam.getBatchIdAndEmployeeIdGrouped();
        // 批次表id和批次数据集
        Map<String, IBatchTask> batchTaskIdToMap = batchHighLoadParam.getBatchTaskIdToMap();

        for (Map.Entry<String, List<BatchTaskOperator>> entry : groupedByOperatePerson.entrySet()) {
            String empUserId = entry.getKey();

            Map<LocalDate, Boolean> dateToHighLoadTriggeredStatus = userIdToRangeResult.get(empUserId);
            if (CollectionUtils.isEmpty(dateToHighLoadTriggeredStatus)) {
                dateToHighLoadTriggeredStatus = initializeDailyCheckMap(baseDate);
            }

            for (BatchTaskOperator batchTaskOperator : entry.getValue()) {
                String batchTaskId = batchTaskOperator.getPid();
                String groupKey = batchTaskId + "-" + empUserId;
                // S5和S2重叠的部分去除（在打卡记录出现过）
                if (!CollectionUtils.isEmpty(batchIdAndEmployeeIdGrouped)
                        && batchIdAndEmployeeIdGrouped.get(groupKey) != null) {
                    continue;
                }
                IBatchTask batchTask = batchTaskIdToMap.get(batchTaskId);
                // 先获取基准日期（BA确认，以结束日期作为基准值）
                LocalDate batchBaseDate = DateUtils.toLocalDate(batchTask.getPlanOperationEndTime());
                // 基准日期不在范围内，或已经为true，不进行操作
                if (!dateToHighLoadTriggeredStatus.containsKey(batchBaseDate)
                        || dateToHighLoadTriggeredStatus.get(batchBaseDate)
                        || !DateUtils.isNightTimeOverlap(batchTask.getPlanOperationStartTime(), batchTask.getPlanOperationEndTime())) {
                    continue;
                }
                dateToHighLoadTriggeredStatus.put(batchBaseDate, true);
            }
            userIdToRangeResult.put(empUserId, dateToHighLoadTriggeredStatus);
        }
        return userIdToRangeResult;
    }

    /**
     * 根据批次id+员工工号进行分组
     *
     * @param clockInRecordList 打卡记录id
     * @return 分组map
     */
    private static Map<String, ClockInRecord> groupByBatchIdAndEmployee(List<ClockInRecord> clockInRecordList) {
        Map<String, ClockInRecord> groupedRecords = MapUtils.newHashMap();
        if (CollectionUtils.isEmpty(clockInRecordList)) {
            return groupedRecords;
        }
        // 3.2 根据S2的pid批量查询打卡任务表，获取到批次id集，纪为S3
        List<String> clockInTaskIds = clockInRecordList
                .stream()
                .map(BaseSubEntity::getPid)
                .distinct()
                .collect(Collectors.toList());

        List<ClockInTask> clockInTaskList = QueryDataHelper.get(
                ClockInTask.class,
                Lists.newArrayList(ID, ClockInTaskFieldConsts.BATCH_TASK_ID, ClockInTaskFieldConsts.OPERATOR, ClockInTaskFieldConsts.OPERATOR_ROLES),
                clockInTaskIds);

        if (CollectionUtils.isEmpty(clockInTaskList)) {
            return groupedRecords;
        }

        // 3.2.1 批次id+打卡记录表人员映射map，后续用来判断操作人员列表数据集去重使用（这个任务id的这个人两个角色都有，只要打一次卡，都认为这单据已经干了活）
        Map<String, String> clockInTaskToBatchTaskIdMap = clockInTaskList
                .stream()
                .collect(Collectors.toMap(BaseEntity::getId, ClockInTask::getBatchTaskId));

        for (ClockInRecord clockInRecord : clockInRecordList) {
            String createBy = clockInRecord.getCreateBy();
            String batchTaskId = clockInTaskToBatchTaskIdMap.get(clockInRecord.getPid());
            // 构建分组键：批次ID + "-" + 创建者
            String groupKey = batchTaskId + "-" + createBy;
            groupedRecords.putIfAbsent(groupKey, clockInRecord);
        }
        return groupedRecords;
    }

    /**
     * 处理高负荷人员主要提示信息
     *
     * @param empUserId 人员工号
     * @param dateToHighLoadTriggeredStatus 当前员工对应的日期范围触发高负荷的结果
     * @param tipMap 提示语言
     */
    public static MultiLangText processHighLoadEmployeeEntry(String empUserId,
                                                             Map<LocalDate, Boolean> dateToHighLoadTriggeredStatus,
                                                             Map<String, MultiLangText> tipMap) {
        // 存在行相同人员时复用
        MultiLangText existingTip = tipMap.get(empUserId);
        if (existingTip != null) {
            return existingTip;
        }

        MultiLangText newTip = createMultiLangText(dateToHighLoadTriggeredStatus);
        if (newTip != null) {
            tipMap.put(empUserId, newTip);
            return newTip;
        }
        return null;
    }

    /**
     * 生成人员对应的提示串
     *
     * @param dateToHighLoadTriggeredStatus 日期范围触发高负荷的结果
     * @return 当前人员提示串
     */
    private static MultiLangText createMultiLangText(Map<LocalDate, Boolean> dateToHighLoadTriggeredStatus) {
        if (CollectionUtils.isEmpty(dateToHighLoadTriggeredStatus)) {
            return null;
        }

        long trueCount = dateToHighLoadTriggeredStatus.values().stream().filter(Boolean.TRUE::equals).count();
        List<Boolean> lastTwo = dateToHighLoadTriggeredStatus.entrySet().stream()
                .skip(Math.max(0, dateToHighLoadTriggeredStatus.size() - 2))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        MultiLangText text = null;
        if (trueCount >= INTEGER_THREE) {
            text = new MultiLangText() {{
                setZhCN(MsgUtils.getLangMessage(ZH_CN, MessageConsts.HIGH_LOAD_CONSECUTIVE_NIGHT_OPERATION, trueCount));
                setEnUS(MsgUtils.getLangMessage(EN_US, MessageConsts.HIGH_LOAD_CONSECUTIVE_NIGHT_OPERATION, trueCount));
            }};
        }

        if (lastTwo.size() == INTEGER_TWO && lastTwo.get(0) && lastTwo.get(1)) {
            String zhMsg = MsgUtils.getLangMessage(ZH_CN, MessageConsts.HIGH_LOAD_NIGHT_OPERATION);
            String enMsg = MsgUtils.getLangMessage(EN_US, MessageConsts.HIGH_LOAD_NIGHT_OPERATION);

            if (text == null) {
                text = new MultiLangText() {{
                    setZhCN(zhMsg);
                    setEnUS(enMsg);
                }};
            } else {
                text.setZhCN(text.getZhCN() + "(" + zhMsg + ")");
                text.setEnUS(text.getEnUS() + "(" + enMsg + ")");
            }
        }
        return text;
    }

    /**
     * 获取操作变更说明
     *
     * @param employeeIdToHighLoadDateResults 高负荷拦截生成的人员 - 区间日期结果mao
     * @return 操作变更说明双语结果
     */
    public static List<MultiLangText> getOperationChangeDesc(Map<String, Map<LocalDate, Boolean>> employeeIdToHighLoadDateResults) {
        List<MultiLangText> multiLangTextList = new ArrayList<>();
        if (CollectionUtils.isEmpty(employeeIdToHighLoadDateResults)) {
            return multiLangTextList;
        }
        for (Map.Entry<String, Map<LocalDate, Boolean>> entry : employeeIdToHighLoadDateResults.entrySet()) {
            Map<LocalDate, Boolean> dateToStatus = entry.getValue();

            MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(entry.getKey());
            String employeeNameZh = employeeFormatNames.getZhCN();
            String employeeNameEn = employeeFormatNames.getEnUS();

            Pair<Integer, String> maxContinuousTruesFromEnd = findMaxContinuousTruesFromEnd(dateToStatus);
            if (maxContinuousTruesFromEnd != null
                    && maxContinuousTruesFromEnd.getLeft() > 1) {
                // 【xxx】已连续n天作为操作人或操作负责人，请合理安排操作。
                multiLangTextList.add(new MultiLangText() {{
                    setZhCN(MsgUtils.getLangMessage(ZH_CN, HIGH_LOAD_OPERATOR_CONSECUTIVE_DAYS,
                            employeeNameZh,
                            maxContinuousTruesFromEnd.getLeft(),
                            maxContinuousTruesFromEnd.getRight()));
                    setEnUS(MsgUtils.getLangMessage(EN_US, HIGH_LOAD_OPERATOR_CONSECUTIVE_DAYS,
                            employeeNameEn,
                            maxContinuousTruesFromEnd.getLeft(),
                            maxContinuousTruesFromEnd.getRight()));
                }});
            }


            Pair<Integer, String> trueCountAndDates = countAndConcatenateTrueDates(dateToStatus);
            if (trueCountAndDates != null
                    && trueCountAndDates.getLeft() > 2) {
                // 【xxx】最近一周已操作了x个夜班，请合理安排操作。（于MM-DD,MM-DD,MM-DD已安排夜间操作）
                multiLangTextList.add(new MultiLangText() {{
                    setZhCN(MsgUtils.getLangMessage(ZH_CN, HIGH_LOAD_NIGHT_SHIFT_OPERATIONS_REMINDER,
                            employeeNameZh,
                            trueCountAndDates.getLeft(),
                            trueCountAndDates.getRight()));
                    setEnUS(MsgUtils.getLangMessage(EN_US, HIGH_LOAD_NIGHT_SHIFT_OPERATIONS_REMINDER,
                            employeeNameEn,
                            trueCountAndDates.getLeft(),
                            trueCountAndDates.getRight()));
                }});
            }
        }
        return multiLangTextList;
    }

    /**
     * 解析数据库操作变更说明 - 根据语言构建多行文本进行展示
     *
     * @param operationChangeDesc 操作变更说明
     * @return 根据语言组装的多行文本
     */
    public static String getLocalizedDescription(String operationChangeDesc) {
        String language = ContextHelper.getLangId();
        List<MultiLangText> operationChangeDescs = JsonUtils.parseArray(operationChangeDesc, MultiLangText.class);
        StringBuilder result = new StringBuilder();

        for (MultiLangText desc : operationChangeDescs) {
            String text = desc.getTextByLanguage(language);
            if (text != null && !text.isEmpty()) {
                if (result.length() > 0) {
                    result.append("\n");
                }
                result.append(text);
            }
        }

        return result.toString();
    }

    /**
     * 预先填充指定日期范围内的所有日期到LinkedHashMap
     *
     * @param firstOverlapDate 基准日期
     * @return 区间日期
     */
    private static Map<LocalDate, Boolean> initializeDailyCheckMap(LocalDate firstOverlapDate) {
        // 计算开始和结束日期（包含目标区间的所有日期）
        LocalDate startDate = firstOverlapDate.minusDays(6);
        LocalDate endDate = firstOverlapDate.minusDays(1);

        // 使用LinkedHashMap保持插入顺序
        Map<LocalDate, Boolean> dailyCheckMap = new LinkedHashMap<>();

        // 遍历日期范围并初始化
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            dailyCheckMap.put(date, false);
        }

        return dailyCheckMap;
    }

    /**
     * 从后往前遍历，判断有多少个连续的true数量，同时统计日期
     *
     * @param map 轮询map
     * @return 从后往前连续true的数量，日期字符串
     */
    private static Pair<Integer,String> findMaxContinuousTruesFromEnd(Map<LocalDate, Boolean> map) {
        List<String> dates = new ArrayList<>();
        if (CollectionUtils.isEmpty(map)) {
            return null;
        }

        int currentLength = 0;
        int maxLength = 0;
        boolean counting = false;

        // 逆序遍历键集合（LinkedHashMap 按插入顺序排列）
        for (LocalDate date : reverseOrder(map.keySet())) {
            Boolean value = map.get(date);
            if (value == null) {
                continue;
            }

            if (value) {
                currentLength++;
                counting = true;
                maxLength = Math.max(maxLength, currentLength);
                dates.add(date.toString());
            } else {
                // 遇到第一个 false 则终止计数
                if (!counting){
                    break;
                }
            }
        }

        Collections.reverse(dates);
        return Pair.of(maxLength, String.join(",", dates));
    }

    /**
     * 辅助方法：逆序遍历 LinkedHashMap 的键
     *
     * @param keys
     * @return
     */
    private static Iterable<LocalDate> reverseOrder(Collection<LocalDate> keys) {
        List<LocalDate> list = new ArrayList<>(keys);
        Collections.reverse(list);
        return list;
    }

    /**
     * 统计有多少个true的同时，将日期用逗号拼接
     *
     * @param map map
     * @return left：count true的数量。right:为true对应的日期
     */
    private static Pair<Integer, String> countAndConcatenateTrueDates(Map<LocalDate, Boolean> map) {
        StringBuilder dateBuilder = new StringBuilder();
        int count = 0;

        for (Map.Entry<LocalDate, Boolean> entry : map.entrySet()) {
            if (Boolean.TRUE.equals(entry.getValue())) {
                count++;
                if (dateBuilder.length() > 0) {
                    dateBuilder.append(",");
                }
                dateBuilder.append(entry.getKey());
            }
        }

        return Pair.of(count, dateBuilder.toString());
    }
}
