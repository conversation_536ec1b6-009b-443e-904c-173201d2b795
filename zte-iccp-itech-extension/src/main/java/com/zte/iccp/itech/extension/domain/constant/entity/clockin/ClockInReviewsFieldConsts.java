package com.zte.iccp.itech.extension.domain.constant.entity.clockin;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/09/06
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ClockInReviewsFieldConsts {

    public static final String REVIEWS_NO = "reviews_no";

    public static final String ORDER_NO = "co_no";

    public static final String OPERATION_SUBJECT = "operation_subject";

    public static final String BATCH_NO = "batch_no";

    public static final String NETWORK_NAME = "network_name";

    public static final String PRODUCT_CLASSIFICATION = "product_classification";

    public static final String RESPONSIBLE_DEPT = "responsible_dept";

    public static final String MARKETING = "marketing";

    public static final String OPERATION_LEVEL = "operation_level";

    public static final String OPERATION_TYPE = "operation_type";

    public static final String OPERATION_TYPE_SELECT = "operation_type_select";

    public static final String PLAN_OPERATION_START_TIME = "plan_operation_start_time";

    public static final String PLAN_OPERATION_END_TIME = "plan_operation_end_time";

    public static final String OPERATION_OWNERS = "operation_owners";

    public static final String CURRENT_PROCESSOR = "current_processor";

    public static final String REPLAY_STATUS = "replay_status";

    public static final String ON_DUTY_DURATION_HOURS = "on_duty_duration_hours";

    public static final String CHANGE_ORDER_ID = "change_order_id";

    public static final String BATCH_TASK_ID = "batch_task_id";

    public static final String ASSIGNMENT_TYPE = "assignment_type";

    public static final String INIT_APPROVE_RESULT = "init_approve_result";
    public static final String INIT_SUBMITTER = "init_submitter";
    public static final String INIT_SUBMIT_TIME = "init_submit_time";

    public static final String ST_SUBMITTER = "st_submitter";
    public static final String ST_SUBMIT_TIME = "st_submit_time";

    public static final String AR_SUBMITTER = "ar_submitter";
    public static final String AR_SUBMIT_TIME = "ar_submit_time";

    public static final String RE_SUBMITTER = "re_submitter";
    public static final String RE_SUBMIT_TIME = "re_submit_time";

    /** 邮件抄送（复盘提交）*/
    public static final String ST_EMAIL = "st_email";

    /** 邮件抄送（复盘审核）*/
    public static final String AR_EMAIL = "ar_email";

    /** 邮件抄送（审核确认）*/
    public static final String RE_EMAIL = "re_email";

    /** 审核结果（复盘审核）*/
    public static final String AR_APPROVE_RESULT = "ar_approve_result";

    /** 是否整改 */
    public static final String IS_RECTIFY = "is_rectify";

    /** 原因分类 */
    public static final String REASON_CLASSIFY = "st_reason_classify";
}
