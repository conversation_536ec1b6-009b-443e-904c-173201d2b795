package com.zte.iccp.itech.extension.domain.enums.faultorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 故障等级
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum FaultLevelEnum implements SingletonTextValuePairsProvider {

    /**
     * 关键一级
     */
    CRITICAL_FIRST("1", "关键一级", "Critical Level 1"),

    /**
     * 关键二级
     */
    CRITICAL_SECOND("2", "关键二级", "Critical Level 2"),

    /**
     * 关键三级
     */
    CRITICAL_THIRD("3", "关键三级", "Critical Level 3");

    /**
     * 编码
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;

    /**
     * 查询枚举 - 中文名称
     * @param nameZh
     * @return AssignmentTypeEnum
     */
    public static FaultLevelEnum fromZhCn(String nameZh) {
        for (FaultLevelEnum faultLevelEnum : FaultLevelEnum.values()) {
            if (faultLevelEnum.getZhCn().equals(nameZh)) {
                return faultLevelEnum;
            }
        }

        return null;
    }

    public static List<TextValuePair> getPropValue(String nameZh) {
        FaultLevelEnum faultLevel = fromZhCn(nameZh);
        return Objects.isNull(faultLevel) ? Lists.newArrayList() : faultLevel.getPropValue();
    }
}
