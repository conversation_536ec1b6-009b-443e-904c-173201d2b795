package com.zte.iccp.itech.extension.domain.model;

import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024/7/2 下午1:46
 */
@ApiModel("分包商-批次-操作人员")
@Setter
@Getter
@BaseSubEntity.Info(value = "subcontractor_network_batch_operator", parent = SubcontractorBatchTask.class)
public class SubcontractorBatchOperator extends BatchTaskOperator {
}