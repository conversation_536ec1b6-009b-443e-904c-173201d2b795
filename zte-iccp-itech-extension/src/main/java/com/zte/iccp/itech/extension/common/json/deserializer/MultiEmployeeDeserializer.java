package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.Employee;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/03
 */
public class MultiEmployeeDeserializer extends JsonDeserializer<List<Employee>> {

    @Override
    public List<Employee> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        return p.getCodec().readValue(p, new TypeReference<List<Employee>>() {});
    }
}
