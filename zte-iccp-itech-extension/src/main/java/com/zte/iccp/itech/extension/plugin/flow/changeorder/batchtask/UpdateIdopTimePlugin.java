package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.IdopAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.ArrayList;
import java.util.Arrays;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * 批次任务，提交后修改审批
 *
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class UpdateIdopTimePlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String businessId = body.getBusinessId();
        BatchTask batchTask = BatchTaskAbility.get(businessId, new ArrayList<>());
        ChangeOrder changeOrder = ChangeOrderAbility.get(batchTask.getChangeOrderId(), Arrays.asList(ID, SOURCE));
        if (changeOrder == null || !DataSourceEnum.IDOP.name().equals(changeOrder.getSource())) {
            return false;
        }

        IdopAbility.updateConfirmOptTime(batchTask);
        return false;
    }
}
