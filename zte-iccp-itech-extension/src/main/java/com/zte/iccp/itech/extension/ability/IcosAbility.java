package com.zte.iccp.itech.extension.ability;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.iccp.itech.extension.ability.changeorder.ApprovalAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.CollectionUtilsEx;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.openapi.model.networksecurity.dto.*;
import com.zte.iccp.itech.extension.openapi.model.networksecurity.vo.ChangeOrderAggregationVO;
import com.zte.iccp.itech.extension.spi.client.IcosClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.itp.msa.core.model.PageRows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.COMMA;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.spi.client.IcosClient.ICOS_CONNECT_TIME_OUT;

/**
 * <AUTHOR> jiangjiawen
 * @date 2024/12/5
 */
public class IcosAbility {


    /**
     * 直接将当前处理人、责任人json存储在text文本中，会存在为"[]"的数据
     */
    private static final String PLACEHOLDER = "[]";

    /**
     * 批次操作人员任务
     */
    private static final String BATCH_OPERATOR_TASK = "batchOperatorTask";

    /**
     * 批次操作人员任务
     */
    private static final String BATCH_OPERATION_STAGE_CLOCK_TASK = "batchOperationStageClockTask";

    /**
     * 查询变更单信息
     *
     * @return List<ChangeOrderAggregationVO>
     */
    public static PageRows<ChangeOrderAggregationVO> queryChangeOrderInfo(ChangeOrderAggregationQueryDTO queryDTO) {
        // 1.第一次查询ids
        Pair<List<String>, Long> initIds = IcosClient.firstIdsQuery(queryDTO);
        if (initIds == null) {
            return emptyRows(queryDTO.getPageNum());
        }

        // 2.第二次查询具体的数据
        List<String> changeOrderIds = initIds.getLeft();
        List<ChangeOrderAggregationResultDTO> changeOrderAggregationResultDtos = IcosClient.queryChangeOrder(
                changeOrderIds, queryDTO.getLangId());
        if (CollectionUtils.isEmpty(changeOrderAggregationResultDtos)) {
            return emptyRows(queryDTO.getPageNum());
        }


        // 获取网络变更单维度下的批次id集
        List<String> batchIds = changeOrderAggregationResultDtos.stream()
                .map(ChangeOrderAggregationResultDTO::getBatchId)
                .filter(StringUtils::isNotEmpty)
                .distinct().collect(Collectors.toList());
        List<BatchOperatorDTO> operators = null;
        List<BatchOperationStageClockDTO> clocks = null;
        // 根据批次集并行异步获取批次操作人员信息和批次操作阶段打卡信息
        if (!CollectionUtils.isEmpty(batchIds)) {
            Map<String, Supplier<?>> tasks = createTasks(BATCH_OPERATOR_TASK, () -> IcosClient.queryBatchOperatorInfo(batchIds),
                    BATCH_OPERATION_STAGE_CLOCK_TASK, () -> IcosClient.queryBatchOperationStageClockInfo(batchIds));

            Map<String, Object> results = AsyncExecuteUtils.asyncQuery(tasks, Long.parseLong(ConfigHelper.get(ICOS_CONNECT_TIME_OUT)));
            Object batchOperatorTask = results.get(BATCH_OPERATOR_TASK);
            if (batchOperatorTask != null) {
                operators = JsonUtils.parseArray(batchOperatorTask, BatchOperatorDTO.class);
            }

            Object batchOperationStageClockTask = results.get(BATCH_OPERATION_STAGE_CLOCK_TASK);
            if (batchOperationStageClockTask != null) {
                clocks = JsonUtils.parseArray(batchOperationStageClockTask, BatchOperationStageClockDTO.class);
            }
        }

        PageRows<ChangeOrderAggregationVO> pageRows = new PageRows<ChangeOrderAggregationVO>() {{
            setCurrent(queryDTO.getPageNum());
            setTotal(initIds.getRight());
        }};

        String langId = queryDTO.getLangId();
        // 3.组装成三营稽核需要的数据
        transferChangeOrderVo(changeOrderAggregationResultDtos, operators, clocks, pageRows, langId);
        return pageRows;
    }

    public static Map<String, Supplier<?>> createTasks(String key1, Supplier<?> supplier1,
                                                       String key2, Supplier<?> supplier2) {
        return MapUtils.newHashMap(key1, supplier1,
                key2, supplier2);
    }

    /**
     * 数据VO包装
     *
     * @param changeOrderAggregationResultDtos 网络变更单基础信息（包含批次概要、操作对象和批次基础信息）
     * @param operators 批次操作人员
     * @param clocks 批次操作阶段打卡
     * @param pageRows 结果rows
     * @param langId 语言
     */
    private static void transferChangeOrderVo(List<ChangeOrderAggregationResultDTO> changeOrderAggregationResultDtos,
                                              List<BatchOperatorDTO> operators,
                                              List<BatchOperationStageClockDTO> clocks,
                                              PageRows<ChangeOrderAggregationVO> pageRows,
                                              String langId) {
        Map<String, List<BatchOperatorDTO>> groupedByBatchIdToOperators = MapUtils.newHashMap();
        Map<String, List<BatchOperationStageClockDTO>> groupedByBatchIdToClocks = MapUtils.newHashMap();
        if (operators != null) {
            groupedByBatchIdToOperators = operators.stream()
                    .collect(Collectors.groupingBy(BatchOperatorDTO::getBatchId));
        }

        if (clocks != null) {
            groupedByBatchIdToClocks = clocks.stream()
                    .collect(Collectors.groupingBy(BatchOperationStageClockDTO::getBatchId));
        }

        List<ChangeOrderAggregationVO> resultList = new ArrayList<>();
        // 1.根据变更单进行分组（包含批次概要、操作对象和批次基本信息）
        Map<String, List<ChangeOrderAggregationResultDTO>> groupedByChangeOrder = changeOrderAggregationResultDtos.stream()
                .collect(Collectors.groupingBy(ChangeOrderAggregationResultDTO::getChangeOrderId));
        List<LookupValue> lookupValues = LookupValueHelper.getLookupValues(LookupValueConstant.TIME_ZONE_ENUM);
        Map<String, String> lookupValueMap = lookupValues.stream()
                .collect(Collectors.toMap(LookupValue::getLookupCode, LookupValue::getMeaning, (v1, v2) -> v1));

        // 2.组装数据
        for (Map.Entry<String, List<ChangeOrderAggregationResultDTO>> entry : groupedByChangeOrder.entrySet()) {
            ChangeOrderAggregationVO aggregationVO = new ChangeOrderAggregationVO();
            // 2.1 网络变更单id为维度的网络变更单信息、批次概要、操作对象和批次基础信息（数据最小维度为操作对象的id）
            List<ChangeOrderAggregationResultDTO> changeOrderAggregationResultDTOList = entry.getValue();
            ChangeOrderAggregationResultDTO resultDTO = changeOrderAggregationResultDTOList.get(0);
            BeanUtils.copyProperties(resultDTO, aggregationVO);
            resultList.add(aggregationVO);
            aggregationVO.setIsEmergencyOperation(StringUtils.equals(resultDTO.getIsEmergencyOperation(), BoolEnum.Y.name()));
            aggregationVO.setIsNetCloseOrControlOperation(StringUtils.equals(resultDTO.getIsNetCloseOrControlOperation(), BoolEnum.Y.name()));
            aggregationVO.setTimeZoneName(lookupValueMap.get(resultDTO.getTimeZone()));
            // 当前处理人
            aggregationVO.setCurrentProcessor(personStrProcess(resultDTO.getCurrentProcessor()));
            // 当前状态
            aggregationVO.setAssignmentStatus(buildAssignmentStatusByLangId(resultDTO.getAssignmentStatus(), langId));
            // 当前进展
            aggregationVO.setCurrentProgress(buildCurrentProgressByLangId(resultDTO.getCurrentProgress(), resultDTO.getAssignmentType(), langId));
            // 工具名称
            aggregationVO.setToolName(buildToolName(resultDTO.getToolNameSelected(), resultDTO.getToolName()));

            Map<String, List<ChangeOrderAggregationResultDTO>> groupedByBatchNoToResultDtos = changeOrderAggregationResultDTOList.stream()
                    .filter(changeOrder -> StringUtils.isNotEmpty(changeOrder.getBatchNo()))
                    .collect(Collectors.groupingBy(ChangeOrderAggregationResultDTO::getBatchNo));
            if (CollectionUtils.isEmpty(groupedByBatchNoToResultDtos)) {
                continue;
            }

            List<BatchInfoDTO> batchInfoDTOS = new ArrayList<>();
            // 2.2 批次概要批次号为维度的批次概要数据填充（批次概要的批次号和批次的批次一对一）
            for (Map.Entry<String, List<ChangeOrderAggregationResultDTO>> batchSummaryEntry : groupedByBatchNoToResultDtos.entrySet()) {
                BatchInfoDTO batchInfoDTO = new BatchInfoDTO();
                List<ChangeOrderAggregationResultDTO> aggregationList = batchSummaryEntry.getValue();
                ChangeOrderAggregationResultDTO batchSummaryResultDTO = aggregationList.get(0);
                BeanUtils.copyProperties(batchSummaryResultDTO, batchInfoDTO);
                batchInfoDTO.setBatchStatus(buildAssignmentStatusByLangId(batchSummaryResultDTO.getBatchStatus(), langId));
                // 操作对象为维度的数据填充
                batchInfoDTO.setOperationObjectInfoDTOS(
                        CollectionUtilsEx.copyListProperties(aggregationList, OperationObjectDTO.class));

                // 批次操作人员数据填充
                if (batchSummaryResultDTO.getBatchId() != null) {
                    batchInfoDTO.setBatchOperatorDTOS(
                            transferBatchOperatorDtoByLangId(groupedByBatchIdToOperators.get(batchSummaryResultDTO.getBatchId()), langId));

                    // 批次操作阶段打卡数据填充
                    batchInfoDTO.setBatchOperationStageClockList(groupedByBatchIdToClocks.get(batchSummaryResultDTO.getBatchId()));
                }
                batchInfoDTOS.add(batchInfoDTO);
            }

            aggregationVO.setBatchInfoDTOS(batchInfoDTOS);
        }
        pageRows.setRows(resultList);
    }

    /***
     * 根据语言构建操作人员
     *
     * @param batchOperatorDtos 操作人员信息（中英文混合）
     * @param langId 语言
     * @return 操作人员信息
     */
    private static List<NetworkBatchOperatorDTO> transferBatchOperatorDtoByLangId(List<BatchOperatorDTO> batchOperatorDtos, String langId) {
        if (CollectionUtils.isEmpty(batchOperatorDtos)) {
            return Collections.emptyList();
        }
        List<NetworkBatchOperatorDTO> batchOperatorResultList = new ArrayList<>(batchOperatorDtos.size());
        for (BatchOperatorDTO batchOperatorDto : batchOperatorDtos) {
            NetworkBatchOperatorDTO networkBatchOperatorDTO = new NetworkBatchOperatorDTO();
            networkBatchOperatorDTO.setOperatorName(batchOperatorDto.getEmpName());
            networkBatchOperatorDTO.setIsRemote(StringUtils.equals(batchOperatorDto.getIsRemote(), BoolEnum.Y.name()));
            if (ZH_CN.equals(langId)) {
                networkBatchOperatorDTO.setOperatorRole(batchOperatorDto.getOperatorRoleZh());
                networkBatchOperatorDTO.setOperatorDepartment(batchOperatorDto.getOrgNamePathZh());
                networkBatchOperatorDTO.setOperatorAttribute(batchOperatorDto.getOperatorAttributeZh());
            } else {
                networkBatchOperatorDTO.setOperatorRole(batchOperatorDto.getOperatorRoleEn());
                networkBatchOperatorDTO.setOperatorDepartment(batchOperatorDto.getOrgNamePathEn());
                networkBatchOperatorDTO.setOperatorAttribute(batchOperatorDto.getOperatorAttributeEn());
            }
            batchOperatorResultList.add(networkBatchOperatorDTO);
        }

        return batchOperatorResultList;

    }


    /**
     * 构建工具名称（网络变更单工具名称用2个字段进行存载，根据结果组装）
     *
     * @param toolNameSelected 工具名称下拉多选
     * @param toolName 工具名称多行文本
     * @return 工具名称
     */
    private static String buildToolName(String toolNameSelected, String toolName) {
        if (StringUtils.isEmpty(toolNameSelected)
                || PLACEHOLDER.equals(toolNameSelected)) {
            return toolName;
        }
        List<TextValuePair> toolNameList = JsonUtils.parseObject(toolNameSelected, new TypeReference<List<TextValuePair>>() {});
        return toolNameList.stream().map(TextValuePair::getValue).collect(Collectors.joining(COMMA));
    }

    /**
     * 根据语言构建任务状态
     *
     * @param assignmentStatus 任务状态
     * @param langId 语言
     * @return 任务状态
     */
    private static String buildAssignmentStatusByLangId(String assignmentStatus, String langId) {
        if (StringUtils.isEmpty(assignmentStatus)) {
            return null;
        }
        AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.fromValue(assignmentStatus);
        return ZH_CN.equals(langId) ? assignmentStatusEnum.getZhCn() : assignmentStatusEnum.getEnUs();
    }

    /**
     * 根据语言构建当前进展
     *
     * @param currentProgress 当前进展
     * @param assignmentType 任务类型
     * @param langId 语言
     * @return 当前进展
     */
    private static String buildCurrentProgressByLangId(String currentProgress, String assignmentType, String langId) {
        if (StringUtils.isEmpty(currentProgress)) {
            return null;
        }

        // 根据任务类型获取对应的实体class
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromValue(assignmentType);
        ApproveFlowCodeEnum approveFlowCodeEnum = assignmentTypeEnum.getApproveFlowCodeEnum();
        SingletonTextValuePairsProvider approveNodeName = ApprovalAbility.getApproveNodeName(
                approveFlowCodeEnum.getFlowEntity(), currentProgress);
        return ZH_CN.equals(langId) ? approveNodeName.getZhCn() : approveNodeName.getEnUs();
    }

    /**
     * 空结果
     *
     * @param page 当前页
     * @return 空结果对象
     */
    private static PageRows<ChangeOrderAggregationVO> emptyRows(int page) {
        return new PageRows<ChangeOrderAggregationVO>() {{
            setRows(null);
            setCurrent(page);
            setTotal(0);
        }};
    }

    /**
     * 当前处理人、责任人低码组件解析成名字+工号，名字+工号
     *
     * @param personStr 人员组件str
     * @return 名字+工号，名字+工号
     */
    private static String personStrProcess(String personStr) {
        // 字段为空或为[]，不处理
        if (StringUtils.isEmpty(personStr)
                || PLACEHOLDER.equals(personStr)) {
            return null;
        }
        List<Employee> employeeList = JsonUtils.parseObject(personStr, new TypeReference<List<Employee>>() {});
        return EmployeeHelper.getEpmNameUIID(employeeList);
    }
}
