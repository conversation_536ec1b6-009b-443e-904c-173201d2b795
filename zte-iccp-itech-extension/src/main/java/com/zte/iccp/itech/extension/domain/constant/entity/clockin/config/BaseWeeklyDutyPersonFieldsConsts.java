package com.zte.iccp.itech.extension.domain.constant.entity.clockin.config;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class BaseWeeklyDutyPersonFieldsConsts {
    /** 星期一 */
    public static final String MONDAY = "monday";

    /** 星期二 */
    public static final String TUESDAY = "tuesday";

    /** 星期三 */
    public static final String WEDNESDAY = "wednesday";

    /** 星期四 */
    public static final String THURSDAY = "thursday";

    /** 星期五 */
    public static final String FRIDAY = "friday";

    /** 星期六 */
    public static final String SATURDAY = "saturday";

    /** 星期日 */
    public static final String SUNDAY = "sunday";
}
