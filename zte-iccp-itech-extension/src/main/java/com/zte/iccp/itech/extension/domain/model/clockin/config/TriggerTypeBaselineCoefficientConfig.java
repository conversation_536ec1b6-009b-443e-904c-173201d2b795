package com.zte.iccp.itech.extension.domain.model.clockin.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.TriggerTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.config.TriggerTypeBaselineCoefficientConfigFieldConsts.*;

/**
 * 触发类型基准系数配置
 * <AUTHOR>
 * @since 2024/09/14
 */
@Getter
@Setter
@BaseEntity.Info("trigger_type_baseline_coefficient_config")
public class TriggerTypeBaselineCoefficientConfig extends BaseEntity {
    @JsonProperty(value = TRIGGER_TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private TriggerTypeEnum triggerType;

    @JsonProperty(value = FIRST_TIME_APPLY)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum firstTimeApply;

    @JsonProperty(value = OPERATION_WITHIN_BUSI)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum operationWithinBusi;

    @JsonProperty(value = BASELINE_COEFFICIENT_INNER)
    private Double baselineCoefficientInner;

    @JsonProperty(value = BASELINE_COEFFICIENT_INTER)
    private Double baselineCoefficientInter;
}
