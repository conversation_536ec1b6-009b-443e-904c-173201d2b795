package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.InformEmailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;


/**
 * 批次任务/分包商批次任务-操作结果审批-通过 （知会通知）  数据兼容
 * 1.内部批次任务审核通过只涉及技术交付部/网络处审核、网服部审核人节点
 * 2.合作方批次任务审核通过只涉及网络责任人审核节点
 * 【${操作主题}${是否紧急操作}（批次${批次号}）】${操作者}通过操作结果审核
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/19
 */
public class ApprovalResultPassMailToPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        InformEmailPlugin plugin = new InformEmailPlugin();
        JSONObject variables = body.getVariables();
        variables.put(ApprovalConstants.NODE_OPERATION, ApproveResultEnum.PASS.name());

        variables.put(ApprovalConstants.NODE_NAME, ApproveNodeEnum.TD_NET_DEPT_APPROVE.name());
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(body.getFlowCode());
        if (ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowEnum) {
            variables.put(ApprovalConstants.NODE_NAME, PartnerApproveNodeEnum.PARTNER_NET_OWNER_APPROVAL.name());
        }
        body.setVariables(variables);
        return plugin.anyTrigger(body, out);
    }
}
