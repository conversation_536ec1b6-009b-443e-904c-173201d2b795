package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/28
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ImportanceEnum implements SingletonTextValuePairsProvider {
    /** 一星 */
    STAR1("1", "★", "★"),
    /** 二星 */
    STAR2("2", "★★", "★★"),
    /** 三星 */
    STAR3("3", "★★★", "★★★"),
    ;

    private final String value;

    private final String zhCn;

    private final String enUs;

    public static ImportanceEnum fromValue(String value) {
        for (ImportanceEnum importance : ImportanceEnum.values()) {
            if (importance.getValue().equals(value)) {
                return importance;
            }
        }

        return null;
    }

    public Integer getIntegerValue() {
        return Integer.valueOf(this.value);
    }

    public String getName(String lang) {
        return ZH_CN.equals(lang) ? this.zhCn : this.enUs;
    }
}
