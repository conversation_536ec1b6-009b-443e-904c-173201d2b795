package com.zte.iccp.itech.extension.domain.constant.entity.clockin;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ClockInCallPlanFieldConsts {
    public static final String OBJECT_ID = "object_id";

    public static final String PREV_PLAN_ID = "prev_plan_id";

    public static final String REASON = "reason";

    public static final String CALL_UTC_TIME = "call_utc_time";

    public static final String CALLEE = "callee";

    public static final String CONTENT = "content";

    public static final String CALL_STATUS = "call_status";

    public static final String RESULT_SEARCH_ID = "result_search_id";
}
