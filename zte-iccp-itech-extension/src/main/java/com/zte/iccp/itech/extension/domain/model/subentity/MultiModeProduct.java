package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.grantfile.IApprovalRecord;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.MultiModeProdConsts.*;

/**
 * 多模从属产品
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/8/8
 */
@ApiModel("多模从属产品")
@Setter
@Getter
@BaseSubEntity.Info(value = "multimode_product", parent = ChangeOrder.class)
public class MultiModeProduct extends BaseSubEntity implements IApprovalRecord {

    @ApiModelProperty("审核提交人")
    @JsonProperty(value = MULTIMODE_REVIEWER_SUBMITTER)
    private String approvedReviewer;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_MULTIMODE_PRODUCT)
    private Date approvedDate;

    @ApiModelProperty("多模产品")
    @JsonProperty(value = MULTIMODE_PRODUCTS)
    private String multimodeProducts;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION)
    private String opinion;

    @ApiModelProperty("审核结果")
    @JsonProperty(APPROVE_RESULT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveResultEnum result;

    @ApiModelProperty("审核人")
    @JsonProperty(APPROVED_BY)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee approver;

    @ApiModelProperty("网络服务部审核组")
    @JsonProperty(value = MULTIMODE_NET_APPROVE_TEAM)
    private String multiModeNetApproveTeam;

}
