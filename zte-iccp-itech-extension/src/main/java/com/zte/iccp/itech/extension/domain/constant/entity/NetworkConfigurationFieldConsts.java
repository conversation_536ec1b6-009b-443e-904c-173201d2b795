package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/28
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NetworkConfigurationFieldConsts {
    /**
     * 网络id
     */
    public static final String NETWORK_ID = "network_id";

    /**
     * 网络属性
     */
    public static final String NETWORK_ATTRIBUTE = "network_attribute";

    /**
     * 等级分值
     */
    public static final String GRADE_SCORE = "grade_score";
}