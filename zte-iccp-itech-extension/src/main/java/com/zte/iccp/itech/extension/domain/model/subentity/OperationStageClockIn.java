package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperationStageClockInFieldConsts.*;

/**
 * <AUTHOR>
 * @date 2025/1/9 下午2:53
 */
@Setter
@Getter
@BaseSubEntity.Info(value = "operation_stage_clock_in", parent = BatchTask.class)
public class OperationStageClockIn extends BaseSubEntity {

    @JsonProperty(value = OPERATION_PHASE)
    private String operationPhase;

    @JsonProperty(value = OPERATION_DURATION)
    private Integer operationDuration;

    @JsonProperty(value = STAGE_START_TIME)
    private Date stageStartTime;

    @JsonProperty(value = STAGE_END_TIME)
    private Date stageEndTime;

    @JsonProperty(value = STAGE_CHECK_IN_PERSON)
    private Object stageCheckInPerson;
}
