package com.zte.iccp.itech.extension.common.helper;

import com.alibaba.fastjson2.JSON;
import com.zte.itp.msa.client.util.HttpClientUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 外部系统调用helper
 *
 * <AUTHOR> 10335201
 * @date 2024-06-18 下午3:26
 **/
public class ClientHelper {
    /***
     * 外部POST接口调用，针对不符合ServiceData统一返回格式的老接口，低代码封装的client方法用不了，此处只返回原始object
     * @param url 外部接口URL
     * @param requestBody 接口入参
     * @param headers 接口头信息
     */
    public static String invokeServiceByUrlPost(String url, Object requestBody, Map<String, Object> headers) {
        Map<String, String> headersString = headers != null ? (Map)headers.entrySet().stream().collect(
                Collectors.toMap(Map.Entry::getKey, (e) -> {
                    return e.getValue() != null ? e.getValue().toString() : "";
                })) : new HashMap(16);
        String paramJson = JSON.toJSONString(requestBody);
        String response = HttpClientUtil.httpPostWithJSON(url, paramJson, headersString);
        return response;
    }
}
