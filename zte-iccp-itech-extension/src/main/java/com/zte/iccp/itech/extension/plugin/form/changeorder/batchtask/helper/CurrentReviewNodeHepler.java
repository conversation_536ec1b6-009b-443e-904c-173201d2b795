package com.zte.iccp.itech.extension.plugin.form.changeorder.batchtask.helper;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.platform.domain.i18n.I18nUtil;
import org.springframework.util.StringUtils;

import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.CURRENT_PROCESSOR;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.CURRENT_REVIEWER_NODE;

public class CurrentReviewNodeHepler {


    // 在图文展示中，设置当前处理节点
    public static String setRichTextCurrentNode(IFormView formView, ApproveRecord approveRecord,
                                              String richTextCurrentNode, boolean isPartner) {
        if (null == approveRecord) {
            return EMPTY_STRING;
        }
        if (ACTIVE_TASK_STATUS.equals(approveRecord.getTaskStatus())) {
            String currentReviewerNode =
                    I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), CURRENT_REVIEWER_NODE);
            String extendedCode = approveRecord.getExtendedCode();
            if (!StringUtils.hasText(extendedCode)) {
                return EMPTY_STRING;
            }
            String processNodeName = EMPTY_STRING;
            processNodeName = getProcessNodeName(isPartner, extendedCode);
            formView.getClientViewProxy().setControlState(richTextCurrentNode,
                    VALUE, currentReviewerNode + COLON + processNodeName);
            return currentReviewerNode + COLON + processNodeName + SEMICOLON + SPACE;
        }
        return EMPTY_STRING;
    }

    // 在图文展示中，设置当前处理人
    public static String setRichTextCurrentReviewer(IFormView formView, Map<String, String> currentReviewerMap,
                                                  String richTextCurrentReviewer) {
        StringBuilder stringBuilder = new StringBuilder();
        String currentReviewer =
                I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), CURRENT_PROCESSOR);
        for (Map.Entry<String, String>  currentReviewerSet : currentReviewerMap.entrySet()) {
            String userInfo = currentReviewerSet.getValue();
            stringBuilder.append(userInfo).append(SPACE);
        }
        if (StringUtils.hasText(stringBuilder.toString())) {
            stringBuilder.insert(INTEGER_ZERO, COLON);
            stringBuilder.insert(INTEGER_ZERO, currentReviewer);
            formView.getClientViewProxy().setControlState(richTextCurrentReviewer,
                    VALUE, stringBuilder);
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(SEMICOLON);
            return stringBuilder.toString();
        }
        return EMPTY_STRING;
    }

    private static String getProcessNodeName(boolean isPartner, String extendedCode) {
        String processNodeName;
        if (!isPartner) {
            ApproveNodeEnum approveNodeEnum = ApproveNodeEnum.getApproveNodeEnum(extendedCode);
            processNodeName = null == approveNodeEnum ? EMPTY_STRING : approveNodeEnum.getName(ContextHelper.getLangId());
        } else {
            PartnerApproveNodeEnum approveNodeEnum = PartnerApproveNodeEnum.getApproveNodeEnum(extendedCode);
            processNodeName = null == approveNodeEnum ? EMPTY_STRING : approveNodeEnum.getName(ContextHelper.getLangId());
        }
        return processNodeName;
    }
}
