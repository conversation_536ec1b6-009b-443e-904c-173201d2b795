package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ActionOperationLogParam;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.function.BiFunction;

/**
 * 操作管理 - 执行动作枚举
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/5/16
 */
@Getter
public enum OperationLogActionEnum {

    /** 动作：操作管理 */
    /**
     * 网络变更单 - 提交申请
     */
    SUBMIT_APPROVAL_NEWEST("submit_approval_newest", "submit.application", AssignmentTypeEnum.NETWORK_CHANGE, "log.operation.desc",
            Lists.newArrayList("getOperationDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 网络变更单 - 重新提交申请
     */
    RESUBMIT("resubmit", "resubmit.application", AssignmentTypeEnum.NETWORK_CHANGE, "log.operation.desc",
            Lists.newArrayList("getOperationDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 内部批次 - 变更通告 待操作执行 - 变更通告 = 实际指的的变更通告后的提交按钮
     */
    BATCH_CHANGE_NOTIFICATION("batch_commit", "change.operation.notice", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, "log.release.notify",
            Lists.newArrayList("getNotificationDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 内部批次 - 变更通告 待反馈操作结果 - 变更通告 = 实际是点击变更通告后回到待发通告节点
     */
    BATCH_RESULT_CHANGE_NOTIFICATION("result_change", "change.operation.notice", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, "log.release.notify",
            Lists.newArrayList("getNotificationDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 内部批次 - 取消操作 内部批次取消会触发取消操作审核，实际指的取消操作后 - 弹框 - 确认
     */
    BATCH_OPERATION_CANCAL("confirm", "operation.canceled.", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, "log.batch.cancel.desc",
            Lists.newArrayList("getOcOperationChangeDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 批次 - 挂起  暂无挂起原因也无映射字段
     */
    BATCH_SUSPEND("task_suspend", "operation.suspension", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, null,
            Lists.newArrayList(), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 批次 - 挂起超期自动关闭
     */
    BATCH_SUSPEND_AUTOMATIC_CLOSE("suspend_automatic_close", "suspend.overdue.automatic.close", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, null,
            Lists.newArrayList(), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 网络变更单 - 任务中心 废止
     */
    ABOLISH("abolish", "manual.abolition", AssignmentTypeEnum.NETWORK_CHANGE, null,
            Lists.newArrayList(), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 网络变更单 - 撤回
     */
    REVOKE("revoke", "manual.revoke", AssignmentTypeEnum.NETWORK_CHANGE, null,
            Lists.newArrayList(), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /** 合作方  */
    /**
     * 合作方网络变更单 - 提交申请
     */
    HZF_SUBMIT_APPROVAL("submit_approval", "submit.application", AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, "log.operation.desc",
            Lists.newArrayList("getOperationDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 合作方网络变更单 - 重新提交申请
     */
    HZF_RESUBMIT("resubmit", "resubmit.application", AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, "log.operation.desc",
            Lists.newArrayList("getOperationDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 合作方批次 - 变更通告 待操作执行 - 变更通告 = 实际指的的变更通告后的提交按钮
     */
    HZF_BATCH_CHANGE_NOTIFICATION("batch_commit", "change.operation.notice", AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, "log.release.notify",
            Lists.newArrayList("getNotificationDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 合作方批次 - 变更通告 待反馈操作结果 - 变更通告 = 实际是点击变更通告后回到待发通告节点
     */
    HZF_BATCH_RESULT_CHANGE_NOTIFICATION("result_change", "change.operation.notice", AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, "log.release.notify",
            Lists.newArrayList("getNotificationDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 合作方批次 - 取消操作 内部批次取消会触发取消操作审核，实际指的取消操作后 - 弹框 - 确认
     */
    HZF_BATCH_OPERATION_CANCAL("confirm", "operation.canceled.", AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, "log.batch.cancel.desc",
            Lists.newArrayList("getOcOperationChangeDesc"), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 合作方批次 - 挂起  暂无挂起原因也无映射字段
     */
    HZF_BATCH_SUSPEND("task_suspend", "operation.suspension", AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, null,
            Lists.newArrayList(), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 合作方批次 - 挂起超期自动关闭（自定义编码，没有实际的编码）
     */
    HZF_BATCH_SUSPEND_AUTOMATIC_CLOSE("suspend_automatic_close", "suspend.overdue.automatic.close", AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, null,
            Lists.newArrayList(), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 网络变更单 - 任务中心 废止
     */
    HZF_ABOLISH("abolish", "manual.abolition", AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, null,
            Lists.newArrayList(), OperationLogActionEnum::convertActionOperationLogContentByLanguage),

    /**
     * 合作方网络变更单 - 撤回
     */
    HZF_REVOKE("revoke", "manual.revoke", AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, null,
            Lists.newArrayList(), OperationLogActionEnum::convertActionOperationLogContentByLanguage),
    ;

    /**
     * 操作编码 - 动作：如挂起、取消、废止、提交单据、重新提交等
     */
    private final String operationCode;

    /**
     * 自定义操作名称msg
     */
    private final String customOperationNameMsg;

    /**
     * 任务类型 - 由于主单据和批次存在相同节点自定义编码，增加任务类型进行区分
     */
    private final AssignmentTypeEnum assignmentTypeEnum;

    /**
     * 操作日志说明msg模板
     */
    private final String operationDescMsg;

    /**
     * 字段调用方法，按顺序
     */

    private final List<String> methodNames;

    /**
     * 获取操作日志 - 说明方法
     */
    private final BiFunction<ActionOperationLogParam, String, String> getOperationDescMethod;

    OperationLogActionEnum(String operationCode,
                           String customOperationNameMsg,
                           AssignmentTypeEnum assignmentTypeEnum,
                           String operationDescMsg,
                           List<String> methodNames,
                           BiFunction<ActionOperationLogParam, String, String> getOperationDescMethod) {
        this.operationCode = operationCode;
        this.customOperationNameMsg = customOperationNameMsg;
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.operationDescMsg = operationDescMsg;
        this.methodNames = methodNames;
        this.getOperationDescMethod = getOperationDescMethod;
    }

    /**
     * 根据审批节点获取操作日志枚举
     *
     * @param action 执行动作
     * @param assignmentTypeEnum 任务类型
     * @return OperationLogEnum
     */
    public static OperationLogActionEnum getOperationLogActionEnum(String action, AssignmentTypeEnum assignmentTypeEnum) {
        return Arrays.stream(OperationLogActionEnum.values())
                .filter(operationLog -> action.equals(operationLog.getOperationCode())
                        && assignmentTypeEnum == operationLog.getAssignmentTypeEnum())
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取操作日志说明调用方法
     *
     * @param businessId 业务id
     * @return 操作日志说明
     */
    public String getLogContent(String businessId, String assignmentCode, String language) {
        ActionOperationLogParam operationLogParam = new ActionOperationLogParam();
        operationLogParam.setOperationLogActionEnum(this);
        operationLogParam.setBusinessId(businessId);
        operationLogParam.setAssignmentCode(assignmentCode);

        return this.getOperationDescMethod.apply(operationLogParam, language);
    }

    private static String convertActionOperationLogContentByLanguage(ActionOperationLogParam operationLogParam, String language) {
        return OperationLogRecordAbility.convertActionOperationLogContentByLanguage(operationLogParam, language);
    }

    /**
     * 是否同步批次保障任务的值
     *  取消、挂起、挂起超期自动关闭
     *
     * @return true:是，false:否
     */
    public boolean isSyncBatchGuaranteeActionData() {
        return Lists.newArrayList(
                BATCH_OPERATION_CANCAL,
                BATCH_SUSPEND,
                BATCH_SUSPEND_AUTOMATIC_CLOSE
        ).contains(this);
    }
}
