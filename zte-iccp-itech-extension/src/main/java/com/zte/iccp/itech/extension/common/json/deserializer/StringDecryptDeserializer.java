package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;

import java.io.IOException;

/**
 * 手机号解密【aes解密】
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/23
 */
public class StringDecryptDeserializer extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String encrypted = p.getCodec().readValue(p, String.class);
        return ConfigHelper.decrypt(encrypted);
    }
}
