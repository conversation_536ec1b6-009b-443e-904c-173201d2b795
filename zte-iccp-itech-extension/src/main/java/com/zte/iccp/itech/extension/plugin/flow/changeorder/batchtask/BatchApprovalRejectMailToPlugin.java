package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.InformEmailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;


/**
 *
 * 批次任务/分包商批次 - 驳回知会通知
 * 【网络变更操作知会】${操作者}审核驳回${操作主题}${是否紧急操作}（批次${批次号}），请您及时处理！
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/23
 */
public class BatchApprovalRejectMailToPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        InformEmailPlugin plugin = new InformEmailPlugin();
        JSONObject variables = body.getVariables();
        variables.put(ApprovalConstants.NODE_OPERATION, ApproveResultEnum.REJECT.name());

        body.setVariables(variables);
        return plugin.anyTrigger(body, out);
    }
}
