package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/08/29
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum BatchOperatorAttrEnum implements SingletonTextValuePairsProvider {
    /** 本地中方 */
    LOCAL_CHINESE("a", "本地中方", "Local Chinese"),
    /** 本地外籍 */
    LOCAL_FOREIGNER("b", "本地外籍", "Local Foreign"),
    /** 网服/产研 */
    NET_SRV_PROD_DEV("c", "其他(网服/产研)", "Other(Network Service Dept./RD)"),
    /** 技术交付部/网络处 */
    TECH_DELIV_NET_OFFICE("d", "技术交付部/网络处", "Technology Delivery Dept."),
    /** 未知 */
    UNKNOWN("x", "未知(未获取到人员属性)", "Unknown (no personnel attribute obtained)"),
    ;

    private final String value;

    private final String zhCn;

    private final String enUs;

    public static BatchOperatorAttrEnum fromValue(String target) {
        for (BatchOperatorAttrEnum value : values()) {
            if (value.getValue().equals(target)) {
                return value;
            }
        }

        throw new IllegalArgumentException(target);
    }
}
