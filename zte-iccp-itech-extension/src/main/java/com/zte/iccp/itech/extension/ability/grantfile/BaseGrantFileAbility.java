package com.zte.iccp.itech.extension.ability.grantfile;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchSummary;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.zlic.model.SecretKey;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;

/**
 * <AUTHOR> 10284287
 * @since 2024/08/08
 */
abstract class BaseGrantFileAbility {

    private static final String RSA_PRI_KEY = "grantFile.secretKey.rsaPriKey";

    private static final String RSA_PUB_KEY = "grantFile.secretKey.rsaPubKey";

    protected static SecretKey getSecretKey() {
        return new SecretKey(ConfigHelper.get(RSA_PRI_KEY), ConfigHelper.get(RSA_PUB_KEY));
    }

    protected static IBatchTask getBatchTask(ApproveFlowCodeEnum flow, String batchTaskId) {
        return (IBatchTask) QueryDataHelper.get(
                GrantFileConfig.ALL.get(flow).getBatchTaskEntity(),
                GrantFileConfig.ALL.get(flow).getBatchTaskFields(),
                batchTaskId);
    }

    protected static IBatchTask getBatchTask(ApproveFlowCodeEnum flow, String changeOrderId, String batchNo) {
        GrantFileConfig.Config cfg = GrantFileConfig.ALL.get(flow);
        return (IBatchTask) QueryDataHelper.queryOne(
                cfg.getBatchTaskEntity(),
                cfg.getBatchTaskFields(),
                Lists.newArrayList(
                        new Filter(cfg.getBatchTaskPidField(), Comparator.EQ, changeOrderId),
                        new Filter(cfg.getBatchTaskNoField(), Comparator.EQ, batchNo)));
    }

    protected static IChangeOrder getChangeOrder(ApproveFlowCodeEnum flow, String changeOrderId) {
        return (IChangeOrder) QueryDataHelper.get(
                GrantFileConfig.ALL.get(flow).getChangeOrderEntity(),
                GrantFileConfig.ALL.get(flow).getChangeOrderFields(),
                changeOrderId);
    }

    protected static IBatchSummary getBatchSummary(
            ApproveFlowCodeEnum flow,
            IChangeOrder changeOrder,
            String batchNo) {
        return QueryDataHelper.queryOne(
                GrantFileConfig.ALL.get(flow).getBatchSummaryEntity(),
                GrantFileConfig.ALL.get(flow).getBatchSummaryFields(),
                changeOrder.getId(),
                Lists.newArrayList(new Filter(
                        GrantFileConfig.ALL.get(flow).getBatchSummaryBatchNoField(),
                        Comparator.EQ,
                        Lists.newArrayList(batchNo))));
    }
}
