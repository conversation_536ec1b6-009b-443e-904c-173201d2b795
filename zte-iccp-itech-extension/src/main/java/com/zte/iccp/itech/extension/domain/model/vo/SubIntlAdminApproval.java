package com.zte.iccp.itech.extension.domain.model.vo;

import com.zte.iccp.itech.extension.domain.model.IntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import lombok.Getter;
import lombok.Setter;

/**
 * 批次 国际会签
 *
 * <AUTHOR> 10284287
 * @since 2024/06/03
 */
@Getter
@Setter
@BaseSubEntity.Info(value = "batch_intl_admin_approval", parent = BatchTask.class)
public class SubIntlAdminApproval extends IntlAdminApproval {
}