package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.ability.EmailAbility;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.EmailConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * 邮件、iCenter消息标题枚举
 *
 * 邮件默认为催办标题，iCenter默认为网络变更操作审批通知。
 * 根据类型区分，变更单找变更单、合作方找合作方
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/26
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum MessageTitleEnum {

    /**
     * 变更单催办
     */
    CHANGE_PRESS_TITLE(Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH), NotifyTypeEnum.PRESS,
            "email.change.press.message.title", "iCenter.change.approval.notify.message.title"),
    /**
     * 合作方变更单催办
     */
    SUBCONTRACTOR_CHANGE_PRESS_TITLE(Lists.newArrayList(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH), NotifyTypeEnum.PRESS,
            "email.subcontractor.change.press.message.title", "iCenter.subcontractor.change.approval.notify.message.title"),
    /**
     * 变更单知会
     */
    CHANGE_INFORM_TITLE(Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH), NotifyTypeEnum.INFORM,
            "email.change.inform.message.title", "iCenter.change.approval.notify.message.title"),
    /**
     * 合作方变更单知会
     */
    SUBCONTRACTOR_CHANGE_INFORM_TITLE(Lists.newArrayList(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH), NotifyTypeEnum.INFORM,
            "email.subcontractor.change.inform.message.title", "iCenter.subcontractor.change.approval.notify.message.title"),
    /**
     * 变更单通告
     */
    CHANGE_NOTIFY_TITLE(Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH), NotifyTypeEnum.RELEASE_NOTICE,
            "email.change.notify.message.title", "iCenter.change.operation.notify.message.title"),
    /**
     * 合作方变更单发布通告
     */
    SUBCONTRACTOR_CHANGE_NOTIFY_TITLE(Lists.newArrayList(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH),NotifyTypeEnum.RELEASE_NOTICE,
            "email.subcontractor.change.notify.message.title","iCenter.subcontractor.change.operation.notify.message.title"),
    /**
     * 变更单已取消
     */
    CHANGE_OPERATION_CANCEL_TITLE(Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH),NotifyTypeEnum.CANCEL,
            "email.change.operation.cancel.message.title","iCenter.change.operation.notify.message.title"),
    /**
     * 合作方变更单已取消
     */
    SUBCONTRACTOR_CHANGE_OPERATION_CANCEL_TITLE(Lists.newArrayList(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH),NotifyTypeEnum.CANCEL,
            "email.subcontractor.change.operation.cancel.message.title","iCenter.subcontractor.change.operation.notify.message.title"),
    /**
     * 变更单已挂起
     */
    CHANGE_OPERATION_SUSPEND_TITLE(Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH),NotifyTypeEnum.SUSPEND,
            "email.change.operation.suspend.message.title","iCenter.change.operation.notify.message.title"),
    /**
     * 合作方变更单已挂起
     */
    SUBCONTRACTOR_CHANGE_OPERATION_SUSPEND_TITLE(Lists.newArrayList(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH),NotifyTypeEnum.SUSPEND,
            "email.subcontractor.change.operation.suspend.message.title","iCenter.subcontractor.change.operation.notify.message.title"),
    /**
     * 变更单已关闭
     */
    CHANGE_OPERATION_CLOSE_TITLE(Lists.newArrayList(AssignmentTypeEnum.NETWORK_CHANGE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH),NotifyTypeEnum.CLOSE,
            "email.change.operation.close.message.title","iCenter.change.operation.notify.message.title"),
    /**
     * 合作方变更单已关闭
     */
    SUBCONTRACTOR_CHANGE_OPERATION_CLOSE_TITLE(Lists.newArrayList(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH),NotifyTypeEnum.CLOSE,
            "email.subcontractor.change.operation.close.message.title","iCenter.subcontractor.change.operation.notify.message.title"),

    /**
     * 打卡复盘 - 催办
     */
    CLOCKIN_REVIEW_PRESS_TITLE(Lists.newArrayList(AssignmentTypeEnum.CLOCK_REVIEW), NotifyTypeEnum.PRESS,
            "email.clockin.review.press.message.title", "email.clockin.review.press.message.title"),

    /**
     * 打卡复盘 - 知会
     */
    CLOCKIN_REVIEW_INFORM_TITLE(Lists.newArrayList(AssignmentTypeEnum.CLOCK_REVIEW), NotifyTypeEnum.INFORM,
            "email.clockin.review.press.message.title", "email.clockin.review.press.message.title"),
    ;

    /**
     * 任务类型：区分变更单、合作方变更单
     */
    private final List<AssignmentTypeEnum> assignmentTypeEnumList;

    /**
     * 通知类型：根据通知类型枚举判断是催办、知会、通知、已挂起、已取消、已关闭
     */
    private final NotifyTypeEnum notifyTypeEnum;

    /**
     * 邮件标题消息
     */
    private final String emailMsg;

    /**
     * iCenter标题消息
     */
    private final String iCenterMsg;

    /**
     * 构建邮件和iCenter标题
     *
     * @param assignmentTypeEnum 任务类型
     * @param notifyTypeEnum 通知类型
     * @return 标题map
     */
    public static Map<String, Object> buildMessageTitle(AssignmentTypeEnum assignmentTypeEnum, NotifyTypeEnum notifyTypeEnum) {
        MessageTitleEnum messageTitleEnum = Arrays.stream(values())
                .filter(item -> item.getAssignmentTypeEnumList().contains(assignmentTypeEnum)
                        && notifyTypeEnum == item.getNotifyTypeEnum())
                .findFirst()
                .orElse(null);
        if (messageTitleEnum == null) {
            return Collections.emptyMap();
        }

        return MapUtils.newHashMap(EmailConsts.EMAIL_MESSAGE_TITLE_ZH,
                MsgUtils.getLangMessage(ZH_CN, messageTitleEnum.emailMsg),

                EmailConsts.EMAIL_MESSAGE_TITLE_EN,
                MsgUtils.getLangMessage(EN_US, messageTitleEnum.emailMsg),

                EmailConsts.ICENTER_MESSAGE_TITLE_ZH,
                MsgUtils.getLangMessage(ZH_CN, messageTitleEnum.iCenterMsg),

                EmailConsts.ICENTER_MESSAGE_TITLE_EN,
                MsgUtils.getLangMessage(EN_US, messageTitleEnum.iCenterMsg)
        );

    }
}
