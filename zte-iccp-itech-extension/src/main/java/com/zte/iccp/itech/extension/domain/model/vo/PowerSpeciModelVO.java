package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> jiangjiawen
 * @date 2024/12/26
 */
@ApiModel("电源规格型号(dataKey)")
@Setter
@Getter
public class PowerSpeciModelVO {

    /** 序号 */
    @ApiModelProperty("序号")
    private String seqNum;

    @JsonProperty("custom_8p8pu195")
    @ApiModelProperty("单据编号")
    private String orderNo;

    @JsonProperty("custom_s7m9chfv")
    @ApiModelProperty("操作主题")
    private String operationSubject;

    @JsonProperty("custom_fqif56qw")
    @ApiModelProperty("任务状态")
    private String assignmentStatus;

    @JsonProperty("custom_g7ssxl8d")
    @ApiModelProperty("操作类型")
    private String operationType;

    @JsonProperty("custom_u87dwblf")
    @ApiModelProperty("代表处")
    private String oresponsibleDept;

    @JsonProperty("custom_89zu9hu7")
    @ApiModelProperty("产品经营团队")
    private String productTeam;

    @JsonProperty("custom_bs9ng8me")
    @ApiModelProperty("产品线")
    private String productLine;

    @JsonProperty("custom_neamkl9x")
    @ApiModelProperty("产品大类")
    private String prodMainCategory;

    @JsonProperty("custom_rkodlwuj")
    @ApiModelProperty("产品小类")
    private String prodSubCategory;

    @JsonProperty("custom_4frfue3p")
    @ApiModelProperty("计划操作开始时间")
    private String operationStartTime;

    @JsonProperty("custom_tzlrflio")
    @ApiModelProperty("物料名称")
    private String materialName;

    @JsonProperty("custom_2f5fdcs3")
    @ApiModelProperty("硬件版本")
    private String hardwareVersion;

    @JsonProperty("custom_crnsrpuz")
    @ApiModelProperty("数量")
    private Integer psmCount;

    @JsonProperty("custom_jx4cewey")
    @ApiModelProperty("序列号")
    private String serialNumber;

    @JsonProperty("custom_0r32nzj4")
    @ApiModelProperty("软件版本")
    private String softwareVersion;

    @JsonProperty("custom_i4cupuky")
    @ApiModelProperty("生产批次")
    private String productionBatch;

    @JsonProperty("custom_878a1bm9")
    @ApiModelProperty("备注说明")
    private String psmDescription;
}
