package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR>
 * @since 2024/04/17
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
/* 网络总监处审核结果*/
public enum NetApprovalResultEnum implements SingletonTextValuePairsProvider {
    /**
     * 同意&需升级审核
     */
    PASS("同意&需升级审核", "Agreed & Upgrade review"),
    /**
     * 同意&需升级审核
     */
    SKIP("同意&终结审核", "Agree & End review"),
    /**
     * 不同意
     */
    TERMINATE("不同意", "Disagree"),

    ;
    private final String zhCn;
    private final String enUs;

    @Override
    public String getValue() {
        return name();
    }

    /**
     * 根据语言环境获取对应描述信息
     *
     * @param language
     * @return String
     */
    public String getTextByLanguage(String language) {
        return ZH_CN.equals(language) ? zhCn : enUs;
    }

}
