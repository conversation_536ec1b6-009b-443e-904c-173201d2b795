/**
 * 版权所有：版权所有(C) 2022，中兴通讯
 * 系统名称：iTech Cloud
 */
package com.zte.iccp.itech.extension.domain.constant;

/**
 * 组件Cid常量类
 * <AUTHOR> 10335201
 * @date 2024-05-09 上午10:58
 **/
public class CidConstants {

    // ===================== 组件CID，(格式：COMPONENT_组件名称_CID) ======================
    /**
     * 责任单位树组件cid
     */
    public static final String COMPONENT_ORGANIZATION_TREE_CID = "iTechCloudCustomTreeSelect_organization";
    /**
     * 产品分类树组件cid
     */
    public static final String COMPONENT_PRODUCT_TREE_CID = "iTechCloudCustomTreeSelect_product";

    /** 合作方_操作主题实体cid */
    public static final String FIELD_OPERATION_SUBJECT_CID = "operation_subject";

    /** 操作主题后缀实体cid */
    public static final String FIELD_OPERATION_SUBJECT_SUFFIX_CID = "operation_subject_suffix";

    /** 属于GDPR管控项目实体cid */
    public static final String FIELD_IS_GDPR_CID = "is_gdpr";

    /** GDPR要求实体cid */
    public static final String FIELD_GDPR_REQUIRE_CID = "gdpr_require";

    /**
     * 网络产品类型cid
     */
    public static final String NETWORK_PRODUCT_TYPE_CID = "network_product_type";

    /**
     * 客户网络名称cid
     */
    public static final String CUSTOM_NETWORK_NAME_CID = "custom_network_name";

    /**
     * 核心网大区TD审核人cid（员工组件）
     */
    public static final String COMPONENT_TD_REVIEWER_CID = "region_td_reviewer";
    /**
     * 核心网大区TD审核人cid（下拉单选）
     */
    public static final String COMPONENT_TD_REVIEWER_SELECTED_CID = "region_td_reviewer_selected";
    /**
     * 操作原因cid
     */
    public static final String COMPONENT_OPERATION_REASON_CID = "operation_reason";
    /**
     * 是否行政领导审批控件 cid
     */
    public static final String COMPONENT_IS_ADMINISTRATION_LEADER_APPROVAL_CID = "is_administration_leader_approval";
    /**
     * 是否政企控件 cid
     */
    public static final String COMPONENT_IS_GOV_ENT_CID = "is_government_enterprise";
    /**
     * 操作对象表批次号控件 cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_BATCH_NO_CID = "operation_object_batch_no";

    /**
     * 操作人员表批次号控件 cid
     */
    public static final String COMPONENT_OPERATOR_BATCH_NO_CID = "operator_object_batch_no";

    /**
     * 批次概要表批次号控件 cid
     */
    public static final String COMPONENT_BATCH_SUMMARY_BATCH_NO_CID = "batch_summary_batch_no";
    /**
     * 批次概要表批次号控件 cid
     */
    public static final String COMPONENT_BATCH_SUMMARY_NE_COUNT_CID = "batch_summary_ne_count";
    /**
     * 操作对象表网络id控件 cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_NETWORK_ID_CID = "operation_object_network_id";
    /**
     * 操作对象表局点名称控件 cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_OFFICE_NAME_CID = "operation_object_office_name";
    /**
     * 操作对象表产品型号控件 cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_PRODUCT_MODEL_ID_CID = "operation_object_product_model";

    /** 产品大类 组件 cid */
    public static final String COMPONENT_PRODUCT_MAIN_CATEGORY_CID = "product_main_category";

    /** 产品小类 组件 cid */
    public static final String COMPONENT_PRODUCT_SUB_CATEGORY_CID = "product_sub_category";

    /** 集成关联产品 - 网元版本 */
    public static final String COMPONENT_NETWORK_UNIT_VERSION_CID = "ne_version_cid";

    /** App 局点 - 网络 */
    public static final String COMPONENT_APP_OFFICE_NETWORK_CID = "app_office_network";

    /** App 局点 - 局点名称 */
    public static final String COMPONENT_APP_OFFICE_NAME_CID = "app_office_name_info";

    /** App 局点 - 产品型号 */
    public static final String COMPONENT_APP_OFFICE_PRODUCT_MODEL_CID = "app_office_product_model";

    /**
     * 操作对象表当前版本控件 cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_CURRENT_VERSION = "operation_object_current_version";
    /**
     * 操作对象表目标版本控件 cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_TARGET_VERSION = "operation_object_target_version";
    /**
     * 操作等级控件 cid（分包商通用）
     */
    public static final String COMPONENT_OPERATION_LEVEL_COMPONENT_CID = "operation_level";
    /**
     * 生成内部操作方案按钮 cid
     */
    public static final String INNER_SOLUTION_BUTTON = "inner_solution_button";

    public static final String AI_CALLBACK_REFRESH_BUTTON = "ai_callback_refresh_button";

    public static final String CUSTOMER_AI_CALLBACK_REFRESH_BUTTON = "customer_ai_callback_refresh_button";

    public static final String PROGRESS_INNER_CID = "iTechCloudCustomProgress_inner";

    public static final String PROGRESS_CUSTOMER_CID = "iTechCloudCustomProgress_customer";

    public static final String PROGRESS_INNER_PROPERTY_KEY = "ai_progress_inner";

    public static final String PROGRESS_CUSTOMER_PROPERTY_KEY = "ai_progress_customer";

    /**
     * 生成客户操作方案按钮 cid
     */
    public static final String CUSTOMER_SOLUTION_BUTTON = "customer_solution_button";

    /**
     * 标准方案-已发布-列表-列表表格 cid
     */
    public static final String STANDARD_SOLUTION_TABLE = "TablePc_uq4hbvbo";

    /**
     * 选项配置-有线业务检查表-列表-列表表格 cid
     */
    public static final String WIRE_BUSINESS_CHECK_TABLE = "TablePc_uq4hbvbo";

    /**
     * 操作场景 cid
     */
    public static final String OPERATION_SCENARIO_CID = "operation_scenario";

    /**
     * 操作场景 KEY
     */
    public static final String OPERATION_SCENARIO_KEY = "operation_scenario";

    /**
     * 重要程度控件 cid（分包商通用）
     */
    public static final String COMPONENT_IMPORTANCE_CID = "importance";
    /**
     * 风险评估控件 cid（分包商通用）
     */
    public static final String COMPONENT_RISK_EVALUATION_CID = "risk_evaluation";
    /**
     * 代表处方案审核人cid（员工组件）
     */
    public static final String COMPONENT_OFFICE_SOLUTION_REVIEW_CID = "office_solution_reviewer";

    public static final String AI_TAG_DOC_ID_CID = "textfield_sianha7t";

    public static final String INTERNAL_OPERATION_SOLUTION_AI_PROPERTY_KEY = "internal_operation_solution_ai";

    public static final String CUSTOMER_OPERATION_SOLUTION_AI_PROPERTY_KEY = "customer_operation_solution_ai";

    public static final String FIELD_PROVINCE_CID = "province";

    public static final String FIELD_AREA_CID = "area";

    /**
     * 提单审批细节（步骤条规则专用）实体CID
     */
    public static final String FIELD_APPROVAL_DETAILS_STEPS_CID = "approval_details_steps";


    /**
     * 内部网络/合作方变更单提单步骤条布局CID
     */
    public static final String COMPONENT_STEPS_CID = "steps";

    /**
     * 步骤条布局CID（分包商/内部变更单）
     */
    public static final String COMPONENT_APPROVAL_DETAILS_STEPS_CID = "c_approval_details_steps";

    /**
     * 内部/合作方网络变更操作单--重新提交按钮cid
     */
    public static final String BUTTON_RESUBMIT_CID = "resubmit";

    /**
     * 代表处方案审核人cid（下拉单选）
     */
    public static final String COMPONENT_OFFICE_SOLUTION_REVIEW_SELECT_CID = "office_solution_reviewer_select";

    /**
     * 主管经理/副经理布局cid（员工组件）
     */
    public static final String COMPONENT_MANAGER_CID = "manager";

    /**
     * 主管经理/副经理布局cid（下拉多选）
     */
    public static final String COMPONENT_MANAGER_MULTI_CID = "manager_multi";

    /**
     * 技术交付部/网络处审核人（员工组件）
     */
    public static final String COMPONENT_TECHNOLOGY_REVIEW_CID = "upgrade_technology_reviewer";

    /**
     * 技术交付部/网络处审核人（下拉单选）
     */
    public static final String COMPONENT_TECHNOLOGY_REVIEW_SELECTED_CID = "upgrade_technology_reviewer_selected";

    /**
     * 技术交付部/网络处审核组（员工组件）
     */
    public static final String COMPONENT_TECHNOLOGY_REVIEW_TEAM_CID = "upgrade_technology_reviewer_team";

    /**
     * 技术交付部/网络处审核组（下拉单选）
     */
    public static final String COMPONENT_TECHNOLOGY_REVIEW_TEAM_MULTI_CID = "upgrade_technology_reviewer_team_multi";

    /**
     * 操作阶段打卡子表单
     */
    public static final String COMPONENT_OPERATION_PHASE_CHECK_CID = "operation_phase_check";

    /**
     * 网服部一体化审批——操作关联产品表子表单布局cid
     */
    public static final String COMPONENT_OPERATION_ASSOC_PROD_NET_INTEGRATION_CID = "operation_assoc_prod_net_integration";

    /**
     * 研发一体化审批——操作关联产品表子表单布局cid
     */
    public static final String COMPONENT_OPERATION_ASSOC_PROD_RD_INTEGRATION_CID = "operation_assoc_prod_rd_integration";

    /**
     * 网服部一体化审批——操作关联产品表子表单实体cid
     */
    public static final String FIELD_OPERATION_ASSOC_PROD_NET_INTEGRATION_CID = "operation_assoc_prod_net_integration";

    /**
     * 研发一体化审批——操作关联产品表子表单实体cid
     */
    public static final String FIELD_OPERATION_ASSOC_PROD_RD_INTEGRATION_CID = "operation_assoc_prod_rd_integration";

    /**
     *  网服部一体化审批——操作关联产品表子表单审核结果布局CID
     */
    public static final String COMPONENT_APPROVE_RESULT_NET_INTEGRATION_CID = "approve_result_net_integration";

    /**
     *  网服部一体化审批——操作关联产品表子表单审核意见布局CID
     */
    public static final String COMPONENT_APPROVE_OPINION_NET_INTEGRATION_CID = "approve_opinion_net_integration";

    /**
     *  网服部一体化审批——操作关联产品表子表单邮件抄送布局CID
     */
    public static final String COMPONENT_EMAIL_NET_INTEGRATION_CID = "email_net_integration";

    /**
     * 研发一体化审批——操作关联产品表产品分类布局CID
     */
    public static final String COMPONENT_PRODUCT_TYPE_RD_INTEGRATION_CID = "product_type_rd_integration";

    /**
     * 研发一体化审批——操作关联产品表审核结果布局CID
     */
    public static final String COMPONENT_APPROVE_RESULT_RD_INTEGRATION_CID = "approve_result_rd_integration";

    /**
     * 研发一体化审批——操作关联产品表审核意见布局CID
     */
    public static final String COMPONENT_APPROVE_OPINION_RD_INTEGRATION_CID = "approve_opinion_rd_intetration";

    /**
     * 研发一体化审批——操作关联产品表邮件抄送布局CID
     */
    public static final String COMPONENT_EMAIL_RD_INTEGRATION_CID = "email_rd_integration";

    /**
     * 操作对象_批次概要cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_BATCH_SUMMARY_CID = "operation_object_batch_summary";

    /**
     * 操作对象_批次概要_计划操作开始时间cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_BATCH_SUMMARY_START_TIME_CID = "cid_plan_operation_start_time";

    /**
     * 操作对象_批次概要_计划操作开始时间cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_BATCH_SUMMARY_END_TIME_CID = "cid_plan_operation_end_time";

    /**
     * 批次任务_发布通告_计划操作开始时间cid
     */
    public static final String COMPONENT_BATCH_RELEASE_TASK_START_TIME_CID = "DateField_dn1lzihk";

    /**
     * 批次任务_发布通告_计划操作结束时间cid
     */
    public static final String COMPONENT_BATCH_RELEASE_TASK_END_TIME_CID = "DateField_nd60ea2q";

    /** PDM 产品型号 */
    public static final String PDM_PRODUCT_MODEL = "pdm_product_model";

    /**
     * 操作对象子表单cid
     */
    public static final String COMPONENT_OPERATION_OBJECT_ORDER_CID = "operation_object_order";

    /**
     * 操作人员子表单中操作账号cid
     */
    public static final String COMPONENT_OPERATOR_ACCOUNT_CID = "operator_account";

    /**
     * 操作人员子表单唯一标识
     */
    public static final String COMPONENT_OPERATOR_TABLE_CID = "operator";

    /**
     * 合作方网络变更单批次任务操作人员子表单唯一标识
     */
    public static final String PARTNER_BATCH_NETWORK_COMPONENT_OPERATOR_TABLE_CID = "operationObjectPersonList";

    /** 批次操作人员子表单 - 操作人员cid */
    public static final String BATCH_OPERATION_PERSON_SUB_TAB_OPERATE_PERSON_CID = "EmployeeField_0ftufhu9";

    /**
     * 操作类型布局唯一标识
     */
    public static final String COMPONENT_OPERATION_TYPE_CID = "operation_type";

    /**
     * 分包商--技术方案检查单据体cid
     */
    public static final String COMPONENT_HZS_TECH_SOLUTION_CHECK_CID = "hzs_tech_solution_check";

    /**
     * 分包商--网络服务部技术方案检查单据体cid
     */
    public static final String FIELD_TECH_SOLUTION_NET_DEPT_CID = "tech_solution_net_dept";
    /**
     * 分包商--办公室产品经理技术方案检查单据体cid
     */
    public static final String FIELD_TECH_SOLUTION_OFFICE_PROD_MANAGER_CID = "tech_solution_office_prod_manager";
    /**
     * 分包商--网络负责人技术方案检查单据体cid
     */
    public static final String FIELD_TECH_SOLUTION_NET_OWNER_CID = "tech_solution_net_owner";

    /**
     * 分包商--网络服务部技术方案检查核对内容字段唯一标识
     */
    public static final String FIELD_CHECK_CONTENT_NET_DEPT_CID = "check_content_net_dept";
    /**
     * 分包商--办公室产品经理技术方案检查核对内容字段唯一标识
     */
    public static final String FIELD_CHECK_CONTENT_OFFICE_PROD_MANAGER_CID = "check_content_office_prod_manager";
    /**
     * 分包商--网络负责人技术方案检查核对内容字段唯一标识
     */
    public static final String FIELD_CHECK_CONTENT_NET_OWNER_CID = "check_content_net_owner";

    /**
     * 网络变更操作单---计划操作开始时间组件cid（分包商通用）
     */
    public static final String COMPONENT_OPERATION_START_TIME_CID = "operation_start_time";

    /**
     * 网络变更操作单---计划操作结束时间组件cid（分包商通用）
     */
    public static final String COMPONENT_OPERATION_END_TIME_CID = "operation_end_time";

    /**
     * 分包商-网络名称布局唯一标识
     */
    public static final String COMPONENT_NETWORK_NAME_CID = "network_name";

    /**
     * 自定义页面 - 查询条件 - 任务名称 / 编码
     */
    public static final String COMPONENT_ASSIGNMENT_CODE_AND_NAME_CID = "assignment_code_and_name";

    /**
     * 自定义页面 - 查询条件 - 任务名称
     */
    public static final String COMPONENT_ASSIGNMENT_NAME_CID = "assignment_name";

    /**
     * 自定义页面 - 查询条件 - 任务编码
     */
    public static final String COMPONENT_ASSIGNMENT_CODE_CID = "assignment_code";

    /**
     * 自定义页面 - 查询条件 - 客户网络 / 网络集
     */
    public static final String COMPONENT_CUSTOMER_NETWORK_OR_NETWORK_COLLECTION = "customer_network";

    /**
     * 自定义页面 - 查询条件 - 创建人
     */
    public static final String COMPONENT_CREATED_BY_CID = "created_by";

    /**
     * 自定义页面 - 查询条件 - 创建时间
     */
    public static final String COMPONENT_CREATED_DATE_CID = "created_date";

    /**
     * 自定义页面 - 查询条件 - 是否超期
     */
    public static final String COMPONENT_OVERDUE_FLAG_CID = "overdue_flag";

    // ===================== 实体CID，(格式：FIELD_字段名称_CID) ======================
    /**
     * 客户名称实体cid
     */
    public static final String FIELD_CUSTOMER_ID_CID = "customer_id";
    /**
     * 操作主题前缀实体cid
     */
    public static final String FIELD_OPERATION_SUBJECT_PREFIX_CID = "operation_subject_prefix";
    /**
     * 操作类型实体cid（内部网络变更单/合作商）
     */
    public static final String FIELD_OPERATION_TYPE_CID = "operation_type";

    /**
     * 操作原因是否关联收费属性实体cid
     */
    public static final String FIELD_OPERATION_REASON_CHARGE_CID = "operation_reason_charge";

    /**
     * 责任单位实体cid
     */
    public static final String FIELD_ORGANIZATION_CID = "organization_id";
    /**
     * 产品分类实体cid
     */
    public static final String FIELD_PRODUCT_CID = "product_id";
    /**
     * 业务中断时长实体cid
     */
    public static final String FIELD_SERVICE_DISCONNECT_DURATION_CID = "service_disconnect_duration";

    /**
     * 客户标识实体cid（运营商）
     */
    public static final String FIELD_CUSTOMER_IDENTIFICATION_CID = "customer_identification";
    /**
     * 操作等级实体cid（分包商通用）
     */
    public static final String FIELD_OPERATION_LEVEL_CID = "operation_level";
    /**
     * 是否特殊场景实体cid
     */
    public static final String FIELD_IS_SPECIAL_SCENARIO_CID = "is_special_scenario";
    /**
     * 是否政企实体cid
     */
    public static final String FIELD_IS_GOV_ENT_CID = "is_government_enterprise";
    /**
     * 是否紧急操作实体cid
     */
    public static final String FIELD_IS_EMERGENCY_OPERATION_CID = "is_emergency_operation";
    /**
     * 是否紧急操作标记实体cid
     */
    public static final String FIELD_IS_EMERGENCY_OPERATION_FLAG_CID = "emergency_operation_flag";
    /**
     * 是否需提供详细保障方案实体cid
     */
    public static final String FIELD_IS_GUARANTEE_SOLUTION_CID = "is_guarantee_solution";
    /**
     * 是否封网/管控期操作实体cid
     */
    public static final String FIELD_IS_NET_CLOSE_OR_CONTROL_OPERATION_CID = "is_net_close_or_control_operation";
    /**
     * 是否封网/管控期操作标记实体cid
     */
    public static final String FIELD_IS_NET_CLOSE_OR_CONTROL_OPERATION_FLAG_CID = "network_sealing_operation_flag";
    /**
     * 网络变更操作单---计划操作结束时间实体cid（分包商通用）
     */
    public static final String FIELD_OPERATION_END_TIME_CID = "operation_end_time";

    /**
     * 网络变更操作单---计划操作开始时间实体cid（分包商通用）
     */
    public static final String FIELD_OPERATION_START_TIME_CID = "operation_start_time";

    /**
     * 操作阶段打卡子表单实体cid
     */
    public static final String FIELD_OPERATION_STAGE_CHECK_IN_TABLE_CID = "operation_stage_check_in_table";

    /**
     * 操作阶段打卡子表单---开始时间实体cid
     */
    public static final String FIELD_OPERATION_PHASE_START_TIME_CID = "stage_start_time";

    /**
     * 操作阶段打卡子表单---结束时间实体cid
     */
    public static final String FIELD_OPERATION_PHASE_END_TIME_CID = "stage_end_time";

    /**
     * 操作阶段打卡子表单---操作时长实体key
     */
    public static final String FIELD_OPERATION_PHASE_OPERATION_DURATION_CID = "operation_duration";

    /**
     * 操作阶段打卡子表单---操作时长组件cid
     */
    public static final String COMPONENT_OPERATION_PHASE_OPERATION_DURATION_CID = "operation_duration";

    /**
     * 核心网产品外场质量保证规范动作单据体cid
     */
    public static final String FIELD_STANDARD_ACTION_CHECK_CID = "standard_action_check";
    /**
     * 核心网产品外场质量保证规范动作子表单--检查分类实体cid
     */
    public static final String FIELD_STANDARD_ACTION_CHECK_TYPE_CID = "standard_action_check_type";
    /**
     * 核心网产品外场质量保证规范动作子表单--检查分类实体cid EN
     */
    public static final String FIELD_STANDARD_ACTION_CHECK_TYPE_EN_CID = "standard_action_check_type_en";
    /**
     * 核心网产品外场质量保证规范动作子表单--检查内容实体cid
     */
    public static final String FIELD_STANDARD_ACTION_CHECK_CONTENT_CID = "standard_action_check_content";
    /**
     * 核心网产品外场质量保证规范动作子表单--检查内容实体cid EN
     */
    public static final String FIELD_STANDARD_ACTION_CHECK_CONTENT_EN_CID = "standard_action_check_content_en";
    /**
     * 技术方案检查单据体cid
     */
    public static final String FIELD_TECH_SOLUTION_CHECK_CID = "tech_solution_check";
    /**
     * 技术方案检查子表单--检查内容实体cid
     */
    public static final String FIELD_TECH_SOLUTION_CHECK_CONTENT_CID = "tech_solution_check_content";
    /**
     * 技术方案检查子表单--检查内容实体cid EN
     */
    public static final String FIELD_TECH_SOLUTION_CHECK_CONTENT_EN_CID = "tech_solution_check_content_en";
    /**
     * 操作类型分组实体cid
     */
    public static final String FIELD_OPERATION_TYPE_GROUP_CID = "operation_type_group";
    /**
     * 重要程度实体cid（分包商通用）
     */
    public static final String FIELD_IMPORTANCE_CID = "importance";
    /**
     * 风险评估实体cid（分包商通用）
     */
    public static final String FIELD_RISK_EVALUATION_CID = "risk_evaluation";
    /**
     * 国家实体cid
     */
    public static final String FIELD_COUNTRY_CID = "country";
    /**
     * 无线产品升级单实体cid
     */
    public static final String FIELD_WIRELESS_UPGRADE_TICKET_CID = "wireless_upgrade_ticket";

    /**
     * 是否需要升级至技术交付部/网络处实体cid
     */
    public static final String FIELD_IS_UPGRADE_TECHNOLOGY_CID = "is_upgrade_technology";

    /**
     * 是否需要升级至技术交付部/网络处布局cid
     */
    public static final String COMPONENT_IS_UPGRADE_TECHNOLOGY_CID = "is_upgrade_technology";

    /**
     * 是否首次应用实体cid
     */
    public static final String FIELD_IS_FIRST_APPLICATION_CID = "is_first_application";

    /**
     * 是否需要多产品联动保障实体唯一标识
     */
    public static final String FIELD_IS_PRODUCT_LINKAGE_GUARANTEE_CID = "is_product_linkage_guarantee";

    /**
     * 是否BCN实体唯一标识（网络变更操作单隐藏字段）
     */
    public static final String FIELD_IS_BCN_CID = "is_bcn";

    /**
     * 核心网大区TD审核人实体唯一标识（员工组件）
     */
    public static final String FIELD_TD_REVIEWER_CID = "region_td_reviewer";
    /**
     * 核心网大区TD审核人实体唯一标识（下拉单选）
     */
    public static final String FIELD_TD_REVIEWER_SELECTED_CID = "region_td_reviewer_selected";

    /**
     * 技术交付部/网络处审核人实体唯一标识（员工组件）
     */
    public static final String FIELD_TECHNOLOGY_REVIEW_CID = "upgrade_technology_reviewer";

    /**
     * 技术交付部/网络处审核人实体唯一标识（下拉单选）
     */
    public static final String FIELD_TECHNOLOGY_REVIEW_SELECTED_CID = "upgrade_technology_reviewer_selected";

    /**
     * 技术交付部/网络处审核组实体唯一标识（员工组件）
     */
    public static final String FIELD_TECHNOLOGY_REVIEW_TEAM_CID = "upgrade_technology_reviewer_team";

    /**
     * 技术交付部/网络处审核组实体唯一标识（下拉单选）
     */
    public static final String FIELD_TECHNOLOGY_REVIEW_TEAM_MULTI_CID = "upgrade_technology_reviewer_team_multi";

    /**
     * 主管经理/副经理实体唯一标识（员工组件）
     */
    public static final String FIELD_MANAGER_CID = "manager";

    /**
     * 产品科长实体唯一标识（员工组件）
     */
    public static final String FIELD_PRODUCT_SECTION_CHIEF_CID = "product_section_chief";

    /**
     * 主管经理/副经理实体唯一标识（下拉多选）
     */
    public static final String FIELD_MANAGER_MULTI_CID = "manager_multi";

    /**
     * 代表处方案审核人实体唯一标识（员工组件）
     */
    public static final String FIELD_OFFICE_SOLUTION_REVIEW_CID = "office_solution_reviewer";

    /**
     * 代表处方案审核人实体唯一标识（下拉单选）
     */
    public static final String FIELD_OFFICE_SOLUTION_REVIEW_SELECT_CID = "office_solution_reviewer_select";

    /**
     * 操作原因实体cid（内部网络变更单/合作商）
     */
    public static final String FIELD_OPERATION_REASON_CID = "operation_reason";

    /**
     * 操作阶段打卡子表单---操作时长
     */
    public static final String FIELD_OPERATION_DURATION_CID = "operation_duration";

    /**
     * 批次概要表_操作账号唯一标识（网络变更单/分包商）
     */
    public static final String FIELD_OPERATION_ACCOUNT_CID = "operation_account";

    /**
     * 操作人员表_操作账号唯一标识（网络变更单提交）
     */
    public static final String FIELD_OPERATOR_ACCOUNT_CID = "operator_account";

    /**
     * 操作人员表唯一标识
     */
    public static final String FIELD_OPERATOR_CID = "operator_table";

    /**
     * 合作方网络变更批次任务操作人员表唯一标识
     */
    public static final String FIELD_SUBCONTRACTOR_NETWORK_BATCH_OPERATOR_CID = "subcontractor_network_batch_operator";

    /**
     * 内部网络变更批次任务操作人员表唯一标识
     */
    public static final String FIELD_INTERNAL_NETWORK_BATCH_OPERATOR_CID = "network_batch_operator";

    /**
     * 操作对象_批次概要表标识（网络变更单/分包商）
     */
    public static final String FIELD_BATCH_SUMMARY_CID = "batch_summary";

    /**
     * 工具落地状态实体唯一标识
     */
    public static final String FIELD_TOOL_USE_CID = "tool_use";

    /**
     * 未使用工具原因实体唯一标识
     */
    public static final String FIELD_NOT_USE_TOOL_REASON_CID = "not_use_tool_reason";

    /**
     * 未使用工具原因布局唯一标识
     */
    public static final String COMPONENT_NOT_USE_TOOL_REASON_CID = "not_use_tool_reason";

    /**
     * 工具名称实体唯一标识（下拉多选）
     */
    public static final String FIELD_TOOL_NAME_SELECTED_CID = "tool_name_selected";

    /**
     * 工具名称实体唯一标识（文本）
     */
    public static final String FIELD_TOOL_NAME_CID = "tool_name";

    /**
     * 客户名称实体唯一标识（绑定的是customer_id，内部变更单和分包商）
     */
    public static final String FIELD_CUSTOMER_NAME_CID = "customer_id";

    /**
     * 客户标识实体唯一标识（内部变更单和分包商）
     */
    public static final String ACCN_TYPE_CID = "accn_type";

    /**
     * 工具名称布局cid（下拉多选）
     */
    public static final String COMPONENT_TOOL_NAME_SELECTED_CID = "tool_name_selected";

    /**
     * 工具名称布局cid（文本）
     */
    public static final String COMPONENT_TOOL_NAME_CID = "tool_name";


    /**
     * 关联操作产品布局cid
     */
    public static final String COMPONENT_INTEGRATED_ASSOCIATED_PRODUCT_CID = "integrated_associated_product";

    /**
     * 关联操作产品布局cid（下拉）
     */
    public static final String COMPONENT_INTEGRATED_ASSOCIATED_PRODUCT_SELECTED_CID = "integrated_associated_products";

    /**
     * 操作人员列表标识（分包商网络变更单）
     */
    public static final String FIELD_PARTNER_OPERATION_PERSON_LIST_CID = "operator_table";

    /**
     * 操作人员列表_操作账号唯一标识（分包商网络变更单）
     */
    public static final String FIELD_PARTNER_OPERATOR_ACCOUNT_CID = "operator_account";

    /**
     * 操作人员子表单唯一标识（分包商）
     */
    public static final String COMPONENT_PARTNER_OPERATOR_TABLE_CID = "operator";

    /**
     * 操作对象子表单实体标识（分包商）
     */
    public static final String FIELD_COMPONENT_PARTNER_OPERATION_OBJECT_CID = "operation_object";

    /**
     * 网络配置 - 网络ID（分包商）
     */
    public static final String FIELD_PARTNER_NETWORK_CONFIGURATION_NETWORK_ID_CID = "network_id";

    /**
     * 操作人员列表_操作账号布局标识（分包商网络变更单）
     */
    public static final String COMPONENT_PARENT_OPERATOR_ACCOUNT_CID = "operator_account";

    /**
     * 分包商_网络名称实体唯一标识
     */
    public static final String FIELD_NETWORK_NAME = "network_name";

    /**
     * 分包商_办事处PD实体唯一标识
     */
    public static final String REPRESENTATIVE_OFFICE_PD_CID = "representative_office_pd";

    /**
     * 操作阶段打卡子表单---操作阶段 propertykey
     */
    public static final String OPERATION_PHASE_KEY = "operation_phase";

    /**
     * 网元清单 propertykey
     */
    public static final String NE_LIST_FILE_PROPERTY_KEY = "ne_list_file";

    /**
     * 批次概要子表单 propertykey
     */
    public static final String BATCH_SUMMARY_TABLE_PROPERTY_KEY = "batch_summary";

    /**
     * 批次概要子表单批次号 propertykey
     */
    public static final String BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY = "batch_no";

    /**
     * 批次概要子表单网元数量 propertykey
     */
    public static final String BATCH_SUMMARY_TABLE_NE_COUNT_PROPERTY_KEY = "ne_count";

    /**
     * 主表单-时区 propertykey（分包商通用）
     */
    public static final String TIME_ZONE_KEY = "time_zone";

    /**
     * 操作对象子表单 propertykey
     */
    public static final String OPERATION_OBJECT_TABLE_PROPERTY_KEY = "operation_object";

    /**
     * 操作对象子表单-网络id propertykey
     */
    public static final String FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY = "network_id";

    /**
     * 操作对象子表单-产品型号id propertykey
     */
    public static final String FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY = "product_model";

    /**
     * 操作对象子表单-产品小类（产品分类） propertykey
     */
    public static final String FIELD_OPERATION_OBJECT_PRODUCT_CLASS_PROPERTY_KEY = "product_class";
    /**
     * 操作对象子表单-批次号 propertykey
     */
    public static final String FIELD_OPERATION_OBJECT_BATCH_NO_PROPERTY_KEY = "batch_info_no";

    /**
     * 操作对象子表单-批次号 propertykey
     */
    public static final String FIELD_OPERATOR_BATCH_NO_KEY = "operator_batch_no";

    /**
     * 操作对象子表单-局点名称 propertykey
     */
    public static final String FIELD_OPERATION_OBJECT_OFFICE_NAME_PROPERTY_KEY = "office_name";

    /**
     * App 局点信息子表单 propertyKey
     */
    public static final String APP_OFFICE_INFO_TABLE_PROPERTY_KEY = "app_location_info";

    /**
     * 分包商--技术方案检查单据体--检查内容 propertykey
     */
    public static final String HZS_CHECK_CONTENT_KEY = "check_content";

    /**
     * 是否大区操作 propertykey
     */
    public static final String IS_REGIONAL_OPERATION_KEY = "is_regional_operation";

    /**
     * 大区相关代表处表 propertykey
     */
    public static final String REGIONAL_REPRESENTATIVE_OFFICES_TABLE_KEY = "regional_representative_offices_table";

    /**
     * 是否需要多产品联动保障 propertykey
     */
    public static final String IS_PRODUCT_LINKAGE_GUARANTEE_KEY = "is_product_linkage_guarantee";

    /**
     * 是否需要操作步骤打卡 propertykey
     */
    public static final String IS_CHECK_IN_KEY = "is_check_in";

    /**
     * 操作步骤打卡表 propertykey
     */
    public static final String OPERATION_STEP_CHECK_IN_TABLE_KEY = "operation_step_check_in_table";

    /**
     * 操作步骤打卡开始时间 propertykey（分包商通用）
     */
    public static final String STEP_START_TIME_CID = "step_start_time";

    /**
     * 操作步骤打卡结束时间 propertykey（分包商通用）
     */
    public static final String STEP_END_TIME_CID = "step_end_time";

    /**
     * 是否高危指令 propertykey
     */
    public static final String IS_HIGH_RISK_INSTRUCTION = "is_high_risk_instruction";

    /**
     * 高危指令表 propertykey
     */
    public static final String HIGH_RISK_INSTRUCTION_TABLE_KEY = "high_risk_instruction_table";

    /**
     * 分包商--是否需要操作步骤打卡 propertykey
     */
    public static final String ZHS_IS_CHECK_IN_KEY = "hzs_is_check_in";

    /**
     * 分包商--操作步骤打卡表 propertykey
     */
    public static final String ZHS_OPERATION_STEP_CHECK_IN_TABLE_KEY = "hzs_operation_step_check_in_table";

    /**
     * 分包商--是否高危指令 propertykey
     */
    public static final String ZHS_IS_HIGH_RISK_INSTRUCTION = "hzs_is_high_risk_instruction";

    /**
     * 操作人员列表--员工 propertykey
     */
    public static final String OPERATOR_NAME_KEY = "operator_name";

    /**
     * 合作方网络变更单批次任务操作人员列表--员工 propertykey
     */
    public static final String OPERATE_PERSON_KEY = "operate_person";

    /**
     * 操作人员列表--归属部门 propertykey
     */
    public static final String OPERATOR_DEPARTMENT_KEY = "operator_department";

    /**
     * 操作人员列表--人员属性 propertykey
     */
    public static final String OPERATOR_ATTRIBUTE_KEY = "operator_attribute";

    /**
     * 合作商网络变更操作单--操作人员列表--联系电话 propertykey
     */
    public static final String OPERATOR_TELEPHONE_KEY = "telephone";

    /**
     * 网络变更操作单--操作人员列表--联系电话 propertykey
     */
    public static final String OPERATOR_PHONE_KEY = "operator_phone";

    /**
     * 操作人员列表--人员角色 propertykey
     */
    public static final String OPERATOR_ROLE_KEY = "operator_role";

    /**
     * 操作人员列表——远程 propertykey
     */
    public static final String OPERATOR_IS_REMOTE = "is_remote";

    /**
     * 操作阶段打卡子表单--打卡责任人 propertykey
     */
    public static final String STAGE_CHECK_IN_PERSON_KEY = "stage_check_in_person";

    /**
     * 大区相关代表处--邮件抄送 propertykey
     */
    public static final String MAIL_CARBON_COPY_KEY = "mail_carbon_copy";

    /** 技术管理 - 任务分类 */
    public static final String COMPONENT_TASK_CATEGORY_CID = "task_category_cid";

    /**
     * 技术管理 - 任务名称(文本)
     */
    public static final String FIELD_TASK_NAME = "task_name";

    /**
     * 技术管理 - 任务单号
     */
    public static final String FIELD_TASK_NO = "cn_no";

    /**
     * 技术管理 - 责任人 - 字段唯一标识
     */
    public static final String FIELD_RESPONSIBLE_PERSON = "responsible_person";

    /**
     * 技术管理 - 知会人 - 字段唯一标识
     */
    public static final String FIELD_INFORMED_PERSON = "informed_person";

    /**
     * 技术管理 - 要求完成日期
     */
    public static final String FIELD_REQUIRED_COMPLETION_DATE = "required_completion_date";

    /**
     * 技术管理 - 责任领导人
     */
    public static final String COMPONENT_RESPONSIBLE_LEADER_PERSON = "responsible_leader_person";

    /**
     * 技术管理 - 目标要求
     */
    public static final String COMPONENT_TARGET_REQUIRE = "target_require";

    /**
     * 多模从属产品子表单映射实体cid
     */
    public static final String FIELD_BILL_MULTIMODE_PRODUCT_CID = "multimode_product";

    /**
     * 多模从属产品子表单布局cid
     */
    public static final String COMPONENT_BILL_MULTIMODE_PRODUCT_CID = "multimode_product";

    /**
     * 多模从属产品名称实体cid
     */
    public static final String FIELD_MULTIMODE_PRODUCT_CID = "multimode_products";

    /**
     * 多模从属产品网络服务部审核组实体cid
     */
    public static final String FIELD_MULTIMODE_PRODUCT_NET_APPROVE_TEAM_CID = "multimode_net_approve_team";

    /**
     * 内部网络变更单 是否授权文件propKey
     */
    public static final String IS_NEED_AUTHORIZATION_FILE = "is_need_authorization_file";

    /**
     * 合作商络变更单 是否授权文件propKey
     */
    public static final String IS_AUTHORIZATION_FILE = "is_authorization_file";

    // ================ 自定义页面表格唯一标识，(格式：TABLE_表格名称_CID) ================
    /**
     * 三营网络配置表格
     */
    public static final String TABLE_DOMESTIC_NETWORK_CONFIGURATION_CID = "domestic_network_configuration";

    /**
     * 操作对象 - 网络查询
     */
    public static final String TABLE_OPERAND_QUERY_NETWORK_CID = "operand_query_network";

    /**
     * 任务中心
     */
    public static final String TABLE_ASSIGNMENT_CID = "assignment";

    /**
     * 任务中心 - 上游任务
     */
    public static final String TABLE_UPSTREAM_ASSIGNMENT_CID = "upstream_assignment";

    /**
     * 任务中心 - 下游任务
     */
    public static final String TABLE_DOWNSTREAM_ASSIGNMENT_CID = "downstream_assignment";

    /**
     * 故障管理任务 - 故障复盘任务
     */
    public static final String TABLE_FAULT_REVIEW_ASSIGNMENT_CID = "c_fault_review_task_table";

    /**
     * 故障管理 - 客户满意度实体cid
     */
    public static final String FIELD_SATISFACTION_RESPONSIBLE_PERSON_CID= "satisfaction_responsible_person";

    /**
     * 故障管理 - 客户满意度实体（故障横推整改）cid
     */
    public static final String FIELD_SATISFACTION_RESPONSIBLE_PERSON_FR_CID= "satisfaction_responsible_person_fr";

    /**
     * 故障管理 - 客户满意度布局cid
     */
    public static final String COMPONENT_SATISFACTION_RESPONSIBLE_PERSON_CID = "c_satisfaction_responsible_person";

    /**
     * 打卡复盘任务 - 关联的整改任务
     */
    public static final String TABLE_RELATION_RECTIFY_ASSIGNMENT_CID = "table_relation_rectify_assignment";

    /**
     * 打卡复盘任务 - 打卡记录表格CID
     */
    public static final String TABLE_CLOCK_IN_RECORD_ASSIGNMENT_CID = "table_clock_in_record";

    /**
     * 故障管理任务 - 故障整改任务
     */
    public static final String TABLE_FAULT_RECTIFY_ASSIGNMENT_CID = "c_fault_rectification_task_table";

    /**
     * 授权中心 - CCN 默认授权文件申请
     */
    public static final String TABLE_CCN_AUTHORIZATION_APPLICATION_CID = "ccn_authorization_application_table";

    /**
     * 网络变更操作单 - 导出模板
     */
    public static final String TABLE_CHANGE_ORDER_EXPORT_TEMPLATE_CID = "change_order_export_template_table";

    /** 配置中心 - 邮件推送群组 */
    public static final String TABLE_EMAIL_SEND_GROUP = "email_send_group";

    /** 配置中心 - 用户群组 */
    public static final String TABLE_USER_GROUP = "user_group";

    /**
     * 有线业务检查表表格
     */
    public static final String TABLE_WIRE_BUSINESS_CHECK_CID = "wire_business_check";


    // ============= 自定义查询条件唯一标识，(格式：CONDITION_查询条件名称_CID) ============
    /**
     * 网络状态
     */
    public static final String CONDITION_NETWORK_STATUS_CID = "condition_network_status";

    /**
     * 网络名称
     */
    public static final String CONDITION_NETWORK_NAME_CID = "condition_network_name";

    /**
     * 客户网络名称
     */
    public static final String CONDITION_CUSTOMER_NETWORK_NAME_CID = "condition_customer_network_name";

    /**
     * 是否政企
     */
    public static final String CONDITION_IS_GOVERNMENT_ENTERPRISE_CID = "condition_is_government_enterprise";

    /**
     * 代表处
     */
    public static final String CONDITION_ORGANIZATION_CID = "iTechCloudCustomTreeSelect_organization";

    /**
     * 网络设备类型
     */
    public static final String CONDITION_DEVICE_CID = "iTechCloudCustomTreeSelect_device";

    /**
     * 提示语
     */
    public static final String CONDITION_PROMPT_CID = "Prompt_no_data";

    /**
     * 文档名称
     */
    public static final String CONDITION_DOCUMENT_NAME_CID = "condition_document_name";

    /**
     * 产品型号自定义页面--隐藏字段--网络id--布局cid
     */
    public static final String CONDITION_QUERY_PARAM_NETWORK_ID_CID = "query_param_network_id";

    /**
     * 产品型号
     */
    public static final String CONDITION_PRODUCT_MODEL_CID = "condition_product_model_keyword";

    /** 高级查询标识 */
    public static final String CONDITION_ADVANCED_QUERY_FLAG_CID = "advanced_query_flag";

    // ===================== 按钮CID，(格式：BUTTON_按钮名称_CID) ======================
    /**
     * 公共按钮 - 搜索
     */
    public static final String BUTTON_SEARCH_CID = "searchButton";

    /**
     * 网络变更操作单 / 分包商网络变更操作单 - 保存草稿按钮
     */
    public static final String BUTTON_SAVE_DRAFT_CID = "saveDraftButton";

    /**
     * 网络变更操作单 / 分包商网络变更操作单 - 保存审批驳回草稿按钮
     */
    public static final String BUTTON_SAVE_APPROVE_DRAFT_CID = "saveResubmit";

    public static final String COLUMNSLAYOUT_AWARE_SUBMISSION_CID = "ColumnsLayout_aware_submission";

    public static final String SUBMIT_PANEL_CID = "submitPanel";

    public static final String RESUBMIT_PANEL_CID = "resubmitPanel";

    /** 网络变更操作单--提交审批按钮cid */
    public static final String BUTTON_SUBMIT_APPROVAL_CID = "submitAndApproval";

    /** 网络变更操作单 - 重新提交 */
    public static final String BUTTON_RESUBMIT_APPROVAL_CID = "resubmitAndApproval";

    /**
     * 任务中心 - 关联任务 - 关联上游任务
     */
    public static final String BUTTON_ASSOCIATE_UPSTREAM_CID = "AssociateUpstreamButton";

    /**
     * 故障管理任务 - 创建故障复盘任务
     */
    public static final String BUTTON_CREATE_FAULT_TASK_CID = "c_create_fault_review_task_button";

    /**
     * CCN 默认授权文件申请单 - 保存申请单
     */
    public static final String BUTTON_SAVE_APPLICATION_CID = "save_application_button";

    /**
     * CCN 默认授权文件申请单 - 提交申请单
     */
    public static final String BUTTON_SUBMIT_APPLICATION_CID = "submit_application_button";

    /**
     * CCN 默认授权文件申请单 - 提交反馈
     */
    public static final String BUTTON_SUBMIT_EVIDENCE_CID = "submit_evidence_button";

    /** 按钮 - 检索 */
    public static final String BUTTON_QUERY_CID = "query_button";

    /** 按钮 - 删除 */
    public static final String BUTTON_DELETE_CID = "delete_button";

    /** 按钮 - 编辑 */
    public static final String BUTTON_EDIT_CID = "edit_button";

    /** 按钮 - 启用 */
    public static final String BUTTON_ENABLED_CID = "enabled_button";

    /** 按钮 - 停用 */
    public static final String BUTTON_DISABLED_CID = "disabled_button";

    /** 按钮 - 撤销 */
    public static final String BUTTON_REVOKE_CID = "revoke_button";

    /** 按钮 - 重新申请 */
    public static final String BUTTON_REAPPLY_CID = "reapply_button";

    /** 按钮 - 保存 */
    public static final String BUTTON_SAVE_CID = "save_button";

    /** 按钮 - 确认 */
    public static final String BUTTON_CONFIRM_CID = "confirm_button";

    /** 按钮 - 关闭 */
    public static final String BUTTON_CLOSE_CID = "close_button";

    /** 技术管理任务 - 保存草稿 */
    public static final String BUTTON_TECHNICAL_TASK_SAVE_DRAFT_CID = "manage_task_credit_draft_button";

    /** CCN 默认授权文件申请单 - 下载授权文件 */
    public static final String BUTTON_DOWNLOAD_AUTHORIZATION_FILE_CID = "download_button";

    /** CCN 默认授权文件申请单 - 反馈 */
    public static final String BUTTON_FEEDBACK_EVIDENCE_CID = "feedback_button";

    /** 自定义群组 - 技术管理任务责任人选择 */
    public static final String BUTTON_SELECT_TECH_RESPONSIBLE_CID = "select_technology_responsible_button";

    /** 自定义群组 - 技术管理任务知会人选择 */
    public static final String BUTTON_SELECT_TECH_INFORM_CID = "select_technology_inform_button";

    /** 自定义群组 - 网络变更 研发领导 选择 */
    public static final String BUTTON_SELECT_RD_LEADER_CID = "select_rd_leader_button";

    /** 自定义群组 - 网络变更 研发经理审核组 选择 */
    public static final String BUTTON_SELECT_RD_MANAGER_TEAM_CID = "select_rd_manager_team_button";

    /** 自定义群组 - 网络变更 测试部审核人 选择 */
    public static final String BUTTON_SELECT_TEST_APPROVER_CID = "select_test_approver_button";

    /** 自定义群组 - 网络变更 网络服务部 选择 SSP支持组  */
    public static final String BUTTON_SELECT_SSP_TEAM_NET_CID = "select_ssp_team_net_button";

    /** 自定义群组 - 网络变更 技术交付部 选择 SSP支持组  */
    public static final String BUTTON_SELECT_SSP_TEAM_CID = "select_ssp_team_button";

    /** 自定义群组 - 网络变更 技术交付部 选择 网络服务部审核组  */
    public static final String BUTTON_SELECT_NETWORK_SERVICE_DEPT_TEAM_CID = "select_network_service_dept_team_button";


    /** 自定义群组 - 网络变更 技术交付部 选择 远程方案负责组  */
    public static final String BUTTON_SELECT_REMOTE_CENTER_SOLUTION_TEAM_CID = "select_remote_center_solution_team_button";

    /** 自定义群组 - 合作方网络变更 邮件抄送 */
    public static final String HZF_ORDER_SELECT_GROUP_BUTTON = "hzf_order_select_group_button";

    /** 自定义群组 - 合作方网络变更 代表处产品科科长 邮件抄送 */
    public static final String HZF_REPRESENTATIVE_CHIEF_SELECT_GROUP_BUTTON = "hzf_representative_chief_select_group_button";

    /** 自定义群组 - 合作方网络变更 网络责任人 邮件抄送 */
    public static final String HZF_NET_OWNER_MAIL_SELECT_GROUP_BUTTON = "hzf_net_owner_mail_select_group_button";

    /** 自定义群组 - 合作方网络变更 网络责任人 办事处产品经理审核组 */
    public static final String HZF_NET_OWNER_APPROVAL_SELECT_GROUP_BUTTON = "hzf_net_owner_approval_select_group_button";

    /** 自定义群组 - 合作方网络变更 办事产品科长 邮件抄送 */
    public static final String HZF_OFFICE_PROD_CHIEF_MAIL_SELECT_GROUP_BUTTON = "hzf_office_prod_chief_mail_select_group_button";

    /** 自定义群组 - 合作方网络变更 办事处PD 邮件抄送 */
    public static final String HZF_OFFICE_TD_MAIL_SELECT_GROUP_BUTTON = "hzf_office_td_mail_select_group_button";

    /** 自定义群组 - 合作方网络变更 办事处产品经理 网络处审核组 */
    public static final String HZF_OFFICE_MANAGER_APPROVAL_SELECT_GROUP_BUTTON = "hzf_office_manager_approval_select_group_button";

    /** 自定义群组 - 合作方网络变更 办事处产品经理 邮件抄送 */
    public static final String HZF_OFFICE_MANAGER_MAIL_SELECT_GROUP_BUTTON = "hzf_office_manager_mail_select_group_button";

    /** 自定义群组 - 合作方网络变更 发布通告 邮件抄送 */
    public static final String HZF_RELEASE_NOTICE_MAIL_SELECT_GROUP_BUTTON = "hzf_release_notice_mail_select_group_button";

    /** 自定义群组 - 合作方网络变更 反馈操作结果 邮件抄送 */
    public static final String HZF_FEED_BACK_OPERATION_RESULT_SELECT_GROUP_BUTTON = "hzf_feed_back_operation_result_select_group_button";

    /** 自定义群组 - 合作方网络变更 操作结果审核 邮件抄送 */
    public static final String HZF_OPERATION_RESULT_REVIEW_SELECT_GROUP_BUTTON = "hzf_operation_result_review_select_group_button";

    /** 自定义群组 - 合作方网络变更 操作取消审核 邮件抄送 */
    public static final String HZF_OPERATION_CANCEL_APPROVAL_SELECT_GROUP_BUTTON = "hzf_operation_cancel_approval_select_group_button";

    /** 自定义群组 - 合作方网络变更 网络责任人 选择 办事处产品经理审核组 */
    public static final String BUTTON_HZF_SELECT_PROD_MANAGER_CID = "hzf_net_owner_approval_select_group_button";

    /** 自定义群组 - 合作方网络变更 办事处产品经理 选择 网络处审核组  */
    public static final String BUTTON_HZF_SELECT_NET_APPROVE_TEAM_CID = "hzf_office_manager_approval_select_group_button";

    /** 自定义群组 - 合作方网络变更 网络处审核 选择 网络处审核组  */
    public static final String BUTTON_HZF_SELECT_NET_DEPT_MAIL_CID = "hzf_net_dept_mail_select_group_button";

    /** 自定义群组 - 网络变更 提交 选择 技术交付部审核组 */
    public static final String BUTTON_SELECT_TECH_REVIEWER_TEAM_CID = "select_technology_reviewer_team_button";

    /** 自定义群组 - 网络变更 提交 选择 邮件抄送 */
    public static final String BUTTON_SELECT_SUBMIT_EMAIL_CID = "select_submit_email_button";

    /** 自定义群组 - 网络变更 网络处总工审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_NETWORK_CHIEF_ENGINE_EMAIL_CID = "select_email_cc_network_chief_engine";

    /** 自定义群组 - 网络变更 技术交付部网络处审核 选择 网络变更通告抄送 */
    public static final String BUTTON_SELECT_NET_DEPT_OPER_NOTICE_CID = "select_network_change_oper_notice_cc_td_net_dept";

    /** 自定义群组 - 网络变更 技术交付部网络处审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_TD_NET_DEPT_APP_EMAIL_CID = "select_email_cc_td_net_dept_app";

    /** 自定义群组 - 网络变更 服务产品支持部 选择 邮件抄送 */
    public static final String BUTTON_SELECT_SERVICE_PROD_SUPPORT_EMAIL_CID = "select_email_cc_service_prod_support";

    /** 自定义群组 - 网络变更 SSP产品支持团队 选择 邮件抄送 */
    public static final String BUTTON_SELECT_SSP_PROD_SUPPORT_EMAIL_CID = "select_email_cc_ssp_prod_support";

    /** 自定义群组 - 网络变更 测试部 选择 邮件抄送 */
    public static final String BUTTON_SELECT_TEST_DEPT_EMAIL_CID = "select_email_cc_test_dept";

    /** 自定义群组 - 网络变更 研发经理审核 选择 网络变更通告抄送 */
    public static final String BUTTON_SELECT_RD_MANAGER_OPER_NOTICE_CID = "select_change_oper_notice_cc_rd_manager";

    /** 自定义群组 - 网络变更 研发经理审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_RD_MANAGER_EMAIL_CID = "select_email_cc_rd_manager";

    /** 自定义群组 - 网络变更 集成团队审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_INTEGRATION_TEAM_EMAIL_CID = "select_email_cc_integration_team";

    /** 自定义群组 - 网络变更 研发领导审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_RD_LEADER_EMAIL_CID = "select_email_cc_rd_leader";

    /** 自定义群组 - 网络变更 远程中心操作实施人指派 选择 邮件抄送 */
    public static final String BUTTON_SELECT_REMOTE_CENTER_OPER_ASSIGN_EMAIL_CID = "select_email_cc_remote_center_oper_assign";

    /** 自定义群组 - 网络变更 远程中心负责人审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_REMOTE_CENTER_OWNER_CID = "select_email_cc_remote_center_owner";

    /** 自定义群组 - 网络变更 远程中心方案提交 选择 邮件抄送 */
    public static final String BUTTON_SELECT_REMOTE_CENTER_SCHEME_EMAIL_CID = "select_email_cc_remote_center_scheme";

    /** 自定义群组 - 网络变更 技术交付部网络处审核远程中心的方案 选择 邮件抄送 */
    public static final String BUTTON_SELECT_NET_DEPT_APP_SOLU_EMAIL_CID = "select_email_cc_td_net_dept_app_solu";

    /** 自定义群组 - 网络变更 网络服务处审核 选择 网络变更通告抄送 */
    public static final String BUTTON_SELECT_NET_SERVICE_DEPT_OPER_NOTICE_CID = "select_change_oper_notice_cc_net_service_dept";

    /** 自定义群组 - 网络变更 网络服务处审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_NET_SERVICE_DEPT_EMAIL_CID = "select_net_service_dept_email_button";

    /** 自定义群组 - 网络变更 大区TD 选择 邮件抄送 */
    public static final String BUTTON_SELECT_REGIONAL_TD_CONFIRM_EMAIL_CID = "select_email_cc_regional_td_confirm";

    /** 自定义群组 - 网络变更 代表处产品TD 选择 邮件抄送 */
    public static final String BUTTON_SELECT_REP_PROD_TD_APP_EMAIL_CID = "select_email_cc_rep_prod_td_app";

    /** 自定义群组 - 批次任务 发布通告 选择 邮件抄送 */
    public static final String BUTTON_SELECT_BATCH_EMAIL_CID = "select_batch_email_cc";

    /** 自定义群组 - 批次任务 操作结果审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_BATCH_APPROVAL_EMAIL_CID = "select_batch_approval_email";

    /** 自定义群组 - 批次任务 反馈操作结果 选择 邮件抄送 */
    public static final String BUTTON_SELECT_BATCH_RESULT_EMAIL_CID = "select_batch_result_email";

    /** 自定义群组 - 批次任务 操作取消审核 选择 邮件抄送 */
    public static final String BUTTON_SELECT_BATCH_OPERATION_CANCEL_EMAIL_CID = "select_batch_oc_email_cc";


    // ==================== 操作CID，(格式：OPERATION_操作名称_CID) ====================
    /**
     * 跳转新建网络变更单操作
     */
    public static final String OPERATION_FORWARD_NETWORK_CHANGE_BILL_CID = "forwardNetworkChangeBill";

    /**
     * 跳转技术管理任务操作
     */
    public static final String OPERATION_FORWARD_TECHNOLOGY_MANAGEMENT_BILL_CID = "forwardTechnologyManagementBill";

    /**
     * 跳转分包商网络变更单操作
     */
    public static final String OPERATION_FORWARD_SUBCONTRACTOR_BILL_CID = "forwardSubcontractorNetworkChangeBill";

    /**
     * 跳转新建 故障复盘 任务
     */
    public static final String OPERATION_CREATE_FAULT_REVIEW_ASSIGNMENT_CID = "createFaultReviewAssignment";

    /**
     * 跳转新建 故障整改 任务
     */
    public static final String OPERATION_CREATE_FAULT_RECTIFY_ASSIGNMENT_CID = "createFaultRectifyAssignment";

    /**
     * 跳转新建 打卡复盘整改 任务
     */
    public static final String OPERATION_CREATE_CLOCK_REVIEWS_RECTIFY_ASSIGNMENT_CID = "createClockReviewsRectifyAssignment";

    /**
     * 跳转新建 操作计划 任务
     */
    public static final String OPERATION_CREATE_OPERATION_PLAN_ASSIGNMENT_CID= "forwardOperationPlanBill";

    /**
     * 删除 故障复盘 任务
     */
    public static final String OPERATION_DELETE_FAULT_REVIEW_ASSIGNMENT_CID = "deleteFaultReviewAssignment";

    /**
     * 删除 故障整改横推 任务
     */
    public static final String OPERATION_DELETE_FAULT_RECTIFY_ASSIGNMENT_CID = "deleteFaultRectifyAssignment";

    /** 废止 故障复盘 任务 */
    public static final String OPERATION_ABOLISH_FAULT_REVIEW_ASSIGNMENT_CID = "abolishFaultReviewAssignment";

    /** 废止 故障整改横推 任务 */
    public static final String OPERATION_ABOLISH_FAULT_RECTIFY_ASSIGNMENT_CID = "abolishFaultRectifyAssignment";

    /** 批量转交 */
    public static final String OPERATION_BATCH_TRANSFER_CID = "batch_transfer";

    /** 批量同意 */
    public static final String OPERATION_BATCH_AGREE_CID = "batch_agree";

    // ==================== 审批人配置表  ====================
    /**审批人配置表 审批节点 PROPERTY_KEY*/
    public static final String APPROVAL_NODE_PROPERTY_KEY = "approval_node";

    // ==================== 技术管理任务  ====================
    /**
     * 自动生成子任务-网络列表表格cid
     */
    public static final String TABLE_MANAGE_TASK_QUERY_NETWORK_CID = "operand_manage_task_query_network";


    /**
     * 客户标识cid
     */
    public static final String FILED_CUSTOMER_FLAG_CID = "customer_identification";

    /**
     * 子任务-实体标识
     */
    public static final String SUB_TABLE_MANAGE_TASK_KEY = "technical_management_subtask";

    /**
     * 子任务表单cid
     */
    public static final String SUB_TABLE_MANAGE_TASK_CID = "technical_management_subtask";

    /** 子任务子表单 cid */
    public static final String SUB_TABLE_TECHNOLOGY_SUB_TASK_CID = "manage_sub_task_table";

    /**
     * 反馈附件cid
     */
    public static final String APPROVAL_ATTACHMENT_CID = "approval_attachment";

    /**
     * 反馈类型cid
     */
    public static final String FEEDBACK_TYPE_CID = "feedback_type";

    /**
     * 进展描述cid
     */
    public static final String PROGRESS_DESCRIPTION_CID = "progress_description";

    /**
     * 技术管理任务-审批页面  说明cid
     */
    public static final String APPROVE_DESCRIPTION = "approve_description";

    /**
     * 技术管理任务-审批页面  审核结果cid
     */
    public static final String APPROVE_RESULT_CID = "approve_result";


    /**
     * 子任务子表单cid
     */
    public static final String TECHNICAL_MANAGEMENT_SUBTASK_TABLE_CID = "technical_management_subtask";

    /** 责任转让页面，修改后的责任人 */
    public static final String RESPONSIBILITY_TRANSFERENCE_PERSON_CID = "new_responsible_person";

    /** 网络变更任务标识 */
    public static final String NETWORK_CHANGE_FLAG = "network_change_flag";

    /** 用户要求保留 */
    public static final String REQUIRE_REVERSE = "require_reverse";

    /** 责任人 */
    public static final String RESPONSIBLE_PERSON = "responsible_person";

    /** 当前处理人 */
    public static final String CURRENT_PROCESSOR = "current_processor";

    /** 当前处理组 */
    public static final String CURRENT_PROCESS_TEAM = "current_process_team";

    /** 系统管理员标识 */
    public static final String SYSTEM_ADMIN_FLAG = "system_admin_flag";

    /** 提示信息 */
    public static final String NOTICE = "notice";

    /**
     * 技术管理任务查询进展cid
     */
    public static final String MANAGE_TASK_CHECK_PROGRESS_TAB_CID = "iTechCloudCustomTaskProgress_check_progress";

    /**
     * 批次任务左侧列表cid
     */
    public static final String ITECH_CLOUD_CUSTOM_BATCHTASK_LIST_CID = "iTechCloudCustomBatchTask_list";

    /**
     * 批次任务右侧iframecid
     * Button
     */
    public static final String ITECH_CLOUD_CUSTOM_BATCHTASK_IFRAME_CID = "iTechCloudCustomIframe_batch_task";

    /**
     * 批次任务自定义刷新按钮
     */
    public static final String ITECH_CLOUD_CUSTOM_BATCHTASK_REFRESH_BUTTON_CID = "iTechCloudCustomButton_iframe_refresh";


    // ==================== 操作CID，(格式：OPERATION_操作名称_CID) ====================
    // ================== 服务对象返回值Key，(格式：SERVICE_字段名称_CID) ================
    /**
     * 服务对象 - NIS - 网络Id
     */
    public static final String SERVICE_NETWORK_ID_CID = "networkId";

    public static final String SERVICE_PRODUCT_MODEL_FULL_ID_PATH_CID = "product_model_full_id_path";

    /**
     * 标准方案-方案附件
     */
    public static final String SCHEME_FILE_CID = "scheme_file";

    /**
     * 标准方案-版本号
     */
    public static final String VERSION_NO_CID = "version_no";

    /**
     * 标准方案-文件大小
     */
    public static final String FILE_SIZE_CID = "file_size";

    /**
     * 标准方案-可用于AI生成操作方案
     */
    public static final String AI_FLAG_CID = "ai_flag";

    /**
     * 标准方案-文档云ID
     */
    public static final String DOC_ID_CID = "doc_id";


    // ==================== 自定义页面-NIS产品型号  ====================

    /**操作对象产品型号查询页面 pageid 及 页面编码 相同*/
    public static final String SELF_PAGE_OPERATION_OBJ_PROD_MODEL_QUERY_PAGE_ID = "product_model_query";

    /**操作对象产品型号查询页面 表格 唯一标识*/
    public static final String SELF_PAGE_OPERATION_OBJ_PROD_MODEL_TABLE_ID = "operation_object_product_model_query_table";

    // ==================== 自定义页面-NIS客户网元  ====================

    /**操作对象产品型号查询页面 pageid 及 页面编码 相同*/
    public static final String SELF_PAGE_APPLY_CCN_AUTHORIZATION_OFFICE_NAME_QUERY_PAGE_ID = "ccn_site_query";

    // ==================== 自定义页面-iVersion版本信息  ====================

    /**操作对象-版本查询页面 表格 唯一标识*/
    public static final String SELF_PAGE_VERSION_TABLE_ID = "version_table";

    /** 当前版本 - CSC 版本列表 */
    public static final String TABLE_CSC_VERSION = "csc_version_table";

    /**自定义页面-版本列表-按钮-查询 */
    public static final String SELF_PAGE_VERSION_BUTTON_QUERY_ID = "version_query_button";

    /**自定义页面-版本列表-查询条件-版本名称 */
    public static final String SELF_PAGE_VERSION_QUERY_VERSION_NAME_ID = "engineering_version_name";

    /**自定义页面-版本列表-查询条件-版本发布号 */
    public static final String SELF_PAGE_VERSION_QUERY_VERSION_NO_ID = "version_release_no";

    /**自定义页面-版本列表-查询条件-公司 */
    public static final String SELF_PAGE_VERSION_QUERY_ORGANIZATION_ID = "corp_code";

    /**自定义页面-版本列表-查询条件-产品经营团队 */
    public static final String SELF_PAGE_VERSION_QUERY_PRODUCT_TEAM_ID = "prod_team";

    /**自定义页面-版本列表-查询条件-产品线 */
    public static final String SELF_PAGE_VERSION_QUERY_PRODUCT_LINE_ID = "prod_line";

    /**自定义页面-版本列表-查询条件-产品大类 */
    public static final String SELF_PAGE_VERSION_QUERY_PRODUCT_CLASS_1_ID = "prod_class_1";

    /**自定义页面-版本列表-查询条件-产品小类 */
    public static final String SELF_PAGE_VERSION_QUERY_PRODUCT_CLASS_2_ID = "prod_class_2";

    /**自定义页面-版本列表-查询条件-产品 */
    public static final String SELF_PAGE_VERSION_QUERY_PRODUCT_NO_ID = "prod_no";

    /** CSC 版本 - 版本发布号 */
    public static final String CSC_VERSION_RELEASE_NO = "csc_version_release_no";

    /** CSC 版本 - 工程版本名称 */
    public static final String CSC_VERSION_RELEASE_NAME = "csc_version_release_name";

    /** 版本ID */
    public static final String VERSION_ID = "version_id";

    /** 版本名称 */
    public static final String VERSION_NAME = "version_name";

    // ==================== 自定义页面-ZXRDC无线产品升级单  ====================

    /**自定义页面-无线产品升级单查询页面 pageid 及 页面编码 相同*/
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_PAGE_ID = "wireless_product_upgrade_form";

    /**自定义页面-无线产品升级单查询页面 表格 唯一标识*/
    public static final String SELF_PAGE_UPGRADE_FORM_TABLE_ID = "wireless_product_upgrade_form_table";

    /**自定义页面-无线产品升级单列表-按钮-查询 */
    public static final String SELF_PAGE_UPGRADE_FORM_BUTTON_QUERY_ID = "button_search_upgrade_form";

    /**自定义页面-无线产品升级单列表-查询条件-申请单号 */
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_APPLY_NO_ID = "apply_no";

    /**自定义页面-无线产品升级单列表-查询条件-申请主题 */
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_APPLY_SUBJECT_ID = "apply_subject";

    /**自定义页面-无线产品升级单列表-查询条件-营销 */
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_MARKETING_ID = "marketing";

    /**自定义页面-无线产品升级单列表-查询条件-代表处 */
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_OFFICE_ID = "office";

    /**自定义页面-无线产品升级单列表-查询条件-运营商 */
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_OPERATOR_ID = "operator";

    /**自定义页面-无线产品升级单列表-查询条件-升级主产品 */
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_MAIN_PRODUCT_ID = "upgrade_main_product";

    /**自定义页面-无线产品升级单列表-查询条件-计划开始时间 */
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_START_DATE_ID = "plan_start_date";

    /**自定义页面-无线产品升级单列表-查询条件-计划结束时间 */
    public static final String SELF_PAGE_UPGRADE_FORM_QUERY_END_DATE_ID = "plan_end_date";


    // ==================== 自定义页面-ZXRDC远程申请单  ====================

    /**批次任务-发布通告页面-远程接入申请子表单 组件唯一标识 CID*/
    public static final String REMOTE_APPLICATION_TABLE_CID = "remote_application_table";

    /**批次任务-发布通告页面-远程接入申请子表单 实体唯一标识 KEY*/
    public static final String REMOTE_APPLICATION_TABLE_KEY = "gdpr_remote_application_form";

    /**自定义页面-远程申请单查询页面 pageid 及 页面编码 相同*/
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_PAGE_ID = "remote_access_application_form";

    /**自定义页面-远程申请单查询页面 表格 实体唯一标识 KEY*/
    public static final String SELF_PAGE_REMOTE_FORM_TABLE_ID = "remote_access_application_form_table";

    /**自定义页面-远程申请单查询页面 查询按钮 唯一标识*/
    public static final String SELF_PAGE_REMOTE_FORM_SEARCH_BUTTON_ID = "search_remote_form";

    /**自定义页面-远程申请单列表-查询条件-申请单号 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_CODE_ID = "apply_task_code_cid";

    /**自定义页面-远程申请单列表-查询条件-经营部 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_OPERATOR_ID = "operator_cid";

    /**自定义页面-远程申请单列表-查询条件-产品团队 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_TEAM_ID = "team_cid";

    /**自定义页面-远程申请单列表-查询条件-安全物理区域 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_AREA_ID = "area_cid";

    /**自定义页面-远程申请单列表-查询条件-责任人 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_PERSON_ID = "responsible_person_cid";

    /**自定义页面-远程申请单列表-查询条件-参与人员 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_PARTICIPANTS_ID = "participants_cid";

    /**自定义页面-远程申请单列表-查询条件-国家/地区 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_COUNTRY_ID = "country_cid";

    /**自定义页面-远程申请单列表-查询条件-项目名称 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_PROJECT_ID = "project_cid";

    /**自定义页面-远程申请单列表-查询条件-一线联系人 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_CONTACT_ID = "contact_cid";

    /**自定义页面-远程申请单列表-查询条件-支持开始时间 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_START_ID = "start_time_cid";

    /**自定义页面-远程申请单列表-查询条件- 支持结束时间 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_END_ID = "end_time_cid";

    /**自定义页面-远程申请单列表-查询条件-单据状态 */
    public static final String SELF_PAGE_REMOTE_FORM_QUERY_STATUS_ID = "task_status_cid";

    /**批次任务-发布通告-远程接入申请单-单据编号 组件cid */
    public static final String REMOTE_APPLICATION_CODE_CID = "remote_application_code";

    /**批次任务-发布通告-远程接入申请单-单据主题 组件cid */
    public static final String REMOTE_APPLICATION_SUBJECT_CID = "remote_application_subject";

    /**批次任务-发布通告-远程接入申请单-经营部 组件cid */
    public static final String REMOTE_APPLICATION_OPERATOR_CID = "remote_application_operator";

    /**批次任务-发布通告-远程接入申请单-产品团队 组件cid */
    public static final String REMOTE_APPLICATION_PRODUCT_TEAM_CID = "remote_application_product_team";

    /**批次任务-发布通告-远程接入申请单-物理安全区域 组件cid */
    public static final String REMOTE_APPLICATION_AREA_CID = "remote_application_area";

    /**批次任务-发布通告-远程接入申请单-责任人 组件cid */
    public static final String REMOTE_APPLICATION_PERSON_CID = "remote_application_person";

    /**批次任务-发布通告-远程接入申请单-支持开始时间 组件cid */
    public static final String REMOTE_APPLICATION_START_TIME_CID = "remote_application_start_time";

    /**批次任务-发布通告-远程接入申请单-支持结束时间 组件cid */
    public static final String REMOTE_APPLICATION_END_TIME_CID = "remote_application_end_time";

    /**=远程接入申请单-单据编号 实体key */
    public static final String REMOTE_APPLICATION_CODE_KEY = "apply_task_code";

    /**远程接入申请单-单据主题 实体key */
    public static final String REMOTE_APPLICATION_SUBJECT_KEY = "subject";

    /**远程接入申请单-经营部 实体key */
    public static final String REMOTE_APPLICATION_OPERATOR_KEY = "operator_dept";

    /**远程接入申请单-产品团队 实体key */
    public static final String REMOTE_APPLICATION_PRODUCT_TEAM_KEY = "product_team";

    /**远程接入申请单-物理安全区域 实体key */
    public static final String REMOTE_APPLICATION_AREA_KEY = "secure_physical_area";

    /**远程接入申请单-责任人 实体key */
    public static final String REMOTE_APPLICATION_PERSON_KEY = "responsible_person";

    /**远程接入申请单-支持开始时间 实体key */
    public static final String REMOTE_APPLICATION_START_TIME_KEY = "start_time";

    /**远程接入申请单-支持结束时间 实体key */
    public static final String REMOTE_APPLICATION_END_TIME_KEY = "end_time";


    /**
     * 批次任务步骤条1，iframecid
     */
    public static final String ITECHCLOUDCUSTOMIFRAME_BATCH_TASK_STEP_ONE_CID = "iTechCloudCustomIframe_batch_task_step_one";

    /**
     * 批次任务步骤条1，iframecid
     */
    public static final String ITECHCLOUDCUSTOMIFRAME_BATCH_TASK_STEP_TWO_CID = "iTechCloudCustomIframe_batch_task_step_two";

    /**
     * 批次任务步骤条1，iframecid
     */
    public static final String ITECHCLOUDCUSTOMIFRAME_BATCH_TASK_STEP_THREE_CID = "iTechCloudCustomIframe_batch_task_step_three";


    // ==================== 网络集  ====================
    /**
     * 国家地区/省州 实体字段key
     */
    public static final String STANDARD_NETWORK_SET_COUNTRY_PROVINCE_KEY =  "country_province";
    /**
     * 代表处 实体字段key
     */
    public static final String STANDARD_NETWORK_SET_ORGANIZATION_KEY =  "organization";
    /**
     * 产品经营团队 实体字段key
     */
    public static final String STANDARD_NETWORK_SET_PRODUCT_TEAM_KEY =  "product_team";
    /**
     * 客户标识 实体字段key
     */
    public static final String STANDARD_NETWORK_SET_CUSTOMER_IDENTIFICATION_KEY =  "customer_identification";
    /**
     * 网络集名称 实体字段key
     */
    public static final String STANDARD_NETWORK_SET_NAME_KEY =  "network_set_name";
    /**
     * 网络选择 实体字段key
     */
    public static final String STANDARD_NETWORK_SET_NETWORK_SELECT_KEY =  "network";
    /**
     * 网络选择 组件cid（标准网络集，我的网络集通用）
     */
    public static final String NETWORK_SET_NETWORK_SELECT_CID =  "network_select";


    // ==================== 自定义页面-任务管理  ====================
    /**
     * 任务管理 - 自定义表格
     */
    public static final String TABLE_ASSIGNMENT_CUSTOM = "table_assignment_custom";
    /**
     * 关联任务实体表格
     */
    public static final String TABLE_ASSIGNMENT = "table_assignment";

    /**
     * 待我处理-复选框上面数字
     */
    public static final String PENDING_HANDLE = "pending_handle";
    /**
     * 我已处理-复选框上面数字
     */
    public static final String HANDLED = "handled";
    /**
     * 我创建的-复选框上面数字
     */
    public static final String CREATE_SELF = "create_self";
    /**
     * 我负责的-复选框上面数字
     */
    public static final String RESPONSIBLE_SELF = "responsible_self";
    /**
     * 待启动-复选框上面数字
     */
    public static final String START = "start";
    /**
     * 执行中-复选框上面数字
     */
    public static final String EXECUTE = "execute";
    /**
     * 已关闭-复选框上面数字
     */
    public static final String CLOSE = "close";
    /**
     * 审批中-复选框上面数字
     */
    public static final String APPROVE = "approve";

    /**
     * 待我处理-复选框
     */
    public static final String PENDING_HANDLE_CHECK = "pending_handle_check";
    /**
     * 我已处理-复选框
     */
    public static final String HANDLED_CHECK = "handled_check";
    /**
     * 我创建的-复选框
     */
    public static final String CREATE_SELF_CHECK = "create_self_check";
    /**
     * 我负责的-复选框
     */
    public static final String RESPONSIBLE_SELF_CHECK = "responsible_self_check";

    /**
     * 待启动-复选框
     */
    public static final String START_CHECK = "start_check";
    /**
     * 执行中-复选框
     */
    public static final String EXECUTE_CHECK = "execute_check";
    /**
     * 已关闭-复选框
     */
    public static final String CLOSE_CHECK = "close_check";
    /**
     * 审批中-复选框
     */
    public static final String APPROVE_CHECK = "approve_check";
    /**
     * 任务总数-图文
     */
    public static final String ASSIGNMENT_COUNT = "assignment_count";
    /**
     * 网络集
     */
    public static final String NETWORK_SET = "network_set";


    // ==================== 操作方案库  ====================
    public static final String OPERATION_SCHEME_NAME_CID = "scheme_name_condition";

    public static final String OPERATION_SCHEME_TYPE_CID = "scheme_type_condition";

    public static final String OPERATION_PRODUCT_TYPE_CID = "iTechCloudCustomTreeSelect_product";

    public static final String OPERATION_TYPE_CID = "operation_type_condition";

    public static final String OPERATION_SCHEME_STATUS_CID = "scheme_status_condition";

    public static final String OPERATION_SCHEME_TABLE_CID = "operation_scheme_table";

    public static final String SCHEME_ATTACHMENT_DATA_KEY = "attachmentfield_jg0kpnnh";

    public static final String FILE_SIZE_SHOW_DATA_KEY = "custom_ez8agudt";

    public static final String INTERNAL_OPERATION_SCHEME_CODE_PROPERTY_KEY = "internal_operation_scheme_code";

    public static final String CUSTOMER_OPERATION_SCHEME_CODE_PROPERTY_KEY = "customer_operation_scheme_code";

    public static final String INTERNAL_OPERATION_SOLUTION_PROPERTY_KEY = "internal_operation_solution";

    public static final String CUSTOMER_OPERATION_SOLUTION_PROPERTY_KEY = "customer_operation_solution";

    /**
     * 分级保障-业务中断时长系数-产品经营团队 KEY
     */
    public static final String PRODUCT_OPERATION_TEAM = "product_operation_team";
    /**
     * 分级保障-业务中断时长系数-业务中断时长上限（分钟） KEY
     */
    public static final String MAXIMUM_SERVICE_INTERRUPTION_DURATION = "maximum_service_interruption_duration";
    /**
     * 分级保障-业务中断时长系数-业务中断时长下限（分钟） KEY
     */
    public static final String MINIMUM_SERVICE_INTERRUPTION_DURATION = "minimum_service_interruption_duration";
    /**
     * 选项配置-操作类型配置-操作类型 KEY
     */
    public static final String FASTCODEFIELD_OPERATE_TYPE = "fastcodefield_operate_type";
    /**
     * 选项配置-操作类型配置-操作类型分组 KEY
     */
    public static final String FASTCODEFIELD_OPERATE_TYPE_GROUP = "fastcodefield_operate_type_group";

    // ----------------------- 审批历史--------------//

    // 技术交付部/网络处审批，字段“对网络服务部的要求”实体cid
    public static final String FIELD_REQ_FOR_NET_SERVICE_DEPT_CID = "req_for_net_service_dept";

    // 远程中心方案负责人（员工组件）布局cid
    public static final String COMPONENT_REMOTE_CENTER_SOLUTION_OWNER_CID = "remote_center_solution_owner";

    // 远程中心方案负责人（下拉单选）布局cid
    public static final String COMPONENT_REMOTE_CENTER_SOLUTION_OWNER_SELECT_CID = "remote_center_solution_owner_select";

    // 远程中心方案负责组（员工组件）布局cid
    public static final String COMPONENT_REMOTE_CENTER_SOLUTION_TEAM_CID = "remote_center_solution_team";

    // 远程中心方案负责组（下拉单选）布局cid
    public static final String COMPONENT_REMOTE_CENTER_SOLUTION_TEAM_MULTISELECT_CID = "remote_center_solution_team_multiselect";

    // 远程中心方案负责人（员工组件）实体cid
    public static final String FIELD_REMOTE_CENTER_SOLUTION_OWNER_CID = "remote_center_solution_owner";

    // 远程中心方案负责人（下拉单选）实体cid
    public static final String FIELD_REMOTE_CENTER_SOLUTION_OWNER_SELECT_CID = "remote_center_solution_owner_select";

    // 远程中心方案负责组（员工组件）实体cid
    public static final String FIELD_REMOTE_CENTER_SOLUTION_TEAM_CID = "remote_center_solution_team";

    // 远程中心方案负责组（下拉单选）实体cid
    public static final String FIELD_REMOTE_CENTER_SOLUTION_TEAM_MULTISELECT_CID = "remote_center_solution_team_multiselect";

    // 远程中心操作实施指派，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_REMOTE_CENTER_OPER_ASSIGN_CID = "person_id_remote_center_oper_assign";

    // 远程中心操作实施指派，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_REMOTE_CENTER_OPER_ASSIGN_CID = "dept_id_remote_center_oper_assign";

    // 研发一体化，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_RD_INTEGRATIONS_CID = "person_id_rd_integrations";

    // 研发一体化，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_RD_INTEGRATIONS_CID = "dept_id_rd_integrations";

    // 研发一体化，支持方式实体唯一标识
    public static final String FIELD_SUPPORT_WAY_ID_RD_INTEGRATIONS_CID = "radiofield_68e6g8il";

    // 研发一体化，联系方式实体唯一标识
    public static final String FIELD_TEL_NUM_RD_INTEGRATIONS_CID = "tel_num_rd_integration";

    // 研发经理审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_RD_MANAGER_CID = "person_id_rd_manager";

    // 研发经理审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_RD_MANAGER_CID = "dept_id_rd_manager";

    // 研发经理审批，联系方式实体唯一标识
    public static final String FIELD_TEL_NO_RD_MANAGER_CID = "tel_no_rd_manager";

    // SSP产品支持团队审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_SSP_PROD_SUPPORT_CID = "person_id_ssp_prod_support";

    // SSP产品支持团队审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_SSP_PROD_SUPPORT_CID = "dept_id_ssp_prod_support";

    // SSP产品支持团队审批，联系方式实体唯一标识
    public static final String FIELD_TEL_NO_SSP_PROD_SUPPORT_CID = "tel_no_ssp_prod_support";

    // 服务产品支持部审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_SERVICE_PROD_SUPPORT_CID = "person_id_service_prod_support";

    // 服务产品支持部审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_SERVICE_PROD_SUPPORT_CID = "dept_id_service_prod_support";

    // 服务产品支持部审批，联系方式实体唯一标识
    public static final String FIELD_TEL_NO_SERVICE_PROD_SUPPORT_CID = "tel_no_service_prod_support";

    // 网服部一体化关联产品审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_NET_INTEGRATION_CID = "person_id_net_integration";

    // 网服部一体化关联产品审批，支持方式实体唯一标识
    public static final String FIELD_SUPPORT_WAY_ID_NET_INTEGRATION_CID = "support_way_net_integration";

    // 网服部一体化关联产品审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_NET_INTEGRATION_CID = "dept_id_net_integration";

    // 网服部一体化关联产品审批，联系方式实体唯一标识
    public static final String FIELD_TEL_NO_NET_INTEGRATION_CID = "tel_no_net_integration";

    // 网络服务部审批审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_NET_SERVICE_DEPT_APPROVE_CID = "person_id_net_service_dept_approve";

    // 网络服务部审批审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_NET_SERVICE_DEPT_APPROVE_CID = "dept_id_net_service_dept_approve";

    // 技术交付部网络处远程方案审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_TD_NET_DEPT_APP_SOLUTION_CID = "person_id_td_net_dept_app_solution";

    // 技术交付部网络处远程方案审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_TD_NET_DEPT_APP_SOLUTION_CID = "dept_id_td_net_dept_app_solution";

    // 远程中心方案审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_REMOTE_CENTER_SCHEME_CID = "person_id_remote_center_scheme";

    // 远程中心方案审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_REMOTE_CENTER_SCHEME_CID = "dept_id_remote_center_scheme";

    // 技术交付部/网络处审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_TD_NET_DEPT_APPROVE_CID = "person_id_td_net_dept_approve";

    // 技术交付部/网络处审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_TD_NET_DEPT_APPROVE_CID = "dept_id_td_net_dept_approve";

    // 技术交付部/网络处审批，联系方式实体唯一标识
    public static final String FIELD_TEL_NO_TD_NET_DEPT_APPROVE_CID = "tel_no_td_net_dept_approve";

    // 代表处产品TD审批，支持人员实体唯一标识
    public static final String FIELD_PERSON_ID_REP_PROD_TD_APP_CID = "person_id_rep_prod_td_app";

    // 代表处产品TD审批，归属部门实体唯一标识
    public static final String FIELD_DEPT_ID_REP_PROD_TD_APP_CID = "dept_id_rep_prod_td_app";

    // 远程中心操作实施指派，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_REMOTE_CENTER_OPER_ASSIGN_CID = "support_person_remote_center_oper_assign";

    // 研发一体化关联产品审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_RD_INTEGRATIONS_CID = "support_person_rd_integration";

    // 研发经理审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_RD_MANAGER_CID = "support_person_rd_manager";

    // SSP产品支持团队审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_SSP_PROD_SUPPORT_CID = "support_person_ssp_prod_support";

    // 服务产品支持部审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_SERVICE_PROD_SUPPORT_CID = "support_person_service_prod_support";

    // 网服部一体化关联产品审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_NET_SERVICE_INTEGRATION_CID = "support_person_net_service_integration";

    // 网络服务部审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_NET_SERVICE_DEPT_APPROVE_CID = "support_person_net_service_dept_approve";

    // 技术交付部网络处远程方案审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_TD_NET_DEPT_APP_SOLUTION_CID = "support_person_td_net_dept_app_solution";

    // 远程中心方案审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_REMOTE_CENTER_SCHEME_CID = "support_person_remote_center_scheme";

    // 技术交付部/网络处审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_TD_NET_DEPT_APPROVE_CID = "support_person_td_net_dept_approve";

    // 代表处产品TD审批，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_REP_PROD_TD_APP_CID = "support_person_rep_prod_td_app";

    // 远程中心操作实施指派，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_REMOTE_CENTER_OPER_ASSIGN_CID = "support_person_remote_center_oper_assign";

    // 研发一体化关联产品审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_RD_INTEGRATIONS_CID = "support_person_rd_integrations";

    // 研发经理审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_RD_MANAGER_CID = "support_person_rd_manager";

    // SSP产品支持团队审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_SSP_PROD_SUPPORT_CID = "support_person_ssp_prod_support";

    // 服务产品支持部审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_SERVICE_PROD_SUPPORT_CID = "support_person_service_prod_support";

    // 网服部一体化关联产品审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_NET_SERVICE_INTEGRATION_CID = "support_person_net_service_integration";

    // 网络服务部审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_NET_SERVICE_DEPT_APPROVE_CID = "support_person_net_service_dept_approve";

    // 技术交付部网络处远程方案审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_TD_NET_DEPT_APP_SOLUTION_CID = "support_person_td_net_dept_app_solution";

    // 远程中心方案审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_REMOTE_CENTER_SCHEME_CID = "support_person_remote_center_scheme";

    // 远程中心方案审批，联系方式实体唯一标识
    public static final String FIELD_TEL_NO_REMOTE_CENTER_SCHEME_CID = "tel_no_remote_center_scheme";

    // 技术交付部/网络处审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_TD_NET_DEPT_APPROVE_CID = "support_person_td_net_dept_approve";

    // 代表处产品TD审批，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_REP_PROD_TD_APP_CID = "support_person_rep_prod_td_app";

    // 分包商网络责任人审核，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_NET_OWNER_CID = "support_person_net_owner";

    // 分包商办事处产品经理审核，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_OFFICE_PROD_MANAGER_CID = "support_person_office_prod_manager";

    // 分包商网络处审核，支持人员列表实体cid
    public static final String FIELD_SUPPORT_PERSON_NET_DEPT_CID = "support_person_net_dept";

    // 分包商网络责任人审核，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_NET_OWNER_CID = "support_person_net_owner";

    // 分包商办事处产品经理审核，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_OFFICE_PROD_MANAGER_CID = "support_person_office_prod_manager";

    // 分包商网络处审核，支持人员列表布局cid
    public static final String COMPONENT_SUPPORT_PERSON_NET_DEPT_CID = "support_person_net_dept";

    // 分包商网络责任人审核，支持人员列表，归属部门实体cid
    public static final String FIELD_DEPT_ID_NET_OWNER_CID = "dept_id_net_owner";

    // 分包商网络责任人审核，支持人员列表，电话实体cid
    public static final String FIELD_TEL_NO_NET_OWNER_CID = "tel_no_net_owner";

    // 分包商办事处产品经理审核，支持人员列表，归属部门实体cid
    public static final String FIELD_DEPT_ID_OFFICE_PROD_MANAGER_CID = "dept_id_office_prod_manager";

    // 分包商办事处产品经理审核，支持人员列表，电话实体cid
    public static final String FIELD_TEL_NO_OFFICE_PROD_MANAGER_CID = "tel_no_office_prod_manager";

    // 分包商网络处审核，支持人员列表，归属部门实体cid
    public static final String FIELD_DEPT_ID_NET_DEPT_CID = "dept_id_net_dept";

    // 分包商网络处审核，支持人员列表，电话实体cid
    public static final String FIELD_TEL_NO_NET_DEPT_CID = "tel_no_net_dept";

    // 分包商网络责任人审核，支持人员列表，支持人员实体cid
    public static final String FIELD_PERSON_ID_NET_OWNER_CID = "person_id_net_owner";

    // 分包商办事处产品经理审核，支持人员列表，支持人员实体cid
    public static final String FIELD_PERSON_ID_OFFICE_PROD_MANAGER_CID = "person_id_office_prod_manager";

    // 分包商网络处审核，支持人员列表，支持人员实体cid
    public static final String FIELD_PERSON_ID_NET_DEPT_CID = "person_id_net_dept";

    // 分包商网络处审核，办事处产品经理实体cid
    public static final String OFFICE_PROD_MANAGER_CID = "office_prod_manager";

    // 分包商网络处审核，办事处产品经理组实体cid
    public static final String OFFICE_PROD_MANAGER_REVIEW_TEAM_CID = "office_prod_manager_review_team";

    /**
     * 自定义页面-人员技能认证信息-表格
     */
    public static final String TABLE_PERSON_CERTIFICATION = "table_person_certification";

    /**
     * 自定义页面-人员技能认证信息-页面编码
     */
    public static final String PERSON_CERTIFICATION = "certificate_list";

    /**
     * 自定义页面-人员技能认证信息-页面标题
     */
    public static final String PERSON_CERTIFICATION_TITLE = "certificate_title";

    // 多模评审审核结果布局cid
    public static final String COMPONENT_APPROVE_RESULT_MULTIMODE_PRODUCT_CID = "approve_result_multimode_product";

    // 多模评审审核意见布局cid
    public static final String COMPONENT_APPROVE_OPINION_MULTIMODE_PRODUCT_CID = "approve_opinion_multimode_product";

    // 多模网络服务部审核布局cid
    public static final String COMPONENT_MULTIMODE_NET_APPROVE_TEAM_CID = "multimode_net_approve_team";
    /**
     * 网络处审核人
     */
    public static final String NET_DEPT_APPROVAL = "net_dept_approval";

    /**
     * 网络处审核组
     */
    public static final String NET_DEPT_APPROVE_TEAM = "net_dept_approve_team";

    // 内部网络变更单审核页面，驳回审核结果布局cid
    public static final String COMPONENT_REJECTION_RESULT_CID = "radiofield_result_rejection";

    // 内部网络变更单，驳回审核结果实体cid
    public static final String FIELD_REJECTION_RESULT_CID = "radiofield_result_rejection";

    // 内部网络变更单，驳回时审批意见布局cid
    public static final String COMPONENT_REJECTION_CID = "textareafield_audit_rejection";

    // 内部网络变更单，驳回时审批意见实体cid
    public static final String FIELD_REJECTION_CID = "textareafield_audit_rejection";

    // 内部网络变更单，驳回时审批意见附件布局cid
    public static final String COMPONENT_REJECTION_ATTACHMENT_FILE_CID = "attachmentfield_attachment_rejection";

    // 内部网络变更单，驳回时审批意见附件实体cid
    public static final String FIELD_REJECTION_ATTACHMENT_FILE_CID = "attachmentfield_attachment_rejection";

    // 内部网络变更单，驳回提示语布局cid
    public static final String COMPONENT_PROMPT_REJECTION_CID = "prompt_rejection";

    // 内部网络变更单，驳回审批人实体cid
    public static final String FIELD_PERSON_REJECTION_CID = "employeefield_person_rejection";

    // 内部网络变更单，驳回审批人布局cid
    public static final String COMPONENT_PERSON_REJECTION_CID = "employeefield_person_rejection";

    // 内部网络变更单，驳回节点自定义编码实体cid
    public static final String FIELD_REJECTION_EXTEND_CODE_CID = "rejection_extend_code";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_PROPERTY_KEY = "multi_product_linkage_guarantee";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_CID = "multi_product_linkage_guarantee_cid";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_PRODUCT_CID = "ServiceObject_product_class";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_PERSON_CID = "EmployeeField_implementation_responsible_person";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_ASSIGNMENT_ID_CID = "BasicData_assignment_id";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_ASSIGNMENT_NAME_CID = "BasicDataProps_assignment_name";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_PLAN_START_TIME_CID = "BasicDataProps_plan_start_time";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_ASSIGNMENT_STATUS_CID = "BasicDataProps_assignment_status";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_CURRENT_PROGRESS_CID = "TextField_current_progress";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_CURRENT_HANDLER_CID = "TextField_current_handler";

    public static final String MULTI_PRODUCT_LINKAGE_GUARANTEE_IS_MAIN_TASK_CID = "SwitchField_is_main_task";

    public static final String MULTI_PROD_GUARANTEE_DELETE_BUTTON_CID = "multi_prod_guarantee_delete_button_cid";

    public static final String MULTI_PROD_GUARANTEE_CREATE_BUTTON_CID = "multi_prod_guarantee_create_button_cid";

    public static final String NETWORK_CHANGE_SUBMIT_72_BUTTON_CID = "submit_over_72_hours_button_cid";

    public static final String NETWORK_CHANGE_CLOSE_BUTTON_CID = "close_button_cid";

    // 合作商网络变更单审核页面，驳回审核结果布局cid
    public static final String COMPONENT_PARTNER_REJECTION_RESULT_CID = "radiofield_result_rejection";

    // 合作商网络变更单，驳回审核结果实体cid
    public static final String FIELD_PARTNER_REJECTION_RESULT_CID = "radiofield_result_rejection";

    // 合作商网络变更单，驳回时审批意见布局cid
    public static final String COMPONENT_PARTNER_REJECTION_CID = "textareafield_audit_rejection";

    // 合作网络变更单，驳回时审批意见实体cid
    public static final String FIELD_PARTNER_REJECTION_CID = "textareafield_audit_rejection";

    // 合作商网络变更单，驳回时审批意见附件布局cid
    public static final String COMPONENT_PARTNER_REJECTION_ATTACHMENT_FILE_CID = "attachmentfield_attachment_rejection";

    // 合作方网络变更单，驳回时审批意见附件实体cid
    public static final String FIELD_PARTNER_REJECTION_ATTACHMENT_FILE_CID = "attachmentfield_attachment_rejection";

    // 合作方网络变更单，驳回提示语布局cid
    public static final String COMPONENT_PARTNER_PROMPT_REJECTION_CID = "partner_prompt_rejection";

    // 合作方网络变更单，驳回审批人实体cid
    public static final String FIELD_PARTNER_PERSON_REJECTION_CID = "employeefield_person_rejection";

    // 合作方网络变更单，驳回审批人布局cid
    public static final String COMPONENT_PARTNER_PERSON_REJECTION_CID = "employeefield_person_rejection";

    // 合作方网络变更单，驳回节点自定义编码实体cid
    public static final String FIELD_PARTNER_REJECTION_EXTEND_CODE_CID = "rejection_extend_code";

    // 合作商网络责任人审批，审批结果实体cid
    public static final String FIELD_APPROVE_RESULT_NET_OWNER_CID = "approve_result_net_owner";

    // 合作商网络责任人审批，是否需要办事处产品经理审核实体Cid
    public static final String FIELD_IS_REVIEWER_OFFICE_PROD_MANAGER_CID = "is_review_office_prod_manager";

    /**
     * 变更单审批页面--支持人员子表单--清除行还是默认加一行的标志位（实体唯一标识）
     */
    public static final String SUPPORT_PERSON_TABLE_FLAG_KEY = "textfield_support_person_table_flag";
    // 合作商网络责任人审批，办事处产品经理实体Cid
    public static final String FIELD_OFFICE_PROD_MANAGER_CID = "office_prod_manager";
    // 合作商网络责任人审批，办事处产品经理实体Cid（下拉）
    public static final String FIELD_OFFICE_PROD_MANAGER_SELECTED_CID = "office_prod_manager_selected";
    // 合作商网络责任人审批，办事处产品经理审核组实体Cid
    public static final String FIELD_OFFICE_PROD_MANAGER_REVIEWER_TEAM_CID = "office_prod_manager_review_team";
    // 合作商网络责任人审批，办事处产品经理审核组实体Cid（下拉）
    public static final String FIELD_OFFICE_PROD_MANAGER_REVIEWER_TEAM_MULTI_CID = "office_prod_manager_review_team_multi";

    // 合作商网络责任人审批，办事处产品经理布局Cid
    public static final String COMPONENT_OFFICE_PROD_MANAGER_CID = "office_prod_manager";
    // 合作商网络责任人审批，办事处产品经理布局Cid（下拉）
    public static final String COMPONENT_OFFICE_PROD_MANAGER_SELECTED_CID = "office_prod_manager_selected";
    // 合作商网络责任人审批，办事处产品经理审核组布局Cid
    public static final String COMPONENT_OFFICE_PROD_MANAGER_REVIEWER_TEAM_CID = "office_prod_manager_review_team";
    // 合作商网络责任人审批，办事处产品经理审核组布局Cid（下拉）
    public static final String COMPONENT_OFFICE_PROD_MANAGER_REVIEWER_TEAM_MULTI_CID = "office_prod_manager_review_team_multi";

    // 合作商网络变更单，办事处产品经理审批_审核结果实体cid
    public static final String FIELD_APPROVE_RESULT_OFFICE_PROD_MANAGER_CID = "approve_result_office_prod_manager";

    // 是否需要升级至网络处实体cid
    public static final String FIELD_IS_UPGRADE_NET_DEPT_CID = "is_upgrade_net_dept";

    // 合作商办事处产品经理审批，网络处审核人实体Cid
    public static final String FIELD_NET_DEPT_APPROVAL_CID = "net_dept_approval";
    // 合作商办事处产品经理审批，网络处审核人（下拉）实体Cid
    public static final String FIELD_NET_DEPT_APPROVAL_SELECTED_CID = "net_dept_approval_selected";
    // 合作商办事处产品经理审批，网络处审核组实体Cid
    public static final String FIELD_NET_DEPT_APPROVE_TEAM_CID = "net_dept_approve_team";
    // 合作商办事处产品经理审批，网络处审核组（下拉）实体Cid
    public static final String FIELD_NET_DEPT_APPROVE_TEAM_MULTI_CID = "net_dept_approve_team_multi";

    // 合作商办事处产品经理审批，网络处审核人布局Cid
    public static final String COMPONENT_NET_DEPT_APPROVAL_CID = "net_dept_approval";
    // 合作商办事处产品经理审批，网络处审核人（下拉）布局Cid
    public static final String COMPONENT_NET_DEPT_APPROVAL_SELECTED_CID = "net_dept_approval_selected";
    // 合作商办事处产品经理审批，网络处审核组布局Cid
    public static final String COMPONENT_NET_DEPT_APPROVE_TEAM_CID = "net_dept_approve_team";
    // 合作商办事处产品经理审批，网络处审核组（下拉）布局Cid
    public static final String COMPONENT_NET_DEPT_APPROVE_TEAM_MULTI_CID = "net_dept_approve_team_multi";

    // 内部网络变更单审批，技术交付部/网络处审核，审核结果实体cid
    public static final String FIELD_APPROVE_RESULT_TD_NET_DEPT_APP_CID = "approve_result_td_net_dept_app";

    // 内部网络变更单审批，技术交付部/网络处审核，是否需要升级至网络服务部实体cid
    public static final String FIELD_IS_NET_DEPT_APPROVAL_CID = "is_net_dept_approval";

    // 内部网络变更单，技术交付部/网络处审核，需要网络处总工审核实体cid
    public static final String FIELD_IS_NETWORK_DEPT_CHIEF_ENGINEER_APPROVAL_CID = "is_network_dept_chief_engineer_approval";

    // 内部网络变更单，技术交付部/网络处审核，网络处总工审核组实体cid
    public static final String FIELD_NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM_CID = "network_dept_chief_engineer_app_team";

    // 内部网络变更单审批，技术交付部/网络处审核，网络服务部审核人（员工组件）实体cid
    public static final String FIELD_NETWORK_SERVICE_DEPT_APPROVER_CID = "network_service_dept_approver";

    // 内部网络变更单审批，技术交付部/网络处审核，网络服务部审核组（员工组件）实体cid
    public static final String FIELD_NETWORK_SERVICE_DEPT_APPROVER_TEAM_CID = "network_service_dept_approver_team";

    // 内部网络变更单审批，技术交付部/网络处审核，网络服务部审核人（下拉单选）实体cid
    public static final String FIELD_NETWORK_SERVICE_DEPT_APPROVER_SELECTED_CID = "network_service_dept_approver_selected";

    // 内部网络变更单审批，技术交付部/网络处审核，网络服务部审核组（下拉复选）实体cid
    public static final String FIELD_NETWORK_SERVICE_DEPT_APPROVER_TEAM_MULTI_CID = "network_service_dept_approver_multi";

    // 内部网络变更单审批，技术交付部/网络处审核，网络服务部审核人（员工组件）布局cid
    public static final String COMPONENT_NETWORK_SERVICE_DEPT_APPROVER_CID = "network_service_dept_approver";

    // 内部网络变更单审批，技术交付部/网络处审核，网络服务部审核组（员工组件）布局cid
    public static final String COMPONENT_NETWORK_SERVICE_DEPT_APPROVER_TEAM_CID = "network_service_dept_approver_team";

    // 内部网络变更单审批，技术交付部/网络处审核，网络服务部审核人（下拉单选）布局cid
    public static final String COMPONENT_NETWORK_SERVICE_DEPT_APPROVER_SELECTED_CID = "network_service_dept_approver_selected";

    // 内部网络变更单审批，技术交付部/网络处审核，网络服务部审核组（下拉复选）布局cid
    public static final String COMPONENT_NETWORK_SERVICE_DEPT_APPROVER_TEAM_MULTI_CID = "network_service_dept_approver_multi";

    // 内部网络变更单审批，网络服务处审核，审核结果实体cid
    public static final String FIELD_APPROVE_RESULT_NET_SERVICE_DEPT_APP_CID = "approve_result_net_service_dept_app";
    // 内部网络变更单审批，网络服务处审核，是否需要升级至研发实体cid
    public static final String FIELD_IS_DEV_DEPT_APPROVAL_CID = "is_dev_dept_approval";

    // 内部网络变更单审批，网络服务处审核，研发经理（员工组件）实体cid
    public static final String FIELD_RD_MANAGER_CID = "rd_manager";

    // 内部网络变更单审批，网络服务处审核，研发经理审核组（员工组件）实体cid
    public static final String FIELD_RD_MANAGER_APPROVE_TEAM_CID = "rd_manager_approve_team";

    // 内部网络变更单审批，网络服务处审核，研发经理（下拉单选）实体cid
    public static final String FIELD_RD_MANAGER_SELECTED_CID = "rd_manager_selected";

    // 内部网络变更单审批，网络服务处审核，研发经理审核组（下拉复选）实体cid
    public static final String FIELD_RD_MANAGER_APPROVE_TEAM_MULTI_CID = "rd_manager_approve_team_multi";

    // 内部网络变更单审批，网络服务处审核，是否需要升级至研发布局cid
    public static final String COMPONENT_IS_DEV_DEPT_APPROVAL_CID = "is_dev_dept_approval";

    // 内部网络变更单审批，网络服务处审核，研发经理（员工组件）布局cid
    public static final String COMPONENT_RD_MANAGER_CID = "rd_manager";

    // 内部网络变更单审批，网络服务处审核，研发经理审核组（员工组件）布局cid
    public static final String COMPONENT_RD_MANAGER_APPROVE_TEAM_CID = "rd_manager_approve_team";

    // 内部网络变更单审批，网络服务处审核，研发经理（下拉单选）布局cid
    public static final String COMPONENT_RD_MANAGER_SELECTED_CID = "rd_manager_selected";

    // 内部网络变更单审批，网络服务处审核，研发经理审核组（下拉复选）布局cid
    public static final String COMPONENT_RD_MANAGER_APPROVE_TEAM_MULTI_CID = "rd_manager_approve_team_multi";

    // 内部网络变更单审批，研发经理审核，审核结果实体cid
    public static final String FIELD_APPROVE_RESULT_RD_MANAGER_CID = "approve_result_rd_manager";

    // 内部网络变更单审批，研发经理审核，是否需要研发领导审核（研发经理）实体cid
    public static final String FIELD_IS_DEV_LEADER_APPROVAL_CID = "is_dev_leader_approval";
    // 内部网络变更单审批，研发经理审核，研发领导实体cid
    public static final String FIELD_RD_LEADER_RD_MANAGER_CID = "rd_leader_rd_manager";
    // 内部网络变更单审批，研发经理审核，研发领导（下拉复选）实体cid
    public static final String FIELD_RD_LEADER_RD_MANAGER_MULTI_CID = "rd_leader_rd_manager_multi";
    // 内部网络变更单审批，研发经理审核，是否需要研发领导审核（研发经理）布局cid
    public static final String COMPONENT_IS_DEV_LEADER_APPROVAL_CID = "is_dev_leader_approval";
    // 内部网络变更单审批，研发经理审核，研发领导布局cid
    public static final String COMPONENT_RD_LEADER_RD_MANAGER_CID = "rd_leader_rd_manager";
    // 内部网络变更单审批，研发经理审核，研发领导（下拉复选）布局cid
    public static final String COMPONENT_RD_LEADER_RD_MANAGER_MULTI_CID = "rd_leader_rd_manager_multi";

    // 技术交付部/网络处审批节点中，字段“是否需要升级到SSP支持组”实体cid
    public static final String FIELD_IS_SSP_SUPPORT_TEAM_APPROVAL_CID = "is_ssp_support_team_approval";

    // 技术交付部/网络处审批节点中，字段“SSP支持组”实体cid
    public static final String FIELD_SSP_SUPPORT_TEAM_CID = "ssp_support_team";

    // 网络服务部审批节点中，字段“是否需要升级到SSP支持组”实体cid
    public static final String FIELD_IS_SSP_SUPPORT_TEAM_NET_CID = "is_ssp_support_team_net";

    // 网络服务部审批节点中，字段“SSP支持组”实体cid
    public static final String FIELD_SSP_SUPPORT_TEAM_NET_CID = "ssp_support_team_net";

    // 故障管理任务催办cid
    public static final String FAULT_RECTIFY_ASSIGNMENT_PRESS_BUTTON_CID = "press_button";

    // 故障管理任务--客户满意度责任人列表cid
    public static final String SATISFACTION_RESPONSIBLE_PERSON_TABLE_CID = "TablePc_uq4hbvbo";

    // ===================== 权限申请 ======================

    /** 代表处审核子表单 表单布局cid */
    public static final String ORGANIZATION_APPROVAER_CID = "organization_approver_cid";

    /** 直管领导 表单布局cid */
    public static final String DIRECT_MANAGEMENT_LEADER_CID = "direct_management_leader_cid";

    /** 代表处群经理 表单布局cid */
    public static final String REPRESENTATIVE_OFFICE_GROUP_MANAGER_CID = "representative_office_group_manager_cid";

    /** 产品技术科长 表单布局cid */
    public static final String PRODUCT_TECHNOLOGY_SECTION_CHIEF_CID = "product_technology_section_chief_cid";

    /** 任务主题 表单布局cid */
    public static final String BILL_NAME_CID = "bill_name_cid";

    /** 单据类型 实体PROPERTY_KEY */
    public static final String BILL_TYPE_PROPERTY_KEY = "bill_type";

    /** 角色名称 实体PROPERTY_KEY */
    public static final String ROLE_NAME_PROPERTY_KEY = "role_name";

    /** 任务主题 实体PROPERTY_KEY */
    public static final String BILL_NAME_PROPERTY_KEY = "bill_name";

    /** 代表处审核子表单 实体PROPERTY_KEY */
    public static final String ORGANIZATION_APPROVAER_PROPERTY_KEY = "organization_approver";

    /** 代表处审核子表单--代表处 实体PROPERTY_KEY */
    public static final String ORGANIZATION_PROPERTY_KEY = "organization";

    /** 代表处审核子表单--审核人 布局cid */
    public static final String APPROVER_CID = "approver_cid";

    /** 代表处审核子表单--审核人 实体PROPERTY_KEY */
    public static final String APPROVER_PROPERTY_KEY = "approver";

    /** 代表处HZS 实体PROPERTY_KEY */
    public static final String ORGANIZATION_HZS_PROPERTY_KEY = "organization_hzs";

    /** 直管领导 实体PROPERTY_KEY */
    public static final String DIRECT_MANAGEMENT_LEADER_PROPERTY_KEY = "direct_management_leader";

    /** 代表处群经理 实体PROPERTY_KEY */
    public static final String REPRESENTATIVE_OFFICE_GROUP_MANAGER_PROPERTY_KEY = "representative_office_group_manager";

    /** 产品技术科长 实体PROPERTY_KEY */
    public static final String PRODUCT_TECHNOLOGY_SECTION_CHIEF_PROPERTY_KEY = "product_technology_section_chief";

    /** 树形服务对象--长编号 实体PROPERTY_KEY */
    public static final String LONG_NUMBER_PROPERTY_KEY = "longnumber";

    /** 树形服务对象--编号 实体PROPERTY_KEY */
    public static final String NUMBER_PROPERTY_KEY = "number";

    /** 类型 实体PROPERTY_KEY */
    public static final String TYPE_PROPERTY_KEY = "type";

    /** 跨合作单位权限 布局cid */
    public static final String CROSS_PARTNER_PERMISSION_CID = "cross_partner_permission_cid";

    /** 中智&高租 布局cid */
    public static final String ZHONG_ZHI_HIGH_LEASE_CID = "zhong_zhi_high_lease_cid";

    /** 权限申请列表页 表格布局cid */
    public static final String PERMISSION_APPLICATION_TABLE = "permission_application_table";

    /** 代表处 布局cid */
    public static final String ORGANIZATIONS_CID = "organizations_cid";

    /** 代表处 布局cid */
    public static final String APPLICATION_TIME_CID = "application_time_cid";

    // ===================== 核心网配置 ======================

    /** 逻辑网元 表单布局cid */
    public static final String LOGICAL_NE_CID = "selectfield_logical_ne_cid";

    /** 逻辑网元 实体PROPERTY_KEY */
    public static final String LOGICAL_NE_PROPERTY_KEY = "selectfield_logical_ne";

    /** 母版物料名 实体PROPERTY_KEY */
    public static final String BASICDATAFIELD_MASTER_MATERIAL = "basicdatafield_master_material";
    public static final String BASICDATAFIELD_MASTER_MATERIAL_CID = "basicdatafield_master_material_cid";

    /** PCB版本 实体PROPERTY_KEY */
    public static final String TEXTFIELD_PCB_VERSION = "textfield_pcb_version";
    public static final String TEXTFIELD_PCB_VERSION_CID = "textfield_pcb_version_cid";

    /** 集成关联产品 表格布局cid */
    public static final String INTEGRATE_ASSOCIATED_PRODUCT_TABLE_CID = "integrate_associated_product_table";

    /** 产品总工 表格布局cid */
    public static final String NETWORK_CHIEF_ENGINEER_TABLE = "network_chief_engineer_table";

    /**
     * 邮件内容（申请客户授权凭证）实体cid
     */
    public static final String FIELD_APPLY_AUTHORIZATION_EMAIL_CONTENT_CID = "apply_authorization_email_content";

    /**
     * 计划操作开始时间（申请客户授权凭证页面）
     */
    public static final String FIELD_PLAN_OPERATION_START_TIME_APPLY_CID = "operation_start_time_apply";

    /**
     * 计划操作结束时间（申请客户授权凭证页面）
     */
    public static final String FIELD_PLAN_OPERATION_END_TIME_APPLY_CID = "operation_end_time_apply";

    /**
     * 操作账号（申请客户授权凭证页面）
     */
    public static final String FIELD_BATCH_OPERATION_ACCOUNT_APPLY_CID = "batch_operation_account_apply";

    /**
     * 计划操作开始时间
     */
    public static final String FIELD_PLAN_OPERATION_START_TIME_CID = "plan_operation_start_time";

    /**
     * 计划操作结束时间
     */
    public static final String FIELD_PLAN_OPERATION_END_TIME_CID = "plan_operation_end_time";

    /**
     * 操作账号
     */
    public static final String FIELD_BATCH_OPERATION_ACCOUNT_CID = "batch_operation_account";

    /**
     * 是否内部抄送邮件实体cid
     */
    public static final String FIELD_IS_INNER_EMAIL_CC_CID = "is_inner_email_cc";

    /**
     * 抄送人（内部员工）实体cid
     */
    public static final String FIELD_APPLY_AUTHORIZATION_EMAIL_INNER_CID =  "apply_authorization_email_inner";

    /**
     * 邮件发送人实体cid
     */
    public static final String FIELD_APPLY_AUTHORIZATION_EMAIL_RECEIVER_CID = "apply_authorization_email_receiver";

    /**
     * 邮件抄送人实体cid
     */
    public static final String FIELD_APPLY_AUTHORIZATION_EMAIL_CC_CID = "apply_authorization_email_cc";

    /**
     * 邮件主题实体cid
     */
    public static final String FIELD_APPLY_AUTHORIZATION_EMAIL_SUBJECT_CID = "apply_authorization_email_subject";


    /** oVDC/NFVI项目且需要研发安排专项集成验证 表单布局cid */
    public static final String OVDC_NFVI_NE_CID = "radiofield_ovdc_nfvi_rd_verify_cid";

    /** oVDC/NFVI项目且需要研发安排专项集成验证 实体PROPERTY_KEY */
    public static final String OVDC_NFVI_PROPERTY_KEY = "radiofield_ovdc_nfvi_rd_verify";

    /** 变更单 集成关联产品列表 实体PROPERTY_KEY */
    public static final String INTEGRATION_RELATED_PRODUCT_PROPERTY_KEY = "integration_related_product";

    /** 变更单 集成关联产品列表-网元版本 实体PROPERTY_KEY */
    public static final String NE_PRODUCT_PROPERTY_KEY = "ne_product";

    /**
     * 申请客户授权凭证模版（中英文模版）
     */
    public static final String FIELD_EMAIL_TEMPLATE_TYPE_CID = "email_template_type";

    /**
     * 是否有保存操作
     */
    public static final String FIELD_HAS_SAVE_OPERATION_CID = "has_save_operation";

    /**
     * 是否有取消操作
     */
    public static final String FIELD_HAS_CANCEL_OPERATION_CID = "has_cancel_operation";

    public static final String SECURITY_REINFORCEMENT_CID = "security_reinforcement_operation_object_cid";

    public static final String SECURITY_OFFICE_NAME_CID = "security_office_name_cid";

    public static final String SECURITY_OFFICE_NAME_PROPERTY_KEY = "security_office_name";

    /**
     * CCN默认授权文件申请_局点名称布局cid
     */
    public static final String COMPONENT_OFFICE_NAME_CID = "c_office_name";

    /**
     * CCN默认授权文件申请_局点查询自定义页面_表格
     */
    public static final String CCN_SITE_QUERY_TABLE_CID = "ccn_site_query_table";

    /**
     * CCN默认授权文件申请_局点查询自定义页面_查询条件_产品小类名称
     */
    public static final String CCN_SITE_QUERY_PRODUCE_NAME_CID = "product_name";

    /**
     * CCN默认授权文件申请_局点查询自定义页面_查询条件_产品小类ID
     */
    public static final String CCN_SITE_QUERY_PRODUCE_ID_CID = "product_id";

    /**
     * CCN默认授权文件申请_局点查询自定义页面_查询条件_客户网络名称
     */
    public static final String CCN_SITE_QUERY_CUSTOMER_NETWORK_NAME_CID = "customer_network_name";

    /**
     * CCN默认授权文件申请_局点查询自定义页面_查询条件_网络名称
     */
    public static final String CCN_SITE_QUERY_NETWORK_NAME_CID = "network_name";

    /**
     * CCN默认授权文件申请_局点查询自定义页面_查询条件_网络ID
     */
    public static final String CCN_SITE_QUERY_NETWORK_ID_CID = "network_id";

    /**
     * CCN默认授权文件申请_局点查询自定义页面_查询条件_客户网元名称
     */
    public static final String CCN_SITE_QUERY_CUSTOMER_SITE_NAME_CID = "site_name";

    public static final String SECURITY_REINFORCEMENT_PROPERTY_KEY = "security_reinforcement_operation_object";

    /** 变更单 涉及V4操作硬件 表单布局cid */
    public static final String INVOLVE_V4_OPERATION_HARDWARE_CID = "involve_v4_operation_hardware_cid";
    public static final String INVOLVE_V4_OPERATION_HARDWARE = "involve_v4_operation_hardware";

    /**
     * V4单板信息_母版物料名弹窗-列表-列表表格 cid
     */
    public static final String V4_BOARD_INFO_MASTER_MATERIAL_TABLE_CID = "TablePc_uq4hbvbo";

    /** 变更单 V4操作硬件 */
    public static final String V4_OPERATION_HARDWARE_INFO = "v4_operation_hardware_info";

    /**
     * 申请CCN授权文件，责任单位实体cid
     */
    public static final String FIELD_APPLY_CCN_AUTHORIZATION_ORGANIZATION_CID = "organization_id";

    /**
     * 申请CCN授权文件，产品分类实体cid
     */
    public static final String FIELD_APPLY_CCN_AUTHORIZATION_PRODUCT_CID = "product_id";

    /**
     * 申请CCN授权文件，客户网络实体cid
     */
    public static final String FIELD_APPLY_CCN_AUTHORIZATION_CUSTOMER_NETWORK_NAME_CID = "customer_network_name";

    /**
     * 抄送人_实体cid
     */
    public static final String FIELD_EMAIL_CC_CID = "email_cc";

    /**
     * 单据编号_实体cid
     */
    public static final String FIELD_AD_NO_CID = "ad_no";

    /**
     * 申请默认授权文件_申请人布局cid
     *
     */
    public static final String COMPONENT_APPLY_BY_CID = "c_apply_by";

    /**
     * 申请默认授权文件_申请人实体cid
     *
     */
    public static final String FIELD_APPLY_BY_CID = "apply_by";

    /**
     * 申请默认授权文件_单据编号布局cid
     */
    public static final String COMPONENT_AD_NO_CID = "c_ad_no";

    /**
     * 申请默认授权文件_代表处布局cid
     */
    public static final String COMPONENT_ORGANIZATION_ID_CID = "c_organization_id";

    /**
     * 申请默认授权文件_产品分类布局cid
     */
    public static final String  COMPONENT_PRODUCT_ID_CID = "c_product_id";

    /**
     * 授权中心 - 网络编码布局cid
     */
    public static final String COMPONENT_ACCREDIT_NETWORK_CODE_CID = "component_network_code";

    /**
     * 授权中心 - 网络名称布局cid
     */
    public static final String COMPONENT_ACCREDIT_NETWORK_NAME_CID = "component_network_name";

    /**
     * 授权中心 - 客户网络名称布局cid
     */
    public static final String COMPONENT_ACCREDIT_CUSTOMER_NETWORK_NAME_CID = "component_customer_network_name";

    /**
     * 授权中心 - 国家/地区布局cid
     */
    public static final String COMPONENT_COUNTRY_CID = "c_country";

    /**
     * 授权中心 - 省/州布局cid
     */
    public static final String COMPONENT_PROVINCE_CID = "c_province";

    /**
     * 授权中心 - 客户标识布局cid
     */
    public static final String COMPONENT_ACCN_TYPE_CID = "c_accn_type";

    /**
     * 授权中心 - 申请说明布局cid
     */
    public static final String COMPONENT_APPLY_DESC_CID = "c_apply_desc";

    /**
     * 授权中心 - 操作账号布局cid
     */
    public static final String COMPONENT_OPERATION_ACCOUNT_CID = "c_operation_account";

    /**
     * 授权中心 - 标题图文展示布局cid
     */
    public static final String COMPONENT_RICHTEXT_TITLE_CID = "RichText_Title";

    /**
     * 授权中心 - 反馈授权情况高级容器布局cid
     */
    public static final String COMPONENT_FEEDBACK_AUTHORIZATION_STATUS_AD_CID = "feedback_authorization_status_ad";

    /**
     * 授权中心 - 默认授权文件布局cid
     */
    public static final String COMPONENT_DEFAULT_AUTHORIZATION_FILE_CID = "c_default_authorization_file";

    /**
     * 授权中心 - 默认授权文件实体cid
     */
    public static final String FILE_DEFAULT_AUTHORIZATION_FILE_CID = "default_authorization_file";

    /**
     * 授权中心 - 上传举证布局cid
     */
    public static final String COMPONENT_UPLOAD_DEFAULT_AUTHORIZATION_FILE_CID = "c_upload_default_authorization_file";

    /**
     * 授权中心 - 重新生成布局cid
     */
    public static final String COMPONENT_REGENERATE_BUTTON_CID = "regenerate_button";

    /**
     * 授权中心 - 备注说明布局cid
     */
    public static final String COMPONENT_REMARK_CID = "c_remark";

    /**
     * CCN默认授权文件申请_知会人布局cid
     */
    public static final String COMPONENT_NOTIFIER_CID = "c_notifier";

    /**
     * CCN默认授权文件申请_抄送人布局cid
     */
    public static final String COMPONENT_EMAIL_CID = "c_email_cc";

    /**
     * CCN默认授权文件申请_按钮布局容器cid1
     */
    public static final String COMPONENT_BUTTON1_CID = "ColumnsLayout_buttons1";

    /**
     * CCN默认授权文件申请_按钮布局容器cid2
     */
    public static final String COMPONENT_BUTTON2_CID = "ColumnsLayout_buttons2";

    /**
     * 行政-网络处主管经理 - 只读
     */
    public static final String APPROVE_RESULT_ADMIN_NET_DEPT_MNG_V_CID = "approve_result_admin_net_dept_mng_v_cid";

    /**
     * 行政-网络处副经理审核 - 只读
     */
    public static final String APPROVE_RESULT_ADMIN_REP_DEPUTY_MANAGER_V_CID = "approve_result_admin_rep_deputy_manager_v_cid";

    /**
     * 行政-网络处副经理审核 - 审批
     */
    public static final String APPROVE_RESULT_ADMIN_REP_DEPUTY_MANAGER_CID = "approve_result_admin_rep_deputy_manager_cid";

    /**
     * 行政-网络处主管经理 - 审批
     */
    public static final String APPROVE_RESULT_ADMIN_NET_DEPT_MNG_CID = "approve_result_admin_net_dept_mng_cid";

    /**
     * 行政审核_网络服务处总监结果cid
     */
    public static final String APPROVE_RESULT_ADMIN_NET_PROD_DIRECTOR_CID = "approve_result_admin_net_prod_director";

    public static final String FILED_AD_STATUS_CID = "ad_status";


    public static final String TABLE_CLOCK = "table_clock";

    /**
     * 自定义打卡单查询页面 - 查询条件 - 操作主题 / 单号 - 布局cid
     */
    public static final String OPERATION_SUBJECT_OR_CODE_CID = "operation_subject_or_code_cid";

    /**
     * 自定义打卡单查询页面 - 查询条件 - 操作时间/打卡时间 - 布局cid
     */
    public static final String OPERATION_TIME_CID = "operation_time_cid";

    /**
     * 自定义打卡单查询页面 - 查询条件 - 操作负责人/打卡人 - 布局cid
     */
    public static final String OPERATING_SUPERVISOR_CID = "operating_supervisor";

    /**
     * 自定义打卡单查询页面 - 查询条件 - 打卡状态/打卡项 - 布局cid
     */
    public static final String CLOCK_STATUS_CID = "clock_status";



    // ==============================================待办任务================================================
    /**
     * 待我处理自定义页面--高级查询--布局容器cid
     */
    public static final String COLUMNS_LAYOUT_ADVANCED_QUERY_CID = "ColumnsLayout_advanced_query";

    /**
     * 待我处理自定义页面--高级查询--任务主题cid
     */
    public static final String TEXT_FIELD_TASK_SUBJECT_CID = "TextField_task_subject";

    /**
     * 待我处理自定义页面--高级查询--单据编码cid
     */
    public static final String TEXT_FIELD_BILL_CODE_CID = "TextField_bill_code";

    /**
     * 待我处理自定义页面--高级查询--任务类型cid
     */
    public static final String MULTI_SELECT_FIELD_TASK_TYPE_CID = "MultiSelectField_task_type";

    /**
     * 待我处理自定义页面--高级查询--审批任务cid
     */
    public static final String SELECT_FIELD_IS_APPROVE_TASK_CID = "SelectField_is_approve_task";

    /**
     * 待我处理自定义页面--高级查询--产品分类cid
     */
    public static final String ITECHCLOUD_CUSTOM_TREE_SELECT_PRODUCT_CID = "iTechCloudCustomTreeSelect_product";

    /**
     * 待我处理自定义页面--高级查询--代表处cid
     */
    public static final String ITECHCLOUD_CUSTOM_TREE_SELECT_ORGANIZATION_CID = "iTechCloudCustomTreeSelect_organization";

    /**
     * 产品经营团队
     */
    public static final String FIELD_PRODUCT_TEAM_CID = "product_team";

    /**
     * 产品线
     */
    public static final String FIELD_PRODUCT_LINE_CID = "product_line";

    /**
     * 变更单 电源规格型 表单布局cid
     */
    public static final String POWER_SPECIFICATION_MODEL_CID = "power_specification_model_cid";

    /**
     * 电源规格型号(ZXDU68 T601) 单据实体标识
     */
    public static final String ENTRYENTITY_POWER_SPECIFICATION_MODEL = "entryentity_power_specification_model";

    /**
     * 电源规格型号(ZXDU68 T601) 子表单唯一标识cid
     */
    public static final String POWER_SPECIFICATION_MODEL_OBJECT_CID = "power_specification_model_object_cid";

    /**
     * 电源规格型号子表单字段 - 物料名称下拉单选cid
     */
    public static final String MATERIAL_NAME_SELECT_CID = "material_name_select_cid";

    /**
     * 电源规格型号子表单字段 - 物料名称下拉单选实体cid
     */
    public static final String MATERIAL_NAME_SELECT_ENTITY_CID = "material_name";

    /**
     * 电源规格型号子表单字段 - 硬件版本下拉单选实体cid
     */
    public static final String HARDWARE_VERSION_SELECT_ENTITY_CID = "hardware_version";

    /**
     * 电源规格型号子表单字段 - 硬件版本下拉单选cid
     */
    public static final String HARDWARE_VERSION_SELECT_CID = "hardware_version_select_cid";

    /**
     * 操作开始时间 - 日期区间cid
     */
    public static final String OPERATION_TIME_RANGE_CID = "operation_time_range_cid";

    /**
     * 操作场景业务对象编码
     */
    public static final String OPERATE_SCENE_BIZ_OBJECT_CODE = "operate_scene_list";

    /**
     * 附件类型照片cid
     */
    public static final String ATTACHMENTFIELD_PHOTOS_CID = "attachmentField_photos_cid";

    /**
     * 网络变更单_数据来源实体cid
     */
    public static final String FIELD_SOURCE_CID = "source";

    /**
     * 批次 - 已挂起按钮cid
     */
    public static final String BUTTON_SUSPENDED_CID = "suspended";

    /**
     * 批次 - 撤销按钮
     */
    public static final String BUTTON_BATCH_REVOKE_CID = "revoke";

    /**
     * 批次 - 取消按钮cid
     */
    public static final String BUTTON_CANCEL_CID = "cancel";

    /**
     * 批次 - 变更发布通告按钮cid
     */
    public static final String BUTTON_CHANGE_CID = "change";

    /**
     * 批次 - 反馈操作结果 -变更发布通告按钮cid
     */
    public static final String BUTTON_RESULT_CHANGE_CID = "result_change";

    /**
     * 批次 - 下载授权文件按钮cid
     */
    public static final String BUTTON_DOWNLOAD_GRANT_FILE_CID = "download_grant_file";


    /**
     * 批次 - 当前节点图文cid
     */
    public static final String RICHTEXT_CURRENT_NODE_CID = "RichText_Current_Node";

    /**
     * 批次 - 当前处理人图文cid
     */
    public static final String RICHTEXT_CURRENT_REVIEWER_CID = "RichText_Current_Reviewer";

    /**
     * 批次 - 当前节点与当前处理人
     */
    public static final String CURRENT_NODE_AND_CURRENT_REVIEWER_CID = "Current_Node_and_Current_Reviewer";

    /**
     * 批次 - 操作主题 页面组件cid
     */
    public static final String BATCH_TASK_OPERATION_SUBJECT = "batch_name";

    /**
     * 批次 - 操作打卡记录高级容器布局cid
     */
    public static final String ADVANCED_CONTAINER_OPERATION_CLOCK_IN_RECORD_CID = "AdvancedContainer_operation_clock_in_record_cid";

    /**
     * 批次 - 批次取消操作弹框自定义页面ID
     */
    public static final String BATCH_CANCEL_OPERATION_POPUP_PAGE_ID = "batch_cancel_operation_popup";

    /**
     * 操作变更说明
     */
    public static final String OC_OPERATION_CHANGE_DESC_CID = "oc_operation_change_desc";

    /**
     * 批次 - 审核结果cid（行政_电信服务总监）
     */
    public static final String APPROVE_RESULT_ADMIN_DIR_TELE_SER_DIRECTOR_CID = "approve_result_admin_dir_tele_ser_director";

    /**
     * 批次 - 审核意见cid（行政_电信服务总监）
     */
    public static final String APPROVE_OPINION_ADMIN_DIR_TELE_SER_DIRECTOR_CID = "approve_opinion_admin_dir_tele_ser_director";

    /**
     * 批次 - 审核人cid（行政_电信服务总监）
     */
    public static final String APPROVED_BY_ADMIN_DIR_TELE_SER_DIRECTOR_CID = "approved_by_admin_dir_tele_ser_director";

    /**
     * 批次 - 审核时间cid（行政_电信服务总监）
     */
    public static final String APPROVED_TIME_ADMIN_DIR_TELE_SER_DIRECTOR_CID = "approved_time_admin_dir_tele_ser_director";

    /**
     * 穿梭框
     */
    public static final String SHUTTLE_BOX_CID = "shuttle_box";

    /**
     * 数据范围
     */
    public static final String DATA_RANGE = "data_range";

    /**
     * 发版通告 - 提示语组件cid
     */
    public static final String SHOW_ERROR_MSG_TIPS_CID = "show_error_msg";

    /**
     * 敏感操作
     */
    public static final String SENSITIVE_OPERATION = "sensitive_operation";

    /**
     * 是否直网操作
     */
    public static final String GRP_DRCT_MNG = "grp_drct_mng";

    /**
     * 敏感区域
     */
    public static final String SENSITIVE_AREAS = "sensitive_areas";

    /**
     * 提交时间
     */
    public static final String SUBMIT_TIME = "submit_time";

    /**
     * 操作计划批次概要子表单 propertykey
     */
    public static final String PLAN_BATCH_SUMMARY = "plan_batch_summary";

    /**
     * 操作计划对象子表单 propertykey
     */
    public static final String PLAN_OPERATION_OBJECT = "plan_operation_object";

    /**
     * 操作计划操作人员子表单 propertykey
     */
    public static final String PLAN_OPERATOR_TABLE = "plan_operator_table";

    /**
     * 冲突检查结果
     */
    public static final String TIME_CONFLICT = "time_conflict";

    /**
     * 操作对象计划开始时间
     */
    public static final String PLAN_OPERATION_START_TIME_OBJ = "plan_operation_start_time_obj";

    /**
     * 操作对象计划结束时间
     */
    public static final String PLAN_OPERATION_END_TIME_OBJ = "plan_operation_end_time_obj";

    /** 添加操作对象（自定义页面），产品设备cid */
    public static final String ADD_OPERATION_OBJECT_PRODUCT_DEVICE_CID = "product_device";

    /** 添加操作对象（自定义页面），客户网络名称cid */
    public static final String ADD_OPERATION_OBJECT_CUSTOMER_NETWORK_NAME_CID = "customer_network_name";

    /** 添加操作对象（自定义页面），客户网络id cid */
    public static final String ADD_OPERATION_OBJECT_NETWORK_ID_CID = "network_id";

    /** 添加操作对象（自定义页面），网络id cid */
    public static final String ADD_OPERATION_OBJECT_CUSTOMER_ID_CID = "customer_id";

    /** 添加操作对象（自定义页面），客户标识cid */
    public static final String ADD_OPERATION_OBJECT_ACCN_TYPE_CID = "accn_type";

    /** 添加操作对象（自定义页面），国家/地区cid */
    public static final String ADD_OPERATION_OBJECT_COUNTRY_CID = "country";

    /** 添加操作对象（自定义页面），省/州cid */
    public static final String ADD_OPERATION_OBJECT_PROVINCE_CID = "province";

    /** 添加操作对象（自定义页面），地市cid */
    public static final String ADD_OPERATION_OBJECT_AREA_CID = "area";

    /** 添加操作对象（自定义页面） - 子表单cid */
    public static final String ADD_OPERATION_OBJECT_TABLE_NETWORK_PRODUCT_CID = "network_product";

    /** 添加操作对象（自定义页面） - 子表单外层高级容器cid */
    public static final String ADD_OPERATION_OBJECT_TABLE_NETWORK_PRODUCT_ADVCONTAINER_CID = "network_product_cid";

    /** 操作对象布局cid */
    public static final String OPERATION_OBJECT_PROD_SUB_CATEGORY_CID = "prod_sub_category";

    /** 操作对象实体cid  - 数量 */
    public static final String ENTITY_OPERATION_OBJECT_COUNT_CID = "count";

    /** 操作对象实体cid  - 数据中文 */
    public static final String ENTITY_OPERATION_OBJECT_DATA_CENTER_CID = "data_center";

    /**
     * 运维界面相关
     */
    public static class Maintenance {

        /** 缓存 Key - 选项 */
        public static final String CACHE_KEY_OPTION = "cache_key_option";

        /** 缓存 Key - 文本 */
        public static final String CACHE_KEY_TEXT = "cache_key_text";

        /** 缓存结果 */
        public static final String CACHE_RESULT = "cache_result";
    }

    /**
     * 系统名称
     */
    public static class SystemName {

        public static final String I_VERSION = "IVersion";

        public static final String CSC = "CSC";
    }
}
