package com.zte.iccp.itech.extension.common.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @since 2024/10/12
 */
public class JsonTextSerializer<T> extends JsonSerializer<T> {
    @Override
    @SneakyThrows
    public void serialize(T value, JsonGenerator gen, SerializerProvider serializers) {
        gen.writeString(JsonUtils.toJsonString(value));
    }
}
