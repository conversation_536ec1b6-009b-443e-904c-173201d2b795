package com.zte.iccp.itech.extension.ability.reportpush;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.enums.RiskEvaluationEnum;
import com.zte.iccp.itech.extension.openapi.model.reportpush.ClockInOrgTotalVO;
import com.zte.iccp.itech.extension.openapi.model.reportpush.ClockInProdDetailVO;
import com.zte.iccp.itech.extension.openapi.model.reportpush.ClockInProdTotalVO;
import com.zte.iccp.itech.extension.spi.client.IcosClient;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.NET_SERVICE_SALES;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @date 2025/4/23 上午9:18
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class ClockInProdPushAbility {

    private static final String TITLE = "netchange.report.push.clock.title";
    private static final String ORG_TITLE = "netchange.report.push.clock.org.title";
    private static final String TABLE_TITLE = "netchange.report.push.clock.table.title";
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String LOCATION = "emailtemplate/reportclockinprodpush.txt";
    private static final String DATA = "${DATA}";
    private static final String TD_START = "<td>";
    private static final String TD_END = "</td>";
    private static final String TR_START = "<tr>";
    private static final String TR_END = "</tr>";
    private static final String CLOCK_IN_QUERY_URL = "clockin.query.url";


    public static void clockInOrgPush() {
        List<ClockInOrgTotalVO> totalVos = IcosClient.getClockInOrgTotalVos();

        if (CollectionUtils.isEmpty(totalVos)) {
            log.info("{} clockInOrgPush total is 0", new Date());
            return;
        }

        List<ClockInProdDetailVO> detailVos = IcosClient.getReportClockInDetailVos();
        Map<String, List<ClockInProdDetailVO>> orgId2DetailVosMap = detailVos.stream()
                .collect(Collectors.groupingBy(ClockInProdDetailVO::getOrganizationIdPath));

        String todayStr = DateUtils.dateToString(new Date(), DATE_FORMAT);
        String tableTitle = ReportPushAbility.getTableTitle(
                MsgUtils.getLangMessage(ZH_CN, TABLE_TITLE, todayStr));

        String url = ConfigHelper.get(CLOCK_IN_QUERY_URL);
        totalVos.forEach(vo -> {
            List<ClockInProdDetailVO> details = orgId2DetailVosMap.get(vo.getIdPath());
            if (CollectionUtils.isEmpty(details)) {
                return;
            }
            details = details.stream()
                    .sorted(Comparator.comparing(ClockInProdDetailVO::getProdTeamId, Comparator.nullsFirst(Comparator.naturalOrder()))
                            .thenComparing(ClockInProdDetailVO::getCoNo, Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());
            String mailTitle = MsgUtils.getLangMessage(ZH_CN, ORG_TITLE,
                    todayStr, vo.getRepresentativeOffice(), vo.getX1(), vo.getX2(), vo.getX3(), vo.getX4(), vo.getX5());

            List<String> empNos = ReportPushAbility.getEmpNos(vo.getIdPath(), null);

            ReportPushAbility.sendMessage(mailTitle, empNos);
            ReportPushAbility.sendMail(mailTitle, getMailInfo(details, tableTitle), url, empNos);
        });
    }


    public static void clockInProdPush() {
        List<ClockInProdTotalVO> totalVos = IcosClient.getReportClockInTotalVos();

        if (CollectionUtils.isEmpty(totalVos)) {
            log.info("{} clockInProdPush total is 0", new Date());
            return;
        }

        List<ClockInProdDetailVO> detailVos = IcosClient.getReportClockInDetailVos();
        Map<String, List<ClockInProdDetailVO>> prodId2DetailVosMap = detailVos.stream()
                .collect(Collectors.groupingBy(ClockInProdDetailVO::getProdLineId));

        String todayStr = DateUtils.dateToString(new Date(), DATE_FORMAT);
        String tableTitle = ReportPushAbility.getTableTitle(
                MsgUtils.getLangMessage(ZH_CN, TABLE_TITLE, todayStr));

        String url = ConfigHelper.get(CLOCK_IN_QUERY_URL);
        totalVos.forEach(vo -> {
            List<ClockInProdDetailVO> details = prodId2DetailVosMap.get(vo.getProdLineId());
            if (CollectionUtils.isEmpty(details)) {
                return;
            }
            details = details.stream()
                    .sorted(Comparator.comparing(ClockInProdDetailVO::getOrganizationIdPath, Comparator.nullsFirst(Comparator.naturalOrder()))
                            .thenComparing(ClockInProdDetailVO::getCoNo, Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());
            String mailTitle = MsgUtils.getLangMessage(ZH_CN, TITLE,
                    todayStr, vo.getProdLine(), vo.getX1(), vo.getX2(), vo.getX3(), vo.getX4(), vo.getX5());

            String prodLineId = details.stream().filter(item -> null != item.getProdClassIdPath())
                    .map(item -> ProductUtils.getLine(item.getProdClassIdPath())).findFirst().orElse(EMPTY_STRING);
            List<String> empNos = ReportPushAbility.getEmpNosByPordLine(NET_SERVICE_SALES, Lists.newArrayList(prodLineId));

            ReportPushAbility.sendMessage(mailTitle, empNos);
            ReportPushAbility.sendMail(mailTitle, getMailInfo(details, tableTitle), url, empNos);
        });


    }

    private static String getMailInfo(List<ClockInProdDetailVO> details, String tableTitle) {
        StringBuilder lineBuilder = new StringBuilder();
        for (int i = 0; i < details.size(); i++) {
            ClockInProdDetailVO detailVo = details.get(i);
            lineBuilder.append(TR_START)
                    .append(TD_START).append(i + 1).append(TD_END)
                    .append(TD_START).append(detailVo.getCoNo()).append(TD_END)
                    .append(TD_START).append(detailVo.getOperationSubject()).append(TD_END)
                    .append(TD_START).append(detailVo.getCustomerNetworkName()).append(TD_END)
                    .append(TD_START).append(detailVo.getProdTeam()).append(TD_END)
                    .append(TD_START).append(RiskEvaluationEnum.getStarStr(detailVo.getFxdj())).append(TD_END)
                    .append(TD_START).append(ResponsibleUtils.getResponsibleName(detailVo.getOrganizationPath())).append(TD_END)
                    .append(TD_START).append(detailVo.getOpTypeCn()).append(TD_END)
                    .append(TD_START).append(handleString(detailVo.getOpEmpCombine())).append(TD_END)
                    .append(TD_START).append(handleString(detailVo.getCheckStatusZh())).append(TD_END)
                    .append(TD_START).append(detailVo.getCheckDetailZh()).append(TD_END)
                    .append(TR_END);
        }
        StringBuilder mailInfo = new StringBuilder();
        Map<String, String> replacements = MapUtils.newHashMap(DATA, lineBuilder.toString());
        mailInfo.append(tableTitle).append(StringBuilderUtils.replaceAll(CommonUtils.getTemplateString(LOCATION), replacements));
        return mailInfo.toString();
    }

    private static String handleString(String str) {
        return null == str ? EMPTY_STRING : str;
    }
}
