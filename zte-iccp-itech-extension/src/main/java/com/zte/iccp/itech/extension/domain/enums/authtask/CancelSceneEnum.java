package com.zte.iccp.itech.extension.domain.enums.authtask;

import com.google.common.collect.Sets;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Set;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.BUTTON_RESULT_CHANGE_CID;

/**
 * <AUTHOR>
 * @since 2024/08/20
 */
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum CancelSceneEnum {
    /** 变更发布通告 */
    CHANGE("2", Sets.newHashSet("batch_commit", BUTTON_RESULT_CHANGE_CID)),
    /** 挂起待重发通告 */
    SUSPEND("3", Sets.newHashSet("suspend", "task_suspend")),
    /** 取消变更操作 */
    ABOLISH("4", Sets.newHashSet("confirm", "task_cancel")),
    ;

    @Getter
    private final String code;

    private final Set<String> operationKeys;

    public static CancelSceneEnum fromOperationKey(String operationKey) {
        for (CancelSceneEnum value : values()) {
            if (value.operationKeys.contains(operationKey)) {
                return value;
            }
        }
        return null;
    }
}
