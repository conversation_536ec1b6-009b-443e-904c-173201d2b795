package com.zte.iccp.itech.extension.domain.enums.export;

import lombok.Getter;

/**
 * 数据范围枚举
 */
@Getter
public enum DataRangeEnum {

    /**
     * 内部网络变更单
     */
    NETWORK_CHANGE("backlog.title.networkChange"),

    /**
     * 分包商变更单
     */
    SUBCONTRACTOR_NETWORK_CHANGE("backlog.title.partnerNetworkChange");

    /**
     * 国际化消息键
     */
    private final String messageKey;

    DataRangeEnum(String messageKey) {
        this.messageKey = messageKey;
    }
}
