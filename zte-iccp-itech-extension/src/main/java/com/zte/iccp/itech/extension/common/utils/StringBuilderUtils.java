package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;

/**
 * <AUTHOR>
 * @date 2025/4/23 上午11:05
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StringBuilderUtils {

    public static String replaceAll(String template, Map<String, String> replacements) {
        StringBuilder sb = new StringBuilder(template);

        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            String target = entry.getKey();
            String replacement = entry.getValue() == null ? EMPTY_STRING : entry.getValue();

            int index;
            while ((index = sb.indexOf(target)) != -1) {
                sb.replace(index, index + target.length(), replacement);
            }
        }

        return sb.toString();
    }
}
