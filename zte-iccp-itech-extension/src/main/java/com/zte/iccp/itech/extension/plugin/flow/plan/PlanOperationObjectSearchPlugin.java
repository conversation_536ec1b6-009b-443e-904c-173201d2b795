package com.zte.iccp.itech.extension.plugin.flow.plan;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.vo.TableDisplayRows;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.ProductModel;
import com.zte.iccp.itech.extension.spi.model.nis.ProductModelVo;
import com.zte.iccp.itech.extension.spi.model.query.NisNetworkQuery;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.core.event.LoadDataEvent;
import com.zte.paas.lcap.core.event.PropertyChangedEvent;
import com.zte.paas.lcap.core.event.SearchEvent;
import com.zte.paas.lcap.ddm.common.api.event.RowSelectEvent;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.BATCH_NO;
import static com.zte.iccp.itech.extension.domain.constant.entity.NetworkConfigurationFieldConsts.NETWORK_ID;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.DATA;

/**
 * 操作计划 - 添加操作对象搜索
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/7/25
 */
public class PlanOperationObjectSearchPlugin extends BaseFormPlugin {

    /** 低代码选中行关键字 */
    private static final String SELECT_ROW_DATAS = "selectRowDatas";

    /** 添加操作对象 子表单已选中行 */
    private static final String SUB_FORM_SELECTED_ROWS = "networkProductList";

    @Override
    public void afterLoadData(LoadDataEvent e) {
        Map<String, Object> customerParmeterMap = getView().getFormShowParameter().getCustomParameters();
        List<BatchSummary> batchSummaryList = JsonUtils.parseArray(
                customerParmeterMap.get(CidConstants.COMPONENT_OPERATION_OBJECT_BATCH_SUMMARY_CID), BatchSummary.class);
        OptionsBuilder optionsBuilder = new OptionsBuilder();
        for (BatchSummary batchSummary : batchSummaryList) {
            TextValuePair batchNo = batchSummary.getBatchNo().get(0);
            optionsBuilder.addOption(
                    new Option(batchNo.getValue(), new Text(batchNo.getText().getZhCN(), batchNo.getText().getEnUS())));
        }
        // 批次概要默认选项赋值
        getView().getClientViewProxy().setOptions(BATCH_NO, optionsBuilder.build());

        // 客户网络名称必填设置
        getView().getClientViewProxy().setControlState(CidConstants.ADD_OPERATION_OBJECT_CUSTOMER_NETWORK_NAME_CID,
                new BasicAttributeBuilder().attribute(REQUIRED, true).build());
    }

    @Override
    public void afterSearch(SearchEvent searchEvent) {
        Object networkId = getModel().getValue(NETWORK_ID);
        if (ObjectUtils.isEmpty(networkId)) {
            return;
        }

        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();
        TableDisplayRows<ProductModelVo> tableDisplayRows = new TableDisplayRows<>();
        TablePc tableInfo = (TablePc) getView().getControl(CidConstants.ADD_OPERATION_OBJECT_TABLE_NETWORK_PRODUCT_CID);
        nisNetworkQuery.setPageNum(tableInfo.getCurrentPage());
        nisNetworkQuery.setPageSize(tableInfo.getPageSize());
        nisNetworkQuery.setNetworkId((String) networkId);

        PageRows<ProductModel> productModelPageRows = NisClient.queryProdModels(nisNetworkQuery);
        if (!CollectionUtils.isEmpty(productModelPageRows.getRows())) {
            List<ProductModelVo> productModelVos = Lists.newArrayList();
            List<ProductModel> productModelList = productModelPageRows.getRows();
            for (ProductModel productModel : productModelList) {
                ProductModelVo productModelVo = new ProductModelVo();
                BeanUtils.copyProperties(productModel, productModelVo);
                // 产品数量
                productModelVo.setTriCount(Integer.toString(productModel.getCount().getTotal()));
                productModelVos.add(productModelVo);
            }
            tableDisplayRows.setRecords(productModelVos);
        }
        tableDisplayRows.setTotal(productModelPageRows.getTotal());
        tableDisplayRows.setCurrent(productModelPageRows.getCurrent());

        // 页面数据展示
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(DATA, JSON.parseObject(JSON.toJSONString(tableDisplayRows)));

        Map<String, Object> viewMap = new HashMap<>();
        viewMap.put(CidConstants.ADD_OPERATION_OBJECT_TABLE_NETWORK_PRODUCT_CID, dataMap);

        getView().getClientViewProxy().setProps(viewMap);
    }

    @Override
    public void propertyChanged(PropertyChangedEvent event) {
        ChangeData changeData = event.getChangeSet()[CommonConstants.INTEGER_ZERO];
        String propertyKey = changeData.getProperty().getKey();
        if (CidConstants.ADD_OPERATION_OBJECT_PRODUCT_DEVICE_CID.equals(propertyKey)) {
            String productDevice = TextValuePairHelper.getValue(getModel().getValue(CidConstants.ADD_OPERATION_OBJECT_PRODUCT_DEVICE_CID));
            // 从网络已注册资产中选择: 展示子表单，隐藏产品型号
            if (productDevice.equals(STR_ONE)) {
                getView().getClientViewProxy().setControlState(CidConstants.ADD_OPERATION_OBJECT_TABLE_NETWORK_PRODUCT_ADVCONTAINER_CID,
                        new PageStatusAttributeBuilder().normal().build());
                getView().getClientViewProxy().setControlState(CidConstants.COMPONENT_PRODUCT_TREE_CID,
                        new PageStatusAttributeBuilder().hidden().build());
            } else {
                // 网络资产中未找到我需要的设备：展示产品型号，隐藏子表单
                getView().getClientViewProxy().setControlState(CidConstants.ADD_OPERATION_OBJECT_TABLE_NETWORK_PRODUCT_ADVCONTAINER_CID,
                        new PageStatusAttributeBuilder().hidden().build());
                getView().getClientViewProxy().setControlState(CidConstants.COMPONENT_PRODUCT_TREE_CID,
                        new PageStatusAttributeBuilder().normal().build());
            }
        }
    }

    @Override
    public void afterRowSelect(RowSelectEvent rowSelectEvent) {
        // 添加操作对象已选数据集
        Object selectNetworkInfo = rowSelectEvent.getArgs().get(SELECT_ROW_DATAS);
        getView().getFormShowParameter().setCustomParameter(SUB_FORM_SELECTED_ROWS, selectNetworkInfo);
    }
}
