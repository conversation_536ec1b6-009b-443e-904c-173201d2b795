package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 操作人员属性枚举
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OperatorAttributeEnum implements SingletonTextValuePairsProvider {

    /** 本地中方 */
    LOCAL_CHINESE("a", "本地中方", "Local Chinese"),
    /** 本地外籍 */
    LOCAL_FOREIGNER("b", "本地外籍", "Local Foreign"),
    /** 网服/产研 */
    NET_SRV_PROD_DEV("c", "其他(网服/产研)", "Other(Network Service Dept./RD)"),
    /** 技术交付部/网络处 */
    TECH_DELIV_NET_OFFICE("d", "技术交付部/网络处", "Technology Delivery Dept."),
    /** 未知 */
    UNKNOWN("x", "未知(未获取到人员属性)", "Unknown (no personnel attribute obtained)"),
    ;

    /**
     * 操作人员角色值
     */
    private final String value;

    /**
     * 操作人员角色中文描述
     */
    private final String zhCn;

    /**
     * 操作人员角色英文描述
     */
    private final String enUs;

    public static OperatorAttributeEnum fromValue(String value) {
        for (OperatorAttributeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new IllegalArgumentException();
    }
}
