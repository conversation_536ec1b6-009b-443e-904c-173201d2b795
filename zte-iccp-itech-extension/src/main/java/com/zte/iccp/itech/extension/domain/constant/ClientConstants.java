package com.zte.iccp.itech.extension.domain.constant;

/**
 * 外部接口常量
 */
public class ClientConstants {
    // =========== 请求方式常量 ============
    /**
     * POST
     */
    public static final String POST = "POST";

    /**
     * GET
     */
    public static final String GET = "GET";

    /**
     * PUT
     */
    public static final String PUT = "PUT";

    /**
     * DELETE
     */
    public static final String DELETE = "DELETE";

    // ============= 版本常量 ==============
    /**
     * 默认版本 - V1
     */
    public static final String DEFAULT_VERSION = "v1";

    // ============ 请求头常量 =============
    /**
     * X-Auth-Value
     */
    public static final String X_AUTH_VALUE = "X-Auth-Value";

    /**
     * X-Itp-Value
     */
    public static final String X_ITP_VALUE = "X-Itp-Value";

    /**
     * X-Lang-Id
     */
    public static final String X_LANG_ID = "X-Lang-Id";

    /**
     * X-Emp-No
     */
    public static final String X_EMP_NO = "X-Emp-No";

    /**
     * X-Tenant-Id
     */
    public static final String X_TENANT_ID = "X-Tenant-Id";
    /**
     * X-Origin-ServiceName
     */
    public static final String X_ORIGIN_SERVICENAME = "X-Origin-ServiceName";

    /**
     * X-System-Name
     */
    public static final String X_SYSTEM_NAME = "X-System-Name";

    /**
     * X-System-Code
     */
    public static final String X_SYSTEM_CODE = "X-System-Code";

    /**
     * X-Org-Id
     */
    public static final String X_ORG_ID = "X-Org-Id";
    /**
     * X-Timestamp
     */
    public static final String X_TIMESTAMP = "X-Timestamp";

    /**
     * X-Operator-Id
     */
    public static final String X_OPERATOR_ID = "X-Operator-Id";

    /**
     * X-App-Id
     */
    public static final String X_APP_ID = "X-App-Id";

    /**
     * X-Access-Key
     */
    public static final String X_ACCESS_KEY = "X-Access-Key";

    /**
     * X-Uuid
     */
    public static final String X_UUID = "X-Uuid";

    /**
     * X-Client-Id
     */
    public static final String X_CLIENT_ID = "X-Client-Id";

    /** 微服务名称 */
    public static final String X_MS_NAME = "X-MS-Name";

    /** 请求key，接口服务方提 */
    public static final String X_SECRET_KEY = "X-Secret-Key";

    public static final String X_DEVICE = "X-DEVICE";

    /**
     * 报文类型
     */
    public static final String CONTENT_TYPE = "Content-Type";

    /**
     * 报文类型值，固定
     */
    public static final String APPLICATION_JSON = "application/json;charset=UTF-8";

    /**
     * 4A组件ID
     */
    public static final String APP_CODE = "AppCode";

    // ============= 默认参数 ==============
    /**
     * 系统用户
     */
    public static final String SYSTEM_USER = "system";

    /**
     * 租户ID
     */
    public static final String DEFAULT_TENANT_ID = "10001";

    /**
     * rows
     */
    public static final String ROWS = "rows";

    /**
     * bo
     */
    public static final String BO = "bo";

    /**
     * 其它参数
     */
    public static final String OTHER = "other";

    /**
     * 成功
     */
    public static final String SUCCESS = "Success";

    /**
     * 失败
     */
    public static final String FAILED = "Failed";

    // ============= 特殊参数 ==============
    // ============== HR系统 ==============
    /**
     * 员工信息 - ID 类型 - 股份12位电脑号
     */
    public static final String HR_PERSON_ID_TYPE_COMPUTER_NO = "T0001";

    /**
     * 员工信息 - ID 类型 - 股份8位工号, 子公司/合作方公司10位工号
     */
    public static final String HR_PERSON_ID_TYPE_SHORT_NO = "T0002";

    /**
     * 员工信息 - ID 类型 - 股份14位工号
     */
    public static final String HR_PERSON_ID_TYPE_WHOLE_NO = "T0003";

    /**
     * 员工信息 - 信息块 - ID信息
     */
    public static final String HR_PERSON_INFO_BLOCK_ID = "B0000";

    /**
     * 员工信息 - 信息块 - 个人信息
     */
    public static final String HR_PERSON_INFO_BLOCK_PERSON = "B0001";

    /**
     * 员工信息 - 信息块 - 人事基本信息
     */
    public static final String HR_PERSON_INFO_BLOCK_PERSONNEL = "B0002";

    /**
     * 员工信息 - 信息块 - 组织信息
     */
    public static final String HR_PERSON_INFO_BLOCK_ORGANIZATION = "B0003";

    /**
     * 员工信息 - 信息块 - 岗位信息
     */
    public static final String HR_PERSON_INFO_BLOCK_POST = "B0004";

    /**
     * 员工信息 - 信息块 - 标签数据（是否脱敏、是否加密）
     */
    public static final String HR_PERSON_INFO_BLOCK_TAG = "B0010";

    /**
     * 员工信息 - 信息块 - 证件信息
     */
    public static final String HR_PERSON_INFO_BLOCK_CERTIFICATE = "B0012";

    /**
     * 员工信息 - 信息块 - 联系方式
     */
    public static final String HR_PERSON_INFO_BLOCK_CONTACT = "B0013";

    /**
     * 员工信息 - 信息块 - 员工属性
     */
    public static final String HR_PERSON_INFO_BLOCK_ATTRIBUTE = "B0014";

    /**
     * 员工信息 - 返回结果 - 公司类型 - 股份公司(中文)
     */
    public static final String HR_PERSON_COMPANY_TYPE_INCORPORATED_ZH = "股份公司";

    /**
     * 员工信息 - 返回结果 - 公司类型 - 股份公司(英文)
     */
    public static final String HR_PERSON_COMPANY_TYPE_INCORPORATED_EN = "Incorporated Company";

    /**
     * 员工信息 - 返回结果 - 公司类型 - 合作方公司(中文)
     */
    public static final String HR_PERSON_COMPANY_TYPE_PARTNER_ZH = "合作方公司";

    /**
     * 员工信息 - 返回结果 - 公司类型 - 合作方公司(英文)
     */
    public static final String HR_PERSON_COMPANY_TYPE_PARTNER_EN = "Partner Company";

    /**
     * 组织信息 - ID 类型 - 股份组织
     */
    public static final String HR_ORGANIZATION_ID_TYPE_STOCK_COMPANY = "T0001";

    /**
     * 组织信息 - ID 类型 - 子公司
     */
    public static final String HR_ORGANIZATION_ID_TYPE_SUBSIDIARY = "T0002";

    /**
     * 组织信息 - ID 类型 - 合作方公司
     */
    public static final String HR_ORGANIZATION_ID_TYPE_PARTNER_COMPANY = "T0003";

    /**
     * 查询类型
     */
    public static final String DEFAULT_QUERY_TYPE = "Q0001";

    // ============== UCS系统 ==============
    /**
     * 用户类型 - 外部用户
     */
    public static final String UCS_USER_TYPE_EXTERNAL = "0";

    /**
     * 用户类型 - 内部用户
     */
    public static final String UCS_USER_TYPE_INTERIOR = "1";

    // ============== CSC系统 ==============
    /**
     * 服务请求ID
     */
    public static final String CSC_REQUEST_ID = "requestId";

    /**
     * 服务请求单号
     */
    public static final String CSC_REQUEST_NO = "requestNo";

    /**
     * 拓展信息类型
     */
    public static final String CSC_EXT_INFO_TYPES = "extInfoTypes";

    /**
     * 拓展信息类型 - 请求方信息
     */
    public static final String CSC_INFO_TYPE_REQUESTER_INFO = "REQUESTER_INFO";

    /**
     * 拓展信息类型 - 项目信息
     */
    public static final String CSC_INFO_TYPE_PROJECT_INFO = "PROJECT_INFO";

    /**
     * 拓展信息类型 - 设备信息
     */
    public static final String CSC_INFO_TYPE_DEVICE_INFO = "DEVICE_INFO";

    /**
     * 拓展信息类型 - 分类定级
     */
    public static final String CSC_INFO_TYPE_CLASSIFICATION_INFO = "CLASSIFICATION_INFO";

    /**
     * 拓展信息类型 - 支持组&受理组&工程师信息
     */
    public static final String CSC_INFO_TYPE_SUPPORT_ORG_INFO = "SUPPORT_ORG_INFO";

    /**
     * 拓展信息类型 - 附件信息
     */
    public static final String CSC_INFO_TYPE_ATTACH_INFO = "ATTACH_INFO";

    /**
     * 拓展信息类型 - 现场服务单信息
     */
    public static final String CSC_INFO_TYPE_ON_SITE_INFO = "ON_SITE ";

    /**
     * 故障程度 - 中文 - 关键一级
     */
    public static final String CSC_FAULT_LEVEL_CRITICAL_FIRST_CN = "关键一级";

    /**
     * 故障程度 - 中文 - 关键二级
     */
    public static final String CSC_FAULT_LEVEL_CRITICAL_SECOND_CN = "关键二级";

    /**
     * 故障程度 - 中文 - 关键三级
     */
    public static final String CSC_FAULT_LEVEL_CRITICAL_THIRD_CN = "关键三级";

    // ============ WarRoom 系统 ===========
    /**
     * WarRoomID
     */
    public static final String WAR_ROOM_ID = "warroomId";

    /**
     * 服务请求单号
     */
    public static final String TICKET_NUMBER = "ticketNumber";


    // ============= EMDM 系统 =============
    /**
     * 国家地区编码
     */
    public static final String EMDM_AREA_CODE = "CODE";

    /**
     * 国家地区中文全称
     */
    public static final String EMDM_AREA_FULL_NAME_ZH = "DESC1";

    /**
     * 国家地区类型
     */
    public static final String EMDM_AREA_TYPE = "DESC2";

    /**
     * 国家地区中文简称
     */
    public static final String EMDM_AREA_NAME_ZH = "DESC3";

    /**
     * 国家地区英文全称
     */
    public static final String EMDM_AREA_FULL_NAME_EN = "DESC4";

    /**
     * 国家地区英文简称
     */
    public static final String EMDM_AREA_NAME_EN = "DESC5";

    /**
     * 请求唯一标识
     */
    public static final String EMDM_P_UUID = "puuid";

    /**
     * 接口编码
     */
    public static final String EMDM_SYN_CODE = "syncode";

    /**
     * 认证用户名
     */
    public static final String EMDM_USER_CODE = "usercode";

    /**
     * 认证密码
     */
    public static final String EMDM_PASSWORD = "password";

    // ============== PDM系统 ==============
    /**
     * 请求参数 - 微服务名称
     */
    public static final String PDM_PARAM_MICROSERVICE_NAME = "MSName";

    /**
     * 请求参数 - 查询条件
     */
    public static final String PDM_PARAM_QUERY_CONDITION = "queryCondition";

    /**
     * 查询条件 - 类型 - 查询产品信息
     */
    public static final String PDM_CONDITION_QUERY_TYPE = "P0000";

    public static final String X_APP_CODE = "X-APP-CODE";
    public static final String X_APP_CODE_VALUE = "iTech_Cloud";
    public static final String X_APP_KEY = "X-APP-KEY";
    public static final String X_APP_KEY_VALUE = "+FeI0p48hE8eEhiGZG+dcBR9uHN5lQ088FzwolCjGHwypCwF3t15Cy+4ukA4ug==";
}
