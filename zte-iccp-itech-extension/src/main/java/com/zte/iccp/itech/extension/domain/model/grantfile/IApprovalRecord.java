package com.zte.iccp.itech.extension.domain.model.grantfile;

import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;

/**
 * 会签节点通用接口类
 *
 * <AUTHOR> jiangji<PERSON>en
 * @date 2025/5/14
 */
public interface IApprovalRecord {

    SingleEmployee getApprover();

    ApproveResultEnum getResult();

    String getOpinion();
}
