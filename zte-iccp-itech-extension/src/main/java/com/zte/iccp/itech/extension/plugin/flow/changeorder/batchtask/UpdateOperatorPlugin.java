package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.OperatorAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

@Slf4j
public class UpdateOperatorPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        // 1、根据业务id查询批次任务
        String businessId = body.getBusinessId();
        BatchTask batchTask = BatchTaskAbility.get(businessId, new ArrayList<>());
        if (batchTask == null) {
            log.info("根据审批中心businessId{}，查询批次任务为空", businessId);
            return false;
        }

        // 2、查询批次任务的操作人员
        Set<String> operatorUserIds = new HashSet<>();
        List<BatchTaskOperator> operators = OperatorAbility.getBatchOperators(batchTask.getId(), BatchTaskOperator.class);
        for (BatchTaskOperator operator : operators) {
            SingleEmployee employee = operator.getOperatePerson();
            if (employee == null) {
                continue;
            }
            operatorUserIds.add(employee.getEmpUIID());
        }

        // 3、将批次任务的操作人员添加到网络变更单
        String changeOrderId = batchTask.getChangeOrderId();

        Assignment assignment = AssignmentAbility.queryAssignment(changeOrderId, Lists.newArrayList(ID), Assignment.class);
        if (assignment == null) {
            log.info("根据changeOrderId{}查询对应的Assignment为空", changeOrderId);
            return false;
        }
        AssignmentAbility.createAssignmentPersonRelevance(assignment.getId(), Lists.newArrayList(operatorUserIds));

        return false;
    }
}