package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.ApprovalAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.clockin.reviews.ClockInReviewsAbility;
import com.zte.iccp.itech.extension.ability.configuration.EmailGroupAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.EmailConsts;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInReviewsFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInReviewsNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdDevIntApproval;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdNetServIntApproval;
import com.zte.iccp.itech.extension.domain.model.subentity.support.*;
import com.zte.iccp.itech.extension.plugin.flow.faultmanagement.SendEmailHelper;
import com.zte.iccp.itech.extension.spi.client.EmailClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.ICenterClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import com.zte.paas.lcap.flow.dto.FlowInfo;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.LookupValueConstant.ASSIGNMENT_STATUS_ENUM;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ASSIGNMENT_CODE;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.BATCH_NO;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.MANAGER;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.OFFICE_SOLUTION_REVIEWER;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.IS_EMERGENCY_OPERATION;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.OPERATION_SUBJECT;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInReviewsFieldConsts.*;

/**
 * 邮件中间层
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/8/7
 */
@Slf4j
public class EmailAbility {

    /**
     * 发送审批邮件睡眠时长
     */
    private static final int ASYNC_WAIT_TIME = 5000;

    /**
     * 变更单查询通用字段
     */
    private static final List<String> CHANGE_ORDER_FIELDS = Lists.newArrayList(
            ID, ChangeOrderFieldConsts.ORDER_NO, ChangeOrderFieldConsts.RESPONSIBLE_DEPT,
            PRODUCT_CATEGORY, OPERATION_SUBJECT, ChangeOrderFieldConsts.OPERATION_LEVEL,
            IS_GOV_ENT, IS_EMERGENCY_OPERATION, IS_AFFECT_TO_B, IS_SPECIAL_SCENARIO, CREATE_BY);

    /**
     * 合作方变更单查询通用字段
     */
    private static final List<String> SUBCONTRACTOR_CHANGE_ORDER_FIELDS = Lists.newArrayList(
            ID, SubcontractorChangeOrderFieldConsts.ORDER_NO, ORGANIZATION_ID,
            SubcontractorChangeOrderFieldConsts.PRODUCT_ID, OPERATION_SUBJECT,
            SubcontractorChangeOrderFieldConsts.OPERATION_LEVEL,
            SubcontractorChangeOrderFieldConsts.IS_EMERGENCY_OPERATION,
            IS_GOV_ENT, CREATE_BY);

    /**
     * 异步邮件发送
     *
     * @param emailParam builder
     */
    public static void asyncSendMail(EmailParam emailParam) {
        AsyncExecuteUtils.execute(() -> {
            preTitleProcess(emailParam);
            // 审批页面
            if (emailParam.isApprovePage()) {
                sendApproveEmail(emailParam);
                //详情页面
            }else {
                sendDetailEmail(emailParam);
            }
        });
    }


    /**
     * 变更单和合作方变更单标题进行了定制化，标题和正文是分开的
     * 前置处理定制化标题
     *
     * @param emailParam builder
     */
    private static void preTitleProcess(EmailParam emailParam) {
        // NotifyTypeEnum标题类型枚举不为空则进行标题统一管理。仅适用于需要进行标题替换的模板，如变更单、合作方变更单、批次任务、合作方批次任务
        if (emailParam.getAssignmentTypeEnum() == null || emailParam.getNotifyTypeEnum() == null) {
            return;
        }

        Map<String, Object> data = emailParam.getData();
        data.putAll(MessageTitleEnum.buildMessageTitle(emailParam.getAssignmentTypeEnum(), emailParam.getNotifyTypeEnum()));
    }

    /**
     * 发送任务中心列表详情知会邮件
     * @param emailParam builder
     */
    private static void sendDetailEmail(EmailParam emailParam) {
        DetailPageEnum detailPageEnum = Objects.requireNonNull(DetailPageEnum.getPageIdByType(emailParam.getAssignmentTypeEnum()));
        String pageId = detailPageEnum.getPageId();

        String hrefUrl = String.format(ConfigHelper.get(HREF_IFRAME_URL), ZH_CN.equals(ContextHelper.getLangId()) ? ZH : EN,
                emailParam.getPkId(), PageStatusEnum.VIEW.name(), ContextHelper.getAppId(), pageId);
        Map<String, Object> data = emailParam.getData();
        data.put(EmailConsts.EMAIL_SERVICE_URL, hrefUrl);

        EmailClient.sendMail(emailParam.getTemplateIdEnum(), emailParam.getMailTos(), emailParam.getMailCCs(), data);

        // 需要同步进行iCenter消息发送
        if (emailParam.getICenterTemplateIdEnum() != null) {
            // icenter消息没有抄送人概念，把收件人和抄送人放一起
            List<String> mailTos = emailParam.getMailTos();
            if (!CollectionUtils.isEmpty(emailParam.getMailCCs())) {
                mailTos.addAll(emailParam.getMailCCs());
            }
            ICenterClient.sendICenter(emailParam.getICenterTemplateIdEnum(), mailTos, data);
        }
    }

    /**
     * 发送审批催办邮件
     * 1.新增逻辑，如果是转交的，获取转交人，发送催办邮件（不重新获取当前处理人）
     *
     * @param emailParam builder
     */
    @SneakyThrows
    private static void sendApproveEmail(EmailParam emailParam) {
        // 1.解决配置在流程节点中当前配置的插件提前发送未获取到处理人问题
        Thread.sleep(ASYNC_WAIT_TIME);

        // 2.查询流程节点审批人、流程实例id、流程任务id
        FlowHandler flowHandler = beforeQueryFlowHandler(emailParam);
        if (flowHandler == null) {
            return;
        }

        if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH == emailParam.getAssignmentTypeEnum() ||
                AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH == emailParam.getAssignmentTypeEnum()) {
            batchApprovalEmailProcess(emailParam, flowHandler.getApproveTaskList());
            return;
        }

        // 3.获取待办跳转页面，其中收件人取审批处理人
        ApprovePageEnum approvePageEnum = getAssignmentApprovePage(emailParam.getPkId(), emailParam.getAssignmentTypeEnum());
        // 兼容审批优化对应新pageID
        String approveNodePageId = ApproveNodeEnum.getApproveNodePageId(emailParam.getNodeCode());
        String pageId = StringUtils.isNotEmpty(approveNodePageId) ? approveNodePageId : approvePageEnum.getPageId();

        Map<String, ApproveTask> approverMap = getApproverMap(flowHandler.getApproveTaskList(), emailParam);
        if (CollectionUtils.isEmpty(approverMap)) {
            return;
        }
        String tabName = emailParam.getTabName();
        if(StringUtils.isEmpty(tabName)){
            Assignment assignment = AssignmentAbility.queryAssignment(
                    emailParam.getPkId(), Lists.newArrayList(ID, ASSIGNMENT_CODE), Assignment.class);
            tabName = assignment.getAssignmentCode();
        }

        for (Map.Entry<String, ApproveTask> entry : approverMap.entrySet()) {
            ApproveTask approveTask = entry.getValue();

            Map<String, Object> data = emailParam.getData();
            String approvalUrl = SendEmailHelper.getApprovalUrl(
                    tabName, flowHandler.getFlowInstanceId(),
                    approveTask.getTaskId(), pageId);
            data.put(EmailConsts.EMAIL_SERVICE_URL, approvalUrl);
            EmailClient.sendMail(emailParam.getTemplateIdEnum(), approveTask.getApprover(), data);
            // 发送iCenter信息 等同邮件逻辑，待办邮件每次只给当前处理人发
            if (emailParam.getICenterTemplateIdEnum() != null) {
                ICenterClient.sendICenter(emailParam.getICenterTemplateIdEnum(), approveTask.getApprover(), data);
            }
        }
    }

    /**
     * 审批人处理
     * 1.如果为转交处理人，转交人固定
     * 2.如果为普通催办，获取处理人集
     *
     * @param approveTaskList approveTaskList
     * @param emailParam emailParam
     * @return Map<String, ApproveTask>
     */
    public static Map<String, ApproveTask> getApproverMap(List<ApproveTask> approveTaskList, EmailParam emailParam) {
        if (!emailParam.isTransfer()) {
            // 直接转换为Map
            return approveTaskList.stream()
                    .collect(Collectors.toMap(ApproveTask::getApprover, approveTask -> approveTask, (o1, o2) -> o1));
        }

        // 获取第一个匹配的ApproveTask
        Optional<ApproveTask> matchingTask = approveTaskList.stream()
                .filter(task -> task.getApprover().equals(emailParam.getTransferor()))
                .findFirst();

        // 如果没有匹配到
        if (!matchingTask.isPresent()) {
            List<String> approverList = approveTaskList.stream().map(ApproveTask::getApprover).collect(Collectors.toList());
            log.info("邮件发送失败，当前处理人集为：{}，没有找到transferor命中的处理人：{}", approverList, emailParam.getTransferor());
            return null;
        }

        // 创建一个包含单个元素的Map
        return MapUtils.newHashMap(matchingTask.get().getApprover(), matchingTask.get());
    }

    /**
     * 审批数据前置查询
     *
     * @param emailParam emailParam
     * @return FlowHandler
     */
    public static FlowHandler beforeQueryFlowHandler(EmailParam emailParam) {
        List<FlowHandler> flowHandlers = FlowHelper.getFlowHandlerByFlowEntityIds(Lists.newArrayList(emailParam.getPkId()));
        if (CollectionUtils.isEmpty(flowHandlers)
                || CollectionUtils.isEmpty(flowHandlers.get(INTEGER_ZERO).getApproveTaskList())) {
            log.warn("【{}】：获取审批人失败，未发送邮件，data信息为：{}", emailParam.getPkId(), JSONObject.toJSONString(emailParam.getData()));
            return null;
        }

        return flowHandlers.get(INTEGER_ZERO);
    }

    /**
     * 批次审批催办邮件处理
     *
     * @param emailParam emailParam
     * @param approveTaskList approveTaskList
     */
    public static void batchApprovalEmailProcess(EmailParam emailParam, List<ApproveTask> approveTaskList) {
        List<String> mailTos = approveTaskList.stream()
                .map(ApproveTask::getApprover)
                .filter(StringUtils::isNotEmpty).distinct()
                .collect(Collectors.toList());

        // 催办转交
        if (emailParam.isTransfer()) {
            mailTos.clear();
            mailTos.add(emailParam.getTransferor());
        }
        // 批次当前节点处理人统一放收件人里
        emailParam.setMailTos(mailTos);
        // 邮件正文是催办，实际URL还是详情邮件逻辑
        sendDetailEmail(emailParam);
    }


    /**
     * 获取审批页面pageId
     *
     * @param pkId 主键id，如为技术管理子任务，为子任务审批表id
     * @param assignmentTypeEnum 任务类型枚举
     * @return ApprovePageEnum
     */
    private static ApprovePageEnum getAssignmentApprovePage(String pkId, AssignmentTypeEnum assignmentTypeEnum) {
        AssignmentStatusEnum assignmentStatus;
        switch (assignmentTypeEnum) {
            case TECHNOLOGY_MANAGEMENT_SUB:
                ManageSubTaskFlow subTaskFlow = ManageTaskAbility.queryBySubFlowId(pkId);
                assignmentStatus = AssignmentStatusEnum.fromValue(subTaskFlow.getStatus());
                break;
            case NETWORK_CHANGE:
            case SUBCONTRACTOR_NETWORK_CHANGE:
            case CLOCK_REVIEW:
                return ApprovePageEnum.getPageIdByType(assignmentTypeEnum);
            case NETWORK_CHANGE_BATCH:
            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                //批次任务和分包商批次任务状态取batch_network_assignment批次表状态
                Class<? extends BaseEntity> batchTaskClass = AssignmentTypeEnum.NETWORK_CHANGE_BATCH == assignmentTypeEnum
                        ? BatchTask.class : SubcontractorBatchTask.class;
                IBatchTask batchTask = (IBatchTask) BatchTaskAbility.queryBatchAssignment(pkId, batchTaskClass);
                assignmentStatus =  AssignmentStatusEnum.fromValue(Objects.requireNonNull(batchTask).getCurrentStatus());
                break;
            default:
                //技术管理任务
                Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(pkId, assignmentTypeEnum, Assignment.class);
                assignmentStatus = AssignmentStatusEnum.fromValue(Objects.requireNonNull(assignment).getAssignmentStatus());
                break;
        }

        // 根据任务状态和任务类型匹配到要跳转的page页面
        return ApprovePageEnum.getPageIdByTypeStatus(assignmentTypeEnum, assignmentStatus);
    }

    /**
     * 发送取消/挂起邮件  -批次任务
     *
     * @param batchId 批任务id
     * @param notifyTypeEnum    通知类型枚举：取消/挂起
     * @param batchTaskClass  批次任务/分包商批次任务区分标识：BATCH_TASK为批次任务，SUBCONTRACT_BATCH_TASK为分包商批次任务
     */
    public static void sendCancelSuspendedEmail(String batchId, NotifyTypeEnum notifyTypeEnum, Class<? extends BaseEntity> batchTaskClass) {
        if (SubcontractorBatchTask.class != batchTaskClass && BatchTask.class != batchTaskClass) {
            return;
        }

        //普通批次任务
        Class<? extends BaseSubEntity> batchOperatorClass = BatchTaskOperator.class;
        Class<? extends BaseEntity> changeOrderClass = ChangeOrder.class;

        List<String> changeOrderFields = new ArrayList<>(CHANGE_ORDER_FIELDS);
        changeOrderFields.add(OFFICE_SOLUTION_REVIEWER);
        changeOrderFields.add(MANAGER);
        //分包商批次任务
        if (batchTaskClass == SubcontractorBatchTask.class) {
            batchOperatorClass = SubcontractorBatchOperator.class;
            changeOrderClass = SubcontractorChangeOrder.class;
            changeOrderFields = SUBCONTRACTOR_CHANGE_ORDER_FIELDS;
        }

        //1.主送人，批次通告内容中操作人员列表内的所有人员
        List<String> mailTos = OperatorAbility.getBatchOperatorPersonsList(batchId, batchOperatorClass);

        //2.抄送人
        List<String> batchFields = Lists.newArrayList(ID, BatchTaskFieldConsts.CHANGE_ORDER_ID,
                BATCH_NO, BatchTaskFieldConsts.EMAIL_CC, BatchTaskFieldConsts.OC_EMAIL_CC, BatchTaskFieldConsts.BATCH_CODE, SOURCE);
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.queryOne(
                batchTaskClass, batchFields, Lists.newArrayList(new Filter(ID, Comparator.EQ, batchId)));
        assert batchTask != null;

        //2.1单据所有节点审核人
        List<String> mailCCs = FlowHelper.getApprovedRecords(batchId)
                .stream().map(ApproveRecord::getApprover).collect(Collectors.toList());

        //2.3本环节维护的抄送人员（待发通告环节的email_cc和取消操作审核环节的oc_email_cc）
        List<Employee> employeeList = Stream.of(
                        batchTask.getEmailCc(),
                        batchTask.getOcEmailCc())
                //过滤掉为null的集合数据
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        mailCCs.addAll(EmployeeHelper.getEpmUIID(employeeList));


        //2.4申请单中选择的代表处方案审核人（办事处PD人）、主管经理/副经理和提单人
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                changeOrderClass, changeOrderFields, batchTask.getChangeOrderId());
        if (changeOrder == null) {
            return;
        }
        mailCCs.add(changeOrder.getCreateBy());
        mailCCs.addAll(ChangeOrderAbility.getChangeOrderPersonList(changeOrder));
        // 主单据实际节点审核人
        mailCCs.addAll(FlowHelper.getAllRecordApprovers(changeOrder.getId()));

        //3.发送邮件
        OperationLevelEnum operationLevel = changeOrder.getOperationLevel();
        // 操作等级
        Map<String, Object> data = MapUtils.newHashMap(EmailConsts.MESSAGE_OPERATE_LEVEL_ZH,operationLevel.getMsg(ZH_CN),
                EmailConsts.MESSAGE_OPERATE_LEVEL_EN,operationLevel.getMsg(EN_US));
        // 内容中文（双语）
        data.putAll(EmailAbility.buildMessageContent(MessageConsts.EmailNotice.MESSAGE_BATCH_CANCEL_SUSPENDED_CONTENT,
                batchTask.getBatchCode(), changeOrder.getOperationSubject(), batchTask.getBatchNo()));

        // 【${操作等级}网络变更操作已取消/挂起】{单据编号}/{操作主题}（批次{批次号}）
        EmailAbility.asyncSendMail(EmailParam.builder()
                .pkId(batchId)
                .templateIdEnum(TemplateIdEnum.EMAIL_NOTIFY_1)
                .iCenterTemplateIdEnum(TemplateIdEnum.ICENTER_PUB_NOTIFY)
                .notifyTypeEnum(notifyTypeEnum)
                .data(data)
                .mailTos(mailTos)
                .mailCCs(mailCCs)
                .isApprovePage(false)
                .source(batchTask.getSource())
                .assignmentTypeEnum(batchTaskClass == SubcontractorBatchTask.class ? AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH :
                        AssignmentTypeEnum.NETWORK_CHANGE_BATCH)
                .build());
    }


    /**
     * 发送主任务关闭邮件
     *
     * @param batchId 批次id
     * @param assignmentTypeEnum 任务类型枚举
     */
    public static void sendMainTaskCloseEmail(String batchId, AssignmentTypeEnum assignmentTypeEnum) {
        if (AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH != assignmentTypeEnum
                && AssignmentTypeEnum.NETWORK_CHANGE_BATCH != assignmentTypeEnum) {
            return;
        }

        //普通批次任务
        List<String> changeOrderFields = new ArrayList<>(CHANGE_ORDER_FIELDS);
        changeOrderFields.add(OFFICE_SOLUTION_REVIEWER);
        changeOrderFields.add(MANAGER);

        Class<? extends BaseEntity> batchTaskClass = BatchTask.class;
        Class<? extends BaseSubEntity> batchOperatorClass = BatchTaskOperator.class;
        Class<? extends BaseEntity> changeOrderClass = ChangeOrder.class;
        //分包商批次任务
        if (AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH == assignmentTypeEnum) {
            batchTaskClass = SubcontractorBatchTask.class;
            batchOperatorClass = SubcontractorBatchOperator.class;
            changeOrderClass = SubcontractorChangeOrder.class;
            changeOrderFields = SUBCONTRACTOR_CHANGE_ORDER_FIELDS;
        }
        //1.获取变更单id
        List<String> fieldList = Lists.newArrayList(ID, BatchTaskFieldConsts.CHANGE_ORDER_ID);
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.queryOne(
                batchTaskClass, fieldList, Lists.newArrayList(new Filter(ID, Comparator.EQ, batchId)));

        //2.获取全部批次任务id
        List<IFilter> conditionFilters = Lists.newArrayList(
                new Filter(BatchTaskFieldConsts.CHANGE_ORDER_ID, Comparator.EQ,
                        Objects.requireNonNull(batchTask).getChangeOrderId()));
        List<? extends BaseEntity> batchTaskList = QueryDataHelper.query(batchTaskClass, Lists.newArrayList(ID), conditionFilters);

        //3.所有批次的操作人员列表内的所有人员
        List<String> mailTos = new ArrayList<>();
        for (BaseEntity batchTaskEntity : batchTaskList) {
            List<String> batchOperatorPersonsList = OperatorAbility.getBatchOperatorPersonsList(batchTaskEntity.getId(), batchOperatorClass);
            mailTos.addAll(batchOperatorPersonsList);
        }

        //4.主任务所有节点审核人
        List<ApproveRecord> approvedRecords = FlowHelper.getApprovedRecords(batchTask.getChangeOrderId());
        List<String> allApproverList = approvedRecords.stream().map(ApproveRecord::getApprover).collect(Collectors.toList());
        mailTos.addAll(allApproverList);

        //5.申请单中选择的代表处方案审核人、主管经理/副经理
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(changeOrderClass, changeOrderFields, batchTask.getChangeOrderId());
        //提单人
        mailTos.add(changeOrder.getCreateBy());
        mailTos.addAll(ChangeOrderAbility.getChangeOrderPersonList(changeOrder));

        //6.发送邮件
        Map<String, Object> data = EmailAbility.buildMessageContent(MessageConsts.EmailNotice.MESSAGE_CHANGE_MAINTASK_CLOSE_CONTENT,
                changeOrder.getOrderNo(), changeOrder.getOperationSubject());
        // 【网络变更操作已关闭】${操作主题}
        EmailAbility.asyncSendMail(EmailParam.builder()
                .pkId(batchId)
                .templateIdEnum(TemplateIdEnum.EMAIL_INFORM_2)
                .notifyTypeEnum(NotifyTypeEnum.CLOSE)
                .data(data)
                .mailTos(mailTos)
                .isApprovePage(false)
                .assignmentTypeEnum(assignmentTypeEnum)
                .build());
    }

    public static void sendDelRelevanceMailByAssignments(List<NetworkChangeAssignment> deleteRelationAssignments) {
        if (CollectionUtils.isEmpty(deleteRelationAssignments)) {
            return;
        }

        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(ContextHelper.getEmpNo());
        // 发邮件：删除关联关系
        AsyncExecuteUtils.execute(() -> {
            for (NetworkChangeAssignment assignment : deleteRelationAssignments) {
                if (CollectionUtils.isEmpty(assignment.getResponsibleEmployee())) {
                    continue;
                }

                Map<String, Object> data = EmailAbility.buildMessageZhContent(MessageConsts.EmailNotice.MESSAGE_MULTI_PROD_GUARANTEE_RELEASE_CONTENT,
                        assignment.getAssignmentName(), assignment.getAssignmentCode(), employeeFormatNames.getZhCN());
                data.putAll(EmailAbility.buildMessageEnContent(MessageConsts.EmailNotice.MESSAGE_MULTI_PROD_GUARANTEE_RELEASE_CONTENT,
                        assignment.getAssignmentName(), assignment.getAssignmentCode(), employeeFormatNames.getEnUS()));

                // 保障任务-${taskName}(任务单号：${taskCode})已由${operator}解除，请关注
                EmailAbility.asyncSendMail(EmailParam.builder()
                        .pkId(assignment.getBillId())
                        .templateIdEnum(TemplateIdEnum.EMAIL_INFOM_1)
                        .data(data)
                        .mailTos(EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee()))
                        .isApprovePage(false)
                        .assignmentTypeEnum(AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType()))
                        .build());
            }
        });
    }

    /**
     * 发邮件：删除的任务
     *
     * @param deleteAssignmentList deleteAssignmentList
     * @param flag 删除/废止
     */
    public static void sendDeleteMail(List<NetworkChangeAssignment> deleteAssignmentList,String flag) {
        if (CollectionUtils.isEmpty(deleteAssignmentList)) {
            return;
        }
        // 默认删除，否则走废止
        String msg = StringUtils.isEmpty(flag) ? MessageConsts.EmailNotice.MESSAGE_MULTI_PROD_GUARANTEE_DELETE_CONTENT :
                MessageConsts.EmailNotice.MESSAGE_MULTI_PROD_GUARANTEE_ABOLISH_CONTENT;

        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(ContextHelper.getEmpNo());
        AsyncExecuteUtils.execute(() -> {
            for (NetworkChangeAssignment assignment : deleteAssignmentList) {
                if (CollectionUtils.isEmpty(assignment.getResponsibleEmployee())) {
                    continue;
                }

                Map<String, Object> data = EmailAbility.buildMessageZhContent(msg,
                        assignment.getAssignmentName(), assignment.getAssignmentCode(), employeeFormatNames.getZhCN());
                data.putAll(EmailAbility.buildMessageEnContent(msg,
                        assignment.getAssignmentName(), assignment.getAssignmentCode(), employeeFormatNames.getEnUS()));

                //保障任务-xxxx(任务单号：xxxx)已由祝永乐10334690删除，请关注
                EmailAbility.asyncSendMail(EmailParam.builder()
                        .pkId(assignment.getBillId())
                        .templateIdEnum(TemplateIdEnum.EMAIL_INFOM_1)
                        .data(data)
                        .mailTos(EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee()))
                        .isApprovePage(false)
                        .assignmentTypeEnum(AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType()))
                        .build());
            }
        });
    }

    /**
     * 发邮件：更新实施责任人
     *
     * @param updatePersonOldAssignments updatePersonOldAssignments
     * @param updatePersonMap updatePersonMap
     */
    public static void sendUpdateEmail(List<NetworkChangeAssignment> updatePersonOldAssignments,
                                       Map<String, List<Employee>> updatePersonMap) {
        if (CollectionUtils.isEmpty(updatePersonOldAssignments)) {
            return;
        }

        String empNo = ContextHelper.getEmpNo();
        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(empNo);
        // 发邮件：更新实施责任人
        AsyncExecuteUtils.execute(() -> {
            for (NetworkChangeAssignment assignment : updatePersonOldAssignments) {
                List<Employee> responsible = updatePersonMap.get(assignment.getId());
                if (CollectionUtils.isEmpty(responsible)) {
                    continue;
                }
                // 抄送人：旧实施责任人，转交人
                List<String> mailCcs = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());
                mailCcs.add(empNo);

                Map<String, Object> data = EmailAbility.buildMessageZhContent(MessageConsts.EmailNotice.MESSAGE_MULTI_PROD_GUARANTEE_TRANSFER_CONTENT,
                        assignment.getAssignmentName(), assignment.getAssignmentCode(), employeeFormatNames.getZhCN());
                data.putAll(EmailAbility.buildMessageEnContent(MessageConsts.EmailNotice.MESSAGE_MULTI_PROD_GUARANTEE_TRANSFER_CONTENT,
                        assignment.getAssignmentName(), assignment.getAssignmentCode(), employeeFormatNames.getEnUS()));

                //保障任务-xxxx(任务单号：xxxx)的保障任务关系已由祝永乐10334690转交给您，请及时处理
                EmailAbility.asyncSendMail(EmailParam.builder()
                        .pkId(assignment.getBillId())
                        .templateIdEnum(TemplateIdEnum.EMAIL_INFOM_1)
                        .data(data)
                        .mailTos(EmployeeHelper.getEpmUIID(responsible))
                        .mailCCs(mailCcs)
                        .isApprovePage(false)
                        .assignmentTypeEnum(AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType()))
                        .build());
            }
        });
    }

    /**
     * 【定制化前缀】{批次名称}{是否紧急操作}（批次{批次号}）已进入{节点名称}环节，请您及时处理！
     * 批次范例：【网络变更操作催办】中国联通云南省分公司_4&5G_UME网管MR&CDT&MOS采集任务（紧急）（批次1）已进入通告审核环节，请您及时处理！
     *
     * @param businessId 批次id
     * @param nodeExtendName 当前节点名称
     * @param approveFlowCodeEnum 审批节点名称
     */
    private static void sendBatchPressMail(String businessId,
                                           String nodeExtendName,
                                           ApproveFlowCodeEnum approveFlowCodeEnum,
                                           boolean isTransfer,
                                           String transferor) {
        Class<? extends BaseEntity> batchTaskClass =  BatchTask.class;
        Class<? extends BaseEntity> changeOrderClass = ChangeOrder.class;
        List<String> changeFields = CHANGE_ORDER_FIELDS;
        if (ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowCodeEnum) {
            batchTaskClass = SubcontractorBatchTask.class;
            changeOrderClass = SubcontractorChangeOrder.class;
            changeFields = SUBCONTRACTOR_CHANGE_ORDER_FIELDS;
        }
        SingletonTextValuePairsProvider pairsProvider = ApprovalAbility.getApproveNodeName(
                changeOrderClass,nodeExtendName);
        if (pairsProvider == null) {
            return;
        }

        //1.查询批次任务获取批次名称和变更单id
        List<String> batchFieldList = Lists.newArrayList(ID, BATCH_NO, BatchTaskFieldConsts.CHANGE_ORDER_ID);
        List<IFilter> batchFilters = Lists.newArrayList(new Filter(ID, Comparator.EQ, businessId));
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.queryOne(batchTaskClass, batchFieldList, batchFilters);
        assert batchTask != null;
        //2.查询变更单获取操作主题、是否紧急操作
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(changeOrderClass, changeFields, batchTask.getChangeOrderId());

        //3.{单据编号}/{操作主题}{是否紧急操作}（批次{批次号}）已进入{当前节点名称}环节，请您及时处理！
        // 正文中文
        Map<String, Object> data = EmailAbility.buildMessageZhContent(MessageConsts.EmailNotice.MESSAGE_BATCH_PRESS_CONTENT,
                changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(ZH_CN),
                batchTask.getBatchNo(), pairsProvider.getZhCn());
        // 正文英文
        data.putAll(EmailAbility.buildMessageEnContent(MessageConsts.EmailNotice.MESSAGE_BATCH_PRESS_CONTENT,
                changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(EN_US),
                batchTask.getBatchNo(), pairsProvider.getEnUs()));
        EmailParam emailParam = builderEmailParam(businessId, data, approveFlowCodeEnum, true, isTransfer, transferor);
        emailParam.setNodeCode(nodeExtendName);
        EmailAbility.asyncSendMail(emailParam);
    }

    /**
     * 构建邮件正文内容双语
     *
     * @param msg msg
     * @param args args
     * @return Map<String, Object>
     */
    public static Map<String, Object> buildMessageContent(String msg, Object... args) {
        return MapUtils.newHashMap(EmailConsts.MESSAGE_INFO_ZH, MsgUtils.getLangMessage(ZH_CN, msg, args),
                EmailConsts.MESSAGE_INFO_EN, MsgUtils.getLangMessage(EN_US, msg, args));
    }

    /**
     * 构建邮件正文中文
     *
     * @param msg  msg
     * @param args args
     * @return Map<String, Object>
     */
    public static Map<String, Object> buildMessageZhContent(String msg, Object... args) {
        return MapUtils.newHashMap(EmailConsts.MESSAGE_INFO_ZH, MsgUtils.getLangMessage(ZH_CN, msg, args));
    }

    /**
     * 构建邮件正文英文
     *
     * @param msg  msg
     * @param args args
     * @return Map<String, Object>
     */
    public static Map<String, Object> buildMessageEnContent(String msg, Object... args) {
        return MapUtils.newHashMap(EmailConsts.MESSAGE_INFO_EN, MsgUtils.getLangMessage(EN_US, msg, args));
    }

    /**
     * 催办邮件通用入口
     *
     * @param businessId 实体主键id
     * @param nodeExtendName 当前节点名称
     * @param isTransfer 是否转交  （拓展参数）
     * @param transferor 转交后处理人 （拓展参数）
     */
    public static void sendPressEmail(String businessId, String nodeExtendName, boolean isTransfer, String transferor) {
        // 1.获取当前流程编码
        FlowInfo flowInfo = FlowHelper.getFlowInfo(businessId);
        if (flowInfo == null) {
            return;
        }

        // 2.获取当前审批节点名称 (在流程中能直接获取到当前节点编码，可以取值直接用，如为转交等则从流程中重新获取)
        if (StringUtils.isEmpty(nodeExtendName)) {
            // 暂不考虑催办邮件从流程获取不到节点的问题
            nodeExtendName = FlowHelper.getApprovalNodeCurrentHandlers(businessId).getExtendedCode();
        }

        // 3.根据类型发送催办邮件
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(flowInfo.getFlowCode());
        switch (approveFlowEnum) {
            case CHANGE_ORDER_COMP_FLOW:
            case SUBCONTRACTOR_OC_FLOW:
                EmailAbility.sendChangeOrderPressMail(businessId, nodeExtendName, approveFlowEnum, isTransfer, transferor);
                break;
            case BATCH_TASK_FLOW:
            case SUBCONTRACTOR_TASK_FLOW:
                EmailAbility.sendBatchPressMail(businessId, nodeExtendName, approveFlowEnum, isTransfer, transferor);
                break;
            case CLOCK_IN_REVIEW_FLOW:
                EmailAbility.sendClockInReviewPressMail(businessId, approveFlowEnum, isTransfer, transferor);
                break;
            default:
                break;
        }
    }

    /**
     * 变更单/合作方变更单催办邮件
     *
     * @param changeOrderId 变更单id
     * @param nodeExtendName 当前节点名称
     * @param approveFlowCodeEnum 流程编码对应流程信息
     * @param isTransfer 是否转交
     * @param transferor 转交人
     */
    private static void sendChangeOrderPressMail(String changeOrderId,
                                                 String nodeExtendName,
                                                 ApproveFlowCodeEnum approveFlowCodeEnum,
                                                 boolean isTransfer,
                                                 String transferor) {
        Class<? extends BaseEntity> flowEntity = approveFlowCodeEnum.getFlowEntity();
        List<String> changeOrderFields = ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW == approveFlowCodeEnum ? CHANGE_ORDER_FIELDS :
                SUBCONTRACTOR_CHANGE_ORDER_FIELDS;
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(flowEntity, changeOrderFields, changeOrderId);

        SingletonTextValuePairsProvider approveNodeName = ApprovalAbility.getApproveNodeName(
                flowEntity, nodeExtendName);
        if (approveNodeName == null) {
            return;
        }

        // 消息正文（中文）
        Map<String, Object> data = buildMessageZhContent(MessageConsts.EmailNotice.MESSAGE_CHANGE_PRESS_CONTENT,
                changeOrder.getOrderNo(),changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(ZH_CN), approveNodeName.getZhCn());
        //消息正文（英文）
        data.putAll(buildMessageEnContent(MessageConsts.EmailNotice.MESSAGE_CHANGE_PRESS_CONTENT,
                changeOrder.getOrderNo(), changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(EN_US), approveNodeName.getEnUs()));

        // {单据编号}/{操作主题}{是否紧急}已进入{下一流程节点名称环节}环节，请您及时处理！
        EmailParam emailParam = builderEmailParam(changeOrderId, data, approveFlowCodeEnum, true, isTransfer, transferor);
        emailParam.setNodeCode(nodeExtendName);
        EmailAbility.asyncSendMail(emailParam);
    }

    /**
     * 打卡复盘催办邮件
     * {单据编号}/{操作主题}（批次{批次号}），已指派给您，请及时处理！
     *
     * @param clockInReviewId 打卡复盘id
     * @param approveFlowCodeEnum 流程编码对应流程信息
     */
    public static void sendClockInReviewPressMail(String clockInReviewId,
                                                  ApproveFlowCodeEnum approveFlowCodeEnum,
                                                  boolean isTransfer,
                                                  String transferor) {
        List<String> clockInReviewFields = Lists.newArrayList(ID, ClockInReviewsFieldConsts.REVIEWS_NO,
                ClockInReviewsFieldConsts.OPERATION_SUBJECT, ClockInReviewsFieldConsts.BATCH_NO);
        ClockInReviews clockInReviews = QueryDataHelper.get(ClockInReviews.class, clockInReviewFields, clockInReviewId);
        if (clockInReviews == null) {
            return;
        }
        Map<String, Object> data = buildMessageContent(MessageConsts.EmailNotice.MESSAGE_CLOCKIN_REVIEWS_PRESS_CONTENT,
                clockInReviews.getReviewsNo(), clockInReviews.getOperationSubject(), clockInReviews.getBatchNo());

        // 打卡复盘不发送iCenter消息
        sendPressMail(clockInReviewId, data, approveFlowCodeEnum, false, isTransfer, transferor);
    }

    /**
     * 催办邮件发送
     *
     * @param businessId 变更单/批次/复盘id
     * @param data 文案
     * @param approveFlowCodeEnum 流程编码
     * @param isICenter 是否发送iCenter信息
     * @param isTransfer 是否转交
     * @param transferor 转交人
     */
    private static void sendPressMail(String businessId,
                                      Map<String, Object> data,
                                      ApproveFlowCodeEnum approveFlowCodeEnum,
                                      boolean isICenter,
                                      boolean isTransfer,
                                      String transferor) {
        EmailAbility.asyncSendMail(builderEmailParam(businessId, data, approveFlowCodeEnum, isICenter, isTransfer, transferor));
    }

    public static EmailParam builderEmailParam(String businessId,
                                                Map<String, Object> data,
                                                ApproveFlowCodeEnum approveFlowCodeEnum,
                                                boolean isICenter,
                                                boolean isTransfer,
                                                String transferor) {
        return EmailParam.builder()
                .pkId(businessId)
                .templateIdEnum(TemplateIdEnum.EMAIL_PRESS_2)
                .iCenterTemplateIdEnum(isICenter ? TemplateIdEnum.ICENTER_PRESS_NOTIFY : null)
                .notifyTypeEnum(NotifyTypeEnum.PRESS)
                .data(data)
                .isApprovePage(true)
                .assignmentTypeEnum(AssignmentTypeEnum.fromFlowCode(approveFlowCodeEnum))
                .isTransfer(isTransfer)
                .transferor(transferor)
                .build();
    }

    /**
     * 打卡复盘知会邮件
     * 【网络变更操作复盘通知/{单据编号}】
     * {操作主题}（批次{批次号}），已进入{复盘状态}，请知悉！
     *
     * @param businessId 打卡复盘id
     * @param nodeCode 知会前置节点编码（自定义编码）
     * @param approveFlowCodeEnum approveFlowCodeEnum
     */
    public static void sendClockInReviewInformMail(String businessId, String nodeCode,ApproveFlowCodeEnum approveFlowCodeEnum) {
        ClockInReviewsNodeEnum approveNodeEnum = ClockInReviewsNodeEnum.fromName(nodeCode);
        if (approveNodeEnum == null) {
            return;
        }
        AssignmentStatusEnum nextNodeStatusEnum = null;
        // 单据编号，操作主题，批次号
        List<String> fields = Lists.newArrayList(ID, ClockInReviewsFieldConsts.REVIEWS_NO,
                ClockInReviewsFieldConsts.OPERATION_SUBJECT, ClockInReviewsFieldConsts.BATCH_NO, CREATE_BY);
        switch (approveNodeEnum) {
            case CLOCKIN_REVIEWS_SUBMIT:
                fields.add(ST_EMAIL);
                nextNodeStatusEnum = AssignmentStatusEnum.REVIEWS_APPROVAL;
                break;
            case CLOCKIN_REVIEWS_APPROVAL:
                // 审核结果，是否整改，抄送人
                fields.addAll(Lists.newArrayList(AR_APPROVE_RESULT, IS_RECTIFY, AR_EMAIL));
                nextNodeStatusEnum = getReviewsApprovalNextNodeStatus(businessId, fields);
                break;
            case CLOCKIN_REVIEWS_RECTIFY:
                fields.add(RE_EMAIL);
                nextNodeStatusEnum = AssignmentStatusEnum.CLOSE;
                break;
            case CLOCKIN_REVIEWS_SYSTEM:
                nextNodeStatusEnum = AssignmentStatusEnum.CLOSE;
                break;
            default:
                break;
        }

        ClockInReviews clockInReviews = QueryDataHelper.get(ClockInReviews.class, fields, businessId);
        List<String> mailTos = new ArrayList<>();
        // 申请人
        mailTos.add(clockInReviews.getCreateBy());
        // 邮件抄送人
        mailTos.addAll(ClockInReviewsAbility.getEmailTos(clockInReviews));
        LookupValue lookupValue = LookupValueHelper.getLookupValue(ASSIGNMENT_STATUS_ENUM, nextNodeStatusEnum.getValue());
        // 中文
        Map<String, Object> data = EmailAbility.buildMessageZhContent(MessageConsts.EmailNotice.MESSAGE_CLOCKIN_REVIEWS_NOTIFY_CONTENT,
                clockInReviews.getReviewsNo(),clockInReviews.getOperationSubject(), clockInReviews.getBatchNo(), lookupValue.getMeaningCn());
        // 英文
        data.putAll(EmailAbility.buildMessageEnContent(MessageConsts.EmailNotice.MESSAGE_CLOCKIN_REVIEWS_NOTIFY_CONTENT,
                clockInReviews.getReviewsNo(),clockInReviews.getOperationSubject(), clockInReviews.getBatchNo(), lookupValue.getMeaningEn()));

        // {单据编号}/{操作主题}（批次{批次号}），已进入{复盘状态}，请知悉！
        sendApproResultInformMail(businessId, data, approveFlowCodeEnum.name(), mailTos, false);
    }

    /**
     * 审核通过/驳回 - 知会邮件发送
     *
     * @param businessId 变更单/批次/复盘id
     * @param data data
     * @param flowCode 流程编码
     * @param mailTos 收件人
     * @param isICenter 是否发送iCenter信息
     */
    public static void sendApproResultInformMail(
            String businessId,
            Map<String, Object> data,
            String flowCode,
            List<String> mailTos,
            boolean isICenter) {

        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(flowCode);
        EmailAbility.asyncSendMail(EmailParam.builder()
                .pkId(businessId)
                .templateIdEnum(TemplateIdEnum.EMAIL_INFORM_2)
                .iCenterTemplateIdEnum(isICenter ? TemplateIdEnum.ICENTER_PRESS_NOTIFY : null)
                .notifyTypeEnum(NotifyTypeEnum.INFORM)
                .data(data)
                .mailTos(mailTos)
                .isApprovePage(false)
                .assignmentTypeEnum(AssignmentTypeEnum.fromFlowCode(approveFlowEnum))
                .build());
    }


    /**
     * 获取复盘审核下节点状态
     *
     * @param billId 打卡复盘id
     * @param fields 字段
     * @return AssignmentStatusEnum
     */
    private static AssignmentStatusEnum getReviewsApprovalNextNodeStatus(String billId, List<String> fields) {
        AssignmentStatusEnum nextNodeStatusEnum;
        ClockInReviews clockInReviews = QueryDataHelper.get(ClockInReviews.class, fields, billId);
        ApproveResultEnum approveResultEnum = ApproveResultEnum.getApproveResultEnum(
                clockInReviews.getArApproveResult().get(0).getValue());
        if (ApproveResultEnum.REJECT == approveResultEnum) {
            nextNodeStatusEnum = AssignmentStatusEnum.REVIEWS_SUBMIT;
        }else {
            nextNodeStatusEnum = clockInReviews.getIsRectify() == BoolEnum.Y ?
                    AssignmentStatusEnum.REVIEWS_RECTIFICATION : AssignmentStatusEnum.REVIEWS_RECTIFY;
        }
        return nextNodeStatusEnum;
    }

    /**
     * 审核结果通过/驳回 - 变更单
     *
     * @param changeOrderId 变更单id
     * @param nodeName 前置节点名称
     * @param approveFlowCodeEnum approveFlowCodeEnum
     * @param approveResultEnum 审核结果（通过/驳回）
     * @param approver 操作者
     */
    public static void sendChangeOrderInformMail(String changeOrderId,
                                                 String nodeName,
                                                 ApproveFlowCodeEnum approveFlowCodeEnum,
                                                 ApproveResultEnum approveResultEnum,
                                                 String approver) {
        ApproveNodeEnum approveNodeEnum = ApproveNodeEnum.getApproveNodeEnum(nodeName);
        if (approveNodeEnum == null) {
            // 驳回到起始节点或终止节点，只给单据创建人发知会邮件的场景（不会传节点编码）
            sendChangeOrderRejectMail(changeOrderId, approveFlowCodeEnum, approver);
            return;
        }

        Set<String> mailTos = new HashSet<>();
        List<String> fields = new ArrayList<>(CHANGE_ORDER_FIELDS);
        String ccField = EMPTY_STRING;
        //1.根据节点名称获取当前审核节点维护的支持人员和邮件抄送人员，如没有则传NONE即可
        Class<? extends BaseSubEntity> subEntityEnum = null;
        switch (approveNodeEnum) {
            //代表处TD审核
            case REP_PROD_TD_APPROVE:
                subEntityEnum = SupportStaffRepProdTdApp.class;
                ccField = EMAIL_CC_REP_PROD_TD_APP;
                fields.add(ccField);
                break;
            //技术交付部/网络处审批
            case TD_NET_DEPT_APPROVE:
                subEntityEnum = SupportStaffNetDeptApprove.class;
                ccField = EMAIL_CC_TD_NET_DEPT_APP;
                fields.add(ccField);
                break;
            //技术交付部/网络处审核远程方案
            case NET_DEPT_REMOTE_SCHEME:
                subEntityEnum = SupportStaffTdNetDeptAppSolution.class;
                ccField = EMAIL_CC_TD_NET_DEPT_APP_SOLU;
                fields.add(ccField);
                break;
            //网络服务部审批
            case NET_SERVICE_DEPT_APP:
                subEntityEnum = SupportStaffNetServiceDeptApprove.class;
                ccField = EMAIL_CC_NET_SERVICE_DEPT;
                fields.add(ccField);
                fields.add(NetServiceDeptAppFieldConsts.IS_DEDICATED_BEAR_GUARANTEE);
                break;
            //网服部一体化关联产品审批
            case NET_INTEGRATION:
                subEntityEnum = OpAssocProdNetServIntApproval.class;
                break;

            //服务产品支持部审批
            case SERVICE_PROD_SUPPORT:
                subEntityEnum = SupportStaffServiceProdSupport.class;
                ccField = EMAIL_CC_SERVICE_PROD_SUPPORT;
                fields.add(ccField);
                break;
            //SSP产品支持团队审批
            case SSP_PROD_SUPPORT:
                subEntityEnum = SupportStaffSspProdSupport.class;
                ccField = EMAIL_CC_SSP_PROD_SUPPORT;
                fields.add(ccField);
                break;
            //测试部审批
            case TEST_DEPT:
                ccField = EMAIL_CC_TEST_DEPT;
                fields.add(ccField);
                break;

            //研发经理审批
            case RD_MANAGER:
                subEntityEnum = SupportStaffRdManager.class;
                ccField = EMAIL_CC_RD_MANAGER;
                fields.add(ccField);
                break;

            //研发一体化关联产品
            case RD_INTEGRATION:
                subEntityEnum = OpAssocProdDevIntApproval.class;
                break;

            //集成团队审批
            case INTEGRATION_TEAM_APP:
                subEntityEnum = SupportStaffRdManager.class;
                ccField = EMAIL_CC_INTEGRATION_TEAM;
                fields.add(ccField);
                break;

            //研发领导审核
            case RD_LEADER_APP:
                ccField = EMAIL_CC_RD_LEADER;
                fields.add(ccField);
                break;

            //远程中心负责人确认
            case REMOTE_CENTER_OWNER:
                ccField = EMAIL_CC_REMOTE_CENTER_OWNER;
                fields.add(ccField);
                break;

            //远程中心方案编写
            case REMOTE_CENTER_SCHEME:
                subEntityEnum = SupportStaffRemoteCenterScheme.class;
                ccField = EMAIL_CC_REMOTE_CENTER_SCHEME;
                fields.add(ccField);
                break;

            //远程中心方案编写
            case REMOTE_CENTER_OPER:
                subEntityEnum = SupportStaffTdRemoteCenterOperAssign.class;
                ccField = EMAIL_CC_REMOTE_CENTER_OPER_ASSIGN;
                fields.add(ccField);
                break;

            //CCN大区TD确认
            case REGIONAL_TD_CONFIRM:
                ccField = EMAIL_CC_REGIONAL_TD_CONFIRM;
                fields.add(ccField);
                break;

            //网络处总工
            case NETWORK_CHIEF_ENGINEER:
                ccField = NetworkChiefEngineerConsts.EMAIL_CC;
                fields.add(ccField);
                break;
            /**
             * 兼容老流程版本
             * （1）ApprovalResultNotifyMailToPlugin中使用当前走默认无支持人员和抄送人逻辑
             * （2）如果后期行政审核_办事处产品科长这个节点要加支持人员或抄送人需要同步修改
             */
            case ADMIN_REP_PROD_CHIEF:
                break;
            default:
                break;
        }
        ChangeOrder changeOrder = QueryDataHelper.get(ChangeOrder.class, fields, changeOrderId);
        //2.如为变更单审批通过知会通知，添加提单人、支持人员和邮件抄送人
        // {操作者}审核驳回{单据编号}/{操作主题}{是否紧急}
        String msg = MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_REJECT_CONTENT;
        if (approveResultEnum == null || ApproveResultEnum.PASS == approveResultEnum) {
            //构建支持人员
            if (subEntityEnum != null) {
                mailTos.addAll(ChangeOrderAbility.getSupportStaffList(changeOrderId, subEntityEnum));
            }
            // {操作者}审核通过{单据编号}/{操作主题}{是否紧急}
            msg = MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_PASS_CONTENT;
        }
        // 提单人
        mailTos.add(changeOrder.getCreateBy());
        // 构建邮件发送人
        mailTos.addAll(ChangeOrderAbility.getChangeOrderPersonList(changeOrder));
        // 邮件推送配置抄送人
        List<String> groupUserIds = EmailGroupAbility.getNetworkChangeCcPerson(changeOrder, approveNodeEnum);
        mailTos.addAll(groupUserIds);

        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(approver);
        // 中文正文
        Map<String, Object> data = EmailAbility.buildMessageZhContent(msg,
                employeeFormatNames.getZhCN(), changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(ZH_CN));
        // 英文中文
        data.putAll(EmailAbility.buildMessageEnContent(msg,
                employeeFormatNames.getEnUS(), changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(EN_US)));

        sendApproResultInformMail(changeOrderId, data, approveFlowCodeEnum.name(), new ArrayList<>(mailTos), true);

        // 更新抄送人
        List<Employee> emailSendGroup = HrClient.queryEmployeeInfo(groupUserIds);
        updateChangeOrderCcPeople(ccField, changeOrder, emailSendGroup);
    }

    /**
     * 更新内部变更单推送人员
     */
    public static boolean updateChangeOrderCcPeople(
            String ccField,
            ChangeOrder changeOrder,
            List<Employee> emailSendGroup) {

        if (StringUtils.isBlank(ccField)) {
            return false;
        }

        ChangeOrder updateChangeOrder = new ChangeOrder();
        try {
            Field[] fields = ChangeOrder.class.getDeclaredFields();

            for (Field field : fields) {
                JsonProperty annotation = field.getAnnotation(JsonProperty.class);
                if (Objects.isNull(annotation) || !ccField.equals(annotation.value())) {
                    continue;
                }

                field.setAccessible(true);
                List<Employee> existCcPerson = Objects.isNull(field.get(changeOrder))
                        ? Lists.newArrayList()
                        : JsonUtils.parseArray(field.get(changeOrder), Employee.class);
                existCcPerson.addAll(emailSendGroup);

                // 可能有重复用户，需要去重
                List<Employee> distinctCcPerson = existCcPerson.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(Employee::getEmpUIID, Function.identity(), (v1, v2) -> v1),
                                map -> new ArrayList<>(map.values())));
                field.set(updateChangeOrder, distinctCcPerson);
            }
        } catch (Exception e) {
            log.error("update CC person error: " + e);
        }

        updateChangeOrder.setId(changeOrder.getId());
        ChangeOrderAbility.update(updateChangeOrder);

        return true;
    }

    /**
     * 发送驳回知会邮件
     *
     * @param changeOrderId 变更单id
     * @param approveFlowEnum approveFlowEnum
     * @param approver approver
     */
    private static void sendChangeOrderRejectMail(String changeOrderId, ApproveFlowCodeEnum approveFlowEnum, String approver) {
        List<String> changeOrderFields = ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW == approveFlowEnum ? CHANGE_ORDER_FIELDS :
                SUBCONTRACTOR_CHANGE_ORDER_FIELDS;
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(approveFlowEnum.getFlowEntity(),
                changeOrderFields, changeOrderId);

        //3.构建发送邮件参数，发送邮件
        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(approver);
        // 消息正文（中文）
        Map<String, Object> data = EmailAbility.buildMessageZhContent(MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_REJECT_CONTENT,
                employeeFormatNames.getZhCN(), changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(ZH_CN));
        // 消息中文（英文）
        data.putAll(EmailAbility.buildMessageEnContent(MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_REJECT_CONTENT,
                employeeFormatNames.getEnUS(), changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(EN_US)));

        //【网络变更操作知会】{操作者}审核驳回{单据编号}/{操作主题}{是否紧急操作}
        sendApproResultInformMail(changeOrderId, data, approveFlowEnum.name(),
                Lists.newArrayList(changeOrder.getCreateBy()), true);
    }


    /**
     * 合作方变更单审核通过/驳回 - 知会
     *
     * @param changeOrderId 合作方变更单id
     * @param nodeName 前置节点名称
     * @param approveFlowCodeEnum approveFlowCodeEnum
     * @param approveResultEnum approveResultEnum
     * @param approver approver
     */
    public static void sendSubcontractorInformMail(String changeOrderId,
                                                   String nodeName,
                                                   ApproveFlowCodeEnum approveFlowCodeEnum,
                                                   ApproveResultEnum approveResultEnum,
                                                   String approver) {
        PartnerApproveNodeEnum approveNodeEnum = PartnerApproveNodeEnum.getApproveNodeEnum(nodeName);
        if (approveNodeEnum == null) {
            sendChangeOrderRejectMail(changeOrderId, approveFlowCodeEnum, approver);
            return;
        }

        Set<String> mailTos = new HashSet<>();
        List<String> fields = new ArrayList<>(SUBCONTRACTOR_CHANGE_ORDER_FIELDS);

        //支持人员&邮件抄送人员
        Class<? extends BaseSubEntity> supportStaffClass = null;
        String ccField = EMPTY_STRING;
        switch (approveNodeEnum) {
            //代表处产品科长审批
            case PARTNER_REPRESENTATIVE_CHIEF_APPROVAL:
                ccField = SubcontractorChangeOrderFieldConsts.RepProdChiefFieldConsts.EMAIL_CC;
                fields.add(ccField);
                break;
            //网络责任人审批
            case PARTNER_NET_OWNER_APPROVAL:
                supportStaffClass = SupportStaffSubNetOwner.class;
                ccField = SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.EMAIL_CC;
                fields.add(ccField);
                break;
            //办事处经理审批
            case PARTNER_OFFICE_MANAGER_APPROVAL:
                supportStaffClass = SupportStaffSubOfficeProdManager.class;
                ccField = SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.EMAIL_CC;
                fields.add(ccField);
                break;
            //网络处审批
            case PARTNER_NET_DEPT_APPROVAL:
                supportStaffClass = SupportStaffSubNetDept.class;
                ccField = SubcontractorChangeOrderFieldConsts.NetDeptFieldConsts.EMAIL_CC;
                fields.add(ccField);
                break;
            //办事处PD_紧急操作行政审批
            case PARTNER_OFFICE_PD_APPROVAL:
                ccField = SubcontractorChangeOrderFieldConsts.OfficePdFieldConsts.EMAIL_CC;
                fields.add(ccField);
                break;
            //办事产品科长-紧急操作行政审批
            case PARTNER_OFFICE_PROD_CHIEF_APPROVAL:
                ccField = SubcontractorChangeOrderFieldConsts.OfficeProdChiefFieldConsts.EMAIL_CC;
                fields.add(ccField);
                break;
            default:
                break;
        }

        //当前分包商变更单需求的邮件相关字段和变更单保持一致，可复用变更单的方法
        IChangeOrder changeOrder = QueryDataHelper.get(SubcontractorChangeOrder.class, fields, changeOrderId);
        //分包商网络变更单审核通过
        String msg = MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_REJECT_CONTENT;
        if (approveResultEnum == null || ApproveResultEnum.PASS == approveResultEnum) {
            //提单人
            mailTos.add(changeOrder.getCreateBy());
            //构建支持人员
            if (supportStaffClass != null) {
                mailTos.addAll(ChangeOrderAbility.getSupportStaffList(changeOrderId, supportStaffClass));
            }
            msg = MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_PASS_CONTENT;
        }

        //邮件抄送人
        mailTos.addAll(ChangeOrderAbility.getChangeOrderPersonList(changeOrder));
        // 邮件推送配置抄送人
        List<String> groupUserIds = EmailGroupAbility.getNetworkChangeCcPerson(changeOrder, null);
        mailTos.addAll(groupUserIds);

        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(approver);
        // 消息正文（中文）
        Map<String, Object> data = EmailAbility.buildMessageZhContent(msg,
                employeeFormatNames.getZhCN(), changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(ZH_CN));
        // 消息正文（英文）
        data.putAll(EmailAbility.buildMessageEnContent(msg,
                employeeFormatNames.getEnUS(), changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(EN_US)));

        // 【网络变更操作知会】{操作者}审核通过(驳回){单据编号}/{操作主题}{是否紧急}
        sendApproResultInformMail(changeOrderId, data, approveFlowCodeEnum.name(), new ArrayList<>(mailTos), true);

        // 更新抄送人
        List<Employee> emailSendGroup = HrClient.queryEmployeeInfo(groupUserIds);
        updatePartnerChangeOrderCcPeople(ccField, changeOrder, emailSendGroup);
    }

    /**
     * 更新合作方邮件推送人员
     */
    public static boolean updatePartnerChangeOrderCcPeople(
            String ccField,
            IChangeOrder changeOrder,
            List<Employee> emailSendGroup) {

        if (StringUtils.isBlank(ccField)) {
            return false;
        }

        SubcontractorChangeOrder updateChangeOrder = new SubcontractorChangeOrder();
        try {
            Field[] fields = SubcontractorChangeOrder.class.getDeclaredFields();

            for (Field field : fields) {
                JsonProperty annotation = field.getAnnotation(JsonProperty.class);
                if (Objects.isNull(annotation) || !ccField.equals(annotation.value())) {
                    continue;
                }

                field.setAccessible(true);
                List<Employee> existCcPerson = Objects.isNull(field.get(changeOrder))
                        ? Lists.newArrayList()
                        : JsonUtils.parseArray(field.get(changeOrder), Employee.class);
                existCcPerson.addAll(emailSendGroup);
                field.set(updateChangeOrder, existCcPerson);
            }
        } catch (Exception e) {
            log.error("update CC person error: " + e);
        }

        updateChangeOrder.setId(changeOrder.getId());
        PartnerChangeOrderAbility.update(updateChangeOrder);

        return true;
    }

    /**
     * 批次知会
     * 1.【通过操作结果审核】
     *     1.1 内部批次有两个节点，分别是技术交付部/网络处审核和网络服务部审核节点。
     *     1.2 合作方批次只有一个节点，为网络责任人审核。
     * 2.【提交反馈操作结果】
     *     2.1 内部只有一个节点为待反馈结果
     *     2.2 合作方只有一个节点为待反馈结果
     * 3.驳回
     *     3.1 批次驳回只给批次的责任人发（责任人在任务表中）
     *
     * @param batchId 批次id
     * @param nodeExtendName 前置节点编码
     * @param approveFlowEnum approveFlowEnum
     * @param approveResultEnum approveResultEnum
     */
    public static void sendBatchInformMail(
            String batchId,
            String nodeExtendName,
            ApproveFlowCodeEnum approveFlowEnum,
            ApproveResultEnum approveResultEnum) {

        ApproveNodeEnum approveNodeEnum = null;
        PartnerApproveNodeEnum partnerApproveNodeEnum = null;
        if (ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW.equals(approveFlowEnum)) {
            partnerApproveNodeEnum = PartnerApproveNodeEnum.getApproveNodeEnum(nodeExtendName);
        } else {
            approveNodeEnum = ApproveNodeEnum.getApproveNodeEnum(nodeExtendName);
        }

        // 1.检索审批记录
        List<ApproveRecord> approvedRecords = FlowHelper.getApprovedRecords(batchId);
        if (CollectionUtils.isEmpty(approvedRecords)) {
            return;
        }
        ApproveRecord approveRecord = approvedRecords.get(approvedRecords.size() - 1);

        // 仅通过 / 驳回发送邮件
        if (!ApproveResultEnum.REJECT.equals(approveResultEnum)
                && !ApproveResultEnum.PASS.equals(approveResultEnum)) {
            return;
        }

        // 2.审核驳回
        // ${操作者}审核驳回${操作主题}${是否紧急操作}（批次${批次号}），请您及时处理！
        if (ApproveResultEnum.REJECT == approveResultEnum) {
            batchApproveRejectProcess(batchId, approveFlowEnum, approveRecord.getApprover());
            return;
        }

        // 3.审批流通过
        // (1) 审批操作结果
        //【${操作主题}${是否紧急操作}（批次${批次号}）】${操作者}通过操作结果审核
        if (ApproveNodeEnum.TD_NET_DEPT_APPROVE == approveNodeEnum
                || ApproveNodeEnum.NET_SERVICE_DEPT_APP == approveNodeEnum
                || PartnerApproveNodeEnum.PARTNER_NET_OWNER_APPROVAL == partnerApproveNodeEnum) {
            batchApproveResultPassProcess(
                    batchId, approveFlowEnum, approveRecord.getApprover(), approveNodeEnum, partnerApproveNodeEnum);
            return;

        }

        // (2) 反馈操作结果
        //【${操作主题}${是否紧急操作}（批次${批次号}）】${操作者}提交反馈操作结果
        if (ApproveNodeEnum.RESULT_TOBE_BACK == approveNodeEnum
                || PartnerApproveNodeEnum.RESULT_TOBE_BACK == partnerApproveNodeEnum) {
            batchFeedbackResultProcess(batchId, approveFlowEnum, approveNodeEnum, partnerApproveNodeEnum);
        }
    }

    /**
     * 发送取消/挂起邮件  -批次任务 兼容保障批次
     *
     * @param batchId 批任务id
     * @param notifyTypeEnum    通知类型枚举：取消/挂起
     * @param batchTaskClass  批次任务/分包商批次任务区分标识：BATCH_TASK为批次任务，SUBCONTRACT_BATCH_TASK为分包商批次任务
     */
    public static void sendBatchOperationEmail(String batchId, NotifyTypeEnum notifyTypeEnum, Class<? extends BaseEntity> batchTaskClass) {
        sendCancelSuspendedEmail(batchId, notifyTypeEnum, batchTaskClass);
        List<BatchTask> guarantyBatchTasks = BatchTaskAbility.getGuarantyBatchTask(batchId);
        guarantyBatchTasks.forEach(item -> {
            sendCancelSuspendedEmail(item.getId(), notifyTypeEnum, batchTaskClass);
        });
    }

    /**
     * 批次审核驳回处理
     */
    private static void batchApproveRejectProcess(
            String batchId,
            ApproveFlowCodeEnum approveFlowEnum,
            String approver) {

        //普通批次任务
        Class<? extends BaseEntity> batchTaskClass = BatchTask.class;
        Class<? extends BaseEntity> changeOrderClass = ChangeOrder.class;
        //分包商批次任务
        if (ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowEnum) {
            batchTaskClass = SubcontractorBatchTask.class;
            changeOrderClass = SubcontractorChangeOrder.class;
        }

        // 2.查询批次任务
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(
                batchTaskClass,
                Lists.newArrayList(ID, BATCH_NO, BATCH_CODE, BatchTaskFieldConsts.CHANGE_ORDER_ID),
                batchId);

        //3.查询变更单信息
        List<String> changeOrderFields = Lists.newArrayList(ID, ChangeOrderFieldConsts.OPERATION_SUBJECT,
                ChangeOrderFieldConsts.IS_EMERGENCY_OPERATION, CREATE_BY);
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                changeOrderClass, changeOrderFields, Objects.requireNonNull(batchTask).getChangeOrderId());

        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(approver);
        // 正文中文
        Map<String, Object> data = EmailAbility.buildMessageZhContent(MessageConsts.EmailNotice.MESSAGE_BATCH_APPROVAL_REJECT_CONTENT,
                employeeFormatNames.getZhCN(), batchTask.getBatchCode(), changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(ZH_CN), batchTask.getBatchNo());
        // 正文英文
        data.putAll(EmailAbility.buildMessageEnContent(MessageConsts.EmailNotice.MESSAGE_BATCH_APPROVAL_REJECT_CONTENT,
                employeeFormatNames.getEnUS(), batchTask.getBatchCode(), changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(EN_US), batchTask.getBatchNo()));

        NetworkChangeAssignment networkChangeAssignment = AssignmentAbility.queryBatchAssignment(batchTask.getId());
        List<String> mailTos = EmployeeHelper.getEpmUIID(
                Objects.requireNonNull(networkChangeAssignment).getResponsibleEmployee());

        //【网络变更操作知会】{操作者}审核驳回{单据编号}/{操作主题}{是否紧急操作}（批次${批次号}），请您及时处理！
        sendApproResultInformMail(batchId, data, approveFlowEnum.name(), mailTos, true);
    }

    /**
     * 批次提交反馈操作结果
     */
    private static void batchFeedbackResultProcess(
            String batchId,
            ApproveFlowCodeEnum approveFlowEnum,
            ApproveNodeEnum approveNodeEnum,
            PartnerApproveNodeEnum partnerApproveNodeEnum) {

        // 1.批次任务 / 分包商批次任务属性映射
        // (1) 批次任务
        Class<? extends BaseEntity> batchTaskClass = BatchTask.class;
        Class<? extends BaseEntity> changeOrderClass = ChangeOrder.class;
        List<String> fields = Lists.newArrayList(
                ID, ChangeOrderFieldConsts.OPERATION_SUBJECT, ChangeOrderFieldConsts.IS_EMERGENCY_OPERATION,
                ChangeOrderFieldConsts.PRODUCT_CATEGORY, ChangeOrderFieldConsts.RESPONSIBLE_DEPT, IS_GOV_ENT,
                IS_SPECIAL_SCENARIO, IS_AFFECT_TO_B, CREATE_BY);
        IBatchTask updateBatchTask = new BatchTask();

        // (2) 分包商批次任务
        if (ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowEnum) {
            batchTaskClass = SubcontractorBatchTask.class;
            changeOrderClass = SubcontractorChangeOrder.class;
            fields = Lists.newArrayList(
                    ID, SubcontractorChangeOrderFieldConsts.PRODUCT_ID,
                    SubcontractorChangeOrderFieldConsts.ORGANIZATION_ID,
                    SubcontractorChangeOrderFieldConsts.IS_GOV_ENT,
                    ChangeOrderFieldConsts.OPERATION_SUBJECT,
                    ChangeOrderFieldConsts.IS_EMERGENCY_OPERATION,
                    CREATE_BY);
            updateBatchTask = new SubcontractorBatchTask();
        }

        // 2.检索批次任务信息
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(batchTaskClass,
                Lists.newArrayList(ID, BATCH_NO, RESULT_MAIL, BatchTaskFieldConsts.CHANGE_ORDER_ID, OPERATION_RESULT),
                batchId);
        updateBatchTask.setId(batchId);

        // 3.检索对应主单信息
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                changeOrderClass, fields, batchTask.getChangeOrderId());

        // 4.获取邮件推送人员
        // 用户选择结果知会 + 邮件推送配置
        List<String> emailsTo = EmailGroupAbility.getBatchCcPerson(
                changeOrder, batchTask, approveNodeEnum, partnerApproveNodeEnum);
        List<String> resultEmails = BatchTaskAbility.updateNodeEmailCcs(
                updateBatchTask, emailsTo, batchTask.getResultMail(), updateBatchTask::setResultMail);
        if (CollectionUtils.isEmpty(resultEmails)) {
            return;
        }

        // 5.发送邮件
        Assignment assignment = AssignmentAbility.queryAssignment(
                batchId, Lists.newArrayList(ID, AssignmentFieldConsts.RESPONSIBLE_EMPLOYEE_FIELD), Assignment.class);
        List<String> employeeInfo = EmployeeHelper.getEpmUIID(Objects.requireNonNull(assignment).getResponsibleEmployee());

        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(employeeInfo.get(0));
        // 正文中文
        Map<String, Object> data = EmailAbility.buildMessageZhContent(
                MessageConsts.EmailNotice.MESSAGE_BATCH_FEEDBACK_RESULT_CONTENT,
                batchTask.getBatchCode(),
                changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(ZH_CN),
                batchTask.getBatchNo(),
                employeeFormatNames.getZhCN());

        // 正文英文
        data.putAll(EmailAbility.buildMessageEnContent(
                MessageConsts.EmailNotice.MESSAGE_BATCH_FEEDBACK_RESULT_CONTENT,
                batchTask.getBatchCode(),
                changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(EN_US),
                batchTask.getBatchNo(),
                employeeFormatNames.getEnUS()));

        // 【网络变更操作知会】【{单据编号}/{操作主题}{是否紧急操作}（批次{批次号}）】{操作者}提交反馈操作结果
        sendApproResultInformMail(batchId, data, approveFlowEnum.name(), resultEmails, true);
    }


    /**
     * 通过操作结果审核
     */
    private static void batchApproveResultPassProcess(
            String batchId,
            ApproveFlowCodeEnum approveFlowCodeEnum,
            String approver,
            ApproveNodeEnum approveNodeEnum,
            PartnerApproveNodeEnum partnerApproveNodeEnum) {

        // 1.批次任务 / 分包商批次任务属性映射
        // (1) 批次任务
        Class<? extends BaseEntity> batchTaskClass = BatchTask.class;
        Class<? extends BaseSubEntity> batchTaskOperatorClass = BatchTaskOperator.class;
        Class<? extends BaseEntity> changeOrderClass = ChangeOrder.class;
        List<String> fields = Lists.newArrayList(
                ID, ChangeOrderFieldConsts.OPERATION_SUBJECT, ChangeOrderFieldConsts.IS_EMERGENCY_OPERATION,
                ChangeOrderFieldConsts.PRODUCT_CATEGORY, ChangeOrderFieldConsts.RESPONSIBLE_DEPT, IS_GOV_ENT,
                IS_SPECIAL_SCENARIO, IS_AFFECT_TO_B, CREATE_BY);
        IBatchTask updateBatchTask = new BatchTask();

        // (2) 分包商批次任务
        if (ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW == approveFlowCodeEnum) {
            batchTaskClass = SubcontractorBatchTask.class;
            batchTaskOperatorClass = SubcontractorBatchOperator.class;
            changeOrderClass = SubcontractorChangeOrder.class;
            fields = Lists.newArrayList(
                    ID, SubcontractorChangeOrderFieldConsts.PRODUCT_ID,
                    SubcontractorChangeOrderFieldConsts.ORGANIZATION_ID,
                    SubcontractorChangeOrderFieldConsts.IS_GOV_ENT,
                    ChangeOrderFieldConsts.OPERATION_SUBJECT,
                    ChangeOrderFieldConsts.IS_EMERGENCY_OPERATION,
                    CREATE_BY);
            updateBatchTask = new SubcontractorBatchTask();
        }

        // 2.查询批次任务，获取本环节维护的抄送人员、批次名称和变更单id
        IBatchTask batchTask = (IBatchTask) QueryDataHelper.get(batchTaskClass,
                Lists.newArrayList(ID, BATCH_CODE, BATCH_NO, APPROVAL_EMAIL, BatchTaskFieldConsts.CHANGE_ORDER_ID),
                batchId);
        updateBatchTask.setId(batchId);

        // 3.查询网络变更单，获取变更单主题、是否紧急操作、创建人（提单人）
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                changeOrderClass, fields, batchTask.getChangeOrderId());

        // 3.确定抄送人员
        // (1) 本批次的操作人员列表内所有人员和群组
        List<String> mailTos = OperatorAbility.getBatchOperatorPersonsList(batchId, batchTaskOperatorClass);

        // (2) 批次责任人
        NetworkChangeAssignment networkChangeAssignment = AssignmentAbility.queryBatchAssignment(batchTask.getId());
        mailTos.addAll(EmployeeHelper.getEpmUIID(
                Objects.requireNonNull(networkChangeAssignment).getResponsibleEmployee()));

        // (3) 邮件群组推送人 + 用户填写抄送人（需更新到审批抄送人字段中）
        List<String> configCcs = EmailGroupAbility.getBatchCcPerson(
                changeOrder, batchTask, approveNodeEnum, partnerApproveNodeEnum);
        List<String> approvalCcs = BatchTaskAbility.updateNodeEmailCcs(
                updateBatchTask, configCcs, batchTask.getApprovalEmail(), updateBatchTask::setApprovalEmail);
        mailTos.addAll(approvalCcs);

        // 5.发送邮件
        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(approver);
        // 正文中文
        Map<String, Object> data = EmailAbility.buildMessageZhContent(
                MessageConsts.EmailNotice.MESSAGE_BATCH_APPROVAL_PASS_CONTENT,
                batchTask.getBatchCode(),
                changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(ZH_CN),
                batchTask.getBatchNo(),
                employeeFormatNames.getZhCN());

        // 正文英文
        data.putAll(EmailAbility.buildMessageEnContent(
                MessageConsts.EmailNotice.MESSAGE_BATCH_APPROVAL_PASS_CONTENT,
                batchTask.getBatchCode(),
                changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(EN_US),
                batchTask.getBatchNo(),
                employeeFormatNames.getEnUS()));

        // 【网络变更操作知会】【{单据编号}/{操作主题}{是否紧急操作}（批次{批次号}）】{操作者}通过操作结果审核
        sendApproResultInformMail(batchId, data, approveFlowCodeEnum.name(), mailTos, true);
    }

    /**
     * 异步发送撤回邮件
     *
     * @param assignment 任务信息
     */
    public static void asyncSendRevokeMail(Assignment assignment) {
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        switch (assignmentTypeEnum) {
            case NETWORK_CHANGE:
            case SUBCONTRACTOR_NETWORK_CHANGE:
                sendChangeOrderRevokeMail(assignment);
                break;
            default:
                break;
        }
    }

    /**
     * 发送网络变更单撤回邮件
     *
     * @param assignment 任务信息
     */
    private static void sendChangeOrderRevokeMail(Assignment assignment) {
        String changeOrderId = assignment.getEntityId();
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        List<String> currentProcessorIds = EmployeeHelper.getEpmUIID(assignment.getCurrentProcessorEmployee());

        Class<? extends BaseEntity> changeOrderEntityClass = assignmentTypeEnum.getApproveFlowCodeEnum().getFlowEntity();
        IChangeOrder changeOrder = (IChangeOrder) QueryDataHelper.get(
                changeOrderEntityClass, Lists.newArrayList(ID, ChangeOrderFieldConsts.IS_EMERGENCY_OPERATION), changeOrderId);

        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(ContextHelper.getEmpNo());
        Map<String, Object> data = EmailAbility.buildMessageZhContent(
                MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_REVOKE_CONTENT, employeeFormatNames.getZhCN(),
                assignment.getAssignmentCode(), assignment.getAssignmentName(), changeOrder.isEmergencyOperationExt(ZH_CN));
        data.putAll(EmailAbility.buildMessageEnContent(
                MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_REVOKE_CONTENT, employeeFormatNames.getEnUS(),
                assignment.getAssignmentCode(), assignment.getAssignmentName(), changeOrder.isEmergencyOperationExt(EN_US)));

        // 5.知会邮件 网络变更操作知会】姓名+工号+撤回+单据编号 +/+ 操作主题+（是否紧急）
        EmailAbility.asyncSendMail(EmailAbility.EmailParam.builder()
                .pkId(changeOrderId)
                .notifyTypeEnum(NotifyTypeEnum.INFORM)
                .templateIdEnum(TemplateIdEnum.EMAIL_INFORM_2)
                .data(data)
                .mailTos(currentProcessorIds)
                .isApprovePage(false)
                .assignmentTypeEnum(assignmentTypeEnum)
                .build());
    }


    @ApiModel("邮件参数")
    @Data
    @Builder
    public static class EmailParam {
        /**
         * 主键id
         */
        private String pkId;
        /**
         * 邮件模板
         */
        private TemplateIdEnum templateIdEnum;

        /**
         * iCenter模板
         */
        private TemplateIdEnum iCenterTemplateIdEnum;

        /**
         * 通知类型枚举：催办、知会、通告、已挂起、已取消、已关闭   ### 如需要进行标题管理，该值必传
         */
        private NotifyTypeEnum notifyTypeEnum;

        /**
         * 主送人, isApprovePage为true时不取当前值
         */
        private List<String> mailTos;
        /**
         * 抄送人
         */
        private List<String> mailCCs;
        /**
         * 模板占位参数值
         */
        private Map<String, Object> data;
        /**
         * 任务类型
         */
        private AssignmentTypeEnum assignmentTypeEnum;

        /**
         * 是否审批页面
         */
        private boolean isApprovePage;

        /**
         *数据来源
         */
        private String source;

        /**
         * 是否转交
         */
        private boolean isTransfer;

        /**
         * 转交人
         */
        private String transferor;

        /**
         * 当前节点编码
         */
        private String nodeCode;

        /**
         * 跳转tab页名称
         */
        private String tabName;
    }
}
