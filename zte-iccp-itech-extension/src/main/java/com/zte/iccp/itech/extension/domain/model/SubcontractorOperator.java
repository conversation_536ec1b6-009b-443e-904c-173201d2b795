package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BatchOperatorAttrEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.*;

@ApiModel("合作方主单操作人员")
@Setter
@Getter
@BaseSubEntity.Info(value = "operator_table", parent = SubcontractorChangeOrder.class)
public class SubcontractorOperator extends BaseSubEntity {

    @ApiModelProperty("人员角色")
    @JsonProperty(value = OPERATOR_ROLE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private OperatorRoleEnum operatorRole;

    @ApiModelProperty("远程操作标识")
    @JsonProperty(value = REMOTE_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum remoteFlag;

    @ApiModelProperty("姓名")
    @JsonProperty(value = OPERATOR_NAME)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee operatorName;

    @JsonProperty(value = TELEPHONE)
    @ApiModelProperty("电话")
    private String operatorPhone;

    @JsonProperty(value = OPERATOR_DEPARTMENT)
    @ApiModelProperty("所属部门")
    private String operatorDepartment;

    @JsonProperty(value = OPERATOR_ACCOUNT)
    @ApiModelProperty("操作账号")
    private List<TextValuePair> operatorAccount;

    @JsonProperty(value = OPERATOR_ATTRIBUTE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("人员属性")
    private BatchOperatorAttrEnum operatorAttribute;

    @JsonProperty(value = TASK_DESC)
    @ApiModelProperty("任务说明")
    private String taskDesc;

    @JsonProperty(value = OPERATOR_BATCH_NO)
    @ApiModelProperty("操作批次")
    private List<TextValuePair> operatorBatchNo;
}
