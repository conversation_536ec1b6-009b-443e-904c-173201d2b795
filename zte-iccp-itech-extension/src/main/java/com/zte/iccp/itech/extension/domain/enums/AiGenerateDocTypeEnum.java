package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_NETWORK_CHANGE_BILL;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_PARTNER_NETWORK_CHANGE_BILL;

/**
 * <AUTHOR>
 * @date 2024/6/14 上午11:13
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum AiGenerateDocTypeEnum {
    /**
     * 内部网络变更单-内部
     */
    CHANGE_ORDER_INTERNAL(PAGE_NETWORK_CHANGE_BILL, "1"),

    /**
     * 内部网络变更单-客户
     */
    CHANGE_ORDER_CUSTOMER(PAGE_NETWORK_CHANGE_BILL, "2"),

    /**
     * 合作商网络变更单-内部
     */
    PARTNER_CHANGE_ORDER_INTERNAL(PAGE_PARTNER_NETWORK_CHANGE_BILL, "1"),

    /**
     * 合作商网络变更单-客户
     */
    PARTNER_CHANGE_ORDER_CUSTOMER(PAGE_PARTNER_NETWORK_CHANGE_BILL, "2"),

    ;

    private final String pageId;

    private final String schemeType;

    public static String getType(String pageId, String schemeType) {
        for (AiGenerateDocTypeEnum aiGenerateDocTypeEnum : AiGenerateDocTypeEnum.values()) {
            if (aiGenerateDocTypeEnum.getPageId().equals(pageId)
                    && aiGenerateDocTypeEnum.getSchemeType().equals(schemeType)) {
                return aiGenerateDocTypeEnum.name();
            }
        }
        return null;
    }
}
