package com.zte.iccp.itech.extension.ability.permissionapplication;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OrganizationApproverConsts;
import com.zte.iccp.itech.extension.domain.model.subentity.OrganizationApprover;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/9 下午7:49
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OrganizationApproverAbility {
    public static List<OrganizationApprover> query(List<String> pids) {
        if (CollectionUtils.isEmpty(pids)) {
            return Lists.newArrayList();
        }
        return QueryDataHelper.queryByPid(OrganizationApprover.class, Lists.newArrayList(), pids);
    }

    /**
     * 检索 代表处审批人
     */
    public static OrganizationApprover query(String pid, String userId) {
        if (!StringUtils.hasText(pid) || !StringUtils.hasText(userId)) {
            return null;
        }

        IFilter filter = new Filter(OrganizationApproverConsts.APPROVER, Comparator.EQ, Lists.newArrayList());

        return QueryDataHelper.queryOne(
                OrganizationApprover.class, Lists.newArrayList(), pid, Lists.newArrayList(filter));
    }

    /**
     * 更新 代表处审核人
     */
    public static void update(OrganizationApprover organizationApprover) {
        if (Objects.isNull(organizationApprover)) {
            return;
        }

        SaveDataHelper.update(organizationApprover);
    }
}
