package com.zte.iccp.itech.extension.common;

import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.plugin.IWebPlugin;
import com.zte.paas.lcap.ddm.common.api.exception.ValidationException;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/10/30
 */
public class LcapBusiException extends ValidationException {
    public LcapBusiException(String msgId) {
        super(MsgUtils.getMessage(msgId));
        handleOrigin();
    }

    public LcapBusiException(String msgId, Map<String, Object> otherMap) {
        super(MsgUtils.getMessage(msgId), otherMap);
        handleOrigin();
    }

    public LcapBusiException(String msgId, Object... args) {
        super(MsgUtils.getMessage(msgId, args), args);
        handleOrigin();
    }

    public LcapBusiException(String msgId, Map<String, Object> otherMap, Object... args) {
        super(MsgUtils.getMessage(msgId, args), otherMap, args);
        handleOrigin();
    }

    public LcapBusiException(Boolean isShow, String msgId, Object... args) {
        super(isShow, MsgUtils.getMessage(msgId, args), args);
    }

    /**
     * openapi来源的异常setShow(false);
     * Web插件来源的异常setShow(true);
     */
    private void handleOrigin() {
        ClassLoader classLoader = LcapBusiException.class.getClassLoader();
        for (StackTraceElement element : Thread.currentThread().getStackTrace()) {
            Class<?> clazz;
            try {
                clazz = classLoader.loadClass(element.getClassName());
            } catch (ClassNotFoundException e) {
                continue;
            }

            if (AbstractOpenApi.class.isAssignableFrom(clazz)) {
                setShow(false);
                return;
            }

            if (IWebPlugin.class.isAssignableFrom(clazz)) {
                setShow(true);
                return;
            }
        }
    }
}
