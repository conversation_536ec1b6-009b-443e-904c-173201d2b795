package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.BATCH_NO;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.*;

@ApiModel("网络批次任务")
@Setter
@Getter
@BaseEntity.Info("batch_network_assignment")
public class BatchTaskExport extends BaseEntity {

    @JsonProperty(value = BATCH_NO)
    @ApiModelProperty("批次号")
    private String batchNo;

    @JsonProperty(value = CHANGE_ORDER_ID)
    @ApiModelProperty("主单id")
    private String changeOrderId;

    @JsonProperty(value = NETWORK_ELEMENT_COUNT)
    @ApiModelProperty("网元数量")
    private Integer neCount;

    @JsonProperty(value = PLAN_OPERATION_START_TIME)
    @ApiModelProperty("确认操作开始时间")
    private Date planOperationStartTime;

    @JsonProperty(value = PLAN_OPERATION_END_TIME)
    @ApiModelProperty("确认操作结束时间")
    private Date planOperationEndTime;

    @JsonProperty(value = IS_RETURNED)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("操作已回退")
    private BoolEnum isReturned;

    @JsonProperty(value = IS_ALL_COMPLETED)
    @ApiModelProperty("计划操作/升级对象（局点/站点、OMC）全部完成")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isAllCompleted;

    @JsonProperty(value = IS_ALL_REACH)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("计划实现的需求全部达成（对已完成操作/升级的局点/站点等对象）")
    private BoolEnum isAllReach;

    @JsonProperty(value = MAJOR_FAULTS_BEHIND)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("有无重大故障遗留")
    private BoolEnum majorFaultsBehind;

    @JsonProperty(value = NETWORK_INDICATORS)
    @ApiModelProperty("操作后（7天内）重要网络指标正常（无显著下降）")
    private Object networkIndicators;

    @JsonProperty(value = IS_FIRST_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是第一次操作/升级完成")
    private BoolEnum isFirstOperation;

    @JsonProperty(value = OPERATION_RESULT)
    @ApiModelProperty("操作结果")
    private Object operationResult;

    @JsonProperty(value = ACTUAL_OPERATION_START_TIME)
    @ApiModelProperty("实际操作开始时间")
    private Date factOperationDateStart;

    @JsonProperty(value = ACTUAL_OPERATION_END_TIME)
    @ApiModelProperty("实际操作结束时间")
    private Date factOperationDateEnd;

    @JsonProperty(value = MULTIPLE_OPERATION_REASON)
    @ApiModelProperty("多次操作原因")
    private Object multipleOperationReason;

    @JsonProperty(value = FAILURE_REASON)
    @ApiModelProperty("失败原因分类")
    private Object failureReason;

    @JsonProperty(value = IS_OPERATION_MANUAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("操作手册齐套性")
    private BoolEnum isOperationManual;

    @JsonProperty(value = OPERATION_DOC_SCORE)
    @ApiModelProperty("操作文档指导性得分")
    private Object operationDocScore;

    @JsonProperty(value = CAN_OPERATION_TIME)
    @ApiModelProperty("操作文档可获取时间")
    private Object canOperationTime;

    @JsonProperty(value = VERSION_TIME)
    @ApiModelProperty("版本及配套文档可获取时间")
    private Object versionTime;

    @JsonProperty(value = FACT_INPUT_DAY)
    @ApiModelProperty("操作实际投入人天")
    private String factInputDay;

    @JsonProperty(value = OPERATION_SUMMARY)
    @ApiModelProperty("操作总结")
    private String operationSummary;

    @JsonProperty(value = OPERATION_PERSON)
    @ApiModelProperty("操作人")
    private Object operationPerson;

    @JsonProperty(value = RESULT_OPERATION_DATE)
    @ApiModelProperty("操作时间")
    private Date resultOperationDate;

    @JsonProperty(value = APP_MULTIPLE_OPERATION_REASON)
    @ApiModelProperty("多次操作原因")
    private Object appMultipleOperationReason;

    @JsonProperty(value = REVIEW_CONFIRMATION)
    @ApiModelProperty("审核确认备注")
    private String reviewConfirmation;

    @JsonProperty(value = TOOL_LANDING_STATUS)
    @ApiModelProperty("工具落地状态")
    private Object toolLandingStatus;

    @JsonProperty(value = NO_TOOL_REASON)
    @ApiModelProperty("未使用工具原因")
    private Object noToolReason;

    @JsonProperty(value = TOOL_NAME)
    @ApiModelProperty("工具名称")
    private String toolName;

    @JsonProperty(value = TOOL_PRODUCT_STATUS)
    @ApiModelProperty("工具产品支持状态")
    private Object toolProductStatus;

    @JsonProperty(value = EFFICIENCY_EVALUATION)
    @ApiModelProperty("操作复杂度及效率评价")
    private Object efficiencyEvaluation;

    @JsonProperty(value = TOOL_SATISFACTION)
    @ApiModelProperty("工具使用满意度")
    private Object toolSatisfaction;

    @JsonProperty(value = REMOTE_SUPPORT)
    @ApiModelProperty("远程支持情况")
    private Object remoteSupport;

    @JsonProperty(value = OPERATOR_EVALUATION)
    @ApiModelProperty("操作人员操作能力评估")
    private Object operatorEvaluation;

    @JsonProperty(value = OPERATION_MATURE)
    @ApiModelProperty("网络变更操作是否成熟")
    private Object operationMature;
}
