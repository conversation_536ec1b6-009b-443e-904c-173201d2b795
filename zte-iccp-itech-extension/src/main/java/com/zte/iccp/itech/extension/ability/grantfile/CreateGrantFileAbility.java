package com.zte.iccp.itech.extension.ability.grantfile;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.ProdTeamAbbrAbility;
import com.zte.iccp.itech.extension.ability.accredit.CcnAuthorizationApplicationAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.entity.CcnAuthorizationApplication;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchSummary;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.spi.client.EmailClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.UcsClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.ucs.vo.UcsUserInfo;
import com.zte.iccp.itech.zlic.ability.GrantFileReadAbility;
import com.zte.iccp.itech.zlic.ability.GrantFileWriteAbility;
import com.zte.iccp.itech.zlic.model.BatchInfo;
import com.zte.iccp.itech.zlic.model.GrantFileInfo;
import com.zte.iccp.itech.zlic.model.NeListInfo;
import com.zte.iccp.itech.zlic.model.NetworkElementInfo;
import com.zte.iccp.itech.zlic.util.ZlicUtils;
import com.zte.paas.lcap.common.api.dto.PopUpSizeDTO;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import com.zte.paas.lcap.ddm.domain.enums.OpenTypeEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.BUSI_SYSTEM_ADMIN_KEY;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/11
 */
@Slf4j
public final class CreateGrantFileAbility extends BaseGrantFileAbility {

    private static final int CREATE_RETRY_COUNT = 3;

    private static final int BATCH_NO_MIN_LENGTH = 3;

    private static final int SA_MAGIC_NUM = -1;

    private static final int SA_END_DELAY_HOURS = 6;

    private static final String SA_ORDER_NO_SUFFIX = "-SA";

    private static final String DOWNLOAD_URL_KEY = "grantFile.downloadPageUrl";

    private static final String FILE_TYPE_NORMAL = MsgUtils.getLangMessage(ZH_CN, "grantFile");

    private static final String FILE_TYPE_SA = MsgUtils.getLangMessage(ZH_CN, "saGrantFile");

    private static final List<String> CCN_AUTHORIZATION_FILE_NE_LIST_FILED =
            Lists.newArrayList("TaskID", "ResourceID", "ResourceName", "ResourceType");

    private IChangeOrder changeOrder;

    private IBatchTask batchTask;

    public void createGrantFile(String flowCode, String batchTaskId) {
        try {
            ApproveFlowCodeEnum flow = ApproveFlowCodeEnum.getApproveFlowEnum(flowCode);
            if (flow == null) {
                throw new IllegalArgumentException(flowCode);
            }

            batchTask = getBatchTask(flow, batchTaskId);

            String changeOrderId = batchTask.getChangeOrderId();
            changeOrder = getChangeOrder(flow, changeOrderId);
            if (changeOrder.getIsNeedAuthorizationFile() != BoolEnum.Y) {
                return;
            }

            createGrantFileCore(flow, batchTaskId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            notifyCreateFailed(null, changeOrder);
            return;
        }

        sendSaGrantFile(changeOrder, batchTask);
    }

    private void createGrantFileCore(ApproveFlowCodeEnum flow, String batchTaskId) {
        String batchNo = batchTask.getBatchNo();

        String normalFileKey = null;
        try {
            normalFileKey = RetryUtils.get(
                    () -> createNormalGrantFile(flow, changeOrder, batchNo),
                    CREATE_RETRY_COUNT);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (StringUtils.isBlank(normalFileKey)) {
            notifyCreateFailed(FILE_TYPE_NORMAL, changeOrder);
        }

        String saFileKey = null;
        try {
            saFileKey = RetryUtils.get(
                    () -> createSaGrantFile(flow, changeOrder, batchNo),
                    CREATE_RETRY_COUNT);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (StringUtils.isBlank(saFileKey)) {
            notifyCreateFailed(FILE_TYPE_SA, changeOrder);
        }

        if (StringUtils.isNotBlank(normalFileKey)
                || StringUtils.isNotBlank(saFileKey)) {
            batchTask.setGrantFile(normalFileKey);
            batchTask.setSaGrantFile(saFileKey);
            BaseEntity baseEntity = (BaseEntity) batchTask;
            baseEntity.setId(batchTaskId);
            SaveDataHelper.update(baseEntity);
        }
    }

    private static String newFilename(boolean sa, IChangeOrder changeOrder, String batchNo) {
        DeptTypeEnum deptType = ChangeOrderAbility.getDeptType(changeOrder);
        batchNo = StringUtils.leftPad(batchNo, BATCH_NO_MIN_LENGTH, "0");

        if (deptType == DeptTypeEnum.INNER) {
            String teamAbbr = ProdTeamAbbrAbility.getAbbr(ZH_CN, changeOrder.getProductCategory());
            String fmt = sa ? "%s-SA-%s-%s%s-%s.zlic" : "%s-%s-%s%s-%s.zlic";
            return String.format(fmt,
                    changeOrder.getOrderNo(),
                    batchNo,
                    changeOrder.getCustomerTypeFlag(),
                    changeOrder.getProvince().getZhCN(),
                    teamAbbr);
        }

        String teamAbbr = ProdTeamAbbrAbility.getAbbr(EN_US, changeOrder.getProductCategory());
        String fmt = sa ? "%s-SA-%s-%s %s-%s.zlic" : "%s-%s-%s %s-%s.zlic";
        return String.format(fmt,
                changeOrder.getOrderNo(),
                batchNo,
                changeOrder.getCountry().getEnUS(),
                changeOrder.getCustomerTypeFlag(),
                teamAbbr);
    }

    private static String createNormalGrantFile(
            ApproveFlowCodeEnum flow,
            IChangeOrder changeOrder,
            String batchNo) {
        byte[] originFile = createOriginFile(flow, changeOrder, batchNo);
        byte[] zlic = ZlicUtils.zlic(originFile, getSecretKey());
        return CloudDiskHelper.upload(newFilename(false, changeOrder, batchNo), zlic);
    }

    private static String createSaGrantFile(
            ApproveFlowCodeEnum flow,
            IChangeOrder changeOrder,
            String batchNo) {
        byte[] originFile = createOriginSaFile(flow, changeOrder, batchNo);
        byte[] zlic = ZlicUtils.zlic(originFile, getSecretKey());
        return CloudDiskHelper.upload(newFilename(true, changeOrder, batchNo), zlic);
    }

    @SneakyThrows
    private static byte[] createOriginFile(
            ApproveFlowCodeEnum flow,
            IChangeOrder changeOrder,
            String batchNo) {
        byte[] data = AttachmentFileHelper.download(changeOrder.getNeListFile());

        NeListInfo neListInfo;
        try (InputStream is = new ByteArrayInputStream(data);
                Workbook workbook = new XSSFWorkbook(is)) {
            neListInfo = GrantFileReadAbility.readNeList(workbook);
        }

        GrantFileInfo grantFileInfo = createGrantFileInfo(flow, neListInfo, changeOrder, batchNo, false);

        try (ByteArrayOutputStream os = new ByteArrayOutputStream();
                Workbook workbook = new XSSFWorkbook()) {
            GrantFileWriteAbility.write(grantFileInfo, workbook);

            workbook.write(os);
            return os.toByteArray();
        }
    }

    @SneakyThrows
    private static byte[] createOriginSaFile(
            ApproveFlowCodeEnum flow,
            IChangeOrder changeOrder,
            String batchNo) {
        byte[] data = AttachmentFileHelper.download(changeOrder.getNeListFile());

        NeListInfo neListInfo;
        try (InputStream is = new ByteArrayInputStream(data);
                Workbook workbook = new XSSFWorkbook(is)) {
            neListInfo = GrantFileReadAbility.readNeList(workbook);
            neListInfo.setNetworkElements(Lists.newArrayList(new NetworkElementInfo() {{
                setBatchNo(batchNo);
                setObjectId(String.valueOf(SA_MAGIC_NUM));
                setExtra(Lists.newArrayList());
            }}));
        }

        GrantFileInfo grantFileInfo = createGrantFileInfo(flow, neListInfo, changeOrder, batchNo, true);
        BatchInfo batchInfo = grantFileInfo.getOperationSummary().get(0);
        batchInfo.setChgOrderNo(batchInfo.getChgOrderNo() + SA_ORDER_NO_SUFFIX);
        batchInfo.setOperationTypeNameZh(MsgUtils.getLangMessage(ZH_CN, SUPER_AUTHORIZATION));
        batchInfo.setOperationTypeNameEn(MsgUtils.getLangMessage(EN_US, SUPER_AUTHORIZATION));
        batchInfo.setNetworkElementCount(SA_MAGIC_NUM);

        try (ByteArrayOutputStream os = new ByteArrayOutputStream();
                Workbook workbook = new XSSFWorkbook()) {
            GrantFileWriteAbility.write(grantFileInfo, workbook);

            workbook.write(os);
            return os.toByteArray();
        }
    }

    private static GrantFileInfo createGrantFileInfo(
            ApproveFlowCodeEnum flow,
            NeListInfo neListInfo,
            IChangeOrder changeOrder,
            String batchNo,
            boolean sa) {
        GrantFileInfo grantFileInfo = new GrantFileInfo();

        grantFileInfo.setNeFields(neListInfo.getNeFields());
        grantFileInfo.setNetworkElements(
                neListInfo.getNetworkElements().stream()
                        .filter(ne -> ne.getBatchNo().equals(batchNo))
                        .collect(Collectors.toList()));


        IBatchTask batchTask = getBatchTask(flow, changeOrder.getId(), batchNo);
        if (batchTask == null) {
            throw new LcapBusiException(BATCH_NO_NOT_EXISTS);
        }

        LookupValue lookupValueZh = LookupValueHelper.getLookupValue(
                ZH_CN,
                LookupValueConstant.OPERATE_TYPE_ENUM,
                changeOrder.getOperationType());
        if (lookupValueZh == null) {
            throw new LcapBusiException(OPERATION_TYPE_NOT_EXISTS);
        }

        LookupValue lookupValueEn = LookupValueHelper.getLookupValue(
                EN_US,
                LookupValueConstant.OPERATE_TYPE_ENUM,
                changeOrder.getOperationType());
        if (lookupValueEn == null) {
            throw new LcapBusiException(OPERATION_TYPE_NOT_EXISTS);
        }

        BatchInfo batchInfo = new BatchInfo();
        batchInfo.setBatchNo(batchNo);
        batchInfo.setChgOrderNo(changeOrder.getOrderNo());
        batchInfo.setOperationTypeNameZh(lookupValueZh.getMeaning());
        batchInfo.setOperationTypeNameEn(lookupValueEn.getMeaning());
        batchInfo.setNetworkElementCount(grantFileInfo.getNetworkElements().size());
        batchInfo.setStartTime(changeOrder.getTimeZone()
                .fix(batchTask.getPlanOperationStartTime()));
        batchInfo.setUserName(batchTask.getBatchOperationAccount());

        Date endTime = changeOrder.getTimeZone()
                .fix(batchTask.getPlanOperationEndTime());
        if (sa) {
            endTime = DateUtils.addHours(endTime, SA_END_DELAY_HOURS);
        }
        batchInfo.setEndTime(endTime);

        grantFileInfo.setOperationSummary(Lists.newArrayList(batchInfo));

        return grantFileInfo;
    }

    private static void sendSaGrantFile(
            IChangeOrder changeOrder,
            IBatchTask batchTask) {
        try {
            List<UcsUserInfo> userInfo = UcsClient.getUserInfo(
                    Lists.newArrayList(changeOrder.getCreateBy()));
            String operator = userInfo.get(0).getPersonName() + userInfo.get(0).getAccountId();

            //TODO feng 暂不考虑时区
            Date startTime = batchTask.getPlanOperationStartTime();
            String batchStartTime = new SimpleDateFormat(DATE_FORM).format(startTime);

            //noinspection unchecked
            String downloadUrl = String.format(
                    ConfigHelper.get(DOWNLOAD_URL_KEY),
                    true,
                    BatchTaskTypeEnum.fromEntityClass((Class<? extends BaseEntity>) batchTask.getClass()),
                    batchTask.getId());

            //TODO feng changeOrder.getSaGrantFileReceiver()没有值的情况要系统内消息

            EmailClient.sendMail(
                    TemplateIdEnum.DOWNLOAD_SA_GRANT_FILE,
                    changeOrder.getSaGrantFileReceiver(),
                    MapUtils.newHashMap(
                            "operator", operator,
                            "batchStartTime", batchStartTime,
                            "changeOrderSubject", changeOrder.getOperationSubject(),
                            "coNo", changeOrder.getOrderNo(),
                            "batchNo", batchTask.getBatchNo(),
                            "downloadUrl", downloadUrl));
            log.info("超级授权文件邮件：{}, 接收人：{}",
                    changeOrder.getOrderNo(),
                    JsonUtils.toJsonString(changeOrder.getSaGrantFileReceiver()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            //TODO feng 系统内消息
        }
    }

    private void notifyCreateFailed(String fileType, IChangeOrder changeOrder) {
        String sysAdmin = ConfigHelper.get(BUSI_SYSTEM_ADMIN_KEY);
        ArrayList<String> receivers = Lists.newArrayList(
                sysAdmin,
                changeOrder.getCreateBy());
        receivers.addAll(changeOrder.getSaGrantFileReceiver());

        List<UcsUserInfo> userInfo = UcsClient.getUserInfo(
                Lists.newArrayList(changeOrder.getCreateBy()));
        String operator = userInfo.get(0).getPersonName() + userInfo.get(0).getAccountId();

        //TODO feng 暂不考虑时区
        Date startTime = batchTask.getPlanOperationStartTime();
        String batchStartTime = new SimpleDateFormat(DATE_FORM).format(startTime);

        String sysAdminName = HrClient.queryEmployeeNameInfo(sysAdmin);

        EmailClient.sendMail(
                TemplateIdEnum.GRANT_FILE_CREATE_FAILED,
                receivers,
                MapUtils.newHashMap(
                        "fileType", fileType == null
                                ? FILE_TYPE_NORMAL + "/" + FILE_TYPE_SA
                                : fileType,
                        "operator", operator,
                        "batchStartTime", batchStartTime,
                        "changeOrderSubject", changeOrder.getOperationSubject(),
                        "sysAdmin", sysAdminName));

        //TODO feng 系统内消息
    }

    /**
     * 生成授权文件 - CCN 授权文件
     * @param application
     */
    public static String createCcnAuthorizationFile(CcnAuthorizationApplication application) {
        String authorizationFileKey = null;
        try {
            // 1.创建授权文件
            // 重试 3 次
            authorizationFileKey = RetryUtils.get(() -> createAndUploadFile(application), CREATE_RETRY_COUNT);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return authorizationFileKey;
    }

    /**
     * 创建授权文件 - CCN 授权文件
     * @param application
     * @return String
     */
    private static String createAndUploadFile(CcnAuthorizationApplication application) {
        // 1.创建原始文件
        // 原始文件为 Excel 格式
        byte[] originFile = createOriginFile(application);

        // 2.原始文件转换 .zlic 文件
        byte[] zlic = ZlicUtils.zlic(originFile, getSecretKey());

        // 3.授权文件命名 + 上传文档云
        return CloudDiskHelper.upload(newFilename(application), zlic);
    }

    /**
     * 创建原始文件 - CCN 授权文件
     * @param application
     * @return byte[]
     */
    @SneakyThrows
    private static byte[] createOriginFile(CcnAuthorizationApplication application) {
        GrantFileInfo grantFileInfo = new GrantFileInfo();

        // 1.包装授权文件信息
        // 由于授权文件格式与 批次任务授权文件 一致, 直接使用批次授权文件对象, 对象命名会存在部分出入

        // (1) 网络清单 NE_List 信息
        // ① Sheet 页表头 - 固定为 TaskID + ResourceID + ResourceName + ResourceType
        grantFileInfo.setNeFields(CCN_AUTHORIZATION_FILE_NE_LIST_FILED);

        // ② Sheet 页数据
        // 仅存在 1 行数据, 且除 ResourceID = 0, 其它属性为空,
        NetworkElementInfo networkElement = new NetworkElementInfo();
        networkElement.setBatchNo(INTEGER_ONE.toString());
        networkElement.setObjectId(INTEGER_ZERO.toString());
        networkElement.setObjectName(EMPTY_STRING);
        networkElement.setExtra(Lists.newArrayList(EMPTY_STRING));
        grantFileInfo.setNetworkElements(Lists.newArrayList(networkElement));

        // (2) 概要 Overview 信息
        // 表头与批次信息一致, 直接包装相关数据即可
        BatchInfo batchInfo = new BatchInfo();
        batchInfo.setBatchNo(INTEGER_ONE.toString());
        batchInfo.setNetworkElementCount(INTEGER_ZERO);

        batchInfo.setChgOrderNo(application.getOrderNo());
        batchInfo.setUserName(application.getOperationAccount());

        batchInfo.setOperationTypeNameZh(MsgUtils.getLangMessage(ZH_CN, ApplyCcnAuthorization.OPERATION_TYPE));
        batchInfo.setOperationTypeNameEn(MsgUtils.getLangMessage(EN_US, ApplyCcnAuthorization.OPERATION_TYPE));

        Date startTime = new Date();
        Date endTime = DateUtils.addYear(startTime, CommonConstants.INTEGER_FIFTY);
        batchInfo.setStartTime(startTime);
        batchInfo.setEndTime(endTime);

        grantFileInfo.setOperationSummary(Lists.newArrayList(batchInfo));

        // 2.写入 Excel 文件
        try (ByteArrayOutputStream os = new ByteArrayOutputStream();
             Workbook workbook = new XSSFWorkbook()) {
            GrantFileWriteAbility.write(grantFileInfo, workbook);

            workbook.write(os);
            return os.toByteArray();
        }
    }

    /**
     * 文件命名 - CCN 默认收取按文件
     * @param application
     * @return String
     */
    public static String newFilename(CcnAuthorizationApplication application) {
        // 1.区分 国内 / 国际 申请单
        DeptTypeEnum deptType = CcnAuthorizationApplicationAbility.getDeptType(application);

        // 2.获取特殊属性信息
        // (1) 区域名称
        String areaName = EMPTY_STRING;
        String language = DeptTypeEnum.INNER.equals(deptType) ? ZH_CN : EN_US;
        List<MultiLangText> areaInfo = DeptTypeEnum.INNER.equals(deptType) ? application.getProvince() : application.getCountry();
        if (!CollectionUtils.isEmpty(areaInfo)) {
            MultiLangText areaDetail = areaInfo.get(INTEGER_ZERO);
            String fullAreaName = areaDetail.getTextByLanguage(language);
            String[] areaNameArray = fullAreaName.split(FORWARD_SLASH);
            areaName = areaNameArray[areaNameArray.length - INTEGER_ONE];
        }

        // (2) 产品经营团队
        String teamAbbr = ProdTeamAbbrAbility.getAbbr(language, TextValuePairHelper.getValue(application.getProductClassification()));

        // 3.包装文件名
        String fmt = "%s-%s%s-%s-%s-%s.zlic";
        return String.format(
                fmt,
                application.getOrderNo(),
                application.getAccnType(),
                areaName,
                application.getOfficeName(),
                application.getOperationAccount(),
                teamAbbr);
    }

    /**
     * 创建异常提示 - CCN 默认授权文件
     * @param formView
     */
    public static void ccnNotifyCreateFailed(IFormView formView) {
        FormShowParameter formShowParameter = formView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setPageId(PageConstants.PAGE_WEB_CREATE_AUTHORIZATION_FILE_FAILED_POP_UP);
        formShowParameter.setBizObjectCode(PageConstants.PAGE_WEB_CREATE_AUTHORIZATION_FILE_FAILED_POP_UP);
        formShowParameter.setPageStatus(PageStatusEnum.VIEW);

        Map<String, Object> customParameters = new HashMap<>();
        customParameters.put(CommonConstants.HIDDEN_OPERATION, true);
        customParameters.put(CommonConstants.FULL_SCREEN, false);
        customParameters.put(CommonConstants.OPEN_TYPE, OpenTypeEnum.POPUP.getValue());

        PopUpSizeDTO.PopUpSizeDTOBuilder popupSize= PopUpSizeDTO.builder();
        popupSize.height(200);
        popupSize.width(500);
        popupSize.unit("px");
        customParameters.put("popupSize", popupSize.build());

        formShowParameter.setCustomParameters(customParameters);
        formView.showForm(formShowParameter);
    }
}
