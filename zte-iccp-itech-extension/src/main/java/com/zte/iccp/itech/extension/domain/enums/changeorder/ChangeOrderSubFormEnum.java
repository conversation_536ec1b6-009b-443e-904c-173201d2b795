package com.zte.iccp.itech.extension.domain.enums.changeorder;


import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.utils.AiParamUtils;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Triple;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.function.Function;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORM;

/**
 * <AUTHOR>
 * @date 2024/6/18 下午7:43
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ChangeOrderSubFormEnum {
    /**
     * 多产品联动保障
     */
    MULTI_PRODUCT_LINKAGE_GUARANTEE("multi_product_linkage_guarantee", "multi_product_linkage_guarantee_cid",
            Lists.newArrayList(
                    Triple.of("product_class","ServiceObject_product_class", object -> ((DynamicDataEntity) object).getString("longname")),
                    Triple.of("assignment_id","BasicData_assignment_id", object -> ((DynamicDataEntity) object).getString("pk_25049603")),
                    Triple.of("assignment_name","BasicDataProps_assignment_name", Object::toString),
                    Triple.of("plan_start_time","BasicDataProps_plan_start_time", Object::toString),
                    Triple.of("implementation_responsible_person", "EmployeeField_implementation_responsible_person", AiParamUtils::getEmployeeNameString),
                    Triple.of("assignment_status","BasicDataProps_assignment_status", Object::toString),
                    Triple.of("current_progress","TextField_current_progress", Object::toString),
                    Triple.of("current_handler","TextField_current_handler", Object::toString))),

    /**
     * 批次概要
     */
    BATCH_SUMMARY("batch_summary", "operation_object_batch_summary",
            Lists.newArrayList(
                    Triple.of("batch_no","batch_summary_batch_no", AiParamUtils::getAllTextNameString),
                    Triple.of("plan_operation_start_time","cid_plan_operation_start_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
                    Triple.of("plan_operation_end_time","cid_plan_operation_end_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
                    Triple.of("operation_account","operation_account", Object::toString),
                    Triple.of("ne_count","batch_summary_ne_count", Object::toString),
                    Triple.of("operation_description", "TextareaField_78r0781l", Object::toString))),

    /**
     * 操作对象
     */
    OPERATION_OBJECT("operation_object", "operation_object_order",
            Lists.newArrayList(
                    Triple.of("batch_info_no","operation_object_batch_no", AiParamUtils::getAllTextNameString),
                    Triple.of("network_id", "operation_object_network_id", object -> ((DynamicDataEntity) object).getString("CustomerNetworkName")),
                    Triple.of("office_name", "operation_object_office_name", Object::toString),
                    Triple.of("product_model", "operation_object_product_model", object -> ((DynamicDataEntity) object).getString("product_model")),
                    Triple.of("current_version_text_field", "current_version_text_field", Object::toString),
                    Triple.of("target_version_text_field", "target_version_text_field", Object::toString),
                    Triple.of("current_version", "operation_object_current_version", object -> ((DynamicDataEntity) object).getString("version_name")),
                    Triple.of("target_version", "operation_object_target_version", object -> ((DynamicDataEntity) object).getString("version_name")))),

    /**
     * 操作人员
     */
    OPERATOR_TABLE("operator_table", "operator",
            Lists.newArrayList(
                    Triple.of("operator_role","SelectField_2rdh3cp1", AiParamUtils::getAllTextNameString),
                    Triple.of("is_remote", "RadioField_8ecc1z7y", AiParamUtils::getAllTextNameString),
                    Triple.of("operator_batch_no", "operator_object_batch_no", AiParamUtils::getAllTextNameString),
                    Triple.of("task_desc", "TextField_lmj97cr2", Object::toString),
                    Triple.of("operator_name", "EmployeeField_u8uiu4um", AiParamUtils::getEmployeeNameString),
                    Triple.of("operator_department", "TextField_hw1jhqh7", Object::toString),
                    Triple.of("operator_phone", "TextField_xyajxcqa", Object::toString),
                    Triple.of("operator_account", "operator_account", AiParamUtils::getAllTextNameString))),

    /**
     * 核心网产品外场质量保证规范动作
     */
    STANDARD_ACTION_CHECK("standard_action_check", "standard_action_check",
            Lists.newArrayList(
                    Triple.of("standard_action_check_type","standard_action_check_type", Object::toString),
                    Triple.of("standard_action_check_content", "standard_action_check_content", Object::toString),
                    Triple.of("standard_action_check_result", "standard_action_check_result", AiParamUtils::getAllTextNameString),
                    Triple.of("standard_action_check_result_desc", "standard_action_check_result_desc", Object::toString))),

    /**
     * 技术方案检查
     */
    TECH_SOLUTION_CHECK("tech_solution_check", "tech_solution_check",
            Lists.newArrayList(
                    Triple.of("tech_solution_check_content","tech_solution_check_content", Object::toString),
                    Triple.of("tech_solution_check_result", "tech_solution_check_result", AiParamUtils::getAllTextNameString),
                    Triple.of("tech_solution_check_result_desc", "tech_solution_check_result_desc", Object::toString))),

    /**
     * 操作阶段打卡
     */
    OPERATION_STAGE_CHECK_IN_TABLE("operation_stage_check_in_table", "operation_phase_check",
            Lists.newArrayList(
                    Triple.of("operation_phase","operation_phase", Object::toString),
                    Triple.of("operation_duration", "operation_duration", Object::toString),
                    Triple.of("stage_start_time", "operation_phase_start_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
                    Triple.of("stage_end_time", "operation_phase_end_time",r -> new SimpleDateFormat(DATE_FORM).format(r)),
                    Triple.of("stage_check_in_person", "stage_check_in_person", Object::toString))),


    /**
     * 高危指令表
     */
    HIGH_RISK_INSTRUCTION_TABLE("high_risk_instruction_table", "TableFieldNew_8y1enit3",
            Lists.newArrayList(
                    Triple.of("high_risk_instruction", "high_risk_instruction", Object::toString))),



    ;


    //实体表唯一标识
    private final String propertyKey;

    //布局唯一标识
    private final String cid;

    //子表单所有字段集合
    private final List<Triple<String, String, Function<Object, String>>> fieldCidKeys;
}
