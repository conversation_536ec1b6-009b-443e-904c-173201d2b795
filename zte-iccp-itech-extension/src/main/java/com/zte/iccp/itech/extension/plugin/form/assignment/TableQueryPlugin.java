package com.zte.iccp.itech.extension.plugin.form.assignment;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.constant.RedisKeys;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicEmployeeInfo;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.paas.lcap.common.spring.ApplicationContextHolder;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.SearchEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.dto.DataAndCountResult;
import com.zte.paas.lcap.ddm.common.cache.DynamicRedisCache;
import com.zte.paas.lcap.ddm.common.constant.AsyncTaskTypeEnum;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import com.zte.paas.lcap.ddm.domain.control.prop.TablePropertyConstants;
import com.zte.paas.lcap.ddm.domain.helper.orm.BaseQueryDataHelper;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import com.zte.paas.lcap.metadata.engine.common.util.IdGenerator;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.HR_PERSON_COMPANY_TYPE_PARTNER_EN;
import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.HR_PERSON_COMPANY_TYPE_PARTNER_ZH;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.TABLE_QEURY_POOL_BEAN_NAME;

@Slf4j
public class TableQueryPlugin extends BaseFormPlugin {

    /** 最早创建时间 */
    private static final String START_DATE = "assignment.firstOrderCreateTime";

    /** 最大截取长度 */
    private static final String MAX_SUB_LENGTH = "assignment.maxSubLength";

    /** 主表缩略名 */
    private static final String MAIN_ABBREVIATION = "a.";

    /** 扩展表缩略名 - 网络变更 */
    private static final String NETWORK_CHANGE_ABBREVIATION = "ance.";

    /** 任务编码前缀 - 网络变更任务 */
    private static final String NETWORK_CHANGE_CODE_PREFIX = "CO";

    /** 任务编码前缀 - 合作方网络变更任务 */
    private static final String PARTNER_NETWORK_CHANGE_CODE_PREFIX = "HZF";

    @Override
    public Optional<DataAndCountResult> queryPageAndCount(SearchEvent event) {
        // 展开子列表不走自定义 SQL
        Map<String, Object> args = event.getJsonArgs();
        if (!CollectionUtils.isEmpty(args) && args.containsKey("parentId")) {
            return Optional.empty();
        }

        IFormView formView = getView();
        IDataModel dataModel = getModel();

        try {
            // 1.获取表格分页参数
            TablePc tableInfo = (TablePc) formView.getControl(CidConstants.TABLE_ASSIGNMENT_CID);
            int currentPage = tableInfo.getCurrentPage();
            int pageSize = tableInfo.getPageSize();

            // 2.拼接检索 SQL
            Pair<String, String> sqlPair = assignmentCenterNetworkChangeSql(
                    dataModel, formView.getPageId(), currentPage, pageSize);
            String querySql = sqlPair.getLeft();
            String countSql = sqlPair.getRight();

            // 3.并发检索分页数据和总记录数
            // 表格按异步逻辑加载总记录数
            Executor pool = ApplicationContextHolder.getBean(TABLE_QEURY_POOL_BEAN_NAME, Executor.class);
            String totalAsyncTaskId = AsyncTaskTypeEnum.TotalAsyncTask.name() + IdGenerator.nextId();

            // 并发查询列表分页数据和总记录数
            CompletableFuture<List<JSONObject>> dataFuture = CompletableFuture.supplyAsync(
                    () -> getData(querySql), pool);
            CompletableFuture.supplyAsync(() -> getCount(totalAsyncTaskId, countSql), pool);

            DataAndCountResult dataAndCountResult = new DataAndCountResult(
                    dataFuture.get(), null, true, totalAsyncTaskId);
            return Optional.of(dataAndCountResult);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SneakyThrows
    private int getCount(String totalAsyncTaskId, String countSql) {
        // 1.缓存检索
        // 查询 SQL SHA256 加密，构建缓存 Key
        String cacheKey = RedisKeys.entityCount(
                EntityHelper.getMainEntityType(Assignment.class), countSql);
        Integer total = CacheUtils.get(cacheKey, Integer.class);
        if (Objects.isNull(total)) {
            // 2.缓存不存在，则实时查询
            Date startDate = DateUtils.stringToDate(ConfigHelper.get(START_DATE), CommonConstants.DATE_FORMAT);
            total = AssignmentAbility.asyncStepCount(countSql, startDate);

            // 系统缓存
            CacheUtils.set(cacheKey, total, 60 * 60);
        }

        // 如果表格开启了异步加载总记录数的功能，则按异步逻辑加载总记录数，要把总记录数放到平台缓存里，才可展示
        JSONObject result = new JSONObject();
        JSONObject data = new JSONObject();
        data.put(TablePropertyConstants.TOTAL, total);
        result.put(TablePropertyConstants.DATA, data);
        SpringContextUtil.getBean(DynamicRedisCache.class).set(totalAsyncTaskId, result, 10 * 60);

        return total;
    }

    private List<JSONObject> getData(String querySql) {
        List<JSONObject> data = BaseQueryDataHelper.query(querySql, new HashMap<>());

        List<String> assignmentIds = data.stream()
                .map(item -> item.getString("pk_25049603"))
                .collect(Collectors.toList());
        Map<String, Integer> hasChildren = AssignmentAbility.checkHasChildren(assignmentIds);

        // todo datakey 后续需要做到 DataKeyEnum
        data.forEach(item -> {
            item.put(CommonConstants.KEY_HAS_CHILDREN, hasChildren.get(item.getString("pk_25049603")));
            item.put("countryselectfield_sl2djn22", JSON.parseArray(item.getString("countryselectfield_sl2djn22")));
            item.put("selectfield_aabarr58", JSON.parseArray(item.getString("selectfield_aabarr58")));
            item.put("selectfield_8py0fmq0", JSON.parseArray(item.getString("selectfield_8py0fmq0")));
            item.put("selectfield_naxfeulg", JSON.parseArray(item.getString("selectfield_naxfeulg")));
            item.put("selectfield_g8zff5lp", JSON.parseArray(item.getString("selectfield_g8zff5lp")));
            item.put("selectfield_whzpsb1p", JSON.parseArray(item.getString("selectfield_whzpsb1p")));
            item.put("selectfield_nh1a3kqb", JSON.parseArray(item.getString("selectfield_nh1a3kqb")));
            item.put("multiselectfield_h0vnnqgf", JSON.parseArray(item.getString("multiselectfield_h0vnnqgf")));
            item.put("multiselectfield_s75ta5kh", JSON.parseArray(item.getString("multiselectfield_s75ta5kh")));
        });

        return data;
    }

    private Pair<String, String> assignmentCenterNetworkChangeSql(
            IDataModel dataModel,
            String pageId,
            int pageNum,
            int pageSize) {

        // 1.权限 SQL
        String userId = ContextHelper.getEmpNo();
        String uppSql = uppSql(userId);
        String relevanceSql = relevanceSql(userId);
        String permissionSql = StringUtils.hasText(uppSql)
                ? String.format(" AND (%s OR %s)",  uppSql, relevanceSql)
                : String.format(" AND %s", relevanceSql);

        // 2.高级查询 SQL
        String conditionSql = queryConditionSql(dataModel, pageId);

        // 3.拼接 数据检索 SQL + 计数 SQL
        String dataSql = networkChangeSelectFieldSql() + permissionSql + conditionSql + orderSql() + limitSql(pageNum, pageSize);
        String countSql = networkChangeSelectCountSql() + permissionSql + conditionSql;
        return Pair.of(dataSql, countSql);
    }

    /**
     * 组件 UPP 检索 SQL
     */
    private String uppSql(String userId) {
        // 1.检索用户 UPP 权限
        Pair<List<String>, List<String>> constraintPair = AssignmentAbility.getUppDataConstraint(userId);
        List<String> organizationConstraint = constraintPair.getLeft();
        List<String> productConstraint = constraintPair.getRight();
        if (CollectionUtils.isEmpty(organizationConstraint) || CollectionUtils.isEmpty(productConstraint)) {
            return "";
        }

        // 2.产品经营团队格式化
        // 组织序列化在 合作方 / 内部条件中
        List<String> productIds = productConstraint.stream()
                .map(item -> String.format("'%s'", item))
                .collect(Collectors.toList());

        // 3.用户授权
        BasicEmployeeInfo basicEmployeeInfo = HrClient.getEmployeeInfo(userId);
        boolean partnerFlag = HR_PERSON_COMPANY_TYPE_PARTNER_ZH.equals(basicEmployeeInfo.getCompanyType())
                || HR_PERSON_COMPANY_TYPE_PARTNER_EN.equals(basicEmployeeInfo.getCompanyType());
        return partnerFlag
                ? partnerUserSql(userId, organizationConstraint, productIds, basicEmployeeInfo.getOrgID())
                : internalUserSql(organizationConstraint, productIds);
    }

    /**
     * 用户授权条件 - 内部用户
     */
    private String internalUserSql(
            List<String> organizationConstraint,
            List<String> productConstraint) {

        // 1.条件格式化
        List<String> organizationIds = organizationConstraint.stream()
                .map(ResponsibleUtils::getResponsible)
                .filter(StringUtils::hasText)
                .map(item -> String.format("'%s'", item))
                .distinct()
                .collect(Collectors.toList());

        // 2.包装数据授权条件
        if (CollectionUtils.isEmpty(organizationIds)) {
            return "";
        }

        return String.format(" a.representative_office_text IN (%s) AND a.product_team IN (%s)",
                String.join(CommonConstants.COMMA, organizationIds),
                String.join(CommonConstants.COMMA, productConstraint));
    }

    /**
     * 用户授权条件 - 合作方用户
     */
    private String partnerUserSql(
            String userId,
            List<String> organizationConstraint,
            List<String> productConstraint,
            String companyId) {

        // 1.条件格式化 - 代表处
        Pair<List<String>, List<String>> organizationPair
                = AssignmentAbility.distinguishInternationalAndDomesticOrganization(
                        userId, organizationConstraint);
        List<String> domestic = organizationPair.getLeft();
        List<String> international = organizationPair.getRight();

        // 2.包装授权条件 - 无有效代表处数据
        if (CollectionUtils.isEmpty(domestic) && CollectionUtils.isEmpty(international)) {
            return "";
        }

        String domesticStr = domestic.stream()
                .map(item -> String.format("'%s'", item))
                .collect(Collectors.joining(CommonConstants.COMMA));
        String internationalStr = international.stream()
                .map(item -> String.format("'%s'", item))
                .collect(Collectors.joining(CommonConstants.COMMA));

        // 3.包装授权条件 - 全量国际代表处，无国内代表处
        if (CollectionUtils.isEmpty(domestic)) {
            return String.format(" a.representative_office_text IN (%s) AND a.product_team IN (%s)",
                    internationalStr,
                    String.join(CommonConstants.COMMA, productConstraint));
        }

        // 4.包装授权条件 - 全量国内代表处，无国际代表处
        if (CollectionUtils.isEmpty(international)) {
            return String.format(" a.representative_office_text IN (%s) AND a.product_team IN (%s) AND JSON_OVERLAPS(a.company_ext, '[\"%s\"]')",
                    domesticStr,
                    String.join(CommonConstants.COMMA, productConstraint),
                    companyId);
        }

        // 5.包装授权条件 - 同时有国内代表处 + 国际代表处
        return String.format(" ((a.representative_office_text IN (%s) AND a.product_team IN (%s)) "
                        + "OR (a.representative_office_text IN (%s) AND a.product_team IN (%s) AND JSON_OVERLAPS(a.company_ext, '[\"%s\"]')))",
                internationalStr,
                String.join(CommonConstants.COMMA, productConstraint),
                domesticStr,
                String.join(CommonConstants.COMMA, productConstraint),
                companyId);
    }

    /**
     * 责任人关系 SQL
     */
    private String relevanceSql(String userId) {
        return String.format(" a.id IN" +
                " (SELECT apr.assignment_id" +
                " FROM assignment_person_relevance apr" +
                " WHERE apr.relevant = '%s'" +
                " AND apr.tenant_id = '10001'" +
                " AND apr.is_deleted = 0)", userId);
    }


    private String queryConditionSql(IDataModel dataModel, String pageId) {

        return getAssignmentNameCondition(dataModel)
                + getAssignmentCodeCondition(dataModel, pageId)
                + getAssignmentStatusCondition(dataModel, true)
                + getOrganizationCondition(dataModel)
                + getProductCondition(dataModel)
                + getEmployeeCondition(
                        dataModel,
                        CidConstants.COMPONENT_CREATED_BY_CID,
                        MAIN_ABBREVIATION + CommonFieldConsts.CREATE_BY)
                + getDateCondition(
                        dataModel,
                        CidConstants.COMPONENT_CREATED_DATE_CID,
                        MAIN_ABBREVIATION + CommonFieldConsts.CREATE_TIME)
                + getOperationStartTime(
                        dataModel,
                        NetworkChangeFieldConsts.PLAN_START_TIME,
                        NetworkChangeFieldConsts.PLAN_START_TIME)
                + getOperationStartTime(
                        dataModel,
                        NetworkChangeFieldConsts.OPERATION_START_TIME_UTC_8,
                        NetworkChangeFieldConsts.OPERATION_START_TIME_UTC_8)
                + getDropDownCondition(
                        dataModel,
                        NetworkChangeFieldConsts.OPERATION_TYPE,
                        NETWORK_CHANGE_ABBREVIATION + "operation_type_text",
                        false)
                + getDropDownCondition(
                        dataModel,
                        NetworkChangeFieldConsts.COUNTRY,
                        NETWORK_CHANGE_ABBREVIATION + "country_text",
                        false)
                + getDropDownCondition(
                        dataModel,
                        CUSTOMER_CLASSIFICATION,
                        MAIN_ABBREVIATION + "customer_classification_ext",
                        true)
                + getDropDownCondition(
                        dataModel,
                        NetworkChangeFieldConsts.OPERATION_REASON,
                        NETWORK_CHANGE_ABBREVIATION + "operation_reason_text",
                        false)
                + getDropDownCondition(
                        dataModel,
                        NetworkChangeFieldConsts.OPERATION_RESULT,
                        NETWORK_CHANGE_ABBREVIATION + "operation_result_text",
                        false)
                + getCurrentProgress(dataModel)
                + getDropDownCondition(
                        dataModel,
                        NetworkChangeFieldConsts.IMPORTANCE,
                        NETWORK_CHANGE_ABBREVIATION + "importance_text",
                        false)
                + getDropDownCondition(
                        dataModel,
                        NetworkChangeFieldConsts.RISK_EVALUATION,
                        NETWORK_CHANGE_ABBREVIATION + "risk_evaluation_text",
                        false)
                + getDropDownCondition(
                        dataModel,
                        NetworkChangeFieldConsts.OPERATION_LEVEL,
                        NETWORK_CHANGE_ABBREVIATION + "operation_level_text",
                        false)
                + getDropDownCondition(
                        dataModel,
                        NETWORK,
                        MAIN_ABBREVIATION + "network_ext",
                        true)
                + getDropDownCondition(
                        dataModel,
                        NetworkChangeFieldConsts.OFFICE_NAME,
                        NETWORK_CHANGE_ABBREVIATION + "office_name_ext",
                        true);
    }

    /**
     * 字段检索 SQL
     */
    private String networkChangeSelectFieldSql() {
        return "SELECT"
                + " a.id AS pk_25049603,"
                + " a.assignment_code AS textfield_81vpnxkw,"
                + " a.assignment_name AS textfield_tz852y9c,"
                + " ance.time_zone AS fastcodefield_juubvu9p,"
                + " ance.plan_start_time AS datefield_4oxj00ma,"
                + " ance.operation_start_time_utc_8 AS datefield_l798o1aw,"
                + " ance.country AS countryselectfield_sl2djn22,"
                + " ance.operation_type AS selectfield_aabarr58,"
                + " ance.operation_reason AS selectfield_8py0fmq0,"
                + " ance.operation_result AS selectfield_naxfeulg,"
                + " ance.operation_level AS selectfield_g8zff5lp,"
                + " ance.risk_evaluation AS selectfield_whzpsb1p,"
                + " ance.importance AS selectfield_nh1a3kqb,"
                + " ance.office_name AS multiselectfield_h0vnnqgf,"
                + " a.customer_classification AS multiselectfield_s75ta5kh,"
                + " a.assignment_status AS fastcodefield_cov2v4j6,"
                + " a.current_processor_employee_field AS employeefield_whz8g82l,"
                + " a.responsible_employee_field AS employeefield_z6go94qd,"
                + " a.create_by AS node_95415043,"
                + " a.create_time AS node_95415044,"
                + " a.last_modified_by AS node_95415045,"
                + " a.last_modified_time AS node_95415046"
                + " FROM assignment a"
                + " INNER JOIN assignment_network_change_ex ance ON a.id = ance.id"
                + " WHERE a.is_deleted = 0"
                + " AND a.tenant_id = 10001"
                + " AND a.assignment_type_text IN ('1', '6')";
    }

    /**
     * 字段检索 SQL
     */
    private String networkChangeSelectCountSql() {
        return "SELECT COUNT(1)"
                + " FROM assignment a"
                + " INNER JOIN assignment_network_change_ex ance ON a.id = ance.id"
                + " WHERE a.is_deleted = 0"
                + " AND a.tenant_id = 10001"
                + " AND a.assignment_type_text IN ('1', '6')";
    }

    /**
     * 排序 SQL
     */
    private String orderSql() {
        return " ORDER BY a.create_time DESC";
    }

    /**
     * 分页限制 SQL
     */
    private String limitSql(int pageNum, int pageSize) {
        return String.format(" LIMIT %d OFFSET %d", pageSize, (pageNum - 1) * pageSize);
    }

    /**
     * 高级查询条件 - 任务名称
     */
    private String getAssignmentNameCondition(IDataModel dataModel) {
        String nameInfo = PropertyValueConvertUtil.getString(dataModel.getValue(ASSIGNMENT_NAME));
        if (!StringUtils.hasText(nameInfo)) {
            return "";
        }

        int concatLength = Integer.parseInt(ConfigHelper.get(MAX_SUB_LENGTH));
        String formatName = SqlUtils.escapeSqlForLike(
                nameInfo.length() > concatLength ? nameInfo.substring(0, concatLength) : nameInfo,
                "%", "_", "#", "--", "\\", ";", "=");

        return String.format(
                " AND a.bill_id IN"
                        + " (SELECT DISTINCT bill_id"
                        + " FROM assignment"
                        + " WHERE MATCH(assignment_name) AGAINST ('%s')"
                        + " AND assignment_name like '%%%s%%'"
                        + " AND is_deleted = 0"
                        + " AND tenant_id = 10001)",
                formatName,
                nameInfo);
    }

    /**
     * 高级查询条件 - 任务编码
     */
    private String getAssignmentCodeCondition(
            IDataModel dataModel,
            String pageId) {

        String codeInfo = PropertyValueConvertUtil.getString(dataModel.getValue(ASSIGNMENT_CODE));
        if (!StringUtils.hasText(codeInfo)) {
            return "";
        }

        // 1.校验是否带特殊前缀 - 网络变更
        // 网络变更任务需处理兼容批次
        String formatCode = SqlUtils.escapeSqlForLike(codeInfo, "%", "_", "#", "--", "\\", ";", "=");
        formatCode = formatCode.toUpperCase();
        boolean networkChangePrefixFlag = formatCode.startsWith(NETWORK_CHANGE_CODE_PREFIX)
                || formatCode.startsWith(PARTNER_NETWORK_CHANGE_CODE_PREFIX);
        if (networkChangePrefixFlag) {
            return String.format(
                    " AND a.bill_id IN"
                            + " (SELECT DISTINCT bill_id"
                            + " FROM assignment"
                            + " WHERE assignment_code LIKE '%s%%'"
                            + " AND is_deleted = 0)",
                    formatCode);
        }

        // 2.不带前缀
        // 先模糊检索 bill_id，然后去重，数量较少直接 IN，数量较多使用子查询
        // 注意：根据页签补充前缀
        String subQuerySql;
        switch (pageId) {
            case PageConstants.PAGE_WEB_NETWORK_CHANGE_ASSIGNMENT:
                subQuerySql = String.format("SELECT bill_id"
                                + " FROM assignment"
                                + " WHERE assignment_code LIKE 'CO%%%s%%' OR 'HZF%%%s%%'",
                        formatCode,
                        formatCode);
                break;

            default:
                return "";
        }

        // 检索 bill_id，内存去重，数据库去重较慢
        List<JSONObject> billIdInfo = BaseQueryDataHelper.query(subQuerySql + " Limit 500", new HashMap<>());
        List<String> billIds = billIdInfo.stream()
                .map(item -> item.getString(BILL_ID))
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        // 阈值初步设定为 200（过多会导致检索缓慢，以及 SQL 超长）
        String condition;
        if (CollectionUtils.isEmpty(billIds)) {
            condition = "'System'";
        } else if (billIds.size() <= 200) {
            condition = billIds.stream()
                    .map(item -> String.format("'%s'", item))
                    .collect(Collectors.joining(CommonConstants.COMMA));
        } else {
            condition = subQuerySql;
        }
        return String.format(" AND a.bill_id IN (%s)", condition);
    }

    /**
     * 高级查询条件 - 任务状态
     */
    private String getAssignmentStatusCondition(
            IDataModel dataModel,
            Boolean extraDraftFlag) {

        // 1.获取 任务状态 组件填写数据
        List<TextValuePair> assignmentStatusInfo
                = ComponentUtils.getChooseComponentInfo(dataModel, ASSIGNMENT_STATUS);
        if (CollectionUtils.isEmpty(assignmentStatusInfo)) {
            return "";
        }

        // 2.枚举校验 - 全部
        List<String> assignmentStatuses = TextValuePairHelper.getValueList(assignmentStatusInfo);
        List<String> formatStatuses = assignmentStatuses.stream()
                .map(SqlUtils::escapeSqlForLike)
                .collect(Collectors.toList());
        for (String assignmentStatusValue : formatStatuses) {
            AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignmentStatusValue);
            if (Objects.isNull(assignmentStatus)) {
                return "";
            }
        }

        // 3.【待启动】根据标识选择补充【审批待启动】状态
        if (extraDraftFlag
                && formatStatuses.contains(AssignmentStatusEnum.START.getValue())) {
            formatStatuses.add(AssignmentStatusEnum.APPROVE_START.getValue());
        }

        return String.format(
                " AND a.assignment_status IN (%s)",
                assignmentStatuses.stream()
                        .map(item -> String.format("'%s'", item))
                        .collect(Collectors.joining(CommonConstants.COMMA)));
    }

    /**
     * 高级查询条件 - 组织（可根据父级获取子级）
     */
    private String getOrganizationCondition(IDataModel dataModel) {

        // 1.获取 组织 组件填写数据
        List<TextValuePair> organizationInfo = ComponentUtils.getChooseComponentInfo(
                dataModel, CidConstants.COMPONENT_ORGANIZATION_TREE_CID);
        if (CollectionUtils.isEmpty(organizationInfo)) {
            return "";
        }

        // 3.转换为叶子节点（代表处）数据
        Set<String> leafPaths = new HashSet<>();
        organizationInfo.forEach(item -> leafPaths.addAll(
                ResponsibleUtils.getOrganizationLeafNodePath(
                        NisClient.queryOrganizationTree(),
                        SqlUtils.escapeSqlForLike(item.getValue()),
                        true,
                        OrganizationTreeVo::getHrOrgId)));

        return String.format(
                " AND a.representative_office_text IN (%s)",
                leafPaths.stream()
                        .map(item -> String.format("'%s'", item))
                        .collect(Collectors.joining(CommonConstants.COMMA)));
    }

    /**
     * 高级查询条件 - 产品（可根据父级获取子级）
     */
    private String getProductCondition(IDataModel dataModel) {

        // 1.获取 产品 组件填写数据
        List<TextValuePair> productInfo = ComponentUtils.getChooseComponentInfo(
                dataModel, CidConstants.COMPONENT_PRODUCT_TREE_CID);
        if (CollectionUtils.isEmpty(productInfo)) {
            return "";
        }

        // 2.转换为叶子节点（产品分类）数据
        Set<String> leafPaths = new HashSet<>();
        productInfo.forEach(item -> leafPaths.addAll(
                ProductUtils.getProductClassificationLeafNodePath(
                        NisClient.queryProductTree(CommonConstants.LEVEL_PRODUCT_SUBCLASS),
                        SqlUtils.escapeSqlForLike(item.getValue()),
                        true)));
        return String.format(
                " AND JSON_OVERLAPS(a.product_classification_ext, '[%s]')",
                leafPaths.stream()
                        .map(item -> String.format("\"%s\"", item))
                        .collect(Collectors.joining(CommonConstants.COMMA)));
    }

    /**
     * 高级查询条件 - 当前进展（兼容查询批次任务）
     */
    public String getCurrentProgress(IDataModel dataModel) {
        // 1.获取 当前进展 组件填写数据
        List<String> currentProgresses = TextValuePairHelper.getValueList(
                ComponentUtils.getChooseComponentInfo(dataModel, CURRENT_PROGRESS));
        if (CollectionUtils.isEmpty(currentProgresses)) {
            return "";
        }

        // 2.包装查询条件
        return String.format(" AND a.bill_id IN"
                + " (SELECT DISTINCT bill_id"
                + " FROM assignment"
                + " WHERE current_progress IN (%s) and is_deleted = 0)",
                currentProgresses.stream()
                        .map(SqlUtils::escapeSqlForLike)
                        .map(item -> String.format("'%s'", item))
                        .collect(Collectors.joining(CommonConstants.COMMA)));
    }

    /**
     * 高级查询 - 操作开始时间（兼容批次任务查询）
     */
    public String getOperationStartTime(
            IDataModel dataModel,
            String componentId,
            String fieldId) {

        Pair<Date, Date> dateSection
                = ComponentUtils.getDateSectionComponentInfo(dataModel, componentId);
        if (Objects.isNull(dateSection.getLeft()) || Objects.isNull(dateSection.getRight())) {
            return "";
        }

        return String.format(" AND a.bill_id IN"
                        + " (SELECT DISTINCT a1.bill_id"
                        + "  FROM assignment_network_change_ex ance1"
                        + "  INNER JOIN assignment a1 ON ance1.id = a1.id"
                        + "  WHERE ance1.%s >= '%s' and ance1.%s < '%s'"
                        + "  AND a1.is_deleted = 0)",
                fieldId, DateUtils.dateToString(dateSection.getLeft(), CommonConstants.DATE_FORM),
                fieldId, DateUtils.dateToString(
                        DateUtils.addDay(dateSection.getRight(), 1),
                        CommonConstants.DATE_FORM));
    }

    /**
     * 高级查询条件 - 用户类
     * 注：1.fieldId 格式：表缩略名 + 字段名
     *    2.AND 前已加空格
     */
    private String getEmployeeCondition(
            IDataModel dataModel,
            String componentId,
            String fieldId) {

        List<Employee> employeeInfo
                = ComponentUtils.getEmployeeComponentInfo(dataModel, componentId);
        if (CollectionUtils.isEmpty(employeeInfo)) {
            return "";
        }

        List<String> employeeIds = employeeInfo.stream()
                .map(Employee::getEmpUIID)
                .map(SqlUtils::escapeSqlForLike)
                .distinct()
                .collect(Collectors.toList());
        return String.format(" AND %s IN (%s)",
                fieldId,
                employeeIds.stream()
                        .map(item -> String.format("'%s'", item))
                        .collect(Collectors.joining(CommonConstants.COMMA)));
    }

    /**
     * 高级查询条件 - 时间区间类
     * 注：1.fieldId 格式：表缩略名 + 字段名
     *    2.AND 前已加空格
     */
    private String getDateCondition(
            IDataModel dataModel,
            String componentId,
            String fieldId) {

        Pair<Date, Date> dateSection
                = ComponentUtils.getDateSectionComponentInfo(dataModel, componentId);
        if (Objects.isNull(dateSection.getLeft()) || Objects.isNull(dateSection.getRight())) {
            return "";
        }

        return String.format(" AND (%s >= '%s' AND %s < '%s')",
                fieldId, DateUtils.dateToString(dateSection.getLeft(), CommonConstants.DATE_FORM),
                fieldId, DateUtils.dateToString(
                        DateUtils.addDay(dateSection.getRight(), 1),
                        CommonConstants.DATE_FORM));
    }

    /**
     * 高级查询条件 - 下拉类
     * 注：1.fieldId 格式：表缩略名 + 字段名
     *    2.AND 前已加空格
     */
    private String getDropDownCondition(
            IDataModel dataModel,
            String componentId,
            String fieldId,
            boolean jsonFieldFlag) {

        List<TextValuePair> componentInfo
                = ComponentUtils.getChooseComponentInfo(dataModel, componentId);
        if (CollectionUtils.isEmpty(componentInfo)) {
            return "";
        }

        // JSON 格式示例：and JSON_OVERLAPS(assignment_type_ext, '["1", "6"]')
        // 文本 格式示例：and assignment_type_text in ('1', '6')
        List<String> values = TextValuePairHelper.getValueList(componentInfo);
        String value = values.stream()
                .map(SqlUtils::escapeSqlForLike)
                .map(item -> String.format(jsonFieldFlag ? "\"%s\"" : "'%s'", item))
                .collect(Collectors.joining(CommonConstants.COMMA));
        return String.format(
                jsonFieldFlag ? " AND JSON_OVERLAPS(%s, '[%s]')" : " AND %s IN (%s)",
                fieldId, value);
    }
}
