package com.zte.iccp.itech.extension.openapi.model.reportpush;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/6/5 上午11:13
 */
@Setter
@Getter
public class GlobalDetailVO {
    /**
     * 操作单号
     */
    private String batchCode;

    /**
     * 操作时间
     */
    private String planOperationStartTime;

    /**
     * 代表处
     */
    private String representativeOffice;

    /**
     * 片区
     */
    private String marketing;

    /**
     * 产品小类
     */
    private String productSubCategory;

    /**
     * 操作主题
     */
    private String operationSubject;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作说明
     */
    private String operationDesc;

    /**
     * 现场人员
     */
    private String sitePerson;

    /**
     * 网服支持人员
     */
    private String networkSupportStaff;

    /**
     * 研发支持人员
     */
    private String developmentSupportStaff;

    /**
     * 产品线
     */
    private String prodLine;

    /**
     * 产品线idPath
     */
    private String prodLineIdPath;

    /**
     * 产品分类idpath
     */
    private String productPath;

    /**
     * 风险等级
     */
    private String riskLevel;
}
