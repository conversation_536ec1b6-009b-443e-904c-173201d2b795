package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.authtask.BatchTask4AuthAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.domain.enums.authtask.CancelSceneEnum;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
/**
 * <AUTHOR>
 * @since 2024/10/10
 */
@Slf4j
public class CancelAuthTaskPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        log.info("{} {}", body.getNodeElement().getNodeExtendName(), body.getBusinessId());

        if (!Boolean.parseBoolean(ConfigHelper.get("authTask.enabled", "false"))) {
            return false;
        }

        AsyncExecuteUtils.execute(() ->
                BatchTask4AuthAbility.cancelAuthTask(CancelSceneEnum.ABOLISH, getEntityClass(body), body.getBusinessId()));

        // 取消关联的保障单批次
        List<BatchTask> batchTasks = BatchTaskAbility.getGuarantyBatchTask(body.getBusinessId());
        batchTasks.forEach(item -> {
            AsyncExecuteUtils.execute(() ->
                    BatchTask4AuthAbility.cancelAuthTask(CancelSceneEnum.ABOLISH, BatchTask.class, item.getId()));
        });
        return false;
    }
}
