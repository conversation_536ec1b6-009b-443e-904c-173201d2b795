package com.zte.iccp.itech.extension.openapi.model.networksecurity.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 批次操作阶段打卡
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/7/24
 */
@Getter
@Setter
public class BatchOperationStageClockDTO {

    /** 操作阶段打卡表id */
    private String operationClockId;

    /** 批次id */
    private String batchId;

    /** 操作阶段 */
    private String operationPhase;

    /** 开始时间 */
    private String stageStartTime;

    /** 结束时间 */
    private String stageEndTime;
}
