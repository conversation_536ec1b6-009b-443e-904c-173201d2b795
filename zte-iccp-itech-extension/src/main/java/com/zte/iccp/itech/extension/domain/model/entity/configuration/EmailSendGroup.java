package com.zte.iccp.itech.extension.domain.model.entity.configuration;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.EmailGroupFieldConsts.*;

/** 邮件推送群组 */
@Setter
@Getter
@BaseEntity.Info("email_send_group")
public class EmailSendGroup extends BaseEntity {

    /** 抄送类型 */
    @JsonProperty(value = CC_TYPE)
    private List<TextValuePair> ccType;

    /** 产品类型 */
    @JsonProperty(value = PRODUCT)
    private List<TextValuePair> product;

    /** 组织 */
    @JsonProperty(value = ORGANIZATION)
    private List<TextValuePair> organization;

    /** 是否政企 */
    @JsonProperty(value = GOVERNMENT_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum governmentFlag;

    /** 抄送人员 */
    @JsonProperty(value = CC_PERSON)
    private List<Employee> ccPerson;

    /** 备注 */
    @JsonProperty(value = REMARK)
    private String remark;

    /** 是否启用 */
    @JsonProperty(value = IN_SERVICE_FLAG)
    private List<TextValuePair> inServiceFlag;
}
