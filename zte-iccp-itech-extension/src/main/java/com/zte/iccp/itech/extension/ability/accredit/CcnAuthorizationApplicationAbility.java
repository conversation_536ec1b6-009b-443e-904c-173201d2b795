package com.zte.iccp.itech.extension.ability.accredit;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.entity.CcnAuthorizationApplication;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.INNER_OFFICE_ORG_CODE_PATH;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

public class CcnAuthorizationApplicationAbility {

    /**
     * 检索 CCN 默认授权文件申请单 - 主键
     * @param id
     * @return CcnAuthorizationApplication
     */
    public static CcnAuthorizationApplication queryApplicationById(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList();

        Filter idFilter = new Filter(ID, Comparator.EQ, id);
        List<IFilter> conditionFilterList = Lists.newArrayList(idFilter);

        return QueryDataHelper.queryOne(CcnAuthorizationApplication.class, fieldList, conditionFilterList);
    }

    /**
     * 检索 CCN 默认授权文件申请单 - 主键
     * @param idList
     * @return CcnAuthorizationApplication
     */
    public static List<CcnAuthorizationApplication> queryApplicationById(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return null;
        }

        List<String> fieldList = Lists.newArrayList();

        Filter idFilter = new Filter(ID, Comparator.IN, idList);
        List<IFilter> conditionFilterList = Lists.newArrayList(idFilter);

        return QueryDataHelper.query(CcnAuthorizationApplication.class, fieldList, conditionFilterList);
    }

    /**
     * 更新CCN 默认授权文件申请单
     * @param application
     */
    public static void update(CcnAuthorizationApplication application) {
        if (Objects.isNull(application)) {
            return;
        }

        SaveDataHelper.update(application);
    }

    /**
     * 获取组织类型
     * @param application
     * @return DeptTypeEnum
     */
    public static DeptTypeEnum getDeptType(CcnAuthorizationApplication application) {
        String organizationPath = TextValuePairHelper.getValue(application.getOrganization());
        if (!StringUtils.hasText(organizationPath)) {
            return null;
        }

        return Arrays.stream(INNER_OFFICE_ORG_CODE_PATH).anyMatch(organizationPath::startsWith)
                ? DeptTypeEnum.INNER
                : DeptTypeEnum.INTER;
    }

    /**
     * 删除申请单
     * @param id
     */
    public static void delete(String id) {
        if (!StringUtils.hasText(id)) {
            return;
        }

        SaveDataHelper.batchDelete(CcnAuthorizationApplication.class, Lists.newArrayList(id));
    }
}
