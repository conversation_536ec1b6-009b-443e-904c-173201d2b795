package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.itp.msa.core.model.RetCode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.*;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SysAuthUtils {

    private static final String SYS_AUTH_ACCESS_KEY = "sysAuth.accessKey[%s]";

    private static final long EXPIRES_SECONDS = 5 * 60;

    private static final String IN_ONE_DOMAIN_NAME = getInOneDomainName();

    private static final String DOMAIN_NAME_PATTERN
            = "(^|https?://)(?!https?://)(?<domainName>[^/:]+).*";

    private static final String DOMAIN_NAME_REPLACEMENT
            = "${domainName}";

    public static void auth(HttpServletRequest request) {
        if (fromInOne(request)) {
            return;
        }

        String authValue = request.getHeader(X_AUTH_VALUE);
        String orgId = request.getHeader(X_ORG_ID);
        String timestamp = HeaderUtils.fromItpValue(
                request.getHeader(X_ITP_VALUE), "timestamp");

        if (StringUtils.isBlank(authValue)
                || StringUtils.isBlank(orgId)
                || StringUtils.isBlank(timestamp)) {
            throw newAuthException("headers insufficient");
        }

        if (expired(timestamp)) {
            throw newAuthException("timestamp expired");
        }

        String accessKey = ConfigHelper.get(String.format(SYS_AUTH_ACCESS_KEY, orgId));
        if (StringUtils.isBlank(accessKey)) {
            throw newAuthException("orgId not registered");
        }

        String sha256 = EncryptorUtils.hmacSha256(String.format("%s%s%s", orgId, accessKey, timestamp));
        if (!sha256.equals(authValue)) {
            throw newAuthException("invalid authValue");
        }
    }

    private static String getInOneDomainName() {
        String raw = ConfigHelper.getRaw("inone.url");
        return raw.replaceAll(DOMAIN_NAME_PATTERN, DOMAIN_NAME_REPLACEMENT);
    }

    private static boolean fromInOne(HttpServletRequest request) {
        String appCode = request.getHeader("AppCode");
        if (StringUtils.isBlank(appCode)) {
            return false;
        }

        String forwardedHost = request.getHeader("x-forwarded-host");
        String fromDomain = forwardedHost.replaceAll(DOMAIN_NAME_PATTERN, DOMAIN_NAME_REPLACEMENT);
        return IN_ONE_DOMAIN_NAME.equals(fromDomain);
    }

    private static LcapBusiException newAuthException(String msg) {
        return new LcapBusiException(RetCode.AUTHFAILED_MSGID, MapUtils.newHashMap("msg", msg));
    }

    private static boolean expired(String timestamp) {
        long input = Long.parseLong(timestamp);
        long now = System.currentTimeMillis() / 1000;
        return Math.abs(now - input) > EXPIRES_SECONDS;
    }
}
