package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.ApprovalLevelEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperationScenarioFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/08
 */
@Getter
@Setter
@BaseEntity.Info("operate_scene_list")
public class OperationScenario extends BaseEntity {
    @JsonProperty(value = NAME)
    private String name;

    @JsonProperty(value = PROD_OPERATION_TEAM)
    private List<TextValuePair> prodOperationTeam;

    @JsonProperty(value = PRODUCT_LINE)
    private List<TextValuePair> productLine;

    @JsonProperty(value = APPROVAL_LEVEL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApprovalLevelEnum approvalLevel;
}