package com.zte.iccp.itech.extension.common.helper;

import com.zte.iccp.itech.extension.common.utils.AesUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.paas.lcap.common.api.ParamServiceHelper;
import com.zte.paas.lcap.common.constant.StringConst;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ConfigHelper {

    public static String getRaw(String key) {
        return getRaw(key, null);
    }

    public static String getRaw(String key, String defaultValue) {
        Environment environment = SpringContextUtil.getBean(Environment.class);
        String value = environment.getProperty(key);
        return StringUtils.isBlank(value)
                ? defaultValue : value;
    }

    public static List<String> get(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return new ArrayList<>();
        }
        List<String> values = new ArrayList<>();
        keys.forEach(key -> {
            if (StringUtils.isNotBlank(get(key))) {
                values.add(get(key));
            }
        });
        return values;
    }

    public static String get(String key) {
        return get(key, StringUtils.EMPTY);
    }

    public static List<String> getList(String key) {
        String value = get(key, StringUtils.EMPTY);
        if (StringUtils.isEmpty(value)) {
            return new ArrayList<>();
        }
        return Arrays.asList(ConfigHelper.get(key).split(CommonConstants.COMMA));
    }

    public static String get(String key, String defaultValue) {
        String value = ParamServiceHelper.getPublicParameter(key);
        return StringUtils.isBlank(value) ? defaultValue : value;
    }

    public static boolean getBoolean(String key) {
        String value = ParamServiceHelper.getPublicParameter(key);
        return "true".equals(value);
    }

    public static String getRawDecrypted(String key) {
        String encrypted = getRaw(key);
        return AesUtils.aesGcmDecrypt(encrypted, getEncKey());
    }

    public static String getDecrypted(String key) {
        String encrypted = get(key);
        return AesUtils.aesGcmDecrypt(encrypted, getEncKey());
    }

    public static String createEncrypted(String content) {
        return AesUtils.aesGcmEncrypt(content, getEncKey());
    }

    public static String decrypt(String content) {
        return AesUtils.aesGcmDecrypt(content, getEncKey());
    }

    public static String[] getArray(String key) {
        return getArray(key, new String[0]);
    }

    public static String[] getArray(String key, String[] defaultValue) {
        String value = get(key, null);
        if (value == null) {
            return defaultValue;
        }

        return value.split(StringConst.COMMA);
    }

    private static String getEncKey() {
        String svcName = getRaw("spring.application.name");
        return StringUtils.leftPad(svcName, 32, "0");
    }
}
