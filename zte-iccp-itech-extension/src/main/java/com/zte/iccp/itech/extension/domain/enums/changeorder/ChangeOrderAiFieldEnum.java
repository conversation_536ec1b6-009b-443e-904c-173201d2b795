package com.zte.iccp.itech.extension.domain.enums.changeorder;

import com.zte.iccp.itech.extension.common.utils.AiParamUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.spi.model.crm.SvcCustomerInfo;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.function.Function;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORM;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.FAST_CODE_MEANING;

/**
 * <AUTHOR>
 * @date 2024/6/13 下午8:10
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ChangeOrderAiFieldEnum {
    //————————单据编号——————
    /**
     * 单据编号
     */
    CN_NO("co_no", "cn_no", Object::toString),


    //————————单行文本——————
    /**
     * 操作主题
     */
    OPERATION_SUBJECTA("operation_subject", "operationSubject", Object::toString),
    /**
     * 操作类型分组
     */
    OPERATION_TYPE_GROUP("operation_type_group", "operation_type_group", Object::toString),
    /**
     * 操作原因是否收费
     */
    OPERATION_REASON_CHARGE("operation_reason_charge", "operation_reason_charge", Object::toString),
    /**
     * 客户标识
     */
    CUSTOMER_IDENTIFICATION("accn_type", "accn_type", Object::toString),
    /**
     * 工具名称
     */
    TOOL_NAME("tool_name", "tool_name", Object::toString),
    /**
     * 不关联原因
     */
    NOT_ASSOCIATE_REASON("not_associate_reason", "TextField_4zvou43d", Object::toString),


    //————————数值——————
    /**
     * 预计业务中断时长
     */
    SERVICE_DISCONNECT_DURATION("service_disconnect_duration", "service_disconnect_duration", Object::toString),


    //————————多行文本——————
    /**
     * 紧急操作原因简述
     */
    EMERGENCY_OPERATION_REASON("emergency_operation_reason", "TextareaField_w7d41t47", Object::toString),
    /**
     * 封网、管控期操作原因
     */
    NET_CLOSE_OR_CONTROL_OPERATION_REASON("net_close_or_control_operation_reason", "TextareaField_99l4hh9c", Object::toString),
    /**
     * 紧急操作原因简述
     */
    OPERATION_DESC("operation_desc", "TextareaField_j0j60nwi", Object::toString),
    /**
     * 附件描述
     */
    ATTACHMENT_DESC("attachment_desc", "attachment_desc", Object::toString),


    //————————复选——————
    /**
     * GDPR要求
     */
    GDPR_REQUIRE("gdpr_require", "CheckboxField_dt5h5qw8", AiParamUtils::getAllTextNameString),
    /**
     * 现场备份无线参数
     */
    BACKUP_RADIO_PARAMETER("backup_radio_parameter", "CheckboxField_t8s8c1wz", AiParamUtils::getAllTextNameString),


    //————————单选——————
    /**
     * 是否BCN
     */
    IS_BCN("is_bcn", "is_bcn", AiParamUtils::getAllTextNameString),
    /**
     * 是否政企
     */
    IS_GOVERNMENT_ENTERPRISE("is_government_enterprise", "is_government_enterprise", AiParamUtils::getAllTextNameString),
    /**
     * 是否首次应用
     */
    IS_FIRST_APPLICATION("is_first_application", "RadioField_wonjhvg5", AiParamUtils::getAllTextNameString),
    /**
     * 触发类型
     */
    TRIGGER_TYPE("trigger_type", "trigger_type", AiParamUtils::getAllTextNameString),
    /**
     * 交付方式
     */
    DELIVERY_MODE("delivery_mode", "delivery_mode", AiParamUtils::getAllTextNameString),
    /**
     * 是否需要多产品联动保障
     */
    IS_PRODUCT_LINKAGE_GUARANTEE("is_product_linkage_guarantee", "is_product_linkage_guarantee", AiParamUtils::getAllTextNameString),
    /**
     * 是否紧急操作
     */
    IS_EMERGENCY_OPERATION("is_emergency_operation", "is_emergency_operation", AiParamUtils::getAllTextNameString),
    /**
     * 是否技术通知单实施
     */
    IS_TECHNICAL_NOTICE("is_technical_notice", "RadioField_irv0qg88", AiParamUtils::getAllTextNameString),
    /**
     * 是否封网、管控期操作
     */
    IS_NET_CLOSE_OR_CONTROL_OPERATION("is_net_close_or_control_operation", "is_net_close_or_control_operation", AiParamUtils::getAllTextNameString),
    /**
     * 是否属于GDPR管控项目
     */
    IS_GDPR("is_gdpr", "RadioField_4btprur8", AiParamUtils::getAllTextNameString),
    /**
     * 保障方式
     */
    GUARANTEE_MODE("guarantee_mode", "RadioField_yskl12k6", AiParamUtils::getAllTextNameString),
    /**
     * 是否多模
     */
    IS_MULTIMODE("is_multimode", "RadioField_wek36qj4", AiParamUtils::getAllTextNameString),
    /**
     * 是否需提供详细保障方案
     */
    IS_GUARANTEE_SOLUTION("is_guarantee_solution", "is_guarantee_solution", AiParamUtils::getAllTextNameString),
    /**
     * 是否关联无线产品升级单
     */
    IS_ASSOCIATE_WIRELESS_UPGRADE("is_associate_wireless_upgrade", "RadioField_noh6274n", AiParamUtils::getAllTextNameString),
    /**
     * 是否升级光模块
     */
    IS_UPGRADING_OPTICAL("is_upgrading_optical", "RadioField_lrqxes11", AiParamUtils::getAllTextNameString),
    /**
     * 是否有第三方终端
     */
    IS_THIRD_PARTY_TERMINAL("is_third_party_terminal", "RadioField_3kr7cmhy", AiParamUtils::getAllTextNameString),
    /**
     * 是否有任务书
     */
    IS_TASK_STATEMENT("is_task_statement", "RadioField_e6ywpeyw", AiParamUtils::getAllTextNameString),
    /**
     * 是否影响ToB业务
     */
    IS_AFFECT_TO_B("is_affect_to_b", "RadioField_gda9ogkj", AiParamUtils::getAllTextNameString),
    /**
     * 是否特殊场景
     */
    IS_SPECIAL_SCENARIO("is_special_scenario", "RadioField_m1ar2xvl", AiParamUtils::getAllTextNameString),
    /**
     * 是否有商务收费合同
     */
    IS_COMMERCIAL_CHARGE_CONTRACT("is_commercial_charge_contract", "RadioField_oz7zfg1j", AiParamUtils::getAllTextNameString),
    /**
     * 是否获得客户扫描许可
     */
    IS_CUSTOMER_SCAN_PERMISSION("is_customer_scan_permission", "RadioField_7a564zlu", AiParamUtils::getAllTextNameString),
    /**
     * 是否商用局
     */
    IS_COMMERCIAL_OFFICE("is_commercial_office", "RadioField_m8x538ur", AiParamUtils::getAllTextNameString),
    /**
     * 预割接是否回退
     */
    IS_CUTOVER_ROLLBACK("is_cutover_rollback", "RadioField_alv27u5l", AiParamUtils::getAllTextNameString),
    /**
     * 是否大区操作
     */
    IS_REGIONAL_OPERATION("is_regional_operation", "RadioField_ck4bq5ly", AiParamUtils::getAllTextNameString),
    /**
     * 是否需要授权文件
     */
    IS_NEED_AUTHORIZATION_FILE("is_need_authorization_file", "RadioField_db1wlhaj", AiParamUtils::getAllTextNameString),

    /**
     * 是否涉及高危指令
     */
    IS_HIGH_RISK_INSTRUCTION("is_high_risk_instruction", "RadioField_ksxehf00", AiParamUtils::getAllTextNameString),
    /**
     * 变更操作来源
     */
    CHANGE_OPERATION_SOURCE("change_operation_source", "RadioField_9w2vdyu0", AiParamUtils::getAllTextNameString),
    /**
     * 是否涉及license文件加载
     */
    LICENSE_LOAD("license_load", "RadioField_j0xbbn18", AiParamUtils::getAllTextNameString),



    //————————下拉单选——————
    /**
     * 操作类型
     */
    OPERATION_TYPE("operation_type", "operation_type", AiParamUtils::getAllTextNameString),
    /**
     * 操作原因
     */
    OPERATION_REASON("operation_reason", "operation_reason", AiParamUtils::getAllTextNameString),
    /**
     * 重要程度
     */
    IMPORTANCE("importance", "importance", AiParamUtils::getAllTextNameString),
    /**
     * 风险评估
     */
    RISK_EVALUATION("risk_evaluation", "risk_evaluation", AiParamUtils::getAllTextNameString),
    /**
     * 操作等级
     */
    OPERATION_LEVEL("operation_level", "operation_level", AiParamUtils::getAllTextNameString),
    /**
     * 工具落地状态
     */
    TOOL_USE("tool_use", "tool_use", AiParamUtils::getAllTextNameString),
    /**
     * 未使用工具原因
     */
    NOT_USE_TOOL_REASON("not_use_tool_reason", "not_use_tool_reason", AiParamUtils::getAllTextNameString),
    /**
     * 客户特殊业务
     */
    CUSTOMER_SPECIAL_SERVICE("customer_special_service", "SelectField_4lr79wlb", AiParamUtils::getAllTextNameString),


    //————————下拉复选——————
    /**
     * 产品分类
     */
    PRODUCT_ID("product_id", "iTechCloudCustomTreeSelect_product", AiParamUtils::getAllTextNameString),
    /**
     * 代表处
     */
    ORGANIZATION_ID("organization_id", "iTechCloudCustomTreeSelect_organization", AiParamUtils::getAllTextNameString),
    /**
     * 工具名称
     */
    TOOL_NAME_SELECTED("tool_name_selected", "tool_name_selected", AiParamUtils::getAllTextNameString),
    /**
     * 多模从属产品
     */
    MULTI_MODE_PRODUCT("multi_mode_product", "iTechCloudCustomTreeSelect_eephklwi", AiParamUtils::getAllTextNameString),


    //————————日期——————
    /**
     * 计划开始时间
     */
    OPERATION_START_TIME("operation_start_time", "operation_start_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),
    /**
     * 计划结束时间
     */
    OPERATION_END_TIME("operation_end_time", "operation_end_time", r -> new SimpleDateFormat(DATE_FORM).format(r)),


    //————————基础资料——————
    /**
     * 操作场景
     */
    OPERATION_SCENARIO("operation_scenario", "operation_scenario",
            r -> (String) PropertyValueConvertUtil.getMap(r).get("textfield_operate_scene")),


    //————————国家/地区——————
    /**
     * 国家/地区
     */
    COUNTRY("country", "CountrySelectField_pni4qp77", AiParamUtils::getAllValueLangNameSring),
    /**
     * 省/州
     */
    PROVINCE("province", "CountrySelectField_1vfg2nns", AiParamUtils::getAllValueLangNameSring),
    /**
     * 地市
     */
    AREA("area", "CountrySelectField_mmucxc4w", AiParamUtils::getAllValueLangNameSring),


    //————————快码——————
    /**
     * 时区
     */
    TIME_ZONE("time_zone", "FastCodeField_2h88k6f8",
            r -> (String) PropertyValueConvertUtil.getMap(r).get(FAST_CODE_MEANING)),


    //————————服务对象——————
    /**
     * 客户名称
     */
    CUSTOMER_ID("customer_id", "customer_id", object -> {
        SvcCustomerInfo svcCustomerInfo = JsonUtils.parseObject(object, SvcCustomerInfo.class);
        return svcCustomerInfo.getCustomerName();
    }),
    /**
     * 无线产品升级单
     */
    WIRELESS_UPGRADE_TICKET("wireless_upgrade_ticket", "wireless_upgrade_ticket", object -> ((DynamicDataEntity) object).getString("textfield_9pq2sfsq")),


    //————————服务对象属性——————


    ;


    //实体唯一标识
    private final String propertyKey;

    //布局唯一标识
    private final String cid;

    //方法
    private final Function<Object, String> function;


}
