package com.zte.iccp.itech.extension.plugin.form.managetask.operation;

import com.zte.iccp.itech.extension.ability.ManageTaskAbility;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.ManageTaskOperationRecordEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLogRecordTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ManageSubTask;
import com.zte.iccp.itech.extension.domain.model.ManageSubTaskFlow;
import com.zte.iccp.itech.extension.domain.model.OperationLogRecord;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.extension.plugin.operation.assignment.SaveTechnologyTaskPlugin;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.flow.dto.SaveBizAndStartFlowDTO;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFlowFieldConsts.*;


/**
 * 技术管理任务-提交(插入创建时间记录)
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/4
 */
public class ManageTaskCommitPlugin extends BaseOperationPlugin {

    /**
     * 子任务单据编号尾部四位，不足，则左补0
     */
    private static final String AUTO_ROLE = "%03d";

    /**
     * 技术管理任务创建时，获取子表单id添加操作记录，低码给子表单id生成的key
     */
    private static final String MANAGE_TASK_SUB_TABLE_ID = "pk_vin65bv5";

    @Override
    public void afterExecute(ExecuteEvent executeEvent) {
        IDataModel dataModel = getModel();
        IFormView formView = getView();

        SaveTechnologyTaskPlugin saveTechnologyTaskPlugin = new SaveTechnologyTaskPlugin();

        String manageTaskId = getPkId();
        // 1.主任务处理：(1)操作记录；(2)邮件发送
        mainTaskProcess(manageTaskId);
        // 2.子任务空处理
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(CidConstants.SUB_TABLE_MANAGE_TASK_CID);
        if (dataEntityCollection.isEmpty()) {
            // 仅生成主任务
            saveTechnologyTaskPlugin.execute(dataModel, formView);
            return;
        }
        // 3.子任务处理
        subTaskProcess(dataEntityCollection, manageTaskId);

        // 4.生成技术管理任务
        saveTechnologyTaskPlugin.execute(dataModel, formView);
    }

    /**
     * 主任务处理：1.操作记录；2.邮件发送
     *
     * @param pkId pkId
     */
    private void mainTaskProcess(String pkId) {
        List<OperationLogRecord> recordList = OperationLogRecordAbility.query(pkId);
        if (!CollectionUtils.isEmpty(recordList)) {
            return;
        }

        OperationLogRecordAbility.insert(buildOperationRecordData(pkId));
    }

    /**
     * 子任务处理
     *
     * @param dataEntityCollection dataEntityCollection
     * @param pkId pkId
     */
    private void subTaskProcess(IDataEntityCollection dataEntityCollection, String pkId) {
        // 子任务执行状态处理
        Map<String, ManageSubTask> executeSubTaskMap = getExecuteSubTaskMap(dataEntityCollection, pkId);
        // 主任务单据编号
        String cnNo = PropertyValueConvertUtil.getString(getModel().getValue(ManageTaskFieldConsts.CN_NO));
        // 子任务操作记录
        List<OperationLogRecord> operationRecordList = new ArrayList<>();
        // 技术管理子任务单据体
        List<Map<String, Object>> multiValues = new ArrayList<>();
        for (int i = 0; i < dataEntityCollection.size(); i++) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) dataEntityCollection.get(i);
            //子表单id
            String subTaskId = (String) dynamicDataEntity.get(MANAGE_TASK_SUB_TABLE_ID);
            ManageSubTask manageSubTask = executeSubTaskMap.get(subTaskId);
            if (manageSubTask != null) {
                continue;
            }
            String subTaskName = (String) dynamicDataEntity.get(SUBTASK_NAME);
            //生成3位id，不够则左补零
            //子任务单据编号 = 主任务单据编号 + "-001"
            String subTaskCnNo = cnNo + CommonConstants.HYPHEN + String.format(AUTO_ROLE, i + 1);
            //启动子任务流程
            startSubTaskFlow(subTaskId, pkId, subTaskName, subTaskCnNo);
            String operationDesc = ManageTaskOperationRecordEnum.MANAGE_TASK_CREDIT.getMsgKey();
            operationRecordList.add(buildSubOperationRecordData(subTaskId, pkId, operationDesc, subTaskName));

            //更新子任务状态
            List<TextValuePair> subTaskStatus = TextValuePairHelper.buildList(
                    AssignmentStatusEnum.EXECUTE.getValue(), AssignmentStatusEnum.EXECUTE.getZhCn(), AssignmentStatusEnum.EXECUTE.getEnUs());
            multiValues.add(MapUtils.newHashMap(ID, subTaskId,
                    SUBTASK_CN_NO, subTaskCnNo,
                    SUBTASK_STATUS, subTaskStatus));
        }
        //批量新增操作日志记录表，父/子任务记录
        if (CollectionUtils.isEmpty(operationRecordList)) {
            return;
        }
        OperationLogRecordAbility.batchInsert(operationRecordList);
        SaveDataHelper.batchUpdate(ManageSubTask.class, pkId, multiValues);
    }

    /**
     * 构建主任务操作记录
     *
     * @param pkId pkId
     * @return OperationLogRecord
     */
    private OperationLogRecord buildOperationRecordData(String pkId) {
        return OperationLogRecord.builder()
                .relationId(pkId)
                .responsiblePerson(ContextHelper.getEmpInfo())
                .operationName(ManageTaskOperationRecordEnum.MANAGE_TASK_CREDIT.getMsgKey())
                .operationType(OperationLogRecordTypeEnum.MANAGE_TASK.getOperationType())
                .build();
    }

    /**
     * 启动子任务流程
     *
     * @param subTaskId 子任务id
     * @param manageTaskId 父任务id
     * @param subTaskName 子任务名称
     * @param subTaskCnNo 子任务单据编号
     */
    private void startSubTaskFlow(String subTaskId, String manageTaskId, String subTaskName,String subTaskCnNo) {
        SaveBizAndStartFlowDTO flow = new SaveBizAndStartFlowDTO();
        flow.setAppId(ContextHelper.getAppId());
        flow.setPageId(PageConstants.PAGE_SUBTASK_FLOW_DETAIL_ID);
        flow.setBizObjCode(EntityHelper.getEntityId(ManageSubTaskFlow.class));
        MultiLangText multiLangText = new MultiLangText();
        multiLangText.setValue(AssignmentStatusEnum.EXECUTE.getValue());
        multiLangText.setZhCN(AssignmentStatusEnum.EXECUTE.getZhCn());
        multiLangText.setEnUS(AssignmentStatusEnum.EXECUTE.getEnUs());
        flow.setTenantId(ContextHelper.getTenantId());
        flow.setParams(MapUtils.newHashMap(APPROVER_PARENT_TASK_ID, manageTaskId,
                APPROVER_SUB_TASK_ID, subTaskId,
                APPROVER_STATUS, multiLangText,
                APPROVER_SUBTASK_NAME, subTaskName,
                SUBTASK_CN_NO, subTaskCnNo)
        );
        FlowServiceHelper.saveBizAndStartFlow(flow);
    }

    /**
     * 编辑问题修改
     * 1.获取到全部的子任务id，根据子任务id查询数据库，如果查询为空，说明是第一次提交执行
     * 2.如果查询不为空，过滤出非草稿状态（即执行中、审批中...）的数据，判断当前要新增的子任务审批数据和操作记录数据
     *
     * @param dataEntityCollection dataEntityCollection
     * @param manageTaskId manageTaskId
     * @return Map<String, ManageSubTask>
     */
    private Map<String, ManageSubTask> getExecuteSubTaskMap(IDataEntityCollection dataEntityCollection,String manageTaskId){
        List<String> subTaskIds = new ArrayList<>();
        for (Object o : dataEntityCollection) {
            DynamicDataEntity dynamicDataEntity = (DynamicDataEntity) o;
            subTaskIds.add((String) dynamicDataEntity.get(MANAGE_TASK_SUB_TABLE_ID));
        }
        List<ManageSubTask> manageSubTasks = ManageTaskAbility.querySubTaskList(subTaskIds, manageTaskId);

        if (CollectionUtils.isEmpty(manageSubTasks)) {
            return MapUtils.newHashMap();
        }
        return manageSubTasks.stream()
                .filter(item -> !CollectionUtils.isEmpty(item.getSubtaskStatus()))
                .filter(item -> {
                    TextValuePair textValuePair = item.getSubtaskStatus().get(CommonConstants.INTEGER_ZERO);
                    return !AssignmentStatusEnum.START.getValue().equals(textValuePair.getValue());
                }).collect(Collectors.toMap(ManageSubTask::getId, item -> item, (o1, o2) -> o1));
    }


    /**
     * 构建子任务操作日志记录对象
     *
     * @param subTaskId 子任务id
     * @param mainTaskId 父任务id
     * @param operationDesc 创建任务
     * @param subTaskName 子任务名称
     * @return OperationLogRecord
     */
    private OperationLogRecord buildSubOperationRecordData(String subTaskId, String mainTaskId, String operationDesc,
                                                           String subTaskName) {
        OperationLogRecord record = new OperationLogRecord();
        record.setRelationId(subTaskId);
        record.setOperationName(operationDesc);
        record.setOperationType(OperationLogRecordTypeEnum.MANAGE_SUBTASK.getOperationType());
        record.setParentRelationId(mainTaskId);
        record.setResponsiblePerson(ContextHelper.getEmpInfo());
        record.setChildName(subTaskName);
        return record;
    }
}
