package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 自定义网络集
 * <AUTHOR> 10335201
 * @date 2024-07-03 上午9:29
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CustomizedNetworkSetConsts {
    public static final String ID = "#id";
    public static final String NETWORK_SET_NAME = "network_set_name";
    public static final String NETWORK = "network";
    public static final String DESCRIPTION = "description";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String LAST_MODIFIED_BY = "last_modified_by";
    public static final String LAST_MODIFIED_TIME = "last_modified_time";
}
