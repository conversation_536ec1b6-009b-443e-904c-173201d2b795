package com.zte.iccp.itech.extension.openapi.model.reportpush;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/21 下午3:15
 */
@Setter
@Getter
public class NetChangeReportInterOrgVO {
    /**
     * 产品经营团队
     */
    private String prodTeam;
    /**
     * 产品经营团队id
     */
    private String prodTeamId;
    /**
     * 组织id路径
     */
    private String organizationIdPath;

    /**
     * 部门Path
     */
    private String organizationEnPath;

    /**
     * 产品idPath
     */
    private String prodClassIdPath;

    /**
     * 产品Path
     */
    private String prodClassEnPath;

    /**
     * 当前组织-产品的单据总数
     */
    private int totalCount;

    /**
     * 类型，当天/未来三天  DAY DAY_THREE
     */
    private String dateRange;


}
