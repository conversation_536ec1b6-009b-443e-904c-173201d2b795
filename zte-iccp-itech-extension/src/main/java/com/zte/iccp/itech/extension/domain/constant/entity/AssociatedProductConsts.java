package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 关联产品基础配置常量
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/12
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AssociatedProductConsts {

    /**
     * 单据状态
     */
    public static final String BILLSTATUSFIELD_ASSOCIATED_PRODUCT = "billstatusfield_associated_product";

    /**
     * 关联类型
     */
    public static final String RADIOFIELD_ASSOCIATED_TYPE = "radiofield_associated_type";

    /**
     * 关联产品中文名
     */
    public static final String TEXTFIELD_ASSOCIATED_PRODUCT_ZH = "textfield_associated_product_zh";

    /**
     * 关联产品英文名
     */
    public static final String TEXTFIELD_ASSOCIATED_PRODUCT_EN = "textfield_associated_product_en";
}
