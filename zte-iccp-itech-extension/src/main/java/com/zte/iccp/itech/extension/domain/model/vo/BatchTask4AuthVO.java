package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.IValueEnumDeserializer;
import com.zte.iccp.itech.extension.domain.enums.IValueEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.spi.model.inet.OperatorVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/08/19
 */
@Getter
@Setter
public class BatchTask4AuthVO {
    /** 来源单号: 批次编号 */
    @JsonProperty("operateCode")
    private String batchCode;

    /** 任务主题: 批次名称 */
    @JsonProperty("operationSubject")
    private String batchName;

    @JsonProperty("pdmProductList")
    private List<PdmProductVO> pdmProdModels;

    /** 代表处编号（仅四层单位的编码） */
    @JsonProperty("officeId")
    private String responsibleDeptId;

    /** 代表处名称 */
    @JsonProperty("officeNameCN")
    private String responsibleDeptNameZh;

    /** 代表处名称 */
    @JsonProperty("officeNameUS")
    private String responsibleDeptNameEn;

    /** 客户编号: 客户ID，CRM接口的id字段，要重新查 */
    @JsonProperty("customerId")
    private String customerId;

    /** 客户名称 */
    @JsonProperty("customerNameCN")
    private String customerNameZh;

    /** 客户名称 */
    @JsonProperty("customerNameUS")
    private String customerNameEn;

    /** 操作类型（中文） */
    @JsonProperty("appOperateTypeCN")
    private String operationTypeZh;

    /** 操作类型（英文） */
    @JsonProperty("appOperateTypeUS")
    private String operationTypeEn;

    /** 操作原因（中文） */
    @JsonProperty("appOperateRsnCN")
    private String operationReasonZh;

    /** 操作原因（英文） */
    @JsonProperty("appOperateRsnUS")
    private String operationReasonEn;

    /** 操作等级（中文） */
    @JsonProperty("operationLevelCN")
    private String operationLevelZh;

    /** 操作等级（英文） */
    @JsonProperty("operationLevelUS")
    private String operationLevelEn;

    /** 操作开始时间 */
    @JsonProperty("appOperateBDat")
    private Date planStartTime;

    /** 操作结束时间  */
    @JsonProperty("appOperateEDat")
    private Date planEndTime;

    /** 国家/省份Id: 国家编码 */
    @JsonProperty("areaId")
    private String countryCode;

    /** 国家/省份: 国家名称 */
    @JsonProperty("areaNameCN")
    private String countryNameZh;

    /** 国家/省份: 国家名称 */
    @JsonProperty("areaNameUS")
    private String countryNameEn;

    /** 地市/地区: 地市名称 */
    @JsonProperty("areaNameSubCN")
    private String cityNameZh;

    /** 地市/地区: 地市名称 */
    @JsonProperty("areaNameSubUS")
    private String cityNameEn;

    /** 操作时区：时区快码前缀，格式：GMT+xxx */
    @JsonProperty("operationTimezone")
    private String timeZone;

    /** 操作说明: 操作描述 */
    @JsonProperty("operationInstructions")
    private String operationDescription;

    /** 方案: 操作方案描述 */
    @JsonProperty("appPlanDesc")
    private String planDescription;

    /** 预计业务中断时长 */
    @JsonProperty("interruptTime")
    private String busiInterruptDuration;

    /** 网络编号 */
    @JsonProperty("appNetworkId")
    private String networkCode;

    /** 网络名称 */
    @JsonProperty("appNetworkNameCN")
    private String networkNameZh;

    /** 网络名称 */
    @JsonProperty("appNetworkNameUS")
    private String networkNameEn;

    /** 客户网络名称 */
    @JsonProperty("appCustomerNetworkName")
    private String customerNetworkName;

    /** 申请单审批人对应网络ID */
    @JsonProperty("approverNetworkId")
    private String approverNetworkId;

    /** 单据类型：: 固定传 1 */
    @JsonProperty("taskType")
    private final String taskType = "1";

    /** 单据URL地址 */
    @JsonProperty("url")
    private String batchTaskUrl;

    /** 高危指令 */
    @JsonProperty("instructions")
    private List<String> highRiskInstructions;

    /** 人员属性: 操作人员列表 */
    @JsonProperty("operators")
    private List<OperatorVO> operators;
}
