package com.zte.iccp.itech.extension.domain.enums.changeorder;


import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts;
import com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;

/**
 * <AUTHOR>
 * @date 2024/5/10 下午5:40
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ValueChangePluginEnum {

    /**
     * 操作类型-联动插件
     */
    OPERATION_TYPE(Lists.newArrayList(FIELD_PRODUCT_CID), new OperationTypePluginValue()),

    /**
     * 操作类型分组-赋值插件
     */
    OPERATION_TYPE_GROUP(Lists.newArrayList(FIELD_OPERATION_TYPE_CID), new OperationTypeGroupPluginValue()),

    /**
     * 涉及V4操作硬件-联动插件
     */
    INVOLVE_V4_OPERATION_HARDWARE_Enum(
            Lists.newArrayList(LOGICAL_NE_PROPERTY_KEY,FIELD_PRODUCT_CID,FIELD_ORGANIZATION_CID,
                    COMPONENT_OPERATION_TYPE_CID,INVOLVE_V4_OPERATION_HARDWARE), new InvolveV4OperationHardwarePluginValue()),

    /**
     * 操作原因-联动插件
     */
    OPERATION_REASON(Lists.newArrayList(FIELD_OPERATION_TYPE_CID), new OperationReasonPluginValue()),

    /** 升级类操作类型-当前版本必填插件 */
    CURRENT_VERSION_REQUIRED(Lists.newArrayList(FIELD_OPERATION_TYPE_CID), new CurrentVersionRequiredPlugin()),

    /**
     * 大区TD审核人插件
     */
    REGION_TD_REVIEWER(
            Lists.newArrayList(FIELD_ORGANIZATION_CID,
                    FIELD_IS_BCN_CID, ACCN_TYPE_CID), new RegionTDReviewerPluginValue()),

    /**
     * 是否行政领导审批-赋值插件
     */
    IS_ADMINISTRATION_LEADER_APPROVAL(
            Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID, FIELD_IS_EMERGENCY_OPERATION_CID,
                    FIELD_OPERATION_LEVEL_CID, FIELD_OPERATION_TYPE_CID, FIELD_IS_GUARANTEE_SOLUTION_CID,
                    FIELD_IS_NET_CLOSE_OR_CONTROL_OPERATION_CID, FIELD_IMPORTANCE_CID, FIELD_RISK_EVALUATION_CID,
                    FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, ChangeOrderFieldConsts.OPERATION_TYPE_GROUP),
            new IsAdministrationLeaderApprovalFormPluginValue()),

    /**
     * 重要程度-自动计算插件
     */
    IMPORTANCE(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID,
            FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, FIELD_CUSTOMER_ID_CID,
            FIELD_COUNTRY_CID, FIELD_OPERATION_TYPE_CID, FIELD_IS_FIRST_APPLICATION_CID), new ImportanceFormPluginValue(false)),

    /**
     * 操作等级-自动计算插件
     */
    OPERATION_LEVEL(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID,
            FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY, FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, FIELD_CUSTOMER_ID_CID,
            FIELD_COUNTRY_CID, FIELD_OPERATION_TYPE_CID, FIELD_IS_FIRST_APPLICATION_CID, FIELD_IS_SPECIAL_SCENARIO_CID,
            FIELD_SERVICE_DISCONNECT_DURATION_CID), new OperationLevelPluginValue(false)),

    /**
     * 风险评估-自动计算插件
     */
    RISK_EVALUATION(
            Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID, FIELD_PRODUCT_CID,FIELD_IS_SPECIAL_SCENARIO_CID,
                    FIELD_OPERATION_TYPE_CID,FIELD_SERVICE_DISCONNECT_DURATION_CID,FIELD_IS_FIRST_APPLICATION_CID), new RiskEvaluationPluginValue()),

    /**
     * 变更单主单【计划操作开始时间】、【计划操作结束时间】时区提示语赋值插件
     */
    TIME_ZONE(Lists.newArrayList(TIME_ZONE_KEY, COMPONENT_OPERATION_START_TIME_CID, COMPONENT_OPERATION_END_TIME_CID, FIELD_ORGANIZATION_CID), new TimeZonePluginValue()),

    /**
     * 变更单批次概要子表单【计划操作开始时间】、【计划操作结束时间】时区提示语赋值插件
     */
    BATCH_SUMMARY_TIME_ZONE(Lists.newArrayList(TIME_ZONE_KEY, FIELD_PLAN_OPERATION_START_TIME_CID, FIELD_PLAN_OPERATION_END_TIME_CID, FIELD_ORGANIZATION_CID), new BatchSummaryTimeZonePlugin()),


    /**
     * 操作主题-拼接赋值插件
     */
    OPERATION_SUBJECT(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_PRODUCT_CID, FIELD_IS_GOV_ENT_CID,
            FIELD_OPERATION_TYPE_CID, FIELD_AREA_CID, FIELD_PROVINCE_CID, FIELD_COUNTRY_CID,
            FIELD_CUSTOMER_ID_CID, ACCN_TYPE_CID, ChangeOrderFieldConsts.LOGICAL_NE),
            new OperationSubjectPluginValue(false)),

    /**
     * 技术方案检查/核心网产品外场质量保证规范动作-数据加载插件
     */
    TECH_CHECK(Lists.newArrayList(FIELD_PRODUCT_CID,FIELD_OPERATION_TYPE_GROUP_CID), new TechnicalSolutionCheckPluginValue()),

    /**
     * 更新操作阶段打卡子表单里【操作准备-开始时间】、【测试验证-结束时间】两个数据
     */
    OPERATION_PHASE_TIME_UPDATE(Lists.newArrayList(FIELD_OPERATION_START_TIME_CID,FIELD_OPERATION_END_TIME_CID), new OperationPhaseCheckTimeChangePlugin()),

    /**
     * 操作原因【是否关联收费】属性插件
     */
    OPERATION_REASON_CHARGE(Lists.newArrayList(FIELD_OPERATION_REASON_CID), new OperationReasonChargePluginValue()),

    /**
     * 操作阶段打卡表中的操作时长变化时，自动计算开始时间和结束时间
     */
    OPERATION_PHASE_TIME_UPDATE_BY_DURATION(Lists.newArrayList(FIELD_OPERATION_DURATION_CID), new OperationPhaseCheckTimeChangeByDurationPlugin()),

    /**
     * 操作步骤打卡表中的开始时间和结束时间检查
     */
    OPERATION_STEP_TIME_CHECK(Lists.newArrayList(STEP_START_TIME_CID,STEP_END_TIME_CID), new OperationStepCheckTimePluginValue()),

    /**
     * 代表处方案审核人插件
     */
    OFFICE_SOLUTION_REVIEWER(Lists.newArrayList(FIELD_PRODUCT_CID, FIELD_ORGANIZATION_CID,
            FIELD_IS_GOV_ENT_CID, FIELD_OFFICE_SOLUTION_REVIEW_SELECT_CID), new OfficeSolutionReviewerPluginValue()),

    /**
     * 经理/副经理插件
     */
    MANAGER(Lists.newArrayList(FIELD_PRODUCT_CID, FIELD_ORGANIZATION_CID,
            FIELD_IS_GOV_ENT_CID), new ManagerPluginValue()),

    /**
     * 技术交付部/网络处审核人（组）插件
     */
    TECHNICAL_REVIEWER(Lists.newArrayList(FIELD_IS_UPGRADE_TECHNOLOGY_CID,
            FIELD_PRODUCT_CID, FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID, ACCN_TYPE_CID, FIELD_OPERATION_TYPE_CID,
            FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY, LOGICAL_NE_PROPERTY_KEY), new TDDNetServiceReviewerPluginValue()),

    /**
     * 网元清单解析生成批次概要插件
     */
    NE_LIST_TO_BATCH_SUMMARY(Lists.newArrayList(NE_LIST_FILE_PROPERTY_KEY), new NeListParseToBatchSummaryPlugin()),

    /**
     * 操作阶段打卡子表单-打卡责任人自动计算插件
     */
    OPERATION_PHASE_PERSON_UPDATE(Lists.newArrayList(FIELD_OPERATION_LEVEL_CID,OPERATOR_NAME_KEY), new OperationPhaseCheckPersonPluginValue()),

    /**
     * 批次概要批次号值变化，新增操作对象插件
     */
    BATCH_SUMMARY_BATCH_NO_NEW_OPERATION_OBJ(Lists.newArrayList(BATCH_SUMMARY_TABLE_BATCH_NO_PROPERTY_KEY),
            new BatchNoChangePluginValue()),

    /**
     * 大区相关代表处的邮件抄送不能超过50人
     */
    REGIONAL_REPRESENTATIVE_OFFICE_PEOPLE(Lists.newArrayList(MAIL_CARBON_COPY_KEY), new RegionalRepresentativeOfficePeoplePluginValue()),

    /**
     * 操作人员列表数据检查
     */
    OPERATOR_CHECK_PEOPLE(Lists.newArrayList(OPERATOR_ROLE_KEY, OPERATOR_NAME_KEY, FIELD_OPERATOR_BATCH_NO_KEY,
            OPERATOR_IS_REMOTE, FIELD_ORGANIZATION_CID), new OperatorCheckPluginValue()),

    /**
     * 当子表单被规则隐藏时，需要清空子表单上的数据
     */
    CLEAR_SUB_FORM_DATA(Lists.newArrayList(IS_REGIONAL_OPERATION_KEY,IS_CHECK_IN_KEY,IS_HIGH_RISK_INSTRUCTION), new ClearSubFormPluginValue()),

    /**
     * 网络名称插件
     */
    TOOL_NAME(Lists.newArrayList(FIELD_PRODUCT_CID, FIELD_TOOL_USE_CID), new ToolNamePluginValue()),

    /**
     * 客户标识插件
     */
    ACCNT_TYPE(Lists.newArrayList(FIELD_CUSTOMER_NAME_CID), new AccntTypePluginValue()),

    /**
     * 员工-归属部门插件
     */
    EMPLOYEE_DEPARTMENT(Lists.newArrayList(OPERATOR_NAME_KEY), new EmployeeDepartmentPluginValue()),

    /**
     * 人员属性列是否展示，人员属性单元格，是否可编辑
     */
    RESPONSIBLE_DEPT_CHANGE(Lists.newArrayList(FIELD_ORGANIZATION_CID), new ResponsibleDeptChangePlugin()),

    /**
     * 操作场景插件
     */
    OPERATION_SCENARIO(Lists.newArrayList(FIELD_ORGANIZATION_CID,FIELD_OPERATION_LEVEL_CID,FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY), new OperationScenarioPluginValue()),

    /**
     * 国省市数据更新插件
     */
    COUNTRY_PROVINCE_AREA(Lists.newArrayList(FIELD_OPERATION_OBJECT_NETWORK_ID_PROPERTY_KEY), new CountryChangeByNetworkPlugin()),

    /**
     * 操作对象清空插件
     */
    OPERATION_OBJECT_CLEAR(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_PRODUCT_CID), new OperationObjectClearPlugin()),

    /**
     * 是否多产品联动——多产品联动列表
     */
    IS_MULTI_PROD_GUARANTEE(Lists.newArrayList(FIELD_PRODUCT_CID, FIELD_ORGANIZATION_CID, FIELD_IS_GOV_ENT_CID), new IsMultiProdGuaranteePluginValue()),

    /**
     * 应该由Idop创建的网络变更单
     */
    NEED_IDOP_CREATE(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_PRODUCT_CID, FIELD_IS_GOV_ENT_CID), new IdopCreatePluginValue()),

    /**
     * 时区插件-赋值插件
     */
    IDOP_TIME_CHANGE(Lists.newArrayList(TIME_ZONE_KEY, "plan_operation_start_time", "plan_operation_end_time"), new IdopTimeChangePluginValue()),

    /**
     * 关联操作产品
     */
    INTEGRATED_ASSOCIATED_PRODUCT(Lists.newArrayList(FIELD_PRODUCT_CID, COMPONENT_INTEGRATED_ASSOCIATED_PRODUCT_CID), new IntegratedAssociatedProductPluginValue()),

    /**
     * 紧急操作/封网、管控操作标记-赋值插件
     */
    EMERGENCY_AND_CLOSE_OR_CONTROL_OPERATION(Lists.newArrayList(FIELD_IS_EMERGENCY_OPERATION_CID,FIELD_IS_NET_CLOSE_OR_CONTROL_OPERATION_CID), new EmergencyAndClosedOperationFlagPluginValue()),

    /**
     * 逻辑网元
     */
    LOGICAL_NE(Lists.newArrayList(FIELD_PRODUCT_CID, FIELD_ORGANIZATION_CID), new LogicalNePlugin()),

    /**
     * 集成关联产品
     */
    INTEGRATION_RELATED_PRODUCT_SUB(Lists.newArrayList(OVDC_NFVI_PROPERTY_KEY), new IntegrationRelatedProductPlugin()),

    /**
     * oVDC/NFVI项目且需要研发安排专项集成验证
     */
    OVDC_NFVI_VERIFY(Lists.newArrayList(LOGICAL_NE_PROPERTY_KEY, FIELD_PRODUCT_CID), new OvdcNfviVerifyPlugin()),

    SECURITY_SUB(Lists.newArrayList(FIELD_OPERATION_OBJECT_OFFICE_NAME_PROPERTY_KEY), new OfficeNameChangePlugin()),

    /**
     * 产品分类 - 操作场景
     */
    PRODUCT_CATEGORY(Lists.newArrayList(FIELD_PRODUCT_CID), new ProductCategoryPluginValue()),

    /**
     * 电源规格型号
     */
    POWER_SPECIFICATION_MODEL(Lists.newArrayList(FIELD_PRODUCT_CID, FIELD_ORGANIZATION_CID,
            COMPONENT_OPERATION_TYPE_CID, MATERIAL_NAME_SELECT_ENTITY_CID,
            ChangeOrderFieldConsts.POWER_SPECIFICATION_MODEL), new PowerSpecificationModelPluginValue()),

    CLEAR_PLAN_TIME(Lists.newArrayList(FIELD_IS_EMERGENCY_OPERATION_CID), new ClearPlanTimePlugin()),

    /**
     * “需要升级至技术交付部/网络处”字段插件
     */
    IS_UPGRADE_TECHNOLOGY_NORMAL(Lists.newArrayList(FIELD_ORGANIZATION_CID, FIELD_PRODUCT_CID,
            FIELD_IS_GOV_ENT_CID, FIELD_IMPORTANCE_CID, FIELD_RISK_EVALUATION_CID, FIELD_COUNTRY_CID),
            new IsUpgradeTechnologyNormalPlugin()),

    /**
     * 产品型号字段插件
     */
    PRODUCT_MODEL(Lists.newArrayList(FIELD_OPERATION_OBJECT_PRODUCT_MODEL_PROPERTY_KEY), new OperationObjectProductModelPlugin()),

    /**
     * 主产品字段插件
     */
    MAIN_PRODUCT(Lists.newArrayList(OperationObjectFieldConsts.IS_MAIN_PRODUCT), new OperationObjectMainProductPlugin()),

    ;

    // valueChange插件的cid要配置实体字段CID
    private final List<String> propIds;

    private final ValueChangeBaseFormPlugin valueChangeBaseFormPlugin;

    public static List<ValueChangeBaseFormPlugin> getValueChangedEventPlugins(String propId) {
        List<ValueChangeBaseFormPlugin> valueChangeBaseFormPlugins = new ArrayList<>();
        for (ValueChangePluginEnum pluginEnum : ValueChangePluginEnum.values()) {
            if (pluginEnum.getPropIds().contains(propId)) {
                valueChangeBaseFormPlugins.add(pluginEnum.getValueChangeBaseFormPlugin());
            }
        }
        return valueChangeBaseFormPlugins;
    }
}
