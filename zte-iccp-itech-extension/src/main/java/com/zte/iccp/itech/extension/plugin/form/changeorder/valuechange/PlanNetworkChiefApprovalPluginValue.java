package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.FormModelProxyHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.AvailabilityEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.plugin.form.changeorder.helper.ReviewerHelper;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM;
import static com.zte.iccp.itech.extension.plugin.form.approvaldetails.constants.InlinePagePropsConstants.PAGE_STATUS_VALUE_EDIT;

/**
 * 网络处总工审核组
 *
 * <AUTHOR>
 * @date 2025-07-17 上午10:52
 **/
public class PlanNetworkChiefApprovalPluginValue implements ValueChangeBaseFormPlugin {

    @Override
    public void operate(ValueChangedEventArgs args) {
        checkOperatorAndRole(args.getModel(), args.getFormView(), false);
    }

    @Override
    public void loadData(LoadDataEventArgs args) {
        PageStatusEnum pageStatusEnum =  args.getFormView().getFormShowParameter().getPageStatus();
        if (!PAGE_STATUS_VALUE_EDIT.equals(pageStatusEnum.name())) {
            return;
        }
        checkOperatorAndRole(args.getModel(), args.getFormView(), true);
    }

    public void checkOperatorAndRole(IDataModel dataModel, IFormView formView, boolean isLoadData) {
        List<Employee> employees = EmployeeHelper.getModelEmployees(dataModel.getValue(NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM));
        if (isLoadData && !CollectionUtils.isEmpty(employees)) {
            return;
        }

        String productCategory = TextValuePairHelper.getValue(dataModel.getValue(FIELD_PRODUCT_CID));
        ApproverConfiguration queryParam = new ApproverConfiguration();
        queryParam.setApprovalNode(ApprovalTypeEnum.NETWORK_CHIEF_ENGINEER);
        queryParam.setBillStatus(AvailabilityEnum.ENABLED.name());
        List<String> accnTypes = FormModelProxyHelper.getSubFormCloumValueList(dataModel, OPERATION_OBJECT_TABLE_PROPERTY_KEY, ACCN_TYPE_CID);
        if (!CollectionUtils.isEmpty(accnTypes)) {
            queryParam.setOperator(OperatorEnum.getCnnValue(accnTypes.get(0)));
        }
        ApproverConfiguration approvalConfiguration = ApproverConfigAbility.getApprovalConfiguration(queryParam, productCategory, 0);
        if (approvalConfiguration == null) {
            return;
        }
        ReviewerHelper.setEmployeeByCid(formView, approvalConfiguration.getApproverGroups(), NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM);
    }
}
