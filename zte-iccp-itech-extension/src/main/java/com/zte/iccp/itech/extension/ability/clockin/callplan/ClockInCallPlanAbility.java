package com.zte.iccp.itech.extension.ability.clockin.callplan;

import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility;
import com.zte.iccp.itech.extension.common.helper.FilterHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.OperationLogTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallReasonEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.CallStatusEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.OperationLog;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.ZonedTime;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInCallPlan;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInCallee;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.spi.client.CcesClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.model.cces.dto.CcesAiCallingReq;
import com.zte.iccp.itech.extension.spi.model.cces.vo.CcesAiCallResultResp;
import com.zte.iccp.itech.extension.spi.model.cces.vo.CcesAiCallingResp;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.COMMA;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_COLON;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInCallPlanFieldConsts.*;
import static com.zte.iccp.itech.extension.spi.model.cces.vo.CcesAiCallResultResp.CONNECTED;
import static com.zte.iccp.itech.extension.spi.model.cces.vo.CcesAiCallResultResp.NOT_CONNECTED;

/**
 * 打卡呼叫计划
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/10
 */
@Slf4j
public class ClockInCallPlanAbility {

    /**
     * 小时 6点
     */
    private static final int HOUR_TIME = 6;

    /**
     * 偏移时间，单位（秒）
     */
    private static final Long OFFSET_TIME = 1800L;

    /**
     * 查询呼叫时间小于当前时间，状态为等待中或呼叫中的数据
     *
     * @return List<ClockInCallPlan>
     */
    public static List<ClockInCallPlan> queryTodoCallTask() {
        IFilter calling = new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.CALLING));
        IFilter tobeCalling = FilterHelper.newMultiFilter(
                new Filter(CALL_UTC_TIME, Comparator.LE, System.currentTimeMillis() / 1000),
                new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.WAITING)));

        return QueryDataHelper.query(
                ClockInCallPlan.class,
                Lists.newArrayList(),
                Lists.newArrayList((IFilter) calling.or(tobeCalling)));
    }

    /**
     * 触发呼叫
     *
     * @param todoCallList todoCallList
     */
    public static void triggerCall(List<ClockInCallPlan> todoCallList) {
        if (CollectionUtils.isEmpty(todoCallList)) {
            return;
        }
        // 相同手机号和相同的内容进行数据分组，在呼叫的时候如果取到其余数据，直接更新统一的searchId
        Map<String, List<ClockInCallPlan>> groupedMap = todoCallList.stream()
                .collect(Collectors.groupingBy(
                        plan -> plan.getCallee().getPhoneNum() + EN_COLON + plan.getContent(),
                        Collectors.toList()));

        // S2数据处理（状态为等待中，前置计划为空的数据）
        // 1.更新状态为呼叫中(避免下一个任务重复查出)
        updateCallPlanStatus(null, todoCallList, CallStatusEnum.CALLING);

        // 相同数据合并
        Map<String, ClockInCallPlan> mergedMap = todoCallList.stream()
                .collect(Collectors.toMap(
                        plan -> plan.getCallee().getPhoneNum() + EN_COLON + plan.getContent(),
                        plan -> plan,
                        (o1, o2) -> o1));

        List<String> userIds = mergedMap.values().stream()
                .map(clockInCallPlan -> clockInCallPlan.getCallee().getUserId())
                .collect(Collectors.toList());

        List<Employee> employees = HrClient.queryEmployeeInfo(userIds);
        Map<String, String> empUIIDMap = employees.stream().collect(Collectors.toMap(Employee::getEmpUIID, Employee::getEmpName));

        // 2.发起呼叫、写入日志表，异常和告警处理
        List<OperationLog> operationLogList = new ArrayList<>();
        for (Map.Entry<String, ClockInCallPlan> entry : mergedMap.entrySet()) {
            ClockInCallPlan clockInCallPlan = entry.getValue();
            // 2.1 调用ai远程呼叫
            CcesAiCallingResp ccesAiCallingResp = invokeAiCallings(clockInCallPlan, empUIIDMap.get(clockInCallPlan.getCallee().getUserId()));
            if (ccesAiCallingResp == null) {
                // 获取呼叫云信息为空，呼叫内容
                log.info("Get call cloud information is empty：{}，The call content is:{}", clockInCallPlan.getObjectId(), clockInCallPlan.getContent());
                continue;
            }
            // 相同数据处理
            sameClockInCallPlanUpdate(clockInCallPlan.getId(),ccesAiCallingResp.getSearchId(),groupedMap.get(entry.getKey()));
            // 2.2呼叫云呼叫后置处理
            invokeAiCallingsAfterProcessor(clockInCallPlan, ccesAiCallingResp.getSearchId(), operationLogList);
        }

        SaveDataHelper.batchCreate(operationLogList);
    }


    /**
     * 相同呼叫计划数据处理
     *
     * @param currentPlanId currentPlanId
     * @param searchId searchId
     * @param sameCountDataList sameCountDataList
     */
    private static void sameClockInCallPlanUpdate(String currentPlanId, String searchId, List<ClockInCallPlan> sameCountDataList) {
        if (sameCountDataList.size() > 1) {
            SaveDataHelper.batchUpdate(sameCountDataList.stream()
                    .filter(item -> !StringUtils.equals(currentPlanId, item.getId()))
                    .map(item -> new ClockInCallPlan() {{
                        setId(item.getId());
                        setResultSearchId(searchId);
                    }}).collect(Collectors.toList()));
        }
    }

    /**
     * 2呼叫云呼叫后置处理
     *
     * @param clockInCallPlan clockInCallPlan
     * @param searchId searchId
     * @param operationLogList operationLogList
     */
    private static void invokeAiCallingsAfterProcessor(ClockInCallPlan clockInCallPlan,
                                                       String searchId,
                                                       List<OperationLog> operationLogList) {
        // 1.写入日志表
        operationLogList.add(new OperationLog() {{
            setType(OperationLogTypeEnum.CLOCK_IN_CALL);
            setTargetId(clockInCallPlan.getId());
            setDetails(clockInCallPlan.getContent());
        }});
        // 2.更新呼叫记录id
        SaveDataHelper.update(new ClockInCallPlan() {{
            setId(clockInCallPlan.getId());
            setResultSearchId(searchId);
        }});
    }

    /**
     * 调用ai远程呼叫
     *
     * @param clockInCallPlan clockInCallPlan
     * @param userName 用户名
     * @return 异步呼叫结果
     */
    public static CcesAiCallingResp invokeAiCallings(ClockInCallPlan clockInCallPlan, String userName) {
        try {
            // 调用呼叫云呼叫接口
            return RetryUtils.get(() -> {
                List<CcesAiCallingResp> resps = CcesClient.aiCallings(Lists.newArrayList(
                        new CcesAiCallingReq() {{
                            setTel(clockInCallPlan.getCallee().getPhoneNum());
                            setName(userName);
                            setContent(clockInCallPlan.getContent());
                        }}));
                return !CollectionUtils.isEmpty(resps) ? resps.get(0) : null;
            });
        } catch (Exception e) {
            // 更新异常失败状态
            log.error("callCloudWaitHandler invoke CallCloudClient#call error，clock_in_call_plan.id：{}", clockInCallPlan.getId(), e);
            SaveDataHelper.update(new ClockInCallPlan() {{
                setCallStatus(CallStatusEnum.FAILED);
                setId(clockInCallPlan.getId());
            }});
            String title = "Failed to call after 3 retries, CallCloudClient#call clock_in_call_plan.id: " + clockInCallPlan.getId();
            AlarmUtils.major(title, e);
            throw e;
        }
    }

    /**
     * 获取呼叫用户工号和名字
     *
     * @param todoCallList todoCallList
     * @return Map<String, String>
     */
    private static Map<String, String> getEmpUIIDMap(List<ClockInCallPlan> todoCallList) {
        if (CollectionUtils.isEmpty(todoCallList)) {
            return Collections.emptyMap();
        }

        List<String> userIds = todoCallList.stream()
                .filter(item -> item.getCallee() != null)
                .map(item -> item.getCallee().getUserId())
                .distinct()
                .collect(Collectors.toList());

        List<Employee> employees = HrClient.queryEmployeeInfo(userIds);
        return employees.stream().collect(Collectors.toMap(Employee::getEmpUIID, Employee::getEmpName));
    }

    /**
     * 更新呼叫计划状态
     *
     * @param callPlans   todoCallList
     * @param status callStatusEnum
     */
    public static void updateCallPlanStatus(OperationCache opCache, List<ClockInCallPlan> callPlans, CallStatusEnum status) {
        List<ClockInCallPlan> toUpdate = callPlans.stream()
                .map(plan -> new ClockInCallPlan() {{
                    setId(plan.getId());
                    setCallStatus(status);
                }}).collect(Collectors.toList());

        if (opCache == null) {
            SaveDataHelper.batchUpdate(toUpdate);
        } else {
            opCache.update(toUpdate);
        }
    }

    /**
     *  1.先处理状态为呼叫中。
     *  2.调用远程呼叫查询结果结果，同时判断当前呼叫计划的id是否为另外一条数据的前置计划
     *      第一种场景：这条数据就是普通的呼叫计划，没有后置呼叫计划，直接修改状态（不管成功还是失败）
     *      第二种场景：这条数据为A：存在后置呼叫计划，如果呼叫成功，捞出全部的后置计划，可能有BCDEF，全部废止。如果呼叫失败，捞出B，呼叫B
     *  3.剩下的数据是状态为等待中，前置计划不为空的数据，这些数据在2里面要么废止了，要么调用下一个B呼叫了，最后能剩下来的数据只有CDEF的数据
     *
     * @param clockInCallPlanList clockInCallPlanList
     */
    public static void updateCallResult(List<ClockInCallPlan> clockInCallPlanList) {
        if (CollectionUtils.isEmpty(clockInCallPlanList)) {
            return;
        }

        OperationCache opCache = new OperationCache();

        // 1.S2中状态= 呼叫中的子集，记为S3
        List<ClockInCallPlan> callingPlans = clockInCallPlanList.stream()
                .filter(clockInCallPlan -> CallStatusEnum.CALLING == clockInCallPlan.getCallStatus())
                .collect(Collectors.toList());

        // 2.S2和S3点差集记为S4  [前置计划不为空，状态为等待中]的数据
        List<ClockInCallPlan> rearPlans = new ArrayList<>(clockInCallPlanList);
        rearPlans.removeAll(callingPlans);
        Map<String, ClockInCallPlan> prevPlanIdMap = rearPlans.stream()
                .collect(Collectors.toMap(
                        ClockInCallPlan::getPrevPlanId,
                        Functions.identity()));
        Map<String, String> empUIIDMap = getEmpUIIDMap(rearPlans);

        // 3.调用远程呼叫查询结果结果
        List<String> resultSearchIds = callingPlans.stream()
                .map(ClockInCallPlan::getResultSearchId)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        List<CcesAiCallResultResp> ccesAiCallResultResps = CcesClient.aiCallResult(resultSearchIds);
        Map<String, CcesAiCallResultResp> resultRearchIdMap = ccesAiCallResultResps.stream()
                .collect(Collectors.toMap(
                        CcesAiCallResultResp::getId,
                        Functions.identity()));

        // 更新呼叫状态
        opCache.update(callingPlans.stream()
                .filter(result -> result.getResultSearchId() != null)
                .map(result -> new ClockInCallPlan() {{
                    setId(result.getId());
                    setCallStatus(resultRearchIdMap.get(result.getResultSearchId()).getIsAnswer() == CONNECTED
                            ? CallStatusEnum.CONNECTED : CallStatusEnum.NOT_CONNECTED);
                }}).collect(Collectors.toList()));

        // 4.遍历S3呼叫中的子集,进行数据处理
        for (ClockInCallPlan callingPlan : callingPlans) {
            // 4.1 获取当前呼叫结果
            CcesAiCallResultResp ccesAiCallResultResp = resultRearchIdMap.get(callingPlan.getResultSearchId());
            if (ccesAiCallResultResp == null) {
                continue;
            }

            // 是否接听：1接听、0未接通
            int isAnswer = ccesAiCallResultResp.getIsAnswer();
            // 2.判断是否存在后置呼叫计划
            List<ClockInCallPlan> matchData = new ArrayList<>();
            // 使用递归，获取到匹配的matchData集,callingPlan.id = difference子集中某个对象的prevPlanId
            getAllMatchData(callingPlan.getId(), prevPlanIdMap, matchData);

            // 存在后置呼叫计划
            if (!CollectionUtils.isEmpty(matchData)) {
                if (isAnswer == CONNECTED) {
                    // 如果已接通，捞出全部的后置计划，可能有BCDEF，全部废止
                    updateCallPlanStatus(opCache, matchData, CallStatusEnum.ABOLISHED);
                } else if (isAnswer == NOT_CONNECTED) {
                    // 如果未接通，捞出B，呼叫B
                    ClockInCallPlan afterClockInCallPlan = prevPlanIdMap.get(callingPlan.getId());
                    List<CcesAiCallingResp> resp = CcesClient.aiCallings(Lists.newArrayList(new CcesAiCallingReq() {{
                        setTel(afterClockInCallPlan.getCallee().getPhoneNum());
                        setName(empUIIDMap.get(callingPlan.getCallee().getUserId()));
                        setContent(afterClockInCallPlan.getContent());
                    }}));

                    opCache.update(new ClockInCallPlan() {{
                        setId(afterClockInCallPlan.getId());
                        setCallStatus(CallStatusEnum.CALLING);
                        setResultSearchId(resp.get(0).getSearchId());
                    }});
                }
            }

            // 汇总呼叫已接通场景还要废止延时重复呼叫计划
            if (callingPlan.getReason() == CallReasonEnum.SUMMARY && isAnswer == CONNECTED) {
                List<ClockInCallPlan> callPlans = QueryDataHelper.query(
                        ClockInCallPlan.class,
                        Lists.newArrayList(ID),
                        Lists.newArrayList(
                                new Filter(REASON, Comparator.EQ, Lists.newArrayList(CallReasonEnum.SUMMARY)),
                                new Filter(OBJECT_ID, Comparator.EQ, callingPlan.getObjectId()),
                                new Filter(CALL_STATUS, Comparator.EQ, Lists.newArrayList(CallStatusEnum.WAITING))));
                updateCallPlanStatus(opCache, callPlans, CallStatusEnum.ABOLISHED);
            }
        }

        opCache.transFlush();
    }

    /**
     * 递归匹配数据
     *
     * @param prevPlanId 当前ClockInCallPlan对象的id
     * @param prevPlanIdMap ClockInCallPlan对象前置计划Map
     * @param matchData 匹配的数据集
     */
    private static void getAllMatchData(String prevPlanId, Map<String, ClockInCallPlan> prevPlanIdMap, List<ClockInCallPlan> matchData) {
        ClockInCallPlan nextPlan = prevPlanIdMap.get(prevPlanId);
        // 递归出口
        if (nextPlan == null) {
            return;
        }

        // 添加匹配的结果集
        matchData.add(nextPlan);
        // 递归调用以查找更多匹配项
        getAllMatchData(nextPlan.getId(), prevPlanIdMap, matchData);
    }

    /**
     * 处理打卡汇总
     */
    public static void handleClockInSummary(){
        // 1.检索打卡任务表，查询出【计划呼叫的UTC时间】小于当前时间的记录
        String msg = MessageConsts.ClockIn.CALLPLAN_CONTENT_SUMMARY_CALL;
        long time = System.currentTimeMillis() / 1000;
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        // 偏离时间查询条件和呼叫内容处理
        if (hour != HOUR_TIME) {
            time = time - OFFSET_TIME;
            msg = MessageConsts.ClockIn.CALLPLAN_CONTENT_OFFSET_SUMMARY_CALL;
        }
        List<ClockInTask> allTimeOutTasks = ClockInQueryAbility.queryAllTimeOutTask(time);

        // 2.再通过打卡任务表的变更单ID字段，查出各打卡任务/变更单从属的代表处，按代表处汇总出统计数据 (任务单的代表处+2层产品+客户标识分组)
        Map<String, Long> groupedChangeOrders = getChangeOrdersGroup(allTimeOutTasks);
        if (CollectionUtils.isEmpty(groupedChangeOrders)) {
            return;
        }

        // 4.创建汇总呼叫的呼叫计划及对应的重复呼叫计划 (任务单的代表处+2层产品+客户标识 + 星期n  来匹配值班人员)
        List<ClockInCallPlan> callPlans = new ArrayList<>();
        //TODO 需求待优化，目前只有国内做呼叫，所以都按北京时间的dayOfWeek取呼叫人
        ZonedTime date = new ZonedTime(TimeZoneEnum.BEIJING, new Date());
        for (Map.Entry<String, Long> entry : groupedChangeOrders.entrySet()) {
            String[] split = entry.getKey().split(COMMA);
            ClockInCallee clockInCallee = CalleeQueryAbility.getOfficeDutyPerson(date, split[0], split[1], split[2]);
            if (clockInCallee == null) {
                log.warn("当前值班人员配置表未配置，代表处:【{}】，2层产品：【{}】，客户标识：【{}】能匹配的数据或未配置当前日期：【{}】能匹配的数据",
                        split[0], split[1], split[2], date.zonedTimeString());
                continue;
            }

            for (int i = 0; i < 3; i++) {
                ClockInCallPlan callPlan = new ClockInCallPlan();
                callPlan.setReason(CallReasonEnum.SUMMARY);
                //办事处id
                callPlan.setObjectId(entry.getKey());
                //呼叫状态
                callPlan.setCallStatus(CallStatusEnum.WAITING);
                //UTC呼叫时间，每次循环+5分钟
                callPlan.setCallUtcTime((System.currentTimeMillis() / 1000) + i * 5 * 60);
                //呼叫对象
                callPlan.setCallee(new ClockInCallee(){{
                    setUserId(clockInCallee.getUserId());
                    setPhoneNum(clockInCallee.getPhoneNum());
                }});
                //呼叫内容
                callPlan.setContent(MsgUtils.getMessage(msg, entry.getValue()));
                callPlans.add(callPlan);
            }
        }
        SaveDataHelper.batchCreate(callPlans);
    }

    /**
     * 通过打卡任务表的变更单ID字段,获取变更单数据
     *
     * @param allTimeOutTasks allTimeOutTasks
     * @return List<ChangeOrder>
     */
    private static Map<String, Long> getChangeOrdersGroup(List<ClockInTask> allTimeOutTasks) {
        if (CollectionUtils.isEmpty(allTimeOutTasks)) {
            return Collections.emptyMap();
        }
        // 根据entity_type分组
        Map<BatchTaskTypeEnum, List<ClockInTask>> groupedTasks = allTimeOutTasks.stream()
                .collect(Collectors.groupingBy(ClockInTask::getEntityType));

        List<ChangeOrder> changeOrderList = new ArrayList<>();
        for (Map.Entry<BatchTaskTypeEnum, List<ClockInTask>> entry : groupedTasks.entrySet()) {
            List<String> changerOrderIds = entry.getValue().stream().map(ClockInTask::getChangeOrderId).collect(Collectors.toList());
            if (entry.getKey().getEntityClass() == BatchTask.class) {
                changeOrderList.addAll(ChangeOrderAbility.get(changerOrderIds, ChangeOrder.class));
            }else {
                changeOrderList.addAll(CollectionUtilsEx.copyListProperties(ChangeOrderAbility.get(changerOrderIds, SubcontractorChangeOrder.class), ChangeOrder.class));
            }
        }

        // 2.按代表处汇总出统计数据 (任务单的代表处+2层产品+客户标识分组)
        return groupChangeOrders(changeOrderList);
    }

    /**
     * 按代表处+2层产品+客户标识分组
     *
     * @param changeOrderList changeOrderList
     * @return Map<String, Long>
     */
    public static Map<String, Long> groupChangeOrders(List<ChangeOrder> changeOrderList) {
        if (CollectionUtils.isEmpty(changeOrderList)) {
            return MapUtils.newHashMap();
        }
        return changeOrderList.stream().collect(Collectors.groupingBy(changeOrder -> {
            // 任务单的代表处
            String responsibleDept = changeOrder.getResponsibleDept();
            // 2层产品
            String productCategory = ProductUtils.getLine(changeOrder.getProductCategory());
            // 客户标识
            String customerTypeFlag = changeOrder.getCustomerTypeFlag();
            return responsibleDept + COMMA + productCategory + COMMA + customerTypeFlag;
        }, Collectors.counting()));
    }

    /**
     * 处理呼叫计划
     */
    public static void handleCallPlan() {
        // 1.查询呼叫计划（状态=等待中 & 呼叫时间<当前时间 ）或状态 = 呼叫中的数据 记为S0
        List<ClockInCallPlan> clockInCallPlanList = queryTodoCallTask();
        if (CollectionUtils.isEmpty(clockInCallPlanList)) {
            return;
        }

        // 呼叫数据前置处理，过滤等待中状态，呼叫信息为空的数据更新为失败
        callBeforeProcessor(clockInCallPlanList);

        // 2.S0中状态 = 等待中 & 前置计划为空的子集，记为S1
        List<ClockInCallPlan> todoCallList = clockInCallPlanList.stream()
                .filter(clockInCallPlan -> CallStatusEnum.WAITING == clockInCallPlan.getCallStatus()
                        && StringUtils.isEmpty(clockInCallPlan.getPrevPlanId()))
                .collect(Collectors.toList());

        // 3.等待中状态，前置计划为空数据处理
        triggerCall(todoCallList);

        // 4.S0和S1点差集记为S2  （包含呼叫中的数据和前置计划不为空的子集）
        List<ClockInCallPlan> difference = new ArrayList<>(clockInCallPlanList);
        difference.removeAll(todoCallList);

        updateCallResult(difference);
    }

    /**
     * 呼叫数据前置处理
     * 1.过滤等待中状态，呼叫信息为空的数据更新为失败
     *
     * @param clockInCallPlanList clockInCallPlanList
     */
    private static void callBeforeProcessor(List<ClockInCallPlan> clockInCallPlanList) {
        Function<ClockInCallPlan, Boolean> noPhoneNum = plan ->
                plan.getCallStatus() == CallStatusEnum.WAITING
                        && (plan.getCallee() == null
                                || StringUtils.isBlank(plan.getCallee().getPhoneNum()));
        Function<ClockInCallPlan, Boolean> noSearchId = plan ->
                plan.getCallStatus() == CallStatusEnum.CALLING
                        && StringUtils.isBlank(plan.getResultSearchId());

        List<ClockInCallPlan> calleeEmptyList = clockInCallPlanList.stream()
                .filter(plan -> noPhoneNum.apply(plan) || noSearchId.apply(plan))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(calleeEmptyList)) {
            return;
        }

        // 呼叫信息为空或呼叫中数据未获取到呼叫云id的数据
        List<String> clockObjIds = calleeEmptyList.stream().map(ClockInCallPlan::getObjectId).distinct().collect(Collectors.toList());
        log.info("ClockInCallPlanAbility#callBeforeProcessor The call information is empty or the call cloud ID data was not obtained during the call.：{}", clockObjIds);
        updateCallPlanStatus(null, calleeEmptyList, CallStatusEnum.FAILED);
        clockInCallPlanList.removeAll(calleeEmptyList);
    }
}
