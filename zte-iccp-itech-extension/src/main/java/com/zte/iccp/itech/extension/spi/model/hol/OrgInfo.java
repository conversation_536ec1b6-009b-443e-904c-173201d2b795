package com.zte.iccp.itech.extension.spi.model.hol;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 组织信息
 * <AUTHOR> 10344315
 * @date 2025-07-21 下午8:29
 **/
@Getter
@Setter
public class OrgInfo {
    @JsonProperty(value = "id")
    private String id;

    @JsonProperty(value = "hrOrgID")
    private String hrOrgId;

    @JsonProperty(value = "hrOrgPID")
    private String hrOrgPId;

    @JsonProperty(value = "hrOldDeptNO")
    private String hrOldDeptNo;

    @JsonProperty(value = "hrOldDeptPNO")
    private String hrOldDeptPNo;

    @JsonProperty(value = "hrLevel")
    private String hrLevel;

    @JsonProperty(value = "hrOrgName")
    private String hrOrgName;

    @JsonProperty(value = "hrOrgNamePath")
    private String hrOrgNamePath;

    @JsonProperty(value = "orgIDPath")
    private String orgIdPath;

    @JsonProperty(value = "enabled")
    private Integer enabled;

    @JsonProperty(value = "orgLevel")
    private String orgLevel;

    @JsonProperty(value = "orgStatusID")
    private Integer orgStatusId;

    @JsonProperty(value = "isPartakePerfStats")
    private Integer isPartakePerfStats;

    @JsonProperty(value = "orgEstDate")
    private String orgEstDate;

    @JsonProperty(value = "remark")
    private String remark;

    @JsonProperty(value = "companyId")
    private String companyId;

    @JsonProperty(value = "companyName")
    private String companyName;

    @JsonProperty(value = "type")
    private String type;

    @JsonProperty(value = "tenantId")
    private String tenantId;
}
