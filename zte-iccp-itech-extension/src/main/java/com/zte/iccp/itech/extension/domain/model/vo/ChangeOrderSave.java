package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.INTERNAL_OPERATION_SOLUTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.MultiModeProdConsts.PLAN_OPERATION_ORDER_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.NE_LIST_FILE;


/**
 * 变更单 后台保存对象
 *
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@Getter
@Setter
@BaseEntity.Info("oc_apply")
public class ChangeOrderSave extends BaseEntity {

    @JsonProperty(value = ORDER_NO)
    private String orderNo;

    @JsonProperty(value = OPERATION_SUBJECT)
    @ApiModelProperty("操作主题")
    private String operationSubject;

    @JsonProperty(value = OPERATION_SUBJECT_SUFFIX)
    @ApiModelProperty("操作主题后缀")
    private String operationSubjectSuffix;

    @ApiModelProperty("主题前缀")
    @JsonProperty(value = OPERATION_SUBJECT_PREFIX)
    private String operationSubjectPrefix;

    @JsonProperty(value = RESPONSIBLE_DEPT)
    @ApiModelProperty("代表处")
    private Object responsibleDept;

    @JsonProperty(value = COUNTRY)
    @ApiModelProperty("国家")
    private Object country;

    @JsonProperty(value = PROVINCE)
    private Object province;

    @JsonProperty(value = CITY)
    private Object city;

    @JsonProperty(value = PRODUCT_CATEGORY)
    @ApiModelProperty("产品分类")
    private Object productCategory;

    @ApiModelProperty("是否属于GDPR管控项目，Y，是；N，否")
    @JsonProperty(value = IS_GDPR)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGdpr;

    @ApiModelProperty("是否政企，Y，是；N，否")
    @JsonProperty(value = IS_GOV_ENT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGovEnt;

    @ApiModelProperty("交付方式，1，远程交付；2，本地交付")
    @JsonProperty(value = DELIVERY_MODE)
    private Object deliveryMode;

    @JsonProperty(value = IS_EMERGENCY_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isEmergencyOperation;

    @ApiModelProperty("紧急操作原因简述")
    @JsonProperty(value = EMERGENCY_OPERATION_REASON)
    private String emergencyOperationReason;

    @JsonProperty(value = EMERGENCY_OPERATION_ATTACH)
    @ApiModelProperty("紧急操作附件")
    private Object emergencyOperationAttach;

    /**
     * 扩充实体属性 - 是否封网、管控期操作
     */
    @JsonProperty(value = IS_NET_CLOSE_OR_CONTROL_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNetCloseOrControlOperation;

    @JsonProperty(value = NET_CLOSE_OR_CONTROL_OPERATION_REASON)
    private String netCloseOrControlOperationReason;

    @ApiModelProperty("封网、管控期操作附件")
    @JsonProperty(value = CLOSE_OR_CONTROL_OPERATION_ATTACH)
    private Object closeOrControlOperationAttach;

    @JsonProperty(value = OPERATION_TYPE)
    @ApiModelProperty("操作类型")
    private Object operationType;

    @ApiModelProperty("操作类型分组")
    @JsonProperty(value = OPERATION_TYPE_GROUP)
    private String operationTypeGroup;

    @JsonProperty(value = OPERATION_REASON)
    @ApiModelProperty("操作原因")
    private Object operationReason;

    @JsonProperty(value = TIME_ZONE)
    private Object timeZone;

    @ApiModelProperty("计划操作开始时间")
    @JsonProperty(value = OPERATION_START_TIME)
    private Date operationStartTime;

    @ApiModelProperty("计划操作结束时间")
    @JsonProperty(value = OPERATION_END_TIME)
    private Date operationEndTime;

    @JsonProperty(value = NE_LIST_FILE)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    @ApiModelProperty("网元清单")
    private AttachmentFile neListFile;

    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;

    @ApiModelProperty("是否需要授权文件")
    @JsonProperty(value = IS_NEED_AUTHORIZATION_FILE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNeedAuthorizationFile;

    @JsonProperty(value = ACCN_TYPE)
    @ApiModelProperty("客户标识")
    private String customerTypeFlag;

    @JsonProperty(value = SOURCE)
    @ApiModelProperty("数据来源")
    private String source;

    @ApiModelProperty("是否首次应用（首次应用需发起方案会签）")
    @JsonProperty(value = CidConstants.FIELD_IS_FIRST_APPLICATION_CID)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isFirstApplication;

    @JsonProperty(value = GDPR_REQUIRE)
    @ApiModelProperty("GDPR要求")
    private Object gdprRequire;

    @JsonProperty(value = OPERATION_DESC)
    @ApiModelProperty("操作说明")
    private String operationDesc;

    @JsonProperty(value = OTHER_OPERATION_ACCESSORIES)
    @ApiModelProperty("其他操作附件")
    private List<MultiAttachmentFile> otherAttachments;

    @JsonProperty(value = PLAN_OPERATION_ORDER_ID)
    @ApiModelProperty("操作计划ID")
    private String planOperationOrderId;

    @JsonProperty(value = IS_SPECIAL_SCENARIO)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isSpecialScenario;

    @JsonProperty(value = IS_TECHNICAL_NOTICE)
    @ApiModelProperty("是否是技术通知单实施")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isTechnicalNotice;

    @JsonProperty(value = TRIGGER_TYPE)
    private Object triggerType;

    @JsonProperty(value = CHANGE_OPERATION_SOURCE)
    private Object changeOperationSource;

    @JsonProperty(value = SERVICE_DISCONNECT_DURATION)
    private Object serviceDisconnectDuration;

    @JsonProperty(value = INTERNAL_OPERATION_SOLUTION)
    @ApiModelProperty("内部操作方案")
    private Object internalOperationSolution;

    @JsonProperty(value = IMPORTANCE)
    private Object importance;

    @JsonProperty(value = RISK_EVALUATION)
    private Object riskEvaluation;

    @JsonProperty(value = OPERATION_LEVEL)
    private Object operationLevel;

    @ApiModelProperty("冲突")
    private String conflict;

    @JsonIgnore
    private List<TextValuePair> productManagementTeam;
}
