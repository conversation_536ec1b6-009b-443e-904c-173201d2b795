package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.ability.ApproverConfigAbility.getApprovalPersonsByType;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OperationObjectFieldConsts.PRODUCT_MODEL;

/**
 * 2.4.16  研发经理审核
 *
 * <AUTHOR>
 * @create 2024/7/18 上午10:23
 * 代表处属于国内：【产品】（优先级：产品型号>产品小类）+【营销/片区】=国内营销+【是否政企】精确匹配，读取审批人和审批组，其中审批人+组=当前跟进人，审批组同样具备审批权限
 * 代表处属于国际：【产品】（优先级：产品型号>产品小类）+【片区】（【是否政企】取值不考虑）精确匹配，读取审批人和审批组，其中审批人+组=当前跟进人，审批组同样具备审批权限
 */
@Deprecated
public class RdManagerHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    @Override
    public List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        List<ApproverConfiguration> approverConfigurations = getApprovers(changeOrder);
        List<String> persons = new ArrayList<>();
        approverConfigurations.forEach(item ->
                persons.addAll(getApprovalPersonsByType(item, null)));
        return persons.stream().distinct().collect(Collectors.toList());
    }

    public List<ApproverConfiguration> getApprovers(ChangeOrder changeOrder) {
        // 查询变跟单操作对象的产品型号idpath
        List<String> idPaths = new ArrayList<>();
        List<OperationObject> operationObjects = QueryDataHelper.
                query(OperationObject.class, Arrays.asList(PRODUCT_MODEL), changeOrder.getId());
        if (!CollectionUtils.isEmpty(operationObjects)) {
            List<String> productIds = operationObjects.stream().map(OperationObject::getProductModel)
                    .collect(Collectors.toList());
            idPaths = NisAbility.queryProductModelIdPath(productIds);
        }

        List<ApproverConfiguration> approverConfigurations = Lists.newArrayList();
        String responsibleDept = changeOrder.getResponsibleDept();
        String productCategory = changeOrder.getProductCategory();
        // 代表处属于国内：【产品】（优先级：产品型号>产品小类）+【营销/片区】=国内营销+【是否政企】精确匹配，
        ApproverConfiguration queryParam = new ApproverConfiguration();
        queryParam.setApprovalNode(ApprovalTypeEnum.RD_DEPARTMENT);
        queryParam.setProdSubCategory(changeOrder.getProductCategory());
        queryParam.setProdTeam(ProductUtils.getTeam(productCategory));
        queryParam.setProdLine(ProductUtils.getLine(productCategory));
        queryParam.setProdMainCategory(ProductUtils.getMain(productCategory));
        queryParam.setProdSubCategory(productCategory);
        queryParam.setSales(ResponsibleUtils.getSales(responsibleDept));
        if (DeptTypeEnum.INNER == ResponsibleUtils.getDeptType(responsibleDept)) {
            queryParam.setIsGov(changeOrder.getIsGovEnt());
        } else {
            queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
        }
        List<ApproverConfiguration> approvers = ApproverConfigAbility.findApproverConfiguration(queryParam);
        if (CollectionUtils.isEmpty(approvers) || CollectionUtils.isEmpty(idPaths)) {
            return approvers;
        }

        List<String> finalIdPaths = idPaths;
        approverConfigurations = approvers.stream().filter(item -> StringUtils.hasText(item.getProductModelId())
                        && finalIdPaths.contains(item.getProductModelId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(approverConfigurations)) {
            return approvers;
        }

        return approverConfigurations;
    }
}
