package com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo;

import com.zte.iccp.itech.extension.domain.model.base.Employee;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApprovedByConfigVO {

    // 审核人
    private List<Employee> approvedBy;

    // 审核组
    private List<Employee> approvedTeam;
}
