package com.zte.iccp.itech.extension.openapi.common;

import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.common.utils.SysAuthUtils;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.LookupValueQueryDto;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.LookupValueVO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 通用OpenApi
 *
 * <AUTHOR>
 * @create 2025/3/20 上午9:09
 */
public class CommonOpenApi extends AbstractOpenApi {

    /*
     * 查询快码信息
     * */
    public ServiceData<List<LookupValueVO>> getLookupValues(HttpServletRequest request,
                                                      @RequestBody LookupValueQueryDto lookupValueQueryDto) {
        SysAuthUtils.auth(request);
        if (!StringUtils.hasText(lookupValueQueryDto.getLookupType())) {
            throw new LcapBusiException("lookupType is not empty");
        }
        ServiceData<List<LookupValueVO>> result = new ServiceData<>();
        result.setBo(LookupValueHelper.getLookupValues(lookupValueQueryDto));
        result.setCode(success());
        return result;
    }

    private static RetCode success() {
        return new RetCode() {{
            setCode(RetCode.SUCCESS_CODE);
            setMsgId(RetCode.SUCCESS_MSGID);
            setMsg(RetCode.SUCCESS_MSG);
        }};
    }
}
