package com.zte.iccp.itech.extension.openapi.ai;

import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.StandardSchemeAbility;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.model.StandardScheme;
import com.zte.iccp.itech.extension.openapi.model.ai.AiTemplateTransmissionCallbackDto;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.AI_FLAG_CID;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.DOC_ID_CID;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.SUCCESS;

/**
 * <AUTHOR>
 * @date 2024/6/13 下午4:35
 */
@Slf4j
public class AiTemplateTransmissionOpenapi extends AbstractOpenApi {

    public ServiceData<Boolean> aiCallBack(@RequestBody AiTemplateTransmissionCallbackDto dto) {

        if (null == dto || StringUtils.isBlank(dto.getSchemeId()) || StringUtils.isBlank(dto.getStatus())) {
            return failure();
        }
        StandardScheme existStandardScheme = StandardSchemeAbility.queryOneBySchemeName(dto.getSchemeId());
        log.info("AI上传标准方案回调接口根据方案ID查询的实体数据:" + existStandardScheme);

        Map<String, Object> values = Maps.newHashMap();
        values.put(DOC_ID_CID, dto.getDocId());
        values.put(AI_FLAG_CID, SUCCESS.equals(dto.getStatus()));
        log.info("AI上传标准方案回调接口返回日志:" + dto.getLog());
        boolean updateFlag = SaveDataHelper.update(StandardScheme.class, dto.getSchemeId(), values);
        if (updateFlag) {
            log.info("AI上传标准方案回调接口更新成功:" + dto.getSchemeId());
            return success();
        }
        log.error("AI上传标准方案回调接口更新数据库失败:" + dto.getSchemeId());
        return failure();
    }

    private static ServiceData<Boolean> success() {
        return new ServiceData<Boolean>() {{
            setBo(true);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
                setMsgId(RetCode.SUCCESS_MSGID);
                setMsg(RetCode.SUCCESS_MSG);
            }});
        }};
    }

    private static ServiceData<Boolean> failure() {
        return new ServiceData<Boolean>() {{
            setBo(false);
            setCode(new RetCode() {{
                setCode(RetCode.VALIDATIONERROR_CODE);
                setMsgId(RetCode.VALIDATIONERROR_MSGID);
                setMsg(RetCode.VALIDATIONERROR_MSG);
            }});
        }};
    }
}
