package com.zte.iccp.itech.extension.spi.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.spi.enums.gtdcenter.ConstraintTypeEnum;
import com.zte.iccp.itech.extension.spi.model.ProductClassificationDto;
import com.zte.iccp.itech.extension.spi.model.TreeServiceObjectVo;
import com.zte.iccp.itech.extension.spi.model.gtdcenter.DataConstraint;
import com.zte.iccp.itech.extension.spi.model.nis.*;
import com.zte.iccp.itech.extension.spi.model.query.*;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.common.constant.LangConst;
import com.zte.paas.lcap.ddm.domain.helper.util.ApiClient;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DEFAULT_PAGE_NO;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_2000;

/**
 * NIS 系统外部接口查询
 */
public class NisClient {
    // ============= 服务域名 ==============
    private static final String SERVICE_HOST = "nis.host";

    // ============= 服务名称 ==============
    /**
     * NIS
     */
    private static final String NIS_SERVICE_NAME = "nis.service.name";

    /**
     * gtdCenter
     */
    private static final String GTD_CENTER_SERVICE_NAME = "gtdCenter.service.name";

    // ============= 请求地址 ==============
    /**
     * 查询网络信息
     */
    private static final String MULTI_CONDITIONS_QUERY_NETWORK = "nis.service.url.queryNetworks";

    /**
     * 对外网络产品列表查询
     */
    private static final String MULTI_CONDITIONS_QUERY_PRODMODELS = "nis.service.url.queryProdModels";

    /**
     * 查询客户标识
     */
    private static final String MULTI_CONDITIONS_QUERY_CUSTOMER_FLAG = "nis.service.url.queryCustomerFlag";
    /**
     * 查询产品树信息
     */
    private static final String MULTI_CONDITIONS_QUERY_PRODUCT_TREE = "nis.service.url.queryProductionTree";

    /**
     * 查询组织树信息
     */
    private static final String MULTI_CONDITIONS_QUERY_DEPARTMENT_TREE = "nis.service.url.queryOrganizationTree";

    /**
     * 查产品型号
     */
    private static final String PRODUCT_MODEL_QUERY = "nis.service.url.queryProductModel";

    /**
     * 查局点信息
     */
    private static final String CUSTOMER_NE_QUERY = "nis.service.url.queryCustomerNe";

    /**
     * 查产品信息
     */
    private static final String PRODUCT_INFO_QUERY = "nis.service.url.queryProductInfo";

    private static final String PRODUCT_CLASS_QUERY = "nis.service.url.queryProductClass";

    private static final String LOGICAL_NE_QUERY = "nis.service.url.queryLogicalNe";

    // ============= 请求参数 ==============
    // =========== 查询网络信息 ============
    /**
     * 网络名称
     */
    private static final String NETWORK_QUERY_NETWORK_NAME = "networkName";

    /**
     * 网络状态
     */
    private static final String NETWORK_QUERY_NETWORK_STATUS = "networkStatus";

    /**
     * 责任单位
     */
    private static final String NETWORK_QUERY_RESPONSIBLE_DEPT = "responsibleDept";

    /**
     * 产品分类路径
     */
    private static final String NETWORK_QUERY_PROD_CLASS_PATH = "prodClassIdPath";

    /**
     * 客户标识
     */
    private static final String NETWORK_QUERY_CUSTOMER_TYPE_FLAG = "customerTypeFlag";

    /**
     * 产品分类路径
     */
    private static final String NETWORK_QUERY_NETWORK_IDS = "networkIds";

    /**
     * 资产产品类型
     */
    private static final String NETWORK_QUERY_ASSET_PROD_CLASS_ID_PATH = "assetProdClassIdPath";

    /**
     * 非受限制主体或受限主体但已备案 标识
     */
    private static final String NETWORK_QUERY_NON_RESTRICTED_OR_REGISTERED = "nonRestrictedOrRegistered";

    /**
     * 非受限制主体或受限主体但已备案 标识
     */
    private static final String NETWORK_QUERY_GOV_ENT_FLAG = "govEntFlag";

    /**
     * 非受限制主体或受限主体但已备案 标识
     */
    private static final String NETWORK_QUERY_NAME_AND_CODE_KEYWORD = "nameAndCodeKeyword";

    /**
     * 资产产品型号第五级ids
     */
    private static final String NETWORK_QUERY_ZTE_PROD_MODEL_PATH = "zteProdModelIds";

    /**
     * 客户网络名称
     */
    private static final String NETWORK_QUERY_CUSTOMER_NETWORK_NAME = "customerNetworkName";

    /**
     * 国家/地区
     */
    private static final String NETWORK_QUERY_COUNTRY_AREA_CODE = "countryAreaCode";

    /**
     * 省/州
     */
    private static final String NETWORK_QUERY_PROVINCE_STATE_CODES = "provinceStateCodes";

    /**
     * 地市
     */
    private static final String NETWORK_QUERY_CITY_CODES = "cityCodes";

    /**
     * 关键字
     */
    private static final String CUSTOMER_FLAG_QUERY_KEYWORD = "keyword";

    /**
     * 页码
     */
    private static final String PAGE_NUM = "pageNum";

    /**
     * 页容
     */
    private static final String PAGE_SIZE = "pageSize";

    /**
     * 最大树层级
     */
    private static final String MAX_LEVEL = "maxLevel";

    /**
     * 是否启用
     */
    private static final String ONLY_ENABLED = "onlyEnabled";

    /**
     * 是否启用
     */
    private static final String NIS_TENANT_CODE = "itechCloud";

    /**
     * NIS外部服务请求X-Itp-Value中开始时间
     */
    private static final String NIS_ITP_VALUE_START_TIME = "startTime";

    /**
     * NIS外部服务请求X-Itp-Value中租户编码
     */
    private static final String NIS_ITP_VALUE_TENANT_CODE = "tenantCode";

    /**
     * NIS外部服务请求X-Itp-Value中token
     */
    private static final String NIS_ITP_VALUE_TOKEN = "token";

    /**
     * NISgtdbff的秘钥
     */
    private static final String NIS_SECRET_KEY = "nis.service.url.header.xItpValue.secretKey";

    /**
     * NIS局点查询--网络id
     */
    private static final String NIS_SITE_QUERY_NETWORK_ID = "networkId";

    /**
     * NIS局点查询--产品小类id
     */
    private static final String NIS_SITE_QUERY_SUBCATEGORY_ID = "subCategoryIds";

    /**
     * NIS局点查询--客户网元名称
     */
    private static final String NIS_SITE_QUERY_CUSTOMER_NE_NAME = "customerNeName";

    /** 缓存 Key: 客户网络名称 */
    private static final String KEY_CUSTOMER_NETWORK_NAME = "customer_network_name";

    /** 组织树 / 产品树 缓存时间 */
    private static final long TREE_DATA_CACHE_EXPIRE_SECONDS = 24 * 60 * 60;

    /**
     * 构建默认请求头
     * @return Map<String, Object>
     */
    public static Map<String, Object> extendRequestHeader() {
        long startTime = System.currentTimeMillis();
        String sha256HexToken = EncryptorUtils.hmacSha256(startTime + NIS_TENANT_CODE + ConfigHelper.get(NIS_SECRET_KEY));
        StringBuilder itpValue = new StringBuilder();
        itpValue.append(NIS_ITP_VALUE_START_TIME).append(CommonConstants.EQUAL_SYMBOL).append(startTime).append(CommonConstants.SEMICOLON)
                .append(NIS_ITP_VALUE_TENANT_CODE).append(CommonConstants.EQUAL_SYMBOL).append(NIS_TENANT_CODE).append(CommonConstants.SEMICOLON)
                .append(NIS_ITP_VALUE_TOKEN).append(CommonConstants.EQUAL_SYMBOL).append(sha256HexToken);

        Map<String, Object> headerMap = new HashMap<>();
        if (StringUtils.hasText(ContextHelper.getToken())) {
            headerMap.put(X_AUTH_VALUE, ContextHelper.getToken());
        }
        headerMap.put(X_ITP_VALUE, itpValue.toString());
        headerMap.put(X_LANG_ID, ContextHelper.getLangId());
        headerMap.put(X_EMP_NO, ContextHelper.getEmpNo());
        headerMap.put(X_TENANT_ID, ContextHelper.getTenantId());

        return headerMap;
    }

    /**
     * 基本接口查询方法 - 根据产品 idPath 查询产品信息
     * @param productIdList
     * @return List<BasicProductInfo>
     */
    public static List<BasicProductInfo> queryProductInfo(List<String> productIdList) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        // 2.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(PRODUCT_INFO_QUERY);
        Object productInfo = ApiClient.invokeServiceByUrl(url, POST, null, productIdList, headerMap);

        // 3.对象转换
        return JsonUtils.parseArray(productInfo, BasicProductInfo.class);
    }

    /**
     * 查询产品全路径名称
     * @param productIdList
     * @return Map<String, String>
     */
    public static Map<String, String> queryProductPathName(List<String> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return new HashMap<>();
        }

        // 1.查询产品信息
        List<BasicProductInfo> basicProductInfoList = queryProductInfo(productIdList);
        if (CollectionUtils.isEmpty(basicProductInfoList)) {
            return new HashMap<>();
        }

        // 2.包装返回数据
        boolean languageFlag = LangConst.ZH_CN.equals(ContextHelper.getLangId());
        return basicProductInfoList.stream()
                .filter(item -> StringUtils.hasText(languageFlag ? item.getNamePathZh() : item.getNamePathEn()))
                .collect(Collectors.toMap(
                        item -> item.getPidPath() + item.getId(),
                        item -> languageFlag ? item.getNamePathZh() : item.getNamePathEn(),
                        (v1, v2) -> v1));
    }

    /**
     * 查询产品名称
     * 1.如果传入1层id，查询结果为产品经营团队名称。如果传入2层id，查询结果为产品线名称，依此类推
     * 2.传入的id不能带后缀/，例如通过产品小类获取产品小类名称，参数为 产品经营团队/产品线/产品大类/产品小类，最后结果集为产品小类名称
     *
     * @param productIdList
     * @return Map<String, String>
     */
    public static Map<String, String> queryProductName(List<String> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return new HashMap<>();
        }

        // 1.查询产品信息
        List<BasicProductInfo> basicProductInfoList = queryProductInfo(productIdList);
        if (CollectionUtils.isEmpty(basicProductInfoList)) {
            return new HashMap<>();
        }

        // 2.包装返回数据
        boolean languageFlag = LangConst.ZH_CN.equals(ContextHelper.getLangId());
        return basicProductInfoList.stream()
                .filter(item -> StringUtils.hasText(languageFlag ? item.getNameZh() : item.getNameEn()))
                .collect(Collectors.toMap(
                        item -> item.getPidPath() + item.getId(),
                        item -> languageFlag ? item.getNameZh() : item.getNameEn(),
                        (v1, v2) -> v1));
    }


    public static PageRows<NetworkElementVo> queryNisSiteList(SiteQuery siteQuery){
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();
        // 2.构建请求体
        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put(PAGE_NUM, siteQuery.getPageNum());
        requestBodyMap.put(PAGE_SIZE, siteQuery.getPageSize());
        requestBodyMap.put(NIS_SITE_QUERY_NETWORK_ID, siteQuery.getNetworkId());
        requestBodyMap.put(NIS_SITE_QUERY_SUBCATEGORY_ID, siteQuery.getSubCategoryIds());
        requestBodyMap.put(NIS_SITE_QUERY_CUSTOMER_NE_NAME, siteQuery.getCustomerNeName());

        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(CUSTOMER_NE_QUERY);
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, requestBodyMap, headerMap);

        // 4.对象转换
        String objectJson = JsonUtils.toJsonString(object);
        PageRows<NetworkElementVo>pageRows = JsonUtils.parseObject(objectJson, PageRows.class);

        String networkElementListJson = JsonUtils.toJsonString(pageRows.getRows());
        List<NetworkElementVo> networkList = JsonUtils.parseArray(networkElementListJson, NetworkElementVo.class);
        pageRows.setRows(networkList);
        return pageRows;
    }

    /**
     * 检索 NIS 网络数据 - 服务发现方式
     * @return List<NisNetwork>
     */
    public static PageRows<NisNetwork> queryNisNetworkList(NisNetworkQuery nisNetworkQuery) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        // 2.构建请求体
        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put(NETWORK_QUERY_NETWORK_NAME, nisNetworkQuery.getNetworkName());
        requestBodyMap.put(NETWORK_QUERY_NETWORK_STATUS, nisNetworkQuery.getNetworkStatus());
        requestBodyMap.put(NETWORK_QUERY_RESPONSIBLE_DEPT, nisNetworkQuery.getResponsibleDept());
        requestBodyMap.put(NETWORK_QUERY_PROD_CLASS_PATH, nisNetworkQuery.getProdClass());
        requestBodyMap.put(NETWORK_QUERY_NETWORK_IDS, nisNetworkQuery.getNetworkIds());
        //资产产品类型
        // 临时兼容 NIS, 为 null 是正常全量数据
        if (!CollectionUtils.isEmpty(nisNetworkQuery.getAssetProdClassIdPath())) {
            requestBodyMap.put(NETWORK_QUERY_ASSET_PROD_CLASS_ID_PATH, nisNetworkQuery.getAssetProdClassIdPath());
        }
        requestBodyMap.put(NETWORK_QUERY_ZTE_PROD_MODEL_PATH, nisNetworkQuery.getZteProdModelIds());
        //客户网络名称
        requestBodyMap.put(NETWORK_QUERY_CUSTOMER_NETWORK_NAME, nisNetworkQuery.getCustomerNetworkName());
        //国家/地区
        requestBodyMap.put(NETWORK_QUERY_COUNTRY_AREA_CODE, nisNetworkQuery.getCountryAreaCode());
        //省/州
        requestBodyMap.put(NETWORK_QUERY_PROVINCE_STATE_CODES, nisNetworkQuery.getProvinceStateCodes());
        //地市
        requestBodyMap.put(NETWORK_QUERY_CITY_CODES, nisNetworkQuery.getCityCodes());
        // 非受限制主体或受限主体但已备案 标识
        requestBodyMap.put(NETWORK_QUERY_NON_RESTRICTED_OR_REGISTERED,nisNetworkQuery.getNonRestrictedOrRegistered());
        // 是否政企
        requestBodyMap.put(NETWORK_QUERY_GOV_ENT_FLAG,nisNetworkQuery.getGovEntFlag());
        // 网络名称或CODE
        requestBodyMap.put(NETWORK_QUERY_NAME_AND_CODE_KEYWORD,nisNetworkQuery.getNameAndCodeKeyword());

        requestBodyMap.put(PAGE_NUM, nisNetworkQuery.getPageNum());
        requestBodyMap.put(PAGE_SIZE, nisNetworkQuery.getPageSize());

        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(MULTI_CONDITIONS_QUERY_NETWORK);
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, requestBodyMap, headerMap);

        // 4.对象转换
        String objectJson = JsonUtils.toJsonString(object);
        PageRows<NisNetwork> networkPageRows = JsonUtils.parseObject(objectJson, PageRows.class);

        String networkListJson = JsonUtils.toJsonString(networkPageRows.getRows());
        List<NisNetwork> networkList = JsonUtils.parseArray(networkListJson, NisNetwork.class);
        networkPageRows.setRows(networkList);

        return networkPageRows;
    }

    /**
     * 查询网络信息（客户网络名称）
     * key - 网络ID   value - 客户网络名称
     */
    public static Map<String, String> queryNetworkName(List<String> networkIdList) {
        if (CollectionUtils.isEmpty(networkIdList)) {
            return new HashMap<>();
        }

        // 1.缓存检索
        Map<String, String> cacheNetworkName = CacheUtils.get(
                KEY_CUSTOMER_NETWORK_NAME, new TypeReference<Map<String, String>>() {});
        if (Objects.isNull(cacheNetworkName)) {
            cacheNetworkName = new HashMap<>();
        }

        // 2.筛选无缓存数据
        Map<String, String> networkNames = new HashMap<>();
        List<String> noneCacheIds = Lists.newArrayList();
        for (String networkId : networkIdList) {
            String networkName = cacheNetworkName.get(networkId);
            if (StringUtils.hasText(networkName)) {
                networkNames.put(networkId, networkName);
            } else {
                noneCacheIds.add(networkId);
            }
        }

        if (CollectionUtils.isEmpty(noneCacheIds)) {
            return networkNames;
        }

        // 3.无缓存数据检索 NIS 获取
        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();
        nisNetworkQuery.setNetworkIds(noneCacheIds);
        nisNetworkQuery.setPageNum(DEFAULT_PAGE_NO);
        nisNetworkQuery.setPageSize(networkIdList.size());

        PageRows<NisNetwork> pageRows = NisClient.queryNisNetworkList(nisNetworkQuery);
        Map<String, String> noneCacheNames = pageRows.getRows().stream()
                .filter(item -> StringUtils.hasText(item.getCustomerNetworkName()))
                .collect(Collectors.toMap(NisNetwork::getNetworkId, NisNetwork::getCustomerNetworkName, (v1, v2) -> v1));

        // 4.新数据设置缓存
        cacheNetworkName.putAll(noneCacheNames);
        CacheUtils.set(KEY_CUSTOMER_NETWORK_NAME, cacheNetworkName, TREE_DATA_CACHE_EXPIRE_SECONDS);

        networkNames.putAll(noneCacheNames);
        return networkNames;
    }

    /**
     * 查询网络信息
     * @param networkIdList
     * @return Map<String, NisNetwork>
     */
    public static List<NisNetwork> queryNetwork(List<String> networkIdList) {
        if (CollectionUtils.isEmpty(networkIdList)) {
            return new ArrayList<>();
        }

        // 1.包装请求参数
        NisNetworkQuery nisNetworkQuery = new NisNetworkQuery();
        nisNetworkQuery.setNetworkIds(networkIdList);
        nisNetworkQuery.setPageNum(DEFAULT_PAGE_NO);
        nisNetworkQuery.setPageSize(networkIdList.size());

        // 2.查询网络信息
        PageRows<NisNetwork> pageRows = NisClient.queryNisNetworkList(nisNetworkQuery);
        if (CollectionUtils.isEmpty(pageRows.getRows())) {
            return new ArrayList<>();
        }

        // 3.包装返回
        return pageRows.getRows();
    }


    /**
     * 对外网络产品列表查询
     *
     * @param nisNetworkQuery nisNetworkQuery
     * @return List<ProductModel>
     */
    public static PageRows<ProductModel> queryProdModels(NisNetworkQuery nisNetworkQuery) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put(PAGE_NUM, nisNetworkQuery.getPageNum());
        requestBodyMap.put(PAGE_SIZE, nisNetworkQuery.getPageSize());

        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) +
                String.format(ConfigHelper.get(MULTI_CONDITIONS_QUERY_PRODMODELS), nisNetworkQuery.getNetworkId());
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, requestBodyMap, headerMap);

        // 4.对象转换
        PageRows<ProductModel> networkPageRows = JsonUtils.parseObject(object, PageRows.class);
        List<ProductModel> networkList = JsonUtils.parseArray(networkPageRows.getRows(), ProductModel.class);
        networkPageRows.setRows(networkList);

        return networkPageRows;
    }
    /**
     * 由于nis资产产品分类只支持到第四级，如果存在五层，拆出最后一层单独作为入参
     * assetProdClassIdPath： 资产产品类型1-4层
     * zteProdModelIds：资产产品类型第五层
     *
     * @param prodClassCid 产品类型
     * @param nisNetworkQuery 网络查询对象
     */
    public static void prodClassInfoProcessor(NisNetworkQuery nisNetworkQuery,Object prodClassCid) {
        List<Map<String, Object>> prodClassInfo = PropertyValueConvertUtil.getListMap(prodClassCid);
        List<String> prodClassPathList = ConvertUtil.getDropDownValue(prodClassInfo);
        if (!CollectionUtils.isEmpty(prodClassPathList)) {

            // 创建两个用于存储不同数据的列表
            List<String> zteProdModelIdList = null;
            List<String> assetProdClassIdPathList = new ArrayList<>();

            // 遍历原始列表,判断是否存在五层，如果超出 进行拆分
            for (String path : prodClassPathList) {
                int slashCount = countSlashes(path);
                if (slashCount == CommonConstants.INTEGER_FIVE) {
                    if (CollectionUtils.isEmpty(zteProdModelIdList)) {
                        zteProdModelIdList = new ArrayList<>();
                    }
                    // 如果斜杠数量为5，则分割字符串并存储到相应的列表
                    String[] parts = path.split(CommonConstants.FORWARD_SLASH);
                    //获取到末尾第五层
                    String lastPart = (parts[parts.length - 1]);
                    zteProdModelIdList.add(lastPart);
                    //前面的4层还是放到原资产产品分类
                    String prodClassSuffix = (parts[parts.length - 1]) + CommonConstants.FORWARD_SLASH;
                    assetProdClassIdPathList.add(path.split(prodClassSuffix)[0]);
                } else {
                    // 如果斜杠数量不为5，则直接添加到lastPartList
                    assetProdClassIdPathList.add(path);
                }
            }
            nisNetworkQuery.setAssetProdClassIdPath(assetProdClassIdPathList);
            nisNetworkQuery.setZteProdModelIds(zteProdModelIdList);
        }
    }

    /**
     * 拆分层数
     *
     * @param str str
     * @return 层数
     */
    private static int countSlashes(String str) {
        int count = 0;
        for (char c : str.toCharArray()) {
            if (c == '/') {
                count++;
            }
        }
        return count;
    }

    public static PageRows<ProductModel> queryProductModels(ProductModelQuery productModelQuery) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();


        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(PRODUCT_MODEL_QUERY);
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, productModelQuery, headerMap);

        // 4.对象转换
        String objectJson = JsonUtils.toJsonString(object);
        PageRows<ProductModel> pageRows = JsonUtils.parseObject(objectJson, PageRows.class);

        String networkListJson = JsonUtils.toJsonString(pageRows.getRows());
        List<ProductModel> networkList = JsonUtils.parseArray(networkListJson, ProductModel.class);
        pageRows.setRows(networkList);

        return pageRows;
    }

    /**
     * 数据查询 - 产品树
     */
    public static List<ProductClassificationTree> queryProductTree(Integer treeLevel) {
        // 1.缓存检索
        String cacheKey = CidConstants.COMPONENT_PRODUCT_TREE_CID + CommonConstants.UNDER_SCORE + treeLevel;
        List<ProductClassificationTree> treeData = CacheUtils.get(
                cacheKey, new TypeReference<List<ProductClassificationTree>>() {});
        if (!CollectionUtils.isEmpty(treeData)) {
            return treeData;
        }

        // 2.缓存中不存在，检索 NIS 获取
        Map<String, Object> headerMap = extendRequestHeader();

        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put(MAX_LEVEL, treeLevel);
        requestBodyMap.put(ONLY_ENABLED, true);

        String url = ConfigHelper.get(SERVICE_HOST)
                + ConfigHelper.get(NIS_SERVICE_NAME)
                + ConfigHelper.get(MULTI_CONDITIONS_QUERY_PRODUCT_TREE);
        Object treeDataObject = ApiClient.invokeServiceByUrl(url, GET, requestBodyMap, null, headerMap);

        // 3.对象转换 + 加入缓存
        ProductClassificationTreeVo productClassificationTreeVo
                = JsonUtils.parseObject(treeDataObject, ProductClassificationTreeVo.class);
        treeData = productClassificationTreeVo.getVoList();
        CacheUtils.set(cacheKey, treeData, TREE_DATA_CACHE_EXPIRE_SECONDS);

        return treeData;
    }

    /**
     * 数据查询 - 组织树
     */
    public static List<OrganizationTreeVo> queryOrganizationTree() {
        // 1.缓存检索
        String cacheKey = CidConstants.COMPONENT_ORGANIZATION_TREE_CID
                + CommonConstants.UNDER_SCORE
                + ContextHelper.getLangId();
        List<OrganizationTreeVo> treeData = CacheUtils.get(
                cacheKey, new TypeReference<List<OrganizationTreeVo>>() {});
        if (!CollectionUtils.isEmpty(treeData)) {
            return treeData;
        }

        // 2.缓存中不存在，检索 NIS 获取
        Map<String, Object> headerMap = extendRequestHeader();
        Map<String, Object> requestBodyMap = new HashMap<>();

        String url = ConfigHelper.get(SERVICE_HOST)
                + ConfigHelper.get(NIS_SERVICE_NAME)
                + ConfigHelper.get(MULTI_CONDITIONS_QUERY_DEPARTMENT_TREE);
        Object treeDataObject = ApiClient.invokeServiceByUrl(url, GET, requestBodyMap, null, headerMap);

        // 3.对象转换 + 加入缓存
        treeData = JsonUtils.parseArray(treeDataObject, OrganizationTreeVo.class);
        CacheUtils.set(cacheKey, treeData, TREE_DATA_CACHE_EXPIRE_SECONDS);

        return treeData;
    }

    /**
     * 查询 NIS 组织树 - 平铺
     */
    public static List<OrganizationTreeVo> queryTiledOrganizationTree() {
        // 1.检索 组织树 数据
        List<OrganizationTreeVo> treeData = queryOrganizationTree();

        // 2.平铺组织树
        List<OrganizationTreeVo> tiledTreeData = Lists.newArrayList();
        tiledOrganizationTree(treeData, tiledTreeData);

        return tiledTreeData;
    }

    /**
     * 平铺组织树
     */
    private static void tiledOrganizationTree(
            List<OrganizationTreeVo> treeData,
            List<OrganizationTreeVo> tiledTreeData) {

        for (OrganizationTreeVo organizationNode : treeData) {
            tiledTreeData.add(organizationNode);

            if (!CollectionUtils.isEmpty(organizationNode.getChild())) {
                tiledOrganizationTree(organizationNode.getChild(), tiledTreeData);
            }
        }
    }


    /**
     * 分页查询nis客户标识
     *
     * @param nisCustomerFlagQuery 查询条件对象
     * @return 分页结果
     */
    public static PageRows<CustomerFlagResult> queryCustomerFlag(CustomerFlagQuery nisCustomerFlagQuery){
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        // 2.构建请求体
        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put(CUSTOMER_FLAG_QUERY_KEYWORD, nisCustomerFlagQuery.getKeyword());
        requestBodyMap.put(PAGE_NUM, nisCustomerFlagQuery.getPageNum());
        requestBodyMap.put(PAGE_SIZE, nisCustomerFlagQuery.getPageSize());

        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(MULTI_CONDITIONS_QUERY_CUSTOMER_FLAG);
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, requestBodyMap, headerMap);

        // 4.对象转换
        String objectJson = JsonUtils.toJsonString(object);
        PageRows<CustomerFlagResult> customerFlagPageRows = JsonUtils.parseObject(objectJson, PageRows.class);

        String customerFlagListJson = JsonUtils.toJsonString(customerFlagPageRows.getRows());
        List<CustomerFlagResult> customerFlagList = JsonUtils.parseArray(customerFlagListJson, CustomerFlagResult.class);
        customerFlagPageRows.setRows(customerFlagList);
        return customerFlagPageRows;
    }

    /**
     * 检索 NIS 网络数据 - 用于网络集功能
     * @return List<NisNetwork>
     */
    public static PageRows<NisNetwork> queryNisNetworkSet(NisNetworkQuery nisNetworkQuery) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        // 2.构建请求体
        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put(NETWORK_QUERY_NETWORK_NAME, nisNetworkQuery.getNetworkName());
        requestBodyMap.put(NETWORK_QUERY_RESPONSIBLE_DEPT, nisNetworkQuery.getResponsibleDept());
        requestBodyMap.put(NETWORK_QUERY_PROD_CLASS_PATH, nisNetworkQuery.getProdClass());
        requestBodyMap.put(NETWORK_QUERY_CUSTOMER_TYPE_FLAG, nisNetworkQuery.getCustomerTypeFlag());
        requestBodyMap.put(PAGE_NUM, nisNetworkQuery.getPageNum());
        requestBodyMap.put(PAGE_SIZE, nisNetworkQuery.getPageSize());

        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(MULTI_CONDITIONS_QUERY_NETWORK);
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, requestBodyMap, headerMap);

        // 4.对象转换
        String objectJson = JsonUtils.toJsonString(object);
        PageRows<NisNetwork> networkPageRows = JsonUtils.parseObject(objectJson, PageRows.class);

        String networkListJson = JsonUtils.toJsonString(networkPageRows.getRows());
        List<NisNetwork> networkList = JsonUtils.parseArray(networkListJson, NisNetwork.class);
        networkPageRows.setRows(networkList);

        return networkPageRows;
    }

    /**
     * 获取用户权限
     * @param userId
     * @param constraintTypeList
     * @return Map<String, List<String>>
     */
    public static Map<ConstraintTypeEnum, Set<String>> getUserConstraint(String userId,
                                                                         List<ConstraintTypeEnum> constraintTypeList) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        // 2.查询 gtdCenter 系统
        String interfaceUrl = "/constraints/" + userId + "/queries";
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(GTD_CENTER_SERVICE_NAME) + interfaceUrl;
        Object object = ApiClient.invokeServiceByUrl(url, POST, new HashMap<>(), constraintTypeList, headerMap);

        // 3.数据包装
        List<DataConstraint> constraintInfo = JsonUtils.parseArray(object, DataConstraint.class);
        return constraintInfo.stream()
                .collect(Collectors.toMap(DataConstraint::getConstraintType, DataConstraint::getConstraintValues, (v1, v2) -> v1));
    }

    /**
     * 查询核心网产品型号=产品小类产品小类
     * @return List<NisNetwork>
     */
    public static List<ProductClassificationDto> queryProductModelNames(ProductQuery params) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + "/api/productclassifications/product/queries";
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, params, headerMap);
        return JsonUtils.parseArray(object, ProductClassificationDto.class);
    }

    public static List<PdmProdModelResp> getPdmProdModels(List<String> nisProdModelIds) {
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME)
                + "/api/productmodels/pdm/queries";
        Map<String, Object> headerMap = extendRequestHeader();

        Object obj = ApiClient.invokeServiceByUrl(url, POST, MapUtils.newHashMap(), nisProdModelIds, headerMap);
        return JsonUtils.parseObject(obj, new TypeReference<List<PdmProdModelResp>>() {});
    }

    /**
     * 查询产品类型详情
     * @return List<NisNetwork>
     */
    public static List<ProductClassification> queryProductClassification(ProductClassificationQuery params) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + "/api/productclassifications/queries/info";
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, params, headerMap);
        return JsonUtils.parseArray(object, ProductClassification.class);
    }

    /**
     * @param name
     * @param level
     * @return
     */
    public static List<OrganizationTreeVo> queryOrganization(String name, String level) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();

        // 2.构建请求体
        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put("name", name);
        requestBodyMap.put("level", level);

        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + "/api/organizations/queries";
        Object object = ApiClient.invokeServiceByUrl(url, GET, requestBodyMap, null, headerMap);

        return JsonUtils.parseArray(object, OrganizationTreeVo.class);
    }

    public static List<TreeServiceObjectVo> queryProdClass(TreeServiceObjectQuery query) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();
        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(PRODUCT_CLASS_QUERY);
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, query, headerMap);

        // 4.对象转换
        String objectJson = JsonUtils.toJsonString(object);
        PageRows<TreeServiceObjectVo> pageRows = JsonUtils.parseObject(objectJson, PageRows.class);

        String networkListJson = JsonUtils.toJsonString(pageRows.getRows());

        return JsonUtils.parseArray(networkListJson, TreeServiceObjectVo.class);
    }

    public static Map<String, TreeServiceObjectVo> getProdClassMap(List<String> ids) {
        TreeServiceObjectQuery query = new TreeServiceObjectQuery();
        query.setCodeList(ids);
        List<TreeServiceObjectVo> vos = queryProdClass(query);
        return vos.stream().collect(Collectors.toMap(TreeServiceObjectVo::getCode, Function.identity()));
    }

    public static PageRows<CoreNetConfigVo> queryLogicalNe(LogicalNeQuery query) {
        Map<String, Object> headerMap = extendRequestHeader();
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(LOGICAL_NE_QUERY);
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, query, headerMap);

        String objectJson = JsonUtils.toJsonString(object);
        PageRows<CoreNetConfigVo> pageRows = JsonUtils.parseObject(objectJson, PageRows.class);

        String networkListJson = JsonUtils.toJsonString(pageRows.getRows());
        List<CoreNetConfigVo> networkList = JsonUtils.parseArray(networkListJson, CoreNetConfigVo.class);
        pageRows.setRows(networkList);

        return pageRows;

    }

    public static List<CoreNetConfigVo> getLogicalNes(LogicalNeQuery query) {
        List<CoreNetConfigVo> vos = Lists.newArrayList();
        query.setPageSize(INTEGER_2000);

        long total = 0;
        do {
            PageRows<CoreNetConfigVo> pageRows = queryLogicalNe(query);
            if (!CollectionUtils.isEmpty(pageRows.getRows())) {
                vos.addAll(pageRows.getRows());
                total = pageRows.getTotal();
                query.setPageNum(query.getPageNum() + 1);
            }
        } while (total > vos.size());

        return vos;
    }

    public static List<NetworkElementVo> getNisElementVoByIds(List<String> elementIds) {
        if(CollectionUtils.isEmpty(elementIds)){
            return new ArrayList<>();
        }

        // 构建请求头
        Map<String, Object> headerMap = extendRequestHeader();
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + ConfigHelper.get(CUSTOMER_NE_QUERY) + "/ids";
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, elementIds, headerMap);
        String objectJson = JsonUtils.toJsonString(object);
        return JsonUtils.parseArray(objectJson, NetworkElementVo.class);
    }

    /**
     * 根据查询条件查询 组织结构信息
     */
    public static List<TreeServiceObjectVo> queryOrganizationClass(TreeServiceObjectQuery query) {
        // 1.构建请求头
        Map<String, Object> headerMap = extendRequestHeader();
        // 3.查询 NIS 系统
        String url = ConfigHelper.get(SERVICE_HOST) + ConfigHelper.get(NIS_SERVICE_NAME) + "/api/organizations/tree/service/object";
        Object object = ApiClient.invokeServiceByUrl(url, POST, null, query, headerMap);

        // 4.对象转换
        String objectJson = JsonUtils.toJsonString(object);
        PageRows<TreeServiceObjectVo> pageRows = JsonUtils.parseObject(objectJson, PageRows.class);

        String networkListJson = JsonUtils.toJsonString(pageRows.getRows());

        return JsonUtils.parseArray(networkListJson, TreeServiceObjectVo.class);
    }
}
