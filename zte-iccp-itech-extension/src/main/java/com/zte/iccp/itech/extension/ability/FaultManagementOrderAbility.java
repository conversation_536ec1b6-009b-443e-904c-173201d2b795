package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.domain.model.entity.SatisfactionResponsiblePerson;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.PERCENT;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.FaultManageOrderFieldConsts.TASK_CODE;
import static com.zte.iccp.itech.extension.domain.constant.entity.SatisfactionResponsiblePersonConsts.*;

public class FaultManagementOrderAbility {
    /**
     * 检索故障管理单 - CSC 单号
     * @param cscCodeList
     * @return List<FaultManagementOrder>
     */
    public static List<FaultManagementOrder> queryOrderByCscCode(List<String> cscCodeList) {
        if (CollectionUtils.isEmpty(cscCodeList)) {
            return Lists.newArrayList();
        }

        List<String> fieldList = Lists.newArrayList();

        Filter codeFilter = new Filter(TASK_CODE, Comparator.IN, cscCodeList);
        List<IFilter> conditionFilterList = Lists.newArrayList(codeFilter);

        return QueryDataHelper.query(FaultManagementOrder.class, fieldList, conditionFilterList);
    }

    /**
     * 检索故障管理单 - ID
     */
    public static FaultManagementOrder queryOrderById(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }

        return QueryDataHelper.get(FaultManagementOrder.class, Lists.newArrayList(), id);
    }

    /**
     * 更新故障管理单
     * @param faultManagementOrderList
     */
    public static void batchUpdate(List<FaultManagementOrder> faultManagementOrderList) {
        if (CollectionUtils.isEmpty(faultManagementOrderList)) {
            return;
        }

        SaveDataHelper.batchUpdate(faultManagementOrderList);
    }

    /**
     * 新增故障管理单
     * @param faultManagementOrder
     * @return String
     */
    public static String insert(FaultManagementOrder faultManagementOrder) {
        if (Objects.isNull(faultManagementOrder)) {
            return CommonConstants.EMPTY_STRING;
        }

        return SaveDataHelper.create(faultManagementOrder);
    }

    /**
     * 查询客户满意度责任人（仅用于保存责任人配置时的唯一性校验，不适用于正常查询数据）
     * @param countryList
     * @param organizationId
     * @param productId
     * @return: java.util.List<com.zte.iccp.itech.extension.domain.model.entity.SatisfactionResponsiblePerson>
     * @author: 朱小安 10335201
     * @date: 2024/8/22 下午5:28
     */
    public static List<SatisfactionResponsiblePerson> querySatisfactionResponsiblePersonList(List<String> countryList,String organizationId,String productId) {
        if (!StringUtils.hasText(organizationId)) {
            return Lists.newArrayList();
        }

        List<String> fieldList = Lists.newArrayList(ORGANIZATION_ID,PRODUCT_ID,COUNTRY);

        List<IFilter> filters = Lists.newArrayList();
        filters.add(new Filter(ORGANIZATION_ID, Comparator.LIKE, organizationId + PERCENT));
        if(!StringUtils.hasText(productId)){
            filters.add(new Filter(PRODUCT_ID, Comparator.IS_NULL, null));
        }else{
            filters.add(new Filter(PRODUCT_ID, Comparator.LIKE, productId + PERCENT));
        }
        if(CollectionUtils.isEmpty(countryList)){
            filters.add(new Filter(COUNTRY, Comparator.IS_NULL, null));
        }else{
            filters.add(new Filter(COUNTRY, Comparator.IN, countryList));
        }

        return QueryDataHelper.query(SatisfactionResponsiblePerson.class, fieldList, filters);

    }


    public static void batchUpdateSatisfactionResponsiblePerson(List<SatisfactionResponsiblePerson> satisfactionResponsiblePersonList) {
        if (CollectionUtils.isEmpty(satisfactionResponsiblePersonList)) {
            return;
        }

        SaveDataHelper.batchUpdate(satisfactionResponsiblePersonList);
    }



}
