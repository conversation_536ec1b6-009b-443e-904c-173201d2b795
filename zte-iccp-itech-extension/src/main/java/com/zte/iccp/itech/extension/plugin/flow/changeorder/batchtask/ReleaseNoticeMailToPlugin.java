package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.BatchReleaseNoticeMailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;


/**
 * 批次任务-发布通告
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/18
 */
public class ReleaseNoticeMailToPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        BatchReleaseNoticeMailPlugin plugin = new BatchReleaseNoticeMailPlugin();
        return plugin.anyTrigger(body,out);
    }
}
