package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.ProdTreeNodeIdDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;
import static com.zte.iccp.itech.extension.domain.constant.subentity.OpAssocProdNetServDeptConfirmFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/26
 */
@Getter
@Setter
@BaseSubEntity.Info(value = "operation_association_prod_net", parent = ChangeOrder.class)
public class OpAssocProdNetServDeptConfirm extends BaseOpAssocProdConfirmEntity {
    @JsonProperty(value = PRODUCT_TYPE)
    @JsonDeserialize(using = ProdTreeNodeIdDeserializer.class)
    private String productType;

    @JsonProperty(value = IS_RELATED_PROD_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isRelatedProdApproval;

    @JsonProperty(value = APPROVAL)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee approver;

    @JsonProperty(value = APPROVAL_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> approvalTeam;

    @JsonProperty(value = INTEGRATED_ASSOCIATED_PRODUCT_NET_ZH)
    private String assocProdNameZh;

    @JsonProperty(value = INTEGRATED_ASSOCIATED_PRODUCT_NET_EN)
    private String assocProdNameEn;
}