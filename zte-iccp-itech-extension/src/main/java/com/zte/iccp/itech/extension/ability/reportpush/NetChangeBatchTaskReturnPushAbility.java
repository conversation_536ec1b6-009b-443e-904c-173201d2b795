package com.zte.iccp.itech.extension.ability.reportpush;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.entity.OrderBy;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.BusinessConsts;
import com.zte.iccp.itech.extension.domain.constant.EmailConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.spi.client.ICenterClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.dto.orm.Range;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.helper.entity.OrderEnum.DESC;
import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.NET_SERVICE_SALES;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.NetworkChangeFieldConsts.PLAN_START_TIME;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum.NETWORK_CHANGE_BATCH;
import static com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH;

/**
 * <AUTHOR>
 * @date 2025/4/9 下午5:37
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class NetChangeBatchTaskReturnPushAbility {
    private static final int SIX_AM = 6;

    private static final int TWENTY_TWO = 22;

    private static final int PAGE_SIZE = 5000;

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    private static final String RETURNED_FORMAT = "%s+%s";

    private static final String LINE_FEED = "\n";

    private static final String TITLE_MSG_ID = "network.change.report.returned.title";

    public static void domesticBatchTaskReturnPush() {
        // 获取当前系统时间
        LocalDateTime now = LocalDateTime.now();

        LocalDateTime yesterday22Pm = now.minusDays(1).withHour(TWENTY_TWO).withMinute(0).withSecond(0).withNano(0);

        Date startDate = Date.from(yesterday22Pm.atZone(ZoneId.systemDefault()).toInstant());

        // 获取当天06:00的时间
        LocalDateTime sixAm = now.toLocalDate().atTime(SIX_AM, 0);
        Date endDate = Date.from(sixAm.atZone(ZoneId.systemDefault()).toInstant());

        List<IFilter> iFilters = getInitFilters(DeptTypeEnum.INTER, startDate, endDate);

        int total = QueryDataHelper.queryCount(NetworkChangeAssignment.class, iFilters);

        String today = DateUtils.dateToString(new Date(), DATE_FORMAT);
        if (total <= 0) {
            log.info("{} domesticBatchTaskReturnPush total is 0", today);
            return;
        }

        List<String> assignmentFields = Lists.newArrayList(ID, ENTITY_ID, ASSIGNMENT_CODE, ASSIGNMENT_NAME, PRODUCT_CLASSIFICATION);
        List<String> batchTaskFields = Lists.newArrayList(ID, BATCH_NAME, BATCH_CODE, OPERATION_RESULT, IS_ROLLBACK_DONE);

        List<String> returnCodeAndNames = Lists.newArrayList();
        List<String> executeCodeAndNames = Lists.newArrayList();
        int pageNum = 0;
        do {
            pageNum++;
            Range range = new Range(pageNum, PAGE_SIZE);
            List<NetworkChangeAssignment> assignments =
                    QueryDataHelper.query(NetworkChangeAssignment.class, assignmentFields, iFilters, range, new OrderBy(ID, DESC));

            List<String> batchIds = assignments.stream().map(NetworkChangeAssignment::getEntityId).collect(Collectors.toList());

            List<BatchTask> batchTasks = QueryDataHelper.get(BatchTask.class, batchTaskFields, batchIds);

            List<SubcontractorBatchTask> subBatchTasks = QueryDataHelper.get(SubcontractorBatchTask.class, batchTaskFields, batchIds);

            //过滤出 操作已回退的批次
            returnCodeAndNames.addAll(batchTasks.stream().filter(item -> BoolEnum.Y.equals(item.getIsRollbackDone()))
                    .map(item -> String.format(RETURNED_FORMAT, item.getBatchCode(), item.getBatchName()))
                    .collect(Collectors.toSet()));

            returnCodeAndNames.addAll(subBatchTasks.stream().filter(item -> BoolEnum.Y.equals(item.getIsRollbackDone()))
                    .map(item -> String.format(RETURNED_FORMAT, item.getBatchCode(), item.getBatchName()))
                    .collect(Collectors.toSet()));

            //过滤出  执行中：有非撤销打卡记录，但没有操作结果的批次
            List<String> nonOperationResultIds = Lists.newArrayList();
            nonOperationResultIds.addAll(batchTasks.stream().filter(item -> CollectionUtils.isEmpty(item.getOperationResult()))
                    .map(BatchTask::getId).collect(Collectors.toList()));
            nonOperationResultIds.addAll(subBatchTasks.stream().filter(item -> CollectionUtils.isEmpty(item.getOperationResult()))
                    .map(SubcontractorBatchTask::getId).collect(Collectors.toList()));

            if (CollectionUtils.isEmpty(nonOperationResultIds)) {
                continue;
            }

            List<ClockInTask> clockInTaskList = QueryDataHelper.query(ClockInTask.class, Lists.newArrayList(ID, ClockInTaskFieldConsts.BATCH_TASK_ID),
                    Lists.newArrayList(new Filter(ClockInTaskFieldConsts.BATCH_TASK_ID, Comparator.IN, nonOperationResultIds)));

            Map<String, List<ClockInRecord>> taskId2RecordMap = ClockInQueryAbility.getClockInRecordList(
                    clockInTaskList.stream().map(ClockInTask::getId).distinct().collect(Collectors.toList()));

            List<String> executeBatchIds = clockInTaskList.stream()
                    .filter(item -> taskId2RecordMap.containsKey(item.getId()))
                    .map(ClockInTask::getBatchTaskId).distinct().collect(Collectors.toList());

            executeCodeAndNames.addAll(assignments.stream().filter(item -> executeBatchIds.contains(item.getEntityId()))
                    .map(item -> String.format(RETURNED_FORMAT, item.getAssignmentCode(), item.getAssignmentName()))
                    .collect(Collectors.toSet()));

        } while (pageNum * PAGE_SIZE < total);

        int returnedTaskCount = returnCodeAndNames.size();
        int executeTaskCount = executeCodeAndNames.size();

        Set<String> codeAndNames = new LinkedHashSet<>();
        codeAndNames.addAll(returnCodeAndNames);
        codeAndNames.addAll(executeCodeAndNames);

        List<String> empNos = ReportPushAbility.getEmpNos(NET_SERVICE_SALES, null);


        String titleZh = MsgUtils.getLangMessage(ZH_CN, TITLE_MSG_ID, today, total, returnedTaskCount, executeTaskCount);
        String titleEn = MsgUtils.getLangMessage(EN_US, TITLE_MSG_ID, today, total, returnedTaskCount, executeTaskCount);
        String info = String.join(LINE_FEED, codeAndNames);
        Map<String, Object> data = MapUtils.newHashMap(EmailConsts.ICENTER_MESSAGE_TITLE_ZH, titleZh,
                EmailConsts.ICENTER_MESSAGE_TITLE_EN, titleEn,
                EmailConsts.MESSAGE_INFO_ZH, info,
                EmailConsts.MESSAGE_INFO_EN, info);

        ICenterClient.sendICenter(TemplateIdEnum.NETWORK_CHANGE_REPORT_PUSH, empNos, data);
    }

    public static List<IFilter> getInitFilters(DeptTypeEnum deptType, Date startDate, Date endDate) {
        List<IFilter> iFilters = Lists.newArrayList();

        if (deptType == DeptTypeEnum.INTER) {
            List<OrganizationTreeVo> tiledTreeData = NisClient.queryTiledOrganizationTree();
            List<String> domesticThreePartsOrgIds = tiledTreeData.stream()
                    .filter(item -> item.getOrgIdPath().startsWith(BusinessConsts.ENGINEERING_SERVICE_THREE_PARTS_OPERATION))
                    .map(OrganizationTreeVo::getHrOrgId)
                    .collect(Collectors.toList());

            IFilter orgFilter = new Filter(REPRESENTATIVE_OFFICE, Comparator.IN, domesticThreePartsOrgIds)
                    .or(new Filter(MARKETING, Comparator.IN, Lists.newArrayList(BusinessConsts.DOMESTIC_MARKETING)));

            iFilters.add(orgFilter);
        }

        iFilters.add(new Filter(ASSIGNMENT_TYPE, Comparator.IN,
                Lists.newArrayList(NETWORK_CHANGE_BATCH.getValue(), SUBCONTRACT_NETWORK_CHANGE_BATCH.getValue())));

        iFilters.add(new Filter(PLAN_START_TIME, Comparator.GE, startDate));

        iFilters.add(new Filter(PLAN_START_TIME, Comparator.LE, endDate));

        iFilters.add(new Filter(ASSIGNMENT_STATUS, Comparator.NOT_IN,
                Lists.newArrayList(AssignmentStatusEnum.ABOLISH.getValue(), AssignmentStatusEnum.SUSPENDED.getValue())));

        return iFilters;
    }


}
