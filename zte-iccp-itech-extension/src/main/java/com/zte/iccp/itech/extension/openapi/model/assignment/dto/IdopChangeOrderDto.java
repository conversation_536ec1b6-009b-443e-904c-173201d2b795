package com.zte.iccp.itech.extension.openapi.model.assignment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.iccp.itech.zlic.model.NeListInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/8/27 下午8:09
 */
@ApiModel("idop网络变更单")
@Getter
@Setter
public class IdopChangeOrderDto {

    @ApiModelProperty("变更单ID")
    private String changeOrderId;

    private String orderNo;

    @ApiModelProperty("主题后缀")
    private String operationSubjectSuffix;

    @ApiModelProperty("是否GDPR管控项目")
    private Boolean isGdpr;

    @ApiModelProperty("GDPR要求")
    private Boolean gdprRequire;

    @ApiModelProperty("交付方式")
    private int deliveryMode;

    @ApiModelProperty("代表处")
    private String department;

    @ApiModelProperty("产品小类名称")
    private String product;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("操作原因")
    private String operationReason;

    @ApiModelProperty("所属时区")
    private String timeZone;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationEndTime;

    @ApiModelProperty("是否紧急操作")
    private Boolean isEmergencyOperation;

    @ApiModelProperty("是否政企")
    private Boolean isGovEnt;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("是否首次应用（首次应用需发起方案会签）")
    private Boolean isFirstApplication;

    @ApiModelProperty("提单人")
    private String createBy;

    @ApiModelProperty("是否需要授权文件")
    private Boolean isNeedAuthorizationFile;

    @ApiModelProperty("网元数量 ")
    private Integer neCount;

    @ApiModelProperty("网络-局点名称 ")
    private List<NetworkOfficeDTO> network;

    @ApiModelProperty("网元清单")
    private NeListInfo neListInfo;

    @ApiModelProperty("操作人员")
    private List<String> operators;

    @ApiModelProperty("冲突")
    private boolean timeConflict;

    @ApiModelProperty("变更单ID")
    private String orderName;

    @ApiModelProperty("国家")
    private String country;

    @ApiModelProperty("操作说明")
    private String description;

}
