package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ChangeOrderFieldConsts {

    // ====================== 网络变更操作单基础字段 =======================
    public static final String ORDER_NO = "co_no";

    public static final String BILL_STATUS = "billstatus";

    /**
     * 操作主题 - 网络变更单名称
     */
    public static final String OPERATION_SUBJECT = "operation_subject";

    public static final String COUNTRY = "country";

    public static final String PROVINCE = "province";

    public static final String CITY = "area";

    public static final String RESPONSIBLE_DEPT = "organization_id";

    public static final String PRODUCT_CATEGORY = "product_id";

    public static final String MULTI_PROD_GUARANTEE = "is_product_linkage_guarantee";

    public static final String IS_GOV_ENT = "is_government_enterprise";

    public static final String IMPORTANCE = "importance";

    public static final String RISK_EVALUATION = "risk_evaluation";

    public static final String OPERATION_LEVEL = "operation_level";

    public static final String OPERATION_SCENARIO = "operation_scenario";

    public static final String IS_MULTI_MODE = "is_multimode";

    public static final String IS_OPER_PLAN_FROM_TEST_DEPT = "is_oper_plan_from_test_dept";

    public static final String IS_OPER_PLAN_NEED_TEST_VERIFY = "is_oper_plan_need_test_verify";

    public static final String IS_DEV_DEPT_APPROVAL = "is_dev_dept_approval";

    public static final String IS_EMERGENCY_OPERATION = "is_emergency_operation";

    public static final String EMERGENCY_OPERATION_FLAG = "emergency_operation_flag";

    public static final String IS_UPGRADE_TECHNOLOGY = "is_upgrade_technology";

    public static final String UPGRADE_TECHNOLOGY_REQUIRE = "upgrade_technology_require";

    /**
     * 紧急操作原因概述
     */
    public static final String EMERGENCY_OPERATION_REASON = "emergency_operation_reason";

    public static final String IS_NET_CLOSE_OR_CONTROL_OPERATION = "is_net_close_or_control_operation";

    public static final String NETWORK_SEALING_OPERATION_FLAG = "network_sealing_operation_flag";

    public static final String NET_CLOSE_OR_CONTROL_OPERATION_REASON = "net_close_or_control_operation_reason";

    /**
     * 封网、管控期操作附件
     */
    public static final String CLOSE_OR_CONTROL_OPERATION_ATTACH = "close_or_control_operation_attach";

    /**
     * 有线产品操作检查单
     */
    public static final String WIRE_PRODUCT_CHECKLIST = "wire_product_checklist";

    /**
     * license文件核对举证
     */
    public static final String LICENSE_FILE = "license_file";

    public static final String OPERATION_TYPE = "operation_type";

    /**
     * 操作类型分组
     */
    public static final String OPERATION_TYPE_GROUP = "operation_type_group";

    /** 操作原因 */
    public static final String OPERATION_REASON = "operation_reason";

    /** 时区 */
    public static final String TIME_ZONE = "time_zone";

    /**
     * 计划操作开始时间
     */
    public static final String OPERATION_START_TIME = "operation_start_time";

    /**
     * 计划操作结束时间
     */
    public static final String OPERATION_END_TIME = "operation_end_time";

    /**
     * 保障方式
     */
    public static final String GUARANTEE_MODE = "guarantee_mode";

    public static final String IS_GUARANTEE_SOLUTION = "is_guarantee_solution";

    public static final String IS_BCN_LINKAGE_GUARANTEE = "is_bcn_linkage_guarantee";

    public static final String NE_LIST_FILE = "ne_list_file";

    public static final String CUSTOMER_ID = "customer_id";

    public static final String LOGICAL_NE = "selectfield_logical_ne";

    /**
     * 客户标识
     */
    public static final String ACCN_TYPE = "accn_type";

    /**
     * 附件描述
     */
    public static final String ATTACHMENT_DESC = "attachment_desc";

    /**
     * 是否需要操作步骤打卡
     */
    public static final String IS_CHECK_IN = "is_check_in";

    /**
     * 操作预计投入人天
     */
    public static final String ESTIMATED_INVESTMENT_TIME = "estimated_investment_time";

    /**
     * 模型包
     */
    public static final String MODEL_PACKAGE = "model_package";

    /**
     * 多模从属产品
     */
    public static final String MULTIMODE_PRODUCT = "multi_mode_product";

    /**
     * 是否属于GDPR管控项目，Y，是；N，否
     */
    public static final String IS_GDPR = "is_gdpr";

    /**
     * 交付方式，1，远程交付；2，本地交付
     */
    public static final String DELIVERY_MODE = "delivery_mode";

    /**
     * 邮件抄送人
     */
    public static final String MAIL_COPY = "mail_copy";

    /**
     * 操作主题前缀
     */
    public static final String OPERATION_SUBJECT_PREFIX = "operation_subject_prefix";

    /**
     * 触发类型
     */
    public static final String TRIGGER_TYPE = "trigger_type";

    /**
     * 是否首次应用
     */
    public static final String IS_FIRST_APPLICATION = "is_first_application";

    /**
     * 是否技术通知单实施
     */
    public static final String IS_TECHNICAL_NOTICE = "is_technical_notice";

    /**
     * 是否有任务书
     */
    public static final String IS_TASK_STATEMENT = "is_task_statement";

    /**
     * 工具落地状态
     */
    public static final String TOOL_USE = "tool_use";

    /**
     * 工具名称
     */
    public static final String TOOL_NAME = "tool_name";

    /**
     * 工具名称_下拉多选
     */
    public static final String TOOL_NAME_SELECTED = "tool_name_selected";

    /**
     * 未使用工具原因
     */
    public static final String NOT_USE_TOOL_REASON = "not_use_tool_reason";

    /**
     * 操作说明
     */
    public static final String OPERATION_DESC = "operation_desc";

    /**
     * 预计业务中断时长（分钟）
     */
    public static final String SERVICE_DISCONNECT_DURATION = "service_disconnect_duration";

    /**
     * CSC请求单号
     */
    public static final String CSC_NO = "csc_no";

    /**
     * 任务书是否超过一个月
     */
    public static final String TASK_STATEMENT_OVER_MONTH = "task_statement_over_month";

    /**
     * 关联无线产品升级单
     */
    public static final String IS_ASSOCIATE_WIRELESS_UPGRADE = "is_associate_wireless_upgrade";

    /**
     * 不关联原因
     */
    public static final String NOT_ASSOCIATE_REASON = "not_associate_reason";

    /**
     * 升级光模块
     */
    public static final String IS_UPGRADING_OPTICAL = "is_upgrading_optical";

    /**
     * 有第三方终端
     */
    public static final String IS_THIRD_PARTY_TERMINAL = "is_third_party_terminal";


    // ====================== 技术交付部-邮件抄送人 =======================
    /**
     * 邮件抄送（技术交付部/网络处审核远程方案）
     */
    public static final String EMAIL_CC_TD_NET_DEPT_APP_SOLU = "email_cc_td_net_dept_app_solu";

    /**
     * 邮件抄送（技术交付部/网络处审核）
     */
    public static final String EMAIL_CC_TD_NET_DEPT_APP = "email_cc_td_net_dept_app";


    // ====================== 研发经理-邮件抄送人 =======================
    /**
     * 邮件抄送（研发经理）
     */
    public static final String EMAIL_CC_RD_MANAGER = "email_cc_rd_manager";

    /**
     * 邮件抄送（代表处产品TD审核）
     */
    public static final String EMAIL_CC_REP_PROD_TD_APP = "email_cc_rep_prod_td_app";

    /**
     * 邮件抄送（大区TD确认）
     */
    public static final String EMAIL_CC_REGIONAL_TD_CONFIRM = "email_cc_regional_td_confirm";

    /**
     * 邮件抄送（远程中心负责人）
     */
    public static final String EMAIL_CC_REMOTE_CENTER_OWNER = "email_cc_remote_center_owner";

    /**
     * 邮件抄送（网络服务部审核）
     */
    public static final String EMAIL_CC_NET_SERVICE_DEPT = "email_cc_net_service_dept";

    /**
     * 邮件抄送（服务产品支持部）
     */
    public static final String EMAIL_CC_SERVICE_PROD_SUPPORT = "email_cc_service_prod_support";

    /**
     * 邮件抄送（SSP产品支持团队）
     */
    public static final String EMAIL_CC_SSP_PROD_SUPPORT = "email_cc_ssp_prod_support";

    /**
     * 邮件抄送（测试部）
     */
    public static final String EMAIL_CC_TEST_DEPT = "email_cc_test_dept";

    /**
     * 邮件抄送（集成团队）
     */
    public static final String EMAIL_CC_INTEGRATION_TEAM = "email_cc_integration_team";

    /**
     * 邮件抄送（研发领导）
     */
    public static final String EMAIL_CC_RD_LEADER = "email_cc_rd_leader";

    /**
     * 网络变更操作通告抄送（技术交付部/网络处审核）
     */
    public static final String NETWORK_CHANGE_OPER_NOTICE_CC_TD_NET_DEPT = "network_change_oper_notice_cc_td_net_dept";

    /**
     * 网络变更操作通告抄送（研发经理）
     */
    public static final String CHANGE_OPER_NOTICE_CC_RD_MANAGER = "change_oper_notice_cc_rd_manager";

    /**
     * 邮件抄送（远程中心方案提交）
     */
    public static final String EMAIL_CC_REMOTE_CENTER_SCHEME = "email_cc_remote_center_scheme";

    /**
     * 邮件抄送（远程中心操作实施指派）
     */
    public static final String EMAIL_CC_REMOTE_CENTER_OPER_ASSIGN = "email_cc_remote_center_oper_assign";

    /**
     * 驳回时审核结果
     */
    public static final String RESULT_REJECTION = "radiofield_result_rejection";

    /**
     * 驳回时审核意见
     */
    public static final String OPINION_REJECTION = "textareafield_audit_rejection";

    /**
     * 驳回审核意见附件
     */
    public static final String ATTACHMENT_REJECTION = "attachmentfield_attachment_rejection";

    /**
     * 驳回时审核人
     */
    public static final String PERSON_REJECTION = "employeefield_person_rejection";

    /**驳回时节点的自定义编码*/
    public static final String REJECTION_EXTEND_CODE = "rejection_extend_code";



    // ====================== 网络变更操作单扩展字段 =======================
    /**
     * 是否行政领导审批
     */
    public static final String IS_ADMINISTRATION_LEADER_APPROVAL = "is_administration_leader_approval";

    /**
     * 方案描述（远程中心方案提交）
     */
    public static final String REMOTE_CENTER_SCHEME_DESC = "remote_center_scheme_desc";


    // ============= 网络变更操作单 - 远程中心负责人审核扩展字段 ==============
    /**
     * 是否远程中心审核
     */
    public static final String IS_REMOTE_CENTER_SUPPORT = "is_remote_center_support";
    /**
     * 网络变更操作是否成熟
     */
    public static final String IS_NETWORK_CHANGE_OPER_MATURE = "is_network_change_oper_mature";


    /**
     * 高危指令表
     */
    public static final String HIGH_RISK_INSTRUCTION = "high_risk_instruction";

    /**
     * 高危指令描述
     */
    public static final String HIGH_RISK_INSTRUCTION_DESCRIPTION = "high_risk_instruction_description";

    /**
     * 是否紧急操作附件
     */
    public static final String EMERGENCY_OPERATION_ATTACH = "emergency_operation_attach";

    /**
     * 是否涉及高危指令
     */
    public static final String IS_HIGH_RISK_INSTRUCTION = "is_high_risk_instruction";


    // ============= 网络变更单技术交付部/网络处审核字段 =====================
    /**
     * 网络服务部审核人
     */
    public static final String NETWORK_SERVICE_DEPT_APPROVER = "network_service_dept_approver";

    /**
     * 网络服务部审核组
     */
    public static final String NETWORK_SERVICE_DEPT_APPROVER_TEAM = "network_service_dept_approver_team";

    /**
     * 是否需要授权文件
     */
    public static final String IS_NEED_AUTHORIZATION_FILE = "is_need_authorization_file";

    /** 预计业务中断时长 */
    public static final String BUSI_INTERRUPT_DURATION = "service_disconnect_duration";

    /** 变更单数据来源 */
    public static final String SOURCE = "source";

    /** 操作主题后缀 */
    public static final String OPERATION_SUBJECT_SUFFIX = "operation_subject_suffix";

    /**
     * 提交按钮
     */
    public static final String SUBMIT_BUTTON = "submit_button";

    /**
     * GDPR要求
     */
    public static final String GDPR_REQUIRE = "gdpr_require";

    /** 是否带业务操作 */
    public static final String IS_BUSINESS_OPERATION = "is_business_operation";

    /**
     * 操作关联产品（是否涉及）
     */
    public static final String IS_ASSOC_PROD = "integrated_associated_product";

    /**
     * 是否大区操作
     */
    public static final String IS_REGIONAL_OPERATION = "is_regional_operation";

    /**
     * 内部操作方案（AI生成）
     */
    public static final String INTERNAL_OPERATION_SOLUTION_AI = "internal_operation_solution_ai";

    /**
     * 客户操作方案（AI生成）
     */
    public static final String CUSTOMER_OPERATION_SOLUTION_AI = "customer_operation_solution_ai";

    /**
     * 操作封装
     */
    public static final String OPERATION_ENCAPSULATION = "operation_encapsulation";

    /**
     * 变更操作来源
     */
    public static final String CHANGE_OPERATION_SOURCE = "change_operation_source";

    /**
     * 是否影响ToB业务
     */
    public static final String IS_AFFECT_TO_B = "is_affect_to_b";

    /** 客户特殊业务 */
    public static final String CUSTOMER_SPECIAL_SERVICE = "customer_special_service";

    /** 是否特殊场景 */
    public static final String IS_SPECIAL_SCENARIO = "is_special_scenario";

    /**
     * 是否有商务收费合同
     */
    public static final String IS_COMMERCIAL_CHARGE_CONTRACT = "is_commercial_charge_contract";

    /**
     * 是否获得客户扫描许可
     */
    public static final String IS_CUSTOMER_SCAN_PERMISSION = "is_customer_scan_permission";

    /**
     * 现场备份无线参数
     */
    public static final String BACKUP_RADIO_PARAMETER = "backup_radio_parameter";

    /**
     * 是否涉及license文件加载
     */
    public static final String LICENSE_LOAD = "license_load";

    /** 是否商用局实体PROPERTY_KEY */
    public static final String IS_COMMERCIAL_OFFICE = "is_commercial_office";

    /**
     * 其他操作附件
     */
    public static final String OTHER_OPERATION_ACCESSORIES = "other_operation_accessories";

    /** 我已知晓 */
    public static final String AWARE_SUBMISSION = "checkboxfield_aware_submission";

    /** 我已知晓 - 隐藏标识 */
    public static final String AWARE_SUBMISSION_HIDDEN = "aware_submission_hidden";

    /**
     * 电源规格型号
     */
    public static final String POWER_SPECIFICATION_MODEL = "power_specification_model";

    /**
     * 经营团队
     */
    public static final String FIELD_PRODUCT_TEAM = "productTeam";

    /**
     * 产品线
     */
    public static final String FIELD_PRODUCT_LINE = "productLine";


    // ======================== 审批人相关名字段 ==========================
    /**
     * 审批人相关字段
     */
    public static class ApproverFieldConsts {

        /**
         * 审核人（技术交付部/网络处审核人）
         */
        public static final String UPGRADE_TECHNOLOGY_REVIEWER = "upgrade_technology_reviewer";

        /**
         * 审核组（技术交付部/网络处审核组）
         */
        public static final String UPGRADE_TECHNOLOGY_REVIEWER_TEAM = "upgrade_technology_reviewer_team";
        /**
         * 技术交付部/网络处-领导审核
         */
        public static final String TD_NET_OFFICE_LEADER_APPROVAL_TEAM = "td_net_office_leader_approval_team";

        /**
         * 网络处总工审核人
         */
        public static final String NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM = "network_dept_chief_engineer_app_team";

        /** 网服部审核填写的SSP支持组审核人 */
        public static final String SSP_SUPPORT_TEAM_NET = "ssp_support_team_net";

        /**
         * 代表处方案审核人
         */
        public static final String OFFICE_SOLUTION_REVIEWER = "office_solution_reviewer";

        /**
         * 代表处方案审核人(下拉)
         */
        public static final String OFFICE_SOLUTION_REVIEWER_MULTI = "office_solution_reviewer_multi";

        /**
         * 主管经理/副经理
         */
        public static final String MANAGER = "manager";

        /**
         * 核心网大区TD审核人
         */
        public static final String REGION_TD_REVIEWER = "region_td_reviewer";

        /**
         * 内部操作方案
         */
        public static final String INTERNAL_OPERATION_SOLUTION = "internal_operation_solution";

        /**
         * 操作checklist——仅内部变更单
         */
        public static final String OPERATION_CHECKLIST = "operation_checklist";

        /**
         * 前后方线下评审记录——仅内部变更单
         */
        public static final String OFFLINE_REVIEW_RECORDS = "offline_review_records";

        /**
         * 客户操作方案
         */
        public static final String CUSTOMER_OPERATION_SOLUTION = "customer_operation_solution";

        /** 远程中心方案编写人 */
        public static final String REMOTE_CENTER_SOLUTION_MAKER = "solution_maker";

        /**
         * 远程中心方案负责人（员工）
         */
        public static final String REMOTE_CENTER_SOLUTION_OWNER = "remote_center_solution_owner";

        /*
        * 远程中心方案负责组（员工）
        * */
        public static final String REMOTE_CENTER_SOLUTION_TEAM = "remote_center_solution_team";

        /*
         * 研发经理
         * */
        public static final String RD_MANAGER = "rd_manager";

        /*
         * 研发经理审核组
         * */
        public static final String RD_MANAGER_APPROVE_TEAM = "rd_manager_approve_team";

        /*
         * 测试部审核人
         * */
        public static final String TEST_DEPT_APPROVER = "test_dept_approver";

        /*
         * 研发领导
         * */
        public static final String RD_LEADER_RD_MANAGER = "rd_leader_rd_manager";
    }

    /**
     * 扩展字段 - 网络服务部审核
     */
    public static class NetServiceDeptAppFieldConsts {
        /** 承载专项保障 */
        public static final String IS_DEDICATED_BEAR_GUARANTEE = "is_dedicated_bearer_guarantee";
        /** 此版本是否首次商用 */
        public static final String IS_COMMERCIAL_USE_FIRST_TIME = "is_commercial_use_first_time";
        /** 首次应用 */
        public static final String IS_FIRST_APPLICATION_NET = "is_first_application_net";
    }

    /**
     * 扩展字段 - 行政审批 - 代表处产品科长
     */
    public static class AdminRepProdChiefFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_admin_rep_prod_chief";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_rep_prod_chief";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_rep_prod_chief";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_admin_rep_prod_chief";
    }

    /**
     * 扩展字段 - 行政审批 - 网络处产品总监
     */
    public static class AdminNetProdDirFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_admin_net_prod_director";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_net_prod_director";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_net_prod_director";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_admin_net_prod_director";
    }

    /**
     * 扩展字段 - 行政审批 - 办事处副经理
     */
    public static class AdminRepDeputyMngFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_admin_rep_deputy_manager";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_rep_deputy_manager";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_rep_deputy_manager";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_rep_deputy_manager";
    }

    /**
     * 扩展字段 - 行政审批 - 电信服务总监
     */
    public static class AdminDirTeleSerFieldConsts {

        /** 审核结果 - 实体唯一标识 */
        public static final String APPROVE_RESULT = "approve_result_admin_dir_tele_ser_director";

        /** 审核意见 - 实体唯一标识 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_dir_tele_ser_director";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_dir_tele_ser_director";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_admin_dir_tele_ser_director";

        /** 审核结果 - 实体字段名 手机端ApproverOpenApi和OperationLogEnum用字段名，查询时用唯一标识 */
        public static final String APPROVE_RESULT_FIELD_NAME = "approve_result_admin_dir_tele_ser_direct";

        /** 审核意见 - 实体字段名 */
        public static final String APPROVE_OPINION_FIELD_NAME = "approve_opinion_admin_dir_tele_ser_direc";

        /** 审核人 - 实体字段名 */
        public static final String APPROVED_BY_FIELD_NAME= "approved_by_admin_dir_tele_ser_director";

        /** 审核时间 - 实体字段名 */
        public static final String APPROVED_TIME_FIELD_NAME= "approved_time_admin_dir_tele_ser_directo";
    }

    /**
     * 扩展字段 - 行政审批 - 网络处主管经理
     */
    public static class AdminNetDeptMngFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_admin_net_dept_mng";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_net_dept_mng";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_net_dept_mng";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_admin_net_dept_mng";
    }

    /**
     * 扩展字段 - 行政审批 - 网服部四层
     */
    public static class AdminNetServiceLV4FieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_admin_net_servcie_lv4";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_net_servcie_lv4";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_net_servcie_lv4";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_admin_net_servcie_lv4";
    }

    /**
     * 扩展字段 - 行政审批 - 网服部三层
     */
    public static class AdminNetServiceLV3FieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_admin_net_servcie_lv3";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_net_servcie_lv3";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_net_servcie_lv3";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_admin_net_servcie_lv3";
    }

    /**
     * 扩展字段 - 行政审批 - 研发三层
     */
    public static class AdminRdDeptLV3FieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_admin_rd_dept_lv3";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_rd_dept_lv3";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_rd_dept_lv3";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_admin_rd_dept_lv3";
    }

    /**
     * 扩展字段 - 行政审批 - 工服三部部长
     */
    public static class AdminEngService3FieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_admin_eng_service3_leader";

        /** 审核意见 实体唯一标识 */
        public static final String APPROVE_OPINION = "approve_opinion_admin_eng_service3_leader";

        /** 审核意见 实体字段名 */
        public static final String APPROVE_OPINION_FIELD_NAME = "approve_opinion_admin_eng_service3_leade";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_admin_eng_service3_leader";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_admin_eng_service3_leader";
    }

    // 内部网络变更单_审批进展集合
    // CCN大区TD确认
    public static class ReginalTdConfirmConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_regional_td_confirm";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_rep_prod_td_app";
    }

    // 代表处TD审批
    public static class RepProdTdApproveConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_rep_prod_td_app";
        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_rep_prod_td_app";
    }

    // 技术交付部/网络处审批
    public static class TdNetDeptApproveConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_td_net_dept_app";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_td_net_dept_app";

        /** 需要领导审核 */
        public static final String IS_TD_NET_OFFICE_LEADER_APPROVAL = "is_td_net_office_leader_approval";

        /** 技术交付部/网络处领导 */
        public static final String TD_NET_OFFICE_LEADER_APPROVAL_TEAM = "td_net_office_leader_approval_team";

        /** 需要升级至网络服务部 */
        public static final String IS_NET_DEPT_APPROVAL = "is_net_dept_approval";

        /** 对网络服务部的要求 */
        public static final String REQ_FOR_NET_SERVICE_DEPT = "req_for_net_service_dept";

        /** 对网络服务部的要求 */
        public static final String SSP_SUPPORT_TEAM = "ssp_support_team";

        /**
         * 需要升级到服务产品支持部（IT外购件支持）
         */
        public static final String IS_SERV_PROD_SUPPORT_DEPT_APPROVAL = "is_serv_prod_support_dept_approval";

        /**
         * 需要升级到SSP支持组
         */
        public static final String IS_SSP_SUPPORT_TEAM_APPROVAL = "is_ssp_support_team_approval";

        /**
         * 需要容量性能评估
         */
        public static final String IS_CAPACITY_PERFORMANCE_EVALUATION = "is_capacity_performance_evaluation";
    }

    // 技术交付部/网络处领导审批
    public static class TdNetDeptLeaderConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_td_net_dept_leader";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_td_net_dept_leader";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_td_net_dept_leader";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_td_net_dept_leader";
    }

    // 远程中心负责人确认
    public static class RemoteCenterOwnerConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_remote_center_owner";
    }

    // 远程中心方案（无）

    // 技术交付部网络处远程方案
    public static class NetDeptRemoteSchemeConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_td_net_dept_app_solu";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_td_net_dept_app_solu";
    }

    // 网络服务部
    public static class NetServiceDeptAppConsts {
        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_net_service_dept_app";

        /** 审核意见 */
        public static final String APPROVE_OPINION_NET_SERVICE_DEPT = "approve_opinion_net_service_dept";

        /** 需要升级至研发 */
        public static final String IS_DEV_DEPT_APPROVAL = "is_dev_dept_approval";

        /** 对研发的要求 */
        public static final String RD_REQ = "rd_req";

        public static final String OPERATION_NOTICE = "change_oper_notice_cc_net_service_dept";

        /**
         * 早期局
         */
        public static final String IS_EARLY_SITE = "is_early_site";

        /**
         * 高风险版本
         */
        public static final String IS_HIGH_RISK_VERSION = "is_high_risk_version";

        /**
         * 重大版本
         */
        public static final String IS_MAJOR_VERSION = "is_major_version";

        /**
         * 方案审核投入人天
         */
        public static final String SOLUTION_REVIEW_INPUT_MANDAY = "solution_review_input_manday";

        /**
         * 中心管理干部挂钩项目
         */
        public static final String IS_PROJECT_LINKED_CENTER_MANAG_CADRE = "is_project_linked_center_manag_cadre";

    }

    // 服务产品支持部
    public static class ServiceProdSupportConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_service_prod_support";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_service_prod_support";
    }

    // 网络处总工
    public static class NetworkChiefEngineerConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_network_chief_engine";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_network_chief_engine";

        /** 邮件抄送 */
        public static final String EMAIL_CC = "email_cc_network_chief_engine";
    }

    // SSP产品支持团队
    public static class SSPProdSupportConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_ssp_prod_support";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_ssp_prod_support";
    }

    // 测试部
    public static class TestDeptConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_test_dept";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_test_dept";
    }

    // 研发经理
    public static class RDManagerConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_rd_manager";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_rd_manager";

        /** 操作验证测试 */
        public static final String OPER_TEST_VERIFY_RD_MANAGER = "oper_test_verify_rd_manager";

        /** 联合测试验证 */
        public static final String JOINT_VERIFY_TEST_RD_MANAGER = "joint_verify_test_rd_manager";

        /** 需要研发领导审核 */
        public static final String IS_DEV_LEADER_APPROVAL = "is_dev_leader_approval";

        /**
         * 需要容量性能评估
         */
        public static final String CAP_PERF_EVAL_RESULT_RD_MANAGER = "cap_perf_eval_result_rd_manager";

        /**
         * 测试验证结论
         */
        public static final String TEST_VERIFY_CONCLUSION_RD_MANAGER = "test_verify_conclusion_rd_manager";
    }

    // 集成团队
    public static class IntegrationTeamAppConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_integration_team";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_integration_team";
    }

    // 研发领导
    public static class RdLeaderAppConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_rd_leader";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_rd_leader";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_rd_leader";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_rd_leader";
    }

    // 远程中心操作方案实施指派
    public static class RemoteCenterOperConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "review_comment_remote_center_oper_assign";
    }

    // 多模从属产品
    public static class MultiModeProdConsts {
        /** 审核意见 */
        public static final String APPROVE_OPINION = "approve_opinion_multimode_product";

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_multimode_product";

        /**
         * 审核提交人
         */
        public static final String MULTIMODE_REVIEWER_SUBMITTER = "multimode_reviewer_submitter";

        /**
         * 审核时间
         */
        public static final String APPROVED_TIME_MULTIMODE_PRODUCT = "approved_time_multimode_product";

        /**
         * 多模产品
         */
        public static final String MULTIMODE_PRODUCTS = "multimode_products";

        /**
         * 审核人
         */
        public static final String APPROVED_BY = "approved_by_multimode_product";

        /**
         * 审核时间
         */
        public static final String APPROVED_TIME = "approved_time_multimode_product";

        /**
         * 网络服务部审核组
         */
        public static final String MULTIMODE_NET_APPROVE_TEAM = "multimode_net_approve_team";

        /**
         * 操作计划ID
         */
        public static final String PLAN_OPERATION_ORDER_ID = "plan_operation_order_id";
    }
}

