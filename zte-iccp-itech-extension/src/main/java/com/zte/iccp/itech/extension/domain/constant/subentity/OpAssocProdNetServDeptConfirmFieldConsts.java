package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 网络服务部审核
 *
 * <AUTHOR> 10284287
 * @since 2024/06/26
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OpAssocProdNetServDeptConfirmFieldConsts {
    public static final String PRODUCT_TYPE = "product_type_net";

    public static final String IS_RELATED_PROD_APPROVAL = "is_related_prod_approval_net";

    public static final String APPROVAL = "approval_net";

    public static final String APPROVAL_TEAM = "approval_team_net";

    /**
     * 操作关联产品中文名（网络服务部审核）
     */
    public static final String INTEGRATED_ASSOCIATED_PRODUCT_NET_ZH = "integrated_associated_product_net_zh";

    /**
     *操作关联产品英文名（网络服务部审核）
     */
    public static final String INTEGRATED_ASSOCIATED_PRODUCT_NET_EN = "integrated_associated_product_net_en";
}
