package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.DATE_FORMAT;

/**
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DateUtils {

    private static final long SEC_A_DAY = 24 * 60 * 60;

    private static final long MS_A_DAY = SEC_A_DAY * 1000;

    public static Date addHours(Date origin, int hours) {
        if (Objects.isNull(origin)) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(origin);
        calendar.add(Calendar.HOUR, hours);
        return calendar.getTime();
    }

    /**
     * 在原日期的基础上增加或者减少天数
     * @param date
     * @param changeDays
     * @return Date
     */
    public static Date addDay(Date date, Integer changeDays){
        if (Objects.isNull(date)) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, changeDays);
        return calendar.getTime();
    }


    /**
     * 在原日期的基础上增加或者减少月份
     */
    public static Date addMonths(Date date, Integer changeMonths){
        if (Objects.isNull(date)) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, changeMonths);
        return calendar.getTime();
    }

    /**
     * 在原日期的基础上增加或者减少年份
     * @param date
     * @param changeYears
     * @return Date
     */
    public static Date addYear(Date date, Integer changeYears){
        if (Objects.isNull(date)) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, changeYears);
        return calendar.getTime();
    }

    /**
     * 计算目标时间是该天的第?秒
     */
    /* Started by AICoder, pid:ofd40idba9o554514a450be670ae6d04a6695e6e */
    public static int getSecondsInDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);

        return hour * 3600 + minute * 60 + second;
    }
    /* Ended by AICoder, pid:ofd40idba9o554514a450be670ae6d04a6695e6e */

    /**
     * 计算A、B两个时间范围是否有重叠部分
     * @param start 时间范围A的起始时间点
     * @param end 时间范围A的结束时间点，可能与起始时间不在同一天
     * @param fromSeconds 时间范围B的起始时间，值是它在一天中的秒位置
     * @param toSeconds 时间范围B的结束时间，值是它在一天中的秒位置
     */
    public static boolean isOverTimeRange(Date start, Date end, int fromSeconds, int toSeconds) {
        if (start == null || end == null || start.after(end)) {
            throw new IllegalArgumentException();
        }

        if (fromSeconds > toSeconds) {
            return isOverTimeRange(start, end, fromSeconds, 24 * 60 * 60)
                    || isOverTimeRange(start, end, 0, toSeconds);
        }

        int dayDiff = dayDiff(end, start);
        if (dayDiff > 1) {
            return true;
        }

        // 获取start和end在当天的秒数
        int startSeconds = getSecondsInDay(start);
        int endSeconds = getSecondsInDay(end);

        if (dayDiff == 1) {
            return isOverlap(startSeconds, 24 * 60 * 60, fromSeconds, toSeconds)
                    || isOverlap(0, endSeconds, fromSeconds, toSeconds);
        }

        return isOverlap(startSeconds, endSeconds, fromSeconds, toSeconds);
    }

    public static int dayDiff(Date a, Date b) {
        final long milliSecondsOneDay = 24L * 60 * 60 * 1000;
        long dayA = a.getTime() - (a.getTime() % milliSecondsOneDay);
        long dayB = b.getTime() - (b.getTime() % milliSecondsOneDay);
        return (int) ((dayA - dayB) / milliSecondsOneDay);
    }

    public static boolean dateOnly(Date date) {
        return date.getTime() % MS_A_DAY == 0;
    }

    public static boolean timeOnly(Date date) {
        return date.getTime() <= MS_A_DAY;
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalTime toLocalTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return LocalTime.of(
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE),
                calendar.get(Calendar.SECOND),
                calendar.get(Calendar.MILLISECOND) * 100000);
    }

    /* Started by AICoder, pid:mdfc6y5734n51b314db908449011af2995789ed6 */
    private static boolean isOverlap(int startA, int endA, int startB, int endB) {
        if (startA == endA || startB == endB) {
            return false;
        }

        // 检查A是否完全包含B
        if (startA <= startB && endA >= endB) {
            return true;
        }

        // 检查B是否完全包含A
        if (startB <= startA && endB >= endA) {
            return true;
        }

        // 检查A的起始点是否在B的区间内
        if (startA >= startB && startA <= endB) {
            return true;
        }

        // 检查A的结束点是否在B的区间内
        return endA >= startB && endA <= endB;
    }
    /* Ended by AICoder, pid:mdfc6y5734n51b314db908449011af2995789ed6 */

    /**
     * string 转 Date
     */
    public static Date stringToDate(String dateString, String datePattern) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        }

        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(datePattern);
            return simpleDateFormat.parse(dateString);
        } catch (ParseException e) {
            throw new LcapBusiException("Date String Error");
        }
    }

    /**
     * Data 转 String
     * @param date
     * @param datePattern
     * @return String
     */
    public static String dateToString(Date date, String datePattern){
        if (null == date){
            return CommonConstants.EMPTY_STRING;
        }

        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(datePattern);
            return simpleDateFormat.format(date);
        } catch (Exception e){
            return CommonConstants.EMPTY_STRING;
        }
    }

    /**
     * 比较日期格式的大小 及yyyy-MM-dd，
     * @param date1 时间1
     * @param date2 时间2
     * @return String
     */
    public static boolean compareDate(Date date1, Date date2) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);

        Date d1 = dateFormat.parse(dateFormat.format(date1));

        Date d2 = dateFormat.parse(dateFormat.format(date2));

        return d1.getTime() > d2.getTime();

    }

    /**
     * 校验日期间隔 - 月份
     */
    public static boolean checkMonthInterval(Date startDate, Date endDate, int months) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return false;
        }

        Date actualEndDate = addMonths(startDate, months);
        return Objects.nonNull(actualEndDate) && actualEndDate.after(endDate);
    }

    /**
     * 计算时间差 - 天
     */
    public static Double getDaysBetween(Date startDate, Date endDate) {
        // 计算时间差（秒）
        Long diffInSeconds = getBetween(startDate, endDate);
        if (Objects.isNull(diffInSeconds)) {
            return null;
        }

        // 转换为天数并保留一位小数
        double diffInDays = (double) diffInSeconds / (60 * 60 * 24);
        return Math.round(diffInDays * 10.0) / 10.0;
    }

    /**
     * 计算时间差 - 秒
     */
    public static Long getBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return null;
        }

        return Duration
                .between(startDate.toInstant(), endDate.toInstant())
                .getSeconds();
    }

    /**
     * 判断时间是否在 06:00:00 之前（包含 06:00:00）
     *
     * @param date 需要检查的时间点
     * @return true: 在 06:00:00 之后 false: 否
     */
    public static boolean checkTime(Date date) {
        // 转换为系统默认时区的 LocalTime（JDK 8 兼容方式）
        LocalTime time = DateUtils.toLocalTime(date);

        if (time.getHour() < 6) {
            return true;
        }

        return time.getHour() == 6 && time.getMinute() == 0 && time.getSecond() == 0;
    }

    /**
     * 判断日期区间是否为夜间操作（0-6)
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return true:是 false:否
     */
    public static boolean isNightTimeOverlap(Date startDate, Date endDate) {
        LocalDateTime start = toLocalDateTime(startDate);
        LocalDateTime end = toLocalDateTime(endDate);

        // 1、结束时间在次日6点前（含）
        if (checkTime(endDate)
                // 2、同一天且开始时间在夜间区间
                || (start.toLocalDate().equals(end.toLocalDate()) && checkTime(startDate))) {
            return true;
        }

        // 3、非同一天直接返回true
        return !start.toLocalDate().equals(end.toLocalDate());
    }

    public static List<Date> dateListConverter(List<Object> objects) {
        return objects.stream()
                .filter(obj -> obj instanceof Date)
                .map(Date.class::cast)
                .collect(Collectors.toList());
    }
}
