package com.zte.iccp.itech.extension.ability.reportpush;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.CommonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.common.utils.StringBuilderUtils;
import com.zte.iccp.itech.extension.openapi.model.reportpush.GlobalSupportDetailVO;
import com.zte.iccp.itech.extension.spi.client.IcosClient;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.domain.constant.TemplateConstants.*;

/**
 * <AUTHOR>
 * @date 2025/6/12 下午2:32
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GlobalSupportPushAbility {

    public static void globalSupportPush() {
        List<GlobalSupportDetailVO> vos = IcosClient.getGlobalSupportDetailVos();
        Map<String, List<GlobalSupportDetailVO>> support2DetailsMap = vos.stream()
                .collect(Collectors.groupingBy(GlobalSupportDetailVO::getSupportStaffNo));

        String trTemplate = CommonUtils.getTemplateString(TR_DETAIL_LOCATION);
        String template = CommonUtils.getTemplateString(GLOBAL_SUPPORT_PUSH_DETAIL_LOCATION);
        String url = ConfigHelper.get(NETCHANGE_QUERY_URL);

        support2DetailsMap.forEach((k, v) -> {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < v.size(); i++) {
                GlobalSupportDetailVO vo = v.get(i);
                Map<String, String> replacements = MapUtils.newHashMap(NUMBER, String.valueOf(i + 1),
                        BATCH_CODE, vo.getBatchCode(), OPERATION_SUBJECT, vo.getOperationSubject(),
                        OP_TYPE_ZH, vo.getOperationType(), PROD_MAIN_CATEGORY, vo.getProductCategory(),
                        PROD_SUB_CATEGORY, vo.getProductSubCategory(), PLAN_OPERATION_START_TIME, vo.getPlanOperationStartTime(),
                        ORGANIZATION3, vo.getMarketing(), ORGANIZATION4, vo.getRepresentativeOffice(),
                        CUREENT_STATUS, vo.getCurrentStatus(), SITE_PERSON, vo.getSitePerson());
                String trTemplateStr = trTemplate;
                stringBuilder.append(StringBuilderUtils.replaceAll(trTemplateStr, replacements));
            }
            String templateStr = template;
            ReportPushAbility.sendMail(MsgUtils.getLangMessage(ZH_CN, GLOBAL_SUPPORT_PUSH_TITLE, v.size()),
                    StringBuilderUtils.replaceAll(templateStr, MapUtils.newHashMap(DETAIL, stringBuilder.toString())),
                    url, Lists.newArrayList(k));
        });
    }
}
