package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.subentity.MultiProductLinkageGuaranteeFieldConsts.*;

/**
 * <AUTHOR>
 * @date 2024/7/29 下午4:01
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = false)
@BaseSubEntity.Info(value = "multi_product_linkage_guarantee", parent = ChangeOrder.class)
public class MultiProductLinkageGuarantee extends BaseSubEntity {

    @JsonProperty(value = PRODUCT_CLASS)
    private String productClass;

    @JsonProperty(value = IMPLEMENTATION_RESPONSIBLE_PERSON)
    private List<Employee> responsiblePerson;

    @JsonProperty(value = ASSIGNMENT_ID)
    private String assignmentId;

    @JsonProperty(value = IS_MAIN_TASK)
    private Boolean isMainTask;

}
