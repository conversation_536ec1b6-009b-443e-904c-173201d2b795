package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.spi.client.HolClient;
import com.zte.iccp.itech.extension.spi.model.hol.*;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.spi.model.hol.InfoBlock.BlockEnum.*;
import static com.zte.iccp.itech.extension.spi.model.hol.InfoBlock.VerEnum.v1;
import static com.zte.paas.lcap.platform.constant.NumberConst.FOUR;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/18
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class HolAbility {

    private static final String REPORT_LEADER_LEVEL_A4 = "a4";

    public static List<Employee> getOrgLeader(String orgId, String roleId) {
        PageRows<OrgLeaderInfo> orgLeaderInfos = HolClient.getOrgLeader(new GetOrgLeaderReq() {{
            setHrOrgID(orgId);
            setHrLevel(FOUR);
        }});

        List<String> ids = orgLeaderInfos.getRows().stream()
                .map(OrgLeaderInfo::getManagerUiid)
                .collect(Collectors.toList());

        Map<String, PersonGeneralInfo> infoZhCn = HolClient.getPersonGeneralInfo(
                ZH_CN,
                new GetPersonGeneralInfoReq() {{
                    setIds(ids);
                    setInfoBlocks(Lists.newArrayList(
                            new InfoBlock(v1, B0001),
                            new InfoBlock(v1, B0003),
                            new InfoBlock(v1, B0004)));
                }});
        Map<String, PersonGeneralInfo> infoEnUs = HolClient.getPersonGeneralInfo(
                EN_US,
                new GetPersonGeneralInfoReq() {{
                    setIds(ids);
                    setInfoBlocks(Lists.newArrayList(
                            new InfoBlock(v1, B0003),
                            new InfoBlock(v1, B0004)));
                }});

        Map<String, Employee> result = merge(
                person2Emp(infoZhCn, roleId),
                person2Emp(infoEnUs, roleId));
        return new ArrayList<>(result.values());
    }

    private static Map<String, Employee> person2Emp(Map<String, PersonGeneralInfo> person, String roleId) {
        return person.entrySet().stream()
                .filter(e -> e.getValue().getPostNO().equals(roleId))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> new Employee() {{
                            setId(e.getKey());
                            setUserName(e.getKey());
                            setEmpUIID(e.getKey());
                            setEmpName(e.getValue().getEmpName());
                            setEmpNameCn(e.getValue().getEmpName());
                            setEmpNameEn(e.getValue().getEmpNameEN());
                            setOrgID(e.getValue().getOrgID());
                            setOrgNamePath(e.getValue().getOrgFullName());
                            setOrgNamePathEn(e.getValue().getOrgFullName());
                        }}));
    }

    private static Map<String, Employee> merge(Map<String, Employee> zhCn, Map<String, Employee> enUs) {
        for (Map.Entry<String, Employee> entry : zhCn.entrySet()) {
            Employee empZhCn = entry.getValue();
            Employee empEnUs = enUs.get(entry.getKey());
            empZhCn.setOrgNamePathEn(empEnUs.getOrgNamePathEn());
        }
        return zhCn;
    }

    public static List<Map<String, Object>> getReportLeaderOptions() {
        GetReportLeaderReq req = new GetReportLeaderReq();
        req.setEmpUIID(ContextHelper.getEmpNo());
        req.setManagementLevel(REPORT_LEADER_LEVEL_A4);
        PageRows<ReportLeaderInfo> pageRows = HolClient.getReportLeader(req);
        if (null == pageRows || CollectionUtils.isEmpty(pageRows.getRows())) {
            return Lists.newArrayList();
        }
        OptionsBuilder optionsBuilder = new OptionsBuilder();
        for (ReportLeaderInfo info : pageRows.getRows()) {
            String value = null == info.getManagerName() ? info.getManageUIID() : info.getManagerName() + info.getManageUIID();
            optionsBuilder.addOption(
                    new Option(value, new Text(value, value)));
        }
        return optionsBuilder.build();

    }

    public static List<Employee> getEmployees(List<String> userIds) {
        Map<String, PersonGeneralInfo> infoZhMap = HolClient.getPersonGeneralInfo(
                ZH_CN,
                new GetPersonGeneralInfoReq() {{
                    setIds(userIds);
                    setInfoBlocks(Lists.newArrayList(
                            new InfoBlock(v1, B0001),
                            new InfoBlock(v1, B0003)));
                }});
        Map<String, PersonGeneralInfo> infoEnMap = HolClient.getPersonGeneralInfo(
                EN_US,
                new GetPersonGeneralInfoReq() {{
                    setIds(userIds);
                    setInfoBlocks(Lists.newArrayList(
                            new InfoBlock(v1, B0003)));
                }});
        List<Employee> result = new ArrayList<>();
        for (Map.Entry<String, PersonGeneralInfo> entry : infoZhMap.entrySet()) {
            Employee employee = new Employee();
            employee.setId(entry.getKey());
            employee.setOrgID(entry.getValue().getOrgID());
            employee.setEmpName(entry.getValue().getEmpName());
            employee.setEmpUIID(entry.getKey());
            employee.setUserName(entry.getKey());
            employee.setEmpNameCn(entry.getValue().getEmpName());
            employee.setEmpNameEn(entry.getValue().getEmpNameEN());
            employee.setOrgNamePath(entry.getValue().getOrgFullName());
            if (infoEnMap.containsKey(entry.getKey())) {
                employee.setOrgNamePathEn(infoEnMap.get(entry.getKey()).getOrgFullName());
            } else {
                employee.setOrgNamePathEn(entry.getValue().getOrgFullName());
            }
            result.add(employee);
        }
        return result;

    }


}