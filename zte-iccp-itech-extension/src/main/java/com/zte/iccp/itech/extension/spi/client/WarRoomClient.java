package com.zte.iccp.itech.extension.spi.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.ClientConstants;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomInfo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomNodeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.zte.iccp.itech.extension.spi.client.BaseInOneClient.get;

/**
 * 作战室client
 *
 * <AUTHOR> 10335201
 * @date 2024-08-05 下午4:14
 **/
@Slf4j
public class WarRoomClient {

    /** 查询 WarRoom 地铁图详情 */
    private static final String QUERY_WAR_ROOM_SUBWAY_MAP
            = "/ZXICCP/iSupport300/zte-iccp-isupport-gateway/zte-iccp-isupport-warroom/processrecord/getSubwayList";

    /** 查询 WarRoom 详情 */
    private static final String PATH_GET_WAR_ROOM_DETAIL = "/ZXICCP/iSupport300/zte-iccp-isupport-warroom/warroom/get";

    /**
     * 查询 WarRoom 地铁图节点信息
     */
    public static WarRoomNodeVo queryWarRoomNode(String warroomId){
        if(StringUtils.isBlank(warroomId)){
            return null;
        }

        return get(
                QUERY_WAR_ROOM_SUBWAY_MAP,
                MapUtils.newHashMap(ClientConstants.WAR_ROOM_ID, warroomId),
                new TypeReference<WarRoomNodeVo>() {});
    }

    /**
     * 查询 WarRoom 详情
     * @param cscCode
     * @return Object
     */
    public static WarRoomInfo queryWarRoomDetailByCscCode(String cscCode){
        if (StringUtils.isBlank(cscCode)){
            return null;
        }

        return get(
                PATH_GET_WAR_ROOM_DETAIL,
                MapUtils.newHashMap(ClientConstants.TICKET_NUMBER, cscCode),
                new TypeReference<WarRoomInfo>() {});
    }
}
