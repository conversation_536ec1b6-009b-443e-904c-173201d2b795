package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SubcontractorChangeOrderFieldConsts {

    // ==================== 分包商网络变更操作单基础字段 ====================
    /** 单据编号 */
    public static final String ORDER_NO = "hzs_no";

    /** 操作主题 */
    public static final String OPERATION_SUBJECT = "operation_subject";

    /** 产品分类 */
    public static final String PRODUCT_ID = "product_id";

    /** 代表处 */
    public static final String ORGANIZATION_ID = "organization_id";

    public static final String BILL_STATUS = "billstatus";

    public static final String SOURCE = "source";

    /** 客户 */
    public static final String CUSTOMER_ID = "customer_id";

    /** 客户标识 */
    public static final String CUSTOMER_TYPE_FLAG = "accn_type";

    /** 国家 */
    public static final String COUNTRY = "country";

    /** 省 */
    public static final String PROVINCE = "province";

    /** 市 */
    public static final String CITY = "area";

    /** 时区 */
    public static final String TIME_ZONE = "time_zone";

    /** 计划操作开始时间 */
    public static final String OPERATION_START_TIME = "operation_start_time";

    /** 计划操作结束时间*/
    public static final String OPERATION_END_TIME = "operation_end_time";

    /** 操作类型 */
    public static final String OPERATION_TYPE = "operation_type";

    /** 操作原因 */
    public static final String OPERATION_REASON = "operation_reason";

    /** 重要程度 */
    public static final String IMPORTANCE = "importance";

    /** 风险评估 */
    public static final String RISK_EVALUATION = "risk_evaluation";

    /** 操作等级 */
    public static final String OPERATION_LEVEL = "operation_level";

    /** 操作类型分组 */
    public static final String OPERATION_TYPE_GROUP = "operation_type_group";

    /** 触发类型 */
    public static final String TRIGGER_TYPE = "trigger_type";

    /**
     * 操作预计投入人天
     */
    public static final String ESTIMATED_INVESTMENT_TIME = "estimated_investment_time";

    /**
     * 是否紧急操作
     */
    public static final String IS_EMERGENCY_OPERATION = "is_emergency_operation";

    /**
     * 紧急操作附件
     */
    public static final String EMERGENCY_OPERATION_ATTACH = "emergency_operation_attach";

    /**
     * 变更单 紧急操作附件
     */
    public static final String EMERGENCY_OPERATION_ATTACHMENT = "emergency_operation_attachment";

    /**
     * 紧急操作原因
     */
    public static final String EMERGENCY_OPERATION_REASON = "emergency_operation_reason";

    /**
     * 是否需要操作步骤打卡
     */
    public static final String IS_CHECK_IN = "hzs_is_check_in";

    /**
     * 网元清单
     */
    public static final String NE_LIST_FILE = "ne_list_file";

    /**
     * 模型包
     */
    public static final String MODEL_PACKAGE = "model_package";

    /**
     * 内部操作方案
     */
    public static final String INTERNAL_OPERATION_SOLUTION = "internal_operation_solution";

    /**
     * 客户操作方案
     */
    public static final String CUSTOMER_OPERATION_SOLUTION = "customer_operation_solution";

    /**
     * 内部操作方案（AI生成）
     */
    public static final String INTERNAL_OPERATION_SOLUTION_AI = "internal_operation_solution_ai";

    /**
     * 客户操作方案（AI生成）
     */
    public static final String CUSTOMER_OPERATION_SOLUTION_AI = "customer_operation_solution_ai";

    /** 其他附件 */
    public static final String OTHER_ATTACHMENT = "other_attachment";

    /** 是否涉及高危指令 */
    public static final String IS_HIGH_RISK_INSTRUCTION = "hzs_is_high_risk_instruction";

    /** 网络负责人 */
    public static final String NETWORK_RESPONSIBLE_PERSON = "network_responsible_person";

    /** 网络责任组 */
    public static final String NETWORK_RESPONSIBLE_TEAM = "network_responsible_team";

    /** 办事处PD审核人 */
    public static final String REPRESENTATIVE_OFFICE_PD = "representative_office_pd";

    /** 是否政企 */
    public static final String IS_GOV_ENT = "is_government_enterprise";

    /** 是否需要授权文件 */
    public static final String IS_AUTHORIZATION_FILE = "is_authorization_file";

    /** 预计业务中断时长 */
    public static final String BUSI_INTERRUPT_DURATION = "service_disconnect_duration";

    /** 是否带业务操作 */
    public static final String IS_BUSINESS_OPERATION = "is_business_operation";

    /** 我已知晓 */
    public static final String AWARE_SUBMISSION = "checkboxfield_aware_submission";

    /**
     * 驳回时审核结果
     */
    public static final String RESULT_REJECTION = "radiofield_result_rejection";

    /**
     * 驳回时审核意见
     */
    public static final String OPINION_REJECTION = "textareafield_audit_rejection";

    /**
     * 驳回审核意见附件
     */
    public static final String ATTACHMENT_REJECTION = "attachmentfield_attachment_rejection";

    /**
     * 驳回时审核人
     */
    public static final String PERSON_REJECTION = "employeefield_person_rejection";

    /**驳回时节点的自定义编码*/
    public static final String REJECTION_EXTEND_CODE = "rejection_extend_code";

    /**
     * 扩展字段 - 代表处产品科科长
     */
    public static class RepProdChiefFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_rep_prod_chief";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "review_comment_rep_prod_chief";

        /** 邮件抄送 */
        public static final String EMAIL_CC = "email_cc_rep_prod_chief";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_rep_prod_chief";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_rep_prod_chief";
    }

    /**
     * 扩展字段 - 网络处
     */
    public static class NetDeptFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_net_dept";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "review_comment_net_dept";

        /** 邮件抄送 */
        public static final String EMAIL_CC = "email_cc_net_dept";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_net_dept";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_net_dept";
    }

    /**
     * 扩展字段 - 办事处PD
     */
    public static class OfficePdFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_office_pd";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "review_comment_office_pd";

        /** 邮件抄送 */
        public static final String EMAIL_CC = "email_cc_office_pd";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_office_pd";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_office_pd";
    }

    /**
     * 扩展字段 - 办事产品科长
     */
    public static class OfficeProdChiefFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_office_prod_chief";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "review_comment_office_prod_chief";

        /** 邮件抄送 */
        public static final String EMAIL_CC = "email_cc_office_prod_chief";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_office_prod_chief";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_office_prod_chief";
    }

    /**
     * 扩展字段 - 网络责任人
     */
    public static class NetOwnerFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_net_owner";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "review_comment_net_owner";

        /** 邮件抄送 */
        public static final String EMAIL_CC = "email_cc_net_owner";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_net_owner";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_net_owner";

        /** 是否需要办事处产品经理审核 */
        public static final String IS_REVIEW_OFFICE_PROD_MANAGER = "is_review_office_prod_manager";

        /** 办事处产品经理 - 系统配置*/
        public static final String OFFICE_PROD_MANAGER = "office_prod_manager";

        /** 办事处产品经理组 - 系统配置*/
        public static final String OFFICE_PROD_MANAGER_REVIEW_TEAM = "office_prod_manager_review_team";

        /** 办事处产品经理 - 用户选择 */
        public static final String OFFICE_PROD_MANAGER_SELECTED = "office_prod_manager_selected";

        /** 办事处产品经理审核组 - 用户选择 */
        public static final String OFFICE_PROD_MANAGER_REVIEW_TEAM_MULTI = "office_prod_manager_review_team_multi";
    }

    /**
     * 扩展字段 - 办事处产品经理
     */
    public static class OfficeProdManagerFieldConsts {

        /** 审核结果 */
        public static final String APPROVE_RESULT = "approve_result_office_prod_manager";

        /** 审核意见 */
        public static final String APPROVE_OPINION = "review_comment_office_prod_manager";

        /** 邮件抄送 */
        public static final String EMAIL_CC = "email_cc_office_prod_manager";

        /** 审核人 */
        public static final String APPROVED_BY = "approved_by_office_prod_manager";

        /** 审核时间 */
        public static final String APPROVED_TIME = "approved_time_office_prod_manager";

        /** 是否需要升级至网络处 */
        public static final String IS_UPGRADE_NET_DEPARTMENT = "is_upgrade_net_dept";

        /** 网络处审核人 - 系统配置*/
        public static final String NET_DEPT_APPROVAL = "net_dept_approval";

        /** 网络处审核组 - 系统配置*/
        public static final String NET_DEPT_APPROVE_TEAM = "net_dept_approve_team";

        /** 网络处审核人 - 用户选择 */
        public static final String NET_DEPT_APPROVAL_SELECTED = "net_dept_approval_selected";

        /** 网络处审核组 - 用户选择 */
        public static final String NET_DEPT_APPROVE_TEAM_MULTI = "net_dept_approve_team_multi";
    }

    /**
     * 紧急操作标记
     */
    public static final String EMERGENCY_OPERATION_FLAG = "emergency_operation_flag";

    /**
     * 封网管控期操作标记
     */
    public static final String NETWORK_SEALING_OPERATION_FLAG = "network_sealing_operation_flag";

    /**
     * 操作说明
     */
    public static final String OPERATION_DESC = "operation_desc";

    /**
     * 客户联系人
     */
    public static final String CUSTOMER_CONTRACT_PERSON = "customer_contact_person";

    /**
     * 操作方案描述
     */
    public static final String OPERATION_SOLUTION_DESC = "operation_solution_desc";

    /**
     * 客户网络名称
     */
    public static final String NETWORK_NAME = "network_name";

    /**
     * 合作方独立完成
     */
    public static final String IS_PARTNER_INDEPENDENTLY = "is_partner_independently";

    /**
     * 附件说明
     */
    public static final String OTHER_ATTACHMENT_DESC = "other_attachment_desc";

}
