package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

/**
 * 批次任务，审核通过不通过，刷新批次审批状态
 *
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class BatchApprovalStatustPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String batchId = body.getBusinessId();
        String status = (String) body.getVariables().get("status");
        BatchTaskAbility.updateApprovalStatus(batchId, BoolEnum.valueOf(status));
        return false;
    }
}
