package com.zte.iccp.itech.extension.common.utils;

import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Iterator;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024/12/27
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class LocalCacheUtils {

    /** 本地线程级的缓存过期时间 */
    public static final int LOCAL_THREAD_EXPIRE_MINUTES = 1;

    private static final int DEFAULT_EXPIRE_MINUTES = 5;

    private static final int MAX_EXPIRE_MINUTES = Integer.MAX_VALUE;

    private static final Map<String, CacheInfo<?>> CACHE = Maps.newHashMap();

    public static <T> T get(String key) {
        return get(key, null, DEFAULT_EXPIRE_MINUTES);
    }

    public static <T> T get(String key, Supplier<T> supplier) {
        return get(key, supplier, DEFAULT_EXPIRE_MINUTES);
    }

    public static <T> T get(String key, Supplier<T> supplier, int expireMinutes) {
        return getCore(key, supplier, expireMinutes, false);
    }

    public static <T> T getAcceptExpired(String key, Supplier<T> supplier, double expireMinutes) {
        return getCore(key, supplier, expireMinutes, true);
    }

    public static void set(String key, Object value) {
        set(key, value, null, MAX_EXPIRE_MINUTES);
    }

    public static void set(String key, Object value, double expireMinutes) {
        set(key, value, null, expireMinutes);
    }

    public static <T> void set(String key, T value, Supplier<T> supplier, double expireMinutes) {
        long expireTimestamp = (long) (System.currentTimeMillis() + 60L * 1000 * expireMinutes);
        CACHE.put(key, new CacheInfo<>(value, supplier, expireTimestamp, expireMinutes));
    }

    public static void refresh() {
        long now = System.currentTimeMillis();
        Iterator<Map.Entry<String, CacheInfo<?>>> iterator = CACHE.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, CacheInfo<?>> entry = iterator.next();
            CacheInfo<?> cacheInfo = entry.getValue();
            if (cacheInfo.expireTimestamp >= now) {
                continue;
            }

            if (cacheInfo.supplier == null) {
                iterator.remove();
            } else {
                cacheInfo.reload();
            }
        }
    }

    @SuppressWarnings("unchecked")
    private static <T> T getCore(
            String key,
            Supplier<T> supplier,
            double expireMinutes,
            boolean acceptExpired) {
        long now = System.currentTimeMillis();
        CacheInfo<T> cacheInfo = (CacheInfo<T>) CACHE.get(key);
        if (cacheInfo != null && (acceptExpired || cacheInfo.expireTimestamp > now)) {
            if (cacheInfo.expireTimestamp <= now) {
                AsyncExecuteUtils.execute(() -> {
                    cacheInfo.supplier = supplier;
                    cacheInfo.reload();
                    T t = (T) cacheInfo.value;
                    set(key, t, supplier, expireMinutes);
                });
            }

            return (T) cacheInfo.value;
        }

        if (supplier == null) {
            return null;
        }

        T t = supplier.get();
        set(key, t, supplier, expireMinutes);
        return t;
    }

    private static class CacheInfo<T> {

        private final double expireMinutes;

        private Object value;

        private Supplier<T> supplier;

        private volatile long expireTimestamp;

        // 将 reloading 声明为 volatile，确保其在多线程环境下的可见性和有序性，从而避免数据竞争问题
        private volatile boolean reloading;

        public CacheInfo(
                Object value,
                Supplier<T> supplier,
                long expireTimestamp,
                double expireMinutes) {
            this.value = value;
            this.supplier = supplier;
            this.expireTimestamp = expireTimestamp;
            this.expireMinutes = expireMinutes;
        }

        public void reload() {
            long now = System.currentTimeMillis();
            if (reloading || expireTimestamp > now) {
                return;
            }

            synchronized (this) {
                if (reloading || expireTimestamp > now) {
                    return;
                }

                reloading = true;
                expireTimestamp = (long) (now + 60L * 1000 * expireMinutes);
            }

            try {
                value = supplier.get();
            } finally {
                reloading = false;
            }
        }
    }
}
