package com.zte.iccp.itech.extension.common.helper;

import com.zte.iccp.itech.extension.spi.client.Uac30Client;
import com.zte.iccp.itech.extension.spi.model.uac30.*;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ZERO;

/**
 * <AUTHOR>
 * @since 2024/10/08
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class Uac30Helper {
    public static List<UserResp> zteUserQuery(String keyword) {
        UacResponse<List<UserResp>> resp = Uac30Client.zteUserQuery(new ZteUserQueryReq() {{
            setKey(keyword);
        }});
        return resp.getData();
    }

    public static List<UserResp> subconUserQuery(String keyword) {
        UacResponse<List<UserResp>> resp = Uac30Client.subconUserQuery(new SubconUserQueryReq() {{
            setKey(keyword);
        }});
        return resp.getData();
    }

    public static List<FuzzyQueryUserResp> fuzzyUserQuery(String keyword) {
        UacResponse<List<List<FuzzyQueryUserResp>>> resp = Uac30Client.userQuery(new UserQueryReq() {{
            setKeyList(Arrays.asList(keyword));
        }});
        return resp.getData().get(INTEGER_ZERO);
    }
}
