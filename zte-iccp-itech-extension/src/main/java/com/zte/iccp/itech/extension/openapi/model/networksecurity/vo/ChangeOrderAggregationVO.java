package com.zte.iccp.itech.extension.openapi.model.networksecurity.vo;

import com.zte.iccp.itech.extension.openapi.model.networksecurity.dto.BatchInfoDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> jiangjiawen
 * @date 2024/12/2
 */
@Getter
@Setter
public class ChangeOrderAggregationVO {
    /** 变更单id	 */
    private String changeOrderId;

    /** 变更单code */
    private String coNo;

    /** 操作主题 */
    private String operationSubject;

    /** 任务类型 */
    private String assignmentType;

    /** 产品经营团队 */
    private String prodTeam;

    /** 产品线 */
    private String prodLine;

    /** 产品大类 */
    private String prodMainCategory;

    /** 产品小类 */
    private String prodSubCategory;

    /** 产品分类id */
    private String productId;

    /** 产品分类名称 */
    private String productName;

    /** 营销 */
    private String sales;

    /** 片区 */
    private String organizationRegion;

    /** 代表处 */
    private String responsibleDeptId;

    /** 代表处id */
    private String organizationId;

    /** 代表处名称 */
    private String organizationName;

    /** 操作类型 */
    private String operationType;

    /** 操作原因 */
    private String operationReason;

    /** 保障方式 */
    private String guaranteeMode;

    /** 操作等级 */
    private String operationLevel;

    /** 风险评估 */
    private String riskEvaluation;

    /** 重要程度 */
    private String importance;

    /** 是否紧急操作 */
    private Boolean isEmergencyOperation;

    /** 是否封网、管控期操作 */
    private Boolean isNetCloseOrControlOperation;

    /** 触发类型 */
    private String triggerType;

    /** 变更操作来源 */
    private String changeOperationSource;

    /** 交付方式 */
    private String deliveryMode;

    /** 时区*/
    private String timeZone;

    /** 根据TimeZoneEnum获取名称  */
    private String timeZoneName;

    /** 国家/地区 */
    private String country;

    /** 省/州 */
    private String province;

    /** 地市 */
    private String area;

    /** 客户id */
    private String customerId;

    /** 客户标识 */
    private String accnType;

    /** 客户名称 */
    private String customerName;

    /** 整体计划操作开始时间 */
    private String operationStartTime;

    /** 整体计划操作结束时间 */
    private String operationEndTime;

    /** 是否升级到网络服务部 */
    private String isNetDeptApproval;

    /** 对网络服务部的要求	 */
    private String reqForNetServiceDept;

    /** 批次信息 */
    private List<BatchInfoDTO> batchInfoDTOS;

    /** 当前处理人 */
    private String currentProcessor;

    /** 主单据状态 */
    private String assignmentStatus;

    /** 提交人 */
    private String commitBy;

    /** 提交人归属组织 */
    private String orgCnName;

    /** 提交人归属组织 */
    private String orgEnName;

    /** 提交时间 */
    private String commitTime;

    /** 创建时间 */
    private String createTime;

    /** 更新时间 */
    private String lastModifiedTime;

    /** 操作封装 */
    private String operationEncapsulation;

    /** 是否首次应用 */
    private String isFirstApplication;

    /** 特殊场景 */
    private String isSpecialScenario;

    /** 是否需提供详细保障方案 */
    private String isGuaranteeSolution;

    /** 商用局 */
    private String isCommercialOffice;

    /** 有商务收费合同 */
    private String isCommercialChargeContract;

    /** 预计业务中断时长(分钟) */
    private String serviceDisconnectDuration;

    /** 是否带业务操作 */
    private String isBusinessOperation;

    /** 是否是技术通知单实施 */
    private String isTechnicalNotice;

    /** 客户特殊业务 */
    private String customerSpecialService;

    /** 是否大区操作 */
    private String isRegionalOperation;

    /** 涉及license文件加载 */
    private String licenseLoad;

    /** 工具名称（多行文本） */
    private String toolName;

    /** 未使用工具原因 */
    private String notUseToolReason;

    /** 工具落地状态 */
    private String toolUse;

    /** 当前进展 */
    private String currentProgress;

    /** 网络服务部审核结果 */
    private String approveResultNetServiceDeptApp;

    /** 网络服务部审核人 */
    private String approvedByNetServiceDept;

    /** 更新人 */
    private String lastModifiedBy;
}
