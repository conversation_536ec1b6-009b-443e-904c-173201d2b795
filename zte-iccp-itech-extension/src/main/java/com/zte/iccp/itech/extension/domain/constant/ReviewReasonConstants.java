package com.zte.iccp.itech.extension.domain.constant;

/**
 * 复盘提交 - 原因归类 - 常量类
 * @author: 李江斌 10318434
 * @date: 2024/12/17
 */
public class ReviewReasonConstants {

    /** 产品原因 */
    public static final String PRODUCT = "reviewReason.product";

    /** 服务原因 */
    public static final String SERVICE = "reviewReason.service";

    /** 外部原因 */
    public static final String EXTERNAL  = "reviewReason.external";

    /** 客户原因 */
    public static final String CUSTOMER = "reviewReason.customer";

    /**
     * 具体原因 - 产品
     */
    public static class ProductReason {

        /** 原因待定 */
        public static final String UNDETERMINED_REASON = "reviewReason.product.undeterminedReason";

        /** 硬件 */
        public static final String HARDWARE = "reviewReason.product.hardware";

        /** 产品安全 */
        public static final String PRODUCT_SECURITY = "reviewReason.product.productSecurity";

        /** 文档 */
        public static final String DOCUMENT = "reviewReason.product.document";

        /** 软件 */
        public static final String SOFTWARE = "reviewReason.product.software";

        /** 外购件 */
        public static final String PURCHASED_COMPONENTS = "reviewReason.product.purchasedComponents";
    }

    /**
     * 具体原因 - 服务
     */
    public static class ServiceReason {

        /** 售前技术方案 */
        public static final String PRE_SALES_TECHNICAL_SOLUTION = "reviewReason.service.perSalesTechnicalSolution";

        /** 批量实施单漏执行 */
        public static final String OMITTED_IMPLEMENTATION_TASKS = "reviewReason.service.omittedImplementationTasks";

        /** 我司工程师技能不足 */
        public static final String ZTE_ENGINEER_INSUFFICIENT_SKILL
                = "reviewReason.service.zteEngineerInsufficientSkills";

        /** 我司外包人员技能不足 */
        public static final String ZTE_OUTSOURCING_PERSONNEL_INSUFFICIENT_SKILL
                = "reviewReason.service.zteOutsourcingPersonnelInsufficientSkills";

        /** 我司工程师操作不规范 */
        public static final String ZTE_ENGINEER_NON_STANDARD_OPERATION
                = "reviewReason.service.zteEngineerNonStandardOperations";

        /** 我司外包人员操作不规范 */
        public static final String ZTE_OUTSOURCING_PERSONNEL_NON_STANDARD_OPERATION
                = "reviewReason.service.zteOutsourcingPersonnelNonStandardOperations";

        /** 我司工程师操作失误 */
        public static final String ZTE_ENGINEER_OPERATION_ERROR = "reviewReason.service.zteEngineerOperationError";

        /** 我司外包人员操作失误 */
        public static final String ZTE_OUTSOURCING_PERSONNEL_OPERATION_ERROR
                = "reviewReason.service.zteOutsourcingPersonnelOperationError";

        /** 培训服务 */
        public static final String TRAINING_SERVICE = "reviewReason.service.trainingService";

        /** 工程质量 */
        public static final String ENGINEERING_QUALITY = "reviewReason.service.engineeringQuality";
    }

    /**
     * 具体原因 - 外部
     */
    public static class ExternalReason {

        /** 我司其他产品 */
        public static final String OUR_ZTE_PRODUCTS = "reviewReason.external.otherZteProduct";

        /** 不可抗力 */
        public static final String FORCE_MAJEURE = "reviewReason.external.forceMajeure";

        /** 第三方 */
        public static final String THIRD_PARTY = "reviewReason.external.thirdParty";
    }

    /**
     * 具体原因 - 客户
     */
    public static class CustomerReason {

        /** 客户需求 */
        public static final String REQUIREMENT = "reviewReason.customer.requirement";

        /** 客户操作失误 */
        public static final String OPERATION_ERROR = "reviewReason.customer.operationError";

        /** 客户技能不足 */
        public static final String INSUFFICIENT_SKILL = "reviewReason.customer.insufficientSkill";

        /** 客户操作不规范 */
        public static final String NON_STANDARD_OPERATION = "reviewReason.customer.nonStandardOperation";

        /** 客户管理 */
        public static final String MANAGEMENT = "reviewReason.customer.management";
    }
}
