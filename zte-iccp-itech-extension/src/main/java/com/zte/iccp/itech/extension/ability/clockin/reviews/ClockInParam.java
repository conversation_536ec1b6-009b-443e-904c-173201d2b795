package com.zte.iccp.itech.extension.ability.clockin.reviews;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.utils.CommonUtils.last;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.BatchTaskTypeEnum.BATCH_TASK;

/**
 * @author: 李江斌 10318434
 * @date: 2024/12/11
 */
public class ClockInParam {

    /**
     * 变更单信息-key为变更单ID
     */
    private Map<String, IChangeOrder> changeOrderMap;

    /**
     * 批次任务信息-key为批次任务ID
     */
    private Map<String, IBatchTask> iBatchTaskMap;

    /**
     * 变更单网络信息-key为变更单ID
     */
    private Map<String, List<NisNetwork>> networkMap;

    /**
     * 产品信息-key为去掉最后/的产品全路径ID
     */
    private Map<String, BasicProductInfo> productMap;

    /**
     * 代表处信息-key为代表处层级的ID
     */
    private Map<String, BasicOrganizationInfo> organizationInfoMap;

    /**
     * 代表处信息英文-key为代表处层级的ID
     */
    private Map<String, BasicOrganizationInfo> organizationInfoEnMap;

    /**
     * 操作类型快码-key为快码编码
     */
    private Map<String, LookupValue> operateTypeLookupMap;

    public ClockInParam(List<ClockInTask> clockInTasks) {
        List<String> insideBatchTaskIdList = new ArrayList<>();
        List<String> subBatchTaskIdList = new ArrayList<>();
        List<String> changeOrderIds = new ArrayList<>();
        List<String> subChangeOrderIds = new ArrayList<>();
        List<String> productNoList = new ArrayList<>();
        List<String> responsibleDeptIdList = new ArrayList<>();
        for (ClockInTask clockInTask : clockInTasks) {
            if (clockInTask.getEntityType() == BATCH_TASK) {
                insideBatchTaskIdList.add(clockInTask.getBatchTaskId());
                changeOrderIds.add(clockInTask.getChangeOrderId());
            } else {
                subBatchTaskIdList.add(clockInTask.getBatchTaskId());
                subChangeOrderIds.add(clockInTask.getChangeOrderId());
            }
            // 产品分类
            String prodClassTemp = clockInTask.getProductClassification();
            productNoList.add(prodClassTemp.substring(0,prodClassTemp.length() - 1));
            // 代表处
            responsibleDeptIdList.add(last(clockInTask.getResponsibleDept()));
        }
        // 查询变更单
        this.changeOrderMap = ClockInQueryAbility.queryChangeOrders(changeOrderIds, subChangeOrderIds);
        // 查询批次任务
        this.iBatchTaskMap = ClockInQueryAbility.queryBatchTasks(insideBatchTaskIdList, subBatchTaskIdList, Lists.newArrayList());
        // 查变更单操作对象（客户网络信息）
        this.networkMap = ClockInQueryAbility.queryNetworkByChangeOrderIds(changeOrderIds, subChangeOrderIds);
        // 查询产品
        List<BasicProductInfo> basicProductInfos = NisClient.queryProductInfo(productNoList);
        this.productMap = basicProductInfos.stream().collect(Collectors.toMap(item -> item.getPidPath() + item.getId(), t -> t, (v1, v2) -> v1));
        // 查询中英文代表处（全路径）
        String langId = ContextHelper.getLangId();
        ContextHelper.setLangId(ZH_CN);
        this.organizationInfoMap = HrClient.queryOrganizationInfo(responsibleDeptIdList);
        ContextHelper.setLangId(EN_US);
        this.organizationInfoEnMap = HrClient.queryOrganizationInfo(responsibleDeptIdList);
        ContextHelper.setLangId(langId);
        // 查询操作类型快码
        List<LookupValue> lookupValues = LookupValueHelper.getLookupValues(
                LookupValueConstant.OPERATE_TYPE_ENUM, new ArrayList<>());
        this.operateTypeLookupMap = lookupValues.stream().collect(Collectors.toMap(LookupValue::getLookupCode, item -> item, (v1, v2) -> v1));
    }

    public IChangeOrder getChangeOrder(String changeOrderId) {
        return changeOrderMap.get(changeOrderId);
    }

    public IBatchTask getIBatchTask(String batchTaskId) {
        return iBatchTaskMap.get(batchTaskId);
    }

    public List<NisNetwork> getNisNetworkList(String changeOrderId) {
        List<NisNetwork> nisNetworkList = networkMap.get(changeOrderId);
        if (CollectionUtils.isNotEmpty(nisNetworkList)) {
            return Collections.unmodifiableList(nisNetworkList);
        }
        return null;
    }

    public BasicProductInfo getProductInfo(String productPath) {
        return productMap.get(productPath);
    }

    public BasicOrganizationInfo getOrganizationInfo(String officeId) {
        return organizationInfoMap.get(officeId);
    }

    public BasicOrganizationInfo getOrganizationInfoEn(String officeId) {
        return organizationInfoEnMap.get(officeId);
    }

    public LookupValue getOperateTypeLookup(String lookupCode) {
        return operateTypeLookupMap.get(lookupCode);
    }
}
