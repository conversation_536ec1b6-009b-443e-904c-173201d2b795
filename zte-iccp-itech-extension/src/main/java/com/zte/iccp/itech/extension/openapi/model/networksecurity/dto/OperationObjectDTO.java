package com.zte.iccp.itech.extension.openapi.model.networksecurity.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 操作对象
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/2
 */
@Getter
@Setter
public class OperationObjectDTO {

    /** 操作对象id */
    private String operationObjectId;

    /** 网络id */
    private String networkId;

    /** 网络code */
    private String networkCode;

    /** 客户网络名称 */
    private String networkName;

    /** 产品型号id */
    private String productModel;

    /** 产品型号名称 */
    private String productModelName;

    /** 局点名称（局点别名） */
    private String officeName;

    /** 当前版本id */
    private String currentVersionId;

    /** 当前版本名称 */
    private String currentVersion;

    /** 目标版本id */
    private String targetVersionId;

    /** 目标版本名称 */
    private String targetVersion;

}
