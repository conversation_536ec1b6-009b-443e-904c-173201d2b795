package com.zte.iccp.itech.extension.plugin.form.approvaldetails.helper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.changeorder.enums.NodeWithoutInlinePageEnum;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iss.approval.sdk.bean.ApprovalProcessDTO;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.control.Control;
import com.zte.paas.lcap.ddm.common.api.control.container.Container;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.platform.domain.i18n.I18nUtil;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.helper.FlowHelper.DEFAULT_VARIANT_VALUE;
import static com.zte.iccp.itech.extension.domain.constant.CidConstants.COMPONENT_STEPS_CID;
import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.SYSTEM_USER;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum.ABOLISH;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum.*;
import static com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin.BasePluginVariants.SUBMIT_TIME;
import static com.zte.iccp.itech.extension.plugin.form.approvaldetails.constants.InlinePagePropsConstants.PAGE_STATUS_VALUE_VIEW;
import static com.zte.iccp.itech.extension.plugin.form.approvaldetails.helper.InlinePageHelper.setInlinePageProps;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.LABEL;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ReviewProgressHelper {

    private static final String COMPLETED_TASK_STATUS = "COMPLETED";

    private static final String ACTIVE_TASK_STATUS = "ACTIVE";

    private static final String NETWORK_CHANGE_FORMAT = "%s[%s]%s %s";

    // 会签节点集合（内部变更单）
    private static final Set<String> COUNTER_SIGN_NODE_EXTEND_ID_SET = Sets.newHashSet(
            RD_INTEGRATION.name(),
            NET_INTEGRATION.name(),
            MULTIMODE_PRODUCT_OWNER.name()
    );

    private static Map<String, String> REVIEW_RESULT_MAP;

    public static void setReviewProgress(List<ApproveRecord> approveRecords,
                                         LoadDataBaseFormPlugin.LoadDataEventArgs args,
                                         Function<String, AdCWithIP> func1,
                                         Function<String, AdCWithoutIP> func2,
                                         Map<String, List<TextValuePair>> resultMap,
                                         BinaryOperator<String> biOpera,
                                         boolean isPartner) {
        if (CollectionUtils.isEmpty(approveRecords)) {
            return;
        }
        approveRecords = approveRecords.stream().filter(v->StringUtils.hasText(v.getExtendedCode())).collect(Collectors.toList());
        List<ReviewProgressNodeProps> reviewProgressNodePropsList
                = getReviewProgressNodeProps(args.getFormView(), approveRecords, func1, func2, resultMap, biOpera, isPartner);
        setReviewProgressNodeProps(args, reviewProgressNodePropsList);
        hiddenNodesDisableValidation(args);
    }

    // 设置网络变更申请单标题（默认展示）
    public static void setNetworkChangeRequestForm(Assignment assignment,
            IChangeOrder changeOrder, String inlinePageCid,
            String componentCid, IDataModel dataModel, IFormView formView) {
        if (null == changeOrder) {
            return;
        }
        // 网络变更申请单
        String networkChange = MsgUtils.getMessage(NETWORK_CHANGE_APPLICATION_FORM);
        // 提单时间
        Map<String, Object> flowVariables = FlowHelper.getFlowVariables((String) dataModel.getRootDataEntity().getPkValue());
        String strSubmitTime = flowVariables.getOrDefault(SUBMIT_TIME.getKey(), "0").toString();
        Date submitDate = DEFAULT_VARIANT_VALUE.equals(strSubmitTime)
                ? new Date() : new Date(Long.parseLong(strSubmitTime));
        String dateStr = DateUtils.dateToString(submitDate, DATE_FORM);
        // 提单人
        ApprovalProcessDTO approvalProcessDTO = getInstanceApprovalProcess(assignment);
        String title;
        if (null != approvalProcessDTO) {
            String submitterId = approvalProcessDTO.getApplicant();
            String submitter = HrClient.queryEmployeeNameInfo(submitterId);
            // 提交申请
            String submitApplication = MsgUtils.getMessage(SUBMIT_APPLICATION);
            title = String.format(NETWORK_CHANGE_FORMAT,
                    networkChange,
                    dateStr,
                    submitter,
                    submitApplication);
        } else {
            // 没有审批流，草稿状态下手动废止场景
            title = networkChange;
            // 步骤条放到第一位
            Object currentStepObj = formView.getAttribute(COMPONENT_STEPS_CID, CURRENT_STEP);
            if (!ObjectUtils.isEmpty(currentStepObj)) {
                formView.getClientViewProxy().setControlState(COMPONENT_STEPS_CID, CURRENT_STEP,
                        (Integer) currentStepObj - 1);
            }
        }

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(LABEL, title);
        Map<String, Object> viewMap = new HashMap<>();
        viewMap.put(componentCid, dataMap);
        formView.getClientViewProxy().setProps(viewMap);

        // 将审批页面中的提单内嵌页面设置为只读
        InlinePageHelper.setInlinePageProps(dataModel, formView, inlinePageCid, OPERATION_VIEW);
    }

    public static void shownSteps(IFormView formView) {
        // 展示步骤条（兼容历史数据）
        formView.getClientViewProxy().setControlState(COMPONENT_STEPS_CID,
                new PageStatusAttributeBuilder().normal().build());
    }

    private static List<ReviewProgressNodeProps> getReviewProgressNodeProps(IFormView formView,
                                                                            List<ApproveRecord> approveRecords,
                                                                            Function<String, AdCWithIP> func1,
                                                                            Function<String, AdCWithoutIP> func2,
                                                                            Map<String, List<TextValuePair>> resultMap,
                                                                            BinaryOperator<String> biOpera,
                                                                            boolean isPartner) {
        List<ReviewProgressNodeProps> reviewProgressNodePropsList = Lists.newArrayList();
        for (int i = 0; i < approveRecords.size(); i++) {
            ApproveRecord approveRecord = approveRecords.get(i);
            ReviewProgressNodeProps reviewProgressNodeProps = new ReviewProgressNodeProps();
            reviewProgressNodePropsList.add(reviewProgressNodeProps);
            String key = approveRecord.getExtendedCode();
            if (!StringUtils.hasText(key)) {
                break;
            }
            // 状态为已完成的节点
            if (COMPLETED_TASK_STATUS.equals(approveRecord.getTaskStatus())) {
                AdCWithIP adCIP = func1.apply(key);
                reviewProgressNodeProps.setContainerCid(adCIP.getEditPanelCid());
                reviewProgressNodeProps.setEmbedCid(adCIP.getEmbedCid());
                reviewProgressNodeProps.setTaskStatus(COMPLETED_TASK_STATUS);
                reviewProgressNodeProps.setApprover(approveRecord.getApprover());
                reviewProgressNodeProps.setExtendedCode(approveRecord.getExtendedCode());
                // 设置已审批节点（会签节点除外）的高级容器标题
                setNodeTitle(formView, key, adCIP.getEditPanelCid(), approveRecord, resultMap, biOpera, isPartner);
            } else if (ACTIVE_TASK_STATUS.equals(approveRecord.getTaskStatus())) {
                // 处于激活中的节点
                AdCWithoutIP adCIP = func2.apply(key);
                reviewProgressNodeProps.setContainerCid(adCIP.getEditPanelCid());
                reviewProgressNodeProps.setTaskStatus(approveRecord.getTaskStatus());
                reviewProgressNodeProps.setApprover(approveRecord.getApprover());
                reviewProgressNodeProps.setExtendedCode(approveRecord.getExtendedCode());
            }
        }
        return reviewProgressNodePropsList;
    }

    public static List<ReviewProgressNodeProps> getReviewProgressNodeProps(IFormView formView,
                                                                           List<ApproveRecord> approveRecords,
                                                                           Function<String, AdCWithIP> func1,
                                                                           Map<String, List<TextValuePair>> resultMap,
                                                                           BinaryOperator<String> biOpera,
                                                                           boolean isPartner) {
        List<ReviewProgressNodeProps> reviewProgressNodePropsList = Lists.newArrayList();
        for (int i = 0; i < approveRecords.size(); i++) {
            ApproveRecord approveRecord = approveRecords.get(i);
            ReviewProgressNodeProps reviewProgressNodeProps = new ReviewProgressNodeProps();
            String key = approveRecord.getExtendedCode();
            if (!StringUtils.hasText(key)) {
                break;
            }
            reviewProgressNodePropsList.add(reviewProgressNodeProps);
            if (COMPLETED_TASK_STATUS.equals(approveRecord.getTaskStatus())) {
                AdCWithIP adCIP = func1.apply(key);
                reviewProgressNodeProps.setContainerCid(adCIP.getEditPanelCid());
                reviewProgressNodeProps.setEmbedCid(adCIP.getEmbedCid());
                reviewProgressNodeProps.setTaskStatus(COMPLETED_TASK_STATUS);
                reviewProgressNodeProps.setApprover(approveRecord.getApprover());
                reviewProgressNodeProps.setExtendedCode(approveRecord.getExtendedCode());
                // 设置高级容器标题
                setNodeTitle(formView, key, adCIP.getEditPanelCid(),approveRecord, resultMap, biOpera, isPartner);
            }
        }
        return reviewProgressNodePropsList;
    }

    private static void setReviewProgressNodeProps(LoadDataBaseFormPlugin.LoadDataEventArgs args,
                                            List<ReviewProgressNodeProps> reviewProgressNodePropsList) {
        // 处理会签节点
        setCounterSignNode(args, reviewProgressNodePropsList);
        for (ReviewProgressNodeProps reviewProgressNodeProp : reviewProgressNodePropsList) {
            String approver = reviewProgressNodeProp.getApprover();
            if (SYSTEM_USER.equals(approver)) {
                continue;
            }
            // 处理审批进展中的高级容器
            if (COMPLETED_TASK_STATUS.equals(reviewProgressNodeProp.getTaskStatus())) {
                args.getFormView().getClientViewProxy().setControlState(reviewProgressNodeProp.getContainerCid(),
                        new PageStatusAttributeBuilder().normal().build());
                // 设置已审批节点的内嵌页面状态为查看
                setInlinePageProps(args.getModel(), args.getFormView(),
                        reviewProgressNodeProp.getEmbedCid(), PAGE_STATUS_VALUE_VIEW);
            } else if (ACTIVE_TASK_STATUS.equals(reviewProgressNodeProp.getTaskStatus())) {
                String userId = RequestHeaderUtils.getUserId();
                // 当前用户和审批人一致
                if (userId.equals(approver)) {
                    // 未审批的节点，高级容器设置为显示和展开
                    args.getFormView().getClientViewProxy().setControlState(reviewProgressNodeProp.getContainerCid(),
                            new PageStatusAttributeBuilder().normal().build());
                    args.getFormView().getClientViewProxy().setExpand(reviewProgressNodeProp.getContainerCid(),true);
                }
            }
        }
    }

    // 处理会签节点
    private static void setCounterSignNode(LoadDataBaseFormPlugin.LoadDataEventArgs args,
                                           List<ReviewProgressNodeProps> reviewProgressNodePropsList) {
        // 深拷贝
        List<ReviewProgressNodeProps> reviewProgressNodePropsListTmp
                = reviewProgressNodePropsList.stream().collect(Collectors.toList());
        // 统计会签节点审批个数
        Map<String, Integer> counterSignNodeNumMap = counterSignNodeNum(reviewProgressNodePropsList);
        Map<String, Integer> counterSignNodeNumMapTmp = Maps.newHashMap();
        Iterator<ReviewProgressNodeProps> iterator = reviewProgressNodePropsListTmp.iterator();
        while(iterator.hasNext()) {
            ReviewProgressNodeProps reviewProgressNodeProp = iterator.next();
            if (COUNTER_SIGN_NODE_EXTEND_ID_SET.contains(reviewProgressNodeProp.getExtendedCode())) {
                // 移除该会签节点
                reviewProgressNodePropsList.remove(reviewProgressNodeProp);
                // 会签节点没有处理完成
                if (ACTIVE_TASK_STATUS.equals(reviewProgressNodeProp.getTaskStatus())) {
                    String userId = RequestHeaderUtils.getUserId();
                    if (userId.equals(reviewProgressNodeProp.getApprover())) {
                        // 未审批的节点，高级容器设置为显示和展开
                        args.getFormView().getClientViewProxy().setControlState(reviewProgressNodeProp.getContainerCid(),
                                new PageStatusAttributeBuilder().normal().build());
                        args.getFormView().getClientViewProxy().setExpand(reviewProgressNodeProp.getContainerCid(),true);
                    }
                } else if (COMPLETED_TASK_STATUS.equals(reviewProgressNodeProp.getTaskStatus())) {
                    setCompletedCounterSignNodeProps(args,
                            counterSignNodeNumMap, counterSignNodeNumMapTmp, reviewProgressNodeProp);
                }
            }
        }
    }

    private static void setCompletedCounterSignNodeProps(LoadDataBaseFormPlugin.LoadDataEventArgs args,
                                                         Map<String, Integer> counterSignNodeNumMap,
                                                         Map<String, Integer> counterSignNodeNumMapTmp,
                                                         ReviewProgressNodeProps reviewProgressNodeProp) {
        counterSignNodeNumMapTmp.compute(reviewProgressNodeProp.getExtendedCode(),
                (key, value) -> (value == null) ? INTEGER_ONE : value + INTEGER_ONE);
        Integer times = counterSignNodeNumMapTmp.get(reviewProgressNodeProp.getExtendedCode());
        int comparison =
                Integer.compare(times, counterSignNodeNumMap.get(reviewProgressNodeProp.getExtendedCode()));
        // 该审批完成
        if (comparison == INTEGER_ZERO) {
            // 该会签节点均审批完成
            args.getFormView().getClientViewProxy().setControlState(reviewProgressNodeProp.getContainerCid(),
                    new PageStatusAttributeBuilder().normal().build());
            // 设置已审批节点的内嵌页面状态为查看
            setInlinePageProps(args.getModel(), args.getFormView(),
                    reviewProgressNodeProp.getEmbedCid(), PAGE_STATUS_VALUE_VIEW);
        }
    }

    private static Map<String, Integer> counterSignNodeNum(List<ReviewProgressNodeProps> reviewProgressNodePropsList) {
        Map<String, Integer> counterSignNodeNumMap = new HashMap<>();
        for (ReviewProgressNodeProps reviewProgressNodeProp : reviewProgressNodePropsList) {
            if (COUNTER_SIGN_NODE_EXTEND_ID_SET.contains(reviewProgressNodeProp.getExtendedCode())) {
                counterSignNodeNumMap.compute(reviewProgressNodeProp.getExtendedCode(),
                        (key, value) -> (value == null) ? INTEGER_ONE : value + INTEGER_ONE);
            }
        }
        return counterSignNodeNumMap;
    }

    private static void hiddenNodesDisableValidation(LoadDataBaseFormPlugin.LoadDataEventArgs args) {
        for (NodeWithoutInlinePageEnum node : NodeWithoutInlinePageEnum.values()) {
            Container container = (Container) args.getFormView().getControl(
                    node.getAdCWithoutIP().getEditPanelCid());
            if (container == null || !HIDDEN.equals(container.getAttribute(BEHAVIOR))) {
                continue;
            }

            disableValidation(container);
        }
    }

    private static void disableValidation(Control control) {
        if (control == null) {
            return;
        }

        if (control instanceof Container) {
            Container container = (Container) control;
            for (Control child : container.getControls()) {
                disableValidation(child);
            }
            return;
        }

        control.getClientViewProxy().setControlState(
                control.getCid(), CommonConstants.REQUIRED, false);
    }
    public static void setReviewProgressNodeProps(IFormView formView,
                                                  IDataModel dataModel,
                                                  List<ReviewProgressNodeProps> reviewProgressNodePropsList) {
        for (ReviewProgressNodeProps reviewProgressNodeProp : reviewProgressNodePropsList) {
            // 处理审批进展中的高级容器
            if (COMPLETED_TASK_STATUS.equals(reviewProgressNodeProp.getTaskStatus())) {
                formView.getClientViewProxy().setControlState(reviewProgressNodeProp.getContainerCid(),
                        new PageStatusAttributeBuilder().normal().build());
                // 设置已审批节点的内嵌页面状态为查看
                setInlinePageProps(dataModel,
                        formView,
                        reviewProgressNodeProp.getEmbedCid(),
                        PAGE_STATUS_VALUE_VIEW);
            }
        }
    }

    // 在图文展示中，设置当前处理节点
    public static String setRichTextCurrentNode(LoadDataBaseFormPlugin.LoadDataEventArgs args,
                                              String assignmentStatus,
                                              String currentNodeCode,
                                              String richTextCurrentNode, boolean isPartner) {
        // 流程终止状态单据，隐藏当前处理节点图文展示
        if (ABOLISH == AssignmentStatusEnum.fromValue(assignmentStatus)) {
            args.getFormView().getClientViewProxy().setControlState(richTextCurrentNode,
                    new PageStatusAttributeBuilder().hidden().build());
            return EMPTY_STRING;
        }
        if (!StringUtils.hasText(currentNodeCode)) {
            return EMPTY_STRING;
        }
        String currentReviewerNode =
                I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), CURRENT_REVIEWER_NODE);
        String processNodeName = getProcessNodeName(isPartner, currentNodeCode);
        args.getFormView().getClientViewProxy().setControlState(richTextCurrentNode,
                VALUE, currentReviewerNode + COLON + processNodeName);
        return currentReviewerNode + COLON + processNodeName + SEMICOLON + SPACE;
    }

    private static String getProcessNodeName(boolean isPartner, String extendedCode) {
        String processNodeName;
        if (!isPartner) {
            ApproveNodeEnum approveNodeEnum = ApproveNodeEnum.getApproveNodeEnum(extendedCode);
            processNodeName = null == approveNodeEnum ? EMPTY_STRING : approveNodeEnum.getName(ContextHelper.getLangId());
        } else {
            PartnerApproveNodeEnum approveNodeEnum = PartnerApproveNodeEnum.getApproveNodeEnum(extendedCode);
            processNodeName = null == approveNodeEnum ? EMPTY_STRING : approveNodeEnum.getName(ContextHelper.getLangId());
        }
        return processNodeName;
    }

    // 在图文展示中，设置当前处理人图文展示
    public static String setRichTextCurrentReviewer(LoadDataBaseFormPlugin.LoadDataEventArgs args,
            String assignmentStatus, List<Employee> currentProcessors, String richTextCurrentReviewer) {
        // 流程终止状态单据，隐藏当前处理人
        if (ABOLISH == AssignmentStatusEnum.fromValue(assignmentStatus)) {
            args.getFormView().getClientViewProxy().setControlState(richTextCurrentReviewer,
                    new PageStatusAttributeBuilder().hidden().build());
            return EMPTY_STRING;
        }
        StringBuilder stringBuilder = new StringBuilder();
        String currentReviewer =
                I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), CURRENT_PROCESSOR);
        for (Employee currentProcessor : currentProcessors) {
            String userName = ZH_CN.equals(ContextHelper.getLangId())
                    ? currentProcessor.getEmpNameCn() : currentProcessor.getEmpNameEn();
            String userInfo = userName + currentProcessor.getEmpUIID();
            stringBuilder.append(userInfo).append(SPACE);
        }
        if (StringUtils.hasText(stringBuilder.toString())) {
            stringBuilder.insert(INTEGER_ZERO, COLON);
            stringBuilder.insert(INTEGER_ZERO, currentReviewer);
            args.getFormView().getClientViewProxy().setControlState(richTextCurrentReviewer,
                    VALUE,  stringBuilder);
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(SEMICOLON);
            return stringBuilder.toString();
        }
        return EMPTY_STRING;
    }

    // 驳回到重新提单时，当前处理节点为"驳回待修改"
    public static void rejectModification(LoadDataBaseFormPlugin.LoadDataEventArgs args, String richTextCid) {
        String currentReviewerNode =
                I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), CURRENT_REVIEWER_NODE);
        String rejectionModification =
                I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), REJECT_MODIFICATION);
        args.getFormView().getClientViewProxy().setControlState(richTextCid,
                VALUE, currentReviewerNode + COLON + rejectionModification);
    }

    public static String getRejectedCurrentNode() {
        String currentReviewerNode =
                MsgUtils.getMessage(CURRENT_REVIEWER_NODE);
        String rejectionModification =
                I18nUtil.getI18nEntryMsg(ContextHelper.getAppId(), REJECT_MODIFICATION);
        return currentReviewerNode + COLON + rejectionModification + SEMICOLON + SPACE;
    }

    /** 设置当前节点与当前审批人 */
    public static void setCurrentNodeAndReviewer(LoadDataBaseFormPlugin.LoadDataEventArgs args,
                                                 String currentNodeAndReviewerCid,
                                                 String currentNodeAndReviewer) {
        args.getFormView().getClientViewProxy().setControlState(currentNodeAndReviewerCid,
                VALUE, currentNodeAndReviewer);
    }


    // 设置高级容器节点标题
    private static void setNodeTitle(IFormView formView,
                                     String key,
                                     String componentCid,
                                     ApproveRecord approveRecord,
                                     Map<String, List<TextValuePair>> resultMap,
                                     BinaryOperator<String> biOpera, boolean isPartner) {
        // 设置已审批节点的高级容器标题
        String title;
        if (REMOTE_CENTER_SCHEME.name().equals(key) && !isPartner) {
            // 获取远程中心方案（内部变更单）审核标题
            title = getRemoteCenterSchemeTitle(key, approveRecord);
        } else if (REMOTE_CENTER_OPER.name().equals(key) && !isPartner) {
            // 获取远程中心操作方案实施指派（内部变更单）审核标题
            title = getRemoteCenterOperTitle(key, approveRecord);
        } else {
            title = getTitle(key, approveRecord, resultMap, biOpera);
        }
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(LABEL, title);
        Map<String, Object> viewMap = new HashMap<>();
        viewMap.put(componentCid, dataMap);
        formView.getClientViewProxy().setProps(viewMap);
    }

    private static String getTitle(String extendNodeCode,
                                   ApproveRecord approveRecord,
                                   Map<String, List<TextValuePair>> resultMap,
                                   BinaryOperator<String> biOpera) {
        if (null == approveRecord) {
            return EMPTY_STRING;
        }
        String approvalResult = EMPTY_STRING;
        if (resultMap.containsKey(approveRecord.getExtendedCode())) {
            // 区分会签节点的审核结果

            List<TextValuePair> approvalResultPair = resultMap.get(approveRecord.getExtendedCode());
            Optional<String> approvalResultText = Optional.ofNullable(approvalResultPair)
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(INTEGER_ZERO))
                    .map(textValuePair -> textValuePair.getTextByLanguage(ContextHelper.getLangId()));
            approvalResult = approvalResultText.orElse(EMPTY_STRING);
        }
        // 姓名 + 工号
        String nameInfo = HrClient.queryEmployeeNameInfo(approveRecord.getApprover());
        SimpleDateFormat fmt = new SimpleDateFormat(DATE_FORM);
        String dateString = fmt.format(approveRecord.getApprovalDate());
        String nodeName = biOpera.apply(extendNodeCode, ContextHelper.getLangId());
        String reviewResult;
        initReviewResultMap();
        if (REVIEW_RESULT_MAP.containsKey(approveRecord.getExtendedCode())) {
            reviewResult = REVIEW_RESULT_MAP.get(approveRecord.getExtendedCode());
        } else {
            reviewResult = MsgUtils.getMessage(MessageConsts.REVIEW_RESULT);
        }
        return nodeName + LEFT_BRACKET + LEFT_SQUARE_BRACKET + dateString
                + RIGHT_SQUARE_BRACKET + nameInfo + SPACE + reviewResult + approvalResult + RIGHT_BRACKET;
    }

    private static void initReviewResultMap() {
        REVIEW_RESULT_MAP = MapUtils.newHashMap(
                REGIONAL_TD_CONFIRM.name(), MsgUtils.getMessage(MessageConsts.BCN_MULTI_PRODUCT_LINKAGE_SUPPORT),
                REMOTE_CENTER_OWNER.name(), MsgUtils.getMessage(MessageConsts.REMOTE_CENTER_ENABLED_OR_NOT));
    }

    // 获取远程中心方案审核标题
    private static String getRemoteCenterSchemeTitle(String extendNodeCode, ApproveRecord approveRecord) {
        if (null == approveRecord || !REMOTE_CENTER_SCHEME.name().equals(extendNodeCode)) {
            return EMPTY_STRING;
        }
        // 姓名 + 工号
        String nameInfo = HrClient.queryEmployeeNameInfo(approveRecord.getApprover());
        SimpleDateFormat fmt = new SimpleDateFormat(DATE_FORM);
        //TODO feng 临时改成服务器时区（北京时间），后续待需求细化再进行调整
        String dateString = fmt.format(approveRecord.getApprovalDate());
        String nodeName = ApproveNodeEnum.getApproveNodeName(extendNodeCode, ContextHelper.getLangId());
        String submitOperationPlan = MsgUtils.getMessage(MessageConsts.SUBMIT_OPERATION_PLAN);
        return nodeName + LEFT_BRACKET + LEFT_SQUARE_BRACKET + dateString
                + RIGHT_SQUARE_BRACKET + nameInfo + SPACE + submitOperationPlan + RIGHT_BRACKET;
    }

    // 获取远程中心操作方案实施指派审核标题
    private static String getRemoteCenterOperTitle(String extendNodeCode, ApproveRecord approveRecord) {
        if (null == approveRecord || !REMOTE_CENTER_OPER.name().equals(extendNodeCode)) {
            return EMPTY_STRING;
        }
        // 姓名 + 工号
        String nameInfo = HrClient.queryEmployeeNameInfo(approveRecord.getApprover());
        SimpleDateFormat fmt = new SimpleDateFormat(DATE_FORM);
        //TODO feng 临时改成服务器时区（北京时间），后续待需求细化再进行调整
        String dateString = fmt.format(approveRecord.getApprovalDate());
        String nodeName = ApproveNodeEnum.getApproveNodeName(extendNodeCode, ContextHelper.getLangId());
        return nodeName + LEFT_BRACKET + LEFT_SQUARE_BRACKET + dateString
                + RIGHT_SQUARE_BRACKET + nameInfo + SPACE  + RIGHT_BRACKET;
    }


    /**
     * 设置批次高级容器节点标, 不包含国际会签
     * @param formView view
     * @param componentCid 高级容器cid
     * @param approveRecord  审核节点记录
     * @param result  审核结果
     * @param isSub 是否是分包商
     */
    public static void setBatchNodeTitle(IFormView formView, String componentCid, ApproveRecord approveRecord,
                                         List<TextValuePair> result, boolean isSub) {
        if (approveRecord == null) {
            return;
        }
        // 设置已审批节点的高级容器标题
        String nodeName = ApproveNodeEnum.getApproveNodeName(approveRecord.getExtendedCode(), ContextHelper.getLangId());
        if(isSub){
            nodeName = PartnerApproveNodeEnum.getApproveNodeName(approveRecord.getExtendedCode(), ContextHelper.getLangId());
        }
        //TODO feng 临时改成服务器时区（北京时间），后续待需求细化再进行调整
        String dateString = DateUtils.dateToString(approveRecord.getApprovalDate(), CommonConstants.DATE_FORM);
        String nameInfo = HrClient.queryEmployeeNameInfo(approveRecord.getApprover());
        String reviewResult = MsgUtils.getMessage(MessageConsts.REVIEW_RESULT);
        Optional<String> approvalResultText = Optional.ofNullable(result)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(INTEGER_ZERO))
                .map(textValuePair -> textValuePair.getTextByLanguage(ContextHelper.getLangId()));
        String approvalResult = approvalResultText.orElse(EMPTY_STRING);
        String title = nodeName + LEFT_BRACKET + LEFT_SQUARE_BRACKET + dateString
                + RIGHT_SQUARE_BRACKET + nameInfo + SPACE + reviewResult + approvalResult + RIGHT_BRACKET;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(LABEL, title);
        Map<String, Object> viewMap = new HashMap<>();
        viewMap.put(componentCid, dataMap);
        formView.getClientViewProxy().setProps(viewMap);
    }

    /**
     * 获取审批流程进展信息
     * @param assignment 任务中心数据
     * @return ApprovalProcessDTO
     */
    public static ApprovalProcessDTO getInstanceApprovalProcess(Assignment assignment) {
        if (Objects.isNull(assignment)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ASSIGNMENT_NOT_EXISTS));
        }

        // 2.获取审批实体ID + 审批流程编码
        String bizId;
        ApproveFlowCodeEnum approveFlowCode;

        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        switch (assignmentType) {
            case NETWORK_CHANGE:
                bizId = assignment.getBillId();
                approveFlowCode = ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW;
                break;

            case SUBCONTRACTOR_NETWORK_CHANGE:
                bizId = assignment.getBillId();
                approveFlowCode = ApproveFlowCodeEnum.SUBCONTRACTOR_OC_FLOW;
                break;

            default:
                throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ERROR_ASSIGNMENT_TYPE));
        }

        // 3.检索审批流程信息
        return FlowHelper.getApprovalProcessInfo(bizId, approveFlowCode);
    }
}
