package com.zte.iccp.itech.extension.domain.model.clockin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.base.TreeSelectNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInReviewsFieldConsts.*;

/**
 * @author: 李江斌 10318434
 * @date: 2024/12/9
 */
@Getter
@Setter
@BaseEntity.Info("clock_in_review") // 是标识，不是表名
public class ClockInReviews extends BaseEntity{
    /**
     * 操作单号（复盘单单号）
     */
    @JsonProperty(value = REVIEWS_NO)
    private String reviewsNo;

    /**
     * 操作主题
     */
    @JsonProperty(value = OPERATION_SUBJECT)
    private String operationSubject;

    /**
     * 批次号
     */
    @JsonProperty(value = BATCH_NO)
    private String batchNo;

    /**
     * 客户网络名称
     */
    @JsonProperty(value = NETWORK_NAME)
    private List<TextValuePair> networkName;

    /**
     * 产品分类
     */
    @JsonProperty(value = PRODUCT_CLASSIFICATION)
    private List<TextValuePair> productClassification;

    /**
     * 代表处
     */
    @JsonProperty(value = RESPONSIBLE_DEPT)
    private List<TextValuePair> responsibleDept;

    /**
     * 营销
     */
    @JsonProperty(value = MARKETING)
    private List<TextValuePair> marketing;

    /**
     * 操作等级
     */
    @JsonProperty(value = OPERATION_LEVEL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private OperationLevelEnum operationLevel;

    /**
     * 操作类型（快码）
     */
    @JsonProperty(value = OPERATION_TYPE)
    private String operationType;

    /**
     * 操作类型（下拉单选）（用于移动审批展示字段）
     */
    @JsonProperty(value = OPERATION_TYPE_SELECT)
    private List<TextValuePair> operationTypeSelect;

    /**
     * 计划操作开始时间
     */
    @JsonProperty(value = PLAN_OPERATION_START_TIME)
    private Date planOperationStartTime;

    /**
     * 计划操作结束时间
     */
    @JsonProperty(value = PLAN_OPERATION_END_TIME)
    private Date planOperationEndTime;

    /**
     * 操作负责人
     */
    @JsonProperty(value = OPERATION_OWNERS)
    private List<Employee> operationOwners;

    /**
     * 当前处理人
     */
    @JsonProperty(value = CURRENT_PROCESSOR)
    private List<Employee> currentProcessor;

    /**
     * 复盘状态--复盘初审1、复盘提交2、复盘审核3、审核确认4、已关闭5
     * 同任务表字段 assignmentStatus 使用 AssignmentStatusEnum.getValue
     */
    @JsonProperty(value = REPLAY_STATUS)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private AssignmentStatusEnum replayStatus;

    /**
     * 值守时长（单位：分钟）
     */
    @JsonProperty(value = ON_DUTY_DURATION_HOURS)
    private Integer onDutyDurationHours;

    /**
     * 变更单ID
     */
    @JsonProperty(value = CHANGE_ORDER_ID)
    private String changeOrderId;

    /**
     * 批次任务ID
     */
    @JsonProperty(value = BATCH_TASK_ID)
    private String batchTaskId;

    /**
     * 任务类型   1内部变更单 6分包商变更单
     */
    @JsonProperty(value = ASSIGNMENT_TYPE)
    private List<TextValuePair> assignmentType;

    // BEGIN 复盘初审字段
    /**
     * 审核结果（复盘初审）
     */
    @JsonProperty(value = INIT_APPROVE_RESULT)
    private List<TextValuePair> initApproveResult;

    /**
     * 提交人（复盘初审）
     */
    @JsonProperty(value = INIT_SUBMITTER)
    private List<Employee> initSubmitter;

    /**
     * 提交时间（复盘初审）
     */
    @JsonProperty(value = INIT_SUBMIT_TIME)
    private Date initSubmitTime;

    // BEGIN 复盘提交字段
    /**
     * 提交人（复盘提交）
     */
    @JsonProperty(value = ST_SUBMITTER)
    private List<Employee> stSubmitter;

    /**
     * 提交时间（复盘提交）
     */
    @JsonProperty(value = ST_SUBMIT_TIME)
    private Date stSubmitTime;

    // BEGIN 复盘审核字段
    /**
     * 提交人（复盘审核）
     */
    @JsonProperty(value = AR_SUBMITTER)
    private List<Employee> arSubmitter;

    /**
     * 提交时间（复盘审核）
     */
    @JsonProperty(value = AR_SUBMIT_TIME)
    private Date arSubmitTime;

    // BEGIN 复盘确认字段
    /**
     * 提交人（复盘确认）
     */
    @JsonProperty(value = RE_SUBMITTER)
    private List<Employee> reSubmitter;

    /**
     * 提交时间（复盘确认）
     */
    @JsonProperty(value = RE_SUBMIT_TIME)
    private Date reSubmitTime;

    @ApiModelProperty("邮件抄送（复盘提交）")
    @JsonProperty(value = ST_EMAIL)
    private List<Employee> stEmail;

    @ApiModelProperty("邮件抄送（复盘审核）")
    @JsonProperty(value = AR_EMAIL)
    private List<Employee> arEmail;

    @ApiModelProperty("邮件抄送（审核确认）")
    @JsonProperty(value = RE_EMAIL)
    private List<Employee> reEmail;

    @ApiModelProperty("审核结果（复盘审核）")
    @JsonProperty(value = AR_APPROVE_RESULT)
    private List<TextValuePair> arApproveResult;

    @ApiModelProperty("是否整改")
    @JsonProperty(value = IS_RECTIFY)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isRectify;

    @JsonProperty(value = REASON_CLASSIFY)
    private List<TreeSelectNode> reasonClassify;
}
