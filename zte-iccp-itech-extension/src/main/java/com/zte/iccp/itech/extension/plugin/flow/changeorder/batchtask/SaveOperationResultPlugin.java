package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.utils.ComponentUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.OPERATION_RESULT;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

public class SaveOperationResultPlugin extends BaseFlowOperationPlugin {

    @Override
    public void afterOperate(ExecuteEvent executeEvent) {
        // 1.获取操作结果
        String batchId = getModel().getRootDataEntity().getPkValue().toString();
        List<TextValuePair> operationResult
                = ComponentUtils.getChooseComponentInfo(getModel(), BatchTaskFieldConsts.OPERATION_RESULT);

        // 2.检索任务中心对应数据
        NetworkChangeAssignment assignment = AssignmentAbility.queryBatchAssignment(batchId);
        if (Objects.isNull(assignment)) {
            return;
        }

        // 3.检索任务中心对应主任务
        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        AssignmentTypeEnum mainAssignmentType = AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)
                ? AssignmentTypeEnum.NETWORK_CHANGE
                : AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE;
        NetworkChangeAssignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                assignment.getBillId(), mainAssignmentType, NetworkChangeAssignment.class);
        if (Objects.isNull(mainAssignment)) {
            return;
        }

        // 4.更新操作结果
        NetworkChangeAssignment updateAssignment = new NetworkChangeAssignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setOperationResult(operationResult);

        NetworkChangeAssignment updateMainAssignment = new NetworkChangeAssignment();
        updateMainAssignment.setId(mainAssignment.getId());

        //获取所有批次操作结果
        List<IBatchTask> batchTasks = BatchTaskAbility.getBatchTaskByChangeOrderId(assignment.getBillId(), assignment,
                Lists.newArrayList(ID, OPERATION_RESULT));
        List<TextValuePair> mainOperationResults = batchTasks.stream()
                .filter(i -> !i.getId().equals(batchId) && !CollectionUtils.isEmpty(i.getOperationResult()))
                .map(IBatchTask::getOperationResult)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        mainOperationResults.addAll(operationResult);
        updateMainAssignment.setOperationResult(mainOperationResults);

        AssignmentAbility.batchUpdate(Lists.newArrayList(updateAssignment, updateMainAssignment));
    }
}
