package com.zte.iccp.itech.extension.common.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;

import java.io.IOException;

/**
 * 手机号加密【aes加密】
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/10/23
 */
public class StringEncryptSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String str, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        gen.writeString(ConfigHelper.createEncrypted(str));
    }
}
