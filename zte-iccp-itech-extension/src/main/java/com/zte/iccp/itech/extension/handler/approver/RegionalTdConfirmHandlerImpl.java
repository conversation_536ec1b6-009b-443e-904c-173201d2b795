package com.zte.iccp.itech.extension.handler.approver;

import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.List;

/**
 * <AUTHOR>
 * &#064;description:  2.4.1核心网大区TD审核
 * &#064;date  2024/4/29 3:21
 * zte-iccp-itech-netchange
 */
public class RegionalTdConfirmHandlerImpl extends AbstractRewardApproverHandler<ChangeOrder> {
    /**
     * N0010  申请单中客户标识属于中国移动或中国联通：【代表处】 +【运营商】（对应申请单中的对应客户标识）精确匹配：大区TD审核人+协同TD审核组均可审核；当前处理人=人+组
     * N0020   申请单中客户标识不属于中国移动或中国联通：【代表处】+【运营商】=其他精确匹配：大区TD审核人+协同TD审核组均可审核；当前处理人=人+组
     */
    @Override
    protected List<String> rewardApproverByNodeExtend(ChangeOrder changeOrder, FlowClient bod) {
        ApproverConfiguration queryParam = new ApproverConfiguration();
        // 核心网大区TD审核
        queryParam.setApprovalNode(ApprovalTypeEnum.CN_REGION_TD);
        // 运营商
        queryParam.setOperator(OperatorEnum.getCmOrCu(changeOrder.getCustomerTypeFlag()));
        // 代表处
        queryParam.setResponsibleDeptId(changeOrder.getResponsibleDept());
        queryParam.setOrganizationRegion(ResponsibleUtils.getRegion(changeOrder.getResponsibleDept()));
        queryParam.setSales(ResponsibleUtils.getSales(changeOrder.getResponsibleDept()));
        return ApproverConfigAbility.getApprovalPersons(queryParam, null);
    }

}
