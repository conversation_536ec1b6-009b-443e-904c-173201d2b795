package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorAttributeEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperatorFieldConsts.*;

@ApiModel("操作人员")
@Setter
@Getter
@BaseSubEntity.Info(value = "operator_table", parent = ChangeOrder.class)
public class Operator extends BaseSubEntity {

    @ApiModelProperty("人员角色")
    @JsonProperty(value = OPERATOR_ROLE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private OperatorRoleEnum operatorRole;

    @ApiModelProperty("远程操作标识")
    @JsonProperty(value = REMOTE_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum remoteFlag;

    @ApiModelProperty("姓名")
    @JsonProperty(value = OPERATOR_NAME)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee operatorName;

    @JsonProperty(value = OPERATOR_PHONE)
    @ApiModelProperty("电话")
    private String operatorPhone;

    @JsonProperty(value = OPERATOR_DEPARTMENT)
    @ApiModelProperty("所属部门")
    private String operatorDepartment;

    @JsonProperty(value = OPERATOR_ACCOUNT)
    @ApiModelProperty("操作账号")
    private List<TextValuePair> operatorAccount;

    @ApiModelProperty("人员属性")
    @JsonProperty(value = OPERATOR_ATTRIBUTE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private OperatorAttributeEnum operatorAttribute;

    @JsonProperty(value = TASK_DESC)
    @ApiModelProperty("任务说明")
    private String taskDesc;

    @JsonProperty(value = OPERATOR_BATCH_NO)
    @ApiModelProperty("操作批次")
    private List<TextValuePair> operatorBatchNo;

    @JsonIgnore
    public String getOperatorEpmNo(){
        if(getOperatorName() ==null){
            return "";
        }
        return getOperatorName().getEmpUIID();
    }
}
