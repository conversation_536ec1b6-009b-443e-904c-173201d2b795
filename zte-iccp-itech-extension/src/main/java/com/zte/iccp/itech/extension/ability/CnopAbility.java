package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiAttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.vo.ChangeOrderSave;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.*;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.CnopBatchTaskVO;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.CnopChangeOrderVO;
import com.zte.iccp.itech.extension.openapi.model.assignment.vo.CreateOrderVO;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.nis.BasicProductInfo;
import com.zte.iccp.itech.extension.spi.model.zxrdc.TreeServiceObjectDto;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.component.util.BillNumberGenerateUtil;
import com.zte.paas.lcap.ddm.common.api.dto.BillNumberGeneratorDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.helper.TaskNoHelper.NETWORK_CHANGE_TASK_CODE;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.LookupValueConstant.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_BILL_NETWORK_CHANGE_BILL;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_NETWORK_CHANGE_BILL;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.CustomizedNetworkSetConsts.ID;
import static com.zte.paas.lcap.ddm.common.api.constant.CommonConstants.SUCCESS;

/**
 * <AUTHOR>
 * @create 2025/3/18 上午10：36
 */
@Slf4j
public class CnopAbility {

    /* CNOP生成变更单的预览态URL */
    private static final String CNOP_CHANGE_ORDER_VIEW_URL = "cnop.changeOrder.view.pageUrl";


    public static List<CnopChangeOrderVO> getOrderInfo(List<CnopChangeOrderQueryDto> queryDtoList) {
        // 获取单据主键ID
        List<String> changerOrderIds = queryDtoList.stream()
                .filter(dto -> StringUtils.hasText(dto.getChangeOrderId()))
                .distinct()
                .map(CnopChangeOrderQueryDto::getChangeOrderId)
                .collect(Collectors.toList());

        // 查询单据信息
        List<ChangeOrder> changeOrders = ChangeOrderAbility.get(changerOrderIds,
                Arrays.asList(ID, ORDER_NO, ChangeOrderFieldConsts.OPERATION_SUBJECT, CREATE_BY), ChangeOrder.class);
        if (changerOrderIds.size() != changeOrders.size()) {
            throw new LcapBusiException(MsgUtils.getMessage(CHANGE_ORDER_ID_NOT_FIND));
        }
        // 查询单据状态
        Map<String, Pair<Assignment, LookupValue>> orderStatusMap = getChangeOrderStatus(changerOrderIds);
        // 查询变更单批次信息
        Map<String, List<CnopBatchTaskVO>> batchTaskInfoMap = getBatchTaskInfo(changerOrderIds);
        // 组装返回结果
        List<CnopChangeOrderVO> orderVOS = new ArrayList<>();
        for (ChangeOrder changeOrder : changeOrders) {
            CnopChangeOrderVO orderVO = new CnopChangeOrderVO();
            orderVO.setChangeOrderId(changeOrder.getId());
            orderVO.setChangeOrderNo(changeOrder.getOrderNo());
            orderVO.setChangeOrderName(changeOrder.getOperationSubject());
            orderVO.setCreateBy(changeOrder.getCreateBy());

            Pair<Assignment, LookupValue> pair = orderStatusMap.getOrDefault(changeOrder.getId(), Pair.of(null, null));
            LookupValue lookupValue = pair.getRight();
            if (lookupValue == null) {
                continue;
            }
            orderVO.setChangeStatus(lookupValue.getLookupCode());
            orderVO.setChangeStatusCn(lookupValue.getMeaningCn());
            orderVO.setChangeStatusEn(lookupValue.getMeaningEn());
            // 如果单据状态为草稿，返回单据详情页的url；否则，返回审批详情页url
            orderVO.setUrl(AssignmentStatusEnum.START.getValue().equals(lookupValue.getLookupCode())
                    ? String.format(ConfigHelper.get(CNOP_CHANGE_ORDER_VIEW_URL), changeOrder.getId(), PAGE_NETWORK_CHANGE_BILL)
                    : String.format(ConfigHelper.get(CNOP_CHANGE_ORDER_VIEW_URL), changeOrder.getId(), PAGE_BILL_NETWORK_CHANGE_BILL));
            orderVO.setLastModifiedTime(pair.getLeft().getLastModifiedTime());
            orderVO.setBatchTaskInfo(batchTaskInfoMap.getOrDefault(changeOrder.getId(), new ArrayList<>()));
            orderVOS.add(orderVO);
        }
        return orderVOS;
    }

    /**
     * 查询单据状态（中英文）
     * Map<单据ID，Pair<任务实体，单据状态快码实体>>
     */
    private static Map<String, Pair<Assignment, LookupValue>> getChangeOrderStatus(List<String> changerOrderIds) {
        // 查询主单任务数据
        List<Assignment> assignmentList = AssignmentAbility.querySpecificTypeAssignment(changerOrderIds, AssignmentTypeEnum.NETWORK_CHANGE, Assignment.class);
        if (changerOrderIds.size() != assignmentList.size()) {
            throw new LcapBusiException(MsgUtils.getMessage(CHANGE_ORDER_ID_NOT_FIND));
        }

        List<String> statusList = assignmentList.stream()
                .filter(assignment -> StringUtils.hasText(assignment.getAssignmentStatus()))
                .map(Assignment::getAssignmentStatus).distinct().collect(Collectors.toList());

        // 查询任务状态快码
        List<LookupValue> lookupValues = LookupValueHelper
                .getLookupValuesZhAndUs(ASSIGNMENT_STATUS_ENUM, statusList);
        if (CollectionUtils.isEmpty(lookupValues)) {
            return new HashMap<>();
        }
        Map<String, LookupValue> valueMap = lookupValues.stream()
                .collect(Collectors.toMap(LookupValue::getLookupCode, Function.identity()));

        Map<String, Pair<Assignment, LookupValue>> resultMap = new HashMap<>();
        for (Assignment assignment : assignmentList) {
            LookupValue lookupValue = valueMap.get(assignment.getAssignmentStatus());
            if (lookupValue == null) {
                continue;
            }
            resultMap.put(assignment.getBillId(), Pair.of(assignment, lookupValue));
        }

        return resultMap;
    }

    /**
     * 查询单据批次任务信息（中英文）
     * Map<单据ID，List<批次任务信息实体>>
     */
    private static Map<String, List<CnopBatchTaskVO>> getBatchTaskInfo(List<String> changerOrderIds) {
        // 批量查询批次任务数据
        List<BatchTask> batchTaskList = BatchTaskAbility
                .listBatchTaskByChangeOrderIdList(changerOrderIds, Lists.newArrayList(
                        BATCH_NAME, BATCH_NO, BATCH_CODE, CHANGE_ORDER_ID, OPERATION_RESULT,
                        ACTUAL_OPERATION_START_TIME, ACTUAL_OPERATION_END_TIME, FACT_INTERRUPT_TIME, TEST_FINISH_TIME));

        if (CollectionUtils.isEmpty(batchTaskList)) {
            return new HashMap<>();
        }

        // 转换VO
        List<CnopBatchTaskVO> batchTaskVOList = convertBatchTaskVo(batchTaskList);

        return batchTaskVOList.stream()
                .collect(Collectors.groupingBy(CnopBatchTaskVO::getChangeOrderId));
    }

    private static List<CnopBatchTaskVO> convertBatchTaskVo(List<BatchTask> batchTaskList) {

        List<CnopBatchTaskVO> batchTaskVOList = new ArrayList<>();
        for (BatchTask batchTask : batchTaskList) {
            CnopBatchTaskVO batchTaskVO = new CnopBatchTaskVO();
            batchTaskVO.setBatchCode(batchTask.getBatchCode());
            batchTaskVO.setBatchName(batchTask.getBatchName());
            batchTaskVO.setBatchNo(batchTask.getBatchNo());
            batchTaskVO.setChangeOrderId(batchTask.getChangeOrderId());
            batchTaskVO.setOperationResultKey(TextValuePairHelper.getValue(batchTask.getOperationResult()));
            batchTaskVO.setActualOperationStartTime(batchTask.getActualOperationStartTime());
            batchTaskVO.setActualOperationEndTime(batchTask.getActualOperationEndTime());
            batchTaskVO.setFactInterruptTime(batchTask.getFactInterruptTime());
            batchTaskVO.setTestFinishTime(batchTask.getTestFinishTime());
            batchTaskVOList.add(batchTaskVO);
        }

        return batchTaskVOList;
    }

    /**
     * cnop 创建网络变更单
     */
    @SneakyThrows
    public static CreateOrderVO createChangeOrder(CnopChangeOrderDto dto, List<MultipartFile> otherAttachments){

        ChangeOrderSave changeOrder = new ChangeOrderSave();
        if (StringUtils.hasText(dto.getCreateBy())) {
            ContextHelper.setEmpNo(dto.getCreateBy());
        }
        // 生成单据编码
        BillNumberGeneratorDTO billNumberGeneratorDTO = new BillNumberGeneratorDTO();
        billNumberGeneratorDTO.setRuleCode(NETWORK_CHANGE_TASK_CODE);
        List<BillNumberGeneratorDTO.Param> params = new ArrayList<>();
        BillNumberGeneratorDTO.Param param = new BillNumberGeneratorDTO.Param();
        param.setCount(1);
        params.add(param);
        billNumberGeneratorDTO.setParams(params);
        List<List<String>> coNos = BillNumberGenerateUtil.generateBatch(billNumberGeneratorDTO);
        if (!CollectionUtils.isEmpty(coNos) && !CollectionUtils.isEmpty(coNos.get(0))) {
            changeOrder.setOrderNo(coNos.get(0).get(0));
        }
        // 查询代表处、产品小类、操作类型和操作原因
        Pair<TreeServiceObjectDto, List<TextValuePair>> orgPair = getOrganizationPair(dto.getDepartmentCode());
        Pair<BasicProductInfo, List<TextValuePair>> productPair = getProductPair(dto.getProductIdPath());
        Pair<LookupValue, Pair<List<TextValuePair>, List<TextValuePair>>> operationPair =
                getOperationTypeAndReasonPair(dto.getOperationType(), dto.getOperationReason());
        // 变更单基本属性赋值
        generateChangeOrderSave(dto, changeOrder, orgPair, productPair, operationPair);

        // 创建变更单和任务
        TransactionHelper.run(() -> {
            String changeOrderId = SaveDataHelper.create(changeOrder);
            changeOrder.setId(changeOrderId);
            // 创建任务
            generateAssignmentTask(changeOrder);
            TechnicalSolutionCheckAbility.saveCheckList(changeOrderId, productPair.getLeft().getIdFullPath(), changeOrder.getOperationTypeGroup());
        });
        // 异步处理附件
        // 直接异步上传MultipartFile类型的文件会找不到文件路径，所以获取文件字节流异步上传
        List<Pair<byte[], String>> fileList = handleOtherAttachments(otherAttachments);
        AsyncExecuteUtils.execute(() -> {
            uploadOrderAttachments(changeOrder.getId(), fileList);
        });

        CreateOrderVO createOrderVO = new CreateOrderVO();
        createOrderVO.setChangeOrderId(changeOrder.getId());
        createOrderVO.setChangeOrderIdDisplay(changeOrder.getOrderNo());
        createOrderVO.setUrl(String.format(ConfigHelper.get(CNOP_CHANGE_ORDER_VIEW_URL), changeOrder.getId(), PAGE_NETWORK_CHANGE_BILL));
        createOrderVO.setChangeStatus(AssignmentStatusEnum.START.getValue());
        createOrderVO.setChangeStatusCn(AssignmentStatusEnum.START.getZhCn());
        createOrderVO.setChangeStatusEn(AssignmentStatusEnum.START.getEnUs());
        return createOrderVO;
    }

    /**
     * 获取 操作类型和操作原因快码值
     *
     * @param typeCode   操作类型快码code
     * @param reasonCode 操作原因快码code
     * @return Pair<操作类型快码实体, Pair<操作类型TextValuePair, 操作原因TextValuePair>>
     */
    private static Pair<LookupValue, Pair<List<TextValuePair>, List<TextValuePair>>> getOperationTypeAndReasonPair(String typeCode, String reasonCode) {
        // 查询操作类型快码值
        List<LookupValue> typeLookupValues = LookupValueHelper.getLookupValuesZhAndUs(OPERATE_TYPE_ENUM, Arrays.asList(typeCode));
        if (CollectionUtils.isEmpty(typeLookupValues)) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(typeCode, OPERATE_TYPE_DATA_NOT_FIND));
        }
        // 查询操作原因快码值
        List<LookupValue> reasonLookupValues = LookupValueHelper.getLookupValuesZhAndUs(OPERATE_REASON_ENUM, Arrays.asList(reasonCode));
        if (CollectionUtils.isEmpty(reasonLookupValues)) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(reasonCode, OPERATE_REASON_DATA_NOT_FIND));
        }
        // 封装TextValuePair数据
        List<TextValuePair> operateType = TextValuePairHelper
                .buildList(typeCode, typeLookupValues.get(0).getMeaningCn(), typeLookupValues.get(0).getMeaningEn());
        List<TextValuePair> operateReason = TextValuePairHelper
                .buildList(reasonCode, reasonLookupValues.get(0).getMeaningCn(), reasonLookupValues.get(0).getMeaningEn());

        return Pair.of(typeLookupValues.get(0), Pair.of(operateType, operateReason));
    }

    /**
     * 获取 组织结构--代表处
     *
     * @param departmentCode 组织编码（具体编码，非编码路径）
     * @return Pair<组织实体，组织TextValuePair>
     */
    private static Pair<TreeServiceObjectDto, List<TextValuePair>> getOrganizationPair(String departmentCode) {

        List<TreeServiceObjectDto> treeServiceObjectDtos = NisAbility.queryOrgnizaList(Lists.newArrayList(departmentCode));

        if (CollectionUtils.isEmpty(treeServiceObjectDtos)) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(departmentCode, DEPARTMENT_DATA_NOT_FIND));
        }
        TreeServiceObjectDto organization = treeServiceObjectDtos.get(0);

        List<TextValuePair> orgTextValuePair = TextValuePairHelper
                .buildList(organization.getCodeFullPath(), organization.getNameFullPathZh(), organization.getNameFullPathEn());
        return Pair.of(organization, orgTextValuePair);
    }

    /**
     * 获取 产品小类
     *
     * @param product 产品小类的idpath（NIS接口提供）
     * @return Pair<产品类型实体，产品类型TextValuePair>
     */
    private static Pair<BasicProductInfo, List<TextValuePair>> getProductPair(String product) {

        // 查询NIS获取产品信息
        String productIdPath = TextValuePairHelper.removeTrailingSlash(product);
        List<BasicProductInfo> productInfos = NisClient.queryProductInfo(Lists.newArrayList(productIdPath));
        // 筛选已启用的产品类型
        productInfos = productInfos.stream().filter(item -> CommonConstants.ENABLED.equals(item.getPStatus())
                        && CommonConstants.ENABLED.equals(item.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productInfos)) {
            throw new LcapBusiException(MsgUtils.getTypedMessage(product, PRODUCT_DATA_NOT_FIND));
        }
        BasicProductInfo productInfo = productInfos.get(0);
        List<TextValuePair> productTextValuePair = TextValuePairHelper
                .buildList(productInfo.getIdFullPath(), productInfo.getNamePathZh(), productInfo.getNamePathEn());

        return Pair.of(productInfo, productTextValuePair);

    }

    private static void generateChangeOrderSave(CnopChangeOrderDto dto,
                                                ChangeOrderSave changeOrder,
                                                Pair<TreeServiceObjectDto, List<TextValuePair>> orgPair,
                                                Pair<BasicProductInfo, List<TextValuePair>> productPair,
                                                Pair<LookupValue, Pair<List<TextValuePair>, List<TextValuePair>>> operationPair) {
        // 网络变更单基本字段赋值
        changeOrder.setSource(DataSourceEnum.CNOP.name());
        changeOrder.setIsGovEnt(BoolEnum.valueOf(dto.getIsGovernmentEnterprise()));
        changeOrder.setResponsibleDept(orgPair.getRight());
        changeOrder.setProductCategory(productPair.getRight());
        changeOrder.setOperationType(operationPair.getRight().getLeft());
        changeOrder.setOperationTypeGroup(operationPair.getLeft().getParentLookupCode());
        changeOrder.setOperationReason(operationPair.getRight().getRight());
        changeOrder.setTimeZone(TimeZoneEnum.historyLookupCode2New(dto.getTimeZone()));
        changeOrder.setDeliveryMode(DeliveryModeEnum.fromValue(String.valueOf(dto.getDeliveryMode())));
        changeOrder.setIsGdpr(BoolEnum.valueOf(dto.getIsGdpr()));
        changeOrder.setIsEmergencyOperation(dto.getIsEmergencyOperation() == null
                ? null
                : BoolEnum.valueOf(dto.getIsEmergencyOperation()));
        changeOrder.setIsFirstApplication(dto.getIsFirstApplication() == null
                ? null
                : BoolEnum.valueOf(dto.getIsFirstApplication()));
        if (dto.getGdprRequire()) {
            changeOrder.setGdprRequire(GgdprRequireEnum.GDPR_REQUIRE.getPropValue());
        }
        // 这里的null是因为需要客户名称，但是CNOP对接参数不含客户名称信息，所以置空
        String namePrefix = String.format("%s_%s_%s_%s", orgPair.getLeft().getNameZh(), null,
                productPair.getLeft().getNameZh(), operationPair.getLeft().getMeaningCn());
        String name = String.format("%s_%s", namePrefix, dto.getOperationSubjectSuffix());
        changeOrder.setOperationSubjectSuffix(dto.getOperationSubjectSuffix());
        changeOrder.setOperationSubjectPrefix(namePrefix);
        changeOrder.setOperationSubject(name);
        changeOrder.setOperationStartTime(dto.getOperationStartTime());
        changeOrder.setOperationEndTime(dto.getOperationEndTime());
    }


    /**
     * 处理附件（获取文件字节流和名称，后续异步上传）
     * @param otherAttachments 附件
     * @return List<Pair<文件字节数组, 文件名称>>
     */
    private static List<Pair<byte[], String>> handleOtherAttachments(List<MultipartFile> otherAttachments) throws IOException {
        if (CollectionUtils.isEmpty(otherAttachments)) {
            return new ArrayList<>();
        }
        List<Pair<byte[], String>> otherAttachmentsNew = new ArrayList<>();
        for (MultipartFile file : otherAttachments) {
            otherAttachmentsNew.add(Pair.of(file.getBytes(), file.getOriginalFilename()));
        }
        return otherAttachmentsNew;
    }

    private static boolean uploadOrderAttachments(String orderId, List<Pair<byte[], String>> otherAttachments){
        if (CollectionUtils.isEmpty(otherAttachments)) {
            return false;
        }
        List<MultiAttachmentFile> attachmentFiles = new ArrayList<>();
        for (Pair<byte[], String> file : otherAttachments) {
            String fileKey = CloudDiskHelper.upload(file.getRight(), file.getLeft());
            MultiAttachmentFile attachmentFile = convertAttachmentFile(fileKey, file.getRight());
            attachmentFiles.add(attachmentFile);
        }
        // 更新变更单的【其他附件】字段
        ChangeOrderSave orderSave = new ChangeOrderSave();
        orderSave.setId(orderId);
        orderSave.setOtherAttachments(attachmentFiles);
        return SaveDataHelper.update(orderSave);
    }

    private static void generateAssignmentTask(ChangeOrderSave changeOrder) {
        String createBy = ContextHelper.getEmpNo();
        List<Employee> responsible = HrClient.queryEmployeeInfo(Lists.newArrayList(createBy));
        // 创建assigment实体
        NetworkChangeAssignment assignment = new NetworkChangeAssignment();
        assignment.setEntityId(changeOrder.getId());
        assignment.setBillType(BillTypeEnum.NETWORK_CHANGE.getPropValue());
        assignment.setApprovalTaskFlag(BoolEnum.N);
        assignment.setAssignmentName(changeOrder.getOperationSubject());
        assignment.setAssignmentCode(changeOrder.getOrderNo());
        assignment.setBillId(changeOrder.getId());
        assignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE.getPropValue());
        assignment.setAssignmentStatus(AssignmentStatusEnum.START.getValue());
        assignment.setResponsibleEmployee(responsible);
        assignment.setPlanStartTime(changeOrder.getOperationStartTime());
        assignment.setTimeZone((String)changeOrder.getTimeZone());
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.getTimeZoneEnum((String)changeOrder.getTimeZone());
        // 如果不是北京时间，需要根据时区转换成北京时间;否则，直接取用户输入的计划操作开始时间即可
        if (timeZoneEnum != null && TimeZoneEnum.BEIJING != timeZoneEnum) {
            assignment.setOperationStartTimeUtc8(
                    TimeZoneEnum.BEIJING.pollute(
                            timeZoneEnum.fix(changeOrder.getOperationStartTime())));
        } else {
            assignment.setOperationStartTimeUtc8(changeOrder.getOperationStartTime());
        }
        assignment.setCurrentProgress(ApproveNodeEnum.DRAFT.name());
        assignment.setCurrentProcessorEmployee(responsible);
        assignment.setProductClassification(TextValuePairHelper.objectTransferList(changeOrder.getProductCategory()));
        assignment.setOperationType(TextValuePairHelper.objectTransferList(changeOrder.getOperationType()));

        // 产品经营团队
        String productIdPath = TextValuePairHelper.getValue(changeOrder.getProductCategory());
        String[] productIds = productIdPath.split(CommonConstants.FORWARD_SLASH);
        assignment.setProductManagementTeam(TextValuePairHelper.buildList(productIds[INTEGER_ZERO], productIds[INTEGER_ZERO], productIds[INTEGER_ZERO]));

        // 代表处
        String organize = TextValuePairHelper.getValue(changeOrder.getResponsibleDept());
        String[] organizes = organize.split(CommonConstants.FORWARD_SLASH);
        assignment.setMarketing(TextValuePairHelper.buildList(organizes[1], organizes[1], organizes[1]));
        assignment.setRepresentativeOffice(TextValuePairHelper.buildList(organizes[INTEGER_THREE], organizes[INTEGER_THREE], organizes[INTEGER_THREE]));
        // 创建任务
        String assigmentId = SaveDataHelper.create(assignment);
        // 创建任务 - 人员关联关系
        AssignmentAbility.createAssignmentPersonRelevance(assigmentId, Arrays.asList(createBy));
    }

    /**
     * 包装附件
     */
    private static MultiAttachmentFile convertAttachmentFile(String fileKey, String filename) {
        MultiAttachmentFile attachmentFile = new MultiAttachmentFile();

        attachmentFile.setDownloadUrl(CommonConstants.FILE_DOWNLOAD_URL_PREFIX + fileKey);
        attachmentFile.setFileKey(fileKey);
        attachmentFile.setId(fileKey);
        attachmentFile.setName(filename);
        attachmentFile.setPercentage(INTEGER_ZERO);
        attachmentFile.setStatus(SUCCESS);

        return attachmentFile;
    }
}
