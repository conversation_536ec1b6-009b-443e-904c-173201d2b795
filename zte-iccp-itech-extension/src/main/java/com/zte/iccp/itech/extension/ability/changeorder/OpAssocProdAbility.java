package com.zte.iccp.itech.extension.ability.changeorder;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.ability.AssociatedProductAbility;
import com.zte.iccp.itech.extension.ability.EmailAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OpAssocProdDevIntApprovalFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.OpAssocProdNetServIntApprovalFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.AssociatedProductAttribute;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.subentity.BaseOpAssocProdConfirmEntity;
import com.zte.iccp.itech.extension.domain.model.subentity.BaseOpAssocProdSubEntity;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdDevIntApproval;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdNetServIntApproval;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecordsDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.APPROVERS;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/05
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OpAssocProdAbility {

    /**
     * 网络变更提单时使用
     *
     * @param changeOrderId 变更单主键
     * @param associatedProductIds 配置表主键
     * @param entityEnumMap 映射map
     */
    public static void save(
            String changeOrderId,
            List<String> associatedProductIds,
            String productIdPath,
            Map<ApproveRoleEnum, Class<? extends BaseSubEntity>> entityEnumMap) {
        // 操作关联产品复选值取至一体化关联产品配置数据，根据组织进行入库
        List<ApproverConfiguration> approverConfigurationList = ApproverAbility.getApproveConfiguration(
                Lists.newArrayList(ApprovalTypeEnum.INTEGRATED_ASSOCIATED_PRODUCT.name()), associatedProductIds, productIdPath);
        if (CollectionUtils.isEmpty(approverConfigurationList)) {
            return;
        }

        // 获取关联产品id数据，获取到实际的基础配置表数据
        List<String> assocProdIds = approverConfigurationList.stream().map(ApproverConfiguration::getAssocProdZh).collect(Collectors.toList());
        List<AssociatedProductAttribute> associatedProductList = AssociatedProductAbility.query(assocProdIds, true);
        if (CollectionUtils.isEmpty(associatedProductList)) {
            return;
        }
        assocProdIds = associatedProductList.stream().map(BaseEntity::getId).collect(Collectors.toList());

        Map<ApproveRoleEnum, List<ApproverConfiguration>> groupedConfigurations = buildGroupedApproveMap(assocProdIds, productIdPath);
        Map<String, AssociatedProductAttribute> productAttributeMap = associatedProductList.stream().collect(Collectors.toMap(BaseEntity::getId, product -> product));
        for (Map.Entry<ApproveRoleEnum, Class<? extends BaseSubEntity>> enumEntry : entityEnumMap.entrySet()) {
            List<ApproverConfiguration> approveConfigurations = groupedConfigurations.get(enumEntry.getKey());
            if (CollectionUtils.isEmpty(approveConfigurations)) {
                continue;
            }
            // 构建List<? extends BaseOpAssocProdSubEntity>进行新增，不破坏原有结构
            save(changeOrderId, buildBaseOpAssocProdSubEntity(enumEntry.getValue(), approveConfigurations, productAttributeMap),
                    enumEntry.getValue());
        }

    }

    /**
     * 构建分组map：key为网络部/研发部，value为配置表数据
     * 说明：对应一体化前置网络服务部或研发审核部初始化数据，需构建和提单时相同的数据量，如不存在则创建新对象。
     *
     * @param assocProdIds 关联产品id
     * @return Map<ApproveRoleEnum, List<ApproverConfiguration>>
     */
    private static Map<ApproveRoleEnum, List<ApproverConfiguration>> buildGroupedApproveMap(List<String> assocProdIds, String productIdPath) {
        // 网服部的数据
        List<ApproverConfiguration> networkDeptList = ApproverAbility.query(ApproveRoleEnum.NETWORK_DEPT, assocProdIds, productIdPath);
        // 研发部的数据
        List<ApproverConfiguration> rdDepList = ApproverAbility.query(ApproveRoleEnum.R_AND_D_DEP, assocProdIds, productIdPath);
        // 取一条数据作为模板构建新对象
        if (CollectionUtils.isEmpty(networkDeptList) && CollectionUtils.isEmpty(rdDepList)) {
            return Collections.emptyMap();
        }

        ApproverConfiguration approverConfiguration;
        if (!CollectionUtils.isEmpty(networkDeptList)) {
            approverConfiguration = networkDeptList.get(CommonConstants.INTEGER_ZERO);
        } else {
            approverConfiguration = rdDepList.get(CommonConstants.INTEGER_ZERO);
        }

        approverConfiguration.clearEntityValue();
        Map<ApproveRoleEnum, List<ApproverConfiguration>> groupedConfigurations = MapUtils.newHashMap();
        groupedConfigurations.put(ApproveRoleEnum.NETWORK_DEPT,
                buildGroupedApproveList(ApproveRoleEnum.NETWORK_DEPT, assocProdIds, networkDeptList, approverConfiguration));
        groupedConfigurations.put(ApproveRoleEnum.R_AND_D_DEP,
                buildGroupedApproveList(ApproveRoleEnum.R_AND_D_DEP, assocProdIds, rdDepList, approverConfiguration));
        return groupedConfigurations;
    }

    /**
     * 构建分组数据
     *
     * @param approveRoleEnum 审核角色枚举：网服部、研发部
     * @param assocProdIds 操作关联产品ids
     * @param approverConfigurationList 网服部或研发部数据
     * @param templateConfig 模板（构建新对象使用）
     * @return Map<ApproveRoleEnum, List<ApproverConfiguration>>
     */
    private static List<ApproverConfiguration> buildGroupedApproveList(ApproveRoleEnum approveRoleEnum,
                                                                                            List<String> assocProdIds,
                                                                                            List<ApproverConfiguration> approverConfigurationList,
                                                                                            ApproverConfiguration templateConfig) {
        List<ApproverConfiguration> networkDept = new ArrayList<>(assocProdIds.size());
        for (String prodId : assocProdIds) {
            ApproverConfiguration config = approverConfigurationList.stream()
                    .filter(c -> c.getAssocProdZh().equals(prodId))
                    .findFirst()
                    .orElseGet(() -> createNewApproverConfiguration(prodId, templateConfig,approveRoleEnum));
            networkDept.add(config);
        }
        return networkDept;
    }


    /**
     * 创建新的ApproverConfiguration对象
     *
     * @param prodId prodId
     * @return ApproverConfiguration
     */
    private static ApproverConfiguration createNewApproverConfiguration(String prodId,
                                                                        ApproverConfiguration approverConfig,
                                                                        ApproveRoleEnum approveRoleEnum) {
        ApproverConfiguration newApproverConfig = new ApproverConfiguration();
        BeanUtils.copyProperties(approverConfig, newApproverConfig);
        // id后续重新生成
        newApproverConfig.setId(null);
        // 审核人为空
        newApproverConfig.setApproverPersons(null);
        // 审核组为空
        newApproverConfig.setApproverGroups(null);
        // 关联操作产品中英文存储的id
        newApproverConfig.setAssocProdZh(prodId);
        newApproverConfig.setAssocProdEn(prodId);
        // 组织角色
        newApproverConfig.setSales(approveRoleEnum.name());
        return newApproverConfig;
    }

    @SneakyThrows
    private static List<BaseOpAssocProdSubEntity> buildBaseOpAssocProdSubEntity(Class<? extends BaseSubEntity> subEntityEnum,
                                                                                List<ApproverConfiguration> approveConfigurations,
                                                                                Map<String, AssociatedProductAttribute> productAttributeMap) {
        List<BaseOpAssocProdSubEntity> assocProdSubEntityList = new ArrayList<>();
        for (ApproverConfiguration approveConfiguration : approveConfigurations) {
            BaseOpAssocProdConfirmEntity baseOpAssocProd = (BaseOpAssocProdConfirmEntity)
                    subEntityEnum.newInstance();
            BeanUtils.copyProperties(approveConfiguration, baseOpAssocProd);
            AssociatedProductAttribute productAttribute = productAttributeMap.get(approveConfiguration.getAssocProdZh());
            baseOpAssocProd.setAssocProdNameZh(productAttribute.getAssocProdZh());
            baseOpAssocProd.setAssocProdNameEn(productAttribute.getAssocProdEn());
            baseOpAssocProd.setProductType(approveConfiguration.getProdLine());
            if (!CollectionUtils.isEmpty(approveConfiguration.getApproverPersons())) {
                baseOpAssocProd.setApprover(SingleEmployee.from(approveConfiguration.getApproverPersons().get(CommonConstants.INTEGER_ZERO)));
            }

            if (!CollectionUtils.isEmpty(approveConfiguration.getApproverGroups())) {
                baseOpAssocProd.setApprovalTeam(approveConfiguration.getApproverGroups());
            }

            assocProdSubEntityList.add(baseOpAssocProd);
        }
        return assocProdSubEntityList;
    }

    public static void save(
            String changeOrderId,
            List<? extends BaseOpAssocProdSubEntity> srcOpAssocProds,
            Class<? extends BaseSubEntity> subEntity) {
        List<? extends BaseOpAssocProdSubEntity> exists = QueryDataHelper
                .query(subEntity, Lists.newArrayList(), changeOrderId);

        // 不管是草稿状态数据还是驳回的，直接先删原来的，然后新增新的数据
        List<String> toDelete = exists.stream().map(BaseEntity::getId).collect(Collectors.toList());
        List<BaseOpAssocProdSubEntity> toCreate = getToCreateData(
                changeOrderId, srcOpAssocProds, subEntity);

        SaveDataHelper.batchDelete(subEntity, toDelete);
        SaveDataHelper.batchCreate(toCreate);
    }

    @SneakyThrows
    private static @NotNull List<BaseOpAssocProdSubEntity> getToCreateData(
            String changeOrderId,
            List<? extends BaseOpAssocProdSubEntity> srcOpAssocProds,
            Class<? extends BaseSubEntity> subEntity) {
        List<BaseOpAssocProdSubEntity> toCreate = Lists.newArrayList();
        for (BaseOpAssocProdSubEntity opAssocProd : srcOpAssocProds) {
            BaseOpAssocProdSubEntity baseOpAssocProd = (BaseOpAssocProdSubEntity)
                    subEntity.newInstance();
            BeanUtils.copyProperties(opAssocProd, baseOpAssocProd);
            baseOpAssocProd.clearEntityValue();
            baseOpAssocProd.setId(null);
            toCreate.add(baseOpAssocProd);

            baseOpAssocProd.setPid(changeOrderId);
        }
        return toCreate;
    }


    /**
     *  同一个产品小类下，关联产品的中文名称不会相同，以关联产品的中文名称作为key，审核人+审核组 = 处理人 作为value
     *
     * @param opAssocProdConfirmEntityList opAssocProdConfirmEntityList
     * @return Map<String, Pair<Set<String>, String>>
     */
    public static Map<String, Set<String>> assembleMap(List<BaseOpAssocProdConfirmEntity> opAssocProdConfirmEntityList) {
        if (CollectionUtils.isEmpty(opAssocProdConfirmEntityList)) {
            return Maps.newHashMap();
        }
         return opAssocProdConfirmEntityList.stream()
                .collect(Collectors.toMap(BaseOpAssocProdConfirmEntity::getAssocProdNameZh, opAssocProdConfirm -> {
                    Set<String> values = Sets.newHashSet();
                    SingleEmployee employee = opAssocProdConfirm.getApprover();
                    if (employee == null) {
                        return values;
                    }
                    String approvalEmpUIID = employee.getEmpUIID();
                    if (org.springframework.util.StringUtils.hasText(approvalEmpUIID)) {
                        values.add(approvalEmpUIID);
                    }
                    List<Employee> employees = opAssocProdConfirm.getApprovalTeam();
                    if (!CollectionUtils.isEmpty(employees)) {
                        employees.stream().map(Employee::getEmpUIID).forEach(values::add);
                    }
                    return values;
                }));
    }


    /**
     * 一体化审批节点后置提交操作
     *
     * @param changeOrderId 变更单id
     * @param taskId 流程任务id
     * @param subEntityEnum1 一体化实体
     * @param subEntityEnum2 前置实体，如网络服务部审核、研发经理审核
     */
    public static void integrationAfterOperate(
            String changeOrderId,
            String taskId,
            Class<? extends BaseSubEntity> subEntityEnum1,
            Class<? extends BaseSubEntity> subEntityEnum2) {
        List<BaseOpAssocProdSubEntity> opAssocProdSubEntityList = QueryDataHelper.query(subEntityEnum1,
                Lists.newArrayList(), changeOrderId);
        if (CollectionUtils.isEmpty(opAssocProdSubEntityList)) {
            return;
        }
        List<ApproveRecord> approvedRecords = FlowServiceHelper
                .getFlowApproveRecords(new ApproveRecordsDTO() {{
                    setTenantId(ContextHelper.getTenantId());
                    setAppId(ContextHelper.getAppId());
                    setBizInstanceId(changeOrderId);
                }});

        ApproveRecord approveRecord = approvedRecords.stream()
                .filter(item -> item.getTaskId() != null && taskId.equals(item.getTaskId()))
                .findFirst()
                .orElse(null);

        if (approveRecord == null) {
            return;
        }
        // 存储的网络服务部审核、研发经理审核的id
        JSONObject jsonObject = JSONObject.parseObject(approveRecord.getApproverTypeValue());
        String upAssociationId = (String) jsonObject.get(APPROVERS);

        List<BaseOpAssocProdConfirmEntity> assocProdConfirmEntityList = QueryDataHelper.query(subEntityEnum2,
                Lists.newArrayList(), changeOrderId);
        Map<String, String> assocProdNameMap = assocProdConfirmEntityList.stream().collect(Collectors.toMap(BaseOpAssocProdSubEntity::getAssocProdNameZh, BaseEntity::getId));

        ChangeOrder changeOrder = QueryDataHelper.get(ChangeOrder.class,
                Lists.newArrayList(ID, ORDER_NO, OPERATION_SUBJECT, IS_EMERGENCY_OPERATION), changeOrderId);

        List<Map<String, Object>> multiValues = new ArrayList<>();
        for (BaseOpAssocProdSubEntity opAssocProdSubEntity : opAssocProdSubEntityList) {
            String operationAssociationProdNetId = assocProdNameMap.get(opAssocProdSubEntity.getAssocProdNameZh());
            if (upAssociationId.equals(operationAssociationProdNetId)) {
                Map<String, Object> updateMap = MapUtils.newHashMap(ID, opAssocProdSubEntity.getId());
                // 1.网络服务部一体化邮件和数据更新
                if (subEntityEnum1 == OpAssocProdNetServIntApproval.class) {
                    // 网络服务部一体化审核人、审核日期
                    updateMap.put(OpAssocProdNetServIntApprovalFieldConsts.APPROVER, EmployeeHelper.getEmployeeJSONArray(approveRecord.getApprover()));
                    updateMap.put(OpAssocProdNetServIntApprovalFieldConsts.APPROVED_DATE, approveRecord.getApprovalDate());
                    // 邮件知会
                    OpAssocProdNetServIntApproval opAssocProdNetServIntApproval = (OpAssocProdNetServIntApproval) opAssocProdSubEntity;
                    sendEmail(changeOrder, opAssocProdNetServIntApproval.getEmailCc(), approveRecord.getApprover(),
                            opAssocProdNetServIntApproval.getResult());

                    // 2.研发经理一体化邮件和数据更新
                } else if (subEntityEnum1 == OpAssocProdDevIntApproval.class) {
                    // 研发一体化审核人、审核日期
                    updateMap.put(OpAssocProdDevIntApprovalFieldConsts.APPROVER, EmployeeHelper.getEmployeeJSONArray(approveRecord.getApprover()));
                    updateMap.put(OpAssocProdDevIntApprovalFieldConsts.APPROVED_DATE, approveRecord.getApprovalDate());

                    //邮件知会
                    OpAssocProdDevIntApproval opAssocProdDevIntApproval = (OpAssocProdDevIntApproval) opAssocProdSubEntity;
                    sendEmail(changeOrder, opAssocProdDevIntApproval.getEmailCc(), approveRecord.getApprover(),
                            opAssocProdDevIntApproval.getResult());
                }
                multiValues.add(updateMap);
            }
        }
        SaveDataHelper.batchUpdate(subEntityEnum1, multiValues);
    }

    /**
     * 发送邮件
     *
     * @param changeOrder 变更单
     * @param emailCc 收件人
     * @param approver 审批人
     */
    private static void sendEmail(ChangeOrder changeOrder,
                                  List<Employee> emailCc,
                                  String approver,
                                  ApproveResultEnum approveResultEnum) {
        if (CollectionUtils.isEmpty(emailCc)) {
            return;
        }
        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(approver);
        String msg = ApproveResultEnum.PASS == approveResultEnum ? MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_PASS_CONTENT
                : MessageConsts.EmailNotice.MESSAGE_CHANGE_APPROVAL_REJECT_CONTENT;
        // 消息正文（中文）
        Map<String, Object> data = EmailAbility.buildMessageZhContent(msg,
                employeeFormatNames.getZhCN(), changeOrder.getOrderNo(), changeOrder.getOperationSubject(), changeOrder.isEmergencyOperationExt(ZH_CN));
        // 消息正文（英文）
        data.putAll(EmailAbility.buildMessageEnContent(msg, employeeFormatNames.getEnUS(), changeOrder.getOrderNo(), changeOrder.getOperationSubject(),
                changeOrder.isEmergencyOperationExt(EN_US)));

        // 【$网络变更操作知会】{操作者}审核通过{单据编号}/{操作主题}{是否紧急}
        EmailAbility.sendApproResultInformMail(changeOrder.getId(), data, ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW.name(),
                EmployeeHelper.getEpmUIID(emailCc), true);
    }


    /**
     * 一体化审批节点，查询活跃关联产品id和审批人映射map
     *
     * @param businessId 业务id
     * @param approveNodeEnum 当前兼容网服部一体化关联产品审核和研发一体化关联产品审核节点
     * @return 上节点关联产品id作为key，审核人集作为value
     */
    public static Map<String, List<String>> getActiveAssocProdIdToApproverMap(String businessId, ApproveNodeEnum approveNodeEnum) {
        if (ApproveNodeEnum.NET_INTEGRATION != approveNodeEnum
                && ApproveNodeEnum.RD_INTEGRATION != approveNodeEnum) {
            return Collections.emptyMap();
        }

        List<ApproveRecord> approveRecordList = FlowHelper.getAllApprovedRecords(businessId);
        // 已经完成的直接从页面取审核人即可
        return approveRecordList.stream()
                .filter(approveRecord -> ACTIVE_TASK_STATUS.equals(approveRecord.getTaskStatus())
                        //  根据approveNodeEnum过滤
                        && approveNodeEnum.name().equals(approveRecord.getExtendedCode()))
                .collect(Collectors.groupingBy(
                        // 提取上节点关联产品的id作为分组键
                        approveRecord -> {
                            JSONObject jsonObject = JSONObject.parseObject(approveRecord.getApproverTypeValue());
                            return (String) jsonObject.get(APPROVERS);
                        },
                        // 审批人集作为value
                        Collectors.mapping(ApproveRecord::getApprover, Collectors.toList())
                ));

    }
}