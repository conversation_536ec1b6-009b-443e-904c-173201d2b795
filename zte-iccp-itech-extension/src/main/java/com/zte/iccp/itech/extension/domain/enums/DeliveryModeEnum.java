package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 交付方式枚举
 *
 * <AUTHOR> 10335201
 * @date 2024-07-11 下午7:37
 **/
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum DeliveryModeEnum implements SingletonTextValuePairsProvider {

    /**远程交付*/
    REMOTE_DELIVERY("1","远程交付","Remote Delivery"),

    /**本地交付*/
    LOCAL_DELIVERY("2","本地交付","Local Delivery"),
    ;

    /**
     * 交付方式值
     */
    private final String value;
    /**
     * 交付方式中文描述
     */
    private final String zhCn;
    /**
     * 交付方式英文描述
     */
    private final String enUs;

    public static DeliveryModeEnum fromValue(String str){
        return Arrays.stream(DeliveryModeEnum.values())
                .filter(item -> item.value.equals(str))
                .findFirst()
                .orElse(null);
    }

}
