package com.zte.iccp.itech.extension.ability.configuration;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.configuration.UserGroupFieldConsts;
import com.zte.iccp.itech.extension.domain.model.entity.configuration.UserGroup;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import org.springframework.util.StringUtils;

import java.util.List;

public class UserGroupAbility {

    /**
     * 构建列表查询条件
     */
    public static List<IFilter> getTableQueryConditions() {
        return Lists.newArrayList(
                new Filter(CommonFieldConsts.CREATE_BY, Comparator.EQ, ContextHelper.getEmpNo()));
    }

    /**
     * 检索用户群组 - 主键
     */
    public static UserGroup get(String id, List<String> fields) {
        if (!StringUtils.hasText(id)) {
            return null;
        }

        return QueryDataHelper.get(UserGroup.class, fields, id);
    }

    /**
     * 检索用户建立重名群组
     */
    public static UserGroup getPersonalSameNameGroup(String groupName) {
        if (!StringUtils.hasText(groupName)) {
            return null;
        }

        return QueryDataHelper.queryOne(
                UserGroup.class,
                Lists.newArrayList(CommonFieldConsts.ID),
                Lists.newArrayList(
                        new Filter(UserGroupFieldConsts.GROUP_NAME, Comparator.EQ, groupName),
                        new Filter(CommonFieldConsts.CREATE_BY, Comparator.EQ, ContextHelper.getEmpNo())));
    }

    /**
     * 删除用户群组
     */
    public static void delete(List<String> groupIds) {
        SaveDataHelper.batchDelete(UserGroup.class, groupIds);
    }
}
