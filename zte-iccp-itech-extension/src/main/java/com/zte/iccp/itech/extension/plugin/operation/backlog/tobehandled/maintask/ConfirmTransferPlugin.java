package com.zte.iccp.itech.extension.plugin.operation.backlog.tobehandled.maintask;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.assignment.TransferAbility;
import com.zte.iccp.itech.extension.ability.clockin.reviews.ClockInReviewsAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.OrganizationApproverAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.PermissionApplication;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.subentity.OrganizationApprover;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iss.approval.sdk.bean.ApprovalTaskDTO;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.flow.dto.FlowInfo;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.common.utils.CommonUtils.extractNumbers;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.CURRENT_PROCESSOR_EMPLOYEE_FIELD;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.clockin.ClockInReviewsFieldConsts.CURRENT_PROCESSOR;

/**
 * 触发时机：点击待办任务--待我处理--汇总任务--转交/批量转交弹出页--点击【确认】按钮
 * 插件逻辑：转交当前处理人，刷新表格数据
 * <AUTHOR> 10335201
 * @date 2024-12-18 上午10:17
 **/
public class ConfirmTransferPlugin extends BaseOperationPlugin {

    /**
     * 转交/批量转交任务 - 确认
     */
    @Override
    public void beforeExecute(ExecuteEvent executeEvent) {
        IFormView formView = getView();
        IDataModel dataModel = getModel();

        // 1.获取任务ID
        Map<String, Object> customParameters = formView.getFormShowParameter().getCustomParameters();
        List<String> assignmentIds = JSONArray.parseArray(
                JSON.toJSONString(customParameters.get(CommonConstants.CUSTOM_PARAME_ASSIGNMENT_ID)),
                String.class);

        // 2.转交当前处理人
        Map<String, List<String>> errorInfo = transferCurrentProcessors(dataModel, assignmentIds);

        // 3.异常信息展示
        showErrorMessage(formView, errorInfo);

        // 4.刷新主页面
        IFormView parentView = formView.getParentView();
        parentView.getClientViewProxy().refreshData(
                parentView.getPageSessionId(), CidConstants.TABLE_ASSIGNMENT_CID, parentView.getPageId());
        formView.getClientViewProxy().sendParentViewCmd(parentView.getClientViewProxy());
    }

    /**
     * 确认转交当前处理人，根据不同任务类型分别处理
     * key - msgId   value - 异常任务编码
     */
    public static Map<String, List<String>> transferCurrentProcessors(
            IDataModel dataModel,
            List<String> assignmentIds) {

        // 1.检索新处理人
        List<Employee> newCurrentProcessor = ComponentUtils.getEmployeeComponentInfo(
                dataModel, CidConstants.RESPONSIBILITY_TRANSFERENCE_PERSON_CID);
        if (CollectionUtils.isEmpty(newCurrentProcessor)) {
            return new HashMap<>();
        }

        // 2.检索任务数据
        List<String> fields = Lists.newArrayList(
                ID, BILL_ID, ENTITY_ID, ASSIGNMENT_CODE, ASSIGNMENT_TYPE, ASSIGNMENT_STATUS,
                CURRENT_PROGRESS, CURRENT_PROCESSOR_EMPLOYEE_FIELD);
        List<Assignment> assignments = AssignmentAbility.get(assignmentIds, fields, Assignment.class);
        if (CollectionUtils.isEmpty(assignments)) {
            return new HashMap<>();
        }

        // 3.根据任务类型进行转交
        AssignmentTypeEnum firstAssignmentType
                = AssignmentTypeEnum.fromTextValuePair(assignments.get(0).getAssignmentType());
        if (Objects.isNull(firstAssignmentType)) {
            return new HashMap<>();
        }

        // todo 后续需要把其它任务类型也改成批量处理的形式，替换掉 for 循环里的 switch
        // 当前暂时只处理网络变更类型的任务
        switch (firstAssignmentType) {
            case NETWORK_CHANGE:
            case SUBCONTRACTOR_NETWORK_CHANGE:
            case NETWORK_CHANGE_BATCH:
            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                return networkChangeTransfer(dataModel, assignments, newCurrentProcessor);

            default:
                break;
        }

        for (Assignment assignment : assignments){
            AssignmentTypeEnum assignmentType
                    = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
            if (Objects.isNull(assignmentType)) {
                continue;
            }

            switch (assignmentType) {
                case TECHNOLOGY_MANAGEMENT:
                case FAULT_MANAGEMENT:
                case OPERATION_PLAN_TASK:
                    transferCurrentProcessor(assignment, newCurrentProcessor);
                    break;

                case TECHNOLOGY_MANAGEMENT_SUB:
                    subTransferConfirm(assignment, newCurrentProcessor);
                    break;

                case PERMISSION_APPLICATION:
                    transferCurrentProcessor(assignment, newCurrentProcessor);
                    permissionEntityConfirm(assignment.getEntityId(), newCurrentProcessor);
                    break;

                case CLOCK_REVIEW:
                    transferCurrentProcessor(assignment, newCurrentProcessor);
                    clockReviewEntityConfirm(assignment.getEntityId(), newCurrentProcessor);
                    break;

                default:
                    break;
            }
        }

        return new HashMap<>();
    }

    /**
     * 转交网络变更相关任务
     */
    private static Map<String, List<String>> networkChangeTransfer(
            IDataModel dataModel,
            List<Assignment> assignments,
            List<Employee> newCurrentProcessor) {

        // 1.区分 网络变更任务 / 批次任务
        List<Assignment> networkChangeAssignments = assignments.stream()
                .filter(item -> {
                    AssignmentTypeEnum assignmentType
                            = AssignmentTypeEnum.fromTextValuePair(item.getAssignmentType());
                    return AssignmentTypeEnum.NETWORK_CHANGE.equals(assignmentType)
                            || AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(assignmentType);
                }).collect(Collectors.toList());
        assignments.removeAll(networkChangeAssignments);

        // 2.获取 继续保留用户 标识
        boolean reserveFlag = !CollectionUtils.isEmpty(
                ComponentUtils.getChooseComponentInfo(dataModel, CidConstants.REQUIRE_REVERSE));

        // 3.转交 网络变更任务 + 批次任务
        String newCurrentProcessorId = newCurrentProcessor.get(0).getEmpUIID();
        Map<String, List<String>> networkChangeErrorInfo = TransferAbility.networkChangeTransferCurrentProcessors(
                networkChangeAssignments, newCurrentProcessorId, reserveFlag);
        Map<String, List<String>> batchErrorInfo = TransferAbility.batchTransferCurrentProcessors(
                assignments, newCurrentProcessorId, reserveFlag);

        return Stream.of(networkChangeErrorInfo, batchErrorInfo)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (list1, list2) -> {
                            List<String> merged = new ArrayList<>(list1);
                            merged.addAll(list2);
                            return merged;
                        }
                ));
    }

    /**
     * 技术管理任务 子任务 转交
     */
    private static void subTransferConfirm(Assignment assignment, List<Employee> transferCurrentProcessors) {
        String billId = assignment.getBillId();
        Employee transferCurrentProcessor = transferCurrentProcessors.get(0);
        String transferId = transferCurrentProcessor.getEmpUIID();

        List<Employee> currentProcessors = assignment.getCurrentProcessorEmployee();
        List<String> currentProcessorIds = EmployeeHelper.getEpmUIID(currentProcessors);

        // 1.更新技术管理子任务审批流当前节点的处理人
        List<TechnologyManagementAssignment> subTechnologyManagementAssignments
                = AssignmentAbility.queryExistSubAssignment(Lists.newArrayList(assignment.getId()));
        if (!CollectionUtils.isEmpty(subTechnologyManagementAssignments)
                && !currentProcessorIds.contains(transferId)){
            String bizId = subTechnologyManagementAssignments.get(0).getApproveSubTaskId();
            FlowInfo flowInfo = FlowHelper.getFlowInfo(bizId);
            if (Objects.nonNull(flowInfo)) {
                FlowHelper.reassignSystemNode(bizId, transferId, ContextHelper.getEmpNo());
            }
        }

        // 3.更新子任务的当前处理人，把原来的【当前处理人】和本次转交的【当前处理人】合并后再去掉属于当前登录用户
        updateCurrentProcessors(assignment, transferCurrentProcessor, currentProcessors);

        // 4.新增主任务当前处理人 - 任务关联关系
        TechnologyManagementAssignment mainTechnologyManagementAssignment = AssignmentAbility.querySpecificTypeAssignment(
                billId,
                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT,
                TechnologyManagementAssignment.class);
        if(mainTechnologyManagementAssignment!=null){
            String mainAssignmentId = mainTechnologyManagementAssignment.getId();
            AssignmentAbility.createAssignmentPersonRelevance(mainAssignmentId, Lists.newArrayList(transferId));
        }
    }

    /**
     * 转交功能业务逻辑 -- 适用于技术管理任务、故障管理任务、权限申请任务、异常复盘任务、操作计划任务
     */
    private static void transferCurrentProcessor(
            Assignment assignment,
            List<Employee> transferCurrentProcessors) {

        Employee transferCurrentProcessor = transferCurrentProcessors.get(0);
        String transferId = transferCurrentProcessor.getEmpUIID();

        // 1.校验包含本次的转交人
        List<Employee> currentProcessors = assignment.getCurrentProcessorEmployee();
        Employee transferUser = currentProcessors.stream()
                .filter(item -> transferId.equals(item.getEmpUIID()))
                .findFirst()
                .orElse(null);

        // 2.更新审批流当前节点的处理人
        FlowInfo flowInfo = FlowHelper.getFlowInfo(assignment.getBillId());
        if (Objects.nonNull(flowInfo) && Objects.isNull(transferUser)) {
            FlowHelper.reassignSystemNode(
                    assignment.getBillId(), transferId, ContextHelper.getEmpNo());
        }

        // 3.更新当前处理人，把原来的【当前处理人】和本次转交的【当前处理人】合并后再去掉属于当前登录用户
        updateCurrentProcessors(assignment, transferCurrentProcessor, currentProcessors);

        // 4.新增当前处理人 - 任务关联关系
        AssignmentAbility.createAssignmentPersonRelevance(assignment.getId(), Lists.newArrayList(transferId));
    }

    /**
     * 权限申请任务 实体同步转交人
     */
    private static void permissionEntityConfirm(
            String applicationId,
            List<Employee> transferCurrentProcessors) {

        // 1.当前处理人转换
        Employee transfer = transferCurrentProcessors.get(0);
        String transferId = transfer.getEmpUIID();
        String transferNameZh = transfer.getEmpNameCn() + transferId;
        String transferNameEn = transfer.getEmpNameEn() + transferId;
        List<TextValuePair> transferUserInfo = TextValuePairHelper.buildList(
                LangUtils.get(transferNameZh, transferNameEn), transferNameZh, transferNameEn);

        // 2.检索 权限申请 实体
        PermissionApplication application = PermissionApplicationAbility.get(applicationId);
        if (Objects.isNull(application)) {
            return;
        }

        // 3.获取当前进展
        List<ApprovalTaskDTO> approveTasks
                = FlowHelper.batchQueryActiveNodes(Lists.newArrayList(applicationId));
        List<String> currentProgresses = approveTasks.stream()
                .map(ApprovalTaskDTO::getNodeId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentProgresses)) {
            return;
        }
        String currentProgress = currentProgresses.get(0);

        // 4.根据当前进展，更新实体处理人
        // (1) 会签节点，更新 代表处审批人 子实体数据
        if (ApprovalConstants.PA_REPRESENTATIVE_OFFICE_APPROVAL_NODE_ID.equals(currentProgress)) {
            // 检索 代表处审批人 数据
            List<OrganizationApprover> approvers
                    = OrganizationApproverAbility.query(Lists.newArrayList(applicationId));
            OrganizationApprover currentApprover = approvers.stream()
                    .filter(item -> ContextHelper.getEmpNo().equals(getFirstValue(item.getApprover())))
                    .findAny()
                    .orElse(null);
            if (Objects.isNull(currentApprover)) {
                return;
            }

            // 包装更新对象
            OrganizationApprover updateApprover = new OrganizationApprover();
            updateApprover.setPid(applicationId);
            updateApprover.setId(currentApprover.getId());
            updateApprover.setApprover(transferUserInfo);

            // 更新 代表处审批人
            OrganizationApproverAbility.update(updateApprover);

            return;
        }

        PermissionApplication updateApplication = new PermissionApplication();
        updateApplication.setId(applicationId);

        if (ApprovalConstants.DIRECT_MANAGEMENT_LEADER_APPROVAL_NODE.equals(currentProgress)) {
            // (2) 直属领导审核
            updateApplication.setDirectManagementLeader(transferUserInfo);
        } else if (ApprovalConstants.PRODUCT_TECHNOLOGY_SECTION_CHIEF_APPROVAL_NODE.equals(currentProgress)) {
            // (3) 产品技术科长审核
            updateApplication.setProductTechnologySectionChief(transferUserInfo);
        } else {
            // (4) 代表处群经理
            updateApplication.setRepresentativeOfficeGroupManager(transferUserInfo);
        }

        PermissionApplicationAbility.update(updateApplication);
    }

    /**
     * 打卡复盘任务 实体同步转交人
     */
    private static void clockReviewEntityConfirm(
            String reviewId,
            List<Employee> transferCurrentProcessors) {

        // 1.检索 权限申请 实体
        ClockInReviews clockInReview = ClockInReviewsAbility.get(
                reviewId, Lists.newArrayList(ID, CURRENT_PROCESSOR));
        if (Objects.isNull(clockInReview)) {
            return;
        }

        // 2.封装当前处理人
        transferCurrentProcessors.addAll(clockInReview.getCurrentProcessor());
        transferCurrentProcessors.removeIf(employee -> ContextHelper.getEmpNo().equals(employee.getEmpUIID()));

        // 3.更新当前处理人
        ClockInReviews updateReview = new ClockInReviews();
        updateReview.setId(clockInReview.getId());
        updateReview.setCurrentProcessor(transferCurrentProcessors);

        ClockInReviewsAbility.update(updateReview);
    }

    private static void updateCurrentProcessors(
            Assignment assignment,
            Employee transferCurrentProcessor,
            List<Employee> currentProcessors) {

        // 由于后续还要继续使用 transferCurrentProcessors
        // 避免引用传递，单独建 updateCurrentProcessors
        currentProcessors = currentProcessors.stream()
                .filter(item -> !ContextHelper.getEmpNo().equals(item.getEmpUIID())
                        && !transferCurrentProcessor.getEmpUIID().equals(item.getEmpUIID()))
                .collect(Collectors.toList());
        List<Employee> updateCurrentProcessors = Lists.newArrayList();
        updateCurrentProcessors.add(transferCurrentProcessor);
        updateCurrentProcessors.addAll(currentProcessors);

        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setCurrentProcessorEmployee(updateCurrentProcessors);
        AssignmentAbility.update(updateAssignment);
    }

    private static String getFirstValue(List<TextValuePair> items) {
        return CollectionUtils.isEmpty(items) ? null : extractNumbers(items.get(0).getValue());
    }

    /**
     * 展示异常信息
     */
    private static void showErrorMessage(
            IFormView formView,
            Map<String, List<String>> errorInfo) {

        for (Map.Entry<String, List<String>> entry : errorInfo.entrySet()) {
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                formView.showMessage(MsgUtils.getMessage(entry.getKey(), entry.getValue()), MsgType.WARNING);
            }
        }
    }
}
