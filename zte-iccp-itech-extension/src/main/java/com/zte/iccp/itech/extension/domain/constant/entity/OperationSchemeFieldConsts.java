package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/1 下午4:46
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OperationSchemeFieldConsts {

    public static final String SCHEME_CODE = "scheme_code";

    public static final String PRODUCT_TYPE = "product_type";

    public static final String OPERATION_TYPE = "operation_type";

    public static final String COUNTRY = "country";

    public static final String SCHEME_ATTACHMENT = "scheme_attachment";

    public static final String SCHEME_TYPE = "scheme_type";

    public static final String BILL_ID = "bill_id";

    public static final String BILL_TYPE = "bill_type";

    public static final String SCHEME_STATUS = "scheme_status";

    public static final String SCHEME_NAME = "scheme_name";

    public static final String PRODUCT_CLASS = "product_class";

    public static final String VERSION_NO = "version_no";

}
