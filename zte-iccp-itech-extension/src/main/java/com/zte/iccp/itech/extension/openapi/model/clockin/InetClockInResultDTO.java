package com.zte.iccp.itech.extension.openapi.model.clockin;

import lombok.Getter;
import lombok.Setter;


/**
 * iNet打卡信息查询入参
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/11/28
 */
@Getter
@Setter
public class InetClockInResultDTO{
    /** 主键id */
    private String clockInId;

    /** 打卡单名称（取操作主题） */
    private String clockInName;

    /** 关联操作单Id */
    private String changeOrderId;

    /** 关联变更操作单code */
    private String coNo;

    /** 批次号 */
    private String batchNo;

    /** 确认操作开始时间 */
    private String planOperationStartTime;

    /** 确认操作结束时间 */
    private String planOperationEndTime;

    /** 营销 */
    private String sales;

    /** 片区 */
    private String organizationRegion;

    /** 代表处 */
    private String responsibleDeptId;

    /** 代表处id */
    private String organizationId;

    /** 代表处名称 */
    private String organizationName;

    /** 产品分类id */
    private String productId;

    /** 产品分类名称 */
    private String productName;

    /** 操作类型 */
    private String operationType;

    /** 客户标识（三营单据等同于运营商；从申请单取） */
    private String accnType;

    /** 风险等级 */
    private String riskEvaluation;

    /** 操作等级 */
    private String operationLevel;

    /** 网络id */
    private String networkId;

    /** 操作对象 - 当前批次客户网络名称 */
    private String networkName;

    /** 打卡记录id */
    private String clockInRecordId;

    /** 打卡项 */
    private String clockInOption;

    /** 打卡角色 */
    private String operatorRoles;

    /** 打卡时间 */
    private String clockInTime;

    /** 打卡人 */
    private String clockInBy;

    /** 打卡记录状态 - 是否已撤销 */
    private String isRevoked;

    /** 打卡任务创建时间 */
    private String createTime;

    /** 打卡任务更新时间 */
    private String lastModifiedTime;
}
