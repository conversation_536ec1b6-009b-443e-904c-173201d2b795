package com.zte.iccp.itech.extension.domain.enums.common.table;

import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.PageConstants;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum DataKeyEnum {

    /** 网络变更任务 - 任务中心 */
    NETWORK_CHANGE_ASSIGNMENT(PageConstants.PAGE_WEB_NETWORK_CHANGE_ASSIGNMENT,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_x9gub8q2",
                    CommonConstants.KEY_REGION, "custom_efq8vijq",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_3qfwp847",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_xdbpzt0n",
                    CommonConstants.KEY_NETWORK, "custom_y97srldb",
                    CommonConstants.KEY_ASSIGNMENT_STATUS, "fastcodefield_cov2v4j6",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_v62ud019",
                    CommonConstants.KEY_OPERATION_RESULT, "selectfield_naxfeulg")),

    /** 内部网络变更任务 - 待我处理 */
    INTERIOR_TO_BE_HANDLED(PageConstants.PAGE_WEB_NETWORK_CHANGE_TO_BE_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_t6af690q",
                    CommonConstants.KEY_REGION, "custom_1lw41lml",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_m8u7vz68",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c",
                    CommonConstants.KEY_NETWORK, "custom_6vp6mddc",
                    CommonConstants.KEY_ASSIGNMENT_STATUS, "fastcodefield_cov2v4j6",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_yfx84b63",
                    CommonConstants.KEY_OPERATION_RESULT, "selectfield_naxfeulg")),

    /** 内部网络变更任务 - 我已处理 */
    INTERIOR_HANDLED_BY_ME(PageConstants.PAGE_WEB_NETWORK_CHANGE_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_t6af690q",
                    CommonConstants.KEY_REGION, "custom_2dg6dqs6",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_m8u7vz68",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c",
                    CommonConstants.KEY_NETWORK, "custom_6vp6mddc",
                    CommonConstants.KEY_ASSIGNMENT_STATUS, "fastcodefield_cov2v4j6",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_yfx84b63",
                    CommonConstants.KEY_OPERATION_RESULT, "selectfield_naxfeulg",
                    CommonConstants.KEY_DEALING_TIME, "custom_vp9xnz7u")),

    /** 内部网络变更任务 - 我发起的 */
    INTERIOR_INITIATED(PageConstants.PAGE_WEB_NETWORK_CHANGE_INITIATED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_t6af690q",
                    CommonConstants.KEY_REGION, "custom_mx506xdj",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_m8u7vz68",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c",
                    CommonConstants.KEY_NETWORK, "custom_6vp6mddc",
                    CommonConstants.KEY_ASSIGNMENT_STATUS, "fastcodefield_cov2v4j6",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_yfx84b63",
                    CommonConstants.KEY_OPERATION_RESULT, "selectfield_naxfeulg")),

    /** 合作方网络变更任务 - 待我处理 */
    PARTNER_TO_BE_HANDLED(PageConstants.PARTNER_CHANGE_ORDER_TO_BE_HANDLED,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_p75yop6c",
                    CommonConstants.KEY_REGION, "custom_5fp2foqn",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_ju979zy7",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c",
                    CommonConstants.KEY_NETWORK, "custom_3uh9095r",
                    CommonConstants.KEY_ASSIGNMENT_STATUS, "fastcodefield_cov2v4j6",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_ckd76qwv",
                    CommonConstants.KEY_OPERATION_RESULT, "selectfield_naxfeulg")),

    /** 合作方网络变更任务 - 我已处理 */
    PARTNER_HANDLED_BY_ME(PageConstants.PARTNER_CHANGE_ORDER_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_m7adm26j",
                    CommonConstants.KEY_REGION, "custom_2lvzcjyr",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_ju979zy7",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c",
                    CommonConstants.KEY_NETWORK, "custom_izxbpys9",
                    CommonConstants.KEY_ASSIGNMENT_STATUS, "fastcodefield_cov2v4j6",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_44wqhns4",
                    CommonConstants.KEY_OPERATION_RESULT, "selectfield_naxfeulg",
                    CommonConstants.KEY_DEALING_TIME, "custom_u1k981kp")),

    /** 合作方网络变更任务 - 我发起的 */
    PARTNER_INITIATED(PageConstants.PARTNER_CHANGE_ORDER_INITIATED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_3d33un2r",
                    CommonConstants.KEY_REGION, "custom_n8bru145",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_ju979zy7",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c",
                    CommonConstants.KEY_NETWORK, "custom_2mtuodn4",
                    CommonConstants.KEY_ASSIGNMENT_STATUS, "fastcodefield_cov2v4j6",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_v2agjojt",
                    CommonConstants.KEY_OPERATION_RESULT, "selectfield_naxfeulg")),

    /** 技术管理任务 - 任务中心 */
    TECHNOLOGY_ASSIGNMENT(PageConstants.PAGE_WEB_TECHNOLOGY_MANAGEMENT_ASSIGNMENT,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_zpawotr3",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_f0pm9yut",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_p4c45u6d",
                    CommonConstants.KEY_NETWORK, "custom_6imrwobu",
                    CommonConstants.KEY_RESPONSIBLE, "employeefield_z6go94qd",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_w2cklyxb",
                    CommonConstants.KEY_OVER_FLAG, "custom_rg1jq0sp",
                    CommonConstants.KEY_OVERDUE_TIME, "custom_ny0y8wqr")),

    /** 技术管理任务 - 待我处理 */
    TECHNOLOGY_TO_BE_HANDLED(PageConstants.PAGE_WEB_TECHNOLOGY_TO_BE_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_y1ewb91e",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_f0pm9yut",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_p4c45u6d",
                    CommonConstants.KEY_NETWORK, "custom_6imrwobu",
                    CommonConstants.KEY_RESPONSIBLE, "employeefield_z6go94qd",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_729bnn5h",
                    CommonConstants.KEY_OVER_FLAG, "custom_omp80knu",
                    CommonConstants.KEY_OVERDUE_TIME, "custom_5rfpqzmu")),

    /** 技术管理任务 - 我已处理 */
    TECHNOLOGY_HANDLED_BY_ME(PageConstants.PAGE_WEB_TECHNOLOGY_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_dasmqg1j",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_f0pm9yut",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_p4c45u6d",
                    CommonConstants.KEY_NETWORK, "custom_6imrwobu",
                    CommonConstants.KEY_RESPONSIBLE, "employeefield_z6go94qd",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_krtvtsws",
                    CommonConstants.KEY_OVER_FLAG, "custom_b9kxef7z",
                    CommonConstants.KEY_OVERDUE_TIME, "custom_lv7k8ooc",
                    CommonConstants.KEY_DEALING_TIME, "custom_fbd3bmrj")),

    /** 技术管理任务 - 我发起的 */
    TECHNOLOGY_INITIATED(PageConstants.PAGE_WEB_TECHNOLOGY_INITIATED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_s3149824",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_f0pm9yut",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_p4c45u6d",
                    CommonConstants.KEY_NETWORK, "custom_6imrwobu",
                    CommonConstants.KEY_RESPONSIBLE, "employeefield_z6go94qd",
                    CommonConstants.KEY_CURRENT_PROGRESS, "custom_508sqhgh",
                    CommonConstants.KEY_OVER_FLAG, "custom_0yw1fc7m",
                    CommonConstants.KEY_OVERDUE_TIME, "custom_95v3oys5")),

    /** 打卡复盘 - 待我处理 */
    CLOCK_REVIEW_TO_BE_HANDLED(PageConstants.PAGE_WEB_ABNORMAL_REVIEW_TO_BE_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_ml24bujk",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_uq2d9y2n",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_vjyehwyh",
                    CommonConstants.KEY_NETWORK, "custom_qme1v7v2")),

    /** 打卡复盘 - 我已处理 */
    CLOCK_REVIEW_HANDLED_BY_ME(PageConstants.PAGE_WEB_ABNORMAL_REVIEW_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_7wvsld4e",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_gesgmax2",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_sa12cx0a",
                    CommonConstants.KEY_NETWORK, "custom_drg1iw65",
                    CommonConstants.KEY_DEALING_TIME, "custom_lao9jcae")),

    /** 打卡复盘 - 我发起的 */
    CLOCK_REVIEW_INITIATED(PageConstants.PAGE_WEB_ABNORMAL_REVIEW_INITIATED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_ylze949m",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_oh3djuih",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_tgmy36yf",
                    CommonConstants.KEY_NETWORK, "custom_w3crsjuo")),

    /** 总页面 - 待我处理 */
    TO_BE_HANDLED(PageConstants.PAGE_WEB_TO_BE_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_ju979zy7",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c")),

    /** 总页面 - 我已处理 */
    HANDLED_BY_ME(PageConstants.PAGE_WEB_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_ju979zy7",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c",
                    CommonConstants.KEY_APPROVAL_TASK_FLAG, "custom_4g70c0fw",
                    CommonConstants.KEY_DEALING_TIME, "custom_lcmf1wix")),

    /** 总页面 - 我发起的 */
    INITIATED(PageConstants.PAGE_WEB_INITIATED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_ju979zy7",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_9dlfxa9c")),

    /** 配置中心 - 邮件推送群组配置 */
    EMAIL_SEND_GROUP(PageConstants.PAGE_WEB_EMAIL_SEND_GROUP,
            MapUtils.newHashMap(
                    CommonConstants.KEY_PRODUCT_OPERATION_TEAM, "custom_lfzy4e3e",
                    CommonConstants.KEY_PRODUCT_LINE, "custom_aowsze5t",
                    CommonConstants.KEY_PRODUCT_CATEGORY, "custom_xudcggg6",
                    CommonConstants.KEY_PRODUCT_SUBCATEGORY, "custom_vcpv1o5n")),

    /** 操作计划任务 - 任务中心 */
    OPERATION_PLAN_ASSIGNMENT(PageConstants.PAGE_WEB_OPERATION_PLAN_MANAGEMENT,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_3ni01dj0",
                    CommonConstants.KEY_REGION, "custom_z4plkj2h",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_e5u7aohv",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_2xvohe3h",
                    CommonConstants.KEY_NETWORK, "custom_lc9frmpq")),

    /** 操作计划任务 - 待我处理 */
    OPERATION_PLAN_TO_BE_HANDLED(PageConstants.PAGE_WEB_OPERATION_PLAN_TO_BE_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_3ni01dj0",
                    CommonConstants.KEY_REGION, "custom_z4plkj2h",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_e5u7aohv",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_2xvohe3h",
                    CommonConstants.KEY_NETWORK, "custom_lc9frmpq")),

    /** 操作计划任务 - 我已处理 */
    OPERATION_PLAN_HANDLED_BY_ME(PageConstants.PAGE_WEB_OPERATION_PLAN_HANDLED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_3ni01dj0",
                    CommonConstants.KEY_REGION, "custom_z4plkj2h",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_e5u7aohv",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_2xvohe3h",
                    CommonConstants.KEY_NETWORK, "custom_lc9frmpq")),

    /** 操作计划任务 - 我发起的 */
    OPERATION_PLAN_INITIATED(PageConstants.PAGE_WEB_OPERATION_INITIATED_BY_ME,
            MapUtils.newHashMap(
                    CommonConstants.KEY_MARKETING, "custom_3ni01dj0",
                    CommonConstants.KEY_REGION, "custom_z4plkj2h",
                    CommonConstants.KEY_REPRESENTATIVE_OFFICE, "custom_e5u7aohv",
                    CommonConstants.KEY_PRODUCT_CLASSIFICATION, "custom_2xvohe3h",
                    CommonConstants.KEY_NETWORK, "custom_lc9frmpq")),
    ;

    /** 页面ID */
    private final String pageCid;

    /** DataKey */
    private final Map<String, String> dataKey;


    /**
     * 根据页面获取对应表格 CID
     */
    public static Map<String, String> getPageTableDataKey(String pageCid) {
        for (DataKeyEnum dataKeyEnum : DataKeyEnum.values()) {
            if (dataKeyEnum.getPageCid().equals(pageCid)) {
                return dataKeyEnum.getDataKey();
            }
        }

        return new HashMap<>();
    }
}
