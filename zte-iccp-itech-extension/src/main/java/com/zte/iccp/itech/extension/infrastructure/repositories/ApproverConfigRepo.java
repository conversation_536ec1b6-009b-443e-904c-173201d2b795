package com.zte.iccp.itech.extension.infrastructure.repositories;

import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.LocalCacheUtils;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.infrastructure.annotations.BindFieldName;
import com.zte.iccp.itech.extension.infrastructure.conditions.ApproverCfgQueryCondition;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.ddm.common.api.dto.orm.Range;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.entity.ApprovalConfFieldConsts.APPROVAL_NODE;

/**
 * 仅用于后台查询，不返回名称等字段
 *
 * <AUTHOR>
 * @since 2024/12/24
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ApproverConfigRepo {

    public static final String IS_EMPTY_FIELD_SUFFIX = "IsEmpty";

    public static final String LOCAL_CACHE_KEY_PREFIX = "approverConfigs:";

    private static final int CACHE_EXPIRE_MINUTES = Integer.parseInt(
            ConfigHelper.get("approverConfig.cacheMinutes", "60"));

    private static final List<FilterAction> FILTER_ACTIONS = initFilterActions();

    public static void reloadAllCache() {
        for (ApprovalTypeEnum approveNode : ApprovalTypeEnum.values()) {
            LocalCacheUtils.set(
                    LOCAL_CACHE_KEY_PREFIX + approveNode,
                    queryAll(approveNode),
                    CACHE_EXPIRE_MINUTES);
        }
    }

    public static void reload(String approveNode) {
        LocalCacheUtils.set(LOCAL_CACHE_KEY_PREFIX + approveNode,
                ApproverConfigRepo.queryAll(ApprovalTypeEnum.getApprovalTypeEnumByName(approveNode)),
                CACHE_EXPIRE_MINUTES);
    }

    @SneakyThrows
    public static List<ApproverConfiguration> query(ApproverCfgQueryCondition condition) {
        Stream<ApproverConfiguration> allConfigs = Objects.requireNonNull(
                LocalCacheUtils.getAcceptExpired(
                        LOCAL_CACHE_KEY_PREFIX + condition.getApproveNode(),
                        () -> queryAll(condition.getApproveNode()),
                        CACHE_EXPIRE_MINUTES)).stream();

        for (FilterAction filterAction : FILTER_ACTIONS) {
            Object condValue = filterAction.conditionField.get(condition);
            if (condValue == null) {
                continue;
            }

            allConfigs = allConfigs.filter(cfg ->
                    filterAction.predicate.test(condValue, cfg));
        }

        return allConfigs.collect(Collectors.toList());
    }

    public static List<ApproverConfiguration> queryAll(ApprovalTypeEnum approveNode) {
        if(approveNode == null) {
            return new ArrayList<>();
        }

        final int pageSize = 5000;
        final int maxPageNo = 20;

        List<ApproverConfiguration> allInNode = Lists.newArrayList();
        for (int pageNo = 1; pageNo <= maxPageNo; ++pageNo) {
            List<ApproverConfiguration> pageRows = QueryDataHelper.query(
                    ApproverConfiguration.class,
                    Lists.newArrayList(),
                    Lists.newArrayList(
                            new Filter(APPROVAL_NODE, Comparator.EQ, Lists.newArrayList(approveNode))),
                    new Range(pageNo, pageSize));
            allInNode.addAll(pageRows);

            if (pageRows.size() < pageSize) {
                break;
            }
        }

        return allInNode;
    }

    @SneakyThrows
    private static List<FilterAction> initFilterActions() {
        try {
            List<FilterAction> filterActions = Lists.newArrayList();

            for (Field conditionField : ApproverCfgQueryCondition.class.getDeclaredFields()) {
                conditionField.setAccessible(true);
                BindFieldName bindFieldName = conditionField.getAnnotation(BindFieldName.class);
                if (bindFieldName == null) {
                    continue;
                }

                String configFieldName = StringUtils.isBlank(bindFieldName.value())
                        ? conditionField.getName() : bindFieldName.value();
                Field configField = ApproverConfiguration.class.getDeclaredField(configFieldName);
                configField.setAccessible(true);

                filterActions.add(new FilterAction(conditionField, configField));
            }

            return filterActions;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    private interface PredicateEx {
        boolean test(Object condValue, ApproverConfiguration config) throws RuntimeException;
    }

    private static class FilterAction {
        private final Field conditionField;

        private final Field configField;

        private final PredicateEx predicate;

        public FilterAction(Field conditionField, Field configField) {
            this.conditionField = conditionField;
            this.configField = configField;

            predicate = buildPredicate();
        }

        private PredicateEx buildPredicate() {
            Class<?> condFieldClass = conditionField.getType();
            Class<?> cfgFieldClass = configField.getType();

            if (Collection.class.isAssignableFrom(condFieldClass)) {
                if (Collection.class.isAssignableFrom(cfgFieldClass)) {
                    return this::containsAll;
                }

                return this::contains;
            }

            if ((condFieldClass.isEnum() && cfgFieldClass.isEnum())
                    || (condFieldClass == String.class && cfgFieldClass == String.class)) {
                return this::equals;
            }

            if (condFieldClass == Boolean.class
                    && conditionField.getName().endsWith(IS_EMPTY_FIELD_SUFFIX)) {
                if (cfgFieldClass == String.class) {
                    return this::stringIsEmpty;
                }

                if (Collection.class.isAssignableFrom(cfgFieldClass)) {
                    return this::collectionIsEmpty;
                }
            }

            throw new LcapBusiException(String.format(
                    "Unsupported: %s %s",
                    conditionField.getName(),
                    configField.getName()));
        }

        @SneakyThrows
        private boolean equals(Object condValue, ApproverConfiguration cfg) {
            return condValue.equals(configField.get(cfg));
        }

        @SneakyThrows
        private boolean contains(Object condValue, ApproverConfiguration cfg) {
            Collection<?> collection = (Collection<?>) condValue;
            return collection.contains(configField.get(cfg));
        }

        @SneakyThrows
        private boolean containsAll(Object condValue, ApproverConfiguration cfg) {
            Collection<?> cltCondValue = (Collection<?>) condValue;
            Collection<?> cltConfigValue = (Collection<?>) configField.get(cfg);
            //noinspection SuspiciousMethodCalls
            return cltConfigValue != null
                    && cltConfigValue.containsAll(cltCondValue);
        }

        @SneakyThrows
        private boolean stringIsEmpty(Object condValue, ApproverConfiguration cfg) {
            boolean isEmpty = (boolean) condValue;
            return isEmpty == StringUtils.isEmpty((String) configField.get(cfg));
        }

        @SneakyThrows
        private boolean collectionIsEmpty(Object condValue, ApproverConfiguration cfg) {
            boolean isEmpty = (boolean) condValue;
            return isEmpty == CollectionUtils.isEmpty((Collection<?>) configField.get(cfg));
        }
    }
}
