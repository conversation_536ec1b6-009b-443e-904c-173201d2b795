package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/05
 */
@Getter
@Setter
@EqualsAndHashCode
public class ProductTreeNode {
    private String parent;

    private String number;

    @JsonProperty("longnumber")
    private String longNumber;

    private String name;

    @JsonProperty("longname")
    private String longName;

    @JsonProperty("isleaf")
    private boolean isLeaf;
}