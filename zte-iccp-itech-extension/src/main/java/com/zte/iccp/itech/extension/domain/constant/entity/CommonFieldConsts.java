package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CommonFieldConsts {
    /**
     * 主键
     */
    public static final String ID = "#id";

    /**
     * 父级ID
     */
    public static final String PID = "#p_id";

    /**
     * 创建人
     */
    public static final String CREATE_BY = "create_by";

    /**
     * 创建时间
     */
    public static final String CREATE_TIME = "create_time";

    /**
     * 最后更新人
     */
    public static final String LAST_MODIFIED_BY = "last_modified_by";

    /**
     * 最后更新时间
     */
    public static final String LAST_MODIFIED_TIME = "last_modified_time";

    /**
     * 是否已删除
     */
    public static final String IS_DELETED = "#is_deleted";

    /**
     * 日期/时间范围开始字段后缀
     */
    public static final String RANGE_START_SUFFIX = "_start_key";

    /**
     * 日期/时间范围结束字段后缀
     */
    public static final String RANGE_END_SUFFIX = "_end_key";
}
