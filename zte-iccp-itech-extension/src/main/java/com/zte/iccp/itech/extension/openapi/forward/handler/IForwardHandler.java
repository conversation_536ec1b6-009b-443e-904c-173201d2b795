package com.zte.iccp.itech.extension.openapi.forward.handler;

import java.util.Map;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/23
 */
public interface IForwardHandler {

    /**
     * 处理请求头，默认直接返回原始请求头
     * @param headers 原始请求头
     * @return 最终使用的请求头
     */
    default Map<String, Object> headers(Map<String, Object> headers) {
        return headers;
    }

    /**
     * 处理query参数，默认直接返回原始参数
     * @param params 原始参数
     * @return 最终使用的参数
     */
    default Map<String, String[]> params(Map<String, String[]> params) {
        return params;
    }

    /**
     * 处理请求体，默认直接返回原始请求体
     * @param body 原始请求体
     * @return 最终使用的请求体
     */
    default Object body(Object body) {
        return body;
    }

    /**
     * 处理目标URL，默认使用query实参内容填充path形参
     * @param url 原始URL
     * @param params 最终使用的query实参
     * @return 最终使用的URL
     */
    default String url(String url, Map<String, String[]> params) {
        for (Map.Entry<String, String[]> entry : params.entrySet()) {
            String[] value = entry.getValue();
            if (value == null) {
                value = new String[0];
            }

            url = url.replace(String.format("{%s}", entry.getKey()), String.join(",", value));
        }

        return url;
    }

    /**
     * 处理响应体bo，默认进行强转
     * @param bo 原始响应体bo
     * @return 处理后的bo
     * @param <T> 出参类型
     */
    default <T> T bo(Object bo) {
        //noinspection unchecked
        return (T) bo;
    }
}
