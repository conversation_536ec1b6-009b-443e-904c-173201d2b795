package com.zte.iccp.itech.extension.domain.enums.clockin;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ReviewReasonEnum {

    /** 产品原因 */
    PRODUCT("reviewReason.product", "PRODUCT"),

    /** 服务原因 */
    SERVICE("reviewReason.service", "SERVICE"),

    /** 外部原因 */
    EXTERNAL("reviewReason.external", "EXTERNAL"),

    /** 客户原因 */
    CUSTOMER("reviewReason.customer", "CUSTOMER"),

    /** 产品 - 原因待定 */
    PRODUCT_UNDETERMINED_REASON("reviewReason.product.undeterminedReason", "49"),

    /** 产品 - 硬件 */
    PRODUCT_HARDWARE("reviewReason.product.hardware", "48"),

    /** 产品 - 产品安全 */
    PRODUCT_PRODUCT_SECURITY("reviewReason.product.productSecurity", "47"),

    /** 产品 - 文档 */
    PRODUCT_DOCUMENT("reviewReason.product.document", "46"),

    /** 产品 - 软件 */
    PRODUCT_SOFTWARE("reviewReason.product.software", "45"),

    /** 产品 - 外购件 */
    PRODUCT_PURCHASED_COMPONENTS("reviewReason.product.purchasedComponents", "44"),

    /** 服务 - 售前技术方案 */
    SERVICE_PRE_SALES_TECHNICAL_SOLUTION("reviewReason.service.perSalesTechnicalSolution", "43"),

    /** 服务 - 批量实施单漏执行 */
    SERVICE_OMITTED_IMPLEMENTATION_TASKS("reviewReason.service.omittedImplementationTasks", "42"),

    /** 服务 - 我司工程师技能不足 */
    SERVICE_ZTE_ENGINEER_INSUFFICIENT_SKILL("reviewReason.service.zteEngineerInsufficientSkills", "41"),

    /** 服务 - 我司外包人员技能不足 */
    SERVICE_ZTE_OUTSOURCING_PERSONNEL_INSUFFICIENT_SKILL(
            "reviewReason.service.zteOutsourcingPersonnelInsufficientSkills", "40"),

    /** 服务 - 我司工程师操作不规范 */
    SERVICE_ZTE_ENGINEER_NON_STANDARD_OPERATION(
            "reviewReason.service.zteEngineerNonStandardOperations", "39"),

    /** 服务 - 我司外包人员操作不规范 */
    SERVICE_ZTE_OUTSOURCING_PERSONNEL_NON_STANDARD_OPERATION(
            "reviewReason.service.zteOutsourcingPersonnelNonStandardOperations", "38"),

    /** 服务 - 我司工程师操作失误 */
    SERVICE_ZTE_ENGINEER_OPERATION_ERROR("reviewReason.service.zteEngineerOperationError", "37"),

    /** 服务 - 我司外包人员操作失误 */
    SERVICE_ZTE_OUTSOURCING_PERSONNEL_OPERATION_ERROR(
            "reviewReason.service.zteOutsourcingPersonnelOperationError", "36"),

    /** 服务 - 培训服务 */
    SERVICE_TRAINING_SERVICE("reviewReason.service.trainingService", "35"),

    /** 服务 - 工程质量 */
    SERVICE_ENGINEERING_QUALITY("reviewReason.service.engineeringQuality", "34"),

    /** 外部 - 我司其他产品 */
    EXTERNAL_OUR_ZTE_PRODUCTS("reviewReason.external.otherZteProduct", "33"),

    /** 外部 - 不可抗力 */
    EXTERNAL_FORCE_MAJEURE("reviewReason.external.forceMajeure", "32"),

    /** 外部 - 第三方 */
    EXTERNAL_THIRD_PARTY("reviewReason.external.thirdParty", "31"),

    /** 客户 - 客户需求 */
    CUSTOMER_REQUIREMENT("reviewReason.customer.requirement", "54"),

    /** 客户 - 客户操作失误 */
    CUSTOMER_OPERATION_ERROR("reviewReason.customer.operationError", "53"),

    /** 客户 - 客户技能不足 */
    CUSTOMER_INSUFFICIENT_SKILL("reviewReason.customer.insufficientSkill", "52"),

    /** 客户 - 客户操作不规范 */
    CUSTOMER_NON_STANDARD_OPERATION("reviewReason.customer.nonStandardOperation", "51"),

    /** 客户 - 客户管理 */
    CUSTOMER_MANAGEMENT("reviewReason.customer.management", "50");

    /**
     * 双语文案 Id
     */
    private final String msgId;

    /**
     * 原因编码
     */
    private final String nodeKey;


    /**
     * 获取双语文案 Id
     */
    public static String getMsgIdByNodeKey(String nodeKey) {
        for (ReviewReasonEnum reviewReasonEnum : ReviewReasonEnum.values()) {
            if (reviewReasonEnum.getNodeKey().equals(nodeKey)) {
                return reviewReasonEnum.getMsgId();
            }
        }

        return CommonConstants.EMPTY_STRING;
    }
}
