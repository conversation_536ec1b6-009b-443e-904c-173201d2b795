package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2024/9/5 上午9:36
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum IdopCancelEnum {
    /**
     * 删除
     */
    DELETE("delete"),
    /**
     * 废止 取消
     */
    ABOLISH("abolish"),
    /**
     * 驳回
     */
    REJECT("reject"),
    ;

    private final String value;

    public static IdopCancelEnum getIdopCancelEnum(String status) {
        return Arrays.stream(IdopCancelEnum.values())
                .filter(item -> item.getValue().equals(status))
                .findFirst()
                .orElse(null);
    }
}
