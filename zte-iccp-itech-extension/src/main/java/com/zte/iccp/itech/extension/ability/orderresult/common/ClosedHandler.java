package com.zte.iccp.itech.extension.ability.orderresult.common;

import com.zte.iccp.itech.extension.ability.orderresult.BaseUpdateStatusHandler;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationSchemeStatusEnum;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
public class ClosedHandler extends BaseUpdateStatusHandler {
    public ClosedHandler() {
        super(AssignmentStatusEnum.CLOSE, OperationSchemeStatusEnum.RELEASED);
    }
}
