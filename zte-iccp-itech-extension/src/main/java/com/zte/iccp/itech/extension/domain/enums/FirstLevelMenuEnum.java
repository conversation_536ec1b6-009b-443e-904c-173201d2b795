package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 一级菜单枚举类
 * <AUTHOR> jiangjiawen
 * @date 2024/8/26
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum FirstLevelMenuEnum {
    /**
     * 我的待办（默认）
     */
    MY_TO_DO("my_to_do_cid", "我的待办"),

    /**
     * 我创建的
     */
    CREATE_BY("create_by_cid", "我创建的"),

    /**
     * 全部任务
     */
    ALL_ASSIGNMENT("all_assignment_cid", "全部任务"),
    ;

    /**
     * 一级菜单唯一标识
     */
    private final String value;

    /**
     * 自定义选项
     */
    private final String name;



    /**
     * 根据值获取枚举
     *
     * @param value value
     * @return FirstLevelMenuEnum
     */
    public static FirstLevelMenuEnum fromValue(String value) {
        for (FirstLevelMenuEnum firstLevelMenuEnum : FirstLevelMenuEnum.values()) {
            if (String.valueOf(firstLevelMenuEnum.getValue()).equals(value)) {
                return firstLevelMenuEnum;
            }
        }

        return null;
    }
}
