package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.NetworkChangeFieldConsts.APPROVE_BATCH_TASK_ID;

@ApiModel("任务实体")
@Setter
@Getter
@BaseEntity.Info("assignment")
public class Assignment extends BaseEntity {

    @ApiModelProperty("实体ID")
    @JsonProperty(value = ENTITY_ID)
    private String entityId;

    @ApiModelProperty("单据ID")
    @JsonProperty(value = BILL_ID)
    private String billId;

    @ApiModelProperty("任务名称")
    @JsonProperty(value = ASSIGNMENT_NAME)
    private String assignmentName;

    @ApiModelProperty("任务编码")
    @JsonProperty(value = ASSIGNMENT_CODE)
    private String assignmentCode;

    @ApiModelProperty("任务状态")
    @JsonProperty(value = ASSIGNMENT_STATUS)
    private String assignmentStatus;

    @ApiModelProperty("任务类型")
    @JsonProperty(value = ASSIGNMENT_TYPE)
    private List<TextValuePair> assignmentType;

    @ApiModelProperty("单据类型")
    @JsonProperty(value = BILL_TYPE)
    private List<TextValuePair> billType;

    @ApiModelProperty("公司")
    @JsonProperty(value = COMPANY)
    private List<TextValuePair> company;

    @ApiModelProperty("客户标识")
    @JsonProperty(value = CUSTOMER_CLASSIFICATION)
    private List<TextValuePair> customerClassification;

    @ApiModelProperty("责任人")
    @JsonProperty(value = RESPONSIBLE_EMPLOYEE_FIELD)
    private List<Employee> responsibleEmployee;

    @ApiModelProperty("营销")
    @JsonProperty(value = MARKETING)
    private List<TextValuePair> marketing;

    @ApiModelProperty("代表处")
    @JsonProperty(value = REPRESENTATIVE_OFFICE)
    private List<TextValuePair> representativeOffice;

    @ApiModelProperty("产品经营团队")
    @JsonProperty(value = PRODUCT_MANAGEMENT_TEAM)
    private List<TextValuePair> productManagementTeam;

    @ApiModelProperty("产品分类")
    @JsonProperty(value = PRODUCT_CLASSIFICATION)
    private List<TextValuePair> productClassification;

    @ApiModelProperty("网络")
    @JsonProperty(value = NETWORK)
    private List<TextValuePair> network;

    @ApiModelProperty("当前进展")
    @JsonProperty(value = CURRENT_PROGRESS)
    private String currentProgress;

    @ApiModelProperty("当前进展是否审批任务")
    @JsonProperty(value = APPROVAL_TASK_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum approvalTaskFlag;

    @ApiModelProperty("当前处理人")
    @JsonProperty(value = CURRENT_PROCESSOR_EMPLOYEE_FIELD)
    private List<Employee> currentProcessorEmployee;

    // ======================= 网络变更任务扩展字段 =======================
    @ApiModelProperty("批次任务审批实体ID")
    @JsonProperty(value = APPROVE_BATCH_TASK_ID)
    private String approveBatchTaskId;
}
