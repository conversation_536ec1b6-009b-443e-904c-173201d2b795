package com.zte.iccp.itech.extension.plugin.form.backlog;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.domain.constant.*;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApprovePageEnum;
import com.zte.iccp.itech.extension.domain.enums.DetailPageEnum;
import com.zte.iccp.itech.extension.domain.enums.UserTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.entity.*;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iss.approval.sdk.bean.*;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.event.RowClickEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import com.zte.paas.lcap.ddm.domain.enums.OpenTypeEnum;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum.*;

public class HyperlinkPlugin extends BaseFormPlugin {

    /** 配置中心 - 任务审批页面路径 */
    private static final String HREF_URL = "assignment.approve.pageUrl";

    /** 任务 - 任务名称 */
    private static final String COMMON_ASSIGNMENT_NAME = "textfield_tz852y9c";

    /**
     * 操作计划审批节点对应的审批页面map
     * key - 审批节点编码
     * value - 审批页面pageId
     */
    private static final Map<String, String> OPERATION_PLAN_APPROVE_NODE_PAGE_ID_MAP = ImmutableMap.of(
            REP_PROD_TD_APPROVE.name(), "PAGE1150078142288637952",
            TD_NET_DEPT_APPROVE.name(), "PAGE1154346778519302144",
            NETWORK_CHIEF_ENGINEER.name(), "PAGE1154346466224009217");

    @Override
    public void afterRowClick(RowClickEvent rowClickEvent) {
        IFormView formView = getView();

        // 1.移除默认 showForm 页面跳转
        formView.getClientViewProxy().getActionResult().clear();

        // 2.任务 + 点击列校验
        // 任务为空，说明校验异常，不跳转
        Assignment assignment = checkAssignmentAndClickField(formView, rowClickEvent);
        if (Objects.isNull(assignment)) {
            return;
        }

        // 3.根据任务类型跳转对应页面
        openPage(assignment);
    }

    /**
     * 任务 + 点击列校验
     */
    private Assignment checkAssignmentAndClickField(
            IFormView formView,
            RowClickEvent rowClickEvent) {

        JSONObject eventArgsValue
                = rowClickEvent.getArgs().getJSONObject(CommonConstants.VALUE);

        // 1.点击字段校验
        String clickField = eventArgsValue.getString(CommonConstants.COL_KEY);
        if (!COMMON_ASSIGNMENT_NAME.equals(clickField)) {
            return null;
        }

        // 2.任务校验
        TablePc tableInfo = (TablePc) formView.getControl(CidConstants.TABLE_ASSIGNMENT_CID);
        String assignmentId = eventArgsValue.getString(tableInfo.getPkId());
        if (!StringUtils.hasText(assignmentId)) {
            return null;
        }

        return AssignmentAbility.querySpecificTypeAssignment(assignmentId, Assignment.class);
    }

    /**
     * 跳转对应单据编辑页面
     */
    public void openPage(Assignment assignment) {
        IFormView formView = getView();

        AssignmentTypeEnum assignmentType
                = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        switch (assignmentType) {
            case NETWORK_CHANGE:
                networkChangeShowForm(formView, assignment);
                break;

            case NETWORK_CHANGE_BATCH:
                batchShowForm(formView, assignment);
                break;

            case SUBCONTRACTOR_NETWORK_CHANGE:
                partnerNetworkChangeShowForm(formView, assignment);
                break;

            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                partnerBatchShowForm(formView, assignment);
                break;

            case TECHNOLOGY_MANAGEMENT:
                technologyShowForm(formView, assignment);
                break;

            case TECHNOLOGY_MANAGEMENT_SUB:
                technologySubShowForm(formView, assignment);
                break;

            case FAULT_MANAGEMENT:
                faultShowForm(formView, assignment);
                break;

            case PERMISSION_APPLICATION:
                permissionShowForm(formView, assignment);
                break;

            case CLOCK_REVIEW:
                clockReviewShowForm(formView, assignment);
                break;

            case OPERATION_PLAN_TASK:
                operationPlanShowForm(formView, assignment);
                break;

            default:
                break;
        }
    }

    /**
     * 页面跳转 - 网络变更
     */
    private void networkChangeShowForm(IFormView formView, Assignment assignment) {

        // 1.计算当前处理人标识
        boolean currentProcessorFlag = checkCurrentProcessor(assignment.getCurrentProcessorEmployee());

        // 2.根据任务状态跳转对应页面
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo;
        switch (assignmentStatus) {
            case START:
                forwardTargetInfo = getInteriorMainNetworkChangeInfo(
                        assignment, currentProcessorFlag ? PageStatusEnum.EDIT : PageStatusEnum.VIEW, PAGE_NETWORK_CHANGE_BILL);
                sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                break;
            case APPROVE_START:
                forwardTargetInfo = getInteriorMainNetworkChangeInfo(
                        assignment, currentProcessorFlag ? PageStatusEnum.EDIT : PageStatusEnum.VIEW, PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL);
                sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                break;

            case ABOLISH:
                forwardTargetInfo = getInteriorMainNetworkChangeInfo(assignment, PageStatusEnum.VIEW, PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL);
                sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                break;

            case APPROVE:
                String pageId = ApproveNodeEnum.getApproveNodePageId(assignment.getCurrentProgress());
                if (currentProcessorFlag) {
                    ApprovePageEnum pageEnum = ApprovePageEnum.getPageIdByType(AssignmentTypeEnum.NETWORK_CHANGE);
                    sendShowFormInstruction(
                            formView,
                            assignment.getEntityId(),
                            assignment.getAssignmentCode(),
                            pageId,
                            pageEnum.getBizObjectCode());
                } else {
                    forwardTargetInfo = getInteriorMainNetworkChangeInfo(assignment, PageStatusEnum.VIEW, pageId);
                    sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                }

                break;

            case EXECUTE:
            case CLOSE:
                // 判断当前单据是否为保障单据，如果是将实体id传入
                forwardTargetInfo = getInteriorBatchNetworkChangeInfo(assignment, null, assignment.getEntityId(), assignment.getAssignmentType());
                sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                break;

            default:
                break;
        }
    }

    /**
     * 页面跳转 - 批次任务
     */
    private void batchShowForm(IFormView formView, Assignment assignment) {

        // 1.检索主任务数据
        Assignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                assignment.getBillId(), AssignmentTypeEnum.NETWORK_CHANGE, Assignment.class);

        // 2.批次跳转批次汇总_v2详情页面
        Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo = getInteriorBatchNetworkChangeInfo(
                mainAssignment,
                assignment.getEntityId(),
                assignment.getBillId(),
                assignment.getAssignmentType());
        sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
    }

    /**
     * 页面跳转 - 合作方网络变更
     */
    private void partnerNetworkChangeShowForm(
            IFormView formView,
            Assignment assignment) {

        // 1.计算当前处理人标识
        boolean currentProcessorFlag = checkCurrentProcessor(assignment.getCurrentProcessorEmployee());

        // 2.根据任务状态跳转对应页面
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo;
        switch (assignmentStatus) {
            case START:
                forwardTargetInfo = getSubcontractMainNetworkChangeInfo(
                        assignment, currentProcessorFlag ? PageStatusEnum.EDIT : PageStatusEnum.VIEW,
                        PAGE_PARTNER_NETWORK_CHANGE_BILL);
                sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                break;
            case APPROVE_START:
                forwardTargetInfo = getSubcontractMainNetworkChangeInfo(
                        assignment, currentProcessorFlag ? PageStatusEnum.EDIT : PageStatusEnum.VIEW,
                        PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL);
                sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                break;

            case ABOLISH:
                forwardTargetInfo = getSubcontractMainNetworkChangeInfo(assignment, PageStatusEnum.VIEW,
                        PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL);
                sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                break;

            case APPROVE:
                if (currentProcessorFlag) {
                    ApprovePageEnum pageEnum = ApprovePageEnum.getPageIdByType(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE);
                    sendShowFormInstruction(
                            formView,
                            assignment.getEntityId(),
                            assignment.getAssignmentCode(),
                            pageEnum.getPageId(),
                            pageEnum.getBizObjectCode());
                } else {
                    forwardTargetInfo = getSubcontractMainNetworkChangeInfo(assignment, PageStatusEnum.VIEW,
                            PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL);
                    sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                }

                break;

            case EXECUTE:
            case CLOSE:
                forwardTargetInfo = getSubcontractBatchNetworkChangeInfo(assignment, null, assignment.getEntityId(), assignment.getAssignmentType());
                sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
                break;

            default:
                break;
        }
    }

    /**
     * 页面跳转 - 合作方网络变更批次任务
     */
    private void partnerBatchShowForm(IFormView formView, Assignment assignment) {
        // 1.检索主任务数据
        Assignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                assignment.getBillId(), AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, Assignment.class);

        // 3.批次跳批次汇总v2_详情
        Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo = getSubcontractBatchNetworkChangeInfo(
                mainAssignment,
                assignment.getEntityId(),
                assignment.getBillId(),
                assignment.getAssignmentType());
        sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());

    }

    /**
     * 页面跳转 - 技术管理任务
     */
    private void technologyShowForm(IFormView formView, Assignment assignment) {

        // 1.计算当前处理人标识
        boolean currentProcessorFlag = checkCurrentProcessor(assignment.getCurrentProcessorEmployee());

        // 2.非当前处理人 / 已关闭 / 已废止，跳转详情页面
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!currentProcessorFlag
                || AssignmentStatusEnum.CLOSE.equals(assignmentStatus)
                || AssignmentStatusEnum.ABOLISH.equals(assignmentStatus)) {
            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo
                    = getMainTechnologyTaskInfo(
                            assignment, PageConstants.PAGE_BILL_MAIN_TECHNOLOGY_DETAIL_BILL, PageStatusEnum.VIEW);
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 3.草稿，跳转编辑页面
        if (AssignmentStatusEnum.START.equals(assignmentStatus)) {
            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo
                    = getMainTechnologyTaskInfo(
                            assignment, PageConstants.PAGE_BILL_TECHNOLOGY_MANAGEMENT_BILL, PageStatusEnum.EDIT);
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 4.执行中 / 审批中，跳转审批页面
        ApprovePageEnum pageEnum = ApprovePageEnum.getPageIdByTypeStatus(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT, assignmentStatus);
        sendShowFormInstruction(
                formView,
                assignment.getEntityId(),
                assignment.getAssignmentCode(),
                pageEnum.getPageId(),
                pageEnum.getBizObjectCode());
    }

    /**
     * 页面跳转 - 技术管理子任务
     */
    private void technologySubShowForm(IFormView formView, Assignment assignment) {

        // 1.计算当前处理人标识
        boolean currentProcessorFlag = checkCurrentProcessor(assignment.getCurrentProcessorEmployee());

        // 2.非当前处理人 / 已关闭 / 已废止，跳转详情页面
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!currentProcessorFlag
                || AssignmentStatusEnum.CLOSE.equals(assignmentStatus)
                || AssignmentStatusEnum.ABOLISH.equals(assignmentStatus)) {
            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo
                    = getSubTechnologyTaskInfo(assignment);
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 3.执行中 / 审批中，跳转审批页面
        ApprovePageEnum pageEnum = ApprovePageEnum.getPageIdByTypeStatus(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB, assignmentStatus);
        sendShowFormInstruction(
                formView,
                assignment.getEntityId(),
                assignment.getAssignmentCode(),
                pageEnum.getPageId(),
                pageEnum.getBizObjectCode());
    }

    /**
     * 页面跳转 - 故障管理任务
     */
    private void faultShowForm(IFormView formView, Assignment assignment) {

        // 1.计算当前处理人标识
        boolean currentProcessorFlag = checkCurrentProcessor(assignment.getCurrentProcessorEmployee());

        // 2.非当前处理人 / 已关闭，跳转
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!currentProcessorFlag || AssignmentStatusEnum.CLOSE.equals(assignmentStatus)) {
            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo
                    = getForwardFaultHandleInfo(assignment.getId());
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 3.其它状态跳转审批界面

        sendShowFormInstruction(formView, assignment, ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name());
    }

    /**
     * 页面跳转 - 权限申请任务
     */
    private void permissionShowForm(IFormView formView, Assignment assignment) {

        // 1.计算当前处理人标识
        boolean currentProcessorFlag = checkCurrentProcessor(assignment.getCurrentProcessorEmployee());

        // 2.非当前处理人 / 有效 / 失效，跳转详情页面
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!currentProcessorFlag
                || AssignmentStatusEnum.VALID.equals(assignmentStatus)
                || AssignmentStatusEnum.INVALID.equals(assignmentStatus)) {
            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo = getForwardPermissionHandleInfo(
                    assignment,
                    PageConstants.PAGE_BILL_PERMISSION_APPLICATION_APPROVAL_DETAIL,
                    PageStatusEnum.VIEW);
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 3.拟制，跳转编辑页面
        if (AssignmentStatusEnum.DRAFT_APPLICATION.equals(assignmentStatus)
                || AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.equals(assignmentStatus)) {
            // 1.检索权限申请单 - 获取扩展属性
            PermissionApplication application = PermissionApplicationAbility.get(assignment.getEntityId());
            if (Objects.isNull(application)) {
                return;
            }

            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo = getForwardPermissionHandleInfo(
                    assignment,
                    UserTypeEnum.EXTERNAL.name().equals(application.getType())
                            ? PageConstants.PAGE_BILL_EXTERNAL_PERMISSION_APPLICATION
                            : PageConstants.PAGE_BILL_INTERNAL_PERMISSION_APPLICATION,
                    PageStatusEnum.EDIT);
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 4.审批中，跳转审批页面
        ApprovePageEnum pageEnum = ApprovePageEnum.getPageIdByType(AssignmentTypeEnum.PERMISSION_APPLICATION);
        sendShowFormInstruction(
                formView,
                assignment.getEntityId(),
                assignment.getAssignmentCode(),
                pageEnum.getPageId(),
                pageEnum.getBizObjectCode());
    }

    /**
     * 页面跳转 - 打卡复盘任务
     */
    private void clockReviewShowForm(IFormView formView, Assignment assignment) {

        // 1.计算当前处理人标识
        boolean currentProcessorFlag = checkCurrentProcessor(assignment.getCurrentProcessorEmployee());

        // 2.非当前处理人 / 已关闭，跳转详情页面
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!currentProcessorFlag
                || AssignmentStatusEnum.CLOSE.equals(assignmentStatus)) {
            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo
                    = getForwardClockReviewHandleInfo(assignment);
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 3.审批中，跳转审批页面
        ApprovePageEnum pageEnum = ApprovePageEnum.getPageIdByType(AssignmentTypeEnum.CLOCK_REVIEW);
        sendShowFormInstruction(
                formView,
                assignment.getEntityId(),
                assignment.getAssignmentCode(),
                pageEnum.getPageId(),
                pageEnum.getBizObjectCode());
    }

    /**
     * 校验用户当前处理人
     */
    private boolean checkCurrentProcessor(List<Employee> currentProcessors) {
        if (CollectionUtils.isEmpty(currentProcessors)) {
            return false;
        }

        List<String> currentProcessorIds = currentProcessors.stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toList());
        return currentProcessorIds.contains(ContextHelper.getEmpNo());
    }

    /**
     * 指令发送
     * @param formView
     * @param entityId
     */
    private void sendShowFormInstruction(
            IFormView formView,
            String entityId,
            String assignmentCode,
            String pageId,
            String bizObjectCode) {

        // 1.获取审批流信息
        Pair<String, String> flowInfo = getFlowInstanceTaskInfo(entityId, formView);

        // 2.生成跳转链接(assignment.approve.pageUrl 审批中心路径 标题后+(审批))
        String url = String.format(ConfigHelper.get(HREF_URL),
                flowInfo.getLeft(),
                flowInfo.getRight(),
                ContextHelper.getAppId(),
                pageId);

        // 4.包装跳转参数
        FormShowParameter formShowParameter = formView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setPageId(pageId);
        formShowParameter.setBizObjectCode(bizObjectCode);
        formShowParameter.setPkId(entityId);
        formShowParameter.setPageStatus(PageStatusEnum.APPROVE);

        Map<String, Object> customParameters = new HashMap<>();
        customParameters.put(URL, url);
        customParameters.put(PAGE_TITLE, assignmentCode);
        customParameters.put(OPEN_TYPE, OpenTypeEnum.NEW_TAB.getValue());
        formShowParameter.setCustomParameters(customParameters);

        // 5.发送指令
        formView.showForm(formShowParameter);
    }

    /**
     * 指令发送
     */
    private void sendShowFormInstruction(
            IFormView formView,
            Assignment assignment,
            String flowCode) {

        // 1.获取审批流信息
        Pair<String, String> flowInfo = getFlowInstanceTaskInfo(assignment, flowCode);

        // 2.获取审批页面信息
        AssignmentTypeEnum assignmentType
                = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        ApprovePageEnum pageEnum = ApprovePageEnum.getPageIdByType(assignmentType);

        // 3.生成跳转链接(assignment.approve.pageUrl 审批中心路径 标题后+(审批))
        String url = String.format(ConfigHelper.get(HREF_URL),
                flowInfo.getLeft(),
                flowInfo.getRight(),
                ContextHelper.getAppId(),
                pageEnum.getPageId());

        // 4.包装跳转参数
        FormShowParameter formShowParameter = formView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setPageId(pageEnum.getPageId());
        formShowParameter.setBizObjectCode(pageEnum.getBizObjectCode());
        formShowParameter.setPkId(assignment.getEntityId());
        formShowParameter.setPageStatus(PageStatusEnum.APPROVE);

        Map<String, Object> customParameters = new HashMap<>();
        customParameters.put(URL, url);
        customParameters.put(PAGE_TITLE, assignment.getAssignmentCode());
        customParameters.put(OPEN_TYPE, OpenTypeEnum.NEW_TAB.getValue());
        formShowParameter.setCustomParameters(customParameters);

        // 5.发送指令
        formView.showForm(formShowParameter);
    }

    /**
     * 获取流程实例id和任务id
     * left - 审批流ID   right - 审批任务ID
     */
    private static Pair<String, String> getFlowInstanceTaskInfo(String entityId, IFormView formView) {
        // 1.获取流程信息
        List<FlowHandler> flowHandlers = FlowHelper.getFlowHandlerByFlowEntityIds(Lists.newArrayList(entityId));
        if (CollectionUtils.isEmpty(flowHandlers)) {
            return Pair.of(EMPTY_STRING, EMPTY_STRING);
        }
        FlowHandler flowHandler = flowHandlers.get(0);
        Map<String, ApproveTask> approveTaskMap = flowHandler.getApproveTaskList()
                .stream()
                .collect(Collectors.toMap(ApproveTask::getApprover, item -> item, (o1, o2) -> o1));

        // 2.包装审批流程ID + 审批任务ID
        ApproveTask approveTask = approveTaskMap.get(ContextHelper.getEmpNo());
        if (Objects.isNull(approveTask)) {
            formView.showMessage(MessageConsts.BACKLOG_APPROVED, MsgType.WARNING);
            return Pair.of(EMPTY_STRING, EMPTY_STRING);
        }

        return Pair.of(flowHandler.getFlowInstanceId(), approveTask.getTaskId());
    }

    /**
     * 获取流程实例id和任务id
     * left - 审批流ID   right - 审批任务ID
     */
    private static Pair<String, String> getFlowInstanceTaskInfo(
            Assignment assignment,
            String flowCode) {

        // 1.获取流程信息
        InsFlowParam insFlowParam = new InsFlowParam();
        insFlowParam.setAppId(ContextHelper.getUacAppId());
        insFlowParam.setAppCode(ContextHelper.getUacAppId());
        insFlowParam.setHandler(I_VERSION_SYSTEM_NAME);

        BusinessParam businessParam = new BusinessParam();
        businessParam.setBusinessId(assignment.getEntityId());
        businessParam.setFlowCode(flowCode);
        insFlowParam.setBusinessIdParams(Lists.newArrayList(businessParam));
        List<NodeApproverDTO> nodes = ApprovalFlowClient.queryActiveNodeApprover(insFlowParam);
        if (CollectionUtils.isEmpty(nodes)) {
            return Pair.of(EMPTY_STRING, EMPTY_STRING);
        }

        // 2.包装审批流程ID + 审批任务ID
        NodeApproverDTO node = nodes.get(0);
        Map<String, List<NodeApprover>> nodeApproveUsers = node.getActiveNodeInfos().stream()
                .collect(Collectors.toMap(ActiveNodeInfo::getExtendedCode, ActiveNodeInfo::getNodeApprovers));

        List<NodeApprover> currentApproveUsers
                = nodeApproveUsers.getOrDefault(assignment.getCurrentProgress(), Lists.newArrayList());
        Map<String, String> approveTasks = currentApproveUsers.stream()
                .collect(Collectors.toMap(NodeApprover::getApprover, NodeApprover::getTaskId, (v1, v2) -> v1));
        return Pair.of(node.getFlowInstanceId(), approveTasks.get(ContextHelper.getEmpNo()));
    }

    /**
     * 跳转参数 - 网络变更 - 主任务
     */
    private Pair<Map<String, String>, Map<String, Object>> getInteriorMainNetworkChangeInfo(
            Assignment assignment,
            PageStatusEnum pageStatus, String pageId) {

        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID + 页面状态
        basicForwardInfo.put(CommonConstants.PK_ID, assignment.getEntityId());
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(ChangeOrder.class));
        basicForwardInfo.put(CommonConstants.PAGE_ID, pageId);
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, pageStatus.name());

        // 2.包装扩展跳转参数
        // (1) 页面标题 + 任务状态 + 提交人 + 操作类型
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(AssignmentFieldConsts.ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(CommonFieldConsts.CREATE_BY, assignment.getCreateBy());
        customerForwardInfo.put(CommonConstants.OPERATION_KEY, CommonConstants.OPERATION_VIEW);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转参数 - 网络变更 - 批次任务
     *
     * @param assignment 主单据任务
     * @param batchId 批次id
     * @param changeOrderId 网络变更单id
     * @param assignmentType 任务类型（如果为批次传批次类型，如果为主单据转主单据任务类型）
     */
    private Pair<Map<String, String>, Map<String, Object>> getInteriorBatchNetworkChangeInfo(
            Assignment assignment,
            String batchId,
            String changeOrderId,
            List<TextValuePair> assignmentType) {

        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.检索批次任务信息
        List<? extends BaseEntity> batchTaskList = QueryDataHelper.query(BatchTask.class,
                Lists.newArrayList(ID, BATCH_NO, SOURCE, CURRENT_STATUS, BATCH_CODE),
                Lists.newArrayList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, assignment.getEntityId())));

        // 无批次任务信息，继续跳转主任务详情
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return getInteriorMainNetworkChangeInfo(assignment, PageStatusEnum.VIEW, PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL);
        }

        // 2.包装基本跳转参数
        IBatchTask batchTask = BatchTaskAbility.getHyperLinkBatchTask(batchTaskList, assignmentType, batchId, changeOrderId);
        if (batchTask == null) {
            return getInteriorMainNetworkChangeInfo(assignment, PageStatusEnum.VIEW, PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL);
        }

        // 3.检索主任务数据
        Assignment mainAssignment = AssignmentAbility.queryAssignment(
                changeOrderId,
                Lists.newArrayList(AssignmentFieldConsts.ASSIGNMENT_CODE),
                Assignment.class);

        // (1) 主键 + 实体对象编码 + 页面ID + 页面状态
        basicForwardInfo.put(CommonConstants.PK_ID, batchTask.getId());
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(BatchTask.class));
        basicForwardInfo.put(PAGE_ID, DetailPageEnum.NETWORK_CHANGE_BATCH.getPageId());
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 2.包装扩展跳转参数
        // (1) 页面标题 + 任务状态 + 提交人 + 操作类型
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, mainAssignment.getAssignmentCode());
        customerForwardInfo.put(AssignmentFieldConsts.ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(CommonFieldConsts.CREATE_BY, assignment.getCreateBy());
        customerForwardInfo.put(CommonConstants.OPERATION_KEY, CommonConstants.OPERATION_VIEW);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转参数 - 技术管理 - 主任务
     * @param assignment
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getMainTechnologyTaskInfo(
            Assignment assignment,
            String pageId,
            PageStatusEnum pageStatusEnum) {

        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        String entityId = assignment.getEntityId();

        // 1.基础信息
        // (1) 主键 + 实体对象编码 + 页面状态
        basicForwardInfo.put(CommonConstants.PK_ID, entityId);
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(TechnologyManagementOrder.class));
        basicForwardInfo.put(CommonConstants.PAGE_ID, pageId);
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, pageStatusEnum.name());

        // 2.补充信息
        // (1) 页面标题 + 任务状态 + 任务ID
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(AssignmentFieldConsts.ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(ManageTaskFieldConsts.TASK_ID, entityId);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转参数 - 技术管理 - 子任务
     * @param assignment
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getSubTechnologyTaskInfo(Assignment assignment) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        String entityId = assignment.getEntityId();

        // 3.基础信息
        // (1) 页面ID + 页面状态 + 实体对象编码
        basicForwardInfo.put(CommonConstants.PK_ID, entityId);
        basicForwardInfo.put(CommonConstants.PAGE_ID, PageConstants.PAGE_BILL_SUB_TECHNOLOGY_DETAIL_BILL);
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, PageStatusEnum.VIEW.name());
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(ManageSubTaskFlow.class));

        // 4.扩展信息
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(ManageTaskFieldConsts.TASK_ID, entityId);
        customerForwardInfo.put(ManageSubTaskFieldConsts.TASK_PID, assignment.getBillId());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转参数 - 合作方网络变更 - 主任务
     * @param assignment
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getSubcontractMainNetworkChangeInfo(
            Assignment assignment,
            PageStatusEnum pageStatus, String pageId) {

        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID + 页面状态
        basicForwardInfo.put(CommonConstants.PK_ID, assignment.getBillId());
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(SubcontractorChangeOrder.class));
        basicForwardInfo.put(CommonConstants.PAGE_ID, pageId);
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, pageStatus.name());

        // 2.包装扩展跳转参数
        // (1) 页面标题 + 任务状态 + 提交人 + 操作类型
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(AssignmentFieldConsts.ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(CommonFieldConsts.CREATE_BY, assignment.getCreateBy());
        customerForwardInfo.put(CommonConstants.OPERATION_KEY, CommonConstants.OPERATION_VIEW);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转参数 - 合作方网络变更 - 批次任务
     */
    private Pair<Map<String, String>, Map<String, Object>> getSubcontractBatchNetworkChangeInfo(
            Assignment assignment,
            String batchId,
            String changeOrderId,
            List<TextValuePair> assignmentType) {

        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.检索批次任务信息
        List<? extends BaseEntity> batchTaskList = QueryDataHelper.query(SubcontractorBatchTask.class,
                Lists.newArrayList(ID, BATCH_NO, CURRENT_STATUS, BATCH_CODE),
                Lists.newArrayList(new Filter(CHANGE_ORDER_ID, Comparator.EQ, changeOrderId)));

        // 无批次任务信息，继续跳转主任务详情
        if (CollectionUtils.isEmpty(batchTaskList)) {
            return getSubcontractMainNetworkChangeInfo(assignment, PageStatusEnum.VIEW, PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL);
        }

        // 2.包装基本跳转参数
        IBatchTask batchTask = BatchTaskAbility.getHyperLinkBatchTask(batchTaskList, assignmentType, batchId, changeOrderId);
        // 等于null只有一种情况，就是当前是保障批次，而且主单据的批次任务没有操作过，还是跳主页面
        if (batchTask == null) {
            return getSubcontractMainNetworkChangeInfo(assignment, PageStatusEnum.VIEW, PAGE_BILL_SUBCONTRACTOR_NETWORK_CHANGE_APPROVE_DETAIL);
        }

        // 3.检索对应主任务
        Assignment mainAssignment = AssignmentAbility.queryAssignment(
                changeOrderId,
                Lists.newArrayList(AssignmentFieldConsts.ASSIGNMENT_CODE),
                Assignment.class);

        // (1) 主键 + 实体对象编码 + 页面ID + 页面状态
        basicForwardInfo.put(CommonConstants.PK_ID, batchTask.getId());
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(SubcontractorBatchTask.class));
        basicForwardInfo.put(CommonConstants.PAGE_ID, DetailPageEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.getPageId());
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 2.包装扩展跳转参数
        // (1) 页面标题 + 任务状态 + 提交人 + 操作类型
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, mainAssignment.getAssignmentCode());
        customerForwardInfo.put(AssignmentFieldConsts.ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customerForwardInfo.put(CommonFieldConsts.CREATE_BY, assignment.getCreateBy());
        customerForwardInfo.put(CommonConstants.OPERATION_KEY, CommonConstants.OPERATION_VIEW);

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转参数 - 故障管理任务
     * @param assignmentId
     * @return Pair<Map<String, String>, Map<String, Object>>
     */
    private Pair<Map<String, String>, Map<String, Object>> getForwardFaultHandleInfo(String assignmentId) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.检索故障管理任务 - 获取扩展属性
        FaultManagementAssignment assignment
                = AssignmentAbility.querySpecificTypeAssignment(assignmentId, FaultManagementAssignment.class);
        if (Objects.isNull(assignment)) {
            return Pair.of(basicForwardInfo, customerForwardInfo);
        }

        // 2.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID
        basicForwardInfo.put(CommonConstants.PK_ID, assignment.getBillId());
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(FaultManagementOrder.class));
        basicForwardInfo.put(CommonConstants.PAGE_ID, PageConstants.PAGE_BILL_FAULT_MANAGEMENT_BILL);
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 2.包装扩展跳转参数
        // (1) 页面标题 + 当前进展
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, assignment.getAssignmentCode());
        customerForwardInfo.put(AssignmentFieldConsts.CURRENT_PROGRESS, assignment.getCurrentProgress());
        customerForwardInfo.put(AssignmentFieldConsts.ASSIGNMENT_CODE, assignment.getAssignmentCode());
        customerForwardInfo.put(AssignmentFieldConsts.FaultManagementFieldConsts.WAR_ROOM, assignment.getWarRoom());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转参数 - 权限申请任务
     */
    private Pair<Map<String, String>, Map<String, Object>> getForwardPermissionHandleInfo(
            Assignment assignment,
            String pageCid,
            PageStatusEnum pageStatusEnum) {

        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID
        basicForwardInfo.put(CommonConstants.PK_ID, assignment.getEntityId());
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(PermissionApplication.class));
        basicForwardInfo.put(CommonConstants.PAGE_ID, pageCid);
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, pageStatusEnum.name());

        // 2.包装扩展跳转参数
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, assignment.getAssignmentCode());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 跳转参数 - 打卡复盘任务
     */
    private Pair<Map<String, String>, Map<String, Object>> getForwardClockReviewHandleInfo(
            Assignment assignment) {

        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        // 1.包装基本跳转参数
        // (1) 主键 + 实体对象编码 + 页面ID
        basicForwardInfo.put(CommonConstants.PK_ID, assignment.getEntityId());
        basicForwardInfo.put(CommonConstants.BIZ_OBJ_CODE, EntityHelper.getEntityId(ClockInReviews.class));
        basicForwardInfo.put(CommonConstants.PAGE_ID, PageConstants.PAGE_BILL_CLOCK_IN_REVIEW);
        basicForwardInfo.put(CommonConstants.PAGE_STATUS, PageStatusEnum.VIEW.name());

        // 2.包装扩展跳转参数
        customerForwardInfo.put(
                CommonConstants.PAGE_TITLE, assignment.getAssignmentCode());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 页面跳转 - 操作计划任务
     */
    private void operationPlanShowForm(IFormView formView, Assignment assignment) {

        // 1.计算当前处理人标识
        boolean currentProcessorFlag = checkCurrentProcessor(assignment.getCurrentProcessorEmployee());

        // 2.非当前处理人 / 已关闭 跳转详情页面
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!currentProcessorFlag
                || AssignmentStatusEnum.CLOSE.equals(assignmentStatus)) {
            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo
                    = getInteriorOperationPlanInfo(
                    assignment, PageConstants.PAGE_PLAN_OPERATION_BILL, PageStatusEnum.VIEW);
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 3.草稿，跳转编辑页面
        if (AssignmentStatusEnum.START.equals(assignmentStatus)) {
            Pair<Map<String, String>, Map<String, Object>> forwardTargetInfo
                    = getInteriorOperationPlanInfo(
                    assignment, PageConstants.PAGE_PLAN_OPERATION_BILL, PageStatusEnum.EDIT);
            sendShowFormInstruction(formView, forwardTargetInfo.getLeft(), forwardTargetInfo.getRight());
            return;
        }

        // 4.审批中，跳转审批页面
        String pageId = OPERATION_PLAN_APPROVE_NODE_PAGE_ID_MAP.get(assignment.getCurrentProgress());
        sendShowFormInstruction(
                formView,
                assignment.getEntityId(),
                assignment.getAssignmentCode(),
                pageId,
                ApprovePageEnum.OPERATION_PLAN_TASK.getBizObjectCode());
    }

    /**
     * 跳转 操作计划 详情页
     * @param assignment
     * @param pageId
     * @return Pair<Map<String, String>, Map<String, Object>>
     * left - 跳转基本参数map， map - key 页面参数名称  value 页面参数值
     * right - 跳转扩展参数map， map - key 页面参数名称  value 页面参数值
     */
    private Pair<Map<String, String>, Map<String, Object>> getInteriorOperationPlanInfo(
            Assignment assignment,
            String pageId,
            PageStatusEnum pageStatusEnum) {
        Map<String, String> basicForwardInfo = new HashMap<>();
        Map<String, Object> customerForwardInfo = new HashMap<>();

        //1.基础信息
        // 主键 + 实体对象编码 + 页面状态
        basicForwardInfo.put(PK_ID, assignment.getBillId());
        basicForwardInfo.put(BIZ_OBJ_CODE, EntityHelper.getEntityId(PlanOperationOrder.class));
        basicForwardInfo.put(PAGE_ID, pageId);
        basicForwardInfo.put(PAGE_STATUS, pageStatusEnum.name());

        // 2.补充信息
        // 页面标题
        customerForwardInfo.put(PAGE_TITLE, assignment.getAssignmentCode());

        return Pair.of(basicForwardInfo, customerForwardInfo);
    }

    /**
     * 指令发送
     */
    private void sendShowFormInstruction(
            IFormView formView,
            Map<String, String> basicForwardInfo,
            Map<String, Object> customerForwardInfo) {

        // 1.设置指令基本参数
        FormShowParameter formShowParameter = formView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setPkId(basicForwardInfo.get(CommonConstants.PK_ID));
        formShowParameter.setPageId(basicForwardInfo.get(CommonConstants.PAGE_ID));
        formShowParameter.setBizObjectCode(basicForwardInfo.get(CommonConstants.BIZ_OBJ_CODE));
        formShowParameter.setPageStatus(PageStatusEnum.valueOf(basicForwardInfo.get(CommonConstants.PAGE_STATUS)));

        // 2.设置指令扩展参数
        Map<String, Object> customParameters = new HashMap<>();
        customParameters.put(CommonConstants.HIDDEN_OPERATION, true);
        customParameters.put(CommonConstants.FULL_SCREEN, true);
        customParameters.put(CommonConstants.OPEN_TYPE, OpenTypeEnum.NEW_TAB.getValue());
        customParameters.putAll(customerForwardInfo);
        formShowParameter.setCustomParameters(customParameters);

        // 3.发送指令
        formView.showForm(formShowParameter);
    }
}
