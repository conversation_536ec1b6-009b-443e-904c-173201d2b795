package com.zte.iccp.itech.extension.openapi.model.assignment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/3/18 上午10：36
 */
@ApiModel("cnop查询单据信息VO")
@Getter
@Setter
public class CnopChangeOrderVO {

    @ApiModelProperty("单据ID")
    private String changeOrderId;

    @ApiModelProperty("单据编号")
    private String changeOrderNo;

    @ApiModelProperty("单据名称")
    private String changeOrderName;

    @ApiModelProperty("单据状态（快码值）")
    private String changeStatus;

    @ApiModelProperty("单据状态中文")
    private String changeStatusCn;

    @ApiModelProperty("单据状态英文")
    private String changeStatusEn;

    @ApiModelProperty("单据创建人")
    private String createBy;

    @ApiModelProperty("单据预览态url")
    private String url;

    @ApiModelProperty("单据更新时间")
    private Date lastModifiedTime;

    @ApiModelProperty("变更单批次任务信息")
    private List<CnopBatchTaskVO> batchTaskInfo;

}
