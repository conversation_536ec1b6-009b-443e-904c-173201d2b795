package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.subentity.PowerSpecificationModelFieldConsts.*;

/**
 * 电源规格型号子单据体
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/12/25
 */
@ApiModel("电源规格型号")
@Setter
@Getter
@EqualsAndHashCode(callSuper = false)
@BaseSubEntity.Info(value = "entryentity_power_specification_model", parent = ChangeOrder.class)
public class PowerSpecificationModel extends BaseSubEntity {

    @ApiModelProperty("物料名称")
    @JsonProperty(value = MATERIAL_NAME)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String materialName;

    @ApiModelProperty("物料名称其他")
    @JsonProperty(value = MATERIAL_NAME_OTHER)
    private String materialNameOther;

    @ApiModelProperty("硬件版本")
    @JsonProperty(value = HARDWARE_VERSION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String hardwareVersion;

    @ApiModelProperty("硬件版本其他")
    @JsonProperty(value = HARDWARE_VERSION_OTHER)
    private String hardwareVersionOther;

    @ApiModelProperty("数量")
    @JsonProperty(value = POWER_SPECIFICATION_MODEL_COUNT)
    private Integer psmCount;

    @ApiModelProperty("序列号")
    @JsonProperty(value = SERIAL_NUMBER)
    private String serialNumber;

    @ApiModelProperty("软件版本")
    @JsonProperty(value = SOFTWARE_VERSION)
    private String softwareVersion;

    @ApiModelProperty("生产批次")
    @JsonProperty(value = PRODUCTION_BATCH)
    private String productionBatch;

    @ApiModelProperty("备注说明")
    @JsonProperty(value = POWER_SPECIFICATION_MODEL_DESC)
    private String psmDescription;
}
