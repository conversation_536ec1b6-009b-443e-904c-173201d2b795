package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.IdopAbility;
import com.zte.iccp.itech.extension.ability.OperationSchemeAbility;
import com.zte.iccp.itech.extension.ability.changeorder.MultiProdGuaranteeAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.IdopCancelEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteTransactionalEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.springframework.util.StringUtils;

import java.util.Objects;


public class DeleteAssignmentPlugin extends BaseOperationPlugin {

    /**
     * 任务主键ID - dataKey
     */
    private static final String ASSIGNMENT_ID = "pk_25049603";

    /**
     * 删除任务
     * @param executeEvent
     */
    @Override
    public void afterExecuteTransactional(ExecuteTransactionalEvent executeEvent) {
        IFormView formView = getView();

        // 1.获取待删除任务主键
        JSONObject assignmentInfo = (JSONObject) executeEvent.getArgs().get(CommonConstants.VALUE);
        String assignmentId = assignmentInfo.getString(ASSIGNMENT_ID);

        // 2.检索任务信息
        Assignment assignment
                = AssignmentAbility.querySpecificTypeAssignment(assignmentId, Assignment.class);
        if (Objects.isNull(assignment)) {
            formView.showMessage(
                    MessageConsts.ASSIGNMENT_MANAGE_NON_EXISTENT_WARNING, MsgType.WARNING);
            return;
        }

        // 3.根据 任务类型 删除任务
        // todo 待优化历史逻辑
        // 历史逻辑中，技术管理任务 / 网络变更任务的删除合并在一起了
        // 为方便后续扩展和问题处理，需要把对应任务类型的删除逻辑拆出来单独做方法
        // 当前仅兼容 权限申请 任务，后续再处理上述优化逻辑
        AssignmentTypeEnum assignmentType
                = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        if (AssignmentTypeEnum.PERMISSION_APPLICATION.equals(assignmentType)) {
            deletePermissionApplicationAssignment(formView, assignment);
            refreshTable(formView, executeEvent);
            return;
        }

        // 2.任务校验
        if (checkAssignment(formView, assignment)) {
            return;
        }

        // 3.删除变更单对应操作方案
        OperationSchemeAbility.delete(assignment);

        // 4.多产品删除保障任务关联
        MultiProdGuaranteeAbility.multiProdGuaranteeDelete(assignmentId, assignmentType);

        // 5.删除任务
        AssignmentAbility.delete(assignmentId);

        // 6.发送IDOP
        IdopAbility.taskCancelSendIdop(assignment.getBillId(), ApproveFlowCodeEnum.CHANGE_ORDER_COMP_FLOW, IdopCancelEnum.DELETE);

        // 7.根据操作, 刷新对应页面
        refreshTable(formView, executeEvent);
    }

    /**
     * 任务基本校验
     */
    private Boolean checkAssignment(IFormView formView, Assignment assignment) {
        // 2.校验任务状态 - 驳回待启动
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (AssignmentStatusEnum.APPROVE_START.equals(assignmentStatus)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_DELETE_ERROR_STATUS_NETWORK_CHANGE_APPROVE_START, MsgType.WARNING);
            return true;
        }

        // 3.校验任务状态 - 草稿待启动
        if (!AssignmentStatusEnum.START.equals(assignmentStatus)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_MANAGE_DETELE_WARNING, MsgType.WARNING);
            return true;
        }

        // 3.校验任务责任人
        // 网络变更任务 / 分包商网络变更任务 - 责任人   技术管理任务 - 创建人
        // 操作计划任务 - 创建人
        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        String userId = ContextHelper.getEmpNo();
        if (AssignmentTypeEnum.NETWORK_CHANGE.equals(assignmentType)
                || AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(assignmentType)) {
            String responsible = EmployeeHelper.getFirstEmpUIID(assignment.getResponsibleEmployee());
            if (!userId.equals(responsible)) {
                formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_DELETE_ERROR_RESPONSIBLE_NETWORK_CHANGE, MsgType.WARNING);
                return true;
            }
        } else {
            String responsible = assignment.getCreateBy();
            if (!userId.equals(responsible)) {
                formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_DELETE_ERROR_RESPONSIBLE_TECHNOLOGY, MsgType.WARNING);
                return true;
            }
        }

        return false;
    }

    /**
     * 删除任务 - 权限申请
     */
    private void deletePermissionApplicationAssignment(IFormView formView, Assignment assignment) {
        // 1.任务状态校验
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!AssignmentStatusEnum.DRAFT_APPLICATION.equals(assignmentStatus)
                && !AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.equals(assignmentStatus)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_MANAGE_DETELE_WARNING, MsgType.WARNING);
            return;
        }

        // 2.删除任务
        AssignmentAbility.delete(assignment.getId());

        // 3.删除对应实体数据
        String applicationId = assignment.getEntityId();
        PermissionApplicationAbility.delete(applicationId);

        // 4.驳回拟制需终止流程
        if (AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.equals(assignmentStatus)) {
            FlowHelper.revokeFlow(applicationId, ApprovalConstants.PERMISSION_APPLICATION);
        }
    }

    /**
     * 列表刷新
     * @param formView
     * @param executeEvent
     */
    private void refreshTable(IFormView formView, ExecuteTransactionalEvent executeEvent) {
        String operationKey = executeEvent.getOperationKey();
        if (!StringUtils.hasText(operationKey)) {
            return;
        }

        switch (operationKey) {
            case CidConstants.OPERATION_DELETE_FAULT_REVIEW_ASSIGNMENT_CID:
                formView.getClientViewProxy()
                        .refreshData(executeEvent.getPageSessionId(), CidConstants.TABLE_FAULT_REVIEW_ASSIGNMENT_CID, executeEvent.getPageId());
                // 设置故障复盘按钮为展示
                formView.getClientViewProxy().setVisible(CidConstants.BUTTON_CREATE_FAULT_TASK_CID, true);
                break;

            case CidConstants.OPERATION_DELETE_FAULT_RECTIFY_ASSIGNMENT_CID:
                formView.getClientViewProxy()
                        .refreshData(executeEvent.getPageSessionId(), CidConstants.TABLE_FAULT_RECTIFY_ASSIGNMENT_CID, executeEvent.getPageId());
                break;

            default:
                formView.getClientViewProxy()
                        .refreshData(executeEvent.getPageSessionId(), CidConstants.TABLE_ASSIGNMENT_CID, executeEvent.getPageId());
                formView.getClientViewProxy()
                        .refreshData(executeEvent.getPageSessionId(), CidConstants.TABLE_ASSIGNMENT, executeEvent.getPageId());
                break;
        }
    }
}
