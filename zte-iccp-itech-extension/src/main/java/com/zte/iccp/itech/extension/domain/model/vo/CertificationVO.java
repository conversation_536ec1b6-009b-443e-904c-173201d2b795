package com.zte.iccp.itech.extension.domain.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: 李江斌 10318434
 * @date: 2024/7/17
 */


@ApiModel("人员技能认证信息VO")
@Setter
@Getter
public class CertificationVO {

    @JsonProperty("custom_0yuhd80s")
    @ApiModelProperty("认证分类")
    private String certificationCategoryName;

    @JsonProperty("custom_32iodxk9")
    @ApiModelProperty("证书等级")
    private String certificationLevelName;

    @JsonProperty("custom_5jx40ub5")
    @ApiModelProperty("认证产品类型")
    private String certificateName;

    @JsonProperty("custom_9ekztclk")
    @ApiModelProperty("证书号")
    private String certificateCode;

    @JsonProperty("custom_kuplj8j8")
    @ApiModelProperty("认证通过日期")
    private String certificateStartTime;

    @JsonProperty("custom_t475lkva")
    @ApiModelProperty("认证有效日期")
    private String certificateEndTime;

}
