package com.zte.iccp.itech.extension.ability.reportpush;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.utils.CommonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.StringBuilderUtils;
import com.zte.iccp.itech.extension.openapi.model.reportpush.NetChangeReportOrgVO;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;

public class ReportMailContentBuilder {
    private static final String PRODUCT_HTML = "<td style=\"padding-left:4px;border:1px solid #bbb;word-break:break-all\">";
    private static final String PRODUCT_TOTAL_HTML = "<td style=\"padding-right:4px;text-align:right;border:1px solid #bbb;word-break:break-all\">";
    private static final String TOTAL_HTML = "<td style=\"padding-right:4px;text-align:right ;background-color:#D1DFED; font-weight:bold;border:1px solid #bbb;word-break:break-all\"> ";
    private static final String TD_END = "</td>";
    private static final String TR_START = "<tr>";
    private static final String TR_END = "</tr>";
    private static final String PRODUCT_TEAM1 = "${PRODUCT_TEAM1}";
    private static final String PRODUCT_TEAM_INNER = "${PRODUCT_TEAM_INNER}";
    private static final String TOTAL1 = "${TOTAL1}";
    private static final String PRODUCT_TEAM2 = "${PRODUCT_TEAM2}";
    private static final String PRODUCT_TEAM_HZF = "${PRODUCT_TEAM_HZF}";
    private static final String TOTAL2 = "${TOTAL2}";
    private static final String TOTAL3 = "${TOTAL3}";
    private static final String LINE1 = "${LINE1}";
    private static final String LINE2 = "${LINE2}";
    private static final String DISPLAY1 = "${DISPLAY1}";
    private static final String DISPLAY2 = "${DISPLAY2}";
    private static final String SUMMARY_LOCATION = "emailtemplate/reportsummarypush.txt";
    private static final String INTERNAL = "INTERNAL";
    private static final String COPERATOR = "COPERATOR";
    private static final String DISPLAY_HIDEEN = "style = \"display:none\"";

    private final StringBuilder builder = new StringBuilder();

    public String buildSummaryMailContent(List<NetChangeReportOrgVO> reportVos) {
        if (CollectionUtils.isEmpty(reportVos)) {
            return null;
        }

        List<NetChangeReportOrgVO> innerVos = reportVos.stream()
                .filter(vo -> INTERNAL.equals(vo.getType()))
                .collect(Collectors.toList());
        List<NetChangeReportOrgVO> hzfVos = reportVos.stream()
                .filter(vo -> COPERATOR.equals(vo.getType()))
                .collect(Collectors.toList());

        List<String> innerProdHtmls = buildProdHtmls(innerVos);
        String productTeam1 = EMPTY_STRING;
        String productTeamInner = EMPTY_STRING;
        String display1 = DISPLAY_HIDEEN;
        if (!CollectionUtils.isEmpty(innerVos)) {
            productTeam1 = innerProdHtmls.get(0);
            productTeamInner = buildTeamRows(innerProdHtmls);
            display1 = EMPTY_STRING;
        }

        List<String> hzfProdHtmls = buildProdHtmls(hzfVos);
        String productTeam2 = EMPTY_STRING;
        String productTeamHzf = EMPTY_STRING;
        String display2 = DISPLAY_HIDEEN;
        if (!CollectionUtils.isEmpty(hzfVos)) {
            productTeam2 = hzfProdHtmls.get(0);
            productTeamHzf = buildTeamRows(hzfProdHtmls);
            display2 = EMPTY_STRING;
        }

        String total1 = buildTotal(innerVos);
        String total2 = buildTotal(hzfVos);
        String total3 = buildTotal(reportVos);

        String template = CommonUtils.getTemplateString(SUMMARY_LOCATION);
        Map<String, String> replacements = MapUtils.newHashMap(PRODUCT_TEAM1, productTeam1,
                PRODUCT_TEAM_INNER, productTeamInner,
                TOTAL1, total1,
                PRODUCT_TEAM2, productTeam2,
                PRODUCT_TEAM_HZF, productTeamHzf,
                TOTAL2, total2,
                TOTAL3, total3,
                LINE1, String.valueOf(innerProdHtmls.size() + 1),
                LINE2, String.valueOf(hzfProdHtmls.size() + 1),
                DISPLAY1, display1,
                DISPLAY2, display2);
        return StringBuilderUtils.replaceAll(template, replacements);
    }

    private String buildTeamRows(List<String> prodHtmls) {
        builder.setLength(0);
        for (String item : prodHtmls.stream().skip(1).collect(Collectors.toList())) {
            builder.append(TR_START).append(item).append(TR_END);
        }
        return builder.toString();
    }

    private String buildTotal(List<NetChangeReportOrgVO> vos) {
        builder.setLength(0);
        appendTotalCell(vos.stream().mapToInt(vo -> vo.getOperatorOrderCount() + vo.getXjOrderCount()).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getOperatorOrderCount).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getZgyd).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getZgdx).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getZglt).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getZggd).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getOther).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getCritical).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getImportant).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getNormal).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getXjOrderCount).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getSixStar).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getFiveStar).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getFourStar).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getThreeStar).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getTwoStar).sum());
        appendTotalCell(vos.stream().mapToInt(NetChangeReportOrgVO::getOneStar).sum());
        return builder.toString();
    }

    private void appendTotalCell(int value) {
        builder.append(TOTAL_HTML).append(value).append(TD_END);
    }

    private List<String> buildProdHtmls(List<NetChangeReportOrgVO> vos) {
        List<String> prodHtmls = Lists.newArrayList();
        for (NetChangeReportOrgVO vo : vos) {
            builder.setLength(0);
            appendProdCell(vo.getProdTeam());
            appendProdTotalCell(vo.getOperatorOrderCount() + vo.getXjOrderCount());
            appendProdTotalCell(vo.getOperatorOrderCount());
            appendProdTotalCell(vo.getZgyd());
            appendProdTotalCell(vo.getZgdx());
            appendProdTotalCell(vo.getZglt());
            appendProdTotalCell(vo.getZggd());
            appendProdTotalCell(vo.getOther());
            appendProdTotalCell(vo.getCritical());
            appendProdTotalCell(vo.getImportant());
            appendProdTotalCell(vo.getNormal());
            appendProdTotalCell(vo.getXjOrderCount());
            appendProdTotalCell(vo.getSixStar());
            appendProdTotalCell(vo.getFiveStar());
            appendProdTotalCell(vo.getFourStar());
            appendProdTotalCell(vo.getThreeStar());
            appendProdTotalCell(vo.getTwoStar());
            appendProdTotalCell(vo.getOneStar());
            prodHtmls.add(builder.toString());
        }
        return prodHtmls;
    }

    private void appendProdCell(String value) {
        builder.append(PRODUCT_HTML).append(value).append(TD_END);
    }

    private void appendProdTotalCell(int value) {
        builder.append(PRODUCT_TOTAL_HTML).append(value).append(TD_END);
    }
}