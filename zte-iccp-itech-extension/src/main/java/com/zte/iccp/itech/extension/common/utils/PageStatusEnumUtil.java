package com.zte.iccp.itech.extension.common.utils;

import com.zte.km.udm.common.util.StringUtils;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;

/**
 * <AUTHOR>
 * @Date 2025-06-28 09:52
 * @ClassName com.zte.iccp.itech.extension.common.utils.PageStatusEnumUtill
 * @description 页面状态枚举工具类
 * @Version 1.0
 */
public class PageStatusEnumUtil {

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 根据当前状态判断当前页面是否是新增状态
     * @Date 上午9:59 2025/6/28
     * @Param status 当前页面状态
     **/
    public static boolean isNew(String status) {
        return !StringUtils.isEmpty(status)
                && PageStatusEnum.NEW.getValue().equals(status);
    }
}
