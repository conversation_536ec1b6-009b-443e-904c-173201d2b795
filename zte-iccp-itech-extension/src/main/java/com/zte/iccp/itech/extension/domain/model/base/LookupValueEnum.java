package com.zte.iccp.itech.extension.domain.model.base;

import java.util.Arrays;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/22
 */
public interface LookupValueEnum {
    String getLookupCode();

    static <T extends Enum<T> & LookupValueEnum> T fromLookupCode(Class<?> clazz, String lookupCode) {
        //noinspection unchecked
        return Arrays.stream(clazz.getEnumConstants())
                .map(e -> (T) e)
                .filter(e -> e.getLookupCode().equals(lookupCode))
                .findFirst()
                .orElse(null);
    }
}
