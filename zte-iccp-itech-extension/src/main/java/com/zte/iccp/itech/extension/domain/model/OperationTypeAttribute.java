package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFieldConsts.*;

@ApiModel("操作类型(operation_type)")
@Setter
@Getter
@BaseEntity.Info("operate_type_list")
public class OperationTypeAttribute extends BaseEntity {
    @JsonProperty(PRODUCT_TEAM)
    @ApiModelProperty("产品经营团队")
    private List<TextValuePair> operationTeam;

    @JsonProperty(PRODUCT_LINE)
    @ApiModelProperty("产品线")
    private List<TextValuePair> productLine;

    @JsonProperty(OPERATE_TYPE_GROUP)
    @ApiModelProperty("操作类型分组")
    private String operateTypeGroup;

    @JsonProperty(OPERATE_TYPE)
    @ApiModelProperty("操作类型")
    private String operateType;

    @JsonProperty(IS_CUSTOMIZE_OPERATION_REASON)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否定制操作原因")
    private String isCustomizOperationReason;

    @JsonProperty(OPERATE_REASON)
    @ApiModelProperty("操作原因")
    private List<String> operateReason;

    @JsonProperty(IMPORTANCE_LEVEL)
    @ApiModelProperty("重要程度")
    private List<TextValuePair> importanceLevel;

    @JsonProperty(RISK_ASSESSMENT)
    @ApiModelProperty("风险评估")
    private List<TextValuePair> riskAssessment;

    @JsonProperty(OPERATE_LEVEL)
    @ApiModelProperty("操作等级")
    private List<TextValuePair> operateLevel;
}
