package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iccp.itech.extension.plugin.flow.email.InformEmailPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

/**
 * 提交审核-驳回催办
 * 1.驳回到起始节点
 * 2.驳回到终止节点
 * 【网络变更操作催办】姓名+工号+审核驳回+操作主题+（是否紧急），请您及时处理！
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/7/22
 */
public class SubmitRejectAgencyMailToPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        InformEmailPlugin plugin = new InformEmailPlugin();
        return plugin.anyTrigger(body, out);
    }
}
