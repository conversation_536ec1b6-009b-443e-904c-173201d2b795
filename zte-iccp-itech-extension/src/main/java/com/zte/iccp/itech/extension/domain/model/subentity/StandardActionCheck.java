package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.subentity.StandardActionCheckFieldConsts.*;


/**
 * <AUTHOR>
 * @date 2024/9/7 下午7:13
 */
@Setter
@Getter
@BaseSubEntity.Info(value = "standard_action_check", parent = ChangeOrder.class)
public class StandardActionCheck extends BaseSubEntity {

    @JsonProperty(value = STANDARD_ACTION_CHECK_TYPE)
    private String standardActionCheckType;

    @JsonProperty(value = STANDARD_ACTION_CHECK_TYPE_EN)
    private String standardActionCheckTypeEn;

    @JsonProperty(value = STANDARD_ACTION_CHECK_CONTENT)
    private String standardActionCheckContent;

    @JsonProperty(value = STANDARD_ACTION_CHECK_CONTENT_EN)
    private String standardActionCheckContentEn;

    @JsonProperty(value = STANDARD_ACTION_CHECK_RESULT)
    private Object standardActionCheckResult;

    @JsonProperty(value = STANDARD_ACTION_CHECK_RESULT_DESC)
    private String standardActionCheckResultDesc;
}
