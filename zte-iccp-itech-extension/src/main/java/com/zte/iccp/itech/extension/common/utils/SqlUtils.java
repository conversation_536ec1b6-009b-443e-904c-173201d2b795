package com.zte.iccp.itech.extension.common.utils;

import org.springframework.util.StringUtils;

public class SqlUtils {

    /**
     * SQL 注入字符转义
     */
    public static String escapeSqlForLike(String keyword, String... args) {
        if (!StringUtils.hasText(keyword)) {
            return "";
        }

        // 1.需要默认转义的符号
        // 单引号 / 双引号
        keyword = keyword
                .replace("'", "''")
                .replace("\"", "\"\"");

        // 2.业务判断会额外影响的需要转义符号
        for (String arg : args) {
            keyword = keyword.replace(arg, String.format("\\%s", arg));
        }

        return keyword;
    }
}
