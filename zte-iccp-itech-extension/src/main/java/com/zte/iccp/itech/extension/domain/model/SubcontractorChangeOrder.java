package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.common.json.deserializer.*;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.changeorder.TriggerTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerOpReasonEnum;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.WIRE_PRODUCT_CHECKLIST;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.ATTACHMENT_REJECTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.MAIL_COPY;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.OPINION_REJECTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.PERSON_REJECTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.REJECTION_EXTEND_CODE;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.RESULT_REJECTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts.*;

@ApiModel("分包商网络变更操作单")
@Setter
@Getter
@BaseEntity.Info("subcontractor_oc")
public class SubcontractorChangeOrder extends BaseEntity implements IChangeOrder {

    @JsonProperty(value = ORDER_NO)
    @ApiModelProperty("单据编号")
    private String orderNo;

    @JsonProperty(value = OPERATION_SUBJECT)
    @ApiModelProperty("操作主题")
    private String operationSubject;

    @JsonDeserialize(using = CustomerIdDeserializer.class)
    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;

    @JsonProperty(value = CUSTOMER_TYPE_FLAG)
    @ApiModelProperty("客户标识")
    private String customerTypeFlag;

    /** 单据状态 */
    @JsonProperty(value = BILL_STATUS)
    private String billStatus;

    /** 数据来源 */
    @JsonProperty(value = SOURCE)
    private String source;

    @JsonProperty(value = COUNTRY)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText country;

    @JsonProperty(value = PROVINCE)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText province;

    @JsonProperty(value = CITY)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText city;

    @JsonProperty(value = TIME_ZONE)
    @JsonDeserialize(using = LookupValueDeserializer.class)
    private TimeZoneEnum timeZone;

    @JsonProperty(value = OPERATION_TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("操作类型")
    private String operationType;

    @JsonProperty(value = OPERATION_REASON)
    @ApiModelProperty("操作原因")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String operationReason;

    @JsonProperty(value = OPERATION_LEVEL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private OperationLevelEnum operationLevel;

    @JsonProperty(value = OPERATION_TYPE_GROUP)
    @ApiModelProperty("操作类型分组")
    private String operationTypeGroup;

    @JsonProperty(value = TRIGGER_TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private TriggerTypeEnum triggerType;

    @JsonProperty(value = ESTIMATED_INVESTMENT_TIME)
    @ApiModelProperty("操作预计投入人天")
    private Long estimatedInvestmentTime;

    @JsonProperty(value = IS_EMERGENCY_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否紧急操作")
    private BoolEnum isEmergencyOperation;

    @JsonProperty(value = EMERGENCY_OPERATION_REASON)
    @ApiModelProperty("紧急操作原因")
    private String emergencyOperationReason;

    @JsonProperty(value = IS_CHECK_IN)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否需要操作步骤打卡")
    private BoolEnum isCheckIn;

    @JsonProperty(value = NE_LIST_FILE)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    @ApiModelProperty("网元清单")
    private AttachmentFile neListFile;

    @JsonProperty(value = MODEL_PACKAGE)
    @ApiModelProperty("模型包")
    private Object modelPackage;

    @JsonProperty(value = INTERNAL_OPERATION_SOLUTION)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    @ApiModelProperty("内部操作方案")
    private AttachmentFile internalOperationSolution;

    @JsonProperty(value = CUSTOMER_OPERATION_SOLUTION)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    @ApiModelProperty("客户操作方案")
    private AttachmentFile customerOperationSolution;

    @JsonProperty(value = INTERNAL_OPERATION_SOLUTION_AI)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    @ApiModelProperty("内部操作方案（AI生成）")
    private AttachmentFile internalOperationSolutionAI;

    @JsonProperty(value = CUSTOMER_OPERATION_SOLUTION_AI)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    @ApiModelProperty("客户操作方案（AI生成）")
    private AttachmentFile customerOperationSolutionAI;

    @JsonProperty(value = OTHER_ATTACHMENT)
    @ApiModelProperty("其他附件")
    private Object otherAttachment;

    @JsonProperty(value = WIRE_PRODUCT_CHECKLIST)
    @ApiModelProperty("有线产品操作检查单")
    private Object wireProductChecklist;

    @JsonProperty(value = EMERGENCY_OPERATION_ATTACHMENT)
    @ApiModelProperty("紧急操作附件")
    private Object emergencyOperationAttachment;

    @JsonProperty(value = IS_HIGH_RISK_INSTRUCTION)
    @ApiModelProperty("是否涉及高危指令")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isHighRiskInstruction;

    @JsonProperty(value = PRODUCT_ID)
    @ApiModelProperty("产品分类")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String productCategory;

    @JsonProperty(value = ORGANIZATION_ID)
    @ApiModelProperty("代表处")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String responsibleDept;

    @JsonProperty(value = REPRESENTATIVE_OFFICE_PD)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("办事处PD审核人")
    private List<Employee> representativeOfficePd;

    @JsonProperty(value = NETWORK_RESPONSIBLE_PERSON)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("网络负责人")
    private List<Employee> networkResponsiblePerson;

    @JsonProperty(value = NETWORK_RESPONSIBLE_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("网络责任组")
    private List<Employee> networkResponsibleTeam;

    @ApiModelProperty("邮件抄送人")
    @JsonProperty(value = MAIL_COPY)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopyExt;

    @ApiModelProperty("邮件抄送（代表处产品科科长）")
    @JsonProperty(value = RepProdChiefFieldConsts.EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy1Ext;

    @ApiModelProperty("邮件抄送（网络责任人）")
    @JsonProperty(value = NetOwnerFieldConsts.EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy2Ext;

    @ApiModelProperty("邮件抄送（办事处产品经理）")
    @JsonProperty(value = OfficeProdManagerFieldConsts.EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy3Ext;

    @ApiModelProperty("邮件抄送（网络处）")
    @JsonProperty(value = NetDeptFieldConsts.EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy4Ext;

    @ApiModelProperty("邮件抄送（办事处PD）")
    @JsonProperty(value = OfficePdFieldConsts.EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy5Ext;

    @ApiModelProperty("邮件抄送（办事产品科长）")
    @JsonProperty(value = OfficeProdChiefFieldConsts.EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy6Ext;

    @JsonProperty(value = IS_GOV_ENT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGovEnt;

    @ApiModelProperty("是否需要授权文件")
    @JsonProperty(value = IS_AUTHORIZATION_FILE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNeedAuthorizationFile;

    @JsonProperty(value = BUSI_INTERRUPT_DURATION)
    @ApiModelProperty("预计业务中断时长")
    private Integer busiInterruptDuration;

    @ApiModelProperty("带业务操作")
    @JsonProperty(value = IS_BUSINESS_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isBusinessOperation;

    @ApiModelProperty("计划操作开始时间")
    @JsonProperty(value = OPERATION_START_TIME)
    private Date operationStartTime;

    @ApiModelProperty("计划操作结束时间")
    @JsonProperty(value = OPERATION_END_TIME)
    private Date operationEndTime;

    @JsonProperty(value = ChangeOrderFieldConsts.GUARANTEE_MODE)
    @ApiModelProperty("保障方式")
    private List<TextValuePair> guaranteeMode;

    @ApiModelProperty("是否需提供详细保障方案")
    @JsonProperty(value = ChangeOrderFieldConsts.IS_GUARANTEE_SOLUTION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGuaranteeSolution;

    @JsonProperty(value = ChangeOrderFieldConsts.IS_FIRST_APPLICATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否首次应用")
    private BoolEnum isFirstApplication;

    @JsonProperty(value = ChangeOrderFieldConsts.IS_REMOTE_CENTER_SUPPORT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("是否远程中心执行")
    private BoolEnum isRemoteCenterSupport;

    @JsonProperty(value = ChangeOrderFieldConsts.IS_NETWORK_CHANGE_OPER_MATURE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("网络变更操作是否成熟")
    private BoolEnum isNetworkChangeOperMature;

    @JsonProperty(value = ChangeOrderFieldConsts.NetServiceDeptAppFieldConsts.IS_COMMERCIAL_USE_FIRST_TIME)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("此版本是否首次商用")
    private BoolEnum isCommercialUseFirstTime;

    @JsonProperty(value = ChangeOrderFieldConsts.NetServiceDeptAppFieldConsts.IS_FIRST_APPLICATION_NET)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("首次应用")
    private BoolEnum isFirstApplicationNet;

    // ==================== 扩展实体 - 代表处产品科科长 =====================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = RepProdChiefFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultRepProdChief;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = RepProdChiefFieldConsts.APPROVE_OPINION)
    private String approveOpinionRepProdChief;

    @ApiModelProperty("审核人")
    @JsonProperty(value = RepProdChiefFieldConsts.APPROVED_BY)
    private List<Employee> approvedByRepProdChief;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = RepProdChiefFieldConsts.APPROVED_TIME)
    private Date approvedTimeRepProdChief;

    // ======================== 扩展实体 - 网络处 =========================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = NetDeptFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultNetDept;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = NetDeptFieldConsts.APPROVE_OPINION)
    private String approveOpinionNetDept;

    @ApiModelProperty("审核人")
    @JsonProperty(value = NetDeptFieldConsts.APPROVED_BY)
    private List<Employee> approvedByNetDept;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = NetDeptFieldConsts.APPROVED_TIME)
    private Date approvedTimeNetDept;

    // ======================= 扩展实体 - 办事处PD ========================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = OfficePdFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultOfficePd;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = OfficePdFieldConsts.APPROVE_OPINION)
    private String approveOpinionOfficePd;

    @ApiModelProperty("审核人")
    @JsonProperty(value = OfficePdFieldConsts.APPROVED_BY)
    private List<Employee> approvedByOfficePd;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = OfficePdFieldConsts.APPROVED_TIME)
    private Date approvedTimeOfficePd;

    // ===================== 扩展实体 - 办事处产品科长 ======================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = OfficeProdChiefFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultOfficeProdChief;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = OfficeProdChiefFieldConsts.APPROVE_OPINION)
    private String approveOpinionOfficeProdChief;

    @ApiModelProperty("审核人")
    @JsonProperty(value = OfficeProdChiefFieldConsts.APPROVED_BY)
    private List<Employee> approvedByOfficeProdChief;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = OfficeProdChiefFieldConsts.APPROVED_TIME)
    private Date approvedTimeOfficeProdChief;

    // ======================= 扩展实体 - 网络责任人 =======================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = NetOwnerFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultNetOwner;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = NetOwnerFieldConsts.APPROVE_OPINION)
    private String approveOpinionNetOwner;

    @ApiModelProperty("审核人")
    @JsonProperty(value = NetOwnerFieldConsts.APPROVED_BY)
    private List<Employee> approvedByNetOwner;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = NetOwnerFieldConsts.APPROVED_TIME)
    private Date approvedTimeNetOwner;

    @ApiModelProperty("是否需要办事处产品经理审核")
    @JsonProperty(value = NetOwnerFieldConsts.IS_REVIEW_OFFICE_PROD_MANAGER)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isReviewOfficeProdManager;

    @JsonProperty(value = NetOwnerFieldConsts.OFFICE_PROD_MANAGER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("办事处产品经理 - 系统配置")
    private List<Employee> officeProdManager;

    @JsonProperty(value = NetOwnerFieldConsts.OFFICE_PROD_MANAGER_REVIEW_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("办事处产品经理组 - 系统配置")
    private List<Employee> officeProdManagerReviewTeam;

    @ApiModelProperty("办事处产品经理 - 用户选择")
    @JsonProperty(value = NetOwnerFieldConsts.OFFICE_PROD_MANAGER_SELECTED)
    private List<TextValuePair> officeProdManagerSelected;

    @ApiModelProperty("办事处产品经理审核组 - 用户选择")
    @JsonProperty(value = NetOwnerFieldConsts.OFFICE_PROD_MANAGER_REVIEW_TEAM_MULTI)
    private List<TextValuePair> officeProdManagerReviewTeamMulti;

    // ===================== 扩展实体 - 办事处产品经理 ======================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = OfficeProdManagerFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultOfficeProdManager;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = OfficeProdManagerFieldConsts.APPROVE_OPINION)
    private String approveOpinionOfficeProdManager;

    @ApiModelProperty("审核人")
    @JsonProperty(value = OfficeProdManagerFieldConsts.APPROVED_BY)
    private List<Employee> approvedByOfficeProdManager;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = OfficeProdManagerFieldConsts.APPROVED_TIME)
    private Date approvedTimeOfficeProdManager;

    @ApiModelProperty("是否需要升级至网络处")
    @JsonProperty(value = OfficeProdManagerFieldConsts.IS_UPGRADE_NET_DEPARTMENT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isUpgradeNetDepartment;

    @JsonProperty(value = OfficeProdManagerFieldConsts.NET_DEPT_APPROVAL)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("网络处审核人 - 系统配置")
    private List<Employee> netDeptApproval;

    @JsonProperty(value = OfficeProdManagerFieldConsts.NET_DEPT_APPROVE_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    @ApiModelProperty("网络处审核组 - 系统配置")
    private List<Employee> netDeptApproveTeam;

    @ApiModelProperty("网络处审核人 - 用户选择")
    @JsonProperty(value = OfficeProdManagerFieldConsts.NET_DEPT_APPROVAL_SELECTED)
    private List<TextValuePair> netDeptApprovalSelected;

    @ApiModelProperty("网络处审核组 - 用户选择")
    @JsonProperty(value = OfficeProdManagerFieldConsts.NET_DEPT_APPROVE_TEAM_MULTI)
    private List<TextValuePair> netDeptApproveTeamMulti;

    @JsonProperty(value = EMERGENCY_OPERATION_FLAG)
    @ApiModelProperty("紧急操作标记")
    private String emergencyOperationFlag;

    @JsonProperty(value = OPERATION_DESC)
    @ApiModelProperty("操作说明")
    private String operationDesc;

    @JsonProperty(value = CUSTOMER_CONTRACT_PERSON)
    @ApiModelProperty("客户联系人")
    private String customerContractPerson;

    @JsonProperty(value = OPERATION_SOLUTION_DESC)
    @ApiModelProperty("操作方案描述")
    private String operationSolutionDesc;

    @JsonDeserialize(using = CustomerIdDeserializer.class)
    @JsonProperty(value = NETWORK_NAME)
    @ApiModelProperty("客户网络名称")
    private String networkName;

    @JsonProperty(value = AWARE_SUBMISSION)
    @ApiModelProperty("我已知晓案例，确认提交")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum awareSubmission;

    @JsonProperty(value = IMPORTANCE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ImportanceEnum importance;

    @JsonProperty(value = RISK_EVALUATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private RiskEvaluationEnum riskEvaluation;

    @ApiModelProperty("驳回时审核结果，PASS：通过；REJECT：驳回")
    @JsonProperty(value = RESULT_REJECTION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveResultEnum resultRejection;

    @ApiModelProperty("驳回时审核意见")
    @JsonProperty(value = OPINION_REJECTION)
    private String opinionRejection;

    @ApiModelProperty("驳回时审核附件")
    @JsonProperty(value = ATTACHMENT_REJECTION)
    private Object attachmentRejection;

    @ApiModelProperty("驳回时审核人")
    @JsonProperty(value = PERSON_REJECTION)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> personRejection;

    @ApiModelProperty("驳回时节点的自定义编码")
    @JsonProperty(value = REJECTION_EXTEND_CODE)
    private String rejectionExtendCode;

    @ApiModelProperty("附件说明")
    @JsonProperty(value = OTHER_ATTACHMENT_DESC)
    private String otherAttachmentDesc;

    @Override
    @JsonIgnore
    public List<String> getSaGrantFileReceiver() {
        ApproverConfiguration apprConf = new ApproverConfiguration();
        apprConf.setApprovalNode(ApprovalTypeEnum.PERSONNEL_OF_THE_REPRESENTATIVE_OFFICE);
        apprConf.setRole(ApproveRoleEnum.PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE_PROJECT_TD);
        apprConf.setProdTeam(ProductUtils.getTeam(productCategory));
        apprConf.setProdLine(ProductUtils.getLine(productCategory));
        apprConf.setSales(ResponsibleUtils.getSales(responsibleDept));
        apprConf.setOrganizationRegion(ResponsibleUtils.getRegion(responsibleDept));
        apprConf.setResponsibleDeptId(responsibleDept);
        return ApproverConfigAbility.getApprovalPriorityPersons(apprConf, productCategory, 1, null);
    }

    @Override
    @JsonIgnore
    public String getOperationReasonZh() {
        return PartnerOpReasonEnum.valueOf(operationReason).getZhCn();
    }

    @Override
    @JsonIgnore
    public String getOperationReasonEn() {
        return PartnerOpReasonEnum.valueOf(operationReason).getEnUs();
    }

    @Override
    @JsonIgnore
    public BoolEnum getIsFirstTimeApply() {
        return BoolEnum.N;
    }


    /**
     * 在邮件中判断是否紧急操作
     * @param langId 语言
     * @return String
     */
    @Override
    @JsonIgnore
    public String isEmergencyOperationExt(String langId){
        if (this.isEmergencyOperation != null && BoolEnum.Y == isEmergencyOperation) {
            return langId == null || StringUtils.equals(ZH_CN, langId) ? CommonConstants.IS_EMERGENCY_OPERATION_EXT_ZH : CommonConstants.IS_EMERGENCY_OPERATION_EXT_EN;
        }
        return null;
    }

    @Override
    @JsonIgnore
    public String getOperationTypeName(String langId) {
        if (this.operationType != null) {
            LookupValue lookupValue = LookupValueHelper.getLookupValue(
                    LookupValueConstant.OPERATE_TYPE_ENUM,
                    this.operationType);
            if (lookupValue != null) {
                return StringUtils.equals(ZH_CN, langId) ? lookupValue.getMeaningCn() : lookupValue.getMeaningEn();
            }
        }
        return null;
    }

    //  todo 后续分包商如果添加逻辑网元字段需要清空
    @Override
    public String getLogicalNe(){
        return null;
    }
}
