package com.zte.iccp.itech.extension.common.utils;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.zte.iccp.itech.extension.common.json.deserializer.DateDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonMultiLangTextDeserializer;
import com.zte.iccp.itech.extension.common.json.provider.PropValueProvider;
import com.zte.iccp.itech.extension.common.json.serializer.DateSerializer;
import com.zte.iccp.itech.extension.common.json.serializer.DynamicDataEntitySerializer;
import com.zte.iccp.itech.extension.common.json.serializer.IValueEnumSerializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.domain.enums.IValueEnum;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.ddm.domain.helper.service.JsonPluginUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/30
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class JsonUtils {

    private static final ObjectMapper OM;

    static {
        OM = new ObjectMapper();
        OM.setVisibility(OM.getSerializationConfig().getDefaultVisibilityChecker()
                .withFieldVisibility(JsonAutoDetect.Visibility.NONE));
        /* Started by AICoder, pid:ea048x702b5725914f6408bee0696303d041b863 */
        OM.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        /* Ended by AICoder, pid:ea048x702b5725914f6408bee0696303d041b863 */

        /* Started by AICoder, pid:made95c853bd33314a0b0911b073cb0e0da9a747 */
        OM.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        /* Ended by AICoder, pid:made95c853bd33314a0b0911b073cb0e0da9a747 */

        /* Started by AICoder, pid:wc7dey26b6f0a851462708f8901b5407fb846595 */
        SimpleModule module = new SimpleModule();
        module.addSerializer(DynamicDataEntity.class, new DynamicDataEntitySerializer());
        module.addSerializer(PropValueProvider.class, new PropValueProviderSerializer());
        module.addSerializer(Date.class, new DateSerializer());
        module.addSerializer(IValueEnum.class, new IValueEnumSerializer());

        module.addDeserializer(Date.class, new DateDeserializer());
        module.addDeserializer(AttachmentFile.class, new SingletonAttachmentFileDeserializer());
        module.addDeserializer(SingleEmployee.class, new SingletonEmployeeDeserializer());
        module.addDeserializer(MultiLangText.class, new SingletonMultiLangTextDeserializer());

        OM.registerModule(module);
        /* Ended by AICoder, pid:wc7dey26b6f0a851462708f8901b5407fb846595 */
    }

    private static final Map<Class<?>, Map<String, Field>> CLASS_MAP = new HashMap<>();

    public static Map<String, Object> parseObject(Object obj) {
        if (obj == null) {
            return MapUtils.newHashMap();
        }

        return parseObject(obj, new TypeReference<Map<String, Object>>() {});
    }

    @SneakyThrows
    public static <T> T parseObject(Object obj, Class<?> clazz) {
        if (obj == null) {
            return null;
        }

        try {
            //noinspection unchecked
            return (T) OM.readValue(toJsonString(obj), clazz);
        } catch (MismatchedInputException e) {
            if (clazz == String.class) {
                //noinspection unchecked
                return (T) toJsonString(obj);
            }

            throw e;
        }
    }

    @SneakyThrows
    public static <T> T parseObject(Object obj, TypeReference<?> typeRef) {
        if (obj == null) {
            return null;
        }

        //noinspection unchecked
        return (T) OM.readValue(toJsonString(obj), typeRef);
    }

    @SneakyThrows
    public static <T> List<T> parseArray(Object obj, Class<?> clazz) {
        List<T> list = new ArrayList<>();
        if (obj == null) {
            return list;
        }

        for (Object o : toJsonArray(obj)) {
            list.add(parseObject(o, clazz));
        }
        return list;
    }

    @SneakyThrows
    public static <T> List<T> parseArray(Object obj, TypeReference<?> typeRef) {
        List<T> list = new ArrayList<>();
        if (obj == null) {
            return list;
        }

        for (Object o : toJsonArray(obj)) {
            list.add(parseObject(o, typeRef));
        }
        return list;
    }

    @SneakyThrows
    public static Field findField(Class<?> clazz, String name) {
        Map<String, Field> fieldMap = CLASS_MAP.computeIfAbsent(clazz, c -> new HashMap<>());
        if (fieldMap.containsKey(name)) {
            return fieldMap.get(name);
        }

        List<Field> allFields = ReflectUtils.getAllFields(clazz);
        for (Field field : allFields) {
            if (field.getName().equals(name)) {
                field.setAccessible(true);
                fieldMap.put(name, field);
                return field;
            }

            JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
            if (jsonProperty != null && jsonProperty.value().equals(name)) {
                field.setAccessible(true);
                fieldMap.put(name, field);
                return field;
            }
        }

        return null;
    }

    @SneakyThrows
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof String) {
            return (String) obj;
        }

        return OM.writeValueAsString(obj);
    }

    private static List<?> toJsonArray(Object obj) {
        if (obj instanceof List) {
            return (List<?>) obj;
        }

        return JsonPluginUtil.parseArray(toJsonString(obj), Object.class);
    }

    public static boolean isJsonArray(String input) {
        if (input == null) {
            return false;
        }

        String trimmed = input.trim();
        if (trimmed.length() < 2) {
            return false;
        }
        // 快速预判首尾字符
        if (trimmed.charAt(0) != '[' || trimmed.charAt(trimmed.length()-1) != ']') {
            return false;
        }
        try {
            return OM.readTree(trimmed).isArray();
        } catch (Exception e) {
            return false;
        }
    }
}
