package com.zte.iccp.itech.extension.domain.enums.subway;

import com.zte.paas.lcap.common.constant.LangConst;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * warroom地铁图节点枚举
 *
 * <AUTHOR> 10335201
 * @date 2024-08-13 下午3:27
 **/
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum WarRoomSubwayEnum {

    /**
     * 故障发起
     */
    FAULT_INITIATION("1","故障发起", "Fault Occurrence"),
    /**
     * 故障申告
     */
    FAULT_REPORTING("2","故障申告", "Fault Reporting"),
    /**
     * 组建Warroom
     */
    BUILD_WARROOM("3","组建Warroom", "Build Warroom"),
    /**
     * 远程接入
     */
    REMOTE_ACCESS("4","远程接入","Remote Access"),
    /**
     * 人员到位
     */
    PERSONNEL_IN_PLACE("5","人员到位","Personnel in Place"),
    /**
     * 故障定位
     */
    FAULT_LOCATION("6","故障定位","Fault Location"),
    /**
     * 业务恢复
     */
    SERVICE_RESTORATION("7","业务恢复","Service Restoration"),
    ;


    private final String code;

    private final String nameZh;

    private final String nameEn;


    public String getName(String language) {
        return LangConst.ZH_CN.equals(language) ? this.getNameZh() : this.getNameEn();
    }
}
