package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.FormModelProxyHelper;
import com.zte.iccp.itech.extension.common.helper.TextValuePairHelper;
import com.zte.iccp.itech.extension.common.utils.BatchHiddenUtils;
import com.zte.iccp.itech.extension.domain.enums.AiProductTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.BuilderViewEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ENABLED_FLAG;

/**
 * 、仅当计划单的“产品分类”属于 承载网、固网及多媒体  这两个产品经营团队时才显示
 * 2、当满足下列条件之一时，置为是，不可编辑
 * 1）敏感区域 = 是
 * 2）或 操作等级 = 关键
 * 3）或 是否紧急操作 = 是
 * 4）或 是否封网、管控期操作 = 是
 * 5）或 是否集团直管网络 =是
 * 非以上情况时，为“否”
 *
 * @date 2025-07-17 下午4:18
 **/
public class PlanSensitiveOperationPluginValue implements ValueChangeBaseFormPlugin {

    /**
     * loadDataEvent统一封装方法
     */
    @Override
    public void loadData(LoadDataEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = formView.getDataModel();
        showSensitiveAreas(dataModel, formView);
    }

    /**
     * propertyChanged统一封装方法
     *
     * @param args 默认入参，args
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        IDataModel dataModel = args.getModel();
        showSensitiveAreas(dataModel, args.getFormView());
    }

    public void showSensitiveAreas(IDataModel dataModel, IFormView view) {
        String product = FormModelProxyHelper.getTextFirstValue(FIELD_PRODUCT_CID, dataModel);
        if (StringUtils.isEmpty(product)) {
            return;
        }
        //仅当计划单的“产品分类”属于 承载网、固网及多媒体  这两个产品经营团队时才显示
        List<String> prodcts = ConfigHelper.get(AiProductTypeEnum.BN.getProdIdPaths());
        prodcts.addAll(ConfigHelper.get(AiProductTypeEnum.FM.getProdIdPaths()));
        if (prodcts.stream().anyMatch(product::startsWith)) {
            BatchHiddenUtils.hiddenTool(view, BuilderViewEnum.READONLY, Lists.newArrayList(SENSITIVE_OPERATION));
        } else {
            BatchHiddenUtils.hiddenTool(view, BuilderViewEnum.HIDDEN, Lists.newArrayList(SENSITIVE_OPERATION));
            dataModel.setValue(SENSITIVE_OPERATION, null);
            return;
        }

        /** 1）敏感区域 = 是
         * 2）或 操作等级 = 关键
         * 3）或 是否紧急操作 = 是
         * 4）或 是否封网、管控期操作 = 是
         * 5）或 是否集团直管网络 =是*/
        String sensitiveAreas = TextValuePairHelper.getValue(dataModel.getValue(SENSITIVE_AREAS));
        String operationLevel = TextValuePairHelper.getValue(dataModel.getValue(COMPONENT_OPERATION_LEVEL_COMPONENT_CID));
        String emergencyOperation = TextValuePairHelper.getValue(dataModel.getValue(FIELD_IS_EMERGENCY_OPERATION_CID));
        String controlOperation = TextValuePairHelper.getValue(dataModel.getValue(FIELD_IS_NET_CLOSE_OR_CONTROL_OPERATION_CID));
        String provinces = FormModelProxyHelper.getValueLangFirstValue(GRP_DRCT_MNG, OPERATION_OBJECT_TABLE_PROPERTY_KEY, dataModel);
        if (ENABLED_FLAG.equals(sensitiveAreas) || OperationLevelEnum.CRITICAL.getValue().equals(operationLevel)
                || ENABLED_FLAG.equals(emergencyOperation) || ENABLED_FLAG.equals(controlOperation)
                || ENABLED_FLAG.equals(provinces)) {
            dataModel.setValue(SENSITIVE_OPERATION, BoolEnum.Y.getPropValue());
        } else {
            dataModel.setValue(SENSITIVE_OPERATION, BoolEnum.N.getPropValue());
        }
    }
}
