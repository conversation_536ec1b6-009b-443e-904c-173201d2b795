package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 对网络服务部的要求
 *
 * <AUTHOR>
 * @date 2024-04-28 下午4:09
 **/
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ReqForNetServiceDeptEnum {
    /**
     * 仅需保障支持
     */
    NEED_GUARANTEE_SUPPORT,

    /**
     * 需审核/完善方案，保障
     */
    NEED_REVIEW_IMPROVE_SOLUTION_AND_GUARANTEE,

    /**
     * 需验证/完善方案，保障
     */
    NEED_VERIFY_IMPROVE_SOLUTION_AND_GUARANTEE,

    /**
     * 需远程中心提供方案并执行
     */
    NEED_REMOTE_CENTER_PROVIDE_SOLUTION_AND_EXECUTE,
    ;

    public static ReqForNetServiceDeptEnum nilValueOf(String name) {
        return name == null ? null : valueOf(name);
    }
}
