package com.zte.iccp.itech.extension.plugin.form.faultmanagement.common;

/**
 * 故障管理任务实体cid
 */
public class FieldCids {
    //------------------------csc单据详情扩展实体cid-----------------------//
    // 任务号
    public static final String FIELD_CSC_TASK_CODE_CID = "csc_task_code";

    // 请求方类型
    public static final String FIELD_REQUESTER_TYPE_CID = "requester_type";

    // 客户
    public static final String FIELD_CUSTOMER_CID = "customer";

    // 客户所在地区
    public static final String FIELD_CUSTOMER_AREA_CID = "customer_area";

    // 产品
    public static final String FIELD_PRODUCT_CID = "product";

    // 产品经营团队
    public static final String FIELD_PROD_TEAM_CID = "prod_team";

    // 故障描述
    public static final String FIELD_FAULT_DESCRIPTION_CID = "fault_description";

    // 故障级别
    public static final String FIELD_FAULT_LEVEL_CID = "fault_level";

    // 客户关注层级
    public static final String FIELD_CUSTOMER_CONCERN_LEVEL_CID = "customer_concern_level";

    // 单据状态
    public static final String FIELD_TASK_STATUS_CID = "task_status";

    // call_log申告时间
    public static final String FIELD_CALL_LOG_TIME_CID = "call_log_time";

    // 故障发生时间
    public static final String FIELD_FAULT_OCCURRENCE_TIME_CID = "fault_occurrence_time";

    // 故障恢复时间
    public static final String FIELD_RECOVERY_TIME_CID = "fault_recovery_time";

    // 原因分类
    public static final String FIELD_REASON_TYPE_CID = "reason_type";

    // 附件
    public static final String FIELD_ATTACHMENT_CID = "attachment";


    // ------------------------------------------------------------ //

    //------------------------故障管理单实体cid-----------------------//

    // 任务单号
    public static final String FIELD_TASK_CODE_CID = "task_code";

    // 任务主题（call log主题）
    public static final String FIELD_TASK_SUBJECT_CID = "task_subject";

    // WarRoom_id
    public static final String FIELD_WARROOM_ID_CID = "warroom_id";

    // 客户id
    public static final String FIELD_CUSTOMER_ID_CID = "customer_id";

    // 办事处/代表处
    public static final String FIELD_ORGANIZATION_CID = "organization";

    // PDM产品id
    public static final String FIELD_PDM_PRODUCT_ID_CID = "pdm_product_id";

    // 产品经营团队idPath
    public static final String FIELD_PRODUCT_TEAM_CID = "product_team";

    // 产品线id
    public static final String FIELD_PRODUCT_LINE_CID = "product_line";

    // 区域id
    public static final String FIELD_AREA_CODE_CID = "area_code";

    // CSC单据状态
    public static final String FIELD_CSC_TASK_STATUS_CID = "csc_task_status";

    // 故障分析_是否需要故障分析报告
    public static final String FIELD_FAULT_ANALYSIS_UPLOAD_CID = "fault_analysis_upload";

    // 故障复盘_是否需要故障复盘
    public static final String FIELD_FAULT_REVIEW_CID = "fault_review";

    // 故障复盘_不进行故障复盘原因
    public static final String FIELD_REVIEW_REASON_CID = "fault_review_reason";

    // 故障复盘_故障复盘报告
    public static final String FIELD_REVIEW_REPORT_CID = "review_report";

    // 故障复盘_是否故障整改
    public static final String FIELD_FAULT_RECTIFICATION_CID = "fault_rectification";

    // 故障复盘_故障整改责任人
    public static final String FIELD_RECTIFICATION_PERSON_CID = "fault_rectification_person";

    // 故障复盘_不进行故障整改原因
    public static final String FIELD_RECTIFICATION_REASON_CID = "fault_rectification_reason";

    // 客户满意度_反馈方式
    public static final String FIELD_SATISFACTION_CONTENT_TYPE_CID = "satisfaction_content_type";

    // 客户满意度_满意度
    public static final String FIELD_SATISFACTION_CID = "satisfaction";

    // 客户满意度_在线反馈内容
    public static final String FIELD_SATISFACTION_CONTENT_CID = "satisfaction_content";

    // 客户满意度_知会人
    public static final String FIELD_INFORMED_PERSON_CID = "informed_person";

    // 客户满意度_给客户发的邮件截图
    public static final String FIELD_CUSTOMER_MAIN_TO_CID = "customer_mail_to";

    // 客户满意度_客户反馈的邮件截图
    public static final String FIELD_CUSTOMER_MAIL_RECEIVE_CID = "customer_mail_receive";

    // 故障分析_节点提交时间
    public static final String FIELD_ANALYSIS_SUBMIT_TIME_CID = "analysis_submit_time";

    // 故障复盘_节点提交时间
    public static final String FIELD_REVIEW_SUBMIT_TIME_CID = "review_submit_time";

    // 故障整改_节点提交时间
    public static final String FIELD_RECTIFICATION_SUBMIT_TIME_CID = "rectification_submit_time";

    // 客户满意度_节点提交时间
    public static final String FIELD_SATISFACTION_SUBMIT_TIME_CID = "satisfaction_submit_time";

    // ---------------------- WarRoom详情实体cid -----------------------//

    // Warroom名称
    public static final String FIELD_WARROOM_NAME_CID = "warroom_name";

    // CSC单号
    public static final String FIELD_CSC_CODE_CID = "csc_code";

    // 故障级别（warroom）
    public static final String FIELD_FAULT_LEVEL_WARROOM_CID = "fault_level_warroom";

    // 客户（warroom）
    public static final String FIELD_CUSTOMER_WARROOM_CID = "customer_warroom";

    // 客户关注层级（warroom）
    public static final String FIELD_CUSTOEMR_ATTENTION_LEVEL_WARROOM_CID = "customer_attention_level_warroom";

    // 区域（warroom）
    public static final String FIELD_AREA_WARROOM_CID = "area_warroom";

    // 关联省份
    public static final String FIELD_RELATED_PROVINCE_CID = "related_province";

    // 产品（warroom）
    public static final String FIELD_PRODUCT_WARROOM_CID = "product_warroom";

    // 关联产品
    public static final String FIELD_RELATED_PRODUCT_CID = "related_product";

    // 故障发生时间（warroom）
    public static final String FIELD_FAULT_OCCURRENCE_TIME_WARROOM_CID = "fault_occurrence_time_warroom";

    // 故障申告时间
    public static final String FIELD_FAULT_DECLARATION_TIME_CID = "fault_declaration_time";

    // 故障描述
    public static final String FIELD_FAULT_DESC_WARROOM_CID = "fault_desc_warroom";

    // 业务影响范围
    public static final String FIELD_BUSINESS_IMPACT_SCOPE_CID = "business_impact_scope";

    // 状态
    public static final String FIELD_STATUS_CID = "status";

    // 创建人（warroom）
    public static final String FIELD_CREATED_BY_WARROOM_CID = "created_by_warroom";

    // 创建时间（warroom）
    public static final String FIELD_CREATED_TIME_WARROOM_CID = "created_time_warroom";

    // 故障分析提交人
    public static final String FIELD_ANALYSIS_SUBMITTER_CID = "analysis_submitter";

    // 故障复盘提交人
    public static final String FIELD_REVIEW_SUBMITTER_CID = "review_submitter";

    // 故障整改提交人
    public static final String FIELD_RECTIFICATION_SUBMITTER_CID = "rectification_submitter";

    // 客户满意度
    public static final String FIELD_SATISFACTION_SUBMITTER_CID = "satisfaction_submitter";

    // 故障分析提交时间
    public static final String FIELD_ANALYSIS_SUBMITTER_TIME_CID = "analysis_submit_time";



}
