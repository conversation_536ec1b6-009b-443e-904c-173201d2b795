package com.zte.iccp.itech.extension.domain.enums.accredit;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum AccnTypeEnum {

    /** 其他 */
    OTHER("0", "其他", "Other"),

    /** 中国移动 */
    CHINA_MOBILE("1", "中国移动", "China Mobile"),

    /** 中国电信 */
    CHINA_TELECOM("2", "中国电信", "China Telecom"),

    /** 中国联通 */
    CHINA_UNICOM("3", "中国联通", "China Unicom");

    /**
     * 编码
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String zhCn;

    /**
     * 英文描述
     */
    private final String enUs;

    /**
     * 根据枚举编码获取对应枚举
     * @param value
     * @return applicationStatusEnum
     */
    public static AccnTypeEnum fromValue(String value) {
        for (AccnTypeEnum accnTypeEnum : AccnTypeEnum.values()) {
            if (accnTypeEnum.getValue().equals(value)) {
                return accnTypeEnum;
            }
        }

        return null;
    }
}
