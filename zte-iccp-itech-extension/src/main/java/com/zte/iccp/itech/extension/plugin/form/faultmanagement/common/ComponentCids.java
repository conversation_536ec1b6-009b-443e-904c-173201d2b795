package com.zte.iccp.itech.extension.plugin.form.faultmanagement.common;

/**
 * 故障管理任务布局cid
 * @author: 朱小安 10335201
 * @date: 2024/8/13 下午4:20
 */
public class ComponentCids {

    //------------------------故障管理任务详情-地铁图cid-----------------------//
    /**
     * warRoom地铁图组件cid
     */
    public static final String COMPONENT_WARROOM_SUBWAY_CID = "warroom_subway";
    /**
     * iTech Cloud地铁图组件cid
     */
    public static final String COMPONENT_CLOUD_SUBWAY_CID = "cloud_subway";

    //------------------------故障管理任务详情布局cid-----------------------//
    // 任务号
    public static final String COMPONENT_CSC_TASK_CODE_CID = "c_csc_task_code";

    // 请求方类型
    public static final String COMPONENT_REQUESTER_TYPE_CID = "c_requester_type";

    // 客户
    public static final String COMPONENT_CUSTOMER_CID = "c_customer";

    // 客户所在地区
    public static final String COMPONENT_CUSTOMER_AREA_CID = "c_customer_area";

    // 产品
    public static final String COMPONENT_PRODUCT_CID = "c_product";

    // 产品经营团队
    public static final String COMPONENT_PROD_TEAM_CID = "c_prod_team";

    // 故障描述
    public static final String COMPONENT_FAULT_DESCRIPTION_CID = "c_fault_description";

    // 故障级别
    public static final String COMPONENT_FAULT_LEVEL_CID = "c_fault_level";

    // 客户关注层级
    public static final String COMPONENT_CUSTOMER_CONCERN_LEVEL_CID = "c_customer_concern_level";

    // 单据状态
    public static final String COMPONENT_TASK_STATUS_CID = "c_task_status";

    // call_log申告时间
    public static final String COMPONENT_CALL_LOG_TIME_CID = "c_call_log_time";

    // 故障发生时间
    public static final String COMPONENT_FAULT_OCCURRENCE_TIME_CID = "c_fault_occurrence_time";

    // 故障恢复时间
    public static final String COMPONENT_RECOVERY_TIME_CID = "c_fault_recovery_time";

    // 故障解决周期
    public static final String COMPONENT_TROUBLESHOOTING_PERIOD_CID = "c_troubleshooting_period";

    // 故障挂起时长
    public static final String COMPONENT_FAULT_SUSPENSION_DURATION_CID = "c_fault_suspension_duration";

    // 原因分类
    public static final String COMPONENT_REASON_TYPE_CID = "c_reason_type";

    // 附件
    public static final String COMPONENT_ATTACHMENT_CID = "c_attachment";


    // ------------------------------------------------------------ //

    //------------------------故障分析布局cid-----------------------//

    // 是否需要故障分析报告
    public static final String COMPONENT_FAULT_ANALYSIS_UPLOAD_CID = "c_fault_analysis_upload";

    // 不提交故障分析原因
    public static final String COMPONENT_FAULT_ANALYSIS_REASON_CID = "c_fault_analysis_reason";

    // ------------------------------------------------------------ //

    //------------------------故障复盘布局cid-----------------------//

    // 是否需要故障复盘
    public static final String COMPONENT_FAULT_REVIEW_CID = "c_fault_review";

    // 不进行故障复盘原因
    public static final String COMPONENT_FAULT_REVIEW_REASON_CID = "c_fault_review_reason";

    // 创建故障复盘任务（按钮）
    public static final String COMPONENT_CREATE_FAULT_REVIEW_TASK_BUTTON_CID = "c_create_fault_review_task_button";

    // 故障复盘任务表格
    public static final String COMPONENT_FAULT_REVIEW_TASK_TABLE_CID = "c_fault_review_task_table";

    // 故障复盘报告
    public static final String COMPONENT_REVIEW_REPORT_CID = "c_review_report";

    // 故障复盘布局容器
    public static final String COMPONENT_COLUMNSLAYOUT_TEMPLATE_REPORT_CID = "ColumnsLayout_template_report";

    // 是否需要横推整改
    public static final String COMPONENT_FAULT_RECTIFICATION_CID = "c_fault_rectification";

    // 故障复盘_不进行故障整改原因
    public static final String COMPONENT_FAULT_RECTIFICATION_REASON_CID = "c_fault_rectification_reason";

    // 故障复盘_节点提交时间
    public static final String COMPONENT_REVIEW_SUBMIT_TIME_CID = "c_review_submit_time";

    // ------------------------------------------------------------ //

    //------------------------故障整改横推布局cid-----------------------//

    // 创建横推整改任务（按钮）
    public static final String COMPONENT_FAULT_RECTIFICATION_BUTTON_CID = "c_fault_rectification_button";

    // 客户满意度责任人
    public static final String COMPONENT_SATISFACTION_RESPONSIBLE_PERSON_FR_CID = "c_satisfaction_responsible_person_fr";

    // 故障整改任务表格
    public static final String COMPONENT_FAULT_RECTIFICATION_TASK_TABLE_CID = "c_fault_rectification_task_table";

    // 故障整改_节点提交时间
    public static final String COMPONENT_RECTIFICATION_SUBMIT_TIME_CID = "c_rectification_submit_time";

    // ------------------------------------------------------------ //

    //------------------------客户满意度布局cid-----------------------//
    // 反馈方式
    public static final String COMPONENT_SATISFACTION_CONTENT_TYPE_CID = "c_satisfaction_content_type";

    // 满意度
    public static final String COMPONENT_SATISFACTION_CID = "c_satisfaction";

    // 向客户邮件调研故障处理满意度
    public static final String COMPONENT_CUSTOMER_MAIN_TO_CID = "c_customer_mail_to";

    // 收到客户邮件反馈，请截图上传
    public static final String COMPONENT_CUSTOMER_MAIN_RECEIVE_CID = "c_customer_mail_receive";

    // 满意度反馈内容
    public static final String COMPONENT_SATISFACTION_CONTENT_CID = "c_satisfaction_content";

    // 知会人
    public static final String COMPONENT_INFORMED_PERSON_CID = "c_informed_person";

    // 客户满意度_节点提交时间
    public static final String COMPONENT_SATISFACTION_SUBMIT_TIME_CID = "c_satisfaction_submit_time";

    // ------------------------------------------------------------ //

    // 提交按钮
    public static final String COMPONENT_SUBMIT_BUTTON_CID = "c_submit_button";

    // 保存按钮
    public static final String COMPONENT_SAVE_BUTTON_CID = "c_save_button";

    //------------------------高级容器布局cid-----------------------//

    // 故障进展地铁图
    public static final String COMPONENT_FAULT_PROGRESS_SUBWAY_ADC_CID = "fault_progress_subway_advanced_container";

    // 故障管理任务详情
    public static final String COMPONENT_FAULT_TASK_DETAIL_ADC_CID = "fault_task_detail_advanced_container";

    // 故障复盘
    public static final String COMPONENT_FAULT_REVIEW_ADC_CID = "fault_review_advanced_container";

    // 故障整改横推
    public static final String COMPONENT_FAULT_RECTIFICATION_ADC_CID = "fault_rectification_advanced_container";

    // 客户满意度
    public static final String COMPONENT_SATISFACTION_ADC_CID = "satisfaction_advanced_container";

    // ------------------------------------------------------------ //
    // 故障复盘任务_删除按钮唯一标识
    public static final String COMPONENT_DELETE_FAULT_REVIEW_TASK_BUTTON = "c_delete_fault_review_task_button";

    // 故障复盘任务_废止按钮唯一标识
    public static final String COMPONENT_ABOLISH_FAULT_REVIEW_TASK_BUTTON = "c_abolish_fault_review_task_button";

    // 故障整改横推任务_删除按钮唯一标识
    public static final String COMPONENT_DELETE_FAULT_RECTIFICATION_BUTTON = "c_delete_fault_rectification_button";

    // 故障整改横推任务_废止按钮唯一标识
    public static final String COMPONENT_ABOLISH_FAULT_RECTIFICATION_BUTTON = "c_abolish_fault_rectification_button";

    // ------------------------------------------------------------- //

    // 故障复盘_提交人
    public static final String COMPONENT_ANALYSIS_REVIEW_SUBMITTER_CID = "c_review_submitter";

    // 故障横推_提交人
    public static final String COMPONENT_RECTIFICATION_SUBMITTER_CID = "c_rectification_submitter";

    // 客户满意度_提交人
    public static final String COMPONENT_SATISFACTION_SUBMITTER_CID = "c_satisfaction_submitter";
}
