package com.zte.iccp.itech.extension.plugin.flow.plan;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.PlanOperationAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.helper.TransactionHelper;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.PlanOperationOrder;
import com.zte.iccp.itech.extension.domain.model.entity.PlanOperationAssignment;
import com.zte.iccp.itech.extension.domain.model.vo.ChangeOrderSave;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.OPERATION_PLAN_HAS_BEEN_CREATED;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.MultiModeProdConsts.PLAN_OPERATION_ORDER_ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;


/**
 * 操作计划流程结束插件
 * <AUTHOR>
 * @description:
 * @date 2024/4/28 19:35
 * zte-iccp-itech-netchange
 */
@Slf4j
public class PlanEndPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String businessId = body.getBusinessId();
        TransactionHelper.run(() -> {
            PlanOperationAssignment assignment = AssignmentAbility.queryAssignment(businessId, Lists.newArrayList(),
                    PlanOperationAssignment.class);
            if (Objects.isNull(assignment)) {
                return;
            }

            // 1 创建网络变更单任务(校验重复创建)
            IFilter changeOrderIdFilter = new Filter(PLAN_OPERATION_ORDER_ID, Comparator.EQ, businessId);
            List<IFilter> filterList = Lists.newArrayList(changeOrderIdFilter);
            ChangeOrder changeOrder = QueryDataHelper.queryOne(ChangeOrder.class, Lists.newArrayList(ID), filterList);
            if (changeOrder != null) {
                throw new LcapBusiException(MsgUtils.getMessage(OPERATION_PLAN_HAS_BEEN_CREATED));
            }
            ChangeOrderSave changeOrderSave = PlanOperationAbility.planOrder2ChangeOrder(businessId);
            // 2创建网络变跟单任务表
            PlanOperationAbility.saveChangeAssignment(assignment, changeOrderSave);

            //3、更新任务状态 当前进展 当前处理人
            PlanOperationAssignment updateAssignment = new PlanOperationAssignment();
            updateAssignment.setId(assignment.getId());
            updateAssignment.setAssignmentStatus(AssignmentStatusEnum.CLOSE.getValue());
            updateAssignment.setCurrentProgress("");
            updateAssignment.setCurrentProcessorEmployee(new ArrayList<>());
            updateAssignment.setCnNo(changeOrderSave.getOrderNo());
            AssignmentAbility.update(updateAssignment);

            // 4 回写网络变更单no到计划单
            PlanOperationOrder planOperationOrder = new PlanOperationOrder();
            planOperationOrder.setId(businessId);
            planOperationOrder.setOrderNo(changeOrderSave.getOrderNo());
            SaveDataHelper.update(planOperationOrder);

            // todo 发送idop

        });
        return false;
    }

}
