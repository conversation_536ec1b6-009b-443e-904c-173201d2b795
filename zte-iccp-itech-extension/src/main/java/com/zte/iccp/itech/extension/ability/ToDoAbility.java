package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.common.table.DataAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.ConvertUtil;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.backlog.BacklogTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ClientData;
import com.zte.iccp.itech.extension.domain.model.FormatFieldData;
import com.zte.iccp.itech.extension.domain.model.InteriorData;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * 待办任务Ability
 *
 * <AUTHOR> 10335201
 * @date 2024-12-20 下午2:30
 **/
public class ToDoAbility {

    /** 网络变更任务 - 营销 */
    private static final String NETWORK_CHANGE_MARKETING = "custom_t6af690q";

    /** 网络变更任务 - 代表处 */
    private static final String NETWORK_CHANGE_REPRESENTATIVE_OFFICE = "custom_m8u7vz68";

    /** 网络变更任务 - 产品分类 */
    private static final String NETWORK_CHANGE_PRODUCT_CLASSIFICATION = "custom_9dlfxa9c";

    /** 网络变更任务 - 客户标识 */
    private static final String NETWORK_CHANGE_CUSTOMER_IDENTIFICATION = "custom_cetqox55";

    /** 网络变更任务 - 网络名称 */
    public static final String NETWORK_CHANGE_NETWORK = "custom_6vp6mddc";

    /** 网络变更任务 - 当前进展 */
    private static final String NETWORK_CHANGE_CURRENT_PROCESS = "custom_yfx84b63";

    /** 网络变更任务 - 操作结果 */
    public static final String NETWORK_CHANGE_OPERATION_RESULT = "selectfield_naxfeulg";

    /** 网络变更任务 - 任务状态 */
    private static final String COMMON_ASSIGNMENT_STATUS = "fastcodefield_cov2v4j6";


    /**
     * 转换自定义字段数据逻辑，适用于【待我处理-内部网络变更单任务】【我已处理-内部网络变更单任务】【我发起的-内部网络变更单任务】
     */
    public static void convertFieldDataForNetworkChangeTask(JSONArray assignmentArray, String pkIdKey,BacklogTypeEnum backlogTypeEnum) {
        if (assignmentArray.isEmpty()) {
            return;
        }

        // 1.查询任务数据
        List<String> assignmentIds = DataAbility.getSearchRecordsPkId(assignmentArray, pkIdKey);
        List<NetworkChangeAssignment> networkChangeAssignments =
                AssignmentAbility.querySpecificTypeAssignment(assignmentIds, NetworkChangeAssignment.class);
        if (CollectionUtils.isEmpty(networkChangeAssignments)) {
            return;
        }

        // 2.获取外部数据信息
        Map<String, Map<String, String>> supplementaryDataMap = new HashMap<>();
        Pair<ClientData, Map<String, FormatFieldData>> clientDataPair =
                AssignmentAbility.getNetworkChangeClientCondition(networkChangeAssignments);
        AssignmentAbility.queryNetworkChangeClientData(clientDataPair.getLeft());

        // 3.获取内部数据信息
        InteriorData interiorData = AssignmentAbility.getNetworkChangeInteriorCondition(networkChangeAssignments);
        AssignmentAbility.queryNetworkChangeInteriorData(interiorData);
        ClientData clientData = clientDataPair.getLeft();
        Map<String, FormatFieldData> formatFieldDataMap = clientDataPair.getRight();

        // 4.数据包装
        for (NetworkChangeAssignment assignment : networkChangeAssignments) {
            Map<String, String> supplementaryData = new HashMap<>();
            FormatFieldData formatFieldData = formatFieldDataMap.get(assignment.getId());
            if (Objects.isNull(formatFieldData)) {
                continue;
            }
            // 4.1.外部数据转换：营销、代表处、产品分类、网络名称
            transferClientData(clientData, supplementaryData, formatFieldData,backlogTypeEnum);

            // 4.2.内部数据转换：客户标识，当前进展，任务状态，操作结果
            transferInteriorData(interiorData, assignment, supplementaryData);

            // todo 遗留2个自定义字段待转换 (1) 是否超期；(2) 超期时间;（业务暂时未提供明确方案）
            supplementaryDataMap.put(assignment.getId(), supplementaryData);
        }
        // 5.包装补充数据指令
        for (int i = 0; i < assignmentArray.size(); i++) {
            JSONObject assignmentInfo = assignmentArray.getJSONObject(i);
            String id = assignmentInfo.getString(pkIdKey);
            Map<String, String> supplementaryData = supplementaryDataMap.getOrDefault(id, new HashMap<>());
            assignmentInfo.putAll(supplementaryData);
        }
    }
    /**
     * 待办任务--内部网络变更任务--内部数据转换：客户标识，当前进展，任务状态，操作结果
     */
    private static void transferClientData(
            ClientData clientData,
            Map<String, String> supplementaryData,
            FormatFieldData formatFieldData,
            BacklogTypeEnum backlogTypeEnum) {
        // 营销 + 代表处
        supplementaryData.put(NETWORK_CHANGE_MARKETING,
                ConvertUtil.convertNameInfo(clientData.getOrganization(),
                        formatFieldData.getMarketing(), COMMA, false));
        supplementaryData.put(NETWORK_CHANGE_REPRESENTATIVE_OFFICE,
                ConvertUtil.convertNameInfo(clientData.getOrganization(),
                        formatFieldData.getRepresentativeOffice(), COMMA, false));
        // 产品分类
        supplementaryData.put(NETWORK_CHANGE_PRODUCT_CLASSIFICATION,
                ConvertUtil.convertNameInfo(clientData.getProduct(),
                        formatFieldData.getProduct(), COMMA, false));
        // 网络数据
        supplementaryData.put(NETWORK_CHANGE_NETWORK,
                ConvertUtil.convertNameInfo(clientData.getNetwork(),
                        formatFieldData.getNetwork(), COMMA, false));
    }

    /**
     * 待办任务--内部网络变更任务--外部数据转换：客户标识，当前进展，任务状态，操作结果
     */
    private static void transferInteriorData(
            InteriorData interiorData,
            NetworkChangeAssignment assignment,
            Map<String, String> supplementaryData) {
        // 客户标识
        supplementaryData.put(NETWORK_CHANGE_CUSTOMER_IDENTIFICATION,
                ConvertUtil.getDropDownInformation(assignment.getCustomerClassification(), ContextHelper.getLangId()));
        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        // 当前进展
        supplementaryData.put(NETWORK_CHANGE_CURRENT_PROCESS,
                AssignmentAbility.getNetWorkCurrentProcess(assignmentType, assignment.getCurrentProgress()
                )
        );
        // 任务状态 - 批次任务/分包商批次任务
        if (AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)) {
            supplementaryData.put(COMMON_ASSIGNMENT_STATUS,
                    interiorData.getBatchStatus().get(assignment.getApproveBatchTaskId()));
        } else if (AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.equals(assignmentType)) {
            supplementaryData.put(COMMON_ASSIGNMENT_STATUS,
                    interiorData.getSubcontractBatchStatus().get(assignment.getApproveBatchTaskId()));
        }
        // 操作结果，主任务不展示
        if (AssignmentTypeEnum.NETWORK_CHANGE.equals(assignmentType)
                || AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.equals(assignmentType)) {
            supplementaryData.put(NETWORK_CHANGE_OPERATION_RESULT, CommonConstants.EMPTY_STRING);
        }
    }
}
