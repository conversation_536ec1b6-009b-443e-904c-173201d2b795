package com.zte.iccp.itech.extension.domain.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/1 下午4:30
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum OperationSchemeStatusEnum {
    /**
     * 评审中
     */
    UNDER_REVIEW("评审中", "Under review"),
    /**
     * 已发布
     */
    RELEASED("已发布", "Released"),
    /**
     * 已废止
     */
    ABOLISHED("已废止", "Abolished"),
    ;

    private final String zhCn;

    private final String enUs;
}
