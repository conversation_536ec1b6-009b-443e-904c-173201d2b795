package com.zte.iccp.itech.extension.openapi.model.reportpush;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/23 下午4:53
 */
@Setter
@Getter
public class ReportInterOrgDetailVO {

    /**
     * 主单号
     */
    private String coNo;

    /**
     * 批次任务名
     */
    private String batchName;

    /**
     * 批次任务编码
     */
    private String batchCode;

    /**
     * 操作类型
     */
    private String opTypeEn;

    /**
     * 操作负责人中文
     */
    private String respCnName;

    /**
     * 操作负责人英文
     */
    private String respEnName;

    /**
     * 操作负责人工号
     */
    private String respEmpNo;

    /**
     * 操作详情
     */
    private String progressEn;

    /**
     * 计划启动时间
     */
    private String planOperationStartTime;

    /**
     * 首次提单时间
     */
    private String applicationDate;

    /**
     * 产品路径
     */
    private String prodClassPath;

    /**
     * 产品id路径
     */
    private String prodClassIdPath;

    /**
     * 组织路径
     */
    private String organizationPath;

    /**
     * 组织id路径
     */
    private String organizationIdPath;

    /**
     * 组织英文路径
     */
    private String organizationEnPath;

    /**
     * 产品英文路径
     */
    private String prodClassEnPath;

    /**
     * 处理情况枚举
     */
    private String currentStatus;


}
