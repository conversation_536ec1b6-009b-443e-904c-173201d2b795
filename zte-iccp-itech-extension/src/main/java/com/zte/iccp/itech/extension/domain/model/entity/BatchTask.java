package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonAttachmentFileDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.common.json.serializer.PropValueProviderSerializer;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInStateEnum;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.RESULT_REJECTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.TIME_CONFLICT;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.IS_HIGH_RISK_INSTRUCTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;
import static com.zte.iccp.itech.extension.domain.constant.subentity.BatchSummaryFieldConsts.*;

@ApiModel("网络批次任务")
@Setter
@Getter
@BaseEntity.Info("batch_network_assignment")
public class BatchTask extends BaseEntity implements IBatchTask {

    @JsonProperty(value = BATCH_NAME)
    @ApiModelProperty("批次任务名称")
    private String batchName;

    @JsonProperty(value = BatchTaskFieldConsts.BATCH_NO)
    @ApiModelProperty("批次号")
    private String batchNo;

    @JsonProperty(value = BATCH_CODE)
    @ApiModelProperty("批次编码")
    private String batchCode;

    @JsonProperty(value = URGENT_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("紧急操作")
    private BoolEnum urgentFlag;

    @JsonProperty(value = EMERGENCY_OPERATION_FLAG)
    @ApiModelProperty("紧急操作标记")
    private String emergencyOperationFlag;

    @JsonProperty(value = CUSTOMER_VOUCHER_FILE)
    @ApiModelProperty("客户授权凭证")
    private List<MultiAttachmentFile> customerVoucherFile;

    @JsonProperty(value = NOTIFICATION_DESC)
    @ApiModelProperty("通告说明")
    private String notificationDesc;

    @JsonProperty(value = CONTROL_PERIOD_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("封网管控期操作")
    private BoolEnum controlPeriodFlag;

    @JsonProperty(value = NETWORK_SEALING_OPERATION_FLAG)
    @ApiModelProperty("封网管控期操作标记")
    private String networkSealingOperationFlag;

    @JsonProperty(value = CONTROL_PERIOD_REASON)
    @ApiModelProperty("封网管控期操作原因")
    private String controlPeriodReason;

    @JsonProperty(value = CONTROL_PERIOD_FILE)
    @ApiModelProperty("封网管控期操作原因附件")
    private Object controlPeriodFile;

    @JsonProperty(value = PLAN_OPERATION_DAY)
    @ApiModelProperty("操作预计投入人天")
    private Long planOperationDay;

    @JsonProperty(value = PERSON_CHANGE_FLAG)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("涉及人员变更")
    private BoolEnum personChangeFlag;

    @JsonProperty(value = STATUS)
    @ApiModelProperty("单据状态")
    private String status;

    @JsonProperty(value = CURRENT_PROCESSOR)
    @ApiModelProperty("当前处理人")
    private String currentProcessor;

    @JsonProperty(value = GRANT_FILE)
    @ApiModelProperty("授权文件")
    private String grantFile;

    @JsonProperty(value = SA_GRANT_FILE)
    @ApiModelProperty("超级授权文件")
    private String saGrantFile;

    @JsonProperty(value = NEED_SP_GRANT_FILE)
    @ApiModelProperty("是否生成超级授权文件")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum needSpGrantFile;

    @JsonProperty(value = URGENT_RESAON)
    @ApiModelProperty("紧急操作原因")
    private String urgentResaon;

    @JsonProperty(value = OPERATION_PLAN_FILE)
    @ApiModelProperty("内部操作方案")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile operationPlanFile;

    @JsonProperty(value = CUSTOMER_OPERATION_SOLUTION)
    @ApiModelProperty("客户操作方案")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile customerOperationSolution;

    @JsonProperty(value = ATTACHMENT)
    @ApiModelProperty("附件")
    private Object attachment;

    @JsonProperty(value = IS_CHECK)
    @ApiModelProperty("是否需要操作方案打卡")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isCheck;

    @JsonProperty(value = MODEL_PACKAGE)
    @ApiModelProperty("模型包")
    private Object modelPackage;

    @JsonProperty(value = IS_URGENT_FILE)
    @ApiModelProperty("紧急操作附件")
    private Object isUrgenFile;

    @JsonProperty(value = IS_HIGH_RISK_INSTRUCTION)
    @ApiModelProperty("是否涉及高危指令")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isHighRiskInstruction;

    @JsonProperty(value = REMARKS)
    @ApiModelProperty("备注说明")
    private String remarks;

    @JsonProperty(value = CHANGE_ORDER_ID)
    @ApiModelProperty("网络变更单ID")
    private String changeOrderId;

    @JsonProperty(value = CURRENT_STATUS)
    @ApiModelProperty("当前状态")
    private String currentStatus;

    @JsonProperty(value = BATCH_OPERATION_ACCOUNT)
    @ApiModelProperty("批次操作账号")
    private String batchOperationAccount;

    @JsonProperty(value = OPERATION_DESCRIPTION)
    @ApiModelProperty("操作描述")
    private String operationDescription;

    @JsonProperty(value = OPERATION_PLAN_DESC)
    @ApiModelProperty("操作方案描述")
    private String operationPlanDesc;

    @JsonProperty(value = PLAN_OPERATION_START_TIME)
    @ApiModelProperty("确认操作开始时间")
    private Date planOperationStartTime;

    @JsonProperty(value = PLAN_OPERATION_END_TIME)
    @ApiModelProperty("确认操作结束时间")
    private Date planOperationEndTime;

    @JsonProperty(value = IS_CHANGE_ADMIN_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("网络变更单是否走行政领导审核")
    private BoolEnum isChangeAdminApproval;

    @JsonProperty(value = IS_CHANGE_REMOTE_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @ApiModelProperty("网络变更单是否涉及远程中心审核")
    private BoolEnum isChangeRemoteApproval;

    @JsonProperty(value = OPERATION_TYPE)
    @ApiModelProperty("网络变更单-操作类型")
    private String operationType;

    @JsonProperty(value = OPERATION_TYPE_GROUP)
    @ApiModelProperty("操作类型分组")
    private String operationTypeGroup;

    @JsonProperty(value = CHANGE_OPERATION_TIME_START)
    @ApiModelProperty("网络变更单设置计划操作开始时间--审核通过后刷新")
    private Date changeOperationTimeStart;

    @JsonProperty(value = CHANGE_OPERATION_TIME_END)
    @ApiModelProperty("网络变更单设置计划操作结束时间--审核通过后刷新")
    private Date changeOperationTimeEnd;

    @JsonProperty(value = IS_NEED_REMOTE_APPROVAL)
    @ApiModelProperty("是否生成超级授权文件")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNeedRemoteApproval;

    @JsonProperty(value = IS_CHANGE_UPLOAD_ELEMENT)
    @ApiModelProperty("网络变更单-是否上传网元清单")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isChangeUploadElement;

    @JsonProperty(value = APPROVAL_STATUS)
    @ApiModelProperty("审核状态")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum approvalStatus;

    @JsonProperty(value = STEP)
    @ApiModelProperty("步骤条")
    private Integer step;

    @JsonProperty(value = SUSPENDED_TIME)
    @ApiModelProperty("挂起时间")
    private Date suspendedTime;

    @ApiModelProperty("邮件抄送(操作结果确认)")
    @JsonProperty(value = APPROVAL_EMAIL)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> approvalEmail;

    @ApiModelProperty("邮件抄送(反馈操作结果)")
    @JsonProperty(value = RESULT_MAIL)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> resultMail;

    @ApiModelProperty("邮件抄送(发布通告)")
    @JsonProperty(value = EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> emailCc;

    /** 审核结果(操作取消审核) */
    @JsonProperty(value = OC_APPROVE_RESULT)
    private List<TextValuePair> ocApproveResult;

    /** 操作变更说明（操作取消审核） */
    @JsonProperty(value = OC_OPERATION_CHANGE_DESC)
    private String ocOperationChangeDesc;

    /** 审核意见（操作取消审核） */
    @JsonProperty(value = OC_REVIEW_OPINION)
    private String ocReviewOpinion;

    @ApiModelProperty("邮件抄送(操作取消审核)")
    @JsonProperty(value = OC_EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> ocEmailCc;

    @JsonProperty(value = TIME_CONFLICT)
    @ApiModelProperty("时间冲突")
    private String timeConflict;

    @JsonProperty(value = CONFIRM_COPY)
    @ApiModelProperty("确认副本")
    private String confirmCopy;

    @JsonProperty(value = CLOCK_IN_STATE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private ClockInStateEnum clockInState;

    // BEGIN 反馈操作结果
    @JsonProperty(value = IS_CANCELED)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private BoolEnum isCanceled;

    @JsonProperty(value = IS_ROLLBACK_DONE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private BoolEnum isRollbackDone;

    @JsonProperty(value = IS_ONE_TIME_SUCCEED)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    @JsonSerialize(using = PropValueProviderSerializer.class)
    private BoolEnum isOneTimeSucceed;

    @JsonProperty(value = OPERATION_RESULT)
    private List<TextValuePair> operationResult;

    @JsonProperty(value = ACTUAL_OPERATION_START_TIME)
    private Date actualOperationStartTime;

    @JsonProperty(value = ACTUAL_OPERATION_END_TIME)
    private Date actualOperationEndTime;

    @JsonProperty(value = FACT_INTERRUPT_TIME)
    private BigDecimal factInterruptTime;

    @JsonProperty(value = TEST_FINISH_TIME)
    private Date testFinishTime;
    // END 反馈操作结果

    // ================ 扩展实体 - 行政审批 - 代表处产品科长 =================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_REP_PROD_CHIEF)
    private List<TextValuePair> approveResultAdminRepProdChief;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_REP_PROD_CHIEF)
    private String approveOpinionAdminRepProdChief;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_REP_PROD_CHIEF)
    private List<Employee> approvedByAdminRepProdChief;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_REP_PROD_CHIEF)
    private Date approvedTimeAdminRepProdChief;

    // ================ 扩展实体 - 行政审批 - 网络处产品总监 =================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_NET_PROD_DIRECTOR)
    private List<TextValuePair> approveResultAdminNetProdDirector;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_NET_PROD_DIRECTOR)
    private String approveOpinionAdminNetProdDirector;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_NET_PROD_DIRECTOR)
    private List<Employee> approvedByAdminNetProdDirector;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_NET_PROD_DIRECTOR)
    private Date approvedTimeAdminNetProdDirector;

    // ================ 扩展实体 - 行政审批 - 办事处副经理 =================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_REP_DEPUTY_MANAGER)
    private List<TextValuePair> approveResultAdminRepDeputyManager;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_REP_DEPUTY_MANAGER)
    private String approveOpinionAdminRepDeputyManager;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_REP_DEPUTY_MANAGER)
    private List<Employee> approvedByAdminRepDeputyManager;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_REP_DEPUTY_MANAGER)
    private Date approvedTimeAdminRepDeputyManager;

    // ================= 扩展实体 - 行政审批 - 网络处主管经理 ==================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_NET_DEPT_MNG)
    private List<TextValuePair> approveResultAdminNetDeptMng;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_NET_DEPT_MNG)
    private String approveOpinionAdminNetDeptMng;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_NET_DEPT_MNG)
    private List<Employee> approvedByAdminNetDeptMng;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_NET_DEPT_MNG)
    private Date approvedTimeAdminNetDeptMng;

    // ================= 扩展实体 - 行政审批 - 网服部四层 ===================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_NET_SERVCIE_LV4)
    private List<TextValuePair> approveResultAdminNetServiceLV4;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_NET_SERVCIE_LV4)
    private String approveOpinionAdminNetServiceLV4;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_NET_SERVCIE_LV4)
    private List<Employee> approvedByAdminNetServiceLV4;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_NET_SERVCIE_LV4)
    private Date approvedTimeAdminNetServiceLV4;

    // ================= 扩展实体 - 行政审批 - 网服部三层 ===================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_NET_SERVCIE_LV3)
    private List<TextValuePair> approveResultAdminNetServiceLV3;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_NET_SERVCIE_LV3)
    private String approveOpinionAdminNetServiceLV3;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_NET_SERVCIE_LV3)
    private List<Employee> approvedByAdminNetServiceLV3;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_NET_SERVCIE_LV3)
    private Date approvedTimeAdminNetServiceLV3;

    // ================== 扩展实体 - 行政审批 - 研发三层 ====================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_RD_DEPT_LV3)
    private List<TextValuePair> approveResultAdminRdDeptLV3;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_RD_DEPT_LV3)
    private String approveOpinionAdminRdDeptLV3;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_RD_DEPT_LV3)
    private List<Employee> approvedByAdminRdDeptLV3;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_RD_DEPT_LV3)
    private Date approvedTimeAdminRdDeptLV3;

    // ================= 扩展实体 - 行政审批 - 工服三部部长 ==================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_ENG_SERVICE3_LEADER)
    private List<TextValuePair> approveResultAdminEngService3Leader;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_ENG_SERVICE3_LEADER)
    private String approveOpinionAdminEngService3Leader;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_ENG_SERVICE3_LEADER)
    private List<Employee> approvedByAdminEngService3Leader;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_ENG_SERVICE3_LEADER)
    private Date approvedTimeAdminEngService3Leader;

    // ================= 扩展实体 - 行政审批 - 电信服务总监 ==================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_ADMIN_DIR_TELE_SER_DIRECTOR)
    private List<TextValuePair> approveResultAdminDirTeleSerDirector;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_ADMIN_DIR_TELE_SER_DIRECTOR)
    private String approveOpinionAdminDirTeleSerDirector;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_ADMIN_DIR_TELE_SER_DIRECTOR)
    private List<Employee> approvedByAdminDirTeleSerDirector;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_ADMIN_DIR_TELE_SER_DIRECTOR)
    private Date approvedTimeAdminDirTeleSerDirector;

    // ----------非行政审批_审核意见集合------------
    // 远程中心负责人审核
    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_TD_NET_DEPT_APP_SOLU)
    private String approveOpinionTdNetDeptAppSolu;

    // 远程中心负责人
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_TD_NET_DEPT_APP_SOLU)
    private List<TextValuePair> approveResultTdNetDeptAppSolu;

    // ===================== 扩展实体 - 操作计划审核 =======================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = APPROVE_RESULT_REP_PROD_CHIEF)
    private List<TextValuePair> approveResultRepProdChief;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = APPROVE_OPINION_REP_PROD_CHIEF)
    private String approveOpinionRepProdChief;

    @ApiModelProperty("审核人")
    @JsonProperty(value = APPROVED_BY_REP_PROD_CHIEF)
    private List<Employee> approvedByRepProdChief;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = APPROVED_TIME_REP_PROD_CHIEF)
    private Date approvedTimeRepProdChief;

    @JsonProperty(value = APPROVAL_NUM)
    @ApiModelProperty("审批次数")
    private Long approvalNum;

    @JsonProperty(value = HAS_CANCEL_OPERATION)
    @ApiModelProperty("是否有取消操作")
    private String hasCancelOperation;

    @JsonProperty(value = HAS_SAVE_OPERATION)
    @ApiModelProperty("是否有取消操作")
    private String hasSaveOperation;


    @JsonProperty(value = NOTICE_COPY)
    @ApiModelProperty("发布通告副本")
    private String noticeCopy;

    @JsonProperty(value = CHANGE_PLAN_DESC_ZH)
    @ApiModelProperty("操作计划变更说明-中文")
    private String changePlanDescZh;

    @JsonProperty(value = CHANGE_PLAN_DESC_US)
    @ApiModelProperty("操作计划变更说明-英文")
    private String changePlanDescUs;

    @JsonProperty(value = OPERATION_START_TIME_APPLY)
    @ApiModelProperty("计划操作开始时间（申请客户授权凭证记录）")
    private Date operationStartTimeApply;

    @JsonProperty(value = OPERATION_END_TIME_APPLY)
    @ApiModelProperty("结束操作结束时间（申请客户授权凭证记录）")
    private Date operationEndTimeApply;

    @JsonProperty(value = BATCH_OPERATION_ACCOUNT_APPLY)
    @ApiModelProperty("操作账号（申请客户授权凭证记录）")
    private String batchOperationAccountApply;

    @JsonProperty(value = ORGANIZATION_ID)
    @ApiModelProperty("网络变更单代表处ID")
    private String organizationId;

    @JsonProperty(value = SOURCE)
    @ApiModelProperty("数据来源")
    private String source;

    @JsonProperty(value = OPERATION_UPDATE_TIME)
    @ApiModelProperty("操作时间(记录最后一次挂起、取消、发布通告)")
    private Date operationUpdateTime;

    @JsonProperty(value = REJECT_NODE)
    @ApiModelProperty("驳回节点CODE")
    private String rejectNode;

    /**
     * 驳回时审核结果，PASS：通过；REJECT：驳回
     */
    @JsonProperty(value = RESULT_REJECTION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveResultEnum resultRejection;

    /**
     * 驳回时审核意见
     */
    @JsonProperty(value = AUDIT_REJECTION)
    private String opinionRejection;

    /**
     * 驳回时审核人
     */
    @JsonProperty(value = PERSON_REJECTION)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> personRejection;

    /**
     * 驳回时审核意见附件
     */
    @JsonProperty(ATTACHMENT_REJECTION)
    private Object attachmentRejection;

    /** 操作变更说明 */
    @JsonProperty(value = OPERATION_CHANGE_DESC)
    private String operationChangeDesc;

    /** 审核说明附件 */
    @JsonProperty(value = BATCH_APPROVAL_DESCRIPTION_FILE)
    private Object approvalDescAttachment;

    /** 操作结果审核标识 */
    @JsonProperty(value = RESULT_REVIEW_FLAG)
    private String resultReviewFlag;
}
