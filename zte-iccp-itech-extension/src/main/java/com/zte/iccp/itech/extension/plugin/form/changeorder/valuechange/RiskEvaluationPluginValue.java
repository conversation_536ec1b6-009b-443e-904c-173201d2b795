package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.GradingGuaranteeAbility;
import com.zte.iccp.itech.extension.ability.OperationTypeAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.OptionBuildUtils;
import com.zte.iccp.itech.extension.common.utils.PageStatusEnumUtil;
import com.zte.iccp.itech.extension.domain.model.OperationTypeAttribute;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.PageStatusAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.OperationTypeFieldConsts.*;

/**
 * 触发时机：网络变更单页面上的业务字段填写时，触发字段包括：产品分类，代表处，是否政企，操作类型，客户，国家，网络名称，产品型号
 * 插件功能：当变更单中字段填写时触发【风险评估】字段自动计算
 * <AUTHOR> 10335201
 * @date 2024-05-13 下午4:12
 **/
public class RiskEvaluationPluginValue implements ValueChangeBaseFormPlugin {

    /**
     * 编辑页面初始化时触发
     * @param args
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/7/31 下午5:22
     */
    @Override
    public void loadData(LoadDataEventArgs args) {
        IDataModel dataModel = args.getModel();
        IFormView formView = args.getFormView();
        String pageStatus = formView.getFormShowParameter().getPageStatus().getValue();
        if (!PageStatusEnumUtil.isNew(pageStatus)) {
            return;
        }
        invoke(dataModel, formView);
    }

    /**
     * 新增页面中数据change时触发
     * @param args 默认入参，ValueChangedEventArgs
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/6/21 上午9:21
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        IDataModel dataModel = args.getModel();
        IFormView formView = args.getFormView();
        invoke(dataModel, formView);
    }

    private void invoke(IDataModel dataModel, IFormView formView) {
        // 如果页面是查看态
        String pageStatus = formView.getFormShowParameter().getPageStatus().getValue();
        if (PageStatusEnum.VIEW.getValue().equals(pageStatus)) {
            formView.getClientViewProxy().setControlState(COMPONENT_RISK_EVALUATION_CID, new PageStatusAttributeBuilder().readOnly().build());
            return;
        }

        // 主表单--责任单位
        Object organizationIdObj = dataModel.getValue(FIELD_ORGANIZATION_CID);

        // 主表单--产品分类
        Object productIdObj = dataModel.getValue(FIELD_PRODUCT_CID);

        // 主表单--是否政企
        Object isGovernmentEnterpriseObj = dataModel.getValue(FIELD_IS_GOV_ENT_CID);

        // 主表单--操作类型
        Object operationTypeObj = dataModel.getValue(FIELD_OPERATION_TYPE_CID);

        if (ObjectUtils.isEmpty(organizationIdObj) || ObjectUtils.isEmpty(productIdObj) || ObjectUtils.isEmpty(isGovernmentEnterpriseObj) || ObjectUtils.isEmpty(operationTypeObj)) {
            return;
        }

        List<TextValuePair> organizationJsonObjects = JsonUtils.parseArray(organizationIdObj, TextValuePair.class);
        String organizationIdPath = organizationJsonObjects.get(INTEGER_ZERO).getValue();
        List<TextValuePair> productJsonObjects = JsonUtils.parseArray(productIdObj, TextValuePair.class);
        String productIdPath = productJsonObjects.get(INTEGER_ZERO).getValue();
        List<TextValuePair> isGovernmentEnterpriseList = JsonUtils.parseArray(isGovernmentEnterpriseObj, TextValuePair.class);
        String isGovernmentEnterprise = isGovernmentEnterpriseList.get(INTEGER_ZERO).getValue();
        List<TextValuePair> operationTypeList = JsonUtils.parseArray(operationTypeObj, TextValuePair.class);
        String operationType = operationTypeList.get(INTEGER_ZERO).getValue();
        // 主表单--业务中断时长
        String serviceDisconnectDuration = PropertyValueConvertUtil.getString(dataModel.getValue(FIELD_SERVICE_DISCONNECT_DURATION_CID));
        // 主表单--操作类型分组
        String operationTypeGroup = PropertyValueConvertUtil.getString(dataModel.getValue(FIELD_OPERATION_TYPE_GROUP_CID));
        // 主表单--是否特殊场景
        Object isSpecialScenarioObj = dataModel.getValue(FIELD_IS_SPECIAL_SCENARIO_CID);
        // 主表单--是否首次操作
        Object isFirstApplicationObj = dataModel.getValue(FIELD_IS_FIRST_APPLICATION_CID);
        String isSpecialScenario = null;
        if(!ObjectUtils.isEmpty(isSpecialScenarioObj)){
            List<TextValuePair> isSpecialScenarioList = JsonUtils.parseArray(isSpecialScenarioObj, TextValuePair.class);
            isSpecialScenario = isSpecialScenarioList.get(INTEGER_ZERO).getValue();
        }
        String isFirstApplication = null;
        if(!ObjectUtils.isEmpty(isFirstApplicationObj)){
            List<TextValuePair> isFirstApplicationList = JsonUtils.parseArray(isFirstApplicationObj, TextValuePair.class);
            isFirstApplication = isFirstApplicationList.get(INTEGER_ZERO).getValue();
        }
        // 提取特殊场景系数、业务中断时长系数、操作类型系数
        List<String> productOperationTeamList = Lists.newArrayList(productIdPath.split(FORWARD_SLASH)[INTEGER_ZERO]+ FORWARD_SLASH);
        List<String> productSubcategoryList = Lists.newArrayList(productIdPath);
        double isSpecialScenarioFactor = ENABLED_FLAG.equals(isSpecialScenario) ? DOUBLE_THREE_QUARTERS : INTEGER_ONE;
        double businessInterruptionDurationFactor = GradingGuaranteeAbility.queryBusinessInterruptionDurationFactor(serviceDisconnectDuration,productOperationTeamList);
        double operationTypeFactor = GradingGuaranteeAbility.queryOperationTypeFactor(productSubcategoryList,operationType);

        // 风险评估最终结果，0：代表用户自动选择（默认），1：一般；2：重要；3：关键
        double riskEvaluationResult = DOUBLE_ZERO;

        // 先根据不同的责任单位来拆分业务场景，
        // 1、（（责任单位属于【国内营销】||【工程服务经营部-工程服务三部】）&&【是否政企】选择是）|| 工程服务经营部/非工程服务国内部
        // 2、（责任单位属于【国内营销】||【工程服务经营部-工程服务三部】）&&【是否政企】选择否
        // 3、其他


        // 抽取判断条件：责任单位属于【国内营销】||【工程服务经营部-工程服务国内部】
        boolean organizationFlag = organizationIdPath.startsWith(ConfigHelper.get(DOMESTIC_SALES_ORG_CODE_PATH)) || organizationIdPath.startsWith(ConfigHelper.get(ENGINEERING_SERVICE_DEPT_DOMESTIC_ORG_CODE_PATH));
        // 抽取判断条件：责任单位属于工程服务经营部-非工程服务国内部（是工程服务经营部下面除了工程服务国内部其他的责任单位）
        boolean organizationFlag1 = organizationIdPath.startsWith(ConfigHelper.get(ENGINEERING_SERVICE_DEPT_ORG_CODE_PATH)) && !organizationIdPath.startsWith(ConfigHelper.get(ENGINEERING_SERVICE_DEPT_DOMESTIC_ORG_CODE_PATH));

        if ((organizationFlag && ENABLED_FLAG.equals(isGovernmentEnterprise)) || organizationFlag1) {
            riskEvaluationResult = getRiskEvaluationResult1(productIdPath, isSpecialScenarioFactor, businessInterruptionDurationFactor, operationTypeFactor);
        } else if (organizationFlag && DISABLED_FLAG.equals(isGovernmentEnterprise)) {
            riskEvaluationResult = getRiskEvaluationResult2(isFirstApplication, operationTypeGroup, productIdPath, isSpecialScenarioFactor, businessInterruptionDurationFactor, operationTypeFactor);
        }
        // 根据result数据给操作等级、风险评估字段赋值或保持用户手动填写
        setValueAndStatus(dataModel, formView,riskEvaluationResult,productIdPath,operationType);
    }

    /**
     * 给【风险评估】字段赋值以及设置显示属性
     * @param dataModel
     * @param formView
     * @param riskEvaluationResult
     * @param productIdPath
     * @param operationType
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/7/31 下午5:20
     */
    private void setValueAndStatus(IDataModel dataModel,IFormView formView,double riskEvaluationResult,String productIdPath,String operationType) {
        // 满足自动计算条件，result不是默认值0，则给【操作等级】赋值并且设置为禁用组件
        if (riskEvaluationResult != DOUBLE_ZERO) {
            double riskEvaluation = riskEvaluationResult > DOUBLE_THREE ? DOUBLE_THREE : riskEvaluationResult;
            String str = String.valueOf((int) Math.floor(riskEvaluation));
            dataModel.setValue(FIELD_RISK_EVALUATION_CID,OptionBuildUtils.getRisk(str));
            formView.getClientViewProxy().setControlState(COMPONENT_RISK_EVALUATION_CID, new PageStatusAttributeBuilder().disable().build());
            return;
        }
        // 不满足自动计算条件，去选项配置里查询当前操作类型的风险评估默认值进行赋值，如果没有默认值则赋空值。并且设置为普通组件，让用户可以手动选择（产品线+操作类型为联合主键）
        List<String> fieldList = Lists.newArrayList(RISK_ASSESSMENT,OPERATE_LEVEL,IMPORTANCE_LEVEL);
        List<IFilter> conditionFilters = Lists.newArrayList();
        List<String> productLines = Lists.newArrayList(
                productIdPath.split(FORWARD_SLASH)[INTEGER_ZERO] + FORWARD_SLASH + productIdPath.split(FORWARD_SLASH)[INTEGER_ONE] + FORWARD_SLASH);
        conditionFilters.add(new Filter(PRODUCT_LINE, Comparator.IN, productLines));
        conditionFilters.add(new Filter(OPERATE_TYPE, Comparator.EQ, operationType));
        // 基于产品线，从产品操作类型维护基础资料获取操作类型
        List<OperationTypeAttribute> operationTypeAttributes = OperationTypeAbility.query(fieldList, conditionFilters);
        if(CollectionUtils.isEmpty(operationTypeAttributes)||CollectionUtils.isEmpty(operationTypeAttributes.get(INTEGER_ZERO).getRiskAssessment())){
            dataModel.setValue(FIELD_RISK_EVALUATION_CID,new OptionsBuilder().build());
        }else{
            List<TextValuePair> riskAssessment = operationTypeAttributes.get(INTEGER_ZERO).getRiskAssessment();
            String val = riskAssessment.get(INTEGER_ZERO).getValue();
            dataModel.setValue(FIELD_RISK_EVALUATION_CID, OptionBuildUtils.getRisk(val));
        }
        formView.getClientViewProxy().setControlState(COMPONENT_RISK_EVALUATION_CID, new PageStatusAttributeBuilder().normal().build());

    }

    /**
     * 自动计算风险评估
     * 1.【算力及核心网】：操作类型系数*中断时长(向下取整)
     * 2.【承载网】：（操作类型系数*中断时长系数 * （if 特殊场景=Y then 1.5 else 1 end）(向下取整)
     * 3.【固网及多媒体】：同上
     * 4.【视频-多媒体视讯系统】：同上
     * 5.其他：手动选择
     */
    private double getRiskEvaluationResult1(
            String productIdPath,
            double isSpecialScenarioFactor,
            double businessInterruptionDurationFactor,
            double operationTypeFactor){
        if(productIdPath.startsWith(ConfigHelper.get(CCN_PROD_ID_PATH))){
            return Math.floor(operationTypeFactor * businessInterruptionDurationFactor);
        } else if (productIdPath.startsWith(ConfigHelper.get(BN_PROD_ID_PATH)) || productIdPath.startsWith(ConfigHelper.get(FM_PROD_ID_PATH)) || productIdPath.startsWith(ConfigHelper.get(MMVS_PROD_ID_PATH))) {
            return Math.floor(operationTypeFactor * businessInterruptionDurationFactor * isSpecialScenarioFactor);
        }
        return DOUBLE_ZERO;
    }

    /**
     *   自动计算风险评估
     *   1、产品分类属于【算力及核心网】：a、操作类型分类属于配置保障：一般；b、【是否首次应用】选项选择是：关键；c、其他条件下：（操作类型系数） 向下取整
     *   2、产品分类属于【承载网】或【固网及多媒体】或【视频-多媒体视讯系统】：a、操作类型分类属于配置保障：一般；b、【是否首次应用】选项选择是：关键；c、其他条件下：（操作类型系数*中断时长系数 * （if 特殊场景=Y then 1.5 else 1 end）） 向下取整
     *   3、产品分类属于其他：手动选择
     */
    private double getRiskEvaluationResult2(
            String isFirstApplication,
            String operationTypeGroup,
            String productIdPath,
            double isSpecialScenarioFactor,
            double businessInterruptionDurationFactor,
            double operationTypeFactor) {
        if(productIdPath.startsWith(ConfigHelper.get(CCN_PROD_ID_PATH))){
            return getRiskEvaluationResult3(isFirstApplication, operationTypeGroup, operationTypeFactor);
        } else if (productIdPath.startsWith(ConfigHelper.get(BN_PROD_ID_PATH)) || productIdPath.startsWith(ConfigHelper.get(FM_PROD_ID_PATH))||productIdPath.startsWith(ConfigHelper.get(MMVS_PROD_ID_PATH))) {
            return getRiskEvaluationResult4(isFirstApplication, operationTypeGroup, isSpecialScenarioFactor, businessInterruptionDurationFactor, operationTypeFactor);
        }
        return DOUBLE_ZERO;
    }

    /**
     *   自动计算风险评估
     *   2、产品分类属于【承载网】或【固网及多媒体】：a、操作类型分类属于配置保障：一般；b、【是否首次应用】选项选择是：关键；c、其他条件下：（操作类型系数*中断时长系数 * （if 特殊场景=Y then 1.5 else 1 end）） 向下取整
     */
    private double getRiskEvaluationResult4(
            String isFirstApplication,
            String operationTypeGroup,
            double isSpecialScenarioFactor,
            double businessInterruptionDurationFactor,
            double operationTypeFactor) {
        if(ENABLED_FLAG.equals(isFirstApplication)){
            return INTEGER_THREE;
        }
        if(StringUtils.isNotBlank(operationTypeGroup) && COOPERATION_GUARANTEE.equals(operationTypeGroup)){
            return INTEGER_ONE;
        }
        return Math.floor(operationTypeFactor * businessInterruptionDurationFactor * isSpecialScenarioFactor);
    }

    /**
     *   自动计算风险评估
     *   1、产品分类属于【算力及核心网】：a、操作类型分类属于配置保障：一般；b、【是否首次应用】选项选择是：关键；c、其他条件下：（操作类型系数） 向下取整
     */
    private double getRiskEvaluationResult3(
            String isFirstApplication,
            String operationTypeGroup,
            double operationTypeFactor) {
        if(ENABLED_FLAG.equals(isFirstApplication)){
            return INTEGER_THREE;
        }
        if(StringUtils.isNotBlank(operationTypeGroup) && COOPERATION_GUARANTEE.equals(operationTypeGroup)){
            return INTEGER_ONE;
        }
        return Math.floor(operationTypeFactor);

    }
}
