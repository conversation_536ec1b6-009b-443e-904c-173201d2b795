package com.zte.iccp.itech.extension.common.helper;

import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * 网络变更操作单设置属性工具插件
 *
 * <AUTHOR> 10347404
 * @date 2024-05-09
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class FormModelProxyHelper {
    /**
     * 写下拉组件
     * @param componentCid 页面布局唯一标识
     * @param formView formView
     * @param lookupValues 快码值
     */
    public static void setFormModelProxyProps(String componentCid,
                                              IFormView formView,
                                              List<LookupValue> lookupValues) {
        OptionsBuilder optionsBuilder = new OptionsBuilder();
        for (LookupValue lookupValue : lookupValues) {
            optionsBuilder
                    .addOption(new Option(lookupValue.getLookupCode(),
                            new Text(lookupValue.getMeaningCn(), lookupValue.getMeaningEn())));
        }
        formView.getClientViewProxy().setOptions(componentCid, optionsBuilder.build());
    }

    /**
     * 取text对象对应的第一个Value值
     * @param cid 组件标识
     * @param dataModel dataModel
     * @return  String text对象对应的第一个Value值
     */
    public static String getTextFirstValue(String cid, IDataModel dataModel) {
        Object textObject = dataModel.getValue(cid);
        if (!(textObject instanceof List)) {
            return null;
        }

        List<TextValuePair> textValuePairList = JsonUtils.parseArray(textObject, TextValuePair.class);
        return CollectionUtils.isEmpty(textValuePairList) ? null : textValuePairList.get(0).getValue();
    }

    public static String getTextFirstName(String cid, IDataModel dataModel) {
        Object textObject = dataModel.getValue(cid);
        if (!(textObject instanceof List)) {
            return EMPTY_STRING;
        }

        List<TextValuePair> textValuePairList = JsonUtils.parseArray(textObject, TextValuePair.class);
        if (CollectionUtils.isEmpty(textValuePairList)) {
            return EMPTY_STRING;
        }

        return textValuePairList.get(0).getTextByLanguage(RequestContextHolder.getLangId());
    }

    /**
     * 取ValueMultiLang对象对应的第一个Value值
     * @param cid 组件标识
     * @param dataModel dataModel
     * @return  String text对象对应的第一个Value值
     */
    public static String getValueLangFirstValue(String cid, IDataModel dataModel) {
        Object textObject = dataModel.getValue(cid);
        if (!(textObject instanceof List)) {
            return null;
        }

        List<MultiLangText> valueMultiLangListList = JsonUtils.parseArray(textObject, MultiLangText.class);
        return CollectionUtils.isEmpty(valueMultiLangListList) ? null : valueMultiLangListList.get(0).getValue();
    }

    public static String getValueLangFirstValue(String cid, String entityKey, IDataModel dataModel) {
        IDataEntityCollection dataEntityCollection = dataModel.getEntryRowEntities(entityKey);
        if (dataEntityCollection.isEmpty()) {
            return null;
        }

        Object textObject = dataModel.getValue(cid);
        if (!(textObject instanceof List)) {
            return null;
        }

        List<MultiLangText> valueMultiLangListList = JsonUtils.parseArray(textObject, MultiLangText.class);
        return CollectionUtils.isEmpty(valueMultiLangListList) ? null : valueMultiLangListList.get(0).getValue();
    }

    public static String getValueLangFirstName(String cid, IDataModel dataModel) {
        Object textObject = dataModel.getValue(cid);
        if (!(textObject instanceof List)) {
            return EMPTY_STRING;
        }

        List<MultiLangText> valueMultiLangListList = JsonUtils.parseArray(textObject, MultiLangText.class);
        if (CollectionUtils.isEmpty(valueMultiLangListList)) {
            return EMPTY_STRING;
        }
        return ZH_CN.equals(RequestContextHolder.getLangId())
                ? valueMultiLangListList.get(0).getZhCN()
                : valueMultiLangListList.get(0).getEnUS();
    }


    /**
     * 取子表单对应列的集合
     *
     * @param tablePropertyKey 子表单的唯一标识
     * @param fieldPropertyKey 子表单对应字段的唯一标识
     * @param dataModel        dataModel
     * @return String text对象对应的第一个Value值
     */
    public static List<String> getSubFormCloumValueList(IDataModel dataModel, String tablePropertyKey, String fieldPropertyKey) {

        List<Object> productModelIdObjectList = dataModel.getEntryColumnObject(tablePropertyKey, fieldPropertyKey);
        return CollectionUtils.isEmpty(productModelIdObjectList) ? null
                : productModelIdObjectList
                .stream()
                .filter(Objects::nonNull)
                .map(String.class::cast)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 获取子表单服务对象列值集合
     * @param dataModel         dataModel
     * @param tablePropertyKey  子表单的唯一标识
     * @param fieldPropertyKey  子表单对应字段的唯一标识
     * @param columnKey         服务对象数据对应列名
     * @return List<String>
     */
    public static List<String> getSubFormServiceColumnValueList(IDataModel dataModel,
                                                                String tablePropertyKey,
                                                                String fieldPropertyKey,
                                                                String columnKey) {
        List<Object> serviceColumnInfoList = dataModel.getEntryColumnObject(tablePropertyKey, fieldPropertyKey);
        if (CollectionUtils.isEmpty(serviceColumnInfoList)) {
            return Lists.newArrayList();
        }

        List<String> valueList = Lists.newArrayList();
        for (Object serviceColumn : serviceColumnInfoList) {
            if (Objects.isNull(serviceColumn)) {
                continue;
            }

            DynamicDataEntity entity = (DynamicDataEntity) serviceColumn;
            if (StringUtils.isNotBlank(entity.getString(columnKey))) {
                valueList.add(entity.getString(columnKey));
            }
        }

        return valueList;
    }

    /**
     * 判断某个操作类型是否属于某一操作类型分组
     * @param operationTypeCode 操作类型code
     * @param operationTypeGroupCode 操作类型分组code
     * @return  Boolean
     */
    public static Boolean isOperationTypeOfGuaranteeType(String operationTypeCode, String operationTypeGroupCode) {
        LookupValue lookupValue = LookupValueHelper.getLookupValue(
                LookupValueConstant.OPERATE_TYPE_ENUM, operationTypeCode);
        if (lookupValue == null) {
            return false;
        }

        String parentLookupCode = lookupValue.getParentLookupCode();
        return operationTypeGroupCode.equals(parentLookupCode);

    }
}
