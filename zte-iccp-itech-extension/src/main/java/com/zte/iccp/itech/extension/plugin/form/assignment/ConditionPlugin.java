package com.zte.iccp.itech.extension.plugin.form.assignment;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerOpReasonEnum;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.plugin.form.BaseFormPlugin;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.iccp.itech.extension.spi.model.nis.ProductClassificationTree;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.event.LoadDataEvent;
import com.zte.paas.lcap.ddm.common.api.form.IClientViewProxy;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.builder.value.OptionsBuilder;
import com.zte.paas.lcap.ddm.domain.helper.support.Option;
import com.zte.paas.lcap.ddm.domain.helper.support.Text;

import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.PageConstants.*;

public class ConditionPlugin extends BaseFormPlugin {

    /** 补充快码 - 操作原因 - 公司发起 */
    private final MultiLangText COMPANY_OPERATION_REASON = new MultiLangText(
            PartnerOpReasonEnum.COMPANY.getZhCn(),
            PartnerOpReasonEnum.COMPANY.getEnUs(),
            PartnerOpReasonEnum.COMPANY.name());

    /** 补充快码 - 操作原因 - 客户发起 */
    private final MultiLangText CUSTOMER_OPERATION_REASON = new MultiLangText(
            PartnerOpReasonEnum.CUSTOMER.getZhCn(),
            PartnerOpReasonEnum.CUSTOMER.getEnUs(),
            PartnerOpReasonEnum.CUSTOMER.name());

    @Override
    public void afterLoadData(LoadDataEvent loadDataEvent) {
        IClientViewProxy clientViewProxy = getView().getClientViewProxy();
        String pageCid = getView().getPageId();

        switch (pageCid) {
            case PAGE_WEB_NETWORK_CHANGE_ASSIGNMENT:
            case PAGE_WEB_NETWORK_CHANGE_INITIATED_BY_ME:
            case PAGE_WEB_NETWORK_CHANGE_TO_BE_HANDLED_BY_ME:
            case PAGE_WEB_NETWORK_CHANGE_HANDLED_BY_ME:
            case PARTNER_CHANGE_ORDER_TO_BE_HANDLED:
            case PARTNER_CHANGE_ORDER_INITIATED_BY_ME:
            case PARTNER_CHANGE_ORDER_HANDLED_BY_ME:
                // 1.网络变更任务下拉框
                networkChangeCondition(clientViewProxy);
                break;

            case PAGE_WEB_TECHNOLOGY_MANAGEMENT_ASSIGNMENT:
            case PAGE_WEB_TECHNOLOGY_INITIATED_BY_ME:
            case PAGE_WEB_TECHNOLOGY_TO_BE_HANDLED_BY_ME:
            case PAGE_WEB_TECHNOLOGY_HANDLED_BY_ME:
                // 2.技术管理下拉框 - 任务分类 / 组织 / 产品分类
                setDropDownLookupValue(
                        clientViewProxy,
                        LookupValueConstant.TASK_CATEGORY_ENUM,
                        AssignmentFieldConsts.TechnologyManagementFieldConsts.TASK_CATEGORY,
                        Lists.newArrayList());
                setOrganizationTree(clientViewProxy, CidConstants.COMPONENT_ORGANIZATION_TREE_CID);
                setProductClassificationTree(clientViewProxy, CommonConstants.LEVEL_PRODUCT_MODEL);
                break;

            case PAGE_WEB_FAULT_MANAGEMENT_ASSIGNMENT:
                // 3.故障管理下拉框 - 组织 / 产品经营团队
                setOrganizationTree(clientViewProxy, CidConstants.COMPONENT_ORGANIZATION_TREE_CID);
                setProductClassificationTree(clientViewProxy, CommonConstants.LEVEL_PRODUCT_MANAGEMENT_TEAM);
                break;

            case PAGE_WEB_APPLICATION_HANDLED_BY_ME:
            case PAGE_WEB_APPLICATION_TO_BE_HANDLED_BY_ME:
                // 4.权限申请下拉框 - 组织 / 产品
                setOrganizationTree(clientViewProxy, CidConstants.COMPONENT_ORGANIZATION_TREE_CID);
                setProductClassificationTree(clientViewProxy, CommonConstants.LEVEL_PRODUCT_MODEL);
                break;

            case PAGE_WEB_ABNORMAL_REVIEW_INITIATED_BY_ME:
            case PAGE_WEB_ABNORMAL_REVIEW_HANDLED_BY_ME:
            case PAGE_WEB_ABNORMAL_REVIEW_TO_BE_HANDLED_BY_ME:
                clockInReviewCondition(clientViewProxy);
                break;

            case PAGE_WEB_OPERATION_PLAN_MANAGEMENT:
                operationPlanCondition(clientViewProxy);
                break;

            default:
                break;
        }
    }

    /**
     * 下拉框 - 网络变更
     */
    private void networkChangeCondition(IClientViewProxy clientViewProxy) {

        // 1.操作类型
        setDropDownLookupValue(
                clientViewProxy,
                LookupValueConstant.OPERATE_TYPE_ENUM,
                AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_TYPE,
                Lists.newArrayList());

        // 2.操作原因
        setDropDownLookupValue(
                clientViewProxy,
                LookupValueConstant.OPERATE_REASON_ENUM,
                AssignmentFieldConsts.NetworkChangeFieldConsts.OPERATION_REASON,
                Lists.newArrayList(COMPANY_OPERATION_REASON, CUSTOMER_OPERATION_REASON));

        // 3.组织
        setOrganizationTree(clientViewProxy, CidConstants.COMPONENT_ORGANIZATION_TREE_CID);

        // 4.产品分类
        setProductClassificationTree(clientViewProxy, CommonConstants.LEVEL_PRODUCT_SUBCLASS);
    }

    /**
     * 下拉框 - 打卡复盘
     */
    private void clockInReviewCondition(IClientViewProxy clientViewProxy) {
        // 1.营销
        setOrganizationTree(clientViewProxy, AssignmentFieldConsts.MARKETING);

        // 2.代表处
        setOrganizationTree(clientViewProxy, AssignmentFieldConsts.REPRESENTATIVE_OFFICE);

        // 3.产品分类
        setProductClassificationTree(clientViewProxy, CommonConstants.LEVEL_PRODUCT_SUBCLASS);

        // 4.操作类型
        setDropDownLookupValue(
                clientViewProxy,
                LookupValueConstant.OPERATE_TYPE_ENUM,
                AssignmentFieldConsts.ClockReviewsFieldConsts.OPERATION_TYPE,
                Lists.newArrayList());
    }

    /**
     * 自定义组件 - 产品分类树
     */
    private void setProductClassificationTree(IClientViewProxy clientViewProxy, Integer treeLevel) {
        // 1.检索产品分类数据
        List<ProductClassificationTree> treeData = NisClient.queryProductTree(treeLevel);

        // 2.设置下拉框
        Map<String, Object> props = MapUtils.newHashMap(
                CidConstants.COMPONENT_PRODUCT_TREE_CID,
                new BasicAttributeBuilder()
                        .attribute(
                                CommonConstants.TREE_DATA,
                                JSONArray.parseArray(JSON.toJSONString(treeData)))
                        .build());
        clientViewProxy.setProps(props);
    }

    /**
     * 自定义组件 - 组织树
     */
    private void setOrganizationTree(IClientViewProxy clientViewProxy, String componentCid) {
        // 1.检索组织数据
        List<OrganizationTreeVo> treeData = NisClient.queryOrganizationTree();

        // 2.设置下拉框
        Map<String, Object> props = MapUtils.newHashMap(
                componentCid,
                new BasicAttributeBuilder()
                        .attribute(
                                CommonConstants.TREE_DATA,
                                JSONArray.parseArray(JSON.toJSONString(treeData)))
                        .build());
        clientViewProxy.setProps(props);
    }

    /**
     * 设置下拉选项 - 快码类型
     */
    private void setDropDownLookupValue(
            IClientViewProxy clientViewProxy,
            String lookupKey,
            String componentCid,
            List<MultiLangText> exOptions) {

        // 1.检索快码
        List<LookupValue> lookupValues = LookupValueHelper.getLookupValues(lookupKey);

        // 2.设置快码范围下拉框
        OptionsBuilder optionsBuilder = new OptionsBuilder();
        for (LookupValue lookupValue : lookupValues) {
            optionsBuilder.addOption(
                    new Option(lookupValue.getLookupCode(), new Text(lookupValue.getMeaningCn(), lookupValue.getMeaningEn())));
        }

        // 3.设置补充字段下拉框
        // 由于部分快码未完全维护在快码平台(如：分包商网络变更 - 操作原因，部分写死在下拉框，维护到快码反而影响内部网络变更功能)，兼容该部分选项
        for (MultiLangText exOption : exOptions) {
            optionsBuilder.addOption(
                    new Option(exOption.getValue(), new Text(exOption.getZhCN(), exOption.getEnUS())));
        }

        clientViewProxy.setOptions(componentCid, optionsBuilder.build());
    }

    /**
     * 下拉框 - 操作计划
     */
    private void operationPlanCondition(IClientViewProxy clientViewProxy) {

        // 1.操作类型
        setDropDownLookupValue(
                clientViewProxy,
                LookupValueConstant.OPERATE_TYPE_ENUM,
                AssignmentFieldConsts.OperationPlanFieldConsts.OPERATION_TYPE,
                Lists.newArrayList());

        // 2.操作原因
        setDropDownLookupValue(
                clientViewProxy,
                LookupValueConstant.OPERATE_REASON_ENUM,
                AssignmentFieldConsts.OperationPlanFieldConsts.OPERATION_REASON,
                Lists.newArrayList(COMPANY_OPERATION_REASON, CUSTOMER_OPERATION_REASON));

        // 3.组织
        setOrganizationTree(clientViewProxy, CidConstants.COMPONENT_ORGANIZATION_TREE_CID);

        // 4.产品分类
        setProductClassificationTree(clientViewProxy, CommonConstants.LEVEL_PRODUCT_SUBCLASS);
    }
}
