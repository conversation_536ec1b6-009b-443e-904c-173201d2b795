package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.model.base.LookupValueEnum;
import lombok.SneakyThrows;

import java.lang.reflect.Field;
import java.util.Map;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/22
 */
public class LookupValueDeserializer<T extends Enum<T> & LookupValueEnum> extends JsonDeserializer<T> {

    @Override
    @SuppressWarnings("unchecked")
    @SneakyThrows
    public T deserialize(JsonParser p, DeserializationContext ctxt) {
        Object o = p.getCodec().readValue(p, Object.class);
        String lookupCode;

        if (o instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) o;
            lookupCode = (String) map.get("lookupCode");
        } else {
            lookupCode = (String) o;
        }

        Field field = JsonUtils.findField(p.getCurrentValue().getClass(), p.getCurrentName());
        if (field == null) {
            return null;
        }

        return LookupValueEnum.fromLookupCode(field.getType(), lookupCode);
    }
}
