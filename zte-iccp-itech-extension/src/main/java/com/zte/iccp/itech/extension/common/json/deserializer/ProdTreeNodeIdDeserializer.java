package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.model.ProductTreeNode;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/05
 */
public class ProdTreeNodeIdDeserializer extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        Object obj = p.getCodec().readValue(p, Object.class);
        if (obj instanceof Map) {
            ProductTreeNode node = JsonUtils.parseObject(obj, ProductTreeNode.class);
            return node.getNumber();
        }

        return obj == null ? null : obj.toString();
    }
}
