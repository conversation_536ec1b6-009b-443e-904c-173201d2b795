package com.zte.iccp.itech.extension.plugin.flow.changeorder;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.IdopAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.SOURCE;

/**
 * 提交 发送修改内容给DIOP并计算冲突
 *
 * <AUTHOR>
 * @create 2024/9/5 上午10:10
 */
public class SubmitSendIdopPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        Object source = body.getVariables().get(SOURCE);
        if (source == null || !DataSourceEnum.IDOP.name().equals(source.toString())) {
            return false;
        }

        IdopAbility.updateIdopChangeOrder(body.getBusinessId());
        return false;
    }
}
