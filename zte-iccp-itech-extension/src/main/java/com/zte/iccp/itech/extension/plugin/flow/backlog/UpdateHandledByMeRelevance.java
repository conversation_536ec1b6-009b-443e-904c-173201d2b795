package com.zte.iccp.itech.extension.plugin.flow.backlog;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.iss.approval.sdk.bean.NodeElement;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.INTEGER_ZERO;
import static com.zte.iccp.itech.extension.plugin.operation.backlog.historicaldata.ApprovalNodeConstants.*;

/**
 * 内部网络变更单/合作方网络变更单，在流程插件中更新我已处理关系
 * 触点在离开节点时执行
 *
 * <AUTHOR> 10347404
 * @since 2025/01/05
 */
public class UpdateHandledByMeRelevance extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(body.getFlowCode());
        NodeElement nodeElement = body.getNodeElement();
        if (null == approveFlowEnum || null == nodeElement) {
            return false;
        }
        String nodeExtendName = nodeElement.getNodeExtendName();
        String businessId = body.getBusinessId();
        switch (approveFlowEnum) {
            case CHANGE_ORDER_COMP_FLOW:
                // 内部网络变更单审批流
                // 判断是否审批节点
                BoolEnum approvalTaskFlag = NETWORK_CHANGE_APPROVAL_NODE
                        .contains(ApproveNodeEnum.getApproveNodeEnum(nodeExtendName))
                        ? BoolEnum.Y : BoolEnum.N;
                updateHandledByMeRelevance(businessId,nodeExtendName, approvalTaskFlag, AssignmentTypeEnum.NETWORK_CHANGE);
                break;
            case SUBCONTRACTOR_OC_FLOW:
                // 合作方网络变更单审批流
                // 判断是否审批节点
                approvalTaskFlag = PARTNER_NETWORK_CHANGE_APPROVAL_NODE
                        .contains(PartnerApproveNodeEnum.getApproveNodeEnum(nodeExtendName))
                        ? BoolEnum.Y : BoolEnum.N;
                updateHandledByMeRelevance(businessId, nodeExtendName,
                        approvalTaskFlag, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE);
                break;
            case BATCH_TASK_FLOW:
                // 批次任务审批流
                // 判断是否审批节点
                approvalTaskFlag = NETWORK_CHANGE_BATCH_APPROVAL_NODE
                        .contains(ApproveNodeEnum.getApproveNodeEnum(nodeExtendName))
                        ? BoolEnum.Y : BoolEnum.N;
                updateHandledByMeRelevance(businessId, nodeExtendName,
                        approvalTaskFlag, AssignmentTypeEnum.NETWORK_CHANGE_BATCH);
                break;
            case SUBCONTRACTOR_TASK_FLOW:
                // 合作方批次任务审批流
                // 判断是否审批节点
                approvalTaskFlag = PARTNER_NETWORK_CHANGE_BATCH_APPROVAL_NODE
                        .contains(PartnerApproveNodeEnum.getApproveNodeEnum(nodeExtendName))
                        ? BoolEnum.Y : BoolEnum.N;
                updateHandledByMeRelevance(businessId, nodeExtendName,
                        approvalTaskFlag, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH);
                break;
            default:
                break;
        }
        return true;
    }

    private void updateHandledByMeRelevance(String businessId,
                                            String nodeExtendName,
                                            BoolEnum approvalTaskFlag,
                                            AssignmentTypeEnum assignmentType) {
        List<Assignment> assignments = AssignmentAbility
                .queryAssignment(Lists.newArrayList(businessId), assignmentType);
        if (CollectionUtils.isEmpty(assignments)) {
            return;
        }
        Assignment assignment = assignments.get(INTEGER_ZERO);
        String assignmentId = assignment.getId();
        List<ApproveRecord> records = FlowHelper.getApprovedRecords(businessId);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        records = records.stream().filter(v -> nodeExtendName.equals(v.getExtendedCode())).collect(Collectors.toList());
        String userId = records.get(INTEGER_ZERO).getHandler();
        AssignmentAbility.updateHandledByMeRelevance(assignmentId, userId, approvalTaskFlag);
    }
}
