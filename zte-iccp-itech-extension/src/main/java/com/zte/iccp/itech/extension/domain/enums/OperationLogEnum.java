package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ApprovalAbility;
import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.SubcontractorChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.IntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.SignatureOperationLogParam;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiModeProduct;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdDevIntApproval;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdNetServIntApproval;
import com.zte.iccp.itech.extension.domain.model.vo.SubIntlAdminApproval;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;


/**
 * 网络变更单操作日志枚举（数据库字段为字段名）
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/5/8
 */
@Getter
public enum OperationLogEnum {
    /**
     * 代表处产品TD审核  【审核结果】{0}; 【审核意见】{1}
     */
    REP_PROD_TD_APPROVE(ApproveNodeEnum.REP_PROD_TD_APPROVE, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.RepProdTdApproveConsts.APPROVE_RESULT, ChangeOrderFieldConsts.RepProdTdApproveConsts.APPROVE_OPINION)),

    /**
     * 技术交付部/网络处审核
     */
    TD_NET_DEPT_APPROVE(ApproveNodeEnum.TD_NET_DEPT_APPROVE, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.TdNetDeptApproveConsts.APPROVE_RESULT, ChangeOrderFieldConsts.TdNetDeptApproveConsts.APPROVE_OPINION)),

    /**
     * 网络处总工
     */
    NETWORK_CHIEF_ENGINEER(ApproveNodeEnum.NETWORK_CHIEF_ENGINEER, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.NetworkChiefEngineerConsts.APPROVE_RESULT, ChangeOrderFieldConsts.NetworkChiefEngineerConsts.APPROVE_OPINION)),

    /**
     * 技术交付部/网络处领导审核
     */
    TD_NET_DEPT_LEADER(ApproveNodeEnum.TD_NET_DEPT_LEADER, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.TdNetDeptLeaderConsts.APPROVE_RESULT, ChangeOrderFieldConsts.TdNetDeptLeaderConsts.APPROVE_OPINION)),

    /**
     * 技术交付部/网络处审核远程中心的方案
     */
    NET_DEPT_REMOTE_SCHEME(ApproveNodeEnum.NET_DEPT_REMOTE_SCHEME, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.NetDeptRemoteSchemeConsts.APPROVE_RESULT, ChangeOrderFieldConsts.NetDeptRemoteSchemeConsts.APPROVE_OPINION)),

    /**
     * 网络服务部审核
     */
    NET_SERVICE_DEPT_APP(ApproveNodeEnum.NET_SERVICE_DEPT_APP, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.NetServiceDeptAppConsts.APPROVE_RESULT, ChangeOrderFieldConsts.NetServiceDeptAppConsts.APPROVE_OPINION_NET_SERVICE_DEPT)),

    /**
     * 服务产品支持部审核
     */
    SERVICE_PROD_SUPPORT(ApproveNodeEnum.SERVICE_PROD_SUPPORT, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.ServiceProdSupportConsts.APPROVE_RESULT, ChangeOrderFieldConsts.ServiceProdSupportConsts.APPROVE_OPINION)),

    /**
     * SSP产品支持团队审核
     */
    SSP_PROD_SUPPORT(ApproveNodeEnum.SSP_PROD_SUPPORT, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.SSPProdSupportConsts.APPROVE_RESULT, ChangeOrderFieldConsts.SSPProdSupportConsts.APPROVE_OPINION)),

    /**
     * 测试部审核
     */
    TEST_DEPT(ApproveNodeEnum.TEST_DEPT, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.TestDeptConsts.APPROVE_RESULT, ChangeOrderFieldConsts.TestDeptConsts.APPROVE_OPINION)),

    /**
     * 研发经理审核
     */
    RD_MANAGER(ApproveNodeEnum.RD_MANAGER, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.RDManagerConsts.APPROVE_RESULT, ChangeOrderFieldConsts.RDManagerConsts.APPROVE_OPINION)),

    /**
     * 集成团队审核
     */
    INTEGRATION_TEAM_APP(ApproveNodeEnum.INTEGRATION_TEAM_APP, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.IntegrationTeamAppConsts.APPROVE_RESULT, ChangeOrderFieldConsts.IntegrationTeamAppConsts.APPROVE_OPINION)),

    /**
     * 研发领导审核
     */
    RD_LEADER_APP(ApproveNodeEnum.RD_LEADER_APP, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.RdLeaderAppConsts.APPROVE_RESULT, ChangeOrderFieldConsts.RdLeaderAppConsts.APPROVE_OPINION)),

    /**
     * 行政审核_办事处产品科长
     */
    ADMIN_REP_PROD_CHIEF(ApproveNodeEnum.ADMIN_REP_PROD_CHIEF, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminRepProdChiefFieldConsts.APPROVE_RESULT, ChangeOrderFieldConsts.AdminRepProdChiefFieldConsts.APPROVE_OPINION)),

    /**
     * 行政审核_网络服务处总监
     */
    ADMIN_NET_PROD_DIR(ApproveNodeEnum.ADMIN_NET_PROD_DIR, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminNetProdDirFieldConsts.APPROVE_RESULT, ChangeOrderFieldConsts.AdminNetProdDirFieldConsts.APPROVE_OPINION)),

    /**
     * 行政审核_电信服务总监
     */
    ADMIN_DIR_TELE_SERVICE(ApproveNodeEnum.ADMIN_DIR_TELE_SERVICE, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminDirTeleSerFieldConsts.APPROVE_RESULT_FIELD_NAME, ChangeOrderFieldConsts.AdminDirTeleSerFieldConsts.APPROVE_OPINION_FIELD_NAME)),

    /**
     * 行政审核_办事处副经理
     */
    ADMIN_REP_DEPUTY_MNG(ApproveNodeEnum.ADMIN_REP_DEPUTY_MNG, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminRepDeputyMngFieldConsts.APPROVE_RESULT, ChangeOrderFieldConsts.AdminRepDeputyMngFieldConsts.APPROVE_OPINION)),

    /**
     * 行政审核_网络服务处经理
     */
    ADMIN_NET_DEPT_MNG(ApproveNodeEnum.ADMIN_NET_DEPT_MNG, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminNetDeptMngFieldConsts.APPROVE_RESULT, ChangeOrderFieldConsts.AdminNetDeptMngFieldConsts.APPROVE_OPINION)),

    /**
     * 行政审核_网络服务部部长
     */
    ADMIN_NETSERVICE_LV4(ApproveNodeEnum.ADMIN_NETSERVICE_LV4, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminNetServiceLV4FieldConsts.APPROVE_RESULT, ChangeOrderFieldConsts.AdminNetServiceLV4FieldConsts.APPROVE_OPINION)),

    /**
     * 行政审核_网研院院长
     */
    ADMIN_NETSERVICE_LV3(ApproveNodeEnum.ADMIN_NETSERVICE_LV3, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminNetServiceLV3FieldConsts.APPROVE_RESULT, ChangeOrderFieldConsts.AdminNetServiceLV3FieldConsts.APPROVE_OPINION)),

    /**
     * 行政审核_研发中心主任
     */
    ADMIN_RD_DEPT_LV3(ApproveNodeEnum.ADMIN_RD_DEPT_LV3, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminRdDeptLV3FieldConsts.APPROVE_RESULT, ChangeOrderFieldConsts.AdminRdDeptLV3FieldConsts.APPROVE_OPINION)),

    /**
     * 行政审核_网络服务处总经理
     */
    ADMIN_ENG_SERVICE3(ApproveNodeEnum.ADMIN_ENG_SERVICE3, AssignmentTypeEnum.NETWORK_CHANGE,
            Lists.newArrayList(ChangeOrderFieldConsts.AdminEngService3FieldConsts.APPROVE_RESULT, ChangeOrderFieldConsts.AdminEngService3FieldConsts.APPROVE_OPINION_FIELD_NAME)),


    /**
     * 国际行政会签
     */
    INTL_ADMIN_APPROVAL(ApproveNodeEnum.INTL_ADMIN_APPROVAL, AssignmentTypeEnum.NETWORK_CHANGE, IntlAdminApproval.class,
            OperationLogEnum::convertIntlAdminSignatureOperationLogContent),

    /**
     * 多模从属产品负责人审核
     */
    MULTIMODE_PRODUCT_OWNER(ApproveNodeEnum.MULTIMODE_PRODUCT_OWNER, AssignmentTypeEnum.NETWORK_CHANGE, MultiModeProduct.class,
            OperationLogEnum::convertIntlAdminSignatureOperationLogContent),

    /**
     * 网服部一体化关联产品审核
     */
    NET_INTEGRATION(ApproveNodeEnum.NET_INTEGRATION, AssignmentTypeEnum.NETWORK_CHANGE, OpAssocProdNetServIntApproval.class,
            OperationLogEnum::convertIntlAdminSignatureOperationLogContent),

    /**
     * 研发一体化关联产品审核
     */
    RD_INTEGRATION(ApproveNodeEnum.RD_INTEGRATION, AssignmentTypeEnum.NETWORK_CHANGE, OpAssocProdDevIntApproval.class,
            OperationLogEnum::convertIntlAdminSignatureOperationLogContent),

    /**
     * 核心网大区TD审核（特殊场景下述） 【是否产品用途BCN需要多产品联动保障】{0};【审核意见】{1}
     */
    REGIONAL_TD_CONFIRM(ApproveNodeEnum.REGIONAL_TD_CONFIRM, AssignmentTypeEnum.NETWORK_CHANGE, "log.regional.td",
            Lists.newArrayList(ChangeOrderFieldConsts.IS_BCN_LINKAGE_GUARANTEE, ChangeOrderFieldConsts.ReginalTdConfirmConsts.APPROVE_OPINION),
            OperationLogEnum::convertOperationLog2Content),

    /**
     * 远程中心负责人审核  【是否远程中心执行】{0};【审核意见】{1}
     */
    REMOTE_CENTER_OWNER(ApproveNodeEnum.REMOTE_CENTER_OWNER, AssignmentTypeEnum.NETWORK_CHANGE,"log.remote.center.owner",
            Lists.newArrayList(ChangeOrderFieldConsts.IS_REMOTE_CENTER_SUPPORT, ChangeOrderFieldConsts.RemoteCenterOwnerConsts.APPROVE_OPINION),
            OperationLogEnum::convertOperationLog2Content),

    /**
     * 远程中心操作实施人指派  【指派操作实施人】
     */
    REMOTE_CENTER_OPER(ApproveNodeEnum.REMOTE_CENTER_OPER, AssignmentTypeEnum.NETWORK_CHANGE,"log.remote.center.operation.implementation",
            OperationLogEnum::convertOperationLogContent),

    /**
     * 远程中心方案提交
     */
    REMOTE_CENTER_SCHEME(ApproveNodeEnum.REMOTE_CENTER_SCHEME, AssignmentTypeEnum.NETWORK_CHANGE,"log.remote.center.scheme",
            Lists.newArrayList(ChangeOrderFieldConsts.REMOTE_CENTER_SCHEME_DESC), OperationLogEnum::convertOperationLog1Content),


    /********* 合作方网络变更单  **********/
    /**
     *  办事处产品科长审核
     */
    PARTNER_REPRESENTATIVE_CHIEF_APPROVAL(PartnerApproveNodeEnum.PARTNER_REPRESENTATIVE_CHIEF_APPROVAL, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE,
            Lists.newArrayList(SubcontractorChangeOrderFieldConsts.RepProdChiefFieldConsts.APPROVE_RESULT, SubcontractorChangeOrderFieldConsts.RepProdChiefFieldConsts.APPROVE_OPINION)),

    /**
     *  网络责任人审核
     */
    PARTNER_NET_OWNER_APPROVAL(PartnerApproveNodeEnum.PARTNER_NET_OWNER_APPROVAL, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE,
            Lists.newArrayList(SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.APPROVE_RESULT, SubcontractorChangeOrderFieldConsts.NetOwnerFieldConsts.APPROVE_OPINION)),

    /**
     *  办事处产品经理审核
     */
    PARTNER_OFFICE_MANAGER_APPROVAL(PartnerApproveNodeEnum.PARTNER_OFFICE_MANAGER_APPROVAL, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE,
            Lists.newArrayList(SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.APPROVE_RESULT, SubcontractorChangeOrderFieldConsts.OfficeProdManagerFieldConsts.APPROVE_OPINION)),

    /**
     *  网络处审核
     */
    PARTNER_NET_DEPT_APPROVAL(PartnerApproveNodeEnum.PARTNER_NET_DEPT_APPROVAL, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE,
            Lists.newArrayList(SubcontractorChangeOrderFieldConsts.NetDeptFieldConsts.APPROVE_RESULT, SubcontractorChangeOrderFieldConsts.NetDeptFieldConsts.APPROVE_OPINION)),

    /**
     *  行政审批_办事处PD
     */
    PARTNER_OFFICE_PD_APPROVAL(PartnerApproveNodeEnum.PARTNER_OFFICE_PD_APPROVAL, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE,
            Lists.newArrayList(SubcontractorChangeOrderFieldConsts.OfficePdFieldConsts.APPROVE_RESULT, SubcontractorChangeOrderFieldConsts.OfficePdFieldConsts.APPROVE_OPINION)),

    /**
     *  行政审批_办事处产品科长
     */
    PARTNER_OFFICE_PROD_CHIEF_APPROVAL(PartnerApproveNodeEnum.PARTNER_OFFICE_PROD_CHIEF_APPROVAL, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE,
            Lists.newArrayList(SubcontractorChangeOrderFieldConsts.OfficeProdChiefFieldConsts.APPROVE_RESULT, SubcontractorChangeOrderFieldConsts.OfficeProdChiefFieldConsts.APPROVE_OPINION)),


    /********* 批次  **********/
    /** 取消操作审核 */
    OPERATION_CANCEL_REVIEW(ApproveNodeEnum.OPERATION_CANCEL_REVIEW, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.OC_APPROVE_RESULT, BatchTaskFieldConsts.OC_REVIEW_OPINION)),

    /** 待发通告 - 申请人执行操作计划 */
    PENDING_NOTIFICATION(ApproveNodeEnum.PENDING_NOTIFICATION, "title.batch.pending.notify", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, "log.release.notify",
            Lists.newArrayList(BatchTaskFieldConsts.NOTIFICATION_DESC), OperationLogEnum::convertOperationLog1Content),

    /**
     * 远程中心负责人审核
     */
    BATCH_REMOTE_CENTER_OWNER(ApproveNodeEnum.REMOTE_CENTER_OWNER, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_TD_NET_DEPT_APP_SOLU, BatchTaskFieldConsts.APPROVE_OPINION_TD_NET_DEPT_APP_SOLU)),

    /**
     * 操作计划审核
     */
    BATCH_CHANGED_BY_REP_PROD_CHIEF(ApproveNodeEnum.CHANGED_BY_REP_PROD_CHIEF, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_REP_PROD_CHIEF, BatchTaskFieldConsts.APPROVE_OPINION_REP_PROD_CHIEF)),

    /**
     * 行政审核_办事处产品科长
     */
    BATCH_ADMIN_REP_PROD_CHIEF(ApproveNodeEnum.ADMIN_REP_PROD_CHIEF, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_PROD_CHIEF, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_PROD_CHIEF)),

    /**
     * 行政审核_网络服务处总监
     */
    BATCH_ADMIN_NET_PROD_DIR(ApproveNodeEnum.ADMIN_NET_PROD_DIR, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_PROD_DIRECTOR, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_PROD_DIRECTOR)),

    /**
     * 行政审核_电信服务总监
     */
    BATCH_ADMIN_DIR_TELE_SERVICE(ApproveNodeEnum.ADMIN_DIR_TELE_SERVICE, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_DIR_TELE_SER_DIRECTOR_FIELD_NAME)),

    /**
     * 行政审核_办事处副经理
     */
    BATCH_ADMIN_REP_DEPUTY_MNG(ApproveNodeEnum.ADMIN_REP_DEPUTY_MNG, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_DEPUTY_MANAGER, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_DEPUTY_MANAGER)),

    /**
     * 行政审核_网络服务处经理
     */
    BATCH_ADMIN_NET_DEPT_MNG(ApproveNodeEnum.ADMIN_NET_DEPT_MNG, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_DEPT_MNG, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_DEPT_MNG)),

    /**
     * 行政审核_网络服务部部长
     */
    BATCH_ADMIN_NETSERVICE_LV4(ApproveNodeEnum.ADMIN_NETSERVICE_LV4, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_SERVCIE_LV4, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_SERVCIE_LV4)),

    /**
     * 行政审核_网研院院长
     */
    BATCH_ADMIN_NETSERVICE_LV3(ApproveNodeEnum.ADMIN_NETSERVICE_LV3, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_NET_SERVCIE_LV3, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_NET_SERVCIE_LV3)),

    /**
     * 行政审核_研发中心主任
     */
    BATCH_ADMIN_RD_DEPT_LV3(ApproveNodeEnum.ADMIN_RD_DEPT_LV3, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_RD_DEPT_LV3, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_RD_DEPT_LV3)),

    /**
     * 行政审核_网络服务处总经理
     */
    BATCH_ADMIN_ENG_SERVICE3(ApproveNodeEnum.ADMIN_ENG_SERVICE3, AssignmentTypeEnum.NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_ENG_SERVICE3_LEADER, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_ENG_SERVICE3_LEADER_FIELD_NAME)),

    /**
     * 国际行政会签
     */
    BATCH_INTL_ADMIN_APPROVAL(ApproveNodeEnum.BATCH_INTL_ADMIN_APPROVAL, AssignmentTypeEnum.NETWORK_CHANGE_BATCH, SubIntlAdminApproval.class,
            OperationLogEnum::convertIntlAdminSignatureOperationLogContent),

    /**
     * 待反馈操作结果（操作结果=操作已取消；显示【备注说明】而不显示【操作总结】；操作结果！=操作已取消，显示【操作总结】（默认展示）而不显示【备注说明】；）
     */
    RESULT_TOBE_BACK(ApproveNodeEnum.RESULT_TOBE_BACK, "title.batch.result.tobe.back", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, "log.batch.result.tobe.back",
            Lists.newArrayList(BatchTaskFieldConsts.OPERATION_RESULT, BatchTaskFieldConsts.BATCH_OPERATION_SUMMARY), OperationLogEnum::convertOperationLog2Content),

    /**
     * 技术交付部/网络处审核 - 操作结果审核
     */
    BATCH_TD_NET_DEPT_APPROVE(ApproveNodeEnum.TD_NET_DEPT_APPROVE, "title.batch.result.under.review", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, "log.batch.result.under.review",
            Lists.newArrayList(BatchTaskFieldConsts.APPROVAL_RESULT, BatchTaskFieldConsts.APPROVAL_DESCRIPTION), OperationLogEnum::convertOperationLog2Content),

    /**
     * 网服部审核人 - 操作结果审核
     */
    BATCH_NET_SERVICE_DEPT_APP(ApproveNodeEnum.NET_SERVICE_DEPT_APP, "title.batch.result.under.review", AssignmentTypeEnum.NETWORK_CHANGE_BATCH, "log.batch.result.under.review",
            Lists.newArrayList(BatchTaskFieldConsts.APPROVAL_RESULT, BatchTaskFieldConsts.APPROVAL_DESCRIPTION), OperationLogEnum::convertOperationLog2Content),



    /********* 合作方批次  **********/
    /**
     * 取消操作审核
     */
    HZF_BATCH_OPERATION_CANCEL_REVIEW(PartnerApproveNodeEnum.OPERATION_CANCEL_REVIEW, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.OC_APPROVE_RESULT, BatchTaskFieldConsts.OC_REVIEW_OPINION)),

    /**
     * 待发通告 - 申请人执行操作计划
     */
    HZF_BATCH_PENDING_NOTIFICATION(PartnerApproveNodeEnum.PENDING_NOTIFICATION, "title.batch.pending.notify", AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, "log.release.notify",
            Lists.newArrayList(BatchTaskFieldConsts.NOTIFICATION_DESC), OperationLogEnum::convertOperationLog1Content),

    /**
     * 行政审批_办事处PD
     */
    HZF_BATCH_PARTNER_OFFICE_PD_APPROVAL(PartnerApproveNodeEnum.PARTNER_OFFICE_PD_APPROVAL, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_OFFICE_PD, BatchTaskFieldConsts.APPROVE_OPINION_OFFICE_PD)),

    /**
     * 操作计划审核
     */
    HZF_BATCH_PARTNER_OPERATION_PLAN_APPROVAL(PartnerApproveNodeEnum.PARTNER_OPERATION_PLAN_APPROVAL, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_OPERATION_PLAN, BatchTaskFieldConsts.APPROVE_OPINION_OPERATION_PLAN)),

    /**
     * 行政审批_办事处产品科长
     */
    HZF_BATCH_PARTNER_OFFICE_PROD_CHIEF_APPROVAL(PartnerApproveNodeEnum.PARTNER_OFFICE_PROD_CHIEF_APPROVAL, AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH,
            Lists.newArrayList(BatchTaskFieldConsts.APPROVE_RESULT_ADMIN_REP_PROD_CHIEF, BatchTaskFieldConsts.APPROVE_OPINION_ADMIN_REP_PROD_CHIEF)),

    /**
     * 待反馈操作结果
     */
    HZF_BATCH_RESULT_TOBE_BACK(PartnerApproveNodeEnum.RESULT_TOBE_BACK, "title.batch.result.tobe.back", AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, "log.batch.result.tobe.back",
            Lists.newArrayList(BatchTaskFieldConsts.OPERATION_RESULT, BatchTaskFieldConsts.BATCH_OPERATION_SUMMARY), OperationLogEnum::convertOperationLog2Content),

    /**
     * 网服部审核人 - 操作结果审核
     */
    HZF_BATCH_PARTNER_NET_OWNER_APPROVAL(PartnerApproveNodeEnum.PARTNER_NET_OWNER_APPROVAL, "title.batch.result.under.review", AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH, "log.batch.result.under.review",
            Lists.newArrayList(BatchTaskFieldConsts.APPROVAL_RESULT, BatchTaskFieldConsts.APPROVAL_DESCRIPTION), OperationLogEnum::convertOperationLog2Content),
    ;

    OperationLogEnum(SingletonTextValuePairsProvider provider, AssignmentTypeEnum assignmentTypeEnum, List<String> fields) {
        this.provider = provider;
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.fields = fields;
    }

    OperationLogEnum(SingletonTextValuePairsProvider provider,
                     AssignmentTypeEnum assignmentTypeEnum,
                     String operationDescMsg,
                     BiFunction<FlowClient, String, String> getOperationLogByBodyAndLanguage) {
        this.provider = provider;
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.operationDescMsg = operationDescMsg;
        this.getOperationLogByBodyAndLanguage = getOperationLogByBodyAndLanguage;
    }

    OperationLogEnum(SingletonTextValuePairsProvider provider,
                     AssignmentTypeEnum assignmentTypeEnum,
                     Class<? extends BaseSubEntity> subEntityClass,
                     Function<SignatureOperationLogParam, String> coSignatureOperationLogFunction) {
        this.provider = provider;
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.subEntityClass = subEntityClass;
        this.coSignatureOperationLogFunction = coSignatureOperationLogFunction;
    }

    OperationLogEnum(SingletonTextValuePairsProvider provider,
                     AssignmentTypeEnum assignmentTypeEnum,
                     String operationDescMsg,
                     List<String> fields,
                     BiFunction<FlowClient, String, String> getOperationLogByBodyAndLanguage) {
        this.provider = provider;
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.operationDescMsg = operationDescMsg;
        this.fields = fields;
        this.getOperationLogByBodyAndLanguage = getOperationLogByBodyAndLanguage;
    }

    OperationLogEnum(SingletonTextValuePairsProvider provider,
                     String customOperationNameMsg,
                     AssignmentTypeEnum assignmentTypeEnum,
                     String operationDescMsg,
                     List<String> fields,
                     BiFunction<FlowClient, String, String> getOperationLogByBodyAndLanguage) {
        this.provider = provider;
        this.customOperationNameMsg = customOperationNameMsg;
        this.assignmentTypeEnum = assignmentTypeEnum;
        this.operationDescMsg = operationDescMsg;
        this.fields = fields;
        this.getOperationLogByBodyAndLanguage = getOperationLogByBodyAndLanguage;
    }

    /**
     * 节点自定义编码（操作名称取自定义编码下的msg）
     */
    private final SingletonTextValuePairsProvider provider;

    /**
     * 特殊msg（取msg时先判断是否存在特殊msg，如不存在取provider中的msg，否则取当前）
     * （如批次待发通告 = 申请人确认操作计划，批次-技术交付部/网络处审核、批次-网服部审核人 = 操作结果审核）
     */
    private String customOperationNameMsg;

    /**
     * 任务类型 - 由于主单据和批次存在相同节点自定义编码，增加任务类型进行区分
     */
    private final AssignmentTypeEnum assignmentTypeEnum;

    /**
     * 会签节点定制（冗余）
     */
    private Class<? extends BaseSubEntity> subEntityClass;

    /**
     * 操作日志模板
     */
    private String operationDescMsg;

    /**
     * 数据库字段，必须按照顺序，如1为审核结果，2为审核意见
     */
    private List<String> fields;

    /**
     * 映射方法
     */
    private BiFunction<FlowClient, String, String> getOperationLogByBodyAndLanguage;

    /**
     * 会签节点定制 - 映射方法
     */
    private Function<SignatureOperationLogParam, String> coSignatureOperationLogFunction;
    /**
     * 适配操作日志不需要动态填充值的方法，如【指派操作实施人】
     *
     * @param body body
     * @param language 语言
     * @return 操作记录日志（中英文）
     */
    private static String convertOperationLogContent(FlowClient body, String language) {
        OperationLogEnum operationLogEnum = getOperationLogEnumByFlowCode(body.getFlowCode(),
                body.getNodeElement().getNodeExtendName());

        String operationLogTemplateMsg = operationLogEnum.getOperationDescMsg();
        return MsgUtils.getLangMessage(language, operationLogTemplateMsg);
    }

    /**
     * 适配操作日志需要动态填充一位的方法，如【提交操作方案】【方案描述】{0}
     *
     * @param body body
     * @param language 语言
     * @return 操作记录日志（中英文）
     */
    private static String convertOperationLog1Content(FlowClient body, String language) {
        return OperationLogRecordAbility.convertSingleFieldTextByLanguage(body, language);
    }

    /**
     * 适配操作日志需要动态填充两位的方法，如【审核结果】{0}; 【审核意见】{1}
     *
     * @param body 流程中心body对象
     * @param language 语言
     * @return 操作日志中心+英文map
     */
    private static String convertOperationLog2Content(FlowClient body, String language) {
        return OperationLogRecordAbility.convertOperationLogRadioAndTextContentByLanguage(body, language);
    }

    /**
     * 国际行政会签 映射方法（查询当前子单据体）
     *
     * @param signatureOperationLogParam 会签操作日志输入参数
     * @return 操作日志中英文Map
     */
    private static String convertIntlAdminSignatureOperationLogContent(SignatureOperationLogParam signatureOperationLogParam) {
        return OperationLogRecordAbility.convertSignatureOperationLogContent(signatureOperationLogParam);
    }

    /**
     * 根据审批节点获取操作日志枚举
     *
     * @param provider 审批节点
     * @param assignmentTypeEnum 任务类型
     * @return OperationLogEnum
     */
    public static OperationLogEnum getOperationLogEnum(SingletonTextValuePairsProvider provider, AssignmentTypeEnum assignmentTypeEnum) {
        return Arrays.stream(OperationLogEnum.values())
                .filter(operationLog -> provider.equals(operationLog.getProvider())
                        && assignmentTypeEnum == operationLog.getAssignmentTypeEnum())
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取操作日志枚举
     *
     * @param flowCode 流程编码
     * @param extendedCode 自定义编码
     * @return OperationLogEnum
     */
    public static OperationLogEnum getOperationLogEnumByFlowCode(String flowCode, String extendedCode) {
        ApproveFlowCodeEnum approveFlowEnum = ApproveFlowCodeEnum.getApproveFlowEnum(flowCode);

        SingletonTextValuePairsProvider provider = ApprovalAbility.getApproveNodeName(approveFlowEnum.getFlowEntity(), extendedCode);
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.fromFlowCode(approveFlowEnum);
        return getOperationLogEnum(provider, assignmentTypeEnum);
    }

    /**
     * 是否同步批次保障数据
     *
     * @return true:是  false:否
     */
    public boolean isSyncBatchGuaranteeApprovalData() {
        // 取消操作审核、反馈操作结果、操作结果审核、系统自动节点 - 发布通告（特殊处理）
        return Lists.newArrayList(OPERATION_CANCEL_REVIEW, RESULT_TOBE_BACK, BATCH_TD_NET_DEPT_APPROVE, BATCH_NET_SERVICE_DEPT_APP).contains(this);
    }

    /**
     * 普通流程 - 实例方法调用
     *
     * @param client client
     * @param language 语言
     * @return 操作日志
     */
    public String getLogContent(FlowClient client, String language) {
        return getOperationLogByBodyAndLanguage.apply(client, language);
    }

    /**
     * 会签节点 - 实例方法调用
     *
     * @param businessId 网络变更单id、批次id
     * @param approver 操作人工号
     * @return 操作日志
     */
    public String getLogContent(Class<? extends BaseSubEntity> subEntityClass, String businessId, String approver) {
        return coSignatureOperationLogFunction.apply(new SignatureOperationLogParam(){{
            setApprover(approver);
            setBusinessId(businessId);
            setSubEntityClass(subEntityClass);
        }});
    }
}
