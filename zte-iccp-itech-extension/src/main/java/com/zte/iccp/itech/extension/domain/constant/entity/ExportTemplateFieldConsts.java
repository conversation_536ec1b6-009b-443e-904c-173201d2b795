package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 网络变更操作单导出模板
 *
 * <AUTHOR> 10309921
 * @date 2025-04-11 上午10:53
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExportTemplateFieldConsts {
    /**
     * 模板名称
     */
    public static final String TEMPLATE_NAME = "template_name";

    /**
     * 模板英文名称
     */
    public static final String TEMPLATE_NAME_EN = "template_name_en";

    /**
     * 模板类型
     */
    public static final String TEMPLATE_TYPE = "template_type";

    /**
     * 数据范围
     */
    public static final String DATA_RANGE = "data_range";

    /**
     * 模板字段
     */
    public static final String TEMPLATE_FIELDS = "template_fields";
}
