package com.zte.iccp.itech.extension.openapi.model.assignment.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.*;

/**
 * <AUTHOR>
 * @create 2025/3/18 上午10：36
 */
@ApiModel("cnop单据批次任务信息VO")
@Getter
@Setter
public class CnopBatchTaskVO {

    @JsonProperty(value = BATCH_NAME)
    @ApiModelProperty("批次任务名称")
    private String batchName;

    @JsonProperty(value = BatchTaskFieldConsts.BATCH_NO)
    @ApiModelProperty("批次号")
    private String batchNo;

    @JsonProperty(value = BATCH_CODE)
    @ApiModelProperty("批次编码")
    private String batchCode;

    @JsonProperty(value = CHANGE_ORDER_ID)
    @ApiModelProperty("网络变更单ID")
    private String changeOrderId;

    @JsonProperty(value = OPERATION_RESULT)
    @ApiModelProperty("操作结果key")
    private String operationResultKey;

    @ApiModelProperty("实际操作开始时间")
    @JsonProperty(value = ACTUAL_OPERATION_START_TIME)
    private Date actualOperationStartTime;

    @ApiModelProperty("实际操作结束时间")
    @JsonProperty(value = ACTUAL_OPERATION_END_TIME)
    private Date actualOperationEndTime;

    @ApiModelProperty("实际业务中断时长(分钟)")
    @JsonProperty(value = FACT_INTERRUPT_TIME)
    private BigDecimal factInterruptTime;

    @ApiModelProperty("测试完成时间")
    @JsonProperty(value = TEST_FINISH_TIME)
    private Date testFinishTime;

}
