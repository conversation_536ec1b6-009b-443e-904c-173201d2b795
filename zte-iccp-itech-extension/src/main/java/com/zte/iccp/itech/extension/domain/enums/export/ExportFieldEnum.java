package com.zte.iccp.itech.extension.domain.enums.export;

import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.enums.export.ExportFieldGroupEnum.*;

/**
 * 导出字段枚举
 * 用于管理导出字段的映射关系和国际化
 * 按照业务分组组织字段
 */
@Getter
public enum ExportFieldEnum {
    // ====================== 网络变更操作申请 =======================//
    /**
     * 任务单号
     */
    ASSIGNMENT_CODE("assignmentCode", "task.no.", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 任务名称
     */
    ASSIGNMENT_NAME("assignmentName", "task.name", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),

    /**
     * 属于GDPR管控项目
     */
    IS_GDPR("isGdpr", "GDPR.controlled.project", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 交付方式
     */
    DELIVERY_MODE("deliveryMode", "delivery.method", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 产品经营团队
     */
    PRODUCT_TEAM("productTeam", "power.speci.model.export.productTeam", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 产品线
     */
    PRODUCT_LINE("productLine", "power.speci.model.export.productLine", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 产品大类
     */
    PRODUCT_MAIN_CATEGORY("productMainCategory", "power.speci.model.export.prodMainCategory", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 产品小类
     */
    PRODUCT_SUB_CATEGORY("productSubCategory", "power.speci.model.export.prodSubCategory", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 操作类型
     */
    OPERATION_TYPE("operationType", "operation.type", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 保障方式
     */
    GUARANTEE_MODE("guaranteeMode", "support.mode", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否需提供详细保障方案
     */
    IS_GUARANTEE_SOLUTION("isGuaranteeSolution", "provide.detailed.support.solution.or.not", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 操作原因
     */
    OPERATION_REASON("operationReason", "operation.reason", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 计划操作开始时间
     */
    OPERATION_START_TIME("operationStartTime", "operation.plan.start.time", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 计划操作结束时间
     */
    OPERATION_END_TIME("operationEndTime", "operation.plan.end.time", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 网元数量
     */
    NE_COUNT("neCount", "ne.number", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 计划操作开始时间（北京时间）
     */
    OPERATION_START_TIME_UTC_8("operationStartTimeUtc8", "operation.plan.start.time.utc.8", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 计划操作结束时间（北京时间）
     */
    OPERATION_END_TIME_UTC_8("operationEndTimeUtc8", "operation.plan.end.time.utc.8", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 客户网络名称
     */
    CUSTOMER_NETWORK_NAME("customerNetworkName", "customer.network.name", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 局点名称
     */
    OFFICE_NAME("officeName", "office.name", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否商用局点
     */
    IS_COMMERCIAL_OFFICE("isCommercialOffice", "is.commercial.use", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否商用收费合同
     */
    IS_COMMERCIAL_CHARGE_CONTRACT("isCommercialChargeContract", "is.business.fee.contract", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * CSC请求单号
     */
    CSC_NO("cscNo", "csc.request.no", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 重要程度
     */
    IMPORTANCE("importance", "importance.degree", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 风险评估
     */
    RISK_EVALUATION("riskEvaluation", "risk.assessment", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 操作等级
     */
    OPERATION_LEVEL("operationLevel", "operation.level", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 操作封装
     */
    OPERATION_ENCAPSULATION("operationEncapsulation", "operation.encapsulation", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 预计业务中断时长（分钟）
     */
    SERVICE_DISCONNECT_DURATION("serviceDisconnectDuration", "expected.business.interruption.duration", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 触发类型
     */
    TRIGGER_TYPE("triggerType", "trigger.type", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否带业务操作
     */
    IS_BUSINESS_OPERATION("isBusinessOperation", "operations.with.services", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否技术通知实施
     */
    IS_TECHNICAL_NOTICE("isTechnicalNotice", "implementation.of.technical.notification", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否特殊场景
     */
    IS_SPECIAL_SCENARIO("isSpecialScenario", "is.special.application.scenarios", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 客户特殊业务
     */
    CUSTOMER_SPECIAL_SERVICE("customerSpecialService", "special.business.customer", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否有任务书
     */
    IS_TASK_STATEMENT("isTaskStatement", "is.upgrade.task", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 任务书是否超过一个月
     */
    TASK_STATEMENT_OVER_MONTH("taskStatementOverMonth", "is.upgrade.task.more.than.a.month", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 是否多模式
     */
    IS_MULTI_MODE("isMultiMode", "is.multimode", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作说明
     */
    OPERATION_DESC("operationDesc", "operating.instructions", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 国家/地区
     */
    COUNTRY("country", "country.region", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 省/州
     */
    PROVINCE("province", "province.state", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 城市
     */
    AREA("area", "city", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 营销
     */
    MARKETING("marketing", "marketing", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 片区
     */
    REGION("region", "region.", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 代表处
     */
    REPRESENTATIVE_OFFICE("representativeOffice", "representative.office", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否政企
     */
    IS_GOVERNMENT_ENTERPRISE("isGovernmentEnterprise", "is.government.enterprise", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),

    /**
     * 客户名称
     */
    CUSTOMER_NAME("customerName", "customer.name", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 客户标识
     */
    ACCN_TYPE("accnType", "customer.label", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 代表处方案评审人
     */
    OFFICE_SOLUTION_REVIEWER("officeSolutionReviewer", "solution.reviewer.in.representative.office", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 附件说明
     */
    ATTACHMENT_DESC("attachmentDesc", "attachment.description", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 需要升级至技术交付部/网络服务处
     */
    IS_UPGRADE_TECHNOLOGY("isUpgradeTechnology", "submit.application.technology.delivery.dept.network.service.office", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 技术交付部/网络服务处要求
     */
    UPGRADE_TECHNOLOGY_REQUIRE("upgradeTechnologyRequire", "need.to.technology.delivery.dept.network.service.office", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 申请人
     */
    APPLY_BY("applyBy", "applicant", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 申请时间
     */
    APPLY_TIME("applyTime", "application.time", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否紧急操作
     */
    IS_EMERGENCY_OPERATION("isEmergencyOperation", "is.emergency.operation", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否网络封网/管控期操作
     */
    IS_NET_CLOSE_OR_CONTROL_OPERATION("isNetCloseOrControlOperation", "is.network.lockdown.and.control.period.operations", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否大区操作
     */
    IS_REGIONAL_OPERATION("isRegionalOperation", "is.regional.operation", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 多产品联动保障
     */
    IS_PRODUCT_LINKAGE_GUARANTEE("isProductLinkageGuarantee", "multi.product.linkage.guarantee", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 产品用途BCN需要多产品联动保障
     */
    IS_PRODUCT_LINKAGE_GUARANTEE_BCN("isProductLinkageGuaranteeBcn", "product.purpose.BCN.needs.meltProduct.support", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 需要网管授权文件
     */
    IS_AUTHORIZATION_FILE("isAuthorizationFile", "is.required.authorization.file", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 变更操作来源
     */
    CHANGE_OPERATION_SOURCE("changeOperationSource", "source.change.operation", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否涉及license文件加载
     */
    LICENSE_LOAD("licenseLoad", "is.license.loading.involved", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 工具落地状态
     */
    TOOL_USE("toolUse", "usage.automatic.tool", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 工具名称
     */
    TOOL_NAME("toolName", "tool.name", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 未使用工具原因
     */
    NOT_USE_TOOL_REASON("notUseToolReason", "reason.not.using.tools", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 时区
     */
    TIME_ZONE("timeZone", "time.zone", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 操作场景
     */
    OPERATION_SCENARIO("operationScenario", "OperationScenario", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 获得客户扫描许可
     */
    IS_CUSTOMER_SCAN_PERMISSION("isCustomerScanPermission", "is.scanning.permitted.customer", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 关联无线产品升级单
     */
    IS_ASSOCIATE_WIRELESS_UPGRADE("isAssociateWirelessUpgrade", "associate.wireless.product.upgrade.ticket", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 无线产品升级单
     */
    WIRELESS_UPGRADE_TICKET("wirelessUpgradeTicket", "wireless.product.upgrade.ticket", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 不关联原因
     */
    NOT_ASSOCIATE_REASON("notAssociateReason", "reason.nonAssociation", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 升级光模块
     */
    IS_UPGRADING_OPTICAL("isUpgradingOptical", "upgrade.optical.module", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 有第三方终端
     */
    IS_THIRD_PARTY_TERMINAL("isThirdPartyTerminal", "is.third.party.terminal", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),


    /**
     * 影响ToB业务
     */
    IS_AFFECT_TO_B("isAffectToB", "is.ToB.services.affected", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),


    /**
     * oVDC/NDVI项目
     */
    RADIOFIELD_OVDC_NFVI_RD_VERIFY("radiofieldOvdcNfviRdVerify", "oVDC.NFVI", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),


    /**
     * 行政领导审核
     */
    IS_ADMINISTRATION_LEADER_APPROVAL("isAdministrationLeaderApproval", "approval.administrative.leaders", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    /**
     * 首次应用
     */
    IS_FIRST_APPLICATION("isFirstApplication", "first.application", NETWORK_CHANGE_APPLICATION, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 网络责任人审核 =======================//
    /**
     * 审核结果
     */
    APPROVE_RESULT_NET_OWNER("approveResultNetOwner", "review.result", NET_OWNER_REVIEW, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核意见
     */
    REVIEW_COMMENT_NET_OWNER("reviewCommentNetOwner", "review.comment", NET_OWNER_REVIEW, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否需要办事处产品经理审核
     */
    IS_REVIEW_OFFICE_PROD_MANAGER("isReviewOfficeProdManager", "review.by.representative.office.product.manager.required", NET_OWNER_REVIEW, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核人
     */
    APPROVED_BY_NET_OWNER("approvedByNetOwner", "reviewer", NET_OWNER_REVIEW, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核时间
     */
    APPROVED_TIME_NET_OWNER("approvedTimeNetOwner", "review.time", NET_OWNER_REVIEW, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),

    // ====================== 办事处产品经理审核 =======================//
    /**
     * 审核结果
     */
    APPROVE_RESULT_OFFICE_PROD_MANAGER("approveResultOfficeProdManager", "review.result", OFFICE_MANAGER, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核意见
     */
    REVIEW_COMMENT_OFFICE_PROD_MANAGER("reviewCommentOfficeProdManager", "review.comment", OFFICE_MANAGER, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 需要升级至网络处
     */
    IS_UPGRADE_NET_DEPT("isUpgradeNetDept", "escalation.to.network.service.office.required", OFFICE_MANAGER, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核人
     */
    APPROVED_BY_OFFICE_PROD_MANAGER("approvedByOfficeProdManager", "reviewer", OFFICE_MANAGER, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核时间
     */
    APPROVED_TIME_OFFICE_PROD_MANAGER("approvedTimeOfficeProdManager", "review.time", OFFICE_MANAGER, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),

    // ====================== 核心网大区TD审核 =======================//
    /**
     * 审核结果
     */
    APPROVE_RESULT_TD_APP_FOR_CN_REGIONAL_TD_REVIEW("approveResultRegionalTdApp", "review.result", CN_REGIONAL_TD_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核意见
     */
    APPROVE_OPINION_TD_APP_FOR_CN_REGIONAL_TD_REVIEW("approveOpinionRegionalTdApp", "review.comment", CN_REGIONAL_TD_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核人
     */
    APPROVED_BY_TD_APP_FOR_CN_REGIONAL_TD_REVIEW("approvedByRegionalTdApp", "reviewer", CN_REGIONAL_TD_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核时间
     */
    APPROVED_TIME_TD_APP_FOR_CN_REGIONAL_TD_REVIEW("approvedTimeRegionalTdApp", "review.time", CN_REGIONAL_TD_REVIEW, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 代表处TD审核 =======================//
    /**
     * 审核结果
     */
    APPROVE_RESULT_TD_APP("approveResultTdApp", "review.result", TD_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核意见
     */
    APPROVE_OPINION_TD_APP("approveOpinionTdApp", "review.comment", TD_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核人
     */
    APPROVED_BY_TD_APP("approvedByTdApp", "reviewer", TD_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核时间
     */
    APPROVED_TIME_TD_APP("approvedTimeTdApp", "review.time", TD_REVIEW, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 技术交付部/网络处审核 =======================//
    /**
     * 审核结果
     */
    APPROVE_RESULT_TD_NET_DEPT_APP("approveResultTdNetDeptApp", "review.result", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核意见
     */
    APPROVE_OPINION_TD_NET_DEPT_APP("approveOpinionTdNetDeptApp", "review.comment", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否需要领导审核
     */
    IS_TD_NET_OFFICE_LEADER_APPROVAL("isTdNetOfficeLeaderApproval", "need.leader.audit", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 技术交付部/网络处领导
     */
    TD_NET_OFFICE_LEADER_APPROVAL_TEAM("tdNetOfficeLeaderApprovalTeam", "technical.delivery.department_network.department.leader", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否需要网络服务部审核
     */
    IS_NET_DEPT_APPROVAL("isNetDeptApproval", "escalation.network.service.dept.required", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 对网络服务部的要求
     */
    REQ_FOR_NET_SERVICE_DEPT("reqForNetServiceDept", "requirements.network.service.dept", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否远程中心执行
     */
    IS_REMOTE_CENTER_SUPPORT("isRemoteCenterSupport", "remote.center.enabled.or.not", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 网络变更操作是否成熟
     */
    IS_NETWORK_CHANGE_OPER_MATURE("isNetworkChangeOperMature", "mature.network.change.operation", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 需要升级到服务产品支持部（IT外购件支持）
     */
    IS_SERV_PROD_SUPPORT_DEPT_APPROVAL("isServProdSupportDeptApproval", "escalation.service.product.support.dept", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 需要升级到SSP支持组
     */
    IS_SSP_SUPPORT_TEAM_APPROVAL("isSspSupportTeamApproval", "escalation.to.ssp.support.team.required", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 需要容量性能评估
     */
    IS_CAPACITY_PERFORMANCE_EVALUATION("isCapacityPerformanceEvaluation", "capacity.and.performance.evaluation.required", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核人
     */
    APPROVED_BY_TD_NET_DEPT_APP("approvedByTdNetDeptApp", "reviewer", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核时间
     */
    APPROVED_TIME_TD_NET_DEPT_APP("approvedTimeTdNetDeptApp", "review.time", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 是否首次应用
     */
    IS_FIRST_APPLICATION_NET_DEPT_APP("isFirstApplicationNetDeptApp", "first.application", TECH_DELIVERY_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 网络服务部审核 =======================//
    /**
     * 审核结果
     */
    APPROVE_RESULT_NET_SERVICE_DEPT_APP("approveResultNetServiceDeptApp", "review.result", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 此版本是否首次商用
     */
    IS_COMMERCIAL_USE_FIRST_TIME("isCommercialUseFirstTime", "firstTime.commercial.use.version", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否首次应用
     */
    IS_FIRST_APPLICATION_NET("isFirstApplicationNet", "first.application", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核意见
     */
    APPROVE_OPINION_NET_SERVICE_DEPT("approveOpinionNetServiceDept", "review.comment", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否需要研发部门审核
     */
    IS_DEV_DEPT_APPROVAL("isDevDeptApproval", "escalation.to.rd.deparatments.required", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 对研发的要求
     */
    RD_REQ("rdReq", "requirement.for.rd", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 早期局
     */
    IS_EARLY_SITE("isEarlySite", "early.verification.site", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 高风险版本
     */
    IS_HIGH_RISK_VERSION("isHighRiskVersion", "high.risk.version", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 重大版本
     */
    IS_MAJOR_VERSION("isMajorVersion", "major.version", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 承载专项保障
     */
    IS_DEDICATED_BEARER_GUARANTEE("isDedicatedBearerGuarantee", "special.support.BN.project", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 方案审核投入人天
     */
    SOLUTION_REVIEW_INPUT_MANDAY("solutionReviewInputManday", "personDays.invested.solution.review", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 中心管理干部挂钩项目
     */
    IS_PROJECT_LINKED_CENTER_MANAG_CADRE("isProjectLinkedCenterManagCadre", "related.RD.center.management.members", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 需要升级至SSP支持组
     */
    IS_SSP_SUPPORT_TEAM_NET("isSspSupportTeamNet", "escalation.SSP.support.team.required", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作方案由测试部提供
     */
    IS_OPER_PLAN_FROM_TEST_DEPT("isOperPlanFromTestDept", "operation.plan.provided.by.testing.dept", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作方案需要验证测试
     */
    IS_OPER_PLAN_NEED_TEST_VERIFY("isOperPlanNeedTestVerify", "verification.test.on.operation.plan.required", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核人
     */
    APPROVED_BY_NET_SERVICE_DEPT("approvedByNetServiceDept", "reviewer", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核时间
     */
    APPROVED_TIME_NET_SERVICE_DEPT("approvedTimeNetServiceDept", "review.time", NETWORK_SERVICE_DEPT_REVIEW, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 研发经理审核 =======================//
    /**
     * 审核结果
     */
    APPROVE_RESULT_RD_MANAGER("approveResultRdManager", "review.result", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核意见
     */
    APPROVE_OPINION_RD_MANAGER("approveOpinionRdManager", "review.comment", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作验证测试
     */
    OPER_TEST_VERIFY_RD_MANAGER("operTestVerifyRdManager", "operation.verification.test", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 联合验证测试
     */
    JOINT_VERIFY_TEST_RD_MANAGER("jointVerifyTestRdManager", "joint.verification.test", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否需要研发领导审核
     */
    IS_DEV_LEADER_APPROVAL("isDevLeaderApproval", "rd.leader.review.required", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 需要容量性能评估
     */
    CAP_PERF_EVAL_RESULT_RD_MANAGER("capPerfEvalResultRdManager", "capacity.and.performance.evaluation.required", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 测试验证结论
     */
    TEST_VERIFY_CONCLUSION_RD_MANAGER("testVerifyConclusionRdManager", "verification.test.conclusion", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核人
     */
    APPROVED_BY_RD_MANAGER("approvedByRdManager", "reviewer", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核时间
     */
    APPROVED_TIME_RD_MANAGER("approvedTimeRdManager", "review.time", RD_MANAGER_REVIEW, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 研发领导审核 =======================//
    /**
     * 审核意见
     */
    APPROVE_OPINION_RD_LEADER("approveOpinionRdLeader", "review.comment", RD_LEADER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核结果
     */
    APPROVE_RESULT_RD_LEADER("approveResultRdLeader", "review.result", RD_LEADER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核人
     */
    APPROVED_BY_RD_LEADER("approvedByRdLeader", "reviewer", RD_LEADER_REVIEW, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 审核时间
     */
    APPROVED_TIME_RD_LEADER("approvedTimeRdLeader", "review.time", RD_LEADER_REVIEW, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 操作计划确认与通告 =======================//
    /**
     * 确认操作开始时间
     */
    PLAN_OPERATION_START_TIME("planOperationStartTime", "confirm.operation.start.time", OPERATION_PLAN_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 确认操作结束时间
     */
    PLAN_OPERATION_END_TIME("planOperationEndTime", "confirm.operation.end.time", OPERATION_PLAN_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 通告发布人
     */
    ANNOUNCER("announcer", "announcement.publisher", OPERATION_PLAN_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 通告发布时间
     */
    ANNOUNCEMENT_RELEASE_TIME("announcementReleaseTime", "announcement.release.time", OPERATION_PLAN_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),

    // ====================== 反馈操作结果 =======================//
    /**
     * 操作回退状态
     */
    IS_RETURNED("isReturned", "operation.rollback.status", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 计划操作是否全部完成
     */
    IS_ALL_COMPLETED("isAllCompleted", "planned.operations.completed", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 计划需求是否全部达成
     */
    IS_ALL_REACH("isAllReach", "planned.requirements.fulfilled", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否有未解决的重大故障
     */
    MAJOR_FAULTS_BEHIND("majorFaultsBehind", "any.unsolved.major.fault", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 重要网络指标是否正常
     */
    NETWORK_INDICATORS("networkIndicators", "important.network.indicators.normal", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否一次性操作完成
     */
    IS_FIRST_OPERATION("isFirstOperation", "operation.completed.at.onetime", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作结果
     */
    OPERATION_RESULT("operationResult", "operation.result", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 实际操作开始时间
     */
    FACT_OPERATION_START_DATE("factOperationStartDate", "actual.operation.start.time", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 实际操作结束时间
     */
    FACT_OPERATION_END_DATE("factOperationEndDate", "actual.operation.end.time", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 多次操作原因
     */
    MULTIPLE_OPERATION_REASON("multipleOperationReason", "reason.for.multiple.operations", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 失败原因分类
     */
    FAILURE_REASON("failureReason", "failure.reason", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作手册齐套性
     */
    IS_OPERATION_MANUAL("isOperationManual", "operation.document.completeness", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作文档指导性评分
     */
    OPERATION_DOC_SCORE("operationDocScore", "operation.document.guidance.score", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作文档可用时间
     */
    CAN_OPERATION_TIME("canOperationTime", "operation.document.availability.time", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 软件及配套文档可用时间
     */
    VERSION_TIME("versionTime", "software.and.supporting.document.availability.time", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 实际投入人天
     */
    FACT_INPUT_DAY("factInputDay", "actual.person.day.invested", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作总结
     */
    OPERATION_SUMMARY("operationSummary", "operation.summary", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 操作人
     */
    OPERATION_PERSON("operationPerson", "operator", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 操作时间
     */
    RESULT_OPERATION_DATE("resultOperationDate", "operation.time", OPERATION_RESULT_FEEDBACK, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),

    // ====================== 审核确认 =======================//
    /**
     * 多次操作原因
     */
    APP_MULTIPLE_OPERATION_REASON("appMultipleOperationReason", "reason.for.multiple.operations", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 失败原因分类
     */
    FAILURE_CATEGORY("failureCategory", "failure.reason", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程支持情况
     */
    REMOTE_SUPPORT("remoteSupport", "remote.support.situation", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 操作人员操作能力评估
     */
    OPERATOR_EVALUATION("operatorEvaluation", "operator.skill.assessment", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 网络变更操作是否成熟
     */
    OPERATION_MATURE("operationMature", "mature.network.change.operation", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 实际投入人天
     */
    ACTUAL_MAN_DAYS("actualManDays", "actual.person.day.invested", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核确认备注
     */
    AUDIT_REMARK("auditRemark", "audit.confirmation.note", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核确认人
     */
    REVIEWED_BY("reviewedBy", "review.confirmed.by", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 审核确认时间
     */
    REVIEW_TIME("reviewTime", "review.confirmation.time", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 复盘是否超期
     */
    IS_REVIEW_OVERDUE("isReviewOverdue", "process.review.overdue_status", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 复盘天数
     */
    REVIEW_DAYS("reviewDays", "process.review.duration_days", REVIEW_CONFIRMATION, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 操作及支持人员列表 =======================//
    /**
     * 现场操作经理姓名
     */
    OPERATION_MANAGER_NAME("operationManagerName", "operation.manager.onsite.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场操作经理部门
     */
    OPERATION_MANAGER_DEPARTMENT("operationManagerDepartment", "operation.manager.onsite.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场操作经理人员属性
     */
    OPERATION_MANAGER_OPERATOR_ATTRIBUTE("operationManagerOperatorAttribute", "operation.manager.onsite.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场操作人员姓名
     */
    OPERATOR_NAME("operatorName", "operator.onsite.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场操作人员部门
     */
    OPERATOR_DEPARTMENT("operatorDepartment", "operator.onsite.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场操作人员人员属性
     */
    OPERATOR_OPERATOR_ATTRIBUTE("operatorOperatorAttribute", "operator.onsite.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场值班人员姓名
     */
    ON_SITE_DUTY_PERSON_NAME("onSiteDutyPersonName", "duty.person.onsite.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场值班人员部门
     */
    ON_SITE_DUTY_PERSON_DEPARTMENT("onSiteDutyPersonDepartment", "duty.person.onsite.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场值班人员人员属性
     */
    ON_SITE_DUTY_PERSON_OPERATOR_ATTRIBUTE("onSiteDutyPersonOperatorAttribute", "duty.person.onsite.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 现场外包人员姓名
     */
    OUTSOURCING_PERSON_NAME("outsourcingPersonName", "outsourcing.person.onsite.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场外包人员部门
     */
    OUTSOURCING_PERSON_DEPARTMENT("outsourcingPersonDepartment", "outsourcing.person.onsite.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场外包人员人员属性
     */
    OUTSOURCING_PERSON_OPERATOR_ATTRIBUTE("outsourcingPersonOperatorAttribute", "outsourcing.person.onsite.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场支持人员姓名
     */
    FIELD_SUPPORT_NAME("fieldSupportName", "field.support.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场支持人员部门
     */
    FIELD_SUPPORT_DEPARTMENT("fieldSupportDepartment", "field.support.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场支持人员人员属性
     */
    FIELD_SUPPORT_OPERATOR_ATTRIBUTE("fieldSupportOperatorAttribute", "field.support.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程电话支持人员姓名
     */
    REMOTE_SUPPORT_NAME("remoteSupportName", "remote.support.phone.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程电话支持人员部门
     */
    REMOTE_SUPPORT_DEPARTMENT("remoteSupportDepartment", "remote.support.phone.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程电话支持人员人员属性
     */
    REMOTE_SUPPORT_OPERATOR_ATTRIBUTE("remoteSupportOperatorAttribute", "remote.support.phone.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场辅助操作人员姓名
     */
    ASSISTANT_OPERATOR_NAME("assistantOperatorName", "assistant.operator.onsite.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场辅助操作人员部门
     */
    ASSISTANT_OPERATOR_DEPARTMENT("assistantOperatorDepartment", "assistant.operator.onsite.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场辅助操作人员人员属性
     */
    ASSISTANT_OPERATOR_OPERATOR_ATTRIBUTE("assistantOperatorOperatorAttribute", "assistant.operator.onsite.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场测试人员姓名
     */
    TESTER_NAME("testerName", "tester.onsite.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场测试人员部门
     */
    TESTER_DEPARTMENT("testerDepartment", "tester.onsite.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场测试人员人员属性
     */
    TESTER_OPERATOR_ATTRIBUTE("testerOperatorAttribute", "tester.onsite.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场交叉检查人员姓名
     */
    CROSS_CHECKER_NAME("crossCheckerName", "cross.check.onsite.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场交叉检查人员部门
     */
    CROSS_CHECKER_DEPARTMENT("crossCheckerDepartment", "cross.check.onsite.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现场交叉检查人员人员属性
     */
    CROSS_CHECKER_OPERATOR_ATTRIBUTE("crossCheckerOperatorAttribute", "cross.check.onsite.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程操作负责人姓名
     */
    OPERATION_MANAGER_NAME_REMOTE("operationManagerNameRemote", "personnel.remote.operation_lead.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程操作负责人部门
     */
    OPERATION_MANAGER_DEPARTMENT_REMOTE("operationManagerDepartmentRemote", "personnel.remote.operation_lead.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程操作负责人人员属性
     */
    OPERATION_MANAGER_OPERATOR_ATTRIBUTE_REMOTE("operationManagerOperatorAttributeRemote", "personnel.remote.operation_lead.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程操作人员姓名
     */
    OPERATOR_NAME_REMOTE("operatorNameRemote", "personnel.remote.operator.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程操作人员部门
     */
    OPERATOR_DEPARTMENT_REMOTE("operatorDepartmentRemote", "personnel.remote.operator.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程操作人员人员属性
     */
    OPERATOR_OPERATOR_ATTRIBUTE_REMOTE("operatorOperatorAttributeRemote", "personnel.remote.operator.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程交叉检查人员姓名
     */
    CROSS_CHECKER_NAME_REMOTE("crossCheckerNameRemote", "personnel.remote.cross_check.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程交叉检查人员部门
     */
    CROSS_CHECKER_DEPARTMENT_REMOTE("crossCheckerDepartmentRemote", "personnel.remote.cross_check.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程交叉检查人员人员属性
     */
    CROSS_CHECKER_OPERATOR_ATTRIBUTE_REMOTE("crossCheckerOperatorAttributeRemote", "personnel.remote.cross_check.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程值守人员姓名
     */
    ON_SITE_DUTY_PERSON_NAME_REMOTE("onSiteDutyPersonNameRemote", "personnel.remote.on_duty.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程值守人员部门
     */
    ON_SITE_DUTY_PERSON_DEPARTMENT_REMOTE("onSiteDutyPersonDepartmentRemote", "personnel.remote.on_duty.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程值守人员人员属性
     */
    ON_SITE_DUTY_PERSON_OPERATOR_ATTRIBUTE_REMOTE("onSiteDutyPersonOperatorAttributeRemote", "personnel.remote.on_duty.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 远程外包人员姓名
     */
    OUTSOURCING_PERSON_NAME_REMOTE("outsourcingPersonNameRemote", "personnel.remote.outsourced.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程外包人员部门
     */
    OUTSOURCING_PERSON_DEPARTMENT_REMOTE("outsourcingPersonDepartmentRemote", "personnel.remote.outsourced.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
     /**
     * 远程外包人员人员属性
     */
    OUTSOURCING_PERSON_OPERATOR_ATTRIBUTE_REMOTE("outsourcingPersonOperatorAttributeRemote", "personnel.remote.outsourced.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程测试人员姓名
     */
    TESTER_NAME_REMOTE("testerNameRemote", "personnel.remote.tester.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程测试人员部门
     */
    TESTER_DEPARTMENT_REMOTE("testerDepartmentRemote", "personnel.remote.tester.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程测试人员人员属性
     */
    TESTER_OPERATOR_ATTRIBUTE_REMOTE("testerOperatorAttributeRemote", "personnel.remote.tester.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程中心支持姓名
     */
    SUPPORT_NAME_REMOTE_CENTER("supportNameRemoteCenter", "personnel.remote.center_support.name", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程中心支持部门
     */
    SUPPORT_DEPARTMENT_REMOTE_CENTER("supportDepartmentRemoteCenter", "personnel.remote.center_support.department", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 远程中心支持人员属性
     */
    SUPPORT_OPERATOR_ATTRIBUTE_REMOTE_CENTER("supportOperatorAttributeRemoteCenter", "personnel.remote.center_support.attribute", OPERATION_PERSONNEL, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 局点信息 =======================//
    /**
     * 局点名称
     */
    OFFICE_NAME_BATCH("officeNameBatch", "office.name", OFFICE_INFO, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 现网版本
     */
    CURRENT_ONLINE_VERSION("currentOnlineVersion", "environment.network.current_version", OFFICE_INFO, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 目标版本
     */
    TARGET_VERSION("targetVersion", "target.version", OFFICE_INFO, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作对象数
     */
    OPERATION_OBJECT_COUNT("operationObjectCount", "operation.target.count", OFFICE_INFO, DataRangeEnum.NETWORK_CHANGE),

    // ====================== CCN Checklist =======================//
    /**
     * 操作时间点是否满足
     */
    OPERATION_TIME_MET("operationTimeMet", "validation.operation.timing.compliance_status", CCN_CHECKLIST, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作时间不满足请提供说明及风险评估
     */
    OPERATION_TIME_ISSUE("operationTimeIssue", "validation.operation.timing.non_compliance", CCN_CHECKLIST, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 备板检查点是否满足
     */
    PREP_CHECK_MET("prepCheckMet", "validation.backup.board.checkpoint_status", CCN_CHECKLIST, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 备板检查不满足请提供说明及风险评估
     */
    PREP_CHECK_ISSUE("prepCheckIssue", "validation.backup.board.non_compliance", CCN_CHECKLIST, DataRangeEnum.NETWORK_CHANGE),

    // ====================== 其他信息 =======================//
    /**
     * 当前进度
     */
    CURRENT_PROGRESS("currentProgress", "current.progress", OTHER_INFO, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 当前处理人
     */
    CURRENT_PROCESSOR("currentProcessor", "current.handler", OTHER_INFO, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 任务状态
     */
    ASSIGNMENT_STATUS("assignmentStatus", "task.status.", OTHER_INFO, DataRangeEnum.NETWORK_CHANGE, DataRangeEnum.SUBCONTRACTOR_NETWORK_CHANGE),
    /**
     * 操作开始时间
     */
    OPERATION_START_TIME_FOR_OTHER("operationStartTimeForOther", "clockInTask.export.operationStartTime", OTHER_INFO, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作结束时间
     */
    OPERATION_END_TIME_FOR_OTHER("operationEndTimeForOther", "clockInTask.export.operationEndTime", OTHER_INFO, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 操作起止时间
     */
    START_AND_END_TIME_OF_OPERATION("startAndEndTimeOfOperation", "clockInTask.export.operationTime", OTHER_INFO, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否外籍独立操作
     */
    IS_FOREIGN_INDEPENDENT_OPERATION("isForeignIndependentOperation", "is.foreign.independent.operation", OTHER_INFO, DataRangeEnum.NETWORK_CHANGE),
    /**
     * 是否本地独立操作
     */
    IS_LOCAL_INDEPENDENT_OPERATION("isLocalIndependentOperation", "is.local.independent.operation", OTHER_INFO, DataRangeEnum.NETWORK_CHANGE);

    /**
     * 属性名称 - 对应VO中的字段名
     */
    private final String propertyName;

    /**
     * 国际化消息键
     */
    private final String messageKey;

    /**
     * 字段所属分组
     */
    private final ExportFieldGroupEnum group;

    /**
     * 字段适用的数据范围
     */
    private final Set<DataRangeEnum> dataRanges;

    /**
     * 构造函数
     *
     * @param propertyName 属性名称
     * @param messageKey 国际化消息键
     * @param group 字段分组
     * @param dataRanges 数据范围
     */
    ExportFieldEnum(String propertyName, String messageKey, ExportFieldGroupEnum group, DataRangeEnum... dataRanges) {
        this.propertyName = propertyName;
        this.messageKey = messageKey;
        this.group = group;
        this.dataRanges = new HashSet<>(Arrays.asList(dataRanges));
    }

    /**
     * 判断字段是否属于指定数据范围
     *
     * @param dataRange 数据范围
     * @return 是否属于该数据范围
     */
    public boolean isInDataRange(DataRangeEnum dataRange) {
        if (dataRange == null) {
            return false;
        }
        return dataRanges.contains(dataRange);
    }

    /**
     * 获取国际化显示名称
     *
     * @return 国际化显示名称
     */
    public String getDisplayName() {
        return MsgUtils.getMessage(messageKey);
    }

    /**
     * 根据字段名称列表获取导出字段枚举列表
     *
     * @param fieldNames 字段名称列表
     * @return 导出字段枚举列表
     */
    public static List<ExportFieldEnum> fromFieldNames(List<String> fieldNames) {
        if (fieldNames == null || fieldNames.isEmpty()) {
            return new ArrayList<>();
        }

        return fieldNames.stream()
                .map(name -> {
                    try {
                        return ExportFieldEnum.valueOf(name);
                    } catch (IllegalArgumentException e) {
                        // 如果找不到对应的枚举值，返回null
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 生成两级表头信息
     * 按照分组顺序生成表头信息，包含每个分组的字段数量
     *
     * @param fields 字段枚举列表
     * @return 两级表头信息列表
     */
    public static List<ExportHeaderInfo> generateTwoLevelHeaders(List<ExportFieldEnum> fields) {
        // 获取有序的分组信息
        Map<ExportFieldGroupEnum, List<ExportFieldEnum>> groups = getOrderedFieldGroups(fields);

        // 创建表头信息列表
        return groups.entrySet().stream()
                .map(entry -> new ExportHeaderInfo(entry.getKey(), entry.getValue().size()))
                .collect(Collectors.toList());
    }

    /**
     * 获取有序的字段分组信息
     * 保持分组的原始顺序，移除空分组
     *
     * @param fields 字段枚举列表
     * @return 有序的分组信息
     */
    public static Map<ExportFieldGroupEnum, List<ExportFieldEnum>> getOrderedFieldGroups(List<ExportFieldEnum> fields) {
        // 创建有序Map
        Map<ExportFieldGroupEnum, List<ExportFieldEnum>> result = new LinkedHashMap<>();

        // 按照枚举定义的顺序添加分组
        for (ExportFieldGroupEnum group : ExportFieldGroupEnum.values()) {
            result.put(group, new ArrayList<>());
        }

        // 将字段添加到对应的分组中
        for (ExportFieldEnum field : fields) {
            result.get(field.getGroup()).add(field);
        }

        // 移除空分组
        result.entrySet().removeIf(entry -> entry.getValue().isEmpty());

        return result;
    }

    /**
     * 表头信息类，用于生成两级表头
     */
    @Getter
    public static class ExportHeaderInfo {
        private final ExportFieldGroupEnum group;
        private final int fieldCount;

        public ExportHeaderInfo(ExportFieldGroupEnum group, int fieldCount) {
            this.group = group;
            this.fieldCount = fieldCount;
        }
    }

    /**
     * 按照分组顺序重新排序字段列表
     * 确保同一分组的字段在一起，同时保持每个分组内部的字段顺序
     *
     * @param fields 原始字段列表
     * @return 排序后的字段列表
     */
    public static List<ExportFieldEnum> sortFieldsByGroup(List<ExportFieldEnum> fields) {
        if (fields == null || fields.isEmpty()) {
            return new ArrayList<>();
        }

        // 按分组对字段进行分类
        Map<ExportFieldGroupEnum, List<ExportFieldEnum>> groupedFields = new LinkedHashMap<>();

        // 初始化所有分组
        for (ExportFieldGroupEnum group : ExportFieldGroupEnum.values()) {
            groupedFields.put(group, new ArrayList<>());
        }

        // 将字段添加到对应的分组中，保持原始顺序
        for (ExportFieldEnum field : fields) {
            groupedFields.get(field.getGroup()).add(field);
        }

        // 按照分组顺序重新组合字段
        List<ExportFieldEnum> sortedFields = new ArrayList<>();
        for (ExportFieldGroupEnum group : ExportFieldGroupEnum.values()) {
            List<ExportFieldEnum> groupFields = groupedFields.get(group);
            if (!groupFields.isEmpty()) {
                sortedFields.addAll(groupFields);
            }
        }

        return sortedFields;
    }
} 
