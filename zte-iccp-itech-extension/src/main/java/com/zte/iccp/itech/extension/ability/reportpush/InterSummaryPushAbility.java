package com.zte.iccp.itech.extension.ability.reportpush;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.openapi.model.reportpush.InterDetailVO;
import com.zte.iccp.itech.extension.openapi.model.reportpush.InterYesterdayFailVO;
import com.zte.iccp.itech.extension.openapi.model.reportpush.InterYesterdaySummaryVO;
import com.zte.iccp.itech.extension.spi.client.IcosClient;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.BusinessConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.TemplateConstants.*;
import static com.zte.iccp.itech.extension.domain.enums.ReportPushTitleEnum.SUM_COUNT;

/**
 * <AUTHOR>
 * @date 2025/5/27 下午8:41
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class InterSummaryPushAbility {
    private static final String INTERNAL_DETAIL_TODAY_APIID = "icos.netchange.report.internal.today.detail.apiId";
    private static final String INTERNAL_DETAIL_TOMORROW_APIID = "icos.netchange.report.internal.tomorrow.detail.apiId";

    public static void interSummaryPush() {
        if (!ConfigHelper.getBoolean("inter.summary.push.enable")) {
            return;
        }

        String todayStr = DateUtils.dateToString(new Date(), DATE_FORMAT);
        String yesterdayStr = DateUtils.dateToString(DateUtils.addDay(new Date(), -1), DATE_FORMAT);

        List<InterYesterdayFailVO> yesterdayFailVos = IcosClient.getInterYesterdayFailDetailVos();

        List<InterDetailVO> todayDetailVos = IcosClient.getInterDetailVos(ConfigHelper.get(INTERNAL_DETAIL_TODAY_APIID));
        List<InterDetailVO> oneTodayDetailVos = todayDetailVos.stream()
                .filter(vo -> ENGINEERING_SERVICE_DEPARTMENT_ONE_ID.equals(vo.getOrganization3Id())).collect(Collectors.toList());
        List<InterDetailVO> twoTodayDetailVos = todayDetailVos.stream()
                .filter(vo -> ENGINEERING_SERVICE_DEPARTMENT_TWO_ID.equals(vo.getOrganization3Id())).collect(Collectors.toList());
        List<InterDetailVO> fiveTodayDetailVos = todayDetailVos.stream()
                .filter(vo -> ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID.equals(vo.getOrganization3Id())).collect(Collectors.toList());
        List<InterDetailVO> mtoTodayDetailVos = todayDetailVos.stream()
                .filter(vo -> ENGINEERING_SERVICE_DEPARTMENT_MTO_ID.equals(vo.getOrganization3Id())).collect(Collectors.toList());

        List<InterDetailVO> tomorrowDetailVos = IcosClient.getInterDetailVos(ConfigHelper.get(INTERNAL_DETAIL_TOMORROW_APIID));
        List<InterDetailVO> oneTomorrowDetailVos = tomorrowDetailVos.stream()
                .filter(vo -> ENGINEERING_SERVICE_DEPARTMENT_ONE_ID.equals(vo.getOrganization3Id())).collect(Collectors.toList());
        List<InterDetailVO> twoTomorrowDetailVos = tomorrowDetailVos.stream()
                .filter(vo -> ENGINEERING_SERVICE_DEPARTMENT_TWO_ID.equals(vo.getOrganization3Id())).collect(Collectors.toList());
        List<InterDetailVO> fiveTomorrowDetailVos = tomorrowDetailVos.stream()
                .filter(vo -> ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID.equals(vo.getOrganization3Id())).collect(Collectors.toList());
        List<InterDetailVO> mtoTomorrowDetailVos = tomorrowDetailVos.stream()
                .filter(vo -> ENGINEERING_SERVICE_DEPARTMENT_MTO_ID.equals(vo.getOrganization3Id())).collect(Collectors.toList());

        Map<String, String> replacements = MapUtils.newHashMap(YESTERDAY, yesterdayStr, YESTERDAY_SUMMARY_PART, getYesterdaySummaryPart(),
                FAIL_COUNT, String.valueOf(yesterdayFailVos.size()), YESTERDAY_FAIL_DETAIL_PART, getYesterdayFailDetailPart(yesterdayFailVos),
                TODAY, todayStr, TODAY_SUMMARY_PART, getSummaryPart(todayDetailVos),
                ONE_COUNT, String.valueOf(oneTodayDetailVos.size()), TODAY_1_DETAIL_PART, getDetailStr(oneTodayDetailVos),
                TWO_COUNT, String.valueOf(twoTodayDetailVos.size()), TODAY_2_DETAIL_PART, getDetailStr(twoTodayDetailVos),
                FIVE_COUNT, String.valueOf(fiveTodayDetailVos.size()), TODAY_5_DETAIL_PART, getDetailStr(fiveTodayDetailVos),
                MTO_COUNT, String.valueOf(mtoTodayDetailVos.size()), TODAY_MTO_DETAIL_PART, getDetailStr(mtoTodayDetailVos),
                TOMORROW_SUMMARY_PART, getSummaryPart(tomorrowDetailVos), TOMORROW_1_DETAIL_PART, getDetailStr(oneTomorrowDetailVos),
                TOMORROW_2_DETAIL_PART, getDetailStr(twoTomorrowDetailVos), TOMORROW_5_DETAIL_PART, getDetailStr(fiveTomorrowDetailVos),
                TOMORROW_MTO_DETAIL_PART, getDetailStr(mtoTomorrowDetailVos));

        ReportPushAbility.sendMail(MsgUtils.getLangMessage(ZH_CN, INTER_SUMMARY_PUSH_TITLE, todayStr),
                StringBuilderUtils.replaceAll(CommonUtils.getTemplateString(INTER_SUMMARY_PUSH_TEMPLATE), replacements),
                ConfigHelper.get(NETCHANGE_QUERY_URL),
                ReportPushAbility.getEmpNos(TECHNOLOGY_DELIVERY_DEPT, null));
    }

    private static String getDetailStr(List<InterDetailVO> oneTodayDetailVos) {
        if (CollectionUtils.isEmpty(oneTodayDetailVos)) {
            return EMPTY_STRING;
        }
        Map<String, List<InterDetailVO>> prodLineId2OneDetailMap = oneTodayDetailVos.stream()
                .collect(Collectors.groupingBy(InterDetailVO::getProdLineId));
        String titleTemplate = CommonUtils.getTemplateString(DETAIL_TABLE_TITLE_LOCATION);
        String template = CommonUtils.getTemplateString(DETAIL_LOCATION);
        StringBuilder stringBuilder = new StringBuilder();
        prodLineId2OneDetailMap.forEach((k, v) -> {
            String prodLine = v.stream().map(InterDetailVO::getProdLine).filter(Objects::nonNull).findFirst().orElse(EMPTY_STRING);
            Map<String, String> titleReplacements = MapUtils.newHashMap(PROD_LINE, prodLine, TOTAL_COUNT, String.valueOf(v.size()));
            String titleTemplateStr = titleTemplate;
            stringBuilder.append(StringBuilderUtils.replaceAll(titleTemplateStr, titleReplacements));

            for (int i = 0; i < v.size(); i++) {
                InterDetailVO vo = v.get(i);
                Map<String, String> replacements = MapUtils.newHashMap(NUMBER, String.valueOf(i + 1),
                        BATCH_CODE, vo.getBatchCode(), OPERATION_SUBJECT, vo.getOperationSubject(),
                        OP_TYPE_ZH, vo.getOpTypeZh(), PROD_MAIN_CATEGORY, vo.getProdMainCategory(),
                        PROD_SUB_CATEGORY, vo.getProdSubCategory(), PLAN_OPERATION_START_TIME, vo.getPlanOperationStartTime(),
                        ORGANIZATION3, vo.getOrganization3(), ORGANIZATION4, vo.getOrganization4(),
                        CUREENT_STATUS, vo.getCurrentStatus(), OP_RESP_EMP, vo.getOpRespEmp(),
                        OP_EMP, vo.getOpEmp(), DUTY_EMP, vo.getDutyEmp(), APPLICATION_DATE, vo.getApplicationDate());
                String templateStr = template;
                stringBuilder.append(StringBuilderUtils.replaceAll(templateStr, replacements));
            }
        });
        return stringBuilder.toString();
    }

    private static String getSummaryPart(List<InterDetailVO> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return EMPTY_STRING;
        }
        Map<String, List<InterDetailVO>> prodLineId2DetailMap = vos.stream()
                .collect(Collectors.groupingBy(InterDetailVO::getProdLineId));
        String template = CommonUtils.getTemplateString(TODAY_SUMMARY_LOCATION);
        StringBuilder stringBuilder = new StringBuilder();
        int i = 0;
        for (Map.Entry<String, List<InterDetailVO>> entry : prodLineId2DetailMap.entrySet()) {
            String prodLine = entry.getValue().stream().map(InterDetailVO::getProdLine)
                    .filter(Objects::nonNull).findFirst().orElse(EMPTY_STRING);
            i = i + 1;
            Map<String, String> replacements = MapUtils.newHashMap(NUMBER, String.valueOf(i),
                    PROD_LINE, prodLine,
                    ONE_COUNT, getSummaryCount(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_ONE_ID),
                    TWO_COUNT, getSummaryCount(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_TWO_ID),
                    FIVE_COUNT, getSummaryCount(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID),
                    MTO_COUNT, getSummaryCount(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_MTO_ID),
                    TOTAL_COUNT, String.valueOf(entry.getValue().size()));
            String templateStr = template;
            stringBuilder.append(StringBuilderUtils.replaceAll(templateStr, replacements));
        }
        Map<String, String> replacements = MapUtils.newHashMap(NUMBER, String.valueOf(i + 1),
                PROD_LINE, SUM_COUNT.getName(),
                ONE_COUNT, getSummaryCount(vos, ENGINEERING_SERVICE_DEPARTMENT_ONE_ID),
                TWO_COUNT, getSummaryCount(vos, ENGINEERING_SERVICE_DEPARTMENT_TWO_ID),
                FIVE_COUNT, getSummaryCount(vos, ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID),
                MTO_COUNT, getSummaryCount(vos, ENGINEERING_SERVICE_DEPARTMENT_MTO_ID),
                TOTAL_COUNT, String.valueOf(vos.size()));
        stringBuilder.append(StringBuilderUtils.replaceAll(template, replacements));
        return stringBuilder.toString();
    }

    private static String getYesterdayFailDetailPart(List<InterYesterdayFailVO> vos) {
        StringBuilder stringBuilder = new StringBuilder();
        String template = CommonUtils.getTemplateString(YESTERDAY_FAIL_DETAIL_LOCATION);
        for (int i = 0; i < vos.size(); i++) {
            String templateStr = template;
            InterYesterdayFailVO vo = vos.get(0);
            Map<String, String> replacements = MapUtils.newHashMap(NUMBER, String.valueOf(i + 1),
                    BATCH_CODE, vo.getBatchCode(), OPERATION_SUBJECT, vo.getOperationSubject(),
                    OP_TYPE_ZH, vo.getOpTypeZh(), PROD_MAIN_CATEGORY, vo.getProdMainCategory(),
                    PROD_SUB_CATEGORY, vo.getProdSubCategory(), PLAN_OPERATION_START_TIME, vo.getPlanOperationStartTime(),
                    ORGANIZATION3, vo.getOrganization3(), ORGANIZATION4, vo.getOrganization4(), CUREENT_STATUS, vo.getCurrentStatus(),
                    OP_RESP_EMP, vo.getOpRespEmp(), OP_EMP, vo.getOpEmp(),
                    DUTY_EMP, vo.getDutyEmp(), OPERATE_RESULT, vo.getOperateResult());
            stringBuilder.append(StringBuilderUtils.replaceAll(templateStr, replacements));
        }
        return stringBuilder.toString();
    }

    private static String getYesterdaySummaryPart() {
        List<InterYesterdaySummaryVO> totalVos = IcosClient.getInterYesterdaySummaryVos();
        if (CollectionUtils.isEmpty(totalVos)) {
            return EMPTY_STRING;
        }
        Map<String, List<InterYesterdaySummaryVO>> yesterSummaryMap = totalVos.stream()
                .collect(Collectors.groupingBy(InterYesterdaySummaryVO::getProdLineId));
        String yesterdaySummaryTem = CommonUtils.getTemplateString(YESTERDAY_SUMMARY_LOCATION);
        StringBuilder yesterdaySummaryPart = new StringBuilder();
        int i = 0;
        for (Map.Entry<String, List<InterYesterdaySummaryVO>> entry : yesterSummaryMap.entrySet()) {
            int count = entry.getValue().stream().mapToInt(InterYesterdaySummaryVO::getOrderCount).sum();
            int failCount = entry.getValue().stream().mapToInt(InterYesterdaySummaryVO::getFailCount).sum();
            String prodLine = entry.getValue()
                    .stream().map(InterYesterdaySummaryVO::getProdLine)
                    .filter(Objects::nonNull).findFirst().orElse(EMPTY_STRING);
            String totalSuccessRate = getSuccessRate(count, failCount);

            String yesterdaySummaryTemStr = yesterdaySummaryTem;
            i = i + 1;
            Map<String, String> replacements = MapUtils.newHashMap(NUMBER, String.valueOf(i),
                    PROD_LINE, prodLine,
                    ONE_COUNT, getCount(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_ONE_ID),
                    ONE_SUCCESS_RATE, getSuccessRate(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_ONE_ID),
                    TWO_COUNT, getCount(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_TWO_ID),
                    TWO_SUCCESS_RATE, getSuccessRate(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_TWO_ID),
                    FIVE_COUNT, getCount(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID),
                    FIVE_SUCCESS_RATE, getSuccessRate(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID),
                    MTO_COUNT, getCount(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_MTO_ID),
                    MTO_SUCCESS_RATE, getSuccessRate(entry.getValue(), ENGINEERING_SERVICE_DEPARTMENT_MTO_ID),
                    TOTAL_COUNT, String.valueOf(count),
                    TOTAL_SUCCESS_RATE, totalSuccessRate);
            yesterdaySummaryPart.append(StringBuilderUtils.replaceAll(yesterdaySummaryTemStr, replacements));
        }
        Map<String, String> replacements = MapUtils.newHashMap(NUMBER, String.valueOf(i + 1),
                PROD_LINE, SUM_COUNT.getName(),
                ONE_COUNT, getCountAndSuccessRate(totalVos, ENGINEERING_SERVICE_DEPARTMENT_ONE_ID).getLeft(),
                ONE_SUCCESS_RATE, getCountAndSuccessRate(totalVos, ENGINEERING_SERVICE_DEPARTMENT_ONE_ID).getRight(),
                TWO_COUNT, getCountAndSuccessRate(totalVos, ENGINEERING_SERVICE_DEPARTMENT_TWO_ID).getLeft(),
                TWO_SUCCESS_RATE, getCountAndSuccessRate(totalVos, ENGINEERING_SERVICE_DEPARTMENT_TWO_ID).getRight(),
                FIVE_COUNT, getCountAndSuccessRate(totalVos, ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID).getLeft(),
                FIVE_SUCCESS_RATE, getCountAndSuccessRate(totalVos, ENGINEERING_SERVICE_DEPARTMENT_FIVE_ID).getRight(),
                MTO_COUNT, getCountAndSuccessRate(totalVos, ENGINEERING_SERVICE_DEPARTMENT_MTO_ID).getLeft(),
                MTO_SUCCESS_RATE, getCountAndSuccessRate(totalVos, ENGINEERING_SERVICE_DEPARTMENT_MTO_ID).getRight(),
                TOTAL_COUNT, getCountAndSuccessRate(totalVos, null).getLeft(),
                TOTAL_SUCCESS_RATE, getCountAndSuccessRate(totalVos, null).getRight());
        yesterdaySummaryPart.append(StringBuilderUtils.replaceAll(yesterdaySummaryTem, replacements));

        return yesterdaySummaryPart.toString();
    }

    private static Pair<String, String> getCountAndSuccessRate(List<InterYesterdaySummaryVO> vos, String orgId) {
        int count;
        int failCount;
        if (null == orgId) {
            count = vos.stream().mapToInt(InterYesterdaySummaryVO::getOrderCount).sum();
            failCount = vos.stream().mapToInt(InterYesterdaySummaryVO::getFailCount).sum();
        } else {
            List<InterYesterdaySummaryVO> orgVos = vos.stream()
                    .filter(vo -> orgId.equals(vo.getOrganization3Id())).collect(Collectors.toList());
            count = orgVos.stream().mapToInt(InterYesterdaySummaryVO::getOrderCount).sum();
            failCount = orgVos.stream().mapToInt(InterYesterdaySummaryVO::getFailCount).sum();
        }

        String countStr = count <= 0 ? EMPTY_STRING : String.valueOf(count);
        String successRate = getSuccessRate(count, failCount);
        return Pair.of(countStr, successRate);

    }

    private static String getSuccessRate(int count, int failCount) {
        if (count <= 0) {
            return EMPTY_STRING;
        }
        return String.format("%.1f%%", (count - failCount) * DOUBLE_HUNDRED / count);
    }

    private static String getSummaryCount(List<InterDetailVO> vos, String orgId) {
        List<InterDetailVO> orgVos = vos.stream()
                .filter(vo -> orgId.equals(vo.getOrganization3Id())).collect(Collectors.toList());
        return CollectionUtils.isEmpty(orgVos) ? EMPTY_STRING : String.valueOf(orgVos.size());
    }

    private static String getCount(List<InterYesterdaySummaryVO> vos, String orgId) {
        return vos.stream()
                .filter(vo -> orgId.equals(vo.getOrganization3Id()))
                .findFirst()
                .map(vo -> String.valueOf(vo.getOrderCount()))
                .orElse(EMPTY_STRING);
    }

    private static String getSuccessRate(List<InterYesterdaySummaryVO> vos, String orgId) {
        return vos.stream()
                .filter(vo -> orgId.equals(vo.getOrganization3Id()))
                .findFirst()
                .map(InterYesterdaySummaryVO::getSuccessPercent)
                .orElse(EMPTY_STRING);
    }


}
