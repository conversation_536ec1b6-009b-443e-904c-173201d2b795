package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.utils.AlarmUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.BATCH_CHANGE;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.BATCH_GUARANTEE_END;

/**
 * 流程插件：批次结束 汇总数据
 *
 * <AUTHOR>
 * @create 2024/7/9 上午9:25
 */
@Slf4j
public class BatchEndPlugin extends BaseFlowPlugin {
    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String flowCode = body.getFlowCode();
        String changeOrderId = (String) body.getVariables().get(BatchTaskFieldConsts.CHANGE_ORDER_ID);
        if (!StringUtils.hasText(changeOrderId)) {
            return false;
        }
        // 内部变更单 如果是保障单主单批次任务 需要把生成保障单批次推到关闭
        if (ApprovalConstants.BATCH_TASK_FLOW.equals(flowCode)) {
            List<BatchTask> batchTasks = BatchTaskAbility.getGuarantyBatchTask(body.getBusinessId());
            try {
                batchTasks.forEach(item -> {
                    // 改变流程变量
                    Map<String, Object> variables = MapUtils.newHashMap(BATCH_CHANGE, BATCH_GUARANTEE_END);
                    FlowHelper.changeFlowParams(item.getId(), variables, ApproveFlowCodeEnum.BATCH_TASK_FLOW);
                    FlowHelper.pushSystemNode(item.getId(), ApprovalConstants.BATCH_SYSTEM_NODE);
                });
            } catch (Exception e) {
                log.error("Guarante batch push error !:{} :{}", body.getNodeElement().getNodeExtendName(), body.getBusinessId());
                AlarmUtils.major("Guarante batch push error", e);
            }
        }
        // d等待5秒，数据刷新后再计算关闭逻辑
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
        }
        BatchTaskAbility.flowEndUpadteAllStatus(flowCode, changeOrderId, body.getBusinessId());
        return false;
    }
}
