package com.zte.iccp.itech.extension.common.utils;

import com.zte.itp.msa.core.model.PageRows;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/08
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PageRowsUtils {
    public static <T> PageRows<T> list2PageRows(List<T> list, int pageNo, int pageSize) {
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = pageNo * pageSize;

        if (fromIndex < 0) {
            fromIndex = 0;
        }

        if (toIndex > list.size()) {
            toIndex = list.size();
        }

        PageRows<T> pageRows = new PageRows<>();
        pageRows.setCurrent(pageNo);
        pageRows.setTotal(list.size());
        pageRows.setRows(list.subList(fromIndex, toIndex));
        return pageRows;
    }
}
