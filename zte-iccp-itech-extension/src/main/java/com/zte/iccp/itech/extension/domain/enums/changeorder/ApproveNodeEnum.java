package com.zte.iccp.itech.extension.domain.enums.changeorder;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveFlowTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdDevMgrConfirm;
import com.zte.iccp.itech.extension.domain.model.subentity.OpAssocProdNetServDeptConfirm;
import com.zte.iccp.itech.extension.domain.model.subentity.support.SupportStaffNetDeptApprove;
import com.zte.iccp.itech.extension.domain.model.subentity.support.SupportStaffNetServiceDeptApprove;
import com.zte.iccp.itech.extension.domain.model.subentity.support.SupportStaffRepProdTdApp;
import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import com.zte.iccp.itech.extension.handler.approver.*;
import com.zte.iccp.itech.extension.handler.approver.admin.InnerAdminHandlerImpl;
import com.zte.iccp.itech.extension.handler.approver.admin.InterAdminHandlerImpl;
import com.zte.iccp.itech.extension.handler.approver.admin.InterBatchAdminHandlerImpl;
import com.zte.iccp.itech.extension.plugin.form.approvaldetails.changeorder.constant.ProcessNodeNameConstants;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.*;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.enums.ApproveFlowTypeEnum.NETWORK_CHANGE;
import static com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum.*;
import static com.zte.iccp.itech.extension.plugin.form.approvaldetails.changeorder.constant.ProcessNodeNameConstants.*;

/**
 * <AUTHOR>
 * &#064;description:  审批节点枚举
 * &#064;date  2024/4/28 19:55
 * zte-iccp-itech-netchange
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ApproveNodeEnum implements SingletonTextValuePairsProvider {

    /**
     * 大区TD审核 --- 定制查询未实现
     */
    REGIONAL_TD_CONFIRM(NETWORK_CHANGE, null,
            Collections.singletonList(REGION_TD_REVIEWER),
            "大区TD审核", "Review by Regional TD", REGIONAL_TD, "PAGE1123918875286155264"),
    /**
     * 代表处产品TD审核  --- 已完成
     */
    REP_PROD_TD_APPROVE(NETWORK_CHANGE, null,
            Collections.singletonList(OFFICE_SOLUTION_REVIEWER),
            "代表处产品TD审核",
            "Review by Product TD of Representative Office", REP_PROD_TD, "PAGE1124002649197883393", SupportStaffRepProdTdApp.class),
    /**
     * 技术交付部/网络处审核 --- 定制查询未实现
     */
    TD_NET_DEPT_APPROVE(NETWORK_CHANGE, null, Lists.newArrayList(
            UPGRADE_TECHNOLOGY_REVIEWER, UPGRADE_TECHNOLOGY_REVIEWER_TEAM),
            "技术交付部/网络处审核",
            "Review by Technology Delivery Dept./Network Service Office", TDD_NET_DEPT, "PAGE1124062156108181505", SupportStaffNetDeptApprove.class),

    /*网络处总工*/
    NETWORK_CHIEF_ENGINEER(NETWORK_CHANGE, new NetworkChiefEngineerHandlerImpl(), null,
            "网络处总工审核",
            "Reviewed by Chief Engineer of the Network Office", NETWORK_CHIEF_ENGINEER_NODE, "PAGE1123931102290489344"),

    /**
     * 	技术交付部网络处领导审核  --- 已完成
     */
    TD_NET_DEPT_LEADER(NETWORK_CHANGE, null,
            Collections.singletonList(TD_NET_OFFICE_LEADER_APPROVAL_TEAM),
            "技术交付部网络处领导审核",
            "Review by Leader of Technology Delivery Dept./Network Service Office",
            TDD_NET_DEPT_LEADER, "PAGE1124356846439284736"),
    /**
     * 远程中心负责人审核
     */
    REMOTE_CENTER_OWNER(NETWORK_CHANGE, null, Lists.newArrayList(
            REMOTE_CENTER_SOLUTION_OWNER, REMOTE_CENTER_SOLUTION_TEAM),
            "远程中心负责人审核",
            "Review by Head of Remote Delivery Center", REMOTE_CENTER_OWNER_APPROVE, "PAGE1124285096275849216"),

    /*远程中心方案提交 */
    REMOTE_CENTER_SCHEME(NETWORK_CHANGE, null,Lists.newArrayList(
            REMOTE_CENTER_SOLUTION_MAKER),
            "远程中心方案提交",
            "Submission of Remote Delivery Center's Solution", REMOTE_CENTER_SCHEME_APPROVE, "PAGE1124287846652948481"),

    /*技术交付部/网络处审核远程中心的方案 */
    NET_DEPT_REMOTE_SCHEME(NETWORK_CHANGE, null,Lists.newArrayList(
            UPGRADE_TECHNOLOGY_REVIEWER, UPGRADE_TECHNOLOGY_REVIEWER_TEAM),
            "技术交付部/网络处审核远程中心的方案", "Review by Technology Delivery Dept" +
            "./Network Service Office (Remote Delivery Center's Solution)", TDD_NET_DEPT_REMOTE_SCHEME, "PAGE1124359543926210560"),

    /*多模从属产品负责人审核*/
    MULTIMODE_PRODUCT_OWNER(NETWORK_CHANGE, new MultimodeProductHandlerImpl(), null,
            "多模从属产品负责人审核",
            "Review by Responsible Person of Multimode Dependent Products", MULTI_PROD_OWNER, "PAGE1124362763142619136"),

    /*网络服务部审核*/
    NET_SERVICE_DEPT_APP(NETWORK_CHANGE, null, Lists.newArrayList(
            NETWORK_SERVICE_DEPT_APPROVER, NETWORK_SERVICE_DEPT_APPROVER_TEAM),
            "网络服务部审核", "Review by Network Service Dept.", NET_SERVICE_DEPT, "PAGE1124365253699366913", SupportStaffNetServiceDeptApprove.class),

    /*网服部一体化关联产品审核*/
    NET_INTEGRATION(NETWORK_CHANGE, new RelProdCountersignHandlerImpl(
            NETWORK_DEPT, OpAssocProdNetServDeptConfirm.class), null,
            "网服部一体化关联产品审核",
            "Integrated Associated Product Review by Network Service Dept.", INTEGER_RELATED_PROD,
            "PAGE1124011450978381825"),

    /*服务产品支持部审核*/
    SERVICE_PROD_SUPPORT(NETWORK_CHANGE, new ServiceProdSupportHandlerImpl(), null,
            "服务产品支持部审核", "Review by Service Product Support Dept.", SERVICE_PRODUCT_SUPPORT_DEPARTMENT,
            "PAGE1124124753209700352"),

    /*SSP产品支持团队审核*/
    SSP_PROD_SUPPORT(NETWORK_CHANGE, null, Collections.singletonList(
            ApproverFieldConsts.SSP_SUPPORT_TEAM_NET),
            "SSP产品支持团队审核", "Review by SSP Support Team", SSP_PRODUCT_SUPPORT_TEAM,
            "PAGE1124128728629751808"),

    /*测试部审核 页面取值*/
    TEST_DEPT(NETWORK_CHANGE, null, Lists.newArrayList(TEST_DEPT_APPROVER),
            "测试部审核", "Review by Testing Dept.", TEST_DEPARTMENT, "PAGE1124130544339759105"),

    /*研发经理审核 页面取值*/
    RD_MANAGER(NETWORK_CHANGE, null, Lists.newArrayList(
            ChangeOrderFieldConsts.ApproverFieldConsts.RD_MANAGER, RD_MANAGER_APPROVE_TEAM),
            "研发经理审核", "Review by R&D Manager", DEV_MANAGER, "PAGE1124132521966678017"),

    /*研发一体化关联产品审核*/
    RD_INTEGRATION(NETWORK_CHANGE, new RelProdCountersignHandlerImpl(
            R_AND_D_DEP, OpAssocProdDevMgrConfirm.class), null,
            "研发一体化关联产品审核", "Integrated Associated Product Review by R&D", DEV_INTEGRATED_RELATED_PRODUCT,
            "PAGE1124259844384731136"),

    /*集成团队审核 暂时不做*/
    INTEGRATION_TEAM_APP(NETWORK_CHANGE, new IntegrationTeamHandlerImpl(), null,
            "集成团队审核", "Review by Integration Team", INTEGRATED_TEAM, "PAGE1124264943353966593"),

    /*研发领导审核 页面取值*/
    RD_LEADER_APP(NETWORK_CHANGE, null, Lists.newArrayList(ChangeOrderFieldConsts.ApproverFieldConsts.RD_LEADER_RD_MANAGER)
            , "研发领导审核", "Review by R&D Leader", DEV_LEADER,"PAGE1124274210219864064"),

    /*远程中心操作实施人指派 */
    REMOTE_CENTER_OPER(NETWORK_CHANGE, new GeneralLogicalHandlerImpl(ApprovalTypeEnum.REMOTE_CENTER_OPERATIONS), null,
            "远程中心操作实施人指派",
            "Remote Delivery Center Operation Executor Assignment", REMOTE_CENTER_OPERATION_IMPLEMENTATION,
            "PAGE1124275664439586817"),

    /*操作计划审核（批次任务）*/
    CHANGED_BY_REP_PROD_CHIEF(NETWORK_CHANGE, new InnerAdminHandlerImpl(PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE), null,
            "操作计划审核", "Review the Operation Plan", REVIEW_OPERATION_PLAN,""),

    /*行政审核_办事处产品科长*/
    ADMIN_REP_PROD_CHIEF(NETWORK_CHANGE, new InnerAdminHandlerImpl(
            PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE), null,
            "行政审核_办事处产品科长",
            "Administrative Review by Product Section Chief of Representative Office", AA_PROD_DIRECTOR_OF_REPRESENTATIVE_OFFICE,
            "PAGE1124350116267454465"),

    /*行政审核_网络服务处总监*/
    ADMIN_NET_PROD_DIR(NETWORK_CHANGE, new InnerAdminHandlerImpl(NETWORK_SERVICE_OFFICE_DIRECTOR), null,
            "行政审核_网络服务处总监",
            "Administrative Review by Product Director of Network Service Office", AA_PROD_DIRECTOR_OF_NETWORK_DEPARTMENT,
            "PAGE1124273767590768640"),

    /*行政审核_电信服务总监*/
    ADMIN_DIR_TELE_SERVICE(NETWORK_CHANGE, new InnerAdminHandlerImpl(DIRECTOR_OF_TELECOM_SERVICES), null,
            "行政审核_电信服务总监",
            "Administrative Review by Telecom Service Director of Network Service Office", AA_DIR_TELE_SERVICE_DEPARTMENT,
            "PAGE1123991865797488640"),

    /*行政审批_办事处副经理*/
    ADMIN_REP_DEPUTY_MNG(NETWORK_CHANGE, new InnerAdminHandlerImpl(
            DEPUTY_MANAGER_OF_THE_REPRESENTATIVE_OFFICE), null,
            "行政审核_办事处副经理",
            "Administrative Review by Deputy Manager of Representative Office", AA_DEPUTY_MANAGER_OF_THE_OFFICE
            ,"PAGE1123989327782195201"),

    /*行政审核_网络服务处经理*/
    ADMIN_NET_DEPT_MNG(NETWORK_CHANGE, new InnerAdminHandlerImpl(
            NETWORK_SERVICE_OFFICE_MANAGER), null,
            "行政审核_网络服务处经理",
            "Administrative Review by Manager of Network Service Office", AA_NETWORK_MANAGER_OF_THE_NETWORK_DEPARTMENT,
            "PAGE1123986747337322497"),

    /*行政审核_网络服务部部长  原网服部四层*/
    ADMIN_NETSERVICE_LV4(NETWORK_CHANGE, new InnerAdminHandlerImpl(
            DIRECTOR_OF_THE_NETWORK_SERVICE_DEPT), null,
            "行政审核_网络服务部部长",
            "Administrative Review by Director of the Network Service Dept.", AA_NETWORK_DEPARTMENT_OF_THE_FOURTH,
            "PAGE1123983645934727168"),

    /*行政审核_网研院院长 原网服部三层*/
    ADMIN_NETSERVICE_LV3(NETWORK_CHANGE, new InnerAdminHandlerImpl(DIRECTOR_OF_THE_INTERNET_RESEARCH_INSTITUTE), null,
            "行政审核_网研院院长",
            "Administrative Review by Network Research Institute Dean", AA_NETWORK_DEPARTMENT_OF_THE_THIRD,
            "PAGE1123977935888953345"),

    /*行政审核_研发中心主任 原研发三层*/
    ADMIN_RD_DEPT_LV3(NETWORK_CHANGE, new InnerAdminHandlerImpl(
            DIRECTOR_OF_THE_R_AND_D_CENTER), null,
            "行政审核_研发中心主任","Administrative Review by Director of the R&D Center", AA_DEV_OF_THE_THIRD,
            "PAGE1123974732040482817"),

    /*行政审核_工程服务国内部总经理 原工服三部部长*/
    ADMIN_ENG_SERVICE3(NETWORK_CHANGE, new InnerAdminHandlerImpl(
            NETWORK_SERVICE_DEPT_GENERAL_MANAGER), null,
            "行政审核_工程服务国内部总经理",
            "Administrative Review by General Manager of the Network Service Office",
            AA_DIRECTOR_OF_THE_THIRD_ENGINEERING_SERVICE_DEPARTMENT,"PAGE1123971267151114241"),

    /*国际行政会签*/
    INTL_ADMIN_APPROVAL(NETWORK_CHANGE, new InterAdminHandlerImpl(), null,
            "国际行政会签", "International Administrative Review", AA_INTERNATIONAL,"PAGE1123913982223691776"),

    /*批次——行政国际审批（和内部变更单保持一致）*/
    BATCH_INTL_ADMIN_APPROVAL(NETWORK_CHANGE, new InterBatchAdminHandlerImpl(), null,
            "国际行政会签", "International Administrative Review", AA_INTERNATIONAL,null),

    //  当前进展状态  无实际审批节点,仅做当前进展状态展示

    /*批次任务执行中*/
    BATCH_APPROVALING(null, null, null, "批次任务执行中",
            "batch task is being executed", ProcessNodeNameConstants.BATCH_APPROVALING, null),

    /*草稿*/
    DRAFT(null, null, null, "草稿", "draft", ProcessNodeNameConstants.DRAFT,null),

    /*驳回待处理*/
    REJECTION(null, null, null,
            "驳回待处理", "rejection to be handled", ProcessNodeNameConstants.REJECTION, null),

    /*流程终止*/
    TERMINATION(null, null, null,
            "流程终止", "process termination", ProcessNodeNameConstants.TERMINATION,null),

    /*操作取消*/
    CANCEL(null, null, null,
            "操作取消", "operation cancel", OPERATION_CANCEL, null),

    /*待发通告*/
    PENDING_NOTIFICATION(null, null, null,
            "待发通告", "pending Notification", BATCH_WAIT_RELEASE_NOTIFY_NODE,""),

    /* 操作取消审核 */
    OPERATION_CANCEL_REVIEW(NETWORK_CHANGE, new InnerAdminHandlerImpl(PRODUCT_SECTION_CHIEF_OF_THE_REPRESENTATIVE_OFFICE), null,
            "操作取消审核", "operation cancel review", BATCH_CANCEL_OPERATION_REVIEW_PN_NODE,null),

    /**
     * 待操作执行
     */
    OPERATION_EXECUTION(null,null,null,
            "待操作执行", "proxy operation execution", ProcessNodeNameConstants.OPERATION_EXECUTION, null),

    /**
     * 待反馈结果
     */
    RESULT_TOBE_BACK(null, null, null,
            "待反馈结果", "result to be back", BATCH_WAIT_FEEDBACK_RESULT, null),

    /*已关闭*/
    CLOSE(null, null, null,
            "已关闭", "close", CLOSED, null),

    ;
    /**
     * 流程类型
     */
    private final ApproveFlowTypeEnum flowType;
    /**
     * 个性化取审批人实现
     */
    private final AbstractRewardApproverHandler handler;

    /**
     * approvalType为null，需要从字段获取时候，对应字段名称
     */
    private final List<String> fieldList;

    /**
     * 中文名称
     */
    private final String zhCn;

    /**
     * 英文名称
     */
    private final String enUs;

    /**
     * 编码
     */
    private final String value;

    /**
     * 审核页PgaeId
     */
    private final String approvalPageId;

    /** 审核节点对应的支持人员实体 */
    private Class<? extends BaseSubEntity> supportStaffEntity;


    ApproveNodeEnum(ApproveFlowTypeEnum flowType,
                    AbstractRewardApproverHandler handler,
                    List<String> fieldList, String zhCn, String enUs, String value,
                    String approvalPageId,
                    Class<? extends BaseSubEntity> supportStaffEntity) {
        this.flowType = flowType;
        this.handler = handler;
        this.fieldList = fieldList;
        this.zhCn = zhCn;
        this.enUs = enUs;
        this.value = value;
        this.approvalPageId = approvalPageId;
        this.supportStaffEntity = supportStaffEntity;
    }

    /**
     * 获取节点类型，防止默认valueOf方法抛出异常
     */
    public static ApproveNodeEnum getApproveNodeEnum(String enumName) {
        for (ApproveNodeEnum value : ApproveNodeEnum.values()) {
            if (value.name().equals(enumName)) {
                return value;
            }
        }
        return null;
    }

    public static String getApproveNodeName(String enumName, String lang) {
        ApproveNodeEnum approveNodeEnum = getApproveNodeEnum(enumName);
        if (null == approveNodeEnum) {
            return EMPTY_STRING;
        }
        return approveNodeEnum.getName(lang);
    }

    /**
     * 获取 技术审批节点
     */
    public static List<String> getApproveNodeEnum() {
        return Arrays.asList(ApproveNodeEnum.REGIONAL_TD_CONFIRM.name(),
                ApproveNodeEnum.REP_PROD_TD_APPROVE.name(),
                ApproveNodeEnum.TD_NET_DEPT_APPROVE.name(),
                ApproveNodeEnum.TD_NET_DEPT_LEADER.name(),
                ApproveNodeEnum.REMOTE_CENTER_OWNER.name(),
                ApproveNodeEnum.REMOTE_CENTER_SCHEME.name(),
                ApproveNodeEnum.NET_DEPT_REMOTE_SCHEME.name(),
                ApproveNodeEnum.MULTIMODE_PRODUCT_OWNER.name(),
                ApproveNodeEnum.NET_SERVICE_DEPT_APP.name(),
                ApproveNodeEnum.NET_INTEGRATION.name(),
                ApproveNodeEnum.SERVICE_PROD_SUPPORT.name(),
                ApproveNodeEnum.SSP_PROD_SUPPORT.name(),
                ApproveNodeEnum.TEST_DEPT.name(),
                ApproveNodeEnum.RD_MANAGER.name(),
                ApproveNodeEnum.RD_INTEGRATION.name(),
                ApproveNodeEnum.INTEGRATION_TEAM_APP.name(),
                ApproveNodeEnum.RD_LEADER_APP.name(),
                ApproveNodeEnum.REMOTE_CENTER_OPER.name(),
                ApproveNodeEnum.CHANGED_BY_REP_PROD_CHIEF.name());
    }

    /**
     * 全量审批节点
     */
    public static List<String> wholeApproveNodeEnum() {
        return Lists.newArrayList(
                ApproveNodeEnum.RD_INTEGRATION.name(),
                ApproveNodeEnum.NET_INTEGRATION.name(),
                ApproveNodeEnum.MULTIMODE_PRODUCT_OWNER.name(),
                ApproveNodeEnum.INTL_ADMIN_APPROVAL.name(),
                ApproveNodeEnum.REGIONAL_TD_CONFIRM.name(),
                ApproveNodeEnum.REP_PROD_TD_APPROVE.name(),
                ApproveNodeEnum.TD_NET_DEPT_APPROVE.name(),
                ApproveNodeEnum.NETWORK_CHIEF_ENGINEER.name(),
                ApproveNodeEnum.TD_NET_DEPT_LEADER.name(),
                ApproveNodeEnum.REMOTE_CENTER_OWNER.name(),
                ApproveNodeEnum.REMOTE_CENTER_SCHEME.name(),
                ApproveNodeEnum.NET_DEPT_REMOTE_SCHEME.name(),
                ApproveNodeEnum.NET_SERVICE_DEPT_APP.name(),
                ApproveNodeEnum.SERVICE_PROD_SUPPORT.name(),
                ApproveNodeEnum.SSP_PROD_SUPPORT.name(),
                ApproveNodeEnum.TEST_DEPT.name(),
                ApproveNodeEnum.RD_MANAGER.name(),
                ApproveNodeEnum.INTEGRATION_TEAM_APP.name(),
                ApproveNodeEnum.RD_LEADER_APP.name(),
                ApproveNodeEnum.REMOTE_CENTER_OPER.name(),
                ApproveNodeEnum.ADMIN_REP_PROD_CHIEF.name(),
                ApproveNodeEnum.ADMIN_NET_PROD_DIR.name(),
                ApproveNodeEnum.ADMIN_DIR_TELE_SERVICE.name(),
                ApproveNodeEnum.ADMIN_REP_DEPUTY_MNG.name(),
                ApproveNodeEnum.ADMIN_NET_DEPT_MNG.name(),
                ApproveNodeEnum.ADMIN_NETSERVICE_LV4.name(),
                ApproveNodeEnum.ADMIN_NETSERVICE_LV3.name(),
                ApproveNodeEnum.ADMIN_RD_DEPT_LV3.name(),
                ApproveNodeEnum.ADMIN_ENG_SERVICE3.name(),
                ApproveNodeEnum.BATCH_INTL_ADMIN_APPROVAL.name(),
                ApproveNodeEnum.CHANGED_BY_REP_PROD_CHIEF.name(),
                ApproveNodeEnum.OPERATION_CANCEL_REVIEW.name())
                ;
    }

    /**
     * 获取会签节点
     */
    public static List<String> getSignApproveNodeEnums() {
        return Lists.newArrayList(NET_INTEGRATION.name(),
                RD_INTEGRATION.name(),
                MULTIMODE_PRODUCT_OWNER.name(),
                INTL_ADMIN_APPROVAL.name(),
                BATCH_INTL_ADMIN_APPROVAL.name());
    }

    /* 获取批次转交责任人节点 */
    public static List<String> getBatchResponsibility() {
        return Arrays.asList(ApproveNodeEnum.PENDING_NOTIFICATION.name(),
                ApproveNodeEnum.RESULT_TOBE_BACK.name(),
                ApproveNodeEnum.OPERATION_EXECUTION.name());
    }

    @Override
    public String getMsgKey() {
        return value;
    }

    public String getName(String lang) {
        return MsgUtils.getLangMessage(lang, getMsgKey());
    }

    public static ApproveNodeEnum fromValue(String name) {
        return Arrays.stream(values()).filter(approveNode -> name.equals(approveNode.name())).findFirst().orElse(null);
    }

    public static Map<String, String> getNodeNames(String language) {
        Map<String, String> nodeNames = new HashMap<>();
        for (ApproveNodeEnum approveNode : ApproveNodeEnum.values()) {
            nodeNames.put(
                    approveNode.name(),
                    LangUtils.get(language, approveNode.getZhCn(), approveNode.getEnUs()));
        }

        return nodeNames;
    }

    /**
     * 电信服务、网络服务处总监
     */
    public static List<String> teleServiceAndNetProdRDirNodes() {
        return Lists.newArrayList(ADMIN_DIR_TELE_SERVICE.name(), ADMIN_NET_PROD_DIR.name());
    }

    /**
     * 通过当前节点code获取当前审批PAGEID
     */
    public static String getApproveNodePageId(String currentProgress) {
        ApproveNodeEnum approveNodeEnum = getApproveNode(currentProgress);
        return approveNodeEnum == null ? "" : approveNodeEnum.getApprovalPageId();
    }

    /**
     * 通过当前节点pageId获取审批枚举
     */
    public static ApproveNodeEnum getApproveNode(String currentProgress) {
        return Stream.of(ApproveNodeEnum.values())
                .filter(node -> node.name().equals(currentProgress))
                .findFirst()
                .orElse(null);
    }
}
