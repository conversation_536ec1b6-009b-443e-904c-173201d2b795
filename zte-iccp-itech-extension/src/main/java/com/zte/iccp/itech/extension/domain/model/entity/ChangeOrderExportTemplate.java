package com.zte.iccp.itech.extension.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.constant.entity.ExportTemplateFieldConsts;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel("网络变更操作单导出模板")
@Setter
@Getter
@BaseEntity.Info("change_order_export_template")
public class ChangeOrderExportTemplate extends BaseEntity {

    @ApiModelProperty("模板名称")
    @JsonProperty(value = ExportTemplateFieldConsts.TEMPLATE_NAME)
    private String templateName;

    @ApiModelProperty("模板英文名称")
    @JsonProperty(value = ExportTemplateFieldConsts.TEMPLATE_NAME_EN)
    private String templateNameEn;

    @ApiModelProperty("模板类型")
    @JsonProperty(value = ExportTemplateFieldConsts.TEMPLATE_TYPE)
    private List<TextValuePair> templateType;

    @ApiModelProperty("数据范围")
    @JsonProperty(value = ExportTemplateFieldConsts.DATA_RANGE)
    private List<TextValuePair> dataRange;

    @ApiModelProperty("模板字段")
    @JsonProperty(value = ExportTemplateFieldConsts.TEMPLATE_FIELDS)
    private List<TextValuePair> templateFields;
}
