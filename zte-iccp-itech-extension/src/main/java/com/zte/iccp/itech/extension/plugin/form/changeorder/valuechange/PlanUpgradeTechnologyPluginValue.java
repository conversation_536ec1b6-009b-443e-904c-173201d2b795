package com.zte.iccp.itech.extension.plugin.form.changeorder.valuechange;

import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.plugin.form.changeorder.helper.ReviewerHelper;
import com.zte.iccp.itech.extension.plugin.form.changeorder.helper.TDDNetServiceReviewerHelper;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.*;
import static com.zte.iccp.itech.extension.plugin.form.approvaldetails.constants.InlinePagePropsConstants.PAGE_STATUS_VALUE_EDIT;

/**
 * 技术交付部/网络处配置审核人（组）插件
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public class PlanUpgradeTechnologyPluginValue implements ValueChangeBaseFormPlugin {

    /**
     * 页面重新加载触发
     *
     * @return
     */
    @Override
    public void loadData(LoadDataEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = formView.getDataModel();
        PageStatusEnum pageStatusEnum = formView.getFormShowParameter().getPageStatus();
        List<ApproverConfiguration> approverConfigurations = TDDNetServiceReviewerHelper.getTechnicalReviewerInfo(dataModel, true);
        if (!PAGE_STATUS_VALUE_EDIT.equals(pageStatusEnum.name())) {
            return;
        }
        List<Employee> employees = EmployeeHelper.getModelEmployees(dataModel.getValue(UPGRADE_TECHNOLOGY_REVIEWER));
        if (!CollectionUtils.isEmpty(employees)) {
            setTechnicalReviewer(approverConfigurations, formView, false, UPGRADE_TECHNOLOGY_REVIEWER);
        }
        List<Employee> employeeTeams = EmployeeHelper.getModelEmployees(dataModel.getValue(UPGRADE_TECHNOLOGY_REVIEWER_TEAM));
        if (!CollectionUtils.isEmpty(employeeTeams)) {
            setTechnicalReviewer(approverConfigurations, formView, true, UPGRADE_TECHNOLOGY_REVIEWER_TEAM);
        }

    }

    /**
     * 页面重新加载触发
     *
     * @return
     */
    @Override
    public void operate(ValueChangedEventArgs args) {
        IFormView formView = args.getFormView();
        IDataModel dataModel = formView.getDataModel();
        List<ApproverConfiguration> approverConfigurations = TDDNetServiceReviewerHelper.getTechnicalReviewerInfo(dataModel, true);
        setTechnicalReviewer(approverConfigurations, formView, false, UPGRADE_TECHNOLOGY_REVIEWER);
        setTechnicalReviewer(approverConfigurations, formView, true, UPGRADE_TECHNOLOGY_REVIEWER_TEAM);
    }

    public void setTechnicalReviewer(List<ApproverConfiguration> approverConfigurations, IFormView formView, Boolean isTeam, String cid) {
        if (CollectionUtils.isEmpty(approverConfigurations)) {
            return;
        }
        if (isTeam) {
            ReviewerHelper.setEmployeeByCid(formView, approverConfigurations.get(0).getApproverGroups(), cid);
            return;
        }
        ReviewerHelper.setEmployeeByCid(formView, approverConfigurations.get(0).getApproverPersons(), cid);
    }
}
