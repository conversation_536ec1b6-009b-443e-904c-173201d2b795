package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.helper.LookupValueHelper;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.common.json.deserializer.*;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.LookupValueConstant;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.changeorder.TriggerTypeEnum;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.zlic.util.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.utils.StackTraceUtils.getStackTrace;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.OPERATION_REASON_NOT_EXISTS;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.ApproverFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.MultiModeProdConsts.PLAN_OPERATION_ORDER_ID;

/**
 * <AUTHOR> 10284287
 * @since 2024/04/27
 */
@Getter
@Setter
@BaseEntity.Info("oc_apply")
@Slf4j
public class ChangeOrder extends BaseEntity implements IChangeOrder {

    // ====================== 网络变更操作单基础字段 =======================
    @JsonProperty(value = ORDER_NO)
    private String orderNo;

    @JsonProperty(value = ChangeOrderFieldConsts.GUARANTEE_MODE)
    @ApiModelProperty("保障方式")
    private List<TextValuePair> guaranteeMode;

    @JsonProperty(value = BILL_STATUS)
    private String billStatus;

    @ApiModelProperty("操作主题 - 网络变更单名称")
    @JsonProperty(value = OPERATION_SUBJECT)
    private String operationSubject;

    @JsonProperty(value = COUNTRY)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText country;

    @JsonProperty(value = PROVINCE)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText province;

    @JsonProperty(value = CITY)
    @JsonDeserialize(using = SingletonMultiLangTextDeserializer.class)
    private MultiLangText city;

    @JsonProperty(value = RESPONSIBLE_DEPT)
    @ApiModelProperty("代表处")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String responsibleDept;

    @JsonProperty(value = PRODUCT_CATEGORY)
    @ApiModelProperty("产品分类")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String productCategory;

    @JsonProperty(value = TRIGGER_TYPE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private TriggerTypeEnum triggerType;

    @JsonProperty(value = MULTI_PROD_GUARANTEE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum multiProdGuarantee;

    @JsonProperty(value = IS_MULTI_MODE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isMultiMode;

    @JsonProperty(value = IS_GOV_ENT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGovEnt;

    @ApiModelProperty("是否属于GDPR管控项目，Y，是；N，否")
    @JsonProperty(value = IS_GDPR)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGdpr;

    @ApiModelProperty("交付方式，1，远程交付；2，本地交付")
    @JsonProperty(value = DELIVERY_MODE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private DeliveryModeEnum deliveryMode;

    @JsonProperty(value = IMPORTANCE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ImportanceEnum importance;

    @JsonProperty(value = RISK_EVALUATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private RiskEvaluationEnum riskEvaluation;

    @JsonProperty(value = OPERATION_LEVEL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private OperationLevelEnum operationLevel;

    @JsonProperty(value = OPERATION_SCENARIO)
    @JsonDeserialize(using = InfraInfoPkIdDeserializer.class)
    private String operationScenario;

    @JsonProperty(value = TdNetDeptApproveConsts.REQ_FOR_NET_SERVICE_DEPT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ReqForNetServiceDeptEnum reqForNetServiceDept;

    @JsonProperty(value = IS_AFFECT_TO_B)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isAffectToB;

    @JsonProperty(value = IS_SPECIAL_SCENARIO)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isSpecialScenario;

    @JsonProperty(value = IS_OPER_PLAN_FROM_TEST_DEPT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isOperPlanFromTestDept;

    @JsonProperty(value = IS_OPER_PLAN_NEED_TEST_VERIFY)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isOperPlanNeedTestVerify;

    @JsonProperty(value = IS_DEV_DEPT_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isDevDeptApproval;

    @JsonProperty(value = IS_EMERGENCY_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isEmergencyOperation;

    @ApiModelProperty("紧急操作原因简述")
    @JsonProperty(value = EMERGENCY_OPERATION_REASON)
    private String emergencyOperationReason;

    @JsonProperty(value = IS_UPGRADE_TECHNOLOGY)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isUpgradeTechnology;

    /** 扩充实体属性 - 是否封网、管控期操作 */
    @JsonProperty(value = IS_NET_CLOSE_OR_CONTROL_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNetCloseOrControlOperation;

    /** 扩充实体属性 - 封网、管控期操作原因简述 */
    @JsonProperty(value = NET_CLOSE_OR_CONTROL_OPERATION_REASON)
    private String netCloseOrControlOperationReason;

    @ApiModelProperty("封网、管控期操作附件")
    @JsonProperty(value = CLOSE_OR_CONTROL_OPERATION_ATTACH)
    private Object closeOrControlOperationAttach;

    @ApiModelProperty("有线产品操作检查单")
    @JsonProperty(value = WIRE_PRODUCT_CHECKLIST)
    private Object wireProductChecks;

    @ApiModelProperty("license文件核对举证")
    @JsonProperty(value = LICENSE_FILE)
    private Object licenseFiles;

    @JsonProperty(value = OPERATION_TYPE)
    @ApiModelProperty("操作类型")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String operationType;

    @ApiModelProperty("操作类型分组")
    @JsonProperty(value = OPERATION_TYPE_GROUP)
    private String operationTypeGroup;

    @JsonProperty(value = OPERATION_REASON)
    @ApiModelProperty("操作原因")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String operationReason;

    @JsonProperty(value = TIME_ZONE)
    @JsonDeserialize(using = LookupValueDeserializer.class)
    private TimeZoneEnum timeZone;

    @ApiModelProperty("计划操作开始时间")
    @JsonProperty(value = OPERATION_START_TIME)
    private Date operationStartTime;

    @ApiModelProperty("计划操作结束时间")
    @JsonProperty(value = OPERATION_END_TIME)
    private Date operationEndTime;

    @JsonProperty(value = INTERNAL_OPERATION_SOLUTION)
    @ApiModelProperty("内部操作方案")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile internalOperationSolution;

    @JsonProperty(value = CUSTOMER_OPERATION_SOLUTION)
    @ApiModelProperty("客户操作方案")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile customerOperationSolution;

    @JsonProperty(value = INTERNAL_OPERATION_SOLUTION_AI)
    @ApiModelProperty("内部操作方案（AI生成）")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile internalOperationSolutionAI;

    @JsonProperty(value = CUSTOMER_OPERATION_SOLUTION_AI)
    @ApiModelProperty("客户操作方案（AI生成）")
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile customerOperationSolutionAI;

    @JsonProperty(value = OTHER_OPERATION_ACCESSORIES)
    @ApiModelProperty("其他操作附件")
    private Object otherOperationSolutionObj;

    @JsonProperty(value = OPERATION_CHECKLIST)
    @ApiModelProperty("操作Checklist")
    private Object operationCheckListObj;

    @JsonProperty(value = OFFLINE_REVIEW_RECORDS)
    @ApiModelProperty("前后方线下评审记录")
    private Object offlineReviewRecordsObj;

    @JsonProperty(value = IS_GUARANTEE_SOLUTION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isGuaranteeSolution;

    @JsonProperty(value = IS_BCN_LINKAGE_GUARANTEE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isBcnLinkageGuarantee;

    @JsonProperty(value = NE_LIST_FILE)
    @JsonDeserialize(using = SingletonAttachmentFileDeserializer.class)
    private AttachmentFile neListFile;

    @JsonDeserialize(using = CustomerIdDeserializer.class)
    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;

    @ApiModelProperty("是否需要操作步骤打卡")
    @JsonProperty(value = IS_CHECK_IN)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isCheckIn;

    @ApiModelProperty("是否需要授权文件")
    @JsonProperty(value = IS_NEED_AUTHORIZATION_FILE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNeedAuthorizationFile;


    @ApiModelProperty("操作预计投入人天")
    @JsonProperty(value = ESTIMATED_INVESTMENT_TIME)
    private Long estimatedInvestmentTime;

    @ApiModelProperty("模型包")
    @JsonProperty(value = MODEL_PACKAGE)
    private Object modelPackage;

    @JsonProperty(value = EMERGENCY_OPERATION_ATTACH)
    @ApiModelProperty("紧急操作附件")
    private Object emergencyOperationAttach;

    @JsonProperty(value = IS_HIGH_RISK_INSTRUCTION)
    @ApiModelProperty("是否需要操作方案打卡")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isHighRiskInstruction;

    @ApiModelProperty("多模从属产品")
    @JsonProperty(value = MULTIMODE_PRODUCT)
    @JsonDeserialize(using = MultiTextValuePairsDeserializer.class)
    private List<TextValuePair> multiProductMode;

    @ApiModelProperty("邮件抄送人")
    @JsonProperty(value = MAIL_COPY)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopyExt;

    @ApiModelProperty("邮件抄送（技术交付部/网络处审核远程方案）")
    @JsonProperty(value = EMAIL_CC_TD_NET_DEPT_APP_SOLU)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy1Ext;

    @ApiModelProperty("邮件抄送（技术交付部/网络处审核）")
    @JsonProperty(value = EMAIL_CC_TD_NET_DEPT_APP)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy2Ext;

    @ApiModelProperty("邮件抄送（研发经理）")
    @JsonProperty(value = EMAIL_CC_RD_MANAGER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy3Ext;

    @ApiModelProperty("邮件抄送（代表处产品TD审核）")
    @JsonProperty(value = EMAIL_CC_REP_PROD_TD_APP)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy4Ext;

    @ApiModelProperty("邮件抄送（大区TD确认）")
    @JsonProperty(value = EMAIL_CC_REGIONAL_TD_CONFIRM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy5Ext;

    @ApiModelProperty("邮件抄送（远程中心负责人确认）")
    @JsonProperty(value = EMAIL_CC_REMOTE_CENTER_OWNER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy6Ext;

    @ApiModelProperty("邮件抄送（网络服务部审核）")
    @JsonProperty(value = EMAIL_CC_NET_SERVICE_DEPT)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy7Ext;

    @ApiModelProperty("邮件抄送（服务产品支持部）")
    @JsonProperty(value = EMAIL_CC_SERVICE_PROD_SUPPORT)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy8Ext;

    @ApiModelProperty("邮件抄送（SSP产品支持团队）")
    @JsonProperty(value = EMAIL_CC_SSP_PROD_SUPPORT)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy9Ext;

    @ApiModelProperty("邮件抄送（测试部）")
    @JsonProperty(value = EMAIL_CC_TEST_DEPT)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy10Ext;

    @ApiModelProperty("邮件抄送（集成团队）")
    @JsonProperty(value = EMAIL_CC_INTEGRATION_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy11Ext;

    @ApiModelProperty("邮件抄送（研发领导）")
    @JsonProperty(value = EMAIL_CC_RD_LEADER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy12Ext;

    @ApiModelProperty("网络变更操作通告抄送（技术交付部/网络处审核）")
    @JsonProperty(value = NETWORK_CHANGE_OPER_NOTICE_CC_TD_NET_DEPT)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy13Ext;

    @ApiModelProperty("网络变更操作通告抄送（研发经理）")
    @JsonProperty(value = CHANGE_OPER_NOTICE_CC_RD_MANAGER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy14Ext;

    @ApiModelProperty("邮件抄送（远程中心方案提交）")
    @JsonProperty(value = EMAIL_CC_REMOTE_CENTER_SCHEME)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy15Ext;

    @ApiModelProperty("邮件抄送（远程中心操作实施指派）")
    @JsonProperty(value = EMAIL_CC_REMOTE_CENTER_OPER_ASSIGN)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> mailCopy16Ext;

    @ApiModelProperty("邮件抄送（网络部总工）")
    @JsonProperty(value = NetworkChiefEngineerConsts.EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> networkChiefEngineerEmail;

    // =================== 网络变更操作单 - 基础扩展字段 ====================
    @ApiModelProperty("是否行政领导审核")
    @JsonProperty(value = IS_ADMINISTRATION_LEADER_APPROVAL)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isAdministrationLeaderApproval;

    @JsonProperty(value = LOGICAL_NE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private String logicalNe;


    // ============= 网络变更操作单 - 远程中心负责人审核扩展字段 ==============
    @ApiModelProperty("是否远程中心执行 - 是否远程中心审核")
    @JsonProperty(value = IS_REMOTE_CENTER_SUPPORT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isRemoteCenterSupport;

    // ============= 网络变更单技术交付部/网络处审核字段 ==============
    @JsonProperty(value = NETWORK_SERVICE_DEPT_APPROVER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> netWorkServiceDeptApprover;

    @JsonProperty(value = NETWORK_SERVICE_DEPT_APPROVER_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> netWorkServiceDeptApproverTeam;

    // =================== 扩展实体 - 网络服务部审核 ========================
    @JsonProperty(value = NetServiceDeptAppFieldConsts.IS_DEDICATED_BEAR_GUARANTEE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isDedicatedBearerGuarantee;

    // ================ 扩展实体 - 行政审批 - 代表处产品科长 =================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminRepProdChiefFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminRepProdChief;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminRepProdChiefFieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminRepProdChief;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminRepProdChiefFieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminRepProdChief;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminRepProdChiefFieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminRepProdChief;

    // ================ 扩展实体 - 行政审批 - 网络处产品总监 =================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminNetProdDirFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminNetProdDirector;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminNetProdDirFieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminNetProdDirector;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminNetProdDirFieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminNetProdDirector;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminNetProdDirFieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminNetProdDirector;

    // ================= 扩展实体 - 行政审批 - 办事处副经理 ==================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminRepDeputyMngFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminRepDeputyManager;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminRepDeputyMngFieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminRepDeputyManager;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminRepDeputyMngFieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminRepDeputyManager;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminRepDeputyMngFieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminRepDeputyManager;

    // ================= 扩展实体 - 行政审批 - 电信服务总监 ==================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminDirTeleSerFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminDirTeleSerDirector;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminDirTeleSerFieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminDirTeleSerDirector;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminDirTeleSerFieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminDirTeleSerDirector;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminDirTeleSerFieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminDirTeleSerDirector;

    // ================ 扩展实体 - 行政审批 - 网络处主管经理 =================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminNetDeptMngFieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminNetDeptMng;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminNetDeptMngFieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminNetDeptMng;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminNetDeptMngFieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminNetDeptMng;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminNetDeptMngFieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminNetDeptMng;

    // ================= 扩展实体 - 行政审批 - 网服部四层 ===================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminNetServiceLV4FieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminNetServiceLV4;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminNetServiceLV4FieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminNetServiceLV4;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminNetServiceLV4FieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminNetServiceLV4;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminNetServiceLV4FieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminNetServiceLV4;

    // ================= 扩展实体 - 行政审批 - 网服部三层 ===================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminNetServiceLV3FieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminNetServiceLV3;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminNetServiceLV3FieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminNetServiceLV3;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminNetServiceLV3FieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminNetServiceLV3;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminNetServiceLV3FieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminNetServiceLV3;

    // ================== 扩展实体 - 行政审批 - 研发三层 ====================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminRdDeptLV3FieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminRdDeptLV3;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminRdDeptLV3FieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminRdDeptLV3;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminRdDeptLV3FieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminRdDeptLV3;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminRdDeptLV3FieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminRdDeptLV3;

    // ================= 扩展实体 - 行政审批 - 工服三部部长 ==================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = AdminEngService3FieldConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultAdminEngService3Leader;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = AdminEngService3FieldConsts.APPROVE_OPINION)
    private String approveOpinionAdminEngService3Leader;

    @ApiModelProperty("审核人")
    @JsonProperty(value = AdminEngService3FieldConsts.APPROVED_BY)
    private List<Employee> approvedByAdminEngService3Leader;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = AdminEngService3FieldConsts.APPROVED_TIME)
    private Date approvedTimeAdminEngService3Leader;

    // ======================== 审批人相关名字段 ==========================
    @JsonProperty(value = UPGRADE_TECHNOLOGY_REVIEWER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> upgradeTechnologyReviewerExt;

    @JsonProperty(value = UPGRADE_TECHNOLOGY_REVIEWER_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> upgradeTechnologyReviewerTeamExt;

    @JsonProperty(value = TD_NET_OFFICE_LEADER_APPROVAL_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> tdNetOfficeLeaderApprovalTeam;

    @JsonProperty(value = NETWORK_DEPT_CHIEF_ENGINEER_APP_TEAM)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> networkDeptChiefEngineerApprovalTeam;

    @JsonProperty(value = OFFICE_SOLUTION_REVIEWER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> officeSolutionReviewer;

    @JsonProperty(value = OFFICE_SOLUTION_REVIEWER_MULTI)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> officeSolutionReviewerMulti;

    @JsonProperty(value = REGION_TD_REVIEWER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> regionTdReviewerExt;

    @JsonProperty(value = ACCN_TYPE)
    @ApiModelProperty("客户标识")
    private String customerTypeFlag;

    @ApiModelProperty("主管经理/副经理")
    @JsonProperty(value = MANAGER)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> managerExt;

    @JsonProperty(value = REMOTE_CENTER_SOLUTION_OWNER)
    @ApiModelProperty("远程中心方案负责人")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> remoteCenterSolutionOwner;

    @JsonProperty(value = REMOTE_CENTER_SOLUTION_TEAM)
    @ApiModelProperty("远程中心方案负责组")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> remoteCenterSolutionTeam;

    @JsonProperty(value = RD_MANAGER)
    @ApiModelProperty("研发经理")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> rdManager;

    @JsonProperty(value = RD_MANAGER_APPROVE_TEAM)
    @ApiModelProperty("研发经理审核组")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> rdManagerApproveTeam;

    @JsonProperty(value = TEST_DEPT_APPROVER)
    @ApiModelProperty("测试部审核人")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> testDeptApprover;

    @JsonProperty(value = SSP_SUPPORT_TEAM_NET)
    @ApiModelProperty("SSP支持组审核人")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> sspSupportTeam;

    @JsonProperty(value = RD_LEADER_RD_MANAGER)
    @ApiModelProperty("研发领导")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> rdLeaderRdManager;

    @JsonProperty(value = REMOTE_CENTER_SOLUTION_MAKER)
    @ApiModelProperty("远程中心方案编写人")
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> solutionMaker;

    @JsonProperty(value = BUSI_INTERRUPT_DURATION)
    @ApiModelProperty("预计业务中断时长")
    private Integer busiInterruptDuration;

    @JsonProperty(value = SOURCE)
    @ApiModelProperty("数据来源")
    private String source;

    @JsonProperty(value = OPERATION_SUBJECT_SUFFIX)
    @ApiModelProperty("操作主题后缀")
    private String operationSubjectSuffix;

    @ApiModelProperty("是否首次应用（首次应用需发起方案会签）")
    @JsonProperty(value = CidConstants.FIELD_IS_FIRST_APPLICATION_CID)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isFirstTimeApply;

    @JsonProperty(value = GDPR_REQUIRE)
    @ApiModelProperty("GDPR要求")
    private Object gdprRequire;

    @JsonProperty(value = IS_BUSINESS_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isBusinessOperation;

    @ApiModelProperty("是否涉及操作关联产品")
    @JsonProperty(value = IS_ASSOC_PROD)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isAssocProd;

    @JsonProperty(value = EMERGENCY_OPERATION_FLAG)
    @ApiModelProperty("紧急操作标记")
    private String emergencyOperationFlag;

    @JsonProperty(value = NETWORK_SEALING_OPERATION_FLAG)
    @ApiModelProperty("封网管控期操作标记")
    private String netWorkSealingOperationFlag;

    @ApiModelProperty("是否大区操作")
    @JsonProperty(value = IS_REGIONAL_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isRegionalOperation;

    @JsonProperty(value = OPERATION_DESC)
    @ApiModelProperty("操作说明")
    private String operationDesc;

    @JsonProperty(value = AWARE_SUBMISSION)
    @ApiModelProperty("我已知晓案例，确认提交")
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum awareSubmission;

    @JsonProperty(value = AWARE_SUBMISSION_HIDDEN)
    @ApiModelProperty("我已知晓案例，确认提交")
    private String awareSubmissionHidden;

    // ================ 研发领导审核 ================
    @ApiModelProperty("审核人")
    @JsonProperty(value = RdLeaderAppConsts.APPROVED_BY)
    private List<Employee> approvedByRdLeader;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = RdLeaderAppConsts.APPROVED_TIME)
    private Date approvedTimeRdLeader;

    // ================ 技术交付部网络处领导审核 ================
    @ApiModelProperty("审核人")
    @JsonProperty(value = TdNetDeptLeaderConsts.APPROVED_BY)
    private List<Employee> approvedByTdNetDeptLeader;

    @ApiModelProperty("审核时间")
    @JsonProperty(value = TdNetDeptLeaderConsts.APPROVED_TIME)
    private Date approvedTimeTdNetDeptLeader;

    // ================ 各节点集合审核意见 ================
    @ApiModelProperty("审核意见")
    @JsonProperty(value = ReginalTdConfirmConsts.APPROVE_OPINION)
    private String approveOpinionRegionalTDConfirm;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = RepProdTdApproveConsts.APPROVE_OPINION)
    private String approveOpinionRepProdTdApprove;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = TdNetDeptApproveConsts.APPROVE_OPINION)
    private String approveOpinionTdNetDeptApprove;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = TdNetDeptLeaderConsts.APPROVE_OPINION)
    private String approveOpinionTdNetDeptLeader;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = RemoteCenterOwnerConsts.APPROVE_OPINION)
    private String approveOpinionRemoteCenterOwner;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = NetDeptRemoteSchemeConsts.APPROVE_OPINION)
    private String approveOpinionNetDeptRemoteScheme;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = ServiceProdSupportConsts.APPROVE_OPINION)
    private String approveOpinionServiceProdSupport;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = SSPProdSupportConsts.APPROVE_OPINION)
    private String approveOpinionSSPProdSupport;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = TestDeptConsts.APPROVE_OPINION)
    private String approveOpinionTestDept;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = RDManagerConsts.APPROVE_OPINION)
    private String approveOpinionRDManager;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = IntegrationTeamAppConsts.APPROVE_OPINION)
    private String approveOpinionIntegrationTeamApp;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = RdLeaderAppConsts.APPROVE_OPINION)
    private String approveOpinionRdLeaderApp;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = RemoteCenterOperConsts.APPROVE_OPINION)
    private String approveOpinionRemoteCenterOper;

    @ApiModelProperty("审核意见")
    @JsonProperty(value = NetServiceDeptAppConsts.APPROVE_OPINION_NET_SERVICE_DEPT)
    private String approveOpinionNetServiceDept;

    // ================ 各节点集合审核结果 ================
    @ApiModelProperty("审核结果")
    @JsonProperty(value = IntegrationTeamAppConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultIntegrationTeam;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = RdLeaderAppConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultRdLeader;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = ReginalTdConfirmConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultRepProdTdApp;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = RDManagerConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultRDManager;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = TestDeptConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultTestDept;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = SSPProdSupportConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultSSPProdSupport;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = ServiceProdSupportConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultServiceProdSupport;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = NetDeptRemoteSchemeConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultNetDeptRemoteScheme;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = TdNetDeptLeaderConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultTdNetDeptLeader;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = TdNetDeptApproveConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultTdNetDeptApprove;

    @ApiModelProperty("审核结果")
    @JsonProperty(value = NetServiceDeptAppConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultNetServiceDeptApp;

    @ApiModelProperty("审核结果(网络处总工)")
    @JsonProperty(value = NetworkChiefEngineerConsts.APPROVE_RESULT)
    private List<TextValuePair> approveResultNetworkChiefEngineerConsts;

    @ApiModelProperty("审核意见(网络处总工)")
    @JsonProperty(value = NetworkChiefEngineerConsts.APPROVE_OPINION)
    private String approveOpinionNetworkChiefEngine;

    @ApiModelProperty("驳回时审核结果，PASS：通过；REJECT：驳回")
    @JsonProperty(value = RESULT_REJECTION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveResultEnum resultRejection;

    @ApiModelProperty("驳回时审核意见")
    @JsonProperty(value = OPINION_REJECTION)
    private String opinionRejection;

    @ApiModelProperty("驳回时审核附件")
    @JsonProperty(value = ATTACHMENT_REJECTION)
    private Object attachmentRejection;

    @ApiModelProperty("驳回时审核人")
    @JsonProperty(value = PERSON_REJECTION)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> personRejection;

    @ApiModelProperty("驳回时节点的自定义编码")
    @JsonProperty(value = REJECTION_EXTEND_CODE)
    private String rejectionExtendCode;

    @JsonProperty(value = PLAN_OPERATION_ORDER_ID)
    @ApiModelProperty("操作计划ID")
    private String planOperationOrderId;

    @ApiModelProperty("冲突")
    private String conflict;

    /**
     * 是否紧急操作拓展(邮件能力支持)
     * @param langId 语种
     * @return （紧急）
     */
    @JsonIgnore
    @Override
    public String isEmergencyOperationExt(String langId) {
        if (this.isEmergencyOperation != null && BoolEnum.Y == isEmergencyOperation) {
            return langId == null || StringUtils.equals(ZH_CN, langId) ? CommonConstants.IS_EMERGENCY_OPERATION_EXT_ZH : CommonConstants.IS_EMERGENCY_OPERATION_EXT_EN;
        }
        return null;
    }

    /**
     * 获取操作类型对应的快码展示名称
     *
     * @return 展示名称
     */
    @JsonIgnore
    @Override
    public String getOperationTypeName(String langId) {
        if (this.operationType != null) {
            LookupValue lookupValue = LookupValueHelper.getLookupValue(
                    LookupValueConstant.OPERATE_TYPE_ENUM,
                    this.operationType);
            if (lookupValue != null) {
                return StringUtils.equals(ZH_CN, langId) ? lookupValue.getMeaningCn() : lookupValue.getMeaningEn();
            }
        }
        return null;
    }

    @Override
    @JsonIgnore
    public List<String> getSaGrantFileReceiver() {
        List<String> reviewers = officeSolutionReviewer == null
                ? Lists.newArrayList()
                : officeSolutionReviewer.stream()
                        .map(Employee::getEmpUIID)
                        .collect(Collectors.toList());
        if (officeSolutionReviewerMulti != null) {
            reviewers.addAll(
                    officeSolutionReviewerMulti.stream()
                            .map(Employee::getEmpUIID)
                            .collect(Collectors.toList()));
        }
        return reviewers;
    }

    @Override
    @JsonIgnore
    public String getOperationReasonZh() {
        LookupValue lookupValue = LookupValueHelper.getLookupValue(
                ZH_CN,
                LookupValueConstant.OPERATE_REASON_ENUM,
                operationReason);
        if (lookupValue == null) {
            throw new LcapBusiException(OPERATION_REASON_NOT_EXISTS);
        }

        return lookupValue.getMeaning();
    }

    @Override
    @JsonIgnore
    public String getOperationReasonEn() {
        LookupValue lookupValue = LookupValueHelper.getLookupValue(
                EN_US,
                LookupValueConstant.OPERATE_REASON_ENUM,
                operationReason);
        if (lookupValue == null) {
            throw new LcapBusiException(OPERATION_REASON_NOT_EXISTS);
        }

        return lookupValue.getMeaning();
    }

    /**
     * 是否属于自动生成保障网络变更单
     */
    @JsonIgnore
    public boolean isSecondaryGuarantyOrder() {
        return !StringUtils.isEmpty(getSource()) && DataSourceEnum.GUARANTEE.name().equals(getSource());
    }

    /**
     * 是否是保障主网络变更单
     */
    @JsonIgnore
    public boolean isPrimaryGuarantyOrder() {
        return BoolEnum.Y == getMultiProdGuarantee() && !DataSourceEnum.GUARANTEE.name().equals(getSource());
    }

    static {
        SaveDataHelper.addAfterUpdateEvent(args -> {
            if (!"oc_apply".equals(EntityHelper.getEntityId(args.getEntity()))) {
                return;
            }

            log.info("ChangeOrder Updated: {}\n{}",
                    JsonUtils.toJsonString(args.getMultiValues()),
                    getStackTrace("com.zte"));
        });
    }
}
