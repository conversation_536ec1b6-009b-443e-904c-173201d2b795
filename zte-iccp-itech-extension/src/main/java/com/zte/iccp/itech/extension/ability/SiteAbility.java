package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.model.vo.TableDisplayRows;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.NetworkElementTableVo;
import com.zte.iccp.itech.extension.spi.model.nis.NetworkElementVo;
import com.zte.iccp.itech.extension.spi.model.query.SiteQuery;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;

/**
 * 局点信息
 *
 * <AUTHOR> 10335201
 * @date 2024-11-08 上午10:36
 **/
public class SiteAbility {

    /**
     * 局点查询
     * @param formView
     * @param dataModel
     * @param resetPage
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/11/8 上午10:37
     */
    public static void queryNetworkNe(IFormView formView, IDataModel dataModel, boolean resetPage) {
        // 1.获取页面查询条件
        SiteQuery siteQuery = new SiteQuery();
        if (resetPage) {
            siteQuery.setPageSize(CommonConstants.INTEGER_TEN);
            siteQuery.setPageNum(CommonConstants.DEFAULT_PAGE_NO);
        } else {
            TablePc tableInfo = (TablePc) formView.getControl(CCN_SITE_QUERY_TABLE_CID);
            siteQuery.setPageSize(tableInfo.getPageSize());
            siteQuery.setPageNum(tableInfo.getCurrentPage());
        }
        siteQuery.setNetworkId(dataModel.getValue(CCN_SITE_QUERY_NETWORK_ID_CID).toString());
        siteQuery.setSubCategoryIds(Lists.newArrayList(dataModel.getValue(CCN_SITE_QUERY_PRODUCE_ID_CID).toString()));
        siteQuery.setCustomerNeName(dataModel.getValue(CCN_SITE_QUERY_CUSTOMER_SITE_NAME_CID).toString());

        // 2.查询 NIS 获取局点数据
        PageRows<NetworkElementVo> pageRows = NisClient.queryNisSiteList(siteQuery);

        // 3.包装查询列表展示对象
        TableDisplayRows<NetworkElementTableVo> tableDisplayRows = getNetworkElementTableVoTableDisplayRows(pageRows);

        // 4.页面数据展示
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(CommonConstants.DATA, JSON.parseObject(JSON.toJSONString(tableDisplayRows)));

        Map<String, Object> viewMap = new HashMap<>();
        viewMap.put(CCN_SITE_QUERY_TABLE_CID, dataMap);
        formView.getClientViewProxy().setProps(viewMap);
    }

    @NotNull
    private static TableDisplayRows<NetworkElementTableVo> getNetworkElementTableVoTableDisplayRows(PageRows<NetworkElementVo> pageRows) {
        TableDisplayRows<NetworkElementTableVo> tableDisplayRows = new TableDisplayRows<>();
        List<NetworkElementVo> networkElementVos = pageRows.getRows();
        if (CollectionUtils.isEmpty(networkElementVos)) {
            tableDisplayRows.setCurrent(CommonConstants.DEFAULT_PAGE_NO.longValue());
            tableDisplayRows.setTotal(CommonConstants.DEFAULT_EMPTY_TOTAL.longValue());
            tableDisplayRows.setRecords(Lists.newArrayList());
            return tableDisplayRows;
        }
        List<NetworkElementTableVo> networkElementTableVos = new ArrayList<>();
        for (NetworkElementVo networkElementVo : networkElementVos) {
            NetworkElementTableVo networkElementTableVo = new NetworkElementTableVo();
            networkElementTableVo.setId(networkElementVo.getId());
            networkElementTableVo.setCustomerNeId(networkElementVo.getCustomerNeId());
            networkElementTableVo.setCustomerNeName(networkElementVo.getCustomerNeName());
            networkElementTableVo.setNeAlias(networkElementVo.getNeAlias());
            networkElementTableVo.setProdOperationTeam(networkElementVo.getProdOperationTeam());
            networkElementTableVo.setProdLine(networkElementVo.getProdLine());
            networkElementTableVo.setProdModel(networkElementVo.getProdModel());
            networkElementTableVo.setProdMainCategory(networkElementVo.getProdMainCategory());
            networkElementTableVo.setProdSubCategory(networkElementVo.getProdSubCategory());
            networkElementTableVos.add(networkElementTableVo);
        }
        tableDisplayRows.setTotal(pageRows.getTotal());
        tableDisplayRows.setCurrent(pageRows.getCurrent());
        tableDisplayRows.setRecords(networkElementTableVos);
        return tableDisplayRows;
    }
}
