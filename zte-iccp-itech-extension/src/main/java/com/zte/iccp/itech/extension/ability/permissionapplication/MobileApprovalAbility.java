package com.zte.iccp.itech.extension.ability.permissionapplication;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.FlowHelper;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.UserTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.PermissionApplication;
import com.zte.iccp.itech.extension.domain.model.base.AttachmentFile;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.subentity.OrganizationApprover;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.ApprovalDetailOpenApi;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo.ApprovalProgressVO;
import com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo.AttachFilesVO;
import com.zte.iccp.itech.extension.openapi.mobile.approval.permission.dto.PermissionApplyDetailDto;
import com.zte.iccp.itech.extension.openapi.mobile.approval.permission.vo.OrganizationApprovalVO;
import com.zte.iccp.itech.extension.openapi.mobile.approval.permission.vo.PermissionApplyDetailVO;
import com.zte.iccp.itech.extension.spi.model.hol.PersonGeneralInfo;
import com.zte.iss.approval.sdk.bean.ApprovalProcessDTO;
import com.zte.iss.approval.sdk.bean.ApprovalTaskRecord;
import com.zte.iss.approval.sdk.bean.FlowApprovalRecordDTO;
import com.zte.iss.approval.sdk.bean.FlowRecordInstanceIdParam;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.helper.FlowHelper.getSystemNodeTask;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.FORWARD_SLASH;

/**
 * 权限申请-移动审批
 *
 * <AUTHOR> 10335201
 * @date 2024-11-29 上午9:54
 **/
public class MobileApprovalAbility {
    /**
     * 查询权限申请单详情
     * @param permissionApplyDetailDto 接口入参
     * @return: com.zte.iccp.itech.extension.openapi.mobile.approvaldetail.permission.vo.PermissionApplyDetailVO
     * @author: 朱小安 10335201
     * @date: 2024/11/28 下午4:54
     */
    public static PermissionApplyDetailVO queryPermissionApplyDetail(PermissionApplyDetailDto permissionApplyDetailDto){
        if(ObjectUtils.isEmpty(permissionApplyDetailDto)|| !StringUtils.hasText(permissionApplyDetailDto.getBillNo())){
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.ORDER_NOT_EXISTS));
        }

        String billNo = permissionApplyDetailDto.getBillNo();
        // 查询单据详情
        PermissionApplication permissionApplication = PermissionApplicationAbility.queryByBillNo(billNo);
        if(ObjectUtils.isEmpty(permissionApplication)){
            return new PermissionApplyDetailVO();
        }

        // 封装返回值
        PermissionApplyDetailVO permissionApplyDetailVO = new PermissionApplyDetailVO();
        String langId = ContextHelper.getLangId();

        //类型 单据编号，任务名称，申请理由，有效时间（直接返回的简单数据）
        permissionApplyDetailVO.setType(permissionApplication.getType());
        permissionApplyDetailVO.setBillNo(billNo);
        permissionApplyDetailVO.setBillName(permissionApplication.getBillName());
        permissionApplyDetailVO.setApplicationReason(permissionApplication.getApplicationReason());
        permissionApplyDetailVO.setExpirationTime(DateUtils.dateToString(permissionApplication.getExpirationTime(), CommonConstants.DATE_FORMAT));



        if (UserTypeEnum.INTERNAL.name().equals(permissionApplication.getType())) {
            permissionApplyDetailVO.setOrganizationApprovals(getOrganizationApprovalVOS(permissionApplication, langId));
        }

        // 模块名称，角色名称，产品，代表处（需要转换的TextValuePair数据）
        getParamByTextValuePair(permissionApplication, permissionApplyDetailVO);

        // 中智&高租权限
        Boolean isZhongZhiHL = permissionApplication.getZhongZhiHighLease();
        if (!ObjectUtils.isEmpty(isZhongZhiHL)) {
            String zhongZhiHighLease = Boolean.TRUE.equals(isZhongZhiHL)
                    ? BoolEnum.Y.getName(langId) : BoolEnum.N.getName(langId);
            permissionApplyDetailVO.setZhongZhiHighLease(zhongZhiHighLease);
        }

        // 附件列表
        Object files = permissionApplication.getFile();
        permissionApplyDetailVO.setAttachFiles(getAttachFiles(files));



        // 启动人，启动时间
        ApprovalProcessDTO approvalProcessDTO = FlowHelper.getApprovalProcessInfo(permissionApplication.getId(), ApproveFlowCodeEnum.PERMISSION_APPLICATION);
        if(!ObjectUtils.isEmpty(approvalProcessDTO)){
            FlowRecordInstanceIdParam flowRecordInstanceIdParam = new FlowRecordInstanceIdParam();
            flowRecordInstanceIdParam.setInsFlowId(approvalProcessDTO.getFlowInstanceId());
            flowRecordInstanceIdParam.setWithDefSchema(true);
            flowRecordInstanceIdParam.setFlowOperate(true);
            flowRecordInstanceIdParam.setHandler(ContextHelper.getEmpNo());
            flowRecordInstanceIdParam.setAppCode(ContextHelper.getUacAppId());
            flowRecordInstanceIdParam.setAppId(ContextHelper.getAppId());
            flowRecordInstanceIdParam.setTenantId(ContextHelper.getTenantId());
            FlowApprovalRecordDTO flowApprovalRecordDTO = ApprovalFlowClient.getFlowRecord(flowRecordInstanceIdParam);

            // 审批进展
            permissionApplyDetailVO.setApprovalProgress(getApprovalProgress(flowApprovalRecordDTO));

            // 启动人，启动时间
            permissionApplyDetailVO.setSubmitDate(approvalProcessDTO.getApplicationDate());
            permissionApplyDetailVO.setSubmitter(ApprovalDetailOpenApi.getSubmitter(approvalProcessDTO.getApplicant()));
        }

        return permissionApplyDetailVO;
    }

    @NotNull
    private static List<OrganizationApprovalVO> getOrganizationApprovalVOS(PermissionApplication permissionApplication, String langId) {
        List<TextValuePair> organizations = permissionApplication.getOrganizations();
        Map<String, String> orgId2NameMap = organizations.stream()
                .collect(Collectors.toMap(
                        item -> item.getValue().split(FORWARD_SLASH)[item.getValue().split(FORWARD_SLASH).length - 1],
                        item -> item.getTextByLanguage(langId)));

        List<OrganizationApprover> approvers = OrganizationApproverAbility.query(Lists.newArrayList(permissionApplication.getId()));
        List<OrganizationApprovalVO> approvalVos = Lists.newArrayList();
        for (OrganizationApprover approver : approvers) {
            OrganizationApprovalVO vo = new OrganizationApprovalVO();
            vo.setOrgId(approver.getOrganization());
            vo.setOrgName(orgId2NameMap.get(approver.getOrganization()));
            vo.setApprover(approver.getApprover().get(0).getTextByLanguage(langId));
            approvalVos.add(vo);
        }
        return approvalVos;
    }

    /**
     * 处理TextValuePair类型的返回值
     * @param permissionApplication 单据实体
     * @param permissionApplyDetailVO 返回值
     * @return: void
     * @author: 朱小安 10335201
     * @date: 2024/11/28 下午4:53
     */
    private static void getParamByTextValuePair(PermissionApplication permissionApplication, PermissionApplyDetailVO permissionApplyDetailVO) {
        String langId = ContextHelper.getLangId();
        List<TextValuePair> moduleNames = permissionApplication.getModuleName();
        if(!CollectionUtils.isEmpty(moduleNames)){
            String moduleName = moduleNames.get(0).getTextByLanguage(langId);
            permissionApplyDetailVO.setModuleName(moduleName);
        }

        List<TextValuePair> roleNames = permissionApplication.getRoleName();
        if(!CollectionUtils.isEmpty(roleNames)){
            String roleName = roleNames.get(0).getTextByLanguage(langId);
            permissionApplyDetailVO.setRoleName(roleName);
        }

        List<TextValuePair> products = permissionApplication.getProduct();
        if(!CollectionUtils.isEmpty(products)){
            StringBuilder sb = new StringBuilder();
            products.forEach(product -> sb.append(product.getTextByLanguage(langId)).append(CommonConstants.COMMA));
            String productName = StringUtils.hasText(sb)?sb.substring(0,sb.length()-1):null;
            permissionApplyDetailVO.setProduct(productName);
        }

        List<TextValuePair> organizations = permissionApplication.getOrganizations();
        if(!CollectionUtils.isEmpty(organizations) && UserTypeEnum.EXTERNAL.name().equals(permissionApplication.getType())){
            StringBuilder sb = new StringBuilder();
            organizations.forEach(organization -> sb.append(organization.getTextByLanguage(langId)).append(CommonConstants.COMMA));
            String organizationName = StringUtils.hasText(sb)?sb.substring(0,sb.length()-1):null;
            permissionApplyDetailVO.setOrganization(organizationName);
        }
    }

    /**
     * 获取审批意见
     * @param flowApprovalRecordDTO
     * @return: java.util.List<com.zte.iccp.itech.extension.openapi.mobile.approval.changerorder.vo.ApprovalProgressVO>
     * @author: 朱小安 10335201
     * @date: 2024/12/3 下午4:29
     */
    private static List<ApprovalProgressVO> getApprovalProgress(FlowApprovalRecordDTO flowApprovalRecordDTO) {
        if (ObjectUtils.isEmpty(flowApprovalRecordDTO)||CollectionUtils.isEmpty(flowApprovalRecordDTO.getApprovalTaskRecordList())) {
            return Lists.newArrayList();
        }

        List<ApprovalTaskRecord> records = flowApprovalRecordDTO.getApprovalTaskRecordList();
        List<ApprovalProgressVO> approvalProgressVOList = new ArrayList<>();
        List<String> approverIdList = records.stream().map(ApprovalTaskRecord::getApprover).collect(Collectors.toList());
        Map<String, PersonGeneralInfo> approverMap = ApprovalDetailOpenApi.getPersonGeneralInfos(approverIdList);
        records.forEach(approveRecord -> {
            ApprovalProgressVO approvalProgressVO = new ApprovalProgressVO();
            approvalProgressVO.setApprovalStatus(approveRecord.getTaskStatus());
            approvalProgressVO.setApprovalNode(approveRecord.getExtendedCode());
            approvalProgressVO.setApprovalOpinion(approveRecord.getOpinion());
            approvalProgressVO.setApprovalDate(approveRecord.getApprovalDate());
            approvalProgressVO.setApprovalResult(approveRecord.getResult());
            if (approverMap.containsKey(approveRecord.getApprover())) {
                approvalProgressVO.setApprovers(Lists.newArrayList(approverMap.get(approveRecord.getApprover())));
            } else {
                PersonGeneralInfo personGeneralInfo = new PersonGeneralInfo();
                personGeneralInfo.setEmpUIID(approveRecord.getApprover());
                approvalProgressVO.setApprovers(Lists.newArrayList(personGeneralInfo));
            }
            approvalProgressVOList.add(approvalProgressVO);
        });

        return approvalProgressVOList;
    }

    /**
     * 获取附件列表
     * @param files 权限申请单附件信息
     * @return: java.util.List<com.zte.iccp.itech.extension.openapi.mobile.approvaldetail.changerorder.vo.AttachFilesVO>
     * @author: 朱小安 10335201
     * @date: 2024/11/28 下午4:30
     */
    private static List<AttachFilesVO> getAttachFiles(Object files) {
        List<AttachFilesVO> attachFilesVOList = Lists.newArrayList();
        if(ObjectUtils.isEmpty(files)){
            return attachFilesVOList;
        }

        List<AttachmentFile> attachmentFiles = JsonUtils.parseArray(files,AttachmentFile.class);
        for (AttachmentFile attachmentFile : attachmentFiles) {
            AttachFilesVO attachFilesVO = new AttachFilesVO();
            attachFilesVO.setId(attachmentFile.getId());
            attachFilesVO.setAttachFileName(attachmentFile.getName());
            attachFilesVO.setAttachFileSize(attachmentFile.getSize());
            attachFilesVO.setFileKey(attachmentFile.getFileKey());
            attachFilesVOList.add(attachFilesVO);
        }
        return attachFilesVOList;
    }
}
