package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.common.json.provider.SingletonTextValuePairsProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/08
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum ApprovalLevelEnum implements SingletonTextValuePairsProvider {
    /** 一级审批 */
    LVL1("一级审核", "1st-level Approval", "1"),
    /** 二级审批 */
    LVL2("二级审核", "2nd-level Approval", "2"),
    /** 三级审批 */
    LVL3("三级审核", "3rd-level Approval", "3"),
    ;

    private final String zhCn;

    private final String enUs;

    private final String value;

    public static ApprovalLevelEnum fromValue(String value) {
        for (ApprovalLevelEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }

        throw new IllegalArgumentException();
    }
}
