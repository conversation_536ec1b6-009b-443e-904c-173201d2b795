package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DataSourceEnum;
import com.zte.iccp.itech.extension.domain.enums.PersonnelScoreLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.ManageFeedbackTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.technologyorder.TaskCategoryEnum;
import com.zte.iccp.itech.extension.domain.model.ManageSubTask;
import com.zte.iccp.itech.extension.domain.model.ManageSubTaskFlow;
import com.zte.iccp.itech.extension.domain.model.TechnologyManagementOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.openapi.model.itech.PersonnelScoreTechManagementTaskDto;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.model.hr.vo.EmployeeInfo;
import com.zte.iccp.itech.extension.spi.model.zxrdc.TreeServiceObjectDto;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.core.orm.query.IFilter;
import com.zte.paas.lcap.ddm.domain.flow.dto.SaveBizAndStartFlowDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.common.helper.TaskNoHelper.TECHNOLOGY_MANAGEMENT_TASK_CODE;
import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.DEFAULT_APPROVER_KEY;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.BASIC_FIELD_BUSINESS_ID;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_BILL_TECHNOLOGY_MANAGEMENT_BILL;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts.SOURCE_ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFieldConsts.SUBTASK_CN_NO;
import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFieldConsts.SUBTASK_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFlowFieldConsts.*;

/**
 * 技术管理任务数据库操作
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/4
 */
public class ManageTaskAbility {


    /**
     * 更新子任务流程表状态
     *
     * @param subTaskFlow subTaskFlow
     */
    public static void update(ManageSubTaskFlow subTaskFlow) {
        if (Objects.isNull(subTaskFlow)) {
            return;
        }
        subTaskFlow.clearEntityValue();
        SaveDataHelper.update(subTaskFlow);
    }

    /**
     * 更新子任务流程表状态
     *
     * @param pkId                 子任务流程表id
     * @param assignmentStatusEnum 任务状态枚举
     */
    public static void updateSubFlowStatusByPkId(String pkId, AssignmentStatusEnum assignmentStatusEnum) {
        if (pkId == null || assignmentStatusEnum == null) {
            return;
        }
        ManageSubTaskFlow manageSubTaskFlow = new ManageSubTaskFlow();
        manageSubTaskFlow.setId(pkId);
        manageSubTaskFlow.setStatus(assignmentStatusEnum.getValue());
        SaveDataHelper.update(manageSubTaskFlow);
    }

    /**
     *  根据子任务id、关联父id查询子任务流程表数据集
     *
     * @param parentTaskId 父任务id
     * @param subTaskId 子任务id
     * @return 子任务流程表结果集
     */
    public static List<ManageSubTaskFlow> query(String subTaskId, String parentTaskId) {
        if (!StringUtils.hasText(subTaskId) && !StringUtils.hasText(parentTaskId)) {
            return null;
        }

        List<IFilter> conditionFilterList = new ArrayList<>();
        if (StringUtils.hasText(subTaskId)) {
            Filter networkIdFilter = new Filter(APPROVER_SUB_TASK_ID, Comparator.EQ, subTaskId);
            conditionFilterList.add(networkIdFilter);
        }

        if (StringUtils.hasText(parentTaskId)) {
            Filter networkIdFilter = new Filter(APPROVER_PARENT_TASK_ID, Comparator.EQ, parentTaskId);
            conditionFilterList.add(networkIdFilter);
        }
        return QueryDataHelper.query(ManageSubTaskFlow.class, Lists.newArrayList(), conditionFilterList);
    }

    /**
     *  根据任务id查询主任务信息
     *
     * @param pkId 任务id
     * @return 主任务信息
     */
    public static ManageSubTaskFlow queryBySubFlowId(String pkId) {
        if (pkId == null) {
            return null;
        }
        Filter conditionFilter = new Filter(ID, Comparator.EQ, pkId);
        List<IFilter> conditionFilterList = Lists.newArrayList(conditionFilter);

        return QueryDataHelper.queryOne(ManageSubTaskFlow.class, Lists.newArrayList(), conditionFilterList);
    }

    /**
     * 检索 技术管理子任务 当前进展
     * @param idList
     * @return Map<String, String>
     */
    public static Map<String, String> querySubCurrentProgress(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new HashMap<>();
        }

        List<String> fieldList = Lists.newArrayList(ID, APPROVER_PROGRESS_DESCRIPTION);

        IFilter idFilter = new Filter(ID, Comparator.IN, idList);
        List<IFilter> conditionFilterList = Lists.newArrayList(idFilter);

        List<ManageSubTaskFlow> orderList = QueryDataHelper.query(ManageSubTaskFlow.class, fieldList, conditionFilterList);
        return orderList.stream()
                .filter(item -> StringUtils.hasText(item.getProgressDescription()))
                .collect(Collectors.toMap(BaseEntity::getId, ManageSubTaskFlow::getProgressDescription));
    }

    //----------------主任务表------------------
    /**
     * 根据主键id更新技术管理任务主任务信息
     *
     * @param managementOrder managementOrder
     */
    public static void update(TechnologyManagementOrder managementOrder) {
        if (Objects.isNull(managementOrder) || managementOrder.getId() == null) {
            return;
        }
        managementOrder.clearEntityValue();
        SaveDataHelper.update(managementOrder);
    }

    /**
     * 更新主任务状态
     *
     * @param pkId                 子任务流程表id
     * @param assignmentStatusEnum 任务状态枚举
     */
    public static void updateTaskStatusByPkId(String pkId, AssignmentStatusEnum assignmentStatusEnum) {
        if (pkId == null || assignmentStatusEnum == null) {
            return;
        }
        TechnologyManagementOrder manageSubTaskFlow = new TechnologyManagementOrder();
        manageSubTaskFlow.setId(pkId);
        manageSubTaskFlow.setStatus(assignmentStatusEnum.getValue());
        SaveDataHelper.update(manageSubTaskFlow);
    }

    /**
     *  根据任务id查询主任务信息
     *
     * @param pkId 任务id
     * @return 主任务信息
     */
    public static TechnologyManagementOrder queryById(String pkId) {
        if (pkId == null) {
            return null;
        }
        Filter conditionFilter = new Filter(ID, Comparator.EQ, pkId);
        List<IFilter> conditionFilterList = Lists.newArrayList(conditionFilter);

        return QueryDataHelper.queryOne(TechnologyManagementOrder.class, Lists.newArrayList(), conditionFilterList);
    }

    /**
     *  根据任务id查询主任务信息
     *
     * @param ids 任务id
     * @return 主任务信息
     */
    public static List<TechnologyManagementOrder> query(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return QueryDataHelper.query(TechnologyManagementOrder.class, Lists.newArrayList(),
                Lists.newArrayList(new Filter(ID, Comparator.IN, ids)));
    }

    /**
     * 检索 技术管理主任务 当前进展
     * @param idList
     * @return Map<String, String>
     */
    public static Map<String, String> queryMainCurrentProgress(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new HashMap<>();
        }

        List<String> fieldList = Lists.newArrayList(ID, APPROVER_PROGRESS_DESCRIPTION);

        IFilter idFilter = new Filter(ID, Comparator.IN, idList);
        List<IFilter> conditionFilterList = Lists.newArrayList(idFilter);

        List<TechnologyManagementOrder> orderList = QueryDataHelper.query(TechnologyManagementOrder.class, fieldList, conditionFilterList);
        return orderList.stream()
                .filter(item -> StringUtils.hasText(item.getProgressDescription()))
                .collect(Collectors.toMap(BaseEntity::getId, TechnologyManagementOrder::getProgressDescription));
    }

    /**
     * 检索 技术管理主任务 审批状态责任人
     * key - assignmentId   value - 责任人信息
     * @param ids
     * @return Map<String, List<Employee>>
     */
    public static Map<String, List<Employee>> queryApprovingResponsible(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }

        List<String> fields = Lists.newArrayList(ID, ManageTaskFieldConsts.ACCEPTOR_PERSON);

        IFilter idFilter = new Filter(ID, Comparator.IN, ids);
        List<IFilter> filters = Lists.newArrayList(idFilter);

        List<TechnologyManagementOrder> orders = QueryDataHelper.query(TechnologyManagementOrder.class, fields, filters);
        return orders
                .stream()
                .collect(Collectors.toMap(BaseEntity::getId, TechnologyManagementOrder::getAcceptorPerson));
    }

    //----------------子任务单据体------------------

    /**
     * 查询子任务单据体数据
     *
     * @param taskId 任务id
     * @param pId 父id
     * @return 子任务单据头对象
     */
    public static ManageSubTask querySubTask(String taskId, String pId) {
        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(pId)) {
            return null;
        }

        //2.查询子任务单据体信息
        return QueryDataHelper.queryOne(ManageSubTask.class, Lists.newArrayList(), pId,
                Lists.newArrayList(new Filter(ID, Comparator.EQ, taskId)));
    }


    /**
     * 根据子任务列表和主任务id获取子任务数据列表
     *
     * @param taskIds 子任务id列表
     * @param pId 主任务id
     * @return 子任务数据集
     */
    public static List<ManageSubTask> querySubTaskList(List<String> taskIds, String pId) {
        if (CollectionUtils.isEmpty(taskIds) || StringUtils.isEmpty(pId)) {
            return new ArrayList<>();
        }

        return QueryDataHelper.query(ManageSubTask.class, Lists.newArrayList(), pId,
                Lists.newArrayList(new Filter(ID, Comparator.IN, taskIds)));

    }


    /**
     * 技术管理子任务更新状态和属性
     * 1.因为子任务为子单据体，同时还要作为自定义页面的值进行展示，状态字段只能用下拉单选
     *
     * @param id id
     * @param pId pid
     * @param assignmentStatusEnum MANAGE_SUB_TASK
     */
    public static void updateSubTaskStatus(String id, String pId, AssignmentStatusEnum assignmentStatusEnum) {
        updateSubTaskStatus(id, pId, assignmentStatusEnum, null);
    }

    /**
     * 技术管理子任务更新状态和属性
     *
     * @param id id
     * @param pId pid
     * @param assignmentStatusEnum MANAGE_SUB_TASK
     * @param extValue 拓展其他值
     */
    public static void updateSubTaskStatus(String id, String pId, AssignmentStatusEnum assignmentStatusEnum, Map<String, Object> extValue) {
        if (pId == null || assignmentStatusEnum == null) {
            return;
        }
        List<TextValuePair> subTaskStatus = TextValuePairHelper.buildList(
                assignmentStatusEnum.getValue(), assignmentStatusEnum.getZhCn(), assignmentStatusEnum.getEnUs());
        Map<String, Object> values = MapUtils.newHashMap(
                SUBTASK_STATUS, subTaskStatus);
        if (extValue != null) {
            values.putAll(extValue);
        }
        SaveDataHelper.update(ManageSubTask.class, pId, id, values);
    }

    /**
     * 根据批次编码批量查询批次任务
     *
     * @param subTaskCodes 子任务编码列表
     * @param subTaskClass 子任务实体类
     * @return 子任务列表
     */
    public static <T extends BaseEntity> List<T> querySubTask(List<String> subTaskCodes, Class<T> subTaskClass) {
        Filter conditionFilter = new Filter(SUBTASK_CN_NO, Comparator.IN, subTaskCodes);
        List<IFilter> conditionFilterList = Lists.newArrayList(conditionFilter);
        return QueryDataHelper.query(subTaskClass, new ArrayList<>(), conditionFilterList);
    }

    public static String createTaskFlow(PersonnelScoreTechManagementTaskDto dto) {
        List<Employee> deduction = EmployeeHelper.getEmployees(Lists.newArrayList(dto.getDeductionEpmNo()));
        if (CollectionUtils.isEmpty(deduction)) {
            throw new LcapBusiException("deduction not found");
        }
        TechnologyManagementOrder order = new TechnologyManagementOrder();
        // 子公司没有4层部门，取公司积分运维人
        List<Employee> subsidiaryMaintenancePersons = new ArrayList<>();
        String responsible = ResponsibleUtils.specificLevelId(dto.getOrgId(), LEVEL_ORGANIZATION_REPRESENTATIVE_OFFICE);
        if (StringUtils.hasText(responsible)) {
            List<TreeServiceObjectDto> organizationInfos = NisAbility.queryOrgnizaList(Lists.newArrayList(responsible));
            if (!CollectionUtils.isEmpty(organizationInfos)) {
                order.setOrganizationId(TextValuePairHelper.buildList(organizationInfos.get(0).getCodeFullPath(),
                        organizationInfos.get(0).getNameZh(), organizationInfos.get(0).getNameEn()));
            }
        } else if (PersonnelScoreLevelEnum.B == dto.getTaskLevel() && !CollectionUtils.isEmpty(dto.getSubsidiaryMaintenancePersons())) {
            subsidiaryMaintenancePersons = EmployeeHelper.getEmployees(dto.getSubsidiaryMaintenancePersons());
        }
        order.setTaskDetail(dto.getTaskDetail());
        order.setTaskName(dto.getTaskName());
        order.setTaskCategory(TaskCategoryEnum.PERSONNEL_QUALIFICATION_MANAGEMENT_TASK.getValue());
        order.setPlanStartTime(new Date());
        order.setRequiredCompletionDate(Date.from(LocalDate.now().plusMonths(dto.getTaskLevel().getCompletedMouth())
                .atStartOfDay(ZoneId.systemDefault()).toInstant()));
        order.setStatus(AssignmentStatusEnum.EXECUTE.getValue());
        order.setCnNo(TaskNoHelper.createTaskNo(TECHNOLOGY_MANAGEMENT_TASK_CODE));
        order.setSource(DataSourceEnum.PERSONNEL.name());
        order.setSourceId(dto.getSourceId());

        MultiLangText feedbackType = new MultiLangText();
        feedbackType.setValue(ManageFeedbackTypeEnum.SUBMIT.getValue());
        feedbackType.setEnUS(ManageFeedbackTypeEnum.SUBMIT.getEnUs());
        feedbackType.setZhCN(ManageFeedbackTypeEnum.SUBMIT.getZhCn());
        order.setFeedbackType(Lists.newArrayList(feedbackType));

        List<EmployeeInfo> employeeInfos = HrClient.queryEmployInfo(dto.getDeductionEpmNo());
        // 验收人
        List<Employee> personnelApprovers = ApproverConfigAbility.
                getPersonnelApprover(dto.getOrgId(), dto.getTaskLevel().getApproveRole());
        personnelApprovers.addAll(getLeaderByLevel(employeeInfos, dto.getTaskLevel().getAcceptorLevel()));
        if (!CollectionUtils.isEmpty(subsidiaryMaintenancePersons)) {
            personnelApprovers.addAll(subsidiaryMaintenancePersons);
        }
        personnelApprovers = personnelApprovers.stream().distinct().collect(Collectors.toList());
        // 兜底审核人
        if (CollectionUtils.isEmpty(personnelApprovers)) {
            personnelApprovers.addAll(EmployeeHelper.getEmployees(ConfigHelper.getList(DEFAULT_APPROVER_KEY)));
        }
        order.setAcceptorPerson(personnelApprovers);
        // 知会人
        order.setInformedPerson(getLeaderByLevel(employeeInfos, dto.getTaskLevel().getInformLevel()));
        order.setResponsiblePerson(deduction);

        // 创建任务
        TransactionHelper.run(() -> {
            Map<String, Object> map = JsonUtils.parseObject(order, Map.class);
            SaveBizAndStartFlowDTO flow = new SaveBizAndStartFlowDTO();
            flow.setTenantId(ContextHelper.getTenantId());
            flow.setAppId(ContextHelper.getAppId());
            flow.setPageId(PAGE_BILL_TECHNOLOGY_MANAGEMENT_BILL);
            flow.setBizObjCode(EntityHelper.getEntityId(TechnologyManagementOrder.class));
            flow.setParams(map);
            order.setId(FlowServiceHelper.saveBizAndStartFlow(flow).get(BASIC_FIELD_BUSINESS_ID).toString());
            // 创建任务表数据
            createOrUpdateMainAssignment(order, dto.getOrgId());
        });
        return order.getId();
    }

    public static TechnologyManagementOrder queryBySourceId(String sourceId) {
        if (!StringUtils.hasText(sourceId)) {
            return null;
        }
        return QueryDataHelper.queryOne(TechnologyManagementOrder.class, Lists.newArrayList(),
                Lists.newArrayList(new Filter(SOURCE_ID, Comparator.EQ, sourceId)));
    }

    /**
     * 创建 / 更新主任务
     */
    private static TechnologyManagementAssignment createOrUpdateMainAssignment(TechnologyManagementOrder order,
                                                                               String orgId) {
        // 获取并包装页面填报信息
        TechnologyManagementAssignment assignment = new TechnologyManagementAssignment();
        assignment.setAssignmentStatus(order.getStatus());
        assignment.setAssignmentCode(order.getCnNo());
        assignment.setTaskCategory(order.getTaskCategory());
        assignment.setAssignmentName(order.getTaskName());
        assignment.setAssignmentType(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT.getPropValue());
        assignment.setBillType(BillTypeEnum.TECHNOLOGY_MANAGEMENT.getPropValue());
        assignment.setBillId(order.getId());
        assignment.setEntityId(order.getId());
        assignment.setCurrentProcessorEmployee(order.getResponsiblePerson());
        assignment.setResponsibleEmployee(order.getResponsiblePerson());
        assignment.setRequiredCompletionTime(order.getRequiredCompletionDate());
        assignment.setApprovalTaskFlag(BoolEnum.N);
        if(StringUtils.hasText(orgId)){
            String marketing = ResponsibleUtils.specificLevelId(orgId, LEVEL_ORGANIZATION_MARKETING);
            String responsible = ResponsibleUtils.specificLevelId(orgId, LEVEL_ORGANIZATION_REPRESENTATIVE_OFFICE);
            assignment.setMarketing(TextValuePairHelper.buildList(marketing, marketing, marketing));
            assignment.setRepresentativeOffice(TextValuePairHelper.buildList(responsible, responsible, responsible));
        }

        // 生成主任务
        String assignmentId = AssignmentAbility.insert(assignment);
        assignment.setId(assignmentId);

        // 创建人 + 责任人 + 验收人
        List<String> acceptorIds = order.getAcceptorPerson().stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toList());
        List<String> responsibleIds = EmployeeHelper.getEpmUIID(order.getResponsiblePerson());
        List<String> informPersons = EmployeeHelper.getEpmUIID(order.getInformedPerson());

        Set<String> relevantSet = new HashSet<>();
        relevantSet.add(ContextHelper.getEmpNo());
        relevantSet.addAll(acceptorIds);
        relevantSet.addAll(responsibleIds);
        relevantSet.addAll(informPersons);
        List<String> mailTos = responsibleIds;
        if (!CollectionUtils.isEmpty(informPersons)) {
            mailTos.addAll(informPersons);
        }
        AssignmentAbility.createAssignmentPersonRelevance(assignmentId, new ArrayList<>(relevantSet));
        List<Employee> responsibleEmployees = assignment.getResponsibleEmployee();
        if (!CollectionUtils.isEmpty(responsibleEmployees)) {
            Employee responsibleEmployee = responsibleEmployees.get(INTEGER_ZERO);
            Map<String, Object> data = EmailAbility.buildMessageZhContent(
                    MessageConsts.EmailNotice.MESSAGE_MANAGETASK_CREATE_PRESS_CONTENT, responsibleEmployee.getEmpNameCn(),
                    assignment.getAssignmentCode(), assignment.getAssignmentName());
            data.putAll(EmailAbility.buildMessageEnContent(
                    MessageConsts.EmailNotice.MESSAGE_MANAGETASK_CREATE_PRESS_CONTENT, responsibleEmployee.getEmpNameEn(),
                    assignment.getAssignmentCode(), assignment.getAssignmentName()));
            EmailAbility.asyncSendMail(EmailAbility.EmailParam.builder()
                    .tabName(assignment.getAssignmentCode())
                    .pkId(assignment.getBillId())
                    .templateIdEnum(TemplateIdEnum.EMAIL_INFOM_1)
                    .data(data)
                    .mailTos(mailTos)
                    .assignmentTypeEnum(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT)
                    .isApprovePage(true)
                    .build());
        }

        return assignment;
    }

    public static void abolishTask(String id) {
        TransactionHelper.run(() -> {
            //更新基础表
            TechnologyManagementOrder order = new TechnologyManagementOrder();
            order.setId(id);
            order.setStatus(AssignmentStatusEnum.ABOLISH.getValue());
            SaveDataHelper.update(order);

            // 更新任务表
            Assignment assignment = AssignmentAbility
                    .queryAssignment(id, Lists.newArrayList(ID), Assignment.class);
            assignment.setAssignmentStatus(AssignmentStatusEnum.ABOLISH.getValue());
            assignment.setCurrentProcessorEmployee(new ArrayList<>());
            assignment.setCurrentProgress("");
            SaveDataHelper.update(assignment);

            //终止流程
            FlowHelper.revokeFlow(id, ApproveFlowCodeEnum.TECH_MANAGE_FLOW.name());
        });
    }

    /**
     * 更具层级获取审核人账号
     */
    public static List<Employee> getLeaderByLevel(List<EmployeeInfo> infos, String level) {
        if (!StringUtils.hasText(level)) {
            return Collections.emptyList();
        }

        // 过滤出符合层级的员工信息
        List<String> manageUiids = infos.stream()
                .filter(item -> level.equals(item.getHrLevel()))
                .map(EmployeeInfo::getManageUiid)
                .distinct() // 去重，避免重复查询
                .collect(Collectors.toList());

        // 如果没有找到符合条件的员工，返回空列表
        if (manageUiids.isEmpty()) {
            return Collections.emptyList();
        }

        // 根据 manageUIIDs 获取对应的员工对象
        return EmployeeHelper.getEmployees(manageUiids);
    }
}
