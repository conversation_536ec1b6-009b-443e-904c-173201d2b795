package com.zte.iccp.itech.extension.openapi.zxrdc;

import com.zte.iccp.itech.extension.openapi.model.clockin.ClockInDTO;
import com.zte.iccp.itech.extension.openapi.model.clockin.ClockInTaskQueryDTO;
import com.zte.iccp.itech.extension.spi.client.ZxrdcClient;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * zxrdc接口转发，解决auth兼容问题
 *
 * <AUTHOR> 10335201
 * @date 2024-11-20 上午10:23
 **/
public class ZxrdcForwardOpenApi extends AbstractOpenApi {
    /**
     *  查询当前用户的打卡任务列表
     * @param clockInTaskQueryDTO
     * @return: com.zte.itp.msa.core.model.ServiceData<java.lang.Object>
     * @author: 朱小安 10335201
     * @date: 2024/11/20 下午4:43
     */
    public ServiceData<Object> queryClockInTaskList(@RequestBody ClockInTaskQueryDTO clockInTaskQueryDTO) {
        return new ServiceData<Object>() {{
            setBo(ZxrdcClient.queryClockInTaskList(clockInTaskQueryDTO));
        }};
    }

    /**
     *  查询指定打卡任务详情
     * @param id
     * @return: com.zte.itp.msa.core.model.ServiceData<java.lang.Object>
     * @author: 朱小安 10335201
     * @date: 2024/11/20 下午4:43
     */
    public ServiceData<Object> queryClockInTaskInfo(@PathVariable String id) {
        return new ServiceData<Object>() {{
            setBo(ZxrdcClient.queryClockInTaskInfo(id));
        }};
    }

    /**
     *  查询打卡选项
     * @param id
     * @return: com.zte.itp.msa.core.model.ServiceData<java.lang.Object>
     * @author: 朱小安 10335201
     * @date: 2024/11/20 下午4:44
     */
    public ServiceData<Object> queryClockInOption(@PathVariable String id) {
        return new ServiceData<Object>() {{
            setBo(ZxrdcClient.queryClockInOptionList(id));
        }};
    }

    /**
     *  提交打卡
     * @param id
     * @param clockInDTO
     * @return: com.zte.itp.msa.core.model.ServiceData<java.lang.Object>
     * @author: 朱小安 10335201
     * @date: 2024/11/20 下午4:46
     */
    public ServiceData<Object> submitClockIn(@PathVariable String id, @RequestBody ClockInDTO clockInDTO) {
        return new ServiceData<Object>() {{
            setBo(ZxrdcClient.submitClockIn(id,clockInDTO));
        }};
    }

    /**
     *  转交打卡任务
     * @param id
     * @param userId
     * @return: com.zte.itp.msa.core.model.ServiceData<java.lang.Object>
     * @author: 朱小安 10335201
     * @date: 2024/11/20 下午4:55
     */
    public ServiceData<Object> transferClockIn(@PathVariable String id, @PathVariable String userId) {
        return new ServiceData<Object>() {{
            setBo(ZxrdcClient.transferClockIn(id,userId));
        }};
    }

    /**
     *  撤销打卡
     * @param id
     * @return: com.zte.itp.msa.core.model.ServiceData<java.lang.Object>
     * @author: 朱小安 10335201
     * @date: 2024/11/20 下午4:59
     */
    public ServiceData<Object> cancelClockIn(@PathVariable String id) {
        return new ServiceData<Object>() {{
            setBo(ZxrdcClient.cancelClockIn(id));
        }};
    }


}
