package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;

import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.reflect.Field;
import java.util.List;
import java.util.stream.Collectors;

public class MultiTextValuePairsDeserializer<T> extends JsonDeserializer<List<T>> {

    @SuppressWarnings("unchecked")
    @Override
    public List<T> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        List<TextValuePair> textValuePairs = p.getCodec().readValue(p, new TypeReference<List<TextValuePair>>() {});

        Field field = JsonUtils.findField(p.getCurrentValue().getClass(), p.getCurrentName());
        if (field == null) {
            return (List<T>) textValuePairs;
        }

        ItemClass itemClass = field.getAnnotation(ItemClass.class);
        if (itemClass == null) {
            return (List<T>) textValuePairs;
        }

        return textValuePairs.stream()
                .map(pair -> (T) SingletonTextValuePairsDeserializer.parse(pair, itemClass.value()))
                .collect(Collectors.toList());
    }

    @Retention(RetentionPolicy.RUNTIME)
    public @interface ItemClass {
        Class<?> value();
    }
}
