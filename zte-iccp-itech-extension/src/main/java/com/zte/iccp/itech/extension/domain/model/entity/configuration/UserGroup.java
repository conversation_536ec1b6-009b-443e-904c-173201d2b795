package com.zte.iccp.itech.extension.domain.model.entity.configuration;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.configuration.UserGroupFieldConsts.*;

/** 邮件推送群组 */
@Setter
@Getter
@BaseEntity.Info("user_group")
public class UserGroup extends BaseEntity {

    /** 群组名称 */
    @JsonProperty(value = GROUP_NAME)
    private String groupName;

    /** 群组人员 */
    @JsonProperty(value = GROUP_USER)
    private List<Employee> groupUser;

    /** 备注说明 */
    @JsonProperty(value = REMARK)
    private String remark;
}
