package com.zte.iccp.itech.extension.ability.reportpush;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.openapi.model.reportpush.NetChangeReportInterOrgVO;
import com.zte.iccp.itech.extension.openapi.model.reportpush.ReportInterOrgDetailVO;
import com.zte.iccp.itech.extension.spi.client.IcosClient;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR>
 * @date 2025/4/23 上午10:02
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class NetChangeInterPushAbility {
    private static final String INTERNAL_TITLE = "netchange.report.push.internal.title";
    private static final String ICOS_INTERNAL_ORG_DAY_DETAIL_APIID = "icos.netchange.report.internal.org.day.detail.apiId";
    private static final String ICOS_INTERNAL_ORG_THREE_DAY_DETAIL_APIID = "icos.netchange.report.internal.org.threeday.detail.apiId";
    private static final String RANGE_DAY = "DAY";
    private static final String RANGE_THREE_DAY = "THREE_DAY";
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String LOCATION = "emailtemplate/reportinternalpush.txt";
    private static final String DATA = "${DATA}";
    private static final String TD_START = "<td>";
    private static final String TD_END = "</td>";
    private static final String TR_START = "<tr>";
    private static final String TR_END = "</tr>";
    private static final String LINE_FEED = "<p></p>";
    private static final String NET_CHANGE_QUERY_URL = "netchange.query.url";
    private static final String INDIA_ORG_ID = "india.org.id";
    private static final String INDIA_COMMON_MAIL = "india.common.mail";

    public static void interOrgDayPush() {
        List<NetChangeReportInterOrgVO> totalVos = IcosClient.getNetChangeReportInterOrg();

        //当天无数据不发送邮件
        if (CollectionUtils.isEmpty(totalVos.stream().filter(item -> RANGE_DAY.equals(item.getDateRange())).collect(Collectors.toList()))) {
            log.info("{} interOrgDayPush total is 0", new Date());
            return;
        }

        Map<String, List<NetChangeReportInterOrgVO>> orgId2totalVosMap = totalVos.stream()
                .collect(Collectors.groupingBy(NetChangeReportInterOrgVO::getOrganizationIdPath));

        List<ReportInterOrgDetailVO> dayDetails = IcosClient.getReportInterOrgDetailVos(ICOS_INTERNAL_ORG_DAY_DETAIL_APIID);
        Map<String, List<ReportInterOrgDetailVO>> orgId2DayDetailsMap = dayDetails.stream()
                .collect(Collectors.groupingBy(ReportInterOrgDetailVO::getOrganizationIdPath));

        List<ReportInterOrgDetailVO> threeDayDetails = IcosClient.getReportInterOrgDetailVos(ICOS_INTERNAL_ORG_THREE_DAY_DETAIL_APIID);
        Map<String, List<ReportInterOrgDetailVO>> orgId2ThreeDayDetailsMap = threeDayDetails.stream()
                .collect(Collectors.groupingBy(ReportInterOrgDetailVO::getOrganizationIdPath));

        String dayDateTitle = DateUtils.dateToString(new Date(), DATE_FORMAT);
        String threeDayTitleStart =  DateUtils.dateToString(DateUtils.addDay(new Date(), 1), DATE_FORMAT);
        String threeDayTitleEnd = DateUtils.dateToString(DateUtils.addDay(new Date(), INTEGER_THREE), DATE_FORMAT);
        String threeDayDateTitle = String.format("%s~%s", threeDayTitleStart, threeDayTitleEnd);
        String url = ConfigHelper.get(NET_CHANGE_QUERY_URL);

        String indiaOrgId = ConfigHelper.get(INDIA_ORG_ID);
        String indiaMail = ConfigHelper.get(INDIA_COMMON_MAIL);
        orgId2totalVosMap.forEach((k, v) -> {
            List<NetChangeReportInterOrgVO> dayTotalVos = v.stream()
                    .filter(item -> RANGE_DAY.equals(item.getDateRange())).collect(Collectors.toList());
            List<ReportInterOrgDetailVO> dayDetailVos = orgId2DayDetailsMap.get(k);
            if (CollectionUtils.isEmpty(dayTotalVos)
                    || CollectionUtils.isEmpty(dayDetailVos)) {
                return;
            }

            String orgName = v.stream().map(item -> ResponsibleUtils.getResponsibleName(item.getOrganizationEnPath()))
                    .findFirst().orElse(EMPTY_STRING);

            String dayTitle = getTitle(dayTotalVos, orgName, dayDateTitle);

            StringBuilder mailInfo = new StringBuilder();

            String dayTemplateStr = getTemplateStr(dayDetailVos);

            mailInfo.append(ReportPushAbility.getTableTitle(dayTitle)).append(dayTemplateStr);

            List<String> empNos = ReportPushAbility.getEmpNos(k, null);

            ReportPushAbility.sendMessage(dayTitle, empNos);

            if (k.equals(indiaOrgId)) {
                empNos.add(indiaMail);
            }

            List<NetChangeReportInterOrgVO> threeDayTotalVos = v.stream()
                    .filter(item -> RANGE_THREE_DAY.equals(item.getDateRange())).collect(Collectors.toList());
            List<ReportInterOrgDetailVO> threeDayDetailVos = orgId2ThreeDayDetailsMap.get(k);
            if (CollectionUtils.isEmpty(threeDayTotalVos)
                    || CollectionUtils.isEmpty(threeDayDetailVos)) {
                ReportPushAbility.sendMail(dayTitle, mailInfo.toString(), url, empNos);
                return;
            }

            String threeDayTitle = getTitle(threeDayTotalVos, orgName, threeDayDateTitle);
            String threeDayTemplateStr = getTemplateStr(threeDayDetailVos);
            mailInfo.append(LINE_FEED).append(ReportPushAbility.getTableTitle(threeDayTitle)).append(threeDayTemplateStr);

            ReportPushAbility.sendMail(dayTitle, mailInfo.toString(), url, empNos);
        });
    }


    private static String getTemplateStr(List<ReportInterOrgDetailVO> dayDetailVos) {
        StringBuilder lineBuilder = new StringBuilder();
        List<ReportInterOrgDetailVO> sortedVos = dayDetailVos.stream()
                .sorted(Comparator.comparing(vo -> vo.getProdClassEnPath() == null ? EMPTY_STRING : vo.getProdClassEnPath()))
                .collect(Collectors.toList());

        sortedVos.forEach(vo -> lineBuilder.append(TR_START)
                .append(TD_START).append(ProductUtils.getMainName(vo.getProdClassEnPath())).append(TD_END)
                .append(TD_START).append(vo.getBatchCode()).append(TD_END)
                .append(TD_START).append(vo.getBatchName()).append(TD_END)
                .append(TD_START).append(vo.getOpTypeEn()).append(TD_END)
                .append(TD_START).append(vo.getRespEnName()).append(vo.getRespEmpNo()).append(TD_END)
                .append(TD_START).append(vo.getPlanOperationStartTime()).append(TD_END)
                .append(TD_START).append(vo.getProgressEn()).append(TD_END)
                .append(TD_START).append(vo.getApplicationDate()).append(TD_END)
                .append(TR_END));

        Map<String, String> replacements = MapUtils.newHashMap(DATA, lineBuilder.toString());
        return StringBuilderUtils.replaceAll(CommonUtils.getTemplateString(LOCATION), replacements);
    }

    private static String getTitle(List<NetChangeReportInterOrgVO> vos, String orgName, String titleDate) {
        String prodTitle = vos.stream().map(item -> String.format("[%s]%s", item.getProdTeam(), item.getTotalCount()))
                .collect(Collectors.joining(COMMA));
        int total = vos.stream().mapToInt(NetChangeReportInterOrgVO::getTotalCount).sum();
        return MsgUtils.getLangMessage(EN_US, INTERNAL_TITLE, titleDate, orgName, total, prodTitle);
    }


}
