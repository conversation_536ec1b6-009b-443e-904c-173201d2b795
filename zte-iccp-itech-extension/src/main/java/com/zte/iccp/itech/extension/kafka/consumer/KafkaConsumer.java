package com.zte.iccp.itech.extension.kafka.consumer;

import com.zte.iccp.itech.extension.common.dynamicbean.DynamicComponent;
import com.zte.iccp.itech.extension.domain.constant.KafkaConstants;
import com.zte.itp.msa.message.annotation.MessageConsumer;
import lombok.extern.slf4j.Slf4j;

@DynamicComponent
@Slf4j
public class KafkaConsumer {

    /**
     * 消费 CSC 服务请求消息
     */
    @MessageConsumer(
            topic = KafkaConstants.Topic.CSC_NANJING,
            key = KafkaConstants.Key.WHOLE,
            handlerType = KafkaHandler.class)
    protected void consumeCscServiceRequestChangedMessage(String record) {}
}
