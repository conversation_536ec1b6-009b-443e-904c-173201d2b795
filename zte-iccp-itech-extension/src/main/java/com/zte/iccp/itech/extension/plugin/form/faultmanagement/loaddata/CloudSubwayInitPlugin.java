package com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultProcessEnum;
import com.zte.iccp.itech.extension.domain.enums.subway.CloudSubwayEnum;
import com.zte.iccp.itech.extension.domain.model.FaultManagementDetails;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.ComponentCids;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.spi.model.warroom.subway.SubwayNodeInfo;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.LinkedList;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR> 10335201
 * @date 2024-08-17 上午10:28
 **/
public class CloudSubwayInitPlugin implements LoadDataBaseFormPlugin {
    @Override
    public void loadData(LoadDataEventArgs args, FaultManagementDetails faultManagementDetails) {
        IFormView formView = args.getFormView();

        String language = ContextHelper.getLangId();

        // 初始化默认的地铁图节点
        List<SubwayNodeInfo> subwayNodeInfos = new LinkedList<>();
        for(CloudSubwayEnum cloudSubwayEnum : CloudSubwayEnum.values()){
            SubwayNodeInfo info = new SubwayNodeInfo();
            info.setNodeCode(cloudSubwayEnum.getCode());
            info.setNodeName(cloudSubwayEnum.getName(language));
            info.setNodeLightingStatus(false);
            subwayNodeInfos.add(info);
        }

        List<FaultManagementOrder> faultManagementOrders = faultManagementDetails.getFaultManagementOrders();
        List<FaultManagementAssignment> faultManagementAssignments = faultManagementDetails.getFaultManagementAssignments();
        // 没查到单据信息时，返回默认的地铁图
        if(CollectionUtils.isEmpty(faultManagementOrders)
                || CollectionUtils.isEmpty(faultManagementAssignments)){
            formView.getClientViewProxy().setControlState(
                    ComponentCids.COMPONENT_CLOUD_SUBWAY_CID,
                    new BasicAttributeBuilder().attribute(SUBWAY_NODE_DATA_ATTRIBUTE_NAME, JSONArray.parseArray(JSON.toJSONString(
                            subwayNodeInfos))).build());
            return;
        }

        // 查到单据信息，封装地铁图信息后返回
        FaultManagementOrder faultManagementOrder = faultManagementOrders.get(INTEGER_ZERO);
        FaultManagementAssignment faultManagementAssignment = faultManagementAssignments.get(INTEGER_ZERO);
        initCloudSubwayData(subwayNodeInfos, faultManagementOrder, faultManagementAssignment);
        formView.getClientViewProxy().setControlState(
                ComponentCids.COMPONENT_CLOUD_SUBWAY_CID,
                new BasicAttributeBuilder().attribute(SUBWAY_NODE_DATA_ATTRIBUTE_NAME, JSONArray.parseArray(JSON.toJSONString(
                        subwayNodeInfos))).build());

    }

    private static void initCloudSubwayData(List<SubwayNodeInfo> subwayNodeInfoList,
                                            FaultManagementOrder faultManagementOrder,
                                            FaultManagementAssignment faultManagementAssignment) {
        if(StringUtils.isNotBlank(faultManagementOrder.getReviewSubmitTime())){
            subwayNodeInfoList.get(INTEGER_ZERO).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_ZERO).setNodeLightingTime(faultManagementOrder.getReviewSubmitTime());
        }
        if(StringUtils.isNotBlank(faultManagementOrder.getRectificationSubmitTime())){
            subwayNodeInfoList.get(INTEGER_ONE).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_ONE).setNodeLightingTime(faultManagementOrder.getRectificationSubmitTime());
        }
        if(StringUtils.isNotBlank(faultManagementOrder.getSatisfactionSubmitTime())){
            subwayNodeInfoList.get(INTEGER_TWO).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_TWO).setNodeLightingTime(faultManagementOrder.getSatisfactionSubmitTime());
        }
        String currentProgress = faultManagementAssignment.getCurrentProgress();
        if (FaultProcessEnum.DEMOTION_CLOSED.name().equals(currentProgress)
                || FaultProcessEnum.CLOSED.name().equals(currentProgress)) {
            subwayNodeInfoList.get(INTEGER_THREE).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_THREE).setNodeLightingTime(DateUtils.dateToString(faultManagementOrder.getLastModifiedTime(),
                    "yyyy-MM-dd HH:mm:ss"));
        }
    }
}
