package com.zte.iccp.itech.extension.plugin.form.faultmanagement.loaddata;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.zte.iccp.itech.extension.ability.FaultManagementOrderAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.enums.subway.CloudSubwayEnum;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.ComponentCids;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.common.FieldCids;
import com.zte.iccp.itech.extension.plugin.form.faultmanagement.interfaces.LoadDataBaseFormPlugin;
import com.zte.iccp.itech.extension.spi.model.warroom.subway.SubwayNodeInfo;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.helper.builder.attribute.BasicAttributeBuilder;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.LinkedList;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;

/**
 * <AUTHOR> 10335201
 * @date 2024-08-17 上午10:28
 **/
public class CloudSubwayInitPlugin implements LoadDataBaseFormPlugin {
    @Override
    public void loadData(LoadDataEventArgs args) {
        IDataModel dataModel = args.getModel();
        IFormView formView = args.getFormView();

        String language = ContextHelper.getLangId();
        String taskCode = PropertyValueConvertUtil.getString(dataModel.getValue(FieldCids.FIELD_TASK_CODE_CID));

        // 初始化默认的地铁图节点
        List<SubwayNodeInfo> subwayNodeInfoList = new LinkedList<>();
        for(CloudSubwayEnum cloudSubwayEnum : CloudSubwayEnum.values()){
            SubwayNodeInfo info = new SubwayNodeInfo();
            info.setNodeCode(cloudSubwayEnum.getCode());
            info.setNodeName(cloudSubwayEnum.getName(language));
            info.setNodeLightingStatus(false);
            subwayNodeInfoList.add(info);
        }

        List<FaultManagementOrder> faultManagementOrderList =  FaultManagementOrderAbility.queryOrderByCscCode(Lists.newArrayList(taskCode));
        // 没查到单据信息时，返回默认的地铁图
        if(CollectionUtils.isEmpty(faultManagementOrderList)){
            formView.getClientViewProxy().setControlState(
                    ComponentCids.COMPONENT_CLOUD_SUBWAY_CID,
                    new BasicAttributeBuilder().attribute(SUBWAY_NODE_DATA_ATTRIBUTE_NAME, JSONArray.parseArray(JSON.toJSONString(
                            subwayNodeInfoList))).build());
            return;
        }

        // 查到单据信息，封装地铁图信息后返回
        FaultManagementOrder faultManagementOrder = faultManagementOrderList.get(CommonConstants.INTEGER_ZERO);
        initCloudSubwayData(subwayNodeInfoList, faultManagementOrder);
        formView.getClientViewProxy().setControlState(
                ComponentCids.COMPONENT_CLOUD_SUBWAY_CID,
                new BasicAttributeBuilder().attribute(SUBWAY_NODE_DATA_ATTRIBUTE_NAME, JSONArray.parseArray(JSON.toJSONString(
                        subwayNodeInfoList))).build());

    }

    private static void initCloudSubwayData(List<SubwayNodeInfo> subwayNodeInfoList, FaultManagementOrder faultManagementOrder) {
        if(StringUtils.isNotBlank(faultManagementOrder.getReviewSubmitTime())){
            subwayNodeInfoList.get(INTEGER_ONE).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_ONE).setNodeLightingTime(faultManagementOrder.getReviewSubmitTime());
        }
        if(StringUtils.isNotBlank(faultManagementOrder.getRectificationSubmitTime())){
            subwayNodeInfoList.get(INTEGER_TWO).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_TWO).setNodeLightingTime(faultManagementOrder.getRectificationSubmitTime());
        }
        if(StringUtils.isNotBlank(faultManagementOrder.getSatisfactionSubmitTime())){
            subwayNodeInfoList.get(INTEGER_THREE).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_THREE).setNodeLightingTime(faultManagementOrder.getSatisfactionSubmitTime());
            subwayNodeInfoList.get(INTEGER_FOUR).setNodeLightingStatus(true);
            subwayNodeInfoList.get(INTEGER_FOUR).setNodeLightingTime(faultManagementOrder.getSatisfactionSubmitTime());
        }
    }
}
