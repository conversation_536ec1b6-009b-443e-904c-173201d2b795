package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;

/**
 * 环境地址工具
 *
 * <AUTHOR>
 * @create 2024/10/18 上午10:26
 */
public class EnvironmentUtils {


    /* 获取itechCloud的uniwork 工作台地址*/
    public static String getUniworkUrl() {
        String ip = ConfigHelper.get(CommonConstants.SERVICE_HOST);
        String project = ConfigHelper.get(CommonConstants.SERVICE_NAME);
        String address = ConfigHelper.get(CommonConstants.SERVICE_MYCHARGE_SUFFIX_PATH);
        return String.join("", ip, project, address);
    }
}
