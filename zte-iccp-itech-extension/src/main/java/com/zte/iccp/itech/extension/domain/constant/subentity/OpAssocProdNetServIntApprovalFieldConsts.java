package com.zte.iccp.itech.extension.domain.constant.subentity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/26
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OpAssocProdNetServIntApprovalFieldConsts {

    public static final String PRODUCT_TYPE = "product_type_net_integration";

    public static final String APPROVER = "approval_net_integration";

    public static final String APPROVED_DATE = "approved_data_net_integration";

    public static final String RESULT = "approve_result_net_integration";

    public static final String OPINION = "approve_opinion_net_intetration";

    public static final String EMAIL_CC = "email_net_integration";

    /**
     * 操作关联产品中文名（网服一体化）
     */
    public static final String INTEGRATED_ASSOCIATED_PRODUCT_NET_INTEGRATION_ZH = "integrated_associated_product_net_in_zh";

    /**
     * 操作关联产品英文名（网服一体化）
     */
    public static final String INTEGRATED_ASSOCIATED_PRODUCT_NET_INTEGRATION_EN = "integrated_associated_product_net_in_en";
}