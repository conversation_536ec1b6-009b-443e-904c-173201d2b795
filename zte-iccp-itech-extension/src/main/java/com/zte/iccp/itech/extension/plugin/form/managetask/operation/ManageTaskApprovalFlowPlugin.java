package com.zte.iccp.itech.extension.plugin.form.managetask.operation;

import com.zte.iccp.itech.extension.ability.*;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.domain.constant.*;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ManageSubTask;
import com.zte.iccp.itech.extension.domain.model.ManageSubTaskFlow;
import com.zte.iccp.itech.extension.domain.model.OperationLogRecord;
import com.zte.iccp.itech.extension.domain.model.TechnologyManagementOrder;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * 技术管理任务审批，关闭任务/驳回任务
 *
 * <AUTHOR> jiangjiawen
 * @date 2024/6/12
 */

@Slf4j
public class ManageTaskApprovalFlowPlugin extends BaseFlowOperationPlugin {

    @Override
    public void afterOperate(ExecuteEvent executeEvent) {
        String manageTaskId = (String) getModel().getRootDataEntity().getPkValue();
        String approveResult = FormModelProxyHelper.getTextFirstValue(CidConstants.APPROVE_RESULT_CID, getModel());

        ManageSubTaskFlow subTaskFlow = ManageTaskAbility.queryBySubFlowId(manageTaskId);
        // 处理主任务流程的逻辑
        if (subTaskFlow == null) {
            mainTaskProcess(manageTaskId, approveResult);
            // 处理子任务流程的逻辑
        } else {
            subTaskProcess(subTaskFlow, approveResult);
        }
    }

    /**
     * 子任务关闭/驳回业务处理
     *
     * @param subTaskFlow subTaskFlow
     * @param approveResult approveResult
     */
    private void subTaskProcess(ManageSubTaskFlow subTaskFlow, String approveResult) {
        AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.EXECUTE;
        ManageTaskOperationRecordEnum operationRecordEnum = ManageTaskOperationRecordEnum.REJECT_TASK;
        if (ApproveResultEnum.PASS.name().equals(approveResult)) {
            List<MultiLangText> feedbackTypeValues = subTaskFlow.getFeedbackType();
            assignmentStatusEnum = AssignmentStatusEnum.getManageTaskStatus(feedbackTypeValues);
            operationRecordEnum = ManageTaskOperationRecordEnum.DISABLE_TASK;
        }

        // 1.更新子任务表
        ManageTaskAbility.updateSubTaskStatus(subTaskFlow.getSubTaskId(), subTaskFlow.getParentTaskId(), assignmentStatusEnum);

        // 1.1更新子任务流程表
        subTaskFlow.setStatus(Objects.requireNonNull(assignmentStatusEnum).getValue());
        ManageTaskAbility.update(subTaskFlow);
        // 2.更新assignment表
        AssignmentAbility.updateTechMgmtTaskStatus(subTaskFlow.getId(),
                assignmentStatusEnum,
                AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB);

        // 3.新增操作日志记录
        OperationLogRecord record = buildOperationLogRecord(subTaskFlow.getSubTaskId(), OperationLogRecordTypeEnum.MANAGE_SUBTASK,
                operationRecordEnum);
        record.setParentRelationId(subTaskFlow.getParentTaskId());
        record.setChildName(subTaskFlow.getSubtaskName());
        OperationLogRecordAbility.insert(record);

        // 4.发送邮件
        if (AssignmentStatusEnum.CLOSE == assignmentStatusEnum) {
            ManageSubTask manageSubTask = ManageTaskAbility.querySubTask(subTaskFlow.getSubTaskId(), subTaskFlow.getParentTaskId());
            List<String> mailTos = EmployeeHelper.getEpmUIID(Objects.requireNonNull(manageSubTask).getSubtaskResponsiblePerson());
            mailTos.add(manageSubTask.getCreateBy());

            TechnologyManagementOrder manageTask = ManageTaskAbility.queryById(manageSubTask.getPid());
            if (!CollectionUtils.isEmpty(manageTask.getInformedPerson())) {
                mailTos.addAll(EmployeeHelper.getEpmUIID(manageTask.getInformedPerson()));
            }

            sendEmail(subTaskFlow.getId(), manageSubTask.getSubtaskCnNo(),
                    subTaskFlow.getSubtaskName(), mailTos, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB);
        }
    }

    /**
     * 同意关闭是知会邮件，收件人为责任人、创建人和知会人     由验收人发起
     *
     * @param pkId 主键id
     * @param orderNo 单据编号
     * @param taskName 任务名称
     * @param mailTos 收件人
     * @param assignmentTypeEnum 任务类型
     */
    private void sendEmail(String pkId,
                           String orderNo,
                           String taskName,
                           List<String> mailTos,
                           AssignmentTypeEnum assignmentTypeEnum) {
        // 【iTech Cloud 技术管理任务】验收人{操作者}已同意关闭【{单据编号}/{任务名称}】,请知悉!
        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(ContextHelper.getEmpNo());
        Map<String, Object> data = EmailAbility.buildMessageZhContent(
                MessageConsts.EmailNotice.MESSAGE_MANAGETASK_PASS_CLOSE_CONTENT, employeeFormatNames.getZhCN(), orderNo, taskName);
        data.putAll(EmailAbility.buildMessageEnContent(
                MessageConsts.EmailNotice.MESSAGE_MANAGETASK_PASS_CLOSE_CONTENT, employeeFormatNames.getEnUS(), orderNo, taskName));

        EmailAbility.asyncSendMail(EmailAbility.EmailParam.builder()
                .pkId(pkId)
                .templateIdEnum(TemplateIdEnum.EMAIL_INFOM_1)
                .data(data)
                .mailTos(mailTos)
                .mailCCs(mailTos)
                .isApprovePage(false)
                .assignmentTypeEnum(assignmentTypeEnum)
                .build());
    }

    /**
     * 驳回关闭是知会邮件，收件人为责任人、创建人和知会人     由验收人发起
     *
     * @param pkId 主键id
     * @param taskCategory 任务分类
     * @param orderNo 单据编号
     * @param taskName 任务名称
     * @param mailTos 收件人
     * @param assignmentTypeEnum 任务类型
     */
    private void sendRejectEmail(String pkId,
            String taskCategory,
            String orderNo,
            String taskName,
            List<String> mailTos,
            AssignmentTypeEnum assignmentTypeEnum) {
        MultiLangText employeeFormatNames = HrClient.getEmployeeFormatNames(ContextHelper.getEmpNo());
        String taskCategoryCn = Objects.requireNonNull(LookupValueHelper.getLookupValue(ZH_CN,
                LookupValueConstant.TASK_CATEGORY_ENUM, taskCategory)).getMeaning();
        String taskCategoryEn = Objects.requireNonNull(LookupValueHelper.getLookupValue(EN_US,
                LookupValueConstant.TASK_CATEGORY_ENUM, taskCategory)).getMeaning();
        Map<String, Object> data = EmailAbility.buildMessageZhContent(
                MessageConsts.EmailNotice.MESSAGE_MANAGETASK_REJECT_CONTENT, orderNo,
                taskCategoryCn, taskName, employeeFormatNames.getZhCN());
        data.putAll(EmailAbility.buildMessageEnContent(
                MessageConsts.EmailNotice.MESSAGE_MANAGETASK_REJECT_CONTENT, orderNo,
                taskCategoryEn, taskName, employeeFormatNames.getEnUS()));

        EmailAbility.asyncSendMail(EmailAbility.EmailParam.builder()
                .pkId(pkId)
                .tabName(orderNo)
                .templateIdEnum(TemplateIdEnum.EMAIL_INFOM_1)
                .data(data)
                .mailTos(mailTos)
                .mailCCs(mailTos)
                .isApprovePage(true)
                .assignmentTypeEnum(assignmentTypeEnum)
                .build());
    }

    /**
     * 主任务关闭/驳回业务处理
     *
     * @param manageTaskId  manageTaskId
     * @param approveResult approveResult
     */
    private void mainTaskProcess(String manageTaskId, String approveResult) {
        // 上节点类型为完成任务，申请关闭、申请废止时通过能做正常映射，但驳回时存在差异，写死
        TechnologyManagementOrder managementOrder = ManageTaskAbility.queryById(manageTaskId);
        AssignmentStatusEnum assignmentStatusEnum = AssignmentStatusEnum.EXECUTE;
        ManageTaskOperationRecordEnum operationRecordEnum = ManageTaskOperationRecordEnum.REJECT_TASK;
        if (ApproveResultEnum.PASS.name().equals(approveResult)) {
            assignmentStatusEnum = AssignmentStatusEnum.getManageTaskStatus(managementOrder.getFeedbackType());
            operationRecordEnum = ManageTaskOperationRecordEnum.DISABLE_TASK;
        }

        // 1.同步更新主任务状态
        ManageTaskAbility.updateTaskStatusByPkId(manageTaskId,assignmentStatusEnum);

        // 2.更新assignment表
        AssignmentAbility.updateTechMgmtTaskStatus(manageTaskId, assignmentStatusEnum, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT);

        // 3.新增操作日志记录
        OperationLogRecord record = buildOperationLogRecord(manageTaskId, OperationLogRecordTypeEnum.MANAGE_TASK,
                operationRecordEnum);
        OperationLogRecordAbility.insert(record);
        // 4.发送邮件
        if (AssignmentStatusEnum.CLOSE == assignmentStatusEnum) {
            List<String> mailTos = EmployeeHelper.getEpmUIID(managementOrder.getResponsiblePerson());
            mailTos.add(managementOrder.getCreateBy());
            if (!CollectionUtils.isEmpty(managementOrder.getInformedPerson())) {
                mailTos.addAll(EmployeeHelper.getEpmUIID(managementOrder.getInformedPerson()));
            }
            sendEmail(manageTaskId, managementOrder.getCnNo(),
                    managementOrder.getTaskName(), mailTos, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT);
        } else if (AssignmentStatusEnum.EXECUTE == assignmentStatusEnum) {
            List<String> mailTos = EmployeeHelper.getEpmUIID(managementOrder.getResponsiblePerson());
            mailTos.add(managementOrder.getCreateBy());
            sendRejectEmail(manageTaskId,
                    managementOrder.getTaskCategory(),
                    managementOrder.getCnNo(),
                    managementOrder.getTaskName(), mailTos, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT);
        }
    }

    /**
     * 构建操作日志记录
     *
     * @param taskId taskId
     * @param operationLogRecordTypeEnum operationLogRecordTypeEnum
     * @param operationRecordEnum operationRecordEnum
     * @return OperationLogRecord
     */
    private OperationLogRecord buildOperationLogRecord(String taskId, OperationLogRecordTypeEnum operationLogRecordTypeEnum,
                                                       ManageTaskOperationRecordEnum operationRecordEnum) {
        return OperationLogRecord.builder()
                .relationId(taskId)
                .operationDescription((String) getModel().getValue(CidConstants.APPROVE_DESCRIPTION))
                .operationType(operationLogRecordTypeEnum.getOperationType())
                .responsiblePerson(ContextHelper.getEmpInfo())
                .operationName(operationRecordEnum.getMsgKey())
                .build();
    }
}
