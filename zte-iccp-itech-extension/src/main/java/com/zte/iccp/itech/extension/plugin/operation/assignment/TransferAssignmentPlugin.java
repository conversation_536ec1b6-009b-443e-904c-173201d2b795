package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.*;
import com.zte.iccp.itech.extension.ability.assignment.TransferAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ApproverAbility;
import com.zte.iccp.itech.extension.ability.changeorder.MultiProdGuaranteeAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ChangeOrderTypeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.entity.*;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiProductLinkageGuarantee;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.iccp.itech.extension.plugin.operation.assignment.helper.TransferFaultManagementHelper;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.paas.lcap.common.api.dto.PopUpSizeDTO;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import com.zte.paas.lcap.ddm.domain.control.buildin.table.TablePc;
import com.zte.paas.lcap.ddm.domain.flow.dto.AddApproverDTO;
import com.zte.paas.lcap.ddm.domain.flow.dto.DeleteApprovalTaskDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.MessageConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_WEB_CONFIRM_TRANSFER_CURRENT_PROCESSOR;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.PAGE_WEB_CONFIRM_TRANSFER_RESPONSIBILITY;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;
import static com.zte.iccp.itech.extension.domain.constant.subentity.ManageSubTaskFieldConsts.SUBTASK_RESPONSIBLE_PERSON;
import static com.zte.iccp.itech.extension.domain.constant.subentity.MultiProductLinkageGuaranteeFieldConsts.IMPLEMENTATION_RESPONSIBLE_PERSON;
import static com.zte.paas.lcap.ddm.domain.enums.OpenTypeEnum.POPUP;
import static com.zte.paas.lcap.platform.constant.SchemaConstant.POPUP_SIZE;

public class TransferAssignmentPlugin extends BaseOperationPlugin {

    /**
     * 转交任务 - 责任人变更
     */
    @Override
    public void afterExecute(ExecuteEvent executeEvent) {
        IFormView formView = getView();
        IDataModel dataModel = getModel();

        // 执行操作
        // OPERATION_TRANSFER - 跳转选择责任人页面
        // OPERATION_CONFIRM - 责任人确认
        if (CommonConstants.OPERATION_TRANSFER.equals(executeEvent.getOperationKey())) {
            forwardChooseResponsibilityPage(formView);
        } else {
            confirmResponsibility(formView, dataModel);
        }
    }

    /**
     * 跳转责任人确认页面
     */
    private static void forwardChooseResponsibilityPage(IFormView formView) {

        // 1.检索任务信息 - 仅单个任务
        TablePc tableInfo = (TablePc) formView.getControl(CidConstants.TABLE_ASSIGNMENT_CID);
        List<String> assignmentIds = tableInfo.getTableCache().getSelectedPkIdList();
        String assignmentId = assignmentIds.get(0);

        List<String> fields = Lists.newArrayList(
                ID, CREATE_BY, BILL_ID, ASSIGNMENT_TYPE, ASSIGNMENT_STATUS,
                AssignmentFieldConsts.REPRESENTATIVE_OFFICE, CURRENT_PROGRESS,
                CURRENT_PROCESSOR_EMPLOYEE_FIELD, RESPONSIBLE_EMPLOYEE_FIELD);
        Assignment assignment
                = AssignmentAbility.querySpecificTypeAssignment(assignmentId, fields, Assignment.class);
        if (Objects.isNull(assignment)) {
            throw new LcapBusiException(
                    MsgUtils.getMessage(MessageConsts.ASSIGNMENT_MANAGE_NON_EXISTENT_WARNING));
        }

        // 2.任务校验
        boolean checkFlag;
        Map<String, Object> extraCustomParameter = new HashMap<>();
        String pageId = PAGE_WEB_CONFIRM_TRANSFER_RESPONSIBILITY;
        String bizObjCode = PAGE_WEB_CONFIRM_TRANSFER_RESPONSIBILITY;

        AssignmentTypeEnum assignmentType
                = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        switch (assignmentType) {
            case NETWORK_CHANGE:
            case SUBCONTRACTOR_NETWORK_CHANGE:
                checkFlag = true;
                extraCustomParameter
                        = TransferAbility.networkChangeTransferResponsibleCheck(assignment);
                break;

            case NETWORK_CHANGE_BATCH:
            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                checkFlag = true;
                extraCustomParameter
                        = TransferAbility.batchTransferResponsibleCheck(assignment, assignmentType);
                break;

            case TECHNOLOGY_MANAGEMENT:
                checkFlag = checkTechnologyAssignment(formView, assignment);
                break;

            case TECHNOLOGY_MANAGEMENT_SUB:
                checkFlag = checkSubAssignment(formView, assignment);
                break;
            case FAULT_MANAGEMENT:
                pageId = PAGE_WEB_CONFIRM_TRANSFER_CURRENT_PROCESSOR;
                bizObjCode = PAGE_WEB_CONFIRM_TRANSFER_CURRENT_PROCESSOR;
                checkFlag = checkFaultManagerAssignment(formView, assignment);
                break;

            default:
                checkFlag = false;
        }

        if (!checkFlag) {
            return;
        }

        // 4.包装跳转参数
        FormShowParameter formShowParameter = formView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setPageId(pageId);
        formShowParameter.setBizObjectCode(bizObjCode);
        formShowParameter.setPageStatus(PageStatusEnum.NEW);

        Map<String, Object> customParameters = new HashMap<>();
        customParameters.put(HIDDEN_OPERATION, true);
        customParameters.put(FULL_SCREEN, false);
        customParameters.put(OPEN_TYPE, POPUP);
        customParameters.put(ASSIGNMENT_TYPE_EXT, assignmentType.name());
        customParameters.putAll(extraCustomParameter);

        PopUpSizeDTO.PopUpSizeDTOBuilder popupSize= PopUpSizeDTO.builder();
        popupSize.width(30);
        popupSize.unit(CommonConstants.PERCENT);
        customParameters.put(POPUP_SIZE, popupSize.build());
        customParameters.put(CommonConstants.CUSTOM_PARAME_ASSIGNMENT_ID, assignment.getId());

        formShowParameter.setCustomParameters(customParameters);
        formView.showForm(formShowParameter);
    }

    /**
     * 技术管理任务 任务校验
     * @param assignment
     * @return boolean
     */
    private static boolean checkTechnologyAssignment(IFormView formView, Assignment assignment) {
        // 1.任务状态校验
        // 执行中 / 审批中 支持转交
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!AssignmentStatusEnum.EXECUTE.equals(assignmentStatus) && !AssignmentStatusEnum.APPROVE.equals(assignmentStatus)) {
            formView.showMessage(ASSIGNMENT_OPERATION_WARNING_TRANSFER_ERROR_STATUS_TECHNOLOGY, MsgType.WARNING);
            return false;
        }

        // 2.任务责任人校验
        // 责任人 / 创建人 支持转交
        String createdBy = assignment.getCreateBy();
        List<String> responsible = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());
        String userId = ContextHelper.getEmpNo();
        if (!userId.equals(createdBy) && !responsible.contains(userId)) {
            formView.showMessage(ASSIGNMENT_OPERATION_WARNING_TRANSFER_ERROR_RESPONSIBLE_TECHNOLOGY, MsgType.WARNING);
            return false;
        }

        return true;
    }

    /**
     * 技术管理任务 - 子任务 任务校验
     * @param assignment
     * @return boolean
     */
    private static boolean checkSubAssignment(IFormView formView, Assignment assignment) {
        // 1.检索主任务
        Assignment mainAssignment =
                AssignmentAbility.querySpecificTypeAssignment(assignment.getBillId(), AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT, Assignment.class);

        // 1.任务状态校验
        // 执行中 / 审批中 支持转交
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!AssignmentStatusEnum.EXECUTE.equals(assignmentStatus) && !AssignmentStatusEnum.APPROVE.equals(assignmentStatus)) {
            formView.showMessage(ASSIGNMENT_OPERATION_WARNING_TRANSFER_ERROR_STATUS_TECHNOLOGY, MsgType.WARNING);
            return false;
        }

        // 2.任务责任人校验
        // 主任务责任人 / 主任务创建人 / 子任务责任人 支持转交
        String mainCreatedBy = mainAssignment.getCreateBy();
        List<String> mainResponsible = EmployeeHelper.getEpmUIID(mainAssignment.getResponsibleEmployee());
        List<String> responsible = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());
        String userId = ContextHelper.getEmpNo();
        if (!userId.equals(mainCreatedBy) && !mainResponsible.contains(userId) && !responsible.contains(userId)) {
            formView.showMessage(ASSIGNMENT_OPERATION_WARNING_TRANSFER_ERROR_RESPONSIBLE_TECHNOLOGY, MsgType.WARNING);
            return false;
        }

        return true;
    }

    /**
     * 故障管理任务 - 任务中心转交功能
     */
    private static boolean checkFaultManagerAssignment(IFormView formView, Assignment assignment) {
         // 当前处理人 + 故障经理 均具有转交权限
         // 国内故障经理：可以转交国内代表处故障管理的单子
         // 国际故障经理：可以转交国际代表处故障管理的单子
        List<Employee> currentProcessors = assignment.getCurrentProcessorEmployee();
        List<Employee> faultManagers = ApproverAbility.getFaultManager(
                TextValuePairHelper.getValue(assignment.getRepresentativeOffice()));

        List<String> validUserIds = Stream.concat(currentProcessors.stream(), faultManagers.stream())
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
        if (validUserIds.contains(ContextHelper.getEmpNo())) {
            return true;
        }

        formView.showMessage(ASSIGNMENT_OPERATION_WARNING_TRANSFER_ERROR_FAULT_MANAGER, MsgType.WARNING);
        return false;
    }



    /**
     * 确认转交责任人
     */
    private static void confirmResponsibility(IFormView formView, IDataModel dataModel) {

        // 1.获取任务ID
        Map<String, Object> customerParmeterMap = formView.getFormShowParameter().getCustomParameters();
        String assignmentId = (String) customerParmeterMap.getOrDefault(CommonConstants.CUSTOM_PARAME_ASSIGNMENT_ID, EMPTY_STRING);

        // 2.检索任务数据
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(assignmentId, Assignment.class);
        if (Objects.isNull(assignment)) {
            return;
        }

        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        if (Objects.isNull(assignmentType)) {
            return;
        }

        switch (assignmentType) {
            case NETWORK_CHANGE:
            case SUBCONTRACTOR_NETWORK_CHANGE:
                changeOrderTransferConfirm(dataModel, customerParmeterMap, assignment);
                break;

            case NETWORK_CHANGE_BATCH:
            case SUBCONTRACT_NETWORK_CHANGE_BATCH:
                batchTransferConfirm(dataModel, customerParmeterMap, assignment, assignmentType);
                break;

            case TECHNOLOGY_MANAGEMENT:
                technologyTransferConfirm(dataModel, assignment);
                break;

            case TECHNOLOGY_MANAGEMENT_SUB:
                subTransferConfirm(dataModel, assignment);
                break;

            case FAULT_MANAGEMENT:
                faultManagerConfirm(dataModel, assignment);
                break;

            default:
                break;
        }

        // 3.刷新父页面
        IFormView parentView = formView.getParentView();
        parentView.getClientViewProxy().refreshData(parentView.getPageSessionId(), TABLE_ASSIGNMENT_CID, parentView.getPageId());
    }

    /**
     * 故障管理 转交当前处理人
     * @param assignment
     */
    private static void faultManagerConfirm(IDataModel dataModel, Assignment assignment) {
        List<Employee> transferCurrentProcessors
                = ComponentUtils.getEmployeeComponentInfo(dataModel, RESPONSIBILITY_TRANSFERENCE_PERSON_CID);
        if (CollectionUtils.isEmpty(transferCurrentProcessors)) {
            return;
        }

        /**
         * 故障管理由于有故障经理角色，在转交的时候需要特殊考虑
         * 假设：当前处理人：A，B，C，D，X1，故障经理：X1，X2
         *     场景1：B转交给bb，当前处理人：bb，A，C, D, X1
         *     场景2：X1转交给bb，当前处理人：bb，A，B，C，D
         *         X1仍然有转交权（场景3），但没有处理单据的权限，
         *     场景3：X2转交给bb，当前处理人：bb，A，B，C，D
         */
        Employee transferCurrentProcessor = transferCurrentProcessors.get(INTEGER_ZERO);
        String transferId = transferCurrentProcessor.getEmpUIID();
        List<TextValuePair> representativeOffices = assignment.getRepresentativeOffice();
        List<Employee> faultManagers = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(representativeOffices)) {
            String representativeOffice = representativeOffices.get(INTEGER_ZERO).getValue();
            faultManagers = ApproverAbility.getFaultManager(representativeOffice);
        }
        List<Employee> currentProcessors = assignment.getCurrentProcessorEmployee();

        List<String> processorIds = TransferFaultManagementHelper.getFaultManagementProcessor(assignment,
                currentProcessors, faultManagers, transferId);
        // processorIds为空，说明不需要更新当前处理人
        if (CollectionUtils.isEmpty(processorIds)) {
            return;
        }

        List<Employee> processors = HrClient.queryEmployeeInfo(processorIds);
        // 3.更新任务中心当前处理人
        updateCurrentProcessors(assignment, processors);
        // 4.新增当前处理人 - 任务关联关系
        AssignmentAbility.createAssignmentPersonRelevance(assignment.getId(), Lists.newArrayList(transferId));
    }

    private static void updateCurrentProcessors(
            Assignment assignment,
            List<Employee> processors) {

        // 由于后续还要继续使用 transferCurrentProcessors
        // 避免引用传递，单独建 updateCurrentProcessors
        List<Employee> updateCurrentProcessors = processors;
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignment.getId());
        updateAssignment.setCurrentProcessorEmployee(updateCurrentProcessors);
        AssignmentAbility.update(updateAssignment);
    }

    /**
     * 网络变更 转交
     */
    private static void changeOrderTransferConfirm(
            IDataModel dataModel,
            Map<String, Object> customParameters,
            Assignment assignment) {

        boolean superAdminFlag = Boolean.parseBoolean(
                customParameters.get(CidConstants.SYSTEM_ADMIN_FLAG).toString());

        List<Employee> newResponsible;
        List<Employee> newCurrentProcessor;
        List<Employee> newCurrentProcessTeam;
        if (superAdminFlag) {
            // 1.系统管理员
            // 获取转交 责任人 + 当前处理人 + 当前处理组
            newResponsible = ComponentUtils.getEmployeeComponentInfo(
                    dataModel, CidConstants.RESPONSIBLE_PERSON);
            newCurrentProcessor = ComponentUtils.getEmployeeComponentInfo(
                    dataModel, CidConstants.CURRENT_PROCESSOR);
            newCurrentProcessTeam = ComponentUtils.getEmployeeComponentInfo(
                    dataModel, CidConstants.CURRENT_PROCESS_TEAM);
        } else {
            // 2.普通用户
            // 获取转交 责任人
            newResponsible = ComponentUtils.getEmployeeComponentInfo(
                    dataModel, CidConstants.RESPONSIBILITY_TRANSFERENCE_PERSON_CID);
            newCurrentProcessor = Lists.newArrayList();
            newCurrentProcessTeam = Lists.newArrayList();
        }

        // 3.任务转交
        networkChangeTransferResponsible(
                assignment,
                new ResponsibleTransferInfo(
                        superAdminFlag,
                        assignment.getResponsibleEmployee(),
                        assignment.getCurrentProcessorEmployee(),
                        newResponsible,
                        newCurrentProcessor,
                        newCurrentProcessTeam));
    }

    public static void networkChangeTransferResponsible(
            Assignment assignment,
            ResponsibleTransferInfo transferInfo) {

        boolean countingSignNode = ApproveNodeEnum.getSignApproveNodeEnums()
                .contains(assignment.getCurrentProgress());
        boolean executing = AssignmentStatusEnum.EXECUTE.getValue().equals(
                assignment.getAssignmentStatus());

        // 1.更新任务中心
        updateAssignmentCenter(assignment.getId(), transferInfo, executing, countingSignNode);

        // 2.新增任务关联关系
        createPersonRelevance(assignment.getId(), transferInfo);

        // 3.更新多联动产品保障任务责任人
        updateMultiProdGuarantee(assignment.getBillId(), transferInfo.targetResponsible);

        // 4.更新当前处理人审批任务
        if (transferInfo.superAdminFlag && !executing && !countingSignNode) {
            transferFlowApproveTask(assignment.getEntityId(), assignment.getCurrentProgress(), transferInfo);
        }

        // 5.记录更新日志
        createTransferLog(assignment, transferInfo);
    }

    /**
     * 更新任务中心
     */
    private static void updateAssignmentCenter(
            String assignmentId,
            ResponsibleTransferInfo transferInfo,
            boolean executing,
            boolean countingSignNode) {

        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignmentId);
        updateAssignment.setResponsibleEmployee(transferInfo.targetResponsible);

        // 非执行中 / 会签节点，超级管理员可以转交当前处理人
        if (transferInfo.superAdminFlag && !executing && !countingSignNode) {
            updateAssignment.setCurrentProcessorEmployee(transferInfo.targetCurrentProcessors);
        }

        AssignmentAbility.update(updateAssignment);
    }

    /**
     * 创建关联关系
     */
    private static void createPersonRelevance(
            String assignmentId,
            ResponsibleTransferInfo transferInfo) {

        Stream<Stream<Employee>> stream = transferInfo.superAdminFlag
                ? Stream.of(transferInfo.targetResponsible.stream(), transferInfo.targetCurrentProcessors.stream())
                : Stream.of(transferInfo.targetResponsible.stream());
        List<String> userIds = stream
                .flatMap(s -> s)
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
        AssignmentAbility.createAssignmentPersonRelevance(assignmentId, userIds);
    }

    /**
     * 转交审批流任务
     */
    private static void transferFlowApproveTask(
            String entityId,
            String currentProgress,
            ResponsibleTransferInfo transferInfo) {

        // 1.检索审批流当前处理人信息
        List<FlowHandler> flowHandlers
                = FlowHelper.getFlowHandlerByFlowEntityIds(Lists.newArrayList(entityId));
        if (CollectionUtils.isEmpty(flowHandlers)) {
            return;
        }

        FlowHandler flowHandler = flowHandlers.get(0);
        if (CollectionUtils.isEmpty(flowHandler.getApproveTaskList())) {
            return;
        }

        // 2.更新审批任务
        List<String> targetUserIds = transferInfo.targetCurrentProcessors.stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toList());
        List<String> remainUserIds = Lists.newArrayList();
        List<String> deleteTaskIds = Lists.newArrayList();
        flowHandler.getApproveTaskList().forEach(item -> {
            if (targetUserIds.contains(item.getApprover())) {
                remainUserIds.add(item.getApprover());
            } else {
                deleteTaskIds.add(item.getTaskId());
            }
        });

        targetUserIds.removeAll(remainUserIds);
        AddApproverDTO addInfo = new AddApproverDTO();
        addInfo.setBusinessId(entityId);
        addInfo.setNodeKey(flowHandler.getApproveTaskList().get(0).getNodeCode());
        addInfo.setApprovers(targetUserIds);
        FlowServiceHelper.addApprover(addInfo);

        deleteTaskIds.forEach(item -> {
            DeleteApprovalTaskDTO deleteInfo = new DeleteApprovalTaskDTO();
            deleteInfo.setTaskId(item);
            FlowServiceHelper.deleteTask(deleteInfo);
        });

        // 3.邮件催办 - 异步
        AsyncExecuteUtils.execute(() ->
                targetUserIds.forEach(item ->
                        EmailAbility.sendPressEmail(entityId, currentProgress, true, item)));
    }

    /**
     * 创建转交日志
     */
    private static void createTransferLog(
            Assignment assignment,
            ResponsibleTransferInfo transferInfo) {

        OperationLogRecordAbility.saveNetworkChangeTransferLog(
                Lists.newArrayList(assignment),
                transferInfo.currentResponsible,
                transferInfo.targetResponsible,
                MessageConsts.Backlog.LOG_TRANSFER_RESPONSIBLE);

        if (!transferInfo.superAdminFlag) {
            OperationLogRecordAbility.saveNetworkChangeTransferLog(
                    Lists.newArrayList(assignment),
                    transferInfo.currentProcessors,
                    transferInfo.targetCurrentProcessors,
                    MessageConsts.Backlog.LOG_TRANSFER_CURRENT_PROCESSOR);
        }
    }

    /**
     * 批次任务转交
     */
    private static void batchTransferConfirm(
            IDataModel dataModel,
            Map<String, Object> customParameters,
            Assignment assignment,
            AssignmentTypeEnum assignmentType) {

        boolean superAdminFlag = Boolean.parseBoolean(
                customParameters.get(CidConstants.SYSTEM_ADMIN_FLAG).toString());

        List<Employee> newResponsible;
        List<Employee> newCurrentProcessor;
        List<Employee> newCurrentProcessTeam;
        if (superAdminFlag) {
            // 1.系统管理员
            // 获取转交 责任人 + 当前处理人 + 当前处理组
            newResponsible = ComponentUtils.getEmployeeComponentInfo(
                    dataModel, CidConstants.RESPONSIBLE_PERSON);
            newCurrentProcessor = ComponentUtils.getEmployeeComponentInfo(
                    dataModel, CidConstants.CURRENT_PROCESSOR);
            newCurrentProcessTeam = ComponentUtils.getEmployeeComponentInfo(
                    dataModel, CidConstants.CURRENT_PROCESS_TEAM);
        } else {
            // 2.普通用户
            // 获取转交 责任人
            newResponsible = ComponentUtils.getEmployeeComponentInfo(
                    dataModel, CidConstants.RESPONSIBILITY_TRANSFERENCE_PERSON_CID);
            newCurrentProcessor = Lists.newArrayList();
            newCurrentProcessTeam = Lists.newArrayList();
        }

        // 3.任务转交
        batchTransferConfirm(
                assignment,
                assignmentType,
                new ResponsibleTransferInfo(
                        superAdminFlag,
                        assignment.getResponsibleEmployee(),
                        assignment.getCurrentProcessorEmployee(),
                        newResponsible,
                        newCurrentProcessor,
                        newCurrentProcessTeam));
    }

    /**
     * 批次任务 - 转交确认
     */
    private static void batchTransferConfirm(
            Assignment assignment,
            AssignmentTypeEnum assignmentType,
            ResponsibleTransferInfo transferInfo) {

        String billId = assignment.getBillId();

        AssignmentTypeEnum mainAssignmentType = AssignmentTypeEnum.NETWORK_CHANGE_BATCH == assignmentType
                ? AssignmentTypeEnum.NETWORK_CHANGE
                : AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE;

        // 1.检索对应主任务
        Assignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                billId, mainAssignmentType, Assignment.class);
        if (Objects.isNull(mainAssignment)) {
            return;
        }

        // 2.更新流程变量
        FlowHelper.changeFlowParams(assignment.getEntityId(),
                MapUtils.newHashMap(VARIABLE_EXECUTE_PERSON, transferInfo.targetResponsible.get(0).getEmpUIID()),
                mainAssignmentType.getApproveFlowCodeEnum());

        // 3.更新审批任务
        batchTransferFlowApproveTask(assignment, transferInfo);

        // 4.更新任务责任人
        batchUpdateAssignmentCenter(assignment, transferInfo);

        // 6.新增任务关联关系
        batchCreatePersonRelevance(mainAssignment.getId(), assignment.getCurrentProgress(), transferInfo);

        // 7.更新多联动产品保障任务责任人
        updateMultiProdGuarantee(billId, transferInfo.targetResponsible);

        // 8.记录更新日志
        batchCreateTransferLog(assignment, transferInfo);
    }

    /**
     * 转交审批流任务 - 批次任务
     */
    private static void batchTransferFlowApproveTask(
            Assignment assignment,
            ResponsibleTransferInfo transferInfo) {

        String batchTaskId = assignment.getEntityId();
        String currentProgress = assignment.getCurrentProgress();

        // 1.特殊节点校验 - 待发通告 / 待反馈结果
        boolean specialNodeFlag = ApproveNodeEnum.PENDING_NOTIFICATION.name().equals(currentProgress)
                || ApproveNodeEnum.RESULT_TOBE_BACK.name().equals(currentProgress);

        // 2.转交审批流任务
        if (transferInfo.superAdminFlag) {
            // 系统管理员，把审批任务转给新的当前处理人 / 当前处理组，并邮件催办
            transferFlowApproveTask(batchTaskId, currentProgress, transferInfo);
        } else if (specialNodeFlag) {
            // 特殊节点，需要把审批任务转到新责任人
            String newResponsibleId = transferInfo.targetResponsible.get(0).getEmpUIID();
            FlowHelper.reassignSystemNode(batchTaskId, newResponsibleId);
            EmailAbility.sendPressEmail(batchTaskId, currentProgress, true, newResponsibleId);
        }
    }

    /**
     * 更新任务中心
     */
    private static void batchUpdateAssignmentCenter(
            Assignment assignment,
            ResponsibleTransferInfo transferInfo) {

        String assignmentId = assignment.getId();
        String currentProgress = assignment.getCurrentProgress();

        // 1.特殊节点校验 - 待发通告 / 待反馈结果
        boolean specialNodeFlag = ApproveNodeEnum.PENDING_NOTIFICATION.name().equals(currentProgress)
                || ApproveNodeEnum.RESULT_TOBE_BACK.name().equals(currentProgress);

        // 2.更新任务数据
        Assignment updateAssignment = new Assignment();
        updateAssignment.setId(assignmentId);
        updateAssignment.setResponsibleEmployee(transferInfo.targetResponsible);
        if (transferInfo.superAdminFlag) {
            updateAssignment.setCurrentProcessorEmployee(transferInfo.targetCurrentProcessors);
        } else if (specialNodeFlag) {
            updateAssignment.setCurrentProcessorEmployee(transferInfo.targetResponsible);
        }
        AssignmentAbility.update(updateAssignment);

        // 3.重新汇总主任务当前处理人
        AssignmentAbility.recalculateMainAssignmentCurrentProcessor(Lists.newArrayList(assignment));
    }

    /**
     * 创建关联关系 - 批次任务
     */
    private static void batchCreatePersonRelevance(
            String mainAssignmentId,
            String currentProgress,
            ResponsibleTransferInfo transferInfo) {

        boolean specialNodeFlag = ApproveNodeEnum.PENDING_NOTIFICATION.name().equals(currentProgress)
                || ApproveNodeEnum.RESULT_TOBE_BACK.name().equals(currentProgress);

        Stream<Stream<Employee>> stream;
        if (transferInfo.superAdminFlag || specialNodeFlag) {
            stream = Stream.of(transferInfo.targetResponsible.stream(), transferInfo.targetCurrentProcessors.stream());
        } else {
            stream = Stream.of(transferInfo.targetResponsible.stream());
        }

        List<String> userIds = stream
                .flatMap(s -> s)
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
        AssignmentAbility.createAssignmentPersonRelevance(mainAssignmentId, userIds);
    }

    /**
     * 创建转交日志 - 批次任务
     */
    private static void batchCreateTransferLog(
            Assignment assignment,
            ResponsibleTransferInfo transferInfo) {

        // 1.特殊节点校验 - 待发通告 / 待反馈结果
        String currentProgress = assignment.getCurrentProgress();
        boolean specialNodeFlag = ApproveNodeEnum.PENDING_NOTIFICATION.name().equals(currentProgress)
                || ApproveNodeEnum.RESULT_TOBE_BACK.name().equals(currentProgress);

        // 2.责任人转交日志
        OperationLogRecordAbility.saveNetworkChangeTransferLog(
                Lists.newArrayList(assignment),
                transferInfo.currentResponsible,
                transferInfo.targetResponsible,
                MessageConsts.Backlog.LOG_TRANSFER_RESPONSIBLE);

        // 3.当前处理人转交日志
        if (transferInfo.superAdminFlag) {
            OperationLogRecordAbility.saveNetworkChangeTransferLog(
                    Lists.newArrayList(assignment),
                    transferInfo.currentProcessors,
                    transferInfo.targetCurrentProcessors,
                    MessageConsts.Backlog.LOG_TRANSFER_CURRENT_PROCESSOR);
        } else if (specialNodeFlag) {
            OperationLogRecordAbility.saveNetworkChangeTransferLog(
                    Lists.newArrayList(assignment),
                    transferInfo.currentProcessors,
                    transferInfo.targetResponsible,
                    MessageConsts.Backlog.LOG_TRANSFER_CURRENT_PROCESSOR);
        }
    }

    /**
     * 转交时若当前任务为多联动产品保障任务，则同步更新状态
     *
     * @param billId         变更单id
     * @param newResponsible 新责任人
     */
    private static void updateMultiProdGuarantee(String billId, List<Employee> newResponsible) {
        // 保障任务缺省一行，也就是说只要是变更单下述判断无效，但是能校验住其他的类型
        List<MultiProductLinkageGuarantee> multiProductLinkageGuarantees = MultiProdGuaranteeAbility.query(Lists.newArrayList(billId));
        if (CollectionUtils.isEmpty(multiProductLinkageGuarantees)) {
            return;
        }

        // 普通变更任务不涉及多模
        NetworkChangeAssignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                billId, AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);

        if (assignment == null || ChangeOrderTypeEnum.NORMAL.name().equals(assignment.getType())) {
            return;
        }

        // 获取保障任务对应的任务id
        List<String> assignmentIds = multiProductLinkageGuarantees.stream().map(MultiProductLinkageGuarantee::getAssignmentId).collect(Collectors.toList());
        // 获取这些任务的变更单id
        List<NetworkChangeAssignment> assignmentList = AssignmentAbility.querySpecificTypeAssignment(assignmentIds, NetworkChangeAssignment.class);
        List<String> changeOrderIds = assignmentList.stream().map(Assignment::getBillId).collect(Collectors.toList());
        // 最后获取全部的保障任务，匹配满足当前变更单id的全部保障任务，更新责任人
        List<MultiProductLinkageGuarantee> multiProductLinkageGuaranteeList = MultiProdGuaranteeAbility.query(changeOrderIds);
        // 过滤出其中任务id为当前保障任务的数据，全部更新责任人
        multiProductLinkageGuaranteeList = multiProductLinkageGuaranteeList.stream()
                .filter(item -> Objects.requireNonNull(assignment).getId().equals(item.getAssignmentId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(multiProductLinkageGuaranteeList)) {
            return;
        }
        // 关联的其他保障任务每条数据对应的pid和id都不一样，循环更新
        multiProductLinkageGuaranteeList.forEach(item -> {
            Map<String, Object> values = MapUtils.newHashMap(IMPLEMENTATION_RESPONSIBLE_PERSON, Lists.newArrayList(newResponsible.get(INTEGER_ZERO)));
            SaveDataHelper.update(MultiProductLinkageGuarantee.class, item.getPid(), item.getId(), values);
        });
    }

    /**
     * 技术管理任务 转交
     * @param assignment
     */
    private static void technologyTransferConfirm(IDataModel dataModel, Assignment assignment) {
        String createdBy = assignment.getCreateBy();
        String assignmentId = assignment.getId();

        List<Employee> newResponsible
                = ComponentUtils.getEmployeeComponentInfo(dataModel, RESPONSIBILITY_TRANSFERENCE_PERSON_CID);
        if (CollectionUtils.isEmpty(newResponsible)) {
            return;
        }

        // 1.转交前后责任人是否一致
        List<String> oldResponsibleIds = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());
        List<String> newResponsibleIds = newResponsible.stream()
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
        if (oldResponsibleIds.equals(newResponsibleIds)) {
            return;
        }

        // 2.更新任务责任人
        Assignment newAssignment = new Assignment();
        newAssignment.setId(assignmentId);
        newAssignment.setResponsibleEmployee(newResponsible);
        AssignmentAbility.update(newAssignment);

        // 3.删除历史责任人 - 任务关联关系
        // 历史责任人若是创建人，继续保留该关联关系
        oldResponsibleIds.remove(createdBy);
        AssignmentAbility.deleteAssignmentPersonRelevance(assignmentId, new ArrayList<>(oldResponsibleIds));

        // 4.新增责任人 - 任务关联关系
        AssignmentAbility.createAssignmentPersonRelevance(assignmentId, Lists.newArrayList(newResponsibleIds));

        // 5.实体任务单独操作
        transferManageTaskConfirm(dataModel,assignmentId);
    }

    /**
     * 技术管理子任务 转交
     * @param assignment
     */
    private static void subTransferConfirm(IDataModel dataModel, Assignment assignment) {
        String createdBy = assignment.getCreateBy();
        String assignmentId = assignment.getId();
        String billId = assignment.getBillId();

        List<Employee> newResponsible
                = ComponentUtils.getEmployeeComponentInfo(dataModel, RESPONSIBILITY_TRANSFERENCE_PERSON_CID);
        if (CollectionUtils.isEmpty(newResponsible)) {
            return;
        }

        // 1.转交前后责任人是否一致
        List<String> oldResponsibleIds = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());
        List<String> newResponsibleIds = newResponsible.stream()
                .map(Employee::getEmpUIID)
                .distinct()
                .collect(Collectors.toList());
        if (oldResponsibleIds.equals(newResponsibleIds)) {
            return;
        }

        // 2.检索对应主任务
        TechnologyManagementAssignment mainAssignment =
                AssignmentAbility.querySpecificTypeAssignment(billId, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT, TechnologyManagementAssignment.class);
        String mainAssignmentId = mainAssignment.getId();
        String mainCreatedBy = mainAssignment.getCreateBy();
        List<String> mainResponsible = EmployeeHelper.getEpmUIID(mainAssignment.getResponsibleEmployee());

        // 3.更新任务责任人
        Assignment newAssignment = new Assignment();
        newAssignment.setId(assignmentId);
        newAssignment.setResponsibleEmployee(newResponsible);
        AssignmentAbility.update(newAssignment);

        // 4.删除历史责任人 - 任务关联关系
        // 子任务对创建人 / 责任人关系 需转换为 主任务对批次任务创建人 / 责任人关系
        // 历史责任人若是子任务创建人 / 主任务创建人 / 责任人，继续保留该关联关系
        oldResponsibleIds.remove(createdBy);
        oldResponsibleIds.remove(mainCreatedBy);
        oldResponsibleIds.removeAll(mainResponsible);
        AssignmentAbility.deleteAssignmentPersonRelevance(mainAssignmentId, new ArrayList<>(oldResponsibleIds));

        // 4.新增责任人 - 任务关联关系
        AssignmentAbility.createAssignmentPersonRelevance(mainAssignmentId, Lists.newArrayList(newResponsibleIds));

        // 5.实体任务单独操作
        transferManageTaskConfirm(dataModel,assignmentId);
    }


    /**
     * 技术管理任务责任转交
     * 1.替换责任人
     * 2.插入责任转交记录
     */
    private static void transferManageTaskConfirm(IDataModel dataModel,String assignmentId) {
        // 1.检索技术变更数据
        TechnologyManagementAssignment mainAssignment =
                AssignmentAbility.querySpecificTypeAssignment(assignmentId, TechnologyManagementAssignment.class);

        //获取新的责任人
        Object newResponsiblePerson = dataModel.getValue(CidConstants.RESPONSIBILITY_TRANSFERENCE_PERSON_CID);
        if (Objects.isNull(mainAssignment) || Objects.isNull(newResponsiblePerson)) {
            return;
        }
        List<Employee> newResponsibleEmployees = JsonUtils.parseArray(newResponsiblePerson, Employee.class);
        String newResponsiblePersonValue = EmployeeHelper.getEpmNameUIID(newResponsibleEmployees);
        String approveSubTaskId = mainAssignment.getApproveSubTaskId();
        // 3. 构建操作日志记录
        //新增操作记录数据，操作名称为：责任转让，操作内容为：由xxx, xxx变更为xxx, xxx
        OperationLogRecord record = new OperationLogRecord();
        record.setRelationId(mainAssignment.getBillId());
        record.setOperationType(OperationLogRecordTypeEnum.MANAGE_TASK.getOperationType());
        // 2.根据子任务审批实体ID是否存在判断主/子任务转交
        List<Employee> oldResponsiblePerson;
        List<String> mailCCs;
        String orderNo;
        if (StringUtils.isNotEmpty(approveSubTaskId)) {
            // 2.1 子任务责任转交操作，获取表中的责任人，用新的责任人替换原责任人数据
            ManageSubTaskFlow subTaskFlow = ManageTaskAbility.queryBySubFlowId(approveSubTaskId);
            ManageSubTask manageSubTask = ManageTaskAbility.querySubTask(subTaskFlow.getSubTaskId(), subTaskFlow.getParentTaskId());
            // 获取旧责任人
            oldResponsiblePerson = Objects.requireNonNull(manageSubTask).getSubtaskResponsiblePerson();
            // 更新新责任人
            SaveDataHelper.update(ManageSubTask.class, subTaskFlow.getParentTaskId(), subTaskFlow.getSubTaskId(),
                    MapUtils.newHashMap(SUBTASK_RESPONSIBLE_PERSON, newResponsiblePerson));
            // 构建操作记录信息
            record.setParentRelationId(subTaskFlow.getParentTaskId());
            record.setRelationId(subTaskFlow.getSubTaskId());
            record.setOperationType(OperationLogRecordTypeEnum.MANAGE_SUBTASK.getOperationType());
            record.setChildName(subTaskFlow.getSubtaskName());
            TechnologyManagementOrder managementOrder = ManageTaskAbility.queryById(subTaskFlow.getParentTaskId());
            mailCCs = EmployeeHelper.getEpmUIID(managementOrder.getAcceptorPerson());
            orderNo = manageSubTask.getSubtaskCnNo();
        }else {
            // 2.2 主任务责任转交操作
            TechnologyManagementOrder managementOrder = ManageTaskAbility.queryById(mainAssignment.getBillId());
            // 检索旧责任人构建其他信息
            oldResponsiblePerson = managementOrder.getResponsiblePerson();
            // 更新新责任人
            managementOrder.setResponsiblePerson(JsonUtils.parseArray(newResponsiblePerson, Employee.class));
            ManageTaskAbility.update(managementOrder);
            // 检索是否存在知会人
            mailCCs = EmployeeHelper.getEpmUIID(managementOrder.getAcceptorPerson());
            orderNo = managementOrder.getCnNo();
        }
        String oldResponsiblePersonValue = EmployeeHelper.getEpmNameUIID(JsonUtils.parseArray(oldResponsiblePerson, Employee.class));
        insetOperationRecord(record, oldResponsiblePersonValue, newResponsiblePersonValue);

        //【iTech Cloud 技术管理任务】【{单据编号}/{任务名称}】责任人发生变更，由{旧责任人}变更为{新责任人}
        MultiLangText oldResponsibleFormatNames = EmployeeHelper.getEmployeeFormatNames(oldResponsiblePerson);
        MultiLangText newResponsibleFormatNames = EmployeeHelper.getEmployeeFormatNames(newResponsibleEmployees);
        Map<String, Object> data = EmailAbility.buildMessageZhContent(MessageConsts.EmailNotice.MESSAGE_MANAGETASK_TRANSFER_CONTENT, orderNo,
                mainAssignment.getAssignmentName(), oldResponsibleFormatNames.getZhCN(), newResponsibleFormatNames.getZhCN());
        data.putAll(EmailAbility.buildMessageEnContent(MessageConsts.EmailNotice.MESSAGE_MANAGETASK_TRANSFER_CONTENT, orderNo,
                mainAssignment.getAssignmentName(), oldResponsibleFormatNames.getEnUS(), newResponsibleFormatNames.getEnUS()));

        List<String> mailTos = EmployeeHelper.getEpmUIID(oldResponsiblePerson);
        mailTos.addAll(EmployeeHelper.getEpmUIID(newResponsiblePerson));
        mailTos.add(mainAssignment.getCreateBy());

        EmailAbility.asyncSendMail(EmailAbility.EmailParam.builder()
                .pkId(mainAssignment.getApproveSubTaskId() != null ? mainAssignment.getApproveSubTaskId() : mainAssignment.getBillId())
                .templateIdEnum(TemplateIdEnum.EMAIL_INFOM_1)
                .data(data)
                .mailTos(mailTos)
                .mailCCs(mailCCs)
                .isApprovePage(false)
                .assignmentTypeEnum(AssignmentTypeEnum.fromTextValuePair(mainAssignment.getAssignmentType()))
                .build());
    }

    /**
     * 保存操作记录
     *
     * @param record record
     * @param oldResponsiblePersonValue 旧责任人
     * @param newResponsiblePersonValue 新责任人
     */
    private static void insetOperationRecord(OperationLogRecord record, String oldResponsiblePersonValue, String newResponsiblePersonValue) {
        record.setOperationDescription(MsgUtils.getMessage(
                MessageConsts.MANAGE_TASK_RESPONSIBILITY_TRANSFERENCE_INFO, oldResponsiblePersonValue, newResponsiblePersonValue));
        record.setOperationName(ManageTaskOperationRecordEnum.RESPONSIBILITY_TRANSFERENCE.getMsgKey());

        record.setResponsiblePerson(ContextHelper.getEmpInfo());
        OperationLogRecordAbility.insert(record);
    }


    /**
     * 转交基础信息对象 - 责任人
     */
    private static final class ResponsibleTransferInfo {

        /** 系统管理员标识 */
        private final boolean superAdminFlag;

        /** 当前责任人 */
        private final List<Employee> currentResponsible;

        /** 当前处理人（全量） */
        private final List<Employee> currentProcessors;

        /** 转交责任人 */
        private final List<Employee> targetResponsible;

        /** 转交处理人（全量） */
        private final List<Employee> targetCurrentProcessors;

        ResponsibleTransferInfo(
                boolean superAdminFlag,
                List<Employee> currentResponsible,
                List<Employee> currentProcessors,
                List<Employee> targetResponsible,
                List<Employee> targetProcessor,
                List<Employee> targetProcessTeam) {

            this.superAdminFlag = superAdminFlag;
            this.currentResponsible = currentResponsible;
            this.currentProcessors = currentProcessors;
            this.targetResponsible = targetResponsible;

            List<String> targetProcessorIds = targetProcessor.stream()
                    .map(Employee::getEmpUIID)
                    .collect(Collectors.toList());
            targetProcessTeam = targetProcessTeam.stream()
                    .filter(item -> !targetProcessorIds.contains(item.getEmpUIID()))
                    .collect(Collectors.toList());
            targetProcessor.addAll(targetProcessTeam);
            this.targetCurrentProcessors = targetProcessor;
        }
    }
}
