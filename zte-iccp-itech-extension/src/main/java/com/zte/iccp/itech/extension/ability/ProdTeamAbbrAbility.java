package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/24
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ProdTeamAbbrAbility {

    private static final String ABBR_NAME_MSG_ID_PREFIX = "prodTeamAbbr.";

    private static final String PROD_LINE_IDS_CFG_KEY_PREFIX = "prodTeam.prodLineIds.";

    private static final List<String> PROD_TEAMS = Lists.newArrayList(
            "ran", "mw", "wantong", "jinyi", "ids",
            "fm", "bn", "dc", "sdi", "ccn",
            "te", "energy", "vd", "gdb");

    private static final Map<String, String> PROD_LINE_2_MSG_ID = MapUtils.newHashMap();

    static {
        for (String team : PROD_TEAMS) {
            String[] prodLineIds = ConfigHelper.getArray(PROD_LINE_IDS_CFG_KEY_PREFIX + team);
            for (String prodLineId : prodLineIds) {
                PROD_LINE_2_MSG_ID.put(prodLineId, ABBR_NAME_MSG_ID_PREFIX + team);
            }
        }
    }

    public static String getAbbr(String langId, String prodCategory) {
        String prodLine = ProductUtils.getLine(prodCategory);
        String msgId =  PROD_LINE_2_MSG_ID.get(prodLine);
        return MsgUtils.getLangMessage(langId, msgId);
    }
}