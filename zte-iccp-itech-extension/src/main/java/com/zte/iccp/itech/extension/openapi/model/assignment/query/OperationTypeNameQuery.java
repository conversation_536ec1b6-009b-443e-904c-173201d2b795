package com.zte.iccp.itech.extension.openapi.model.assignment.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024/8/22 下午5:30
 */
@ApiModel("操作类型名称下拉框查询")
@Setter
@Getter
public class OperationTypeNameQuery {

    @ApiModelProperty("快码类型")
    @NotNull
    private String type;

    //操作类型 =产品小类名称； 操作原因 = 操作类型code
    @ApiModelProperty("扩展字段")
    @NotNull
    private String  ext;
}
