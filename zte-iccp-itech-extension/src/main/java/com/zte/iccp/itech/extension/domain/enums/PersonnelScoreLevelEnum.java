package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.zlic.util.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 人员积分任务等级
 *
 * <AUTHOR>
 * @create 2025/6/16 下午1:53
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum PersonnelScoreLevelEnum {
    /*
     * B等级
     * */
    B(1, Lists.newArrayList(ApproveRoleEnum.SECURITY_DIRECTOR, ApproveRoleEnum.TD_SECTION_CHIEF), "4", null),
    /*
     * C等级
     * */
    C(6, Lists.newArrayList(ApproveRoleEnum.SECURITY_DIRECTOR), "3", "4"),
    /*
     * D等级
     * */
    D(6, Lists.newArrayList(ApproveRoleEnum.SECURITY_DIRECTOR), null, "4"),
    ;

    /**
     * 要求完成月
     */
    private final long completedMouth;


    /**
     * 审核人配置角色
     */
    private final List<ApproveRoleEnum> approveRole;

    /**
     * 知会人领导层级
     */
    private final String informLevel;

    /**
     * 验收人领导层级
     */
    private final String acceptorLevel;

}
