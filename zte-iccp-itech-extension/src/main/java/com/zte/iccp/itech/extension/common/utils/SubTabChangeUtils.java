package com.zte.iccp.itech.extension.common.utils;

import com.zte.paas.lcap.ddm.common.api.form.IFormView;

/**
 * @version V1.0
 * @author:
 * @date: 2025/7/1 下午8:17
 * @Description:
 */
public class SubTabChangeUtils {

    public static void refreshData(IFormView iFormView, String tableId) {
        if (iFormView != null && iFormView.getClientViewProxy() != null) {
            iFormView.getClientViewProxy().refreshData(iFormView.getPageSessionId(), tableId, true);
        }
    }
}
