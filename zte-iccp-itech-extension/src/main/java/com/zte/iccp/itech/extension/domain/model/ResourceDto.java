package com.zte.iccp.itech.extension.domain.model;

import com.zte.iccp.itech.extension.domain.enums.ResourceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname RoleMenuVo
 * @Description 角色菜单资源vo
 * @Date 2024/11/20 下午9:17
 * @<NAME_EMAIL>
 */
@Getter
@Setter
@ApiModel("菜单资源对象")
public class ResourceDto implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("fullName")
    private String fullName;

    @ApiModelProperty("resourceUrl")
    private String resourceUrl;

    @ApiModelProperty("resourceOrder")
    private Integer resourceOrder;

    @ApiModelProperty("methodType")
    private String methodType;

    @ApiModelProperty("resourceType")
    private ResourceTypeEnum resourceType;

    @ApiModelProperty("extendTxt")
    private String extendTxt;

    @ApiModelProperty("parentId")
    private Long parentId;

    @ApiModelProperty("resourceCode")
    private String resourceCode;

    @ApiModelProperty("children")
    private List<ResourceDto> children;
}
