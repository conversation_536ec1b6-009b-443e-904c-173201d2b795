package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ToolManagementFieldConsts.*;

@ApiModel("工具管理(tool_management)")
@Setter
@Getter
@BaseEntity.Info("tool_management")
public class ToolManagementAttribute extends BaseEntity {
    @JsonProperty(PROD_OPERATION_TEAM)
    @ApiModelProperty("产品经营团队")
    private List<TextValuePair> operationTeam;

    @JsonProperty(PRODUCT_LINE)
    @ApiModelProperty("产品线")
    private List<TextValuePair> productLine;

    @JsonProperty(TOOL_ZH)
    @ApiModelProperty("工具中文名")
    private String toolZh;

    @JsonProperty(TOOL_EN)
    @ApiModelProperty("工具英文名")
    private String toolEn;

    @JsonProperty(IS_VALID)
    @ApiModelProperty("是否有效")
    private List<TextValuePair> isValid;
}
