package com.zte.iccp.itech.extension.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import java.io.ByteArrayOutputStream;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/09
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ThrowableUtils {

    @SneakyThrows
    public static String getStackTrace(Throwable e) {
        if (e == null) {
            return "-";
        }

        try (ByteArrayOutputStream os = new ByteArrayOutputStream();
                PrintWriter writer = new PrintWriter(os)) {
            e.printStackTrace(writer);
            writer.flush();
            return String.format("%s%n%s",
                    e.getMessage(),
                    new String(os.toByteArray(), StandardCharsets.UTF_8));
        }
    }
}
