package com.zte.iccp.itech.extension.domain.enums.permissionapplication;

import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EMPTY_STRING;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;

/**
 * <AUTHOR>
 * @date 2024/10/12 上午11:11
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PermissionApplicationStatusEnum {

    /**
     * 拟制
     */
    DRAFT("0", "拟制", "Draft"),

    /**
     * 审批中
     */
    APPROVING("1", "审批中", "Approving"),

    /**
     * 有效
     */
    VALID("2", "有效", "Valid"),

    /**
     * 失效
     */
    INVALID("3", "失效", "Invalid"),

    /**
     * 拟制（申请驳回后）
     */
    APPROVAL_RETURN_DRAFT("4", "拟制", "Draft"),
    ;

    private final String status;

    private final String nameZh;

    private final String nameEn;

    public static PermissionApplicationStatusEnum getByName(String name) {
        for (PermissionApplicationStatusEnum statusEnum : PermissionApplicationStatusEnum.values()) {
            if (statusEnum.getNameEn().equals(name) || statusEnum.getNameZh().equals(name)) {
                return statusEnum;
            }
        }
        return null;
    }

    public String getName() {
        return ZH_CN.equals(ContextHelper.getLangId()) ? this.nameZh : this.nameEn;
    }

    public static String getName(String status) {
        for (PermissionApplicationStatusEnum statusEnum : PermissionApplicationStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return ZH_CN.equals(ContextHelper.getLangId()) ? statusEnum.nameZh : statusEnum.nameEn;
            }
        }

        return null;
    }

    /**
     * 转换 任务中心 状态枚举
     * 非枚举必备属性，采用方法转换
     */
    public static String convertAssignmentStatus(String applicationStatus) {
        PermissionApplicationStatusEnum validStatusEnum = null;
        for (PermissionApplicationStatusEnum statusEnum : PermissionApplicationStatusEnum.values()) {
            if (statusEnum.getStatus().equals(applicationStatus)) {
                validStatusEnum = statusEnum;
            }
        }
        if (Objects.isNull(validStatusEnum)) {
            return EMPTY_STRING;
        }

        switch (validStatusEnum) {
            case DRAFT:
                return AssignmentStatusEnum.DRAFT_APPLICATION.getValue();

            case APPROVING:
                return AssignmentStatusEnum.APPROVE.getValue();

            case VALID:
                return AssignmentStatusEnum.VALID.getValue();

            case INVALID:
                return AssignmentStatusEnum.INVALID.getValue();

            case APPROVAL_RETURN_DRAFT:
                return AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.getValue();

            default:
                return EMPTY_STRING;
        }
    }
}
