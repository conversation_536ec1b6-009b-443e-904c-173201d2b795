package com.zte.iccp.itech.extension.common.utils;

/* Started by AICoder, pid:l418cq5d11ze8b61425c088ff02d3847faf44165 */

import com.zte.iccp.itech.extension.common.LcapBusiException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class RetryUtils {

    private static final int DEFAULT_RETRY_COUNT = 3;

    private static final long DEFAULT_INTERVAL = 1000;

    public static <T> T get(Supplier<T> supplier) {
        return get(supplier, Objects::nonNull, DEFAULT_RETRY_COUNT, DEFAULT_INTERVAL);
    }

    public static <T> T get(Supplier<T> supplier, int retryCount) {
        return get(supplier, Objects::nonNull, retryCount, DEFAULT_INTERVAL);
    }

    @SneakyThrows
    public static <T> T get(Supplier<T> supplier, Predicate<T> predicate, int retryCount, long interval) {
        for (int i = 0; i < retryCount; i++) {
            try {
                T result = supplier.get();
                if (predicate.test(result)) {
                    return result;
                }
            } catch (Exception e) {
                if (i == retryCount - 1) {
                    throw e;
                }
            }
            TimeUnit.MILLISECONDS.sleep(interval);
        }

        throw new LcapBusiException("retry get failed");
    }

    public static void run(Runnable runnable) {
        run(runnable, v -> true, DEFAULT_RETRY_COUNT, DEFAULT_INTERVAL);
    }

    public static void run(Runnable runnable, int retryCount) {
        run(runnable, v -> true, retryCount, DEFAULT_INTERVAL);
    }

    @SneakyThrows
    public static void run(Runnable runnable, Predicate<Void> predicate, int retryCount, long interval) {
        for (int i = 0; i < retryCount; i++) {
            try {
                runnable.run();
                if (predicate.test(null)) {
                    return;
                }
            } catch (Exception e) {
                if (i == retryCount - 1) {
                    throw e;
                }
            }
            TimeUnit.MILLISECONDS.sleep(interval);
        }

        throw new LcapBusiException("retry run failed");
    }
}

/* Ended by AICoder, pid:l418cq5d11ze8b61425c088ff02d3847faf44165 */
