package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.IS_NEED_AUTHORIZATION_FILE;

/**
 * <AUTHOR>
 * @date 2024/7/29 上午11:32
 */
@Getter
@Setter
@BaseEntity.Info("oc_apply")
public class ChangeOrderAll extends BaseEntity {

    @JsonProperty(value = ORDER_NO)
    private String orderNo;

    @JsonProperty(value = OPERATION_SUBJECT)
    private String operationSubject;

    @JsonProperty(value = OPERATION_SUBJECT_PREFIX)
    private String operationSubjectPrefix;

    @JsonProperty(value = OPERATION_SUBJECT_SUFFIX)
    private String operationSubjectSuffix;

    @JsonProperty(value = IS_GDPR)
    private List<TextValuePair> isGdpr;

    @JsonProperty(value = GDPR_REQUIRE)
    private List<TextValuePair> gdprRequire;

    @JsonProperty(value = DELIVERY_MODE)
    private List<TextValuePair> deliveryMode;

    @JsonProperty(value = RESPONSIBLE_DEPT)
    private List<TextValuePair> responsibleDept;

    @JsonProperty(value = IS_GOV_ENT)
    private List<TextValuePair> isGovEnt;

    @JsonProperty(value = OPERATION_TYPE)
    private List<TextValuePair> operationType;

    @JsonProperty(value = OPERATION_TYPE_GROUP)
    private String operationTypeGroup;

    @JsonProperty(value = OPERATION_REASON)
    private List<TextValuePair> operationReason;

    @JsonProperty(value = IMPORTANCE)
    private List<TextValuePair> importance;

    @JsonProperty(value = RISK_EVALUATION)
    private List<TextValuePair> riskEvaluation;

    @JsonProperty(value = OPERATION_LEVEL)
    private List<TextValuePair> operationLevel;

    @JsonProperty(value = IS_EMERGENCY_OPERATION)
    private List<TextValuePair> isEmergencyOperation;

    @JsonProperty(value = EMERGENCY_OPERATION_REASON)
    private String emergencyOperationReason;

    @JsonProperty(value = IS_NET_CLOSE_OR_CONTROL_OPERATION)
    private List<TextValuePair> isNetCloseOrControlOperation;

    @JsonProperty(value = NET_CLOSE_OR_CONTROL_OPERATION_REASON)
    private String netCloseOrControlOperationReason;

    @JsonProperty(value = TRIGGER_TYPE)
    private List<TextValuePair> triggerType;

    @JsonProperty(value = IS_FIRST_APPLICATION)
    private List<TextValuePair> isFirstApplication;

    @JsonProperty(value = IS_TECHNICAL_NOTICE)
    private List<TextValuePair> isTechnicalNotice;

    @JsonProperty(value = MULTI_PROD_GUARANTEE)
    private List<TextValuePair> isMultiProdGuarantee;

    @JsonProperty(value = IS_BCN_LINKAGE_GUARANTEE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isBcnLinkageGuarantee;

    @JsonProperty(value = TOOL_USE)
    private List<TextValuePair> toolUse;

    @JsonProperty(value = OPERATION_DESC)
    private String operationDesc;

    @JsonProperty(value = TIME_ZONE)
    private String timeZone;

    @JsonProperty(value = OPERATION_START_TIME)
    private Date operationStartTime;

    @JsonProperty(value = OPERATION_END_TIME)
    private Date operationEndTime;

    @JsonProperty(value = SERVICE_DISCONNECT_DURATION)
    private BigDecimal serviceDisconnectDuration;

    @JsonProperty(value = CUSTOMER_ID)
    private String customerId;

    @JsonProperty(value = ACCN_TYPE)
    private String customerTypeFlag;

    @JsonProperty(value = PRODUCT_CATEGORY)
    private List<TextValuePair> productCategory;

    @JsonProperty(value = SOURCE)
    private String source;

    @JsonProperty(value = OPERATION_ENCAPSULATION)
    private Object operationEncapsulation;

    @JsonProperty(value = CHANGE_OPERATION_SOURCE)
    private Object changeOperationSource;

    @JsonProperty(value = IS_AFFECT_TO_B)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isAffectToB;

    @JsonProperty(value = IS_NEED_AUTHORIZATION_FILE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isNeedAuthorizationFile;

    @JsonProperty(value = IS_MULTI_MODE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isMultiMode;

    @JsonProperty(value = MULTIMODE_PRODUCT)
    private Object multiModeProduct;

    @JsonProperty(value = LOGICAL_NE_PROPERTY_KEY)
    private Object selectfieldLogicalNe;

    @JsonProperty(value = IS_COMMERCIAL_OFFICE)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isCommercialOffice;

    @JsonProperty(value = IS_REGIONAL_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isRegionalOperation;

    @JsonProperty(value = OVDC_NFVI_PROPERTY_KEY)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum ovdcNfviPropertyKey;

    @JsonProperty(value = IS_BUSINESS_OPERATION)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isBusinessOperation;

    @JsonProperty(value = OPERATION_SCENARIO_KEY)
    private Object operationScenario;

    @JsonProperty(value = FIELD_IS_SPECIAL_SCENARIO_CID)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private BoolEnum isSpecialScenario;

    @JsonProperty(value = CUSTOMER_SPECIAL_SERVICE)
    private Object customerSpecialService;


    @JsonProperty(value = IS_COMMERCIAL_CHARGE_CONTRACT)
    private Object isCommercialChargeContract;

    @JsonProperty(value = IS_CUSTOMER_SCAN_PERMISSION)
    private Object isCustomerScanPermission;

    @JsonProperty(value = BACKUP_RADIO_PARAMETER)
    private Object backupRadioParameter;

    @JsonProperty(value = LICENSE_LOAD)
    private Object licenseLoad;

    @JsonProperty(value = COUNTRY)
    private Object country;

    @JsonProperty(value = PROVINCE)
    private Object province;

    @JsonProperty(value = CITY)
    private Object area;

}
