package com.zte.iccp.itech.extension.openapi.maintenance;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.BatchSummaryAbility;
import com.zte.iccp.itech.extension.ability.ObjectLinkInstanceAbility;
import com.zte.iccp.itech.extension.ability.PartnerChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.changeorder.OperationObjectAbility;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.domain.model.subentity.SubconBatchSummary;
import com.zte.iccp.itech.extension.plugin.flow.changeorder.SaveBatchTaskPlugin;
import com.zte.iccp.itech.extension.plugin.flow.partnerchangeorder.SavePartnerBatchTaskPlugin;
import com.zte.paas.lcap.common.api.RequestHeaderUtils;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.core.orm.query.Comparator;
import com.zte.paas.lcap.core.orm.query.Filter;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import lombok.NonNull;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ENTITY_ID;

/**
 * <AUTHOR>
 * &#064;description:  修复批次创建异常的问题
 * &#064;date  2025/7/6 18:49
 * zte-iccp-itech-netchange
 */
public class BatchTaskFixPlugin extends AbstractOpenApi {

    /**
     * 刷新审核人配置 片区字段赛错成代表处接口
     */
    public void batchTaskFixPlugin(@NonNull @RequestBody List<String> orderIdList) {

        for (String changeOrderId : orderIdList) {
            // 1.检索主任务
            NetworkChangeAssignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                    changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, NetworkChangeAssignment.class);
            ChangeOrder changeOrder = ChangeOrderAbility.get(changeOrderId, Lists.newArrayList());
            // 查询对应批次
            List<BatchTask> batchTasks = BatchTaskAbility.batchGetByChangeOrderId(
                    changeOrderId, BatchTask.class, Lists.newArrayList());
            if (Objects.isNull(mainAssignment) || changeOrder.isSecondaryGuarantyOrder() || CollectionUtils.isEmpty(
                    batchTasks)) {
                //找不到。保障单类型。无批次，不需要处理
                continue;
            }
            List<String> batchEntityIds = batchTasks.stream()
                    .map(BatchTask::getId)
                    .collect(Collectors.toList());
            Filter entityIdFilter = new Filter(ENTITY_ID, Comparator.IN, batchEntityIds);
            List<Assignment> assignmentList = QueryDataHelper.query(
                    Assignment.class, Lists.newArrayList(), Lists.newArrayList(entityIdFilter));
            if (!CollectionUtils.isEmpty(assignmentList)) {
                //防止重复操作
                continue;
            }
            // 2.设置线程用户
            String empNo = CollectionUtils.isEmpty(mainAssignment.getResponsibleEmployee())
                    ? mainAssignment.getCreateBy()
                    : mainAssignment.getResponsibleEmployee().get(0).getEmpUIID();
            RequestContextHolder.setEmpNo(empNo);
            RequestHeaderUtils.setEmpNo(empNo);

            doFix(changeOrderId, batchTasks, mainAssignment, changeOrder);
        }
    }

    private static void doFix(
            String changeOrderId,
            List<BatchTask> batchTasks,
            NetworkChangeAssignment mainAssignment,
            ChangeOrder changeOrder) {
        // 4.检索变更单批次概要
        List<BatchSummary> batchSummaryList = BatchSummaryAbility.listBatchSummary(changeOrderId, BatchSummary.class);

        // 5.检索变更单操作对象
        Map<String, List<OperationObject>> batchOpeationObjectMap = OperationObjectAbility.getBatchOperationObject(
                changeOrderId, BatchSummary.class);

        // 获取批次编号和id关系
        Map<String, String> batchNo2Id = batchTasks.stream()
                .collect(Collectors.toMap(BatchTask::getBatchNo, BatchTask::getId));

        /*补充由于时区等问题导致批次创建异常的7，8，9步骤*/
        // 7.新增列表批次任务
        List<String> batchIds = new SaveBatchTaskPlugin().createListBatchAssignment(
                mainAssignment, batchSummaryList, batchOpeationObjectMap, batchNo2Id, changeOrder);

        // 8.新增列表主任务 / 批次任务关联关系
        ObjectLinkInstanceAbility.createParentChildrenLinkInstance(
                NetworkChangeAssignment.class, mainAssignment.getId(), batchIds);
        // 9.主任务变更
        // 状态: 执行中, 当前进展: 批次审批中, 当前处理人: 主任务责任人, 是否审批任务: 否
        NetworkChangeAssignment newAssignment = new NetworkChangeAssignment();
        newAssignment.setId(mainAssignment.getId());
        newAssignment.setAssignmentStatus(AssignmentStatusEnum.EXECUTE.getValue());
        newAssignment.setCurrentProgress(ApproveNodeEnum.BATCH_APPROVALING.name());
        newAssignment.setCurrentProcessorEmployee(Boolean.FALSE
                ? Lists.newArrayList()
                : EmployeeHelper.uniqueEmployees(mainAssignment.getResponsibleEmployee()));
        newAssignment.setApprovalTaskFlag(BoolEnum.N);
        AssignmentAbility.update(newAssignment);
        /*补充由于时区等问题导致批次创建异常的7，8，9步骤 end*/
    }


    public void batchPartnerTaskFixPlugin(@NonNull @RequestBody List<String> orderIdList) {

        for (String changeOrderId : orderIdList) {

            // 1.检索网络变更单
            SubcontractorChangeOrder changeOrder = PartnerChangeOrderAbility.get(changeOrderId);
            // 2. 校验生成批次
            List<SubcontractorBatchTask> batchTasks = BatchTaskAbility
                    .batchGetByChangeOrderId(changeOrderId, SubcontractorBatchTask.class, Lists.newArrayList());
            // 3.检索主任务
            NetworkChangeAssignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                    changeOrderId, AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE, NetworkChangeAssignment.class);

            if (Objects.isNull(mainAssignment) || CollectionUtils.isEmpty(batchTasks)) {
                //找不到。无批次，不需要处理
                continue;
            }
            // 2.设置线程用户
            String empNo = CollectionUtils.isEmpty(mainAssignment.getResponsibleEmployee())
                    ? mainAssignment.getCreateBy()
                    : mainAssignment.getResponsibleEmployee().get(0).getEmpUIID();
            RequestContextHolder.setEmpNo(empNo);
            RequestHeaderUtils.setEmpNo(empNo);
            // 4.检索变更单批次概要
            doFixPartner(changeOrderId, batchTasks, mainAssignment, changeOrder);
        }
    }

    private static void doFixPartner(
            String changeOrderId,
            List<SubcontractorBatchTask> batchTasks,
            NetworkChangeAssignment mainAssignment,
            SubcontractorChangeOrder changeOrder) {
        List<BatchSummary> batchSummaries =
                BatchSummaryAbility.listBatchSummary(changeOrderId, SubconBatchSummary.class);
        // 5.检索变更单操作对象
        Map<String, List<OperationObject>> batchOpeationObjectMap =
                OperationObjectAbility.getBatchOperationObject(changeOrderId, SubconBatchSummary.class);

        // 6. 获取批次编号和id关系
        Map<String, String> batchNo2Id = batchTasks.stream()
                .collect(Collectors.toMap(SubcontractorBatchTask::getBatchNo, SubcontractorBatchTask::getId));

        // 7.新增列表批次任务
        // 初始情况下, 批次任务创建人 / 责任人是主任务的责任人, 已有主任务关联关系, 无需再新增关联关系
        List<String> batchIds = new SavePartnerBatchTaskPlugin().createListBatchAssignment(
                mainAssignment, batchSummaries, batchOpeationObjectMap, batchNo2Id, changeOrder);

        // 8.新增列表主任务 / 批次任务关联关系
        ObjectLinkInstanceAbility.createParentChildrenLinkInstance(
                NetworkChangeAssignment.class, mainAssignment.getId(), batchIds);

        // 9.主任务属性变更
        // 状态: 执行中, 当前进展: 批次审批中, 当前处理人: 主任务责任人, 是否审批任务: 否
        NetworkChangeAssignment newAssignment = new NetworkChangeAssignment();
        newAssignment.setId(mainAssignment.getId());
        newAssignment.setAssignmentStatus(AssignmentStatusEnum.EXECUTE.getValue());
        newAssignment.setCurrentProgress(PartnerApproveNodeEnum.BATCH_APPROVALING.name());
        newAssignment.setCurrentProcessorEmployee(
                EmployeeHelper.uniqueEmployees(mainAssignment.getResponsibleEmployee()));
        newAssignment.setApprovalTaskFlag(BoolEnum.N);

        AssignmentAbility.update(newAssignment);
    }

}
