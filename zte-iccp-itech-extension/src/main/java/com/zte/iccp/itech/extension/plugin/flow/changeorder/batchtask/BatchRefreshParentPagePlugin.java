package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.zte.iccp.itech.extension.plugin.operation.flow.BaseFlowOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;

import java.util.HashMap;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.ITECH_CLOUD_CUSTOM_BATCHTASK_REFRESH_BUTTON_CID;

/**
 * 流程提交后 刷新最外层批次汇总页面
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/2/17
 */
public class BatchRefreshParentPagePlugin extends BaseFlowOperationPlugin {
    @Override
    public final void afterOperate(ExecuteEvent event) {
        // 刷新操作，间隔时间2秒（提交之后马上刷状态可能还是上次的状态）
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            // do nothing.
        }
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("refresh", true);
        getView().getClientViewProxy().setControlState(ITECH_CLOUD_CUSTOM_BATCHTASK_REFRESH_BUTTON_CID, dataMap);
    }
}
