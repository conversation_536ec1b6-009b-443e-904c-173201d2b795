package com.zte.iccp.itech.extension.openapi.model.reportpush;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/6/12 下午2:37
 */
@Setter
@Getter
public class GlobalSupportDetailVO {

    /**
     * 操作单号
     */
    private String batchCode;

    /**
     * 操作主题
     */
    private String operationSubject;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 产品大类
     */
    private String productCategory;

    /**
     * 产品小类
     */
    private String productSubCategory;

    /**
     * 操作时间
     */
    private String planOperationStartTime;

    /**
     * 代表处
     */
    private String representativeOffice;

    /**
     * 片区
     */
    private String marketing;

    /**
     * 当前状态
     */
    private String currentStatus;

    /**
     * 现场人员
     */
    private String sitePerson;

    /**
     * 支持人员
     */
    private String supportStaff;

    /**
     * 支持人员工号
     */
    private String supportStaffNo;
}
