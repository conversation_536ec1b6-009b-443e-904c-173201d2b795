package com.zte.iccp.itech.extension.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-11 上午9:17
 **/
@Setter
@Getter
public class UserPermissionDto  {

    /**
     * 账号
     * */
    private String accountId;

    /**
     * 角色code
     * */
    private String roleCode;

    /**
     * 组织
     * */
    private String organization;

    /**
     * 组织名称
     * */
    private String organizationName;

    /**
     * 产品
     * */
    private String product;

    /**
     * 产品名称
     * */
    private String productName;

    /**
     * 查询方式 user 按用户查询  type按角色查询
     * */
    private String queryType;


    /** 查询的授权开始时间*/
    private Date beginDate;

    /** 有效期或者查询的授权结束时间*/
    private Date endDate;

    /** 查询页码 */
    private Integer pageNum;

    /** 查询页长 */
    private Integer pageSize;
}
