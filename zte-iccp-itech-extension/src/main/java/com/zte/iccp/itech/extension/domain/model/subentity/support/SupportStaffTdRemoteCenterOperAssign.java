package com.zte.iccp.itech.extension.domain.model.subentity.support;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.changeorder.EntityEnum.SupportModeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.subentity.SupportStaffFieldConsts.*;

@ApiModel("支持人员 -远程中心操作实施指派")
@Setter
@Getter
@BaseSubEntity.Info(value = "support_person_remote_center_oper_assign", parent = ChangeOrder.class)
public class SupportStaffTdRemoteCenterOperAssign extends BaseSubEntity {

    @ApiModelProperty("支持人员")
    @JsonProperty(value = PERSON_ID_REMOTE_CENTER_OPER_ASSIGN)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee person;

    @ApiModelProperty("支持方式")
    @JsonProperty(value = SUPPORT_WAY_REMOTE_CENTER_OPER_ASSIGN)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private SupportModeEnum supportMode;

    @ApiModelProperty("任务说明")
    @JsonProperty(value = TASK_DESC_REMOTE_CENTER_OPER_ASSIGN)
    private String taskDescription;

    @ApiModelProperty("所属部门")
    @JsonProperty(value = DEPT_ID_REMOTE_CENTER_OPER_ASSIGN)
    private String department;

    @ApiModelProperty("联系方式")
    @JsonProperty(value = TEL_NO_REMOTE_CENTER_OPER_ASSIGN)
    private String telephoneNumber;
}
