package com.zte.iccp.itech.extension.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.SneakyThrows;

import java.util.Map;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/13
 */
public class InfraInfoPkIdDeserializer extends JsonDeserializer<String> {

    protected String pkPattern = "pk_\\d+";

    @SneakyThrows
    @Override
    public final String deserialize(JsonParser p, DeserializationContext ctxt) {
        Object o = p.getCodec().readValue(p, Object.class);
        if (o instanceof String) {
            return (String) o;
        }

        if (o instanceof Map) {
            //noinspection unchecked
            Map<String, Object> map = (Map<String, Object>) o;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getKey().matches(pkPattern)) {
                    return String.valueOf(entry.getValue());
                }
            }
        }

        return null;
    }
}
