package com.zte.iccp.itech.extension.domain.constant.entity;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PersonRelevanceFieldConsts {

    /** 任务ID */
    public static final String ASSIGNMENT_ID = "assignment_id";

    /** 关联人员 */
    public static final String RELEVANT = "relevant";

    /** 是否本人处理 */
    public static final String SELF_HANDLER_FLAG = "self_handler_flag";

    /** 是否审批任务 */
    public static final String APPROVAL_TASK_FLAG = "approval_task_flag";
}
