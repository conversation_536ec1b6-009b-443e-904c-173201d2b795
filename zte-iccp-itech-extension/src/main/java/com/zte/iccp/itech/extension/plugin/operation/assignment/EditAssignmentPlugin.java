package com.zte.iccp.itech.extension.plugin.operation.assignment;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.ManageTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.helper.EmployeeHelper;
import com.zte.iccp.itech.extension.common.helper.EntityHelper;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.UserTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ChangeOrderTypeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.plugin.operation.BaseOperationPlugin;
import com.zte.paas.lcap.ddm.common.api.constant.MsgType;
import com.zte.paas.lcap.ddm.common.api.control.elementstatus.PageStatusEnum;
import com.zte.paas.lcap.ddm.common.api.event.ExecuteEvent;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.common.api.form.parameter.FormShowParameter;
import com.zte.paas.lcap.ddm.domain.enums.OpenTypeEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.PageConstants.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts.ASSIGNMENT_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.*;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.CREATE_BY;
import static com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts.TASK_ID;

public class EditAssignmentPlugin extends BaseOperationPlugin {

    /**
     * 主键ID dataKey
     */
    private static final String ASSIGNMENT_ID_KEY = "pk_25049603";

    /**
     * 编辑 - 跳转任务对应单据
     * @param executeEvent
     */
    @Override
    public void afterExecute(ExecuteEvent executeEvent) {
        IFormView formView = getView();

        // 1.获取任务数据主键
        JSONObject assignmentInfo = (JSONObject) executeEvent.getArgs().get(CommonConstants.VALUE);
        String assignmentId = assignmentInfo.getString(ASSIGNMENT_ID_KEY);

        // 2.检索任务
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(assignmentId, Assignment.class);
        if (Objects.isNull(assignment)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_MANAGE_NON_EXISTENT_WARNING, MsgType.WARNING);
            return;
        }

        // 可修改范围：
        // 网络变更任务：待启动 + 审批待启动
        // 技术管理任务：待启动 + 执行中
        // 分包商网络变更任务：待启动 + 审批待启动
        // 操作计划：待启动（草稿）

        // 3.任务类型校验
        // 技术管理子任务 + 网络变更 - 批次任务 + 分包商网络变更 - 批次任务 不支持从任务中心 / 任务管理处修改
        AssignmentTypeEnum assignmentType = AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        if (Objects.isNull(assignmentType)
                || AssignmentTypeEnum.NETWORK_CHANGE_BATCH.equals(assignmentType)
                || AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB.equals(assignmentType)
                || AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.equals(assignmentType)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_TYPE, MsgType.WARNING);
            return;
        }

        // 4.跳转对应单据编辑页面
        forwardEditPage(assignment, assignmentType);
    }

    /**
     * 跳转对应单据编辑页面
     */
    public void forwardEditPage(Assignment assignment, AssignmentTypeEnum assignmentType) {
        IFormView formView = getView();

        switch (assignmentType) {
            case NETWORK_CHANGE:
                forwardNetworkChangeBill(formView, assignment);
                break;

            case TECHNOLOGY_MANAGEMENT:
                forwardTechnologyManagementBill(formView, assignment);
                break;

            case SUBCONTRACTOR_NETWORK_CHANGE:
                forwardSubcontractNetworkChangeBill(formView, assignment);
                break;

            case PERMISSION_APPLICATION:
                forwardPermissionApplicationBill(formView, assignment);
                break;

            case OPERATION_PLAN_TASK:
                forwardOperationBill(formView, assignment);
                break;

            default:
                break;
        }
    }

    /**
     * 跳转网络变更单
     */
    private void forwardNetworkChangeBill(IFormView formView, Assignment assignment) {
        // 1.任务状态校验
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        //多产品联动保障主任务，满足一定条件允许编辑，只允许编辑多产品联动保障子表单，页面状态是VIEW
        boolean isOver72Hours = this.isOver72HoursEdit(assignment);
        if (!AssignmentStatusEnum.START.equals(assignmentStatus) && !AssignmentStatusEnum.APPROVE_START.equals(assignmentStatus)
                && !isOver72Hours) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_NETWORK_CHANGE, MsgType.WARNING);
            return;
        }

        // 2.任务责任人校验
        String userId = ContextHelper.getEmpNo();
        List<String> responsible = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());
        if (!responsible.contains(userId)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_RESPONSIBLE_NETWORK_CHANGE, MsgType.WARNING);
            return;
        }

        // 3.页面跳转
        Map<String, Object> customParameters = getDefaultCustomParameters(
                MsgUtils.getMessage(AssignmentTitle.NETWORK_CHANGE), OpenTypeEnum.NEW_TAB);
        customParameters.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customParameters.put(CREATE_BY, assignment.getCreateBy());
        customParameters.put(OPERATION_KEY, OPERATION_VIEW);

        FormShowParameter formShowParameter = convertFormShowParameter(
                formView,
                assignment.getEntityId(),
                AssignmentStatusEnum.START.equals(assignmentStatus) ? PAGE_NETWORK_CHANGE_BILL : PAGE_NETWORK_CHANGE_APPROVE_START_DETAIL,
                ChangeOrder.class,
                customParameters);
        formShowParameter.setPageStatus(isOver72Hours ? PageStatusEnum.VIEW : PageStatusEnum.EDIT);
        formView.showForm(formShowParameter);
    }

    private boolean isOver72HoursEdit(Assignment assignment) {
        NetworkChangeAssignment mainAssignment = AssignmentAbility.querySpecificTypeAssignment(
                assignment.getId(), NetworkChangeAssignment.class);
        ChangeOrder mainChangeOrder = ChangeOrderAbility.get(assignment.getBillId(),
                Lists.newArrayList(MULTI_PROD_GUARANTEE, TIME_ZONE, OPERATION_START_TIME));
        if (null == mainAssignment || null == mainChangeOrder) {
            return false;
        }

        return Lists.newArrayList(ChangeOrderTypeEnum.NORMAL.name(),
                ChangeOrderTypeEnum.MULTI_PRODUCT_GUARANTEE_MAIN_TASK.name()).contains(mainAssignment.getType())
                && BoolEnum.Y.equals(mainChangeOrder.getMultiProdGuarantee())
                && AssignmentStatusEnum.APPROVE.getValue().equals(mainAssignment.getAssignmentStatus())
                && null != mainAssignment.getPlanStartTime()
                && System.currentTimeMillis() + HOURS_72 < mainChangeOrder.getTimeZone().fix(mainChangeOrder.getOperationStartTime()).getTime();
    }

    /**
     * 跳转分包商网络变更单
     */
    private void forwardSubcontractNetworkChangeBill(IFormView formView, Assignment assignment) {
        // 1.任务状态校验
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!AssignmentStatusEnum.START.equals(assignmentStatus) && !AssignmentStatusEnum.APPROVE_START.equals(assignmentStatus)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_NETWORK_CHANGE, MsgType.WARNING);
            return;
        }

        // 2.任务责任人校验
        String userId = ContextHelper.getEmpNo();
        List<String> responsible = EmployeeHelper.getEpmUIID(assignment.getResponsibleEmployee());
        if (!responsible.contains(userId)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_RESPONSIBLE_NETWORK_CHANGE, MsgType.WARNING);
            return;
        }

        // 3.页面跳转
        Map<String, Object> customParameters = getDefaultCustomParameters(
                MsgUtils.getMessage(AssignmentTitle.PARTNER_NETWORK_CHANGE), OpenTypeEnum.NEW_TAB);
        customParameters.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customParameters.put(CREATE_BY, assignment.getCreateBy());
        customParameters.put(OPERATION_KEY, OPERATION_VIEW);

        FormShowParameter formShowParameter = convertFormShowParameter(
                formView,
                assignment.getEntityId(),
                PAGE_PARTNER_NETWORK_CHANGE_BILL,
                SubcontractorChangeOrder.class,
                customParameters);
        formView.showForm(formShowParameter);
    }

    /**
     * 跳转技术管理单
     */
    private void forwardTechnologyManagementBill(IFormView formView, Assignment assignment) {
        // 1.任务状态校验
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!AssignmentStatusEnum.START.equals(assignmentStatus) && !AssignmentStatusEnum.EXECUTE.equals(assignmentStatus)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_TECHNOLOGY, MsgType.WARNING);
            return;
        }

        // 2.检索任务对应单据
        TechnologyManagementOrder order = ManageTaskAbility.queryById(assignment.getBillId());

        // 3.创建人 / 验收人校验
        String userId = ContextHelper.getEmpNo();
        String createdBy = assignment.getCreateBy();
        List<Employee> acceptorPerson = JsonUtils.parseArray(order.getAcceptorPerson(), Employee.class);
        List<String> accpetorPersonIdList = acceptorPerson.stream().map(Employee::getEmpUIID).collect(Collectors.toList());
        if (!userId.equals(createdBy) && !accpetorPersonIdList.contains(userId)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_RESPONSIBLE_TECHNOLOGY, MsgType.WARNING);
            return;
        }

        // 4.页面跳转
        Map<String, Object> customParameters = getDefaultCustomParameters(
                MsgUtils.getMessage(AssignmentTitle.TECHNOLOGY), OpenTypeEnum.POPUP);
        customParameters.put(TASK_ID, assignment.getBillId());
        customParameters.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());

        FormShowParameter formShowParameter = convertFormShowParameter(
                formView,
                assignment.getEntityId(),
                PAGE_BILL_TECHNOLOGY_MANAGEMENT_BILL,
                TechnologyManagementOrder.class,
                customParameters);
        formView.showForm(formShowParameter);
    }

    /**
     * 跳转权限任务
     */
    private void forwardPermissionApplicationBill(IFormView formView, Assignment assignment) {
        // 1.任务状态校验
        AssignmentStatusEnum assignmentStatus
                = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!AssignmentStatusEnum.DRAFT_APPLICATION.equals(assignmentStatus)
                && !AssignmentStatusEnum.APPROVAL_RETURN_DRAFT_APPLICATION.equals(assignmentStatus)) {
            formView.showMessage(
                    MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_PERMISSION, MsgType.WARNING);
            return;
        }

        // 2.检索权限申请单
        PermissionApplication application = PermissionApplicationAbility.get(assignment.getEntityId());

        // 3.创建人 / 当前处理人 校验
        String userId = ContextHelper.getEmpNo();
        String createdBy = assignment.getCreateBy();
        List<String> currentProcessor = EmployeeHelper.getEpmUIID(assignment.getCurrentProcessorEmployee());
        if (!userId.equals(createdBy) && !currentProcessor.contains(userId)) {
            formView.showMessage(
                    MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_RESPONSIBLE_PERMISSION, MsgType.WARNING);
            return;
        }

        // 4.页面跳转
        Map<String, Object> customParameters = getDefaultCustomParameters(
                MsgUtils.getMessage(AssignmentTitle.PERMISSION), OpenTypeEnum.POPUP);
        FormShowParameter formShowParameter = convertFormShowParameter(
                formView,
                assignment.getEntityId(),
                UserTypeEnum.EXTERNAL.name().equals(application.getType())
                        ? PAGE_BILL_EXTERNAL_PERMISSION_APPLICATION
                        : PAGE_BILL_INTERNAL_PERMISSION_APPLICATION,
                PermissionApplication.class,
                customParameters);
        formView.showForm(formShowParameter);
    }

    private Map<String, Object> getDefaultCustomParameters(String titleName, OpenTypeEnum openType) {
        Map<String, Object> customParameters = new HashMap<>();

        customParameters.put(HIDDEN_OPERATION, true);
        customParameters.put(FULL_SCREEN, true);
        customParameters.put(OPEN_TYPE, openType.getValue());
        customParameters.put(PAGE_TITLE, titleName);

        return customParameters;
    }

    private FormShowParameter convertFormShowParameter(
            IFormView formView,
            String entityId,
            String pageId,
            Class<? extends BaseEntity> entityClass,
            Map<String, Object> customParameters) {

        FormShowParameter formShowParameter = formView.getFormShowParameter().createSubFormShowParameter();
        formShowParameter.setPkId(entityId);
        formShowParameter.setPageId(pageId);
        formShowParameter.setBizObjectCode(EntityHelper.getEntityId(entityClass));
        formShowParameter.setPageStatus(PageStatusEnum.EDIT);
        formShowParameter.setCustomParameters(customParameters);

        return formShowParameter;
    }

    /**
     * 跳转操作计划单
     */
    private void forwardOperationBill(IFormView formView, Assignment assignment) {
        // 1.任务状态校验
        AssignmentStatusEnum assignmentStatus = AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());
        if (!AssignmentStatusEnum.START.equals(assignmentStatus)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_STATUS_OPERATION_PLAN, MsgType.WARNING);
            return;
        }

        // 2.任务创建人校验
        String userId = ContextHelper.getEmpNo();
        String createdBy = assignment.getCreateBy();
        if (!Objects.equals(userId, createdBy)) {
            formView.showMessage(MessageConsts.ASSIGNMENT_OPERATION_WARNING_EDIT_ERROR_CREATED_BY_OPERATION_PLAN, MsgType.WARNING);
            return;
        }

        // 3.页面跳转
        Map<String, Object> customParameters = getDefaultCustomParameters(
                MsgUtils.getMessage(AssignmentTitle.OPERATION_PLAN), OpenTypeEnum.NEW_TAB);
        customParameters.put(ASSIGNMENT_STATUS, assignment.getAssignmentStatus());
        customParameters.put(CREATE_BY, assignment.getCreateBy());
        customParameters.put(OPERATION_KEY, OPERATION_VIEW);

        FormShowParameter formShowParameter = convertFormShowParameter(
                formView,
                assignment.getEntityId(),
                PAGE_PLAN_OPERATION_BILL,
                PlanOperationOrder.class,
                customParameters);
        formShowParameter.setPageStatus(PageStatusEnum.EDIT);
        formView.showForm(formShowParameter);
    }
}
