package com.zte.iccp.itech.extension.domain.constant.entity.clockin.config;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/09/14
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ProdOperationTypeOdcConfigFieldConsts {
    public static final String CUSTOMER_FLAG = "customer_flag";

    public static final String PROD_TYPE = "prod_type";

    public static final String OPERATION_TYPE = "operation_type";

    public static final String ON_DUTY_COEFFICIENT_INNER = "on_duty_coefficient_inner";

    public static final String ON_DUTY_COEFFICIENT_INTER = "on_duty_coefficient_inter";
}
