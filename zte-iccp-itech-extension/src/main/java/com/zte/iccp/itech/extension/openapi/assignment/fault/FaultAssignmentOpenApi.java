package com.zte.iccp.itech.extension.openapi.assignment.fault;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.FaultManagementOrderAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.CacheUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.domain.constant.*;
import com.zte.iccp.itech.extension.domain.constant.entity.AssignmentFieldConsts;
import com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.BillTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.FaultProcessEnum;
import com.zte.iccp.itech.extension.domain.enums.faultorder.PhaseStatusEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.FaultManagementOrder;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.CscDetailMessageDTO;
import com.zte.iccp.itech.extension.spi.client.CscClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.PdmClient;
import com.zte.iccp.itech.extension.spi.client.WarRoomClient;
import com.zte.iccp.itech.extension.spi.model.csc.dto.CscDetailQuery;
import com.zte.iccp.itech.extension.spi.model.csc.vo.BasicInfoVo;
import com.zte.iccp.itech.extension.spi.model.csc.vo.CscDetailInfoVo;
import com.zte.iccp.itech.extension.spi.model.csc.vo.UserInfoVo;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.pdm.dto.vo.ProductInfoByLevelVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomInfo;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.ddm.domain.flow.dto.SaveBizAndStartFlowDTO;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.platform.domain.api.AbstractOpenApi;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.FlowConstants.*;

@Slf4j
public class FaultAssignmentOpenApi extends AbstractOpenApi {

    /** 合法 CSC 编码前缀 - CI - CSC 一线故障单 */
    private static final String LEGAL_CSC_CODE_PREFIX_CI = "CI";

    /** 合法 CSC 编码前缀 - EI - eSupport 一线故障单 */
    private static final String LEGAL_CSC_CODE_PREFIX_EI = "EI";

    /** 合法 CSC 编码前缀 - PI - SSP 一线故障单 */
    private static final String LEGAL_CSC_CODE_PREFIX_PI = "PI";

    /** 正则表达式 - 数字匹配 */
    private static final String NUMBER_REGEX = "[0-9]+";

    /**
     * 故障管理任务绑定 WarRoom
     * @return ServiceData<Boolean>
     */
    public ServiceData<Void> warRoomBinding() {
        ServiceData<Void> serviceData = new ServiceData<>();

        // 1.检索未绑定 WarRoom 故障管理任务
        List<FaultManagementAssignment> unbindingAssignmentList = AssignmentAbility.queryUnbindingFaultManagementAssignment();
        if (CollectionUtils.isEmpty(unbindingAssignmentList)) {
            log.info("无故障管理任务待绑定 warroom");
            return serviceData;
        }

        // 2.异步绑定 WarRoom
        AsyncExecuteUtils.execute(() -> bindingWarRoom(unbindingAssignmentList));

        return serviceData;
    }

    /**
     * 异步绑定 WarRoom
     * @param unbindingAssignmentList
     */
    public void bindingWarRoom(List<FaultManagementAssignment> unbindingAssignmentList) {
        log.info("异步绑定 warroom 流程开始");

        // 1.检索故障管理单
        List<String> cscCodeList = unbindingAssignmentList.stream().map(FaultManagementAssignment::getAssignmentCode).collect(Collectors.toList());
        List<FaultManagementOrder> orderList = FaultManagementOrderAbility.queryOrderByCscCode(cscCodeList);
        Map<String, FaultManagementOrder> orderMap = orderList.stream()
                .collect(Collectors.toMap(FaultManagementOrder::getTaskCode, Function.identity(), (v1, v2) -> v1));

        List<FaultManagementAssignment> updateAssignmentList = Lists.newArrayList();
        List<FaultManagementOrder> updateOrderList = Lists.newArrayList();
        for (FaultManagementAssignment assignment : unbindingAssignmentList) {
            FaultManagementOrder order = orderMap.get(assignment.getAssignmentCode());
            if (Objects.isNull(order)) {
                continue;
            }

            // 2.检索 CSC 服务请求绑定 WarRoom 详情
            WarRoomInfo warRoomInfo;
            try {
                warRoomInfo = WarRoomClient.queryWarRoomDetailByCscCode(assignment.getAssignmentCode());
            } catch (Exception ex) {
                log.info("单据编号: " + assignment.getAssignmentCode() + ex.getMessage());
                continue;
            }

            if (Objects.isNull(warRoomInfo)) {
                log.info("单据编号: " + assignment.getAssignmentCode() + " 无 warroom 信息");
                continue;
            }

            // 3.设置线程用户
            String userId = transferUserId(warRoomInfo.getCreatedBy());
            RequestContextHolder.setEmpNo(userId);

            // 4.包装更新对象 - 故障管理单
            FaultManagementOrder updateOrder = new FaultManagementOrder();
            updateOrder.setId(order.getId());
            updateOrder.setWarRoomId(warRoomInfo.getWarroomId());
            updateOrderList.add(updateOrder);

            // 5.包装更新对象 - 故障管理任务
            FaultManagementAssignment updateAssignment = new FaultManagementAssignment();
            updateAssignment.setId(assignment.getId());
            updateAssignment.setWarRoom(warRoomInfo.getWarroomId());
            updateAssignment.setWarRoomTitle(warRoomInfo.getTitle());
            updateAssignmentList.add(updateAssignment);
        }

        // 5.绑定 WarRoom
        FaultManagementOrderAbility.batchUpdate(updateOrderList);
        AssignmentAbility.batchUpdate(updateAssignmentList);

        log.info("异步绑定 warroom 流程结束");
    }

    /**
     * 全名称转换工号
     * @param fullUserName
     * @return String
     */
    private String transferUserId(String fullUserName) {
        if (!StringUtils.hasText(fullUserName)) {
            return ClientConstants.SYSTEM_USER;
        }

        Pattern pattern = Pattern.compile(NUMBER_REGEX);
        Matcher matcher = pattern.matcher(fullUserName);

        return matcher.find() ? matcher.group() : ClientConstants.SYSTEM_USER;
    }

    /**
     * 根据 CSC kafka 消息，创建 / 更新故障管理单 + 故障管理任务
     */
    public ServiceData<Boolean> createOrUpdateFaultAssignment(
            @RequestBody CscDetailMessageDTO cscDetailMessage) {

        ServiceData<Boolean> serviceData = new ServiceData<>();
        serviceData.setBo(false);
        // 先暂时关闭 OpenApi 相关功能
        if (true) {
            return serviceData;
        }

        // 1.CSC 编码校验
        // 此处被拦截的消息无意义，后续不再打日志
        String cscCode = cscDetailMessage.getRequestNo();
        if (!cscCode.startsWith(LEGAL_CSC_CODE_PREFIX_CI)
                && !cscCode.startsWith(LEGAL_CSC_CODE_PREFIX_EI)
                && !cscCode.startsWith(LEGAL_CSC_CODE_PREFIX_PI)) {
            return serviceData;
        }

        // 2.检索 CSC 服务请求详情
        CscDetailQuery cscDetailQuery = new CscDetailQuery();
        cscDetailQuery.setRequestNo(cscCode);
        CscDetailInfoVo cscDetail = CscClient.getCscDetail(cscDetailQuery);
        if (Objects.isNull(cscDetail)) {
            log.info(String.format("CSC detail not exist: %s", cscCode));
            return serviceData;
        }

        // 3.校验服务请求关键信息
        if (Objects.isNull(cscDetail.getBasicInfo())
                || Objects.isNull(cscDetail.getSupportOrgInfo())) {
            log.info(String.format(
                    "%s CSC detail missing BasicInfo or SupportOrgInfo: %s",
                    cscCode,
                    JsonUtils.toJsonString(cscDetailMessage)));
            return serviceData;
        }

        // 4.基本数据转换
        // PDM 产品ID + 产品经营团队 + 代表处 + 支持工程师
        FormatData formatData = transferSpecialCscDetailData(cscDetail, cscDetailMessage);

        // 5.检索故障管理任务
        // (1) 故障管理任务存在，则更新故障管理任务信息
        // (2) 故障管理任务不存在，则新增故障管理任务信息
        // 仅新建时校验核心数据有效性，更新时不更新核心数据，无需再进行校验，校验结果均为 true
        boolean result = true;
        List<String> fields = Lists.newArrayList(
                CommonFieldConsts.ID, CommonFieldConsts.CREATE_BY,
                CommonFieldConsts.LAST_MODIFIED_BY, AssignmentFieldConsts.ENTITY_ID,
                AssignmentFieldConsts.CURRENT_PROCESSOR_EMPLOYEE_FIELD,
                AssignmentFieldConsts.CURRENT_PROGRESS);
        Assignment assignment = AssignmentAbility.queryByAssignmentCode(cscCode, fields, Assignment.class);

        if (Objects.isNull(assignment)) {
            result = createFaultAssignmentAndOrder(cscDetail, cscDetailMessage, formatData);
        } else {
            FaultProcessEnum currentProcess = FaultProcessEnum.valueOf(assignment.getCurrentProgress());

            if (FaultProcessEnum.TO_SUBMIT_FAULT_ANALYSIS_REPORT.equals(currentProcess)) {
                updateSpecialProgressAssignmentAndOrder(assignment, cscDetail, formatData);
            } else {
                updateNormalProgressAssignmentAndOrder(assignment, cscDetail);
            }
        }

        serviceData.setBo(result);
        return serviceData;
    }

    /**
     * 创建 故障管理单 / 故障管理任务
     */
    private boolean createFaultAssignmentAndOrder(
            CscDetailInfoVo cscDetail,
            CscDetailMessageDTO cscDetailMessage,
            FormatData formatData) {

        // 1.服务请求详情校验
        boolean checkResult = checkCscDetail(cscDetailMessage, formatData);
        if (!checkResult) {
            return false;
        }

        // 2.设置线程用户
        RequestContextHolder.setEmpNo(cscDetail.getBasicInfo().getCreatorIdCardAb());

        // 3.创建故障管理单
        FaultManagementOrder faultOrder = convertFaultOrder(cscDetail, formatData);
        String faultOrderId = createFaultExecuteFlow(faultOrder, cscDetailMessage);

        // 4.创建故障管理任务
        FaultManagementAssignment faultAssignment
                = convertFaultAssignment(faultOrderId, cscDetail, cscDetailMessage, formatData);
        String assignmentId = AssignmentAbility.insert(faultAssignment);
        faultAssignment.setId(assignmentId);

        // 5.故障管理任务关联人员
        List<String> relevant
                = EmployeeHelper.getEpmUIID(faultAssignment.getCurrentProcessorEmployee());
        relevant.add(ContextHelper.getEmpNo());

        AssignmentAbility.createAssignmentPersonRelevance(faultAssignment.getId(), relevant);

        return true;
    }

    /**
     * 故障任务 / 故障管理单更新 - 待提交故障分析报告
     */
    private void updateSpecialProgressAssignmentAndOrder(
            Assignment faultAssignment,
            CscDetailInfoVo cscDetail,
            FormatData formatData) {

        // 1.数据准备
        String assignmentId = faultAssignment.getId();
        String faultOrderId = faultAssignment.getEntityId();

        String cscStatusId = cscDetail.getBasicInfo().getPhaseStatusId();
        PhaseStatusEnum cscStatusEnum = PhaseStatusEnum.fromValue(cscStatusId);
        List<TextValuePair> cscStatus = Objects.isNull(cscStatusEnum)
                ? Lists.newArrayList()
                : TextValuePairHelper.buildList(cscStatusEnum.getValue(), cscStatusEnum.getZhCn(), cscStatusEnum.getEnUs());

        // 2.设置线程用户
        String userId = cscDetail.getBasicInfo().getCreatorIdCardAb();
        RequestContextHolder.setEmpNo(userId);

        // 3.更新 故障管理单 - CSC 单据状态
        FaultManagementOrder updateOrder = new FaultManagementOrder();
        updateOrder.setId(faultOrderId);
        updateOrder.setCscTaskStatus(cscStatus);
        FaultManagementOrderAbility.batchUpdate(Lists.newArrayList(updateOrder));

        // 4.更新 故障管理任务
        FaultManagementAssignment updateAssignment = new FaultManagementAssignment();
        updateAssignment.setId(assignmentId);
        updateAssignment.setCurrentProcessorEmployee(formatData.getSupportEngineer());
        AssignmentAbility.update(updateAssignment);

        // 5.更新人员关联关系
        List<String> historyRelevant = EmployeeHelper.getEpmUIID(faultAssignment.getCurrentProcessorEmployee());
        historyRelevant.remove(faultAssignment.getCreateBy());
        AssignmentAbility.deleteAssignmentPersonRelevance(assignmentId, historyRelevant);

        List<String> relevant = formatData.getSupportEngineer().stream()
                .map(Employee::getEmpUIID)
                .collect(Collectors.toList());
        AssignmentAbility.createAssignmentPersonRelevance(assignmentId, relevant);

        // 6.重新指派节点处理人
        if (!historyRelevant.contains(relevant.get(0))) {
            FlowHelper.reassignSystemNode(faultOrderId, relevant.get(0));
        }
    }

    /**
     * 故障任务 / 故障管理单更新 - 其它状态
     */
    private void updateNormalProgressAssignmentAndOrder(
            Assignment faultAssignment,
            CscDetailInfoVo cscDetail) {

        // 1.数据准备
        String cscStatusId = cscDetail.getBasicInfo().getPhaseStatusId();
        PhaseStatusEnum cscStatusEnum = PhaseStatusEnum.fromValue(cscStatusId);
        List<TextValuePair> cscStatus = Objects.isNull(cscStatusEnum)
                ? Lists.newArrayList()
                : TextValuePairHelper.buildList(cscStatusEnum.getValue(), cscStatusEnum.getZhCn(), cscStatusEnum.getEnUs());

        // 2.设置线程用户
        String userId = faultAssignment.getLastModifiedBy();
        RequestContextHolder.setEmpNo(userId);

        // 3.更新 故障管理单
        FaultManagementOrder updateOrder = new FaultManagementOrder();
        updateOrder.setId(faultAssignment.getEntityId());
        updateOrder.setCscTaskStatus(cscStatus);
        FaultManagementOrderAbility.batchUpdate(Lists.newArrayList(updateOrder));

        // 4.更新 故障管理任务
        FaultManagementAssignment updateAssignment = new FaultManagementAssignment();
        updateAssignment.setId(faultAssignment.getId());
        AssignmentAbility.update(updateAssignment);
    }

    /**
     * CSC 详情特殊数据转换
     */
    private FormatData transferSpecialCscDetailData(
            CscDetailInfoVo cscDetail,
            CscDetailMessageDTO cscMessage) {

        FormatData formatData = new FormatData();

        // 1.PDM 产品编码 + 产品经营团队
        transferPdmProductInfo(formatData, cscMessage.getPdmNo());

        // 2.区域
        transferEmdmAreaInfo(formatData, cscMessage.getAreaNo());

        // 3.代表处
        transferOrganizationInfo(
                formatData, cscDetail.getSupportOrgInfo().getCurrSupportGroupHrDeptOrg());

        // 4.支持工程师
        transferCscSupportEngineer(
                formatData, cscDetail.getSupportOrgInfo().getCurrSupportUserList());

        return formatData;
    }

    /**
     * 转换 PDM 产品信息
     */
    private void transferPdmProductInfo(FormatData formatData, String pdmProductNo) {
        if (!StringUtils.hasText(pdmProductNo)) {
            return;
        }

        // 1.缓存中有产品型号数据，根据缓存中数据做转换
        List<ProductInfoByLevelVo> productModels = CacheUtils.get(
                CidConstants.PDM_PRODUCT_MODEL, new TypeReference<List<ProductInfoByLevelVo>>() {});
        if (!CollectionUtils.isEmpty(productModels)) {
            setPdmProductByCache(formatData, pdmProductNo, productModels);
            return;
        }

        // 2.缓存中无产品型号数据，从 PDM 获取该数据
        // 异步查询产品型号以设置缓存
        setPdmProductBySystem(formatData, pdmProductNo);
        AsyncExecuteUtils.execute(() -> CacheUtils.set(
                CidConstants.PDM_PRODUCT_MODEL,
                ProductUtils.getPdmProductModels(),
                30 * 24 * 60 * 60));
    }

    /**
     * 查询 缓存 转换 PDM 信息
     */
    private void setPdmProductByCache(
            FormatData formatData,
            String pdmProductNo,
            List<ProductInfoByLevelVo> productModels) {

        // 1.获取产品型号
        Map<String, ProductInfoByLevelVo> productModelMap = productModels.stream()
                .collect(Collectors.toMap(ProductInfoByLevelVo::getItemNo, Function.identity()));
        ProductInfoByLevelVo productModel = productModelMap.get(pdmProductNo);
        if (Objects.isNull(productModel)) {
            return;
        }

        // 2.数据转换
        String fullPath = productModel.getFullPath();
        String[] productNos = fullPath.split(CommonConstants.FORWARD_SLASH);
        Map<String, String> teamLineIdMap = ProductUtils.getPdmProductTeamAndLineIdMap();

        formatData.setProductManageTeam(teamLineIdMap.get(productNos[ProdCateLvlConsts.TEAM- 1]));
        formatData.setProductLine(teamLineIdMap.get(productNos[ProdCateLvlConsts.LINE- 1]));
        formatData.setPdmProductNo(pdmProductNo);
        formatData.setPdmFullProductNo(fullPath);
    }

    /**
     * 查询 PDM 转换产品信息
     */
    private void setPdmProductBySystem(FormatData formatData, String pdmProductNo) {
        // 1.检索 产品小类 + 产品大类 + 产品经营团队 + 产品线 编码
        String productSubclassNo = getProductParentNo(pdmProductNo);
        String productClassNo = getProductParentNo(productSubclassNo);
        String productLineNo = getProductParentNo(productClassNo);
        String productManageTeamNo = getProductParentNo(productLineNo);

        // 2.检索 NIS 获取 产品经营团队 + 产品线 编码
        Map<String, String> teamLineIdMap = ProductUtils.getPdmProductTeamAndLineIdMap();

        // 3.数据转换
        formatData.setProductLine(teamLineIdMap.get(productLineNo));
        formatData.setProductManageTeam(teamLineIdMap.get(productManageTeamNo));
        formatData.setPdmProductNo(pdmProductNo);
        formatData.setPdmFullProductNo(String.join(CommonConstants.FORWARD_SLASH,
                        productManageTeamNo, productLineNo, productClassNo, productSubclassNo, pdmProductNo));
    }

    /**
     * 获取产品父级编码
     * @param productNo
     * @return String
     */
    private String getProductParentNo(String productNo) {
        if (!StringUtils.hasText(productNo)) {
            return CommonConstants.EMPTY_STRING;
        }

        // 1.检索 PDM 系统
        List<ProductInfoByLevelVo> productInfo = PdmClient.queryProductInfo(Lists.newArrayList(productNo));
        if (CollectionUtils.isEmpty(productInfo)) {
            return CommonConstants.EMPTY_STRING;
        }

        // 2.获取父级编码
        return productInfo.get(CommonConstants.INTEGER_ZERO).getParentNo();
    }

    /**
     * 转换 组织 信息
     */
    private void transferOrganizationInfo(FormatData formatData, String organizationId) {
        if (!StringUtils.hasText(organizationId)) {
            return;
        }

        // 1.检索组织信息
        Map<String, BasicOrganizationInfo> organizationMap
                = HrClient.queryOrganizationInfo(Lists.newArrayList(organizationId));
        BasicOrganizationInfo organizationInfo = organizationMap.get(organizationId);
        if (Objects.isNull(organizationInfo)) {
            return;
        }

        // 2.代表处校验
        String fullPath = organizationInfo.getOrgIDPath();

        // (1) 非代表处组织, 不创建故障管理单及对应任务
        String[] wholePathArray = fullPath.split(CommonConstants.SPECIAL_HYPHEN);
        if (wholePathArray.length < 4) {
            return;
        }

        // (2) 代表处需属于国内政企 / 工程服务经营部
        String[] innerOrgPath = BusinessConsts.OFFICE_ORG_CODE_PATH;
        String[] marketingArray = innerOrgPath[0].split(CommonConstants.FORWARD_SLASH);
        String[] representativeOfficeArray = innerOrgPath[1].split(CommonConstants.FORWARD_SLASH);
        if (!marketingArray[1].equals(wholePathArray[1])
                && !representativeOfficeArray[1].equals(wholePathArray[1])) {
            return;
        }

        // 3.数据转换
        formatData.setRepresentativeOffice(wholePathArray[3]);
    }

    /**
     * 转换 EMDM 区域信息
     * EMDM 区域编码固定为 4 位，且国家 4 位在开头
     */
    private void transferEmdmAreaInfo(FormatData formatData, String areaCode) {
        if (!StringUtils.hasText(areaCode) || areaCode.length() < CommonConstants.INTEGER_FOUR) {
            return;
        }

        String countryAreaCode = areaCode.substring(CommonConstants.INTEGER_ZERO, CommonConstants.INTEGER_FOUR);
        formatData.setArea(countryAreaCode);
    }

    /**
     * 转换 CSC 支持工程师
     */
    private void transferCscSupportEngineer(FormatData formatData, List<UserInfoVo> supportUsers) {
        if (CollectionUtils.isEmpty(supportUsers)) {
            return;
        }

        List<String> supportUserIds = supportUsers.stream()
                .map(UserInfoVo::getIdCardAb)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());
        formatData.setSupportEngineer(HrClient.queryEmployeeInfo(supportUserIds));
    }

    /**
     * CSC 服务请求信息校验
     */
    private boolean checkCscDetail(CscDetailMessageDTO cscDetailMessage, FormatData formatData) {
        String cscCode = cscDetailMessage.getRequestNo();

        // 1.故障等级 - 关键一级 / 关键二级 / 关键三级
        FaultLevelEnum faultLevel = FaultLevelEnum.fromZhCn(cscDetailMessage.getSeverityCn());
        if (!FaultLevelEnum.CRITICAL_FIRST.equals(faultLevel)
                && !FaultLevelEnum.CRITICAL_SECOND.equals(faultLevel)
                && !FaultLevelEnum.CRITICAL_THIRD.equals(faultLevel)) {
            log.info(String.format("Invalid fault level: %s", cscCode));
            return false;
        }

        // 2.代表处属于国内营销 / 工程服务经营部范围
        if (!StringUtils.hasText(formatData.getRepresentativeOffice())) {
            log.info(String.format("Invalid organization: %s", cscCode));
            return false;
        }

        // 3.PDM 产品属于 NIS 产品经营团队范围
        if (!StringUtils.hasText(formatData.getProductManageTeam())) {
            log.info(String.format("Invalid product team: %s", cscCode));
            return false;
        }

        return true;
    }

    /**
     * 包装 故障管理单 实体对象
     * @param cscDetail
     * @param formatData
     * @return FaultManagementOrder
     */
    private FaultManagementOrder convertFaultOrder(CscDetailInfoVo cscDetail, FormatData formatData) {
        FaultManagementOrder faultOrder = new FaultManagementOrder();

        // 1.数据准备
        BasicInfoVo basicInfo = cscDetail.getBasicInfo();

        // 2.包装故障管理单 - 基础属性
        // 任务单号 + 任务主题 + 客户ID + PDM 产品ID
        faultOrder.setTaskCode(basicInfo.getRequestNo());
        faultOrder.setTaskSubject(basicInfo.getSubject());
        faultOrder.setCustomerId(basicInfo.getClientNo());

        // 3.包装故障管理单 - 特殊属性
        // csc 任务状态
        PhaseStatusEnum cscStatusEnum = PhaseStatusEnum.fromValue(basicInfo.getPhaseStatusId());
        List<TextValuePair> cscStatus = Objects.isNull(cscStatusEnum)
                ? Lists.newArrayList()
                : TextValuePairHelper.buildList(cscStatusEnum.getValue(), cscStatusEnum.getZhCn(), cscStatusEnum.getEnUs());
        faultOrder.setCscTaskStatus(cscStatus);

        // 代表处
        List<TextValuePair> organization =
                TextValuePairHelper.buildList(formatData.getRepresentativeOffice(), CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING);
        faultOrder.setOrganization(organization);

        // pdm 产品编码(全路径) + 产品经营团队 idPath + 产品线 idPath
        List<TextValuePair> productManageTeam =
                TextValuePairHelper.buildList(formatData.getProductManageTeam(), CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING);
        List<TextValuePair> productLine =
                TextValuePairHelper.buildList(formatData.getProductLine(), CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING);
        faultOrder.setPdmProductId(formatData.getPdmProductNo());
        faultOrder.setProductTeam(productManageTeam);
        faultOrder.setProductLine(productLine);

        // 区域
        faultOrder.setAreaCode(formatData.getArea());

        return faultOrder;
    }

    /**
     * 包装 故障管理任务 实体对象
     */
    private FaultManagementAssignment convertFaultAssignment(
            String faultOrderId,
            CscDetailInfoVo cscDetail,
            CscDetailMessageDTO cscDetailMessage,
            FormatData formatData) {

        FaultManagementAssignment faultAssignment = new FaultManagementAssignment();

        // 1.数据准备
        BasicInfoVo basicInfo = cscDetail.getBasicInfo();

        // 2.包装故障管理任务 - 故障管理单属性
        faultAssignment.setBillId(faultOrderId);
        faultAssignment.setEntityId(faultOrderId);

        // 3.包装故障管理任务 - 服务请求属性
        // 任务名称 + 任务编码 + 客户编码
        faultAssignment.setAssignmentName(basicInfo.getSubject());
        faultAssignment.setAssignmentCode(basicInfo.getRequestNo());
        faultAssignment.setCustomer(basicInfo.getClientNo());

        // 故障程度
        FaultLevelEnum faultLevel = Objects.requireNonNull(FaultLevelEnum.fromZhCn(cscDetailMessage.getSeverityCn()));
        faultAssignment.setFaultLevel(faultLevel.getPropValue());

        // 4.包装故障管理任务 - 转换数据属性
        // 区域 + 产品经营团队 + pdm 产品编号 + 支持工程师 + 代表处
        faultAssignment.setRegion(formatData.getArea());
        faultAssignment.setProductManagementTeam(
                TextValuePairHelper.buildList(formatData.getProductManageTeam(), CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING));
        faultAssignment.setPdmProduct(formatData.getPdmFullProductNo());
        faultAssignment.setCurrentProcessorEmployee(formatData.getSupportEngineer());
        faultAssignment.setRepresentativeOffice(
                TextValuePairHelper.buildList(formatData.getRepresentativeOffice(), CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING));

        // 5.包装故障管理任务 - 默认属性
        faultAssignment.setAssignmentType(AssignmentTypeEnum.FAULT_MANAGEMENT.getPropValue());
        faultAssignment.setBillType(BillTypeEnum.FAULT_MANAGEMENT.getPropValue());
        faultAssignment.setAssignmentStatus(AssignmentStatusEnum.EXECUTE.getValue());
        faultAssignment.setCurrentProgress(FaultProcessEnum.TO_SUBMIT_FAULT_ANALYSIS_REPORT.name());
        faultAssignment.setApprovalTaskFlag(BoolEnum.N);

        return faultAssignment;
    }

    /**
     * 创建 故障管理任务 流程
     */
    private String createFaultExecuteFlow(
            FaultManagementOrder faultOrder,
            CscDetailMessageDTO cscDetailMessage) {

        // 1.流程对象 - 基础参数
        SaveBizAndStartFlowDTO flow = new SaveBizAndStartFlowDTO();
        flow.setTenantId(ContextHelper.getTenantId());
        flow.setAppId(ContextHelper.getAppId());
        flow.setPageId(PageConstants.PAGE_BILL_FAULT_MANAGEMENT_BILL);
        flow.setBizObjCode(EntityHelper.getEntityId(FaultManagementOrder.class));
        flow.setTenantId(ClientConstants.DEFAULT_TENANT_ID);

        // 2.流程对象 - 自定义参数
        Map<String, Object> faultOrderInfo = JsonUtils.parseObject(faultOrder, Map.class);
        Map<String, Object> paramInfo = new HashMap<>();
        paramInfo.put(VARIABLE_CSC_CODE, faultOrder.getTaskCode());
        paramInfo.put(AREA_CODE, cscDetailMessage.getAreaNo());
        List<TextValuePair> organizations = faultOrder.getOrganization();
        if (!CollectionUtils.isEmpty(organizations)) {
            paramInfo.put(ORGANIZATION, organizations.get(CommonConstants.INTEGER_ZERO).getValue());
        }
        paramInfo.putAll(faultOrderInfo);
        flow.setParams(paramInfo);

        // 3.创建流程
        JSONObject idInfo = FlowServiceHelper.saveBizAndStartFlow(flow);
        return idInfo.getString(FlowConstants.BASIC_FIELD_BUSINESS_ID);
    }

    @ApiModel("字段转换数据")
    @Setter
    @Getter
    private static class FormatData {

        @ApiModelProperty("PDM产品编号")
        private String pdmProductNo;

        @ApiModelProperty("PDM产品编号全路径")
        private String pdmFullProductNo;

        @ApiModelProperty("产品经营团队")
        private String productManageTeam;

        @ApiModelProperty("产品线")
        private String productLine;

        @ApiModelProperty("代表处")
        private String representativeOffice;

        @ApiModelProperty("区域")
        private String area;

        @ApiModelProperty("支持工程师")
        private List<Employee> supportEngineer;
    }
}
