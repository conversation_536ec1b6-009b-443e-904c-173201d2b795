package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.HIGH_RISK_INSTRUCTION;
import static com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts.HIGH_RISK_INSTRUCTION_DESCRIPTION;

/**
 * <AUTHOR>
 * @create 2024/7/4 下午3:38
 */
@ApiModel("高危指令")
@Setter
@Getter
@BaseSubEntity.Info(value = "high_risk_instruction_table", parent = ChangeOrder.class)
public class HighInstruction extends BaseSubEntity {

    @ApiModelProperty("高危指令表")
    @JsonProperty(value = HIGH_RISK_INSTRUCTION)
    private String instructions;

    @ApiModelProperty("描述")
    @JsonProperty(value = HIGH_RISK_INSTRUCTION_DESCRIPTION)
    private String instructionsDesc;
}
