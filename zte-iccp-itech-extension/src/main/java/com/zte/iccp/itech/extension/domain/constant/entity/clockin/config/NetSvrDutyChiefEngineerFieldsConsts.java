package com.zte.iccp.itech.extension.domain.constant.entity.clockin.config;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NetSvrDutyChiefEngineerFieldsConsts {
    public static final String PROD_TYPE = "prod_type";

    public static final String CUSTOMER_ID = "customer_id";

    public static final String CUSTOMER_FLAG = "customer_flag";
}
