package com.zte.iccp.itech.extension.domain.model.subentity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.ProdTreeNodeIdDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonEmployeeDeserializer;
import com.zte.iccp.itech.extension.common.json.deserializer.SingletonTextValuePairsDeserializer;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.model.grantfile.IApprovalRecord;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.subentity.OpAssocProdNetServIntApprovalFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/26
 */
@Getter
@Setter
@BaseSubEntity.Info(value = "operation_assoc_prod_net_integration", parent = ChangeOrder.class)
public class OpAssocProdNetServIntApproval extends BaseOpAssocProdSubEntity implements IApprovalRecord {
    @JsonProperty(value = PRODUCT_TYPE)
    @JsonDeserialize(using = ProdTreeNodeIdDeserializer.class)
    private String productType;

    @JsonProperty(value = APPROVER)
    @JsonDeserialize(using = SingletonEmployeeDeserializer.class)
    private SingleEmployee approver;

    @JsonProperty(value = APPROVED_DATE)
    private Date approvedDate;

    @JsonProperty(value = RESULT)
    @JsonDeserialize(using = SingletonTextValuePairsDeserializer.class)
    private ApproveResultEnum result;

    @JsonProperty(value = OPINION)
    private String opinion;

    @JsonProperty(value = EMAIL_CC)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> emailCc;

    @JsonProperty(value = INTEGRATED_ASSOCIATED_PRODUCT_NET_INTEGRATION_ZH)
    private String assocProdNameZh;

    @JsonProperty(value = INTEGRATED_ASSOCIATED_PRODUCT_NET_INTEGRATION_EN)
    private String assocProdNameEn;
}
