package com.zte.iccp.itech.extension.common.helper;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.ClientConstants;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ActiveApproverNode;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.google.common.collect.Lists;
import com.zte.iss.approval.sdk.bean.*;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.iss.approval.sdk.client.ApprovalTaskClient;
import com.zte.iss.approval.sdk.parameter.BasePara;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.ddm.common.api.dto.UacParamDTO;
import com.zte.paas.lcap.ddm.domain.flow.dto.*;
import com.zte.paas.lcap.ddm.domain.repository.IParameterRepository;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import com.zte.paas.lcap.flow.dto.FlowInfo;
import com.zte.paas.lcap.flow.dto.FlowInstDTO;
import com.zte.paas.lcap.flow.gateway.IFlowInstGateway;
import com.zte.paas.lcap.flow.gateway.impl.FlowInstGatewayImpl;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.APPROVERS;
import static com.zte.iccp.itech.extension.domain.constant.ApprovalConstants.SYSTEM_USER;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum.*;
import static com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin.BasePluginVariants.FLOW_VERSION;
import static com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin.BasePluginVariants.SUBMIT_TIME;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/24
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class FlowHelper {

    public static final String DEFAULT_VARIANT_VALUE = "-";

    public static final String ACTIVE_TASK_STATUS = "ACTIVE";

    /** 流程关闭状态（撤回后流程就是此状态，等待重提交） */
    public static final String FLOW_EXTERNALLY_TERMINATED_STATUS = "EXTERNALLY_TERMINATED";

    private static final Map<String, Class<? extends Enum<?>>> FLOWS = MapUtils.newHashMap(
            CHANGE_ORDER_COMP_FLOW.name(), ApproveNodeEnum.class,
            SUBCONTRACTOR_OC_FLOW.name(), PartnerApproveNodeEnum.class,
            BATCH_TASK_FLOW.name(), ApproveNodeEnum.class);

    /**
     * 网络变更单（内部、分包商）、技术管理任务、故障管理任务流程编码
     */
    public static final List<String> FLOW_CODES = Lists.newArrayList(CHANGE_ORDER_COMP_FLOW.name(),
            SUBCONTRACTOR_OC_FLOW.name(),
            ApproveFlowCodeEnum.TECH_MANAGE_FLOW.name(),
            ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name());


    public static void changeVariables(
            String bizId,
            Map<BaseFlowPlugin.FlowVariant, Object> variables) {
        FlowInfo flowInfo = FlowHelper.getFlowInfo(bizId);
        if (flowInfo == null) {
            return;
        }

        //noinspection unchecked,rawtypes
        HashMap<String, ResetParameterParamDTO> params = (HashMap) variables.entrySet().stream()
                .collect(Collectors.toMap(
                        e -> e.getKey().getKey(),
                        e -> new ResetParameterParamDTO() {{
                            setParameterType(ParamType.String);
                            setParameterValue(e.getValue());
                        }}));

        ParamChangeFlowInstIdDTO dto = new ParamChangeFlowInstIdDTO() {{
            setAppCode(ContextHelper.getUacAppId());
            setAppId(ContextHelper.getUacAppId());
            setHandler(ContextHelper.getEmpNo());
            setTenantId(ContextHelper.getTenantId());
        }};
        dto.setFlowInstId(flowInfo.getFlowInstanceId());
        dto.setParameterMap(params);

        log.info(ApprovalFlowClient.changeFlowParams(dto));
    }

    public static FlowInfo getFlowInfo(String businessId) {
        if (StringUtils.isBlank(businessId)) {
            return null;
        }

        List<FlowInfo> flowInfos = FlowServiceHelper.queryFlowInfoByBizId(new FlowInfoQuery() {{
            setTenantId(ContextHelper.getTenantId());
            setBusinessId(businessId);
        }});

        return CollectionUtils.isEmpty(flowInfos) ? null : flowInfos.get(0);
    }

    public static List<ApproveRecord> getApprovedRecords(String businessId) {
        List<ApproveRecord> allRecords = getAllApprovedRecords(businessId);

        FlowInfo flowInfo = getFlowInfo(businessId);
        if (flowInfo == null) {
            return recordsCommonDeal(allRecords);
        }

        Class<? extends Enum<?>> nodeEnumClass = FLOWS.get(flowInfo.getFlowCode());
        if (nodeEnumClass == null) {
            return recordsCommonDeal(allRecords);
        }

        return recordsSpecialDeal(nodeEnumClass, allRecords);
    }

    public static List<ApproveRecord> getAllSpecialDealApprovedRecords(String businessId) {
        List<ApproveRecord> approveRecords = getAllApprovedRecords(businessId);
        // 用栈存
        Stack<ApproveRecord> approveRecordStack = getAllSpecialDealApprovedRecords(approveRecords);
        return filterApprovedRecords(new ArrayList<>(approveRecordStack));
    }

    @NotNull
    public static Stack<ApproveRecord> getAllSpecialDealApprovedRecords(List<ApproveRecord> approveRecords) {
        Stack<String> extendCodeStack = new Stack<>();
        Stack<ApproveRecord> approveRecordStack = new Stack<>();
        for (int i = approveRecords.size()-1; i >= 0; i--) {
            String extendCode = approveRecords.get(i).getExtendedCode();
            if (extendCodeStack.contains(extendCode) && i-1 >= 0) {
                while(!extendCodeStack.isEmpty()) {
                    String extendCodeTmp = extendCodeStack.peek();
                    if (extendCodeTmp.equals(extendCode)) {
                        break;
                    } else {
                        extendCodeStack.pop();
                        approveRecordStack.pop();
                    }
                }
            }
            extendCodeStack.push(approveRecords.get(i).getExtendedCode());
            approveRecordStack.push(approveRecords.get(i));
        }
        return approveRecordStack;
    }

    public static List<ApproveRecord> filterApprovedRecords(List<ApproveRecord> approveRecords) {
        return approveRecords.stream()
                .filter(record -> COMPLETED_TASK_STATUS.equals(record.getTaskStatus())
                        || ACTIVE_TASK_STATUS.equals(record.getTaskStatus()))
                .collect(Collectors.groupingBy(ApproveRecord::getExtendedCode))
                .values()
                .stream()
                .flatMap(group -> {
                    // 找出该分组中所有 approvalDate == null 的记录
                    List<ApproveRecord> nullDateRecords = group.stream()
                            .filter(record -> record.getApprovalDate() == null)
                            .collect(Collectors.toList());

                    if (!nullDateRecords.isEmpty()) {
                        // 返回所有 approvalDate == null 的记录
                        return nullDateRecords.stream();
                    } else {
                        // 否则，返回 approvalDate 最大的记录
                        return group.stream()
                                .max(Comparator.comparing(ApproveRecord::getApprovalDate))
                                .map(Stream::of)
                                .orElseGet(Stream::empty);
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static List<ApproveRecord> getAllApprovedRecords(String businessId) {
        List<ApproveRecord> allRecords = FlowServiceHelper
                .getFlowApproveRecords(new ApproveRecordsDTO() {{
                    setTenantId(ContextHelper.getTenantId());
                    setAppId(ContextHelper.getAppId());
                    setBizInstanceId(businessId);
                }});
        if (CollectionUtils.isEmpty(allRecords)) {
            return Collections.emptyList();
        }

        Map<String, Object> flowVariables = getFlowVariables(businessId);
        String strSubmitTime = flowVariables.getOrDefault(SUBMIT_TIME.getKey(), "0").toString();
        long submitTime = Long.parseLong(DEFAULT_VARIANT_VALUE.equals(strSubmitTime) ? "0" : strSubmitTime);
        allRecords = allRecords.stream()
                .filter(rec -> rec.getCreatedDate().getTime() >= submitTime &&
                        !StringUtils.equals(REASSIGN_TASK_STATUS, rec.getTaskStatus()))
                .collect(Collectors.toList());
        return allRecords;
    }

    /**
     * 获取提交后的sumtime的审核记录 不排序不去重，兼容老场景没有submitTime查询所有记录
     * @param businessId
     */
    public static List<ApproveRecord> getAfterSubmitApprovedRecords(String businessId) {
        List<ApproveRecord> allRecords = FlowServiceHelper
                .getFlowApproveRecords(new ApproveRecordsDTO() {{
                    setTenantId(ContextHelper.getTenantId());
                    setAppId(ContextHelper.getAppId());
                    setBizInstanceId(businessId);
                }});
        if (CollectionUtils.isEmpty(allRecords)) {
            return Collections.emptyList();
        }

        // 优先过滤重新指派的数据
        allRecords = allRecords.stream()
                .filter(record -> !record.getTaskStatus().equals(REASSIGN_TASK_STATUS))
                .collect(Collectors.toList());

        List<ApproveRecord> uniqueRecords = beforeFilterRecords(allRecords);

        Map<String, Object> flowVariables = getFlowVariables(businessId);
        String strSubmitTime = flowVariables.getOrDefault(SUBMIT_TIME.getKey(), "0").toString();
        long submitTime = Long.parseLong(DEFAULT_VARIANT_VALUE.equals(strSubmitTime) ? "0" : strSubmitTime);
        if (submitTime == 0) {
            return uniqueRecords;
        }

        return uniqueRecords.stream()
                // 待发通告或取消操作审核或提交时间大于submitTime  并且非重新指派的数据
                .filter(rec -> ApproveNodeEnum.PENDING_NOTIFICATION.name().equals(rec.getExtendedCode())
                        || rec.getCreatedDate().getTime() >= submitTime)
                .sorted(Comparator.comparing(ApproveRecord::getCreatedDate))
                .collect(Collectors.toList());
    }

    /**
     * 前置过滤
     *
     * @param allRecords allRecords
     * @return List<ApproveRecord>
     */
    private static List<ApproveRecord> beforeFilterRecords(List<ApproveRecord> allRecords) {
        // 1.过滤出会签节点数据
        List<ApproveRecord> intlApprovalRecordList = allRecords.stream()
                .filter(record -> ApproveNodeEnum.BATCH_INTL_ADMIN_APPROVAL.name()
                        .equals(record.getExtendedCode()))
                .collect(Collectors.toList());

        // 2.过滤出非会签节点数据，根据审批记录中的extendedCode进行分组，再根据时期取最近的数据
        List<ApproveRecord> uniqueRecords = new ArrayList<>(allRecords.stream()
                // 非会签节点
                .filter(rec -> !ApproveNodeEnum.BATCH_INTL_ADMIN_APPROVAL.name()
                        .equals(rec.getExtendedCode()))
                .collect(Collectors.toMap(
                        // 根据唯一标识获取两条相同的数据，再根据时期取最近的数据
                        ApproveRecord::getExtendedCode,
                        record -> record,
                        BinaryOperator.maxBy(Comparator.comparing(ApproveRecord::getCreatedDate))
                ))
                .values());

        // 3.数据聚合，会签节点数据+过滤后的非会签节点的数据
        if (!CollectionUtils.isEmpty(intlApprovalRecordList)) {
            uniqueRecords.addAll(intlApprovalRecordList);
        }
        return uniqueRecords;
    }

    public static String getCurrentNodeCode(String businessId) {
        return getApprovedRecords(businessId).stream()
                .filter(rec -> ACTIVE_TASK_STATUS.equals(rec.getTaskStatus()))
                .map(ApproveRecord::getExtendedCode)
                .findFirst()
                .orElse(null);
    }

    public static String getCurrentNodeId(String businessId) {
        return getApprovedRecords(businessId).stream()
                .filter(rec -> ACTIVE_TASK_STATUS.equals(rec.getTaskStatus()))
                .map(ApproveRecord::getNodeId)
                .findFirst()
                .orElse(null);
    }

    /**
     * 系统审批流程推到下一个节点
     */
    public static void pushSystemNode(String bizId, String nodeKey) {
        FlowInfoQuery flowInfoQuery = new FlowInfoQuery();
        flowInfoQuery.setBusinessId(bizId);
        flowInfoQuery.setTenantId(ContextHelper.getTenantId());
        List<FlowInfo> flowInfos = FlowServiceHelper.queryFlowInfoByBizId(flowInfoQuery);
        if (CollectionUtils.isEmpty(flowInfos) || flowInfos.get(0) == null) {
            return;
        }

        CloseActiveNodesDTO closeActiveNodesDTO = new CloseActiveNodesDTO();
        closeActiveNodesDTO.setAppId(ContextHelper.getUacAppId());
        closeActiveNodesDTO.setAppCode(ContextHelper.getUacAppId());
        closeActiveNodesDTO.setHandler(I_VERSION_SYSTEM_NAME);
        closeActiveNodesDTO.setFlowInstanceId(flowInfos.get(0).getFlowInstanceId());
        closeActiveNodesDTO.setActiveNodeKeys(Lists.newArrayList(nodeKey));
        ApprovalFlowClient.closeActiveNodes(closeActiveNodesDTO);
    }

    /**
     * 审批流程节点提交
     * @param bizId
     * @param parameterMap
     */
    public static void submitSystemNode(String bizId, HashMap<String, ResetParameter> parameterMap) {
        String userId = ContextHelper.getEmpNo();

        // 1.检索实体对应审批流程
        List<ApproveTask> taskList = getSystemNodeTask(bizId);
        if (CollectionUtils.isEmpty(taskList)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.APPROVAL_FLOW_NOT_FOUND));
        }
        Map<String, String> taskApproverMap = taskList.stream()
                .collect(Collectors.toMap(ApproveTask::getApprover, ApproveTask::getTaskId, (v1, v2) -> v1));

        // 2.包装提交审批信息
        OpinionDTO opinionDTO = new OpinionDTO();
        opinionDTO.setParameterMap(parameterMap);

        opinionDTO.setHandler(userId);
        opinionDTO.setAppId(ContextHelper.getUacAppId());
        opinionDTO.setAppCode(ContextHelper.getUacAppId());
        opinionDTO.setSecretKey(ContextHelper.getAccessSecret());
        opinionDTO.setResult(CommonConstants.Y);

        String taskId = taskApproverMap.get(userId);
        if (StringUtils.isBlank(taskId)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.MobileApprove.REVIEWER_OR_APPROVE_TEAM_NOT_EXISTS));
        }
        opinionDTO.setTaskId(taskApproverMap.get(userId));

        // 3.提交审批信息
        ApprovalTaskClient.submit(opinionDTO);
    }

    /**
     * 只改变流程变量
     * @params params 流程变量注意Object别直接传入 枚举和下拉框对象，只需要value值
     */
    public static void changeFlowParams(String bizId, Map<String, Object> params, ApproveFlowCodeEnum flowCodeEnum) {
        ParamChangeBussinssIdDTO dto = new ParamChangeBussinssIdDTO();
        dto.setBusinessId(bizId);
        dto.setOpinion(ContextHelper.getEmpNo());
        dto.setAppId(ContextHelper.getAppId());
        dto.setHandler(ContextHelper.getEmpNo());
        dto.setTenantId(ContextHelper.getTenantId());
        dto.setFlowCode(flowCodeEnum.name());
        HashMap<String, ResetParameterParamDTO> parameterMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            ResetParameterParamDTO param = new ResetParameterParamDTO();
            param.setParameterValue(entry.getValue());
            if (entry.getValue() instanceof String) {
                param.setParameterType(ParamType.String);
            } else if (entry.getValue() instanceof Integer) {
                param.setParameterType(ParamType.Integer);
            } else if (entry.getValue() instanceof Double) {
                param.setParameterType(ParamType.Double);
            } else {
                param.setParameterType(ParamType.Object);
            }
            parameterMap.put(entry.getKey(), param);
        }
        dto.setParameterMap(parameterMap);
        log.info(ApprovalFlowClient.changeFlowParams(dto));
    }

    /* 废止流程 */
    public static void revokeFlow(String bizId, String flowCode) {
        RevokeBusinessIdDTO businessIdDTO = new RevokeBusinessIdDTO();
        businessIdDTO.setBusinessId(bizId);
        businessIdDTO.setHandler(ContextHelper.getEmpNo());
        businessIdDTO.setTenantId(ClientConstants.DEFAULT_TENANT_ID);
        businessIdDTO.setFlowCode(flowCode);
        businessIdDTO.setAppCode(ContextHelper.getUacAppId());
        businessIdDTO.setAppId(ContextHelper.getAppId());
        ApprovalFlowClient.revoke(businessIdDTO);
    }


    public static void rollbackToStarter(String businessId, String flowCode, String opinion) {
        RollbackBusinessDTO rollbackBusinessDTO = new RollbackBusinessDTO();
        rollbackBusinessDTO.setBusinessId(businessId);
        rollbackBusinessDTO.setFlowCode(flowCode);
        rollbackBusinessDTO.setOpinion(opinion);
        rollbackBusinessDTO.setHandler(ContextHelper.getEmpNo());
        rollbackBusinessDTO.setAppCode(ContextHelper.getUacAppId());
        rollbackBusinessDTO.setAppId(ContextHelper.getAppId());
        rollbackBusinessDTO.setTenantId(ContextHelper.getTenantId());
        ApprovalFlowClient.rollbackToStarter(rollbackBusinessDTO);
    }

    public static Map<String, Object> getFlowVariables(String businessId) {
        FlowInfo flowInfo = getFlowInfo(businessId);
        if (flowInfo == null || StringUtils.isBlank(flowInfo.getFlowInstanceId())) {
            return Maps.newHashMap();
        }

        FlowInstanceVo flowInstance = ApprovalFlowClient.getFlowInstance(
                new RequestData<String>(flowInfo.getFlowInstanceId()) {{
                    setAppId(ContextHelper.getUacAppId());
                    setAppCode(ContextHelper.getUacAppId());
                    setHandler(ContextHelper.getEmpNo());
                }});
        return flowInstance.getBusinessParam();
    }

    /**
     * 审批记录通用处理方法，保留相同节点中创建时间更晚的记录，按创建时间排序
     */
    private static List<ApproveRecord> recordsCommonDeal(List<ApproveRecord> records) {
        Map<String, ApproveRecord> map = records.stream()
                .filter(rec -> StringUtils.isNotBlank(rec.getExtendedCode())
                        || ACTIVE_TASK_STATUS.equals(rec.getTaskStatus()))
                .collect(Collectors.toMap(
                        rec -> StringUtils.isBlank(rec.getExtendedCode())
                                ? rec.getNodeId() : rec.getExtendedCode(),
                        Function.identity(),
                        FlowHelper::recordCommonMerge));
        return map.values().stream()
                .sorted(Comparator.comparing(ApproveRecord::getCreatedDate))
                .collect(Collectors.toList());
    }

    private static ApproveRecord recordCommonMerge(ApproveRecord a, ApproveRecord b) {
        if (ACTIVE_TASK_STATUS.equals(a.getTaskStatus())
                && ACTIVE_TASK_STATUS.equals(b.getTaskStatus())) {
            return a.getApprover().equals(ContextHelper.getEmpNo())
                    ? a : b;
        }

        return a.getCreatedDate().after(b.getCreatedDate())
                ? a : b;
    }

    private static @NotNull List<ApproveRecord> recordsSpecialDeal(
            Class<? extends Enum<?>> nodeEnumClass,
            List<ApproveRecord> allRecords) {
        int prevOridnal = Integer.MAX_VALUE;
        List<ApproveRecord> result = Lists.newArrayList();
        Map<String, Integer> nodeName2Ordinal = Arrays
                .stream(nodeEnumClass.getEnumConstants())
                .collect(Collectors.toMap(Enum::name, Enum::ordinal));
        allRecords = allRecords.stream()
                .sorted(FlowHelper::recordSpecialCompare)
                .collect(Collectors.toList());
        for (ApproveRecord rec : allRecords) {
            int ordinal = nodeName2Ordinal.getOrDefault(
                    rec.getExtendedCode(),
                    ACTIVE_TASK_STATUS.equals(rec.getTaskStatus())
                            ? Integer.MAX_VALUE - 1 : Integer.MAX_VALUE);
            if (ordinal >= prevOridnal) {
                continue;
            }

            result.add(0, rec);
            prevOridnal = ordinal;
        }
        return result;
    }

    private static int recordSpecialCompare(ApproveRecord a, ApproveRecord b) {
        // 状态检查只做一次
        final boolean aActive = ACTIVE_TASK_STATUS.equals(a.getTaskStatus());
        final boolean bActive = ACTIVE_TASK_STATUS.equals(b.getTaskStatus());

        // 1. 优先级规则：ACTIVE状态优先
        if (aActive != bActive) {
            return aActive ? -1 : 1;
        }

        // 2. 相同状态下：当前用户处理的记录优先
        if (aActive) { // 仅当两者都是ACTIVE时才需要检查
            final boolean aIsMine = a.getApprover().equals(ContextHelper.getEmpNo());
            final boolean bIsMine = b.getApprover().equals(ContextHelper.getEmpNo());

            if (aIsMine != bIsMine) {
                return aIsMine ? -1 : 1;
            }
        }

        // 3. 默认规则：按创建时间降序
        return b.getCreatedDate().compareTo(a.getCreatedDate());
    }

    /**
     * 查询我已处理的任务(全部)
     * 查询审批中心存在两个问题：
     * (1)第一个不是全部任务都生成了统一待办任务；
     * (2)第二个是需要拿id去任务表捞数据需要拿到全部的id，而审批中心有限制，每次查询最大分页数为500
     *
     * @param flowCodes 流程编码
     * @return ids 正常的数据去列表任务表获取
     */
    public static List<String> getMyTaskDone(List<String> flowCodes) {
        List<String> resultList = new ArrayList<>();
        TaskDoneDTO taskDoneDTO = new TaskDoneDTO();
        taskDoneDTO.setTenantId(ContextHelper.getTenantId());
        // 查询用户
        taskDoneDTO.setEmpNo(ContextHelper.getEmpNo());
        // 流程编码
        taskDoneDTO.setFlowCodes(!CollectionUtils.isEmpty(flowCodes) ? flowCodes : FLOW_CODES);
        int currentPage = 1;
        while (true) {
            // 设置当前页和每页大小
            taskDoneDTO.setPageNo((long) currentPage);
            taskDoneDTO.setPageSize(Long.valueOf(CommonConstants.INTEGER_500));

            // 调用查询方法
            PageRows<ApprovalTaskCompleted> pageRows = FlowServiceHelper.queryMyTaskDone(taskDoneDTO);
            // 循环出口
            if (pageRows.getRows().isEmpty()) {
                break;
            }
            List<String> businessIds = pageRows.getRows().stream().map(ApprovalTaskCompleted::getBusinessId).collect(Collectors.toList());
            // 将查询结果添加到总结果列表中
            resultList.addAll(businessIds);

            // 当前页数增加
            currentPage++;
        }
        return resultList;
    }

    /**
     * 查询待我处理的任务(全部)
     *
     * @param flowCodes 流程编码
     * @return ids
     */
    public static List<String> getMyTaskToDo(List<String> flowCodes) {
        List<String> resultList = new ArrayList<>();
        int currentPage = 1;
        TaskToDoDTO taskToDoDTO = new TaskToDoDTO();
        taskToDoDTO.setTenantId(ContextHelper.getTenantId());
        //查询用户
        taskToDoDTO.setEmpNo(ContextHelper.getEmpNo());
        //流程编码
        taskToDoDTO.setFlowCodes(!CollectionUtils.isEmpty(flowCodes) ? flowCodes : FLOW_CODES);
        while (true) {
            // 设置当前页和每页大小
            taskToDoDTO.setPageNo((long) currentPage);
            taskToDoDTO.setPageSize(Long.valueOf(CommonConstants.INTEGER_500));

            // 调用查询方法
            PageRows<TaskVo> pageRows = FlowServiceHelper.queryMyTaskToDo(taskToDoDTO);
            // 循环出口
            if (pageRows.getRows().isEmpty()) {
                break;
            }
            List<String> businessIds = pageRows.getRows().stream().map(TaskVo::getBusinessId).collect(Collectors.toList());
            // 将查询结果添加到总结果列表中
            resultList.addAll(businessIds);

            // 当前页数增加
            currentPage++;
        }
        return resultList;
    }

    /**
     * 根据流程主键id查询审批流程获取当前处理人
     *
     * @param flowEntityIds 流程主键id
     * @return List<FlowHandler>
     */
    public static List<FlowHandler> getFlowHandlerByFlowEntityIds(List<String> flowEntityIds) {
        ApproverQuery approverQuery = new ApproverQuery();
        approverQuery.setBusinessIdList(flowEntityIds);
        approverQuery.setTenantId(ContextHelper.getTenantId());
        return FlowServiceHelper.batchQueryFlowHandlers(approverQuery);
    }

    /**
     * 获取待审批分组Map
     *
     * @param changeOrderId changeOrderId
     * @return Map<String, ApproveRecord>
     */
    public static Map<String, ApproveRecord> getTodoApprovalGroupRecordMap(String changeOrderId, ApproveNodeEnum approveNodeEnum, String userId) {
        Map<String, ApproveRecord> map = MapUtils.newHashMap();
        // 1.查询原始审批记录
        List<ApproveRecord> approvedRecords = FlowServiceHelper
                .getFlowApproveRecords(new ApproveRecordsDTO() {{
                    setTenantId(ContextHelper.getTenantId());
                    setAppId(ContextHelper.getAppId());
                    setBizInstanceId(changeOrderId);
                }});

        approvedRecords = approvedRecords.stream()
                .filter(record -> approveNodeEnum.name().equals(record.getExtendedCode())
                        && ACTIVE_TASK_STATUS.equals(record.getTaskStatus())
                        && userId.equals(record.getApprover()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(approvedRecords)) {
            for (ApproveRecord approvedRecord : approvedRecords) {
                JSONObject jsonObject = JSONObject.parseObject(approvedRecord.getApproverTypeValue());
                String multimodeProductName = (String) jsonObject.get(APPROVERS);
                //相同的审批记录取最上层的
                map.putIfAbsent(multimodeProductName, approvedRecord);
            }
        }
        return map;
    }

    /**
     * 重新指派节点处理人
     * @param bizId
     * @param taskReceiver
     */
    public static void reassignSystemNode(String bizId, String taskReceiver) {
        commonReassign(bizId, taskReceiver, null);
    }

    /**
     * 重新替换节点处理人（多个处理人的情况）
     */
    public static void reassignSystemNode(String bizId, String taskReceiver, String oldApprover) {
        commonReassign(bizId, taskReceiver, oldApprover);
    }

    private static void commonReassign(String bizId, String taskReceiver, String oldApprover) {
        // 1.检索实体对应审批流程
        List<ApproveTask> tasks = getSystemNodeTask(bizId);
        if (CollectionUtils.isEmpty(tasks)) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.APPROVAL_FLOW_NOT_FOUND));
        }
        ApproveTask approveTask;
        if (StringUtils.isEmpty(oldApprover)) {
            approveTask = tasks.get(CommonConstants.INTEGER_ZERO);
        } else {
            approveTask = tasks.stream()
                    .filter(t -> t.getApprover().equals(oldApprover))
                    .findFirst()
                    .orElse(null);
        }
        if (approveTask == null) {
            throw new LcapBusiException(MsgUtils.getMessage(MessageConsts.APPROVER_NOT_FOUND));
        }

        // 3.校验新审批人是否有审批任务
        // 有审批任务，干掉转交人的审批任务
        // 没有审批任务，将转交人的审批任务转给新处理人
        boolean hasTaskFlag = tasks.stream().anyMatch(t -> t.getApprover().equals(taskReceiver));
        if (hasTaskFlag) {
            DeleteApprovalTaskDTO deleteInfo = new DeleteApprovalTaskDTO();
            deleteInfo.setTaskId(approveTask.getTaskId());
            FlowServiceHelper.deleteTask(deleteInfo);
        } else {
            TaskReassignDTO taskReassignDTO = new TaskReassignDTO();
            taskReassignDTO.setTaskReceiver(taskReceiver);
            taskReassignDTO.setTaskId(approveTask.getTaskId());
            FlowServiceHelper.reassign(taskReassignDTO);
        }
    }

    /**
     * 检索节点任务信息
     * @param bizId
     * @return List<ApproveTask>
     */
    public static List<ApproveTask> getSystemNodeTask(String bizId) {
        if (StringUtils.isBlank(bizId)) {
            return Lists.newArrayList();
        }

        ApproveTaskQuery approveTaskQuery = new ApproveTaskQuery();
        approveTaskQuery.setTenantId(ContextHelper.getTenantId());
        approveTaskQuery.setBusinessId(bizId);
        return FlowServiceHelper.queryApproveTaskByBusinessId(approveTaskQuery);
    }

    /**
     * 批量查询active审批节点信息
     * @return List<ApproveTask> CommonConstants.ACTIVE_TASK_STATUS Active
     */
    public static List<ApprovalTaskDTO> batchQueryActiveNodes(List<String> businessIds) {
        List<ApprovalTaskDTO> activeNodes = new ArrayList<>();
        if (CollectionUtils.isEmpty(businessIds)) {
            return activeNodes;
        }
        IFlowInstGateway flowInstGateway = SpringContextUtil.getBeanFactory().getBean(FlowInstGatewayImpl.class);
        for (List<String> ids : Lists.partition(businessIds, CommonConstants.APPROVAL_QUERY_MAX_SIZE)) {
            List<FlowInstDTO> flowInstDOList = flowInstGateway.getFlowInstByBusinessIds(ids);
            if (CollectionUtils.isEmpty(flowInstDOList)) {
                continue;
            }
            List<String> flowids = flowInstDOList.stream().map((inst) -> {
                return inst.getFlowInstanceId();
            }).collect(Collectors.toList());
            BatchProcessparamDTO batchProcessparamDTO = new BatchProcessparamDTO();
            batchProcessparamDTO.setHandler(RequestHeaderUtils.getEmpNo());
            batchProcessparamDTO.setLang(RequestHeaderUtils.getLangId());
            batchProcessparamDTO.setTenantId(RequestHeaderUtils.getTenantId());
            batchProcessparamDTO.setInsFlowIdList(flowids);
            assembleRequestParam(batchProcessparamDTO);
            Map<String, ApprovalProcessDTO> stringApprovalProcessDTOMap = ApprovalFlowClient.queryBatchProcess(batchProcessparamDTO);
            Iterator var8 = flowids.iterator();

            while (var8.hasNext()) {
                String flowInstId = (String) var8.next();
                Optional.ofNullable(stringApprovalProcessDTOMap.get(flowInstId)).ifPresent((approvalProcessDTO) -> {
                    Optional.ofNullable(approvalProcessDTO.getApprovalTaskList()).ifPresent((approvalTaskDTOS) -> {
                        approvalTaskDTOS.forEach((approvalTaskDTO) -> {
                            if (CommonConstants.ACTIVE_TASK_STATUS.equals(approvalTaskDTO.getStatus())) {
                                activeNodes.add(approvalTaskDTO);
                            }
                        });
                    });
                });
            }
        }
        return activeNodes;
    }

    /**
     * 批量查询active审批节点信息（所有状态）
     * @return List<ApproveTask>
     */
    public static Map<String, ApprovalProcessDTO> batchQueryNodes(List<String> businessIds) {
        Map<String, ApprovalProcessDTO> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(businessIds)) {
            return resultMap;
        }
        IFlowInstGateway flowInstGateway = SpringContextUtil.getBean(FlowInstGatewayImpl.class);
        List<FlowInstDTO> allFlowInstDOList = flowInstGateway.getFlowInstByBusinessIds(businessIds);
        for (List<FlowInstDTO> flowInstDOList : Lists.partition(allFlowInstDOList, DEFAULT_PAGE_SIZE)) {
            Map<String, String> flowIdToBusinessId = flowInstDOList.stream()
                    .collect(Collectors.toMap(
                            FlowInstDTO::getFlowInstanceId,
                            FlowInstDTO::getBusinessId
                    ));

            List<String> flowInstIds = new ArrayList<>(flowIdToBusinessId.keySet());

            BatchProcessparamDTO batchProcessparamDTO = new BatchProcessparamDTO();
            batchProcessparamDTO.setHandler(RequestHeaderUtils.getEmpNo());
            batchProcessparamDTO.setLang(RequestHeaderUtils.getLangId());
            batchProcessparamDTO.setTenantId(RequestHeaderUtils.getTenantId());
            batchProcessparamDTO.setInsFlowIdList(flowInstIds);
            assembleRequestParam(batchProcessparamDTO);

            Map<String, ApprovalProcessDTO> flowIdToProcessMap = ApprovalFlowClient.queryBatchProcess(batchProcessparamDTO);

            flowIdToProcessMap.forEach((flowId, processDto) -> {
                String businessId = flowIdToBusinessId.get(flowId);
                if (businessId != null && processDto != null) {
                    resultMap.put(businessId, processDto);
                }
            });
        }
        return resultMap;
    }

    public static void assembleRequestParam(BasePara basePara) {
        String appId = RequestContextHolder.getAppId();
        IParameterRepository parameterRepository = (IParameterRepository) SpringContextUtil.getBeanFactory().getBean(IParameterRepository.class);
        UacParamDTO uacParam = parameterRepository.getUacParams(basePara.getTenantId(), appId);
        Optional.ofNullable(uacParam).ifPresent((uacParamDTO) -> {
            basePara.setAppId(uacParamDTO.getAppId());
            basePara.setSecretKey(uacParamDTO.getAccessSecret());
            basePara.setAppCode(uacParam.getAppId());
        });
    }

    /*
     * 专用：有审批人+进入审批时间
     * */
    public static List<NodeApprover> queryActiveNodes(List<String> businessIds) {
        List<NodeApprover> activeNodes = new ArrayList<>();
        if (CollectionUtils.isEmpty(businessIds)) {
            return activeNodes;
        }

        IFlowInstGateway flowInstGateway = SpringContextUtil.getBeanFactory().getBean(FlowInstGatewayImpl.class);
        // 分批中心活跃节点 最大传参值只支持100
        for (List<String> ids : Lists.partition(businessIds, CommonConstants.INTEGER_FIFTY)) {
            List<FlowInstDTO> flowInstDOList = flowInstGateway.getFlowInstByBusinessIds(ids);
            if (CollectionUtils.isEmpty(flowInstDOList)) {
                continue;
            }
            List<String> flowids = flowInstDOList.stream().map(FlowInstDTO::getFlowInstanceId).collect(Collectors.toList());

            InsFlowParam insFlowParam = new InsFlowParam();
            insFlowParam.setAppId(ContextHelper.getUacAppId());
            insFlowParam.setAppCode(ContextHelper.getUacAppId());
            insFlowParam.setHandler(I_VERSION_SYSTEM_NAME);
            insFlowParam.setFlowInstanceIds(flowids);
            List<NodeApproverDTO> nodeApproverDTOS = ApprovalFlowClient.queryActiveNodeApprover(insFlowParam);
            activeNodes.addAll(nodeApproverDTOS.stream()
                    .filter(item -> !CollectionUtils.isEmpty(item.getActiveNodeInfos()))
                    .flatMap(item -> item.getActiveNodeInfos().stream())
                    .filter(v -> !CollectionUtils.isEmpty(v.getNodeApprovers()))
                    //专用 保留技术审批节点
                    .filter(i -> StringUtils.isNotEmpty(i.getExtendedCode()) && ApproveNodeEnum.getApproveNodeEnum().contains(i.getExtendedCode()))
                    .flatMap(v -> v.getNodeApprovers().stream())
                    .collect(Collectors.toList()));
        }
        return activeNodes;
    }

    /**
     * 检索审批进展
     * @param bizId
     * @param approveFlowCode
     * @return ApprovalProcessDTO
     */
    public static ApprovalProcessDTO getApprovalProcessInfo(String bizId, ApproveFlowCodeEnum approveFlowCode) {
        ProcessBusinessIdDTO instanceParam = new ProcessBusinessIdDTO();
        instanceParam.setFlowCode(approveFlowCode.name());
        instanceParam.setBusinessId(bizId);
        instanceParam.setHandler(I_VERSION_SYSTEM_NAME);

        List<ApprovalProcessDTO> approvalProcessList = ApprovalFlowClient.queryProcess(instanceParam);
        return CollectionUtils.isEmpty(approvalProcessList) ? null : approvalProcessList.get(CommonConstants.INTEGER_ZERO);
    }

    /**
     * 查询单据创建时间
     */
    public static Date getFlowCreateDate(String businessId) {
        if (StringUtils.isEmpty(businessId)) {
            return null;
        }
        FlowInfo flowInfo = FlowHelper.getFlowInfo(businessId);
        if (flowInfo == null
                || FLOW_EXTERNALLY_TERMINATED_STATUS.equals(flowInfo.getStatusCode())) {
            return null;
        }

        FlowInstGatewayImpl flowInstGateway = SpringContextUtil.getBean(FlowInstGatewayImpl.class);
        List<FlowInstDTO> flowInstDOList = flowInstGateway.getFlowInstInfoByFlowInstIds(Collections.singletonList(flowInfo.getFlowInstanceId()));
        return Optional.ofNullable(flowInstDOList)
                .orElse(Collections.emptyList())
                .stream()
                .map(FlowInstDTO::getCreateTime)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }


    public static FlowApprovalRecordDTO getFlowRecord(String flowInstanceId) {
        FlowRecordInstanceIdParam flowRecordInstanceIdParam = new FlowRecordInstanceIdParam();
        flowRecordInstanceIdParam.setInsFlowId(flowInstanceId);
        flowRecordInstanceIdParam.setWithDefSchema(true);
        flowRecordInstanceIdParam.setFlowOperate(true);
        flowRecordInstanceIdParam.setHandler(ContextHelper.getEmpNo());
        flowRecordInstanceIdParam.setAppCode(ContextHelper.getUacAppId());
        flowRecordInstanceIdParam.setAppId(ContextHelper.getAppId());
        flowRecordInstanceIdParam.setTenantId(ContextHelper.getTenantId());
        return ApprovalFlowClient.getFlowRecord(flowRecordInstanceIdParam);
    }


    /**
     * 获取审批节点和当前处理人
     *
     * @param businessId 流程主键id
     * @return ActiveApproverNode
     */
    public static ActiveApproverNode getApprovalNodeCurrentHandlers(String businessId) {
        List<NodeApproverDTO> approvalActiveNodes = getApprovalActiveNode(businessId);

        if (CollectionUtils.isEmpty(approvalActiveNodes)) {
            log.error("get approval active node null businessId:{}", businessId);
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                // do nothing.
            }
            return getOldActiveApproverNode(businessId);
        }

        NodeApproverDTO nodeApproverDTO = approvalActiveNodes.get(0);
        ActiveApproverNode activeApproverNode = Optional.of(nodeApproverDTO)
                .map(NodeApproverDTO::getActiveNodeInfos)
                .filter(activeNodeInfos -> !CollectionUtils.isEmpty(activeNodeInfos))
                .map(activeNodeInfos -> activeNodeInfos.get(0))
                .map(activeNodeInfo -> {
                    List<String> currentReviewers = Optional.of(activeNodeInfo)
                            .map(ActiveNodeInfo::getNodeApprovers)
                            .filter(nodeApprovers -> !CollectionUtils.isEmpty(nodeApprovers))
                            // 如果过滤出来为空集直接返回空
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(NodeApprover::getApprover)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList());

                    return new ActiveApproverNode() {{
                        setExtendedCode(activeNodeInfo.getExtendedCode());
                        setApprover(currentReviewers);
                    }};
                })
                .orElse(null);

        if (activeApproverNode == null) {
            log.error("get approval active node incomplete businessId:{}", businessId);
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                // do nothing.
            }
            return getOldActiveApproverNode(businessId);
        }

        return activeApproverNode;
    }

    /**
     * 通用查询当前活跃节点 当前处理人、节点扩展编码、nodeKey
     */
    public static ActiveApproverNode getActiveNodeInfo(String businessId) {
        List<NodeApproverDTO> approvalActiveNodes = fetchApprovalActiveNodesWithRetry(businessId);
        if (CollectionUtils.isEmpty(approvalActiveNodes)) {
            log.error("get approval active node null businessId:{}", businessId);
            return null;
        }

        NodeApproverDTO nodeApproverDTO = approvalActiveNodes.get(0);
        return Optional.ofNullable(nodeApproverDTO)
                .map(NodeApproverDTO::getActiveNodeInfos)
                .filter(activeNodeInfos -> !CollectionUtils.isEmpty(activeNodeInfos))
                .map(activeNodeInfos -> activeNodeInfos.get(0))
                .map(activeNodeInfo -> {
                    List<String> currentReviewers = Optional.of(activeNodeInfo)
                            .map(ActiveNodeInfo::getNodeApprovers)
                            .filter(nodeApprovers -> !CollectionUtils.isEmpty(nodeApprovers))
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(NodeApprover::getApprover)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList());
                    return new ActiveApproverNode() {{
                        setExtendedCode(activeNodeInfo.getExtendedCode());
                        setNodeKey(activeNodeInfo.getNodeCode());
                        setApprover(currentReviewers);
                    }};
                })
                .orElse(null);
    }

    private static List<NodeApproverDTO> fetchApprovalActiveNodesWithRetry(String businessId) {
        for (int i = 0; i <= 2; i++) {
            try {
                List<NodeApproverDTO> nodes = getApprovalActiveNode(businessId);
                if (!CollectionUtils.isEmpty(nodes)) {
                    return nodes;
                }
                Thread.sleep(2000);
            } catch (Exception e) {
                // 恢复中断状态
                Thread.currentThread().interrupt();
            }
        }
        return Collections.emptyList();
    }

    /**
     * 获取审批节点和当前处理人
     * @param businessId 流程主键id
     * @return Map<String, List<String>>
     */
    public static ActiveApproverNode getOldActiveApproverNode(String businessId) {
        // 获取当前审批人信息
        FlowQueryDTO flowQueryDTO = new FlowQueryDTO();
        flowQueryDTO.setTenantId(ContextHelper.getTenantId());
        flowQueryDTO.setBusinessId(businessId);
        List<String> currentReviewers = FlowServiceHelper.queryFlowHandlers(flowQueryDTO).stream().distinct().collect(Collectors.toList());
        String currentNodeCode = FlowHelper.getCurrentNodeCode(businessId);
        return new ActiveApproverNode(){{
            setApprover(currentReviewers);
            setExtendedCode(currentNodeCode);
        }};
    }

    /**
     * 查询审批中节点以及审批人信息
     *
     * @param businessId 流程主键id
     * @return List<NodeApproverDTO>
     */
    public static List<NodeApproverDTO> getApprovalActiveNode(String businessId) {
        FlowInfo flowInfo = FlowHelper.getFlowInfo(businessId);
        if (flowInfo == null) {
            return Collections.emptyList();
        }
        InsFlowParam insFlowParam = new InsFlowParam();
        insFlowParam.setHandler(ContextHelper.getEmpNo());
        insFlowParam.setFlowInstanceIds(Collections.singletonList(flowInfo.getFlowInstanceId()));
        insFlowParam.setTenantId(ContextHelper.getTenantId());

        BusinessParam businessIdParam = new BusinessParam();
        businessIdParam.setBusinessId(businessId);
        businessIdParam.setFlowCode(flowInfo.getFlowCode());
        insFlowParam.setBusinessIdParams(Collections.singletonList(businessIdParam));

        insFlowParam.setAppId(ContextHelper.getUacAppId());
        insFlowParam.setSecretKey(ContextHelper.getAccessSecret());
        insFlowParam.setAppCode(ContextHelper.getUacAppId());
        return ApprovalFlowClient.queryActiveNodeApprover(insFlowParam);
    }


    /**
     * 审批记录排序
     */
    public static void sortApproveRecords(List<ApproveRecord> approveRecords) {
        if (CollectionUtils.isEmpty(approveRecords)) {
            return;
        }

        approveRecords.sort((t1, t2) -> {
            if (null == t1.getApprovalDate() && null == t2.getApprovalDate()) {
                return INTEGER_ZERO;
            }
            if (null != t1.getApprovalDate() && null == t2.getApprovalDate()) {
                return INTEGER_ONE;
            }
            if (null == t1.getApprovalDate() && null != t2.getApprovalDate()) {
                return INTEGER_NEGATIVE_1;
            }
            return t2.getApprovalDate().compareTo(t1.getApprovalDate());
        });
    }

    @SneakyThrows
    public static List<ApproveRecord> getActiveApproveRecord(List<ApproveRecord> approveRecords,
            ApproveRecordsDTO approveRecordsDTO) {
        List<ApproveRecord> activeApproveRecords = approveRecords.stream()
                .filter(v -> ACTIVE_TASK_STATUS.equals(v.getTaskStatus())
                        && org.springframework.util.StringUtils.hasText(v.getExtendedCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activeApproveRecords)) {
            // 如果没有从审批中心获取到当前处理节点，2S后重新获取一次
            Thread.sleep(2000);
            approveRecords = FlowServiceHelper.getFlowApproveRecords(approveRecordsDTO);
        }
        return approveRecords;
    }

    /**
     * 获取单据所有节点实际审核人
     *
     * @param businessId businessId
     * @return 节点实际审核人
     */
    public static List<String> getAllRecordApprovers(String businessId) {
        return getApprovedRecords(businessId).stream()
                .map(ApproveRecord::getApprover)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取批次所有节点实际审核人
     *
     * @param businessId businessId
     * @return 节点实际审核人
     */
    public static List<String> getBatchAllRecordApprovers(String businessId) {
        return getAfterSubmitApprovedRecords(businessId).stream()
                .filter(item -> item.getApprover() != null && !SYSTEM_USER.equals(item.getApprover()))
                .map(ApproveRecord::getApprover)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取自定义流程版本
     */
    public static int getCustomizeFlowVersion(String businessId) {
        Map<String, Object> flowVariables = getFlowVariables(businessId);
        if(CollectionUtils.isEmpty(flowVariables)){
            return 0;
        }

        return (int) flowVariables.getOrDefault(FLOW_VERSION.getKey(), 0);
    }

    /**
     * @param bizId
     * @param nodeKey
     * @param needGenerateAdminTask 是否能查询历史记录
     */
    public static void pushSystemNode(String bizId, String nodeKey, boolean needGenerateAdminTask, String opinion) {
        FlowInfoQuery flowInfoQuery = new FlowInfoQuery();
        flowInfoQuery.setBusinessId(bizId);
        flowInfoQuery.setTenantId(ContextHelper.getTenantId());
        List<FlowInfo> flowInfos = FlowServiceHelper.queryFlowInfoByBizId(flowInfoQuery);
        if (CollectionUtils.isEmpty(flowInfos)) {
            return;
        }

        CloseActiveNodesDTO closeActiveNodesDTO = new CloseActiveNodesDTO();
        closeActiveNodesDTO.setAppId(ContextHelper.getUacAppId());
        closeActiveNodesDTO.setAppCode(ContextHelper.getUacAppId());
        closeActiveNodesDTO.setOpinion(opinion);
        closeActiveNodesDTO.setHandler(ContextHelper.getEmpNo());
        closeActiveNodesDTO.setFlowInstanceId(flowInfos.get(0).getFlowInstanceId());
        closeActiveNodesDTO.setActiveNodeKeys(Lists.newArrayList(nodeKey));
        closeActiveNodesDTO.setNeedGenerateAdminTask(needGenerateAdminTask);
        ApprovalFlowClient.closeActiveNodes(closeActiveNodesDTO);
    }

    /**
     * getApprovedRecords重载方法，不调用审批中心getFlowInfo方法，修改为状态判断，然后取流程编码
     *
     * @param approveRecordList approveRecordList
     * @param assignmentTypeEnum 任务类型枚举
     * @return 处理后的List<ApproveRecord>
     */
    public static List<ApproveRecord> getApprovedRecords(List<ApproveRecord> approveRecordList,
                                                         AssignmentTypeEnum assignmentTypeEnum) {
        // 原判断getFlowInfo为空会调用recordsCommonDeal(allRecords);但实际只有在草稿状态时会为空，现在草稿状态也不会走到查询审批记录逻辑，剔除
        Class<? extends Enum<?>> nodeEnumClass = FLOWS.get(assignmentTypeEnum.getApproveFlowCodeEnum().name());
        if (nodeEnumClass == null) {
            return recordsCommonDeal(approveRecordList);
        }

        return recordsSpecialDeal(nodeEnumClass, approveRecordList);
    }

    /**
     * 根据业务id、taskId命名审批记录
     *
     * @param businessId businessId
     * @param taskId taskId
     * @return ApproveRecord
     */
    public static ApproveRecord findApproveRecordByTaskId(String businessId,String taskId) {
        List<ApproveRecord> approvedRecords = FlowServiceHelper
                .getFlowApproveRecords(new ApproveRecordsDTO() {{
                    setTenantId(ContextHelper.getTenantId());
                    setAppId(ContextHelper.getAppId());
                    setBizInstanceId(businessId);
                }});

        // 匹配taskId对应的审批记录
        return approvedRecords.stream()
                .filter(item -> item.getTaskId() != null && taskId.equals(item.getTaskId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 单据是否审批过
     *
     * @param businessId 单据id
     * @return true:审批过，false：没有审批过
     */
    public static boolean isApproved(String businessId) {
        List<ApproveRecord> approvedRecords = FlowServiceHelper
                .getFlowApproveRecords(new ApproveRecordsDTO() {{
                    setTenantId(ContextHelper.getTenantId());
                    setAppId(ContextHelper.getAppId());
                    setBizInstanceId(businessId);
                }});

        return Optional.ofNullable(approvedRecords)
                .orElse(Lists.newArrayList())
                .stream()
                // 检查是否存在至少一个ApproveRecord对象，其approvalDate不为null
                .anyMatch(record -> record.getApprovalDate() != null);
    }

    /**
     * 当前审批自定义编码  是否被当天审批过
     *
     * @param businessId id
     * @param extendedCode 自定义编码
     * @return 是否被审批过
     */
    public static boolean hasExtendedCodeApprovalToday(String businessId, String extendedCode) {
        List<ApproveRecord> approvedRecords = FlowServiceHelper
                .getFlowApproveRecords(new ApproveRecordsDTO() {{
                    setTenantId(ContextHelper.getTenantId());
                    setAppId(ContextHelper.getAppId());
                    setBizInstanceId(businessId);
                }});
        if (CollectionUtils.isEmpty(approvedRecords)) {
            return false;
        }

        return approvedRecords.stream()
                .anyMatch(record ->
                        extendedCode.equals(record.getExtendedCode())
                                && isSameDay(record.getApprovalDate())
                );

    }

    private static boolean isSameDay(Date approvalDate) {
        if (approvalDate == null) {
            return false;
        }

        return DateUtils.toLocalDate(approvalDate).equals(LocalDate.now());
    }


    public static void rollbackToNode(String businessId, String flowCode, String nodeKey ,String opinion) {
        RollbackBusinessDTO rollbackBusinessDTO = new RollbackBusinessDTO();
        rollbackBusinessDTO.setBusinessId(businessId);
        rollbackBusinessDTO.setFlowCode(flowCode);
        rollbackBusinessDTO.setNodeKey(nodeKey);
        rollbackBusinessDTO.setOpinion(opinion);
        rollbackBusinessDTO.setAppCode(ContextHelper.getUacAppId());
        rollbackBusinessDTO.setHandler(ContextHelper.getEmpNo());
        rollbackBusinessDTO.setAppId(ContextHelper.getAppId());
        rollbackBusinessDTO.setTenantId(ContextHelper.getTenantId());
        ApprovalFlowClient.rollbackToNode(rollbackBusinessDTO);
    }
}
