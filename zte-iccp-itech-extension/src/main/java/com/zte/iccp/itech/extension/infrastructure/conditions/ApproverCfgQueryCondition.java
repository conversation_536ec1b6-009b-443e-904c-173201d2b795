package com.zte.iccp.itech.extension.infrastructure.conditions;

import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.ApproveRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.infrastructure.annotations.BindFieldName;
import com.zte.iccp.itech.extension.infrastructure.repositories.ApproverConfigRepo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 注意：判空条件字段名的后缀必须是{@value ApproverConfigRepo#IS_EMPTY_FIELD_SUFFIX}
 *
 * <AUTHOR>
 * @since 2024/12/25
 */
@SuppressWarnings("unused")
@Setter
@RequiredArgsConstructor
public class ApproverCfgQueryCondition {

    @Getter
    private final ApprovalTypeEnum approveNode;

    @BindFieldName("role")
    private List<ApproveRoleEnum> roles;

    //BEGIN 变更单基础字段
    @BindFieldName
    private String billStatus;

    @BindFieldName("isGov")
    private BoolEnum isGovEnt;

    @BindFieldName
    private String operator;

    @BindFieldName("operator")
    private Boolean operatorIsEmpty;

    @BindFieldName("operationType")
    private List<String> operationTypes;

    @BindFieldName("operationType")
    private Boolean operationTypeIsEmpty;
    //END 变更单基础字段

    //BEGIN 网络相关字段
    @BindFieldName
    private String logicalNe;
    //END 网络相关字段

    @BindFieldName("logicalNe")
    private Boolean logicalNeIsEmpty;

    //BEGIN 组织树相关字段
    @BindFieldName
    private String sales;

    @BindFieldName("sales")
    private Boolean salesIsEmpty;

    @BindFieldName
    private String organizationRegion;

    @BindFieldName("organizationRegion")
    private Boolean organizationRegionIsEmpty;

    @BindFieldName("responsibleDeptId")
    private String responsibleDept;

    @BindFieldName("responsibleDeptId")
    private Boolean responsibleDeptIsEmpty;
    //END 组织树相关字段

    //BEGIN 产品树相关字段
    @BindFieldName("prodTeam")
    private List<String> prodOperationTeams;

    @BindFieldName("prodTeam")
    private Boolean prodOperationTeamIsEmpty;

    @BindFieldName("prodLine")
    private List<String> prodLines;

    @BindFieldName("prodLine")
    private Boolean prodLineIsEmpty;

    @BindFieldName("prodMainCategory")
    private List<String> prodMainCategories;

    @BindFieldName("prodMainCategory")
    private Boolean prodMainCategoryIsEmpty;

    @BindFieldName("prodSubCategory")
    private List<String> prodSubCategories;

    @BindFieldName("prodSubCategory")
    private Boolean prodSubCategoryIsEmpty;

    @BindFieldName("productModelId")
    private List<String> productModels;

    @BindFieldName("productModelId")
    private Boolean productModelIsEmpty;
    //END 产品树相关字段
}