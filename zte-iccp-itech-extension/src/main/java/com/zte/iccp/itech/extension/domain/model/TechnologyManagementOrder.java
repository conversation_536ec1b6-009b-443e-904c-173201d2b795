package com.zte.iccp.itech.extension.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zte.iccp.itech.extension.common.json.deserializer.MultiEmployeeDeserializer;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.MultiLangText;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.entity.ManageTaskFieldConsts.*;


@ApiModel("技术管理任务单")
@Setter
@Getter
@BaseEntity.Info("createTechnicalManagementTask")
public class TechnologyManagementOrder extends BaseEntity {

    @ApiModelProperty("任务分类")
    @JsonProperty(value = TASK_CATEGORY)
    private String taskCategory;

    @ApiModelProperty("任务名称")
    @JsonProperty(value = TASK_NAME)
    private String taskName;

    @ApiModelProperty("责任单位")
    @JsonProperty(value = ORGANIZATION_ID)
    private Object organizationId;

    @ApiModelProperty("客户标识")
    @JsonProperty(value = CUSTOMER_IDENTIFICATION)
    private Object customerIdentification;

    @ApiModelProperty("产品分类")
    @JsonProperty(value = PRODUCT_ID)
    private Object productId;

    @ApiModelProperty("责任人")
    @JsonProperty(value = RESPONSIBLE_PERSON)
    private List<Employee> responsiblePerson;

    @ApiModelProperty("验收人")
    @JsonProperty(value = ACCEPTOR_PERSON)
    private List<Employee> acceptorPerson;

    @ApiModelProperty("要求完成日期 yyyy-MM-dd")
    @JsonProperty(value = REQUIRED_COMPLETION_DATE)
    private Date requiredCompletionDate;

    @ApiModelProperty("任务说明")
    @JsonProperty(value = TASK_DETAIL)
    private String taskDetail;

    @ApiModelProperty("附件")
    @JsonProperty(value = ATTACHMENT)
    private Object attachment;

    /**
     * 状态：1-待开始，2-执行中，3-审批中，4-已关闭，5-已废止
     */
    @ApiModelProperty("状态")
    @JsonProperty(value = STATUS)
    private String status;

    @ApiModelProperty("进展描述")
    @JsonProperty(value = PROGRESS_DESCRIPTION)
    private String progressDescription;

    @ApiModelProperty("反馈类型")
    @JsonProperty(value = FEEDBACK_TYPE)
    private List<MultiLangText> feedbackType;

    @ApiModelProperty("反馈附件")
    @JsonProperty(value = APPROVAL_ATTACHMENT)
    private Object approvalAttachment;

    @ApiModelProperty("主任务单据编号")
    @JsonProperty(value = CN_NO)
    private String cnNo;

    @ApiModelProperty("责任领导")
    @JsonProperty(value = RESPONSIBLE_LEADER_PERSON)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> responsibleLeaderPerson;

    @ApiModelProperty("知会人")
    @JsonProperty(value = INFORMED_PERSON)
    @JsonDeserialize(using = MultiEmployeeDeserializer.class)
    private List<Employee> informedPerson;

    @ApiModelProperty("目标要求")
    @JsonProperty(value = TARGET_REQUIRE)
    private String targetRequire;

    @ApiModelProperty("计划开始时间 yyyy-MM-dd")
    @JsonProperty(value = PLAN_START_TIME)
    private Date planStartTime;

    @JsonProperty(value = SOURCE)
    @ApiModelProperty("数据来源")
    private String source;

    @JsonProperty(value = SOURCE_ID)
    @ApiModelProperty("数据来源id")
    private String sourceId;
}
