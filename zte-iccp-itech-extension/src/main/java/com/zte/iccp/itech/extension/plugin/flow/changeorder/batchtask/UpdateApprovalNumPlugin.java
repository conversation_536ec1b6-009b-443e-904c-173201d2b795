package com.zte.iccp.itech.extension.plugin.flow.changeorder.batchtask;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.common.Out;
import com.zte.iccp.itech.extension.common.helper.SaveDataHelper;
import com.zte.iccp.itech.extension.domain.constant.ApprovalConstants;
import com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.plugin.flow.BaseFlowPlugin;
import com.zte.iss.approval.sdk.bean.FlowClient;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.APPROVAL_NUM;
import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.ID;

/**
 * 批次任务，修改审批次数 + 提交时间
 *
 * <AUTHOR>
 * @since 2025/01/06
 */
public class UpdateApprovalNumPlugin extends BaseFlowPlugin {

    @Override
    public boolean anyTrigger(FlowClient body, Out<JSONObject> out) {
        String batchId = body.getBusinessId();
        String flowCode = body.getFlowCode();
        Map<String, Object> values = Maps.newHashMap();
        values.put(BatchTaskFieldConsts.SUBMIT_TIME, new Date());
        Class clazz;
        if (ApprovalConstants.BATCH_TASK_FLOW.equals(flowCode)) {
            BatchTask batchTask = BatchTaskAbility.get(batchId, Arrays.asList(ID, APPROVAL_NUM));
            Long num = batchTask.getApprovalNum() == null ? 1 : batchTask.getApprovalNum() + 1;
            values.put(APPROVAL_NUM, num);
            clazz = BatchTask.class;
        } else {
            SubcontractorBatchTask batchTask = BatchTaskAbility.getSub(batchId, Arrays.asList(ID, APPROVAL_NUM));
            Long num = batchTask.getApprovalNum() == null ? 1 : batchTask.getApprovalNum() + 1;
            values.put(APPROVAL_NUM, num);
            clazz = SubcontractorBatchTask.class;
        }
        SaveDataHelper.update(clazz, batchId, values);
        return false;
    }
}
