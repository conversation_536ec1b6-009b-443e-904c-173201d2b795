package com.zte.iccp.itech.extension.common.helper;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.common.json.provider.PropValueProvider;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.model.base.BaseEntity;
import com.zte.iccp.itech.extension.domain.model.base.BaseSubEntity;
import com.zte.paas.lcap.core.entity.MainEntityType;
import com.zte.paas.lcap.ddm.domain.helper.orm.BaseDeleteDataHelper;
import com.zte.paas.lcap.ddm.domain.helper.orm.BaseSaveDataHelper;
import com.zte.paas.lcap.metadata.engine.common.enums.EntityMappingTypeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.iccp.itech.extension.domain.constant.entity.CommonFieldConsts.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/05/08
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class SaveDataHelper {

    private static final List<Consumer<UpdateEventArgs>> AFTER_UPDATE_EVENTS = Lists.newArrayList();

    public static void addAfterUpdateEvent(Consumer<UpdateEventArgs> event) {
        AFTER_UPDATE_EVENTS.add(event);
    }

    public static boolean update(BaseEntity entity) {
        return dispatch(entity,
                e -> update(e.getClass(), e.getId(), toMap(e)),
                e -> update(e.getClass(), e.getPid(), e.getId(), toMap(e)));
    }

    public static boolean update(Class<? extends BaseEntity> clazz, String id, Map<String, Object> values) {
        String entityId = EntityHelper.getEntityId(clazz);
        handleFixedFields(entityId, values);
        handlePropProvider(id, null, values);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        if (!BaseSaveDataHelper.update(mainEntityType, mainEntityType.getKey(), values, EntityMappingTypeEnum.KEY)) {
            return false;
        }

        onAfterUpdateEvent(clazz, Lists.newArrayList(values));
        return true;
    }

    public static boolean update(Class<? extends BaseSubEntity> clazz, String pid, String id, Map<String, Object> values) {
        String entityId = EntityHelper.getEntityId(clazz);
        handleFixedFields(entityId, values);
        handlePropProvider(id, pid, values);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        if(!BaseSaveDataHelper.update(mainEntityType, entityId, values, EntityMappingTypeEnum.KEY)) {
            return false;
        }

        onAfterUpdateEvent(clazz, Lists.newArrayList(values));
        return true;
    }

    public static boolean batchUpdate(List<? extends BaseEntity> multiValues) {
        if (CollectionUtils.isEmpty(multiValues)) {
            return false;
        }

        BaseEntity entity = multiValues.get(0);
        return dispatch(entity,
                e -> batchUpdate(e.getClass(), toListMap(multiValues)),
                e -> batchUpdate(e.getClass(), e.getPid(), toListMap(multiValues)));
    }

    public static boolean batchUpdate(Class<? extends BaseEntity> clazz, List<Map<String, Object>> multiValues) {
        if (CollectionUtils.isEmpty(multiValues)) {
            return false;
        }

        String entityId = EntityHelper.getEntityId(clazz);
        handleFixedFields(entityId, multiValues);
        handlePropProvider(null, multiValues);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        if (!BaseSaveDataHelper.updateBatch(mainEntityType, entityId, multiValues)) {
            return false;
        }

        onAfterUpdateEvent(clazz, multiValues);
        return true;
    }

    public static boolean batchUpdate(Class<? extends BaseSubEntity> clazz, String pid, List<Map<String, Object>> multiValues) {
        if (CollectionUtils.isEmpty(multiValues)) {
            return false;
        }

        String entityId = EntityHelper.getEntityId(clazz);
        handleFixedFields(entityId, multiValues);
        handlePropProvider(pid, multiValues);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        if (!BaseSaveDataHelper.updateBatch(mainEntityType, entityId, multiValues)) {
            return false;
        }

        onAfterUpdateEvent(clazz, multiValues);
        return true;
    }

    public static String create(BaseEntity entity) {
        return dispatch(entity,
                e -> create(e.getClass(), toMap(e)),
                e -> create(e.getClass(), e.getPid(), toMap(e)));
    }

    public static String create(Class<? extends BaseEntity> clazz, Map<String, Object> values) {
        String entityId = EntityHelper.getEntityId(clazz);
        handleFixedFields(entityId, values);
        handlePropProvider(null, null, values);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        return BaseSaveDataHelper.save(mainEntityType, mainEntityType.getKey(), values, EntityMappingTypeEnum.KEY);
    }

    public static String create(Class<? extends BaseSubEntity> clazz, String pid, Map<String, Object> values) {
        String entityId = EntityHelper.getEntityId(clazz);
        handleFixedFields(entityId, values);
        handlePropProvider(null, pid, values);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        return BaseSaveDataHelper.save(mainEntityType, entityId, values, EntityMappingTypeEnum.KEY);
    }

    public static List<String> batchCreate(List<? extends BaseEntity> multiValues) {
        if (CollectionUtils.isEmpty(multiValues)) {
            return Lists.newArrayList();
        }

        BaseEntity entity = multiValues.get(0);
        return dispatch(entity,
                e -> batchCreate(e.getClass(), toListMap(multiValues)),
                e -> batchCreate(e.getClass(), e.getPid(), toListMap(multiValues)));
    }

    public static List<String> batchCreate(
            Class<? extends BaseEntity> clazz,
            List<Map<String, Object>> multiValues) {
        if (CollectionUtils.isEmpty(multiValues)) {
            return Lists.newArrayList();
        }

        String entityId = EntityHelper.getEntityId(clazz);
        handleFixedFields(entityId, multiValues);
        handlePropProvider(null, multiValues);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        return BaseSaveDataHelper.saveBatch(mainEntityType, mainEntityType.getKey(), multiValues);
    }

    public static List<String> batchCreate(
            Class<? extends BaseSubEntity> clazz,
            String pid,
            List<Map<String, Object>> multiValues) {
        if (CollectionUtils.isEmpty(multiValues)) {
            return Lists.newArrayList();
        }

        String entityId = EntityHelper.getEntityId(clazz);
        handleFixedFields(entityId, multiValues);
        handlePropProvider(pid, multiValues);

        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        return BaseSaveDataHelper.saveBatch(mainEntityType, entityId, multiValues);
    }

    public static void batchDelete(Class<? extends BaseEntity> clazz, List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        String entityId = EntityHelper.getEntityId(clazz);
        MainEntityType mainEntityType = EntityHelper.getMainEntityType(clazz);
        BaseDeleteDataHelper.deleteDataByIds(mainEntityType, entityId, idList);
    }

    private static void handlePropProvider(String pid, List<Map<String, Object>> multiValues) {
        multiValues.forEach(m -> handlePropProvider(null, pid, m));
    }

    private static void handlePropProvider(String id, String pid, Map<String, Object> values) {
        for (Map.Entry<String, Object> entry : values.entrySet()) {
            if (entry.getValue() instanceof PropValueProvider) {
                PropValueProvider<?> provider = (PropValueProvider<?>) entry.getValue();
                entry.setValue(provider.getPropValue());
            }
        }

        if (StringUtils.isNotBlank(id)) {
            values.putIfAbsent(ID, id);
        }

        if (StringUtils.isNotBlank(pid)) {
            values.putIfAbsent(PID, pid);
        }
    }

    private static <T> T dispatch(
            BaseEntity entity,
            Function<BaseEntity, T> baseFunc,
            Function<BaseSubEntity, T> subFunc) {
        return entity instanceof BaseSubEntity
                ? subFunc.apply((BaseSubEntity) entity)
                : baseFunc.apply(entity);
    }

    private static Map<String, Object> toMap(BaseEntity entity) {
        return JsonUtils.parseObject(JsonUtils.toJsonString(entity), Map.class);
    }

    private static List<Map<String, Object>> toListMap(List<? extends BaseEntity> multiValues) {
        return multiValues.stream()
                .map(SaveDataHelper::toMap)
                .collect(Collectors.toList());
    }

    private static void handleFixedFields(String entityId, Map<String, Object> values) {
        // 允许手动指定创建时间和更新时间
        Object createTime = values.get(CREATE_TIME);
        if (createTime != null) {
            values.put(String.format("%s_%s", entityId, CREATE_TIME), createTime);
            values.remove(CREATE_TIME);
        }

        Object lastModifiedTime = values.get(LAST_MODIFIED_TIME);
        if (lastModifiedTime != null) {
            values.put(String.format("%s_%s", entityId, LAST_MODIFIED_TIME), lastModifiedTime);
            values.remove(LAST_MODIFIED_TIME);
        }
    }

    private static void handleFixedFields(String entityId, List<Map<String, Object>> multiValues) {
        multiValues.forEach(values -> handleFixedFields(entityId, values));
    }

    private static void onAfterUpdateEvent(
            Class<? extends BaseEntity> entity,
            List<Map<String, Object>> multiValues) {
        UpdateEventArgs args = new UpdateEventArgs(entity, multiValues);
        for (Consumer<UpdateEventArgs> afterUpdateEvent : AFTER_UPDATE_EVENTS) {
            try {
                afterUpdateEvent.accept(args);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @Getter
    @AllArgsConstructor
    public static class UpdateEventArgs {
        private final Class<? extends BaseEntity> entity;

        private final List<Map<String, Object>> multiValues;
    }
}
