package com.zte.iccp.itech.extension.domain.model.subentity;

import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;

import java.util.List;

/**
 * <AUTHOR> 10284287
 * @since 2024/06/26
 */
public abstract class BaseOpAssocProdConfirmEntity extends BaseOpAssocProdSubEntity {
    public abstract BoolEnum getIsRelatedProdApproval();

    public abstract void setIsRelatedProdApproval(BoolEnum value);

    public abstract void setApprovalTeam(List<Employee> approvalTeam);

    public abstract List<Employee> getApprovalTeam();

    public abstract void setApprover(SingleEmployee approver);

    public abstract SingleEmployee getApprover();
}