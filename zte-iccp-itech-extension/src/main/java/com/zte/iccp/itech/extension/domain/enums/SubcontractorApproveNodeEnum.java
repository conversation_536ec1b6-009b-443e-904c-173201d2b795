package com.zte.iccp.itech.extension.domain.enums;

import com.zte.iccp.itech.extension.handler.AbstractRewardApproverHandler;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;

import static com.zte.iccp.itech.extension.domain.enums.ApproveFlowTypeEnum.PARTNER_NETWORK_CHANGE;

/**
 * <AUTHOR>
 * &#064;description:  分包商 审批节点枚举
 * &#064;date  2024/7/02 19:55
 * zte-iccp-itech-netchange
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum SubcontractorApproveNodeEnum {

    /*行政审批办事处科长*/
    ADMIN_REP_PROD_CHIEF(PARTNER_NETWORK_CHANGE, null, null),

    /*办事处PD*/
    OFFICE_PD_APPROVAL(PARTNER_NETWORK_CHANGE, null, null),
    ;
    /**
     * 流程类型
     */
    private final ApproveFlowTypeEnum flowType;
    /**
     * 个性化取审批人实现
     */
    private final Class<? extends AbstractRewardApproverHandler> handler;

    /**
     * approvalType为null，需要从字段获取时候，对应字段名称
     */
    private final List<String> fieldList;

    /**
     * 获取节点类型，防止默认valueOf方法抛出异常
     */
    public static SubcontractorApproveNodeEnum getApproveNodeEnum(String enumName) {
        for (SubcontractorApproveNodeEnum value : SubcontractorApproveNodeEnum.values()) {
            if (value.name()
                    .equals(enumName)) {
                return value;
            }
        }
        return null;
    }
}
