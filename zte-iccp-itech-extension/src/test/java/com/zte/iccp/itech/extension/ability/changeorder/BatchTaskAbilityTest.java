package com.zte.iccp.itech.extension.ability.changeorder;

import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.EmailAbility;
import com.zte.iccp.itech.extension.ability.authtask.BatchTask4AuthAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInTaskUpdateAbility;
import com.zte.iccp.itech.extension.ability.configuration.EmailGroupAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.enums.*;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.authtask.CancelSceneEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.BatchApprNodeMappStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.*;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.domain.model.entity.SubcontractorBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IBatchTask;
import com.zte.iccp.itech.extension.domain.model.grantfile.IChangeOrder;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchTaskOperator;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiProductLinkageGuarantee;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.common.api.ParamServiceHelper;
import com.zte.paas.lcap.common.classloader.CustomClassLoaderHelper;
import com.zte.paas.lcap.core.entity.IDataEntity;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.MainEntityType;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.event.ChangeData;
import com.zte.paas.lcap.core.event.PropertyChangedEvent;
import com.zte.paas.lcap.core.metadata.IDataEntityProperty;
import com.zte.paas.lcap.core.metadata.IDataEntityType;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowHandler;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CidConstants.FIELD_OPERATION_PHASE_OPERATION_DURATION_CID;
import static com.zte.iccp.itech.extension.domain.constant.entity.BatchTaskFieldConsts.URGENT_FLAG;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ContextHelper.class, ConfigHelper.class, QueryDataHelper.class, MsgUtils.class, HrClient.class,
        ParamServiceHelper.class, FlowHelper.class, AssignmentAbility.class, EmailGroupAbility.class,
        TextValuePairHelper.class, SaveDataHelper.class, AlarmUtils.class, ClockInAbility.class, FlowServiceHelper.class,
        MultiProdGuaranteeAbility.class, AssignmentTypeEnum.class, JsonUtils.class, OperatorRoleEnum.class, EntityHelper.class,
        ChangeOrderAbility.class, BatchTask4AuthAbility.class, ClockInTaskUpdateAbility.class, CustomClassLoaderHelper.class,
        PropertyValueConvertUtil.class, DateUtils.class,TimeZoneEnum.class})
public class BatchTaskAbilityTest {

    @Mock
    private IFormView formView;

    @Before
    public void setUp() {
        formView = Mockito.mock(IFormView.class);

        mockStatic(ConfigHelper.class);
        mockStatic(ContextHelper.class);
        mockStatic(FlowHelper.class);
        mockStatic(ParamServiceHelper.class);
        mockStatic(QueryDataHelper.class);
        mockStatic(MsgUtils.class);
        mockStatic(HrClient.class);
        mockStatic(TextValuePairHelper.class);

        mockStatic(AssignmentAbility.class);
        mockStatic(EmailGroupAbility.class);
        mockStatic(MultiProdGuaranteeAbility.class);
        mockStatic(AssignmentTypeEnum.class);
        mockStatic(SaveDataHelper.class);
        mockStatic(AlarmUtils.class);
        mockStatic(JsonUtils.class);
        mockStatic(ClockInAbility.class);
        mockStatic(OperatorRoleEnum.class);
        mockStatic(EntityHelper.class);
        mockStatic(FlowServiceHelper.class);
        mockStatic(ChangeOrderAbility.class);
        mockStatic(BatchTask4AuthAbility.class);
        mockStatic(ClockInTaskUpdateAbility.class);
        mockStatic(PropertyValueConvertUtil.class);
        mockStatic(DateUtils.class);

        mockStatic(CustomClassLoaderHelper.class);
        when(CustomClassLoaderHelper.getAppCustomClassLoader(eq(null), eq(null)))
                .thenReturn(CommonUtils.class.getClassLoader());

        TimeZoneEnum.values();

        mockStatic(TimeZoneEnum.class);
    }

    @Test
    public void showOperationStageClockInTableTest() {
        // true, true
        PowerMockito.when(PropertyValueConvertUtil.getString(any()))
                .thenReturn("changeOrderId");
        PowerMockito.when(EntityHelper.getEntityId(eq(BatchTask.class)))
                .thenReturn("batch_network_assignment");
        IDataModel dataModel = PowerMockito.mock(IDataModel.class);
        MainEntityType entityType = PowerMockito.mock(MainEntityType.class);
        PowerMockito.when(dataModel.getMainEntityType()).thenReturn(entityType);
        ChangeOrder changeOrder = PowerMockito.mock(ChangeOrder.class);
        PowerMockito.when(QueryDataHelper.get(any(), anyList(), anyString()))
                .thenReturn(changeOrder);
        PowerMockito.when(ChangeOrderAbility.getDeptType(any()))
                .thenReturn(DeptTypeEnum.INNER);

        PowerMockito.when(dataModel.getValue(anyString())).thenReturn("test");
        PowerMockito.when(ConfigHelper.get(anyString())).thenReturn("test");

        BatchTaskAbility.showOperationStageClockInTable(dataModel);

        // true, false
        PowerMockito.when(ConfigHelper.get(anyString()))
                .thenReturn("no");
        BatchTaskAbility.showOperationStageClockInTable(dataModel);

        // false, false
        PowerMockito.when(ChangeOrderAbility.getDeptType(any()))
                .thenReturn(DeptTypeEnum.INTER);
        BatchTaskAbility.showOperationStageClockInTable(dataModel);

        // false, true
        PowerMockito.when(ConfigHelper.get(anyString()))
                .thenReturn("test");
        BatchTaskAbility.showOperationStageClockInTable(dataModel);

        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }

    @Test
    public void getInterClockInOrgIdsTest() {
        PowerMockito.when(ConfigHelper.get(anyString())).thenReturn("");
        BatchTaskAbility.getInterClockInOrgIds();
        PowerMockito.when(ConfigHelper.get(anyString())).thenReturn("test");
        BatchTaskAbility.getInterClockInOrgIds();
        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }

    @Test
    public void showClockInRecordTableTest() {
        // true, true
        PowerMockito.when(ChangeOrderAbility.getDeptType(any())).thenReturn(DeptTypeEnum.INNER);

        ChangeOrder changeOrder = PowerMockito.mock(ChangeOrder.class);
        PowerMockito.when(changeOrder.getResponsibleDept()).thenReturn("test");
        PowerMockito.when(ConfigHelper.get(anyString())).thenReturn("test");

        BatchTaskAbility.showClockInRecordTable(changeOrder);

        // true, false
        PowerMockito.when(ConfigHelper.get(anyString())).thenReturn("no");
        BatchTaskAbility.showClockInRecordTable(changeOrder);

        // false, false
        PowerMockito.when(ChangeOrderAbility.getDeptType(any())).thenReturn(DeptTypeEnum.INTER);
        BatchTaskAbility.showClockInRecordTable(changeOrder);

        // false, true
        PowerMockito.when(ConfigHelper.get(anyString())).thenReturn("test");
        BatchTaskAbility.showClockInRecordTable(changeOrder);

        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }


    @Test
    public void queryByChangeOrderIdOperationTimeTest() {
        // 空参情况
        BatchTask batchTask = BatchTaskAbility.queryByChangeOrderIdOperationTime("");
        Assert.assertNull(batchTask);

        // 无任务情况
        when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(Assignment.class)))
                .thenReturn(null);
        batchTask = BatchTaskAbility.queryByChangeOrderIdOperationTime("changeOrderId");
        Assert.assertNull(batchTask);

        // 无多产品情况
        Assignment assignment = new Assignment();
        assignment.setId("assignmentId");

        when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(Assignment.class)))
                .thenReturn(assignment);
        when(MultiProdGuaranteeAbility.queryByAssignmentIds(anyList()))
                .thenReturn(Lists.newArrayList());
        batchTask = BatchTaskAbility.queryByChangeOrderIdOperationTime("changeOrderId");
        Assert.assertNull(batchTask);

        // 无批次任务情况
        when(MultiProdGuaranteeAbility.queryByAssignmentIds(anyList()))
                .thenReturn(Lists.newArrayList(new MultiProductLinkageGuarantee()));
        when(QueryDataHelper.query(eq(BatchTask.class), anyList(), anyList()))
                .thenReturn(Lists.newArrayList());
        batchTask = BatchTaskAbility.queryByChangeOrderIdOperationTime("changeOrderId");
        Assert.assertNull(batchTask);

        // 有批次任务情况
        BatchTask validTask = new BatchTask();
        validTask.setOperationUpdateTime(new Date());
        when(QueryDataHelper.query(eq(BatchTask.class), anyList(), anyList()))
                .thenReturn(Lists.newArrayList(new BatchTask()));
        batchTask = BatchTaskAbility.queryByChangeOrderIdOperationTime("changeOrderId");
        Assert.assertNull(batchTask);
    }

    @Test
    public void testGetHyperLinkBatchTask() {
        // batchTaskList为空的场景
        IBatchTask batchTask = BatchTaskAbility.getHyperLinkBatchTask(Collections.emptyList(), new ArrayList<>(), null, null);
        Assert.assertNull(batchTask);

        // source为空的场景
        batchTask = BatchTaskAbility.getHyperLinkBatchTask(Lists.newArrayList(new BatchTask() {{
            setId("1");
            setSource(null);
        }}), new ArrayList<>(), null, null);
        Assert.assertNotNull(batchTask);

        // source不为空，但是source不等于DataSourceEnum.GUARANTEE.name()的场景
        batchTask = BatchTaskAbility.getHyperLinkBatchTask(Lists.newArrayList(new BatchTask() {{
            setId("1");
            setSource("1");
        }}), new ArrayList<>(), null, null);
        Assert.assertNotNull(batchTask);

        // queryByChangeOrderIdOperationTime为空
        String entityId = "1";
        Mockito.when(MultiProdGuaranteeAbility.getMainLatestProcessedBatchInfo(entityId)).thenReturn(null);
        batchTask = BatchTaskAbility.getHyperLinkBatchTask(Lists.newArrayList(new BatchTask() {{
            setId("1");
            setSource("GUARANTEE");
        }}), new ArrayList<>(), entityId, entityId);

        Assert.assertNull(batchTask);


        String changeOrderId = "1";
        Assignment assignment = new Assignment();
        assignment.setId("1");
        Mockito.when(AssignmentAbility.querySpecificTypeAssignment(
                changeOrderId, AssignmentTypeEnum.NETWORK_CHANGE, Assignment.class)).thenReturn(assignment);

        List<MultiProductLinkageGuarantee> multiProducts = new ArrayList<>();
        MultiProductLinkageGuarantee multiProduct = new MultiProductLinkageGuarantee();
        multiProduct.setId("1");
        multiProduct.setPid("1");
        multiProducts.add(multiProduct);
        Mockito.when(MultiProdGuaranteeAbility.queryByAssignmentIds(Lists.newArrayList(assignment.getId()))).thenReturn(multiProducts);

        Mockito.when(QueryDataHelper.query(eq(BatchTask.class),
                        anyList(),
                        anyList()))
                .thenReturn(Lists.newArrayList(new BatchTask(){{
                    setId("1");
                    setBatchNo("1");
                    setOperationUpdateTime(new Date());
                }}));
        Mockito.when(MultiProdGuaranteeAbility.getMainLatestProcessedBatchInfo(entityId)).thenReturn(new BatchTask(){{
            setId("1");
            setBatchNo("1");
        }});

        batchTask = BatchTaskAbility.getHyperLinkBatchTask(Lists.newArrayList(new BatchTask() {{
            setId("1");
            setSource("GUARANTEE");
            setBatchNo("1");
        }}), new ArrayList<>(), entityId, entityId);

        Assert.assertNotNull(batchTask);

        // 任务类型为批次，非保障单
        List<TextValuePair> textValuePairs = Lists.newArrayList(new TextValuePair() {{
            // 内部批次
            setValue("2");
        }});
        when(AssignmentTypeEnum.fromTextValuePair(anyList())).thenReturn(AssignmentTypeEnum.NETWORK_CHANGE_BATCH);
        batchTask = BatchTaskAbility.getHyperLinkBatchTask(Lists.newArrayList(new BatchTask() {{
            setId("1");
            setBatchNo("1");
        }}), textValuePairs, "1", "1");
        Assert.assertNotNull(batchTask);
    }


    @Test
    public void testGetMatchBatchTask() {
        List<FlowHandler> flowHandlerList = new ArrayList<>();
        flowHandlerList.add(new FlowHandler(){{
            setApproveTaskList(Lists.newArrayList(new ApproveTask(){{
                setApprover("10010");
                setBusinessId("1");
            }}));
            setBusinessId("1");
        }});
        Mockito.when(FlowHelper.getFlowHandlerByFlowEntityIds(anyList())).thenReturn(flowHandlerList);

        String empNo = "10010";
        Mockito.when(ContextHelper.getEmpNo()).thenReturn(empNo);
        IBatchTask iBatchTask = new BatchTask(){{
            setId("1");
            setCurrentStatus("6");
        }};
        IBatchTask batchTask = BatchTaskAbility.getMatchBatchTask(Lists.newArrayList(iBatchTask));
        Assert.assertNotNull(batchTask);

        empNo = "10011";
        Mockito.when(ContextHelper.getEmpNo()).thenReturn(empNo);

        batchTask = BatchTaskAbility.getMatchBatchTask(Lists.newArrayList(iBatchTask));
        Assert.assertNull(batchTask);

        iBatchTask = new BatchTask(){{
            setId("2");
            setCurrentStatus("6");
        }};

        batchTask = BatchTaskAbility.getMatchBatchTask(Lists.newArrayList(iBatchTask));
        Assert.assertNull(batchTask);
    }

    @Test
    public void getIChangeOrderByBatchId() {
        BatchTaskAbility.getIChangeOrderByBatchId("1", SubcontractorBatchTask.class);

        Mockito.when(QueryDataHelper.get(eq(SubcontractorBatchTask.class),
                        anyList(), anyString()))
                .thenReturn(new SubcontractorBatchTask());
        IChangeOrder batchTask = BatchTaskAbility.getIChangeOrderByBatchId("1", SubcontractorBatchTask.class);
        Assert.assertNull(batchTask);
    }

    @Test
    public void pushResultTobeBack() {
        BatchTaskAbility.pushResultTobeBack();

        BatchTask batchTask = new BatchTask();
        batchTask.setId("1");
        batchTask.setChangeOrderId("1");
        batchTask.setPlanOperationStartTime(new Date());
        ArrayList<BatchTask> batchTasks = Lists.newArrayList(batchTask);
        Mockito.when(DateUtils.dateToString(any(), anyString())).thenReturn("2025-03-25 14:00:00");
        Mockito.when(DateUtils.stringToDate(anyString(), any())).thenReturn(new Date());
        Mockito.when(QueryDataHelper.query(eq(BatchTask.class), anyList(), anyList())).thenReturn(batchTasks);
        BatchTaskAbility.pushResultTobeBack();

        ChangeOrder order = new ChangeOrder();
        order.setId("1");
        order.setTimeZone(TimeZoneEnum.UTC_MINUS_0200);
        Mockito.when(QueryDataHelper.get(eq(ChangeOrder.class), anyList(), anyList())).thenReturn(Lists.newArrayList(order));
        BatchTaskAbility.pushResultTobeBack();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        batchTask.setPlanOperationStartTime(calendar.getTime());
        Mockito.when(QueryDataHelper.query(eq(BatchTask.class), anyList(), anyList())).thenReturn(Lists.newArrayList(batchTask));
        BatchTaskAbility.pushResultTobeBack();

        NetworkChangeAssignment assignment=  new NetworkChangeAssignment();
        Mockito.when(AssignmentAbility.queryBatchAssignment(anyString())).thenReturn(assignment);


        assignment.setCurrentProcessorEmployee(Lists.newArrayList(new Employee()));
        Mockito.when(AssignmentAbility.queryBatchAssignment("1")).thenReturn(assignment);
        BatchTaskAbility.pushResultTobeBack();
        PowerMockito.verifyStatic(FlowHelper.class, times(0));
        FlowHelper.pushSystemNode("1", "1");
    }

    @Test
    public void getCurrentProcessor() {
        // getCurrentProcessorEmployee为空
        Mockito.when(AssignmentAbility.queryBatchAssignment(anyString())).thenReturn(new NetworkChangeAssignment(){{
            setId("id");
        }});

        List<String> currentProcessor = BatchTaskAbility.getCurrentProcessor("1", "1");
        Assert.assertFalse(currentProcessor.isEmpty());
    }

    @Test
    public void testOperationCancelTitleShow() {
        titleProcess(null,new ArrayList<>());

        Mockito.when(MsgUtils.getMessage(MessageConsts.REVIEW_RESULT)).thenReturn("审核结果：");
        Mockito.when(TextValuePairHelper.getLanguageText(anyList())).thenReturn("审核通过");
        ApproveRecord approveRecord = new ApproveRecord() {{
            setApprover("1");
            setApprovalDate(new Date());
        }};
        String title = BatchTaskAbility.getOperationCancelTitle(
                BatchTask.class, approveRecord, BatchApprNodeMappStatusEnum.OPERATION_CANCEL_REVIEW, "1");
        Assert.assertNotNull(title);
    }

    @Test
    public void getPendingNotifyTitle() {
        Class<? extends BaseEntity> entityClass = BatchTask.class;
        String ocOperationChangeDesc = null;
        String batchId = "1";
        BatchApprNodeMappStatusEnum batchApprNodeMappStatusEnum = BatchApprNodeMappStatusEnum.PENDING_NOTIFICATION;
        BatchTask batchTask = new BatchTask() {{
            setOcOperationChangeDesc(ocOperationChangeDesc);
        }};

        ApproveRecord approveRecord = new ApproveRecord() {{
            setApprovalDate(new Date());
            setApprover("1");
            setOpinion("1");
        }};
        // 1.getOcOperationChangeDesc为空
        when(QueryDataHelper.get(eq(BatchTask.class),anyList(),anyString())).thenReturn(batchTask);
        when(HrClient.queryEmployeeNameInfo(anyString())).thenReturn("1");
        when(MsgUtils.getLangMessage(anyString(), anyString())).thenReturn("1");
        String result = BatchTaskAbility.getPendingNotifyTitle(
                entityClass, approveRecord, batchApprNodeMappStatusEnum, batchId);
        Assert.assertNotNull(result);

        // 2.getOcOperationChangeDesc不为空，AssignmentStatusEnum不为取消操作审核
        batchTask.setOcOperationChangeDesc("1");
        batchTask.setCurrentStatus("10");
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        when(HrClient.queryEmployeeNameInfo(anyString())).thenReturn("1");
        when(MsgUtils.getLangMessage(anyString(), anyString())).thenReturn("1");
        result = BatchTaskAbility.getPendingNotifyTitle(
                entityClass, approveRecord, batchApprNodeMappStatusEnum, batchId);
        Assert.assertNotNull(result);

        // 3.getOcOperationChangeDesc不为空，AssignmentStatusEnum为取消操作审核
        batchTask.setOcOperationChangeDesc("1");
        batchTask.setCurrentStatus("22");
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        when(HrClient.queryEmployeeNameInfo(anyString())).thenReturn("1");
        when(MsgUtils.getLangMessage(anyString(), anyString())).thenReturn("1");
        result = BatchTaskAbility.getPendingNotifyTitle(
                entityClass, approveRecord, batchApprNodeMappStatusEnum, batchId);
        Assert.assertNotNull(result);
    }

    /**
     * 标题处理通用方法
     *
     * @param ocOperationChangeDesc ocOperationChangeDesc
     * @param ocApproveResult ocApproveResult
     */
    private void titleProcess(String ocOperationChangeDesc,List<TextValuePair> ocApproveResult) {
        Mockito.when(QueryDataHelper.get(eq(BatchTask.class),anyList(),anyString())).thenReturn(new BatchTask(){{
            setId("1");
            setOcOperationChangeDesc(ocOperationChangeDesc);
            setOcApproveResult(ocApproveResult);
        }});

        BatchApprNodeMappStatusEnum batchApprNodeMappStatusEnum = BatchApprNodeMappStatusEnum.OPERATION_CANCEL_REVIEW;
        //标题
        Mockito.when(MsgUtils.getLangMessage("zh_CN", batchApprNodeMappStatusEnum.getTitleMsg())).thenReturn("操作取消审核");
        // 审核人
        Mockito.when(HrClient.queryEmployeeNameInfo(anyString())).thenReturn("张三10086");
    }

    @Test
    public void updateBatchTaskEmailCcsTest() {
        BatchTask batchTask = new BatchTask();

        // 空抄送
        when(EmailGroupAbility.getBatchCcPerson(any(), any(), any(), any())).thenReturn(Lists.newArrayList());
        when(HrClient.queryEmployeeInfo(anyList())).thenReturn(Lists.newArrayList());
        when(SaveDataHelper.batchUpdate(anyList())).thenReturn(true);

        List<Employee> result = BatchTaskAbility.updateBatchTaskEmailCcs(batchTask);
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        // 填写抄送
        Employee emailCc = new Employee();
        emailCc.setEmpUIID("10330932");
        batchTask.setEmailCc(Lists.newArrayList(emailCc));

        result = BatchTaskAbility.updateBatchTaskEmailCcs(batchTask);
        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void updatePartnerBatchTaskEmailCcsTest() {
        SubcontractorBatchTask batchTask = new SubcontractorBatchTask();

        // 空抄送
        when(EmailGroupAbility.getBatchCcPerson(any(), any(), any(), any())).thenReturn(Lists.newArrayList());
        when(HrClient.queryEmployeeInfo(anyList())).thenReturn(Lists.newArrayList());
        when(SaveDataHelper.batchUpdate(anyList())).thenReturn(true);

        List<Employee> result = BatchTaskAbility.updatePartnerBatchTaskEmailCcs(batchTask);
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        // 填写抄送
        Employee emailCc = new Employee();
        emailCc.setEmpUIID("10330932");
        batchTask.setEmailCc(Lists.newArrayList(emailCc));

        result = BatchTaskAbility.updatePartnerBatchTaskEmailCcs(batchTask);
        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void listBatchTaskByChangeOrderIdListTest() {
        when(QueryDataHelper.query(eq(BatchTask.class), anyList(), anyList())).thenReturn(new ArrayList<>());
        Assert.assertTrue(CollectionUtils.isEmpty(BatchTaskAbility.listBatchTaskByChangeOrderIdList(new ArrayList<>(), new ArrayList<>())));
    }

    @Test
    public void updatePartnerBatchTaskEmailCcsTest_WithDuplicateEmailCc() {
        SubcontractorBatchTask batchTask = new SubcontractorBatchTask();
        String existingEmpId = "10330932";

        // 1. Mock默认邮件列表包含 existingEmpId
        when(EmailGroupAbility.getBatchCcPerson(any(), any(), any(), any()))
                .thenReturn(Lists.newArrayList(existingEmpId));
        Employee employee = new Employee();
        employee.setEmpUIID(existingEmpId);
        when(HrClient.queryEmployeeInfo(anyList()))
                .thenReturn(Lists.newArrayList(employee));

        // 2. 设置批次任务抄送人包含同ID
        Employee duplicateCc = new Employee();
        duplicateCc.setEmpUIID(existingEmpId);
        batchTask.setEmailCc(Lists.newArrayList(duplicateCc));

        // 3. 执行方法
        List<Employee> result = BatchTaskAbility.updatePartnerBatchTaskEmailCcs(batchTask);

        // 4. 验证重复项被过滤，总人数=默认列表长度（1）
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testCheckOperators_LocalWatchMenCountGreaterThanOne() throws Exception {
        IDataEntityCollection dataEntityCollection = PowerMockito.mock(IDataEntityCollection.class);
        IDataModel model = PowerMockito.mock(IDataModel.class);
        IFormView view = PowerMockito.mock(IFormView.class);
        when(model.getEntryRowEntities(anyString())).thenReturn(dataEntityCollection);

        SingleEmployee employee = new SingleEmployee();
        employee.setEmpUIID("emp123");
        SingleEmployee employee2 = new SingleEmployee();
        employee2.setEmpUIID("emp222");

        // 配置getPersonRemoteFlagMap中的value
        TextValuePair textValuePair = new TextValuePair();
        textValuePair.setValue("LOCAL");

        // 配置getOperatorRoleEnumListMap中的key
        PowerMockito.when(OperatorRoleEnum.fromValue(any())).thenReturn(OperatorRoleEnum.WATCHMAN);

        DynamicDataEntity dynamicDataEntity = PowerMockito.mock(DynamicDataEntity.class);
        IDataEntityType iDataEntityType = PowerMockito.mock(IDataEntityType.class);
        when(dynamicDataEntity.getDataEntityType()).thenReturn(iDataEntityType);
        IDataEntityProperty iDataEntityProperty = PowerMockito.mock(IDataEntityProperty.class);
        when(iDataEntityType.getProperty(anyString())).thenReturn(iDataEntityProperty);

        // 配置getOperatorRoleEnumListMap中的元素个数
        when(dataEntityCollection.size()).thenReturn(1);
        when(dataEntityCollection.get(0)).thenReturn(dynamicDataEntity);

        PowerMockito.when(JsonUtils.parseArray((Object) any(), (Class<?>) any()))
                .thenReturn(Arrays.asList(employee)) //getOperatorRoleEnumListMap
                .thenReturn(Arrays.asList(textValuePair)); //getPersonRemoteFlagMap

        BatchTaskAbility.checkOperators(model, view, BatchTaskOperator.class);

        // 配置getOperatorRoleEnumListMap中的元素个数
        when(dataEntityCollection.size()).thenReturn(2);
        when(dataEntityCollection.get(0)).thenReturn(dynamicDataEntity);
        when(dataEntityCollection.get(1)).thenReturn(dynamicDataEntity);

        PowerMockito.when(JsonUtils.parseArray((Object) any(), (Class<?>) any()))
                .thenReturn(Arrays.asList(employee)) //getOperatorRoleEnumListMap
                .thenReturn(Arrays.asList(employee)) //getOperatorRoleEnumListMap
                .thenReturn(Arrays.asList(textValuePair)) //getPersonRemoteFlagMap
                .thenReturn(Arrays.asList(textValuePair)); //getPersonRemoteFlagMap

        BatchTaskAbility.checkOperators(model, view, BatchTaskOperator.class);

        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }

    @Test
    public void getBatchTaskById() {
        List<String> fields = Arrays.asList("1");
        Assignment assignment = new Assignment();
        BatchTaskAbility.getBatchTaskById("1", null, fields);
        BatchTaskAbility.getBatchTaskByChangeOrderId("1", null, fields);

        BatchTaskAbility.getBatchTaskById("1", assignment, fields);
        BatchTaskAbility.getBatchTaskByChangeOrderId("1", assignment, fields);

        Mockito.when(TextValuePairHelper.getValue(any())).thenReturn("1");
        BatchTaskAbility.getBatchTaskById("1", assignment, fields);
        BatchTaskAbility.getBatchTaskByChangeOrderId("1", assignment, fields);

        Mockito.when(TextValuePairHelper.getValue(any())).thenReturn("2");
        BatchTaskAbility.getBatchTaskById("1", assignment, fields);
        BatchTaskAbility.getBatchTaskByChangeOrderId("1", assignment, fields);

        Mockito.when(TextValuePairHelper.getValue(any())).thenReturn("7");
        IBatchTask batchTaskById = BatchTaskAbility.getBatchTaskById("1", assignment, fields);
        BatchTaskAbility.getBatchTaskByChangeOrderId("1", assignment, fields);
        Assert.assertNull(batchTaskById);
    }

    @Test
    public void updateNodeEmailCcsTest() {
        Employee configEmail = new Employee();
        configEmail.setEmpUIID("configCc");

        Employee exEmail = new Employee();
        exEmail.setEmpUIID("exEmailCc");

        PowerMockito.when(HrClient.queryEmployeeInfo(anyList()))
                .thenReturn(Lists.newArrayList(configEmail, exEmail));

        // 批次任务更新
        IBatchTask batchTask = new BatchTask();
        batchTask.setId("batchTaskId");

        List<String> emails = BatchTaskAbility.updateNodeEmailCcs(
                batchTask,
                Lists.newArrayList("configCc"),
                Lists.newArrayList(exEmail),
                batchTask::setResultMail);
        Assert.assertFalse(CollectionUtils.isEmpty(emails));

        // 合作方批次任务更新
        IBatchTask partnerBatchTask = new SubcontractorBatchTask();
        batchTask.setId("partnerBatchTaskId");

        emails = BatchTaskAbility.updateNodeEmailCcs(
                partnerBatchTask,
                Lists.newArrayList("configCc"),
                Lists.newArrayList(exEmail),
                partnerBatchTask::setApprovalEmail);
        Assert.assertFalse(CollectionUtils.isEmpty(emails));
    }

    @Test
    public void listenerTimeTest() {
        // 1.主流程
        IDataModel model = PowerMockito.mock(IDataModel.class);
        IFormView view = PowerMockito.mock(IFormView.class);
        PropertyChangedEvent event = PowerMockito.mock(PropertyChangedEvent.class);
        ChangeData changeData = PowerMockito.mock(ChangeData.class);
        IDataEntityProperty property = PowerMockito.mock(IDataEntityProperty.class);

        when(event.getChangeSet()).thenReturn(new ChangeData[]{changeData});
        when(changeData.getProperty()).thenReturn(property);
        when(property.getKey()).thenReturn("plan_operation_start_time");
        when(model.getValue(anyString())).thenReturn(new Date(4901066335000L)).thenReturn(new Date(1745397445L));
        when(EntityHelper.getEntityId(any())).thenReturn("666");
        MainEntityType mainEntityType = PowerMockito.mock(MainEntityType.class);
        when(model.getMainEntityType()).thenReturn(mainEntityType);
        when(mainEntityType.getKey()).thenReturn("777");

        IDataEntity iDataEntity = PowerMockito.mock(IDataEntity.class);
        when(model.getRootDataEntity()).thenReturn(iDataEntity);
        when(iDataEntity.getPkValue()).thenReturn("111");
        BatchTask batchTask = PowerMockito.mock(BatchTask.class);
        ChangeOrder iChangeOrder = PowerMockito.mock(ChangeOrder.class);
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        when(batchTask.getChangeOrderId()).thenReturn("666");
        when(QueryDataHelper.get(eq(ChangeOrder.class), anyList(), anyString())).thenReturn(iChangeOrder);

        when(TextValuePairHelper.getValue(any())).thenReturn("111");
        when(QueryDataHelper.get(eq(SubcontractorBatchTask.class), anyList(), anyString())).thenReturn(null);
        BatchTaskAbility.listenerTime(event, model, view);
        Assert.assertNotNull(iChangeOrder);

        // 2.开始时间小于结束时间分支
        when(model.getValue(anyString())).thenReturn(new Date(4901066335000L)).thenReturn(new Date(4901066336000L));
        BatchTaskAbility.listenerTime(event, model, view);

        // 3.结束时间为空分支
        when(model.getValue(anyString())).thenReturn(new Date(4901066335000L)).thenReturn(null);
        BatchTaskAbility.listenerTime(event, model, view);

    }

    @Test
    public void createFlowAndSave() {
        ChangeOrder changeOrder = new ChangeOrder();
        changeOrder.setId("1");
        changeOrder.setResponsibleDept("11111");
        changeOrder.setProductCategory("111111");
        changeOrder.setImportance(ImportanceEnum.STAR1);
        changeOrder.setRiskEvaluation(RiskEvaluationEnum.STAR1);

        List<BatchTask> batchTasks = new ArrayList<>();
        BatchTask task = new BatchTask();
        batchTasks.add(task);
        Assignment assignment = new Assignment();
        when(AssignmentAbility.querySpecificTypeAssignment(anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(Assignment.class)))
                .thenReturn(assignment);
        when(ConfigHelper.get(anyString()))
                .thenReturn("2222");
        when(JsonUtils.parseObject((Object) any(), eq(Map.class)))
                .thenReturn(new HashMap<>());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("business_id", 1);
        when(FlowServiceHelper.saveBizAndStartFlow(any()))
                .thenReturn(jsonObject);
        BatchTaskAbility.createFlowAndSave(changeOrder, batchTasks);
        Assert.assertNotNull(changeOrder);
    }

    @Test
    public void processBatchCancel() {
        // 内部批次
        Class<? extends BaseEntity> batchClassEntity = BatchTask.class;
        String batchId = "1";
        ApproveFlowCodeEnum approveFlowCodeEnum = ApproveFlowCodeEnum.BATCH_TASK_FLOW;
        List<BatchTask> batchTaskList = new ArrayList<>();
        BatchTask batchTask1 = new BatchTask() {{
            setId("1");
            setChangeOrderId("1");
            setCurrentStatus("3");
        }};
        BatchTask batchTask2 = new BatchTask() {{
            setId("2");
            setChangeOrderId("2");
            setCurrentStatus("4");
        }};
        batchTaskList.add(batchTask1);
        batchTaskList.add(batchTask2);
        when(QueryDataHelper.get(eq(BatchTask.class),anyList(),anyString())).thenReturn(batchTask1);
        when(SaveDataHelper.update(eq(BatchTask.class), anyString(), anyMap())).thenReturn(true);
        when(QueryDataHelper.query(eq(BatchTask.class), anyList(), anyList())).thenReturn(batchTaskList);
        when(ChangeOrderAbility.getGuaranteeIdsByChangeOrderId(anyString())).thenReturn(null);
        AsyncExecuteUtils.execute(() -> EmailAbility.sendBatchOperationEmail(batchId, NotifyTypeEnum.CANCEL, batchClassEntity));
        when(ConfigHelper.get(anyString(), anyString())).thenReturn("false");

        BatchTaskAbility.processBatchCancel(batchClassEntity,batchId, approveFlowCodeEnum);
        Assert.assertNotNull(batchTaskList);

        // 合作方批次
        List<SubcontractorBatchTask> subcontractorBatchTaskList = new ArrayList<>();
        SubcontractorBatchTask subcontractorBatchTask1 = new SubcontractorBatchTask() {{
            setId("1");
            setChangeOrderId("1");
            setCurrentStatus("3");
        }};
        SubcontractorBatchTask subcontractorBatchTask2 = new SubcontractorBatchTask() {{
            setId("2");
            setChangeOrderId("2");
            setCurrentStatus("4");
        }};
        subcontractorBatchTaskList.add(subcontractorBatchTask1);
        subcontractorBatchTaskList.add(subcontractorBatchTask2);
        Class<? extends BaseEntity> batchClassEntity2 = SubcontractorBatchTask.class;
        approveFlowCodeEnum = ApproveFlowCodeEnum.SUBCONTRACTOR_TASK_FLOW;
        when(QueryDataHelper.get(eq(SubcontractorBatchTask.class),anyList(),anyString())).thenReturn(subcontractorBatchTask1);
        when(SaveDataHelper.update(eq(SubcontractorBatchTask.class), anyString(), anyMap())).thenReturn(true);
        when(QueryDataHelper.query(eq(SubcontractorBatchTask.class), anyList(), anyList())).thenReturn(subcontractorBatchTaskList);
        AsyncExecuteUtils.execute(() -> EmailAbility.sendBatchOperationEmail(batchId, NotifyTypeEnum.CANCEL, batchClassEntity2));
        when(ConfigHelper.get(anyString(),anyString())).thenReturn("true");

        List<String> batchTaskIds = Lists.newArrayList("1","2");
        when(ChangeOrderAbility.getGuaranteeIdsByChangeOrderId(anyString())).thenReturn(batchTaskIds);
        for (String batchTaskId : batchTaskIds) {
            AsyncExecuteUtils.execute(() -> BatchTask4AuthAbility.cancelAuthTask(CancelSceneEnum.ABOLISH, batchClassEntity, batchTaskId));
        }

        ClockInTaskUpdateAbility.batchCancelClockInTasks(batchTaskIds);
        BatchTaskAbility.processBatchCancel(batchClassEntity,batchId, approveFlowCodeEnum);
        Assert.assertNotNull(subcontractorBatchTaskList);
    }

    @Test
    public void changeOrderExecuteProcess() {
        Assignment assignment = new Assignment();
        assignment.setEntityId("1");
        assignment.setAssignmentType(Lists.newArrayList(new TextValuePair() {{
            setValue("1");
        }}));

        List<BatchTask> batchTaskList = new ArrayList<>();
        new BatchTask(){{
            setId("1");
            setBatchNo("1");
        }};
        when(QueryDataHelper.query(eq(BatchTask.class),anyList(),anyList())).thenReturn(batchTaskList);
        BatchTaskAbility.flowEndUpadteAllStatus("1","1","1");
        Assert.assertThrows(NullPointerException.class, () -> BatchTaskAbility.changeOrderExecuteProcess(formView, assignment));
    }

    @Test
    public void checkOperationTimeTest() {
        IFormView view = PowerMockito.mock(IFormView.class);
        ChangeData changeData = PowerMockito.mock(ChangeData.class);
        IDataEntityProperty property = PowerMockito.mock(IDataEntityProperty.class);
        when(changeData.getProperty()).thenReturn(property);
        when(property.getKey()).thenReturn("plan_operation_start_time");
        when(changeData.getProperty().getKey()).thenReturn("operation_duration");
        IDataModel dataModel = PowerMockito.mock(IDataModel.class);

        PowerMockito.when(dataModel.getValue(eq(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID), eq(0))).thenReturn("test1");
        PowerMockito.when(dataModel.getValue(eq(FIELD_OPERATION_PHASE_OPERATION_DURATION_CID), eq(1))).thenReturn("test2");
        PowerMockito.when(PropertyValueConvertUtil.getBigDecimal(eq("test1"))).thenReturn(null);
        PowerMockito.when(PropertyValueConvertUtil.getBigDecimal(eq("test2"))).thenReturn(null);
        BatchTaskAbility.checkOperationTime(dataModel, view, changeData);


        PowerMockito.when(PropertyValueConvertUtil.getBigDecimal(eq("test1"))).thenReturn(BigDecimal.valueOf(100));
        PowerMockito.when(PropertyValueConvertUtil.getBigDecimal(eq("test2"))).thenReturn(BigDecimal.valueOf(100));
        BatchTaskAbility.checkOperationTime(dataModel, view, changeData);

        PowerMockito.when(PropertyValueConvertUtil.getBigDecimal(eq("test1"))).thenReturn(BigDecimal.valueOf(999999));
        BatchTaskAbility.checkOperationTime(dataModel, view, changeData);

        PowerMockito.when(PropertyValueConvertUtil.getBigDecimal(eq("test1"))).thenReturn(BigDecimal.valueOf(100));
        PowerMockito.when(PropertyValueConvertUtil.getBigDecimal(eq("test2"))).thenReturn(BigDecimal.valueOf(999999));
        Assert.assertFalse(BatchTaskAbility.checkOperationTime(dataModel, view, changeData));
    }

    @Test
    public void testIsAdministrationApprovalFalse() {
        BatchTaskAbility.isAdministrationApproval("COOPERATION_GUARANTEE", "1", "1");
        BatchTaskAbility.isAdministrationApproval("1", "1", "1");
        BatchTaskAbility.isAdministrationApproval("1", "Y", "1");
        Assert.assertTrue(BatchTaskAbility.isAdministrationApproval("1", "Y", "Y"));
    }

    @Test
    public void changeFlowPrompt() {
        IDataModel dataModel = PowerMockito.mock(IDataModel.class);
        IFormView view = PowerMockito.mock(IFormView.class);
        IDataEntity entity = PowerMockito.mock(IDataEntity.class);
        when(dataModel.getRootDataEntity()).thenReturn(entity);
        when(entity.getPkValue()).thenReturn("1");

        ChangeData changeData = PowerMockito.mock(ChangeData.class);
        IDataEntityProperty property = PowerMockito.mock(IDataEntityProperty.class);
        when(changeData.getProperty()).thenReturn(property);
        when(property.getKey()).thenReturn("is_urgent");
        ChangeData[] changes = {changeData};
        PropertyChangedEvent event = new PropertyChangedEvent(changes, true);

        MainEntityType entityType = PowerMockito.mock(MainEntityType.class);
        PowerMockito.when(dataModel.getMainEntityType()).thenReturn(entityType);
        PowerMockito.when(EntityHelper.getEntityId(eq(SubcontractorBatchTask.class)))
                .thenReturn("1");
        BatchTask batchTask = new BatchTask();
        batchTask.setIsChangeRemoteApproval(BoolEnum.N);
        batchTask.setOperationTypeGroup("COOPERATION_GUARANTEE");
        when(QueryDataHelper.get(eq(BatchTask.class),anyList(),anyString()))
                .thenReturn(batchTask);
        BatchTaskAbility.changeFlowPrompt(event, dataModel, view);

        batchTask.setOperationTypeGroup("1");
        when(QueryDataHelper.get(eq(BatchTask.class),anyList(),anyString()))
                .thenReturn(batchTask);
        BatchTaskAbility.changeFlowPrompt(event, dataModel, view);
        Assert.assertNotNull(batchTask);
    }

    @Test
    public void getPageBatchTask() {
        IDataModel mock = mock(IDataModel.class);
        IDataEntity iDataEntity = PowerMockito.mock(IDataEntity.class);
        when(mock.getRootDataEntity()).thenReturn(iDataEntity);
        when(iDataEntity.getPkValue()).thenReturn("111");
        mock.setValue(URGENT_FLAG, TextValuePairHelper.buildList("Y", "Y", "Y"));

        when(mock.getValue(URGENT_FLAG)).thenReturn(new Object());
        when(TextValuePairHelper.getValue(eq(Object.class)))
                .thenReturn("Y");
        Assert.assertThrows(NullPointerException.class, ()
                -> BatchTaskAbility.getPageBatchTask(mock));
    }

    private static final long CURRENT_TIME = 1625097600000L;
    private static final long SEVEN_DAYS_LATER = CURRENT_TIME + 7 * 24 * 60 * 60 * 1000L;
    @Test
    public void testIsRevokeAccessible_AllConditionsMet() {
        // Mock ContextHelper and System
        PowerMockito.mockStatic(ContextHelper.class);
        PowerMockito.mockStatic(System.class);
        when(ContextHelper.getEmpNo()).thenReturn("EMP123");

        // Create test data
        ApproveRecord record = Mockito.mock(ApproveRecord.class);
        Date approvalDate = new Date();
        when(record.getApprovalDate()).thenReturn(approvalDate);
        when(record.getApprover()).thenReturn("EMP123");

        // Test
        boolean result = BatchTaskAbility.isRevokeAccessible(
                record,
                AssignmentStatusEnum.CLOSE,
                ResultReviewFlagEnum.RESULT_REVIEW_PASSED.name());

        assertTrue(result);
    }

    @Test
    public void testIsRevokeAccessible_NullRecord() {
        boolean result = BatchTaskAbility.isRevokeAccessible(
                null,
                AssignmentStatusEnum.CLOSE,
                ResultReviewFlagEnum.RESULT_REVIEW_PASSED.name());

        assertFalse(result);
    }

    @Test
    public void testIsRevokeAccessible_NullApprovalDate() {
        ApproveRecord record = Mockito.mock(ApproveRecord.class);
        when(record.getApprovalDate()).thenReturn(null);

        boolean result = BatchTaskAbility.isRevokeAccessible(
                record,
                AssignmentStatusEnum.CLOSE,
                ResultReviewFlagEnum.RESULT_REVIEW_PASSED.name());

        assertFalse(result);
    }

    @Test
    public void testIsRevokeAccessible_WrongStatus() {
        PowerMockito.mockStatic(ContextHelper.class);
        when(ContextHelper.getEmpNo()).thenReturn("EMP123");

        ApproveRecord record = Mockito.mock(ApproveRecord.class);
        when(record.getApprovalDate()).thenReturn(new Date());
        when(record.getApprover()).thenReturn("EMP123");

        boolean result = BatchTaskAbility.isRevokeAccessible(
                record,
                AssignmentStatusEnum.EXECUTE,
                ResultReviewFlagEnum.RESULT_REVIEW_PASSED.name());

        assertFalse(result);
    }

    @Test
    public void testIsRevokeAccessible_WrongReviewFlag() {
        PowerMockito.mockStatic(ContextHelper.class);
        when(ContextHelper.getEmpNo()).thenReturn("EMP123");

        ApproveRecord record = Mockito.mock(ApproveRecord.class);
        when(record.getApprovalDate()).thenReturn(new Date());
        when(record.getApprover()).thenReturn("EMP123");

        boolean result = BatchTaskAbility.isRevokeAccessible(
                record,
                AssignmentStatusEnum.CLOSE,
                "WRONG_FLAG");

        assertFalse(result);
    }

    @Test
    public void testIsRevokeAccessible_WrongApprover() {
        PowerMockito.mockStatic(ContextHelper.class);
        when(ContextHelper.getEmpNo()).thenReturn("EMP123");

        ApproveRecord record = Mockito.mock(ApproveRecord.class);
        when(record.getApprovalDate()).thenReturn(new Date());
        when(record.getApprover()).thenReturn("DIFFERENT_EMP");

        boolean result = BatchTaskAbility.isRevokeAccessible(
                record,
                AssignmentStatusEnum.CLOSE,
                ResultReviewFlagEnum.RESULT_REVIEW_PASSED.name());

        assertFalse(result);
    }

    @Test
    public void testIsRevokeAccessible_ExpiredTime() {
        PowerMockito.mockStatic(ContextHelper.class);
        PowerMockito.mockStatic(System.class);
        when(ContextHelper.getEmpNo()).thenReturn("EMP123");
        when(System.currentTimeMillis()).thenReturn(SEVEN_DAYS_LATER + 1000);

        ApproveRecord record = Mockito.mock(ApproveRecord.class);
        Date approvalDate = new Date(CURRENT_TIME);
        when(record.getApprovalDate()).thenReturn(approvalDate);
        when(record.getApprover()).thenReturn("EMP123");

        boolean result = BatchTaskAbility.isRevokeAccessible(
                record,
                AssignmentStatusEnum.CLOSE,
                ResultReviewFlagEnum.RESULT_REVIEW_PASSED.name());

        assertFalse(result);
    }
}
