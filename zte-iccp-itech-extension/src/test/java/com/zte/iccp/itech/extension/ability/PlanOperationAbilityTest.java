package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.TriggerTypeEnum;
import com.zte.iccp.itech.extension.domain.model.PlanOperationOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.PlanOperationAssignment;
import com.zte.iccp.itech.extension.domain.model.subentity.PlanBatchSummary;
import com.zte.iccp.itech.extension.domain.model.subentity.PlanOperationObject;
import com.zte.iccp.itech.extension.domain.model.subentity.PlanOperator;
import com.zte.iccp.itech.extension.domain.model.vo.ChangeOrderSave;
import com.zte.iccp.itech.zlic.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.*;

/**
 * <AUTHOR>
 * @create 2025/7/25 下午2:48
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ConfigHelper.class, ContextHelper.class, SaveDataHelper.class, QueryDataHelper.class,
        TaskNoHelper.class, EmployeeHelper.class, FlowHelper.class, MsgUtils.class})
public class PlanOperationAbilityTest {

    @InjectMocks
    private PlanOperationAbility ability;

    @Before
    public void setUp() throws Exception {
        mockStatic(ConfigHelper.class);
        mockStatic(ContextHelper.class);
        mockStatic(SaveDataHelper.class);
        mockStatic(QueryDataHelper.class);
        mockStatic(TaskNoHelper.class);
        mockStatic(EmployeeHelper.class);
        mockStatic(FlowHelper.class);
        mockStatic(MsgUtils.class);
    }

    @Test
    public void get() {
        Assert.assertNull(ability.get("1", new ArrayList<>()));
    }

    @Test
    public void planOrder2ChangeOrder() {
        PlanOperationOrder plan = new  PlanOperationOrder();
        plan.setTriggerType(TriggerTypeEnum.ENGINEERING_DELIVERY);
        when(QueryDataHelper.get(eq(PlanOperationOrder.class), anyList(), anyString()))
                .thenReturn(plan);
        ability.planOrder2ChangeOrder("1");

        plan.setTimeZone(TimeZoneEnum.BEIJING);
        when(QueryDataHelper.get(eq(PlanOperationOrder.class), anyList(), anyString()))
                .thenReturn(plan);
        PlanBatchSummary summary = mock(PlanBatchSummary.class);
        when(QueryDataHelper.query(eq(PlanBatchSummary.class), anyList(), anyString()))
                .thenReturn(Lists.newArrayList(summary));

        PlanOperationObject object = mock(PlanOperationObject.class);
        when(QueryDataHelper.query(eq(PlanOperationObject.class), anyList(), anyString()))
                .thenReturn(Lists.newArrayList(object));

        PlanOperator operator = mock(PlanOperator.class);
        when(QueryDataHelper.query(eq(PlanOperator.class), anyList(), anyString()))
                .thenReturn(Lists.newArrayList(operator));
        Assert.assertNotNull(ability.planOrder2ChangeOrder("1"));
    }


    @Test
    public void planAssignment2Change() {
        PlanOperationAssignment assignment = mock(PlanOperationAssignment.class);
        ChangeOrderSave changeOrder = mock(ChangeOrderSave.class);
        ability.saveChangeAssignment(assignment, changeOrder);
        Assert.assertNotNull(changeOrder);
    }

    @Test
    public void revokeBeforeCheck() {
        // 1.操作用户不为责任人
        when(ContextHelper.getEmpNo()).thenReturn("user1");
        when(EmployeeHelper.getEpmUIID(any())).thenReturn(Lists.newArrayList("user2"));

        Assignment assignment = new Assignment();
        assignment.setAssignmentStatus("2");
        List<Employee> employees = new ArrayList<>();
        Employee employee = new Employee();
        employee.setEmpUIID("user2");
        assignment.setResponsibleEmployee(employees);
        assignment.setEntityId("1");
        boolean approvedFlag = ability.revokeBeforeCheck(assignment);
        Assert.assertFalse(approvedFlag);

        // 2.操作用户为责任人，但是单据状态不是审核中
        employee.setEmpUIID("user1");
        when(ContextHelper.getEmpNo()).thenReturn("user1");
        when(EmployeeHelper.getEpmUIID(any())).thenReturn(Lists.newArrayList("user1"));
        approvedFlag = ability.revokeBeforeCheck(assignment);
        Assert.assertFalse(approvedFlag);

        // 3.正常场景
        assignment.setAssignmentStatus("3");
        when(ContextHelper.getEmpNo()).thenReturn("user1");
        when(EmployeeHelper.getEpmUIID(any())).thenReturn(Lists.newArrayList("user1"));
        approvedFlag = ability.revokeBeforeCheck(assignment);
        Assert.assertTrue(approvedFlag);
    }

    @Test
    public void revokeTest() {
        List<TextValuePair> assignmentType = new ArrayList<>();
        TextValuePair textValuePair = new TextValuePair();
        textValuePair.setValue("10");
        assignmentType.add(textValuePair);

        Assignment assignment = new Assignment();
        assignment.setEntityId("1");
        assignment.setAssignmentType(assignmentType);
        assignment.setId("1");

        ability.revoke(assignment);
        Assert.assertNotNull(assignment);
    }

}