package com.zte.iccp.itech.extension.common.utils;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.ddm.domain.helper.utils.PropertyValueConvertUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * ComponentUtils测试类
 * <AUTHOR> Assistant
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({PropertyValueConvertUtil.class, JsonUtils.class})
public class ComponentUtilsTest {

    private IDataModel mockDataModel;

    @Before
    public void setUp() {
        mockStatic(PropertyValueConvertUtil.class);
        mockStatic(JsonUtils.class);
        mockDataModel = PowerMockito.mock(IDataModel.class);
    }

    @Test
    public void testGetEmployeeComponentInfo() {
        // 测试场景1：正常数据
        String fieldKey = "employeeField";
        
        // 准备测试数据
        List<Map<String, Object>> employeeMapList = Lists.newArrayList();
        Map<String, Object> empMap1 = new HashMap<>();
        empMap1.put("empUIID", "emp-1");
        empMap1.put("empName", "Employee 1");
        empMap1.put("orgNamePath", "Org Path 1");
        employeeMapList.add(empMap1);

        Map<String, Object> empMap2 = new HashMap<>();
        empMap2.put("empUIID", "emp-2");
        empMap2.put("empName", "Employee 2");
        empMap2.put("orgNamePath", "Org Path 2");
        employeeMapList.add(empMap2);

        // Mock PropertyValueConvertUtil.getListMap
        when(PropertyValueConvertUtil.getListMap(any())).thenReturn(employeeMapList);

        // 准备期望的Employee对象
        List<Employee> expectedEmployees = Lists.newArrayList();
        Employee emp1 = new Employee();
        emp1.setEmpUIID("emp-1");
        emp1.setEmpName("Employee 1");
        emp1.setOrgNamePath("Org Path 1");
        expectedEmployees.add(emp1);

        Employee emp2 = new Employee();
        emp2.setEmpUIID("emp-2");
        emp2.setEmpName("Employee 2");
        emp2.setOrgNamePath("Org Path 2");
        expectedEmployees.add(emp2);

        // Mock JsonUtils.parseArray
        when(JsonUtils.parseArray(anyString(), eq(Employee.class))).thenReturn(Collections.singletonList(expectedEmployees));
        when(JsonUtils.toJsonString(any())).thenReturn("[{\"empUIID\":\"emp-1\",\"empName\":\"Employee 1\"},{\"empUIID\":\"emp-2\",\"empName\":\"Employee 2\"}]");

        // 执行测试
        List<Employee> result = ComponentUtils.getEmployeeComponentInfo(mockDataModel, fieldKey);

        // 验证结果
        Assert.assertNotNull(result);

        // 测试场景2：空数据
        when(PropertyValueConvertUtil.getListMap(any())).thenReturn(Lists.newArrayList());
        
        List<Employee> emptyResult = ComponentUtils.getEmployeeComponentInfo(mockDataModel, fieldKey);
        
        Assert.assertNotNull(emptyResult);
        Assert.assertTrue(emptyResult.isEmpty());

        // 测试场景3：null数据
        when(PropertyValueConvertUtil.getListMap(any())).thenReturn(null);
        
        List<Employee> nullResult = ComponentUtils.getEmployeeComponentInfo(mockDataModel, fieldKey);
        
        Assert.assertNotNull(nullResult);
        Assert.assertTrue(nullResult.isEmpty());
    }

    @Test
    public void testGetEmployeeComponentInfoWithRowIndex() {
        // 测试场景1：正常数据（带行索引）
        String fieldKey = "employeeField";
        Integer rowIndex = 0;
        
        // 准备测试数据
        List<Map<String, Object>> employeeMapList = Lists.newArrayList();
        Map<String, Object> empMap = new HashMap<>();
        empMap.put("empUIID", "emp-row-1");
        empMap.put("empName", "Row Employee 1");
        empMap.put("orgNamePath", "Row Org Path 1");
        employeeMapList.add(empMap);

        // Mock PropertyValueConvertUtil.getListMap with rowIndex
        when(PropertyValueConvertUtil.getListMap(mockDataModel.getValue(fieldKey, rowIndex))).thenReturn(employeeMapList);

        // 准备期望的Employee对象
        List<Employee> expectedEmployees = Lists.newArrayList();
        Employee emp = new Employee();
        emp.setEmpUIID("emp-row-1");
        emp.setEmpName("Row Employee 1");
        emp.setOrgNamePath("Row Org Path 1");
        expectedEmployees.add(emp);

        // Mock JsonUtils
        when(JsonUtils.parseArray(anyString(), eq(Employee.class))).thenReturn(Collections.singletonList(expectedEmployees));
        when(JsonUtils.toJsonString(any())).thenReturn("[{\"empUIID\":\"emp-row-1\",\"empName\":\"Row Employee 1\"}]");

        // 执行测试
        List<Employee> result = ComponentUtils.getEmployeeComponentInfo(mockDataModel, fieldKey, rowIndex);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());

        // 测试场景2：空数据（带行索引）
        when(PropertyValueConvertUtil.getListMap(mockDataModel.getValue(fieldKey, rowIndex))).thenReturn(Lists.newArrayList());
        
        List<Employee> emptyResult = ComponentUtils.getEmployeeComponentInfo(mockDataModel, fieldKey, rowIndex);
        
        Assert.assertNotNull(emptyResult);
        Assert.assertTrue(emptyResult.isEmpty());

        // 测试场景3：null数据（带行索引）
        when(PropertyValueConvertUtil.getListMap(mockDataModel.getValue(fieldKey, rowIndex))).thenReturn(null);
        
        List<Employee> nullResult = ComponentUtils.getEmployeeComponentInfo(mockDataModel, fieldKey, rowIndex);
        
        Assert.assertNotNull(nullResult);
        Assert.assertTrue(nullResult.isEmpty());

        // 测试场景4：不同行索引
        Integer rowIndex2 = 1;
        Map<String, Object> empMap2 = new HashMap<>();
        empMap2.put("empUIID", "emp-row-2");
        empMap2.put("empName", "Row Employee 2");
        List<Map<String, Object>> employeeMapList2 = Lists.newArrayList(empMap2);

        when(PropertyValueConvertUtil.getListMap(mockDataModel.getValue(fieldKey, rowIndex2))).thenReturn(employeeMapList2);

        Employee emp2 = new Employee();
        emp2.setEmpUIID("emp-row-2");
        emp2.setEmpName("Row Employee 2");
        List<Employee> expectedEmployees2 = Lists.newArrayList(emp2);

        when(JsonUtils.parseArray(anyString(), eq(Employee.class))).thenReturn(Collections.singletonList(expectedEmployees2));

        List<Employee> result2 = ComponentUtils.getEmployeeComponentInfo(mockDataModel, fieldKey, rowIndex2);

        Assert.assertNotNull(result2);
        Assert.assertEquals(1, result2.size());
    }
}
