package com.zte.iccp.itech.extension.ability;

import com.zte.iccp.itech.extension.ability.changeorder.BatchTaskAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.domain.enums.IdopCancelEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.vo.ChangeOrderSave;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.IdopChangeOrderDto;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.NetworkOfficeDTO;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.TimeConflictDTO;
import com.zte.iccp.itech.extension.spi.client.CrmClient;
import com.zte.iccp.itech.extension.spi.client.EmdmClient;
import com.zte.iccp.itech.extension.spi.client.IdopClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.LookupValue;
import com.zte.iccp.itech.extension.spi.model.ProductClassificationDto;
import com.zte.iccp.itech.extension.spi.model.crm.BasicCustomerInfo;
import com.zte.iccp.itech.extension.spi.model.emdm.vo.EmdmAreaDetail;
import com.zte.iccp.itech.extension.spi.model.nis.DeviceLocation;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.extension.spi.model.nis.OrganizationTreeVo;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.paas.lcap.component.util.BillNumberGenerateUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @create 2025/5/19 上午10:53
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ConfigHelper.class, ContextHelper.class, CrmClient.class, NisAbility.class, EmdmClient.class, IdopClient.class,
        LookupValueHelper.class, BillNumberGenerateUtil.class, TextValuePairHelper.class, ChangeOrderAbility.class,
        TechnicalSolutionCheckAbility.class, SaveDataHelper.class, TransactionHelper.class, AssignmentAbility.class,
        QueryDataHelper.class, BatchTaskAbility.class, NisClient.class})
public class IdopAbilityTest {

    @InjectMocks
    private IdopAbility idopAbility;

    @Before
    public void setUp() {
        mockStatic(ConfigHelper.class);
        mockStatic(ContextHelper.class);
        mockStatic(NisAbility.class);
        mockStatic(EmdmClient.class);
        mockStatic(CrmClient.class);
        mockStatic(IdopClient.class);

        mockStatic(LookupValueHelper.class);
        mockStatic(BillNumberGenerateUtil.class);
        mockStatic(TextValuePairHelper.class);
        mockStatic(ChangeOrderAbility.class);
        mockStatic(TechnicalSolutionCheckAbility.class);
        mockStatic(SaveDataHelper.class);
        mockStatic(TransactionHelper.class);
        mockStatic(AssignmentAbility.class);
        mockStatic(QueryDataHelper.class);
        mockStatic(BatchTaskAbility.class);
        mockStatic(NisClient.class);
        when(ConfigHelper.get(anyString())).thenReturn("1");
    }

    @Test
    public void createChangeOrder() {
        IdopChangeOrderDto dto = build();
        PageRows<NisNetwork> pageRows = new PageRows<>();
        PowerMockito.when(NisClient.queryNisNetworkList(any())).thenReturn(pageRows);
        idopAbility.createChangeOrder(dto);

        dto.setIsGovEnt(true);
        Assert.assertNotNull(idopAbility.createChangeOrder(dto));
    }

    @Test
    public void updateChangeOrder() {
        IdopChangeOrderDto dto = build();
        PageRows<NisNetwork> pageRows = new PageRows<>();
        PowerMockito.when(NisClient.queryNisNetworkList(any())).thenReturn(pageRows);
        dto.setChangeOrderId("1");
        Assignment assignment = new Assignment();
        assignment.setAssignmentStatus("1");
        when(AssignmentAbility.querySpecificTypeAssignment(anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(Assignment.class)))
                .thenReturn(assignment);
        ChangeOrderSave save = new ChangeOrderSave();
        when(QueryDataHelper.queryOne(eq(ChangeOrderSave.class), anyList(), anyList()))
                .thenReturn(save);
        idopAbility.updateChangeOrder(dto);

        dto.setIsGovEnt(true);
        idopAbility.updateChangeOrder(dto);
        Assert.assertNotNull(dto);
    }


    private IdopChangeOrderDto build() {
        IdopChangeOrderDto dto = new IdopChangeOrderDto();
        dto.setProduct("1");
        dto.setDepartment("1");
        dto.setCountry("1");
        dto.setCustomerName("1");
        dto.setIsGdpr(false);
        dto.setIsEmergencyOperation(false);
        dto.setIsFirstApplication(false);
        dto.setIsNeedAuthorizationFile(false);

        NetworkOfficeDTO networkOfficeDTO = new NetworkOfficeDTO();
        networkOfficeDTO.setNetworkId("testNetworkId");
        dto.setNetwork(Lists.newArrayList(networkOfficeDTO));

        Map<String, ProductClassificationDto> map = new HashMap<>();
        ProductClassificationDto pro = new ProductClassificationDto();
        map.put("1", pro);
        when(NisAbility.getProductModelMap(anyList(), anyList()))
                .thenReturn(map);

        OrganizationTreeVo org = new OrganizationTreeVo();
        when(NisAbility.getOrganization(anyString()))
                .thenReturn(org);

        EmdmAreaDetail detail = new EmdmAreaDetail();
        when(EmdmClient.queryAreaDetailByName(anyString(), anyString()))
                .thenReturn(Lists.newArrayList(detail));

        Map<String, BasicCustomerInfo> customerNameMap = new HashMap<>();
        BasicCustomerInfo info = new BasicCustomerInfo();
        customerNameMap.put("1", info);
        when(CrmClient.queryBasicCustomerInfo(anyList()))
                .thenReturn(customerNameMap);

        LookupValue value = new LookupValue();
        value.setLookupCode("1");
        when(LookupValueHelper.getLookupValuesZhAndUs(anyString(), anyList()))
                .thenReturn(Lists.newArrayList(value));
        return dto;
    }

    @Test
    public void setProvinceAndCityTest() throws Exception {
        IdopChangeOrderDto dto = new IdopChangeOrderDto();
        ChangeOrderSave changeOrder = new ChangeOrderSave();
        NetworkOfficeDTO networkOfficeDTO = new NetworkOfficeDTO();
        networkOfficeDTO.setNetworkId("testNetworkId");
        dto.setNetwork(Lists.newArrayList(networkOfficeDTO));

        // pageRows为空
        PageRows<NisNetwork> pageRows = new PageRows<>();
        PowerMockito.when(NisClient.queryNisNetworkList(any())).thenReturn(pageRows);
        Whitebox.invokeMethod(IdopAbility.class,
                "setProvinceAndCity", dto, changeOrder);

        // pageRows不为空
        NisNetwork network = new NisNetwork();
        pageRows.setRows(Lists.newArrayList(network));

        // deviceLocations为空
        Whitebox.invokeMethod(IdopAbility.class,
                "setProvinceAndCity", dto, changeOrder);

        // deviceLocations不为空
        DeviceLocation deviceLocation = new DeviceLocation();
        network.setDeviceLocations(Lists.newArrayList(deviceLocation));
        // provinceCode, cityCode为空
        Whitebox.invokeMethod(IdopAbility.class,
                "setProvinceAndCity", dto, changeOrder);

        // provinceCode, cityCode不为空
        deviceLocation.setProvinceStateCode("0012");
        deviceLocation.setCityCode("052415");
        List<EmdmAreaDetail> provinces = Lists.newArrayList();
        // 根据provinceCode\cityCode查询到的数据为空；
        PowerMockito.when(EmdmClient.queryAreaDetailByCode(anyList()))
                        .thenReturn(provinces);
        Whitebox.invokeMethod(IdopAbility.class,
                "setProvinceAndCity", dto, changeOrder);

        // 根据provinceCode\cityCode查询到的数据不为空；
        provinces.add(new EmdmAreaDetail());
        Whitebox.invokeMethod(IdopAbility.class,
                "setProvinceAndCity", dto, changeOrder);
        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }

    @Test
    public void batchUpdateConflict() throws Exception {
        TimeConflictDTO dto = new TimeConflictDTO();
        dto.setChangeOrderId("1");
        dto.setTimeConflict(false);
        Assignment assignment = new Assignment();
        assignment.setAssignmentStatus("1");
        when(AssignmentAbility.querySpecificTypeAssignment(anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(Assignment.class)))
                .thenReturn(assignment);

        ChangeOrder order = new ChangeOrder();
        when(ChangeOrderAbility.get(anyString(), anyList()))
                .thenReturn(order);

        idopAbility.batchUpdateConflict(dto);

        when(BatchTaskAbility.listBatchTasks(anyList(), anyList()))
                .thenReturn(Lists.newArrayList(new BatchTask()));
        Whitebox.invokeMethod(idopAbility, "updateBatchTime"
                , dto, Lists.newArrayList("1"));
        Assert.assertNotNull(dto);
    }

    @Test
    public void syncIdopStatus() {
        String changeOrderId = "1";
        AssignmentTypeEnum assignmentTypeEnum = AssignmentTypeEnum.NETWORK_CHANGE;
        AssignmentStatusEnum syncAssignmentStatusEnum = AssignmentStatusEnum.START;
        IdopCancelEnum syncIdopEnum = IdopCancelEnum.REJECT;
        ChangeOrder changeOrder = new ChangeOrder() {{
            setId("1");
        }};

        // 1.source == null
        when(QueryDataHelper.get(eq(ChangeOrder.class),anyList(),anyString())).thenReturn(changeOrder);
        IdopAbility.syncIdopStatus(changeOrderId, assignmentTypeEnum, syncAssignmentStatusEnum, syncIdopEnum);
        Assert.assertNotNull(changeOrder);

        // 2.source!=null & source!=IDOP
        changeOrder.setSource("1");
        when(QueryDataHelper.get(eq(ChangeOrder.class),anyList(),anyString())).thenReturn(changeOrder);
        IdopAbility.syncIdopStatus(changeOrderId, assignmentTypeEnum, syncAssignmentStatusEnum, syncIdopEnum);
        Assert.assertNotNull(changeOrder);

        // 3.source!=null & source==IDOP
        changeOrder.setSource("IDOP");
        when(QueryDataHelper.get(eq(ChangeOrder.class),anyList(),anyString())).thenReturn(changeOrder);
        IdopAbility.syncIdopStatus(changeOrderId, assignmentTypeEnum, syncAssignmentStatusEnum, syncIdopEnum);
        Assert.assertNotNull(changeOrder);
    }
}