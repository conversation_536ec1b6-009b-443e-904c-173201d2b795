package com.zte.iccp.itech.extension.common.utils;

import com.zte.iccp.itech.zlic.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.reflect.Whitebox;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

import static org.junit.Assert.*;

/**
 * <AUTHOR> 10284287
 * @since 2024/07/11
 */
public class DateUtilsTest {
    /* Started by AICoder, pid:n003ecb88dp630a142510976a09bf41d798797dc */
    @Test
    public void testAddDay() {
        Date date = new Date();
        Integer changeDays = 5;
        Date result = DateUtils.addDay(date, changeDays);
        assertNotNull(result);
        // 可以添加更多的断言来验证结果的正确性，例如：
        // assertEquals(expectedDate, result);
    }

    @Test
    public void testAddDayWithNullDate() {
        Date date = null;
        Integer changeDays = 5;
        Date result = DateUtils.addDay(date, changeDays);
        assertNull(result);
    }
    /* Ended by AICoder, pid:n003ecb88dp630a142510976a09bf41d798797dc */

    /* Started by AICoder, pid:o9b88t1a55db59e1457408eab0918b36e9532720 */
    @Test
    public void testGetSecondsInDay_Normal() {
        Date date = new Date();
        int seconds = DateUtils.getSecondsInDay(date);
        assertTrue(seconds >= 0 && seconds < 24 * 60 * 60);
    }

    @Test
    public void testGetSecondsInDay_BoundaryStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date date = cal.getTime();
        assertEquals(0, DateUtils.getSecondsInDay(date));
    }

    @Test
    public void testGetSecondsInDay_BoundaryEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        Date date = cal.getTime();
        assertEquals(24 * 60 * 60 - 1, DateUtils.getSecondsInDay(date));
    }

    @Test(expected = NullPointerException.class)
    public void testGetSecondsInDay_Null() {
        DateUtils.getSecondsInDay(null);
    }
    /* Ended by AICoder, pid:o9b88t1a55db59e1457408eab0918b36e9532720 */

    /* Started by AICoder, pid:s8f0694058jefe414a290a0650df3c3706228ce6 */
    @Test
    public void testIsOverTimeRange_Overlap() {
        // 测试有重叠部分的情况
        Date start = new Date(2022, 0, 1, 10, 0, 0); // 2022-01-01 10:00:00
        Date end = new Date(2022, 0, 1, 12, 0, 0); // 2022-01-01 12:00:00
        int fromSeconds = 10 * 60 * 60; // 10:00:00 对应的秒数
        int toSeconds = 12 * 60 * 60; // 12:00:00 对应的秒数
        boolean result = DateUtils.isOverTimeRange(start, end, fromSeconds, toSeconds);
        assertTrue(result);
    }

    @Test
    public void testIsOverTimeRange_NoOverlap() {
        // 测试没有重叠部分的情况
        Date start = new Date(2022, 0, 1, 14, 0, 0); // 2022-01-01 14:00:00
        Date end = new Date(2022, 0, 1, 16, 0, 0); // 2022-01-01 16:00:00
        int fromSeconds = 10 * 60 * 60; // 10:00:00 对应的秒数
        int toSeconds = 12 * 60 * 60; // 12:00:00 对应的秒数
        boolean result = DateUtils.isOverTimeRange(start, end, fromSeconds, toSeconds);
        assertFalse(result);
    }

    @Test
    public void testIsOverTimeRange_CrossDate() {
        // 测试跨日期的情况
        Date start = new Date(2022, 0, 1, 22, 0, 0); // 2022-01-01 22:00:00
        Date end = new Date(2022, 0, 2, 2, 0, 0); // 2022-01-02 02:00:00
        int fromSeconds = 22 * 60 * 60; // 22:00:00 对应的秒数
        int toSeconds = 2 * 60 * 60; // 02:00:00 对应的秒数
        boolean result = DateUtils.isOverTimeRange(start, end, fromSeconds, toSeconds);
        assertTrue(result);
    }
    /* Ended by AICoder, pid:s8f0694058jefe414a290a0650df3c3706228ce6 */

    /* Started by AICoder, pid:ub768j39d90688714fc6085a807bdb467a75486b */

    @Test
    public void testIsOverTimeRange_StartBeforeEnd() {
        Date start = new Date(160945920000L); // 2021-01-01 12:00:00
        Date end = new Date(160946280000L); // 2021-01-01 13:00:00
        assertFalse(DateUtils.isOverTimeRange(start, end, 3600, 7200));
    }

    @Test
    public void testIsOverTimeRange_FromAfterTo() {
        Date start = new Date(160945920000L); // 2021-01-01 12:00:00
        Date end = new Date(160946280000L); // 2021-01-01 13:00:00
        assertTrue(DateUtils.isOverTimeRange(start, end, 7200, 3600));
    }

    @Test
    public void testIsOverTimeRange_SameDay() {
        Date start = new Date(160945920000L); // 2021-01-01 12:00:00
        Date end = new Date(160946280000L); // 2021-01-01 13:00:00
        assertFalse(DateUtils.isOverTimeRange(start, end, 3600, 7200));
    }

    @Test
    public void testIsOverTimeRange_NullDates() {
        assertThrows(IllegalArgumentException.class, () -> {
            DateUtils.isOverTimeRange(null, new Date(), 3600, 7200);
        });
        assertThrows(IllegalArgumentException.class, () -> {
            DateUtils.isOverTimeRange(new Date(), null, 3600, 7200);
        });
        assertThrows(IllegalArgumentException.class, () -> {
            DateUtils.isOverTimeRange(new Date(2), new Date(1), 3600, 7200);
        });
    }
    /* Ended by AICoder, pid:ub768j39d90688714fc6085a807bdb467a75486b */

    @Test
    public void testIsOverTimeRange_Over3Day() {
        Date start = new Date(160945920000L); // 2021-01-01 12:00:00
        Date end = new Date(160946280000L + 3 * 24 * 60 * 60 * 1000); // 2021-01-04 13:00:00
        assertTrue(DateUtils.isOverTimeRange(start, end, 7200, 3600));
    }

    /* Started by AICoder, pid:n8b6ak75b357123146440b37a0bee92a88d346be */
    @Test
    public void testIsOverlap() throws Exception {
        // 测试A完全包含B的情况
        assertTrue(Whitebox.invokeMethod(DateUtils.class, "isOverlap", 10, 20, 15, 18));

        // 测试B完全包含A的情况
        assertTrue(Whitebox.invokeMethod(DateUtils.class, "isOverlap", 15, 18, 10, 20));

        // 测试A的起始点在B的区间内的情况
        assertTrue(Whitebox.invokeMethod(DateUtils.class, "isOverlap", 15, 22, 10, 20));

        // 测试A的结束点在B的区间内的情况
        assertTrue(Whitebox.invokeMethod(DateUtils.class, "isOverlap", 1, 18, 10, 20));

        // 测试没有重叠的情况
        assertFalse(Whitebox.invokeMethod(DateUtils.class, "isOverlap", 10, 15, 16, 20));

        // 测试边界情况，即startA == endA的情况
        assertFalse(Whitebox.invokeMethod(DateUtils.class, "isOverlap", 10, 10, 5, 15));

        // 测试边界情况，即startB == endB的情况
        assertFalse(Whitebox.invokeMethod(DateUtils.class, "isOverlap", 5, 15, 10, 10));
    }

    /**
     * 测试 getDaysBetween 方法
     */
    @Test
    public void testGetDaysBetween_NormalCase() {
        // 准备测试数据 - 正好相差1天
        Calendar cal1 = Calendar.getInstance();
        cal1.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
        cal1.set(Calendar.MILLISECOND, 0);
        Date startDate = cal1.getTime();

        Calendar cal2 = Calendar.getInstance();
        cal2.set(2023, Calendar.JANUARY, 2, 0, 0, 0);
        cal2.set(Calendar.MILLISECOND, 0);
        Date endDate = cal2.getTime();

        // 执行测试
        Double result = DateUtils.getDaysBetween(startDate, endDate);

        // 验证结果
        assertNotNull(result);
        assertEquals(1.0, result, 0.001);
    }

    /**
     * 测试 getDaysBetween 方法 - 零天情况
     */
    @Test
    public void testGetDaysBetween_ZeroDays() {
        // 准备测试数据 - 相同时间点
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date date = cal.getTime();

        // 执行测试
        Double result = DateUtils.getDaysBetween(date, date);

        // 验证结果
        assertNotNull(result);
        assertEquals(0.0, result, 0.001);
    }

    /**
     * 测试 getDaysBetween 方法 - 小数天数
     */
    @Test
    public void testGetDaysBetween_FractionalDays() {
        // 准备测试数据 - 相差12小时（0.5天）
        Calendar cal1 = Calendar.getInstance();
        cal1.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
        cal1.set(Calendar.MILLISECOND, 0);
        Date startDate = cal1.getTime();

        Calendar cal2 = Calendar.getInstance();
        cal2.set(2023, Calendar.JANUARY, 1, 12, 0, 0);
        cal2.set(Calendar.MILLISECOND, 0);
        Date endDate = cal2.getTime();

        // 执行测试
        Double result = DateUtils.getDaysBetween(startDate, endDate);

        // 验证结果
        assertNotNull(result);
        assertEquals(0.5, result, 0.001);
    }

    /**
     * 测试 getDaysBetween 方法 - 大数天数
     */
    @Test
    public void testGetDaysBetween_LargeDays() {
        // 准备测试数据 - 相差365天（1年）
        Calendar cal1 = Calendar.getInstance();
        cal1.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
        cal1.set(Calendar.MILLISECOND, 0);
        Date startDate = cal1.getTime();

        Calendar cal2 = Calendar.getInstance();
        cal2.set(2024, Calendar.JANUARY, 1, 0, 0, 0);
        cal2.set(Calendar.MILLISECOND, 0);
        Date endDate = cal2.getTime();

        // 执行测试
        Double result = DateUtils.getDaysBetween(startDate, endDate);

        // 验证结果
        assertNotNull(result);
        // 考虑闰年等因素，保留一位小数的精度进行比较
        assertEquals(365.0, result, 0.1);
    }

    /**
     * 测试 getDaysBetween 方法 - 负数天数
     */
    @Test
    public void testGetDaysBetween_NegativeDays() {
        // 准备测试数据 - 结束日期早于开始日期
        Calendar cal1 = Calendar.getInstance();
        cal1.set(2023, Calendar.JANUARY, 2, 0, 0, 0);
        cal1.set(Calendar.MILLISECOND, 0);
        Date startDate = cal1.getTime();

        Calendar cal2 = Calendar.getInstance();
        cal2.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
        cal2.set(Calendar.MILLISECOND, 0);
        Date endDate = cal2.getTime();

        // 执行测试
        Double result = DateUtils.getDaysBetween(startDate, endDate);

        // 验证结果
        assertNotNull(result);
        assertEquals(-1.0, result, 0.001);
    }

    /**
     * 测试 getDaysBetween 方法 - 一个参数为null
     */
    @Test
    public void testGetDaysBetween_OneNullParam() {
        // 准备测试数据
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
        Date date = cal.getTime();

        // 执行测试1 - startDate为null
        Double result1 = DateUtils.getDaysBetween(null, date);

        // 验证结果1
        assertNull(result1);

        // 执行测试2 - endDate为null
        Double result2 = DateUtils.getDaysBetween(date, null);

        // 验证结果2
        assertNull(result2);
    }

    /**
     * 测试 getDaysBetween 方法 - 两个参数都为null
     */
    @Test
    public void testGetDaysBetween_BothNullParams() {
        // 执行测试
        Double result = DateUtils.getDaysBetween(null, null);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试 getDaysBetween 方法 - 验证精度（保留一位小数）
     */
    @Test
    public void testGetDaysBetween_Precision() {
        // 准备测试数据 - 相差1天7小时30分钟（大约1.31天，应四舍五入为1.3天）
        Calendar cal1 = Calendar.getInstance();
        cal1.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
        cal1.set(Calendar.MILLISECOND, 0);
        Date startDate = cal1.getTime();

        Calendar cal2 = Calendar.getInstance();
        cal2.set(2023, Calendar.JANUARY, 2, 7, 30, 0);
        cal2.set(Calendar.MILLISECOND, 0);
        Date endDate = cal2.getTime();

        // 执行测试
        Double result = DateUtils.getDaysBetween(startDate, endDate);

        // 验证结果
        assertNotNull(result);
        assertEquals(1.3, result, 0.001); // 验证四舍五入到1位小数
    }

    // 测试用例：05:59:59 → false
    @Test
    public void testCheckTime_Before6AM() {
        Date date = createSpecificTime(5, 59, 59);
        assertTrue(DateUtils.checkTime(date));
    }

    // 测试用例：06:00:00 → false（不包含）
    @Test
    public void testCheckTime_Exact6AM() {
        Date date = createSpecificTime(6, 0, 0);
        assertTrue(DateUtils.checkTime(date));
    }

    // 测试用例：06:00:01 → true
    @Test
    public void testCheckTime_After6AMWithSeconds() {
        Date date = createSpecificTime(6, 0, 1);
        assertFalse(DateUtils.checkTime(date));
    }

    // 测试用例：06:01:00 → true
    @Test
    public void testCheckTime_After6AMWithMinutes() {
        Date date = createSpecificTime(6, 1, 0);
        assertFalse(DateUtils.checkTime(date));
    }

    // 测试用例：07:00:00 → true
    @Test
    public void testCheckTime_After7AM() {
        Date date = createSpecificTime(7, 0, 0);
        assertFalse(DateUtils.checkTime(date));
    }

    // 辅助方法：创建指定时分秒的日期对象
    private Date createSpecificTime(int hour, int minute, int second) {
        // 使用系统默认时区构建 LocalTime
        LocalTime time = LocalTime.of(hour, minute, second);
        // 获取当前日期（避免跨天问题）
        LocalDate today = LocalDate.now();
        // 组合为 LocalDateTime 并转换为 Date
        return DateUtils.toDate(today.atTime(time));
    }

    private static Date parseDate(String dateStr) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.parse(dateStr);
    }

    @Test
    public void testEndBefore6() throws Exception {
        Date start = parseDate("2023-07-01 23:00:00");
        Date end = parseDate("2023-07-02 05:59:59");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testEndAt6() throws Exception {
        Date start = parseDate("2023-07-01 23:00:00");
        Date end = parseDate("2023-07-02 06:00:00");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testEndAfter6() throws Exception {
        Date start = parseDate("2023-07-01 23:00:00");
        Date end = parseDate("2023-07-02 06:00:01");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testSameDayStartInNight() throws Exception {
        Date start = parseDate("2023-07-01 05:00:00");
        Date end = parseDate("2023-07-01 07:00:00");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testSameDayStartNotInNight() throws Exception {
        Date start = parseDate("2023-07-01 06:00:01");
        Date end = parseDate("2023-07-01 07:00:00");
        Assert.assertFalse(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testDifferentDays() throws Exception {
        Date start = parseDate("2023-07-01 10:00:00");
        Date end = parseDate("2023-07-02 10:00:00");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testSameDayBothInNight() throws Exception {
        Date start = parseDate("2023-07-01 03:00:00");
        Date end = parseDate("2023-07-01 05:00:00");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testStartAt6() throws Exception {
        Date start = parseDate("2023-07-01 06:00:00");
        Date end = parseDate("2023-07-01 07:00:00");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testEndAt6WithDifferentDays() throws Exception {
        Date start = parseDate("2023-07-01 23:00:00");
        Date end = parseDate("2023-07-02 06:00:00");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
    }

    @Test
    public void testStartBefore6EndNextDayAfter6() throws Exception {
        Date start = parseDate("2023-07-01 05:00:00");
        Date end = parseDate("2023-07-02 07:00:00");
        Assert.assertTrue(DateUtils.isNightTimeOverlap(start, end));
        DateUtils.dateListConverter(Lists.newArrayList(start));
    }
}