package com.zte.iccp.itech.extension.handler.approver;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.common.utils.ProductUtils;
import com.zte.iccp.itech.extension.common.utils.ResponsibleUtils;
import com.zte.iccp.itech.extension.domain.enums.ApprovalTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.DeptTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import org.junit.Assert;
import java.util.Objects;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * GeneralLogicalHandlerImpl测试类
 * <AUTHOR> Assistant
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ApproverConfigAbility.class, NisAbility.class, QueryDataHelper.class, 
                ProductUtils.class, ResponsibleUtils.class})
public class GeneralLogicalHandlerImplTest {

    private GeneralLogicalHandlerImpl handler;

    @Before
    public void setUp() {
        mockStatic(ApproverConfigAbility.class);
        mockStatic(NisAbility.class);
        mockStatic(QueryDataHelper.class);
        mockStatic(ProductUtils.class);
        mockStatic(ResponsibleUtils.class);
        
        handler = new GeneralLogicalHandlerImpl(ApprovalTypeEnum.REMOTE_CENTER_SOLUTION);
    }

    @Test
    public void testGetApprovers() {
        // 测试场景1：核心网产品（CCN）
        ChangeOrder changeOrder1 = new ChangeOrder();
        changeOrder1.setId("change-order-1");
        changeOrder1.setResponsibleDept("responsible-dept-1");
        changeOrder1.setProductCategory("ccn-product");
        changeOrder1.setLogicalNe("logical-ne-1");
        changeOrder1.setIsGovEnt(BoolEnum.Y);

        // Mock ProductUtils
        when(ProductUtils.getTeam(anyString())).thenReturn("team-1");
        when(ProductUtils.getLine(anyString())).thenReturn("line-1");
        when(ProductUtils.getMain(anyString())).thenReturn("main-1");
        when(ProductUtils.isCcn(anyString())).thenReturn(true);

        // Mock ResponsibleUtils
        when(ResponsibleUtils.getSales(anyString())).thenReturn("sales-1");
        when(ResponsibleUtils.getRegion(anyString())).thenReturn("region-1");
        when(ResponsibleUtils.getDeptType(anyString())).thenReturn(DeptTypeEnum.INNER);

        // Mock ApproverConfigAbility
        ApproverConfiguration expectedConfig1 = new ApproverConfiguration();
        expectedConfig1.setId("config-1");
        when(ApproverConfigAbility.getApprovalConfiguration(any(ApproverConfiguration.class), anyString(), eq(4)))
                .thenReturn(expectedConfig1);

        // 执行测试
        ApproverConfiguration result1 = handler.getApprovers(changeOrder1);

        // 验证结果
        Assert.assertNotNull(result1);
        Assert.assertEquals("config-1", result1.getId());

        // 测试场景2：核心网产品，片区查询失败，回退到营销查询
        when(ApproverConfigAbility.getApprovalConfiguration(any(ApproverConfiguration.class), anyString(), eq(4)))
                .thenReturn(null) // 第一次返回null
                .thenReturn(expectedConfig1); // 第二次返回配置

        ApproverConfiguration result2 = handler.getApprovers(changeOrder1);

        Assert.assertNotNull(result2);
        Assert.assertEquals("config-1", result2.getId());

        // 测试场景3：非核心网产品，有操作对象
        ChangeOrder changeOrder3 = new ChangeOrder();
        changeOrder3.setId("change-order-3");
        changeOrder3.setResponsibleDept("responsible-dept-3");
        changeOrder3.setProductCategory("non-ccn-product");

        when(ProductUtils.isCcn("non-ccn-product")).thenReturn(false);

        // Mock操作对象查询
        List<OperationObject> operationObjects = Lists.newArrayList();
        OperationObject opObj1 = new OperationObject();
        opObj1.setProductModel("product-model-1");
        TextValuePair isMainProduct = new TextValuePair();
        isMainProduct.setValue("Y");
        opObj1.setIsMainProduct(Lists.newArrayList(isMainProduct));
        operationObjects.add(opObj1);

        OperationObject opObj2 = new OperationObject();
        opObj2.setProductModel("product-model-2");
        operationObjects.add(opObj2);

        when(QueryDataHelper.query(eq(OperationObject.class), anyList(), anyString()))
                .thenReturn(Lists.newArrayList(opObj1, opObj2));

        // Mock NisAbility
        List<String> productModelIdPaths = Lists.newArrayList("product-model-id-path-1");
        when(NisAbility.queryProductModelIdPath(anyList())).thenReturn(productModelIdPaths);

        // Mock ApproverConfigAbility for non-CCN
        ApproverConfiguration expectedConfig3 = new ApproverConfiguration();
        expectedConfig3.setId("config-3");
        when(ApproverConfigAbility.getApprovalConfiguration(any(ApproverConfiguration.class), anyString(), eq(3)))
                .thenReturn(expectedConfig3);

        ApproverConfiguration result3 = handler.getApprovers(changeOrder3);

        Assert.assertNotNull(result3);
        Assert.assertEquals("config-3", result3.getId());

        // 测试场景4：非核心网产品，无操作对象
        when(QueryDataHelper.query(eq(OperationObject.class), anyList(), anyString()))
                .thenReturn(Lists.newArrayList());

        ApproverConfiguration expectedConfig4 = new ApproverConfiguration();
        expectedConfig4.setId("config-4");
        when(ApproverConfigAbility.getApprovalConfiguration(any(ApproverConfiguration.class), anyString(), eq(0)))
                .thenReturn(expectedConfig4);

        ApproverConfiguration result4 = handler.getApprovers(changeOrder3);

        Assert.assertNotNull(result4);
        Assert.assertEquals("config-4", result4.getId());

        // 测试场景5：非核心网产品，操作对象无主产品标识
        OperationObject opObjNoMain = new OperationObject();
        opObjNoMain.setProductModel("product-model-no-main");
        opObjNoMain.setIsMainProduct(Lists.newArrayList()); // 空的主产品标识
        when(QueryDataHelper.query(eq(OperationObject.class), anyList(), anyString()))
                .thenReturn(Lists.newArrayList(opObjNoMain));

        // 应该使用第一个产品型号
        when(NisAbility.queryProductModelIdPath(Lists.newArrayList("product-model-no-main")))
                .thenReturn(Lists.newArrayList("fallback-product-model-id-path"));

        ApproverConfiguration result5 = handler.getApprovers(changeOrder3);

        Assert.assertNotNull(result5);

        // 测试场景6：非核心网产品，片区查询失败，回退到营销查询
        when(ApproverConfigAbility.getApprovalConfiguration(any(ApproverConfiguration.class), anyString(), eq(3)))
                .thenReturn(null) // 第一次返回null
                .thenReturn(expectedConfig3); // 第二次返回配置

        ApproverConfiguration result6 = handler.getApprovers(changeOrder3);

        Assert.assertNotNull(result6);
        Assert.assertEquals("config-3", result6.getId());

        // 测试场景7：国际代表处（非内部）
        ChangeOrder changeOrderIntl = new ChangeOrder();
        changeOrderIntl.setId("change-order-intl");
        changeOrderIntl.setResponsibleDept("intl-dept");
        changeOrderIntl.setProductCategory("intl-product");

        when(ResponsibleUtils.getDeptType("intl-dept")).thenReturn(DeptTypeEnum.INTER);
        when(ProductUtils.isCcn("intl-product")).thenReturn(false);

        when(QueryDataHelper.query(eq(OperationObject.class), anyList(), anyString()))
                .thenReturn(Lists.newArrayList());

        ApproverConfiguration result7 = handler.getApprovers(changeOrderIntl);

        Assert.assertNotNull(result7);
    }
}
