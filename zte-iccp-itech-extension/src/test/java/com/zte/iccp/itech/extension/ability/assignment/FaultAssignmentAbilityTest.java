package com.zte.iccp.itech.extension.ability.assignment;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.iccp.itech.extension.ability.AssignmentAbility;
import com.zte.iccp.itech.extension.ability.FaultManagementOrderAbility;
import com.zte.iccp.itech.extension.ability.ObjectLinkInstanceAbility;
import com.zte.iccp.itech.extension.ability.changeorder.ApproverAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.*;
import com.zte.iccp.itech.extension.domain.constant.CidConstants;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.TechnologyManagementAssignment;
import com.zte.iccp.itech.extension.domain.model.kafka.ServiceRequestMessage;
import com.zte.iccp.itech.extension.spi.client.CscClient;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.PdmClient;
import com.zte.iccp.itech.extension.spi.model.csc.vo.BasicInfoVo;
import com.zte.iccp.itech.extension.spi.model.csc.vo.CscDetailInfoVo;
import com.zte.iccp.itech.extension.spi.model.csc.vo.FaultInfoVo;
import com.zte.iccp.itech.extension.spi.model.csc.vo.SupportOrgInfoVo;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.pdm.dto.vo.ProductInfoByLevelVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.ProcessRecordVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomNodeVo;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.paas.lcap.common.api.metadata.engine.manage.model.ObjectInstanceLinkDO;
import com.zte.paas.lcap.common.spring.ApplicationContextHolder;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.powermock.api.mockito.PowerMockito.mockStatic;


@RunWith(PowerMockRunner.class)
@PrepareForTest({ConfigHelper.class, ContextHelper.class, ApplicationContextHolder.class, AsyncExecuteUtils.class,
        CacheUtils.class, MsgUtils.class, ProductUtils.class, FlowHelper.class, FlowServiceHelper.class,
        SaveDataHelper.class, AssignmentAbility.class, ApproverAbility.class, FaultManagementOrderAbility.class,
        ObjectLinkInstanceAbility.class, ApprovalFlowClient.class, CscClient.class, HrClient.class, PdmClient.class})
public class FaultAssignmentAbilityTest {

    @Before
    public void setUp() {
        mockStatic(ConfigHelper.class);
        PowerMockito.when(ConfigHelper.getRaw(anyString())).thenReturn("10");

        mockStatic(ContextHelper.class);
        PowerMockito.when(ContextHelper.getAppId()).thenReturn("appId");
        PowerMockito.when(ContextHelper.getUacAppId()).thenReturn("appCode");
        PowerMockito.when(ContextHelper.getEmpNo()).thenReturn("userId");

        mockStatic(ApplicationContextHolder.class);

        mockStatic(AsyncExecuteUtils.class);
        mockStatic(CacheUtils.class);
        mockStatic(MsgUtils.class);
        mockStatic(ProductUtils.class);

        mockStatic(FlowHelper.class);
        mockStatic(FlowServiceHelper.class);
        mockStatic(SaveDataHelper.class);

        mockStatic(AssignmentAbility.class);
        mockStatic(ApproverAbility.class);
        mockStatic(FaultManagementOrderAbility.class);
        mockStatic(ObjectLinkInstanceAbility.class);

        mockStatic(ApprovalFlowClient.class);
        mockStatic(CscClient.class);
        mockStatic(HrClient.class);
        mockStatic(PdmClient.class);
    }

    @Test
    public void consumeServiceRequestMsgTest_create_checkError() {
        PowerMockito
                .when(AssignmentAbility.queryByAssignmentCode(anyString(), anyList(), eq(Assignment.class)))
                .thenReturn(null);

        // 1.单号异常
        ServiceRequestMessage invalidCodeMsg = new ServiceRequestMessage();
        invalidCodeMsg.setRequestNo("OP20250723000400");
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidCodeMsg));

        // 2.详情缺失
        ServiceRequestMessage nullDetailMsg = new ServiceRequestMessage();
        nullDetailMsg.setRequestNo("CI20250723000400");

        PowerMockito.when(CscClient.getCscDetail(any())).thenReturn(null);
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(nullDetailMsg));

        CscDetailInfoVo defaultDetail = new CscDetailInfoVo();
        PowerMockito.when(CscClient.getCscDetail(any())).thenReturn(defaultDetail);
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(nullDetailMsg));

        BasicInfoVo basicInfo = new BasicInfoVo();
        defaultDetail.setBasicInfo(basicInfo);
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(nullDetailMsg));

        FaultInfoVo faultInfo = new FaultInfoVo();
        basicInfo.setFaultInfo(faultInfo);
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(nullDetailMsg));

        SupportOrgInfoVo supportOrgInfo = new SupportOrgInfoVo();
        defaultDetail.setSupportOrgInfo(supportOrgInfo);
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidCodeMsg));

        // 故障等级异常
        ServiceRequestMessage invalidFaultLevelMsg = new ServiceRequestMessage();
        invalidFaultLevelMsg.setRequestNo("EI20250723000400");
        invalidFaultLevelMsg.setSeverityCn("严重");
        invalidFaultLevelMsg.setSeverityEn("Serious");

        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidFaultLevelMsg));

        // PDM 信息异常
        ServiceRequestMessage nullPdmMsg = new ServiceRequestMessage();
        nullPdmMsg.setRequestNo("PI20250723000400");
        nullPdmMsg.setSeverityCn("关键一级");
        nullPdmMsg.setSeverityEn("Critical Level 1");

        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(nullPdmMsg));

        ServiceRequestMessage invalidPdmMsg = new ServiceRequestMessage();
        invalidPdmMsg.setRequestNo("CI20250723000400");
        invalidPdmMsg.setSeverityCn("关键二级");
        invalidPdmMsg.setSeverityEn("Critical Level 2");
        invalidPdmMsg.setPdmNo("errorPdmNo");

        PowerMockito.when(CacheUtils.get(eq(CidConstants.PDM_PRODUCT_MODEL), any(TypeReference.class))).thenReturn(Lists.newArrayList());
        PowerMockito.when(PdmClient.queryProductInfo(anyList())).thenReturn(Lists.newArrayList());
        PowerMockito.when(ProductUtils.getPdmProductTeamAndLineIdMap()).thenReturn(new HashMap<>());

        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidPdmMsg));

        PowerMockito
                .when(PdmClient.queryProductInfo(anyList()))
                .thenReturn(Lists.newArrayList(new ProductInfoByLevelVo()));
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidPdmMsg));

        PowerMockito
                .when(CacheUtils.get(eq(CidConstants.PDM_PRODUCT_MODEL), any(TypeReference.class)))
                .thenReturn(Lists.newArrayList(new ProductInfoByLevelVo()));
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidPdmMsg));

        // 代表处 信息异常
        ProductInfoByLevelVo productModel = new ProductInfoByLevelVo();
        productModel.setItemNo("productModel");
        productModel.setFullPath("productTeam/productLine/productMainCategory/productSubCategory/productModel");
        PowerMockito
                .when(CacheUtils.get(eq(CidConstants.PDM_PRODUCT_MODEL), any(TypeReference.class)))
                .thenReturn(Lists.newArrayList(productModel));
        PowerMockito
                .when(ProductUtils.getPdmProductTeamAndLineIdMap())
                .thenReturn(MapUtils.newHashMap(
                        "productTeam", "productTeam",
                        "productLine", "productLine"));

        ServiceRequestMessage invalidOrganizationMsg = new ServiceRequestMessage();
        invalidOrganizationMsg.setRequestNo("CI20250723000400");
        invalidOrganizationMsg.setSeverityCn("关键二级");
        invalidOrganizationMsg.setSeverityEn("Critical Level 2");
        invalidOrganizationMsg.setPdmNo("productModel");

        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidOrganizationMsg));

        supportOrgInfo.setCurrSupportGroupHrDeptOrg("organization");
        PowerMockito.when(HrClient.queryOrganizationInfo(anyList())).thenReturn(new HashMap<>());
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidOrganizationMsg));

        BasicOrganizationInfo organizationInfo = new BasicOrganizationInfo();
        organizationInfo.setOrgIDPath("");
        PowerMockito
                .when(HrClient.queryOrganizationInfo(anyList()))
                .thenReturn(MapUtils.newHashMap("organization", organizationInfo));
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidOrganizationMsg));

        organizationInfo.setOrgIDPath("company－marketing－region－organization");
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FaultAssignmentAbility.consumeServiceRequestMsg(invalidOrganizationMsg));
    }

    @SneakyThrows
    @Test
    public void consumeServiceRequestMsgTest_create() {
        PowerMockito
                .when(AssignmentAbility.queryByAssignmentCode(anyString(), anyList(), eq(Assignment.class)))
                .thenReturn(null);

        // 消息
        ServiceRequestMessage message = new ServiceRequestMessage();
        message.setRequestNo("CI20250723000400");
        message.setSeverityCn("关键一级");
        message.setSeverityEn("Critical Level 1");
        message.setPdmNo("productModel");
        message.setAreaNo("00010012");

        // 服务请求详情
        CscDetailInfoVo defaultDetail = new CscDetailInfoVo();

        BasicInfoVo basicInfo = new BasicInfoVo();
        basicInfo.setPhaseStatusId("1");
        defaultDetail.setBasicInfo(basicInfo);

        FaultInfoVo faultInfo = new FaultInfoVo();
        basicInfo.setFaultInfo(faultInfo);

        SupportOrgInfoVo supportOrgInfo = new SupportOrgInfoVo();
        supportOrgInfo.setCurrSupportGroupHrDeptOrg("organization");
        defaultDetail.setSupportOrgInfo(supportOrgInfo);
        PowerMockito.when(CscClient.getCscDetail(any())).thenReturn(defaultDetail);

        // pdm 产品信息
        ProductInfoByLevelVo productModel = new ProductInfoByLevelVo();
        productModel.setItemNo("productModel");
        productModel.setFullPath("productTeam/productLine/productMainCategory/productSubCategory/productModel");
        PowerMockito
                .when(CacheUtils.get(eq(CidConstants.PDM_PRODUCT_MODEL), any(TypeReference.class)))
                .thenReturn(Lists.newArrayList(productModel));
        PowerMockito
                .when(ProductUtils.getPdmProductTeamAndLineIdMap())
                .thenReturn(MapUtils.newHashMap(
                        "productTeam", "productTeam",
                        "productLine", "productLine"));

        // 代表处
        BasicOrganizationInfo organizationInfo = new BasicOrganizationInfo();
        organizationInfo.setOrgIDPath("ORG0000000－ORG0002700－region－organization");
        PowerMockito
                .when(HrClient.queryOrganizationInfo(anyList()))
                .thenReturn(MapUtils.newHashMap("organization", organizationInfo));

        // 故障管理审批流
        PowerMockito.when(SaveDataHelper.create(any())).thenReturn("faultOrderId");
        PowerMockito.when(ApprovalFlowClient.start(any())).thenReturn("flowInstanceId");

        // 故障管理经理
        PowerMockito.when(ApproverAbility.getFaultManagerByWholePath(anyString()))
                .thenReturn(Lists.newArrayList(new Employee()));

        PowerMockito.when(AssignmentAbility.insert(any())).thenReturn("assignmentId");
        PowerMockito.doNothing().when(AssignmentAbility.class, "createAssignmentPersonRelevance", anyString(), anyList());

        FaultAssignmentAbility.consumeServiceRequestMsg(message);
        PowerMockito.verifyStatic(AssignmentAbility.class, times(1));
        AssignmentAbility.createAssignmentPersonRelevance(anyString(), anyList());
    }

    @SneakyThrows
    @Test
    public void consumeServiceRequestMsgTest_update() {
        ServiceRequestMessage message = new ServiceRequestMessage();
        message.setRequestNo("requestNo");
        message.setSeverityCn("关键一级");

        Assignment assignment = new Assignment();
        assignment.setId("assignmentId");
        assignment.setEntityId("entityId");
        assignment.setLastModifiedBy("lastModifiedBy");
        PowerMockito
                .when(AssignmentAbility.queryByAssignmentCode(anyString(), anyList(), eq(Assignment.class)))
                .thenReturn(assignment);

        assignment.setCurrentProgress("CLOSED");
        FaultAssignmentAbility.consumeServiceRequestMsg(message);
        PowerMockito.verifyStatic(CscClient.class, times(0));
        CscClient.getCscDetail(any());

        assignment.setCurrentProgress("DEMOTION_CLOSED");
        FaultAssignmentAbility.consumeServiceRequestMsg(message);
        PowerMockito.verifyStatic(CscClient.class, times(0));
        CscClient.getCscDetail(any());

        CscDetailInfoVo defaultDetail = new CscDetailInfoVo();
        BasicInfoVo basicInfo = new BasicInfoVo();

        basicInfo.setFaultInfo(new FaultInfoVo());
        basicInfo.setPhaseStatusId("1");
        defaultDetail.setSupportOrgInfo(new SupportOrgInfoVo());
        defaultDetail.setBasicInfo(basicInfo);
        PowerMockito.when(CscClient.getCscDetail(any())).thenReturn(defaultDetail);
        PowerMockito.doNothing().when(FaultManagementOrderAbility.class, "batchUpdate", anyList());

        assignment.setCurrentProgress("TO_REPORT_CUSTOMER_SATISFACTION");
        FaultAssignmentAbility.consumeServiceRequestMsg(message);
        PowerMockito.verifyStatic(CscClient.class, times(1));
        CscClient.getCscDetail(any());
    }

    @SneakyThrows
    @Test
    public void consumeServiceRequestMsgTest_demotionClose() {
        ServiceRequestMessage message = new ServiceRequestMessage();
        message.setRequestNo("requestNo");
        message.setSeverityCn("严重");

        Assignment assignment = new Assignment();
        assignment.setId("assignmentId");
        assignment.setEntityId("entityId");
        PowerMockito
                .when(AssignmentAbility.queryByAssignmentCode(anyString(), anyList(), eq(Assignment.class)))
                .thenReturn(assignment);

        // 任务已关闭
        assignment.setCurrentProgress("CLOSED");
        FaultAssignmentAbility.consumeServiceRequestMsg(message);
        PowerMockito.verifyStatic(FlowHelper.class, times(0));
        FlowHelper.revokeFlow(anyString(), eq(ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name()));

        assignment.setCurrentProgress("DEMOTION_CLOSED");
        FaultAssignmentAbility.consumeServiceRequestMsg(message);
        PowerMockito.verifyStatic(FlowHelper.class, times(0));
        FlowHelper.revokeFlow(anyString(), eq(ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name()));

        // 仅处理故障管理任务
        PowerMockito.doNothing().when(AssignmentAbility.class, "update", any());
        PowerMockito.doNothing().when(FlowHelper.class, "revokeFlow",
                anyString(), eq(ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name()));
        PowerMockito
                .when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList());

        assignment.setCurrentProgress("CONFIRM_FAULT_REVIEW");
        FaultAssignmentAbility.consumeServiceRequestMsg(message);
        PowerMockito.verifyStatic(FlowHelper.class, times(1));
        FlowHelper.revokeFlow(anyString(), eq(ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name()));

        // 废止执行任务
        ObjectInstanceLinkDO approveLink = new ObjectInstanceLinkDO();
        approveLink.setBillIdOut("approveId");
        PowerMockito
                .when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList(approveLink));

        Assignment approveAssignment = new Assignment();
        approveAssignment.setId("approveId");
        approveAssignment.setEntityId("technicalOrderId");
        approveAssignment.setAssignmentStatus(AssignmentStatusEnum.APPROVE.getValue());
        PowerMockito
                .when(AssignmentAbility.get(anyList(), anyList(), eq(Assignment.class)))
                .thenReturn(Lists.newArrayList(approveAssignment));
        PowerMockito
                .when(AssignmentAbility.querySpecificTypeAssignment(
                        anyList(), eq(AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT_SUB), eq(Assignment.class)))
                .thenReturn(Lists.newArrayList());
        PowerMockito
                .when(FlowServiceHelper.batchRevoke(any()))
                .thenReturn(Lists.newArrayList());
        PowerMockito.doNothing().when(AssignmentAbility.class, "batchUpdate", anyList());

        FaultAssignmentAbility.consumeServiceRequestMsg(message);
        PowerMockito.verifyStatic(FlowHelper.class, times(2));
        FlowHelper.revokeFlow(anyString(), eq(ApproveFlowCodeEnum.FAULT_MANAGE_FLOW.name()));
    }

    @SneakyThrows
    @Test
    public void faultReviewAssignmentFinishedTest() {
        // 故障管理任务不存在
        PowerMockito
                .when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList());
        FaultAssignmentAbility.faultReviewAssignmentFinished("reviewAssignmentId");
        PowerMockito.verifyStatic(FlowHelper.class, times(0));
        FlowHelper.pushSystemNode(anyString(), anyString());

        // 故障管理任务存在
        ObjectInstanceLinkDO instance = new ObjectInstanceLinkDO();
        instance.setBillIdOut("faultAssignmentId");
        PowerMockito
                .when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList(instance));

        Assignment faultAssignment = new Assignment();
        faultAssignment.setId("faultAssignmentId");
        faultAssignment.setEntityId("faultOrderId");
        PowerMockito
                .when(AssignmentAbility.querySpecificTypeAssignment(anyString(), anyList(), any()))
                .thenReturn(faultAssignment);

        PowerMockito.doNothing().when(FlowHelper.class, "pushSystemNode", anyString(), anyString());
        PowerMockito.doNothing().when(AssignmentAbility.class, "update", any());

        FaultAssignmentAbility.faultReviewAssignmentFinished("reviewAssignmentId");
        PowerMockito.verifyStatic(FlowHelper.class, times(1));
        FlowHelper.pushSystemNode(anyString(), anyString());
    }

    @SneakyThrows
    @Test
    public void faultRectifyAssignmentFinishedTest() {
        // 故障管理任务不存在
        PowerMockito
                .when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList());
        FaultAssignmentAbility.faultRectifyAssignmentFinished("rectifyAssignmentId");
        PowerMockito.verifyStatic(FlowHelper.class, times(0));
        FlowHelper.pushSystemNode(anyString(), anyString());

        // 有在途整改横推任务
        ObjectInstanceLinkDO instance = new ObjectInstanceLinkDO();
        instance.setBillIdOut("faultAssignmentId");
        PowerMockito
                .when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList(instance));

        Assignment faultAssignment = new Assignment();
        faultAssignment.setId("faultAssignmentId");
        faultAssignment.setEntityId("faultOrderId");
        PowerMockito
                .when(AssignmentAbility.querySpecificTypeAssignment(anyString(), anyList(), any()))
                .thenReturn(faultAssignment);

        TechnologyManagementAssignment rectifyAssignment = new TechnologyManagementAssignment();
        rectifyAssignment.setId("promotionAssignmentId");
        rectifyAssignment.setAssignmentStatus("2");
        PowerMockito
                .when(AssignmentAbility.queryFaultTechAssignments(anyString(), anyList()))
                .thenReturn(Lists.newArrayList(rectifyAssignment));

        FaultAssignmentAbility.faultRectifyAssignmentFinished("rectifyAssignmentId");
        PowerMockito.verifyStatic(FlowHelper.class, times(0));
        FlowHelper.pushSystemNode(anyString(), anyString());

        // 整改横推任务已完成
        PowerMockito
                .when(AssignmentAbility.queryFaultTechAssignments(anyString(), anyList()))
                .thenReturn(Lists.newArrayList());
        PowerMockito.doNothing().when(FlowHelper.class, "pushSystemNode", anyString(), anyString());
        PowerMockito.doNothing().when(AssignmentAbility.class, "update", any());

        FaultAssignmentAbility.faultRectifyAssignmentFinished("reviewAssignmentId");
        PowerMockito.verifyStatic(FlowHelper.class, times(1));
        FlowHelper.pushSystemNode(anyString(), anyString());
    }

    @SneakyThrows
    @Test
    public void updateFaultWarRoomTimeInfoTest() {
        // 服务请求详情
        CscDetailInfoVo requestDetail = new CscDetailInfoVo();
        BasicInfoVo basicInfo = new BasicInfoVo();
        basicInfo.setDeclareDate("2025-10-22 00:00:00");
        requestDetail.setBasicInfo(basicInfo);

        // WarRoom 地铁图详情
        WarRoomNodeVo warRoomNode = new WarRoomNodeVo();

        ProcessRecordVo responseRecord = new ProcessRecordVo();
        responseRecord.setResponseDuration(100);
        warRoomNode.setResponse(Lists.newArrayList(responseRecord));

        ProcessRecordVo locatingRecord = new ProcessRecordVo();
        locatingRecord.setFaultLocatingResultFilledTime(new Date());
        warRoomNode.setFaultLocating(Lists.newArrayList(locatingRecord));

        // 无任务
        PowerMockito
                .when(AssignmentAbility.queryAssignment(anyString(), anyList(), any()))
                .thenReturn(null);
        FaultAssignmentAbility.updateFaultWarRoomTimeInfo(
                "faultOrderId", requestDetail, warRoomNode);
        PowerMockito.verifyStatic(AssignmentAbility.class, times(0));
        AssignmentAbility.update(any());

        // 数据更新
        Assignment assignment = new Assignment();
        assignment.setId("faultAssignmentId");
        PowerMockito
                .when(AssignmentAbility.queryAssignment(anyString(), anyList(), any()))
                .thenReturn(assignment);
        PowerMockito.doNothing().when(AssignmentAbility.class, "update", any());

        FaultAssignmentAbility.updateFaultWarRoomTimeInfo(
                "faultOrderId", requestDetail, warRoomNode);
        PowerMockito.verifyStatic(AssignmentAbility.class, times(1));
        AssignmentAbility.update(any());
    }
}
