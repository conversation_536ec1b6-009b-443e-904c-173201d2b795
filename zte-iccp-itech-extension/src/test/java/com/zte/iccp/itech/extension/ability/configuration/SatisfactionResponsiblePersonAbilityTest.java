package com.zte.iccp.itech.extension.ability.configuration;

import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.domain.model.entity.SatisfactionResponsiblePerson;
import com.zte.iccp.itech.zlic.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({QueryDataHelper.class})
public class SatisfactionResponsiblePersonAbilityTest {

    @Before
    public void setUp() {
        mockStatic(QueryDataHelper.class);
    }

    @Test
    public void getSatisfactionResponsibleTest_paramBlank() {
        Assert.assertTrue(CollectionUtils.isEmpty(
                SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                        "", "org", "team", "line")));
        Assert.assertTrue(CollectionUtils.isEmpty(
                SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                        "country", "", "team", "line")));
        Assert.assertTrue(CollectionUtils.isEmpty(
                SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                        "country", "org", "", "line")));
        Assert.assertTrue(CollectionUtils.isEmpty(
                SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                        "country", "org", "team", "")));
    }

    @Test
    public void getSatisfactionResponsibleTest_levelQuery() {
        PowerMockito
                .when(QueryDataHelper.query(any(), anyList(), anyList()))
                .thenReturn(Lists.newArrayList(new SatisfactionResponsiblePerson()));
        List<SatisfactionResponsiblePerson> responsible
                = SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                        "country", "org", "team", "line");
        Assert.assertFalse(CollectionUtils.isEmpty(responsible));

        PowerMockito
                .when(QueryDataHelper.query(any(), anyList(), anyList()))
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList(new SatisfactionResponsiblePerson()));
        responsible = SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                "country", "org", "team", "line");
        Assert.assertFalse(CollectionUtils.isEmpty(responsible));

        PowerMockito
                .when(QueryDataHelper.query(any(), anyList(), anyList()))
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList(new SatisfactionResponsiblePerson()));
        responsible = SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                "country", "org", "team", "line");
        Assert.assertFalse(CollectionUtils.isEmpty(responsible));

        PowerMockito
                .when(QueryDataHelper.query(any(), anyList(), anyList()))
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList(new SatisfactionResponsiblePerson()));
        responsible = SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                "country", "org", "team", "line");
        Assert.assertFalse(CollectionUtils.isEmpty(responsible));

        PowerMockito
                .when(QueryDataHelper.query(any(), anyList(), anyList()))
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList(new SatisfactionResponsiblePerson()));
        responsible = SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                "country", "org", "team", "line");
        Assert.assertFalse(CollectionUtils.isEmpty(responsible));

        PowerMockito
                .when(QueryDataHelper.query(any(), anyList(), anyList()))
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList())
                .thenReturn(Lists.newArrayList(new SatisfactionResponsiblePerson()));
        responsible = SatisfactionResponsiblePersonAbility.getSatisfactionResponsible(
                "country", "org", "team", "line");
        Assert.assertFalse(CollectionUtils.isEmpty(responsible));
    }
}
