package com.zte.iccp.itech.extension.ability;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.ability.changeorder.MultiProdGuaranteeAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.CacheUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.CommonConstants;
import com.zte.iccp.itech.extension.domain.constant.MessageConsts;
import com.zte.iccp.itech.extension.domain.enums.ApproveResultEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLogActionEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLogEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLogRecordTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.partnerchangeorder.PartnerApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.model.ActionOperationLogParam;
import com.zte.iccp.itech.extension.domain.model.IntlAdminApproval;
import com.zte.iccp.itech.extension.domain.model.OperationLogRecord;
import com.zte.iccp.itech.extension.domain.model.SignatureOperationLogParam;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.entity.BatchTask;
import com.zte.iccp.itech.extension.domain.model.entity.NetworkChangeAssignment;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.FlowClient;
import com.zte.iss.approval.sdk.bean.NodeElement;
import com.zte.paas.lcap.common.spring.ApplicationContextHolder;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.EN_US;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ZH_CN;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR> jiangjiawen
 * @date 2025/5/13
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({FlowHelper.class, AssignmentAbility.class, MultiProdGuaranteeAbility.class, MsgUtils.class, CacheUtils.class,
        ContextHelper.class, HrClient.class, ConfigHelper.class, SaveDataHelper.class, QueryDataHelper.class,
        ApplicationContextHolder.class})
public class OperationLogRecordAbilityTest {

    @Before
    public void setUp() {
        mockStatic(ConfigHelper.class);
        when(ConfigHelper.getRaw("inone.url")).thenReturn("111");
        mockStatic(HrClient.class);
        mockStatic(FlowHelper.class);
        mockStatic(SaveDataHelper.class);

        mockStatic(QueryDataHelper.class);
        mockStatic(ContextHelper.class);

        mockStatic(ApplicationContextHolder.class);
        mockStatic(CacheUtils.class);
        mockStatic(MsgUtils.class);

        mockStatic(AssignmentAbility.class);
        mockStatic(MultiProdGuaranteeAbility.class);

    }

    @Test
    public void saveSignatureOperationLog() {
        Date operationDate = new Date();
        ApproveRecord approveRecord = null;
        Assignment assignment = new Assignment();
        assignment.setId("1");
        assignment.setAssignmentType(Lists.newArrayList(new TextValuePair(){{
            // 网络变更任务
            setValue("1");
        }}));
        assignment.setBillId("1");

        // 1.approveRecord为空场景
        PowerMockito.when(FlowHelper.findApproveRecordByTaskId(anyString(),anyString())).thenReturn(approveRecord);

        OperationLogRecordAbility.saveSignatureOperationLog(operationDate, "1", "1");
        Assert.assertNull(approveRecord);

        // 2.assignment为空场景
        approveRecord = new ApproveRecord();
        approveRecord.setApprover("110");
        PowerMockito.when(FlowHelper.findApproveRecordByTaskId(anyString(),anyString())).thenReturn(approveRecord);
        PowerMockito.when(AssignmentAbility.queryAssignment(anyString(),anyList(),eq(Assignment.class))).thenReturn(null);
        OperationLogRecordAbility.saveSignatureOperationLog(operationDate, "1", "1");
        Assert.assertNotNull(approveRecord);

        // 3.任务类型为网络变更单，审批节点为国际行政会签
        approveRecord.setExtendedCode(ApproveNodeEnum.INTL_ADMIN_APPROVAL.name());
        PowerMockito.when(FlowHelper.findApproveRecordByTaskId(anyString(),anyString())).thenReturn(approveRecord);
        PowerMockito.when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        PowerMockito.when(QueryDataHelper.query(eq(IntlAdminApproval.class), anyList(), anyString()))
                .thenReturn(Lists.newArrayList(new IntlAdminApproval() {{
                    setApprover(new SingleEmployee() {{
                        setEmpUIID("1");
                    }});
                }}));
        PowerMockito.when(HrClient.queryEmployeeInfo(anyList())).thenReturn(Lists.newArrayList(new Employee(){{
            setEmpUIID("1");
        }}));
        PowerMockito.when(AssignmentAbility.querySpecificTypeAssignment(
                        anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(NetworkChangeAssignment.class)))
                .thenReturn(new NetworkChangeAssignment(){{
                    setType("NORMAL");
                }});
        PowerMockito.when(SaveDataHelper.create(any())).thenReturn("1");
        OperationLogRecordAbility.saveSignatureOperationLog(operationDate, "1", "1");
        Assert.assertNotNull(assignment);
    }


    @Test
    public void convertIntlAdminSignatureOperationLogContent() {
        SignatureOperationLogParam operationLogParam = new SignatureOperationLogParam();
        operationLogParam.setBusinessId("1");
        operationLogParam.setSubEntityClass(IntlAdminApproval.class);
        operationLogParam.setApprover("1");
        // 1.intlAdminApprovalList为空场景
        PowerMockito.when(QueryDataHelper.query(eq(IntlAdminApproval.class),anyList(),anyString())).thenReturn(null);
        String operationLogContent = OperationLogRecordAbility.convertSignatureOperationLogContent(operationLogParam);
        Assert.assertNull(operationLogContent);


        // 2.intlAdminApproval为null场景
        PowerMockito.when(QueryDataHelper.query(eq(IntlAdminApproval.class),anyList(),anyString())).thenReturn(Lists.newArrayList(new IntlAdminApproval() {{
            setApprover(new SingleEmployee() {{
                setEmpUIID("2");
            }});
        }}));
         operationLogContent = OperationLogRecordAbility.convertSignatureOperationLogContent(operationLogParam);
        Assert.assertNull(operationLogContent);

        // 3.intlAdminApproval.getResult() == null 场景
        PowerMockito.when(QueryDataHelper.query(eq(IntlAdminApproval.class),anyList(),anyString())).thenReturn(Lists.newArrayList(new IntlAdminApproval() {{
            setApprover(new SingleEmployee() {{
                setResult(null);
                setEmpUIID("1");
            }});
        }}));
         operationLogContent = OperationLogRecordAbility.convertSignatureOperationLogContent(operationLogParam);
        Assert.assertNull(operationLogContent);

        // 4.正常场景（getResult == PASS）
        PowerMockito.when(QueryDataHelper.query(eq(IntlAdminApproval.class),anyList(),anyString())).thenReturn(Lists.newArrayList(new IntlAdminApproval() {{
            setApprover(new SingleEmployee() {{
                setResult(ApproveResultEnum.PASS);
                setEmpUIID("1");
            }});
        }}));

        PowerMockito.when(MsgUtils.getLangMessage(anyString(),anyString())).thenReturn("1");
        operationLogContent = OperationLogRecordAbility.convertSignatureOperationLogContent(operationLogParam);
        Assert.assertNotNull(operationLogContent);

        // 5.正常场景（getResult != PASS）
        PowerMockito.when(QueryDataHelper.query(eq(IntlAdminApproval.class),anyList(),anyString())).thenReturn(Lists.newArrayList(new IntlAdminApproval() {{
            setApprover(new SingleEmployee() {{
                setResult(ApproveResultEnum.TERMINATE);
                setEmpUIID("1");
            }});
        }}));

        PowerMockito.when(MsgUtils.getLangMessage(anyString(),anyString())).thenReturn("1");
        operationLogContent = OperationLogRecordAbility.convertSignatureOperationLogContent(operationLogParam);
        Assert.assertNotNull(operationLogContent);
    }

    @Test
    public void convertSingleFieldTextByLanguage() {
        // 1.匹配到参数为空的场景 - REGIONAL_TD_CONFIRM（两个参数）
        Assert.assertThrows(IllegalArgumentException.class, () -> OperationLogRecordAbility.convertSingleFieldTextByLanguage(new FlowClient() {{
            setBusinessId("1");
            setFlowCode("CHANGE_ORDER_COMP_FLOW");
            setNodeElement(new NodeElement() {{
                setNodeExtendName("REGIONAL_TD_CONFIRM");
            }});
        }}, CommonConstants.ZH_CN));

        // 1.正常场景

        String result = "1";
        PowerMockito.when(MsgUtils.getLangMessage(anyString(),anyString())).thenReturn(result);
        JSONObject jsonObject = new JSONObject();
        OperationLogRecordAbility.convertSingleFieldTextByLanguage(new FlowClient() {{
            setBusinessId("1");
            setFlowCode("CHANGE_ORDER_COMP_FLOW");
            setNodeElement(new NodeElement() {{
                setNodeExtendName("REMOTE_CENTER_SCHEME");
            }});

            setVariables(jsonObject);
        }}, CommonConstants.ZH_CN);
        Assert.assertNotNull(result);
    }

    @Test
    @SneakyThrows
    public void isGuaranteeBatch() {
        String businessId = "1";
        BatchTask batchTask = new BatchTask() {{
            setSource(null);
        }};
        // 1.类型为非批次
        boolean flag = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "isGuaranteeBatch", businessId, AssignmentTypeEnum.NETWORK_CHANGE);
        Assert.assertTrue(flag);

        // 2.类型为内部批次，source为空
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        flag = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "isGuaranteeBatch", businessId, AssignmentTypeEnum.NETWORK_CHANGE_BATCH);
        Assert.assertTrue(flag);

        // 3.类型为内部批次，source不为空，但不是GUARANTEE
        batchTask.setSource("1");
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        flag = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "isGuaranteeBatch", businessId, AssignmentTypeEnum.NETWORK_CHANGE_BATCH);
        Assert.assertTrue(flag);

        // 4.正常场景
        batchTask.setSource("GUARANTEE");
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        flag = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "isGuaranteeBatch", businessId, AssignmentTypeEnum.NETWORK_CHANGE_BATCH);
        Assert.assertFalse(flag);
    }


    @Test
    @SneakyThrows
    public void getOperationType() {
        Assignment assignment = new Assignment();
        assignment.setBillId("1");
        List<TextValuePair> textValuePairList = new ArrayList<>();
        TextValuePair textValuePair = new TextValuePair();
        textValuePair.setValue("6");
        textValuePairList.add(textValuePair);
        assignment.setAssignmentType(textValuePairList);

        String changeOrderId = "1";
        NetworkChangeAssignment networkChangeAssignment = new NetworkChangeAssignment();
        // 1.操作类型为 SUBCONTRACTOR_NETWORK_CHANGE
        OperationLogRecordTypeEnum operationLogRecordTypeEnum = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationType", assignment);
        Assert.assertEquals(operationLogRecordTypeEnum, OperationLogRecordTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE);

        // 2.操作类型为 NETWORK_CHANGE_BATCH
        textValuePair.setValue("2");
        operationLogRecordTypeEnum = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationType", assignment);
        Assert.assertEquals(operationLogRecordTypeEnum, OperationLogRecordTypeEnum.NETWORK_CHANGE);

        // 3.操作类型为 NETWORK_CHANGE，但是他的单据id对应的数据不为多产品联动保障子任务
        textValuePair.setValue("1");
        networkChangeAssignment.setType("NORMAL");
        when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(NetworkChangeAssignment.class))).thenReturn(networkChangeAssignment);
        operationLogRecordTypeEnum = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationType", assignment);
        Assert.assertEquals(operationLogRecordTypeEnum, OperationLogRecordTypeEnum.NETWORK_CHANGE);

        // 4.操作类型为NETWORK_CHANGE，但是他的单据id对应的数据为多产品联动保障子任务
        textValuePair.setValue("1");
        networkChangeAssignment.setType("MULTI_PRODUCT_GUARANTEE_SUB_TASK");
        when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(NetworkChangeAssignment.class))).thenReturn(networkChangeAssignment);
        operationLogRecordTypeEnum = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationType", assignment);
        Assert.assertEquals(operationLogRecordTypeEnum, OperationLogRecordTypeEnum.GUARANTEE);

        // 5.任务类型不匹配，走默认逻辑
        textValuePair.setValue("8");
        operationLogRecordTypeEnum = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationType", assignment);
        Assert.assertNull(operationLogRecordTypeEnum);
    }

    @Test
    @SneakyThrows
    public void getOperationNameMsg() {
        // 1.getSpecialMsg为空场景
        String operationNameMsg = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationNameMsg", OperationLogEnum.REP_PROD_TD_APPROVE);
        Assert.assertEquals(operationNameMsg, ApproveNodeEnum.REP_PROD_TD_APPROVE.getMsgKey());

        // 2.getSpecialMsg不为空场景
        operationNameMsg = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationNameMsg", OperationLogEnum.PENDING_NOTIFICATION);
        Assert.assertEquals(operationNameMsg, "title.batch.pending.notify");
    }

    @Test
    @SneakyThrows
    public void getParentRelationId() {
        Assignment assignment = new Assignment();
        TextValuePair textValuePair = new TextValuePair();
        textValuePair.setValue("2");
        List<TextValuePair> pairList = new ArrayList<>();
        pairList.add(textValuePair);
        assignment.setAssignmentType(pairList);
        assignment.setBillId("1");

        // 1.任务类型为 NETWORK_CHANGE_BATCH
        String parentRelationId = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getParentRelationId", assignment);
        Assert.assertEquals(parentRelationId, "1");

        // 2.任务类型为 SUBCONTRACT_NETWORK_CHANGE_BATCH
        textValuePair.setValue("7");
        parentRelationId = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getParentRelationId", assignment);
        Assert.assertEquals(parentRelationId, "1");

        // 3.任务类型为其他
        textValuePair.setValue("1");
        parentRelationId = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getParentRelationId", assignment);
        Assert.assertNull(parentRelationId);
    }

    @Test
    public void saveFlowOperationLog() {
        TextValuePair textValuePair = new TextValuePair() {{
            setValue("2");
        }};
        Assignment assignment = new Assignment();
        assignment.setAssignmentType(Lists.newArrayList(textValuePair));

        BatchTask batchTask = new BatchTask();
        batchTask.setSource("GUARANTEE");
        NodeElement nodeElement = new NodeElement() {{
            setNodeExtendName("OPERATION_EXECUTION");
        }};

        FlowClient flowClient = new FlowClient() {{
            setBusinessId("1");
            setFlowCode("BATCH_TASK_FLOW");
            setNodeElement(nodeElement);
        }};

        List<Assignment> assignmentList = Lists.newArrayList(new Assignment() {{
            setEntityId("1");
        }});


        // 1.beforeCheck前置校验为false
        when(ContextHelper.getEmpNo()).thenReturn("1");
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        OperationLogRecordAbility.saveFlowOperationLog(flowClient);
        Assert.assertNotNull(batchTask);


        // 2.beforeCheck前置校验为true，operationLogEnum为空
        batchTask.setSource("1");
        when(ContextHelper.getEmpNo()).thenReturn("1");
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        OperationLogRecordAbility.saveFlowOperationLog(flowClient);
        Assert.assertNotNull(batchTask);

        // 3.assignmentTypeEnum为NETWORK_CHANGE_BATCH，是保障主任务，但是匹配linkageGuarantees为空场景
        batchTask.setSource("1");
        when(ContextHelper.getEmpNo()).thenReturn("1");
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        when(MsgUtils.getLangMessage(anyString(), anyString(), anyString(), anyString())).thenReturn("1");
        when(MultiProdGuaranteeAbility.getSubTakByMainId(anyString())).thenReturn(null);
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        when(CacheUtils.setIfAbsent(anyString(), any(), anyLong())).thenReturn(true);

        assignment.setBillId("1");
        nodeElement.setNodeExtendName("OPERATION_CANCEL_REVIEW");
        flowClient.setVariables(new JSONObject());
        OperationLogRecordAbility.saveFlowOperationLog(flowClient);
        Assert.assertNotNull(batchTask);

        // 4.assignmentTypeEnum为NETWORK_CHANGE_BATCH，是保障主任务，正常添加保障日志
        batchTask.setSource("1");
        when(ContextHelper.getEmpNo()).thenReturn("1");
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        when(MsgUtils.getLangMessage(anyString(), anyString(), anyString(), anyString())).thenReturn("1");
        when(MultiProdGuaranteeAbility.getSubTakByMainId(anyString())).thenReturn(assignmentList);
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        when(CacheUtils.setIfAbsent(anyString(), any(), anyLong())).thenReturn(true);

        assignment.setBillId("1");
        nodeElement.setNodeExtendName("OPERATION_CANCEL_REVIEW");
        flowClient.setVariables(new JSONObject());
        OperationLogRecordAbility.saveFlowOperationLog(flowClient);
        Assert.assertNotNull(batchTask);

        // 5.任务类型为网络变更单，操作日志枚举getOperationLogTemplateMsg不为空
        when(ContextHelper.getEmpNo()).thenReturn("1");
        assignment.setAssignmentType(Lists.newArrayList(new TextValuePair(){{
            setValue("1");
        }}));
        assignment.setEntityId("1");
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(NetworkChangeAssignment.class)))
                .thenReturn(new NetworkChangeAssignment(){{
                    setType("NORMAL");
                }});
        when(CacheUtils.setIfAbsent(anyString(), any(), anyLong())).thenReturn(true);
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        flowClient.setFlowCode("CHANGE_ORDER_COMP_FLOW");
        nodeElement.setNodeExtendName("REGIONAL_TD_CONFIRM");
        OperationLogRecordAbility.saveFlowOperationLog(flowClient);
        Assert.assertNotNull(batchTask);

        // 6.幂等成立
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(NetworkChangeAssignment.class)))
                .thenReturn(new NetworkChangeAssignment(){{
                    setType("NORMAL");
                }});
        when(CacheUtils.setIfAbsent(anyString(), any(), anyLong())).thenReturn(true);
        when(QueryDataHelper.query(eq(OperationLogRecord.class), anyList(), anyList(),any(),any()))
                .thenReturn(Lists.newArrayList(new OperationLogRecord(){{
                    setCreateTime(new Date());
                    setOperationName("regional.td");
                }}));
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        OperationLogRecordAbility.saveFlowOperationLog(flowClient);
        Assert.assertNotNull(batchTask);

        // 7.幂等不成立
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(NetworkChangeAssignment.class)))
                .thenReturn(new NetworkChangeAssignment(){{
                    setType("NORMAL");
                }});
        when(CacheUtils.setIfAbsent(anyString(), any(), anyLong())).thenReturn(true);
        when(QueryDataHelper.query(eq(OperationLogRecord.class), anyList(), anyList(),any(),any()))
                .thenReturn(Lists.newArrayList(new OperationLogRecord(){{
                    setCreateTime(new Date());
                    setOperationName("1");
                }}));
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        OperationLogRecordAbility.saveFlowOperationLog(flowClient);
        Assert.assertNotNull(batchTask);

        // 8.setIfAbsent为false
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(NetworkChangeAssignment.class)))
                .thenReturn(new NetworkChangeAssignment(){{
                    setType("NORMAL");
                }});
        when(CacheUtils.setIfAbsent(anyString(), any(), anyLong())).thenReturn(false);
        OperationLogRecordAbility.saveFlowOperationLog(flowClient);
        Assert.assertNotNull(batchTask);
    }

    @Test
    public void convertOperationLogRadioAndTextContentByLanguage() {
        JSONObject variables = new JSONObject();
        NodeElement nodeElement = new NodeElement() {{
            setNodeExtendName("REMOTE_CENTER_SCHEME");
        }};
        FlowClient flowClient = new FlowClient() {{
            setBusinessId("1");
            setFlowCode("CHANGE_ORDER_COMP_FLOW");
            setNodeElement(nodeElement);
            setVariables(variables);
        }};
        // 1.fields的大小小于2
        Assert.assertThrows(IllegalArgumentException.class, () -> OperationLogRecordAbility.convertOperationLogRadioAndTextContentByLanguage(flowClient, CommonConstants.ZH_CN));

        // 2.日志枚举类型为RESULT_TOBE_BACK,operationResultValue为空
        flowClient.setFlowCode("BATCH_TASK_FLOW");
        nodeElement.setNodeExtendName("RESULT_TOBE_BACK");
        when(MsgUtils.getLangMessage(anyString(), anyString(), anyString(), anyString())).thenReturn("1");
        OperationLogRecordAbility.convertOperationLogRadioAndTextContentByLanguage(flowClient, CommonConstants.ZH_CN);
        Assert.assertNotNull(flowClient);

        // 3.日志枚举类型为RESULT_TOBE_BACK,operationResultValue不为空，但操作结果不为5
        flowClient.setFlowCode("BATCH_TASK_FLOW");
        nodeElement.setNodeExtendName("RESULT_TOBE_BACK");
        variables.put("operation_result", "1");
        when(MsgUtils.getLangMessage(anyString(), anyString(), anyString(), anyString())).thenReturn("1");
        OperationLogRecordAbility.convertOperationLogRadioAndTextContentByLanguage(flowClient, CommonConstants.ZH_CN);
        Assert.assertNotNull(flowClient);

        // 4.日志枚举类型为RESULT_TOBE_BACK,operationResultValue不为空，但操作结果为5
        flowClient.setFlowCode("BATCH_TASK_FLOW");
        nodeElement.setNodeExtendName("RESULT_TOBE_BACK");
        variables.put("operation_result", "5");
        when(MsgUtils.getLangMessage(anyString(), anyString(), anyString(), anyString())).thenReturn("1");
        OperationLogRecordAbility.convertOperationLogRadioAndTextContentByLanguage(flowClient, CommonConstants.ZH_CN);
        Assert.assertNotNull(flowClient);
    }

    @Test
    @SneakyThrows
    public void getRadioOrTextValue() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("aaa.en_us", "1");
        FlowClient flowClient = new FlowClient() {{
            setVariables(jsonObject);
        }};
        String result = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getRadioOrTextValue", flowClient, "aaa", EN_US);

        Assert.assertEquals(result,"1");
    }

    @Test
    @SneakyThrows
    public void processOperationDescMsg() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("aaa.fff", "1");
        FlowClient flowClient = new FlowClient() {{
            setVariables(jsonObject);
        }};
        // 1.类型不为 RESULT_TOBE_BACK
        String result = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "processOperationDescMsg", flowClient, OperationLogEnum.BATCH_TD_NET_DEPT_APPROVE);
        Assert.assertEquals(result,"log.batch.result.under.review");

        // 2.类型为 RESULT_TOBE_BACK，但是不需要修改
        jsonObject.put("operation_result", "1");
        result = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "processOperationDescMsg", flowClient, OperationLogEnum.RESULT_TOBE_BACK);
        Assert.assertEquals(result,"log.batch.result.tobe.back");

        // 3.类型为 RESULT_TOBE_BACK，但是需要修改
        jsonObject.put("operation_result", "5");
        result = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "processOperationDescMsg", flowClient, OperationLogEnum.RESULT_TOBE_BACK);
        Assert.assertEquals(result,"log.batch.result.tobe.back.ext");

    }

    @Test
    @SneakyThrows
    public void processOperationFields() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("aaa", "1");
        FlowClient flowClient = new FlowClient() {{
            setVariables(jsonObject);
        }};
        // 1.类型不为 RESULT_TOBE_BACK
        jsonObject.put("operation_result", "1");
        List<String> fields = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "processOperationFields", flowClient, OperationLogEnum.BATCH_TD_NET_DEPT_APPROVE);
        Assert.assertFalse(fields.isEmpty());

        // 2.类型为 RESULT_TOBE_BACK，但是不需要修改
        fields = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "processOperationFields", flowClient, OperationLogEnum.RESULT_TOBE_BACK);
        Assert.assertFalse(fields.isEmpty());



        // 3.类型为 RESULT_TOBE_BACK，但是需要修改
        jsonObject.put("operation_result", "5");
        fields = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "processOperationFields", flowClient, OperationLogEnum.RESULT_TOBE_BACK);
        Assert.assertFalse(fields.isEmpty());
    }

    @Test
    @SneakyThrows
    public void getOperationNamePrefix() {
        Assignment assignment = new Assignment();
        assignment.setAssignmentCode("1");

        List<TextValuePair> textValuePairList = new ArrayList<>();
        TextValuePair textValuePair = new TextValuePair();
        textValuePair.setValue("2");
        textValuePairList.add(textValuePair);
        assignment.setAssignmentType(textValuePairList);

        String assignmentCode = "1";
        // 1.任务类型为 NETWORK_CHANGE_BATCH
        String operationNamePrefix = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationNamePrefix", assignment);
        Assert.assertEquals(operationNamePrefix, assignmentCode);


        // 2.任务类型为 SUBCONTRACT_NETWORK_CHANGE_BATCH
        textValuePair.setValue("7");
        operationNamePrefix = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationNamePrefix", assignment);
        Assert.assertEquals(operationNamePrefix, assignmentCode);

        // 3.任务类型为其他
        textValuePair.setValue("1");
        operationNamePrefix = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "getOperationNamePrefix", assignment);
        Assert.assertNull(operationNamePrefix);
    }

    @Test
    public void systemNodeSyncBatchGuaranteeData() {
        FlowClient body = new FlowClient();
        body.setFlowCode("SUBCONTRACTOR_TASK_FLOW");
        body.setBusinessId("1");
        // 1.非内部批次
        OperationLogRecordAbility.systemNodeSyncBatchGuaranteeData(body);
        Assert.assertNotNull(body);

        // 2.没有保障子任务数据
        body.setFlowCode("BATCH_TASK_FLOW");
        BatchTask batchTask = new BatchTask();
        batchTask.setChangeOrderId("1");
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(batchTask);
        when(MultiProdGuaranteeAbility.getSubTakByMainId(anyString())).thenReturn(null);
        OperationLogRecordAbility.systemNodeSyncBatchGuaranteeData(body);
        Assert.assertNotNull(body);

        // 3.正常场景
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("notification_desc", "1");
        body.setVariables(jsonObject);
        when(QueryDataHelper.get(eq(BatchTask.class),anyList(),anyString())).thenReturn(batchTask);
        when(MultiProdGuaranteeAbility.getSubTakByMainId(anyString())).thenReturn(Lists.newArrayList(new Assignment(){{
            setEntityId("1");
            setAssignmentStatus("3");
        }}));
        when(MsgUtils.getLangMessage(anyString(), anyString(), anyString())).thenReturn("1");
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        OperationLogRecordAbility.systemNodeSyncBatchGuaranteeData(body);
        Assert.assertNotNull(body);

        // 4.operationLogRecordList为空场景
        when(QueryDataHelper.get(eq(BatchTask.class),anyList(),anyString())).thenReturn(batchTask);
        when(MultiProdGuaranteeAbility.getSubTakByMainId(anyString())).thenReturn(Lists.newArrayList(new Assignment(){{
            setEntityId("1");
            setAssignmentStatus("5");
        }}));
        when(MsgUtils.getLangMessage(anyString(), anyString(), anyString())).thenReturn("1");
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        OperationLogRecordAbility.systemNodeSyncBatchGuaranteeData(body);
        Assert.assertNotNull(body);
    }

    @Test
    public void saveActionOperationLog() {
        String businessId = "1";
        String operationKey = "submit_approval_newest";
        Date date = new Date();
        Assignment assignment = null;
        // 1.assignment为空
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        OperationLogRecordAbility.saveActionOperationLog(businessId, operationKey, date);
        Assert.assertNull(assignment);

        // 2.operationLogActionEnum为空
        assignment = new Assignment();
        assignment.setAssignmentType(Lists.newArrayList(new TextValuePair() {{
            setValue("9");
        }}));
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        OperationLogRecordAbility.saveActionOperationLog(businessId, operationKey, date);
        Assert.assertNotNull(assignment);

        // 3.isSyncBatchGuaranteeActionData为false
        assignment = new Assignment();
        operationKey = "batch_commit";
        assignment.setAssignmentType(Lists.newArrayList(new TextValuePair() {{
            setValue("2");
        }}));
        assignment.setAssignmentCode("1");
        assignment.setEntityId("1");
        assignment.setBillId("1");
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(new BatchTask() {{
            setNotificationDesc("111");
        }});
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        OperationLogRecordAbility.saveActionOperationLog(businessId, operationKey, date);
        Assert.assertNotNull(assignment);

        // 4.isSyncBatchGuaranteeActionData为true
        operationKey = "confirm";
        when(AssignmentAbility.queryAssignment(anyString(), anyList(), eq(Assignment.class))).thenReturn(assignment);
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(new BatchTask() {{
            setOcOperationChangeDesc("111");
        }});
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        OperationLogRecordAbility.saveActionOperationLog(businessId, operationKey, date);
        Assert.assertNotNull(assignment);
    }


    @Test
    public void convertActionOperationLogContentByLanguage() {
        ActionOperationLogParam actionOperationLogParam = new ActionOperationLogParam();
        actionOperationLogParam.setBusinessId("1");
        actionOperationLogParam.setAssignmentCode("1");

        // 1.operationDescTemplateMsg为空 todo 后期挂起增加挂起字段和挂起原因时需修改当前UT用例
        actionOperationLogParam.setOperationLogActionEnum(OperationLogActionEnum.BATCH_SUSPEND);
        String result = OperationLogRecordAbility.convertActionOperationLogContentByLanguage(actionOperationLogParam, ZH_CN);
        Assert.assertNull(result);

        // 2.报异常
        actionOperationLogParam.setOperationLogActionEnum(OperationLogActionEnum.BATCH_CHANGE_NOTIFICATION);
        when(QueryDataHelper.get(eq(BatchTask.class), anyList(), anyString())).thenReturn(new BatchTask());
        result = OperationLogRecordAbility.convertActionOperationLogContentByLanguage(actionOperationLogParam, ZH_CN);
        Assert.assertNull(result);
    }

    @Test
    @SneakyThrows
    public void buildGuaranteeBatchOperationLog() {
        Date date = new Date();
        String operationDescZh = "1";
        String operationDescEn = "1";
        String operationNameMsg = "1";
        Assignment assignment = new Assignment();
        assignment.setBillId("1");
        assignment.setAssignmentType(Lists.newArrayList(new TextValuePair() {{
            setValue("1");
        }}));
        String methodToExecute = "buildGuaranteeBatchOperationLog";
        // 1.非内部批次
        List<OperationLogRecord> operationLogRecordList = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, methodToExecute, assignment, operationDescZh, operationDescEn, operationNameMsg, date);
        Assert.assertTrue(operationLogRecordList.isEmpty());

        // 2.assignmentList为空
        assignment.setAssignmentType(Lists.newArrayList(new TextValuePair() {{
            setValue("2");
        }}));
        when(MultiProdGuaranteeAbility.getSubTakByMainId(anyString())).thenReturn(null);
        operationLogRecordList = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, methodToExecute, assignment, operationDescZh, operationDescEn, operationNameMsg, date);
        Assert.assertTrue(operationLogRecordList.isEmpty());

        // 3.empNo为iTechCloud,保障单任务状态为废止状态
        when(MultiProdGuaranteeAbility.getSubTakByMainId(anyString())).thenReturn(Lists.newArrayList(new Assignment() {{
            setEntityId("1");
            setAssignmentStatus("5");
        }}));
        when(ContextHelper.getEmpNo()).thenReturn("iTechCloud");
        when(MsgUtils.getLangMessage(anyString(), anyString(), anyString(), anyString())).thenReturn("1");
        operationLogRecordList = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, methodToExecute, assignment, operationDescZh, operationDescEn, operationNameMsg, date);
        Assert.assertEquals(0, operationLogRecordList.size());
    }

    @Test
    @SneakyThrows
    public void buildNormalOperationLogRecord() {
        when(ContextHelper.getEmpNo()).thenReturn("iTechCloud");
        OperationLogRecord operationLogRecord = Whitebox.invokeMethod(
                OperationLogRecordAbility.class, "buildNormalOperationLogRecord", new Assignment(){{
                    setEntityId("1");
                    setAssignmentCode("1");
                    setBillId("1");
                    setAssignmentType(Lists.newArrayList(new TextValuePair(){{
                        setValue("2");
                    }}));
                }},"1");
        Assert.assertNotNull(operationLogRecord);
    }

    @Test
    public void saveGuaranteeAbolishOperationLog() {
        String operationKey = "abolish";
        String changeOrderId = "1";
        Assignment assignment = null;
        String empNo = "iTechCloud";
        List<NetworkChangeAssignment> toBeDeprecatedTasks = Lists.newArrayList(new NetworkChangeAssignment() {{
            setEntityId("1");
        }});

        // 1.operationKey为空
        OperationLogRecordAbility.saveGuaranteeAbolishOperationLog(toBeDeprecatedTasks, changeOrderId,null,null);
        Assert.assertNull(assignment);

        // 2.assignment为null
        when(AssignmentAbility.queryAssignment(anyString(),anyList(),eq(Assignment.class))).thenReturn(assignment);
        OperationLogRecordAbility.saveGuaranteeAbolishOperationLog(toBeDeprecatedTasks, changeOrderId, operationKey, null);
        Assert.assertNull(assignment);

        // 3.empNo为 iTechCloud,approveRecord为null场景
        assignment = new Assignment();
        assignment.setAssignmentCode("1");
        List<ApproveRecord> approvedRecords = new ArrayList<>();

        when(AssignmentAbility.queryAssignment(anyString(),anyList(),eq(Assignment.class))).thenReturn(assignment);
        when(ContextHelper.getEmpNo()).thenReturn(empNo);
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        OperationLogRecordAbility.saveGuaranteeAbolishOperationLog(toBeDeprecatedTasks, changeOrderId, operationKey, new Date());
        Assert.assertNotNull(assignment);

        // 4.empNo为 iTechCloud,approveRecord不为null场景
        approvedRecords.add(new ApproveRecord() {{
            setApprovalDate(new Date());
            setApprover("1");
        }});
        when(AssignmentAbility.queryAssignment(anyString(),anyList(),eq(Assignment.class))).thenReturn(assignment);
        when(ContextHelper.getEmpNo()).thenReturn(empNo);
        when(FlowHelper.getApprovedRecords(anyString())).thenReturn(approvedRecords);
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(new ArrayList<>());
        OperationLogRecordAbility.saveGuaranteeAbolishOperationLog(toBeDeprecatedTasks, changeOrderId, operationKey, new Date());
        Assert.assertNotNull(assignment);
    }

    @SneakyThrows
    @Test
    public void saveNetworkChangeTransferLogTest() {
        NetworkChangeAssignment networkChangeAssignment = new NetworkChangeAssignment();
        networkChangeAssignment.setBillId("changeOrderId");
        networkChangeAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE.getPropValue());
        networkChangeAssignment.setCurrentProgress(ApproveNodeEnum.TD_NET_DEPT_APPROVE.name());

        Assignment batchAssignment = new Assignment();
        batchAssignment.setBillId("batchChangeOrderId");
        batchAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getPropValue());
        batchAssignment.setCurrentProgress(ApproveNodeEnum.RESULT_TOBE_BACK.name());

        Assignment partnerAssignment = new Assignment();
        partnerAssignment.setBillId("partnerChangeOrderId");
        partnerAssignment.setAssignmentType(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getPropValue());
        partnerAssignment.setCurrentProgress(PartnerApproveNodeEnum.PARTNER_NET_DEPT_APPROVAL.name());

        Employee user = new Employee();
        user.setEmpUIID("userId");
        user.setEmpNameCn("userNameCn");
        user.setEmpNameEn("userNameEn");

        Employee newCurrentProcessor = new Employee();
        newCurrentProcessor.setEmpUIID("newCurrentProcessorId");
        newCurrentProcessor.setEmpNameCn("newCurrentProcessorNameCn");
        newCurrentProcessor.setEmpNameEn("newCurrentProcessorNameEn");

        PowerMockito.when(AssignmentAbility.querySpecificTypeAssignment(
                anyString(), eq(AssignmentTypeEnum.NETWORK_CHANGE), eq(NetworkChangeAssignment.class)))
                        .thenReturn(networkChangeAssignment);
        PowerMockito.when(SaveDataHelper.batchCreate(anyList()))
                .thenReturn(Lists.newArrayList("logId"));

        List<String> result = OperationLogRecordAbility.saveNetworkChangeTransferLog(
                Lists.newArrayList(networkChangeAssignment, batchAssignment, partnerAssignment),
                Lists.newArrayList(user),
                Lists.newArrayList(newCurrentProcessor),
                MessageConsts.Backlog.LOG_TRANSFER_CURRENT_PROCESSOR);
        Assert.assertFalse(CollectionUtils.isEmpty(result));

        // 重复不记录日志
        result = OperationLogRecordAbility.saveNetworkChangeTransferLog(
                Lists.newArrayList(networkChangeAssignment, batchAssignment, partnerAssignment),
                Lists.newArrayList(user),
                Lists.newArrayList(user),
                MessageConsts.Backlog.LOG_TRANSFER_CURRENT_PROCESSOR);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void saveResultReviewRevokeLogTest() {
        NetworkChangeAssignment networkChangeAssignment = new NetworkChangeAssignment();
        networkChangeAssignment.setBillId("changeOrderId");
        networkChangeAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getPropValue());
        networkChangeAssignment.setCurrentProgress(ApproveNodeEnum.TD_NET_DEPT_APPROVE.name());
        networkChangeAssignment.setType("1");
        OperationLogRecordAbility.saveResultReviewRevokeLog(networkChangeAssignment);
        Assert.assertNotNull(networkChangeAssignment);
    }
}
