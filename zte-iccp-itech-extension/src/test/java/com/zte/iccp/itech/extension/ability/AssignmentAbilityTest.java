package com.zte.iccp.itech.extension.ability;

import com.google.common.collect.Sets;
import com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.permissionapplication.PermissionApplicationAbility;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.AsyncExecuteUtils;
import com.zte.iccp.itech.extension.common.utils.MapUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.BusinessConsts;
import com.zte.iccp.itech.extension.domain.enums.OperationLevelEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.*;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.entity.*;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.enums.gtdcenter.ConstraintTypeEnum;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicEmployeeInfo;
import com.zte.iccp.itech.extension.spi.model.hr.vo.BasicOrganizationInfo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.ProcessRecordVo;
import com.zte.iccp.itech.extension.spi.model.warroom.vo.WarRoomNodeVo;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.common.api.metadata.engine.manage.model.ObjectInstanceLinkDO;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.ClientConstants.HR_PERSON_COMPANY_TYPE_PARTNER_ZH;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.powermock.api.mockito.PowerMockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ContextHelper.class, ConfigHelper.class, QueryDataHelper.class, SaveDataHelper.class,
        ChangeOrderAbility.class, ObjectLinkInstanceAbility.class, NisClient.class, HrClient.class,
        PermissionApplicationAbility.class, MsgUtils.class, TimeZoneEnum.class, AsyncExecuteUtils.class,
        EmployeeHelper.class})
public class AssignmentAbilityTest {

    @Before
    public void setUp() {
        mockStatic(ConfigHelper.class);
        mockStatic(ContextHelper.class);
        mockStatic(QueryDataHelper.class);
        mockStatic(SaveDataHelper.class);
        mockStatic(EmployeeHelper.class);

        mockStatic(MsgUtils.class);

        mockStatic(ChangeOrderAbility.class);
        mockStatic(ObjectLinkInstanceAbility.class);

        mockStatic(NisClient.class);
        mockStatic(HrClient.class);
        mockStatic(PermissionApplicationAbility.class);
        mockStatic(TimeZoneEnum.class);
        mockStatic(AsyncExecuteUtils.class);
    }

    @Test
    public void querySpecificTypeAssignmentTest_allFields() {
        // 无 id
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                "", Assignment.class);
        Assert.assertNull(assignment);

        // 正常检索
        PowerMockito.when(QueryDataHelper.get(eq(Assignment.class), anyList(), anyString()))
                .thenReturn(new Assignment());
        assignment = AssignmentAbility.querySpecificTypeAssignment(
                "assignmentId", Assignment.class);
        Assert.assertNotNull(assignment);
    }

    @Test
    public void querySpecificTypeAssignmentTest() {
        // 无 id
        Assignment assignment = AssignmentAbility.querySpecificTypeAssignment(
                "",
                Lists.newArrayList(),
                Assignment.class);
        Assert.assertNull(assignment);

        // 正常检索
        PowerMockito.when(QueryDataHelper.get(eq(Assignment.class), anyList(), anyString()))
                .thenReturn(new Assignment());
        assignment = AssignmentAbility.querySpecificTypeAssignment(
                "assignmentId",
                Lists.newArrayList(),
                Assignment.class);
        Assert.assertNotNull(assignment);
    }

    @Test
    public void querySpecificTypeAssignmentTest_multiIds() {
        // 无 id
        List<Assignment> assignments = AssignmentAbility.querySpecificTypeAssignment(
                Lists.newArrayList(), Assignment.class);
        Assert.assertTrue(CollectionUtils.isEmpty(assignments));

        // 正常检索
        PowerMockito.when(QueryDataHelper.get(eq(Assignment.class), anyList(), anyList()))
                .thenReturn(Lists.newArrayList());
        assignments = AssignmentAbility.querySpecificTypeAssignment(
                Lists.newArrayList("assignmentId"), Assignment.class);
        Assert.assertTrue(CollectionUtils.isEmpty(assignments));
    }

    @Test
    public void getTest() {
        // 无 id
        List<Assignment> assignments = AssignmentAbility.get(
                Lists.newArrayList(), Lists.newArrayList(), Assignment.class);
        Assert.assertTrue(CollectionUtils.isEmpty(assignments));

        // 正常检索
        PowerMockito.when(QueryDataHelper.get(eq(Assignment.class), anyList(), anyList()))
                .thenReturn(Lists.newArrayList());
        assignments = AssignmentAbility.get(
                Lists.newArrayList("assignmentId"), Lists.newArrayList(), Assignment.class);
        Assert.assertTrue(CollectionUtils.isEmpty(assignments));
    }

    @Test
    public void queryByAssignmentCodeTest() {
        // 无 id
        Assignment assignment = AssignmentAbility.queryByAssignmentCode(
                "",
                Lists.newArrayList(),
                Assignment.class);
        Assert.assertNull(assignment);

        // 正常检索
        PowerMockito.when(QueryDataHelper.queryOne(eq(Assignment.class), anyList(), anyList()))
                .thenReturn(new Assignment());
        assignment = AssignmentAbility.queryByAssignmentCode(
                "assignmentCode",
                Lists.newArrayList(),
                Assignment.class);
        Assert.assertNotNull(assignment);
    }

    @Test
    public void updateFaultWarRoomTimeInfoTest() {
        // 无任务
        PowerMockito.when(QueryDataHelper.queryOne(any(), anyList(), anyList()))
                .thenReturn(null);
        AssignmentAbility.updateFaultWarRoomTimeInfo("faultOrderId", new WarRoomNodeVo());
        PowerMockito.verifyStatic(SaveDataHelper.class, times(0));
        SaveDataHelper.update(any());

        // 数据更新
        Assignment assignment = new Assignment();
        assignment.setId("assignmentId");
        PowerMockito.when(QueryDataHelper.queryOne(any(), anyList(), anyList()))
                .thenReturn(assignment);

        // 分支 1
        AssignmentAbility.updateFaultWarRoomTimeInfo("faultOrderId", new WarRoomNodeVo());
        PowerMockito.verifyStatic(SaveDataHelper.class, times(1));
        SaveDataHelper.update(any());

        // 分支 2
        WarRoomNodeVo nodeInfo = new WarRoomNodeVo();
        nodeInfo.setResponse(Lists.newArrayList((ProcessRecordVo) null));
        nodeInfo.setInPlace(Lists.newArrayList((ProcessRecordVo) null));

        AssignmentAbility.updateFaultWarRoomTimeInfo("faultOrderId", nodeInfo);

        // 分支 3
        nodeInfo.setResponse(Lists.newArrayList(new ProcessRecordVo()));
        nodeInfo.setInPlace(Lists.newArrayList(new ProcessRecordVo()));

        AssignmentAbility.updateFaultWarRoomTimeInfo("faultOrderId", nodeInfo);
    }

    @Test
    public void queryFaultTechAssignmentsTest() {
        // 无任务ID
        List<TechnologyManagementAssignment> assignments
                = AssignmentAbility.queryFaultTechAssignments("", Lists.newArrayList());
        Assert.assertTrue(CollectionUtils.isEmpty(assignments));

        // 无 故障复盘 / 故障整改 任务
        PowerMockito.when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList());
        assignments = AssignmentAbility.queryFaultTechAssignments("faultId", Lists.newArrayList());
        Assert.assertTrue(CollectionUtils.isEmpty(assignments));

        // 默认检索
        ObjectInstanceLinkDO objectInstanceLinkDO = new ObjectInstanceLinkDO();
        objectInstanceLinkDO.setBillIdOut("techId");

        PowerMockito.when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList(objectInstanceLinkDO));
        PowerMockito.when(QueryDataHelper.query(eq(TechnologyManagementAssignment.class), anyList(), anyList()))
                .thenReturn(Lists.newArrayList());
        assignments = AssignmentAbility.queryFaultTechAssignments("faultId", Lists.newArrayList());
        Assert.assertTrue(CollectionUtils.isEmpty(assignments));

        // 条件检索
        assignments = AssignmentAbility.queryFaultTechAssignments(
                "faultId", Lists.newArrayList("4"));
        Assert.assertTrue(CollectionUtils.isEmpty(assignments));
    }

    @Test
    public void getNetworkChangeClientConditionTest() {
        // 空参情况
        Pair<ClientData, Map<String, FormatFieldData>> result =
                AssignmentAbility.getNetworkChangeClientCondition(Lists.newArrayList());
        Assert.assertTrue(CollectionUtils.isEmpty(result.getRight()));

        // 获取任务对应属性
        NetworkChangeAssignment assignment = new NetworkChangeAssignment();
        assignment.setRepresentativeOffice(
                TextValuePairHelper.buildList(Lists.newArrayList("organizationId")));
        assignment.setProductClassification(
                TextValuePairHelper.buildList(Lists.newArrayList("productId/")));
        assignment.setNetwork(
                TextValuePairHelper.buildList(Lists.newArrayList("networkId")));

        result = AssignmentAbility.getNetworkChangeClientCondition(
                Lists.newArrayList(assignment));
        Assert.assertFalse(CollectionUtils.isEmpty(result.getRight()));
    }

    @Test
    public void queryNetworkChangeClientDataTest() {
        ClientData clientData = new ClientData();
        clientData.setOrganizationId(new HashSet<String>(){{add("organizationId");}});
        clientData.setProductId(new HashSet<String>(){{add("productId");}});
        clientData.setNetworkId(new HashSet<String>(){{add("networkId");}});

        BasicOrganizationInfo organization = new BasicOrganizationInfo();
        organization.setHrOrgNamePath("RepresentativeOffice/Region/Marketing/Company");
        PowerMockito.when(HrClient.queryOrganizationInfo(anyList()))
                .thenReturn(MapUtils.newHashMap("organizationId", organization));

        PowerMockito.when(NisClient.queryProductPathName(anyList()))
                .thenReturn(MapUtils.newHashMap("productId", "product"));
        PowerMockito.when(NisClient.queryNetworkName(anyList()))
                .thenReturn(MapUtils.newHashMap("networkId", "network"));

        AssignmentAbility.queryNetworkChangeClientData(clientData);
        Assert.assertFalse(CollectionUtils.isEmpty(clientData.getFullOrganization()));
        Assert.assertFalse(CollectionUtils.isEmpty(clientData.getProduct()));
        Assert.assertFalse(CollectionUtils.isEmpty(clientData.getNetwork()));
    }

    @Test
    public void getNetworkChangeInteriorConditionTest() {
        // 空参情况
        InteriorData interiorData
                = AssignmentAbility.getNetworkChangeInteriorCondition(Lists.newArrayList());

        Assert.assertTrue(CollectionUtils.isEmpty(interiorData.getBatchApproveId()));
        Assert.assertTrue(CollectionUtils.isEmpty(interiorData.getSubcontractBatchApproveId()));

        // 批次任务 + 合作方批次任务情况
        NetworkChangeAssignment batchAssignment = new NetworkChangeAssignment();
        batchAssignment.setEntityId("batchId");
        batchAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getPropValue());

        NetworkChangeAssignment partnerBatchAssignment = new NetworkChangeAssignment();
        partnerBatchAssignment.setEntityId("partnerBatchId");
        partnerBatchAssignment.setAssignmentType(AssignmentTypeEnum.SUBCONTRACT_NETWORK_CHANGE_BATCH.getPropValue());

        interiorData = AssignmentAbility.getNetworkChangeInteriorCondition(
                Lists.newArrayList(batchAssignment, partnerBatchAssignment));

        Assert.assertFalse(CollectionUtils.isEmpty(interiorData.getBatchApproveId()));
        Assert.assertFalse(CollectionUtils.isEmpty(interiorData.getSubcontractBatchApproveId()));
    }

    @Test
    public void queryNetworkChangeInteriorDataTest() {
        when(ContextHelper.getLangId()).thenReturn("zh_CN");

        InteriorData interiorData = new InteriorData();
        interiorData.setBatchApproveId(new HashSet<>());
        interiorData.setSubcontractBatchApproveId(new HashSet<>());

        AssignmentAbility.queryNetworkChangeInteriorData(interiorData);

        Assert.assertTrue(CollectionUtils.isEmpty(interiorData.getBatchStatus()));
        Assert.assertTrue(CollectionUtils.isEmpty(interiorData.getSubcontractBatchStatus()));
        Assert.assertFalse(CollectionUtils.isEmpty(interiorData.getMainCurrentProgress()));
        Assert.assertFalse(CollectionUtils.isEmpty(interiorData.getSubCurrentProgress()));
    }

    @Test
    public void getUppPermissionFilterTest() {
        Map<ConstraintTypeEnum, Set<String>> map = new HashMap<>();
        map.put(ConstraintTypeEnum.ORGANIZATION, Sets.newHashSet("ORG0000000/ORG2223728/ORG2223784/ORG2223815",
                "ORG0000000/ORG2223728/ORG2223786/123", "ORG0000000/ORG0002700/123/123", "1/2/3/4"));
        map.put(ConstraintTypeEnum.PRODUCT, Sets.newHashSet("prd1", "prd2"));
        when(NisClient.getUserConstraint(any(), any())).thenReturn(map);

        BasicEmployeeInfo basicEmployeeInfo = new BasicEmployeeInfo();
        basicEmployeeInfo.setCompanyType(HR_PERSON_COMPANY_TYPE_PARTNER_ZH);
        basicEmployeeInfo.setOrgID("123");
        when(HrClient.getEmployeeInfo(any())).thenReturn(basicEmployeeInfo);

        PermissionApplication application = new PermissionApplication();
        TextValuePair textValuePair = new TextValuePair();
        textValuePair.setValue(BusinessConsts.ENGINEERING_SERVICE_SALES);
        application.setOrganizations(Lists.newArrayList(textValuePair));
        List<PermissionApplication> zhongZhiHLPermission = Lists.newArrayList(application);
        when(PermissionApplicationAbility.queryZhongZhiHLPermission(any())).thenReturn(zhongZhiHLPermission);

        AssignmentAbility.getUppPermissionFilter("分包商");

        PermissionApplication application1 = new PermissionApplication();
        TextValuePair textValuePair1 = new TextValuePair();
        textValuePair1.setValue(BusinessConsts.NET_SERVICE_SALES);
        application1.setOrganizations(Lists.newArrayList(textValuePair1));
        List<PermissionApplication> zhongZhiHLPermission1 = Lists.newArrayList(application1);
        when(PermissionApplicationAbility.queryZhongZhiHLPermission(any())).thenReturn(zhongZhiHLPermission1);

        AssignmentAbility.getUppPermissionFilter("分包商");

        Map<ConstraintTypeEnum, Set<String>> map1 = new HashMap<>();
        map1.put(ConstraintTypeEnum.ORGANIZATION, Sets.newHashSet("ORG0000000/ORG2223728/ORG2223786/123",
                "ORG0000000/ORG0002700/123/123", "1/2/3/4", "ORG0000000/ORG2223728/ORG2223781/123"));
        map1.put(ConstraintTypeEnum.PRODUCT, Sets.newHashSet("prd1", "prd2"));
        when(NisClient.getUserConstraint(any(), any())).thenReturn(map1);

        PermissionApplication application2 = new PermissionApplication();
        TextValuePair textValuePair2 = new TextValuePair();
        textValuePair2.setValue("1/2/3/4");
        TextValuePair textValuePair3 = new TextValuePair();
        textValuePair3.setValue("123");
        TextValuePair textValuePair4 = new TextValuePair();
        textValuePair4.setValue("ORG0000000/ORG2223728/ORG2223781/123");
        application2.setOrganizations(Lists.newArrayList(textValuePair2, textValuePair3, textValuePair4));
        List<PermissionApplication> zhongZhiHLPermission2 = Lists.newArrayList(application2);
        when(PermissionApplicationAbility.queryZhongZhiHLPermission(any())).thenReturn(zhongZhiHLPermission2);

        Assert.assertNotNull(AssignmentAbility.getUppPermissionFilter("分包商"));

    }

    @Test
    public void getAssignmentCurrentProgress() {

        //1.extendedCode不为null并且不是草稿状态
        String result = AssignmentAbility.getAssignmentCurrentProgress(null, "1");
        Assert.assertEquals("1", result);

        //2.extendedCode不为null并且是草稿状态
        when(QueryDataHelper.queryOne(eq(Assignment.class), anyList(), anyList())).thenReturn(new Assignment() {{
            setId("1");
            setCurrentProgress("1");
        }});
        result = AssignmentAbility.getAssignmentCurrentProgress("1", "1");
        Assert.assertNotNull(result);

        //3.extendedCode为null
        when(QueryDataHelper.queryOne(eq(Assignment.class), anyList(), anyList())).thenReturn(new Assignment() {{
            setId("1");
            setCurrentProgress("1");
        }});
        result = AssignmentAbility.getAssignmentCurrentProgress("1", null);
        Assert.assertNotNull(result);

        // 3.Assignment为null
        when(QueryDataHelper.queryOne(eq(Assignment.class), anyList(), anyList())).thenReturn(null);
        when(MsgUtils.getMessage(anyString(), anyString())).thenReturn("1");
        Assert.assertThrows(Exception.class, () -> AssignmentAbility.getAssignmentCurrentProgress("1", null));
    }


    @Test
    public void updatePlanStartTimeByEntityIds() {
        List<String> list = new ArrayList<>();
        AssignmentAbility.updatePlanStartTimeByEntityIds(list, null);
        list.add("1");
        AssignmentAbility.updatePlanStartTimeByEntityIds(list, null);

        PowerMockito.when(QueryDataHelper.query(eq(NetworkChangeAssignment.class), anyList(), anyList()))
                .thenReturn(Lists.newArrayList(new NetworkChangeAssignment()));
        AssignmentAbility.updatePlanStartTimeByEntityIds(list, new Date());
        Assert.assertNotNull(list);

        PowerMockito.when(TimeZoneEnum.getTimeZoneEnum(any())).thenReturn(TimeZoneEnum.BEIJING);
        AssignmentAbility.updatePlanStartTimeByEntityIds(list, new Date());
        PowerMockito.when(TimeZoneEnum.getTimeZoneEnum(any())).thenReturn(TimeZoneEnum.UTC);
        AssignmentAbility.updatePlanStartTimeByEntityIds(list, new Date());

    }

    @Test
    public void getTransferCurrentProcessorExtraParameterTest() {
        // 1.非网络变更任务
        Assignment assignment = new Assignment();
        assignment.setId("assignmentId");
        assignment.setAssignmentType(AssignmentTypeEnum.FAULT_MANAGEMENT.getPropValue());

        PowerMockito.when(QueryDataHelper.get(eq(Assignment.class), anyList(), anyList()))
                .thenReturn(Lists.newArrayList(assignment));

        Map<String, Object> parameters = AssignmentAbility.getTransferCurrentProcessorExtraParameter(
                Lists.newArrayList("assignmentId"));
        Assert.assertFalse(CollectionUtils.isEmpty(parameters));

        // 2.网络变更类任务 - 抛出异常
        assignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE.getPropValue());
        assignment.setAssignmentStatus(AssignmentStatusEnum.EXECUTE.getValue());

        PowerMockito.when(ChangeOrderAbility.todoTransferBeforeCheck(anyList()))
                .thenReturn("assignmentId");
        Assert.assertThrows(
                LcapBusiException.class,
                () -> AssignmentAbility.getTransferCurrentProcessorExtraParameter(
                        Lists.newArrayList("assignmentId")));

        // 3.网络变更类任务 - 正常
        assignment.setAssignmentType(AssignmentTypeEnum.SUBCONTRACTOR_NETWORK_CHANGE.getPropValue());
        assignment.setAssignmentStatus(AssignmentStatusEnum.APPROVE.getValue());

        PowerMockito.when(ChangeOrderAbility.todoTransferBeforeCheck(anyList()))
                .thenReturn("");

        parameters = AssignmentAbility.getTransferCurrentProcessorExtraParameter(
                Lists.newArrayList("assignmentId"));
        Assert.assertFalse(CollectionUtils.isEmpty(parameters));
    }

    @SneakyThrows
    @Test
    public void recalculateMainAssignmentCurrentProcessorTest() {
        Employee employee1 = new Employee();
        employee1.setEmpUIID("employeeId1");

        Employee employee2 = new Employee();
        employee2.setEmpUIID("employeeId2");

        Assignment batchAssignment = new Assignment();
        batchAssignment.setId("batchAssignmentId");
        batchAssignment.setBillId("changeOrderId");
        batchAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getPropValue());
        batchAssignment.setCurrentProcessorEmployee(Lists.newArrayList(employee1));

        Assignment anotherBatchAssignment = new Assignment();
        anotherBatchAssignment.setId("anotherBatchAssignmentId");
        batchAssignment.setBillId("changeOrderId");
        batchAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE_BATCH.getPropValue());
        batchAssignment.setCurrentProcessorEmployee(Lists.newArrayList(employee2));

        Assignment mainAssignment = new Assignment();
        mainAssignment.setId("mainAssignmentId");
        mainAssignment.setBillId("changeOrderId");
        mainAssignment.setAssignmentType(AssignmentTypeEnum.NETWORK_CHANGE.getPropValue());

        PowerMockito.when(QueryDataHelper.query(any(), anyList(), anyList()))
                .thenReturn(Lists.newArrayList(batchAssignment, anotherBatchAssignment, mainAssignment));
        PowerMockito.when(SaveDataHelper.batchUpdate(anyList())).thenReturn(true);

        AssignmentAbility.recalculateMainAssignmentCurrentProcessor(Lists.newArrayList(batchAssignment));
        verifyStatic(SaveDataHelper.class, times(1));
        SaveDataHelper.batchUpdate(anyList());
    }

    @Test
    public void checkHasChildrenTest() {
        ObjectInstanceLinkDO instance = new ObjectInstanceLinkDO();
        instance.setBillIdIn("assignmentId");

        PowerMockito.when(ObjectLinkInstanceAbility.queryLinkInstance(any(), anyList()))
                .thenReturn(Lists.newArrayList(instance));

        Map<String, Integer> result = AssignmentAbility.checkHasChildren(
                Lists.newArrayList("assignmentId", "otherId"));
        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void asyncStepCountTest() {
        PowerMockito.when(ConfigHelper.get(anyString())).thenReturn("3");
        PowerMockito.when(AsyncExecuteUtils.asyncQuery(anyList()))
                .thenReturn(Lists.newArrayList(1, 3));

        int result = AssignmentAbility.asyncStepCount(
                "SELECT COUNT(1) FROM assignment where is_deleted = 0", new Date());
        Assert.assertEquals(result, 4);
    }

    @Test
    public void convertClockInReviewAssignment() {
        ClockInReviews clockInReviews = new ClockInReviews();
        clockInReviews.setId("1");
        clockInReviews.setOperationSubject("1");
        clockInReviews.setReviewsNo("1");
        clockInReviews.setMarketing(new ArrayList<>());
        clockInReviews.setResponsibleDept(new ArrayList<>());
        clockInReviews.setProductClassification(new ArrayList<>());
        clockInReviews.setNetworkName(new ArrayList<>());
        clockInReviews.setOperationType("1");
        clockInReviews.setBatchNo("1");
        clockInReviews.setOperationLevel(OperationLevelEnum.NORMAL);
        clockInReviews.setPlanOperationStartTime(new Date());
        clockInReviews.setOnDutyDurationHours(1);
        clockInReviews.setReplayStatus(AssignmentStatusEnum.APPROVE);
        clockInReviews.setOperationOwners(new ArrayList<>());

        // 1、currentProcessors不为空场景
        when(EmployeeHelper.uniqueEmployees(anyList())).thenReturn(new ArrayList<>());
        ClockInReviewsAssignment clockInReviewsAssignment = AssignmentAbility.convertClockInReviewAssignment(clockInReviews, Lists.newArrayList(new Employee() {{
            setEmpUIID("1");
        }}));
        Assert.assertNotNull(clockInReviewsAssignment);

        // 2、currentProcessors为空场景
        when(EmployeeHelper.uniqueEmployees(anyList())).thenReturn(new ArrayList<>());
        when(ConfigHelper.getList(anyString())).thenReturn(Lists.newArrayList("10000"));
        when(HrClient.queryEmployeeInfo(anyList())).thenReturn(Lists.newArrayList(new Employee() {{
            setEmpUIID("1");
        }}));
        clockInReviewsAssignment = AssignmentAbility.convertClockInReviewAssignment(clockInReviews, Lists.newArrayList());
        Assert.assertNotNull(clockInReviewsAssignment);
    }
}
