package com.zte.iccp.itech.extension.ability.changeorder;

import com.alibaba.fastjson2.JSONObject;
import com.zte.iccp.itech.extension.ability.EmailAbility;
import com.zte.iccp.itech.extension.ability.IdopAbility;
import com.zte.iccp.itech.extension.ability.OperationLogRecordAbility;
import com.zte.iccp.itech.extension.ability.nis.NisAbility;
import com.zte.iccp.itech.extension.common.helper.*;
import com.zte.iccp.itech.extension.common.utils.LangUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.constant.entity.ChangeOrderFieldConsts;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.JsonUtils;
import com.zte.iccp.itech.extension.domain.enums.BoolEnum;
import com.zte.iccp.itech.extension.domain.enums.OperationLogActionEnum;
import com.zte.iccp.itech.extension.domain.enums.OperatorRoleEnum;
import com.zte.iccp.itech.extension.domain.enums.TimeZoneEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentStatusEnum;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.Operator;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.SingleEmployee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.entity.Assignment;
import com.zte.iccp.itech.extension.domain.model.subentity.BatchSummary;
import com.zte.iccp.itech.extension.domain.model.subentity.MultiProductLinkageGuarantee;
import com.zte.iccp.itech.extension.domain.model.subentity.OperationObject;
import com.zte.iccp.itech.extension.domain.model.subentity.support.SupportStaffNetServiceDeptApprove;
import com.zte.iccp.itech.extension.domain.model.vo.ChangeOrderSave;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.IdopChangeOrderDto;
import com.zte.iccp.itech.extension.openapi.model.assignment.dto.NetworkOfficeDTO;
import com.zte.iccp.itech.extension.spi.client.HrClient;
import com.zte.iccp.itech.extension.spi.client.IdopClient;
import com.zte.iccp.itech.extension.spi.client.NisClient;
import com.zte.iccp.itech.extension.spi.model.nis.NetworkElementVo;
import com.zte.iccp.itech.extension.spi.model.nis.NisNetwork;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.paas.lcap.core.entity.IDataEntity;
import com.zte.paas.lcap.core.entity.IDataEntityCollection;
import com.zte.paas.lcap.core.entity.dataentity.DynamicDataEntity;
import com.zte.paas.lcap.core.entity.datamodel.IDataModel;
import com.zte.paas.lcap.core.metadata.IDataEntityType;
import com.zte.paas.lcap.ddm.common.api.form.IFormView;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.springframework.beans.BeanUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.*;


/**
 * 网络变更单UT
 *
 * <AUTHOR> jiangjiawen
 * @date 2025/3/28
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({QueryDataHelper.class,
        ConfigHelper.class,
        OperationObjectAbility.class,
        NisClient.class, IdopClient.class,
        TextValuePairHelper.class,
        FlowHelper.class, ContextHelper.class, EmployeeHelper.class,EntityHelper.class,
        MsgUtils.class, MultiProdGuaranteeAbility.class, EmailAbility.class, IdopAbility.class,
        DateUtils.class, JsonUtils.class, MsgUtils.class, HrClient.class, SaveDataHelper.class,
        OperationObjectAbility.class, NisClient.class, ConfigHelper.class, QueryDataHelper.class, NisAbility.class, LangUtils.class, BeanUtils.class})
public class ChangeOrderAbilityTest {

    @Before
    public void setUp() {
        mockStatic(DateUtils.class);
        mockStatic(JsonUtils.class);
        mockStatic(MsgUtils.class);
        mockStatic(QueryDataHelper.class);
        mockStatic(ConfigHelper.class);
        mockStatic(OperationObjectAbility.class);
        mockStatic(NisClient.class);
        mockStatic(IdopClient.class);
        mockStatic(TextValuePairHelper.class);
        mockStatic(FlowHelper.class);
        mockStatic(ContextHelper.class);
        mockStatic(EmployeeHelper.class);
        mockStatic(SaveDataHelper.class);
        mockStatic(HrClient.class);
        mockStatic(MultiProdGuaranteeAbility.class);
        mockStatic(EmailAbility.class);
        mockStatic(IdopAbility.class);
        mockStatic(EntityHelper.class);
        mockStatic(NisAbility.class);
        mockStatic(LangUtils.class);
        mockStatic(BeanUtils.class);

    }

    /* Started by AICoder, pid:i9da7z846e46b8114a8a0982611be78c1f852184 */
    @Test
    public void testCheckTime_TimeZoneIsNull() {
        IDataModel model = PowerMockito.mock(IDataModel.class);
        IFormView view = PowerMockito.mock(IFormView.class);
        when(model.getValue(anyString())).thenReturn(null);

        ChangeOrderAbility.checkTime(model, view);
        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }

    @Test
    public void testCheckTime_InvalidTimeZone() {
        IDataModel model = PowerMockito.mock(IDataModel.class);
        IFormView view = PowerMockito.mock(IFormView.class);
        JSONObject timeZone = new JSONObject();
        timeZone.put("lookupCode", "INVALID");
        when(model.getValue(eq(ChangeOrderFieldConsts.TIME_ZONE))).thenReturn(timeZone);

        ChangeOrderAbility.checkTime(model, view);
        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }

    @Test
    public void testCheckTime_StartTimeBeforeCurrentTime() throws Exception{
        JSONObject timeZone = new JSONObject();
        timeZone.put("lookupCode", "new_time_zone_code_27");
        IDataModel model = PowerMockito.mock(IDataModel.class);
        IFormView view = PowerMockito.mock(IFormView.class);
        when(model.getValue(eq(ChangeOrderFieldConsts.TIME_ZONE))).thenReturn(timeZone);

        // startTime为null
        ChangeOrderAbility.checkTime(model, view);

        // startTime不为null
        // startTime早于当前时间
        Date startTime = new Date(System.currentTimeMillis() - 1000);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_FORM);
        String startTimeStr = simpleDateFormat.format(startTime);

        when(model.getValue(eq(ChangeOrderFieldConsts.OPERATION_START_TIME))).thenReturn(startTime);
        when(DateUtils.dateToString(any(), anyString())).thenReturn(startTimeStr);
        when(DateUtils.stringToDate(anyString(), anyString())).thenReturn(startTime);
        ChangeOrderAbility.checkTime(model, view);

        // startTime晚于当前时间
        startTime = new Date(System.currentTimeMillis() + 9999);
        when(model.getValue(eq(ChangeOrderFieldConsts.OPERATION_START_TIME))).thenReturn(startTime);

        // 模拟checkNotUrgentFlag()方法执行
        Class<?> clazz = Class.forName("com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility");
        Method method = clazz.getDeclaredMethod("checkTime",
                IDataModel.class, IFormView.class);

        method.invoke(clazz, model, view);

        PowerMockito.when(TextValuePairHelper.getValue(any()))
                .thenReturn("N") // urgentFlag
                .thenReturn("ORG0000000/ORG0002700/123");
        IDataEntity iDataEntity = PowerMockito.mock(IDataEntity.class);
        PowerMockito.when(model.getRootDataEntity()).thenReturn(iDataEntity);
        PowerMockito.when(iDataEntity.getPkValue()).thenReturn(null);
        method.invoke(clazz, model, view);
        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }

    @Test
    public void testCheckNotUrgentFlag_UrgentOperation() throws Exception{
        JSONObject timeZone = new JSONObject();
        timeZone.put("lookupCode", "VALID");

        Class<?> clazz = Class.forName("com.zte.iccp.itech.extension.ability.changeorder.ChangeOrderAbility");
        Method privateMethod = clazz.getDeclaredMethod("checkNotUrgentFlag",
                IDataModel.class, IFormView.class, String.class,
                long.class, String.class, TimeZoneEnum.class);
        privateMethod.setAccessible(true);

        IDataModel model = PowerMockito.mock(IDataModel.class);
        IFormView view = PowerMockito.mock(IFormView.class);

        when(model.getValue(eq("operation_type_group"))).thenReturn("COOPERATION_GUARANTEE");

        privateMethod.invoke(clazz, model, view, "N", 10210, "N", TimeZoneEnum.CASABLANCA);
        privateMethod.invoke(clazz, model, view, "Y", 10210, "N", TimeZoneEnum.CASABLANCA);
        privateMethod.invoke(clazz, model, view, "Y", 10210, "GUARANTEE", TimeZoneEnum.CASABLANCA);
        privateMethod.invoke(clazz, model, view, "N", 10210, "GUARANTEE", TimeZoneEnum.CASABLANCA);

        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }
    /* Ended by AICoder, pid:i9da7z846e46b8114a8a0982611be78c1f852184 */


    @Test
    public void todoTransferBeforeCheck() {

        // 场景1：类型不是网络变更单/合作方网络变更单  同时结果assignmentList为空
        List<Assignment> assignments = Lists.newArrayList();
        Assignment assignment = new Assignment() {{
            setId("id1");
            // 批次任务
            setAssignmentType(Lists.newArrayList(new TextValuePair() {{
                // 批次任务
                setValue("2");
            }}));
            setAssignmentStatus("1");
        }};
        assignments.add(assignment);

        AssignmentTypeEnum.fromTextValuePair(assignment.getAssignmentType());
        AssignmentStatusEnum.fromValue(assignment.getAssignmentStatus());

        String assignmentCodes = ChangeOrderAbility.todoTransferBeforeCheck(assignments);

        Assert.assertNull(assignmentCodes);


        // 场景2：类型是网络变更单状态不是执行中    同时结果assignmentList为空
        Assignment assignment2 = new Assignment() {{
            setId("id2");
            setAssignmentType(Lists.newArrayList(new TextValuePair() {{
                // 网络变更单
                setValue("1");
            }}));
            setAssignmentStatus("1");
        }};
        assignments.add(assignment2);
        AssignmentTypeEnum.fromTextValuePair(assignment2.getAssignmentType());
        AssignmentStatusEnum.fromValue(assignment2.getAssignmentStatus());

        assignmentCodes = ChangeOrderAbility.todoTransferBeforeCheck(assignments);
        Assert.assertNull(assignmentCodes);
        // 场景3：类型是合作方网络变更单状态是执行中   同时结果assignmentList不为空
        Assignment assignment3 = new Assignment() {{
            setId("id3");
            // 批次任务
            setAssignmentType(Lists.newArrayList(new TextValuePair() {{
                // 网络变更单
                setValue("1");
            }}));
            setAssignmentStatus("2");
        }};

        AssignmentTypeEnum.fromTextValuePair(assignment3.getAssignmentType());
        AssignmentStatusEnum.fromValue(assignment3.getAssignmentStatus());

        assignments.clear();
        assignments.add(assignment3);
        assignmentCodes = ChangeOrderAbility.todoTransferBeforeCheck(assignments);
        Assert.assertNotNull(assignmentCodes);

    }

    @Test
    public void getEntryAllColumnData() {
        IDataEntityCollection dataEntityCollection = PowerMockito.mock(IDataEntityCollection.class);
        DynamicDataEntity dynamicDataEntity = PowerMockito.mock(DynamicDataEntity.class);

        dataEntityCollection.add(dynamicDataEntity);

        List<String> entryAllColumnData = ChangeOrderAbility.getEntryAllColumnData(dataEntityCollection, "batch_no");
        Assert.assertTrue(entryAllColumnData.isEmpty());
    }

    @Test
    @SneakyThrows
    public void getTimeZoneTipsDataMapTest() {
        // 主流程
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.BEIJING;
        TimeZoneEnum timeZoneEnum2 = TimeZoneEnum.EASTERN_TIME;
        String deptId = "ORG0000000/ORG2223728/666";
        when(DateUtils.dateToString(any(), anyString())).thenReturn("2024-10-22 10:00:00");

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_FORM);
        Date date =  simpleDateFormat.parse("2024-10-22 10:00:00");
        when(DateUtils.stringToDate(anyString(), anyString())).thenReturn(date);

        Assert.assertNotNull(ChangeOrderAbility.getTimeZoneTipsDataMap(new Date(), "666", timeZoneEnum, deptId, ""));

        // 非东八区时区分支
        ChangeOrderAbility.getTimeZoneTipsDataMap(new Date(), "666", timeZoneEnum2, "666", "");

        // IDOP提示语展示分支
        ChangeOrderAbility.getTimeZoneTipsDataMap(new Date(), "666", timeZoneEnum, "666", "1111");

        // 页面时间为空分支
        ChangeOrderAbility.getTimeZoneTipsDataMap(null, "666", timeZoneEnum, deptId, "1111");
    }

    @Test
    public void getIdopTimeTipsTest() {
        // 主流程
        Map<String,String> timeZoneMap = new HashMap<>();
        timeZoneMap.put("lookupCode", "Y");

        when(JsonUtils.parseObject(any(), eq(Map.class))).thenReturn(timeZoneMap);
        when(MsgUtils.getMessage(anyString())).thenReturn("666");
        Assert.assertNotNull(ChangeOrderAbility.getIdopTimeTips( "IDOP", "666"));

        // 【时间冲突】不为Y分支
        timeZoneMap.put("lookupCode", "N");
        ChangeOrderAbility.getIdopTimeTips( "IDOP", "666");

        // 非IDOP提示语分支
        ChangeOrderAbility.getIdopTimeTips("CNOP", "666");
        ChangeOrderAbility.getIdopTimeTips("IDOP", null);
    }

    @Test
    public void isGroupDirectManagementNetworkTest() {
        // 主流程
        ChangeOrder changeOrder = new ChangeOrder();
        changeOrder.setResponsibleDept("ORG0000000/ORG2223728/ORG2223781/ORG2223820/666");
        changeOrder.setIsGovEnt(BoolEnum.N);

        OperationObject object1 = new OperationObject();
        object1.setBatchNo(TextValuePairHelper.buildList("1","1","1"));
        object1.setNetworkId("111");
        OperationObject object2 = new OperationObject();
        List<OperationObject> opObjects = Lists.newArrayList(object1, object2);

        NisNetwork network = new NisNetwork();
        network.setGrpDrctMngFlag(BoolEnum.Y);
        List<NisNetwork> nisNetworks = Lists.newArrayList(network);

        when(QueryDataHelper.get(eq(ChangeOrder.class), anyList(), anyString())).thenReturn(changeOrder);
        when(ConfigHelper.get(anyString())).thenReturn("ORG0000000/ORG2223728/ORG2223781/ORG2223820");
        when(OperationObjectAbility.listOperationObject(any(), eq(OperationObject.class)))
                .thenReturn(opObjects);
        when(NisClient.queryNetwork(any())).thenReturn(nisNetworks);
        Assert.assertTrue(ChangeOrderAbility.isGroupDirectManagementNetwork("666",""));

        // 返回值为false分支
        network.setGrpDrctMngFlag(BoolEnum.N);
        Assert.assertFalse(ChangeOrderAbility.isGroupDirectManagementNetwork("666",""));

        // 代表处校验不通过分支
        changeOrder.setResponsibleDept("ORG0000000/ORG2223728/ORG2223781/666");
        Assert.assertFalse(ChangeOrderAbility.isGroupDirectManagementNetwork("666",""));

        changeOrder.setIsGovEnt(BoolEnum.Y);
        Assert.assertFalse(ChangeOrderAbility.isGroupDirectManagementNetwork("666",""));
        Assert.assertFalse(ChangeOrderAbility.isGroupDirectManagementNetwork("666","1"));

        changeOrder.setResponsibleDept("666");
        Assert.assertFalse(ChangeOrderAbility.isGroupDirectManagementNetwork("666",""));

        when(QueryDataHelper.get(eq(ChangeOrder.class), anyList(), anyString())).thenReturn(null);

        // 入参为空分支
        when(QueryDataHelper.get(eq(ChangeOrder.class), anyList(), anyString())).thenReturn(null);
        Assert.assertFalse(ChangeOrderAbility.isGroupDirectManagementNetwork("666",""));
    }

    @Test
    public void saveAssignmentTask() {
        ChangeOrderSave changeOrder = new ChangeOrderSave();
        IdopChangeOrderDto dto = new IdopChangeOrderDto();
        changeOrder.setResponsibleDept(TextValuePairHelper.buildList("1/1/1/1", "1/1/1/1", "1/1/1/1"));
        when(HrClient.queryEmployeeInfo(anyList()))
                .thenReturn(null);
        when(TextValuePairHelper.getValue(any()))
                .thenReturn("1/1/1/1");
        ChangeOrderAbility.saveAssignmentTask(changeOrder, dto, "1");
        Assert.assertNotNull(dto);
        dto.setTimeZone("new_time_zone_code_27");
        ChangeOrderAbility.saveAssignmentTask(changeOrder, dto, "1");
        dto.setTimeZone("new_time_zone_code_26");
        ChangeOrderAbility.saveAssignmentTask(changeOrder, dto, "1");

    }

    @Test
    public void getSupportPersonnelEmpUIIDs() {
        // 1.supportPersonnelList为空
        when(QueryDataHelper.query(eq(SupportStaffNetServiceDeptApprove.class),anyList(),anyString())).thenReturn(null);
        List<String> supportPersonnelEmpUIIDs = ChangeOrderAbility.getSupportPersonnels(
                SupportStaffNetServiceDeptApprove.class, "1");
        Assert.assertTrue(supportPersonnelEmpUIIDs.isEmpty());

        // 1.supportPersonnelList正常场景
        when(QueryDataHelper.query(eq(SupportStaffNetServiceDeptApprove.class),anyList(),anyString()))
                .thenReturn(Lists.newArrayList(new SupportStaffNetServiceDeptApprove(){{
                    setPerson(new SingleEmployee(){{
                        setEmpUIID("1");
                    }});
                }}));
       supportPersonnelEmpUIIDs = ChangeOrderAbility.getSupportPersonnels(
               SupportStaffNetServiceDeptApprove.class, "1");
        Assert.assertEquals(1, supportPersonnelEmpUIIDs.size());


    }

    @Test
    public void getGuaranteeIdsByChangeOrderId() {
        ChangeOrderAbility.getGuaranteeIdsByChangeOrderId("1");

        ChangeOrder changeOrder = new ChangeOrder();
        changeOrder.setMultiProdGuarantee(BoolEnum.Y);
        when(QueryDataHelper.get(eq(ChangeOrder.class), anyList(), anyString()))
                .thenReturn(changeOrder);
        ChangeOrderAbility.getGuaranteeIdsByChangeOrderId("1");

        MultiProductLinkageGuarantee tee = new MultiProductLinkageGuarantee();
        tee.setAssignmentId("1");
        tee.setIsMainTask(false);
        when(MultiProdGuaranteeAbility.query(anyList()))
                .thenReturn(Lists.newArrayList(tee));
        Assert.assertNotNull(ChangeOrderAbility.getGuaranteeIdsByChangeOrderId("1"));
    }

    @Test
    public void revoke() {
        // 1.getSource == null
        List<TextValuePair> assignmentType = new ArrayList<>();
        TextValuePair textValuePair = new TextValuePair();
        textValuePair.setValue("6");
        assignmentType.add(textValuePair);

        FlowHelper.revokeFlow(anyString(), anyString());
        when(SaveDataHelper.update(any())).thenReturn(true);

        OperationLogRecordAbility.saveActionOperationLog("1", OperationLogActionEnum.REVOKE.getOperationCode(), new Date());

        when(QueryDataHelper.get(eq(SubcontractorChangeOrder.class),anyList(),anyString())).thenReturn(new SubcontractorChangeOrder(){{
            setId("1");
            setSource(null);
        }});

        Assignment assignment = new Assignment();
        assignment.setEntityId("1");
        assignment.setAssignmentType(assignmentType);
        assignment.setId("1");

        List<Employee> employees = Lists.newArrayList(new Employee() {{
            setEmpUIID("1");
        }});
        assignment.setResponsibleEmployee(employees);
        assignment.setAssignmentCode("CO2025100");
        assignment.setAssignmentName("1");
        assignment.setCurrentProcessorEmployee(employees);

        ChangeOrderAbility.revoke(assignment);
        Assert.assertNotNull(assignment);
    }

    @Test
    public void revokeBeforeCheck() {
        // 1.操作用户不为责任人
        when(ContextHelper.getEmpNo()).thenReturn("user1");
        when(EmployeeHelper.getEpmUIID(any())).thenReturn(Lists.newArrayList("user2"));

        Assignment assignment = new Assignment();
        // 执行中
        assignment.setAssignmentStatus("2");
        List<Employee> employees = new ArrayList<>();
        Employee employee = new Employee();
        employee.setEmpUIID("user2");
        assignment.setResponsibleEmployee(employees);
        assignment.setEntityId("1");
        boolean approvedFlag = ChangeOrderAbility.revokeBeforeCheck(assignment);
        Assert.assertFalse(approvedFlag);

        // 2.操作用户为责任人，但是单据状态不是审核中
        employee.setEmpUIID("user1");
        when(ContextHelper.getEmpNo()).thenReturn("user1");
        when(EmployeeHelper.getEpmUIID(any())).thenReturn(Lists.newArrayList("user1"));
        approvedFlag = ChangeOrderAbility.revokeBeforeCheck(assignment);
        Assert.assertFalse(approvedFlag);

        // 3.操作用户为责任人，单据状态是审核中，但单据被审核过
        assignment.setAssignmentStatus("3");
        when(ContextHelper.getEmpNo()).thenReturn("user1");
        when(EmployeeHelper.getEpmUIID(any())).thenReturn(Lists.newArrayList("user1"));
        when(FlowHelper.isApproved(anyString())).thenReturn(true);
        approvedFlag = ChangeOrderAbility.revokeBeforeCheck(assignment);
        Assert.assertFalse(approvedFlag);

        // 4.正常场景
        when(ContextHelper.getEmpNo()).thenReturn("user1");
        when(EmployeeHelper.getEpmUIID(any())).thenReturn(Lists.newArrayList("user1"));
        when(FlowHelper.isApproved(anyString())).thenReturn(false);
        approvedFlag = ChangeOrderAbility.revokeBeforeCheck(assignment);
        Assert.assertTrue(approvedFlag);
    }

    @Test
    public void checkOperatorsCount(){
        IDataModel model = mock(IDataModel.class);
        IDataEntityCollection dataEntityCollection = mock(IDataEntityCollection.class);
        when(EntityHelper.getEntityId(eq(Operator.class)))
                .thenReturn(null);
        Assert.assertTrue(ChangeOrderAbility.checkOperatorsCount(model, Operator.class));

        IDataEntityType iDataEntityType = PowerMockito.mock(IDataEntityType.class);
        PowerMockito.when(dataEntityCollection.getDataEntityType()).thenReturn(iDataEntityType);
        SingleEmployee employee = new SingleEmployee();
        employee.setEmpUIID("1");

        TextValuePair textValuePair = new TextValuePair();
        textValuePair.setValue("LOCAL");
        PowerMockito.when(JsonUtils.parseArray((Object) any(), (Class<?>) any()))
                .thenReturn(Arrays.asList(employee));
    }

    @Test
    public void testCreateChangeOrder() {
        // 测试场景1：完整数据流程
        IdopChangeOrderDto dto = new IdopChangeOrderDto();
        dto.setChangeOrderId("test-change-order-id");
        dto.setNeCount(2);
        dto.setOperationStartTime(new Date());
        dto.setOperationEndTime(new Date(System.currentTimeMillis() + 3600000));
        dto.setTimeConflict(true);

        // 准备网络数据
        List<NetworkOfficeDTO> networks = Lists.newArrayList();
        NetworkOfficeDTO network1 = new NetworkOfficeDTO();
        network1.setNetworkId("network-1");
        network1.setOfficeName("Office 1");
        network1.setOfficeId("office-1");
        networks.add(network1);

        NetworkOfficeDTO network2 = new NetworkOfficeDTO();
        network2.setNetworkId("network-2");
        network2.setOfficeName("Office 2");
        network2.setOfficeId("office-2");
        networks.add(network2);

        dto.setNetwork(networks);

        // 准备操作人员数据
        List<String> operators = Lists.newArrayList("emp-1", "emp-2");
        dto.setOperators(operators);

        // Mock TextValuePairHelper.buildList
        List<TextValuePair> batchNoList = Lists.newArrayList();
        TextValuePair batchNo = new TextValuePair();
        batchNo.setValue("1");
        batchNoList.add(batchNo);
        when(TextValuePairHelper.buildList("1", "1", "1")).thenReturn(batchNoList);

        // Mock Y常量
        TextValuePair yValue = new TextValuePair();
        yValue.setValue(Y);
        List<TextValuePair> yList = Lists.newArrayList(yValue);
        when(TextValuePairHelper.buildList(eq(Y), eq(EMPTY_STRING), eq(EMPTY_STRING))).thenReturn(yList);

        // Mock SaveDataHelper
        when(SaveDataHelper.create(any(BatchSummary.class))).thenReturn("batch-summary-id");
        when(SaveDataHelper.batchCreate(anyList())).thenReturn(Lists.newArrayList("op-obj-1", "op-obj-2"));

        // Mock NisAbility.queryNisElmentMap
        Map<String, NetworkElementVo> networkElementMap = new HashMap<>();
        NetworkElementVo element1 = new NetworkElementVo();
        element1.setId("office-1");
        element1.setProdModelId("prod-model-1");
        networkElementMap.put("office-1", element1);

        NetworkElementVo element2 = new NetworkElementVo();
        element2.setId("office-2");
        element2.setProdModelId("prod-model-2");
        networkElementMap.put("office-2", element2);

        when(NisAbility.queryNisElmentMap(anyList())).thenReturn(networkElementMap);

        // Mock HrClient.queryEmployeeInfo
        List<Employee> employees = Lists.newArrayList();
        Employee emp1 = new Employee();
        emp1.setEmpUIID("emp-1");
        emp1.setEmpName("Employee 1");
        emp1.setOrgNamePath("Org Path 1");
        emp1.setOrgNamePathEn("Org Path 1 EN");
        employees.add(emp1);

        Employee emp2 = new Employee();
        emp2.setEmpUIID("emp-2");
        emp2.setEmpName("Employee 2");
        emp2.setOrgNamePath("Org Path 2");
        emp2.setOrgNamePathEn("Org Path 2 EN");
        employees.add(emp2);

        when(HrClient.queryEmployeeInfo(anyList())).thenReturn(employees);

        // Mock LangUtils.get
        when(LangUtils.get(anyString(), anyString())).thenReturn("Mocked Org Name");

        // 执行测试方法
        ChangeOrderAbility.createChangeOrder(dto);

        // 测试场景2：网络数据为空
        IdopChangeOrderDto dto2 = new IdopChangeOrderDto();
        dto2.setChangeOrderId("test-change-order-id-2");
        dto2.setNeCount(0);
        dto2.setOperationStartTime(new Date());
        dto2.setOperationEndTime(new Date(System.currentTimeMillis() + 3600000));
        dto2.setTimeConflict(false);
        dto2.setNetwork(Lists.newArrayList()); // 空网络列表
        dto2.setOperators(Lists.newArrayList("emp-1"));

        ChangeOrderAbility.createChangeOrder(dto2);

        // 测试场景3：操作人员为空
        IdopChangeOrderDto dto3 = new IdopChangeOrderDto();
        dto3.setChangeOrderId("test-change-order-id-3");
        dto3.setNeCount(1);
        dto3.setOperationStartTime(new Date());
        dto3.setOperationEndTime(new Date(System.currentTimeMillis() + 3600000));
        dto3.setTimeConflict(false);
        dto3.setNetwork(networks);
        dto3.setOperators(Lists.newArrayList()); // 空操作人员列表

        ChangeOrderAbility.createChangeOrder(dto3);

        // 测试场景4：网络元素映射为空
        when(NisAbility.queryNisElmentMap(anyList())).thenReturn(new HashMap<>());
        ChangeOrderAbility.createChangeOrder(dto);

        // 测试场景5：员工信息查询为空
        when(HrClient.queryEmployeeInfo(anyList())).thenReturn(Lists.newArrayList());
        ChangeOrderAbility.createChangeOrder(dto);

        // 测试场景6：网络数据包含空的officeId
        List<NetworkOfficeDTO> networksWithEmptyOffice = Lists.newArrayList();
        NetworkOfficeDTO networkWithEmptyOffice = new NetworkOfficeDTO();
        networkWithEmptyOffice.setNetworkId("network-empty");
        networkWithEmptyOffice.setOfficeName("Office Empty");
        networkWithEmptyOffice.setOfficeId(""); // 空的officeId
        networksWithEmptyOffice.add(networkWithEmptyOffice);

        NetworkOfficeDTO networkWithNullOffice = new NetworkOfficeDTO();
        networkWithNullOffice.setNetworkId("network-null");
        networkWithNullOffice.setOfficeName("Office Null");
        networkWithNullOffice.setOfficeId(null); // null的officeId
        networksWithEmptyOffice.add(networkWithNullOffice);

        dto.setNetwork(networksWithEmptyOffice);
        ChangeOrderAbility.createChangeOrder(dto);

        // 测试场景7：操作人员列表包含员工信息查询不到的情况
        dto.setNetwork(networks); // 恢复正常网络数据
        dto.setOperators(Lists.newArrayList("emp-not-exist"));

        // Mock员工查询返回部分结果
        List<Employee> partialEmployees = Lists.newArrayList();
        Employee existingEmp = new Employee();
        existingEmp.setEmpUIID("emp-exist");
        existingEmp.setEmpName("Existing Employee");
        existingEmp.setOrgNamePath("Existing Org Path");
        existingEmp.setOrgNamePathEn("Existing Org Path EN");
        partialEmployees.add(existingEmp);

        when(HrClient.queryEmployeeInfo(anyList())).thenReturn(partialEmployees);
        ChangeOrderAbility.createChangeOrder(dto);

        // 验证方法调用
        Assert.assertNotNull(dto);
    }
}
