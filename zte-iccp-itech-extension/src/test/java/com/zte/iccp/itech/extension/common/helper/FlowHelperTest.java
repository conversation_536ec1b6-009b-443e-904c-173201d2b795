package com.zte.iccp.itech.extension.common.helper;

import com.alibaba.fastjson.JSONObject;
import com.zte.iccp.itech.extension.common.LcapBusiException;
import com.zte.iccp.itech.extension.common.utils.DateUtils;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.changeorder.ApproveNodeEnum;
import com.zte.iccp.itech.extension.domain.enums.flow.ApproveFlowCodeEnum;
import com.zte.iccp.itech.extension.domain.model.ActiveApproverNode;
import com.zte.iccp.itech.zlic.util.Lists;
import com.zte.iss.approval.sdk.bean.*;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.paas.lcap.common.api.ParamServiceHelper;
import com.zte.paas.lcap.common.context.RequestContextHolder;
import com.zte.paas.lcap.ddm.common.api.dto.UacParamDTO;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecord;
import com.zte.paas.lcap.ddm.domain.flow.dto.ApproveRecordsDTO;
import com.zte.paas.lcap.ddm.domain.repository.IParameterRepository;
import com.zte.paas.lcap.flow.adapter.repository.impl.FlowServiceHelper;
import com.zte.paas.lcap.flow.dto.ApproveTask;
import com.zte.paas.lcap.flow.dto.FlowInfo;
import com.zte.paas.lcap.flow.dto.FlowInstDTO;
import com.zte.paas.lcap.flow.gateway.impl.FlowInstGatewayImpl;
import com.zte.paas.lcap.platform.util.RequestHeaderUtils;
import org.junit.Assert;
import org.junit.Before;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.BeanFactory;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.ACTIVE_TASK_STATUS;
import static com.zte.iccp.itech.extension.domain.constant.CommonConstants.COMPLETED_TASK_STATUS;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.mockito.Mockito.when;

import org.mockito.ArgumentCaptor;
import java.util.Collections;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @create 2025/3/20 下午4:17
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({FlowServiceHelper.class, ContextHelper.class, ConfigHelper.class, RequestHeaderUtils.class,
        ApprovalFlowClient.class, ParamServiceHelper.class, SpringContextUtil.class, RequestContextHolder.class,
        ApproveNodeEnum.class, MsgUtils.class})
public class FlowHelperTest {

    @Mock
    private BeanFactory beanFactory;

    @Mock
    private FlowInstGatewayImpl flowInstGateway;

    @Mock
    private IParameterRepository parameterRepository;

    @InjectMocks
    private FlowHelper flowHelper;

    @Before
    public void setUp() {
        mockStatic(FlowServiceHelper.class);
        mockStatic(ParamServiceHelper.class);
        mockStatic(ApprovalFlowClient.class);
        mockStatic(ContextHelper.class);
        mockStatic(SpringContextUtil.class);
        mockStatic(MsgUtils.class);
        when(SpringContextUtil.getBeanFactory()).thenReturn(beanFactory);
    }

    @Test
    public void getCustomizeFlowVersion() {
        flowHelper.getCustomizeFlowVersion("1");
        List<FlowInfo> flowInfos = new ArrayList<>();
        FlowInfo info = new FlowInfo();
        info.setFlowInstanceId("1");
        flowInfos.add(info);
        when(FlowServiceHelper.queryFlowInfoByBizId(any())).thenReturn(flowInfos);
        FlowInstanceVo vo = new FlowInstanceVo();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("1", "1");
        vo.setBusinessParam(jsonObject);
        when(ApprovalFlowClient.getFlowInstance(any())).thenReturn(vo);
        Assert.assertNotNull(flowHelper.getCustomizeFlowVersion("1"));
    }

    @Test
    public void pushSystemNode() {
        flowHelper.pushSystemNode("1", "1", false, "");

        List<FlowInfo> infos = new ArrayList<>();
        FlowInfo info = new FlowInfo();
        infos.add(info);
        when(FlowServiceHelper.queryFlowInfoByBizId(any())).thenReturn(infos);
        flowHelper.pushSystemNode("1", "1", false, "");
        PowerMockito.verifyStatic(ApprovalFlowClient.class, times(1));
        ApprovalFlowClient.closeActiveNodes(any());
    }

    @Test
    public void getAfterSubmitApprovedRecords() {
        // 1.allRecords为空场景
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(null);

        List<ApproveRecord> approvedRecords = FlowHelper.getAfterSubmitApprovedRecords("1");
        Assert.assertTrue(approvedRecords.isEmpty());


        // 2.submitTime == 0场景
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("1", "1");
        getAfterSubmitApprovedRecordsProcess(jsonObject,"BATCH_INTL_ADMIN_APPROVAL","BATCH_INTL_ADMIN_APPROVAL2","REASSIGN_TASK_STATUS");
        approvedRecords = FlowHelper.getAfterSubmitApprovedRecords("1");
        Assert.assertNotNull(approvedRecords);

        // strSubmitTime为-场景
        jsonObject.put("submitTime", "-");
        getAfterSubmitApprovedRecordsProcess(jsonObject,"BATCH_INTL_ADMIN_APPROVAL","BATCH_INTL_ADMIN_APPROVAL2",null);
        approvedRecords = FlowHelper.getAfterSubmitApprovedRecords("1");
        Assert.assertNotNull(approvedRecords);


        // 3.正常场景
        jsonObject.put("submitTime", "100000");
        getAfterSubmitApprovedRecordsProcess(jsonObject,"BATCH_INTL_ADMIN_APPROVAL","BATCH_INTL_ADMIN_APPROVAL2",null);
        approvedRecords = FlowHelper.getAfterSubmitApprovedRecords("1");
        Assert.assertNotNull(approvedRecords);

        // 4.intlApprovalRecordList为空场景
        jsonObject.put("submitTime", "100000");
        getAfterSubmitApprovedRecordsProcess(jsonObject,"PENDING_NOTIFICATION","B",null);
        approvedRecords = FlowHelper.getAfterSubmitApprovedRecords("1");
        Assert.assertNotNull(approvedRecords);

        jsonObject.put("submitTime", "100000");
        getAfterSubmitApprovedRecordsProcess(jsonObject,"PENDING_NOTIFICATION","B","REASSIGN_TASK_STATUS");
        approvedRecords = FlowHelper.getAfterSubmitApprovedRecords("1");
        Assert.assertNotNull(approvedRecords);

        // 2025-03-28 00:00:00
        jsonObject.put("submitTime", "1743091200");
        getAfterSubmitApprovedRecordsProcess(jsonObject,"PENDING_NOTIFICATION","B","REASSIGN_TASK_STATUS");
        approvedRecords = FlowHelper.getAfterSubmitApprovedRecords("1");
        Assert.assertNotNull(approvedRecords);
    }

    /**
     * getAfterSubmitApprovedRecords通用处理方法
     * @param jsonObject jsonObject
     */
    private void getAfterSubmitApprovedRecordsProcess(JSONObject jsonObject,String intlApproveNode,String otherApproveNode,String taskStatus ) {
        List<ApproveRecord> approveRecordList = new ArrayList<>();
        ApproveRecord approveRecord1 = new ApproveRecord() {{
            setExtendedCode(intlApproveNode);
            setCreatedDate(new Date());
            setTaskStatus("COMPLETED");
        }};

        ApproveRecord approveRecord2 = new ApproveRecord() {{
            setExtendedCode(otherApproveNode);
            setCreatedDate(new Date());
            setTaskStatus(taskStatus != null ? taskStatus : "ACTIVE");
        }};

        ApproveRecord approveRecord3 = new ApproveRecord() {{
            // 节点不为PENDING_NOTIFICATION并且创建时间小于submitTime，并且非指派状态
            setExtendedCode("otherApproveNode3");
            // 毫秒值为1742227200
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
            setTaskStatus("REASSIGN");
        }};

        ApproveRecord approveRecord4 = new ApproveRecord() {{
            // 节点不为PENDING_NOTIFICATION并且创建时间小于submitTime，并且为指派状态
            setExtendedCode("otherApproveNode4");
            // 毫秒值为1742227200
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
            // 非重新指派
            setTaskStatus("REASSIGN");
        }};
        approveRecordList.add(approveRecord1);
        approveRecordList.add(approveRecord2);
        approveRecordList.add(approveRecord3);
        approveRecordList.add(approveRecord4);
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(approveRecordList);

        when(FlowServiceHelper.queryFlowInfoByBizId(any())).thenReturn(Lists.newArrayList(new FlowInfo(){{
            setFlowCode(ApproveFlowCodeEnum.BATCH_TASK_FLOW.name());
            setFlowInstanceId("1");
        }}));

        FlowInstanceVo vo = new FlowInstanceVo();
        vo.setBusinessParam(jsonObject);
        when(ApprovalFlowClient.getFlowInstance(any())).thenReturn(vo);
    }

    @Test
    public void getFlowCreateDateTest() {
        Date data = FlowHelper.getFlowCreateDate(null);
        Assert.assertNull(data);

        // 流程状态为EXTERNALLY_TERMINATED
        when(FlowServiceHelper.queryFlowInfoByBizId(any())).thenReturn(Lists.newArrayList(new FlowInfo(){{
            setStatusCode("EXTERNALLY_TERMINATED");
        }}));
        data = FlowHelper.getFlowCreateDate("1");
        Assert.assertNull(data);

        List<FlowInstDTO> flowInstDTOs = Lists.newArrayList();
        when(flowInstGateway.getFlowInstInfoByFlowInstIds(any())).thenReturn(flowInstDTOs);
        data = FlowHelper.getFlowCreateDate("1");
        when(FlowServiceHelper.queryFlowInfoByBizId(any())).thenReturn(Lists.newArrayList(new FlowInfo()));
        when(SpringContextUtil.getBean(eq(FlowInstGatewayImpl.class))).thenReturn(flowInstGateway);
        FlowHelper.getFlowCreateDate("1");
        Assert.assertNotNull(flowInstDTOs);
    }


    @Test
    public void getApprovedRecordsTest() {
        List<ApproveRecord> approveRecords = FlowHelper.getApprovedRecords("1");
        Assert.assertNotNull(approveRecords);
        FlowInfo flowInfo = new FlowInfo();
        flowInfo.setFlowCode("CHANGE_ORDER_COMP_FLOW");
        List<FlowInfo> flowInfos = Lists.newArrayList(flowInfo);
        when(FlowServiceHelper.queryFlowInfoByBizId(any())).thenReturn(flowInfos);
        approveRecords = FlowHelper.getApprovedRecords("1");
        Assert.assertNotNull(approveRecords);
        flowInfo.setFlowCode("test");
        approveRecords = FlowHelper.getApprovedRecords("1");
        Assert.assertNotNull(approveRecords);
    }

    @Test
    public void getAllSpecialDealApprovedRecordsTest() {
        List<ApproveRecord> approveRecords = FlowHelper.getAllSpecialDealApprovedRecords("1");
        Assert.assertNotNull(approveRecords);

        List<ApproveRecord> approveRecordRes = Lists.newArrayList();
        ApproveRecord approveRecord1 = new ApproveRecord() {{
            setExtendedCode("A");
            setApprovalDate(null);
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord2 = new ApproveRecord() {{
            setExtendedCode("A");
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord3 = new ApproveRecord() {{
            setExtendedCode("A");
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 01:00:01","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord4 = new ApproveRecord() {{
            setExtendedCode("B");
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord5 = new ApproveRecord() {{
            setExtendedCode("B");
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord6 = new ApproveRecord() {{
            setExtendedCode("B");
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 01:00:01","yyyy-MM-dd HH:mm:ss"));
        }};
        approveRecordRes.add(approveRecord1);
        approveRecordRes.add(approveRecord2);
        approveRecordRes.add(approveRecord4);
        approveRecordRes.add(approveRecord3);
        approveRecordRes.add(approveRecord5);
        approveRecordRes.add(approveRecord6);
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(approveRecordRes);
        approveRecords = FlowHelper.getAllSpecialDealApprovedRecords("1");
        Assert.assertNotNull(approveRecords);
    }

    @Test
    public void filterApprovedRecordsTest() throws Exception{
        List<ApproveRecord> approveRecordRes = Lists.newArrayList();
        Whitebox.invokeMethod(FlowHelper.class, "filterApprovedRecords",approveRecordRes);
        ApproveRecord approveRecord1 = new ApproveRecord() {{
            setExtendedCode("A");
            setTaskStatus(ACTIVE_TASK_STATUS);
            setApprovalDate(null);
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord2 = new ApproveRecord() {{
            setExtendedCode("A");
            setTaskStatus("test");
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord3 = new ApproveRecord() {{
            setExtendedCode("A");
            setTaskStatus(COMPLETED_TASK_STATUS);
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 01:00:01","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord4 = new ApproveRecord() {{
            setExtendedCode("B");
            setTaskStatus("test");
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord5 = new ApproveRecord() {{
            setExtendedCode("B");
            setTaskStatus(COMPLETED_TASK_STATUS);
            setApprovalDate(new Date());
            setCreatedDate(DateUtils.stringToDate("2025-03-18 00:00:00","yyyy-MM-dd HH:mm:ss"));
        }};
        ApproveRecord approveRecord6 = new ApproveRecord() {{
            setExtendedCode("B");
            setApprovalDate(new Date());
            setTaskStatus(COMPLETED_TASK_STATUS);
            setCreatedDate(DateUtils.stringToDate("2025-03-18 01:00:01","yyyy-MM-dd HH:mm:ss"));
        }};
        approveRecordRes.add(approveRecord1);
        approveRecordRes.add(approveRecord2);
        approveRecordRes.add(approveRecord3);
        approveRecordRes.add(approveRecord4);
        approveRecordRes.add(approveRecord5);
        approveRecordRes.add(approveRecord6);
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(approveRecordRes);
        Whitebox.invokeMethod(FlowHelper.class, "filterApprovedRecords",approveRecordRes);
        Assert.assertNotNull(approveRecord1);
    }

    @Test
    public void batchQueryNodesTest() {
        // 模拟 ContextHelper 方法
        when(ContextHelper.getUacAppId()).thenReturn("testAppId");
        when(ContextHelper.getAccessSecret()).thenReturn("testSecret");
        when(ContextHelper.getEmpNo()).thenReturn("testEmpNo");
        when(ContextHelper.getTenantId()).thenReturn("testTenantId");
        
        // 模拟 RequestContextHolder
        mockStatic(RequestContextHolder.class);
        when(RequestContextHolder.getAppId()).thenReturn("testAppId");
        
        // 模拟 SpringContextUtil
        mockStatic(SpringContextUtil.class);
        BeanFactory beanFactory = PowerMockito.mock(BeanFactory.class);
        when(SpringContextUtil.getBeanFactory()).thenReturn(beanFactory);
        
        // 模拟 IParameterRepository
        IParameterRepository parameterRepository = PowerMockito.mock(IParameterRepository.class);
        UacParamDTO uacParamDTO = new UacParamDTO();
        uacParamDTO.setAppId("testAppId");
        uacParamDTO.setAccessSecret("testSecret");
        when(parameterRepository.getUacParams(anyString(), anyString())).thenReturn(uacParamDTO);
        when(beanFactory.getBean(IParameterRepository.class)).thenReturn(parameterRepository);
        
        // 模拟 FlowInstGatewayImpl
        when(SpringContextUtil.getBean(FlowInstGatewayImpl.class)).thenReturn(flowInstGateway);
        
        // 1. 测试空输入场景
        Map<String, ApprovalProcessDTO> result = FlowHelper.batchQueryNodes(null);
        Assert.assertTrue(result.isEmpty());
        
        result = FlowHelper.batchQueryNodes(Collections.emptyList());
        Assert.assertTrue(result.isEmpty());
        
        // 2. 测试正常输入场景
        List<String> businessIds = Arrays.asList("1", "2", "3");
        List<FlowInstDTO> flowInstDTOList = new ArrayList<>();
        
        // 创建测试数据
        FlowInstDTO flowInstDTO1 = new FlowInstDTO();
        flowInstDTO1.setFlowInstanceId("flow1");
        flowInstDTO1.setBusinessId("1");
        
        FlowInstDTO flowInstDTO2 = new FlowInstDTO();
        flowInstDTO2.setFlowInstanceId("flow2");
        flowInstDTO2.setBusinessId("2");
        
        FlowInstDTO flowInstDTO3 = new FlowInstDTO();
        flowInstDTO3.setFlowInstanceId("flow3");
        flowInstDTO3.setBusinessId("3");
        
        flowInstDTOList.add(flowInstDTO1);
        flowInstDTOList.add(flowInstDTO2);
        flowInstDTOList.add(flowInstDTO3);
        
        // Mock FlowInstGateway
        when(SpringContextUtil.getBean(eq(FlowInstGatewayImpl.class))).thenReturn(flowInstGateway);
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(flowInstDTOList);
        
        // Mock RequestHeaderUtils
        mockStatic(RequestHeaderUtils.class);
        when(RequestHeaderUtils.getEmpNo()).thenReturn("testEmpNo");
        when(RequestHeaderUtils.getLangId()).thenReturn("zh_CN");
        when(RequestHeaderUtils.getTenantId()).thenReturn("testTenantId");
        
        // Mock ApprovalFlowClient
        Map<String, ApprovalProcessDTO> mockProcessMap = new HashMap<>();
        ApprovalProcessDTO processDTO1 = new ApprovalProcessDTO();
        ApprovalProcessDTO processDTO2 = new ApprovalProcessDTO();
        ApprovalProcessDTO processDTO3 = new ApprovalProcessDTO();
        
        mockProcessMap.put("flow1", processDTO1);
        mockProcessMap.put("flow2", processDTO2);
        mockProcessMap.put("flow3", processDTO3);
        mockProcessMap.put("flow4", null);
        mockProcessMap.put(null, processDTO3);

        when(ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class))).thenReturn(mockProcessMap);
        
        // 执行测试
        result = FlowHelper.batchQueryNodes(businessIds);
        
        // 验证结果
        Assert.assertEquals(3, result.size());
        Assert.assertSame(processDTO1, result.get("1"));
        Assert.assertSame(processDTO2, result.get("2"));
        Assert.assertSame(processDTO3, result.get("3"));
        
        // 验证方法调用
        ArgumentCaptor<BatchProcessparamDTO> paramCaptor = ArgumentCaptor.forClass(BatchProcessparamDTO.class);
        PowerMockito.verifyStatic(ApprovalFlowClient.class);
        ApprovalFlowClient.queryBatchProcess(paramCaptor.capture());
        
        BatchProcessparamDTO capturedParam = paramCaptor.getValue();
        Assert.assertEquals("testEmpNo", capturedParam.getHandler());
        Assert.assertEquals("zh_CN", capturedParam.getLang());
        Assert.assertEquals("testTenantId", capturedParam.getTenantId());
        Assert.assertEquals(Arrays.asList("flow1", "flow2", "flow3"), capturedParam.getInsFlowIdList());
        
        // 3. 测试flowInstDOList为空的场景
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(Collections.emptyList());
        result = FlowHelper.batchQueryNodes(businessIds);
        Assert.assertTrue(result.isEmpty());
        
        // 4. 测试flowIdToProcessMap部分匹配的场景
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(flowInstDTOList);
        mockProcessMap.clear();
        mockProcessMap.put("flow1", processDTO1);
        // 只返回部分数据
        when(ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class))).thenReturn(mockProcessMap);
        
        result = FlowHelper.batchQueryNodes(businessIds);
        Assert.assertEquals(1, result.size());
        Assert.assertSame(processDTO1, result.get("1"));
        Assert.assertNull(result.get("2"));
        Assert.assertNull(result.get("3"));
        
        // 5. 测试大批量数据分批处理的场景
        List<String> largeBatchIds = new ArrayList<>();
        for (int i = 0; i < 120; i++) {
            largeBatchIds.add("id" + i);
        }
        
        // 模拟第一批和第二批的返回
        List<FlowInstDTO> batch1 = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            FlowInstDTO dto = new FlowInstDTO();
            dto.setFlowInstanceId("flow" + i);
            dto.setBusinessId("id" + i);
            batch1.add(dto);
        }
        
        List<FlowInstDTO> batch2 = new ArrayList<>();
        for (int i = 100; i < 120; i++) {
            FlowInstDTO dto = new FlowInstDTO();
            dto.setFlowInstanceId("flow" + i);
            dto.setBusinessId("id" + i);
            batch2.add(dto);
        }
        
        // 设置mock按顺序返回不同的结果
        when(flowInstGateway.getFlowInstByBusinessIds(any()))
            .thenReturn(batch1)
            .thenReturn(batch2);
        
        // 模拟两批的处理结果
        Map<String, ApprovalProcessDTO> batchResult1 = new HashMap<>();
        Map<String, ApprovalProcessDTO> batchResult2 = new HashMap<>();
        
        for (int i = 0; i < 100; i++) {
            ApprovalProcessDTO dto = new ApprovalProcessDTO();
            batchResult1.put("flow" + i, dto);
        }
        
        for (int i = 100; i < 120; i++) {
            ApprovalProcessDTO dto = new ApprovalProcessDTO();
            batchResult2.put("flow" + i, dto);
        }
        
        when(ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class)))
            .thenReturn(batchResult1)
            .thenReturn(batchResult2);
        
        result = FlowHelper.batchQueryNodes(largeBatchIds);
        
        // 验证结果包含所有120个条目
        Assert.assertEquals(100, result.size());
        
        // 6. 测试 flowIdToProcessMap 为 null 的情况 (实际使用空Map代替)
        when(ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class))).thenReturn(new HashMap<>());
        result = FlowHelper.batchQueryNodes(businessIds);
        Assert.assertTrue(result.isEmpty());
        
        // 7. 测试流程实例ID重复的情况
        List<FlowInstDTO> duplicateFlowIdList = new ArrayList<>();
        FlowInstDTO duplicateDto1 = new FlowInstDTO();
        duplicateDto1.setFlowInstanceId("duplicateFlow");
        duplicateDto1.setBusinessId("biz1");
        
        duplicateFlowIdList.add(duplicateDto1);
        
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(duplicateFlowIdList);
        Map<String, ApprovalProcessDTO> duplicateProcessMap = new HashMap<>();
        ApprovalProcessDTO duplicateProcessDTO = new ApprovalProcessDTO();
        duplicateProcessMap.put("duplicateFlow", duplicateProcessDTO);
        when(ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class))).thenReturn(duplicateProcessMap);
        
        result = FlowHelper.batchQueryNodes(Arrays.asList("biz1"));
        Assert.assertEquals(1, result.size());
        Assert.assertSame(duplicateProcessDTO, result.get("biz1"));
        
        // 9. 测试业务ID重复但对应不同流程实例ID的情况
        List<FlowInstDTO> duplicateBizIdList = new ArrayList<>();
        FlowInstDTO bizDto1 = new FlowInstDTO();
        bizDto1.setFlowInstanceId("flow1");
        bizDto1.setBusinessId("duplicateBiz");
        
        FlowInstDTO bizDto2 = new FlowInstDTO();
        bizDto2.setFlowInstanceId("flow2");
        bizDto2.setBusinessId("duplicateBiz"); // 相同的业务ID
        
        duplicateBizIdList.add(bizDto1);
        duplicateBizIdList.add(bizDto2);
        
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(duplicateBizIdList);
        Map<String, ApprovalProcessDTO> bizProcessMap = new HashMap<>();
        ApprovalProcessDTO bizProcessDTO1 = new ApprovalProcessDTO();
        ApprovalProcessDTO bizProcessDTO2 = new ApprovalProcessDTO();
        bizProcessMap.put("flow1", bizProcessDTO1);
        bizProcessMap.put("flow2", bizProcessDTO2);
        when(ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class))).thenReturn(bizProcessMap);
        
        result = FlowHelper.batchQueryNodes(Arrays.asList("duplicateBiz"));
        Assert.assertEquals(1, result.size());
        // 验证使用了最后一个流程实例的处理信息
        Assert.assertSame(bizProcessDTO2, result.get("duplicateBiz"));
        
        // 10. 测试流程实例为null的情况
        List<FlowInstDTO> nullFlowIdList = new ArrayList<>();
        FlowInstDTO nullFlowDto = new FlowInstDTO();
        nullFlowDto.setFlowInstanceId(null);
        nullFlowDto.setBusinessId("nullFlowBiz");
        nullFlowIdList.add(nullFlowDto);
        
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(nullFlowIdList);
        result = FlowHelper.batchQueryNodes(Arrays.asList("nullFlowBiz"));
        Assert.assertEquals(0, result.size());
    }

    /**
     * 测试 batchQueryNodes 方法中 flowInstDOList 为空的分支
     */
    @Test
    public void testBatchQueryNodesWithEmptyFlowInstDOList() {
        // 模拟 ContextHelper 方法
        when(ContextHelper.getUacAppId()).thenReturn("testAppId");
        when(ContextHelper.getAccessSecret()).thenReturn("testSecret");
        when(ContextHelper.getEmpNo()).thenReturn("testEmpNo");
        when(ContextHelper.getTenantId()).thenReturn("testTenantId");

        // 模拟 RequestContextHolder
        mockStatic(RequestContextHolder.class);
        when(RequestContextHolder.getAppId()).thenReturn("testAppId");

        // 模拟 SpringContextUtil
        mockStatic(SpringContextUtil.class);
        BeanFactory beanFactory = PowerMockito.mock(BeanFactory.class);
        when(SpringContextUtil.getBeanFactory()).thenReturn(beanFactory);

        // 模拟 IParameterRepository
        IParameterRepository parameterRepository = PowerMockito.mock(IParameterRepository.class);
        UacParamDTO uacParamDTO = new UacParamDTO();
        uacParamDTO.setAppId("testAppId");
        uacParamDTO.setAccessSecret("testSecret");
        when(parameterRepository.getUacParams(anyString(), anyString())).thenReturn(uacParamDTO);
        when(beanFactory.getBean(IParameterRepository.class)).thenReturn(parameterRepository);

        // 模拟 FlowInstGatewayImpl
        when(SpringContextUtil.getBean(FlowInstGatewayImpl.class)).thenReturn(flowInstGateway);

        // 测试 flowInstDOList 为空的情况
        List<String> businessIds = Arrays.asList("1", "2", "3");

        // 第一次返回空列表，第二次返回非空列表
        when(flowInstGateway.getFlowInstByBusinessIds(any()))
            .thenReturn(Collections.emptyList())
            .thenReturn(Arrays.asList(new FlowInstDTO()));

        // 执行测试
        Map<String, ApprovalProcessDTO> result = FlowHelper.batchQueryNodes(businessIds);

        // 验证结果为空
        Assert.assertTrue(result.isEmpty());

        // 验证方法调用
        PowerMockito.verifyStatic(ApprovalFlowClient.class, times(0));
        ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class));
    }

    /**
     * 测试 batchQueryNodes 方法中 businessId 或 processDto 为 null 的分支
     */
    @Test
    public void testBatchQueryNodesWithNullBusinessIdOrProcessDto() {
        // 模拟 ContextHelper 方法
        when(ContextHelper.getUacAppId()).thenReturn("testAppId");
        when(ContextHelper.getAccessSecret()).thenReturn("testSecret");
        when(ContextHelper.getEmpNo()).thenReturn("testEmpNo");
        when(ContextHelper.getTenantId()).thenReturn("testTenantId");

        // 模拟 RequestContextHolder
        mockStatic(RequestContextHolder.class);
        when(RequestContextHolder.getAppId()).thenReturn("testAppId");

        // 模拟 SpringContextUtil
        mockStatic(SpringContextUtil.class);
        BeanFactory beanFactory = PowerMockito.mock(BeanFactory.class);
        when(SpringContextUtil.getBeanFactory()).thenReturn(beanFactory);

        // 模拟 IParameterRepository
        IParameterRepository parameterRepository = PowerMockito.mock(IParameterRepository.class);
        UacParamDTO uacParamDTO = new UacParamDTO();
        uacParamDTO.setAppId("testAppId");
        uacParamDTO.setAccessSecret("testSecret");
        when(parameterRepository.getUacParams(anyString(), anyString())).thenReturn(uacParamDTO);
        when(beanFactory.getBean(IParameterRepository.class)).thenReturn(parameterRepository);

        // 模拟 FlowInstGatewayImpl
        when(SpringContextUtil.getBean(FlowInstGatewayImpl.class)).thenReturn(flowInstGateway);

        // 模拟 RequestHeaderUtils
        mockStatic(RequestHeaderUtils.class);
        when(RequestHeaderUtils.getEmpNo()).thenReturn("testEmpNo");
        when(RequestHeaderUtils.getLangId()).thenReturn("zh_CN");
        when(RequestHeaderUtils.getTenantId()).thenReturn("testTenantId");

        // 测试 processDto 为 null 的情况
        List<String> businessIds = Arrays.asList("1", "2", "3");

        // 创建测试数据
        List<FlowInstDTO> flowInstDTOList = new ArrayList<>();

        FlowInstDTO flowInstDTO1 = new FlowInstDTO();
        flowInstDTO1.setFlowInstanceId("flow1");
        flowInstDTO1.setBusinessId("1"); // 使用非null的businessId

        FlowInstDTO flowInstDTO2 = new FlowInstDTO();
        flowInstDTO2.setFlowInstanceId("flow2");
        flowInstDTO2.setBusinessId("2");

        flowInstDTOList.add(flowInstDTO1);
        flowInstDTOList.add(flowInstDTO2);

        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(flowInstDTOList);

        // 模拟 processDto 为 null 的情况
        Map<String, ApprovalProcessDTO> mockProcessMap = new HashMap<>();
        mockProcessMap.put("flow1", null); // processDto 为 null
        ApprovalProcessDTO processDTO2 = new ApprovalProcessDTO();
        mockProcessMap.put("flow2", processDTO2);

        when(ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class))).thenReturn(mockProcessMap);

        // 执行测试
        Map<String, ApprovalProcessDTO> result = FlowHelper.batchQueryNodes(businessIds);

        // 验证结果
        Assert.assertEquals(1, result.size());
        Assert.assertNull(result.get("1"));
        Assert.assertSame(processDTO2, result.get("2"));

        // 验证方法调用
        PowerMockito.verifyStatic(ApprovalFlowClient.class, times(1));
        ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class));
    }

    /**
     * 测试 batchQueryActiveNodes 方法 - 空输入
     */
    @Test
    public void testBatchQueryActiveNodesWithEmptyInput() {
        Assert.assertNotNull(FlowHelper.batchQueryActiveNodes(Collections.emptyList()));
    }

    /**
     * 测试 batchQueryActiveNodes 方法 - 分批处理
     */
    @Test
    public void testBatchQueryActiveNodesWithPartition() {
        // 模拟依赖
        mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBean(FlowInstGatewayImpl.class)).thenReturn(flowInstGateway);

        // 创建测试数据
        List<String> businessIds = Arrays.asList("1", "2", "3");

        // 模拟返回空列表，触发continue分支
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(Collections.emptyList());

        assertThrows(Exception.class, () -> FlowHelper.batchQueryActiveNodes(businessIds));
    }

    /**
     * 测试 batchQueryNodes 方法中的 continue 分支
     */
    @Test
    public void testBatchQueryNodesWithContinueBranch() {
        // 模拟 ContextHelper 方法
        when(ContextHelper.getUacAppId()).thenReturn("testAppId");
        when(ContextHelper.getAccessSecret()).thenReturn("testSecret");
        when(ContextHelper.getEmpNo()).thenReturn("testEmpNo");
        when(ContextHelper.getTenantId()).thenReturn("testTenantId");

        // 模拟 RequestContextHolder
        mockStatic(RequestContextHolder.class);
        when(RequestContextHolder.getAppId()).thenReturn("testAppId");

        // 模拟 SpringContextUtil
        mockStatic(SpringContextUtil.class);
        BeanFactory beanFactory = PowerMockito.mock(BeanFactory.class);
        when(SpringContextUtil.getBeanFactory()).thenReturn(beanFactory);

        // 模拟 IParameterRepository
        IParameterRepository parameterRepository = PowerMockito.mock(IParameterRepository.class);
        UacParamDTO uacParamDTO = new UacParamDTO();
        uacParamDTO.setAppId("testAppId");
        uacParamDTO.setAccessSecret("testSecret");
        when(parameterRepository.getUacParams(anyString(), anyString())).thenReturn(uacParamDTO);
        when(beanFactory.getBean(IParameterRepository.class)).thenReturn(parameterRepository);

        // 模拟 FlowInstGatewayImpl
        when(SpringContextUtil.getBean(FlowInstGatewayImpl.class)).thenReturn(flowInstGateway);

        // 创建大量业务ID，超过单次查询上限
        List<String> businessIds = new ArrayList<>();
        for (int i = 0; i < 120; i++) {
            businessIds.add("biz" + i);
        }

        // 模拟第一批返回空列表，第二批返回非空列表
        List<FlowInstDTO> batch2 = new ArrayList<>();
        FlowInstDTO dto = new FlowInstDTO();
        dto.setFlowInstanceId("flow100");
        dto.setBusinessId("biz100");
        batch2.add(dto);

        when(flowInstGateway.getFlowInstByBusinessIds(any()))
            .thenReturn(Collections.emptyList())
            .thenReturn(batch2);

        // 模拟 ApprovalFlowClient.queryBatchProcess 的返回
        Map<String, ApprovalProcessDTO> processMap = new HashMap<>();
        ApprovalProcessDTO processDTO = new ApprovalProcessDTO();
        processMap.put("flow100", processDTO);
        when(ApprovalFlowClient.queryBatchProcess(any(BatchProcessparamDTO.class))).thenReturn(processMap);

        assertNotNull(FlowHelper.batchQueryNodes(businessIds));
    }

    @Test
    public void testQueryActiveNodes() {
        // 模拟依赖
        mockStatic(SpringContextUtil.class);
        mockStatic(ApprovalFlowClient.class);
        mockStatic(ContextHelper.class);

        // 模拟 ContextHelper 方法
        when(ContextHelper.getUacAppId()).thenReturn("testAppId");
        when(ContextHelper.getAccessSecret()).thenReturn("testSecret");
        when(ContextHelper.getEmpNo()).thenReturn("testEmpNo");
        when(ContextHelper.getTenantId()).thenReturn("testTenantId");

        // 模拟 SpringContextUtil
        BeanFactory beanFactory = PowerMockito.mock(BeanFactory.class);
        when(SpringContextUtil.getBeanFactory()).thenReturn(beanFactory);

        // 模拟 FlowInstGatewayImpl
        FlowInstGatewayImpl flowInstGateway = PowerMockito.mock(FlowInstGatewayImpl.class);
        when(SpringContextUtil.getBean(FlowInstGatewayImpl.class)).thenReturn(flowInstGateway);

        // 1. 测试空输入场景
        List<NodeApprover> result = FlowHelper.queryActiveNodes(null);
        assertTrue(result.isEmpty());

        result = FlowHelper.queryActiveNodes(Collections.emptyList());
        assertTrue(result.isEmpty());

        // 2. 测试 flowInstDOList 为空的场景
        List<String> businessIds = Arrays.asList("1", "2", "3");
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(Collections.emptyList());

        assertThrows(Exception.class, () -> FlowHelper.queryActiveNodes(businessIds));

        // 3. 测试正常场景 - 有流程实例但没有活动节点
        List<FlowInstDTO> flowInstDTOList = new ArrayList<>();
        FlowInstDTO flowInstDTO = new FlowInstDTO();
        flowInstDTO.setFlowInstanceId("flow1");
        flowInstDTOList.add(flowInstDTO);

        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(flowInstDTOList);

        // 模拟 ApprovalFlowClient.queryActiveNodeApprover 返回空活动节点
        List<NodeApproverDTO> emptyNodeApprovers = new ArrayList<>();
        when(ApprovalFlowClient.queryActiveNodeApprover(any(InsFlowParam.class))).thenReturn(emptyNodeApprovers);

        assertThrows(Exception.class, () -> FlowHelper.queryActiveNodes(businessIds));

        // 4. 测试正常场景 - 有流程实例和活动节点，但节点没有审批人
        NodeApproverDTO nodeApproverDTO = new NodeApproverDTO();
        List<ActiveNodeInfo> activeNodeInfos = new ArrayList<>();
        ActiveNodeInfo activeNodeInfo = new ActiveNodeInfo();
        activeNodeInfo.setExtendedCode("TEST_NODE");
        activeNodeInfo.setNodeApprovers(Collections.emptyList());
        activeNodeInfos.add(activeNodeInfo);
        nodeApproverDTO.setActiveNodeInfos(activeNodeInfos);

        List<NodeApproverDTO> nodeApproverDTOs = new ArrayList<>();
        nodeApproverDTOs.add(nodeApproverDTO);

        when(ApprovalFlowClient.queryActiveNodeApprover(any(InsFlowParam.class))).thenReturn(nodeApproverDTOs);

        assertThrows(Exception.class, () -> FlowHelper.queryActiveNodes(businessIds));

        // 5. 测试正常场景 - 有流程实例和活动节点，有审批人，但extendedCode为空
        nodeApproverDTO = new NodeApproverDTO();
        activeNodeInfos = new ArrayList<>();
        activeNodeInfo = new ActiveNodeInfo();
        activeNodeInfo.setExtendedCode(null);

        List<NodeApprover> nodeApprovers = new ArrayList<>();
        NodeApprover nodeApprover = new NodeApprover();
        nodeApprover.setApprover("testApprover");
        nodeApprovers.add(nodeApprover);

        activeNodeInfo.setNodeApprovers(nodeApprovers);
        activeNodeInfos.add(activeNodeInfo);
        nodeApproverDTO.setActiveNodeInfos(activeNodeInfos);

        nodeApproverDTOs = new ArrayList<>();
        nodeApproverDTOs.add(nodeApproverDTO);

        when(ApprovalFlowClient.queryActiveNodeApprover(any(InsFlowParam.class))).thenReturn(nodeApproverDTOs);

        assertThrows(Exception.class, () -> FlowHelper.queryActiveNodes(businessIds));

        // 6. 测试正常场景 - 有流程实例和活动节点，有审批人，extendedCode不在ApproveNodeEnum中
        nodeApproverDTO = new NodeApproverDTO();
        activeNodeInfos = new ArrayList<>();
        activeNodeInfo = new ActiveNodeInfo();
        activeNodeInfo.setExtendedCode("INVALID_NODE");
        activeNodeInfo.setNodeApprovers(nodeApprovers);
        activeNodeInfos.add(activeNodeInfo);
        nodeApproverDTO.setActiveNodeInfos(activeNodeInfos);

        nodeApproverDTOs = new ArrayList<>();
        nodeApproverDTOs.add(nodeApproverDTO);

        // 模拟 ApproveNodeEnum.getApproveNodeEnum() 返回不包含 "INVALID_NODE" 的列表
        PowerMockito.mockStatic(ApproveNodeEnum.class);
        when(ApproveNodeEnum.getApproveNodeEnum()).thenReturn(Arrays.asList("VALID_NODE"));

        when(ApprovalFlowClient.queryActiveNodeApprover(any(InsFlowParam.class))).thenReturn(nodeApproverDTOs);

        assertThrows(Exception.class, () -> FlowHelper.queryActiveNodes(businessIds));
    }

    @Test
    public void queryActiveNodes() {
        FlowInstDTO flowInstDTO1 = new FlowInstDTO();
        flowInstDTO1.setFlowInstanceId("flow1");
        flowInstDTO1.setBusinessId("1");
        BeanFactory beanFactory = PowerMockito.mock(BeanFactory.class);
        when(SpringContextUtil.getBeanFactory()).thenReturn(beanFactory);
        when(beanFactory.getBean(eq(FlowInstGatewayImpl.class))).thenReturn(flowInstGateway);
        when(flowInstGateway.getFlowInstByBusinessIds(any())).thenReturn(Arrays.asList(flowInstDTO1));
        when(beanFactory.getBean(eq(IParameterRepository.class))).thenReturn(parameterRepository);
        FlowHelper.batchQueryActiveNodes(Arrays.asList("1"));
        List<NodeApprover> nodeApprovers = FlowHelper.queryActiveNodes(Arrays.asList("1"));
        Assert.assertNotNull(nodeApprovers);
    }

    @Test
    public void getApprovedRecords2() {
        List<ApproveRecord> approveRecordList = new ArrayList<>();
        ApproveRecord approveRecord = new ApproveRecord();
        approveRecord.setExtendedCode("1");
        approveRecord.setTaskStatus("ACTIVE");
        approveRecordList.add(approveRecord);
        //2.，AssignmentTypeEnum为非网络变更单任务
        List<ApproveRecord> approvedRecords = FlowHelper.getApprovedRecords(approveRecordList, AssignmentTypeEnum.TECHNOLOGY_MANAGEMENT);
        Assert.assertTrue(!approvedRecords.isEmpty());


        //3.AssignmentTypeEnum为网络变更单任务
        approvedRecords = FlowHelper.getApprovedRecords(approveRecordList, AssignmentTypeEnum.NETWORK_CHANGE);
        Assert.assertTrue(!approvedRecords.isEmpty());
    }


    @Test
    public void findApproveRecordByTaskId() {
        List<ApproveRecord> approvedRecords = new ArrayList<>();
        String businessId = "1";
        approvedRecords.add(new ApproveRecord() {{
            setTaskId("1");
        }});
        // 1.taskId不为空，但是匹配不到的场景
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(approvedRecords);
        ApproveRecord approveRecord = FlowHelper.findApproveRecordByTaskId(businessId, "2");
        Assert.assertNull(approveRecord);

        // 2.正常匹配场景
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(approvedRecords);
        approveRecord = FlowHelper.findApproveRecordByTaskId(businessId, "1");
        Assert.assertNotNull(approveRecord);
    }

    @Test
    public void getActiveNodeInfo() {
        FlowInfo flowInfo = new FlowInfo();
        flowInfo.setFlowCode("CHANGE_ORDER_COMP_FLOW");
        List<FlowInfo> flowInfos = Lists.newArrayList(flowInfo);
        when(FlowServiceHelper.queryFlowInfoByBizId(any()))
                .thenReturn(flowInfos);
        flowHelper.getActiveNodeInfo("1");

        List<NodeApproverDTO> emptyNodeApprovers = new ArrayList<>();
        when(ApprovalFlowClient.queryActiveNodeApprover(any(InsFlowParam.class)))
                .thenReturn(emptyNodeApprovers);

        NodeApproverDTO nodeApproverDTO = new NodeApproverDTO();
        List<ActiveNodeInfo> activeNodeInfos = new ArrayList<>();
        ActiveNodeInfo activeNodeInfo = new ActiveNodeInfo();
        activeNodeInfo.setExtendedCode("TEST_NODE");
        activeNodeInfo.setNodeApprovers(Collections.emptyList());
        activeNodeInfos.add(activeNodeInfo);
        nodeApproverDTO.setActiveNodeInfos(activeNodeInfos);
        emptyNodeApprovers.add(nodeApproverDTO);
        when(ApprovalFlowClient.queryActiveNodeApprover(any(InsFlowParam.class)))
                .thenReturn(emptyNodeApprovers);
        ActiveApproverNode ac = flowHelper.getActiveNodeInfo("1");
        Assert.assertNotNull(ac);

        when(ApprovalFlowClient.queryActiveNodeApprover(any(InsFlowParam.class)))
                .thenThrow(new NullPointerException());
        flowHelper.getActiveNodeInfo("1");
    }

    @Test
    public void isApproved() {
        String businessId = "1";
        // 1.审批记录集为空场景
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(null);
        boolean approvedFlag = FlowHelper.isApproved(businessId);
        Assert.assertFalse(approvedFlag);

        // 2.审批记录集被审批过
        List<ApproveRecord> approvedRecords = new ArrayList<>();
        ApproveRecord approveRecord = new ApproveRecord();
        approveRecord.setApprovalDate(new Date());
        approvedRecords.add(approveRecord);
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(approvedRecords);

        approvedFlag = FlowHelper.isApproved(businessId);
        Assert.assertTrue(approvedFlag);

        // 3.审批记录未被审批过
        approveRecord.setApprovalDate(null);
        when(FlowServiceHelper.getFlowApproveRecords(any())).thenReturn(approvedRecords);

        approvedFlag = FlowHelper.isApproved(businessId);
        Assert.assertFalse(approvedFlag);
    }

    @Test
    public void testHasExtendedCodeApprovalToday_NoRecords() {
        when(ContextHelper.getTenantId()).thenReturn("tenant1");
        when(ContextHelper.getAppId()).thenReturn("app1");
        ApproveRecordsDTO dto = new ApproveRecordsDTO();
        dto.setTenantId("tenant1");
        dto.setAppId("app1");
        dto.setBizInstanceId("biz1");
        List<ApproveRecord> emptyList = Collections.emptyList();
        PowerMockito.when(FlowServiceHelper.getFlowApproveRecords(dto)).thenReturn(emptyList);

        boolean result = FlowHelper.hasExtendedCodeApprovalToday("biz1", "code1");
        Assert.assertFalse(result);
    }

    @Test
    public void testHasExtendedCodeApprovalToday_MatchFound() {
        when(ContextHelper.getTenantId()).thenReturn("tenant1");
        when(ContextHelper.getAppId()).thenReturn("app1");
        ApproveRecordsDTO dto = new ApproveRecordsDTO();
        dto.setTenantId("tenant1");
        dto.setAppId("app1");
        dto.setBizInstanceId("biz1");

        Date approvalDate = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());

        List<ApproveRecord> records = Lists.newArrayList(new ApproveRecord() {{
            setApprovalDate(approvalDate);
            setExtendedCode("code1");
        }});
        PowerMockito.when(FlowServiceHelper.getFlowApproveRecords(dto)).thenReturn(records);

        boolean result = FlowHelper.hasExtendedCodeApprovalToday("biz1", "code1");
        assertTrue(result);
    }

    @Test
    public void testHasExtendedCodeApprovalToday_CodeNotMatch() {
        when(ContextHelper.getTenantId()).thenReturn("tenant1");
        when(ContextHelper.getAppId()).thenReturn("app1");
        ApproveRecordsDTO dto = new ApproveRecordsDTO();
        dto.setTenantId("tenant1");
        dto.setAppId("app1");
        dto.setBizInstanceId("biz1");

        Date approvalDate = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
        List<ApproveRecord> records = Lists.newArrayList(new ApproveRecord() {{
            setApprovalDate(approvalDate);
            setExtendedCode("code2");
        }});

        PowerMockito.when(FlowServiceHelper.getFlowApproveRecords(dto)).thenReturn(records);

        boolean result = FlowHelper.hasExtendedCodeApprovalToday("biz1", "code1");
        assertFalse(result);
    }

    @Test
    public void testHasExtendedCodeApprovalToday_DateNotSameDay() {
        when(ContextHelper.getTenantId()).thenReturn("tenant1");
        when(ContextHelper.getAppId()).thenReturn("app1");
        ApproveRecordsDTO dto = new ApproveRecordsDTO();
        dto.setTenantId("tenant1");
        dto.setAppId("app1");
        dto.setBizInstanceId("biz1");

        LocalDate yesterday = LocalDate.now().minusDays(1);
        Date approvalDate = Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant());
        List<ApproveRecord> records = Lists.newArrayList(new ApproveRecord() {{
            setApprovalDate(approvalDate);
            setExtendedCode("code1");
        }});

        PowerMockito.when(FlowServiceHelper.getFlowApproveRecords(dto)).thenReturn(records);

        boolean result = FlowHelper.hasExtendedCodeApprovalToday("biz1", "code1");
        assertFalse(result);
    }

    @Test
    public void testHasExtendedCodeApprovalToday_NullApprovalDate() {
        when(ContextHelper.getTenantId()).thenReturn("tenant1");
        when(ContextHelper.getAppId()).thenReturn("app1");
        ApproveRecordsDTO dto = new ApproveRecordsDTO();
        dto.setTenantId("tenant1");
        dto.setAppId("app1");
        dto.setBizInstanceId("biz1");

        List<ApproveRecord> records = Lists.newArrayList(new ApproveRecord() {{
            setApprovalDate(null);
            setExtendedCode("code1");
        }});
        PowerMockito.when(FlowServiceHelper.getFlowApproveRecords(dto)).thenReturn(records);

        boolean result = FlowHelper.hasExtendedCodeApprovalToday("biz1", "code1");
        assertFalse(result);
    }

    @Test
    public void reassignSystemNodeTest() {
        // 1.无审批任务
        PowerMockito.when(FlowServiceHelper.queryApproveTaskByBusinessId(any()))
                .thenReturn(Lists.newArrayList());
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FlowHelper.reassignSystemNode("bizId", "taskReceiver"));

        ApproveTask approveTask = new ApproveTask();
        approveTask.setApprover("anotherUser");
        approveTask.setTaskId("taskId");
        PowerMockito.when(FlowServiceHelper.queryApproveTaskByBusinessId(any()))
                .thenReturn(Lists.newArrayList(approveTask));
        Assert.assertThrows(
                LcapBusiException.class,
                () -> FlowHelper.reassignSystemNode("bizId", "taskReceiver", "oldApprover"));

        // 2.被转交人有审批任务
        approveTask.setApprover("oldApprover");

        ApproveTask existApproveTask = new ApproveTask();
        existApproveTask.setApprover("taskReceiver");
        existApproveTask.setTaskId("taskId");

        PowerMockito.when(FlowServiceHelper.deleteTask(any())).thenReturn(true);
        PowerMockito.when(FlowServiceHelper.queryApproveTaskByBusinessId(any()))
                .thenReturn(Lists.newArrayList(approveTask, existApproveTask));
        FlowHelper.reassignSystemNode("bizId", "taskReceiver", "oldApprover");

        // 3.被转交人无审批任务
        PowerMockito.when(FlowServiceHelper.reassign(any())).thenReturn("");
        PowerMockito.when(FlowServiceHelper.queryApproveTaskByBusinessId(any()))
                .thenReturn(Lists.newArrayList(approveTask));
        FlowHelper.reassignSystemNode("bizId", "taskReceiver");
    }

    @Test
    public void changeFlowParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("str", "str");
        params.put("1", 1);
        params.put("1.1", 1.1);
        params.put("object", new ApproveTask());
        FlowHelper.changeFlowParams("1", params, ApproveFlowCodeEnum.FAULT_MANAGE_FLOW);
        Assert.assertNotNull(params);
    }

    @Test
    public void rollbackToNodeTest() {
        String businessId = "id1";
        FlowHelper.rollbackToNode(businessId, "1", "1","1");
        Assert.assertNotNull(businessId);
    }
}