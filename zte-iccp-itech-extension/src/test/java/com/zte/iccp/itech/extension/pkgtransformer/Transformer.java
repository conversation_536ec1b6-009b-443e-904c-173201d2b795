package com.zte.iccp.itech.extension.pkgtransformer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.zte.itp.msa.util.encrypt.EncryptorUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.zte.iccp.itech.extension.pkgtransformer.Configurations.*;

/**
 * 将测试环境导出的应用包修改为可导入生产的版本
 * <AUTHOR> 10284287
 * @since 2024/07/13
 */
@Slf4j
public class Transformer {

    private static final String RND_GUID = "852bfcc46d1911f0b836fa163e7cc9b3";

    private static String fileIn;

    private static String resultFileName;

    private static Map<String, Map<String, String>> replacementMap;

    private static final StringBuilder WARN = new StringBuilder("\n");

    public static void main(String[] args) {
        encrypt();

        read();
        if (replacementMap.containsKey(RELEASE_PKG_PREFIX)) {
            resultFileName = replacementMap.get(RELEASE_PKG_PREFIX) + resultFileName;
        }
        PackageHelper.transform(fileIn, replaceFileName(fileIn, resultFileName), Transformer::handle);
        log.warn(WARN.toString());
    }

    @SneakyThrows
    private static void read() {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(System.in))) {
            System.out.println("原始应用包全路径: ");
            while (StringUtils.isBlank(fileIn) || !new File(fileIn).exists()) {
                fileIn = reader.readLine();
            }

            String defaultResultName = fileIn.replaceAll("^.+(V.+\\.zip)$", "$1");

            System.out.printf("输出文件名(默认【%s】):%n", defaultResultName);
            resultFileName = reader.readLine();
            if (StringUtils.isBlank(resultFileName)) {
                resultFileName = defaultResultName;
            }

            while (replacementMap == null) {
                System.out.println("模式(PROD|UAT):");

                String map = reader.readLine();
                switch (map) {
                    case "PROD":
                        replacementMap = TO_PROD;
                        break;
                    case "UAT":
                        replacementMap = TEST_2_UAT;
                        break;
                    default:
                        System.out.println("模式代码错误");
                }
            }

            System.out.println("%n%n%n开始转换应用包...");
        }
    }

    private static void encrypt(String... plaintexts) {
        if (plaintexts.length == 0) {
            return;
        }

        StringBuilder s = new StringBuilder();
        for (String plaintext : plaintexts) {
            String encrypted = EncryptorUtils.encryptPwd(plaintext, RND_GUID);
            s.append(plaintext)
                    .append(" -> ENC(")
                    .append(encrypted)
                    .append(")\n");
        }
        log.info(s.toString());

        throw new RuntimeException();
    }

    private static String decrypt(String origin) {
        String unboxed = origin.replaceAll("^ENC\\(([^)]+)\\)$", "$1");
        if (unboxed.equals(origin)) {
            return origin;
        }

        return EncryptorUtils.decryptByKey(unboxed, RND_GUID);
    }

    /* Started by AICoder, pid:r6a63ib3a0ca46c141730813b05c1d0a14386932 */
    private static String replaceFileName(String fullPath, String newFileName) {
        int lastIndex = fullPath.lastIndexOf(File.separator);
        if (lastIndex == -1) {
            return newFileName;
        } else {
            return fullPath.substring(0, lastIndex + 1) + newFileName;
        }
    }
    /* Ended by AICoder, pid:r6a63ib3a0ca46c141730813b05c1d0a14386932 */

    private static void handle(EntryInfo entryInfo) {
        if (entryInfo.getEntryName().endsWith(".json")) {
            handleJson(entryInfo);
        }
    }

    private static void handleJson(EntryInfo entryInfo) {
        String json = new String(entryInfo.getData(), StandardCharsets.UTF_8);
        String fileName = getFileName(entryInfo.getEntryName());

//        json = turnOffMoa(json);

        if (replacementMap.containsKey(fileName)) {
            for (Map.Entry<String, String> mapping : replacementMap.get(fileName).entrySet()) {
                json = json.replace(mapping.getKey(), decrypt(mapping.getValue()));
            }
        }
        for (Map.Entry<String, String> mapping : replacementMap.get("*").entrySet()) {
            json = json.replace(mapping.getKey(), decrypt(mapping.getValue()));
        }

        checkResult(fileName, json);

        entryInfo.setData(json.getBytes(StandardCharsets.UTF_8));
    }

    private static void checkResult(String fileName, String json) {
        Pattern p = Pattern.compile(
                "(test|uat).*\\.zte\\.com\\.cn" +
                        "|(?<!\"parameter_default_value\":\"http://)(?<!\"parameter_default_value\":\"https://)(?<!\\d)(\\d+\\.){3}\\d+");
        Matcher m = p.matcher(json);
        int l = 50;

        while (m.find()) {
            int i = json.indexOf(m.group());
            String s = json.substring(Math.max(i - l, 0), Math.min(i + m.group().length() + l, json.length()));
            WARN.append(String.format("%s: %s\n", fileName, s));
        }
    }

    private static String turnOffMoa(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            if (jsonObject.containsKey("defFlowInfoDTO")) {
                JSONArray jsonArray = jsonObject
                        .getJSONObject("defFlowInfoDTO")
                        .getJSONObject("definition")
                        .getJSONArray("children");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject c = jsonArray.getJSONObject(i);
                    JSONArray pendingNoticeList = c.getJSONObject("props")
                            .getJSONArray("pendingNoticeList");
                    if (pendingNoticeList != null && !pendingNoticeList.isEmpty()) {
                        for (int j = pendingNoticeList.size() - 1; j >= 0; j--) {
                            JSONObject pendingNotice = pendingNoticeList.getJSONObject(j);
                            if ("MOA".equals(pendingNotice.getString("templateType"))) {
                                pendingNoticeList.remove(j);
                            }
                        }
                    }
                }

                json = jsonObject.toJSONString(
                                JSONWriter.Feature.PrettyFormat,
                                JSONWriter.Feature.WriteNulls)
                        .replaceAll("\\[[\r\n\\s]+]", "[]")
                        .replaceAll("\\{[\r\n\\s]+}", "{}");
            }
        } catch (Exception e) {
            return json;
        }

        return json;
    }

    private static String getFileName(String entryName) {
        String[] parts = entryName.contains(File.separator)
                ? entryName.split(Pattern.quote(File.separator))
                : entryName.split("/");
        return parts[parts.length -1];
    }
}
