package com.zte.iccp.itech.extension.ability.common.export;

import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.ContextHelper;
import com.zte.iccp.itech.extension.common.utils.MsgUtils;
import com.zte.iccp.itech.extension.domain.enums.TemplateIdEnum;
import com.zte.iccp.itech.extension.domain.enums.export.ExportFieldEnum;
import com.zte.iccp.itech.extension.domain.model.export.TwoLevelHeaderSheet;
import com.zte.iccp.itech.extension.spi.client.EmailClient;
import com.zte.paas.lcap.helper.util.FileStorageUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MsgUtils.class, FileStorageUtils.class, ConfigHelper.class, ContextHelper.class, EmailClient.class, StringUtils.class})
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
public class TwoLevelExportAbilityTest {

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(MsgUtils.class);
        PowerMockito.mockStatic(ConfigHelper.class);
        PowerMockito.mockStatic(StringUtils.class);

        // Mock message translations
        when(MsgUtils.getMessage(anyString())).thenReturn("Translated Message");
    }

    @Test
    public void testWriteWithNullParameters() {
        TwoLevelExportAbility.write(null, new XSSFWorkbook());
        final TwoLevelHeaderSheet<Object> sheet = new TwoLevelHeaderSheet<>();
        TwoLevelExportAbility.write(sheet, null);
        assertNotNull(sheet);
    }

    @Test
    public void testCreateTwoLevelSheet() {
        // 使用空列表而不是mock对象，避免加载大枚举
        List<ExportFieldEnum> fields = new ArrayList<>();

        // Execute
        TwoLevelHeaderSheet<?> result = TwoLevelExportAbility.createTwoLevelSheet(fields, "sheet.name");

        // Verify - 避免依赖大枚举的Mock结果
        assertNotNull(result);
        assertEquals("Translated Message", result.getSheetName());
        assertNotNull(result.getExportFields());
        assertNotNull(result.getHeaderInfos());
    }

    @Test
    public void testWriteWithValidData() {
        // Prepare test data - 使用简化的测试数据避免使用大枚举
        Workbook workbook = new XSSFWorkbook();
        TwoLevelHeaderSheet<TestData> sheet = createEmptyTestSheet();

        // Add test elements
        List<TestData> elements = new ArrayList<>();
        elements.add(new TestData("Test Name", "Test Value"));
        elements.add(new TestData(null, "Test Value 2")); // Test null value handling
        sheet.setElements(elements);

        // Execute
        TwoLevelExportAbility.write(sheet, workbook);

        // Verify
        Sheet resultSheet = workbook.getSheet("Translated Message");
        assertNotNull(resultSheet);
        // 由于使用空字段列表，只有表头行
        assertTrue(resultSheet.getPhysicalNumberOfRows() >= 2);
    }

    @Test
    public void testWriteWithHyperlinks() {
        // Prepare test data
        Workbook workbook = new XSSFWorkbook();
        TwoLevelHeaderSheet<TestData> sheet = createEmptyTestSheet();

        // Add test elements
        List<TestData> elements = new ArrayList<>();
        elements.add(new TestData("Test Name", "Test Value"));
        sheet.setElements(elements);

        // Add hyperlinks
        Map<Integer, List<String>> hyperlinks = new HashMap<>();
        List<String> links = new ArrayList<>();
        links.add("https://example.com");
        hyperlinks.put(0, links);
        sheet.setHyperlinks(hyperlinks);

        // Execute
        TwoLevelExportAbility.write(sheet, workbook);

        // Verify
        Sheet resultSheet = workbook.getSheet("Translated Message");
        assertNotNull(resultSheet);
    }

    @Test
    public void testWriteWithEmptyElements() {
        // Prepare test data
        Workbook workbook = new XSSFWorkbook();
        TwoLevelHeaderSheet<TestData> sheet = createEmptyTestSheet();

        // Set empty elements list
        sheet.setElements(new ArrayList<>());

        // Execute
        TwoLevelExportAbility.write(sheet, workbook);

        // Verify
        Sheet resultSheet = workbook.getSheet("Translated Message");
        assertNotNull(resultSheet);
        // Only header rows, no data rows
        assertEquals(2, resultSheet.getLastRowNum() + 1);
    }

    @Test
    public void testSetDynamicColumnWidths() {
        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Test Sheet");

        // 创建表头行
        Row groupRow = sheet.createRow(0);
        Row fieldRow = sheet.createRow(1);

        // 创建一些测试单元格
        Cell nameCell = fieldRow.createCell(0);
        nameCell.setCellValue("测试字段名称");

        Cell codeCell = fieldRow.createCell(1);
        codeCell.setCellValue("测试编号");

        Cell otherCell = fieldRow.createCell(2);
        otherCell.setCellValue("其他字段");

        // 执行测试方法
        TwoLevelExportAbility.setDynamicColumnWidths(sheet);

        // 验证列宽设置
        int nameColumnWidth = sheet.getColumnWidth(0);
        int codeColumnWidth = sheet.getColumnWidth(1);
        int otherColumnWidth = sheet.getColumnWidth(2);

        // 验证列宽被设置了
        assertTrue(nameColumnWidth > 0);
        assertTrue(codeColumnWidth > 0);
        assertTrue(otherColumnWidth > 0);
    }

    @Test
    public void testCreateSXSSFWorkbook() {
        // 创建一个工作簿
        SXSSFWorkbook workbook = TwoLevelExportAbility.createSXSSFWorkbook(100);

        // 验证工作簿被正确创建
        assertNotNull(workbook);

        // 清理临时文件
        workbook.dispose();
    }

    @Test(expected = RuntimeException.class)
    public void testWriteWithException() {
        // 准备测试数据
        Workbook workbook = PowerMockito.mock(XSSFWorkbook.class);
        TwoLevelHeaderSheet<TestData> sheet = createEmptyTestSheet();

        // 添加测试元素
        List<TestData> elements = new ArrayList<>();
        elements.add(new TestData("Test Name", "Test Value"));
        sheet.setElements(elements);

        // 模拟异常情况
        PowerMockito.when(workbook.getSheet(anyString())).thenThrow(new NullPointerException("Test exception"));

        // 执行 - 应该抛出RuntimeException
        TwoLevelExportAbility.write(sheet, workbook);
    }

    @Ignore
    @Test
    public void testCreateErrorReportExcel() {
        // 创建一个异常
        Exception testException = new RuntimeException("Test exception message");
        TemplateIdEnum mockTemplateId = TemplateIdEnum.CHANGE_ORDER_EXPORT;

        // Mock静态方法
        PowerMockito.mockStatic(FileStorageUtils.class);
        PowerMockito.mockStatic(ContextHelper.class);
        PowerMockito.mockStatic(EmailClient.class);

        when(FileStorageUtils.uploadExportFile(anyString(), any(byte[].class))).thenReturn("test-key");
        when(FileStorageUtils.buildDownLoadUrl(anyString(), anyString())).thenReturn("http://example.com/download");
        when(ContextHelper.getEmpNo()).thenReturn("12345");

        // 执行方法
        TwoLevelExportAbility.createErrorReportExcel(testException, mockTemplateId);

        // 验证方法被调用
        PowerMockito.verifyStatic(FileStorageUtils.class);
        FileStorageUtils.uploadExportFile(anyString(), any(byte[].class));

        PowerMockito.verifyStatic(FileStorageUtils.class);
        FileStorageUtils.buildDownLoadUrl(anyString(), anyString());

        PowerMockito.verifyStatic(EmailClient.class);
        EmailClient.sendMail(any(TemplateIdEnum.class), anyList(), any(), anyMap(), any());
    }

    @Test
    public void testAppendRowsToExistingSheet() throws Exception {
        // 创建包含表头的工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Test Sheet");

        // 创建表头行
        Row groupRow = sheet.createRow(0);
        Row fieldRow = sheet.createRow(1);

        // 创建几个数据行
        for (int i = 0; i < 3; i++) {
            Row row = sheet.createRow(i + 2);
            Cell cell = row.createCell(0);
            cell.setCellValue("Existing Data " + i);
        }

        // 准备要追加的数据
        TwoLevelHeaderSheet<TestData> appendSheet = createEmptyTestSheet();

        List<TestData> elements = new ArrayList<>();
        elements.add(new TestData("Appended Name 1", "Test Value 1"));
        elements.add(new TestData("Appended Name 2", "Test Value 2"));
        appendSheet.setElements(elements);

        // 追加数据
        TwoLevelExportAbility.appendRowsToExistingSheet(appendSheet, workbook, sheet);

        // 验证工作表存在
        assertNotNull(sheet);
    }

    @Test
    public void testCalculateTextLength() {
        // 通过反射调用私有方法calculateTextLength
        try {
            Method method = TwoLevelExportAbility.class.getDeclaredMethod("calculateTextLength", String.class);
            method.setAccessible(true);

            // 测试空字符串
            assertEquals(0, method.invoke(null, (String) null));
            assertEquals(0, method.invoke(null, ""));

            // 测试纯英文字符串
            assertEquals(3, method.invoke(null, "abc"));

            // 测试中文字符串 (中文字符应计为2个单位)
            assertEquals(4, method.invoke(null, "中文"));
        } catch (Exception e) {
            fail("反射调用calculateTextLength方法失败: " + e.getMessage());
        }
    }

    @Test
    public void testSetDynamicColumnWidthsWithNullSheet() {
        // 调用方法时传入null，应该不会抛出异常
        TwoLevelExportAbility.setDynamicColumnWidths(null);

        // 创建没有行的工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Empty Sheet");

        // 应该不会抛出异常
        TwoLevelExportAbility.setDynamicColumnWidths(sheet);

        // 只有头行但没有字段行的情况
        sheet.createRow(0);
        TwoLevelExportAbility.setDynamicColumnWidths(sheet);
        assertNotNull(workbook);
    }

    @Test
    public void testCreateErrorReportSheetWithNullMessage() throws Exception {
        Workbook workbook = new XSSFWorkbook();
        Exception exception = new NullPointerException(); // NullPointerException通常没有消息

        // 使用反射调用私有方法
        Method method = TwoLevelExportAbility.class.getDeclaredMethod(
                "createErrorReportSheet", Workbook.class, Exception.class);
        method.setAccessible(true);
        Sheet sheet = (Sheet) method.invoke(null, workbook, exception);

        // 验证结果
        Row dataRow = sheet.getRow(1);
        assertEquals("No error message", dataRow.getCell(2).getStringCellValue());
    }

    /**
     * 创建简单的空测试Sheet，避免使用ExportFieldEnum
     */
    private TwoLevelHeaderSheet<TestData> createEmptyTestSheet() {
        TwoLevelHeaderSheet<TestData> sheet = new TwoLevelHeaderSheet<>();
        sheet.setSheetName("Translated Message");

        // 使用空列表避免加载大枚举
        List<ExportFieldEnum> fields = new ArrayList<>();
        sheet.setExportFields(fields);

        // 创建空的header info列表
        List<ExportFieldEnum.ExportHeaderInfo> headerInfos = new ArrayList<>();
        sheet.setHeaderInfos(headerInfos);

        return sheet;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TestData {
        private String name;
        private String value;
    }
}