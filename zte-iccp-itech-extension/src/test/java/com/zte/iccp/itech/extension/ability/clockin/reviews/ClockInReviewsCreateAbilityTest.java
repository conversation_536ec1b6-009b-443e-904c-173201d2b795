package com.zte.iccp.itech.extension.ability.clockin.reviews;

import com.google.common.collect.Lists;
import com.zte.iccp.itech.extension.ability.ApproverConfigAbility;
import com.zte.iccp.itech.extension.ability.PartnerChangeOrderAbility;
import com.zte.iccp.itech.extension.ability.clockin.ClockInQueryAbility;
import com.zte.iccp.itech.extension.common.helper.ConfigHelper;
import com.zte.iccp.itech.extension.common.helper.QueryDataHelper;
import com.zte.iccp.itech.extension.domain.enums.assignment.AssignmentTypeEnum;
import com.zte.iccp.itech.extension.domain.enums.clockin.ClockInOptionEnum;
import com.zte.iccp.itech.extension.domain.model.ApproverConfiguration;
import com.zte.iccp.itech.extension.domain.model.ChangeOrder;
import com.zte.iccp.itech.extension.domain.model.SubcontractorChangeOrder;
import com.zte.iccp.itech.extension.domain.model.base.Employee;
import com.zte.iccp.itech.extension.domain.model.base.TextValuePair;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInRecord;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInReviews;
import com.zte.iccp.itech.extension.domain.model.clockin.ClockInTask;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

/**
 * <AUTHOR> 10344315
 * @date 2025-05-14 上午8:36
 **/
@RunWith(PowerMockRunner.class)
@PrepareForTest({QueryDataHelper.class,
        ClockInQueryAbility.class,
        ConfigHelper.class,
        ApproverConfigAbility.class,
        PartnerChangeOrderAbility.class
})
public class ClockInReviewsCreateAbilityTest {
    @Before
    public void setUp() {
        mockStatic(QueryDataHelper.class);
        mockStatic(ClockInQueryAbility.class);
        mockStatic(ConfigHelper.class);
        mockStatic(ApproverConfigAbility.class);
        mockStatic(PartnerChangeOrderAbility.class);
    }

    @Test
    public void testFindRecord() throws Exception{
        // 组装方法入参
        List<String> batchTaskIdList = Lists.newArrayList();
        Map<String, List<ClockInTask>> cancelBatchTaskIdClockMap = new HashMap<>();
        Map<String, List<ClockInTask>> abnormalBatchTaskIdClockMap = new HashMap<>();
        Map<String, ClockInRecord> recordMap = new HashMap<>();

        // 获取被测方法
        Class clazz = Class.forName("com.zte.iccp.itech.extension.ability.clockin.reviews.ClockInReviewsCreateAbility");
        Method method = clazz.getDeclaredMethod("findRecord", List.class, Map.class, Map.class, Map.class);
        method.setAccessible(true);

        // 模拟方法内部执行
        PowerMockito.when(QueryDataHelper.query(eq(ClockInTask.class), anyList(), anyList()))
                        .thenReturn(Lists.newArrayList(new ClockInTask(){{
                            setId("clockInTaskId");
                            setBatchTaskId("batchTaskId");
                        }}));
        PowerMockito.when(ClockInQueryAbility.getClockInRecordList(anyList()))
                        .thenReturn(Lists.newArrayList(new ClockInRecord(){{
                            setId("clockInRecordId");
                            setPid("clockInTaskId");
                            setClockInOption(ClockInOptionEnum.CANCEL);
                        }}).stream().collect(Collectors.groupingBy(ClockInRecord::getPid)));
        PowerMockito.when(ConfigHelper.get(anyString())).thenReturn("UziOrgPathId");

        method.invoke(clazz, batchTaskIdList, cancelBatchTaskIdClockMap, abnormalBatchTaskIdClockMap, recordMap);

        Assert.assertTrue(CollectionUtils.isEmpty(Lists.newArrayList()));
    }

    @Test
    public void testGetClockInReviewsInitCurrentProcessors() {
        // 测试场景1：合作方网络变更单
        ClockInReviews clockInReviews1 = new ClockInReviews();
        clockInReviews1.setChangeOrderId("subcontractor-change-order-id");
        TextValuePair assignmentType1 = new TextValuePair();
        assignmentType1.setValue("6"); // SUBCONTRACTOR_NETWORK_CHANGE
        clockInReviews1.setAssignmentType(Lists.newArrayList(assignmentType1));

        // Mock ApproverConfigAbility.getApproverConfiguration
        ApproverConfiguration approverConfig1 = new ApproverConfiguration();
        List<Employee> approvers1 = Lists.newArrayList();
        Employee emp1 = new Employee();
        emp1.setEmpUIID("emp-1");
        emp1.setEmpName("Employee 1");
        approvers1.add(emp1);
        approverConfig1.setApproverPersons(approvers1);

        PowerMockito.when(ApproverConfigAbility.getApproverConfiguration(any(), any(), any()))
                .thenReturn(approverConfig1);

        // Mock getApprovers方法
        PowerMockito.when(ApproverConfigAbility.getApprovers(any(ApproverConfiguration.class)))
                .thenReturn(approvers1);

        // Mock QueryDataHelper.get for SubcontractorChangeOrder
        SubcontractorChangeOrder subcontractorChangeOrder = new SubcontractorChangeOrder();
        subcontractorChangeOrder.setId("subcontractor-change-order-id");
        PowerMockito.when(QueryDataHelper.get(eq(SubcontractorChangeOrder.class), anyList(), anyString()))
                .thenReturn(subcontractorChangeOrder);

        // 执行测试
        List<Employee> result1 = ClockInReviewsCreateAbility.getClockInReviewsInitCurrentProcessors(clockInReviews1);

        // 验证结果
        Assert.assertNotNull(result1);
        Assert.assertEquals(1, result1.size());
        Assert.assertEquals("emp-1", result1.get(0).getEmpUIID());

        // 测试场景2：内部网络变更单
        ClockInReviews clockInReviews2 = new ClockInReviews();
        clockInReviews2.setChangeOrderId("internal-change-order-id");
        TextValuePair assignmentType2 = new TextValuePair();
        assignmentType2.setValue("1"); // INTERNAL_NETWORK_CHANGE
        clockInReviews2.setAssignmentType(Lists.newArrayList(assignmentType2));

        // Mock QueryDataHelper.get for ChangeOrder
        ChangeOrder changeOrder = new ChangeOrder();
        changeOrder.setId("internal-change-order-id");
        changeOrder.setProductCategory("product-category");
        changeOrder.setResponsibleDept("responsible-dept");
        changeOrder.setIsGovEnt(Lists.newArrayList(new TextValuePair()));
        PowerMockito.when(QueryDataHelper.get(eq(ChangeOrder.class), anyList(), anyString()))
                .thenReturn(changeOrder);

        // Mock ApproverConfigAbility.getApprover
        ApproverConfiguration approverConfig2 = new ApproverConfiguration();
        List<Employee> approvers2 = Lists.newArrayList();
        Employee emp2 = new Employee();
        emp2.setEmpUIID("emp-2");
        emp2.setEmpName("Employee 2");
        approvers2.add(emp2);
        approverConfig2.setApproverPersons(approvers2);

        PowerMockito.when(ApproverConfigAbility.getApprover(any(ChangeOrder.class), anyString(), any()))
                .thenReturn(approverConfig2);

        // 执行测试
        List<Employee> result2 = ClockInReviewsCreateAbility.getClockInReviewsInitCurrentProcessors(clockInReviews2);

        // 验证结果
        Assert.assertNotNull(result2);
        Assert.assertEquals(1, result2.size());
        Assert.assertEquals("emp-2", result2.get(0).getEmpUIID());

        // 测试场景3：空的AssignmentType
        ClockInReviews clockInReviews3 = new ClockInReviews();
        clockInReviews3.setChangeOrderId("test-change-order-id");
        clockInReviews3.setAssignmentType(Lists.newArrayList());

        // 执行测试 - 应该返回空列表或默认值
        List<Employee> result3 = ClockInReviewsCreateAbility.getClockInReviewsInitCurrentProcessors(clockInReviews3);

        // 验证结果
        Assert.assertNotNull(result3);

        // 测试场景4：ApproverConfiguration为null的情况
        ClockInReviews clockInReviews4 = new ClockInReviews();
        clockInReviews4.setChangeOrderId("null-config-change-order-id");
        clockInReviews4.setAssignmentType(Lists.newArrayList(assignmentType1));

        PowerMockito.when(ApproverConfigAbility.getApproverConfiguration(any(), any(), any()))
                .thenReturn(null);
        PowerMockito.when(ApproverConfigAbility.getApprovers(null))
                .thenReturn(Lists.newArrayList());

        // 执行测试
        List<Employee> result4 = ClockInReviewsCreateAbility.getClockInReviewsInitCurrentProcessors(clockInReviews4);

        // 验证结果
        Assert.assertNotNull(result4);
    }
}
