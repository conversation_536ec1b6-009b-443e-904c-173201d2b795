package com.zte.iccp.itech.extension.common.utils;

import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @since 2025/07/22
 */
public class LangUtilsTest {
    @Test
    public void containsZhCharTest() {
        // 只含中文
        assertTrue(LangUtils.containsZhChar("你好"));
        // 混合中英文
        assertTrue(LangUtils.containsZhChar("hello你好"));
        // 混合中文和数字
        assertTrue(LangUtils.containsZhChar("123你好456"));
        // 只含英文
        assertFalse(LangUtils.containsZhChar("hello"));
        // 只含数字
        assertFalse(LangUtils.containsZhChar("123456"));
        // 只含特殊符号
        assertFalse(LangUtils.containsZhChar("!@#$%"));
        // 空字符串
        assertFalse(LangUtils.containsZhChar(""));
        // null
        assertFalse(LangUtils.containsZhChar(null));
        // 只含空格
        assertFalse(LangUtils.containsZhChar("   "));
        // 中文标点
        assertFalse(LangUtils.containsZhChar("，。！？"));
        // 英文和特殊符号
        assertFalse(LangUtils.containsZhChar("abc!@#"));
        // 中文和特殊符号
        assertTrue(LangUtils.containsZhChar("！你好！"));
    }

    @Test
    public void allZhCharTest() {
        // 只含中文
        assertTrue(LangUtils.allZhChar("你好"));
        // 混合中英文
        assertFalse(LangUtils.allZhChar("hello你好"));
        // 混合中文和数字
        assertFalse(LangUtils.allZhChar("123你好456"));
        // 只含英文
        assertFalse(LangUtils.allZhChar("hello"));
        // 只含数字
        assertFalse(LangUtils.allZhChar("123456"));
        // 只含特殊符号
        assertFalse(LangUtils.allZhChar("!@#$%"));
        // 空字符串
        assertFalse(LangUtils.allZhChar(""));
        // null
        assertFalse(LangUtils.allZhChar(null));
        // 只含空格
        assertFalse(LangUtils.allZhChar("   "));
        // 中文标点
        assertFalse(LangUtils.allZhChar("，。！？"));
        // 英文和特殊符号
        assertFalse(LangUtils.allZhChar("abc!@#"));
        // 中文和特殊符号
        assertFalse(LangUtils.allZhChar("！你好！"));
    }
}