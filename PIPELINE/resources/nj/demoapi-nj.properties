#\u76EE\u524D\u4F7F\u7528\u7684\u9A71\u52A8\u7248\u672C UME\u65E0\u6CD5\u91C7\u96C6SQL\uFF0C\u9700\u8981\u66F4\u6362\u6570\u636E\u5E93\u9A71\u52A8
lcap.dynamicDatasource.goldenDriverClassName=com.mysql.jdbc.Driver

# \u6392\u9664 upp \u67E5\u8BE2\u5FEB\u7801\u7EA6\u675F\u6570\u636E\u63A5\u53E3(\u6240\u6709\u7684\u73AF\u5883\u90FD\u662F\u540C\u4E00\u4E2A\u63A5\u53E3)\uFF0C\u914D\u7F6E\u9879\u6570\u7EC4\u4E0B\u6807\u6570\u5B57[?]\uFF0C\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u6309\u7167\u987A\u5E8F\u9012\u589E # \u751F\u4EA7\u73AF\u5883\uFF1A
web.interceptor.header-verification.excludePathPatterns[43] = /v1/app/*/fastCodeConstraint
web.interceptor.header-verification.excludePathPatterns[44] = /v1/app/*/fastCodeTreeDataConstraint
web.interceptor.header-verification.excludePathPatterns[45] = /v1/app/*/fastCodeConstraint
web.interceptor.header-verification.excludePathPatterns[46] = /v1/app/*/fastCodeTreeDataConstraint

# \u63A8\u9001\u65E5\u5FD7\u4E2D\u5FC3\u5BA1\u8BA1\u65E5\u5FD7\uFF0C\u9700\u914D\u7F6E\u7CFB\u7EDF\u552F\u4E00\u6807\u8BC6\uFF08pdm\u7F16\u53F7\uFF09\uFF0C\u5176\u4ED6\u7CFB\u7EDF\u914D\u7F6E\u81EA\u5DF1\u7684\u7F16\u53F7
lcap.sub.syscode = 570056

lcap.default.instance.route.url=https://itech.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi
plugin_load_type=S3
lcap.comment.mobile.sysCode=200320
register.address=nj-msr.msp.zte.com.cn:10081
register.node.ip=***********
register.node.port=80
register.version=v1
servicecenter=msb
#apijson\u7248\u672C\u53F7
apijson.db.version=5.7.22

#apijson\u6570\u636E\u5E93\u4FE1\u606F\uFF0C\u548C\u4F4E\u4EE3\u7801\u5143\u6570\u636E\u5B58\u50A8\u7684\u6570\u636E\u5E93\u4E00\u81F4\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\uFF09
apijson.db.schema=itech_promgr
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF0C\u5F85\u5B9A\uFF09
jdbc1.type=com.alibaba.druid.pool.DruidDataSource
jdbc1.driverClassName=com.mysql.jdbc.Driver
#\u5143\u6570\u636E\u5E93\u914D\u7F6E\u4FE1\u606F\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u6570\u636E\u5E93\u914D\u7F6E\uFF09
jdbc1.url=*****************************************,************:8201/itech_promgr?useUnicode=true&characterEncoding=UTF-8&useCursorFetch=true&autoReconnect=true&zeroDateTimeBehavior=convertToNull&connectTimeout=60000&failOverReadOnly=false&loadBalancePingTimeout=1000&allowMultiQueries=true&isConnectionLevel=true&blackTaskTime=5&intervalTime=3000
#druid\u914D\u7F6E
spring.datasource.druid.stat-view-servlet.enabled=false
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.db-type=mysql
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=500

#jdbc\u8FDE\u63A5\u76F8\u5173\u914D\u7F6E
jdbc.maxActive=100
#jdbc.minIdle = 20
#jdbc.initialSize = 20
jdbc.validationQuery=SELECT 1 FROM DUAL
jdbc.testOnBorrow=true
jdbc.testOnReturn=true
jdbc.minEvictableIdleTimeMillis=600000
jdbc.druid.maxEvictableIdleTimeMillis=3600000
jdbc.timeBetweenEvictionRunsMillis=60000
jdbc.removeAbandoned=false
jdbc.poolPreparedStatements=false
jdbc.druid.keepAlive=true

#flyway\u76F8\u5173\u914D\u7F6E
spring.flyway.enabled=false
spring.flyway.encoding=UTF-8
spring.flyway.locations=classpath:db/migration/prod
spring.flyway.sql-migration-prefix=V
spring.flyway.sql-migration-separator=__
spring.flyway.validate-on-migrate=true
spring.flyway.baseline-on-migrate=true
spring.flyway.placeholder-replacement=false


#spring.flyway.out-of-order = true
#\u8BBE\u8BA1\u6001\u63A5\u53E3\u9274\u6743\u914D\u7F6E\uFF0C\u4E0D\u7528\u6539
lcappro.design.auth.enabled=true
inone.url=https://icosg.dt.zte.com.cn
# \u5B9E\u4F53\u540C\u6B65es\u7684\u63A5\u53E3\u5730\u5740\u540E\u7F00

sync.interface.suffix = /api/v1/sync
# \u4E0A\u4F20\u6587\u4EF6\u5927\u5C0F\u9650\u5236
spring.servlet.multipart.max-file-size = 200MB
spring.servlet.multipart.max-request-size = 200MB
#uac\u76F8\u5173\u914D\u7F6E
#\u9009\u586B\uFF0C\u79DF\u6237ID\uFF0C\u9ED8\u8BA410001\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.tenantId = 10001

#\u9009\u586B\uFF0C\u7CFB\u7EDF\u7F16\u7801/PDM\u7F16\u7801\u7B49\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.appId = ************
#\u5FC5\u586B\uFF0C\u670D\u52A1\u53D1\u884C\u7AEF\uFF0C\u548C\u73AF\u5883\u8981\u5BF9\u5E94\uFF0C\u5982\uFF1Ahttps://uac.zte.com.cn/zte-sec-uac-iportalbff/oidc/${uac.sdk.tenantId}
uac.sdk.issuer = https://uac.zte.com.cn/zte-sec-uac-iportalbff/oidc/10001
#\u5FC5\u586B\uFF0C\u4E1A\u52A1\u7CFB\u7EDF\u57FA\u7840url\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.baseUrl = https://itech.zte.com.cn/zte-iccp-itech-demobff
#\u5FC5\u586B\uFF0C\u7CFB\u7EDF\u7F16\u7801\uFF0C\u7533\u8BF7\u83B7\u5F97\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.clientId = ************
uac.sdk.responseRule = msa
uac.sdk.store = redis
uac.url = https://uac.zte.com.cn
uac.sdk.env = prod
uac.sdk.cookie.httpOnly = false
uac.sdk.gateAuth.externalAccessEnabled = true
uac.sdk.cookie.secure = true
# \u83B7\u53D6\u8D26\u6237\u4FE1\u606F\u4F7F\u7528uac3.0\u67E5\u8BE2\uFF08\u5185\u573A\u73AF\u5883\uFF1AuacEmployeeProvider\uFF0C \u5916\u573A\u73AF\u5883\uFF1AuacEmployeeOuterProvider\uFF09
lcap.employee.provider = uacEmployeeProvider

# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879
# hcp.bucket.non-anonymous = xxxx
# hcp.endpoint = xxxx.xxxx.zte.com.cn
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************
# uac3.0\u6279\u91CF\u67E5\u8BE2\u8D26\u6237\u7C7B\u578B\uFF08\u5185\u573A\u73AF\u5883\uFF1AT0001\uFF0C \u5916\u573A\u73AF\u5883\uFF1AT0000\uFF09
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879
#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
# hcp.bucket.non-anonymous = xxxx
# hcp.endpoint = xxxx.xxxx.zte.com.cn
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************
#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E

#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
uac.idType = T0001
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09

# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************
# uac3.0\u79D8\u94A5\uFF08\u65E0\u9700\u4FEE\u6539\uFF0C\u4EA7\u7EBFuac\u7684\u7EDF\u4E00\u79D8\u94A5\uFF09

# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
# \u544A\u8B66\u4E0A\u62A5\u5730\u5740 \u5FC5\u586B\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF0C\u627EUME\uFF09

#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
dtems.agent.amosBaseUrl = https://zcloudamos.dt.zte.com.cn
# \u5E94\u7528\u5B9E\u4F8BID\uFF0C\u5FC5\u586B
dtems.agent.applicationInsId = 5398806042717206

#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache

# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879
# \u7EC4\u4EF6\u542F\u7528\u603B\u5F00\u5173 \u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u4E3Atrue
# hcp.bucket.non-anonymous = xxxx

# hcp.endpoint = xxxx.xxxx.zte.com.cn
dtems.agent.enable = true
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************
dtems.agent.codes.default = 504774601
#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
dtems.agent.moc = cn.dt.business
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09
#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************
# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
# alarm.code.operation = xxx
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
# alarm.businessId = xxx
# alarm.code.rule = xxx
# alarm.code.privilege = xxx
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879
# hcp.bucket.non-anonymous = xxxx
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
# hcp.endpoint = xxxx.xxxx.zte.com.cn
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879
# hcp.bucket.non-anonymous = xxxx
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
# hcp.endpoint = xxxx.xxxx.zte.com.cn
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
# alarm.code.pluginLoading = xxx
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************

#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
#\u56FD\u5BB6\u5730\u533A\u5BF9\u63A5emdm\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF0C\u627Eemdm\uFF09
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

emdm.queryUrl = https://emdm.zte.com.cn/esbmule/services/custom/queryPatchGJDQ
#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E

emdm.usercode = iepms300
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************
# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD

# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD

#ucs\u914D\u7F6E\uFF0C\u8C03\u7528\u5458\u5DE5\u4E2D\u5FC3\u4F7F\u7528\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF0C\u627Euac\uFF09
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache

ucs.tenantId = 10001

#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache

#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879
# hcp.bucket.non-anonymous = xxxx
# hcp.endpoint = xxxx.xxxx.zte.com.cn

#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

#\u7F13\u5B58\u4E91\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
msa.ccs.enable = true

#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************

#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
msa.ccs.resourceCode = zxiccp-iepms300-itechcloud
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
cacheCloud.server = http://cache.zcloud.zte.com.cn:8888/zte-itp-msp-cachecloud/

#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09
#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
redis.mode = 4
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

redis.customCacheManager = true
# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
msa.redis.driveType = jedis
msa.redis.serializerType = genericJackson2Json

msa.redis.sotimeout = 1000

# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
msa.redis.maxAttempts = 2
msa.redis.maxRedirects = 5
msa.redis.pool.maxActive = 300
msa.redis.pool.maxWait = 3000
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
msa.redis.pool.maxIdle = 300

msa.redis.pool.minIdle = 300
msa.redis.pool.minEvictableIdleTimeMillis = 1800000
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache

# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879

# hcp.bucket.non-anonymous = xxxx
msa.redis.timeout = 3000
# hcp.endpoint = xxxx.xxxx.zte.com.cn

#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
msa.redis.cacheManager.expire = 86400
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************

#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
#\u7F13\u5B58\u4E91\u54E8\u5175\u8D44\u6E90\uFF0C\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D

#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09
#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
spring.redis.sentinel.master = zxiccp-iepms300-itechcloud
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
spring.redis.sentinel.nodes = ************:36379,************:36379,*************:36379,************:36379,***********:36379

#\u6587\u6863\u4E91\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
cloudDiskSDK.host = https://idrive.zte.com.cn/zte-km-cloududm-clouddisk
cloudDiskSDK.httpClientLogSwitch = true

cloudDiskSDK.xOriginServiceName = EPMS
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache

##\u9644\u4EF6\u9884\u89C8\u914D\u7F6E \u6D4B\u8BD5\u73AF\u5883\uFF1Ahttps://moatest.zte.com.cn:8443  \u751F\u4EA7\u914D\u7F6E\uFF1Ahttps://docview.zte.com.cn
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
lcap.file.previewUrl = https://docview.zte.com.cn


# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879

# hcp.bucket.non-anonymous = xxxx

# hcp.endpoint = xxxx.xxxx.zte.com.cn

#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************

#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
#
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
cos.host.https = https://apigwfw.zte.com.cn:2443

#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09

#\u6743\u9650\u914D\u7F6E,\u6362\u6210\u5E94\u7528\u4FA7\u81EA\u5DF1\u7684\uFF1B\u82E5\u672A\u7528\u5230\u6743\u9650\uFF0C\u5219\u91C7\u7528\u4F4E\u4EE3\u7801\u9ED8\u8BA4\u914D\u7F6E
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

upp.auth.productId = 570056
# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E

upp.auth.moduleId = ************
upp.auth.tenantId = 10001

# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
upp.auth.authUrl = https://uac.zte.com.cn/zte-sec-upp-authbff
upp.auth.manageUrl = https://uac.zte.com.cn/zte-sec-upp-bff
#upp\u56DE\u8C03\u63A5\u53E3\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u57DF\u540D\u914D\u7F6E\uFF09
upp.specialDataAuth.url.format = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=page&bizObject=specialDataAuth#/app/%s/page/PAGE0917349422860693510

#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
upp.fieldAuth.url.format = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/dataPermission.html#/upp/%s/roleAuthorization/fieldAuth

# \u6240\u6709\u73AF\u5883\u7528\u540C\u4E00\u4E2A\uFF0C\u5E94\u7528\u4FA7\u65E0\u9700\u4FEE\u6539

#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache

#RSA\u52A0\u89E3\u5BC6\u5DE5\u5177\u7C7B\u914D\u7F6E(\u6240\u6709\u7684\u73AF\u5883\u90FD\u662F\u540C\u4E00\u4E2A\uFF0C\u5E94\u7528\u4FA7\u65E0\u9700\u989D\u5916\u7533\u8BF7)
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879

# hcp.bucket.non-anonymous = xxxx

# hcp.endpoint = xxxx.xxxx.zte.com.cn
upp.auth.readTimeout = 2000
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
upp.auth.connectTimeout = 2000

upp.auth.cacheMaxSize = 10000
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************
upp.auth.cacheInitSize = 1000
#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E

#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D


#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

upp.auth.maxConnections = 30
#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09

upp.auth.maxPerRoute = 10
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E

upp.auth.cacheResourceTime = 60
upp.auth.prod.constraintId = 720126
upp.auth.org.constraintId = 720127

plugin.public.upp.role.codes = iTech0001,iTech0002,iTech0003,iTech0004,iTech0005,iTech0006,iTech0007,iTech0008,iTech0009,iTech0010,iTech0011,iTech0012,iTech0013,iTech0014,iTech0015
# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD

#kafak\u76F8\u5173\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879

# hcp.bucket.non-anonymous = xxxx
message.server = http://message.zcloud.zte.com.cn:8888/zte-itp-msp-message/v1
message.inoneAppCode = KENC(vbOl6hefCQRY/gjqXGMtyt1bEISWYMoSRg9CM5y1davF09p52zi0cadUx5weQMkO$$hApMMMuPBs34GuiM)
# hcp.endpoint = xxxx.xxxx.zte.com.cn


#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
spring.kafka.consumer.groupId = zte-iccp-itech-netchange
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

spring.kafka.enabled = true

#\u5BA1\u6279\u4E2D\u5FC3\u76F8\u5173\u914D\u7F6E
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************

#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
approval.kafka.topic = zte-iss-approval-nj
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D

approval.sdk.app.appId = ************
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09

approval.sdk.app.appCode = ************
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
approval.sdk.header.xEmpNo.default = iTechCloud
approval.sdk.kafka.receipt.enabled = false
approval.sdk.webhook.url = https://itech.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/flow/webhookProcess
approval.sdk.use.signatureAlgorithm = SHA256

# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
#\u5BF9\u63A5msa\u52A0\u5BC6\u5730\u5740
keypath = http://msp.zte.com.cn/zte-itp-isoa-security/securityctl/getsk
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
#\u65B0\u589E\u9644\u4EF6\u4E0A\u4F20\u6587\u4EF6\u683C\u5F0F\u6821\u9A8C\uFF08\u7528\u4E8E\u6E17\u900F\u6D4B\u8BD5\uFF09\uFF0C\u914D\u7F6E\u7684\u6587\u4EF6\u7C7B\u578B\u5747\u6821\u9A8C\u4E0D\u901A\u8FC7
fileFormat.limit = php,exe,asp,jsp,aspx

lcap.redis.exception.inject.enabled = true
lcap.redis.exception.inject.down = false
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache

lcap.openapi.path.download = https://itech.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/attachment/temp/download/
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879

# hcp.bucket.non-anonymous = xxxx

# hcp.endpoint = xxxx.xxxx.zte.com.cn

#\u5BA1\u6279\u8BE6\u60C5\u9875\u652F\u6301\u8BC4\u8BBA\u529F\u80FD#icenter\u63D0\u4F9B\u7684\u7CFB\u7EDF\u7F16\u7801\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

#\u9009\u586B\uFF0C\u79DF\u6237ID\uFF0C\u9ED8\u8BA410001
# lcap.comment.icenter.sysCode = xxx

# lcap.comment.icenter.type = xxx
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************

#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
#\u9009\u586B\uFF0C\u7CFB\u7EDF\u7F16\u7801/PDM\u7F16\u7801\u7B49
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
# lcap.comment.email.address = xxx
# lcap.comment.serviceName = xxx
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09

#\u63A5\u53E3\u4E0D\u9274\u6743\u767D\u540D\u5355\u914D\u7F6E\uFF0C\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

web.interceptor.header-verification.excludePathPatterns[0] = *.js,*.gif,*.jpg,*.png,*.css,*.ico
# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
web.interceptor.header-verification.excludePathPatterns[1] = /swagger-*/**
web.interceptor.header-verification.excludePathPatterns[2] = /v3/api-docs

web.interceptor.header-verification.excludePathPatterns[3] = /v1/app/*/page/*/constraint/*/list
web.interceptor.header-verification.excludePathPatterns[4] = /v1/app/*/model/*/constraint/*/list
web.interceptor.header-verification.excludePathPatterns[5] = /v1/models/formdata

# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
web.interceptor.header-verification.excludePathPatterns[6] = /v1/models/formdata/*
web.interceptor.header-verification.excludePathPatterns[7] = /v1/attachment/tempurl
web.interceptor.header-verification.excludePathPatterns[8] = /v1/attachment/batch/tempurl

web.interceptor.header-verification.excludePathPatterns[9] = /v1/attachment/temp/download/*
web.interceptor.header-verification.excludePathPatterns[10] = /v1/flow/reassignTask
web.interceptor.header-verification.excludePathPatterns[11] = /v1/flow/pageFlowInstance

web.interceptor.header-verification.excludePathPatterns[12] = /v1/flowInstance/getFlowInstanceProcess
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
web.interceptor.header-verification.excludePathPatterns[13] = /v1/flowInstance/getOperateRecords

web.interceptor.header-verification.excludePathPatterns[14] = /v1/flowInstance/getPageInstanceDataByFlowInstance
web.interceptor.header-verification.excludePathPatterns[15] = /v1/flow/myTaskToDo
web.interceptor.header-verification.excludePathPatterns[16] = /v1/flow/saveFlowInstanceData

web.interceptor.header-verification.excludePathPatterns[17] = /v1/flow/submitTask
web.interceptor.header-verification.excludePathPatterns[18] = /v1/attachmentFile/queryInfo
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
web.interceptor.header-verification.excludePathPatterns[19] = /v1/attachmentFile/download

web.interceptor.header-verification.excludePathPatterns[20] = /prometheus
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879

# hcp.bucket.non-anonymous = xxxx
web.interceptor.header-verification.excludePathPatterns[21] = /error
# hcp.endpoint = xxxx.xxxx.zte.com.cn

web.interceptor.header-verification.excludePathPatterns[22] = /v1/flow/webhookProcess

web.interceptor.header-verification.excludePathPatterns[23] = /v1/bizobject/batchquery
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

web.interceptor.header-verification.excludePathPatterns[24] = /v1/bizobject/queryentity
web.interceptor.header-verification.excludePathPatterns[25] = /v1/application/batchquery

web.interceptor.header-verification.excludePathPatterns[26] = /v1/app/*/document/download/*
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************

#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
web.interceptor.header-verification.excludePathPatterns[27] = /v1/entryentity/redirect/*
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
web.interceptor.header-verification.excludePathPatterns[28] = /customComponents/resources/download/**
web.interceptor.header-verification.excludePathPatterns[29] = /v1/attachment/download
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
web.interceptor.header-verification.excludePathPatterns[30] = /v1/attachment/uploadFile
web.interceptor.header-verification.excludePathPatterns[31] = /v1/app/*/file/uncheck/downloadImage
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

web.interceptor.header-verification.excludePathPatterns[32] = /v1/app/file/uncheck/download
# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
web.interceptor.header-verification.excludePathPatterns[33] = /v1/app/*/bizObj/*/treeDataConstraint
web.interceptor.header-verification.excludePathPatterns[34] = /v1/app/*/bizObj/*/dataConstraint

web.interceptor.header-verification.excludePathPatterns[35] = /v1/app/*/bizObj/*/billDataConstraint
web.interceptor.header-verification.excludePathPatterns[36] = /scheduler/**
web.interceptor.header-verification.excludePathPatterns[37] = /APP0984032412495249408/batchtasks/*/4auth

# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
web.interceptor.header-verification.excludePathPatterns[38] = /zte-iccp-itech-demoapi/idop/**
web.interceptor.header-verification.excludePathPatterns[39] = /zte-iccp-itech-demoapi/networksecurity/**
# \u65B0\u589E\u4E0B\u8F7D\u52A0\u5BC6\u6587\u4EF6\u63A5\u53E3\uFF0C\u6DFB\u52A0\u5230\u767D\u540D\u5355\u4E2D
web.interceptor.header-verification.excludePathPatterns[40] = /v1/app/file/uncheck/encryptDownload
web.interceptor.header-verification.excludePathPatterns[41] = /zte-iccp-itech-demoapi/cnop/**
web.interceptor.header-verification.excludePathPatterns[42] = /zte-iccp-itech-demoapi/common/**
#\u751F\u4EA7\u7981\u7528swagger
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
springfox.documentation.enabled = false

springfox.documentation.auto-startup = false
swagger.exclude.url = /v2/api-docs

# \u5FEB\u7801\u67E5\u8BE2\u7684\u7CFB\u7EDF\u7F16\u7801\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
fastcode.iss.system.code = IEPMS
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache


# \u6570\u636E\u79BB\u7EBF\u5BFC\u51FA\u7684\u9608\u503C\uFF08\u9488\u5BF9\u6240\u6709\u5BFC\u51FA\u529F\u80FD\uFF09
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879

# hcp.bucket.non-anonymous = xxxx
sync.export.data.limit = 200
# hcp.endpoint = xxxx.xxxx.zte.com.cn

# \u5355\u636E\u4F53\u5BFC\u5165\u7684\u6700\u5927\u6570\u91CF\uFF08\u9488\u5BF9\u6240\u6709\u5355\u636E\u4F53\u5BFC\u5165\uFF09

entryEntity.import.maxCount = 100
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

#\u516C\u5171\u90AE\u7BB1\u5730\u5740\u90AE\u4EF6\u53D1\u9001\u5730\u5740\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

email.address = <EMAIL>
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************

#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E

#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
# \u670D\u52A1\u6839\u8DEF\u5F84\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
route.url = https://itech.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09

ruleEngine.impl = inferenceRuleEngine
#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09

# \u7EC4\u4EF6\u5143\u6570\u636E\u914D\u7F6E
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

lcap.metadata.name = RadioField_1666706847500906498,TreeView_1666708164319424512,Button_1666708164319424513,FormContainer_1668511540921364481,FlexContainer_1668511540921364521,Page_1668511540921364522,RootHeader_1668511540921364523,RootContent_1668511540921364524,RootFooter_1668511540921364525,CheckboxField_1669157069015523330,CreatorField_1669159037637271554,ModifierField_1669160258221678594,ColumnsLayout_1669163100164628482,TextField_1669263997930668034,TextareaField_1669264775302971393,NumberField_1669264843577851906,SelectField_1669264989531242498,MultiSelectField_1669265075824852994,TimeRangeField_1669265341731143681,TimeField_1669265453039583233,CascadeDateField_1669265491434242050,DateField_1669265512862941186,AttachmentField_1669266526156455937,EmployeeField_1669267520932118529,DepartmentSelectField_1669267628729925634,Steps_1669267902223712258,Link_1669268060441247745,TablePc_1669268167463108610,FieldLayoutPanel_1669268524767477762,AdvancedContainer_1669269111378640898,TabsLayout_1669269190479020033,ServiceObject_1669269190479021128,ToolbarField_1669269324835160065,Column_1671396272621334529,Tag_1673211663472484353,BillEntity_1674597481403899905,FastCodeField_1674610451040423938,FilterControl_1675305711890087937,BillStatusField_1675305711890087938,BasicDataField_1675305721830084930,BasicDataProps_1675305770033407910,BillNumberField_1675315325835024930,EntryEntity_1677146589633851394,SubEntryEntity_1677152291462983682,MultiFastCodeField_1677590491235467265,ServiceEntity_1679010253945192449,ServiceEntryEntity_1679052327948005378,TableFieldNew_1679310696938815489,CountrySelectField_1679760740479250434,Search_1679760740479250489,TabPanel_1681843769295593473,AssociationList_1681960665247842306,GroupDataField_1682277538857496578,ParentBasicDataField_1683369541332328449,MyReviewField_1686935437505396737,ToolbarItem_1688373444119355393,Prompt_1688726315538108418,EditorField_1688872066648133634,TeamViewPageField_1689878053168603137,CommentField_1690904008878206978,TeamConfigPageField_1690971833915285505,ApproveHistoryField_1691002512976228353,FlowProcessField_1691003592871096322,ExtendEntity_1691063693937098754,ApproveCommunicationField_1691064211279331330,AdvancedContainerToolbar_1692425780766523393,BillTypeField_1693824780483575810,MultiBasicDataField_1694190376775278594,PictureField_1694623101919408129,ServiceObjectProps_1697926811336531969,BusinessObjectProps_1697926453910528002,RichText_1697446694240657410,BusinessObjectField_1697919178500169730,ConditionField_1699715635744354306,ApprovalProgress_1706205948884803585,OperationRecords_1716749328987996162,SubTableField_1716287217534500865,MyReviewTileField_1716718545015562241,FormStatusDiagram_1712444431648636930,FilterProgramme_1726514685908578306,SubButtonItem_1725407107878014978,TextField0113_1729106748838604802,AttachmentDownloader_1742397387786379266,InlinePage_1742891095489077250,FlowChart_1745733431735631873,SwitchField_1745733468167356417,TreeSelectField_1746880575654084610,QrCode_1762315391887990786,MultiBusinessObjectField_1762315391887990996,Breadcrumb_1765568886942736386,SlotComponent_1771435586834993154,Iframe_1782769796791902209,RefComplexCondition_1783127124235382785,AggregationField_1783694852430053378,Card_1792759766927376385,CardContainer_1792759926776496130,CardToolbar_1792760098340306946,MultilingualTextField_1805044544690065409,MultiServiceObject_1724134829869895678,Control_1669268214657445762,StdExtMultiSelectField_1675304721833081930,StdExtSelectField_1669263167465103610,Divider_1724032682457898940,FilterCustom_1726514685908574851,ChangeDifference_1727245121817569854,GridCol_1969269111378640584,GridLayouter_1969269111378684572,GridRow_1969269111378648957,DropdownListField_1669264989531244587,Dialog_1669268524767476832,Filter_1669268524767473747,ValidateContainer_1746880575654483612,Div_1969269111378640897,SelectNewField_1109438420029014016,RadioNewField_1666706847500906499,DepartmentField_1669267628724824854,MultiSelectNewField_1669265075824852995,CheckboxNewField_1669157069015523331
# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E

#\u51FA\u73B0\u9519\u8BEF\u65F6, \u76F4\u63A5\u629B\u51FA\u5F02\u5E38

spring.mvc.throw-exception-if-no-handler-found = true

#\u4E0D\u8981\u4E3A\u6211\u4EEC\u5DE5\u7A0B\u4E2D\u7684\u8D44\u6E90\u6587\u4EF6\u5EFA\u7ACB\u6620\u5C04
spring.resources.add-mappings = false
# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD

#\u6743\u9650\u5185\u7F6E\u7684\u670D\u52A1\u6CE8\u518C\u63A5\u53E3\uFF0C\u67E5\u8BE2\u7CFB\u7EDF\u7684\u4E1A\u52A1\u5BF9\u8C61\u5217\u8868\uFF1B\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
lcap.auth.service.register.boListUrl = https://itech.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/app/bo/pageQuery

#RN\u8BBF\u95EEURL\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
rnUrl = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo

dynamic.domain.form.model.expire.time = 28800
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
dynamic.domain.sub.page.expire.time = 28800
dynamic.domain.operationContext.expire.time = 28800

lcap.ddm.validator.disable = true
dynamic.domain.load.status.mark.header.expire.time = 28800
dynamic.domain.load.table.field.data.expire.time = 28800
lcap.db.open.duplicate = false

#\u6839\u636E\u90E8\u7F72\u73AF\u5883\u914D\u7F6E\u57DF\u540D\u548C\u4E0A\u4E0B\u6587(dev=https://lcap.dev.zte.com.cn/zte-paas-lcap-demobff/zte-paas-lcap-demoapi,test=https://lcap.test.zte.com.cn/zte-paas-lcap-demobff/zte-paas-lcap-demoapi)\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
lcap.context.path.download = https://itech.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi

# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879
#\u65E5\u5FD7\u4E2D\u5FC3\u914D\u7F6E\uFF0C\u9700\u8981\u4E1A\u52A1\u7533\u8BF7topic
# hcp.bucket.non-anonymous = xxxx
isoalog.kafka.topic = ms-zte-crm-app-nj
# hcp.endpoint = xxxx.xxxx.zte.com.cn

#\u6587\u4EF6\u5B58\u50A8\u65B9\u5F0F\uFF08\u53EF\u9009Nas\u3001S3\uFF0C\u4E0D\u914D\u7F6E\u9ED8\u8BA4\u4E0A\u4F20\u81F3\u6587\u6863\u4E91\uFF09
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
lcap.oss.strategy = CloudUdm
#\u914D\u7F6E\u7684NAS\u8DEF\u5F84\u5730\u5740\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
lcap.oss.strategy.nas.path = /usr/local/app/lcap_pro/plugins/meta

#nas\u6700\u5927\u6587\u4EF6\u5927\u5C0F
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************
lcap.oss.strategy.nas.file.max.size = 209715200
#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647bca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E

#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
# s3\u914D\u7F6E,\u4E1A\u52A1\u81EA\u5DF1\u7533\u8BF7\uFF0C\u5BCC\u6587\u672C\u7EC4\u4EF6\u5FC5\u987B\u9879
# hcp.bucket.non-anonymous = xxxx
#\u6570\u636E\u6E90\u914D\u7F6E\uFF0C\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u4F4E\u4EE3\u7801\u9879\u76EE\u7EC4\u63D0\u4F9B\u52A0\u5BC6\u540E\u7684\u914D\u7F6E\uFF09
# hcp.endpoint = xxxx.xxxx.zte.com.cn

#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
#\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
lcap.host = itech.zte.com.cn
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************
lcap.host.port = itech.zte.com.cn/zte-iccp-itech-demobff/


# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406start******************
#\u4F7F\u7528\u5DE5\u5177\u7C7B\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/38430efe32d647crnbca1a94a88c5c073f7/view \u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u751F\u6210\u914D\u7F6E
#\u52A0\u89E3\u5BC6key\u96C6\u5408\uFF0C\u5373\u9700\u8981\u89E3\u5BC6\u7684key\u6DFB\u52A0\u5230\u6B64\u914D\u7F6E\u4E2D
encrypt.property.list = datasource.config.gcm.encrypt.key,notice.key

#\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD

#*************\u660E\u6587\u5BC6\u7801\u6CBB\u7406end******************

# jetcache\u4E24\u7EA7\u7F13\u5B58\u914D\u7F6E
jetcache.statIntervalMinutes = 60
jetcache.areaInCacheName = false
jetcache.hiddenPackages = com.zte
jetcache.local.default.type = caffeine
jetcache.local.default.keyConvertor = fastjson2
jetcache.local.default.limit = 500
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
# \u6307\u5B9A\u591A\u957F\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\uFF0C\u5C31\u8BA9\u7F13\u5B58\u5931\u6548\uFF0C\u5F53\u524D\u53EA\u6709\u672C\u5730\u7F13\u5B58\u652F\u6301\u30020\u8868\u793A\u4E0D\u4F7F\u7528\u8FD9\u4E2A\u529F\u80FD
jetcache.local.default.expireAfterAccessInMillis = 0
jetcache.local.default.expireAfterWriteInMillis = 1800000
jetcache.local.otherArea.type = caffeine
jetcache.local.otherArea.keyConvertor = fastjson2
jetcache.local.otherArea.limit = 100
jetcache.local.otherArea.expireAfterAccessInMillis = 0
jetcache.local.otherArea.expireAfterWriteInMillis = 1800000
jetcache.remote.default.type = redis.zlettuce
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
jetcache.remote.default.keyPrefix = lcap_
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
jetcache.remote.default.broadcastChannel = defaultDynamicDomainService
jetcache.remote.default.keyConvertor = fastjson2
jetcache.remote.default.valueEncoder = kryo5
jetcache.remote.default.valueDecoder = kryo5
jetcache.remote.default.expireAfterWriteInMillis = 3600000
jetcache.remote.default.uri = redis-sentinel://${spring.redis.password}@${spring.redis.sentinel.nodes}?timeout=10s&sentinelMasterId=${spring.redis.sentinel.master}&database=${redis.uri.database}

jetcache.remote.otherArea.type = redis.zlettuce
jetcache.remote.otherArea.keyPrefix = lcap_
#\u4E24\u7EA7\u7F13\u5B58\u652F\u6301\u66F4\u65B0\u4EE5\u540E\u5931\u6548\u5176\u4ED6JVM\u4E2D\u7684local cache
jetcache.remote.otherArea.broadcastChannel = otherDynamicDomainService
jetcache.remote.otherArea.keyConvertor = fastjson2
jetcache.remote.otherArea.valueEncoder = kryo5
jetcache.remote.otherArea.valueDecoder = kryo5
jetcache.remote.otherArea.expireAfterWriteInMillis = 3600000
jetcache.remote.otherArea.uri = redis-sentinel://${spring.redis.password}@${spring.redis.sentinel.nodes}?timeout=10s&sentinelMasterId=${spring.redis.sentinel.master}&database=${redis.uri.database}
redis.uri.database = 1

#\u9875\u9762\u8FD0\u884C\u6001url\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
page.running.url.format = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=app#/app/%s/page/%s
#\u5E94\u7528\u5FAE\u670D\u52A1\u540D\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
spring.application.name = zte-iccp-itech-demoapi

dt.mockInfo.groupId = demoapi-1
dt.mockInfo.isMockTest = true

# \u4F4E\u4EE3\u7801\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u914D\u7F6E\uFF0C\u4E0D\u7528\u6539
lcap.approval.sdk.app.appId = KENC(Rg2D7BkVU+D6IsQW0u+rv4dluFnAqg0cl7HtzewaQ2mucraL$$tnJHy34bGROYwlBs)
lcap.approval.sdk.app.appCode = lcap-pro

lcap.flow.update.enable = true

notice.center.base.url = https://icenterapi.zte.com.cn/zte-icenter-notice-message

#\u5355\u636E\u8F6C\u6362\uFF0C\u4E1A\u52A1\u81EA\u5DF1\u914D\u7F6E
lcap.app.map = {APP0984032412495249408:"1846873593768927234"}

# \u6D41\u7A0BSDK\u662F\u5426\u9700\u8981\u4F20\u9012tenantId\u8BF7\u6C42\u5934
approval.sdk.request.header.tenantId.source = true

# ai\u52A9\u624B\u89D2\u8272UPP\u914D\u7F6E\uFF0C\u89D2\u8272\u7F16\u7801\uFF08\u6240\u6709\u73AF\u5883\u90FD\u4E3A\uFF1Aai-bot-viewer\uFF09
chat.bot.config.roleCode = ai-bot-viewer
# ai\u52A9\u624B\u89D2\u8272UPP\u914D\u7F6E\uFF0C\u4EA7\u54C1ID\uFF08\u4EA7\u7EBF\u73AF\u5883\u4E3A\uFF1A270023\uFF09
chat.bot.config.productId = 270023
# ai\u52A9\u624B\u89D2\u8272UPP\u914D\u7F6E\uFF0C\u6A21\u5757ID\uFF08\u4EA7\u7EBF\u73AF\u5883\u4E3A\uFF1A300050\uFF09
chat.bot.config.moduleId = 300050
# bff \u7684\u670D\u52A1\u540D\uFF0C\u4E0D\u914D\u7F6E\u5C31\u9ED8\u8BA4 zte-paas-lcap-demobff\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
bff.name = zte-iccp-itech-demobff
# true\u8868\u793A\u8868\u5355\u4E0Ahidden\u7684\u7EC4\u4EF6\u7684\u6570\u636E\u4E0D\u8865\u5168\u3001\u5728\u4F7F\u7528\u65F6\u61D2\u52A0\u8F7D\u7684\u914D\u7F6E\uFF0C\u4F18\u5316\u52A0\u8F7D\u6027\u80FD\uFF0C\u5EFA\u8BAE\u8BBE\u7F6E\u6210true\uFF0C\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u81EA\u5DF1\u7684app id
plugin.APP0984032412495249408.lazyLoadComplexObjSwitch = true

#\u81EA\u5B9A\u4E49\u7EC4\u4EF6\u5E93\u7684\u5B58\u50A8\u914D\u7F6E\uFF08\u53EF\u9009Nas\u3001S3\uFF0C\u76EE\u524D\u53EA\u80FD\u662FS3\uFF0C\u9700\u8981\u5E94\u7528\u4FA7\u5355\u72EC\u7533\u8BF7\u8D44\u6E90\uFF0C\u7533\u8BF7\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/ab1e47ae8c2a43bab077e1ae8b0391d4/view\uFF09
lcap.component.strategy = S3

#\u8BBF\u95EES3\u57DF\u540D\uFF0C\u56FA\u5B9A\u5982\u4E0B
hcp.endpoint = ida.hcp.zte.com.cn
#S3\u5206\u914D\u7684\u6876\u4FE1\u606F\uFF0C\u7531\u4E1A\u52A1\u7533\u8BF7
hcp.bucket.non-anonymous = lcap
hcp.bucket = lcap

# \u503C\u5B88\u6253\u5361\u8F6C\u4EA4\u63A5\u53E3\u5E73\u66FF\uFF0C\u82E5\u4E3Atrue\u4F7F\u7528uac\u65B0\u63A5\u53E3\uFF0C\u5426\u5219\u4F7F\u7528uac\u8001\u63A5\u53E3\u67E5\u8BE2\u8F6C\u4EA4\u4EBA\u5458
plugin.public.clockInTask.queryTransfer.switch=true


# \u4E34\u65F6\u914D\u7F6E
# \u5173\u95ED\u540E\u53F0\u5B57\u6BB5\u5FC5\u586B\u6821\u9A8C
plugin.APP0984032412495249408.skipValidateSwitch = true

#*************** \u4E1A\u52A1\u914D\u7F6E ******************

# \u4EA7\u54C1\u5206\u7C7B\u6811
#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51
plugin.public.ccn.prodIdPath = 4882300824853184532/
#\u627F\u8F7D\u7F51
plugin.public.bn.prodIdPath = 4882300824853184517/
#\u56FA\u7F51\u53CA\u591A\u5A92\u4F53
plugin.public.fm.prodIdPath = 4882300824853184533/
#\u89C6\u9891-\u591A\u5A92\u4F53\u89C6\u8BAF\u7CFB\u7EDF
plugin.public.mmvs.prodIdPath = 4882300824853184533/4882300824853184636/
#RAN
plugin.public.ran.prodIdPath = 4882300824853184531/
#\u901A\u4FE1\u80FD\u6E90
plugin.public.te.prodIdPath = 4882300824853184519/
#\u91D1\u7BC6\u4FE1\u79D1
plugin.public.goldendbtech.prodIdPath = 4882300824853184543/
#\u89C6\u9891
plugin.public.vd.prodIdPath = 4882300824853184528/
#\u6570\u636E\u4E2D\u5FC3
plugin.public.dc.prodIdPath = 4882300824853184536/

#\u4EA7\u54C1\u7ECF\u8425\u56E2\u961F\u8D1F\u8D23\u4EA7\u54C1\u7EBF\u8303\u56F4
#\u65E0\u7EBF: 4G, 5G
plugin.public.prodTeam.prodLineIds.ran = 4882300824853184531/4882300824853184625/,4882300824853184531/4882300824853184619/
#\u5FAE\u6CE2: RAN/\u5FAE\u6CE2
plugin.public.prodTeam.prodLineIds.mw = 4882300824853184531/4882300824853184627/
#\u4E2D\u5174\u7696\u901A/\u4E2D\u5174\u7696\u901A
plugin.public.prodTeam.prodLineIds.wantong = 4882300824853184541/4882300824853184682/
#\u4E2D\u5174\u91D1\u6613/\u4E2D\u5174\u91D1\u6613
plugin.public.prodTeam.prodLineIds.jinyi = 4882300824853184545/4882300824853184678/
#IDS: \u4EA7\u4E1A\u6570\u5B57\u5316\u65B9\u6848/\u4EA7\u4E1A\u6570\u5B57\u5316\u65B9\u6848
plugin.public.prodTeam.prodLineIds.ids = 4882300824853184514/4882300824853184646/
#\u56FA\u7F51\uFF1A\u6570\u5B57\u5BB6\u5EAD, \u56FA\u7F51\u7EC8\u7AEF, \u5149\u63A5\u5165\u4EA7\u54C1
plugin.public.prodTeam.prodLineIds.fm = 4882300824853184533/4882300824853184642/,4882300824853184533/4882300824853184633/,4882300824853184533/4882300824853184630/
#\u627F\u8F7D\u7F51\uFF1A\u5149\u4F20\u9001\u4EA7\u54C1, IP\u7F51\u7EDC\u4EA7\u54C1, \u7EFC\u5408\u4F20\u9001\u4EA7\u54C1
plugin.public.prodTeam.prodLineIds.bn = 4882300824853184517/4882300824853184635/,4882300824853184517/4882300824853184637/,4882300824853184517/4882300824853184629/
#\u6570\u636E\u4E2D\u5FC3/\u6570\u636E\u4E2D\u5FC3
plugin.public.prodTeam.prodLineIds.dc = 4882300824853184536/4882300824853184648/
#\u6570\u636E\u667A\u80FD\u53CA\u670D\u52A1/\u6570\u636E\u667A\u80FD
plugin.public.prodTeam.prodLineIds.sdi = 4882300824853184530/4882300824853184645/
#CCN\uFF1A\u6838\u5FC3\u7F51, \u8BED\u97F3\u53CA\u6D88\u606F, \u670D\u52A1\u5668\u53CA\u5B58\u50A8, \u4E91\u53CA\u667A\u7B97
plugin.public.prodTeam.prodLineIds.ccn = 4882300824853184532/4882300824853184624/,4882300824853184532/4882300824853184626/,4882300824853184532/4882300824853184643/,4882300824853184532/4882300824853184644/
#TE\uFF1A\u901A\u4FE1\u7535\u6E90, \u901A\u4FE1\u50A8\u80FD
plugin.public.prodTeam.prodLineIds.te = 4882300824853184519/4882300824853184652/,4882300824853184519/4882300824853184628/
#\u80FD\u6E90\u9884\u7814/\u5E73\u53F0
plugin.public.prodTeam.prodLineIds.energy = 4882300824853184606/4882300824853184764/
#VD\uFF1A\u591A\u5A92\u4F53\u89C6\u8BAF\u7CFB\u7EDF, \u4E91\u89C6\u9891
plugin.public.prodTeam.prodLineIds.vd = 4882300824853184528/5620930280523632649/,4882300824853184528/4882300824853184651/
#\u91D1\u7BC6\u4FE1\u79D1/GoldenDB
plugin.public.prodTeam.prodLineIds.gdb = 4882300824853184543/4882300824853184681/

# \u7F51\u7EDC\u53D8\u66F4\u5355\u5BA1\u6279
plugin.public.approval.ccnAreaTd.prodIdPath = 4882300824853184517/4882300824853184637/4089667296003358752/2279220247717695566/,4882300824853184517/4882300824853184637/4089667296003358752/2279220247717695564/
plugin.public.approval.repOfficeTd.inter.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849
plugin.public.approval.repOfficeTd.inter.countryCode = 0219
plugin.public.approval.repOfficeTd.inter.prodIdPath = 4882300824853184531/
plugin.public.approval.tdNetOffice.inner.negative.prodIdPath = 4882300824853184533/
plugin.public.approval.tdNetOffice.inter.negative.prodIdPath = 4882300824853184531/
plugin.public.approval.tdNetOffice.inter.negative.countryCode = 0219
plugin.public.approval.tdNetOffice.inter.negative.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849
plugin.public.approval.remoteCenterOwner.prodIdPath = 4882300824853184531/,4882300824853184532/
plugin.public.approval.testDeptApproval.prodIdPath = 4882300824853184532/4882300824853184624/,4882300824853184532/4882300824853184643/,4882300824853184532/4882300824853184644/

plugin.public.grantFile.downloadPageUrl = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=zh&type=app&bizObject=null&sa=%s&entityType=%s&bizId=%s#/app/APP0984032412495249408/page/download_grant_file

# CCES
plugin.public.cces.host = https://cces.zte.com.cn:9119/
plugin.public.cces.callings.url = api/aicall/callings
plugin.public.cces.callresult.url = api/aicall/callresult
plugin.public.cces.clientId = KENC(NFvLaqAdRCT+n7X30QtP0qqrTAdONop39XegHPnus0I8F0GL+u9vPleHojSqxPo+/pFmiw==$$Z5i6ckDIcIMNgzAR)

# CRM
plugin.public.crm.host = https://ssc.zte.com.cn:8443/
plugin.public.crm.service.name = zte-crm-account-info
plugin.public.crm.service.url.queryAccount = /selectAccountPageByKeyWords/v2
plugin.public.crm.service.url.querySimpleInfo = /api/customer/simple/info
plugin.public.crm.service.url.getCustomerDetails = /noPermisonAccount
plugin.public.crm.service.url.queryCustomerInfoList = /ZXISS_SS300/ACCOUNT_INFO/zte-crm-account-info/api/customer/batchDetail/v2

# HRM
plugin.public.hr.service.name.person = zte-hrm-usercenter-pginfo
plugin.public.hr.service.name.organization = zte-hrm-usercenter-orginfo
plugin.public.hr.service.url.conditionQueryOrganizationInfo = /ihol/usercenter/orginfo/usercenter/queryOrgInfo
plugin.public.hr.service.url.queryPersonGeneralInfo = /ihol/usercenter/pginfo/usercenter/plain/getPersonGeneralInfo
plugin.public.hr.service.url.queryOrganizationInfo = /ihol/usercenter/orginfo/usercenter/getOrgInfo

# HRM-enable
plugin.public.hr.enable.service.url.queryCertificate = /enable-bff/srv/engineer/certificate/query

# NIS
plugin.public.nis.host = http://***********/
plugin.public.nis.service.name = /zte-iccp-iepms-nis
plugin.public.gtdCenter.service.name = /zte-iccp-iepms-gtdcenter
plugin.public.nis.service.url.queryProductionTree = /api/productclassifications/tree
plugin.public.nis.service.url.queryOrganizationTree = /api/organizations/tree
plugin.public.nis.service.url.queryNetworks = /api/networks/queries

plugin.public.nis.service.url.queryProductModel = /api/productmodels/queries
plugin.public.nis.service.url.queryCustomerNe = /api/networks/networkelements/queries
plugin.public.nis.service.url.queryProdModels = /api/networks/%s/products/queries
plugin.public.nis.service.url.queryCustomerFlag = /api/customer/queryCustomerFlag
plugin.public.nis.service.url.queryProductInfo = /api/productclassifications/selectbyidpath
plugin.public.nis.service.url.queryProductClass = /api/productclassifications/tree/service/object
plugin.public.nis.service.url.queryLogicalNe = /api/cnconfig/logicalne/queries

# AI\u5BF9\u63A5
plugin.public.icrm.ai.host = https://boassistant.dt.zte.com.cn/
plugin.public.icrm.ai.service.name = zte-doc-ai-assistant
plugin.public.icrm.ai.service.url.templateTransmission = /templateTransmission
plugin.public.icrm.ai.service.url.templateTransmission.async = /templateTransmission/async
plugin.public.icrm.ai.service.url.generate = /itech/doc/generate
plugin.public.icrm.ai.service.url.generate.progress = /itech/doc/process

# iVersion
plugin.public.iVersion.host = https://iversion.zte.com.cn/
plugin.public.iVersion.service.name = zte-plm-iversion-api
plugin.public.iVersion.url.getVersionInfoAndCorrelationInfo = https://iversion.zte.com.cn/zte-plm-iversion-api/publish/versionOutgo/getReleaseVersionInfo
plugin.public.iVersion.url.getBasicAndIncrementVerByCondition = https://iversion.zte.com.cn/zte-plm-iversion-api/publish/getBasicAndIncrementVerByCondition

# \u6587\u6863\u4E91
plugin.public.clouddisk.url.sendPartData = https://idrive.zte.com.cn/zte-km-cloududm-clouddisk/file/sendPartData
plugin.public.clouddisk.url.getDownloadToken = https://idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadToken
plugin.public.clouddisk.url.downloadByToken = https://idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/{appCode}/{token}
plugin.public.clouddisk.url.queryFileStatus = https://idrive.zte.com.cn/zte-km-cloududm-clouddisk/file/queryFileStatus
plugin.public.clouddisk.url.finishPartObject = https://idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/finishPartObject
plugin.public.clouddisk.url.queryFileInfo = https://idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/queryFileInfo

# PDM

plugin.public.pdm.url.queryProductInfoByLevel = https://pdmweb.zte.com.cn/zte-plm-iproduct-category/category/v1/query
plugin.public.pdm.url.queryProductInfoByLinkage = https://pdmweb.zte.com.cn/zte-plm-iproduct-category/relation/v1/query

# ZXRDC
plugin.public.zxrdc.upgradeForm.query.url = https://zxrdc.zte.com.cn/api/srv/MOS/Operate/QueryPumAppInfoList
plugin.public.zxrdc.remoteApplyForm.query.url = https://zxrdc.zte.com.cn/api/srv/GDPR/RmcApp/QueryAppDataInfo
plugin.public.zxrdc.clockInOption.query.url = https://zxrdc.zte.com.cn/api/srv/MOS/OcmINet/clockintasks/{id}/options
plugin.public.zxrdc.clockInTask.query.url = https://zxrdc.zte.com.cn/api/srv/MOS/OcmINet/queries
plugin.public.zxrdc.clockInTask.detail.query.url = https://zxrdc.zte.com.cn/api/srv/MOS/OcmINet/clockintasks/{id}
plugin.public.zxrdc.clockIn.submit.url = https://zxrdc.zte.com.cn/api/srv/MOS/OcmINet/clockintasks/{id}/clocks
plugin.public.zxrdc.clockIn.transfer.url = https://zxrdc.zte.com.cn/api/srv/MOS/OcmINet/clockintasks/{id}/owner/{userId}
plugin.public.zxrdc.clockIn.cancel.url = https://zxrdc.zte.com.cn/api/srv/MOS/OcmINet/clockintasks/{id}/clocks/latest

#CSC
plugin.public.csc.detail.url = https://cscesapi.zte.com.cn:8443/zte-crm-csc-requestservice/common/request/detail

# UCS
plugin.public.ucs.keyword.appId = 10015
plugin.public.ucs.keyword.tenantId = 10001
plugin.public.ucs.host = https://bmtucsapi.zte.com.cn:8443/
plugin.public.ucs.service.name = zte-bmt-ucs-api
plugin.public.ucs.url.queryAllUserInfo = /srv/v2/account/queryall

# EMDM
plugin.public.emdm.url.queryAreaInfo = https://emdm.zte.com.cn/esbmule/services/custom/query
plugin.public.emdm.body.sysCode = KENC(dHmQQmzMywWB3V3WQz5oIjhTJOVI0CGEsnSICA==$$IjsAep7V49L1wz5Y)
plugin.public.emdm.body.userCode = KENC(mLffc26bzj7c1HvTwZzXXBiK0io=$$mQ7xLPBUVM0jS+cj)
plugin.public.emdm.body.word = KENC(mLffc26bzj7c1HvTwZzXXBiK0io=$$mQ7xLPBUVM0jS+cj)

plugin.public.batchTask.view.pageUrl = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&pageStatus=VIEW&instanceDataId=%s&lang=zh&type=app&bizObject=batch_network_assignment#/app/APP0984032412495249408/page/%s
plugin.public.subconBatchTask.view.pageUrl = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&pageStatus=VIEW&instanceDataId=%s&lang=zh&type=app&bizObject=subcontractor_batch_task#/app/APP0984032412495249408/page/PAGE1014466079771930648

plugin.public.client.inet.empNo = JTn7u3WRQCD8fQJ6SmwP55OAxTTS+1INbvPwrbiSD+7I0Zo7

plugin.public.client.inet.device = 1064164951058583552
plugin.public.client.inet.timestamp = 1711680858

#---------------------------------------------------\u4EA7\u54C1\u5206\u7C7B\u3001\u4EE3\u8868\u5904\u7B49\u7CFB\u7EDF\u914D\u7F6E\u4FE1\u606F\uFF08\u540E\u53F0\u914D\u7F6E+\u524D\u53F0\u89C4\u5219\uFF09----------------------------------------------------------

#RAN/4G\uFF08\u914D\u7F6E\u9879key\uFF1A\u9700\u8981\u6838\u5FC3\u7F51\u5927\u533ATD\u5BA1\u6838\u7684\u4EA7\u54C1\u5206\u7C7BID\u8DEF\u5F84\uFF09
plugin.public.rule.data.key.approval.ccnAreaTd.prodIdPath = 4882300824853184531/4882300824853184625/

plugin.public.rule.data.key.cloudpc.prodIdPath = 6287645758770831362/

plugin.public.rule.data.key.zjt.prodIdPath = 4882300824853184678/

#\u83F2\u5F8B\u5BBE\u5DE5\u7A0B\u670D\u52A1\u5904\u3001\u7F05\u7538\u5DE5\u7A0B\u670D\u52A1\u5904\u3001\u5229\u6BD4\u4E9A\u5DE5\u7A0B\u670D\u52A1\u5904\u3001\u57C3\u53CA\u5DE5\u7A0B\u670D\u52A1\u5904\u3001\u610F\u5927\u5229\u5DE5\u7A0B\u670D\u52A1\u5904\uFF08\u914D\u7F6E\u9879key\uFF1A\u9700\u8981\u4EE3\u8868\u5904TD\u5BA1\u6838\u7684\u6D77\u5916\u7EC4\u7EC7\u7F16\u7801\u8DEF\u5F84\uFF09
plugin.public.rule.data.key.approval.repOfficeTd.inter.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849

#RAN\uFF08\u914D\u7F6E\u9879key\uFF1A\u9700\u8981\u4EE3\u8868\u5904TD\u5BA1\u6838\u7684\u6D77\u5916\u4EA7\u54C1ID\u8DEF\u5F84\uFF09
plugin.public.rule.data.key.approval.repOfficeTd.inter.prodIdPath = 4882300824853184531/

#\u901A\u4FE1\u80FD\u6E90\uFF08\u914D\u7F6E\u9879key\uFF1A\u56FD\u5185\u53EF\u4EE5\u7ED5\u8FC7\u3010\u6280\u672F\u4EA4\u4ED8\u90E8/\u7F51\u7EDC\u5904\u5BA1\u6838\u3011\u7684\u4EA7\u54C1\u5206\u7C7BID\u8DEF\u5F84\uFF09
plugin.public.rule.data.key.approval.tdNetOffice.inner.negative.prodIdPath = 4882300824853184519/

#RAN\uFF08\u914D\u7F6E\u9879key\uFF1A\u56FD\u9645\u53EF\u4EE5\u7ED5\u8FC7\u3010\u6280\u672F\u4EA4\u4ED8\u90E8/\u7F51\u7EDC\u5904\u5BA1\u6838\u3011\u7684\u4EA7\u54C1\u5206\u7C7BID\u8DEF\u5F84\uFF09
plugin.public.rule.data.key.approval.tdNetOffice.inter.negative.prodIdPath = 4882300824853184531/

#\u83F2\u5F8B\u5BBE\u5DE5\u7A0B\u670D\u52A1\u5904\u3001\u7F05\u7538\u5DE5\u7A0B\u670D\u52A1\u5904\u3001\u5229\u6BD4\u4E9A\u5DE5\u7A0B\u670D\u52A1\u5904\u3001\u57C3\u53CA\u5DE5\u7A0B\u670D\u52A1\u5904\u3001\u610F\u5927\u5229\u5DE5\u7A0B\u670D\u52A1\u5904\uFF08\u914D\u7F6E\u9879key\uFF1A\u56FD\u9645\u53EF\u4EE5\u7ED5\u8FC7\u3010\u6280\u672F\u4EA4\u4ED8\u90E8/\u7F51\u7EDC\u5904\u5BA1\u6838\u3011\u7684\u7EC4\u7EC7\u7F16\u7801\u8DEF\u5F84\uFF09
plugin.public.rule.data.key.approval.tdNetOffice.inter.negative.orgCodePath = **********/**********/**********/ORG2233669,**********/**********/**********/ORG2223870,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223780/ORG2227329,**********/**********/ORG2223778/ORG2223849

#RAN\uFF08\u914D\u7F6E\u9879key\uFF1A\u9700\u8FDC\u7A0B\u4E2D\u5FC3\u8D1F\u8D23\u4EBA\u5BA1\u6838\u7684\u4EA7\u54C1ID\u8DEF\u5F84\uFF09
plugin.public.rule.data.key.approval.remoteCenterOwner.prodIdPath = 4882300824853184531/

#RAN/4G\uFF08\u914D\u7F6E\u9879key\uFF1A\u9700\u6D4B\u8BD5\u90E8\u5BA1\u6838\u7684\u4EA7\u54C1ID\u8DEF\u5F84\uFF09
plugin.public.rule.data.key.approval.testDeptApproval.prodIdPath = 4882300824853184531/4882300824853184625/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51
plugin.public.rule.data.key.ccn.prodIdPath = 4882300824853184532/

#\u627F\u8F7D\u7F51
plugin.public.rule.data.key.bn.prodIdPath = 4882300824853184517/

#\u56FA\u7F51\u53CA\u591A\u5A92\u4F53
plugin.public.rule.data.key.fm.prodIdPath = 4882300824853184533/

#RAN
plugin.public.rule.data.key.ran.prodIdPath = 4882300824853184531/
plugin.public.rule.data.key.ran.4g.prodIdPath = 4882300824853184531/4882300824853184625/
plugin.public.rule.data.key.ran.5g.prodIdPath = 4882300824853184531/4882300824853184619/
plugin.public.rule.data.key.ran.mw.prodIdPath = 4882300824853184531/4882300824853184627/

#\u901A\u4FE1\u80FD\u6E90
plugin.public.rule.data.key.te.prodIdPath = 4882300824853184519/

#\u91D1\u7BC6\u4FE1\u79D1
plugin.public.rule.data.key.goldendbtech.prodIdPath = 4882300824853184543/

#\u6570\u636E\u4E2D\u5FC3
plugin.public.rule.data.key.dc.prodIdPath = 4882300824853184536/

#\u89C6\u9891
plugin.public.rule.data.key.vd.prodIdPath = 4882300824853184528/

#\u89C6\u9891-\u591A\u5A92\u4F53\u89C6\u8BAF\u7CFB\u7EDF
plugin.public.rule.data.key.mmvs.prodIdPath = 4882300824853184533/4882300824853184636/

#\u56FD\u5185\u8425\u9500
plugin.public.rule.data.key.domestic.sales.orgCodePath = **********/ORG0002700

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8\uFF08\u77EDid\uFF09
plugin.public.rule.data.key.engineering.service.dept.short.orgCodePath = **********

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8
plugin.public.rule.data.key.engineering.service.dept.orgCodePath = **********/**********

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8/\u5DE5\u7A0B\u670D\u52A1\u4E00\u90E8
plugin.public.rule.data.key.engineering.service.dept.1.orgCodePath = **********/**********/**********

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8/\u5DE5\u7A0B\u670D\u52A1\u4E8C\u90E8
plugin.public.rule.data.key.engineering.service.dept.2.orgCodePath = **********/**********/ORG2223780

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8/\u5DE5\u7A0B\u670D\u52A1\u4E94\u90E8
plugin.public.rule.data.key.engineering.service.dept.5.orgCodePath = **********/**********/ORG2223778

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8/MTO\u5DE5\u7A0B\u670D\u52A1\u90E8
plugin.public.rule.data.key.engineering.service.dept.mto.orgCodePath = **********/**********/ORG2223785

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8-\u5DE5\u7A0B\u670D\u52A1\u4E09\u90E8\uFF08\u5DE5\u7A0B\u670D\u52A1\u56FD\u5185\u90E8\uFF09
plugin.public.rule.data.key.engineering.service.dept.domestic.orgCodePath = **********/**********/ORG2223781

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8-\u5DE5\u7A0B\u670D\u52A1\u4E09\u90E8\uFF08\u5DE5\u7A0B\u670D\u52A1\u56FD\u5185\u90E8\uFF09\uFF08\u77EDid\uFF09
plugin.public.rule.data.key.engineering.service.dept.domestic.short.orgCodePath = ORG2223781

#\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8-\u5DE5\u7A0B\u670D\u52A1\u4E09\u90E8\uFF08\u5DE5\u7A0B\u670D\u52A1\u56FD\u5185\u90E8\uFF09-\u7F51\u7EDC\u670D\u52A1\u90E8
plugin.public.rule.data.key.engineering.service.dept.domestic.network.orgCodePath = **********/**********/ORG2223781/ORG2223820

#\u4E4C\u5179\u522B\u514B\u5DE5\u670D\u5904
plugin.public.rule.data.key.uzbekistan.orgCodePath=**********/**********/**********/ORG2223862
# \u56FD\u9645\u8BD5\u70B9\u6253\u5361\u914D\u7F6E\u9879\u76EE
plugin.public.rule.data.key.internation.clockin.orgCodePath=**********/**********/**********/ORG2223862,**********/**********/ORG2223780/ORG2223821,**********/**********/ORG2223778/ORG2223843,**********/**********/ORG2223778/ORG2226502


#\u627F\u8F7D\u7F51-\u5149\u4F20\u9001\u4EA7\u54C1
plugin.public.rule.data.key.bn.optical.transmission.prodIdPath = 4882300824853184517/4882300824853184635/

#\u627F\u8F7D\u7F51/IP\u7F51\u7EDC\u4EA7\u54C1/\u6570\u901A\u4EA7\u54C1/\u9AD8\u7AEF\u8DEF\u7531\u5668\u53CABRAS
plugin.public.rule.data.key.bn.ip.data.bars.prodIdPath = 4882300824853184517/4882300824853184637/4089667296003358752/2279220247717695564/

#\u627F\u8F7D\u7F51/IP\u7F51\u7EDC\u4EA7\u54C1/\u6570\u901A\u4EA7\u54C1/\u9AD8\u7AEF\u4EA4\u6362\u673A
plugin.public.rule.data.key.bn.ip.data.switch.prodIdPath = 4882300824853184517/4882300824853184637/4089667296003358752/2279220247717695566/

#RAN/4G/FDD-LTE\u822A\u7EBF\u65E0\u7EBF\u7CFB\u7EDF
plugin.public.rule.data.key.ran.4g.fdd.lte.atg.prodIdPath = 4882300824853184531/4882300824853184625/4089667296003358858/

#RAN/4G/FDD\u65E0\u7EBF\u7CFB\u7EDF
plugin.public.rule.data.key.ran.4g.fdd.sys.prodIdPath = 4882300824853184531/4882300824853184625/4089667296003358861/

#RAN/4G/FDD-LTE\u65B0\u90AE\u901A
plugin.public.rule.data.key.ran.4g.fdd.lte.post.prodIdPath = 4882300824853184531/4882300824853184625/4089667296003358859/

#RAN/4G/TD-LTE\u65E0\u7EBF\u5B50\u7CFB\u7EDF
plugin.public.rule.data.key.ran.4g.fdd.td.lte.prodIdPath = 4882300824853184531/4882300824853184625/4089667296003358863/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u6838\u5FC3\u7F51
plugin.public.rule.data.key.ccn.ccn.prodIdPath = 4882300824853184532/4882300824853184624/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u670D\u52A1\u5668\u53CA\u5B58\u50A8
plugin.public.rule.data.key.ccn.ssp.prodIdPath = 4882300824853184532/4882300824853184643/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u4E91\u53CA\u667A\u7B97
plugin.public.rule.data.key.ccn.cloud.ai.prodIdPath = 4882300824853184532/4882300824853184644/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u96C6\u6210\u7B2C\u4E09\u65B9\u4E0E\u7EC4\u7F51\u8BBE\u5907
plugin.public.rule.data.key.ccn.integration.devices.prodIdPath = 4882300824853184532/4242938329977716738/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u96C6\u6210\u7B2C\u4E09\u65B9\u4E0E\u7EC4\u7F51\u8BBE\u5907/\u7B2C\u4E09\u65B9\u4E91\u5E73\u53F0
plugin.public.rule.data.key.ccn.integr.cloud.prodIdPath = 4882300824853184532/4242938329977716738/3360232902691480227/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u96C6\u6210\u7B2C\u4E09\u65B9\u4E0E\u7EC4\u7F51\u8BBE\u5907/\u5176\u4ED6\u7B2C\u4E09\u65B9\u8BBE\u5907
plugin.public.rule.data.key.ccn.integr.device.prodIdPath = 4882300824853184532/4242938329977716738/3360232902691480228/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u96C6\u6210\u7B2C\u4E09\u65B9\u4E0E\u7EC4\u7F51\u8BBE\u5907/\u7B2C\u4E09\u65B9APP
plugin.public.rule.data.key.ccn.integr.app.prodIdPath = 4882300824853184532/4242938329977716738/3360232902691480229/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u96C6\u6210\u7B2C\u4E09\u65B9\u4E0E\u7EC4\u7F51\u8BBE\u5907/\u7B2C\u4E09\u65B9\u5B89\u5168\u8BBE\u5907
plugin.public.rule.data.key.ccn.integr.security.prodIdPath = 4882300824853184532/4242938329977716738/2378448108622971031/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u8BED\u97F3\u53CA\u6D88\u606F
plugin.public.rule.data.key.ccn.voice.message.prodIdPath = 4882300824853184532/4882300824853184626/

#\u7B97\u529B\u53CA\u6838\u5FC3\u7F51/\u6838\u5FC3\u7F51/IMS-\u65B0
plugin.public.rule.data.key.ccn.ccn.ims.data.prodIdPath = 4882300824853184532/4882300824853184624/3846597978400402678/

#\u627F\u8F7D\u7F51/IP\u7F51\u7EDC\u4EA7\u54C1/\u6570\u901A\u4EA7\u54C1
plugin.public.rule.data.key.bn.ip.data.prodIdPath = 4882300824853184517/4882300824853184637/4089667296003358752/

#\u6570\u636E\u667A\u80FD\u53CA\u670D\u52A1
plugin.public.rule.data.key.sdi.prodIdPath = 4882300824853184530/

#\u80FD\u6E90\u9884\u7814/\u5E73\u53F0
plugin.public.rule.data.key.energy.prodIdPath = 4882300824853184606/

#\u4E2D\u5174\u7696\u901A
plugin.public.rule.data.key.zxwt.prodIdPath = 4882300824853184541/

#\u4F4E\u4EE3\u7801-\u56FD\u5BB6/\u5730\u533A\u7EC4\u4EF6-\u4E2D\u56FD-\u7F16\u7801
plugin.public.rule.data.key.china.code = 0001

#\u7F51\u7EDC\u670D\u52A1\u5904/\u5DE5\u7A0B\u670D\u52A1\u56FD\u5185\u90E8/\u5DE5\u7A0B\u670D\u52A1\u7ECF\u8425\u90E8/\u4E2D\u5174\u901A\u8BAF\u80A1\u4EFD\u6709\u9650\u516C\u53F8
plugin.public.rule.data.key.engineering.service.office.orgCodePath = **********/**********/ORG2223781/ORG2223820

#\u83F2\u5F8B\u5BBE\u5DE5\u7A0B\u670D\u52A1\u5904
plugin.public.rule.data.key.philippines.orgCodePath = **********/**********/**********/ORG2233669

#\u7F05\u7538\u5DE5\u7A0B\u670D\u52A1\u5904
plugin.public.rule.data.key.myanmar.orgCodePath = **********/**********/**********/ORG2223870

#\u5229\u6BD4\u4E9A\u5DE5\u7A0B\u670D\u52A1\u5904
plugin.public.rule.data.key.libya.orgCodePath = **********/**********/ORG2223780/ORG2223821

#\u57C3\u53CA\u5DE5\u7A0B\u670D\u52A1\u5904
plugin.public.rule.data.key.egypt.orgCodePath = **********/**********/ORG2223780/ORG2227329

#\u610F\u5927\u5229\u5DE5\u7A0B\u670D\u52A1\u5904
plugin.public.rule.data.key.italy.orgCodePath = **********/**********/ORG2223778/ORG2223849

plugin.public.rule.data.key.bn.optical.transmission.mstp.prodIdPath = 4882300824853184517/4882300824853184629/4089667296003358751
plugin.public.rule.data.key.bn.optical.transmission.itn.mstp.prodIdPath = 4882300824853184517/4882300824853184629/3612290697593791175/2153302807731857068

plugin.public.email.host = https://icenterapi.zte.com.cn/
plugin.public.email.service.name = zte-icenter-notice-message
plugin.public.email.service.url.send = /notice/template/send

plugin.public.email.enable = true

#4A appcode

#4A secretKey

plugin.public.authTask.enabled = true
plugin.public.clockInTask.enabled = true

plugin.public.service.host = https://itech.zte.com.cn/
plugin.public.service.name = zte-iss-bobase-portalui
#\u6211\u7684\u5F85\u529E
plugin.public.service.myCharge.suffix.path = /ztem_965130

#\u6211\u7684\u5F85\u529E\u5BA1\u6279\u9875
plugin.public.assignment.approve.pageUrl = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/processDetail.html?tenantId=10001&flowId=%s&taskId=%s&taskType=APPROVAL#/app/%s/page/%s

#\u4EFB\u52A1\u5217\u8868 \u8BE6\u60C5\u8DF3\u8F6Curl
plugin.public.assignment.detail.pageUrl = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=app&instanceDataId=%s&pageStatus=VIEW&openType=newTab#/app/%s/page/%s

#\u5BF9\u63A5diop\u914D\u7F6E
plugin.public.idop.check.enable = false
plugin.public.idop.host = https://idop.zte.com.cn/back/
plugin.public.idop.create.page.address = https://idop.zte.com.cn/main/operation-center/oc/add
plugin.public.idop.username = itech_cloud

plugin.public.idop.create.tips.page = idop_create_tips_release
## idop\u670D\u52A1\u914D\u7F6E\u4EA7\u54C1\u7EBFcode
plugin.public.idop.product.line.code = 100000431337,100000100016,NIS001

plugin.public.rule.data.key.bn.optical.transmission.wdn.prodIdPath = 4882300824853184517/4882300824853184635/2837691407589391971/
plugin.public.rule.data.key.bn.optical.transmission.otn.prodIdPath = 4882300824853184517/4882300824853184635/4089667296003358753/

plugin.public.bn.mstp.itn.prodIds = 4882300824853184517/4882300824853184629/4089667296003358751/,4882300824853184517/4882300824853184629/3612290697593791175/

plugin.public.inone.appName = iEPMS-WEB
plugin.public.inone.supplierName = ZXICCP_iEPMS300
plugin.public.icos.versionEnv = ONLINE
plugin.public.icos.connectTimeOut = 50000
plugin.public.icos.serviceHost = https://icos.dt.zte.com.cn
plugin.public.icos.changeOrder.queryId.apiId = 1061408502700867584
plugin.public.icos.changeOrder.queryAll.apiId = 1061416302965456896
plugin.public.icos.clockInTask.queryId.apiId = 1062405814889775104
plugin.public.icos.clockInTask.queryAll.apiId = 1062421054356488192

plugin.public.icos.netchange.report.domestic.day.apiId = 1109922808777965568
plugin.public.icos.netchange.report.org.day.apiId = 1111979640736481280
plugin.public.icos.netchange.report.org.week.apiId = 1111980225036582912
plugin.public.icos.netchange.report.internal.org.day.detail.apiId = 1111978834784190464
plugin.public.icos.netchange.report.internal.org.threeday.detail.apiId = 1112012243568984064
plugin.public.icos.netchange.report.internal.org.apiId = 1112885093158912000
plugin.public.icos.netchange.report.domestic.clock.prod.detail.apiId = 1123302797560807424
plugin.public.icos.netchange.report.domestic.clock.prod.total.apiId = 1122970045170876416
plugin.public.icos.netchange.report.domestic.clock.org.total.apiId = 1125484468297039872
plugin.public.icos.netchange.report.internal.yesterday.summary.apiId = 1122971059059982336
plugin.public.icos.netchange.report.internal.yesterday.fail.detail.apiId = 1122973932737036288
plugin.public.icos.netchange.report.internal.today.detail.apiId = 1122973188487151616
plugin.public.icos.netchange.report.internal.tomorrow.detail.apiId = 1122972793119473664
plugin.public.icos.netchange.report.global.today.detail.apiId = 1124425437683941376
plugin.public.icos.netchange.report.global.three.day.detail.apiId = 1124819680302759936
plugin.public.icos.netchange.report.global.support.detail.apiId = 1125033154991652864

plugin.public.inter.summary.push.cron = 0 0 12 * * ?
plugin.public.global.prod.line.push.cron = 0 0 7,16 * * ?
plugin.public.clock.in.org.push.cron = 0 0 8 * * ?
plugin.public.global.support.push.cron = 0 0 16 * * ?

plugin.public.fm.cpe.homedict.cpe.cloud.id.path = 4882300824853184533/4882300824853184633/4738190609975747399/3846597978400402689/
plugin.public.fm.mmvs.id.path = 4882300824853184533/4882300824853184636/

plugin.public.inter.summary.push.enable = true
plugin.public.global.prod.line.push.enable = true

plugin.public.clockin.query.url = https://itech.zte.com.cn/zte-iss-bobase-portalui/ztem_198803198374479084
plugin.public.netchange.query.url = https://itech.zte.com.cn/zte-iss-bobase-portalui/ztem_198803198374479068

plugin.public.prodTeam.prodLineIds.tps = 4882300824853184519/4882300824853184628/
plugin.public.clockin.reviews.rectify.flow.handlers = 10031768,10082196
plugin.public.assignment.firstOrderCreateTime = 2024-06-01
plugin.public.assignment.maxSubLength = 10

msa.redis.database = 8
plugin.public.assignment.href.iframeUrl = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=%s&type=app&instanceDataId=%s&pageStatus=%s&openType=newTab#/app/%s/page/%s
plugin.public.assignment.prefixUrl = https://itech.zte.com.cn/zte-iss-bobase-portalui/ztea_************/page/link?code=%s&name=%s&url=%s&isTempTab=true
plugin.public.assignment.threadTotal = 5

# \u5E94\u7528\u5206\u53D1\u64CD\u4F5C\u767D\u540D\u5355\uFF0C\u591A\u4E2A\u5DE5\u53F7\u4F7F\u7528\u82F1\u6587\u9017\u53F7\u5206\u9694(\u975E\u5FC5\u586B\uFF0C\u4E0D\u6D89\u53CA\u76F8\u5173\u529F\u80FD\u53EF\u4E0D\u6DFB\u52A0)\uFF0C\u5E94\u7528\u5206\u53D1\u529F\u80FD\u4ECB\u7ECD:https://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/a33077fb62aa490ea726f3dd95c7cabd/view
lcap.app.distribute.emp.whiteList = 0668000911,0668000895

# \u5065\u5EB7\u68C0\u67E5info\u63A5\u53E3
msa.expose.info.enable = true

#msa\u52A0\u5BC6
msa.encrypt.datakey = F9YSNbTtJFNjFm/ew/vR3jCwVqkA7MkjWo7rFmUZthEP0wlKqWvpQfwBjZ2eU5tch07/74qRaRipchMRY7AXIg==
msa.rootkey.factor1 = zte-iccp-itech-demoapi-prod
msa.ccs.encrypt.factor = ENC(ogMXPh5GJuyUt3ZkbrRBBlpTq0jGsiZ0pgupMrSA4YNs3wm5noISBlYA1ARm7Eo8sjcIpbaeXXM=)

msa.security.server.dt-kms.enable = true
#kms\u76F8\u5173\u52A0\u5BC6\u53C2\u6570\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms.dt.zte.com.cn:8443
msa.security.server.dt-kms.appName = ENC(rxeZjVBbdN+0xUaKBVi6aj1eRp43d3jfj9ddyZKSnTvtjL9RUfARRho=)
msa.security.server.dt-kms.userId = ENC(BRk55iKreQbO7NryShlNwfLGGJJ1IbHm8A6fiDvjer2d59NU/gQc98MeRVE=)
msa.security.server.dt-kms.appSecret = ENC(Fa8lKTGrs68qwH2js7AdiEiU6z+/tVKem7YVRXcGGzGy1aBLxkpLnK2H9hKKNW94GnaOMimlZ4LYHcyghcbW3fw6HxlZggzqmqE=)
msa.security.server.dt-kms.defaultMasterKey = itechcloud

#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:demoapi:demoapi:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = ZTgzNGIyMDNjYmU1N2IzNjQyM2MwOTYwNWE0NzUwNGQ2ZWMyMGNkNWRlZWRiNTVmYjFlZjhlNGU4NzlkNTAyZjEyMzcyN2JlNTQ2OGIyMzNhMTliNjRiMDM3OWI0YTY3$$MTJlZTVlNDVjYWEzOGExMjExMDM3YTg5MmNmMjJhYmU=&&OGZjNDI0ZTAtMjcwNy00YzkxLWJkNjgtODFkYWRiZTUxZTYy

msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.cipher = N2RhNGNjYTFmYTljNDkzMDczMjhlMjc3ZDk1ZDcyMGYzNmFmZmZiODk0YWZhZDIzZjgwNmEyYTk5MDljMDIyODliNDI3NjBmMmI1MzFlYzljMDhlNzFjYjcxMTM2OTFj$$NGQwOWY0NTYzYWYwNjAyNDY3ZGRmMWIyYzcwYmNkMmI=&&OGZjNDI0ZTAtMjcwNy00YzkxLWJkNjgtODFkYWRiZTUxZTYy
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.sensitiveData = ZTllYTM2NTUzMjA2YTFlMWIzNmE4NjVjZjQzZGEzYTM2NjdiMThiYWUwNDE0NjM3Mzc0NjIwNWM3OTkzZjQwYTczNTkzMTJjYWUxNTBlMTc1MjAwNTgzY2I5MjU4NDgz$$MzYwMTFjMDFiOTBjYmVkZmNhZWYwN2ZiZTY5OWI3Y2I=&&OGZjNDI0ZTAtMjcwNy00YzkxLWJkNjgtODFkYWRiZTUxZTYy

# \u4EE5\u4E0B\u53C2\u6570\u9700\u91CD\u65B0\u7528kms\u52A0\u5BC6
jdbc1.username = KENC(WQGa/vGP950ngkXnbgeab6518IgP9eqX$$CsQy9+8YI2tA9gqi)

jdbc1.password = KENC(VFmqWbWJGmOrRniIym4WkOjUnCXji7AQ058=$$OqUcWP0T6kHSd4xl)

lcap.approval.sdk.app.secretKey = KENC(2JysCgk9Ij+15uc8zM4UgNWaKrYHXwWRdZO1tbkly3g4Qj/uQlJ6Ytnh+yIls2YLI0nEWjMDcoOeHmm0fZTPxUHMIh+osK6epfiCYchd5Qg=$$uQR79lki5t2JmA/N)

approval.sdk.app.secretKey = KENC(iUkOKClJzYMXadSVxd2lSCatj5OjuUMEu4svto7ufQhdgbb2UF2ucqZ7NbZ5u2deMhAqzqWo74UbJqqOJ3Y8FQ194ed6E1rkWRaRq+Degqw=$$s3UVxXAO5LlVBphY)

upp.auth.encryptor = KENC(PNHDM2mhxfkHV79DiXiZFiTdKv9yzDz1t0cHSyqAiD0Vaoa+HL0iDCW+xu1HFZLQSU8i6xLld8E6mU9nC2w13g4DJXpfParBzEqlf3aPOLjjiHqAlvkbCHLIPgeCfiaTUxEa2BlmeWY9Csx/9CA5f7QsYz3soNcaUhuTHhVBud7r4DI6FQASF/fuRqMsGkfx33uvHvyxKI1KkXklCuf7g0XzsSZTewQQrdpwDEiB61Zbay/L+RbE5LfLPkO1DxCn3jjbgPKqAi8llBc1UIaJb4qPZIR7A05FslN8xv8h3ips7ccNSidOlg==$$voJ5LgNPfnDYJ+Rr)

upp.sha256.salt = KENC(R1AdY1yo3NZjKIzX3tTfAwphjLUxu8I12l40jYdyFx6OwZ5ftUkPSvl9GrbFkY+yf7/mThAPnzYLsmyXSrIm4FG7YQKL5iyEPL/tnJWHau1sv19oxueO23hKmIKmgEc+eua6oFzPYTOmZhM5QjWJzFlrc9YPQQiXY1EqMl9qFIooNvsgeRFuyxTzMqXgiF0F$$86PJTnLixmtKUww2)

upp.auth.productSecretKey = KENC(6E89ayu3OYEf1ith5tiKxdutn7h7ZXa/AYwTEQExbjg6HiO6h9wSoRWrPBraM9SZ$$yI+doEVxsYJlyhPE)

spring.redis.sentinel.password = KENC(w9DFbtggPfxTlDQDyaq+axcWLL2ohTtz2kEiUFDMAChR1tDWKFo8kSbA7HQZRGFMY+e7DR3d4fqmcasPwxyp+qjOI/4M8IGHkrBDR60kw6U=$$hAoWyqXws9AERSkm)

spring.redis.password = KENC(uZHXo8pFzFQTYF2IV3LjOlzM5jMYlOlwj73M2sQZUXfXZEPbC1F/kzZ7qASyMw==$$hF2bumMfV56QPx+P)

ucs.accessKey = KENC(Nkfn7lgYXDdIWvZ+Lhm4eLfpmIx4VCVZLoEkhQi0tpuMoaOBjB/vWkuJumJwdsRH9YECZw==$$zDCqJn+XVjxA0cdk)
ucs.secretKey = KENC(GBuKSCpw1KaKXnsvwqNzCeKu1U3tFRE/9ssxQmnkuGKFiVWEBtVQ41zGw5eMAqg+/68CQQ==$$mSIn30XpzvzCGxpT)

cloudDiskSDK.xSecretKey = KENC(627MigXfQ7HsV7bwcL6jJTcTPfyhVaAH1xMflN9zTSBNe+fyfRxACq4MUWryVWWLO5Nz2w==$$jnFH8kIGdrVbJRyk)

cloudDiskSDK.xOrgId = KENC(Bc6OArtFzwLejncfWqGR0Af6nPwbTsldLOZB/O1/UfiZjWgrqEenwXT1bVrY/rj3vqBNjQ==$$5RuzabgbPRJH1mWw)

#\u7F16\u7801\u7EC4\u4EF6\u5BF9\u63A5\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF0C\u7533\u8BF7\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/#/shared/0c9c59bb56f1417783fe24ec773484b3/wiki/page/ffc1076125554581acba262b511e981d/view\uFF09
apicaller.header.appKey = KENC(wGNwZiz2w8cc+mMxxdw9dJQvPz67Sq/rIa0bm2lzL2csZWXS$$BSEyEJmb64vhlXPQ)
apicaller.header.secretKey = KENC(Q92SnKJ2T0VCOnTSnP+CskEXM6DiIm4em/vaANkNPe2bPygOSCk1vQW6G6QBmzoDPqwdjQk5pIzDZF5hIX94DeSeZ5nf6WjsDq9RCZinei4=$$3vnaNzcYdH////WN)

inone.appCode = KENC(nVbYXFpjAPBCdpTD91/E7YT99f7CzwisHubUf56TM506wZ/GY6OAIO9tqt1e65Hk$$XCgsr0zpFhhnowy5)

#\u5FC5\u586B\uFF0C\u7533\u8BF7\u83B7\u5F97\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.clientSecret = KENC(LBffH2LXds7niTJierFc/So3zAR9EUGWKt7PCanebpGO67CG12mtgj+bp17/fgQC252SaQMEhpM6Qa+GQSy4zwOg/JbwtHGxiqgH0Z3CvRU=$$4pMuE6GYJ8zIN52A)
uac.sdk.accessKey = KENC(fFv0uB4xtd9hwtHyaxj58Q9eVUIEiSgAcyjHZuiQKW6rplSWxxtYsT+bb6v33Esf$$3yDnqf6SSVotLafK)
uac.sdk.accessSecret = KENC(BGMa2wccy4o80xpvYbO1m5nRA6qC0qpKfjCs81LOgV/ohk5He3NDLWEv2kON76J5syhnIMUmGxgCX+Xb6vDUialMMx9cjgf19rEpyIRDNS0=$$2MM7zF8CrFXGke/D)

#S3\u5BC6\u94A5\uFF0C\u4F4E\u4EE3\u7801\u63D0\u4F9B.\u9700\u8981\u4F7F\u7528\u5DE5\u5177\u7C7B\u91CD\u65B0\u89E3\u5BC6\uFF0C\u518D\u6839\u636E\u670D\u52A1\u540D\u91CD\u65B0\u52A0\u5BC6\uFF08\u9700\u8981\u5E94\u7528\u4FA7\u6839\u636E\u5DE5\u5177\u7C7B\u91CD\u65B0\u751F\u6210\u914D\u7F6E\uFF09
hcp.accessKey = KENC(Os0oyzedAFA+den255TYCFwM1ug=$$fA57m+5JB5gyzKh/)
hcp.secretKey = KENC(d1GrIMFklODAW7RwkMgSBWO05JbcoM+AbkPzVMtxtVrEqnfdhTaa6QXQ7Ag/QMlo$$Zz67rP7uQMqbxsNM)

#demoapi\u548Cpromgrapi\u539F\u503C\u9700\u4FDD\u6301\u4E00\u81F4
datasource.config.gcm.encrypt.key = KENC(M1Jj6UMhBzUyv9C/8gn+dsFcpNJkveBrfUmUBGpura6GT/JcZcTTpDo3ostlKa9F$$G1SenGj80LhS0xDa)
common.app.param.secret = KENC(1sjvQR7K9Qkh4VqzcYk3Ua4hVHjDbrSZRhyjv/io94pvKA0/pIJALrKxaA3KeZAU$$OAo0YaXdciZ751br)

notice.key = cdN7Kk7JbyQ/Fw4Evba1vo15jZdQGRJljgZqZlWwZ/iiEVh/GRB82Z49+xo=

uac.key = KENC(Z8Fw8fCDwgBo9rq0t39/SjjwNVJKBYHwhQ9tvK2jB1M=$$4Yaj1/4HPPoNeC8D)

approval.sdk.app.rk = KENC(gmROnBij85w9blDxDHjUR7UDGPVGMFdrf90Hbhq6wyUQ1aXcIxZN76SC566u59ct$$32L0Zs8R8jzwxeIu)

esConfig.encrypt.key = KENC(1N6PV1vZHvyr51JhnBP2j77LRLT7OWYTIr/HZv2LC8ZRZTCfhyXICF492RUGTmTtEzRvlvs1zjJjTmyemVAY4CBB1oQgWAnVZVPSNrf+0AoKNE6eRaVNpU8sZgSbHKIjtP1ce7e6ZY0=$$QKDOTb4K4NBgsKlz)

uac.sdk.secretKey = KENC(iURchF4V6+OFbjeTt3afGcWF1PioB4LAd+5O/s1eSuBI/3V/HUNyXrQ2V4oEfjuem/iUaRXvRdn7af8g44KRJGAD+mU8huX5Y1CbRQ==$$dEprDbWbsZus9nrA)

emdm.password = KENC(FKlFky6YPPOccxXHwOlH2yPiHJBjCZAc9fl/X6rpD4JtujoWjq7iQB5vIdXDvvJlhUhTWRsw7cFCs7JYx5IKtASD$$ve2Y/BomzjQTKv5W)

datasource.config.rsa.public-key = KENC(R5MW3IkTmeUUtuClQxIHUdRnLzxsdl+/AXetJslTS9qSzdC1uudGfSdiMEJhJfza+gHMLtgLPK4mXT6AEWVlYirhUzbZz6yqYBJU9JVEj0N636KgnykrzSsgrSrER7Ly9zgCWAQDxSwqVAkYqo/bfldK8eNW1lNnMZMkW5bCHGR0iCHoB0S2GsZryg06PXlyDyOOH23elPav3iWQEnb9sD+7/+6oac0qUoc58k2dVSjiC501wODuFDxXtRPLInBC+v4G8U0OEDWa/R4U7MWFzHnbRBfc+rohu1lz7hXbNfuUEySUShO3Hg==$$LM8/l5m67LynDq/8)

#plugin.public\u52A0\u5BC6
#SysAuthAccessKey
plugin.public.sysAuth.accessKey[iNet] = KENC(/zOyVK4CnmsxPIH9pfyGGdX/oBsAf3oUapliqBvhLTDBgPhWC5BMtEUYYuYsIgza32HwPg==$$fSNhr7gr9TUoxNXI)
plugin.public.sysAuth.accessKey[iDOP] = KENC(EPq6aF9OTntevN9uOqFf0Bi6HxdT+1g2YoqSwWAXUr1xmhCQzpuhBQHSv7JkYqURsJHNag==$$UaldWhafXK43apG+)
plugin.public.sysAuth.accessKey[iCOS] = KENC(qnQSNbDrprT04ujn1SPJtr0L/WhMITM0hjccqdm5iS6pI18N1ELihFkkVYiEtKouLuaoODSM0FBVmqgijBycXlkV6p5C0NLzQOyiW9Nh/h0=$$jZs+vj1q7kx5eTkm)
plugin.public.sysAuth.accessKey[CNOP] = KENC(haoMQepbMuCe8ChoUlNclMPQFSNgBQodMe5GlA1NsM2zrmFwpFIWKgn3bYtUx9suVxuUew==$$shjK+LqbdyFe0b40)

# \u6388\u6743\u6587\u4EF6\u76F8\u5173\u5BC6\u94A5
plugin.public.grantFile.secretKey.rsaPriKey = KENC(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$$P5iWlqBrFdxRdEkd)

plugin.public.grantFile.secretKey.rsaPubKey = KENC(IAwaqI8d1om3PqfP/46GeARwSZ8ruvoCxJlPDkgzXu2RCz5QPnObdk0IDJqO890YEh7ksAbrtChgddFfQA+j0WpvjSw8qr/x2F9OWruOdsbaLacNdWOcGaJhgGwxn+2JrqEU+GqDbpQwF3uk3aBLBSIGg+bP9/J91Xb/lsorrnCyrWJ2PYkAcwOoQ+JhfDAQt35AmVPUPlT4aG9A2zeiOsxiob8GPmqSzha68dS77TdEDYyr9wyaCL1Dt8hC7qoFJM9scwlwRB2BsiXB+DYzp1YQW5pMsMtWhx4cYnDWmn2XvN8Y8OVezvkQJos+5FIkCcd7uwZzLrB/iMUR9kneid3WQzfv2S42GjlxYeh4vn/UOEPZWjGeogiPu4IdEkAXukFdzN7nBpQMWteIzd7IGsS+z6+u3en/MWP92iS4F2Xqv92GKgNCwf02iQ6bIvEjwjk5m/8gbh5QNmUZADSVn4GwHTyzAgZjYvhj+wgSz6SQoOaNbGimPL4WQfieRh3cxQ5jN6Um39Y0TETMDafR8NiONM1DOQUh$$s7IQ1kVlgEWtP7fa)

plugin.public.cces.clientSecret = KENC(mmahc+9iHfx/kDVm/jkUWD7DHkSwSF+XofcmJUrV1LQqbtIHScG+r3pVAZb2QmGScJ/NmA==$$5YlayAYpY0MXe4Zk)

plugin.public.nis.service.url.header.xItpValue.secretKey = KENC(QwWe4Tif00E3eehWCLYaAjQge1LAt/06XlgumkEY9JpsGmdgr+tBvoKK9NnOt/vq8ojcB+yi/HR5iyh8qS3DqM+dcjTvP3+IqDW06yV2UmA=$$LkE7a7W9WvgxjHJ8)

plugin.public.pdm.auth.value = KENC(gSWeuMFIee48FiHiVpGhV5K5+v15Z3XvJ+aCUO49yoWvFyUUh/+zAjAqdcSO01J+$$xlosNn3eEBJ5xMiv)

plugin.public.zxrdc.auth.value = KENC(95vy1KbbRSOSbozJheeRbnHF3UkBh80Evqzyjhrh4gLyG8IjxEByqKkUVSlbBdkx/BFD4A==$$jXXd9MIDw5nTUi5k)

plugin.public.ucs.keyword.accessKey = KENC(Ph3q3kS1XFy72+u32Go8xw/CI5F64AnG44ZGDr1AqmWDs6t0ZAi1X4FYGBzUfnPPOUgOmQ==$$DzrQVLMYTmqBwgc0)

plugin.public.ucs.keyword.secretKey = KENC(EMmZ8BOo4uc5DCVo70YrIHKnpM+gYi0pWrE74lt5fBd7K3oeUGfw1TpSNg6PIwEeZV0ZMg==$$S2iZVzrM0mQ9KCPA)

plugin.public.client.inet.authValue = KENC(0fevxj0NUPB6yaeBfPmXA0Hs2DDbLmUfUz578mkKdBZeRu6uuOuV1jVmX4IE3HtMCunl7w50fVq6ABDtpFLUrDYoaZpT85hQBaIMPHZTh2PJYw7XsK+eUf7nGTO07eon6bRtRWvt3/I=$$MzP2II2xP8YTJS9X)

plugin.public.email.pkey = KENC(HmPHOJhzbCvXMvuRI8hRPE9cEbXp/BPy3tysMaA9KQsAF0vuLzPaTgXngRS2hKZ4D5RuA97Z3daO3Hfu4BztiFgFISZIpFFlgsfG6Q==$$9fTXL32rBKVzXhKU)

plugin.public.email.appCode = KENC(uSDFAMfC/HLGQbykwT7qjObn8mtKZUTr7jbs4fwJKx4MUvOajoeESQBawutex/Od$$DT8wfO3GVGrraeCz)

plugin.public.email.secretKey = KENC(060FovlCvy+jdg3zPZnQVYdEKCLAj5NaCwFNjjyXrLjlAuWF9Gn1R4inUo2BFW/wMhd2fzLWVhic4glBsjrauBxEuQ1GrdpdkBpVSKakQLg=$$UzqoqFsTSwwfn77e)

# \u9ED8\u8BA4\u5BA1\u6279\u4EBA\u5DE5\u53F7
plugin.public.default.approver.key = 10077093

plugin.public.idop.password = KENC(KtVqye9nC9vy2837xaIduXPoCbkhM9H3u0cMDV3nlCl/ytHdmUCOJU0GKs7EDlvb6IL2th9R7TXHXIcl6e8Id7NjMoMgwbI3UGkFlAKckAljW9Vq1f7+z7wxtTbyzZ6plDr96wbJF1fMdHQNzaxK9Id6oH3fyFXIyVUFbRjH98LIST++EcECuD7svM75RnqV+OeYKfuG1cvTup93czpQbE9oJTBULKuaIjx5skYfVdz4jDg1wgi48kXe+a9Hwh76VXJ8MntqVxbH/PjJ8Hjb6DDgGwWrCTnxNTH3H7EpkktLhVQHfyoh+Ds0H6CetfV1f5gMhWld4jjYjSFGsI07FERrHDKe4+ksgx5iRInGNDfKRwUT9ulhQLbGshdB0AKNwQZ77EUIrIHUBdR1flsaAjsB49Z7orzMbZitgM7znIcwS+y4jHryn+0bIuJjUvAmf3afHriNpMtL4vG3YCDcZZ/J7gVbGrh6$$1ZSWho+AiMoQNI5m)

plugin.public.inone.code = KENC(CfZ/i/B+T3tDcErjsh31RLScnzMuq5HdD59CX+5OudyKOqZL8Zdqg/6ou7zaB42X$$KmOTXSWtdjKGIlDU)

#\u4E1A\u52A1\u5957\u4EF6\u914D\u7F6E
biz.kit.extend.entity.schema = {"id":"##id##","type":"ExtendEntity","props":{"key":"##key##","name":{"type":"i18n","en_US":"##nameEn##","zh_CN":"##nameCn##"},"extendName":"##extendName##","indexField":[],"queryEngine":false,"createdByBizKit":true,"bizKitCode":"##bizKitCode##","usedByPages":[]},"fields":[],"nodeType":"Entity","parentId":"##parentId##"}
biz.kit.element.template = {"code":"OnboardingKit","icon":"DivOutlined","tags":[],"group":"Layouter","props":[{"name":"writeBack","type":"Boolean","required":true,"description":"","defaultValue":false},{"name":"behavior","type":"Behavior","required":true,"description":"","defaultValue":"NORMAL"},{"name":"rowSpan","type":"Number","description":"","defaultValue":null},{"name":"colSpan","type":"Number","description":"","defaultValue":null},{"name":"dataRewriteTime","type":"Behavior","required":false,"description":"","defaultValue":{}},{"name":"items","type":"object","required":true,"description":"","defaultValue":{}},{"name":"bizRules","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u4E1A\u52A1\u89C4\u5219\u5FEB\u7167","defaultValue":{}},{"name":"pageRules","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u9875\u9762\u89C4\u5219\u5FEB\u7167","defaultValue":{}},{"name":"fields","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u9875\u9762\u5B57\u6BB5\u5FEB\u7167","defaultValue":{}},{"name":"operations","type":"object","required":false,"description":"\u751F\u4EA7\u65B9\u64CD\u4F5C\u5FEB\u7167","defaultValue":{}},{"name":"label","type":"I18nExpression","required":true,"description":"","defaultValue":{"en_US":"BizKitEnName","zh_CN":"BizKitCnName"}}],"scope":["page","layout"],"title":{"en_US":"BizKitEnName","zh_CN":"BizKitCnName"},"package":"com.zte.paas.lcap.OnboardingKit","category":"\u4E1A\u52A1\u5957\u4EF6","keywords":[],"priority":"102","configure":{"props":[{"name":"behavior","type":"field","items":[],"scope":["page","layout"],"title":"\u72B6\u6001","setter":{"name":"ChoiceSetter","props":{"mode":"single","options":[{"label":"\u666E\u901A","value":"NORMAL"},{"label":"\u9690\u85CF","value":"HIDDEN"}],"behavior":"NORMAL"}},"display":"inline","condition":"","defaultValue":"NORMAL","supportVariable":false},{"name":"layout","type":"group","items":[{"name":"rowSpan","type":"field","scope":["page","layout"],"title":"\u884C\u95F4\u8DDD","setter":{"name":"SelectSetter","props":{"options":[{"label":"\u8D85\u5927(24px)","value":24},{"label":"\u5927(20px)","value":20},{"label":"\u4E2D(16px)","value":16},{"label":"\u5C0F(12px)","value":12},{"label":"\u8D85\u5C0F(8px)","value":8},{"label":"\u65E0(0px)","value":0}]}},"display":"inline","condition":"","defaultValue":"","supportVariable":false},{"name":"colSpan","type":"field","scope":["page","layout"],"title":"\u5217\u95F4\u8DDD","setter":{"name":"SelectSetter","props":{"options":[{"label":"\u8D85\u5927(24px)","value":24},{"label":"\u5927(20px)","value":20},{"label":"\u4E2D(16px)","value":16},{"label":"\u5C0F(12px)","value":12},{"label":"\u8D85\u5C0F(8px)","value":8},{"label":"\u65E0(0px)","value":0}]}},"display":"inline","condition":"","defaultValue":"","supportVariable":false}],"scope":["page","layout","serviceLayout"],"title":"\u5E03\u5C40","display":"block","supportVariable":false},{"name":"dataRewriteTime","type":"field","tip":"\u5F53\u5355\u636E\u72B6\u6001\u7B49\u4E8E\u6B64\u5904\u5B9A\u4E49\u65F6\uFF0C\u4F1A\u5C06\u5957\u4EF6\u4E2D\u6570\u636E\u56DE\u5199\u5230\u5957\u4EF6\u6240\u5C5E\u539F\u4E1A\u52A1\u5BF9\u8C61\u4E2D","items":[],"scope":["page","layout"],"title":"\u6570\u636E\u56DE\u5199\u65F6\u673A","setter":"DataRewriteSetter","display":"block","supportVariable":false,"condition":{"type":"JSFunction","value":"(field)=> { return ##WriteBack## }"}},{"name":"items","type":"field","title":"\u5143\u7D20\u8BBE\u7F6E","setter":{"name":"DynamicFieldsSetter","props":{}},"display":"block","supportVariable":false},{"name":"advance","type":"group","items":[{"tip":"\u7531\u5B57\u6BCD\u3001\u4E0B\u5212\u7EBF\u548C\u6570\u5B57\u7EC4\u6210\uFF0C\u4EE5\u5B57\u6BCD\u5F00\u5934\uFF0C\u6700\u957F\u53EF\u8F93\u516564\u4E2A\u5B57\u7B26","name":"cid","type":"field","items":"","scope":["page","layout"],"title":"\u552F\u4E00\u6807\u8BC6","setter":{"name":"StringSetter","props":{"behavior":"NORMAL","maxLength":64,"copyWidget":true}},"display":"inline","condition":"","defaultValue":"","supportVariable":false}],"scope":["page","layout"],"title":"\u9AD8\u7EA7","setter":"","display":"block","supportVariable":false}],"supports":{"style":false},"component":{"isModal":false,"isContainer":false}},"subcategory":"","componentName":"RendererKit"}
biz.kit.sync.value.biz.rule = {"code":"##code##","name":{"type":"i18n","en_US":"SyncValues","zh_CN":"SyncValues"},"draft":false,"boCode":"##bizObjCode##","enabled":true,"category":"businessRule","entryKey":null,"priority":null,"condition":null,"createdBy":null,"ruleUnits":[{"type":"If","actions":[{"type":"Expression","expression":"syncBizKitValue(##bizKeyWithQuotation##)"}],"condition":{"expression":""}},{"type":"Else","actions":[],"condition":{"expression":""}}],"createDate":null,"extensions":null,"description":null,"trueActions":[],"falseActions":null,"descriptionCn":"","descriptionEn":"","isAggregation":false,"lastUpdatedBy":"system","mainEntryRule":true,"triggerEvents":["value_changed"],"currentTrigger":null,"lastUpdateDate":1732001353890,"parentEntryKey":null,"entryEntityRule":false,"runningRuleUnit":null,"dependencyFieldIds":["##bizKey##"],"subEntryEntityRule":false,"valueRangeRuleUnits":null}

# \u83DC\u5355\u7BA1\u7406 \u5185\u90E8\u9875\u9762\u94FE\u63A5url(\u4E0D\u540C\u73AF\u5883\u9700\u66FF\u6362\u57DF\u540D\u4E3A\u5F53\u524D\u73AF\u5883\u57DF\u540D\uFF0C\u72EC\u7ACB\u90E8\u7F72\u7684\u5E94\u7528\u9700\u66FF\u6362zte-paas-lcap-frontendrenderdemo\u4E3A\u81EA\u8EAB\u670D\u52A1\u540D)
menu.link.url.format = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&type=app#/app/{0}/page/{1}

# \u662F\u5426\u6253\u5F00\u5185\u90E8\u7F51\u7EDC\u53D8\u66F4\u5355\u81EA\u5B9A\u4E49\u5FC5\u586B\u6821\u9A8C
plugin.public.change.network.check.enable = true

plugin.public.cnop.changeOrder.view.pageUrl = https://itech.zte.com.cn/zte-iccp-itech-frontendrenderdemo/app.html?tenantId=10001&lang=zh&type=app&instanceDataId=%s&pageStatus=VIEW&openType=newTab#/app/APP0984032412495249408/page/%s

plugin.public.india.org.id = **********/**********/**********/**********
plugin.public.india.common.mail = <EMAIL>

# \u4EBA\u5458\u79EF\u5206\u6821\u9A8C\u767D\u540D\u5355\u8D26\u53F7
plugin.public.check.personnel.enable = false
plugin.public.check.personnel.white = 10282740

# \u7F51\u7EDC\u53D8\u66F4\u4EFB\u52A1\u540C\u6B65\u5BFC\u51FA\u4E0A\u9650
plugin.public.change.order.sync.export.limit = 50

# \u662F\u5426\u5F00\u542F\u540C\u65F6\u7F16\u8F91\u6821\u9A8C\u51B2\u7A81\uFF0C\u9ED8\u8BA4\u5F00\u542F\u3002 false\u53D6\u6D88\u3002
lcap.save.check.conflict=false

#\u5934\u4FE1\u606F\u8FC7\u5927\uFF0C\u5BFC\u81F4\u8BF7\u6C42400\u5F02\u5E38
server.max-http-header-size=40000