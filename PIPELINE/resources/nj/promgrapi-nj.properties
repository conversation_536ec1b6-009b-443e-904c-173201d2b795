#*****************************************************
#*****************************************************
#MSB
register.address = nj-msr.msp.zte.com.cn:10081
#MSB
register.node.ip = ***********
register.node.port = 80
servicecenter = msb
#\u670D\u52A1\u7248\u672C
register.version = v1
jdbc1.type = com.alibaba.druid.pool.DruidDataSource

#\u670D\u52A1\u7248\u672C

jdbc1.driverClassName = com.goldendb.jdbc.Driver

jdbc1.url = *****************************************,************:8201/itech_promgr?useCursorFetch = true&autoReconnect=true&zeroDateTimeBehavior=convertToNull&connectTimeout=60000&failOverReadOnly=false&loadBalancePingTimeout=1000&allowMultiQueries=true&isConnectionLevel=true&blackTaskTime=5&intervalTime=3000


jdbc.maxActive = 100


# \u4F4E\u4EE3\u7801\u5143\u6570\u636E\u5E93\u914D\u7F6E\uFF0C\u6309\u7167msa4.0.3\u52A0\u5BC6\u65B9\u5F0F\u81EA\u884C\u52A0\u5BC6\uFF0CMSA4.0.3\u5347\u7EA7\u8BF4\u660E
jdbc.minIdle = 50

# \u4F4E\u4EE3\u7801\u5143\u6570\u636E\u5E93\u914D\u7F6E\uFF0C\u6309\u7167msa4.0.3\u52A0\u5BC6\u65B9\u5F0F\u81EA\u884C\u52A0\u5BC6\uFF0CMSA4.0.3\u5347\u7EA7\u8BF4\u660E
jdbc.initialSize = 50


jdbc.timeBetweenEvictionRunsMillis = 60000

jdbc.minEvictableIdleTimeMillis = 300000


jdbc.testWhileIdle = true
jdbc.testOnBorrow = true

jdbc.testOnReturn = true

uac.sdk.tenantId = 10001


uac.sdk.appId = ************
uac.sdk.issuer = https://uac.zte.com.cn/zte-sec-uac-iportalbff/oidc/10001
uac.sdk.baseUrl = https://itech.zte.com.cn/zte-iccp-itech-demobff
uac.sdk.clientId = ************

uac.sdk.responseRule = msa

uac.sdk.store = redis

uac.url = https://uac.zte.com.cn
#uac\u7528\u6237\u7528\u81EA\u5DF1\u7684\u76F8\u5173\u73AF\u5883\u914D\u7F6E
uac.sdk.cookie.httpOnly = false
uac.sdk.gateAuth.externalAccessEnabled = true
uac.sdk.cookie.secure = true
uac.sdk.env = prod

lcap.employee.provider = uacEmployeeProvider

uac.idType = T0001


#uac\u7528\u6237\u7528\u81EA\u5DF1\u7684\u76F8\u5173\u73AF\u5883\u914D\u7F6E
cloudDiskSDK.host = https://idrive.zte.com.cn/zte-km-cloududm-clouddisk
cloudDiskSDK.httpClientLogSwitch = true
cloudDiskSDK.xOriginServiceName = EPMS

# \u6CE8\u610F\u751F\u4EA7\u914D\u7F6Eprod\u4E00\u5B9A\u8981\u6253\u5F00\u6B64\u914D\u7F6E\uFF0C\u5F00\u53D1\u6D4B\u8BD5\u73AF\u5883\u914D\u7F6Edev\u6216\u8005\u4E0D\u914D\u7F6E
approval.kafka.topic = zte-iss-approval-nj
approval.sdk.app.appId = APPF8141692615437951


# \u83B7\u53D6\u8D26\u6237\u4FE1\u606F\u4F7F\u7528uac3.0\u67E5\u8BE2\uFF08\u5185\u573A\u73AF\u5883\uFF1AuacEmployeeProvider\uFF0C \u5916\u573A\u73AF\u5883\uFF1AuacEmployeeOuterProvider\uFF09
approval.sdk.app.appCode = lcap-pro
approval.sdk.header.xEmpNo.default = 10309799
# uac3.0\u6279\u91CF\u67E5\u8BE2\u8D26\u6237\u7C7B\u578B\uFF08\u5185\u573A\u73AF\u5883\uFF1AT0001\uFF0C \u5916\u573A\u73AF\u5883\uFF1AT0000\uFF09
approval.sdk.kafka.receipt.enabled = false
approval.sdk.webhook.url = https://itech.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/flow/webhookProcess
# uac3.0\u79D8\u94A5 \u53C2\u8003\u8FDE\u63A5 \u3010V1.24.33\u53CA\u4EE5\u4E0A\u7248\u672C\u3011\u4F4E\u4EE3\u7801\u5E94\u7528\u5F00\u53D1\u5E73\u53F0\u4E0D\u5B89\u5168\u52A0\u5BC6\u65B9\u5F0F\u6574\u6539\u901A\u77E5
approval.sdk.request.header.tenantId.source = true
msa.ccs.enable = true

msa.ccs.resourceCode = zxiccp-iepms300-itechcloud

# \u6CE8\u610F\u751F\u4EA7\u914D\u7F6Eprod\u4E00\u5B9A\u8981\u6253\u5F00\u6B64\u914D\u7F6E\uFF0C\u5F00\u53D1\u6D4B\u8BD5\u73AF\u5883\u914D\u7F6Edev\u6216\u8005\u4E0D\u914D\u7F6E
#\u6587\u6863\u4E91\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
cacheCloud.server = http://cache.zcloud.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4

#cloudDiskSDK.xSecretType = HMACSHA256
redis.customCacheManager = true
# \u83B7\u53D6\u8D26\u6237\u4FE1\u606F\u4F7F\u7528uac3.0\u67E5\u8BE2\uFF08\u5185\u573A\u73AF\u5883\uFF1AuacEmployeeProvider\uFF0C \u5916\u573A\u73AF\u5883\uFF1AuacEmployeeOuterProvider\uFF09
msa.redis.driveType = jedis
msa.redis.serializerType = genericJackson2Json

# uac3.0\u6279\u91CF\u67E5\u8BE2\u8D26\u6237\u7C7B\u578B\uFF08\u5185\u573A\u73AF\u5883\uFF1AT0001\uFF0C \u5916\u573A\u73AF\u5883\uFF1AT0000\uFF09
#\u5E94\u7528\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u7684\u76F8\u5173\u914D\u7F6E
msa.redis.sotimeout = 1000

msa.redis.maxAttempts = 2
#\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u7684kafka\u914D\u7F6E\uFF0C\u4E0D\u7528\u4FEE\u6539
# uac3.0\u79D8\u94A5 \u53C2\u8003\u8FDE\u63A5 \u3010V1.24.33\u53CA\u4EE5\u4E0A\u7248\u672C\u3011\u4F4E\u4EE3\u7801\u5E94\u7528\u5F00\u53D1\u5E73\u53F0\u4E0D\u5B89\u5168\u52A0\u5BC6\u65B9\u5F0F\u6574\u6539\u901A\u77E5
msa.redis.maxRedirects = 5
#\u4F7F\u75284A\u7533\u8BF7\u7684appId
msa.redis.pool.maxActive = 8

#\u4F7F\u75284A\u7533\u8BF7\u7684secretKey
msa.redis.pool.maxWait = 3000

#\u4F7F\u75284A\u7533\u8BF7\u7684appId
msa.redis.pool.maxIdle = 300
#\u6587\u6863\u4E91\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
#\u5E94\u7528\u81EA\u5B9A\u4E49\u7684\u9ED8\u8BA4\u5DE5\u53F7\uFF08\u4EFB\u610F\uFF09
msa.redis.pool.minIdle = 300
msa.redis.pool.minEvictableIdleTimeMillis = 1800000
msa.redis.timeout = 3000
#\u5BA1\u6279\u4E2D\u5FC3\u56DE\u8C03\u5E94\u7528\u7684url
#cloudDiskSDK.xSecretType = HMACSHA256
msa.redis.cacheManager.expire = 86400
# \u6D41\u7A0BSDK\u662F\u5426\u9700\u8981\u4F20\u9012tenantId\u8BF7\u6C42\u5934

inone.url = https://icosg.dt.zte.com.cn
message.server = http://message.zcloud.zte.com.cn:8888/zte-itp-msp-message/v1

spring.flyway.enabled = false
#\u5E94\u7528\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u7684\u76F8\u5173\u914D\u7F6E
spring.flyway.encoding = UTF-8

spring.flyway.locations = classpath:db/migration/prod
#\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u7684kafka\u914D\u7F6E\uFF0C\u4E0D\u7528\u4FEE\u6539
spring.flyway.sql-migration-prefix = V
spring.flyway.sql-migration-separator = __
#\u4F7F\u75284A\u7533\u8BF7\u7684appId
spring.flyway.validate-on-migrate = false
spring.flyway.baseline-on-migrate = true
#\u4F7F\u75284A\u7533\u8BF7\u7684secretKey
spring.flyway.placeholder-replacement = false
spring.flyway.table = promgrapi_flyway_schema_history
#\u4F7F\u75284A\u7533\u8BF7\u7684appId
spring.flyway.out-of-order = true
msa.expose.info.enable = true
#\u5E94\u7528\u81EA\u5B9A\u4E49\u7684\u9ED8\u8BA4\u5DE5\u53F7\uFF08\u4EFB\u610F\uFF09
lcap.app.upload.target.service = zte-paas-lcap-demoapi.v1
lcap.app.upload.target.path = zte-paas-lcap-demoapi
lcap.mc.version.no = v1
#\u5BA1\u6279\u4E2D\u5FC3\u56DE\u8C03\u5E94\u7528\u7684url
lcap.mc.service.name = zte-paas-lcap-promcapi

# \u6D41\u7A0BSDK\u662F\u5426\u9700\u8981\u4F20\u9012tenantId\u8BF7\u6C42\u5934

lcap.mc.route.url = https://ida.zte.com.cn/zte-paas-lcap-promcbff/zte-paas-lcap-promcapi


#msa\u52A0\u5BC6
msa.encrypt.datakey = V5G4LbA9q7K9ZPchxeHVWwaCAaoCqcmLUvs32nN5/2CFM1oj26ticrcj3MyH3TYbNI5QhOChM5/3vcEwvYN9Aw==
msa.rootkey.factor1 = zte-iccp-itech-promgrapi-prod
msa.ccs.encrypt.factor = ENC(MhecWhhtF9h00j5sVGROUqgyB2jgCCnEWbvR2bNIpjWtO9WH3NzPMLKMTrAZRrz7GZcnJwva8kE=)

msa.security.server.dt-kms.enable = true
#kms\u914D\u7F6E\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms.dt.zte.com.cn:8443
msa.security.server.dt-kms.appName = ENC(doqB1h8tINODlPzxDh4cN8rPt3aTI0KX1DLWtubsERF3Xy0b2Zx4Akg=)
msa.security.server.dt-kms.userId = ENC(CKSLJyhEyio/lYeAOYOXjZKvONCc3snJrjslUy4AJSW3YpdPi61snCR/ovo=)
msa.security.server.dt-kms.appSecret = ENC(UFNkrHFyiI2c6MqM4DnNV3OA6tscqKfwVpa3tAjztGOrs8MTGzVayDdwJpOKWkMX/58t+BgKqFd6tHICPdf9g/wAk0NnfVkqakg=)
msa.security.server.dt-kms.defaultMasterKey = itechcloud

#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:demoapi:demoapi:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = ZTgzNGIyMDNjYmU1N2IzNjQyM2MwOTYwNWE0NzUwNGQ2ZWMyMGNkNWRlZWRiNTVmYjFlZjhlNGU4NzlkNTAyZjEyMzcyN2JlNTQ2OGIyMzNhMTliNjRiMDM3OWI0YTY3$$MTJlZTVlNDVjYWEzOGExMjExMDM3YTg5MmNmMjJhYmU=&&OGZjNDI0ZTAtMjcwNy00YzkxLWJkNjgtODFkYWRiZTUxZTYy

msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.cipher = N2RhNGNjYTFmYTljNDkzMDczMjhlMjc3ZDk1ZDcyMGYzNmFmZmZiODk0YWZhZDIzZjgwNmEyYTk5MDljMDIyODliNDI3NjBmMmI1MzFlYzljMDhlNzFjYjcxMTM2OTFj$$NGQwOWY0NTYzYWYwNjAyNDY3ZGRmMWIyYzcwYmNkMmI=&&OGZjNDI0ZTAtMjcwNy00YzkxLWJkNjgtODFkYWRiZTUxZTYy
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.sensitiveData = ZTllYTM2NTUzMjA2YTFlMWIzNmE4NjVjZjQzZGEzYTM2NjdiMThiYWUwNDE0NjM3Mzc0NjIwNWM3OTkzZjQwYTczNTkzMTJjYWUxNTBlMTc1MjAwNTgzY2I5MjU4NDgz$$MzYwMTFjMDFiOTBjYmVkZmNhZWYwN2ZiZTY5OWI3Y2I=&&OGZjNDI0ZTAtMjcwNy00YzkxLWJkNjgtODFkYWRiZTUxZTYy


# \u4EE5\u4E0B\u53C2\u6570\u9700\u91CD\u65B0\u7528kms\u52A0\u5BC6
jdbc1.username = KENC(5nXFO1uuEvIZE7yoErUqY15CxLyw9QFR$$LPnFYm+G+YSGJSEv)

jdbc1.password = KENC(DCBQlvsRdybMWR+qbeB4DIse3w+I58td2Ac=$$uHBEECSwZ1qIDfgx)

cloudDiskSDK.xOrgId = KENC(7P3nFt4Cg5iiVXF9gMH9kBfvEwNkpkPmgyeHaSdeA0J1vSmlcdhF3OHCvE4HqUDQcZRS3A==$$R/2SPsnX6xiIBzwS)
cloudDiskSDK.xSecretKey = KENC(aCHynvJ7lkXcamwct7rNZNqku3kCqJ4dMFLLFJs84bpL33f9SRpZFaIx6xWCgAznNdoL5Q==$$SCoX7emL+kxtMw5l)

approval.sdk.app.secretKey = KENC(uL1QheEEII4dKmjE7Ew+P04g5ON6xd0gscIQNaqMb4nfA4/ief9RbNrln+ahUimllNpWOIJRa0wyp2Pcm8URfbuV5wS/TvNCl4JR2qwzB3o=$$XFVz9VXpighxDBKT)

uac.sdk.clientSecret = KENC(oz11EiPInw8UZ0ijy5bEc9UiZT4+PLyYbDKEKN23vDJv9SiLWpVmkPXyQvqfd40G7JLhGyLYknnr2xx7bvcPnoVkETPHSceJsn/Wr+DBsDk=$$vkACSypfp2uGVS8S)
uac.sdk.accessKey = KENC(ZaqNxKZ8/AvBd5ZrUAnZzPERPMavuvS649Ew69n+YkmZwEQfuIVXr7633lFVXPah$$szzUnWvq1KU4lA8/)
uac.sdk.accessSecret = KENC(A+dyRPaCiIqjw9GEHfgzEU79/FhNtqYRCt34atRlcnxfrlpJtO2fZPVtLen3morAnEcZhvjaj66bKJO3LeujiQW2yfqxOOtevyHwdoXQyRw=$$d2KO7wXTNBlRhFij)

uac.key = KENC(zfpWTXrA5V5K4ejcHdutdqR/8MEDLXUznSXV1nLQtktL4HNRLXOKs2DSwfZW6p0dG+KohAPIJ6lN7eKAYOQvjXYqSdBloFVGTdiiRA==$$rqu3MqJGqAzWLrif)

inone.appCode = KENC(MF0xU3QdRdoPN3E2XvD0BOzk509nIjy76Hu7jMWMp7iLu/TmRjmcjUi4ForhV3Ot$$8axroCLkwe5jaPCn)

#demoapi\u548Cpromgrapi\u539F\u503C\u9700\u4FDD\u6301\u4E00\u81F4
datasource.config.gcm.encrypt.key = KENC(of9yeoyzV1/5y04ql4iwJMVz5hZ6Y6xxzYSXy76DyiOw+M+8r4x2vRVDoupo/MMI$$4oLXIeB0vUAsUFl3)
common.app.param.secret = KENC(UNiaJsNlWDf2GIoPsk6pI1vPD2tOuSj/Dz16+3vAIp+AoDoTYUjIVldr4vNs2riS$$a0vQZmN+NpV9KyEc)

# \u6570\u636E\u6E90\u52A0\u89E3\u5BC6\u76F8\u5173\u53C2\u6570
# \u8BE6\u7EC6\u751F\u6210\u6307\u5357\u8BF7\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/e76cd1492ed642b1b6b711cd4025285b/view
common.rsa.public-key = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi8wjqVGf1LmsPjU0WJrNZfZXqjrgt0oVlwQSJpMLq9hFhYSE98/t7iJo/gh/UhQBY7QxOAeuxAZfLEcBj/oP8T0Yn3SbSTA1V/67vD4KR4xZGhjJYjxtTvu9vI+lrHnw62ypcs3b1OZSTuswbbGXHCJ+ApYOMc7+l5+ywXEu4tJXNBphdOqcy16J6FSX8mZSuQ5mNyEdIb0VMCVCCrn4cLFh9XYeydob0ThMEqfDTW4anQA56kzkp6aDobG53nDWIKSAZNMU6whPRC4fYngkvhVBBMa+JXStjgbSNhJ/YyxUKEz+633Tf5xW0SmzJvG/Ck8QDanXXB1cJFjmO2aLHwIDAQAB

common.rsa.private-key = KENC(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$$BWliQWik4p6Bn8Qa)