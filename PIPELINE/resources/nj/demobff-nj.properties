# \u5927\u6587\u4EF6\u4E0A\u4F20\u8D85\u65F6\u95EE\u9898
ribbon.ReadTimeout = 120000
hystrix.command.default.execution.timeout.enabled = true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds = 120000

#\u5934\u4FE1\u606F\u8FC7\u5927\uFF0C\u5BFC\u81F4\u8BF7\u6C42400\u5F02\u5E38
server.max-http-header-size=40000

upp.kafka.topic = sec-upp-api
upp.kafka.bootstrap-serversc = kafka-upp-001.dt.zte.com.cn:9092
upp.kafka.consumer.enableAutoCommit = false
upp.kafka.consumer.autoOffsetReset = latest
# \u4E1A\u52A1\u540D\u79F0\uFF0C\u4FDD\u8BC1\u552F\u4E00\uFF0C\u63A8\u8350\u4F7F\u7528\u4E1A\u52A1\u65B9\u670D\u52A1\u540D\u5373\u53EF
upp.kafka.consumer.groupId = zte-iccp-itech-demobff
upp.kafka.listen.enabled = true
upp.auth.encryptor = KENC(B0GiJLxtgwwYuJdK4zYcHI7aIDLydlndCQ20MiEQVtrwQbg7eMcbo2fGEvHXnCP+na9LkCureI3oCCR6fdk03xkyV5nMyKtY0fvLraa/Z98o3jJ6did1NWNLM2TpDilCgBeV2eRo9tLvOvaR4LKBu2p3YQn3eWAT60q0hXXA4KHWiKkJwD4Rr83xYxkGb5CuoLK9UpID/cnWaDtnpwZElHdXJ5XIFyZ2OoxXbWqoGo8e3M1BR2FeIIZ0WGZPlPVwIyRwsV0d5Bkkmru2rVqGi108TJ89mxXTEA7WHM2h8TOg9XWexASyVg==$$A3VXGWXStJ9SZ+Oo)
upp.sha256.salt = KENC(Nu4eJ6HKkjjBtOM/e9Xpe8G3J2O6pJrAHU1MhOvMKZMGOM3h6eZWA2sjutMZNljSw7GsusS8BIyiKMGr8haWyBTmZKqnqYHqR1cjXaskl/t0PUlsL2u1rZG6nzTXPYJR7ygaPOZL2kA1QqprmgwAS2q4ZRXu5TyUdyj0IQIMD9TAAasW08KxuQukcF0wumAs$$UHcy2TIOs6+vqP9j)

# \u4E1A\u52A1\u65B9\u72EC\u7ACB\u90E8\u7F72demoapi\u7684\u670D\u52A1\u540D\uFF08\u5207\u8BB0\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u5426\u5219\u4F1A\u6743\u9650\u6821\u9A8C\u6709\u95EE\u9898\uFF09
demoapi.service.name=zte-iccp-itech-demoapi
# \u53EF\u4EE5\u4E0D\u914D\u7F6E\uFF0C\u6DFB\u52A0\u8BE5\u914D\u7F6E\u5219\u901A\u8FC7\u5F53\u524D\u9875\u6253\u5F00\u9875\u9762\u65F6\u4E0D\u663E\u793A\u5934\u90E8
ui.config.project.currentPage.hideTop.appIds=291020441815

# 22\u7248\u672C\u65B0\u589E\u4E86\u5355\u9009\uFF08\u65B0\uFF09\u3001\u4E0B\u62C9\u5355\u9009\uFF08\u65B0\uFF09\u5B57\u6BB5\uFF0C\u662F\u5426\u652F\u6301\u65E7\u5B57\u6BB5\uFF0C\u9ED8\u8BA4\u4E3Afalse\uFF0C\u5373\u4E0D\u652F\u6301\u3002\u5982\u679C\u914D\u7F6Etrue\uFF0C\u9700\u8981\u914D\u5408 ui.config.project.oldFeildEnv \u4F7F\u7528
# \u8BE6\u60C5\u8BF7\u89C1\uFF1ALCAP-PRO 25.22\u7248\u672C\u66F4\u65B0\u2014\u65B0\u589E\u5355\u9009\uFF08\u65B0\uFF09\u3001\u4E0B\u62C9\u5355\u9009\uFF08\u65B0\uFF09\u3001\u7EC4\u7EC7\uFF08\u65B0\uFF09\u5B57\u6BB5&&\u65E5\u671F\u652F\u6301\u65F6\u533A\u4F18\u5316\u3001\u6D41\u7A0B\u8282\u70B9\u652F\u6301\u81EA\u5B9A\u4E49\u6821\u9A8C\u7B49
ui.config.project.supportOldFeild = true
# \u652F\u6301\u65E7\u5B57\u6BB5\u7684\u73AF\u5883\uFF0C\u652F\u6301\u914D\u7F6E\u591A\u4E2A\u73AF\u5883\u7F16\u7801\uFF08\u4E3A\u4F4E\u4EE3\u7801\u5E73\u53F0\u7684\u73AF\u5883\u7BA1\u7406\u9875\u9762\u91CC\u7684\u73AF\u5883\u7F16\u7801\uFF09\uFF0C\u7528\u9017\u53F7\u9694\u5F00\uFF0C\u4F8B\u5982\uFF1Adev1,dev2 #
ui.config.project.oldFeildEnv = iTechCloud

#MSB\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
register.address = nj-msr.msp.zte.com.cn:10081
register.node.ipport = ***********:80,***********:80,*************:80

register.version = v1

servicecenter = msb

springfox.documentation.enabled = false


# \u5F53\u524D\u4EA7\u54C1id, \u5411\u7EDF\u4E00\u6743\u9650\u7CFB\u7EDF\u7D22\u8981\uFF08\u533A\u5206\u73AF\u5883\uFF09
upp.auth.productId = 570056
# \u5F53\u524D\u6A21\u5757id, \u5411\u81EA\u5DF1\u4E1A\u52A1\u4EA7\u54C1\u7684\u4EA7\u54C1\u7BA1\u7406\u5458\u6216\u8005DT\u7BA1\u7406\u5458\u7D22\u8981\uFF08\u66F4\u5177\u81EA\u8EAB\u4E1A\u52A1\u5224\u65AD\u662F\u5426\u4F7F\u7528\uFF09\uFF08\u533A\u5206\u73AF\u5883\uFF09
upp.auth.moduleId = 291020441815
# \u79DF\u6237id,4304:\u6D4B\u8BD5\u73AF\u5883    2:\u751F\u4EA7\u73AF\u5883
upp.auth.tenantId = 2

# \u9274\u6743\u5730\u5740\uFF08\u533A\u5206\u73AF\u5883\uFF09
# \u9274\u6743\u5730\u5740\uFF08\u533A\u5206\u73AF\u5883\uFF09
#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-authbff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-authbff
#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-authbff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-authbff
upp.auth.authUrl = https://uac.zte.com.cn/zte-sec-upp-authbff
# \u6388\u6743\u5730\u5740\uFF08\u533A\u5206\u73AF\u5883\uFF09
# \u6388\u6743\u5730\u5740\uFF08\u533A\u5206\u73AF\u5883\uFF09
#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-bff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-bff
#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-bff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-bff
upp.auth.manageUrl = https://uac.zte.com.cn/zte-sec-upp-bff


# msa gateway \u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
# msa gateway \u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
msa.gateway.routes[0].id = ucsRoute
msa.gateway.routes[0].uri = forward:/ucs/
# api\u670D\u52A1\u8DEF\u7531\u914D\u7F6E
msa.gateway.routes[0].stripPrefix = true
msa.gateway.routes[0].predicates[0] = Path=/ucs/**
# api\u670D\u52A1\u8DEF\u7531\u914D\u7F6E
msa.gateway.routes[1].id = zte-iccp-itech-demoapi
msa.gateway.routes[1].uri = lb://zte-iccp-itech-demoapi/v1
msa.gateway.routes[1].predicates[0] = Path=/zte-iccp-itech-demoapi/**
msa.gateway.routes[2].id = uac
msa.gateway.routes[2].stripPrefix = true
msa.gateway.routes[2].uri = forward:/uac/
msa.gateway.routes[2].predicates[0] = Path=/uac/**


# \u662F\u5426\u663E\u793A\u5206\u4EAB\u6309\u94AE\uFF0C\u9ED8\u8BA4\u503C\u662Ftrue
# \u662F\u5426\u663E\u793A\u5206\u4EAB\u6309\u94AE\uFF0C\u9ED8\u8BA4\u503C\u662Ftrue
ui.config.project.isShowShare = true
# \u63A7\u5236\u79FB\u52A8\u7AEF\u5BA1\u6279\u8BE6\u60C5\u9875\u9762\u7684\u8C03icenter\u63A5\u53E3\u7684
# \u63A7\u5236\u79FB\u52A8\u7AEF\u5BA1\u6279\u8BE6\u60C5\u9875\u9762\u7684\u8C03icenter\u63A5\u53E3\u7684
ui.config.project.isProduct = true
# \u591A\u79DF\u6237\u79DF\u6237id\u8C03\u8BD5
# \u8FD9\u4E2A\u662F\u63D0\u4F9B\u7ED9market\u63A7\u5236\u662F\u5426\u663E\u793A\u5934\u90E8\u5173\u95ED\u6309\u94AE\u7684
# \u591A\u79DF\u6237\u79DF\u6237id\u8C03\u8BD5
# \u8FD9\u4E2A\u662F\u63D0\u4F9B\u7ED9market\u63A7\u5236\u662F\u5426\u663E\u793A\u5934\u90E8\u5173\u95ED\u6309\u94AE\u7684
ui.config.project.isShowClose = true
# \u591A\u79DF\u6237\u79DF\u6237id\u8C03\u8BD5
# \u591A\u79DF\u6237\u79DF\u6237id\u8C03\u8BD5
#ui.config.project.tenantId = 20001
# \u662F\u5426\u663E\u793A\u5BA1\u6279\u8BE6\u60C5\u9875\u6D41\u7A0B\u72B6\u6001
ui.config.project.isShowProcessStatus = true
ui.config.project.defaultUserAvatar = true
# \u662F\u5426\u5C55\u793A\u9644\u4EF6\u9884\u89C8
ui.config.project.isPreview = true
ui.config.project.envConfig = prod
#\u5E94\u7528\u5BFC\u51FA\u5BFC\u5165\u9650\u5236200M
#\u5E94\u7528\u5BFC\u51FA\u5BFC\u5165\u9650\u5236200M
ui.config.app.package.upload.size = 200
#\u6D41\u7A0B\u901A\u77E5\u6A21\u677F\u83DC\u5355\u7684\u94FE\u63A5\uFF0C\u751F\u4EA7\u73AF\u5883\u9700\u8981\u66FF\u6362\u5BF9\u5E94\u7684\u5730\u5740
ui.config.notice.center.url = https://i.zte.com.cn/zte-icenter-notice-messageweb/#/template-management

#\u9009\u586B\uFF0C\u79DF\u6237ID\uFF0C\u9ED8\u8BA410001\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.tenantId = 10001
#\u9009\u586B\uFF0C\u7CFB\u7EDF\u7F16\u7801/PDM\u7F16\u7801\u7B49\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.appId = 291020441815
#\u5FC5\u586B\uFF0C\u670D\u52A1\u53D1\u884C\u7AEF\uFF0C\u548C\u73AF\u5883\u8981\u5BF9\u5E94\uFF0C\u5982\uFF1Ahttps://uac.zte.com.cn/zte-sec-uac-iportalbff/oidc/${uac.sdk.tenantId}
uac.sdk.issuer = https://uac.zte.com.cn/zte-sec-uac-iportalbff/oidc/10001
#\u5FC5\u586B\uFF0C\u4E1A\u52A1\u7CFB\u7EDF\u57FA\u7840url\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.baseUrl = https://itech.zte.com.cn/zte-iccp-itech-demobff
#\u5FC5\u586B\uFF0C\u7CFB\u7EDF\u7F16\u7801\uFF0C\u7533\u8BF7\u83B7\u5F97\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
uac.sdk.clientId = 291020441815

#\u9009\u586B\uFF0C\u5B58\u50A8\u4E2D\u95F4\u4EF6\uFF0C\u591A\u5B9E\u4F8B\u90E8\u7F72\u4F7F\u7528redis\uFF0C\u5355\u5B9E\u4F8B\u90E8\u7F72\u53EF\u4EE5\u4F7F\u7528memory(\u672C\u5730\u7F13\u5B58)\uFF0C\u9ED8\u8BA4redis
uac.sdk.store = redis
#\u8FD4\u56DE\u4F53\u89C4\u8303\uFF0Cidn:iDN\u683C\u5F0F\uFF0Cmsa:msa\u683C\u5F0F\uFF0C\u9ED8\u8BA4iDN
uac.sdk.responseRule = msa
msa.gateway.tokenAuthEnable = false
msa.gateway.uac.enabled = false
uac.sdk.env = prod
uac.sdk.cookie.httpOnly = false
uac.sdk.gateAuth.externalAccessEnabled = true
uac.sdk.cookie.secure = true
uac.sdk.token.httpOnlyReadHeaderEnabled = true

# \u9274\u6743\u767D\u540D\u5355\u63A5\u53E3\u914D\u7F6E\uFF08\u5E94\u7528\u4FA7\u6309\u9700\u6DFB\u52A0\u914D\u7F6E\uFF09
url.white.list = /zte-iccp-itech-demoapi/customComponents/resources/download/**,/zte-iccp-itech-demoapi/v1/flow/webhookProcess,/zte-iccp-itech-demoapi/scheduler/**,/zte-iccp-itech-demoapi/APP0984032412495249408/grantfiles,/zte-iccp-itech-demoapi/APP0984032412495249408/batchtasks/*/4auth,/zte-iccp-itech-demoapi/idop/**,/zte-iccp-itech-demoapi/assignment/fault/**,/zte-iccp-itech-demoapi/networksecurity/**,/zte-iccp-itech-demoapi/cnop/**,/zte-iccp-itech-demoapi/common/**

msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-itechcloud
cacheCloud.server = http://cache.zcloud.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4
http.socketTimeout = 60000
spring.http.client.read-timeout = 60000
spring.http.client.connect-timeout = 60000

msa.security.server.dt-kms.enable = true

# \u8DF3\u8F6C\u767B\u5F55uac\u73AF\u5883\u6807\u8BC6
# \u8DF3\u8F6C\u767B\u5F55uac\u6D4B\u8BD5\u73AF\u5883uactest.zte.com.cn:5555\uFF1Atest
# \u8DF3\u8F6C\u767B\u5F55uac81\u73AF\u5883uactest.zte.com.cn\uFF1Auat
# \u8DF3\u8F6C\u767B\u5F55uac\u751F\u4EA7\u73AF\u5883\uFF0C\u9ED8\u8BA4\u4E3A\u7A7A\uFF0C\u65E0\u9700\u914D\u7F6E
uac.login.env =

#\u65E5\u5FD7\u4E2D\u5FC3\u914D\u7F6E(\u5FC5\u586B)\uFF0C\u9700\u8981\u4E1A\u52A1\u7533\u8BF7topic\uFF0C\u53EF\u8054\u7CFB\u8C22\u65FB\u7FF00668001012\uFF0C\u4F4E\u4EE3\u7801\u914D\u7F6E\u4E3Ams-zte-lcap-app
isoalog.kafka.topic = ms-zte-crm-app-nj

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Methods\u7684\u53C2\u6570,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3AGET,POST,DELETE,PUT,OPTIONS
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = GET,POST,DELETE
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Methods =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Origin,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3A*.zte.com.cn
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = https://lcap.test.zte.com.cn,http://lcap.dev.zte.com.cn(\u53EF\u914D\u7F6E\u591A\u4E2A\u57DF\u540D\uFF0C\u9700\u8981\u52A0\u4E0Ahttp/https\u8BF7\u6C42)
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Origin =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Headers,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3A*
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = x-requested-with
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Headers =

#prometheus\u63A5\u53E3\u6E17\u900F\u6D4B\u8BD5\u654F\u611F\u4FE1\u606F\u6CC4\u9732\u6CBB\u7406\uFF08\u6CE8\u610Froutes[*]\u5E8F\u53F7,\u9700\u8981\u6309\u7167\u5B9E\u9645\u60C5\u51B5\u6B63\u5E8F\u4FEE\u6539\uFF09
msa.gateway.routes[3].id = infoRoute
msa.gateway.routes[3].stripPrefix = false
msa.gateway.routes[3].uri = http://127.0.0.1:${management.server.port}/${spring.application.name}/
msa.gateway.routes[3].predicates[0] = Path=/info
msa.gateway.routes[4].id = zte-crm-iepms-docfile
msa.gateway.routes[4].uri = lb://zte-crm-iepms-docfile/v1
msa.gateway.routes[4].predicates[0] = Path=/zte-crm-iepms-docfile/**
msa.gateway.routes[4].stripPrefix = false

#msa\u52A0\u5BC6
msa.ccs.encrypt.factor = ENC(xM0D70QnusVKNzFL3DsP1wSWTASsSqc2KAbthIN2TX9HBlXD+5oc4yesfqF02kYHMF6LC+UwfAQ=)
msa.encrypt.datakey = fhlibybPv8UY0qLOqf+BwHLL/KKjCjfgXDPxqLLSMzJiu3bDE1IHnAE3I41GbtTWf0nQEelUDDrBDs4nmCm/Pw==
msa.rootkey.factor1 = zte-iccp-itech-demobff-prod


#kms\u76F8\u5173\u52A0\u5BC6\u53C2\u6570\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms.dt.zte.com.cn:8443
msa.security.server.dt-kms.appName = ENC(CFG7UKVZGRp6ahuac5JOhquWxUE9ntBprKKyqATchzyZjHaOKKw3MbU=)
msa.security.server.dt-kms.userId = ENC(W/BjyH/DedPkDnE/LmomwWWufXfYQO3K***************************=)
msa.security.server.dt-kms.appSecret = ENC(5kjMT1O7q1krvvd6a3brvyBq8oPWlLrf0A2BI33JpwjICT1NE3KeyxNHwvOmpHc2CED7JSUJExY30sLxXFk8lXW7SqgOY4EP7Pk=)
msa.security.server.dt-kms.defaultMasterKey = itechcloud

#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:demobff:demobff:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = ODFkN2JmNGM1ZDFhNDg1MGNlZjE0YTg3YmI4ZWYyYjg3NzNkMTk0MTdkZjNlMjE3MTdmZjdjZmIwNjc3ZTYzNTYxMjQ4NGJjNTUwNTQyZTJkMjk1OGRhM2Q1NjAwNWFk$$NWFiNDI4MzA4OTM0NGY3NDNjMDZjODBkMmIxNzFlY2Y=&&YzljMjEzZGYtMWRmMy00ZTBhLWFiYjItYjA3NTVkM2NiNWRi

#\u4EE5\u4E0B\u53C2\u6570\u9700\u91CD\u65B0\u7528kms\u52A0\u5BC6
uac.sdk.accessKey = KENC(4lGQvjYTEApmn4B3vWgbLVeMxtPiPEnviUiZDuwoDFH0rZ6Ku+h1Z4q+lttwjrgR$$8Gf7Rz+9jicMx6C1)
uac.sdk.clientSecret = KENC(35gPLTQ1bHOlXCJITyrAtsU6Z8rwctztXgPaiDSQgk0Ksc6hFzP83quES5uj3gEMioZyVIZ7qZicEExR+v/WW2UICH/oX6Barex37n0YVBo=$$w9/iSaHK8BCxS5fv)
uac.sdk.accessSecret = KENC(TIpzv7M+wRUSd+1CMGwmRC2jFiMtiABqXeYaHXJXuBSe3nm8ljp5hc6Ixyk230zClLfxwe5zGG7uCov/+7jhC6O0Tz0NfVDyqODBJPWLKkA=$$4djVKo4f/Jww9qSL)
upp.auth.productSecretKey = KENC(a+FFLk5k8MqfOjKmivu3blOcMSM7fDaav6lsSiggcqA1KDff0MSZpvdhYLbNkSME$$hVzeZnbkNV4SRkND)
#uac\u79D8\u94A5\u53D8\u66F4\uFF0C\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/c10edd9e029648b487c9247897376977/view
# \u9700\u8981\u8FDB\u884Ckms\u52A0\u5BC6\uFF0C\u751F\u4EA7\u914D\u7F6E\u8054\u7CFBuac\u56E2\u961F\u738B\u864E\u83B7\u53D6
uac.sdk.token.encryptKey = KENC(dzBiPkIfvE7Evn3Dt80707hvPysHqGkEKAk1qDOi3Tc=$$D5vZLwy5Ubq7VPpf)
uac.sdk.appAuth.encryptKey = KENC(HM2jIeYmRaFbWSCX2Apmy+iiOFfK9pN5FPZ85/Af6/c=$$elPhUDpzQrCcW4BZ)