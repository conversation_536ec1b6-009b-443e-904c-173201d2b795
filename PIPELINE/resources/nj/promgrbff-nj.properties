upp.kafka.topic = sec-upp-api
upp.kafka.bootstrap-servers = kafka-upp-001.dt.zte.com.cn:9092
upp.kafka.consumer.enableAutoCommit = false
upp.kafka.consumer.autoOffsetReset = latest
# \u4E1A\u52A1\u540D\u79F0\uFF0C\u4FDD\u8BC1\u552F\u4E00\uFF0C\u63A8\u8350\u4F7F\u7528\u4E1A\u52A1\u65B9\u670D\u52A1\u540D\u5373\u53EF
upp.kafka.consumer.groupId = zte-iccp-itech-promgrbff
upp.kafka.listen.enabled = true
upp.auth.encryptor = KENC(s+hLIKy4AWPolJb54OJbZE9BvEpzhnadiJkNFNwzAe280ABxe5mswOHxHgmo0GJ53pTLODixxpxCd6KI2Hf+gOH197K3pDoIj2LJ6KZl9KBs1rQXQxl3T7FhJXHi7csyjxiHfO0cgikaMsL2dwYHf6VXmuiqpllKB+r+H6+0+JJyC5OOkF2NZJ22HLeCge4CPxkspMunCpjj9UiAS4GiVAPa3GxxZ5E/ZufFDwYL0c3IzeNFJr4uOYUcYRdqS9uYziJxV2iQzpPH3vt4A/h5j2+LB147bVV/WDEjug/NgHSvtKFqPSHEJA==$$rKEEoajHpsETS+c9)
upp.sha256.salt = KENC(whFma41Zhb7KycoLIrPIfdfqAZ3r4Td1REhPfahyLzkNyw4Dj99tAuzrsLjfmDnn4DTjPOrFxarREgC8t7NzaOd6sEm+DZpst3+PBhf3D1Ruer/BMlarVKGp4n8h/EALEtIbILfWmMdBYVR0qwKIgCMtgU+6+t8/Dv0C9tt7FR1gFQ735Qiuyd9iew7953Ie$$3BOOr22WDn+5evb/)

#MSB
register.address = nj-msr.msp.zte.com.cn:10081
register.node.ip = ***********
register.node.port = 80
servicecenter = msb
#\u670D\u52A1\u7248\u672C
register.version = v1


# msa gateway \u914D\u7F6E
msa.gateway.routes[0].id = ucsRoute
msa.gateway.routes[0].uri = forward:/ucs/
msa.gateway.routes[0].stripPrefix = true
msa.gateway.routes[0].predicates[0] = Path=/ucs/**
msa.gateway.routes[1].id = zte-iccp-itech-promgrapi
msa.gateway.routes[1].uri = lb://zte-iccp-itech-promgrapi/v1
msa.gateway.routes[1].predicates[0] = Path=/zte-iccp-itech-promgrapi/**
msa.gateway.routes[2].id = uac
msa.gateway.routes[2].stripPrefix = true
msa.gateway.routes[2].uri = forward:/uac/
msa.gateway.routes[2].predicates[0] = Path=/uac/**
msa.gateway.routes[3].id = infoRoute
msa.gateway.routes[3].uri = http://127.0.0.1:${management.server.port}/${spring.application.name}/
msa.gateway.routes[3].stripPrefix = false
msa.gateway.routes[3].predicates[0] = Path=/info

msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-itechcloud

#\u7EDF\u4E00\u6743\u9650\u5BF9\u63A5\u83B7\u53D6\u6743\u9650\u914D\u7F6E \u5BF9\u5E94\u53C2\u6570\u54A8\u8BE2 \u5F6D\u7EC610236545 \u4E0D\u8D70\u7EDF\u4E00\u6743\u9650\u4E0D\u914D\u7F6E
upp.auth.productId = 570056
upp.auth.moduleId = 291020441815
upp.auth.tenantId = 2


#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-authbff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-authbff
upp.auth.authUrl = http://uac.zte.com.cn/zte-sec-upp-authbff

#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-bff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-bff
upp.auth.manageUrl = http://uac.zte.com.cn/zte-sec-upp-bff


uac.sdk.tenantId = 10001
uac.sdk.appId = 291020441815
uac.sdk.issuer = https://uac.zte.com.cn/zte-sec-uac-iportalbff/oidc/10001
uac.sdk.baseUrl = https://itech.zte.com.cn/zte-iccp-itech-promgrbff
uac.sdk.clientId = 291020441815



# \u751F\u4EA7\u914D\u7F6Eprod\uFF0C\u5F00\u53D1\u6D4B\u8BD5\u73AF\u5883\u914D\u7F6Edev\u6216\u4E0D\u914D\u7F6E
uac.sdk.env = prod
msa.gateway.tokenAuthEnable = false
msa.gateway.uac.enabled = false
uac.sdk.responseRule = msa
uac.sdk.cookie.httpOnly = true
uac.sdk.gateAuth.externalAccessEnabled = true
uac.sdk.cookie.secure = true
uac.sdk.store = redis
cacheCloud.server = http://cache.zcloud.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4


# \u5176\u4ED6\u914D\u7F6E
# swagger\u6587\u6863\u5730\u5740\u8BBF\u95EE\u914D\u7F6E\uFF0C\u6D4B\u8BD5\u73AF\u5883\u7532\u4E59\u914D\u7F6Etrue\uFF0C\u751F\u4EA7\u73AF\u5883\u5EFA\u8BAE\u914D\u7F6Efalse
springfox.documentation.enabled = false
# \u81EA\u5B9A\u4E49\u7EC4\u4EF6\u5E93\u5305\u4E0A\u4F20\u5927\u5C0F
ui.config.app.package.upload.size = 200
ui.config.project.watermarkURL = https://itas.dt.zte.com.cn:443/inject-font/config.js, https://itas.dt.zte.com.cn:443/inject-font/font.js
ui.config.project.privacyPolicyBaseUrl = https://uac.zte.com.cn
#\u5BA2\u6237\u7AEF\u63D0\u4EA4\u7684\u8868\u5355\u8BF7\u6C42\u6765\u6E90\u8FDB\u884C\u5408\u6CD5\u6027\u6821\u9A8C\uFF0C\u9632\u6B62CSRF\u653B\u51FB
common.referrerMap[refererPatterns] = *.zte.com.cn


# \u8DF3\u8F6C\u767B\u5F55uac\u73AF\u5883\u6807\u8BC6
# \u8DF3\u8F6C\u767B\u5F55uac\u6D4B\u8BD5\u73AF\u5883uactest.zte.com.cn:5555\uFF1Atest
# \u8DF3\u8F6C\u767B\u5F55uac81\u73AF\u5883uactest.zte.com.cn\uFF1Auat
# \u8DF3\u8F6C\u767B\u5F55uac\u751F\u4EA7\u73AF\u5883\uFF0C\u9ED8\u8BA4\u4E3A\u7A7A\uFF0C\u65E0\u9700\u914D\u7F6E
uac.login.env =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Methods\u7684\u53C2\u6570,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3AGET,POST,DELETE,PUT,OPTIONS
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = GET,POST,DELETE
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Methods =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Origin,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3A*.zte.com.cn
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = https://lcap.test.zte.com.cn,http://lcap.dev.zte.com.cn(\u53EF\u914D\u7F6E\u591A\u4E2A\u57DF\u540D\uFF0C\u9700\u8981\u52A0\u4E0Ahttp/https\u8BF7\u6C42)
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Origin =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Headers,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3A*
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = x-requested-with
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Headers =


#msa\u52A0\u5BC6
msa.ccs.encrypt.factor = ENC(pkKkOyDcRyYhv055/yc/7JMvyxRS5Q/BEPLzdk6FPJnYPm7xpMI0SNlNVysGWNzcS8hDpuym2l0=)
msa.encrypt.datakey = LKqP5cB0u+GCMZvgmWW87lB2y/WiEbDaVYhO++coFAKsZdn8V0/02Fs8NscaZDdLEACvWHp96He4Vy12ANAk7w==
msa.rootkey.factor1 = zte-iccp-itech-promgrbff-prod

msa.security.server.dt-kms.enable = true
#kms\u914D\u7F6E\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms.dt.zte.com.cn:8443
msa.security.server.dt-kms.appName = ENC(RvZXeo+OX7cMH2UPebtDalO4zcXsJEgU1zx3lD/LYasX25+zJfZQmIs=)
msa.security.server.dt-kms.userId = ENC(03DUJjEmwqEYtFMjQ2UIgP4LKFfnbyK/n+P3IFj9vOuqaLZwIKKMwW+/kM8=)
msa.security.server.dt-kms.appSecret = ENC(/CzVhs8jUt9o3QAFP+6Uh3b8NrMghXglbVgWUyscfHHaQ2sWQKX/FllbmDHzxyUu6ZrPhICSQcH71ENItcxCOaepR8KwIaf9fdk=)
msa.security.server.dt-kms.defaultMasterKey = itechcloud

#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:promgrbff:promgrbff:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = MzlkMzRmZjc1ODBmNzdkZjU3M2IwMzNjMzM2YjYwNTBkOTFiYzU0ZTIzYWY3ZTllZmM3NTVhZTA4ZjUxMDM4OGFhMzBlM2QzZjEzNGE1N2RmZjMzNTZjMjg2NGU1ZGZk$$YzFlZGMzNjFkMjZjZjQyZGQ5YTYxOTYyNTNjZjMzOGE=&&NDBmZTgyMzEtZTdkNi00N2YxLWI2MTUtYTE0MGU5MzM1ODcx

# \u4EE5\u4E0B\u53C2\u6570\u9700\u91CD\u65B0\u7528kms\u52A0\u5BC6

uac.sdk.clientSecret = KENC(ywSm1Phxbj773FVjRiYcs+KlahyTYSFIm1TiIPGS3pAqkU9z3TbayFA0pulI55Qx7BUGnFSdCXST/6lYha/o01cLK5mUsuBirBnKw2GeQDg=$$ffhfuA0d+j5NvErU)
uac.sdk.accessKey = KENC(r3xbS5tSnmFZJ783pIDyvhYVDBWJOgTNRPmBQ2NSvEHXahVm/S653gxFPXcLrvDI$$tzH9ZwKwgI5NsrY7)
uac.sdk.accessSecret = KENC(z+f2F8iHkJZxtMkCgeo6XCSy+/0lT7N8Km1PSlM5mKsY7Q7OumKcx1XRrCerl7I9QdvA8Vphst8WFssvdDQ3HfNAxq3bxXzkLjbOUJ1yRIA=$$9++6jBBm7CwcNfaW)

#uac\u79D8\u94A5\u53D8\u66F4\uFF0C\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/c10edd9e029648b487c9247897376977/view
# \u9700\u8981\u8FDB\u884Ckms\u52A0\u5BC6\uFF0C\u751F\u4EA7\u914D\u7F6E\u8054\u7CFBuac\u56E2\u961F\u738B\u864E\u83B7\u53D6
uac.sdk.appAuth.encryptKey = KENC(StoWfB2WOy0/R4cUa7C5KS0q7jTnXS+9BQx5fsYxuOc=$$GdQVocttnVTYoB34)
uac.sdk.token.encryptKey = KENC(1An1IXtAwGjd3clFtrcz3+vDYWegi2cT1WngSJPv6Gs=$$iOIcQwkZ5J9bA0C7)

# \u4EA7\u54C1\u5BC6\u94A5key\uFF0C\u5411\u7EDF\u4E00\u6743\u9650\u7D22\u8981\uFF08\u533A\u5206\u73AF\u5883\uFF09
upp.auth.productSecretKey = KENC(DqqFiH85497WMB5IpJ59vBMzK2FFb+4ubBwFydiO5q5s44jCM++3Hyh4JbHqCovD$$RrlZP9BK7wGJTO77)