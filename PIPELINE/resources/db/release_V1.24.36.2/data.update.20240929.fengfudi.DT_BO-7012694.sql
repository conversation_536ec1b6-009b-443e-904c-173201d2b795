UPDATE batch_network_assignment_batch_result e
JOIN batch_network_assignment m
ON e.id = m.id
SET e.batch_result_last_update_time = IFNULL(m.last_modified_time, current_timestamp);

ALTER TABLE batch_network_assignment_batch_result MODIFY batch_result_last_update_time datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp comment '反馈操作结果-修改时间';

UPDATE subcontractor_batch_task_batch_result e
JOIN subcontractor_batch_task m
ON e.id = m.id
SET e.batch_result_last_update_time = IFNULL(m.last_modified_time, current_timestamp);

ALTER TABLE subcontractor_batch_task_batch_result MODIFY batch_result_last_update_time datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp comment '反馈操作结果-修改时间';