UPDATE office_duty_personnel SET customer_flag = '[{"value":"CU","text":{"zh_CN":"中国联通","type":"i18n","en_US":""}}]'
WHERE customer_flag = '中国联通';

-- 1.修改办事处值班人员表客户标识字段string类型 - > json类型
ALTER TABLE office_duty_personnel MODIFY customer_flag JSON;

-- 2.修改技术管理任务产品分类字段json - > string
ALTER TABLE technical_management_task MODIFY task_category VARCHAR(100);

-- 创建备份表，将部分需要同步的数据保存备份
CREATE TABLE IF NOT EXISTS itech_bak.technical_management_task_2025_05_06
SELECT id, task_category FROM technical_management_task
WHERE task_category in ('[{"text": {"en_US": "", "zh_CN": "通用任务"}, "value": "1"}]','[{"text": {"type": "i18n", "en_US": "", "zh_CN": "通用任务"}, "value": "1"}]','[{"text": {"type": "i18n", "en_US": "", "zh_CN": "自定义任务"}, "value": "2"}]');

-- 2.1 历史数据维护
update technical_management_task tmt
join itech_bak.technical_management_task_2025_05_06 backup ON tmt.id = backup.id
set tmt.task_category = '1'
where tmt.task_category in (
    '[{"text": {"en_US": "", "zh_CN": "通用任务"}, "value": "1"}]',
    '[{"text": {"type": "i18n", "en_US": "", "zh_CN": "通用任务"}, "value": "1"}]'
);

update technical_management_task tmt
join itech_bak.technical_management_task_2025_05_06 backup ON tmt.id = backup.id
set tmt.task_category = '2'
where tmt.task_category = '[{"text": {"type": "i18n", "en_US": "", "zh_CN": "自定义任务"}, "value": "2"}]';
