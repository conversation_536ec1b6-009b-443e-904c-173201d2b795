CREATE TABLE IF NOT EXISTS `assignment_new_20250122` LIKE `assignment`;

SET @time = now();

INSERT IGNORE INTO `assignment_new_20250122`
    SELECT *
    FROM `assignment`;

-- BEGIN 实际表变更
ALTER TABLE `assignment_new_20250122`
    ADD KEY ix_assignment_type_ext((CAST(assignment_type_ext AS UNSIGNED ARRAY))),
    ADD KEY ix_current_processor_employee_ext((CAST(current_processor_employee_field_ext AS UNSIGNED ARRAY)),`create_time`),
    ADD KEY ix_create_time(create_time);
-- END 实际表变更

RENAME TABLE `assignment` TO `assignment_bak_20250122`;
RENAME TABLE `assignment_new_20250122` TO `assignment`;

INSERT INTO `assignment`
(id, bill_id, assignment_name, assignment_code, assignment_status, assignment_type, assignment_type_ext, marketing, marketing_ext, representative_office, representative_office_ext, product_classification, product_classification_ext, network, network_ext, responsible, responsible_ext, tenant_id, create_by, create_time, last_modified_by, last_modified_time, is_deleted, is_draft, customer_classification, customer_classification_ext, product_management_team, product_management_team_ext, company, company_ext, current_processor, current_processor_ext, current_progress, approval_task_flag, bill_type, bill_type_ext, entity_id, current_processor_employee_field, current_processor_employee_field_ext, responsible_employee_field, responsible_employee_field_ext, approval_task_flag_ext)
SELECT id, bill_id, assignment_name, assignment_code, assignment_status, assignment_type, assignment_type_ext, marketing, marketing_ext, representative_office, representative_office_ext, product_classification, product_classification_ext, network, network_ext, responsible, responsible_ext, tenant_id, create_by, create_time, last_modified_by, last_modified_time, is_deleted, is_draft, customer_classification, customer_classification_ext, product_management_team, product_management_team_ext, company, company_ext, current_processor, current_processor_ext, current_progress, approval_task_flag, bill_type, bill_type_ext, entity_id, current_processor_employee_field, current_processor_employee_field_ext, responsible_employee_field, responsible_employee_field_ext, approval_task_flag_ext
    FROM `assignment_bak_20250122`
    WHERE last_modified_time >= @time
ON DUPLICATE KEY UPDATE
    bill_id = VALUES(bill_id),
    assignment_name = VALUES(assignment_name),
    assignment_code = VALUES(assignment_code),
    assignment_status = VALUES(assignment_status),
    assignment_type = VALUES(assignment_type),
    assignment_type_ext = VALUES(assignment_type_ext),
    marketing = VALUES(marketing),
    marketing_ext = VALUES(marketing_ext),
    representative_office = VALUES(representative_office),
    representative_office_ext = VALUES(representative_office_ext),
    product_classification = VALUES(product_classification),
    product_classification_ext = VALUES(product_classification_ext),
    network = VALUES(network),
    network_ext = VALUES(network_ext),
    responsible = VALUES(responsible),
    responsible_ext = VALUES(responsible_ext),
    tenant_id = VALUES(tenant_id),
    create_by = VALUES(create_by),
    create_time = VALUES(create_time),
    last_modified_by = VALUES(last_modified_by),
    last_modified_time = VALUES(last_modified_time),
    is_deleted = VALUES(is_deleted),
    is_draft = VALUES(is_draft),
    customer_classification = VALUES(customer_classification),
    customer_classification_ext = VALUES(customer_classification_ext),
    product_management_team = VALUES(product_management_team),
    product_management_team_ext = VALUES(product_management_team_ext),
    company = VALUES(company),
    company_ext = VALUES(company_ext),
    current_processor = VALUES(current_processor),
    current_processor_ext = VALUES(current_processor_ext),
    current_progress = VALUES(current_progress),
    approval_task_flag = VALUES(approval_task_flag),
    bill_type = VALUES(bill_type),
    bill_type_ext = VALUES(bill_type_ext),
    entity_id = VALUES(entity_id),
    current_processor_employee_field = VALUES(current_processor_employee_field),
    current_processor_employee_field_ext = VALUES(current_processor_employee_field_ext),
    responsible_employee_field = VALUES(responsible_employee_field),
    responsible_employee_field_ext = VALUES(responsible_employee_field_ext),
    approval_task_flag_ext = VALUES(approval_task_flag_ext);
