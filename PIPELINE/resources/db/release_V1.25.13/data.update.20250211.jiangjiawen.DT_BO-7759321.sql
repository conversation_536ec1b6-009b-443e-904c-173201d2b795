-- 创建临时表
-- 打卡记录、打卡任务操作等级临时表
CREATE TABLE IF NOT EXISTS itech_bak.fp_operation_level_DT_BO_7759321
SELECT id, operation_level FROM clock_in_reviews
WHERE JSON_OVERLAPS (operation_level_ext ,'["3","2","1"]')
and is_deleted = 0

UNION ALL

SELECT id,operation_level FROM clock_in_task
WHERE JSON_OVERLAPS (operation_level_ext ,'["3","2","1"]')
and is_deleted = 0;


-- 打卡复盘任务状态临时表
CREATE TABLE IF NOT EXISTS itech_bak.fp_assignment_status_DT_BO_7759321
SELECT id, replay_status FROM clock_in_reviews
WHERE JSON_OVERLAPS (replay_status_ext ,'["13","14","15","16","8","4"]');

-- CCN默认授权文件申请【单据状态】临时表
CREATE TABLE IF NOT EXISTS itech_bak.apply_ccn_authorization_DT_BO_7759321
SELECT id, ad_status FROM apply_ccn_authorization
WHERE JSON_OVERLAPS (ad_status_ext ,'["1","2","3"]');

-- 打卡复盘表【操作等级】
UPDATE clock_in_reviews SET operation_level ='[{"text": {"en_US": "Critical", "value": "3", "zh_CN": "关键"}, "value": "3"}]'
WHERE JSON_CONTAINS (operation_level_ext ,'["3"]') ;
UPDATE clock_in_reviews SET operation_level ='[{"text": {"en_US": "Important", "value": "2", "zh_CN": "重要"}, "value": "2"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["2"]') ;
UPDATE clock_in_reviews SET operation_level ='[{"text": {"en_US": "Common", "value": "1", "zh_CN": "一般"}, "value": "1"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["1"]') ;

-- 打卡任务表【操作等级】
UPDATE clock_in_task SET operation_level ='[{"text": {"en_US": "Critical", "value": "3", "zh_CN": "关键"}, "value": "3"}]'
WHERE JSON_CONTAINS (operation_level_ext ,'["3"]') ;
UPDATE clock_in_task SET operation_level ='[{"text": {"en_US": "Important", "value": "2", "zh_CN": "重要"}, "value": "2"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["2"]') ;
UPDATE clock_in_task SET operation_level ='[{"text": {"en_US": "Common", "value": "1", "zh_CN": "一般"}, "value": "1"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["1"]') ;


-- 更新打卡复盘任务状态
UPDATE clock_in_reviews SET replay_status ='[{"text": {"en_US": "Preliminary Review", "value": "13", "zh_CN": "复盘初审"}, "value": "13"}]'
WHERE JSON_CONTAINS (replay_status_ext ,'["13"]') ;
UPDATE clock_in_reviews SET replay_status ='[{"text": {"en_US": "Review Submitted", "value": "14", "zh_CN": "复盘提交"}, "value": "14"}]'
WHERE JSON_CONTAINS (replay_status_ext ,'["14"]') ;
UPDATE clock_in_reviews SET replay_status ='[{"text": {"en_US": "In Reviewing", "value": "15", "zh_CN": "复盘审核"}, "value": "15"}]'
WHERE JSON_CONTAINS (replay_status_ext ,'["15"]') ;
UPDATE clock_in_reviews SET replay_status ='[{"text": {"en_US": "Review Confirmed", "value": "16", "zh_CN": "审核确认"}, "value": "16"}]'
WHERE JSON_CONTAINS (replay_status_ext ,'["16"]') ;
-- 设计态码值错误，生产和测试状态均为8，但实际应该为17
UPDATE clock_in_reviews SET replay_status ='[{"text": {"en_US": "Being Rectified", "value": "17", "zh_CN": "整改中"}, "value": "17"}]'
WHERE JSON_CONTAINS (replay_status_ext ,'["8"]') ;
UPDATE clock_in_reviews SET replay_status ='[{"text": {"en_US": "Closed", "value": "4", "zh_CN": "已关闭"}, "value": "4"}]'
WHERE JSON_CONTAINS (replay_status_ext ,'["4"]') ;


-- 更新CCN默认授权文件申请【单据状态】
UPDATE apply_ccn_authorization SET ad_status ='[{"text": {"en_US": "To be Generated", "value": "1", "zh_CN": "待生成"}, "value": "1"}]'
WHERE JSON_CONTAINS (ad_status_ext ,'["1"]') ;
UPDATE apply_ccn_authorization SET ad_status ='[{"text": {"en_US": "To be Fed Back", "value": "2", "zh_CN": "待反馈"}, "value": "2"}]'
WHERE  JSON_CONTAINS (ad_status_ext ,'["2"]') ;
UPDATE apply_ccn_authorization SET ad_status ='[{"text": {"en_US": "Closed", "value": "3", "zh_CN": "已关闭"}, "value": "3"}]'
WHERE  JSON_CONTAINS (ad_status_ext ,'["3"]') ;


