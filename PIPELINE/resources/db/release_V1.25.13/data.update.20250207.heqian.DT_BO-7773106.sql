-- 更新历史任务中心权限申请模块字段英文展示
 UPDATE assignment_permission_ex  SET module ='[{"text": {"en_US": "Technology Delivery Cloud Platform", "zh_CN": "技术交付云平台"}, "value": "iTechCloud"}]'
 WHERE  JSON_CONTAINS (module ,'[{"text": {"zh_CN": "技术交付云平台"}, "value": "iTechCloud"}]') ;

-- 更新操作等级英文名称 assignment_network_change_ex、change_order、subcontractor_oc
-- 创建临时表
CREATE TABLE IF NOT EXISTS itech_bak.operation_level_2025_02_10
SELECT id, operation_level FROM assignment_network_change_ex
WHERE JSON_OVERLAPS (operation_level_ext ,'["3","2","1"]')
UNION ALL
SELECT id,operation_level FROM change_order
WHERE JSON_OVERLAPS (operation_level_ext ,'["3","2","1"]')
UNION ALL
SELECT id,operation_level FROM subcontractor_oc
WHERE JSON_OVERLAPS (operation_level_ext ,'["3","2","1"]') ;

-- 关键
UPDATE assignment_network_change_ex  SET operation_level ='[{"text": {"en_US": "Critical", "value": "3", "zh_CN": "关键"}, "value": "3"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["3"]') ;
UPDATE change_order  SET operation_level ='[{"text": {"en_US": "Critical", "value": "3", "zh_CN": "关键"}, "value": "3"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["3"]') ;
UPDATE subcontractor_oc  SET operation_level ='[{"text": {"en_US": "Critical", "value": "3", "zh_CN": "关键"}, "value": "3"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["3"]') ;
-- 重要
UPDATE assignment_network_change_ex  SET operation_level ='[{"text": {"en_US": "Important", "value": "2", "zh_CN": "重要"}, "value": "2"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["2"]') ;
UPDATE change_order  SET operation_level ='[{"text": {"en_US": "Important", "value": "2", "zh_CN": "重要"}, "value": "2"}]'
WHERE  JSON_CONTAINS (operation_level_ext,'["2"]') ;
UPDATE subcontractor_oc  SET operation_level ='[{"text": {"en_US": "Important", "value": "2", "zh_CN": "重要"}, "value": "2"}]'
WHERE  JSON_CONTAINS (operation_level_ext,'["2"]') ;
-- 一般
UPDATE assignment_network_change_ex  SET operation_level ='[{"text": {"en_US": "Common", "value": "1", "zh_CN": "一般"}, "value": "1"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["1"]') ;
UPDATE change_order  SET operation_level ='[{"text": {"en_US": "Common", "value": "1", "zh_CN": "一般"}, "value": "1"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["1"]') ;
UPDATE subcontractor_oc  SET operation_level ='[{"text": {"en_US": "Common", "value": "1", "zh_CN": "一般"}, "value": "1"}]'
WHERE  JSON_CONTAINS (operation_level_ext ,'["1"]') ;