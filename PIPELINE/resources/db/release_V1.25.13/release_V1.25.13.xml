<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">
    <changeSet id="release_V1.25.13_tag" author="10284287">
        <tagDatabase tag="release_V1.25.13_tag"/>
    </changeSet>

    <changeSet id="release_V1.25.13_10284287" author="10284287">
        <sqlFile path="db/release_V1.25.13/data.update.20250122.fengfudi.DT_BO-7764345.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.13/rollback/data.rollback.20250122.fengfudi.DT_BO-7764345.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>

    <changeSet id="release_V1.25.13_6092003901" author="6092003901">
        <sqlFile path="db/release_V1.25.13/data.update.20250207.heqian.DT_BO-7773106.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.13/rollback/data.rollback.20250207.heqian.DT_BO-7773106.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>

    <changeSet id="release_V1.25.13_6781000092" author="6781000092">
        <sqlFile path="db/release_V1.25.13/data.update.20250211.jiangjiawen.DT_BO-7759321.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.13/rollback/data.rollback.20250211.jiangjiawen.DT_BO-7759321.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>

    <changeSet id="release_V1.25.13_10309939" author="10309939">
        <sqlFile path="db/release_V1.25.13/data.update.20250212.kangtianshuo.DT_BO-7773052.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.13/rollback/data.rollback.20250212.kangtianshuo.DT_BO-7773052.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
