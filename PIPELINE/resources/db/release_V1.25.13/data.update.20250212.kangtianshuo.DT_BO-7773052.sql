-- 更新任务类型 assignment_type
CREATE TABLE IF NOT EXISTS itech_bak.assignment_2025_02_11
SELECT id, assignment_type FROM assignment;

UPDATE assignment a
INNER JOIN itech_bak.assignment_2025_02_11 a1 ON a.id = a1.id
SET a.assignment_type = (
    CASE
        WHEN JSON_CONTAINS (a.assignment_type_ext, JSON_ARRAY('1')) THEN '[{"text": {"en_US": "Network Change Task", "value": "1", "zh_CN": "网络变更任务"}, "value": "1"}]'
        WHEN JSON_CONTAINS (a.assignment_type_ext, JSON_ARRAY('2')) THEN '[{"text": {"en_US": "Batch Task of Network Change", "value": "2", "zh_CN": "网络变更 - 批次任务"}, "value": "2"}]'
        WHEN JSON_CONTAINS (a.assignment_type_ext, JSON_ARRAY('3')) THEN '[{"text": {"en_US": "Technical Management Task", "value": "3", "zh_CN": "技术管理任务"}, "value": "3"}]'
        WHEN JSON_CONTAINS (a.assignment_type_ext, JSON_ARRAY('4')) THEN '[{"text": {"en_US": "Technical Management Subtask", "value": "4", "zh_CN": "技术管理 - 子任务"}, "value": "4"}]'
        WHEN JSON_CONTAINS (a.assignment_type_ext, JSON_ARRAY('5')) THEN '[{"text": {"en_US": "Fault Management Task", "value": "5", "zh_CN": "故障管理任务"}, "value": "5"}]'
        WHEN JSON_CONTAINS (a.assignment_type_ext, JSON_ARRAY('6')) THEN '[{"text": {"en_US": "Partner Network Change Task", "value": "6", "zh_CN": "合作方网络变更任务"}, "value": "6"}]'
        WHEN JSON_CONTAINS (a.assignment_type_ext, JSON_ARRAY('7')) THEN '[{"text": {"en_US": "Batch Task of Partner Network Change", "value": "7", "zh_CN": "合作方网络变更 - 批次任务"}, "value": "7"}]'
        WHEN JSON_CONTAINS (a.assignment_type_ext, JSON_ARRAY('8')) THEN '[{"text": {"en_US": "Permission Application", "value": "8", "zh_CN": "iTechCloud权限申请"}, "value": "8"}]'
        ELSE '[{"text": {"en_US": "Review Task", "value": "9", "zh_CN": "打卡复盘"}, "value": "9"}]'
    END
)
WHERE a.id = a1.id;

-- 更新 触发类型基准系数配置 trigger_type / first_time_apply / operation_within_busi
CREATE TABLE IF NOT EXISTS itech_bak.trigger_type_baseline_coefficient_config_2025_02_12
SELECT id, trigger_type, first_time_apply, operation_within_busi FROM trigger_type_baseline_coefficient_config;

UPDATE trigger_type_baseline_coefficient_config a
INNER JOIN itech_bak.trigger_type_baseline_coefficient_config_2025_02_12 a1 ON a.id = a1.id
SET a.trigger_type = (
    CASE
        WHEN JSON_CONTAINS (a.trigger_type_ext, JSON_ARRAY('1')) THEN '[{"text": {"en_US": "Engineering Delivery", "value": "1", "zh_CN": "工程交付"}, "value": "1"}]'
        ELSE '[{"text": {"en_US": "Network Maintenance", "value": "2", "zh_CN": "网络维护"}, "value": "2"}]'
    END
)
WHERE a.id = a1.id;

UPDATE trigger_type_baseline_coefficient_config a
INNER JOIN itech_bak.trigger_type_baseline_coefficient_config_2025_02_12 a1 ON a.id = a1.id
SET a.first_time_apply = (
    CASE
        WHEN JSON_CONTAINS (a.first_time_apply_ext, JSON_ARRAY('Y')) THEN '[{"text": {"en_US": "Yes", "value": "Y", "zh_CN": "是"}, "value": "Y"}]'
        ELSE '[{"text": {"en_US": "No", "value": "N", "zh_CN": "否"}, "value": "N"}]'
    END
)
WHERE a.id = a1.id;

UPDATE trigger_type_baseline_coefficient_config a
INNER JOIN itech_bak.trigger_type_baseline_coefficient_config_2025_02_12 a1 ON a.id = a1.id
SET a.operation_within_busi = (
    CASE
        WHEN JSON_CONTAINS (a.operation_within_busi_ext, JSON_ARRAY('Y')) THEN '[{"text": {"en_US": "Yes", "value": "Y", "zh_CN": "是"}, "value": "Y"}]'
        ELSE '[{"text": {"en_US": "No", "value": "N", "zh_CN": "否"}, "value": "N"}]'
    END
)
WHERE a.id = a1.id;

-- 更新 标准方案 task_type / scheme_type
CREATE TABLE IF NOT EXISTS itech_bak.standard_scheme_copy_2025_02_12
SELECT id, task_type, scheme_type, `language` FROM standard_scheme_copy;

UPDATE standard_scheme_copy ssc
INNER JOIN itech_bak.standard_scheme_copy_2025_02_12 ssc1 ON ssc.id = ssc1.id
SET ssc.task_type = '[{"text": {"en_US": "Network Change", "value": "1", "zh_CN": "网络变更"}, "value": "1"}]'
WHERE ssc.id = ssc1.id;

UPDATE standard_scheme_copy ssc
INNER JOIN itech_bak.standard_scheme_copy_2025_02_12 ssc1 ON ssc.id = ssc1.id
SET ssc.scheme_type = (
    CASE
        WHEN JSON_CONTAINS (ssc.scheme_type_ext, JSON_ARRAY('1')) THEN '[{"text": {"en_US": "Internal Standard Solution", "value": "1", "zh_CN": "内部标准方案"}, "value": "1"}]'
        WHEN JSON_CONTAINS (ssc.scheme_type_ext, JSON_ARRAY('2')) THEN '[{"text": {"en_US": "Customer Solution Template", "value": "2", "zh_CN": "客户方案模板"}, "value": "2"}]'
        ELSE '[{"text": {"en_US": "Internal Solution Template", "value": "3", "zh_CN": "内部方案模板"}, "value": "3"}]'
    END
)
WHERE ssc.id = ssc1.id;

UPDATE standard_scheme_copy ssc
INNER JOIN itech_bak.standard_scheme_copy_2025_02_12 ssc1 ON ssc.id = ssc1.id
SET ssc.`language` = (
    CASE
        WHEN JSON_CONTAINS (ssc.language_ext, JSON_ARRAY('1')) THEN '[{"text": {"en_US": "Chinese", "value": "1", "zh_CN": "中文"}, "value": "1"}]'
        ELSE '[{"text": {"en_US": "English", "value": "2", "zh_CN": "英文"}, "value": "2"}]'
    END
)
WHERE ssc.id = ssc1.id;

-- 更新 权限申请 module_name
CREATE TABLE IF NOT EXISTS itech_bak.permission_application_2025_02_13
SELECT id, module_name FROM permission_application;

UPDATE permission_application pa
INNER JOIN itech_bak.permission_application_2025_02_13 pa1 ON pa.id = pa1.id
SET pa.module_name = '[{"text": {"id": "", "code": "itech.cloud.name", "type": "i18n", "en_US": "Technology Delivery Cloud Platform", "zh_CN": "技术交付云平台"}, "value": "iTechCloud"}]'
WHERE pa.id = pa1.id;

-- 更新 操作等级值守时长与频次配置 operation_level
CREATE TABLE IF NOT EXISTS itech_bak.operation_lvl_od_duration_frequency_config_2025_02_13
SELECT id, operation_level FROM operation_lvl_od_duration_frequency_config;

UPDATE operation_lvl_od_duration_frequency_config a
INNER JOIN itech_bak.operation_lvl_od_duration_frequency_config_2025_02_13 a1 ON a.id = a1.id
SET a.operation_level = (
    CASE
        WHEN JSON_CONTAINS (a.operation_level_ext, JSON_ARRAY('1')) THEN '[{"text": {"en_US": "Common", "value": "1", "zh_CN": "一般"}, "value": "1"}]'
        WHEN JSON_CONTAINS (a.operation_level_ext, JSON_ARRAY('2')) THEN '[{"text": {"en_US": "Important", "value": "2", "zh_CN": "重要"}, "value": "2"}]'
        ELSE '[{"text": {"en_US": "Critical", "value": "3", "zh_CN": "关键"}, "value": "3"}]'
    END
)
WHERE a.id = a1.id;

-- 更新 操作场景 approval_level
CREATE TABLE IF NOT EXISTS itech_bak.operate_scene_2025_02_13
SELECT id, selectfield_approval_level FROM t_entity_operate_scene;

UPDATE t_entity_operate_scene a
INNER JOIN itech_bak.operate_scene_2025_02_13 a1 ON a.id = a1.id
SET a.selectfield_approval_level = (
    CASE
        WHEN JSON_CONTAINS (a.selectfield_approval_level_ext, JSON_ARRAY('1')) THEN '[{"text": {"en_US": "Level-1 Review", "value": "1", "zh_CN": "一级审核"}, "value": "1"}]'
        WHEN JSON_CONTAINS (a.selectfield_approval_level_ext, JSON_ARRAY('2')) THEN '[{"text": {"en_US": "Level-2 Review", "value": "2", "zh_CN": "二级审核"}, "value": "2"}]'
        WHEN JSON_CONTAINS (a.selectfield_approval_level_ext, JSON_ARRAY('3')) THEN '[{"text": {"en_US": "Level-3 Review", "value": "3", "zh_CN": "三级审核"}, "value": "3"}]'
        ELSE '[]'
    END
)
WHERE a.id = a1.id;

-- 更新 操作类型 partner_network_changes_available, radiofield_is_customize_operation_reason, operate_level
CREATE TABLE IF NOT EXISTS itech_bak.operate_type_2025_02_13
SELECT id, partner_network_changes_available, radiofield_is_customize_operation_reason, operate_level
FROM t_entity_operate_type;

UPDATE t_entity_operate_type a
INNER JOIN itech_bak.operate_type_2025_02_13 a1 ON a.id = a1.id
SET a.partner_network_changes_available = (
    CASE
        WHEN JSON_CONTAINS (a.partner_network_changes_available_ext, JSON_ARRAY('Y')) THEN '[{"text": {"en_US": "Yes", "value": "Y", "zh_CN": "是"}, "value": "Y"}]'
        ELSE '[{"text": {"en_US": "No", "value": "N", "zh_CN": "否"}, "value": "N"}]'
    END
)
WHERE a.id = a1.id;

UPDATE t_entity_operate_type a
INNER JOIN itech_bak.operate_type_2025_02_13 a1 ON a.id = a1.id
SET a.radiofield_is_customize_operation_reason = (
    CASE
        WHEN JSON_CONTAINS (a.radiofield_is_customize_operation_reason_ext, JSON_ARRAY('Y')) THEN '[{"text": {"en_US": "Yes", "value": "Y", "zh_CN": "是"}, "value": "Y"}]'
        ELSE '[{"text": {"en_US": "No", "value": "N", "zh_CN": "否"}, "value": "N"}]'
    END
)
WHERE a.id = a1.id;

UPDATE t_entity_operate_type a
INNER JOIN itech_bak.operate_type_2025_02_13 a1 ON a.id = a1.id
SET a.operate_level = (
    CASE
        WHEN JSON_CONTAINS (a.operate_level_ext, JSON_ARRAY('1')) THEN '[{"text": {"en_US": "Common", "value": "1", "zh_CN": "一般"}, "value": "1"}]'
        WHEN JSON_CONTAINS (a.operate_level_ext, JSON_ARRAY('2')) THEN '[{"text": {"en_US": "Important", "value": "2", "zh_CN": "重要"}, "value": "2"}]'
        WHEN JSON_CONTAINS (a.operate_level_ext, JSON_ARRAY('3')) THEN '[{"text": {"en_US": "Critical", "value": "3", "zh_CN": "关键"}, "value": "3"}]'
        ELSE '[]'
    END
)
WHERE a.id = a1.id;
