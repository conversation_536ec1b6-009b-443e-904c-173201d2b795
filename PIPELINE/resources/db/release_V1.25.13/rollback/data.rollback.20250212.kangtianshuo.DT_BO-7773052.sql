-- 更新任务类型 assignment_type
UPDATE assignment a
LEFT JOIN itech_bak.assignment_2025_02_11 a1 ON a.id = a1.id
SET a.assignment_type = a1.assignment_type
where a.assignment_type != a1.assignment_type;

DROP TABLE itech_bak.assignment_2025_02_11;

-- 更新 触发类型基准系数配置 trigger_type / first_time_apply / operation_within_busi
UPDATE trigger_type_baseline_coefficient_config a
LEFT JOIN itech_bak.trigger_type_baseline_coefficient_config_2025_02_12 a1 ON a.id = a1.id
SET a.trigger_type = a1.trigger_type
where a.trigger_type != a1.trigger_type;

UPDATE trigger_type_baseline_coefficient_config a
LEFT JOIN itech_bak.trigger_type_baseline_coefficient_config_2025_02_12 a1 ON a.id = a1.id
SET a.operation_within_busi = a1.operation_within_busi
where a.operation_within_busi != a1.operation_within_busi;

UPDATE trigger_type_baseline_coefficient_config a
LEFT JOIN itech_bak.trigger_type_baseline_coefficient_config_2025_02_12 a1 ON a.id = a1.id
SET a.operation_within_busi = a1.operation_within_busi
where a.operation_within_busi != a1.operation_within_busi;

DROP TABLE itech_bak.trigger_type_baseline_coefficient_config_2025_02_12;

-- 更新 标准方案 task_type / scheme_type
UPDATE standard_scheme_copy a
LEFT JOIN itech_bak.standard_scheme_copy_2025_02_12 a1 ON a.id = a1.id
SET a.task_type = a1.task_type
WHERE a.task_type != a1.task_type;

UPDATE standard_scheme_copy a
LEFT JOIN itech_bak.standard_scheme_copy_2025_02_12 a1 ON a.id = a1.id
SET a.scheme_type = a1.scheme_type
WHERE a.scheme_type != a1.scheme_type;

UPDATE standard_scheme_copy a
LEFT JOIN itech_bak.standard_scheme_copy_2025_02_12 a1 ON a.id = a1.id
SET a.`language` = a1.`language`
WHERE a.`language` != a1.`language`;

DROP TABLE itech_bak.standard_scheme_copy_2025_02_12;

-- 更新 权限申请 module_name
UPDATE permission_application a
LEFT JOIN itech_bak.permission_application_2025_02_13 a1 ON a.id = a1.id
SET a.module_name = a1.module_name
WHERE a.module_name != a1.module_name;

DROP TABLE itech_bak.permission_application_2025_02_13;

-- 更新 操作等级值守时长与频次配置 operation_level
UPDATE operation_lvl_od_duration_frequency_config a
LEFT JOIN itech_bak.operation_lvl_od_duration_frequency_config_2025_02_13 a1 ON a.id = a1.id
SET a.operation_level = a1.operation_level
WHERE a.operation_level != a1.operation_level;

DROP TABLE itech_bak.operation_lvl_od_duration_frequency_config_2025_02_13;

-- 更新 操作场景 approval_level
UPDATE t_entity_operate_scene a
LEFT JOIN itech_bak.operate_scene_2025_02_13 a1 ON a.id = a1.id
SET a.selectfield_approval_level = a1.selectfield_approval_level
WHERE a.selectfield_approval_level != a1.selectfield_approval_level;

DROP TABLE itech_bak.operate_scene_2025_02_13;

-- 更新 操作类型 partner_network_changes_available, radiofield_is_customize_operation_reason, operate_level
UPDATE t_entity_operate_type a
LEFT JOIN itech_bak.operate_type_2025_02_13 a1 ON a.id = a1.id
SET a.partner_network_changes_available = a1.partner_network_changes_available
WHERE a.partner_network_changes_available != a1.partner_network_changes_available;

UPDATE t_entity_operate_type a
LEFT JOIN itech_bak.operate_type_2025_02_13 a1 ON a.id = a1.id
SET a.radiofield_is_customize_operation_reason = a1.radiofield_is_customize_operation_reason
WHERE a.radiofield_is_customize_operation_reason != a1.radiofield_is_customize_operation_reason;

UPDATE t_entity_operate_type a
LEFT JOIN itech_bak.operate_type_2025_02_13 a1 ON a.id = a1.id
SET a.operate_level = a1.operate_level
WHERE a.operate_level != a1.operate_level;

DROP TABLE itech_bak.operate_type_2025_02_13;
