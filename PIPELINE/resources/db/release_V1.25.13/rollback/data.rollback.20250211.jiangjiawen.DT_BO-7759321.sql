
 -- 打卡复盘表回滚操作等级
UPDATE clock_in_reviews t
LEFT JOIN itech_bak.fp_operation_level_DT_BO_7759321 t1 ON t.id = t1.id
SET t.operation_level = t1.operation_level
where t.operation_level != t1.operation_level;

-- 打卡任务表回滚操作等级
UPDATE clock_in_task t
LEFT JOIN itech_bak.fp_operation_level_DT_BO_7759321 t1 ON t.id = t1.id
SET t.operation_level = t1.operation_level
where t.operation_level != t1.operation_level;

-- 更新打卡复盘表任务状态
UPDATE clock_in_reviews t
LEFT JOIN itech_bak.fp_assignment_status_DT_BO_7759321 t1 ON t.id = t1.id
SET t.replay_status = t1.replay_status
where t.replay_status != t1.replay_status;

-- 删除临时表
DROP TABLE itech_bak.fp_operation_level_DT_BO_7759321;
DROP TABLE itech_bak.fp_assignment_status_DT_BO_7759321;