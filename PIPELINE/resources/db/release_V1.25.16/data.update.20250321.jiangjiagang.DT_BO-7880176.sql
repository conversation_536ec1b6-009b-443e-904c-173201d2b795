-- 设置表中assignment_network_change_ex新增的create_clock_in_task字段设置为1（表示该批次任务创建了打卡任务）
update
	assignment_network_change_ex ance
left join assignment ass on
	ance.id = ass.id
set
	ance.create_clock_in_task = 1
where
	ass.entity_id in (
	select
		distinct cit.batch_task_id
	from
		clock_in_task cit);

-- 设置表中assignment_network_change_ex新增的create_clock_in_task字段设置为1（表示该网络变更任务创建了打卡任务）
update
	assignment_network_change_ex ance
left join assignment ass on
	ance.id = ass.id
set
	ance.create_clock_in_task = 1
where
	ass.entity_id in (
	select
		distinct cit.change_order_id
	from
		clock_in_task cit);