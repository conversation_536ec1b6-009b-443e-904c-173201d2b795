<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">
    <changeSet id="release_V1.25.16_tag" author="10344315">
        <tagDatabase tag="release_V1.25.16_tag"/>
    </changeSet>

    <changeSet id="release_V1.25.16_10344315" author="10344315">
        <sqlFile path="db/release_V1.25.16/data.update.20250321.jiangjiagang.DT_BO-7880176.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.16/rollback/data.rollback.20250321.jiangjiagang.DT_BO-7880176.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>

    <changeSet id="release_V1.25.16_10309939" author="10309939">
        <sqlFile path="db/release_V1.25.16/data.update.20250327.kangtianshuo.DT_BO-7917530.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.16/rollback/data.rollback.20250327.kangtianshuo.DT_BO-7917530.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
