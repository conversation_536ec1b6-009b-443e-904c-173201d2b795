update
	assignment t1
set
	product_management_team_ext = null,
	company_ext = null,
	product_management_team = null,
	company = null
where
	json_overlaps(t1.assignment_type_ext,
	'["2", "7"]')
	and t1.is_deleted = 0;


update
	assignment_network_change_ex t1
left join assignment ass on
		t1.id = ass.id
set
	t1.operation_level = null,
	t1.operation_level_ext = null
where
	json_overlaps(ass.assignment_type_ext, '["2","7"]')
	and ass.is_deleted = 0;