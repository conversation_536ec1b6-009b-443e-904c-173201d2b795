-- 数据退回
 UPDATE
 	tech_solution_check t
 JOIN itech_bak.tech_solution_check_2025_04_28 t1 ON
 	t.id = t1.id
 SET
 	t.tech_solution_check_content = t1.tech_solution_check_content ;

UPDATE
	standard_action_check t
JOIN itech_bak.standard_action_check_2025_04_28 t1 ON
	t.id = t1.id
SET
	t.standard_action_check_type = t1.standard_action_check_type ,
	t.standard_action_check_content = t1.standard_action_check_content;

-- 删除备份表
DROP TABLE itech_bak.tech_solution_check_2025_04_28;
DROP TABLE itech_bak.standard_action_check_2025_04_28;