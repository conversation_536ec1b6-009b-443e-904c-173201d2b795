-- 需要先导入低代码 新增字段后才能执行sql
-- 技术方案检查备份表
CREATE TABLE IF NOT EXISTS itech_bak.tech_solution_check_2025_04_28
SELECT id,tech_solution_check_content
FROM tech_solution_check
WHERE is_deleted =0 AND tech_solution_check_content IS NOT NULL ;

-- 核心网检查备份表
CREATE TABLE IF NOT EXISTS itech_bak.standard_action_check_2025_04_28
SELECT id,standard_action_check_type,standard_action_check_content
FROM standard_action_check
WHERE is_deleted = 0 ;

-- 更新技术方案检查双语
UPDATE tech_solution_check t
LEFT JOIN  technical_solution_check t1
ON t.tech_solution_check_content = t1.check_content_zh  AND  t1.check_node ='操作申请'
LEFT JOIN  technical_solution_check t2
ON t.tech_solution_check_content = t2.check_content_en AND  t2.check_node ='操作申请'
SET t.tech_solution_check_content = COALESCE(t1.check_content_zh, t2.check_content_zh),
	t.tech_solution_check_content_en = COALESCE(t1.check_content_en , t2.check_content_en)
WHERE t.is_deleted = 0 ;

-- 更新核心网检查双语
UPDATE standard_action_check t
LEFT JOIN  technical_solution_check t1
ON t.standard_action_check_type = t1.group_one_zh
AND t.standard_action_check_content = t1.check_content_zh
AND  t1.check_node ='操作申请CCN'
LEFT JOIN  technical_solution_check t2
ON t.standard_action_check_type = t2.group_one_en
AND t.standard_action_check_content = t2.check_content_en
AND  t2.check_node ='操作申请CCN'
SET t.standard_action_check_type = COALESCE(t1.group_one_zh  , t2.group_one_zh),
	t.standard_action_check_type_en = COALESCE(t1.group_one_en, t2.group_one_en),
	t.standard_action_check_content = COALESCE(t1.check_content_zh , t2.check_content_zh),
	t.standard_action_check_content_en = COALESCE(t1.check_content_en , t2.check_content_en)
WHERE t.is_deleted = 0 ;
