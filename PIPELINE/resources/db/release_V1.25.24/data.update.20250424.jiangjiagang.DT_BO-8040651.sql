-- 设置assignment表中批次任务对应的product_management_team_ext、company_ext字段
update
	assignment batch
left join (
	select
		bill_id,
		product_management_team_ext,
		product_management_team,
		company_ext,
		company
	from
		assignment
	where
		json_overlaps(assignment_type_ext, '["1","6"]')
		and is_deleted = 0
) as net_change on
	batch.bill_id = net_change.bill_id
set
	batch.product_management_team_ext = coalesce(
        batch.product_management_team_ext,
        net_change.product_management_team_ext),
	batch.product_management_team = coalesce(
        batch.product_management_team,
        net_change.product_management_team),
	batch.company = coalesce(
        batch.company,
        net_change.company),
	batch.company_ext = coalesce(
        batch.company_ext,
        net_change.company_ext)
where
	json_overlaps(batch.assignment_type_ext, '["2","7"]')
	and batch.is_deleted = 0;


-- 根据网络变更单中的operation_level字段，设置assignment_network_change_ex表中批次任务对应的operation_level字段
update
	assignment_network_change_ex batch
left join (
	select
		id,
		bill_id,
		assignment_type_ext
	from
		assignment
	where
		is_deleted = 0
) as ass on
	batch.id = ass.id
left join (
	select
		ance.id,
		ance.operation_level,
		ance.operation_level_ext,
		ass.bill_id
	from
		assignment_network_change_ex ance
	inner join assignment ass on
		ance.id = ass.id
	where
		json_overlaps(assignment_type_ext, '["1","6"]')
			and ass.is_deleted = 0
) as net_change on
	ass.bill_id = net_change.bill_id
set
	batch.operation_level = coalesce(
        batch.operation_level,
        net_change.operation_level),
	batch.operation_level_ext = coalesce(
        batch.operation_level_ext,
        net_change.operation_level_ext)
where
	json_overlaps(ass.assignment_type_ext, '["2","7"]');