ALTER TABLE `assignment` CHANGE `assignment_type_text` DELETED1149066329882480640 varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci GENERATED ALWAYS AS (json_unquote(json_extract(`assignment_type_ext`,_utf8mb4'$[0]'))) STORED COMMENT '生成列 - 任务类型';
ALTER TABLE `assignment` CHANGE`representative_office_text` DELETED1149066329999921153 varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci GENERATED ALWAYS AS (json_unquote(json_extract(`representative_office_ext`,_utf8mb4'$[0]'))) STORED COMMENT '生成列 - 代表处';
ALTER TABLE `assignment` CHANGE`product_team` DELETED1149066330117361664 varchar(200) GENERATED ALWAYS AS (json_unquote(json_extract(`product_management_team_ext`,_utf8mb4'$[0]'))) STORED COMMENT '生成列 - 产品经营团队';

ALTER TABLE `assignment` RENAME KEY `ix_assignment_type_text` TO `idx_assignment_type_text`;
ALTER TABLE `assignment` RENAME KEY `ix_user_permission` TO `idx_user_permission`;
ALTER TABLE `assignment` RENAME KEY `ix_assignment_name` TO `idx_assignment_name`;
ALTER TABLE `assignment` RENAME KEY `ix_current_progress` TO `idx_current_progress`;

ALTER TABLE `assignment_network_change_ex` RENAME KEY `ix_operation_type` TO `idx_operation_type`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `ix_country` TO `idx_country`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `ix_importance` TO `idx_importance`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `ix_risk_evaluation` TO `idx_risk_evaluation`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `ix_operation_level` TO `idx_operation_level`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `ix_operation_result` TO `idx_operation_result`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `ix_operation_reason` TO `idx_operation_reason`;