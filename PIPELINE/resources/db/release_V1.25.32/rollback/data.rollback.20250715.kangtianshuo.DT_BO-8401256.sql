-- 数据回滚
UPDATE assignment_network_change_ex ance
INNER JOIN itech_bak.assignment_network_change_ex_2025_07_15 ance1 ON ance.id = ance1.id
SET ance.time_zone = ance1.time_zone
WHERE ance.id = ance1.id;

UPDATE change_order co
INNER JOIN itech_bak.change_order_2025_07_15 co1 ON co.id = co1.id
SET co.time_zone = co1.time_zone
WHERE co.id = co1.id;

UPDATE subcontractor_oc so
INNER JOIN itech_bak.subcontractor_oc_2025_07_15 so1 ON so.id = so1.id
SET so.time_zone = so1.time_zone
WHERE so.id = so1.id;

UPDATE batch_network_assignment bna
INNER JOIN itech_bak.batch_network_assignment_2025_07_15 bna1 ON bna.id = bna1.id
SET bna.time_zone = bna1.time_zone
WHERE bna.id = bna1.id;

UPDATE subcontractor_batch_task sbt
INNER JOIN itech_bak.subcontractor_batch_task_2025_07_15 sbt1 ON sbt.id = sbt1.id
SET sbt.time_zone = sbt1.time_zone
WHERE sbt.id = sbt1.id;

UPDATE clock_in_task cit
INNER JOIN itech_bak.clock_in_task_2025_07_15 cit1 ON cit.id = cit1.id
SET cit.time_zone = cit1.time_zone
WHERE cit.id = cit1.id;



-- 删除备份表
DROP TABLE itech_bak.assignment_network_change_ex_2025_07_15;
DROP TABLE itech_bak.change_order_2025_07_15;
DROP TABLE itech_bak.subcontractor_oc_2025_07_15;
DROP TABLE itech_bak.batch_network_assignment_2025_07_15;
DROP TABLE itech_bak.subcontractor_batch_task_2025_07_15;
DROP TABLE itech_bak.clock_in_task_2025_07_15;