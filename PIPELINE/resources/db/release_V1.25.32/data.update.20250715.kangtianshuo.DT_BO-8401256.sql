-- 待刷新数据备份
-- 共计 6 张表：assignment_network_change_ex, change_order, subcontractor_oc, batch_network_assignment, subcontractor_batch_task, clock_in_task
CREATE TABLE IF NOT EXISTS itech_bak.assignment_network_change_ex_2025_07_15
SELECT id, time_zone
FROM assignment_network_change_ex;

CREATE TABLE IF NOT EXISTS itech_bak.change_order_2025_07_15
SELECT id, time_zone
FROM change_order;

CREATE TABLE IF NOT EXISTS itech_bak.subcontractor_oc_2025_07_15
SELECT id, time_zone
FROM subcontractor_oc;

CREATE TABLE IF NOT EXISTS itech_bak.batch_network_assignment_2025_07_15
SELECT id, time_zone
FROM batch_network_assignment;

CREATE TABLE IF NOT EXISTS itech_bak.subcontractor_batch_task_2025_07_15
SELECT id, time_zone
FROM subcontractor_batch_task;

CREATE TABLE IF NOT EXISTS itech_bak.clock_in_task_2025_07_15
SELECT id, time_zone
FROM clock_in_task;



-- 时区快码更新
-- 生产能跑存储过程就好了 T^T
UPDATE assignment_network_change_ex ance
INNER JOIN itech_bak.assignment_network_change_ex_2025_07_15 ance1 ON ance.id = ance1.id
SET ance.time_zone = (
    CASE
        WHEN ance1.time_zone = 'new_time_zone_code_1' THEN 'Etc/GMT+12'
        WHEN ance1.time_zone = 'new_time_zone_code_2' THEN 'Pacific/Apia'
        WHEN ance1.time_zone = 'new_time_zone_code_3' THEN 'Pacific/Honolulu'
        WHEN ance1.time_zone = 'new_time_zone_code_4' THEN 'America/Anchorage'
        WHEN ance1.time_zone = 'new_time_zone_code_5' THEN 'America/Los_Angeles'
        WHEN ance1.time_zone = 'new_time_zone_code_6' THEN 'America/Denver'
        WHEN ance1.time_zone = 'new_time_zone_code_7' THEN 'America/Phoenix'
        WHEN ance1.time_zone = 'new_time_zone_code_8' THEN 'America/Chihuahua'
        WHEN ance1.time_zone = 'new_time_zone_code_9' THEN 'America/Regina'
        WHEN ance1.time_zone = 'new_time_zone_code_10' THEN 'America/Chicago'
        WHEN ance1.time_zone = 'new_time_zone_code_11' THEN 'America/Guatemala'
        WHEN ance1.time_zone = 'new_time_zone_code_12' THEN 'America/Mexico_City'
        WHEN ance1.time_zone = 'new_time_zone_code_13' THEN 'America/Bogota'
        WHEN ance1.time_zone = 'new_time_zone_code_14' THEN 'America/New_York'
        WHEN ance1.time_zone = 'new_time_zone_code_15' THEN 'America/Indianapolis'
        WHEN ance1.time_zone = 'new_time_zone_code_16' THEN 'America/Halifax'
        WHEN ance1.time_zone = 'new_time_zone_code_17' THEN 'America/Caracas'
        WHEN ance1.time_zone = 'new_time_zone_code_18' THEN 'America/Santiago'
        WHEN ance1.time_zone = 'new_time_zone_code_19' THEN 'America/St_Johns'
        WHEN ance1.time_zone = 'new_time_zone_code_20' THEN 'America/Sao_Paulo'
        WHEN ance1.time_zone = 'new_time_zone_code_21' THEN 'America/Buenos_Aires'
        WHEN ance1.time_zone = 'new_time_zone_code_22' THEN 'America/Godthab'
        WHEN ance1.time_zone = 'new_time_zone_code_23' THEN 'America/Halifax'
        WHEN ance1.time_zone = 'new_time_zone_code_24' THEN 'Atlantic/Cape_Verde'
        WHEN ance1.time_zone = 'new_time_zone_code_25' THEN 'Atlantic/Azores'
        WHEN ance1.time_zone = 'new_time_zone_code_26' THEN 'Europe/London'
        WHEN ance1.time_zone = 'new_time_zone_code_27' THEN 'Asia/Shanghai'
        WHEN ance1.time_zone = 'new_time_zone_code_28' THEN 'Europe/Berlin'
        WHEN ance1.time_zone = 'new_time_zone_code_29' THEN 'Europe/Prague'
        WHEN ance1.time_zone = 'new_time_zone_code_30' THEN 'Europe/Paris'
        WHEN ance1.time_zone = 'new_time_zone_code_31' THEN 'Europe/Belgrade'
        WHEN ance1.time_zone = 'new_time_zone_code_32' THEN 'Africa/Luanda'
        WHEN ance1.time_zone = 'new_time_zone_code_33' THEN 'Europe/Athens'
        WHEN ance1.time_zone = 'new_time_zone_code_34' THEN 'Africa/Harare'
        WHEN ance1.time_zone = 'new_time_zone_code_35' THEN 'Europe/Helsinki'
        WHEN ance1.time_zone = 'new_time_zone_code_36' THEN 'Africa/Cairo'
        WHEN ance1.time_zone = 'new_time_zone_code_37' THEN 'Europe/Athens'
        WHEN ance1.time_zone = 'new_time_zone_code_38' THEN 'Asia/Jerusalem'
        WHEN ance1.time_zone = 'new_time_zone_code_39' THEN 'Asia/Baghdad'
        WHEN ance1.time_zone = 'new_time_zone_code_40' THEN 'Asia/Riyadh'
        WHEN ance1.time_zone = 'new_time_zone_code_41' THEN 'Europe/Moscow'
        WHEN ance1.time_zone = 'new_time_zone_code_42' THEN 'Africa/Nairobi'
        WHEN ance1.time_zone = 'new_time_zone_code_43' THEN 'Asia/Tehran'
        WHEN ance1.time_zone = 'new_time_zone_code_44' THEN 'Asia/Muscat'
        WHEN ance1.time_zone = 'new_time_zone_code_45' THEN 'Asia/Baku'
        WHEN ance1.time_zone = 'new_time_zone_code_46' THEN 'Asia/Kabul'
        WHEN ance1.time_zone = 'new_time_zone_code_47' THEN 'Asia/Yekaterinburg'
        WHEN ance1.time_zone = 'new_time_zone_code_48' THEN 'Asia/Karachi'
        WHEN ance1.time_zone = 'new_time_zone_code_49' THEN 'Asia/Calcutta'
        WHEN ance1.time_zone = 'new_time_zone_code_50' THEN 'Asia/Katmandu'
        WHEN ance1.time_zone = 'new_time_zone_code_51' THEN 'Asia/Novosibirsk'
        WHEN ance1.time_zone = 'new_time_zone_code_52' THEN 'Asia/Almaty'
        WHEN ance1.time_zone = 'new_time_zone_code_53' THEN 'Asia/Colombo'
        WHEN ance1.time_zone = 'new_time_zone_code_54' THEN 'Asia/Rangoon'
        WHEN ance1.time_zone = 'new_time_zone_code_55' THEN 'Asia/Krasnoyarsk'
        WHEN ance1.time_zone = 'new_time_zone_code_56' THEN 'Asia/Bangkok'
        WHEN ance1.time_zone = 'new_time_zone_code_57' THEN 'Africa/Monrovia'
        WHEN ance1.time_zone = 'new_time_zone_code_58' THEN 'Asia/Singapore'
        WHEN ance1.time_zone = 'new_time_zone_code_59' THEN 'Australia/Perth'
        WHEN ance1.time_zone = 'new_time_zone_code_60' THEN 'Asia/Taipei'
        WHEN ance1.time_zone = 'new_time_zone_code_61' THEN 'Asia/Ulaanbaatar'
        WHEN ance1.time_zone = 'new_time_zone_code_62' THEN 'Asia/Tokyo'
        WHEN ance1.time_zone = 'new_time_zone_code_63' THEN 'Asia/Seoul'
        WHEN ance1.time_zone = 'new_time_zone_code_64' THEN 'Asia/Yakutsk'
        WHEN ance1.time_zone = 'new_time_zone_code_65' THEN 'Australia/Adelaide'
        WHEN ance1.time_zone = 'new_time_zone_code_66' THEN 'Australia/Darwin'
        WHEN ance1.time_zone = 'new_time_zone_code_67' THEN 'Australia/Brisbane'
        WHEN ance1.time_zone = 'new_time_zone_code_68' THEN 'Asia/Vladivostok'
        WHEN ance1.time_zone = 'new_time_zone_code_69' THEN 'Pacific/Guam'
        WHEN ance1.time_zone = 'new_time_zone_code_70' THEN 'Australia/Hobart'
        WHEN ance1.time_zone = 'new_time_zone_code_71' THEN 'Australia/Sydney'
        WHEN ance1.time_zone = 'new_time_zone_code_72' THEN 'Pacific/Guadalcanal'
        WHEN ance1.time_zone = 'new_time_zone_code_73' THEN 'Pacific/Auckland'
        WHEN ance1.time_zone = 'new_time_zone_code_74' THEN 'Pacific/Fiji'
        WHEN ance1.time_zone = 'new_time_zone_code_75' THEN 'Pacific/Tongatapu'
        ELSE ance1.time_zone
    END
)
WHERE ance.id = ance1.id;


UPDATE change_order co
INNER JOIN itech_bak.change_order_2025_07_15 co1 ON co.id = co1.id
SET co.time_zone = (
    CASE
        WHEN co1.time_zone = 'new_time_zone_code_1' THEN 'Etc/GMT+12'
        WHEN co1.time_zone = 'new_time_zone_code_2' THEN 'Pacific/Apia'
        WHEN co1.time_zone = 'new_time_zone_code_3' THEN 'Pacific/Honolulu'
        WHEN co1.time_zone = 'new_time_zone_code_4' THEN 'America/Anchorage'
        WHEN co1.time_zone = 'new_time_zone_code_5' THEN 'America/Los_Angeles'
        WHEN co1.time_zone = 'new_time_zone_code_6' THEN 'America/Denver'
        WHEN co1.time_zone = 'new_time_zone_code_7' THEN 'America/Phoenix'
        WHEN co1.time_zone = 'new_time_zone_code_8' THEN 'America/Chihuahua'
        WHEN co1.time_zone = 'new_time_zone_code_9' THEN 'America/Regina'
        WHEN co1.time_zone = 'new_time_zone_code_10' THEN 'America/Chicago'
        WHEN co1.time_zone = 'new_time_zone_code_11' THEN 'America/Guatemala'
        WHEN co1.time_zone = 'new_time_zone_code_12' THEN 'America/Mexico_City'
        WHEN co1.time_zone = 'new_time_zone_code_13' THEN 'America/Bogota'
        WHEN co1.time_zone = 'new_time_zone_code_14' THEN 'America/New_York'
        WHEN co1.time_zone = 'new_time_zone_code_15' THEN 'America/Indianapolis'
        WHEN co1.time_zone = 'new_time_zone_code_16' THEN 'America/Halifax'
        WHEN co1.time_zone = 'new_time_zone_code_17' THEN 'America/Caracas'
        WHEN co1.time_zone = 'new_time_zone_code_18' THEN 'America/Santiago'
        WHEN co1.time_zone = 'new_time_zone_code_19' THEN 'America/St_Johns'
        WHEN co1.time_zone = 'new_time_zone_code_20' THEN 'America/Sao_Paulo'
        WHEN co1.time_zone = 'new_time_zone_code_21' THEN 'America/Buenos_Aires'
        WHEN co1.time_zone = 'new_time_zone_code_22' THEN 'America/Godthab'
        WHEN co1.time_zone = 'new_time_zone_code_23' THEN 'America/Halifax'
        WHEN co1.time_zone = 'new_time_zone_code_24' THEN 'Atlantic/Cape_Verde'
        WHEN co1.time_zone = 'new_time_zone_code_25' THEN 'Atlantic/Azores'
        WHEN co1.time_zone = 'new_time_zone_code_26' THEN 'Europe/London'
        WHEN co1.time_zone = 'new_time_zone_code_27' THEN 'Asia/Shanghai'
        WHEN co1.time_zone = 'new_time_zone_code_28' THEN 'Europe/Berlin'
        WHEN co1.time_zone = 'new_time_zone_code_29' THEN 'Europe/Prague'
        WHEN co1.time_zone = 'new_time_zone_code_30' THEN 'Europe/Paris'
        WHEN co1.time_zone = 'new_time_zone_code_31' THEN 'Europe/Belgrade'
        WHEN co1.time_zone = 'new_time_zone_code_32' THEN 'Africa/Luanda'
        WHEN co1.time_zone = 'new_time_zone_code_33' THEN 'Europe/Athens'
        WHEN co1.time_zone = 'new_time_zone_code_34' THEN 'Africa/Harare'
        WHEN co1.time_zone = 'new_time_zone_code_35' THEN 'Europe/Helsinki'
        WHEN co1.time_zone = 'new_time_zone_code_36' THEN 'Africa/Cairo'
        WHEN co1.time_zone = 'new_time_zone_code_37' THEN 'Europe/Athens'
        WHEN co1.time_zone = 'new_time_zone_code_38' THEN 'Asia/Jerusalem'
        WHEN co1.time_zone = 'new_time_zone_code_39' THEN 'Asia/Baghdad'
        WHEN co1.time_zone = 'new_time_zone_code_40' THEN 'Asia/Riyadh'
        WHEN co1.time_zone = 'new_time_zone_code_41' THEN 'Europe/Moscow'
        WHEN co1.time_zone = 'new_time_zone_code_42' THEN 'Africa/Nairobi'
        WHEN co1.time_zone = 'new_time_zone_code_43' THEN 'Asia/Tehran'
        WHEN co1.time_zone = 'new_time_zone_code_44' THEN 'Asia/Muscat'
        WHEN co1.time_zone = 'new_time_zone_code_45' THEN 'Asia/Baku'
        WHEN co1.time_zone = 'new_time_zone_code_46' THEN 'Asia/Kabul'
        WHEN co1.time_zone = 'new_time_zone_code_47' THEN 'Asia/Yekaterinburg'
        WHEN co1.time_zone = 'new_time_zone_code_48' THEN 'Asia/Karachi'
        WHEN co1.time_zone = 'new_time_zone_code_49' THEN 'Asia/Calcutta'
        WHEN co1.time_zone = 'new_time_zone_code_50' THEN 'Asia/Katmandu'
        WHEN co1.time_zone = 'new_time_zone_code_51' THEN 'Asia/Novosibirsk'
        WHEN co1.time_zone = 'new_time_zone_code_52' THEN 'Asia/Almaty'
        WHEN co1.time_zone = 'new_time_zone_code_53' THEN 'Asia/Colombo'
        WHEN co1.time_zone = 'new_time_zone_code_54' THEN 'Asia/Rangoon'
        WHEN co1.time_zone = 'new_time_zone_code_55' THEN 'Asia/Krasnoyarsk'
        WHEN co1.time_zone = 'new_time_zone_code_56' THEN 'Asia/Bangkok'
        WHEN co1.time_zone = 'new_time_zone_code_57' THEN 'Africa/Monrovia'
        WHEN co1.time_zone = 'new_time_zone_code_58' THEN 'Asia/Singapore'
        WHEN co1.time_zone = 'new_time_zone_code_59' THEN 'Australia/Perth'
        WHEN co1.time_zone = 'new_time_zone_code_60' THEN 'Asia/Taipei'
        WHEN co1.time_zone = 'new_time_zone_code_61' THEN 'Asia/Ulaanbaatar'
        WHEN co1.time_zone = 'new_time_zone_code_62' THEN 'Asia/Tokyo'
        WHEN co1.time_zone = 'new_time_zone_code_63' THEN 'Asia/Seoul'
        WHEN co1.time_zone = 'new_time_zone_code_64' THEN 'Asia/Yakutsk'
        WHEN co1.time_zone = 'new_time_zone_code_65' THEN 'Australia/Adelaide'
        WHEN co1.time_zone = 'new_time_zone_code_66' THEN 'Australia/Darwin'
        WHEN co1.time_zone = 'new_time_zone_code_67' THEN 'Australia/Brisbane'
        WHEN co1.time_zone = 'new_time_zone_code_68' THEN 'Asia/Vladivostok'
        WHEN co1.time_zone = 'new_time_zone_code_69' THEN 'Pacific/Guam'
        WHEN co1.time_zone = 'new_time_zone_code_70' THEN 'Australia/Hobart'
        WHEN co1.time_zone = 'new_time_zone_code_71' THEN 'Australia/Sydney'
        WHEN co1.time_zone = 'new_time_zone_code_72' THEN 'Pacific/Guadalcanal'
        WHEN co1.time_zone = 'new_time_zone_code_73' THEN 'Pacific/Auckland'
        WHEN co1.time_zone = 'new_time_zone_code_74' THEN 'Pacific/Fiji'
        WHEN co1.time_zone = 'new_time_zone_code_75' THEN 'Pacific/Tongatapu'
        ELSE co1.time_zone
    END
)
WHERE co.id = co1.id;


UPDATE subcontractor_oc so
INNER JOIN itech_bak.subcontractor_oc_2025_07_15 so1 ON so.id = so1.id
SET so.time_zone = (
    CASE
        WHEN so1.time_zone = 'new_time_zone_code_1' THEN 'Etc/GMT+12'
        WHEN so1.time_zone = 'new_time_zone_code_2' THEN 'Pacific/Apia'
        WHEN so1.time_zone = 'new_time_zone_code_3' THEN 'Pacific/Honolulu'
        WHEN so1.time_zone = 'new_time_zone_code_4' THEN 'America/Anchorage'
        WHEN so1.time_zone = 'new_time_zone_code_5' THEN 'America/Los_Angeles'
        WHEN so1.time_zone = 'new_time_zone_code_6' THEN 'America/Denver'
        WHEN so1.time_zone = 'new_time_zone_code_7' THEN 'America/Phoenix'
        WHEN so1.time_zone = 'new_time_zone_code_8' THEN 'America/Chihuahua'
        WHEN so1.time_zone = 'new_time_zone_code_9' THEN 'America/Regina'
        WHEN so1.time_zone = 'new_time_zone_code_10' THEN 'America/Chicago'
        WHEN so1.time_zone = 'new_time_zone_code_11' THEN 'America/Guatemala'
        WHEN so1.time_zone = 'new_time_zone_code_12' THEN 'America/Mexico_City'
        WHEN so1.time_zone = 'new_time_zone_code_13' THEN 'America/Bogota'
        WHEN so1.time_zone = 'new_time_zone_code_14' THEN 'America/New_York'
        WHEN so1.time_zone = 'new_time_zone_code_15' THEN 'America/Indianapolis'
        WHEN so1.time_zone = 'new_time_zone_code_16' THEN 'America/Halifax'
        WHEN so1.time_zone = 'new_time_zone_code_17' THEN 'America/Caracas'
        WHEN so1.time_zone = 'new_time_zone_code_18' THEN 'America/Santiago'
        WHEN so1.time_zone = 'new_time_zone_code_19' THEN 'America/St_Johns'
        WHEN so1.time_zone = 'new_time_zone_code_20' THEN 'America/Sao_Paulo'
        WHEN so1.time_zone = 'new_time_zone_code_21' THEN 'America/Buenos_Aires'
        WHEN so1.time_zone = 'new_time_zone_code_22' THEN 'America/Godthab'
        WHEN so1.time_zone = 'new_time_zone_code_23' THEN 'America/Halifax'
        WHEN so1.time_zone = 'new_time_zone_code_24' THEN 'Atlantic/Cape_Verde'
        WHEN so1.time_zone = 'new_time_zone_code_25' THEN 'Atlantic/Azores'
        WHEN so1.time_zone = 'new_time_zone_code_26' THEN 'Europe/London'
        WHEN so1.time_zone = 'new_time_zone_code_27' THEN 'Asia/Shanghai'
        WHEN so1.time_zone = 'new_time_zone_code_28' THEN 'Europe/Berlin'
        WHEN so1.time_zone = 'new_time_zone_code_29' THEN 'Europe/Prague'
        WHEN so1.time_zone = 'new_time_zone_code_30' THEN 'Europe/Paris'
        WHEN so1.time_zone = 'new_time_zone_code_31' THEN 'Europe/Belgrade'
        WHEN so1.time_zone = 'new_time_zone_code_32' THEN 'Africa/Luanda'
        WHEN so1.time_zone = 'new_time_zone_code_33' THEN 'Europe/Athens'
        WHEN so1.time_zone = 'new_time_zone_code_34' THEN 'Africa/Harare'
        WHEN so1.time_zone = 'new_time_zone_code_35' THEN 'Europe/Helsinki'
        WHEN so1.time_zone = 'new_time_zone_code_36' THEN 'Africa/Cairo'
        WHEN so1.time_zone = 'new_time_zone_code_37' THEN 'Europe/Athens'
        WHEN so1.time_zone = 'new_time_zone_code_38' THEN 'Asia/Jerusalem'
        WHEN so1.time_zone = 'new_time_zone_code_39' THEN 'Asia/Baghdad'
        WHEN so1.time_zone = 'new_time_zone_code_40' THEN 'Asia/Riyadh'
        WHEN so1.time_zone = 'new_time_zone_code_41' THEN 'Europe/Moscow'
        WHEN so1.time_zone = 'new_time_zone_code_42' THEN 'Africa/Nairobi'
        WHEN so1.time_zone = 'new_time_zone_code_43' THEN 'Asia/Tehran'
        WHEN so1.time_zone = 'new_time_zone_code_44' THEN 'Asia/Muscat'
        WHEN so1.time_zone = 'new_time_zone_code_45' THEN 'Asia/Baku'
        WHEN so1.time_zone = 'new_time_zone_code_46' THEN 'Asia/Kabul'
        WHEN so1.time_zone = 'new_time_zone_code_47' THEN 'Asia/Yekaterinburg'
        WHEN so1.time_zone = 'new_time_zone_code_48' THEN 'Asia/Karachi'
        WHEN so1.time_zone = 'new_time_zone_code_49' THEN 'Asia/Calcutta'
        WHEN so1.time_zone = 'new_time_zone_code_50' THEN 'Asia/Katmandu'
        WHEN so1.time_zone = 'new_time_zone_code_51' THEN 'Asia/Novosibirsk'
        WHEN so1.time_zone = 'new_time_zone_code_52' THEN 'Asia/Almaty'
        WHEN so1.time_zone = 'new_time_zone_code_53' THEN 'Asia/Colombo'
        WHEN so1.time_zone = 'new_time_zone_code_54' THEN 'Asia/Rangoon'
        WHEN so1.time_zone = 'new_time_zone_code_55' THEN 'Asia/Krasnoyarsk'
        WHEN so1.time_zone = 'new_time_zone_code_56' THEN 'Asia/Bangkok'
        WHEN so1.time_zone = 'new_time_zone_code_57' THEN 'Africa/Monrovia'
        WHEN so1.time_zone = 'new_time_zone_code_58' THEN 'Asia/Singapore'
        WHEN so1.time_zone = 'new_time_zone_code_59' THEN 'Australia/Perth'
        WHEN so1.time_zone = 'new_time_zone_code_60' THEN 'Asia/Taipei'
        WHEN so1.time_zone = 'new_time_zone_code_61' THEN 'Asia/Ulaanbaatar'
        WHEN so1.time_zone = 'new_time_zone_code_62' THEN 'Asia/Tokyo'
        WHEN so1.time_zone = 'new_time_zone_code_63' THEN 'Asia/Seoul'
        WHEN so1.time_zone = 'new_time_zone_code_64' THEN 'Asia/Yakutsk'
        WHEN so1.time_zone = 'new_time_zone_code_65' THEN 'Australia/Adelaide'
        WHEN so1.time_zone = 'new_time_zone_code_66' THEN 'Australia/Darwin'
        WHEN so1.time_zone = 'new_time_zone_code_67' THEN 'Australia/Brisbane'
        WHEN so1.time_zone = 'new_time_zone_code_68' THEN 'Asia/Vladivostok'
        WHEN so1.time_zone = 'new_time_zone_code_69' THEN 'Pacific/Guam'
        WHEN so1.time_zone = 'new_time_zone_code_70' THEN 'Australia/Hobart'
        WHEN so1.time_zone = 'new_time_zone_code_71' THEN 'Australia/Sydney'
        WHEN so1.time_zone = 'new_time_zone_code_72' THEN 'Pacific/Guadalcanal'
        WHEN so1.time_zone = 'new_time_zone_code_73' THEN 'Pacific/Auckland'
        WHEN so1.time_zone = 'new_time_zone_code_74' THEN 'Pacific/Fiji'
        WHEN so1.time_zone = 'new_time_zone_code_75' THEN 'Pacific/Tongatapu'
        ELSE so1.time_zone
    END
)
WHERE so.id = so1.id;


UPDATE batch_network_assignment bna
INNER JOIN itech_bak.batch_network_assignment_2025_07_15 bna1 ON bna.id = bna1.id
SET bna.time_zone = (
    CASE
        WHEN bna1.time_zone = 'new_time_zone_code_1' THEN 'Etc/GMT+12'
        WHEN bna1.time_zone = 'new_time_zone_code_2' THEN 'Pacific/Apia'
        WHEN bna1.time_zone = 'new_time_zone_code_3' THEN 'Pacific/Honolulu'
        WHEN bna1.time_zone = 'new_time_zone_code_4' THEN 'America/Anchorage'
        WHEN bna1.time_zone = 'new_time_zone_code_5' THEN 'America/Los_Angeles'
        WHEN bna1.time_zone = 'new_time_zone_code_6' THEN 'America/Denver'
        WHEN bna1.time_zone = 'new_time_zone_code_7' THEN 'America/Phoenix'
        WHEN bna1.time_zone = 'new_time_zone_code_8' THEN 'America/Chihuahua'
        WHEN bna1.time_zone = 'new_time_zone_code_9' THEN 'America/Regina'
        WHEN bna1.time_zone = 'new_time_zone_code_10' THEN 'America/Chicago'
        WHEN bna1.time_zone = 'new_time_zone_code_11' THEN 'America/Guatemala'
        WHEN bna1.time_zone = 'new_time_zone_code_12' THEN 'America/Mexico_City'
        WHEN bna1.time_zone = 'new_time_zone_code_13' THEN 'America/Bogota'
        WHEN bna1.time_zone = 'new_time_zone_code_14' THEN 'America/New_York'
        WHEN bna1.time_zone = 'new_time_zone_code_15' THEN 'America/Indianapolis'
        WHEN bna1.time_zone = 'new_time_zone_code_16' THEN 'America/Halifax'
        WHEN bna1.time_zone = 'new_time_zone_code_17' THEN 'America/Caracas'
        WHEN bna1.time_zone = 'new_time_zone_code_18' THEN 'America/Santiago'
        WHEN bna1.time_zone = 'new_time_zone_code_19' THEN 'America/St_Johns'
        WHEN bna1.time_zone = 'new_time_zone_code_20' THEN 'America/Sao_Paulo'
        WHEN bna1.time_zone = 'new_time_zone_code_21' THEN 'America/Buenos_Aires'
        WHEN bna1.time_zone = 'new_time_zone_code_22' THEN 'America/Godthab'
        WHEN bna1.time_zone = 'new_time_zone_code_23' THEN 'America/Halifax'
        WHEN bna1.time_zone = 'new_time_zone_code_24' THEN 'Atlantic/Cape_Verde'
        WHEN bna1.time_zone = 'new_time_zone_code_25' THEN 'Atlantic/Azores'
        WHEN bna1.time_zone = 'new_time_zone_code_26' THEN 'Europe/London'
        WHEN bna1.time_zone = 'new_time_zone_code_27' THEN 'Asia/Shanghai'
        WHEN bna1.time_zone = 'new_time_zone_code_28' THEN 'Europe/Berlin'
        WHEN bna1.time_zone = 'new_time_zone_code_29' THEN 'Europe/Prague'
        WHEN bna1.time_zone = 'new_time_zone_code_30' THEN 'Europe/Paris'
        WHEN bna1.time_zone = 'new_time_zone_code_31' THEN 'Europe/Belgrade'
        WHEN bna1.time_zone = 'new_time_zone_code_32' THEN 'Africa/Luanda'
        WHEN bna1.time_zone = 'new_time_zone_code_33' THEN 'Europe/Athens'
        WHEN bna1.time_zone = 'new_time_zone_code_34' THEN 'Africa/Harare'
        WHEN bna1.time_zone = 'new_time_zone_code_35' THEN 'Europe/Helsinki'
        WHEN bna1.time_zone = 'new_time_zone_code_36' THEN 'Africa/Cairo'
        WHEN bna1.time_zone = 'new_time_zone_code_37' THEN 'Europe/Athens'
        WHEN bna1.time_zone = 'new_time_zone_code_38' THEN 'Asia/Jerusalem'
        WHEN bna1.time_zone = 'new_time_zone_code_39' THEN 'Asia/Baghdad'
        WHEN bna1.time_zone = 'new_time_zone_code_40' THEN 'Asia/Riyadh'
        WHEN bna1.time_zone = 'new_time_zone_code_41' THEN 'Europe/Moscow'
        WHEN bna1.time_zone = 'new_time_zone_code_42' THEN 'Africa/Nairobi'
        WHEN bna1.time_zone = 'new_time_zone_code_43' THEN 'Asia/Tehran'
        WHEN bna1.time_zone = 'new_time_zone_code_44' THEN 'Asia/Muscat'
        WHEN bna1.time_zone = 'new_time_zone_code_45' THEN 'Asia/Baku'
        WHEN bna1.time_zone = 'new_time_zone_code_46' THEN 'Asia/Kabul'
        WHEN bna1.time_zone = 'new_time_zone_code_47' THEN 'Asia/Yekaterinburg'
        WHEN bna1.time_zone = 'new_time_zone_code_48' THEN 'Asia/Karachi'
        WHEN bna1.time_zone = 'new_time_zone_code_49' THEN 'Asia/Calcutta'
        WHEN bna1.time_zone = 'new_time_zone_code_50' THEN 'Asia/Katmandu'
        WHEN bna1.time_zone = 'new_time_zone_code_51' THEN 'Asia/Novosibirsk'
        WHEN bna1.time_zone = 'new_time_zone_code_52' THEN 'Asia/Almaty'
        WHEN bna1.time_zone = 'new_time_zone_code_53' THEN 'Asia/Colombo'
        WHEN bna1.time_zone = 'new_time_zone_code_54' THEN 'Asia/Rangoon'
        WHEN bna1.time_zone = 'new_time_zone_code_55' THEN 'Asia/Krasnoyarsk'
        WHEN bna1.time_zone = 'new_time_zone_code_56' THEN 'Asia/Bangkok'
        WHEN bna1.time_zone = 'new_time_zone_code_57' THEN 'Africa/Monrovia'
        WHEN bna1.time_zone = 'new_time_zone_code_58' THEN 'Asia/Singapore'
        WHEN bna1.time_zone = 'new_time_zone_code_59' THEN 'Australia/Perth'
        WHEN bna1.time_zone = 'new_time_zone_code_60' THEN 'Asia/Taipei'
        WHEN bna1.time_zone = 'new_time_zone_code_61' THEN 'Asia/Ulaanbaatar'
        WHEN bna1.time_zone = 'new_time_zone_code_62' THEN 'Asia/Tokyo'
        WHEN bna1.time_zone = 'new_time_zone_code_63' THEN 'Asia/Seoul'
        WHEN bna1.time_zone = 'new_time_zone_code_64' THEN 'Asia/Yakutsk'
        WHEN bna1.time_zone = 'new_time_zone_code_65' THEN 'Australia/Adelaide'
        WHEN bna1.time_zone = 'new_time_zone_code_66' THEN 'Australia/Darwin'
        WHEN bna1.time_zone = 'new_time_zone_code_67' THEN 'Australia/Brisbane'
        WHEN bna1.time_zone = 'new_time_zone_code_68' THEN 'Asia/Vladivostok'
        WHEN bna1.time_zone = 'new_time_zone_code_69' THEN 'Pacific/Guam'
        WHEN bna1.time_zone = 'new_time_zone_code_70' THEN 'Australia/Hobart'
        WHEN bna1.time_zone = 'new_time_zone_code_71' THEN 'Australia/Sydney'
        WHEN bna1.time_zone = 'new_time_zone_code_72' THEN 'Pacific/Guadalcanal'
        WHEN bna1.time_zone = 'new_time_zone_code_73' THEN 'Pacific/Auckland'
        WHEN bna1.time_zone = 'new_time_zone_code_74' THEN 'Pacific/Fiji'
        WHEN bna1.time_zone = 'new_time_zone_code_75' THEN 'Pacific/Tongatapu'
        ELSE bna1.time_zone
    END
)
WHERE bna.id = bna1.id;


UPDATE subcontractor_batch_task sbt
INNER JOIN itech_bak.subcontractor_batch_task_2025_07_15 sbt1 ON sbt.id = sbt1.id
SET sbt.time_zone = (
    CASE
        WHEN sbt1.time_zone = 'new_time_zone_code_1' THEN 'Etc/GMT+12'
        WHEN sbt1.time_zone = 'new_time_zone_code_2' THEN 'Pacific/Apia'
        WHEN sbt1.time_zone = 'new_time_zone_code_3' THEN 'Pacific/Honolulu'
        WHEN sbt1.time_zone = 'new_time_zone_code_4' THEN 'America/Anchorage'
        WHEN sbt1.time_zone = 'new_time_zone_code_5' THEN 'America/Los_Angeles'
        WHEN sbt1.time_zone = 'new_time_zone_code_6' THEN 'America/Denver'
        WHEN sbt1.time_zone = 'new_time_zone_code_7' THEN 'America/Phoenix'
        WHEN sbt1.time_zone = 'new_time_zone_code_8' THEN 'America/Chihuahua'
        WHEN sbt1.time_zone = 'new_time_zone_code_9' THEN 'America/Regina'
        WHEN sbt1.time_zone = 'new_time_zone_code_10' THEN 'America/Chicago'
        WHEN sbt1.time_zone = 'new_time_zone_code_11' THEN 'America/Guatemala'
        WHEN sbt1.time_zone = 'new_time_zone_code_12' THEN 'America/Mexico_City'
        WHEN sbt1.time_zone = 'new_time_zone_code_13' THEN 'America/Bogota'
        WHEN sbt1.time_zone = 'new_time_zone_code_14' THEN 'America/New_York'
        WHEN sbt1.time_zone = 'new_time_zone_code_15' THEN 'America/Indianapolis'
        WHEN sbt1.time_zone = 'new_time_zone_code_16' THEN 'America/Halifax'
        WHEN sbt1.time_zone = 'new_time_zone_code_17' THEN 'America/Caracas'
        WHEN sbt1.time_zone = 'new_time_zone_code_18' THEN 'America/Santiago'
        WHEN sbt1.time_zone = 'new_time_zone_code_19' THEN 'America/St_Johns'
        WHEN sbt1.time_zone = 'new_time_zone_code_20' THEN 'America/Sao_Paulo'
        WHEN sbt1.time_zone = 'new_time_zone_code_21' THEN 'America/Buenos_Aires'
        WHEN sbt1.time_zone = 'new_time_zone_code_22' THEN 'America/Godthab'
        WHEN sbt1.time_zone = 'new_time_zone_code_23' THEN 'America/Halifax'
        WHEN sbt1.time_zone = 'new_time_zone_code_24' THEN 'Atlantic/Cape_Verde'
        WHEN sbt1.time_zone = 'new_time_zone_code_25' THEN 'Atlantic/Azores'
        WHEN sbt1.time_zone = 'new_time_zone_code_26' THEN 'Europe/London'
        WHEN sbt1.time_zone = 'new_time_zone_code_27' THEN 'Asia/Shanghai'
        WHEN sbt1.time_zone = 'new_time_zone_code_28' THEN 'Europe/Berlin'
        WHEN sbt1.time_zone = 'new_time_zone_code_29' THEN 'Europe/Prague'
        WHEN sbt1.time_zone = 'new_time_zone_code_30' THEN 'Europe/Paris'
        WHEN sbt1.time_zone = 'new_time_zone_code_31' THEN 'Europe/Belgrade'
        WHEN sbt1.time_zone = 'new_time_zone_code_32' THEN 'Africa/Luanda'
        WHEN sbt1.time_zone = 'new_time_zone_code_33' THEN 'Europe/Athens'
        WHEN sbt1.time_zone = 'new_time_zone_code_34' THEN 'Africa/Harare'
        WHEN sbt1.time_zone = 'new_time_zone_code_35' THEN 'Europe/Helsinki'
        WHEN sbt1.time_zone = 'new_time_zone_code_36' THEN 'Africa/Cairo'
        WHEN sbt1.time_zone = 'new_time_zone_code_37' THEN 'Europe/Athens'
        WHEN sbt1.time_zone = 'new_time_zone_code_38' THEN 'Asia/Jerusalem'
        WHEN sbt1.time_zone = 'new_time_zone_code_39' THEN 'Asia/Baghdad'
        WHEN sbt1.time_zone = 'new_time_zone_code_40' THEN 'Asia/Riyadh'
        WHEN sbt1.time_zone = 'new_time_zone_code_41' THEN 'Europe/Moscow'
        WHEN sbt1.time_zone = 'new_time_zone_code_42' THEN 'Africa/Nairobi'
        WHEN sbt1.time_zone = 'new_time_zone_code_43' THEN 'Asia/Tehran'
        WHEN sbt1.time_zone = 'new_time_zone_code_44' THEN 'Asia/Muscat'
        WHEN sbt1.time_zone = 'new_time_zone_code_45' THEN 'Asia/Baku'
        WHEN sbt1.time_zone = 'new_time_zone_code_46' THEN 'Asia/Kabul'
        WHEN sbt1.time_zone = 'new_time_zone_code_47' THEN 'Asia/Yekaterinburg'
        WHEN sbt1.time_zone = 'new_time_zone_code_48' THEN 'Asia/Karachi'
        WHEN sbt1.time_zone = 'new_time_zone_code_49' THEN 'Asia/Calcutta'
        WHEN sbt1.time_zone = 'new_time_zone_code_50' THEN 'Asia/Katmandu'
        WHEN sbt1.time_zone = 'new_time_zone_code_51' THEN 'Asia/Novosibirsk'
        WHEN sbt1.time_zone = 'new_time_zone_code_52' THEN 'Asia/Almaty'
        WHEN sbt1.time_zone = 'new_time_zone_code_53' THEN 'Asia/Colombo'
        WHEN sbt1.time_zone = 'new_time_zone_code_54' THEN 'Asia/Rangoon'
        WHEN sbt1.time_zone = 'new_time_zone_code_55' THEN 'Asia/Krasnoyarsk'
        WHEN sbt1.time_zone = 'new_time_zone_code_56' THEN 'Asia/Bangkok'
        WHEN sbt1.time_zone = 'new_time_zone_code_57' THEN 'Africa/Monrovia'
        WHEN sbt1.time_zone = 'new_time_zone_code_58' THEN 'Asia/Singapore'
        WHEN sbt1.time_zone = 'new_time_zone_code_59' THEN 'Australia/Perth'
        WHEN sbt1.time_zone = 'new_time_zone_code_60' THEN 'Asia/Taipei'
        WHEN sbt1.time_zone = 'new_time_zone_code_61' THEN 'Asia/Ulaanbaatar'
        WHEN sbt1.time_zone = 'new_time_zone_code_62' THEN 'Asia/Tokyo'
        WHEN sbt1.time_zone = 'new_time_zone_code_63' THEN 'Asia/Seoul'
        WHEN sbt1.time_zone = 'new_time_zone_code_64' THEN 'Asia/Yakutsk'
        WHEN sbt1.time_zone = 'new_time_zone_code_65' THEN 'Australia/Adelaide'
        WHEN sbt1.time_zone = 'new_time_zone_code_66' THEN 'Australia/Darwin'
        WHEN sbt1.time_zone = 'new_time_zone_code_67' THEN 'Australia/Brisbane'
        WHEN sbt1.time_zone = 'new_time_zone_code_68' THEN 'Asia/Vladivostok'
        WHEN sbt1.time_zone = 'new_time_zone_code_69' THEN 'Pacific/Guam'
        WHEN sbt1.time_zone = 'new_time_zone_code_70' THEN 'Australia/Hobart'
        WHEN sbt1.time_zone = 'new_time_zone_code_71' THEN 'Australia/Sydney'
        WHEN sbt1.time_zone = 'new_time_zone_code_72' THEN 'Pacific/Guadalcanal'
        WHEN sbt1.time_zone = 'new_time_zone_code_73' THEN 'Pacific/Auckland'
        WHEN sbt1.time_zone = 'new_time_zone_code_74' THEN 'Pacific/Fiji'
        WHEN sbt1.time_zone = 'new_time_zone_code_75' THEN 'Pacific/Tongatapu'
        ELSE sbt1.time_zone
    END
)
WHERE sbt.id = sbt1.id;


UPDATE clock_in_task cit
INNER JOIN itech_bak.clock_in_task_2025_07_15 cit1 ON cit.id = cit1.id
SET cit.time_zone = (
    CASE
        WHEN cit1.time_zone = 'new_time_zone_code_1' THEN 'Etc/GMT+12'
        WHEN cit1.time_zone = 'new_time_zone_code_2' THEN 'Pacific/Apia'
        WHEN cit1.time_zone = 'new_time_zone_code_3' THEN 'Pacific/Honolulu'
        WHEN cit1.time_zone = 'new_time_zone_code_4' THEN 'America/Anchorage'
        WHEN cit1.time_zone = 'new_time_zone_code_5' THEN 'America/Los_Angeles'
        WHEN cit1.time_zone = 'new_time_zone_code_6' THEN 'America/Denver'
        WHEN cit1.time_zone = 'new_time_zone_code_7' THEN 'America/Phoenix'
        WHEN cit1.time_zone = 'new_time_zone_code_8' THEN 'America/Chihuahua'
        WHEN cit1.time_zone = 'new_time_zone_code_9' THEN 'America/Regina'
        WHEN cit1.time_zone = 'new_time_zone_code_10' THEN 'America/Chicago'
        WHEN cit1.time_zone = 'new_time_zone_code_11' THEN 'America/Guatemala'
        WHEN cit1.time_zone = 'new_time_zone_code_12' THEN 'America/Mexico_City'
        WHEN cit1.time_zone = 'new_time_zone_code_13' THEN 'America/Bogota'
        WHEN cit1.time_zone = 'new_time_zone_code_14' THEN 'America/New_York'
        WHEN cit1.time_zone = 'new_time_zone_code_15' THEN 'America/Indianapolis'
        WHEN cit1.time_zone = 'new_time_zone_code_16' THEN 'America/Halifax'
        WHEN cit1.time_zone = 'new_time_zone_code_17' THEN 'America/Caracas'
        WHEN cit1.time_zone = 'new_time_zone_code_18' THEN 'America/Santiago'
        WHEN cit1.time_zone = 'new_time_zone_code_19' THEN 'America/St_Johns'
        WHEN cit1.time_zone = 'new_time_zone_code_20' THEN 'America/Sao_Paulo'
        WHEN cit1.time_zone = 'new_time_zone_code_21' THEN 'America/Buenos_Aires'
        WHEN cit1.time_zone = 'new_time_zone_code_22' THEN 'America/Godthab'
        WHEN cit1.time_zone = 'new_time_zone_code_23' THEN 'America/Halifax'
        WHEN cit1.time_zone = 'new_time_zone_code_24' THEN 'Atlantic/Cape_Verde'
        WHEN cit1.time_zone = 'new_time_zone_code_25' THEN 'Atlantic/Azores'
        WHEN cit1.time_zone = 'new_time_zone_code_26' THEN 'Europe/London'
        WHEN cit1.time_zone = 'new_time_zone_code_27' THEN 'Asia/Shanghai'
        WHEN cit1.time_zone = 'new_time_zone_code_28' THEN 'Europe/Berlin'
        WHEN cit1.time_zone = 'new_time_zone_code_29' THEN 'Europe/Prague'
        WHEN cit1.time_zone = 'new_time_zone_code_30' THEN 'Europe/Paris'
        WHEN cit1.time_zone = 'new_time_zone_code_31' THEN 'Europe/Belgrade'
        WHEN cit1.time_zone = 'new_time_zone_code_32' THEN 'Africa/Luanda'
        WHEN cit1.time_zone = 'new_time_zone_code_33' THEN 'Europe/Athens'
        WHEN cit1.time_zone = 'new_time_zone_code_34' THEN 'Africa/Harare'
        WHEN cit1.time_zone = 'new_time_zone_code_35' THEN 'Europe/Helsinki'
        WHEN cit1.time_zone = 'new_time_zone_code_36' THEN 'Africa/Cairo'
        WHEN cit1.time_zone = 'new_time_zone_code_37' THEN 'Europe/Athens'
        WHEN cit1.time_zone = 'new_time_zone_code_38' THEN 'Asia/Jerusalem'
        WHEN cit1.time_zone = 'new_time_zone_code_39' THEN 'Asia/Baghdad'
        WHEN cit1.time_zone = 'new_time_zone_code_40' THEN 'Asia/Riyadh'
        WHEN cit1.time_zone = 'new_time_zone_code_41' THEN 'Europe/Moscow'
        WHEN cit1.time_zone = 'new_time_zone_code_42' THEN 'Africa/Nairobi'
        WHEN cit1.time_zone = 'new_time_zone_code_43' THEN 'Asia/Tehran'
        WHEN cit1.time_zone = 'new_time_zone_code_44' THEN 'Asia/Muscat'
        WHEN cit1.time_zone = 'new_time_zone_code_45' THEN 'Asia/Baku'
        WHEN cit1.time_zone = 'new_time_zone_code_46' THEN 'Asia/Kabul'
        WHEN cit1.time_zone = 'new_time_zone_code_47' THEN 'Asia/Yekaterinburg'
        WHEN cit1.time_zone = 'new_time_zone_code_48' THEN 'Asia/Karachi'
        WHEN cit1.time_zone = 'new_time_zone_code_49' THEN 'Asia/Calcutta'
        WHEN cit1.time_zone = 'new_time_zone_code_50' THEN 'Asia/Katmandu'
        WHEN cit1.time_zone = 'new_time_zone_code_51' THEN 'Asia/Novosibirsk'
        WHEN cit1.time_zone = 'new_time_zone_code_52' THEN 'Asia/Almaty'
        WHEN cit1.time_zone = 'new_time_zone_code_53' THEN 'Asia/Colombo'
        WHEN cit1.time_zone = 'new_time_zone_code_54' THEN 'Asia/Rangoon'
        WHEN cit1.time_zone = 'new_time_zone_code_55' THEN 'Asia/Krasnoyarsk'
        WHEN cit1.time_zone = 'new_time_zone_code_56' THEN 'Asia/Bangkok'
        WHEN cit1.time_zone = 'new_time_zone_code_57' THEN 'Africa/Monrovia'
        WHEN cit1.time_zone = 'new_time_zone_code_58' THEN 'Asia/Singapore'
        WHEN cit1.time_zone = 'new_time_zone_code_59' THEN 'Australia/Perth'
        WHEN cit1.time_zone = 'new_time_zone_code_60' THEN 'Asia/Taipei'
        WHEN cit1.time_zone = 'new_time_zone_code_61' THEN 'Asia/Ulaanbaatar'
        WHEN cit1.time_zone = 'new_time_zone_code_62' THEN 'Asia/Tokyo'
        WHEN cit1.time_zone = 'new_time_zone_code_63' THEN 'Asia/Seoul'
        WHEN cit1.time_zone = 'new_time_zone_code_64' THEN 'Asia/Yakutsk'
        WHEN cit1.time_zone = 'new_time_zone_code_65' THEN 'Australia/Adelaide'
        WHEN cit1.time_zone = 'new_time_zone_code_66' THEN 'Australia/Darwin'
        WHEN cit1.time_zone = 'new_time_zone_code_67' THEN 'Australia/Brisbane'
        WHEN cit1.time_zone = 'new_time_zone_code_68' THEN 'Asia/Vladivostok'
        WHEN cit1.time_zone = 'new_time_zone_code_69' THEN 'Pacific/Guam'
        WHEN cit1.time_zone = 'new_time_zone_code_70' THEN 'Australia/Hobart'
        WHEN cit1.time_zone = 'new_time_zone_code_71' THEN 'Australia/Sydney'
        WHEN cit1.time_zone = 'new_time_zone_code_72' THEN 'Pacific/Guadalcanal'
        WHEN cit1.time_zone = 'new_time_zone_code_73' THEN 'Pacific/Auckland'
        WHEN cit1.time_zone = 'new_time_zone_code_74' THEN 'Pacific/Fiji'
        WHEN cit1.time_zone = 'new_time_zone_code_75' THEN 'Pacific/Tongatapu'
        ELSE cit1.time_zone
    END
)
WHERE cit.id = cit1.id;