<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">
    <changeSet id="release_V1.25.32_tag" author="0668000911">
        <tagDatabase tag="release_V1.25.32_tag"/>
    </changeSet>

    <changeSet id="release_V1.25.32_0668000911" author="0668000911">
        <sqlFile path="db/release_V1.25.32/ddl.update.20250708.fengfudi.DT_BO-8511254.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.32/rollback/ddl.rollback.20250708.fengfudi.DT_BO-8511254.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>
    <changeSet id="release_V1.25.32_10309939" author="10309939">
        <sqlFile path="db/release_V1.25.32/data.update.20250715.kangtianshuo.DT_BO-8401256.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.32/rollback/data.rollback.20250715.kangtianshuo.DT_BO-8401256.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>
    <changeSet id="release_V1.25.32_10344315" author="10344315">
        <sqlFile path="db/release_V1.25.32/data.update.20250717.jiangjiagang.DT_BO-8511621.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        <rollback>
            <sqlFile path="db/release_V1.25.32/rollback/data.rollback.20250717.jiangjiagang.DT_BO-8511621.sql" encoding="utf8" stripComments="true" endDelimiter=";"/>
        </rollback>
    </changeSet>
</databaseChangeLog>
