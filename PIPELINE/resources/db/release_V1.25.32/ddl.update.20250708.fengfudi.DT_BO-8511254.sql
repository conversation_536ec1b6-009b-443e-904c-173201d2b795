ALTER TABLE `assignment` CHANGE `DELETED1149066329882480640` `assignment_type_text` varchar(2) COLLATE utf8mb4_general_ci GENERATED ALWAYS AS (json_unquote(json_extract(`assignment_type_ext`,_utf8mb4'$[0]'))) STORED COMMENT '生成列 - 任务类型';
ALTER TABLE `assignment` CHANGE `DELETED1149066329999921153` `representative_office_text` varchar(200) COLLATE utf8mb4_general_ci GENERATED ALWAYS AS (json_unquote(json_extract(`representative_office_ext`,_utf8mb4'$[0]'))) STORED COMMENT '生成列 - 代表处';
ALTER TABLE `assignment` CHANGE `DELETED1149066330117361664` `product_team` varchar(200) COLLATE utf8mb4_general_ci GENERATED ALWAYS AS (json_unquote(json_extract(`product_management_team_ext`,_utf8mb4'$[0]'))) STORED COMMENT '生成列 - 产品经营团队';

ALTER TABLE `assignment` RENAME KEY `idx_assignment_type_text` TO `ix_assignment_type_text`;
ALTER TABLE `assignment` RENAME KEY `idx_user_permission` TO `ix_user_permission`;
ALTER TABLE `assignment` RENAME KEY `idx_assignment_name` TO `ix_assignment_name`;
ALTER TABLE `assignment` RENAME KEY `idx_current_progress` TO `ix_current_progress`;

ALTER TABLE `assignment_network_change_ex` RENAME KEY `idx_operation_type` TO `ix_operation_type`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `idx_country` TO `ix_country`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `idx_importance` TO `ix_importance`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `idx_risk_evaluation` TO `ix_risk_evaluation`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `idx_operation_level` TO `ix_operation_level`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `idx_operation_result` TO `ix_operation_result`;
ALTER TABLE `assignment_network_change_ex` RENAME KEY `idx_operation_reason` TO `ix_operation_reason`;