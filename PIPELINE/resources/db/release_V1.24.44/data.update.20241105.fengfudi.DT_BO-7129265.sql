UPDATE change_order_p e
LEFT JOIN change_order m
ON e.id = m.id
SET e.cop_last_update_time = IFNULL(m.last_modified_time, current_timestamp);

ALTER TABLE change_order_p MODIFY `cop_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-p';

UPDATE change_order_rep_prod_td_approve e
LEFT JOIN change_order m
ON e.id = m.id
SET e.corpta_last_update_time = IFNULL(m.last_modified_time, current_timestamp);

ALTER TABLE change_order_rep_prod_td_approve MODIFY `corpta_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-代表处产品TD审核';
