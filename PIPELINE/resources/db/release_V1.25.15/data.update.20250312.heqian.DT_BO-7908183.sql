-- 将产品信息信息刷新到新增冗余字段，需要先导入低代码的包才有 redundan字段
UPDATE
	approver_config
SET
	redundan = COALESCE(prod_sub_category_id_path, prod_main_category_id_path,
	prod_line_id_path, prod_operation_team_id_path),
	redundan_ext =COALESCE (prod_sub_category_id_path_ext,	prod_main_category_id_path_ext,
	prod_line_id_path_ext,	prod_operation_team_id_path_ext)
WHERE
	is_deleted = 0
	AND redundan IS NULL
	AND JSON_OVERLAPS (approval_node_ext ,
	'["RD_LEADER","RD_DEPARTMENT","NETWORK_DEPT",
"TECHNOLOGY_DELIVERY_DEPT_NETWORK_OFFICE","REMOTE_CENTER_SOLUTION","REMOTE_CENTER_OPERATIONS"]');