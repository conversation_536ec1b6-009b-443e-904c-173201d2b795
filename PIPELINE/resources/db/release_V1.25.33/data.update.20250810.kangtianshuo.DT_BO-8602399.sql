-- 删除故障管理任务
CREATE TABLE IF NOT EXISTS itech_bak.fault_assignment_2025_08_09
SELECT id
FROM assignment
WHERE assignment_type_text = '5'
AND is_deleted = 0;

UPDATE assignment a
INNER JOIN itech_bak.fault_assignment_2025_08_09 fa ON a.id = fa.id
SET a.is_deleted = 1
WHERE a.id = fa.id;

-- 删除故障管理审批实体
CREATE TABLE IF NOT EXISTS itech_bak.fault_order_2025_08_09
SELECT id
FROM fault_management_task
WHERE is_deleted = 0;

UPDATE fault_management_task fmt
INNER JOIN itech_bak.fault_order_2025_08_09 fo ON fmt.id = fo.id
SET fmt.is_deleted = 1
WHERE fmt.id = fo.id;

-- 删除技术管理任务，及其子任务（当前仅复盘任务 / 整改任务 / 横推任务用到类型 8）
CREATE TABLE IF NOT EXISTS itech_bak.fault_technical_assignment_2025_08_09
SELECT id
FROM assignment
WHERE bill_id IN (
    SELECT a.bill_id
    FROM assignment a
    INNER JOIN lcap_schema_object_link_instance lli on a.id = lli.bill_id_out
    WHERE lli.link_type_id = '8')
AND is_deleted = 0;

UPDATE assignment a
INNER JOIN itech_bak.fault_technical_assignment_2025_08_09 fta ON a.id = fta.id
SET a.is_deleted = 1
WHERE a.id = fta.id;

-- 删除技术管理任务审批实体
CREATE TABLE IF NOT EXISTS itech_bak.fault_technical_order_2025_08_09
SELECT id
FROM technical_management_task
WHERE id IN (
    SELECT a.entity_id
    FROM assignment a
    INNER JOIN lcap_schema_object_link_instance lli on a.id = lli.bill_id_out
    WHERE lli.link_type_id = '8')
AND is_deleted = 0;

UPDATE technical_management_task tmt
INNER JOIN itech_bak.fault_technical_order_2025_08_09 fto ON tmt.id = fto.id
SET tmt.is_deleted = 1
WHERE tmt.id = fto.id;

-- 删除技术管理子任务审批实体
CREATE TABLE IF NOT EXISTS itech_bak.fault_sub_order_2025_08_09
SELECT id
FROM manage_sub_task_flow
WHERE parent_task_id IN (
          SELECT a.entity_id
          FROM assignment a
          INNER JOIN lcap_schema_object_link_instance lli on a.id = lli.bill_id_out
          WHERE lli.link_type_id = '8')
AND is_deleted = 0;

UPDATE manage_sub_task_flow mstf
INNER JOIN itech_bak.fault_sub_order_2025_08_09 fso ON mstf.id = fso.id
SET mstf.is_deleted = 1
WHERE mstf.id = fso.id;
