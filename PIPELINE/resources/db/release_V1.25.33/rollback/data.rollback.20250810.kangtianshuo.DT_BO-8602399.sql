-- 数据回滚
UPDATE assignment a
INNER JOIN itech_bak.fault_assignment_2025_08_09 fa ON a.id = fa.id
SET a.is_deleted = 0
WHERE a.id = fa.id;

UPDATE fault_management_task fmt
INNER JOIN itech_bak.fault_order_2025_08_09 fo ON fmt.id = fo.id
SET fmt.is_deleted = 0
WHERE fmt.id = fo.id;

UPDATE assignment a
INNER JOIN itech_bak.fault_technical_assignment_2025_08_09 fta ON a.id = fta.id
SET a.is_deleted = 0
WHERE a.id = fta.id;

UPDATE technical_management_task tmt
INNER JOIN itech_bak.fault_technical_order_2025_08_09 fto ON tmt.id = fto.id
SET tmt.is_deleted = 0
WHERE tmt.id = fto.id;

UPDATE manage_sub_task_flow mstf
INNER JOIN itech_bak.fault_sub_order_2025_08_09 fso ON mstf.id = fso.id
SET fso.is_deleted = 0
WHERE mstf.id = fso.id;

-- 删除备份表
DROP TABLE itech_bak.fault_assignment_2025_08_09;
DROP TABLE itech_bak.fault_order_2025_08_09;
DROP TABLE itech_bak.fault_technical_assignment_2025_08_09;
DROP TABLE itech_bak.fault_technical_order_2025_08_09;
DROP TABLE itech_bak.fault_sub_order_2025_08_09;