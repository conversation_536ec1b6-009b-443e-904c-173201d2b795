-- V1.25.11版本元数据升级脚本 - start
/**
 * 作者: 莫剑飞10335774
 * 日期: 2024-11-25
 * 功能: 数据源支持多租户，新增一个业务租户字段，并赋值默认值
 */
-- Started by AICoder, pid:wa41b91e64x34d414254083a200dae337f39c073
-- 创建存储过程以添加 business_tenant_id 列（如果不存在）
CREATE PROCEDURE add_business_tenant_id_column()
BEGIN
    -- 声明变量
    DECLARE column_exists INT DEFAULT 0;

    -- 检查字段是否存在
    SELECT COUNT(*)
    INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'lcap_datasource_config'
      AND COLUMN_NAME = 'business_tenant_id';

    -- 如果字段不存在，则添加字段
    IF column_exists = 0 THEN
        ALTER TABLE lcap_datasource_config
            ADD COLUMN business_tenant_id VARCHAR(64) DEFAULT 'systemCode' COMMENT '业务多租户';

        -- 更新现有记录，将 NULL 或空字符串设置为 'systemCode'
        UPDATE lcap_datasource_config
        SET business_tenant_id = 'systemCode'
        WHERE business_tenant_id IS NULL OR business_tenant_id = '';
    END IF;
END //


-- 执行存储过程
CALL add_business_tenant_id_column()//

-- 删除存储过程以清理环境
DROP PROCEDURE IF EXISTS add_business_tenant_id_column//


/**
* 作者: 李泽宇
* 日期: 2024-11-26
* 功能: 应用表支持多租户,修改为联合主键
*/
-- 1. 删除现有主键
ALTER TABLE lcap_application DROP PRIMARY KEY//

-- 2. 添加联合主键
ALTER TABLE lcap_application ADD PRIMARY KEY (id, tenant_id)//

 /**
 * 作者: 李泽宇
 * 日期: 2024-08-30
 * 功能: 应用表新增归属租户，应用分发之后，应用可能是属于N个租户，这些租户都有同一个归属生产方租户
 * 历史数据处理策略:初始化这个字段=tenant_id,原因:历史数据的租户都是10001没有其他租户应用
 *
 */
-- 创建存储过程
CREATE PROCEDURE AddBelongTenantIdColumn()
BEGIN
    DECLARE column_exists INT DEFAULT 0;

    -- 检查字段是否存在
    SELECT COUNT(*)
    INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'lcap_application'
      AND COLUMN_NAME = 'belong_tenant_id';

    -- 如果字段不存在，则添加
    IF column_exists = 0 THEN
        ALTER TABLE lcap_application
        ADD COLUMN `belong_tenant_id` VARCHAR(64) DEFAULT NULL COMMENT '所属租户字段' after tenant_id;
        update lcap_application set belong_tenant_id = tenant_id where belong_tenant_id is null;
    END IF;
END //

-- 执行存储过程
CALL AddBelongTenantIdColumn()//

-- 删除存储过程
DROP PROCEDURE IF EXISTS AddBelongTenantIdColumn//


-- 添加应用分发日志表
create table if not exists lcap_app_distribute_log (
  id varchar(64) not null COMMENT '主键',
  app_id varchar(64) not null COMMENT '应用id',
  tenant_id varchar(64) not null COMMENT '分发租户id',
  distributed_tenant_id varchar(64) not null COMMENT '被分发租户id',
  distribute_status  tinyint DEFAULT 0 COMMENT '分发状态0分发中，1分发成功，2分发失败',
  migrate_id varchar(64) DEFAULT NULL COMMENT '迁移id导入记录表的id',
  create_by varchar(32) DEFAULT NULL COMMENT '创建人',
  create_time datetime DEFAULT NULL COMMENT '创建日期',
  last_modified_by varchar(32) DEFAULT NULL COMMENT '更新人',
  last_modified_time datetime DEFAULT NULL COMMENT '更新日期',
  is_deleted tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除标志位',
  primary key (id)
) ENGINE = InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT = '分发日志表'//


/**
 * 作者: 莫剑飞10335774
 * 日期: 2024-11-30
 * 功能: 子表单弹窗布局新增是否开启有效性校验字段
 */
-- Started by AICoder, pid:qec34s955dcf4021490e0a4cf01787279eb45078
-- 创建存储过程
CREATE PROCEDURE add_is_validate_column()
BEGIN
    -- 检查字段是否存在
    DECLARE column_exists INT DEFAULT 0;
    SELECT COUNT(*)
    INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'lcap_layout'
      AND COLUMN_NAME = 'is_validate';

    -- 如果字段不存在，则添加
    IF column_exists = 0 THEN
        ALTER TABLE lcap_layout
            ADD COLUMN is_validate SMALLINT DEFAULT 1 COMMENT '是否开启有效性校验';
    END IF;
END //

-- 立即执行存储过程
CALL add_is_validate_column()//

-- 删除存储过程以清理环境
DROP PROCEDURE IF EXISTS add_is_validate_column//


/**
* 作者: 李泽宇
* 日期: 2024-11-26
* 功能: 导入模板表,自定义组件配置表添加联合主键租户id+主键id,支持应用分发多租户需求
*/
ALTER TABLE lcap_component_library_config DROP PRIMARY KEY//
ALTER TABLE lcap_component_library_config ADD PRIMARY KEY (id, tenant_id)//


/**
* 作者: 蔡睿
* 功能: 修改为联合主键
*/
-- 1. 删除现有主键
ALTER TABLE lcap_schema_change_solution DROP PRIMARY KEY//

-- 2. 添加联合主键
ALTER TABLE lcap_schema_change_solution ADD PRIMARY KEY (id, tenant_id)//

/**
* 作者: 黄剑雄
* 日期: 2024-12-25
* 功能: 修改自定义页面模板schema
*/

UPDATE lcap_schema_template
SET tenant_id='10001', template_code='Blank_Custom_Page', template_type='page', template_name_cn='空白页面', template_name_en='Blank Custom Page', description_cn='无预置字段，适用完全自定义的开发', description_en='Custom Page', content='{"pages": [{"id": "", "flowData": null, "formType": "display", "dataSource": {}, "formStatus": "offline", "componentsMap": [], "componentsTree": [{"id": "node_cvaiavio", "css": "body{background-color:#f2f3f5}", "props": {"__style__": ":root{\\n  background:#fff;\\n}", "className": "page_8rvoslt1", "pageStyle": {"backgroundColor": "#f2f3f5"}, "showTitle": true, "contentMargin": "20", "contentBgColor": "white", "contentPadding": "20", "templateVersion": "1.0.0", "contentMarginMobile": "0", "contentBgColorMobile": "white", "contentPaddingMobile": "0"}, "methods": {"__initMethods__": {"type": "js", "source": "function (exports, module) { /*set actions code here*/ }", "compiled": "function (exports, module) { /*set actions code here*/ }"}}, "children": [{"id": "node_s6oeb8a1x5a", "props": {"fieldId": "rootHeader_7is7s1cz"}, "elementType": "RootHeader", "componentName": "RootHeader"}, {"id": "node_1koa8guj619", "props": {"fieldId": "rootContent_u4rn6fgk", "__style__": ":root {\\n  padding: 20px;\\n}\\n"}, "elementType": "RootContent", "componentName": "RootContent"}, {"id": "node_ssdyb2j3qwp", "props": {"fieldId": "rootFooter_41gl1641"}, "elementType": "RootFooter", "componentName": "RootFooter"}], "dataSource": {"list": [], "sync": true, "online": [{"id": "85unhhx8lrp7wrtc", "name": "urlParams", "formUuid": "PAGE0882739062639706112", "protocal": "URI", "isReadonly": true, "description": "当前页面地址的参数：如 ida.zte.com.cn/APP_xxxx/workbench?id=1&name=爱搭，可通过 this.state.urlParams.name 获取到爱搭"}], "offline": [], "globalConfig": {}}, "lifeCycles": {"constructor": {"type": "js", "source": "function constructor() {\\nvar module = { exports: {} };\\nvar _this = this;\\nthis.__initMethods__(module.exports, module);\\nObject.keys(module.exports).forEach(function(item) {\\n  if(typeof module.exports[item] === ''function''){\\n  _this[item] = module.exports[item];\\n  }\\n});\\n\\n}", "compiled": "function constructor() {\\nvar module = { exports: {} };\\nvar _this = this;\\nthis.__initMethods__(module.exports, module);\\nObject.keys(module.exports).forEach(function(item){\\n  if(typeof module.exports[item] === ''function''){\\n  _this[item] = module.exports[item];\\n}\\n});\\n\\n}"}, "componentDidMount": {"id": "didMount", "name": "didMount", "type": "actionRef", "params": {}}, "componentWillUnmount": null}, "componentName": "Page"}]}], "schemaVersion": "v5"}', category='CustomPage', adaptor='PC', version='v1', logo=NULL, pictures=NULL, status='released', usage_count=0, create_by='10261216', create_time='2023-06-29 19:08:22.0', last_modified_by='10261216', last_modified_time='2024-12-12 10:03:44.0', is_deleted=0, `scope`='APP'
WHERE id='880223193097486228'//


# 25.11版本
/**
* 作者: 李夏隆
* 日期：2024-12-17
* 功能: 支持在线业务定制功能,添加 dev_type 字段
*/
ALTER TABLE lcap_page_schema_history
ADD COLUMN dev_type smallint not null default 0 comment "schema 开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_page_schema
ADD COLUMN dev_type smallint not null default 0 comment "schema 开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_page
ADD COLUMN dev_type smallint not null default 0 comment "schema 开发类型，0-原厂,1-二开，2-业务定制"//


/**
* 作者:黎元斌
* 日期：2024-12-18
* 功能: 支持在线业务定制功能,添加 dev_type 字段
*/
ALTER TABLE lcap_schema_entity_model_history
ADD COLUMN dev_type smallint not null default 0 comment "schema 开发类型，0-原厂,1-二开，2-业务定制";//

ALTER TABLE lcap_schema_entity_model
ADD COLUMN dev_type smallint not null default 0 comment "schema 开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_schema_entity
ADD COLUMN dev_type smallint not null default 0 comment "schema 开发类型，0-原厂,1-二开，2-业务定制"//

/**
* 作者: 王承彬
* 日期：2024-12-31
* 功能: 支持在线业务定制功能,添加 dev_type 字段
*/
ALTER TABLE lcap_layout
ADD COLUMN dev_type smallint not null default 0 comment "schema 开发类型，0-原厂,1-二开，2-业务定制"//

/**
* 作者: 谢叶胤
* 日期：2024-01-03
* 功能: 添加 dev_type 字段
*/
ALTER TABLE lcap_flow ADD dev_type varchar(8) DEFAULT 0 NULL COMMENT '元数据类型'//


/**
* 作者: 王礼章
* 日期：2025-02-17
* 功能: 修改单据标题翻译提示词
*/
update lcap_ai_prompt_config lac set lac.prompt = '**你的角色:** 假如你是一个翻译官
**你的任务:** 将用户的输入，根据不同场景返回翻译结果，翻译结果是个json对象，其中，input值为用户的输入值，outputEn为英文翻译结果，outPutZh为中文翻译结果，outputI18n为文案翻译结果，并遵守以下规则。
场景1：输入为全中文时，outputEn值要求将输入翻译成英文，且不同英文单词中通过下划线连接，单词头字母为小写；outputI18n值要求将输入翻译成英文，且每个单词首字母大写，空格隔开；
场景2：输入为全字母、下划线、数字的组合时，outputEn值遵循不同英文单词中通过下划线连接，大写字母改成小写字母的规则；outPutZh值要求将输入翻译成中文

**返回的结果示例**
```json
[
       {
              "input": "员工姓名",
              "outputEn": "employee_name",
              "outPutZh": "员工姓名",
              "outputI18n": "Employee Name"
       }
]
```

**用户的输入:**
```%s```
请按照上面的提示给出正确的json格式的结果，不需要使用```json```包裹结果，结果是个json数组，也不需要给出任何推理过程。'
where id = '100000023'//

-- -- V1.25.11版本元数据升级脚本 - end



-- V1.25.12版本元数据升级脚本 - start
-- 添加业务管理员资源表
create table if not exists lcap_biz_manager_resources (
  id varchar(64) not null COMMENT '主键',
  original_id varchar(64) not null COMMENT '原始数据id',
  code_ext1 varchar(64) COMMENT '预留编码字段1，如布局编码、流程编码',
  code_ext2 varchar(1000) COMMENT '预留编码字段2',
  resource_type varchar(200) not null COMMENT '数据类型，如layout/flow',
  create_by varchar(32) DEFAULT NULL COMMENT '创建人',
  create_time datetime DEFAULT NULL COMMENT '创建日期',
  last_modified_by varchar(32) DEFAULT NULL COMMENT '更新人',
  last_modified_time datetime DEFAULT NULL COMMENT '更新日期',
  tenant_id varchar(64) NOT NULL DEFAULT '10001' COMMENT '所属租户id',
  is_deleted tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除标志位',
  primary key (id),
  key idx_original_id_resource_type (original_id, resource_type)
) ENGINE = InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '业务管理员资源表'//


-- 扩展元素表
CREATE TABLE `lcap_meta_element_extend` (
	`id` VARCHAR(64) NOT NULL COMMENT '主键' COLLATE 'utf8mb4_general_ci',
	`CODE` VARCHAR(160) NOT NULL COLLATE 'utf8mb4_general_ci',
	`type` VARCHAR(32) NOT NULL COMMENT '类型' COLLATE 'utf8mb4_general_ci',
	`name_cn` VARCHAR(300) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`name_en` VARCHAR(300) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`package_name` VARCHAR(240) NOT NULL COMMENT '所属包名' COLLATE 'utf8mb4_general_ci',
	`group` VARCHAR(30) NOT NULL COMMENT '所属分组' COLLATE 'utf8mb4_general_ci',
	`content` JSON NULL DEFAULT NULL COMMENT '元素内容',
	`icon` VARCHAR(60) NULL DEFAULT NULL COMMENT 'icon' COLLATE 'utf8mb4_general_ci',
	`description` VARCHAR(300) NULL DEFAULT NULL COMMENT '描述' COLLATE 'utf8mb4_general_ci',
	`tenant_id` VARCHAR(64) NOT NULL DEFAULT '10001' COMMENT '租户id' COLLATE 'utf8mb4_general_ci',
	`create_by` VARCHAR(60) NOT NULL COMMENT '创建人账号' COLLATE 'utf8mb4_general_ci',
	`create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`last_modified_by` VARCHAR(60) NOT NULL COMMENT '最后更新人账号' COLLATE 'utf8mb4_general_ci',
	`last_modified_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
	`is_deleted` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '逻辑删除，1表示删除',
	`source_type` VARCHAR(64) NULL DEFAULT 'SYSTEM' COLLATE 'utf8mb4_general_ci',
	`source_app_id` VARCHAR(64) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`source_biz_obj_code` VARCHAR(64) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `idx_code_source_app_id` (`CODE`, `source_app_id`) USING BTREE,
	INDEX `idx_source_app_id_source_biz_obj_code` (`source_app_id`, `source_biz_obj_code`) USING BTREE
)
COMMENT='扩展元素表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB//

-- 扩展元素关系表
CREATE TABLE `lcap_meta_model_element_relationship_extend` (
	`id` VARCHAR(64) NOT NULL COMMENT '主键' COLLATE 'utf8mb4_general_ci',
	`model_id` VARCHAR(64) NOT NULL COMMENT '元模型id' COLLATE 'utf8mb4_general_ci',
	`element_id` VARCHAR(64) NOT NULL COMMENT '元素id' COLLATE 'utf8mb4_general_ci',
	`group` VARCHAR(30) NOT NULL COMMENT '分组' COLLATE 'utf8mb4_general_ci',
	`index` SMALLINT(5) NOT NULL DEFAULT '0' COMMENT '序号',
	`tenant_id` VARCHAR(64) NOT NULL DEFAULT '10001' COMMENT '租户id' COLLATE 'utf8mb4_general_ci',
	`create_by` VARCHAR(60) NOT NULL COMMENT '创建人账号' COLLATE 'utf8mb4_general_ci',
	`create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`last_modified_by` VARCHAR(60) NOT NULL COMMENT '最后更新人账号' COLLATE 'utf8mb4_general_ci',
	`last_modified_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
	`is_deleted` TINYINT(3) NOT NULL DEFAULT '0' COMMENT '逻辑删除，1表示删除',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `idx_element_id_tenant_id` (`element_id`, `tenant_id`, `is_deleted`) USING BTREE,
	INDEX `idx_model_id_tenant_id` (`model_id`, `tenant_id`) USING BTREE
)
COMMENT='元模型与扩展元素关系表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB//


/**
* 作者: 匡许良
* 功能: 业务套件规则树配置
*/
delete from `lcap_meta_rule` where code in ('bizFormPageRuleBizKitCondition','bizFormPageRuleBizKitAction')//

INSERT INTO `lcap_meta_rule` (`id`, `code`, `name_cn`, `name_en`, `group`, `status`, `other_metadata`, `tenant_id`, `create_by`, `create_time`, `last_modified_by`, `last_modified_time`, `is_deleted`) VALUES ('11000000310000', 'bizFormPageRuleBizKitCondition', '实体表单页面规则业务套件条件', 'bizFormPageRuleBizKitCondition', '3', '1', '{"supportFields": ["currentEntity", "currentEntityChildren"], "supportLayouts": ["allControl"], "supportFieldSegments": "allEntity", "supportFunctionTypes": ["generic", "math", "text", "date", "flow"], "supportLayoutSegments": "allLayout", "supportBlackListFunctions": ["showTableSideButton"], "supportWhiteListFunctions": ["getProperty", "getPageProperty"]}', '10001', '10321367', '2023-09-20 16:39:36', '10321367', '2024-06-26 14:16:19', 0)//

INSERT INTO `lcap_meta_rule` (`id`, `code`, `name_cn`, `name_en`, `group`, `status`, `other_metadata`, `tenant_id`, `create_by`, `create_time`, `last_modified_by`, `last_modified_time`, `is_deleted`) VALUES ('11000000320000', 'bizFormPageRuleBizKitAction', '实体表单页面规则业务套件操作', 'bizFormPageRuleBizKitAction', '3', '1', '{"supportFields": ["currentEntity", "currentEntityChildren"], "supportLayouts": ["allControl"], "supportFieldSegments": "allEntity", "supportFunctionTypes": ["generic", "math", "text", "date", "page", "flow"], "supportLayoutSegments": "allLayout", "supportBlackListFunctions": ["hideTableSideButton", "showTableSideButton", "lockTableSideButton", "unlockTableSideButton", "hideField", "hideFields", "showField", "showFields", "lockField", "lockFields", "unlockField", "unlockFields", "hideElement", "hideElements", "showElement", "showElements", "lockElement", "lockElements", "unlockElement", "unlockElements"], "supportWhiteListFunctions": []}', '10001', '10321367', '2023-09-21 20:59:24', '10321367', '2024-06-26 14:16:19', 0)//

-- 如果表较大，加字段可能较耗时。建议手动执行
-- 业务套件场景字段更长
ALTER TABLE lcap_schema_entity MODIFY entity_id VARCHAR(128)//

-- 业务规则增加来源字段
ALTER TABLE lcap_schema_rule_item ADD source_app_id VARCHAR(64), add source_parent_code VARCHAR(64), ADD source_code VARCHAR(64), add source_page_ids VARCHAR(1024)//

/**
 * 作者: 陈志强0668000989
 * 日期: 2024-12-30
 * 功能: 原厂、二开给应用增加标签
 */
-- Started by AICoder, pid:da09bkd837n06dc145df09d1a00479343a21940f
-- 创建存储过程以添加 app_type 列
CREATE PROCEDURE add_app_type_column()
BEGIN
    -- 声明变量以检查列是否存在
    DECLARE column_exists INT DEFAULT 0;

    -- 检查列是否存在
    SELECT COUNT(*)
    INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'lcap_application'
      AND COLUMN_NAME = 'app_type';

    -- 如果列不存在，则添加列
    IF column_exists = 0 THEN
        ALTER TABLE lcap_application
            ADD COLUMN app_type tinyint DEFAULT 0 COMMENT '业务多租户';
    END IF;
END //

-- 执行存储过程
CALL add_app_type_column()//

-- 删除存储过程以清理环境
DROP PROCEDURE IF EXISTS add_app_type_column//

-- 解释：
-- 1. 使用 `INFORMATION_SCHEMA.COLUMNS` 表检查列是否存在。
-- 2. 如果列不存在，使用 `ALTER TABLE` 添加列。
-- 3. 存储过程执行完毕后立即删除，以避免不必要的存储过程残留。

-- Ended by AICoder, pid:da09bkd837n06dc145df09d1a00479343a21940f

/**
* 作者: 刘江平
* 日期：2025-01-16
* 功能: 元素扩展表变更
*/
-- 新增字段
ALTER TABLE `lcap_meta_element_extend`
ADD COLUMN `enable_flag` int NOT NULL COMMENT '启用标识'//
ALTER TABLE `lcap_meta_element_extend`
ADD COLUMN `version` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本号'//
ALTER TABLE `lcap_meta_element_extend`
ADD COLUMN `dev_type` int NOT NULL  DEFAULT 0 COMMENT '开发类型 0原厂1二开2业务定制'//

-- 修改字段
ALTER TABLE lcap_meta_element_extend CHANGE source_type subtype varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子类型'//
ALTER TABLE lcap_meta_element_extend CHANGE source_app_id app_id varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用ID'//
ALTER TABLE lcap_meta_element_extend CHANGE source_biz_obj_code biz_obj_code varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务对象编码'//

-- 删除索引
DROP INDEX `idx_code_source_app_id` ON `lcap_meta_element_extend`//
DROP INDEX `idx_source_app_id_source_biz_obj_code` ON `lcap_meta_element_extend`//

-- 创建索引
CREATE INDEX `idx_type_app_id` ON `lcap_meta_element_extend` (`type`, `app_id`)//

/**
* 作者: 刘江平
* 日期：2025-01-16
* 功能: 元素扩展表变更
*/
DELETE FROM lcap_schema_template WHERE ID IN ('999263781135581186', '999263781135581187')//
INSERT INTO lcap_schema_template
(id, tenant_id, template_code, template_type, template_name_cn, template_name_en, description_cn, description_en, content, category, adaptor, version, logo, pictures, status, usage_count, create_by, create_time, last_modified_by, last_modified_time, is_deleted, `scope`)
VALUES('999263781135581186', '10001', 'Biz_Form_Kit', 'FormKit', '表单业务套件模板', 'Form Biz Kit Template', '表单业务套件模板', 'Form Biz Kit Template', '{"layouts": [{"code": "", "name": {"en_US": "Default Layout", "zh_CN": "默认布局"}, "appId": "", "schemas": [{"content": {"pages": [{"id": "", "flowData": null, "formType": "form", "dataSource": {}, "formStatus": "offline", "componentsMap": [], "componentsTree": [{"id": "BizKit_Id_123456", "props": {"kitType": "FormKit", "writeBack": true}, "children": [{"props": {}, "children": [{"props": {"width": 100, "isMerging": false}, "children": [], "elementType": "GridCol", "componentName": "GridCol"}], "elementType": "GridRow", "componentName": "GridRow"}], "elementType": "GridLayouter", "componentName": "GridLayouter"}]}], "schemaVersion": "v5"}}], "tenantId": "", "isDefault": true, "deviceType": "[\\"pc\\"]", "layoutType": "[\\"bizKit\\"]", "defaultFlag": 0, "bizObjectCode": "", "entityFieldId": ""}], "plugins": [], "version": "1.0.0", "metaCode": "BillFormModel", "ruleItems": [], "entityModule": {"meta": {"version": "1.0.0"}, "entityMeta": {"id": "", "name": {"type": "i18n", "en_US": "Blank Bill", "zh_CN": "空白单据"}, "appId": "", "entities": [{"id": "", "type": "BillEntity", "props": {"key": "id", "name": {"en_US": "Blank Bill", "zh_CN": "空白单据"}, "pkId": "", "tableName": "", "pkFieldName": "id", "billsConversion": false}, "fields": [{"id": "node_95415043", "type": "CreatorField", "props": {"key": "node_95415043", "name": "创建人", "fieldId": "node_95415043", "notNull": true, "fieldName": "create_by", "maxLength": 64, "showEmployeeNo": true, "disabledOperations": ["delete", "copy", "edit"]}, "nodeType": "Field", "parentId": "", "componentName": "TextField"}, {"id": "node_95415044", "type": "DateField", "props": {"key": "node_95415044", "name": "创建时间", "format": "YYYY-MM-DD", "fieldId": "node_95415044", "notNull": true, "fieldName": "create_time", "maxLength": 64, "disabledOperations": ["delete", "copy", "edit"], "reportChangeRecord": false}, "nodeType": "Field", "parentId": "", "componentName": "DateField"}, {"id": "node_95415045", "type": "ModifierField", "props": {"key": "node_95415045", "name": "修改人", "fieldId": "node_95415045", "notNull": true, "fieldName": "last_modified_by", "maxLength": 64, "showEmployeeNo": true, "disabledOperations": ["delete", "copy", "edit"]}, "nodeType": "Field", "parentId": "", "componentName": "TextField"}, {"id": "node_95415046", "type": "DateField", "props": {"key": "node_95415046", "name": "修改时间", "format": "YYYY-MM-DD", "fieldId": "node_95415046", "notNull": true, "fieldName": "last_modified_time", "maxLength": 64, "disabledOperations": ["delete", "copy", "edit"], "reportChangeRecord": false}, "nodeType": "Field", "parentId": "", "componentName": "DateField"}], "nodeType": "Entity"}], "modelType": "BizKitFormModel", "modelProps": {}}}, "engineVersion": "1.0.0", "operationItems": []}', 'Bill', 'PC', 'v1', NULL, NULL, 'released', 0, '10281355', '2024-10-31 19:23:05', '10284355', '2025-01-23 08:43:44', 0, 'APP')//
INSERT INTO lcap_schema_template
(id, tenant_id, template_code, template_type, template_name_cn, template_name_en, description_cn, description_en, content, category, adaptor, version, logo, pictures, status, usage_count, create_by, create_time, last_modified_by, last_modified_time, is_deleted, `scope`)
VALUES('999263781135581187', '10001', 'Biz_Blank_Kit', 'BlankKit', '空白业务套件模板', 'Blank Biz Kit Template', '空白业务套件模板', 'Blank Biz Kit Template', '{"layouts": [{"code": "", "name": {"en_US": "Default Layout", "zh_CN": "默认布局"}, "appId": "", "schemas": [{"content": {"pages": [{"id": "", "flowData": null, "formType": "form", "dataSource": {}, "formStatus": "offline", "componentsMap": [], "componentsTree": [{"id": "BlankKit_Id_123456", "props": {"kitType": "BlankKit", "writeBack": false}, "children": [{"props": {}, "children": [{"props": {"width": 100, "isMerging": false}, "children": [], "elementType": "GridCol", "componentName": "GridCol"}], "elementType": "GridRow", "componentName": "GridRow"}], "elementType": "GridLayouter", "componentName": "GridLayouter"}]}], "schemaVersion": "v5"}}], "tenantId": "", "isDefault": true, "deviceType": "[\\"pc\\"]", "layoutType": "[\\"bizKit\\"]", "defaultFlag": 0, "bizObjectCode": "", "entityFieldId": ""}], "plugins": [], "version": "1.0.0", "metaCode": "BillFormModel", "ruleItems": [], "entityModule": {"meta": {"version": "1.0.0"}, "entityMeta": {"id": "", "name": {"type": "i18n", "en_US": "Blank Bill", "zh_CN": "空白单据"}, "appId": "", "entities": [{"id": "", "type": "BillEntity", "props": {"key": "id", "name": {"en_US": "Blank Bill", "zh_CN": "空白单据"}, "pkId": "", "tableName": "", "pkFieldName": "id", "billsConversion": false}, "fields": [], "nodeType": "Entity"}], "modelType": "BizKitFormModel", "modelProps": {}}}, "engineVersion": "1.0.0", "operationItems": []}', 'Bill', 'PC', 'v1', NULL, NULL, 'released', 0, '10281355', '2024-10-31 19:23:05', '10284355', '2025-01-23 08:43:44', 0, 'APP')//


/**
* 作者: 于雪润
* 日期：2025-01-20
* 功能: 规则注册表，规则条目表增加dev_type字段
*/
ALTER TABLE `lcap_schema_rule_item`
ADD COLUMN `dev_type` tinyint NOT NULL DEFAULT '0' COMMENT '0:原厂,1:二开定制,2:业务配置'//

ALTER TABLE `lcap_register_function`
ADD COLUMN `dev_type` tinyint NOT NULL DEFAULT '0' COMMENT '0:原厂,1:二开定制,2:业务配置'//

/**
* 作者: 朱坤家
* 日期：2025-01-15
* 功能: 业务管理员支持创建业务对象，添加 dev_type 字段
*/
ALTER TABLE lcap_schema_operation_item ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_auth_res ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_schema_plugin ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//


/**
* 作者: 李夏隆
* 日期：2025-01-16
* 功能: 二开功能，相关资源类添加 dev_type 字段， 涉及 OpenAPI管理、变更方案、导入模板管理、插件包管理
*/
ALTER TABLE lcap_api ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_schema_change_solution ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_import_template ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_plugin_management ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

/**
* 作者: 李夏隆
* 日期：2025-01-16
* 功能: 二开功能，相关资源类添加 dev_type 字段， 涉及 服务注册，自定义组件
*/
ALTER TABLE lcap_service ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

ALTER TABLE lcap_component_library_config ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

/**
* 作者: 朱坤家
* 日期：2025-01-13
* 功能: 业务管理员支持创建业务对象，添加 dev_type 字段
*/
ALTER TABLE lcap_biz_object ADD COLUMN dev_type smallint not null default 0 comment "开发类型，0-原厂,1-二开，2-业务定制"//

/**
* 作者: 蔡睿
* 日期：2025-01-16
* 功能: 记录业务对象引用业务套件信息
*/
CREATE TABLE IF NOT EXISTS `lcap_biz_object_bizkit_reference` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用ID',
  `biz_object_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属业务对象编码',
  `version` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单据使用业务套件版本',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套件编码',
  `source_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套件来源业务对象',
  `source_page_ids` json DEFAULT NULL COMMENT '套件使用页面集合',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `last_modified_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标志位：0、生效 | 1、失效',
  `dev_type` smallint NOT NULL DEFAULT '0' COMMENT 'schema 开发类型，0-原厂,1-二开，2-业务定制',
  `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '10001' COMMENT '所属租户id',
  PRIMARY KEY (`id`),
  KEY `lcap_biz_object_bizkit_reference_app_id_IDX` (`app_id`) USING BTREE,
  KEY `lcap_biz_object_bizkit_reference_biz_object_code_IDX` (`biz_object_code`) USING BTREE,
  KEY `lcap_biz_object_bizkit_reference_code_IDX` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务对象套件引用关系表'//

/**
* 作者: 李泽瀚
* 日期：2025-01-23
* 功能: 应用设置-菜单管理
*/
CREATE TABLE IF NOT EXISTS `lcap_menu` (
  `id` varchar(64) NOT NULL COMMENT '菜单ID',
  `app_id` varchar(64) NOT NULL COMMENT '应用ID',
  `menu_code` varchar(64) NOT NULL COMMENT '菜单编码',
  `parent_code` varchar(64) DEFAULT NULL COMMENT '父节点编码',
  `menu_type` tinyint NOT NULL COMMENT '菜单类型, 0-GROUP, 1-ITEM',
  `menu_name_cn` varchar(100) NOT NULL COMMENT '菜单名称(中文)',
  `menu_name_en` varchar(100) NOT NULL COMMENT '菜单名称(英文)',
  `menu_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `link_type` varchar(20) DEFAULT NULL COMMENT '链接类型(PAGE/URL)',
  `link_url` varchar(500) DEFAULT NULL COMMENT '链接地址',
  `link_page_id` varchar(64) DEFAULT NULL COMMENT '关联页面ID',
  `tenant_id` varchar(20) NOT NULL DEFAULT '10001' COMMENT '租户ID',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `last_modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_app_id` (`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表'//

/**
* 作者: 匡许良
* 日期：2025-01-21
* 功能: 业务套件同步的规则标明来源类型
*/
ALTER TABLE lcap_schema_rule_item ADD COLUMN source_parent_type VARCHAR(32) comment "来源类型"//

/**
* 作者: 蔡睿
* 日期：2025-02-19
* 功能: 元素扩展表增加状态字段
*/
ALTER TABLE lcap_meta_element_extend ADD status int DEFAULT 0 NOT NULL COMMENT '套件状态，0-开发中，1-已发布'//


/**
* 作者: 丁小雨
* 日期：2025-02-21
* 功能: 菜单增加原厂标识
*/
ALTER TABLE lcap_menu ADD dev_type smallint DEFAULT 0 NOT NULL  COMMENT '菜单开发类型，0-原厂,1-二开，2-业务定制'//

/**
* 作者: 朱坤家
* 日期：2025-02-25
* 功能: 开发者可以回退布局版本，添加版本描述 version_description 字段
*/
ALTER TABLE lcap_page_schema ADD version_description varchar(300) DEFAULT NULL COMMENT '版本描述'//
ALTER TABLE lcap_page_schema_history ADD version_description varchar(300) DEFAULT NULL COMMENT '版本描述'//


/**
* 作者: 李夏隆
* 日期：2025-03-03
* 功能: 开发者可以回退布局版本，添加版本描述 version_description 字段
*/
ALTER TABLE lcap_schema_entity_model ADD version_description varchar(300) DEFAULT NULL COMMENT '版本描述'//
ALTER TABLE lcap_schema_entity_model_history ADD version_description varchar(300) DEFAULT NULL COMMENT '版本描述'//

/**
* 作者: 王礼章
* 日期：2025-03-06
* 功能: 规范lcap_meta_element_extend列名CODE为小写
*/
ALTER TABLE lcap_meta_element_extend CHANGE COLUMN CODE code VARCHAR(160) NOT NULL COLLATE 'utf8mb4_general_ci'//

/**
* 作者: 蔡睿
* 日期：2025-03-10
* 功能: 规则增加套件解析元数据
*/
delete from lcap_meta_rule lmr where code in ('bizFormPageRuleBizKitEntryCondition','bizFormPageRuleBizKitEntryAction')//
INSERT INTO lcap_meta_rule (id,code,name_cn,name_en,`group`,status,other_metadata,tenant_id,create_by,create_time,last_modified_by,last_modified_time,is_deleted) VALUES
     ('11000000410000','bizFormPageRuleBizKitEntryCondition','套件表单页面规则单据体条件','bizFormPageRuleBizKitEntryCondition','3','1','{"supportFields": ["currentEntityParent", "currentEntity", "currentEntityChildren"], "supportLayouts": ["currentControl"], "supportFieldSegments": "bothEntity", "supportFunctionTypes": ["generic", "math", "text", "date", "aggregation", "flow"], "supportLayoutSegments": "entryLayout", "supportBlackListFunctions": [], "supportWhiteListFunctions": ["getProperty", "getPageProperty"]}','10001','10321367','2023-09-21 21:01:51','10321367','2024-11-25 20:54:00',0),
     ('11000000420000','bizFormPageRuleBizKitEntryAction','套件表单页面规则单据体操作','bizFormPageRuleBizKitEntryAction','3','1','{"supportFields": ["currentEntityParent", "currentEntity", "currentEntityChildren"], "supportLayouts": ["currentControl", "currentControlChildren"], "supportFieldSegments": "bothEntity", "supportFunctionTypes": ["generic", "math", "text", "date", "page", "aggregation", "flow"], "supportLayoutSegments": "entryLayout", "supportBlackListFunctions": ["hideField", "hideFields", "showField", "showFields", "lockField", "lockFields", "unlockField", "unlockFields", "hideElement", "hideElements", "showElement", "showElements", "lockElement", "lockElements", "unlockElement", "unlockElements"], "supportWhiteListFunctions": []}','10001','10321367','2023-09-21 21:03:23','10321367','2024-11-25 20:54:00',0)//
-- V1.25.12版本元数据升级脚本 - end