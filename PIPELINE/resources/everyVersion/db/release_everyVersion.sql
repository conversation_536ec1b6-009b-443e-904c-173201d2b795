UPDATE batch_network_assignment_batch_result SET batch_result_last_update_time = current_timestamp WHERE batch_result_last_update_time IS NULL;
ALTER TABLE batch_network_assignment_batch_result MODIFY batch_result_last_update_time datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp comment '反馈操作结果-修改时间';

UPDATE subcontractor_batch_task_batch_result SET batch_result_last_update_time = current_timestamp WHERE batch_result_last_update_time IS NULL;
ALTER TABLE subcontractor_batch_task_batch_result MODIFY batch_result_last_update_time datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp comment '反馈操作结果-修改时间';

UPDATE change_order_p SET cop_last_update_time = current_timestamp WHERE cop_last_update_time IS NULL;
ALTER TABLE change_order_p MODIFY `cop_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-p';

UPDATE change_order_rep_prod_td_approve SET corpta_last_update_time = current_timestamp WHERE corpta_last_update_time IS NULL;
ALTER TABLE change_order_rep_prod_td_approve MODIFY `corpta_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-代表处产品TD审核';

UPDATE change_order_td_net_dept_approve SET td_last_update_time = current_timestamp WHERE td_last_update_time IS NULL;
ALTER TABLE change_order_td_net_dept_approve MODIFY `td_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-技术交付部/网络处审核';

UPDATE subcontractor_oc_net_dept SET nd_last_update_time = current_timestamp WHERE nd_last_update_time IS NULL;
ALTER TABLE subcontractor_oc_net_dept MODIFY `nd_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-网络处审核';

UPDATE change_order_net_service_dept_app SET net_last_update_time = current_timestamp WHERE net_last_update_time IS NULL;
ALTER TABLE change_order_net_service_dept_app MODIFY `net_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-网络服务部审核';

UPDATE change_order_rd_manager SET rd_last_update_time = current_timestamp WHERE rd_last_update_time IS NULL;
ALTER TABLE change_order_rd_manager MODIFY `rd_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-研发经理审核';


-- 任务表生成列
ALTER TABLE `assignment` MODIFY COLUMN `assignment_type_text` VARCHAR(2) GENERATED ALWAYS AS (json_unquote(`assignment_type_ext`->'$[0]')) STORED COMMENT '生成列 - 任务类型';
ALTER TABLE `assignment` MODIFY COLUMN `representative_office_text` varchar(200) GENERATED ALWAYS AS (json_unquote(`representative_office_ext`->'$[0]')) STORED COMMENT '生成列 - 代表处';
ALTER TABLE `assignment` MODIFY COLUMN `product_team` varchar(200) GENERATED ALWAYS AS (json_unquote(`product_management_team_ext`->'$[0]')) STORED COMMENT '生成列 - 产品经营团队';

---- 网络变更扩展表生成列
ALTER TABLE `assignment_network_change_ex` MODIFY COLUMN `operation_type_text` VARCHAR(100) GENERATED ALWAYS AS (json_unquote(`operation_type_ext`->'$[0]')) STORED COMMENT '生成列 - 操作类型';
ALTER TABLE `assignment_network_change_ex` MODIFY COLUMN `country_text` VARCHAR(20) GENERATED ALWAYS AS (json_unquote(`country_ext`->'$[0]')) STORED COMMENT '生成列 - 国家';
ALTER TABLE `assignment_network_change_ex` MODIFY COLUMN `importance_text` VARCHAR(2) GENERATED ALWAYS AS (json_unquote(`importance_ext`->'$[0]')) STORED COMMENT '生成列 - 重要程度';
ALTER TABLE `assignment_network_change_ex` MODIFY COLUMN `risk_evaluation_text` VARCHAR(2) GENERATED ALWAYS AS (json_unquote(`risk_evaluation_ext`->'$[0]')) STORED COMMENT '生成列 - 风险评估';
ALTER TABLE `assignment_network_change_ex` MODIFY COLUMN `operation_level_text` VARCHAR(2) GENERATED ALWAYS AS (json_unquote(`operation_level_ext`->'$[0]')) STORED COMMENT '生成列 - 操作等级';
ALTER TABLE `assignment_network_change_ex` MODIFY COLUMN `operation_result_text` VARCHAR(2) GENERATED ALWAYS AS (json_unquote(`operation_result_ext`->'$[0]')) STORED COMMENT '生成列 - 操作结果';
ALTER TABLE `assignment_network_change_ex` MODIFY COLUMN `operation_reason_text` VARCHAR(100) GENERATED ALWAYS AS (json_unquote(`operation_reason_ext`->'$[0]')) STORED COMMENT '生成列 - 操作原因';

-- 内部批次操作结果审核入湖新增更新时间字段
UPDATE batch_network_assignment_batch_result_app SET result_review_last_update_time = current_timestamp WHERE result_review_last_update_time IS NULL;
ALTER TABLE batch_network_assignment_batch_result_app MODIFY `result_review_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-操作结果审核';

-- 合作方批次操作结果审核入湖新增更新时间字段
UPDATE subcontractor_batch_task_batch_result_app SET result_review_last_update_time = current_timestamp WHERE result_review_last_update_time IS NULL;
ALTER TABLE subcontractor_batch_task_batch_result_app MODIFY `result_review_last_update_time` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp COMMENT '最后更新时间-操作结果审核';
