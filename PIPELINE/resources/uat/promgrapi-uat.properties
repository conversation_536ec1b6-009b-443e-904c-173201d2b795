register.address = msruat.zte.com.cn:10081
register.node.ip = ************
register.node.port = 8888
register.version = v1
servicecenter = msb

jdbc1.type = com.alibaba.druid.pool.DruidDataSource

jdbc1.driverClassName = com.goldendb.jdbc.Driver

# \u4F4E\u4EE3\u7801\u5143\u6570\u636E\u5E93\u914D\u7F6E\uFF0C\u6309\u7167msa4.0.3\u52A0\u5BC6\u65B9\u5F0F\u81EA\u884C\u52A0\u5BC6\uFF0CMSA4.0.3\u5347\u7EA7\u8BF4\u660E

jdbc1.url = *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

jdbc.maxActive = 100
jdbc.minIdle = 50
jdbc.initialSize = 50
jdbc.timeBetweenEvictionRunsMillis = 60000
jdbc.minEvictableIdleTimeMillis = 300000
jdbc.testWhileIdle = true
jdbc.testOnBorrow = true
jdbc.testOnReturn = true

#uac\u7528\u6237\u7528\u81EA\u5DF1\u7684\u76F8\u5173\u73AF\u5883\u914D\u7F6E
uac.sdk.tenantId = 10001
uac.sdk.appId = ************
uac.sdk.issuer = https://uactest.zte.com.cn/zte-sec-uac-iportalbff/oidc/10001
uac.sdk.baseUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-promgrbff
uac.sdk.clientId = ************

uac.sdk.responseRule = msa
uac.sdk.store = redis
uac.url = https://uactest.zte.com.cn
uac.sdk.cookie.httpOnly = false
uac.sdk.gateAuth.externalAccessEnabled = true
uac.sdk.cookie.secure = true
# \u6CE8\u610F\u751F\u4EA7\u914D\u7F6Eprod\u4E00\u5B9A\u8981\u6253\u5F00\u6B64\u914D\u7F6E\uFF0C\u5F00\u53D1\u6D4B\u8BD5\u73AF\u5883\u914D\u7F6Edev\u6216\u8005\u4E0D\u914D\u7F6E
# uac.sdk.env = prod

# \u83B7\u53D6\u8D26\u6237\u4FE1\u606F\u4F7F\u7528uac3.0\u67E5\u8BE2\uFF08\u5185\u573A\u73AF\u5883\uFF1AuacEmployeeProvider\uFF0C \u5916\u573A\u73AF\u5883\uFF1AuacEmployeeOuterProvider\uFF09
lcap.employee.provider = uacEmployeeProvider
# uac3.0\u6279\u91CF\u67E5\u8BE2\u8D26\u6237\u7C7B\u578B\uFF08\u5185\u573A\u73AF\u5883\uFF1AT0001\uFF0C \u5916\u573A\u73AF\u5883\uFF1AT0000\uFF09
uac.idType = T0001
# uac3.0\u79D8\u94A5 \u53C2\u8003\u8FDE\u63A5 \u3010V1.24.33\u53CA\u4EE5\u4E0A\u7248\u672C\u3011\u4F4E\u4EE3\u7801\u5E94\u7528\u5F00\u53D1\u5E73\u53F0\u4E0D\u5B89\u5168\u52A0\u5BC6\u65B9\u5F0F\u6574\u6539\u901A\u77E5

#\u6587\u6863\u4E91\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
cloudDiskSDK.host = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk
cloudDiskSDK.httpClientLogSwitch = true
cloudDiskSDK.xOriginServiceName = EPMS

#\u5E94\u7528\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u7684\u76F8\u5173\u914D\u7F6E

#\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u7684kafka\u914D\u7F6E\uFF0C\u4E0D\u7528\u4FEE\u6539
approval.kafka.topic = zte-iss-approval-nj
#\u4F7F\u75284A\u7533\u8BF7\u7684appId
approval.sdk.app.appId = ************
#\u4F7F\u75284A\u7533\u8BF7\u7684secretKey

#\u4F7F\u75284A\u7533\u8BF7\u7684appId
approval.sdk.app.appCode = ************
#\u5E94\u7528\u81EA\u5B9A\u4E49\u7684\u9ED8\u8BA4\u5DE5\u53F7\uFF08\u4EFB\u610F\uFF09
approval.sdk.header.xEmpNo.default = 10284287
approval.sdk.kafka.receipt.enabled = false
#\u5BA1\u6279\u4E2D\u5FC3\u56DE\u8C03\u5E94\u7528\u7684url
approval.sdk.webhook.url = https://itech.uat.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/flow/webhookProcess
# \u6D41\u7A0BSDK\u662F\u5426\u9700\u8981\u4F20\u9012tenantId\u8BF7\u6C42\u5934
approval.sdk.request.header.tenantId.source = true

#\u7F13\u5B58\u4E91\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09https://i.zte.com.cn/#/shared/569154dd0096477f9824fe2286c110fb/wiki/page/4fc5d276e28343cb9425bbc91acaeeed/view

msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-itech-uat
cacheCloud.server = http://mspuat.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4
msa.redis.database = 7
redis.customCacheManager = true
msa.redis.driveType = jedis
msa.redis.serializerType = genericJackson2Json
msa.redis.sotimeout = 1000
msa.redis.maxAttempts = 2
msa.redis.maxRedirects = 5
msa.redis.pool.maxActive = 250
msa.redis.pool.maxWait = 3000
msa.redis.pool.maxIdle = 300
msa.redis.pool.minIdle = 300
msa.redis.pool.minEvictableIdleTimeMillis = 1800000
msa.redis.timeout = 3000
msa.redis.cacheManager.expire = 86400

# inone \u5E94\u7528\u7F16\u7801(\u5E73\u53F0\u5BF9\u5E94\u7684\u5382\u5546:ZXLCAP_PRO300 \u5E94\u7528:LCAP_PRO300_SUBS)

inone.url = https://icosg.uat.zte.com.cn
#kafak\u76F8\u5173\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

message.server = http://uat.sz-message.zcloud.zte.com.cn:8888/zte-itp-msp-message/v1
spring.flyway.enabled = false
spring.flyway.encoding = UTF-8
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
# \u5E94\u7528\u5217\u8868\u6570\u636E\u6743\u9650\u7F16\u7801
#----------\u4EE3\u7801\u5E93\u6709\u7684\u503C\u5DF2\u2018##\u2019\u6807\u5FD7----------

spring.flyway.locations = classpath:db/migration/V12432/update
# redis cacheManager\u8FC7\u671F\u65F6\u95F4,\u5355\u4F4D\u79D2\uFF0C\u9ED8\u8BA4\u4E3A0,\u4EE3\u8868\u6C38\u4E0D\u8FC7\u671F

# redis cacheManager\u8FC7\u671F\u65F6\u95F4,\u5355\u4F4D\u79D2\uFF0C\u9ED8\u8BA4\u4E3A0,\u4EE3\u8868\u6C38\u4E0D\u8FC7\u671F

#\u7F13\u5B58\u4E91\u8D44\u6E90\u7F16\u7801

spring.flyway.sql-migration-prefix = V

#\u96C6\u7FA4\u95F4\u6700\u5927\u8DF3\u8F6C\u6B21\u6570,\u5982\u679C\u4E0D\u8BBE\u7F6EmaxRedirects\u503C\uFF0C\u6E90\u7801\u4E2D\u9ED8\u8BA4\u4E3A5\u3002\u4E00\u822C\u5F53\u6B64\u503C\u8BBE\u7F6E\u8FC7\u5927\u65F6\uFF0C\u5BB9\u6613\u62A5\uFF1AToo many Cluster redirections

spring.flyway.sql-migration-separator = __

spring.flyway.validate-on-migrate = false

spring.flyway.baseline-on-migrate = true

spring.flyway.placeholder-replacement = false

spring.flyway.table = promgrapi_flyway_schema_history

spring.flyway.out-of-order = true

##info/health/prometheus\u5B89\u5168\u6CBB\u7406\u5BB9\u5668\u6539\u52A8

msa.expose.info.enable = true

#\u7EC4\u4EF6\u5E93\u4E0A\u4F20\u670D\u52A1\u914D\u7F6E\uFF0C\u5BF9\u5E94\u4F4E\u4EE3\u7801\u81EA\u8EABdemoapi\u670D\u52A1\u540D\u548C\u7248\u672C\u53F7\uFF0C\u6D4B\u8BD5\u73AF\u5883\uFF1AV3,protest:V3,uat:V2,prouat:V3\uFF0C\u751F\u4EA7:V1

lcap.app.upload.target.service = zte-iccp-itech-demoapi.v1

lcap.app.upload.target.path = zte-iccp-itech-demoapi

#\u4F4E\u4EE3\u7801\u670D\u52A1\u73AF\u5883\u7BA1\u7406\u9879\u76EE\u540D\u548C\u7248\u672C\u53F7\uFF0CTEST:V1,PROTEST:V3,UAT:V1,PROUAT\uFF1AV3,\u751F\u4EA7\uFF1AV1

lcap.mc.version.no = v1

lcap.mc.service.name = zte-paas-lcap-promcapi

# mgr\u8C03\u7528mc\u6807\u51C6\u670D\u52A1\u7684\u57DF\u540D+bff+\u670D\u52A1\u540D >=24.41\u7248\u672C\u9700\u8981\u914D\u7F6E\uFF08\u8FD9\u91CC\u76F4\u63A5\u5C06\u751F\u4EA7\u7684\u914D\u7F6E\u6807\u51C6\u793A\u4F8B\u8D34\u8FDB\u6765\uFF0C\u6D4B\u8BD5\u73AF\u5883\u66FF\u6362\u6D4B\u8BD5\u73AF\u5883\u57DF\u540D\u5373\u53EF\uFF09
lcap.mc.route.url = https://lcappro.uat.zte.com.cn/zte-paas-lcap-promcbff/zte-paas-lcap-promcapi

#msa\u52A0\u5BC6
msa.encrypt.datakey = u72fq23XdFpsT0/cwv9imCCAtRqo2K50ZI92a7sGWnN3Gm2oIf5OnWGl05eVNZ0bpLF7RDBU9xMC9i99ARrGyQ==
msa.rootkey.factor1 = zte-iccp-itech-promgrapi-uat
msa.ccs.encrypt.factor = ENC(kENQhldz7nMhgB3SO8fL88Yck+nj5cQ1GDGV1Ir3gDtCF6FlRuquDbZSUfpfPYCDlbshIshMdNY=)

#kms\u914D\u7F6E\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn
msa.security.server.dt-kms.appName = ENC(MRqlV12nrAqdJKnUZgJUjdkDmrfWux5q5rXl0glQq5f5GPeAk5YtZLBRug==)
msa.security.server.dt-kms.userId = ENC(/sb///cynkegs1PbXENMZNf0nBF4j4i0+4fLfNyZzr/9IMhoHAi8eoa7I8o=)
msa.security.server.dt-kms.appSecret = ENC(M9a8LkLhvZk1JDMK7F4mWj0sXm0ImlAu8Gt9YfRIQTqsn+LxMFFC5C/wzfpho4S4pOyz)
msa.security.server.dt-kms.defaultMasterKey = itechcloud

#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:demoapi:demoapi:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = MThjNjFmMzNiNmRjYjU3MTQ2OTVjYjliMDUyNTI1ZTY4MjEwNDhjNDQ0NWE0MzA4ZDQ0NzhmM2RhMmQ4Zjk1Y2E5NjdlNzIxYzgxN2U5NzNhOGMwOGQ2YjQ3YWU4OTA1$$NzExMTAxZDBjYjFiNTU2ZDk2MGExYjBlY2M0Zjc3MDE=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3

msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.cipher = YWQ5ZTNkNWQxYWQwMzVlOGFjYzc5ZDc0ZTUxMTAyODU3YzgxZmU5MTYwMzllYWVlZDFlOGViMWJlNTZiMmQ5OGRiZDJlYTMyYzQ4NWZiZTczMDQxMjVkNTg3YjdhNTk4$$MzgxNzkzYzJiNDg2NGMxNmQ5NTUyZGFjYmNmNjA0MzY=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.sensitiveData = YWJlZDY5MzM5NzNlYWZlMzFhODJhNjgxOGFkODI2NTAyM2ZjMGNhMmM2ZmFhZGM2NmJmNjA1Y2NjMTRjMTBlMzZhOWIxMjJmM2MzNTM3MDhmMjY3OWVmMTIzOGQwNmVl$$NzQ4ZjMwMGJiMzZlZTExYmNmYzRlZDQ5ZGFjMDExYmY=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3


# \u4EE5\u4E0B\u53C2\u6570\u9700\u91CD\u65B0\u7528kms\u52A0\u5BC6
jdbc1.username = KENC(3iWVNjVCc7+ygHN1VifBuV5JaKQ/dw==$$/L70Pqp/yvZCKFYT)
jdbc1.password = KENC(BoeIsfCuEFUBcfoO1Vaw54cSrBmhzmzGxm/z/7M=$$La5SVgUMh58WfMIN)
cloudDiskSDK.xOrgId = KENC(b3+xjpI5Mnd1drxPEjWUTGFpnTuDIqrUUNLMV1rBQg==$$wi3g1vzOYuHm1a2u)
cloudDiskSDK.xSecretKey = KENC(78QSVzh5vxFtfVk2mSB9qwgOatdoNIYV5PjHQdocOg==$$Zog3amnWMoVvlKGw)
approval.sdk.app.secretKey = KENC(8ZTdUuwLagzjP+GLaixayscdfqjlOielVvzo8LvKg259ZwQ/Rbdg3ftOEX/NCC6KC5WrbIcFPUhmo9dfZPSNmEjCvGfBHovmFQIyKqCZY2s=$$JryAZ5toA+08B94j)
uac.sdk.clientSecret = KENC(a0DMt/3Zd3f77tkOndQpdx6B9I47gKb1ZjL1Ixc1QU/5wLCX3i0+Y+0XG10yAXpzKBtvPRIdx9O24rf4+LG2t3iojFOO2uRxA4FPAOvIAo8=$$qDffGvfcSmU/7xLb)
uac.sdk.accessKey = KENC(DLtyxiNayVe62TV/5Pm/jNIp8y/TOwnjdKeLXL+6W2yQqrU2i1VJF/vuaR1J2mMu$$QaGbw1wNGUKwx9T2)
uac.sdk.accessSecret = KENC(X02ItlsyJ0SjO961Wv0btEVef/IbKKzI/z+P1PnlW65egfSGQ++BLqtEolvm0ANOmBRdoK1sxBLHrqa6aquVrLuvyiMt0zVCkGeo5Icw8Kw=$$ARtbaYHT191rHdj+)
uac.key = KENC(a8ZE48vGiS8NumZmUkaf67a4bpry3YMAlkF4RkV/taY=$$JKv+/bKgzQ7YUMu+)
inone.appCode = KENC(qCvF0b4KEU34KiK2rfPQf233aW232NYaSzZFjLCM8l79MhCwmbUj5xwllcxDEmVe$$BZyqMjFv28akLFWn)

# \u9700\u8981\u4E0Edemoapi\u79D8\u94A5\u76F8\u540C
common.app.param.secret = KENC(AewUIIXx1y6zdGSqeuhfIDLWiHQVNFPQtm3x3EZf8heQ2gd+KyvbLKgJGHOD1FY8$$2kHJJWUFzzjumGRd)
datasource.config.gcm.encrypt.key = KENC(cRVzKu6G5SF1ZeelIEwVoGb+C/mS9kDMbiQm5Xh3Uh4GpupOgiL64Dl5EnLCqNmB$$45QkN62vkRf0yk1I)

# \u6570\u636E\u6E90\u52A0\u89E3\u5BC6\u76F8\u5173\u53C2\u6570
# \u8BE6\u7EC6\u751F\u6210\u6307\u5357\u8BF7\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/e76cd1492ed642b1b6b711cd4025285b/view
common.rsa.public-key = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjWKOYSdA2DK6YTwxDYtY2r9v4jyfgPViPT32HG0QC0kcTTkpwyocZOV1jXq7Q4QsOuMhDSWQvyweW2UsxMX6Ki6P5zJ3oiXdk4HtMjMKf8DbsHM3PngLq1z7uc57itcPDmI79mOYaILFKfc8kdkz6ta0JCSamrtwujDmTaeZEC8sIPB56ee8cfr7SIWRqaJpxVsY3l1hKkpaUFdaFgcHj4TNmbh06nX1DTqTEXBqTFshfCg3mOliKQUCrx5eTzdaauUHOtGm0JRDD9JfR7CUVgV0VWHc2j0e0JEcrORrfQJqDRvLjMCyARjcPJ5YJrf3+dFONm2k1MDr68T7DUUEWQIDAQAB

common.rsa.private-key = KENC(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$$MdvQpfghYW7CFp2c)
