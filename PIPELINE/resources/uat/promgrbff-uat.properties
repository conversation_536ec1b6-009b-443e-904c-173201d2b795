upp.kafka.topic = sec-upp-api
# \u6D4B\u8BD5\u73AF\u5883  \u751F\u4EA7 kafka-upp-001.dt.zte.com.cn:9092
upp.kafka.bootstrap-servers = 10.54.154.197:9092,10.54.154.200:9092,10.54.155.106:9092
upp.kafka.consumer.enableAutoCommit = false
upp.kafka.consumer.autoOffsetReset = latest
# \u4E1A\u52A1\u540D\u79F0\uFF0C\u4FDD\u8BC1\u552F\u4E00\uFF0C\u63A8\u8350\u4F7F\u7528\u4E1A\u52A1\u65B9\u670D\u52A1\u540D\u5373\u53EF
upp.kafka.consumer.groupId = zte-iccp-itech-promgrbff
upp.kafka.listen.enabled = true
# upp.sha256.salt\u3001upp.auth.encryptor\u9700\u8981\u4F7F\u7528kms\u52A0\u5BC6\uFF0C\u539F\u6587\u8BF7\u67E5\u770B\u6B64\u9875\u9762 https://i.zte.com.cn/index/ispace/#/space/61445c56c7ec4dd3ad1610aa6bdc43d5/wiki/page/480777e72ab44eeba0e4626b23c1b496/view
# \u5982\u679C\u6CA1\u6709\u9875\u9762\u6743\u9650\u8BF7\u8054\u7CFB\u674E\u6CFD\u5B87\uFF080668001050\uFF09
# \u5982\u679C\u6CA1\u6709\u9875\u9762\u6743\u9650\u8BF7\u8054\u7CFB\u674E\u6CFD\u5B87\uFF080668001050\uFF09KENC(xxxxx)
upp.auth.encryptor = MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQ4jFhRnywOFZrLak94eVayPQti43zMlHAZZSl1AVkxJzbI3y44HiVMsyrO9kfZmr6LXp5B/LH1qxp650x8utG7qesScUTe0vl16eKyU5EIBOYJqO4DVa6aP4206vj1e27WQbahWArf9YSsL9++GhXFehqjXA3zRMwhyEhNw2D1QIDAQAB
upp.sha256.salt = JOz5usHTQ7jT1KELLKmPLIvou3dhyCHC/F+HwU5K4zgqGVQUG98PBWymJwZY3QqqXPIGL5auKXA/84XQyG4afxnD/WxLfr1/60nG8WREpHidpKIx50fUEK/NvxyKs+7s

#*****************************************************
#MSB
register.address = msruat.zte.com.cn:10081
register.node.ip = ************
register.node.port = 8888
servicecenter = msb
#\u670D\u52A1\u7248\u672C
register.version = v1

# msa gateway \u914D\u7F6E
msa.gateway.routes[0].id = ucsRoute
msa.gateway.routes[0].uri = forward:/ucs/
msa.gateway.routes[0].stripPrefix = true
msa.gateway.routes[0].predicates[0] = Path=/ucs/**
msa.gateway.routes[1].id = zte-iccp-itech-promgrapi
msa.gateway.routes[1].uri = lb://zte-iccp-itech-promgrapi/v1
msa.gateway.routes[1].predicates[0] = Path=/zte-iccp-itech-promgrapi/**
msa.gateway.routes[2].id = uac
msa.gateway.routes[2].stripPrefix = true
msa.gateway.routes[2].uri = forward:/uac/
msa.gateway.routes[2].predicates[0] = Path=/uac/**
msa.gateway.routes[3].id = infoRoute
msa.gateway.routes[3].uri = http://127.0.0.1:${management.server.port}/${spring.application.name}/
msa.gateway.routes[3].stripPrefix = false
msa.gateway.routes[3].predicates[0] = Path=/info

msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-001
msa.ccs.encrypt.factor = ENC(tX9QX466x29hab2/QBpYRPF3Eg8PZp0JzZlYrgRXI51ItnowVFrykOUm8L+eRYgTjEi+Det5vDM=)
msa.encrypt.datakey = ImRc9tg7hbatAcVQjvTOOwcrMBDRinKTiKqyhbXzTB/wTw9dX2xu6BmVBolgz+pBKVOFTNUTaEB8NcMKUDO3hA==
msa.rootkey.factor1 = zte-iccp-itech-promgrbff

#\u7EDF\u4E00\u6743\u9650\u5BF9\u63A5\u83B7\u53D6\u6743\u9650\u914D\u7F6E \u5BF9\u5E94\u53C2\u6570\u54A8\u8BE2 \u5F6D\u7EC610236545 \u4E0D\u8D70\u7EDF\u4E00\u6743\u9650\u4E0D\u914D\u7F6E
#\u7EDF\u4E00\u6743\u9650\u5BF9\u63A5\u83B7\u53D6\u6743\u9650\u914D\u7F6E \u5BF9\u5E94\u53C2\u6570\u54A8\u8BE2 \u5F6D\u7EC610236545 \u4E0D\u8D70\u7EDF\u4E00\u6743\u9650\u4E0D\u914D\u7F6E
upp.auth.productId = 1561334
upp.auth.moduleId = 993688769932
upp.auth.tenantId = 4304
# \u4EA7\u54C1\u5BC6\u94A5key\uFF0C\u5411\u7EDF\u4E00\u6743\u9650\u7D22\u8981\uFF08\u533A\u5206\u73AF\u5883\uFF09
# \u4EA7\u54C1\u5BC6\u94A5key\uFF0C\u5411\u7EDF\u4E00\u6743\u9650\u7D22\u8981\uFF08\u533A\u5206\u73AF\u5883\uFF09
upp.auth.productSecretKey = kS+J5rvC8YkAA5P5+6sUf5sn4CZCAb2Z

#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-authbff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-authbff
upp.auth.authUrl = https://uactest.zte.com.cn:8082/zte-sec-upp-authbff

#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-bff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-bff
upp.auth.manageUrl = https://uactest.zte.com.cn:8082/zte-sec-upp-bff

uac.sdk.tenantId = 10001
uac.sdk.appId = 993688769932
uac.sdk.issuer = https://uactest.zte.com.cn/zte-sec-uac-iportalbff/oidc/10001
uac.sdk.baseUrl = https://itech.uat.zte.com.cn/zte-iccp-itech-promgrbff
uac.sdk.clientId = 993688769932
uac.sdk.clientSecret = d81c9c3fb73447868bbb2806cc9ab34261404e82f0ddfc0bf93d6c7294c3ef43
uac.sdk.accessKey = 993688769932ty4i6i7e7arkecaedctk
uac.sdk.accessSecret = d48a27f6208ce8bb041b64d3b0d3cc7153d5b38b1487be03b0bec4f94bc530ba

#uac\u5347\u7EA7\u53C2\u6570\u589E\u52A0\uFF1A\u6D4B\u8BD5\u4EFF\u771F\u73AF\u5883\u79D8\u94A5\u914D\u7F6E\u53D8\u66F4\u66FF\u6362
#\u5E94\u7528\u8BBF\u95EE\u7BA1\u63A7\u8BA4\u8BC1\u52A0\u5BC6\u5BC6\u94A5\uFF0C\u4E1A\u52A1\u4FA7\u81EA\u884C\u52A0\u5BC6\u5B58\u50A8\uFF0C\u89E3\u5BC6\u4F7F\u7528 test = testtgyarxsaxtdy
uac.sdk.appAuth.encryptKey = uatkfeidyuafyngk
#token\u4EE4\u724C\u8BA4\u8BC1\u52A0\u5BC6\u5BC6\u94A5\uFF0C\u4E1A\u52A1\u4FA7\u81EA\u884C\u52A0\u5BC6\u5B58\u50A8\uFF0C\u89E3\u5BC6\u4F7F\u7528 test = uat5XQmnKynEPjwZ
uac.sdk.token.encryptKey = uat5XQmnKynEPjwZ

# \u751F\u4EA7\u914D\u7F6Eprod\uFF0C\u5F00\u53D1\u6D4B\u8BD5\u73AF\u5883\u914D\u7F6Edev\u6216\u4E0D\u914D\u7F6E
# uac.sdk.env = prod
msa.gateway.tokenAuthEnable = false
msa.gateway.uac.enabled = false
uac.sdk.responseRule = msa
uac.sdk.cookie.httpOnly = true
uac.sdk.gateAuth.externalAccessEnabled = true
uac.sdk.cookie.secure = true
uac.sdk.store = redis
cacheCloud.server = http://mspuat.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4
msa.redis.database = 9


# \u5176\u4ED6\u914D\u7F6E
# swagger\u6587\u6863\u5730\u5740\u8BBF\u95EE\u914D\u7F6E\uFF0C\u6D4B\u8BD5\u73AF\u5883\u7532\u4E59\u914D\u7F6Etrue\uFF0C\u751F\u4EA7\u73AF\u5883\u5EFA\u8BAE\u914D\u7F6Efalse
springfox.documentation.enabled = false
# \u81EA\u5B9A\u4E49\u7EC4\u4EF6\u5E93\u5305\u4E0A\u4F20\u5927\u5C0F
ui.config.app.package.upload.size = 200
ui.config.project.watermarkURL = https://itas.test.zte.com.cn:555/inject-font/config.js, https://itas.test.zte.com.cn:555/inject-font/font.js
ui.config.project.privacyPolicyBaseUrl = https://uactest.zte.com.cn
#\u5BA2\u6237\u7AEF\u63D0\u4EA4\u7684\u8868\u5355\u8BF7\u6C42\u6765\u6E90\u8FDB\u884C\u5408\u6CD5\u6027\u6821\u9A8C\uFF0C\u9632\u6B62CSRF\u653B\u51FB
common.referrerMap[refererPatterns] = *.zte.com.cn
