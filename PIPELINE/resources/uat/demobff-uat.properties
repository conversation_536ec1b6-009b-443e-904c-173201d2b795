# \u5927\u6587\u4EF6\u4E0A\u4F20\u8D85\u65F6\u95EE\u9898
ribbon.ReadTimeout = 120000
hystrix.command.default.execution.timeout.enabled = true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds = 120000
#\u5934\u4FE1\u606F\u8FC7\u5927\uFF0C\u5BFC\u81F4\u8BF7\u6C42400\u5F02\u5E38
server.max-http-header-size=40000

# \u6D4B\u8BD5\u73AF\u5883 upp.auth.authUrl=https://uactest.zte.com.cn/zte-sec-upp-authbff
upp.kafka.topic = sec-upp-api
# \u6D4B\u8BD5\u73AF\u5883 upp.kafka.bootstrap-servers = 10.54.158.157:9092,10.54.158.158:9092,10.54.158.159:9092;
upp.kafka.bootstrap-serversc = 10.54.154.197:9092,10.54.154.200:9092,10.54.155.106:9092
upp.kafka.consumer.enableAutoCommit = false
upp.kafka.consumer.autoOffsetReset = latest
# \u4E1A\u52A1\u540D\u79F0\uFF0C\u4FDD\u8BC1\u552F\u4E00\uFF0C\u63A8\u8350\u4F7F\u7528\u4E1A\u52A1\u65B9\u670D\u52A1\u540D\u5373\u53EF
upp.kafka.consumer.groupId = zte-iccp-itech-demobff
upp.kafka.listen.enabled = true
# upp.sha256.salt\u3001upp.auth.encryptor\u9700\u8981\u4F7F\u7528kms\u52A0\u5BC6\uFF0C\u539F\u6587\u8BF7\u67E5\u770B\u6B64\u9875\u9762 https://i.zte.com.cn/index/ispace/#/space/61445c56c7ec4dd3ad1610aa6bdc43d5/wiki/page/480777e72ab44eeba0e4626b23c1b496/view
# \u5982\u679C\u6CA1\u6709\u9875\u9762\u6743\u9650\u8BF7\u8054\u7CFB\u674E\u6CFD\u5B87\uFF080668001050\uFF09KENC(xxxxx)
upp.auth.encryptor = MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQ4jFhRnywOFZrLak94eVayPQti43zMlHAZZSl1AVkxJzbI3y44HiVMsyrO9kfZmr6LXp5B/LH1qxp650x8utG7qesScUTe0vl16eKyU5EIBOYJqO4DVa6aP4206vj1e27WQbahWArf9YSsL9++GhXFehqjXA3zRMwhyEhNw2D1QIDAQAB
upp.sha256.salt = JOz5usHTQ7jT1KELLKmPLIvou3dhyCHC/F+HwU5K4zgqGVQUG98PBWymJwZY3QqqXPIGL5auKXA/84XQyG4afxnD/WxLfr1/60nG8WREpHidpKIx50fUEK/NvxyKs+7s

# \u4E1A\u52A1\u65B9\u72EC\u7ACB\u90E8\u7F72demoapi\u7684\u670D\u52A1\u540D\uFF08\u5207\u8BB0\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u5426\u5219\u4F1A\u6743\u9650\u6821\u9A8C\u6709\u95EE\u9898\uFF09
demoapi.service.name=zte-iccp-itech-demoapi
# \u53EF\u4EE5\u4E0D\u914D\u7F6E\uFF0C\u6DFB\u52A0\u8BE5\u914D\u7F6E\u5219\u901A\u8FC7\u5F53\u524D\u9875\u6253\u5F00\u9875\u9762\u65F6\u4E0D\u663E\u793A\u5934\u90E8
ui.config.project.currentPage.hideTop.appIds=993688769932

# 22\u7248\u672C\u65B0\u589E\u4E86\u5355\u9009\uFF08\u65B0\uFF09\u3001\u4E0B\u62C9\u5355\u9009\uFF08\u65B0\uFF09\u5B57\u6BB5\uFF0C\u662F\u5426\u652F\u6301\u65E7\u5B57\u6BB5\uFF0C\u9ED8\u8BA4\u4E3Afalse\uFF0C\u5373\u4E0D\u652F\u6301\u3002\u5982\u679C\u914D\u7F6Etrue\uFF0C\u9700\u8981\u914D\u5408 ui.config.project.oldFeildEnv \u4F7F\u7528
# \u8BE6\u60C5\u8BF7\u89C1\uFF1ALCAP-PRO 25.22\u7248\u672C\u66F4\u65B0\u2014\u65B0\u589E\u5355\u9009\uFF08\u65B0\uFF09\u3001\u4E0B\u62C9\u5355\u9009\uFF08\u65B0\uFF09\u3001\u7EC4\u7EC7\uFF08\u65B0\uFF09\u5B57\u6BB5&&\u65E5\u671F\u652F\u6301\u65F6\u533A\u4F18\u5316\u3001\u6D41\u7A0B\u8282\u70B9\u652F\u6301\u81EA\u5B9A\u4E49\u6821\u9A8C\u7B49
ui.config.project.supportOldFeild = true 
# \u652F\u6301\u65E7\u5B57\u6BB5\u7684\u73AF\u5883\uFF0C\u652F\u6301\u914D\u7F6E\u591A\u4E2A\u73AF\u5883\u7F16\u7801\uFF08\u4E3A\u4F4E\u4EE3\u7801\u5E73\u53F0\u7684\u73AF\u5883\u7BA1\u7406\u9875\u9762\u91CC\u7684\u73AF\u5883\u7F16\u7801\uFF09\uFF0C\u7528\u9017\u53F7\u9694\u5F00\uFF0C\u4F8B\u5982\uFF1Adev1,dev2 #
ui.config.project.oldFeildEnv = iTechCloudUAT

register.node.ip = ************
register.node.port = 8888

servicecenter = msb
upp.auth.tenantId = 4304
upp.auth.authUrl = https://uactest.zte.com.cn/zte-sec-upp-authbff
upp.auth.manageUrl = https://uactest.zte.com.cn/zte-sec-upp-bff
ucs.sdk.mode = ucstoken
ucs.sdk.issuer = http://test55.ucs.zte.com.cn/zte-bmt-ucs-portalbff/oidc/10001
ucs.sdk.clientId = 10001
ucs.sdk.secretKey = KENC(v2874g5xYUba+qKA1mfMJuYSLHDK9tBCdSoKcYqwz9MpO0K9A/7kDjSUNLDy5lbcQ0w3mg==$$yrl4SsAmCqhH3S1Z)
ucs.sdk.baseUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff
ucs.sdk.scope = openid
ucs.sdk.store = memory
ucs.sdk.checkState = false
msa.gateway.routes[0].id = zte-iccp-itech-demoapi
msa.gateway.routes[0].uri = lb://zte-iccp-itech-demoapi/v1
msa.gateway.routes[0].predicates[0] = Path=/zte-iccp-itech-demoapi/**
msa.gateway.routes[0].stripPrefix = true
msa.gateway.routes[1].id = zte-iccp-itech-demoapi
msa.gateway.routes[1].uri = lb://zte-iccp-itech-demoapi/v1
msa.gateway.routes[1].predicates[0] = Path=/zte-iccp-itech-demoapi/**
msa.gateway.routes[2].id = uac
msa.gateway.routes[2].stripPrefix = true
msa.gateway.routes[2].uri = forward:/uac/
msa.gateway.routes[2].predicates[0] = Path=/uac/**
#prometheus\u63A5\u53E3\u6E17\u900F\u6D4B\u8BD5\u654F\u611F\u4FE1\u606F\u6CC4\u9732\u6CBB\u7406\uFF08\u6CE8\u610Froutes[*]\u5E8F\u53F7,\u9700\u8981\u6309\u7167\u5B9E\u9645\u60C5\u51B5\u6B63\u5E8F\u4FEE\u6539\uFF09
msa.gateway.routes[3].id = infoRoute
msa.gateway.routes[3].stripPrefix = false
msa.gateway.routes[3].uri = http://127.0.0.1:${management.server.port}/${spring.application.name}/
msa.gateway.routes[3].predicates[0] = Path=/info
msa.gateway.routes[4].id = zte-crm-iepms-docfile
msa.gateway.routes[4].uri = lb://zte-crm-iepms-docfile/v1
msa.gateway.routes[4].predicates[0] = Path=/zte-crm-iepms-docfile/**
msa.gateway.routes[4].stripPrefix = false
ui.config.project.isShowShare = true
ui.config.project.isProduct = true
ui.config.project.isShowClose = true
ui.config.project.isShowProcessStatus = true
ui.config.app.package.upload.size = 200
ui.config.notice.center.url = https://icenter-systest.test.zte.com.cn/zte-icenter-notice-messageweb/#/template-management
uac.sdk.tenantId = 10001
uac.sdk.issuer = https://uactest.zte.com.cn:5555/zte-sec-uac-iportalbff/oidc/10001
uac.sdk.baseUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff
uac.sdk.cookie.httpOnly = false

uac.sdk.gateAuth.externalAccessEnabled = true

uac.sdk.cookie.secure = true
uac.sdk.token.httpOnlyReadHeaderEnabled = true
uac.sdk.clientSecret = KENC(oUzdTX34X5NNd5rHwm/434mpWmfdtrInwICwYH9rgnXIrAtvedZPJD1/5/76PQfPOWTB9bcGNlUoXMaO7XNbtFUkVIDwEViYmQLEL0qzYz8=$$Q9FCUMC1tScxIRse)
uac.sdk.appId = 261677920140
uac.sdk.clientId = 261677920140
uac.sdk.accessKey = KENC(****************************************************************$$vuV+kkPBEYcK3mhL)
uac.sdk.accessSecret = KENC(zHagrXraWW9RCO7SECru3OsVwvEWaGk2J5qDxQx3ctlDSCqw7X2hJqEpOXO8vUlvN/pxlXoTAoq++mpY/6IQY3Vgtw8tyBLaGCrlLNqGnO4=$$Yvspvuv7E55QmopQ)

uac.sdk.appAuth.encryptKey = KENC(FWw4fiGnhpQlD3EB62yyznCL3d87bZHHxDHLeeI9s9s=$$h3y4JtaW3llSzOB/)


uac.sdk.token.encryptKey = KENC(DYAZIgmwCVTOsmbc1AZzKM6iycvYOezaE3se5b5dm9Y=$$BsP5kshPs1iR5bBt)
upp.auth.productId = 6751046






upp.auth.moduleId = 261677920140

upp.auth.productSecretKey = KENC(kKIz5fzkZ6DJCZD1t8k6R9nX0AIwBH7GnEjD9VFicfK8kbUFxxcdoeUhk4np8aXd$$ub7M0Kze6U7kKznt)
register.version = v1
msa.gateway.tokenAuthEnable = false
msa.gateway.uac.enabled = false
uac.sdk.responseRule = msa
uac.sdk.store = redis
msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-003
cacheCloud.server = http://mspuat.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4
msa.redis.pool.maxIdle = 300
msa.redis.pool.minIdle = 300
msa.redis.database = 2
url.white.list = /zte-iccp-itech-demoapi/customComponents/resources/download/**,/zte-iccp-itech-demoapi/v1/flow/webhookProcess,/zte-iccp-itech-demoapi/APP0984032412495249408/grantfiles,/zte-iccp-itech-demoapi/APP0984032412495249408/batchtasks/**/4auth,/zte-iccp-itech-demoapi/idop/**,/zte-plm-iversion-api/**,/zte-iccp-itech-demoapi/networksecurity/**,/zte-iccp-itech-demoapi/cnop/**,/zte-iccp-itech-demoapi/common/**
ui.config.project.isPreview = true
msa.ccs.encrypt.factor = ENC(/zqDPYGks8uYDbS8JPSCP/FzHORL3CqJOCl5Q3nkZdj9dOmjb/16PlvKoESBxUDRq5I0O5q3TXY=)
msa.encrypt.datakey = l+Fb6yh2JhjUez8Belk3CbR82NtMEc9tG6f487oMqXW0bv6xT0PnToSQECff5BZatZruboMIKE/U6Nbv70yIvg==
msa.rootkey.factor1 = zte-iccp-itech-demobff


# \u8DF3\u8F6C\u767B\u5F55uac\u73AF\u5883\u6807\u8BC6
# \u8DF3\u8F6C\u767B\u5F55uac\u6D4B\u8BD5\u73AF\u5883uactest.zte.com.cn:5555\uFF1Atest
# \u8DF3\u8F6C\u767B\u5F55uac81\u73AF\u5883uactest.zte.com.cn\uFF1Auat
# \u8DF3\u8F6C\u767B\u5F55uac\u751F\u4EA7\u73AF\u5883\uFF0C\u9ED8\u8BA4\u4E3A\u7A7A\uFF0C\u65E0\u9700\u914D\u7F6E
uac.login.env = test

#\u65E5\u5FD7\u4E2D\u5FC3\u914D\u7F6E(\u5FC5\u586B)\uFF0C\u9700\u8981\u4E1A\u52A1\u7533\u8BF7topic\uFF0C\u53EF\u8054\u7CFB\u8C22\u65FB\u7FF00668001012\uFF0C\u4F4E\u4EE3\u7801\u914D\u7F6E\u4E3Ams-zte-lcap-app
isoalog.kafka.topic = ms-zte-lcap-app

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Methods\u7684\u53C2\u6570,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3AGET,POST,DELETE,PUT,OPTIONS
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = GET,POST,DELETE
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Methods =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Origin,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3A*.zte.com.cn
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = https://lcap.test.zte.com.cn,http://lcap.dev.zte.com.cn(\u53EF\u914D\u7F6E\u591A\u4E2A\u57DF\u540D\uFF0C\u9700\u8981\u52A0\u4E0Ahttp/https\u8BF7\u6C42)
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Origin =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Headers,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3A*
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = x-requested-with
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Headers =


#kms\u76F8\u5173\u52A0\u5BC6\u53C2\u6570\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn
msa.security.server.dt-kms.appName = ENC(iaey1QFKEGtG1X1v8LAS5NW0A0aWp3LQ1JzC0FqFCLB5kbeh6NHj/RJ1dg==)
msa.security.server.dt-kms.userId = ENC(lhQtTCKGmhYLwE+3jeJqqdjbETQs791NZxYSSRNGZT7hsICL/zxLE7hoKXM=)
msa.security.server.dt-kms.appSecret = ENC(vhDMoDT/*****************************/tHaRFPaUpZSlVZsMRrFRjKdjjNhW0V)
msa.security.server.dt-kms.defaultMasterKey = itechcloud

#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:demobff:demobff:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = YzU2YTMwOTQ4YTVkZjVhZjgzMzI5NzliNGUwZmE3YzllZjBmY2MxM2U5OTk3YjIzNzAyOGYwNGM0NjEzNzM2NzAyOWJhMGJhZGFiNmJiMmI2M2QxMTMxMjBkZDc3NTc3$$NzQ3ZWU4ODIxNDllM2E4NmI4M2JmMjdhM2FjYWVmMzQ=&&ZDcyMGJmN2UtNDNjNi00MGM5LThmNGUtYzYzNjA5ZGZhMzFl
