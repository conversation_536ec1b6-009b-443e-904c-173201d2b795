upp.kafka.topic = sec-upp-api
# \u6D4B\u8BD5\u73AF\u5883  \u751F\u4EA7 kafka-upp-001.dt.zte.com.cn:9092
upp.kafka.bootstrap-servers = 10.54.158.157:9092,10.54.158.158:9092,10.54.158.159:9092
upp.kafka.consumer.enableAutoCommit = false
upp.kafka.consumer.autoOffsetReset = latest
# \u4E1A\u52A1\u540D\u79F0\uFF0C\u4FDD\u8BC1\u552F\u4E00\uFF0C\u63A8\u8350\u4F7F\u7528\u4E1A\u52A1\u65B9\u670D\u52A1\u540D\u5373\u53EF
upp.kafka.consumer.groupId = zte-iccp-itech-promgrbff
upp.kafka.listen.enabled = true
# upp.sha256.salt\u3001upp.auth.encryptor\u9700\u8981\u4F7F\u7528kms\u52A0\u5BC6\uFF0C\u539F\u6587\u8BF7\u67E5\u770B\u6B64\u9875\u9762 https://i.zte.com.cn/index/ispace/#/space/61445c56c7ec4dd3ad1610aa6bdc43d5/wiki/page/480777e72ab44eeba0e4626b23c1b496/view
# \u5982\u679C\u6CA1\u6709\u9875\u9762\u6743\u9650\u8BF7\u8054\u7CFB\u674E\u6CFD\u5B87\uFF080668001050\uFF09
# \u5982\u679C\u6CA1\u6709\u9875\u9762\u6743\u9650\u8BF7\u8054\u7CFB\u674E\u6CFD\u5B87\uFF080668001050\uFF09KENC(xxxxx)
upp.auth.encryptor = KENC(MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCD1DZ4iy/N+X8Iop4hRIlqY4VOSuM4KhlUFBvfDwVspkaoSyzxXfT6cDlVm954eegOxp6zvv/r6bJBxqeXXjejKWCFkRLsCI7JsYANaP+k3u1zgNY+suA7m2DYEKnu5V/7UI/2wY8DATvIRpXuvQCHJI2bsA8cxpzaMMXe5NiZIQIDAQAB)
upp.sha256.salt = KENC(lMLmiXoDThWbmH8pFsguLIvou3dhyCHC/F+HwIOcr/UPA7bJUFqO9qQhJwZY3QqqXPIGL5auKXA/84XQyG4afxnD8IHncp5/wLuw8WREpHidpKRWzCqhk7R3/WxLf/Wg)

#*****************************************************
#MSB
register.address = msrtest.zte.com.cn:10081
register.node.ip = ************
register.node.port = 8888
servicecenter = msb
#\u670D\u52A1\u7248\u672C
register.version = v1

# msa gateway \u914D\u7F6E
msa.gateway.routes[0].id = ucsRoute
msa.gateway.routes[0].uri = forward:/ucs/
msa.gateway.routes[0].stripPrefix = true
msa.gateway.routes[0].predicates[0] = Path=/ucs/**
msa.gateway.routes[1].id = zte-iccp-itech-promgrapi
msa.gateway.routes[1].uri = lb://zte-iccp-itech-promgrapi/v1
msa.gateway.routes[1].predicates[0] = Path=/zte-iccp-itech-promgrapi/**
msa.gateway.routes[2].id = uac
msa.gateway.routes[2].stripPrefix = true
msa.gateway.routes[2].uri = forward:/uac/
msa.gateway.routes[2].predicates[0] = Path=/uac/**
msa.gateway.routes[3].id = infoRoute
msa.gateway.routes[3].uri = http://127.0.0.1:${management.server.port}/${spring.application.name}/
msa.gateway.routes[3].stripPrefix = false
msa.gateway.routes[3].predicates[0] = Path=/info

msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-001
msa.ccs.encrypt.factor = ENC(tX9QX466x29hab2/QBpYRPF3Eg8PZp0JzZlYrgRXI51ItnowVFrykOUm8L+eRYgTjEi+Det5vDM=)
msa.encrypt.datakey = ImRc9tg7hbatAcVQjvTOOwcrMBDRinKTiKqyhbXzTB/wTw9dX2xu6BmVBolgz+pBKVOFTNUTaEB8NcMKUDO3hA==
msa.rootkey.factor1 = zte-iccp-itech-promgrbff
#\u7EDF\u4E00\u6743\u9650\u5BF9\u63A5\u83B7\u53D6\u6743\u9650\u914D\u7F6E \u5BF9\u5E94\u53C2\u6570\u54A8\u8BE2 \u5F6D\u7EC610236545 \u4E0D\u8D70\u7EDF\u4E00\u6743\u9650\u4E0D\u914D\u7F6E

#\u7EDF\u4E00\u6743\u9650\u5BF9\u63A5\u83B7\u53D6\u6743\u9650\u914D\u7F6E \u5BF9\u5E94\u53C2\u6570\u54A8\u8BE2 \u5F6D\u7EC610236545 \u4E0D\u8D70\u7EDF\u4E00\u6743\u9650\u4E0D\u914D\u7F6E
#\u7EDF\u4E00\u6743\u9650\u5BF9\u63A5\u83B7\u53D6\u6743\u9650\u914D\u7F6E \u5BF9\u5E94\u53C2\u6570\u54A8\u8BE2 \u5F6D\u7EC610236545 \u4E0D\u8D70\u7EDF\u4E00\u6743\u9650\u4E0D\u914D\u7F6E
upp.auth.productId = 6751046
upp.auth.moduleId = 261677920140
# \u4EA7\u54C1\u5BC6\u94A5key\uFF0C\u5411\u7EDF\u4E00\u6743\u9650\u7D22\u8981\uFF08\u533A\u5206\u73AF\u5883\uFF09
upp.auth.tenantId = 4304

# \u4EA7\u54C1\u5BC6\u94A5key\uFF0C\u5411\u7EDF\u4E00\u6743\u9650\u7D22\u8981\uFF08\u533A\u5206\u73AF\u5883\uFF09
# \u4EA7\u54C1\u5BC6\u94A5key\uFF0C\u5411\u7EDF\u4E00\u6743\u9650\u7D22\u8981\uFF08\u533A\u5206\u73AF\u5883\uFF09
upp.auth.productSecretKey = KENC(TW0N51iwKoPkfIvaYV0WSVIKrvzXEv9eUZ/RBJxmLH6FDUZDivZXmiEpkmSLqwVd$$wKhsebHPx7WV0POk)

#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-authbff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-authbff
upp.auth.authUrl = http://uactest.zte.com.cn/zte-sec-upp-authbff

#\u6D4B\u8BD5\u73AF\u5883\uFF1Ahttp://uactest.zte.com.cn/zte-sec-upp-bff\uFF1B\u751F\u4EA7\u73AF\u5883\uFF1Ahttp://uac.zte.com.cn/zte-sec-upp-bff
upp.auth.manageUrl = http://uactest.zte.com.cn/zte-sec-upp-bff

uac.sdk.tenantId = 10001
uac.sdk.appId = 261677920140
uac.sdk.issuer = https://uactest.zte.com.cn:5555/zte-sec-uac-iportalbff/oidc/10001
uac.sdk.baseUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-promgrbff
uac.sdk.clientId = 261677920140

# \u751F\u4EA7\u914D\u7F6Eprod\uFF0C\u5F00\u53D1\u6D4B\u8BD5\u73AF\u5883\u914D\u7F6Edev\u6216\u4E0D\u914D\u7F6E
# uac.sdk.env = prod
msa.gateway.tokenAuthEnable = false
msa.gateway.uac.enabled = false
uac.sdk.responseRule = msa
uac.sdk.cookie.httpOnly = true
uac.sdk.gateAuth.externalAccessEnabled = true
uac.sdk.cookie.secure = true
uac.sdk.store = redis
cacheCloud.server = http://uat.sz-cache.zcloud.zte.com.cn:8888/zte-itp-msp-cachecloud
redis.mode = 4

# \u5176\u4ED6\u914D\u7F6E
# swagger\u6587\u6863\u5730\u5740\u8BBF\u95EE\u914D\u7F6E\uFF0C\u6D4B\u8BD5\u73AF\u5883\u7532\u4E59\u914D\u7F6Etrue\uFF0C\u751F\u4EA7\u73AF\u5883\u5EFA\u8BAE\u914D\u7F6Efalse
springfox.documentation.enabled = false
# \u81EA\u5B9A\u4E49\u7EC4\u4EF6\u5E93\u5305\u4E0A\u4F20\u5927\u5C0F
ui.config.app.package.upload.size = 200
ui.config.project.watermarkURL = https://itas.test.zte.com.cn:555/inject-font/config.js, https://itas.test.zte.com.cn:555/inject-font/font.js
ui.config.project.privacyPolicyBaseUrl = https://uactest.zte.com.cn
#\u5BA2\u6237\u7AEF\u63D0\u4EA4\u7684\u8868\u5355\u8BF7\u6C42\u6765\u6E90\u8FDB\u884C\u5408\u6CD5\u6027\u6821\u9A8C\uFF0C\u9632\u6B62CSRF\u653B\u51FB
common.referrerMap[refererPatterns] = *.zte.com.cn

# \u8DF3\u8F6C\u767B\u5F55uac\u73AF\u5883\u6807\u8BC6
# \u8DF3\u8F6C\u767B\u5F55uac\u6D4B\u8BD5\u73AF\u5883uactest.zte.com.cn:5555\uFF1Atest
# \u8DF3\u8F6C\u767B\u5F55uac81\u73AF\u5883uactest.zte.com.cn\uFF1Auat
# \u8DF3\u8F6C\u767B\u5F55uac\u751F\u4EA7\u73AF\u5883\uFF0C\u9ED8\u8BA4\u4E3A\u7A7A\uFF0C\u65E0\u9700\u914D\u7F6E
uac.login.env = test

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Methods\u7684\u53C2\u6570,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3AGET,POST,DELETE,PUT,OPTIONS
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = GET,POST,DELETE
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Methods =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Origin,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3A*.zte.com.cn
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = https://lcap.test.zte.com.cn,http://lcap.dev.zte.com.cn(\u53EF\u914D\u7F6E\u591A\u4E2A\u57DF\u540D\uFF0C\u9700\u8981\u52A0\u4E0Ahttp/https\u8BF7\u6C42)
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Origin =

# \u914D\u7F6E\u63A5\u53E3\u8BF7\u6C42\u5934Access-Control-Allow-Headers,\u7981\u6B62\u8DE8\u57DF\u8BF7\u6C42\uFF0C\u975E\u5FC5\u586B\uFF0C\u9ED8\u8BA4\u503C\u4E3A*
# \u4F8B\u5982\uFF1A Access-Control-Allow-Methods = x-requested-with
# \u8BE6\u7EC6\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/fdfb0acfbd104387a8cf4685e039702b/view
Access-Control-Allow-Headers =

#kms\u914D\u7F6E\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn
msa.security.server.dt-kms.appName = ENC(uuA8FnMUpqgsXIaYgy07fWTlD6zB52JweQR2CfLAhHcWO6PFNDtycTpE9w==)
msa.security.server.dt-kms.userId = ENC(041Mu6TNLL21levUcrMCt8s7Vj5QWYKZ4NRgp6/mzOnvVAx6Ah7/TmBp1As=)
msa.security.server.dt-kms.appSecret = ENC(ikvwsrYGmtmS25kRtc/1HRNebPgn5tlhRg+tKhkHANo0ka3isIyyyHB7NYplbhIimRho)
msa.security.server.dt-kms.defaultMasterKey = itechcloud
#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:promgrbff:promgrbff:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = MDg0ODI1M2YzMzRkZTFhYzgzZjgzMGZjODZmZjE2YjUyNjkwZGY3MzQ4NTNlNjYzMWYxZmQzMWNlZjA5NDhmYzY3ODlhM2ZjMGM4YTc3MTFiOTJlNTNjZGNjNmIwZjM2$$MjMwZjJhN2U4MmVjNTliM2M0MDhmYTY4YjkzZGJlNzg=&&ODg2NmU0MjktOGMyMC00NmI2LTg4MDYtN2U0MzRjYjFhZTNk

# \u4EE5\u4E0B\u53C2\u6570\u9700\u91CD\u65B0\u7528kms\u52A0\u5BC6
#uac\u79D8\u94A5\u53D8\u66F4\uFF0C\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/c10edd9e029648b487c9247897376977/view
uac.sdk.clientSecret = KENC(IRrUbLbRnBdzE8DvNavkW8052fO2Cjn0oyfC5XKKWC5BcQalKt9COO0ur+8joMATtv+FNnLP35V+VHsbdeTSheSlfFDMpvS0mzHhIs3zc+Y=$$YS3CNkfjp79hxTTi)
uac.sdk.accessKey = KENC(J2dQHCe6Wb64WKDO+OpqVaWpwdLs75O25EkfZg61aYIUx+JwmL620w+Qd/eHW4GD$$uYIOFjlGXvuATfoj)
uac.sdk.accessSecret = KENC(GxXYHqb0CS0ZwjQPeN4QGK0SEJEmitmJx7C96M1NvepcpQKWkfbyI9KbdfyivCGKCgkUDCwyOI1BaVDN0S/9rEWXONi4HdqK0eIDBW+yIbE=$$LBF0UUEDC8aQt2Dj)
#uac\u5347\u7EA7\u53C2\u6570\u589E\u52A0\uFF1A\u6D4B\u8BD5\u4EFF\u771F\u73AF\u5883\u79D8\u94A5\u914D\u7F6E\u53D8\u66F4\u66FF\u6362
#\u5E94\u7528\u8BBF\u95EE\u7BA1\u63A7\u8BA4\u8BC1\u52A0\u5BC6\u5BC6\u94A5\uFF0C\u4E1A\u52A1\u4FA7\u81EA\u884C\u52A0\u5BC6\u5B58\u50A8\uFF0C\u89E3\u5BC6\u4F7F\u7528 test = testtgyarxsaxtdy
uac.sdk.appAuth.encryptKey = KENC(ew+wJO4/mqwuaFpXe7pBpUXKaxVpfbHVa6v1rT+7cos=$$csrEPtpqBe7seWs1)
#token\u4EE4\u724C\u8BA4\u8BC1\u52A0\u5BC6\u5BC6\u94A5\uFF0C\u4E1A\u52A1\u4FA7\u81EA\u884C\u52A0\u5BC6\u5B58\u50A8\uFF0C\u89E3\u5BC6\u4F7F\u7528 test = uat5XQmnKynEPjwZ
uac.sdk.token.encryptKey = KENC(hSN0cRNAq8hp9vTxTao80uTS8kqdIjQn1mCK2yAp09Y=$$Ptw5WwpEkxm/sRoF)
