register.address = msrtest.zte.com.cn:10081
register.node.ip = ************
register.node.port = 8888
register.version = v1
servicecenter = msb

jdbc1.type = com.alibaba.druid.pool.DruidDataSource

jdbc1.driverClassName = com.goldendb.jdbc.Driver

# \u4F4E\u4EE3\u7801\u5143\u6570\u636E\u5E93\u914D\u7F6E\uFF0C\u6309\u7167msa4.0.3\u52A0\u5BC6\u65B9\u5F0F\u81EA\u884C\u52A0\u5BC6\uFF0CMSA4.0.3\u5347\u7EA7\u8BF4\u660E

jdbc1.url = ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

msa.encrypt.datakey = vQGj5BejackQAPCl4IvZqcaZACwxcks4KkSvJAYvFtvbVxc4KuZi+t8TMQqI8TWH+g1jAUst0/vd2PIAfaosXg==
msa.rootkey.factor1 = zte-iccp-itech-promgrapi
msa.ccs.encrypt.factor = ENC(LjvI+Iv6swOtWNcw0Tk1AubV50CN2Wn5k5Yvj3V1q65FT6J9M9gYWHluslAuHFz69Tw9jYsGj4k=)

jdbc.maxActive = 100
jdbc.minIdle = 50
jdbc.initialSize = 50
jdbc.timeBetweenEvictionRunsMillis = 60000
jdbc.minEvictableIdleTimeMillis = 300000
jdbc.testWhileIdle = true
jdbc.testOnBorrow = true
jdbc.testOnReturn = true

#uac\u7528\u6237\u7528\u81EA\u5DF1\u7684\u76F8\u5173\u73AF\u5883\u914D\u7F6E
uac.sdk.tenantId = 10001
uac.sdk.appId = 261677920140
uac.sdk.issuer = https://uactest.zte.com.cn:5555/zte-sec-uac-iportalbff/oidc/10001
uac.sdk.baseUrl = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff
uac.sdk.clientId = 261677920140

uac.sdk.responseRule = msa
uac.sdk.store = redis
uac.url = https://uactest.zte.com.cn:5555
uac.sdk.cookie.httpOnly = false
uac.sdk.gateAuth.externalAccessEnabled = true
uac.sdk.cookie.secure = true
# \u6CE8\u610F\u751F\u4EA7\u914D\u7F6Eprod\u4E00\u5B9A\u8981\u6253\u5F00\u6B64\u914D\u7F6E\uFF0C\u5F00\u53D1\u6D4B\u8BD5\u73AF\u5883\u914D\u7F6Edev\u6216\u8005\u4E0D\u914D\u7F6E
# uac.sdk.env = prod

# \u83B7\u53D6\u8D26\u6237\u4FE1\u606F\u4F7F\u7528uac3.0\u67E5\u8BE2\uFF08\u5185\u573A\u73AF\u5883\uFF1AuacEmployeeProvider\uFF0C \u5916\u573A\u73AF\u5883\uFF1AuacEmployeeOuterProvider\uFF09
lcap.employee.provider = uacEmployeeProvider
# uac3.0\u6279\u91CF\u67E5\u8BE2\u8D26\u6237\u7C7B\u578B\uFF08\u5185\u573A\u73AF\u5883\uFF1AT0001\uFF0C \u5916\u573A\u73AF\u5883\uFF1AT0000\uFF09
uac.idType = T0001
# uac3.0\u79D8\u94A5 \u53C2\u8003\u8FDE\u63A5 \u3010V1.24.33\u53CA\u4EE5\u4E0A\u7248\u672C\u3011\u4F4E\u4EE3\u7801\u5E94\u7528\u5F00\u53D1\u5E73\u53F0\u4E0D\u5B89\u5168\u52A0\u5BC6\u65B9\u5F0F\u6574\u6539\u901A\u77E5

#\u6587\u6863\u4E91\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09
cloudDiskSDK.host = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk
cloudDiskSDK.httpClientLogSwitch = true
cloudDiskSDK.xOriginServiceName = EPMS

#\u5E94\u7528\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u7684\u76F8\u5173\u914D\u7F6E

#\u5BF9\u63A5\u5BA1\u6279\u4E2D\u5FC3\u7684kafka\u914D\u7F6E\uFF0C\u4E0D\u7528\u4FEE\u6539
approval.kafka.topic = zte-iss-approval-nj
#\u4F7F\u75284A\u7533\u8BF7\u7684appId
approval.sdk.app.appId = 261677920140
#\u4F7F\u75284A\u7533\u8BF7\u7684secretKey

#\u4F7F\u75284A\u7533\u8BF7\u7684appId
approval.sdk.app.appCode = 261677920140
#\u5E94\u7528\u81EA\u5B9A\u4E49\u7684\u9ED8\u8BA4\u5DE5\u53F7\uFF08\u4EFB\u610F\uFF09
approval.sdk.header.xEmpNo.default = 10284287
approval.sdk.kafka.receipt.enabled = false
#\u5BA1\u6279\u4E2D\u5FC3\u56DE\u8C03\u5E94\u7528\u7684url
approval.sdk.webhook.url = https://iepms.test.zte.com.cn/zte-iccp-itech-demobff/zte-iccp-itech-demoapi/v1/flow/webhookProcess
# \u6D41\u7A0BSDK\u662F\u5426\u9700\u8981\u4F20\u9012tenantId\u8BF7\u6C42\u5934
approval.sdk.request.header.tenantId.source = true

#\u7F13\u5B58\u4E91\u914D\u7F6E\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09https://i.zte.com.cn/#/shared/569154dd0096477f9824fe2286c110fb/wiki/page/4fc5d276e28343cb9425bbc91acaeeed/view

msa.ccs.enable = true
msa.ccs.resourceCode = zxiccp-iepms300-001
cacheCloud.server = http://mspuat.zte.com.cn:8888/zte-itp-msp-cachecloud/
redis.mode = 4
redis.customCacheManager = true
msa.redis.driveType = jedis
msa.redis.serializerType = genericJackson2Json
msa.redis.sotimeout = 1000
msa.redis.maxAttempts = 2
msa.redis.maxRedirects = 5
msa.redis.pool.maxActive = 8
msa.redis.pool.maxWait = 3000
msa.redis.pool.maxIdle = 300
msa.redis.pool.minIdle = 300
msa.redis.pool.minEvictableIdleTimeMillis = 1800000
msa.redis.timeout = 3000
msa.redis.cacheManager.expire = 86400

# inone \u5E94\u7528\u7F16\u7801(\u5E73\u53F0\u5BF9\u5E94\u7684\u5382\u5546:ZXLCAP_PRO300 \u5E94\u7528:LCAP_PRO300_SUBS)
inone.url = https://icosg.test.zte.com.cn
#kafak\u76F8\u5173\uFF08\u9700\u8981\u4FEE\u6539\u6210\u5E94\u7528\u4FA7\u7684\u914D\u7F6E\uFF09

message.server = http://msptest.zte.com.cn:8888/zte-itp-msp-message/v1
spring.flyway.enabled = false
spring.flyway.encoding = UTF-8
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
# \u5E94\u7528\u5217\u8868\u6570\u636E\u6743\u9650\u7F16\u7801
#----------\u4EE3\u7801\u5E93\u6709\u7684\u503C\u5DF2\u2018##\u2019\u6807\u5FD7----------

spring.flyway.locations = classpath:db/migration/V12432/update
# redis cacheManager\u8FC7\u671F\u65F6\u95F4,\u5355\u4F4D\u79D2\uFF0C\u9ED8\u8BA4\u4E3A0,\u4EE3\u8868\u6C38\u4E0D\u8FC7\u671F

# redis cacheManager\u8FC7\u671F\u65F6\u95F4,\u5355\u4F4D\u79D2\uFF0C\u9ED8\u8BA4\u4E3A0,\u4EE3\u8868\u6C38\u4E0D\u8FC7\u671F

#\u7F13\u5B58\u4E91\u8D44\u6E90\u7F16\u7801

spring.flyway.sql-migration-prefix = V

#\u96C6\u7FA4\u95F4\u6700\u5927\u8DF3\u8F6C\u6B21\u6570,\u5982\u679C\u4E0D\u8BBE\u7F6EmaxRedirects\u503C\uFF0C\u6E90\u7801\u4E2D\u9ED8\u8BA4\u4E3A5\u3002\u4E00\u822C\u5F53\u6B64\u503C\u8BBE\u7F6E\u8FC7\u5927\u65F6\uFF0C\u5BB9\u6613\u62A5\uFF1AToo many Cluster redirections

spring.flyway.sql-migration-separator = __

spring.flyway.validate-on-migrate = false

spring.flyway.baseline-on-migrate = true

spring.flyway.placeholder-replacement = false

spring.flyway.table = promgrapi_flyway_schema_history

spring.flyway.out-of-order = true

##info/health/prometheus\u5B89\u5168\u6CBB\u7406\u5BB9\u5668\u6539\u52A8

msa.expose.info.enable = true

#\u7EC4\u4EF6\u5E93\u4E0A\u4F20\u670D\u52A1\u914D\u7F6E\uFF0C\u5BF9\u5E94\u4F4E\u4EE3\u7801\u81EA\u8EABdemoapi\u670D\u52A1\u540D\u548C\u7248\u672C\u53F7\uFF0C\u6D4B\u8BD5\u73AF\u5883\uFF1AV3,protest:V3,uat:V2,prouat:V3\uFF0C\u751F\u4EA7:V1

lcap.app.upload.target.service = zte-iccp-itech-demoapi.v1

lcap.app.upload.target.path = zte-iccp-itech-demoapi

#\u4F4E\u4EE3\u7801\u670D\u52A1\u73AF\u5883\u7BA1\u7406\u9879\u76EE\u540D\u548C\u7248\u672C\u53F7\uFF0CTEST:V1,PROTEST:V3,UAT:V1,PROUAT\uFF1AV3,\u751F\u4EA7\uFF1AV1

lcap.mc.version.no = v1

lcap.mc.service.name = zte-paas-lcap-promcapi

# mgr\u8C03\u7528mc\u6807\u51C6\u670D\u52A1\u7684\u57DF\u540D+bff+\u670D\u52A1\u540D >=24.41\u7248\u672C\u9700\u8981\u914D\u7F6E\uFF08\u8FD9\u91CC\u76F4\u63A5\u5C06\u751F\u4EA7\u7684\u914D\u7F6E\u6807\u51C6\u793A\u4F8B\u8D34\u8FDB\u6765\uFF0C\u6D4B\u8BD5\u73AF\u5883\u66FF\u6362\u6D4B\u8BD5\u73AF\u5883\u57DF\u540D\u5373\u53EF\uFF09
lcap.mc.route.url = https://lcappro.test.zte.com.cn/zte-paas-lcap-promcbff/zte-paas-lcap-promcapi

#kms\u914D\u7F6E\uFF0C\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn
msa.security.server.dt-kms.appName = ENC(C5BU8AimfN+Md8sEH4E1pMl8dfBnvbzHYxxLI3+2YZ8Feq5EUwWYByJo3Q==)
msa.security.server.dt-kms.userId = ENC(kcrWvN092QrGdvWMGywufGaB/sS35khvm1Lm9Rc1YeRdKuc+3Bl6n4xDqrc=)
msa.security.server.dt-kms.appSecret = ENC(9zJU9AGAK0aST4e/2g/t2zNtuCGvAr6fmT1vrWtyUYlONK/QFGbT95EoM2PBC80GLgVK)
msa.security.server.dt-kms.defaultMasterKey = itechcloud

#\u4EE5\u4E0B\u51E0\u4E2A\u914D\u7F6E\u53C2\u8003https://i.zte.com.cn/#/shared/2511cc3638074d3aac8e09068ca9d3b1/wiki/page/9c2d17b6d8c842189de08a07f9a05165/view\u4E2D2.2\u7AE0\u8282\u914D\u7F6E\u9879\u8BF4\u660E
msa.security.server.dt-kms.masterKeys.itechcloud.defaultDataKey = encryptor
msa.security.server.dt-kms.masterKeys.itechcloud.secretKeyType = itech:demoapi:demoapi:secret
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.encryptor = MjYzNGYyNDEwODMwYTU5MTExYWUzM2Q1M2YxOWMxM2RmMmViODliNWI2ZDRhY2E1OWM0Y2FmYzVkYTVkZjQ4YTNkOTIzODk5NTgxYjYwOGZiOTkwMjIzM2NiMmI1Njgy$$Y2E5OWUzZjM3Y2VlZjA4ZTljNDZlMWQwZWU4YzA5NDY=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3

#\u8BE5\u914D\u7F6E\u9879\u7684\u503C\u8981\u548Cdemoapi\u4E2D\u8BE5\u914D\u7F6E\u9879\u7684\u503C\u4FDD\u6301\u4E00\u81F4
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.cipher = NDc0NjU5NmEzZmExNmFmZDE4ZDU3OTdkZGIwNzU4MGRlOTY1OWNlODkxMzQxYTM1MDJmYTc4ZmM4MTVkNjMwNzQ0OGU0YTUxYzk5NjE0NWI3ZTgxMjhiZGYyNDE1MTBj$$ZjBkYTM5NDk1NmVhNTlkYmUwMGRjM2Q3MWMwNDJmZTk=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3
msa.security.server.dt-kms.masterKeys.itechcloud.dataKeys.sensitiveData = ZTExM2NmNWI0OTcwOTQxOTdhOWU5ZWE1NGRhMDUwYTQ3ZTJiYzkwOTU0MTgwMDUzYTQ0YTdlYTRmZjU1ZTJjMzU5NTlkZTg1YTVmZDIyZmM5ZWExMTMxODM5ZTc3YTVj$$MDZmOTdkODgwMjc5NjZkM2VmNmQ4OWE2MGM3OGQyZjY=&&YjYwYTAxZTctY2JjYi00Y2NhLTliZjUtZjI2M2E5MWVhNzM3
# \u4EE5\u4E0B\u53C2\u6570\u9700\u91CD\u65B0\u7528kms\u52A0\u5BC6
jdbc1.username = KENC(iu8GEFayXxhlNp8NgSuPUlnWzC6KYA==$$mp0/2xhIDoSfCfYn)
jdbc1.password = KENC(arm5t6reXTsR5wQwt0zb9wN3zAiC4mvvgFbKdrA=$$izi1zjABBDS3Obln)
cloudDiskSDK.xOrgId = KENC(WH/iG1ujRF5UqMbAzCOSnqZUP7G/HAX2otYeDJqb9w==$$zaXPnLe0yuQMcEsy)
cloudDiskSDK.xSecretKey = KENC(hfFUXAgXI+MQ4kboJEmepGsik6OtM43QARNTYrOnnQ==$$/tXlOVVMtDpgad9B)
approval.sdk.app.secretKey = KENC(xvOD8/dItrntJrEDJXarHgYMnN0CIghSj4rVUWHZOW13Gh7t2LbfxqYlZlh3Sk0wn/TJtBiaJAYOeqhZN0j7UjvGh7UZzwrErXZWuuF1ENU=$$vLlRTeAhlezT+rZh)
uac.sdk.clientSecret = KENC(DwQMtnKD5lLsC/qTWR1jMvVSJ+nEo122IvbjAPyxW7hPHbznxZrmkgtvh5g/qNj9uIGUSMGDp+pg8MMutKjSMLp2cFGleXOoWhR3iHojcfg=$$0L155ic30+vQagt1)
uac.sdk.accessKey = KENC(V2gJGQ5jRe52QvsPGnRDvZCRAtpEEQ3n41veNdxaZuT5rIi+Hpvu9Fef9qSA/YrC$$GCprj1kfUmz2fi4z)
uac.sdk.accessSecret = KENC(GZxAnbQPXd8KVwzjJaJefI02DGR85Gm5zE82tG6zgh8zjWliSoa8idCLsiSC/zvGRgH0gQzZR8D4rhib3xaFYDVLI9F4c6FvNzgvVzBe4KY=$$yKTlOEB/GA1/xo/+)
uac.key = KENC(q39SgTDPRnlvJXV2D/4xlUW5z5XJiunME1+a/1bc6AvNr+V/CTUEqoWRPmqpcFYa/d0A7dek9iH2UtXRIQNpi6cRiyZV638M4pzSnQ==$$DwOZsmfB05nqRzi/)
inone.appCode = KENC(7s6lZDXzKQohfH+gr73qf5cg4LjpifJxoFDmi2ZYnohAi+PhdzWN/YEN8vaLeeDp$$/rc95i5lsdaoAla1)

# \u9700\u8981\u4E0Edemoapi\u79D8\u94A5\u76F8\u540C
common.app.param.secret = KENC(aOkV9KdpURUhrVBgYaOMNt8MsQcn5Y9egw6odV8nrUBEYlMzlCTlOo2scxu3Q9Yw$$nD0pXfLkZ31zaRoJ)
datasource.config.gcm.encrypt.key = KENC(insmN2oDLPVq1kVgV3CZVLpcRzC+AZEOka1qIx1LDraRdtSi20EOd57sO+bNhT2d$$TFI/puyZCqW9vMIk)

# \u6570\u636E\u6E90\u52A0\u89E3\u5BC6\u76F8\u5173\u53C2\u6570
# \u8BE6\u7EC6\u751F\u6210\u6307\u5357\u8BF7\u53C2\u8003\uFF1Ahttps://i.zte.com.cn/index/ispace/#/space/e88feb85c1714f8092d2a70e0b6d6fb1/wiki/page/e76cd1492ed642b1b6b711cd4025285b/view
common.rsa.public-key = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlyx2NuFQXajp8FBBl0n0vEJEJB6mBPh6O9GnX0mw/VD5LVs/mYnNd2cv7Yki7K3+WVmMqgoGG6obPGu7IU+Qaq5/dXNwCX1VAMnkPgLhRQevh227jJrgstLsFzTmA5DgRFzjExrAZbFrVHAPYUlk0y1eXpsCQmNJ8h3WzCxmaYyOHHuSFAbs6yXXcRebMM4+V2P1p2o70hKcrWRGiwHGl5QXx8qPnWPAA6mIwdJ1JViuNR3uL4i/NTnjbA/Mc1O43lryP8DZo7V3a1VerG6sPjcOCQ4nPm3ygoqe6OwbKV6xUSIvyS+jCI05Gq58YRxyp+V3zMsNMpv5+/aPLui4GwIDAQAB

common.rsa.private-key = KENC(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$$oQfmJ6NiWP4OfHV3)
