# 制品库二级/三级文件夹（产品大类/产品组） test
repo_path: crm/iepms

service_name: zte-iccp-itech-netchange

parent_path: .

#自定义需要进行单元测试 kw客户端扫描 sonar检查的module。聚合项目指定为父工程和根目录
ensure_verify: zte-iccp-itech-netchange:.

#需要打成jar包上传的文件夹
upload_jar_list: zte-iccp-itech-extension, zte-iccp-itech-zlic
upload_parent_pom: zte-iccp-itech-netchange

# 镜像制品库
artifactory_domain: artsz.zte.com.cn
#测试环境镜像库
snap_docker_repo: it-snapshot-docker
#仿真环境镜像库
alpha_docker_repo: it-alpha-docker
#测试环境版本文件
snap_gen_repo: it-snapshot-generic
#仿真环境版本文件
alpha_gen_repo: it-alpha-generic

# 镜像版本号前缀+yyyymmddhhmmss
image_version: v1.0.0

# 系统设置配置的artifactory servers
art_server_id: art_sz_server